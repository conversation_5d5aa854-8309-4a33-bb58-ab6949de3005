#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成配置文件差异对比的Excel文件
"""

import os
import pandas as pd
from collections import defaultdict

def parse_properties_file(file_path):
    """解析properties文件为键值对"""
    properties = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    properties[key.strip()] = {
                        'value': value.strip(),
                        'line': line_num
                    }
    except Exception as e:
        print(f"解析properties文件 {file_path} 失败: {e}")
    
    return properties

def compare_properties_files(file1_path, file2_path):
    """对比两个properties文件"""
    props1 = parse_properties_file(file1_path)
    props2 = parse_properties_file(file2_path)
    
    differences = []
    
    all_keys = set(props1.keys()) | set(props2.keys())
    
    for key in sorted(all_keys):
        if key in props1 and key in props2:
            if props1[key]['value'] != props2[key]['value']:
                differences.append({
                    '配置项': key,
                    '差异类型': '值不同',
                    '85目录值': props1[key]['value'],
                    '86目录值': props2[key]['value'],
                    '分析': analyze_difference(key, props1[key]['value'], props2[key]['value'])
                })
        elif key in props1:
            differences.append({
                '配置项': key,
                '差异类型': '仅在85中',
                '85目录值': props1[key]['value'],
                '86目录值': '',
                '分析': '85环境独有配置'
            })
        else:
            differences.append({
                '配置项': key,
                '差异类型': '仅在86中',
                '85目录值': '',
                '86目录值': props2[key]['value'],
                '分析': '86环境独有配置'
            })
    
    return differences

def analyze_difference(key, value1, value2):
    """分析差异的原因"""
    key_lower = key.lower()
    
    # 服务器地址差异
    if '*************' in value1 and '*************' in value2:
        return '服务器IP地址差异(85→86)'
    elif '*************' in value1 and '*************' in value2:
        return '服务器IP地址差异(86→85)'
    elif '*************' in value1 or '*************' in value2:
        return '测试环境服务器地址'
    
    # 环境差异
    if 'test' in value1.lower() or 'test' in value2.lower():
        return '测试/生产环境差异'
    
    # 支付环境差异
    if '*************' in value1 and '**************' in value2:
        return '支付环境差异(生产→测试)'
    elif '**************' in value1 and '*************' in value2:
        return '支付环境差异(测试→生产)'
    
    # 路径差异
    if 'path' in key_lower or 'dir' in key_lower or 'url' in key_lower:
        if '/EOMAPP/FTPUpLoad/' in value1 and '/EOMAPP/UploadFiles/' in value2:
            return '文件路径结构差异'
        elif value1.startswith('/') or value2.startswith('/'):
            return '文件路径配置差异'
    
    # 时间/版本差异
    if 'year' in key_lower or 'time' in key_lower or 'date' in key_lower:
        return '时间/版本配置差异'
    
    # 开关配置
    if value1 in ['start', 'close', 'open', 'true', 'false'] or value2 in ['start', 'close', 'open', 'true', 'false']:
        return '功能开关配置差异'
    
    return '配置值差异'

def generate_config_diff_excel():
    """生成配置差异Excel文件"""
    
    config_85_dir = "D:/Code/svn/EOM/config/85"
    config_86_dir = "D:/Code/svn/EOM/config/86"
    
    # Properties文件列表
    properties_files = [
        'FtpConfig.properties',
        'WebService-config.properties'
    ]
    
    all_differences = []
    file_summary = []
    
    for config_file in properties_files:
        file1_path = os.path.join(config_85_dir, config_file)
        file2_path = os.path.join(config_86_dir, config_file)
        
        if os.path.exists(file1_path) and os.path.exists(file2_path):
            print(f"对比文件: {config_file}")
            
            differences = compare_properties_files(file1_path, file2_path)
            
            # 为每个差异添加文件名
            for diff in differences:
                diff['配置文件'] = config_file
                all_differences.append(diff)
            
            # 统计信息
            only_85 = len([d for d in differences if d['差异类型'] == '仅在85中'])
            only_86 = len([d for d in differences if d['差异类型'] == '仅在86中'])
            different = len([d for d in differences if d['差异类型'] == '值不同'])
            
            file_summary.append({
                '配置文件': config_file,
                '总差异数': len(differences),
                '仅在85中': only_85,
                '仅在86中': only_86,
                '值不同': different,
                '主要差异类型': get_main_diff_type(differences)
            })
    
    # 创建DataFrame
    differences_df = pd.DataFrame(all_differences)
    summary_df = pd.DataFrame(file_summary)
    
    # 按差异类型分组统计
    if not differences_df.empty:
        type_stats = differences_df.groupby('差异类型').size().reset_index(name='数量')
        analysis_stats = differences_df.groupby('分析').size().reset_index(name='数量').sort_values('数量', ascending=False)
        
        # 重要差异筛选
        important_diffs = differences_df[
            differences_df['分析'].str.contains('服务器|环境|支付', na=False)
        ].copy()
    else:
        type_stats = pd.DataFrame()
        analysis_stats = pd.DataFrame()
        important_diffs = pd.DataFrame()
    
    # 生成Excel文件
    output_file = 'EOM配置文件差异对比表.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 所有差异详情
        if not differences_df.empty:
            differences_df.to_excel(writer, sheet_name='所有差异详情', index=False)
        
        # 重要差异
        if not important_diffs.empty:
            important_diffs.to_excel(writer, sheet_name='重要差异', index=False)
        
        # 文件汇总
        summary_df.to_excel(writer, sheet_name='文件汇总', index=False)
        
        # 差异类型统计
        if not type_stats.empty:
            type_stats.to_excel(writer, sheet_name='差异类型统计', index=False)
        
        # 差异原因分析
        if not analysis_stats.empty:
            analysis_stats.to_excel(writer, sheet_name='差异原因分析', index=False)
        
        # 配置对比矩阵
        if not differences_df.empty:
            matrix_data = []
            for _, row in differences_df.iterrows():
                matrix_data.append({
                    '配置文件': row['配置文件'],
                    '配置项': row['配置项'],
                    '85环境': '✓' if row['85目录值'] else '',
                    '86环境': '✓' if row['86目录值'] else '',
                    '是否一致': '❌' if row['差异类型'] != '相同' else '✅',
                    '差异级别': get_diff_level(row['分析'])
                })
            
            matrix_df = pd.DataFrame(matrix_data)
            matrix_df.to_excel(writer, sheet_name='配置对比矩阵', index=False)
    
    print(f"\n✅ 配置差异Excel文件生成完成！")
    print(f"📄 文件名：{output_file}")
    print(f"📊 差异总数：{len(all_differences)} 个")
    print(f"📋 配置文件：{len(properties_files)} 个")
    
    # 显示主要差异
    if not differences_df.empty:
        print(f"\n📈 差异分布：")
        for _, row in type_stats.iterrows():
            print(f"  {row['差异类型']}: {row['数量']}个")
        
        print(f"\n🎯 主要差异原因：")
        for _, row in analysis_stats.head(5).iterrows():
            print(f"  {row['分析']}: {row['数量']}个")

def get_main_diff_type(differences):
    """获取主要差异类型"""
    if not differences:
        return '无差异'
    
    type_counts = {}
    for diff in differences:
        diff_type = diff['差异类型']
        type_counts[diff_type] = type_counts.get(diff_type, 0) + 1
    
    main_type = max(type_counts, key=type_counts.get)
    return f"{main_type}({type_counts[main_type]}个)"

def get_diff_level(analysis):
    """获取差异级别"""
    if '服务器' in analysis or 'IP' in analysis:
        return '高'
    elif '环境' in analysis or '支付' in analysis:
        return '中'
    elif '路径' in analysis or '开关' in analysis:
        return '低'
    else:
        return '一般'

if __name__ == "__main__":
    generate_config_diff_excel()
