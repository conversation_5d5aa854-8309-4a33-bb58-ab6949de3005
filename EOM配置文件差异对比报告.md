# EOM系统配置文件差异对比报告

## 📋 对比说明

本报告对比了config目录下85和86两个子目录中的配置文件差异：
- **85目录**: 可能是85服务器的配置
- **86目录**: 可能是86服务器的配置

**对比文件数量**: 6 个

---

## 🔍 FtpConfig.properties

**文件类型**: Properties配置文件
**差异状态**: 有差异

### 📌 仅在85中存在的配置

| 配置项 | 值 |
|--------|-----|
| `BACKUP_PATH` | `/data/work5` |
| `OVERTIME_DAY` | `3` |
| `PAYNOTIFY_URL` | `http://*************:8080/EOM/Payment_notifyUrl.action` |
| `CONT_PAPER_PDF_URL` | `/EOMAPP/UploadFiles/openroderdocx2pdf/paper/` |
| `DSJ_PSD` | `%pNj#uf3` |
| `UNIT_VERIFY_TRUTH_URL` | `http://10.113.222.58:9999/pois/check/` |
| `BUSINOTIFY_URL` | `http://*************:8080/EOM/Payment_busiNotifyUrl.action` |
| `UNIT_FILE_DIR` | `/EOMAPP/UploadFiles/FtpFiles/S3851PATH/` |
| `BOSSNO` | `aagh38,aagh7h` |
| `WECHAT_URL` | `http://**************:8080/zqwx/zq/receive/invoice` |
| `DSJ_PORT` | `21` |
| `DOCUMENT_URL` | `/EOMAPP/UploadFiles/Document/` |
| `REFUNDNOTIFY_URL` | `http://*************:8080/EOM/PayRefund_payRefundNotify.action` |
| `PAYMENT_URL` | `http://*************:18002/` |
| `CONT_PDF_URL` | `/EOMAPP/UploadFiles/openroderdocx2pdf/` |
| `CONTRACTCSV_URL` | `/EOMAPP/contracts/contractCsv/` |
| `DSJ_USER` | `zx_ftp` |
| `MAIN_PATH` | `/data/work4` |
| `BACKUP_HOST` | `*************` |
| `MAIN_HOST` | `*************` |
| `CANCEL_NOTIFY_URL` | `http://*************:8080/EOM/Payment_cancelNotifyUrl.action` |
| `KEY` | `go2ve0rn1me9nt1an0de2nt3er3prise` |
| `MERCHANT_NO` | `3002001` |

### 🔄 值不同的配置

| 配置项 | 85目录值 | 86目录值 |
|--------|----------|----------|
| `SIGN_UPLOADPDF` | `http://*************:8080/EOM/EomSignAction_uploadPdf.action` | `http://*************:8080/EOM/EomSignAction_uploadPdf.action` |
| `FTP_YEAR` | `201912` | `201907` |
| `USER_PHOTO_DIR` | `/EOMAPP/FTPUpLoad/PhotoDir/` | `/EOMAPP/UploadFiles/PhotoDir/` |
| `CONTRACTFTP_URL` | `/EOMAPP/contracts/` | `/EOMAPP/UploadFiles/contracts/` |
| `PAY_API_URL` | `http://*************:18002/` | `http://**************:18002/` |
| `SIGN_DOWNLOADPDF` | `http://*************:8080/EOM/EomSignAction_downloadPdf.action?path=` | `http://*************:8080/EOM/EomSignAction_downloadPdf.action?path=` |

### 📊 统计信息

- 仅在85中: 23 项
- 仅在86中: 0 项
- 值不同: 6 项
- 相同配置: 56 项

---

## 🔍 WebService-config.properties

**文件类型**: Properties配置文件
**差异状态**: 有差异

### 📌 仅在85中存在的配置

| 配置项 | 值 |
|--------|-----|
| `PREINVAPPLY_RISKDATA` | `/EOMAPP/UploadFiles/AuditWorkFile/` |

### 📌 仅在86中存在的配置

| 配置项 | 值 |
|--------|-----|
| `CONTROL_HOLIDAYTASK_STATE` | `0` |

### 🔄 值不同的配置

| 配置项 | 85目录值 | 86目录值 |
|--------|----------|----------|
| `CONTRACT_ADDERSS` | `http://*************:8080/OrderSysIntoContract/Contract_Info_Recevie_srv.svc?wsdl` | `http://*************:8080/OrderSysIntoContract/Contract_Info_Recevie_srv.svc?wsdl` |
| `SERVER_ADDRESS` | `*************` | `*************` |
| `HONOR_UPLOAD` | `/EOMAPP/UploadFiles/EnjoyFiles/` | `` |
| `AUDIT_INTERS_LOGIN_SWITCH` | `close` | `start` |
| `CONTRACT_UPDATEADDERSS` | `http://*************:8080/OrderSysIntoContract/Contract_Status_Change_srv.svc?wsdl` | `http://*************:8080/OrderSysIntoContract/Contract_Status_Change_srv.svc?wsdl` |

### 📊 统计信息

- 仅在85中: 1 项
- 仅在86中: 1 项
- 值不同: 5 项
- 相同配置: 58 项

---

## 🔍 applicationContext-action.xml

**文件类型**: XML配置文件
**差异状态**: 有差异

### 📝 详细差异

```diff
--- 85/applicationContext-action.xml
+++ 86/applicationContext-action.xml
@@ -1,4 +1,5 @@
 <?xml version="1.0" encoding="UTF-8"?>
+
 <beans xmlns="http://www.springframework.org/schema/beans"
 	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
 	xmlns:tx="http://www.springframework.org/schema/tx"
@@ -7,21 +8,7 @@
 		    http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
 		    http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd"
 	default-autowire="byName">
-	<!-- 集团客户快速建档 -->
-	<bean id="UnitInfoAction" class="com.xinxinsoft.action.ums.UnitInfoAction" autowire="byType" scope="prototype">
-		<property name="unitInfoService">
-			<ref bean="UnitInfoService"/>
-		</property>
-        	<property name="attachmentService">
-            		<ref bean="AttachmentService" />
-        	</property>
-        	<property name="groupCustomerService">
-            		<ref bean="GroupCustomerService" />
-        	</property>
-        	<property name="iBossByNoService">
-            		<ref bean="IbbnService" />
-        	</property>
-	</bean>
+
 	<!-- 后台系统用户action -->
 	<bean id="LoginUserAction" class="com.xinxinsoft.action.core.user.LoginUserAction"
 		scope="prototype">
@@ -30,6 +17,46 @@
 		</property>
 	</bean>

+<bean id="OMSAction" class="com.xinxinsoft.action.appOpenAction.OMSAction" autowire="byType" scope="prototype">
+        <property name="systemUserService">
+            <ref bean="SystemUserService" />
+        </property>
+        <property name="omsService">
+            <ref bean="OMSService" />
+        </property>
+        <property name="querySer">
+            <ref bean="VariousSqlQueryService" />
+        </property>
+        <property name="groupCustomerService">
+            <ref bean="GroupCustomerService" />
+        </property>
+    </bean>
+    <bean id="PMSAction" class="com.xinxinsoft.action.appOpenAction.PMSAction" autowire="byType" scope="prototype">
... (还有 1092 行差异)
```

---

## 🔍 applicationContext-jpbm.xml

**文件类型**: XML配置文件
**差异状态**: 有差异

### 📝 详细差异

```diff
--- 85/applicationContext-jpbm.xml
+++ 86/applicationContext-jpbm.xml
@@ -33,41 +33,43 @@
 	<bean id="DecisionImp" class="com.xinxinsoft.jpbm.AssignmentUtils.DecisionImp">
 	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
-	<bean id="DecisionImpl" class="com.xinxinsoft.jpbm.AssignmentUtils.DecisionImpl">
-	    <property name="processEngine" ref="processEngine"></property>
+
+	<bean id="TransferJBPMUtils" class="com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils">
+	    <property name="jbpmUtil" ref="JBPMUtil"></property>
+
 	</bean>
 	<bean id="ClaimForFundsImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ClaimForFundsImp">
 	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
-	<bean id="TransferJBPMUtils" class="com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils">
-	    <property name="jbpmUtil" ref="JBPMUtil"></property>
-
+	<bean id="DecisionImpl" class="com.xinxinsoft.jpbm.AssignmentUtils.DecisionImpl">
+	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
 	<bean id="DecisionImpTwo" class="com.xinxinsoft.jpbm.AssignmentUtils.DecisionImpTwo">
 	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
-	<bean id="receiveApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.receiveApplyImp">
+	    <bean id="receiveApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.receiveApplyImp">
 	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
 	<bean id="BusinssDecisionImpl" class="com.xinxinsoft.jpbm.AssignmentUtils.BusinssDecisionImpl">
 	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
-	<bean id="ManuallnvApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ManuallnvApplyImp">
+	    <bean id="ManuallnvApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ManuallnvApplyImp">
 	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
 	<bean id="BigAADecisionHandlerImpl" class="com.xinxinsoft.jpbm.AssignmentUtils.BigAADecisionHandlerImpl">
 	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
-	 <bean id="ReceiptApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ReceiptApplyImp">
-	    	<property name="processEngine" ref="processEngine"></property>
+	<bean id="ReceiptApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ReceiptApplyImp">
+	    <property name="processEngine" ref="processEngine"></property>
 	</bean>
-
-	<bean id="StartEndEvent" class="com.xinxinsoft.service.appOpenService.jbpmEvent.StartEndEvent">
+	<bean id="StartEndEvent"
+		class="com.xinxinsoft.service.appOpenService.jbpmEvent.StartEndEvent">
 		<property name="noResApplyService">
 			<ref bean="NoResApplyService" />
... (还有 46 行差异)
```

---

## 🔍 applicationContext-manager.xml

**文件类型**: XML配置文件
**差异状态**: 有差异

### 📝 详细差异

```diff
--- 85/applicationContext-manager.xml
+++ 86/applicationContext-manager.xml
@@ -562,7 +562,7 @@
 		</property>
 	</bean>

-	<!-- 尊享码上传 -->
+	<!-- 尊享码上传
 	<bean id="HonorUpLoadTask" class="com.xinxinsoft.task.HonorUpLoadTask">
 		<property name="activityService">
 			 <ref bean="ActivityService"/>
@@ -573,7 +573,7 @@
 		<property name="honorCodeBusinessService">
 			 <ref bean="HonorCodeBusinessService"/>
 		</property>
-	</bean>
+	</bean>-->

 	<!-- 转账管理 -->
 	<bean id="TransferInformationService" class="com.xinxinsoft.service.transfer.TransferInformationService">
@@ -1147,6 +1147,8 @@
 	<!--app动态导入合同数据-->
 	<bean id="AppDynamicContractService" class="com.xinxinsoft.service.contract.AppDynamicContractService" />

+	<!--USIM物联网卡管理-->
+	<bean id="USIMService" class="com.xinxinsoft.service.USIMService.USIMService" />

 	<!--问卷调查服务方法类-->
 	<bean id="QuestionnaireService" class="com.xinxinsoft.service.QuestionSurveyService.QuestionnaireService" />
@@ -1181,13 +1183,11 @@
 			<ref bean="JobLogServicer"/>
 		</property>
 	</bean>
-	<!--USIM物联网卡管理-->
-	<bean id="USIMService" class="com.xinxinsoft.service.USIMService.USIMService" />
 	<!--自助缴费验证-->
 	<bean id="GroupInviteCodeService" class="com.xinxinsoft.service.groupInviteCodeService.GroupInviteCodeService"></bean>
 	<!--白名单定时器-->
 	<bean id="EnterPriseTask" class="com.xinxinsoft.task.EnterPriseTask" />
-
+	<!--服务器内存检测-->
 	<bean id="SystemWarningTask" class="com.xinxinsoft.task.SystemWarningTask" scope="prototype">
 		<property name="pushService">
 			<ref bean="smsPushService" />
@@ -1195,6 +1195,8 @@
 	</bean>
 	<!--离网号码恢复管理-->
 	<bean id="SRestoreDeadUserService" class="com.xinxinsoft.service.SRestoreDeadUser.SRestoreDeadUserService" />
+
+
... (还有 35 行差异)
```

---

## 🔍 struts.xml

**文件类型**: XML配置文件
**差异状态**: 有差异

### 📝 详细差异

```diff
--- 85/struts.xml
+++ 86/struts.xml
@@ -27,8 +27,6 @@
 			</interceptor-ref>
 		</action>

-<!-- 集团客户快速建档 -->
-		<action name="unitInfo_*" class="UnitInfoAction" method="{1}"></action>
 		<!-- 后台系统角色ACTON -->
 		<action name="jsp/role/roleAction!*" class="RoleAction" method="{1}">
 			<result name="display">/jsp/role/editRole.jsp</result>
@@ -49,6 +47,12 @@
 			<interceptor-ref name="token">
 				<param name="includeMethods">editRole,addRole</param>
 			</interceptor-ref>
+		</action>
+		<!-- 我的合同模块 -->
+		<action name="jsp/oms/omsSrv_*" class="OMSAction" method="{1}"></action>
+		<action name="jsp/pms/pmsSrv_*" class="PMSAction" method="{1}"></action>
+		<action name="contractAct_*" class="customClauseContractAction"
+			method="{1}">
 		</action>

 		<!-- 后台系统组织结构ACTON -->
@@ -379,8 +383,8 @@

 		<!-- 欠费管理，欠费信息 -->
 		<action name="Arrears_*" class="ArrearsAction" method="{1}"></action>
-		<!-- 欠费统计 -->
- 		<action name="ArrearsStatisticalAction_*" class="ArrearsStatisticalAction" method="{1}"></action>
+ 		<!-- 欠费统计 -->
+		<action name="ArrearsStatisticalAction_*" class="ArrearsStatisticalAction" method="{1}"></action>
 		<!-- 欠费管理，协议补录 -->
 		<action name="Agreement_*" class="AgreementAction" method="{1}"></action>
 		 <!-- 欠费管理，催缴单信息 -->
@@ -410,76 +414,58 @@
 		 <!-- 稽核接口 -->
 		 <action name="AuditWorksheetHttpAction_*" class="AuditWorksheetHttpAction" method="{1}"></action>
 		 <!-- 稽核 -->
-	 	 <action name="AuditWorksheetAction_*" class="AuditWorksheetAction" method="{1}"></action>
-	          <!-- 合同认领 -->
+		 <action name="AuditWorksheetAction_*" class="AuditWorksheetAction" method="{1}"></action>
+                 <!-- 合同认领 -->
 		 <action name="ClaimAContractAction_*" class="ClaimAContractAction" method="{1}"></action>
-		 <!-- 预开票 -->
+                  <!-- 开票 -->
 		  <action name="preinvApply_*" class="PreinvApplyAction" method="{1}"></action>
-		<!-- 预开票APP -->
- 		<action name="AppPreinvApply_*" class="AppPreinvApplyAction" method="{1}"></action>
-                  <!-- 尊享码开放接口 -->
... (还有 319 行差异)
```

---

## 📊 对比总结

### 📈 统计信息
- **对比文件总数**: 6 个
- **有差异文件**: 6 个
- **无差异文件**: 0 个
- **差异比例**: 100.0%

### 🎯 主要差异类型

根据对比结果，主要差异可能包括：
1. **服务器地址差异**: 不同服务器的IP地址配置
2. **端口配置差异**: 服务端口的不同设置
3. **环境配置差异**: 开发/测试/生产环境的区别
4. **数据库连接差异**: 不同数据库实例的连接配置
5. **第三方接口差异**: 外部系统接口地址的不同

### 💡 建议

1. **配置标准化**: 建议统一两套环境的配置格式
2. **环境隔离**: 确保测试和生产环境的配置正确隔离
3. **配置管理**: 建立配置文件版本管理机制
4. **自动化部署**: 使用配置模板减少手工配置错误

---

*本报告由自动化脚本生成，详细展示了85和86两套配置的所有差异。*