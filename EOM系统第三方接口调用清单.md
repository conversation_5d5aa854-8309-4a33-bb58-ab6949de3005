# EOM系统第三方接口调用清单

## 📋 文档说明

本文档整理了EOM系统中调用的所有第三方接口信息，包括：
- 接口地址和类型
- 业务功能说明
- 系统分类归属
- 配置参数信息

**📊 统计信息**：
- 接口总数：104 个
- 系统分类：12 个
- 配置文件：4 个

---

## 📊 接口分类概览

| 系统分类 | 接口数量 | 主要功能 |
|----------|----------|----------|
| 业务支撑系统(BOSS) | 2 | 核心业务处理、预开发、订单管理 |
| 企业信息门户(EIP) | 6 | 统一待办、流程管理、单点登录 |
| 企业服务总线(ESB) | 8 | 系统集成、服务调用、数据交换 |
| 其他系统 | 44 | 其他业务系统接口 |
| 内容管理系统(CMS) | 2 | 内容管理、收入合同 |
| 合同管理系统 | 4 | 合同生成、状态管理、数据同步 |
| 客户管理系统 | 4 | 客户信息查询、集团管理 |
| 微信平台 | 2 | 微信集成、消息推送 |
| 支付系统 | 18 | 订单支付、退款处理、资金管理 |
| 电子签章系统 | 6 | 电子签名、证书管理、文档签章 |
| 短信网关 | 4 | 短信发送、消息通知、验证码 |
| 附件管理系统 | 4 | 文件上传、资料管理 |

---

## 🔍 详细接口信息

### 📦 业务支撑系统(BOSS)

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `BOSSTargetNamespace` | http://ws.sitech.com | HTTP API | 第三方接口 | WebService-config.properties |
| `BOSSTargetNamespace` | http://ws.sitech.com | HTTP API | 第三方接口 | WebService-config.properties |


### 📦 企业信息门户(EIP)

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `eipGetDayCodeKeyTargetNamespace` | http://eipsps.scmcc.com.cn/ | HTTP API | 第三方接口 | WebService-config.properties |
| `eipGetDayCodeKeyTargetNamespace` | http://eipsps.scmcc.com.cn/ | HTTP API | 第三方接口 | WebService-config.properties |
| `eipGetDayCodeKeyWsdl` | http://************/EIP.SSOAppCenterServer.WebService/SSO... | HTTP API | EIP每日认证对接口 | WebService-config.properties |
| `eipTaskTodoWsdl` | http://************/EIP.SSOAppCenterServer.WebService/Tas... | HTTP API | EIP统一待办接口 | WebService-config.properties |
| `eipTaskTodoWsdl` | http://************/EIP.SSOAppCenterServer.WebService/Tas... | HTTP API | EIP统一待办接口 | WebService-config.properties |
| `taskUrl` | http://*************:8080/EOM/systemUserAction!EipTask.ac... | HTTP API | 第三方接口 | WebService-config.properties |


### 📦 企业服务总线(ESB)

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `ESBWS_URL` | http://*************:51000/esbWS/rest/ | HTTP API | ESB正式环境地址 | FtpConfig.properties |
| `ESBWS_URL` | http://*************:51000/esbWS/rest/ | HTTP API | ESB正式环境地址 | FtpConfig.properties |
| `ESB_URL` | http://*************:51000/esbWS/rest/ | HTTP API | ESB接口地址 | WebService-config.properties |
| `ESB_URL` | http://**************:58000/esbWS/rest/ | HTTP API | ESB接口地址 | WebService-config.properties |
| `ESB_URL` | http://*************:51000/esbWS/rest/ | HTTP API | ESB接口地址 | WebService-config.properties |
| `ESB_URL` | http://**************:58000/esbWS/rest/ | HTTP API | ESB接口地址 | WebService-config.properties |
| `TEST_ESBWS_URL` | http://*************:52000/esbWS/rest/ | HTTP API | ESB测试环境地址 | FtpConfig.properties |
| `TEST_ESBWS_URL` | http://*************:52000/esbWS/rest/ | HTTP API | ESB测试环境地址 | FtpConfig.properties |


### 📦 其他系统

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `BUSINOTIFY_URL` | http://*************:8080/EOM/Payment_busiNotifyUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `BUSINOTIFY_URL` | http://*************:8080/EOM/Payment_busiNotifyUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `CANCEL_API_NOTIFY_URL` | http://*************:8080/EOM/Payment_cancelNotifyAPI.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `CANCEL_API_NOTIFY_URL` | http://*************:8080/EOM/Payment_cancelNotifyAPI.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `CANCEL_NOTIFY_URL` | http://*************:8080/EOM/Payment_cancelNotifyUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `CANCEL_NOTIFY_URL` | http://*************:8080/EOM/Payment_cancelNotifyUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `ENDPOINT_URL` | http://************:17001/services/ContractService | HTTP API | 服务接口 | FtpConfig.properties |
| `ENDPOINT_URL` | http://************:17001/services/ContractService | HTTP API | 服务接口 | FtpConfig.properties |
| `LV_LOGIN_ADDRESS` | http://**************:8080/cscwf/LoginNewServlet | HTTP API | 第三方接口 | WebService-config.properties |
| `LV_LOGIN_ADDRESS` | http://**************:8080/cscwf/LoginNewServlet | HTTP API | 第三方接口 | WebService-config.properties |
| `QRYPREDEALWSDL_URL` | http://************:10000/rest/1.0/qryPreDeal? | HTTP API | 查询预开发状态接口 | FtpConfig.properties |
| `QRYPREDEALWSDL_URL` | http://************:10000/rest/1.0/qryPreDeal? | HTTP API | 查询预开发状态接口 | FtpConfig.properties |
| `QRYRESOURCERESULT4PREORDERWSDL_URL` | http://************:10000/rest/1.0/qryResourceResult4PreO... | HTTP API | 财务移动任务接口 | FtpConfig.properties |
| `QRYRESOURCERESULT4PREORDERWSDL_URL` | http://************:10000/rest/1.0/qryResourceResult4PreO... | HTTP API | 财务移动任务接口 | FtpConfig.properties |
| `REFUNDNOTIFY_URL` | http://*************:8080/EOM/PayRefund_payRefundNotify.a... | HTTP API | 第三方接口 | FtpConfig.properties |
| `REFUNDNOTIFY_URL` | http://*************:8080/EOM/PayRefund_payRefundNotify.a... | HTTP API | 第三方接口 | FtpConfig.properties |
| `REFUNDNOTIFY_URL` | http://*************:8080/EOM/RefundOrder_saveRefundOrder... | HTTP API | 第三方接口 | FtpConfig.properties |
| `REFUND_NOTIFY_URL` | http://*************:8080/EOM/RefundOrder_saveRefundOrder... | HTTP API | 第三方接口 | WebService-config.properties |
| `REQUER_URL` | http://*************:9999/pois/ | HTTP API | 达梦接口地址 | FtpConfig.properties |
| `REQUER_URL` | http://*************:9999/pois/ | HTTP API | 达梦接口地址 | FtpConfig.properties |
| `REQ_SERVER_ADDRESS` | http://*************:21010/gather/services/AioxPort?wsdl | WebService | 审计接口服务器地址 | WebService-config.properties |
| `REQ_SERVER_ADDRESS` | http://*************:23003/gather/services/AioxPort?wsdl | WebService | 审计接口服务器地址 | WebService-config.properties |
| `REQ_SERVER_ADDRESS` | http://*************:21010/gather/services/AioxPort?wsdl | WebService | 审计接口服务器地址 | WebService-config.properties |
| `REQ_SERVER_ADDRESS` | http://*************:23003/gather/services/AioxPort?wsdl | WebService | 审计接口服务器地址 | WebService-config.properties |
| `REQ_SERVER_QN` | http://www.asiainfo.com/web/ | HTTP API | 第三方接口 | WebService-config.properties |
| `REQ_SERVER_QN` | http://www.asiainfo.com/web/ | HTTP API | 第三方接口 | WebService-config.properties |
| `S3851APPCFM_URL` | HTTP://************:10000/rest/1.0/s3851AppCfm? | HTTP API | REST API接口 | FtpConfig.properties |
| `S3851APPCFM_URL` | HTTP://************:10000/rest/1.0/s3851AppCfm? | HTTP API | REST API接口 | FtpConfig.properties |
| `STARTPREORDERWSDL_URL` | http://************:10000/rest/1.0/startPreOrder? | HTTP API | 预开发业务发起接口 | FtpConfig.properties |
| `STARTPREORDERWSDL_URL` | http://************:10000/rest/1.0/startPreOrder? | HTTP API | 预开发业务发起接口 | FtpConfig.properties |
| `UNIT_VERIFY_TRUTH_URL` | http://*************:9999/pois/check/ | HTTP API | 集团验真接口 | FtpConfig.properties |
| `UNIT_VERIFY_TRUTH_URL` | http://*************:9999/pois/check/ | HTTP API | 集团验真接口 | FtpConfig.properties |
| `appPushSoapWsdl` | http://*************:8080/portal/ws/pushMsgService?wsdl | WebService | 消息推送接口 | WebService-config.properties |
| `appPushSoapWsdl` | http://*************:8080/portal/ws/pushMsgService?wsdl | WebService | 消息推送接口 | WebService-config.properties |
| `mainAcctCheckSoapWsdl` | http://10.109.209.100:9081/uac/services/MainAcctCheckServ... | WebService | 账号密码验证接口 | WebService-config.properties |
| `mainAcctCheckSoapWsdl` | http://10.109.209.100:9081/uac/services/MainAcctCheckServ... | WebService | 账号密码验证接口 | WebService-config.properties |
| `qryPreDealWsdl` | http://*************:11800/services/qryPreDeal?wsdl | WebService | 查询预开发状态接口 | WebService-config.properties |
| `qryPreDealWsdl` | http://*************:11800/services/qryPreDeal?wsdl | WebService | 查询预开发状态接口 | WebService-config.properties |
| `smAuthenCheckSoapWsdl` | http://10.109.209.100:9081/uac/services/SmAuthenCheckServ... | WebService | 短信验证码验证接口 | WebService-config.properties |
| `smAuthenCheckSoapWsdl` | http://10.109.209.100:9081/uac/services/SmAuthenCheckServ... | WebService | 短信验证码验证接口 | WebService-config.properties |
| `wsdl` | http://10.101.11.222/FaMobileTaskApp/FinanceMobileTask.asmx | HTTP API | 财务移动任务接口 | WebService-config.properties |
| `wsdl` | http://fa.scmcc.com.cn/FaMobileTaskApp/FinanceMobileTask.... | HTTP API | 财务移动任务接口 | WebService-config.properties |
| `wsdl` | http://10.101.11.222/FaMobileTaskApp/FinanceMobileTask.asmx | HTTP API | 财务移动任务接口 | WebService-config.properties |
| `wsdl` | http://fa.scmcc.com.cn/FaMobileTaskApp/FinanceMobileTask.... | HTTP API | 财务移动任务接口 | WebService-config.properties |


### 📦 内容管理系统(CMS)

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `OSB_CMS_CMS_HQ_00013` | http://**************:8100/CMS/OSB_CMS_CMS_HQ_PageInquiry... | HTTP API | 第三方接口 | WebService-config.properties |
| `OSB_CMS_CMS_HQ_00023` | http://**************:8100/CMS/OSB_CMS_CMS_HQ_ImportReven... | HTTP API | 第三方接口 | WebService-config.properties |


### 📦 合同管理系统

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `CONTRACT_ADDERSS` | http://*************:8080/OrderSysIntoContract/Contract_I... | WebService | 合同接口数据录入接口 | WebService-config.properties |
| `CONTRACT_ADDERSS` | http://*************:8080/OrderSysIntoContract/Contract_I... | WebService | 合同接口数据录入接口 | WebService-config.properties |
| `CONTRACT_UPDATEADDERSS` | http://*************:8080/OrderSysIntoContract/Contract_S... | WebService | 合同接口数据更改接口 | WebService-config.properties |
| `CONTRACT_UPDATEADDERSS` | http://*************:8080/OrderSysIntoContract/Contract_S... | WebService | 合同接口数据更改接口 | WebService-config.properties |


### 📦 客户管理系统

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `BO_GET_SERVER_ADDRESS` | http://*************:8090/ecmServer?wsdl | WebService | 客户信息获取接口 | WebService-config.properties |
| `BO_GET_SERVER_ADDRESS` | http://*************:8090/ecmServer?wsdl | WebService | 客户信息获取接口 | WebService-config.properties |
| `customerSoapWsdl` | http://*************:8080/ecmServer?wsdl | WebService | 集团客户信息查询接口 | WebService-config.properties |
| `customerSoapWsdl` | http://*************:8080/ecmServer?wsdl | WebService | 集团客户信息查询接口 | WebService-config.properties |


### 📦 微信平台

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `WECHAT_URL` | http://**************:8080/zqwx/zq/receive/invoice | HTTP API | 微信运营平台接口 | FtpConfig.properties |
| `WECHAT_URL` | http://**************:8080/zqwx/zq/receive/invoice | HTTP API | 微信运营平台接口 | FtpConfig.properties |


### 📦 支付系统

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `APP_PAY_CENTER_API` | http://*************:8080/EOM/services/payCenterAPIWebSer... | WebService | WebService接口 | FtpConfig.properties |
| `APP_PAY_CENTER_API` | http://*************:8080/EOM/services/payCenterAPIWebSer... | WebService | WebService接口 | FtpConfig.properties |
| `APP_PAY_VOUCHER_CENTER` | http://*************:8080/EOM/services/voucherCenterWebSe... | WebService | WebService接口 | FtpConfig.properties |
| `APP_PAY_VOUCHER_CENTER` | http://*************:8080/EOM/services/voucherCenterWebSe... | WebService | WebService接口 | FtpConfig.properties |
| `PAYMENT_URL` | http://10.113.171.41:18002/ | HTTP API | 支付接口地址 | FtpConfig.properties |
| `PAYMENT_URL` | http://10.113.171.41:18002/ | HTTP API | 支付接口地址 | FtpConfig.properties |
| `PAYNOTIFY_URL` | http://*************:8080/EOM/Payment_PaymentNotify.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `PAYNOTIFY_URL` | http://*************:8080/EOM/Payment_notifyUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `PAYNOTIFY_URL` | http://*************:8080/EOM/PaymentOrder_savePaymentOrd... | HTTP API | 第三方接口 | FtpConfig.properties |
| `PAYNOTIFY_URL` | http://*************:8080/EOM/Payment_PaymentNotify.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `PAYNOTIFY_URL` | http://*************:8080/EOM/Payment_notifyUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `PAY_API_NOTIFY_URL` | http://*************:8080/EOM/Payment_notifyAPIUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `PAY_API_NOTIFY_URL` | http://*************:8080/EOM/Payment_notifyAPIUrl.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `PAY_API_URL` | http://10.113.171.139:18002/ | HTTP API | 支付中心API接口 | FtpConfig.properties |
| `PAY_API_URL` | http://10.113.171.139:18002/ | HTTP API | 支付中心API接口 | FtpConfig.properties |
| `PAY_NOTIFY_URL` | http://*************:8080/EOM/PaymentOrder_savePaymentOrd... | HTTP API | 第三方接口 | WebService-config.properties |
| `曾经调用过PAYMENT_URL` | http://*************:51000/esbWs/rest/ | HTTP API | 支付接口地址 | FtpConfig.properties |
| `曾经调用过PAYMENT_URL` | http://*************:51000/esbWs/rest/ | HTTP API | 支付接口地址 | FtpConfig.properties |


### 📦 电子签章系统

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `SIGN_DOWNLOADPDF` | http://*************:8080/EOM/EomSignAction_downloadPdf.a... | HTTP API | 第三方接口 | FtpConfig.properties |
| `SIGN_DOWNLOADPDF` | http://*************:8080/EOM/EomSignAction_downloadPdf.a... | HTTP API | 第三方接口 | FtpConfig.properties |
| `SIGN_UPLOADPDF` | http://*************:8080/EOM/EomSignAction_uploadPdf.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `SIGN_UPLOADPDF` | http://*************:8080/EOM/EomSignAction_uploadPdf.action | HTTP API | 第三方接口 | FtpConfig.properties |
| `SIGN_URL` | http://*************:9100 | HTTP API | 签章接口基础地址 | FtpConfig.properties |
| `SIGN_URL` | http://*************:9100 | HTTP API | 签章接口基础地址 | FtpConfig.properties |


### 📦 短信网关

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `sendMessageWsdl` | http://10.101.11.212/ReportEditService/ReportEditService.... | WebService | 短信网关发送接口 | WebService-config.properties |
| `sendMessageWsdl` | http://10.101.11.212/ReportEditService/ReportEditService.... | WebService | 短信网关发送接口 | WebService-config.properties |
| `sendMessageWsdl` | http://10.101.11.212/ReportEditService/ReportEditService.... | WebService | 短信网关发送接口 | DataBase-config.properties |
| `sendMessageWsdl` | http://10.101.11.212/ReportEditService/ReportEditService.... | WebService | 短信网关发送接口 | DataBase-config.properties |


### 📦 附件管理系统

| 接口名称 | 接口地址 | 接口类型 | 功能说明 | 配置文件 |
|----------|----------|----------|----------|----------|
| `ATTACHMENT_138_TEST_URL` | http://************:50080/cust | HTTP API | 第三方接口 | FtpConfig.properties |
| `ATTACHMENT_138_TEST_URL` | http://************:50080/cust | HTTP API | 第三方接口 | FtpConfig.properties |
| `ATTACHMENT_138_URL` | http://*************:8080/cust | HTTP API | 138系统资料上传接口 | FtpConfig.properties |
| `ATTACHMENT_138_URL` | http://*************:8080/cust | HTTP API | 138系统资料上传接口 | FtpConfig.properties |


## 🌐 网络拓扑信息

### 📍 主要服务器地址

| 服务器地址 | 用途说明 |
|------------|----------|
| `10.101.11.212` | 第三方系统服务器 |
| `10.101.11.222` | 第三方系统服务器 |
| `************` | 第三方系统服务器 |
| `10.109.209.100` | 4A认证系统 |
| `*************` | 第三方系统服务器 |
| `**************` | 第三方系统服务器 |
| `*************` | EOM系统主服务器 |
| `*************` | 第三方系统服务器 |
| `**************` | 第三方系统服务器 |
| `10.113.171.139` | 支付中心(测试) |
| `*************` | ESB服务总线 |
| `10.113.171.41` | 支付中心(生产) |
| `*************` | 第三方系统服务器 |
| `*************` | 第三方系统服务器 |
| `*************` | 消息推送系统 |
| `*************` | 客户管理系统 |
| `************` | 第三方系统服务器 |
| `*************` | 电子签章系统 |
| `*************` | 第三方系统服务器 |
| `**************` | 第三方系统服务器 |
| `*************` | BOSS业务系统 |
| `************` | 第三方系统服务器 |
| `************` | 第三方系统服务器 |
| `**************` | CMS内容管理系统 |
| `eipsps.scmcc.com.cn` | 第三方系统服务器 |
| `fa.scmcc.com.cn` | 财务移动任务系统 |
| `ws.sitech.com` | 第三方系统服务器 |
| `www.asiainfo.com` | 第三方系统服务器 |

## 🔐 安全配置信息

### 🔑 认证配置

| 系统 | 认证方式 | 配置说明 |
|------|----------|----------|
| 4A系统 | Token认证 | SERVICEID、加密账号密码 |
| 支付系统 | 商户认证 | 商户号、加密KEY |
| 电子签章 | OAuth2 | 渠道编码、访问令牌 |
| FTP服务 | 用户密码 | 用户名、密码认证 |
| 大数据平台 | Kerberos | 用户名认证 |

### 🔒 加密信息

- **支付系统加密KEY**: `go2ve0rn1me9nt1an0de2nt3er3prise`
- **4A系统账号**: 加密存储
- **FTP密码**: 明文配置(建议加密)

---

## 📝 维护建议

### 🔄 定期检查项目

1. **接口可用性**: 定期检查接口连通性
2. **认证有效性**: 检查Token、证书有效期
3. **配置安全性**: 敏感信息加密存储
4. **版本兼容性**: 关注第三方系统版本变更

### 🚨 风险提示

1. **单点故障**: 关键接口需要备用方案
2. **网络依赖**: 网络中断影响业务连续性
3. **安全风险**: 明文密码存在安全隐患
4. **版本风险**: 第三方系统升级可能影响兼容性

---

*本文档基于配置文件自动生成，如有变更请及时更新配置并重新生成文档。*