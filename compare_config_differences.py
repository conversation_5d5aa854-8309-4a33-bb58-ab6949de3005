#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
对比config目录中85和86两个子目录的配置文件差异
"""

import os
import difflib
from collections import defaultdict

def read_file_content(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.readlines()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return f.readlines()
        except Exception as e:
            return [f"读取文件失败: {e}\n"]
    except Exception as e:
        return [f"读取文件失败: {e}\n"]

def parse_properties_file(file_path):
    """解析properties文件为键值对"""
    properties = {}
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    properties[key.strip()] = {
                        'value': value.strip(),
                        'line': line_num
                    }
    except Exception as e:
        print(f"解析properties文件 {file_path} 失败: {e}")
    
    return properties

def compare_properties_files(file1_path, file2_path):
    """对比两个properties文件"""
    props1 = parse_properties_file(file1_path)
    props2 = parse_properties_file(file2_path)
    
    differences = {
        'only_in_85': {},
        'only_in_86': {},
        'different_values': {},
        'same_values': {}
    }
    
    all_keys = set(props1.keys()) | set(props2.keys())
    
    for key in all_keys:
        if key in props1 and key in props2:
            if props1[key]['value'] != props2[key]['value']:
                differences['different_values'][key] = {
                    '85': props1[key]['value'],
                    '86': props2[key]['value']
                }
            else:
                differences['same_values'][key] = props1[key]['value']
        elif key in props1:
            differences['only_in_85'][key] = props1[key]['value']
        else:
            differences['only_in_86'][key] = props2[key]['value']
    
    return differences

def compare_text_files(file1_path, file2_path):
    """对比两个文本文件"""
    content1 = read_file_content(file1_path)
    content2 = read_file_content(file2_path)
    
    # 生成差异
    diff = list(difflib.unified_diff(
        content1, content2,
        fromfile=f"85/{os.path.basename(file1_path)}",
        tofile=f"86/{os.path.basename(file2_path)}",
        lineterm=''
    ))
    
    return diff

def analyze_config_differences():
    """分析配置文件差异"""
    
    config_85_dir = "D:/Code/svn/EOM/config/85"
    config_86_dir = "D:/Code/svn/EOM/config/86"
    
    # 获取配置文件列表
    config_files = [
        'FtpConfig.properties',
        'WebService-config.properties',
        'applicationContext-action.xml',
        'applicationContext-jpbm.xml',
        'applicationContext-manager.xml',
        'struts.xml'
    ]
    
    doc_content = []
    
    # 文档头部
    doc_content.extend([
        "# EOM系统配置文件差异对比报告",
        "",
        "## 📋 对比说明",
        "",
        "本报告对比了config目录下85和86两个子目录中的配置文件差异：",
        "- **85目录**: 可能是85服务器的配置",
        "- **86目录**: 可能是86服务器的配置",
        "",
        f"**对比文件数量**: {len(config_files)} 个",
        "",
        "---",
        ""
    ])
    
    # 对比每个配置文件
    total_differences = 0
    
    for config_file in config_files:
        file1_path = os.path.join(config_85_dir, config_file)
        file2_path = os.path.join(config_86_dir, config_file)
        
        if not os.path.exists(file1_path) or not os.path.exists(file2_path):
            doc_content.extend([
                f"## ❌ {config_file}",
                "",
                "**状态**: 文件缺失",
                f"- 85目录: {'存在' if os.path.exists(file1_path) else '缺失'}",
                f"- 86目录: {'存在' if os.path.exists(file2_path) else '缺失'}",
                "",
                "---",
                ""
            ])
            continue
        
        print(f"对比文件: {config_file}")
        
        if config_file.endswith('.properties'):
            # Properties文件特殊处理
            differences = compare_properties_files(file1_path, file2_path)
            
            has_diff = (differences['only_in_85'] or 
                       differences['only_in_86'] or 
                       differences['different_values'])
            
            if has_diff:
                total_differences += 1
                doc_content.extend([
                    f"## 🔍 {config_file}",
                    "",
                    "**文件类型**: Properties配置文件",
                    f"**差异状态**: {'有差异' if has_diff else '无差异'}",
                    ""
                ])
                
                # 仅在85中存在的配置
                if differences['only_in_85']:
                    doc_content.extend([
                        "### 📌 仅在85中存在的配置",
                        "",
                        "| 配置项 | 值 |",
                        "|--------|-----|"
                    ])
                    for key, value in differences['only_in_85'].items():
                        doc_content.append(f"| `{key}` | `{value}` |")
                    doc_content.append("")
                
                # 仅在86中存在的配置
                if differences['only_in_86']:
                    doc_content.extend([
                        "### 📌 仅在86中存在的配置",
                        "",
                        "| 配置项 | 值 |",
                        "|--------|-----|"
                    ])
                    for key, value in differences['only_in_86'].items():
                        doc_content.append(f"| `{key}` | `{value}` |")
                    doc_content.append("")
                
                # 值不同的配置
                if differences['different_values']:
                    doc_content.extend([
                        "### 🔄 值不同的配置",
                        "",
                        "| 配置项 | 85目录值 | 86目录值 |",
                        "|--------|----------|----------|"
                    ])
                    for key, values in differences['different_values'].items():
                        doc_content.append(f"| `{key}` | `{values['85']}` | `{values['86']}` |")
                    doc_content.append("")
                
                # 统计信息
                doc_content.extend([
                    "### 📊 统计信息",
                    "",
                    f"- 仅在85中: {len(differences['only_in_85'])} 项",
                    f"- 仅在86中: {len(differences['only_in_86'])} 项", 
                    f"- 值不同: {len(differences['different_values'])} 项",
                    f"- 相同配置: {len(differences['same_values'])} 项",
                    ""
                ])
            else:
                doc_content.extend([
                    f"## ✅ {config_file}",
                    "",
                    "**文件类型**: Properties配置文件",
                    "**差异状态**: 无差异",
                    f"**相同配置项**: {len(differences['same_values'])} 个",
                    ""
                ])
        
        else:
            # XML文件等其他文件
            diff = compare_text_files(file1_path, file2_path)
            
            if diff:
                total_differences += 1
                doc_content.extend([
                    f"## 🔍 {config_file}",
                    "",
                    "**文件类型**: XML配置文件",
                    "**差异状态**: 有差异",
                    "",
                    "### 📝 详细差异",
                    "",
                    "```diff"
                ])
                
                # 只显示前50行差异，避免文档过长
                for i, line in enumerate(diff[:50]):
                    doc_content.append(line.rstrip())
                
                if len(diff) > 50:
                    doc_content.append(f"... (还有 {len(diff) - 50} 行差异)")
                
                doc_content.extend([
                    "```",
                    ""
                ])
            else:
                doc_content.extend([
                    f"## ✅ {config_file}",
                    "",
                    "**文件类型**: XML配置文件", 
                    "**差异状态**: 无差异",
                    ""
                ])
        
        doc_content.extend(["---", ""])
    
    # 总结
    doc_content.extend([
        "## 📊 对比总结",
        "",
        f"### 📈 统计信息",
        f"- **对比文件总数**: {len(config_files)} 个",
        f"- **有差异文件**: {total_differences} 个",
        f"- **无差异文件**: {len(config_files) - total_differences} 个",
        f"- **差异比例**: {total_differences/len(config_files)*100:.1f}%",
        "",
        "### 🎯 主要差异类型",
        "",
        "根据对比结果，主要差异可能包括：",
        "1. **服务器地址差异**: 不同服务器的IP地址配置",
        "2. **端口配置差异**: 服务端口的不同设置", 
        "3. **环境配置差异**: 开发/测试/生产环境的区别",
        "4. **数据库连接差异**: 不同数据库实例的连接配置",
        "5. **第三方接口差异**: 外部系统接口地址的不同",
        "",
        "### 💡 建议",
        "",
        "1. **配置标准化**: 建议统一两套环境的配置格式",
        "2. **环境隔离**: 确保测试和生产环境的配置正确隔离",
        "3. **配置管理**: 建立配置文件版本管理机制",
        "4. **自动化部署**: 使用配置模板减少手工配置错误",
        "",
        "---",
        "",
        "*本报告由自动化脚本生成，详细展示了85和86两套配置的所有差异。*"
    ])
    
    # 写入文档
    output_file = 'EOM配置文件差异对比报告.md'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(doc_content))
    
    print(f"\n✅ 配置文件差异对比完成！")
    print(f"📄 报告文件：{output_file}")
    print(f"📊 对比文件：{len(config_files)} 个")
    print(f"🔍 有差异文件：{total_differences} 个")
    print(f"✅ 无差异文件：{len(config_files) - total_differences} 个")

if __name__ == "__main__":
    analyze_config_differences()
