/*
Bootstrap_Search_Suggest - v0.0.2
Description: 这是一个基于 bootstrap 按钮式下拉菜单组件的搜索建议插件，必须使用于按钮式下拉菜单组件上。
Author: lzwme <<EMAIL>>
Github: https://github.com/lzwme/bootstrap-suggest-plugin
Update: 2016-02-22 16:47:00
*/
!function(a){function b(a,b){console.trace(a),b&&console.trace(b)}function c(a){var b={};return b.id=a.attr("data-id"),b.key=a.attr("data-key"),b.index=a.attr("data-index"),b}function d(a,b,c){var d,e=b||{},f=e.id||"",g=e.key||"";c&&c.multiWord?(d=a.val().split(c.separator||" "),d[d.length-1]=g,a.val(d.join(c.separator||" ")).focus()):a.attr("data-id",f).focus().val(g),a.trigger("onSetSelectValue",[e,(c.data.value||c.result.value)[e.index]])}function e(b,c,d){c.is(":visible")&&setTimeout(function(){d.autoDropup&&a(window).height()-b.offset().top<c.height()&&b.offset().top>c.height()+a(window).scrollTop()?c.parents(".input-group").addClass("dropup"):c.parents(".input-group.dropup").removeClass("dropup")},100);var e={};return"left"===d.listAlign?e={left:b.siblings("div").width()-b.parent().width(),right:"auto"}:"right"===d.listAlign&&(e={left:"auto",right:"0"}),d.autoMinWidth===!1&&(e["min-width"]=b.parent().width()),c.css(e),b}function f(a,b){var c,d,e;return-1===b.indexId&&!b.idField||b.multiWord?a:(c=a.css("background-color").replace(/ /g,"").split(",",3).join(","),d=b.inputBgColor||"rgba(255,255,255,0.1)",e=b.inputWarnColor||"rgba(255,255,0,0.1)",!a.val()||a.attr("data-id")?a.css("background",d):(-1===e.indexOf(c)&&(a.trigger("onUnsetSelectValue"),a.css("background",e)),a))}function g(a,b,c){var d,e,f=a.parent().find("tbody tr."+c.listHoverCSS);f.length>0&&(d=(f.index()+3)*f.height(),e=Number(b.css("max-height").replace("px","")),b.scrollTop(d>e||b.scrollTop()>e?d-e:0))}function h(a,b){a.find("tr."+b.listHoverCSS).removeClass(b.listHoverCSS)}function i(b,c){var d=a(b),e=d.parent(".input-group").find("ul.dropdown-menu"),f=d.data("bsSuggest");return 0===e.length?!1:f?!1:(d.data("bsSuggest",{target:b,options:c}),!0)}function j(a){var c=!0;for(var d in a)if("value"===d){c=!1;break}return c?(b("返回数据格式错误!"),!1):0===a.value.length?!1:a}function k(b,c){return"__index"===b||a.isArray(c.effectiveFields)&&c.effectiveFields.length>0&&-1===a.inArray(b,c.effectiveFields)?!1:!0}function l(b,c){return-1!==a.inArray(b,c.searchFields)?!0:!1}function m(b,e,g){e.find("tbody tr").each(function(){a(this).off("mouseenter").on("mouseenter",function(){h(e,g),a(this).addClass(g.listHoverCSS)}).off("mousedown").on("mousedown",function(){d(b,c(a(this)),g),f(b,g)})})}function n(a,b,c){var d,f,g,h,i,j,l,n=a.parent().find("ul.dropdown-menu"),o=0,p=['<table class="table table-condensed table-sm">'];if(b=c.processData(b),b===!1||0===(d=b.value.length))return n.empty().hide(),a;if(c.showHeader){h="<thead><tr>";for(g in b.value[0])k(g,c)!==!1&&(h+=0===o?"<th>"+(c.effectiveFieldsAlias[g]||g)+"("+d+")</th>":"<th>"+(c.effectiveFieldsAlias[g]||g)+"</th>",o++);h+="</tr></thead>",p.push(h)}for(p.push("<tbody>"),f=0;d>f;f++){o=0,i="",j=b.value[f][c.idField]||"",l=b.value[f][c.keyField]||"";for(g in b.value[f])l||c.indexKey!==o||(l=b.value[f][g]),j||c.indexId!==o||(j=b.value[f][g]),o++,k(g,c)!==!1&&(i+='<td data-name="'+g+'">'+b.value[f][g]+"</td>");i='<tr data-index="'+(b.value[f].__index||f)+'" data-id="'+j+'" data-key="'+l+'">'+i+"</tr>",p.push(i)}return p.push("</tbody></table>"),n.html(p.join("")).show(),m(a,n,c),n.css("max-height")&&Number(n.css("max-height").replace("px",""))<Number(n.find("table:eq(0)").css("height").replace("px",""))&&Number(n.css("min-width").replace("px",""))<Number(n.css("width").replace("px",""))?n.css("padding-right","20px").find("table:eq(0)").css("margin-bottom","20px"):n.css("padding-right",0).find("table:eq(0)").css("margin-bottom",0),e(a,n,c),a}function o(c,d,e,f){var g,h,i,m,n,o,p,q={value:[]};if(c=c||"",f.url)n=-1!==f.url.indexOf("?")?"&":"?",o=f.jsonp?[f.url+c,n,f.jsonp,"=?"].join(""):f.url+c,a.ajax({type:"GET",url:o,dataType:"json",timeout:3e3}).done(function(a){e(d,a,f),d.trigger("onDataRequestSuccess",a),"firstByUrl"===f.getDataMethod?(f.data=a,f.url=null):f.result=f.processData(a)}).fail(b);else{if(g=f.data,h=j(g))if(c){for(p=g.value.length,i=0;p>i;i++)for(m in g.value[i])if(a.trim(g.value[i][m])&&(l(m,f)||k(m,f))&&(-1!==g.value[i][m].toString().indexOf(c)||-1!==c.indexOf(g.value[i][m]))){q.value.push(g.value[i]),q.value[q.value.length-1].__index=i;break}}else q=g;e(d,q,f)}}function p(a){return j(a)}var q={init:function(b){var j=this,k=a.extend({url:null,jsonp:null,data:{},getDataMethod:"firstByUrl",delayUntilKeyup:!1,indexId:0,indexKey:0,idField:"",keyField:"",effectiveFields:[],effectiveFieldsAlias:{},searchFields:[],showHeader:!1,showBtn:!0,allowNoKeyword:!0,multiWord:!1,separator:",",processData:p,getData:o,autoMinWidth:!1,autoDropup:!1,autoSelect:!0,listAlign:"left",inputBgColor:"",inputWarnColor:"rgba(255,0,0,.1)",listStyle:{"padding-top":0,"max-height":"375px","max-width":"800px",overflow:"auto",width:"auto",transition:"0.3s","-webkit-transition":"0.3s","-moz-transition":"0.3s","-o-transition":"0.3s"},listHoverStyle:"background: #07d; color:#fff",listHoverCSS:"jhover",keyLeft:37,keyUp:38,keyRight:39,keyDown:40,keyEnter:13},b);if(!b.showHeader&&k.effectiveFields&&k.effectiveFields.length>1&&(k.showHeader=!0),"firstByUrl"===k.getDataMethod&&k.url&&!k.delayUntilKeyup){var l=-1!==b.url.indexOf("?")?"&":"?",m=b.jsonp?[b.url,l,b.jsonp,"=?"].join(""):b.url;a.ajax({type:"GET",url:m,dataType:"json",timeout:5e3}).done(function(b){k.data=b,k.url=null,a(j).trigger("onDataRequestSuccess",b)}).fail(function(a,b){console.error(m+" : "+b)})}return a("head:eq(0)").append("<style>."+k.listHoverCSS+"{"+k.listHoverStyle+"}</style>"),j.each(function(){var j=a(this),l=j.parents(".input-group:eq(0)").find("ul.dropdown-menu");return i(this,k)===!1?void console.warn("不是一个标准的 bootstrap 下拉式菜单:",this):(k.showBtn||j.css("border-radius","4px").parents(".input-group:eq(0)").css("width","100%").find(".input-group-btn>.btn").hide(),j.removeClass("disabled").attr("disabled",!1).attr("autocomplete","off"),l.css(k.listStyle),k.inputBgColor||(k.inputBgColor=j.css("background-color")),j.on("keydown",function(b){var e,f="";if("none"!==l.css("display")){if(e=l.find("."+k.listHoverCSS),f="",b.keyCode===k.keyDown){if(0===e.length?f=c(l.find("table tbody tr:first").mouseover()):0===e.next().length?(h(l,k),k.autoSelect&&a(this).val(a(this).attr("alt")).attr("data-id","")):(h(l,k),0!==e.next().length&&(f=c(e.next().mouseover()))),g(j,l,k),!k.autoSelect)return}else if(b.keyCode===k.keyUp){if(0===e.length?f=c(l.find("table tbody tr:last").mouseover()):0===e.prev().length?(h(l,k),k.autoSelect&&a(this).val(a(this).attr("alt")).attr("data-id","")):(h(l,k),0!==e.prev().length&&(f=c(e.prev().mouseover()))),g(j,l,k),!k.autoSelect)return}else b.keyCode===k.keyEnter?(f=c(e),l.hide().empty()):a(this).attr("data-id","");f&&""!==f.key&&d(a(this),f,k)}}).on("keyup",function(c){var d,e;return c.keyCode===k.keyDown||c.keyCode===k.keyUp||c.keyCode===k.keyEnter?(a(this).val(a(this).val()),void f(j,k)):(a(this).attr("data-id",""),f(j,k),d=a(this).val(),void((""===a.trim(d)||d!==a(this).attr("alt"))&&(a(this).attr("alt",a(this).val()),b.multiWord&&(e=d.split(k.separator||" "),d=e[e.length-1]),(0!==d.length||k.allowNoKeyword)&&k.getData(a.trim(d),j,n,k))))}).on("focus",function(){e(j,l,k)}).on("blur",function(){l.css("display","")}).on("click",function(){var b,c=a(this).val();return""!==a.trim(c)&&c===a(this).attr("alt")&&l.find("table tr").length?l.show():void("none"===l.css("display")&&(k.multiWord&&(b=c.split(k.separator||" "),c=b[b.length-1]),(0!==c.length||k.allowNoKeyword)&&k.getData(a.trim(c),j,n,k)))}),j.parent().find("button:eq(0)").attr("data-toggle","").on("click",function(){var a;"none"===l.css("display")?(a="block",k.url?j.click().focus():(n(j,k.data,k),e(j,l,k))):a="none",l.css("display",a)}),void l.on("mouseenter",function(){}).on("mouseleave",function(){j.focus()}))})},show:function(){var a=this.data("bsSuggest");return a&&a.options&&this.parent().find("ul.dropdown-menu").show(),this},hide:function(){var a=this.data("bsSuggest");return a&&a.options&&this.parent().find("ul.dropdown-menu").css("display",""),this},disable:function(){return a(this).data("bsSuggest")?void a(this).attr("disabled",!0).parent().find(".input-group-btn>.btn").addClass("disabled"):!1},enable:function(){return a(this).data("bsSuggest")?void a(this).attr("disabled",!1).parent().find(".input-group-btn>.btn").removeClass("disabled"):!1},destroy:function(){a(this).off().removeData("bsSuggest").parent().find(".input-group-btn>.btn").off()},version:function(){return"0.0.1"}};a.fn.bsSuggest=function(a){return"string"==typeof a&&q[a]?q[a].apply(this,Array.prototype.slice.call(arguments,1)):"object"!=typeof a&&a?void 0:q.init.apply(this,arguments)}}(window.jQuery);