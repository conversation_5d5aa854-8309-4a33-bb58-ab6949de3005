/*左侧导航*/
(function ($)
{
    $.fn.portalAccordion = function (options)
    {
        return $.portalui.run.call(this, "portalAccordion", arguments);
    };

    $.fn.portalGetAccordionManager = function ()
    {
        return $.portalui.get(this);
    };

    $.portalDefaults.Accordion = {
        showBorder:false,//是否显示边框
        width:150,
        height:0,   //组件高度，0表示自适应
        itemPadding:'15px 0px 15px 0px'
    };
    $.portalMethos.Accordion = {};

    $.portalui.controls.Accordion = function (element, options)
    {
        $.portalui.controls.Accordion.base.constructor.call(this, element, options);
    };
    $.portalui.controls.Accordion.portalExtend($.portalui.core.UIComponent, {
        __getType: function ()
        {
            return 'Accordion';
        },
        __idPrev: function ()
        {
            return 'Accordion';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.Accordion;
        },
        _render: function ()
        {
            var g = this, p = this.options;
            g.Accordion = $(g.element);

            var itms = $('<div class="m-accordion-item"></div>');

            var tit = g.Accordion.children();
            var title;

            if(tit.attr('img') != '' && tit.attr('img') != undefined){
                title =  $('<h2 class="m-accordion-title"></h2>')

                title.css('background','url(' + tit.attr('img') + ')');
                title.css('background-repeat','no-repeat');
                title.css('background-position','-6px 10px');
            }else{
                title =  $('<h2 class="m-accordion-title">' + tit.attr('title') + '</h2>');
            }

            var lis = tit.children().children();

            $.each(lis,function(i,__li){
                var i;
                if($(__li).attr('on') == 'true' || $(__li).attr('on'))
                    i = $('<a class="on" href="' + $(__li).attr('url') + '">' + __li.innerHTML + '</a>')
                else
                    i = $('<a href="' + $(__li).attr('url') + '">' + __li.innerHTML + '</a>')
                itms.append(i);
            })

            g.Accordion.addClass('m-accordion-panel');
            if(p.height != 0){
                g.Accordion.css('height', p.height + 'px');
                g.Accordion.css('width', p.width + 'px');

                g.Accordion.css('overflow-x', 'hidden');
                g.Accordion.css('overflow-y', 'auto');
            }
            g.Accordion.empty();
            g.Accordion.append(title);
            g.Accordion.append(itms);
        },
        setHeight: function (value){
            var g = this, p = this.options;
            if(value == 0){
                g.Accordion.css('height','auto');
                g.Accordion.css('overflow-y', 'hidden');
            }else{
                g.Accordion.css('height', value + 'px');
                g.Accordion.css('width', p.width + 'px');

                g.Accordion.css('overflow-x', 'hidden');
                g.Accordion.css('overflow-y', 'auto');
            }

        }
    });

})(jQuery);