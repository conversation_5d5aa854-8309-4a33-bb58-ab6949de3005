/*复选框*/
(function ($)
{
    $.fn.portalCheckBox = function (options)
    {
        return $.portalui.run.call(this, "portalCheckBox", arguments);
    };
    $.fn.portalGetCheckBoxManager = function ()
    {
        return $.portalui.run.call(this, "portalGetCheckBoxManager", arguments);
    };
    $.portalDefaults.CheckBox = {
        disabled: false,
        readonly : false //只读
    };

    $.portalMethos.CheckBox = {};

    $.portalui.controls.CheckBox = function (element, options)
    {
        $.portalui.controls.CheckBox.base.constructor.call(this, element, options);
    };
    $.portalui.controls.CheckBox.portalExtend($.portalui.controls.Input, {
        __getType: function ()
        {
            return 'CheckBox';
        },
        __idPrev: function ()
        {
            return 'CheckBox';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.CheckBox;
        },
        _render: function ()
        {
            var g = this, p = this.options;
            g.input = $(g.element);
            g.link = $('<a class="m-checkbox"></a>');
            g.wrapper = g.input.addClass('m-hidden').wrap('<div class="m-checkbox-wrapper"></div>').parent();
            g.wrapper.prepend(g.link);
            g.link.click(function ()
            {
                if (g.input.attr('disabled') || g.input.attr('readonly')) { return false; }
                if (p.disabled || p.readonly) return false;
                if (g.trigger('beforeClick', [g.element]) == false) return false; 
                if ($(this).hasClass("m-checkbox-checked"))
                {
                    g._setValue(false);
                }
                else
                {
                    g._setValue(true);
                }
                g.input.trigger("change");
            });
            g.wrapper.hover(function ()
            {
                if (!p.disabled)
                    $(this).addClass("m-over");
            }, function ()
            {
                $(this).removeClass("m-over");
            });
            this.set(p);
            this.updateStyle();
        },
        _setCss: function (value)
        {
            this.wrapper.css(value);
        },
        _setValue: function (value)
        {
            var g = this, p = this.options;
            if (!value)
            {
                g.input[0].checked = false;
                g.link.removeClass('m-checkbox-checked');
            }
            else
            {
                g.input[0].checked = true;
                g.link.addClass('m-checkbox-checked');
            }
        },
        _setDisabled: function (value)
        {
            if (value)
            {
                this.input.attr('disabled', true);
                this.wrapper.addClass("m-disabled");
            }
            else
            {
                this.input.attr('disabled', false);
                this.wrapper.removeClass("m-disabled");
            }
        },
        _getValue: function ()
        {
            return this.element.checked;
        },
        updateStyle: function ()
        {
            if (this.input.attr('disabled'))
            {
                this.wrapper.addClass("m-disabled");
                this.options.disabled = true;
            }
            if (this.input[0].checked)
            {
                this.link.addClass('m-checkbox-checked');
            }
            else
            {
                this.link.removeClass('m-checkbox-checked');
            }
        }
    });
})(jQuery);