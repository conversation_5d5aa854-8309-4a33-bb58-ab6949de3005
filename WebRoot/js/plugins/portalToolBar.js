/*工具栏*/
(function ($)
{

    $.fn.portalToolBar = function (options)
    {
        return $.portalui.run.call(this, "portalToolBar", arguments);
    };

    $.fn.portalGetToolBarManager = function ()
    {
        return $.portalui.run.call(this, "portalGetToolBarManager", arguments);
    };

    $.portalDefaults.ToolBar = {};

    $.portalMethos.ToolBar = {};

    $.portalui.controls.ToolBar = function (element, options)
    {
        $.portalui.controls.ToolBar.base.constructor.call(this, element, options);
    };
    $.portalui.controls.ToolBar.portalExtend($.portalui.core.UIComponent, {
        __getType: function ()
        {
            return 'ToolBar';
        },
        __idPrev: function ()
        {
            return 'ToolBar';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.ToolBar;
        },
        _render: function ()
        {
            var g = this, p = this.options;
			g.toolbarItemCount = 0;
            g.toolBar = $(this.element);
            g.toolBar.addClass("m-toolbar");
            g.set(p);
        },
        _setItems: function (items)
        {
            var g = this;
            g.toolBar.html("");
            $(items).each(function (i, item)
            {
                g.addItem(item);
            });
        },
		removeItem: function (itemid)
        {
            var g = this, p = this.options;
            $("> .m-toolbar-item[toolbarid=" + itemid + "]", g.toolBar).remove();
        },
        setEnabled: function (itemid)
        {
            var g = this, p = this.options;
            $("> .m-toolbar-item[toolbarid=" + itemid + "]", g.toolBar).removeClass("m-toolbar-item-disable");
        },
        setDisabled: function (itemid)
        {
            var g = this, p = this.options;
            $("> .m-toolbar-item[toolbarid=" + itemid + "]", g.toolBar).addClass("m-toolbar-item-disable");
        },
        isEnable: function (itemid)
        {
            var g = this, p = this.options;
            return !$("> .m-toolbar-item[toolbarid=" + itemid + "]", g.toolBar).hasClass("m-toolbar-item-disable");
        },
        addItem: function (item)
        {
            var g = this, p = this.options;
            if (item.line || item.type == "line")
            {
                g.toolBar.append('<div class="m-toolbar-separator"></div>');
                return;
            }
            if (item.type == "text")
            {
                g.toolBar.append('<div class="m-toolbar-item m-toolbar-text"><span>' + item.text || "" + '</span></div>');
                return;
            }
            var ditem = $('<div class="m-toolbar-item m-toolbar-panel-btn"><span></span><!--<div class="m-toolbar-panel-btn-l"></div><div class="m-toolbar-panel-btn-r"></div></div>-->');
            g.toolBar.append(ditem);
            if(!item.id) item.id = 'item-'+(++g.toolbarItemCount);
			ditem.attr("toolbarid", item.id);
            if (item.img)
            {
                ditem.append("<img src='" + item.img + "' />");
                ditem.addClass("m-toolbar-item-hasicon");
            }
            else if (item.icon)
            {
                ditem.append("<div class='m-icon m-icon-" + item.icon + "'></div>");
                ditem.addClass("m-toolbar-item-hasicon");
            }
			else if (item.color)
			{
				ditem.append("<div class='m-toolbar-item-color' style='background:"+item.color+"'></div>");
                ditem.addClass("m-toolbar-item-hasicon");
			}
            item.text && $("span:first", ditem).html(item.text);
            item.disable && ditem.addClass("m-toolbar-item-disable");
            item.click && ditem.click(function () { if ($(this).hasClass("m-toolbar-item-disable")) return;item.click(item); });
			if (item.menu)
            {
                item.menu = $.portalMenu(item.menu);
                ditem.hover(function ()
                {
					if ($(this).hasClass("m-toolbar-item-disable")) return;
                    g.actionMenu && g.actionMenu.hide();
                    var left = $(this).offset().left;
                    var top = $(this).offset().top + $(this).height();
                    item.menu.show({ top: top, left: left });
                    g.actionMenu = item.menu;
                    $(this).addClass("m-toolbar-item-over");
                }, function ()
                {
					if ($(this).hasClass("m-toolbar-item-disable")) return;
                    $(this).removeClass("m-toolbar-item-over");
                });
            }
            else
            {
                ditem.hover(function ()
				{
					if ($(this).hasClass("m-toolbar-item-disable")) return;
					$(this).addClass("m-toolbar-item-over");
				}, function ()
				{
					if ($(this).hasClass("m-toolbar-item-disable")) return;
					$(this).removeClass("m-toolbar-item-over");
				});
            }
        }
    });
	//旧写法保留
    $.portalui.controls.ToolBar.prototype.setEnable = $.portalui.controls.ToolBar.prototype.setEnabled;
    $.portalui.controls.ToolBar.prototype.setDisable = $.portalui.controls.ToolBar.prototype.setDisabled;
})(jQuery);