/*下拉框*/
(function ($)
{

    $.fn.portalComboBox = function (options)
{
        return $.portalui.run.call(this, "portalComboBox", arguments);
    };

    $.fn.portalGetComboBoxManager = function ()
{
        return $.portalui.run.call(this, "portalGetComboBoxManager", arguments);
    };

    $.portalDefaults.ComboBox = {
        resize: true,           //是否调整大小
        isMultiSelect: false,   //是否多选
        isShowCheckBox: false,  //是否选择复选框
        columns: null,       //表格状态
        selectBoxWidth: null, //宽度
        selectBoxHeight: 120, //高度
        onBeforeSelect: false, //选择前事件
        onSelected: null, //选择值事件 
        initValue: null,
        initText: null,
        valueField: 'id',
        textField: 'text',
        valueFieldID: null,
        slide: false,           //是否以动画的形式显示
        split: ";",
        data: null,
        tree: null,            //下拉框以树的形式显示，tree的参数跟portalTree的参数一致 
        treeLeafOnly: true,   //是否只选择叶子
        condition: null,       //列表条件搜索 参数同 portalForm
        grid: null,              //表格 参数同 portalGrid
        onStartResize: null,
        onEndResize: null,
        hideOnLoseFocus: true,
        hideGridOnLoseFocus: false,
        url: null,              //数据源URL(需返回JSON)
        emptyText: null,       //空行
        addRowButton: '新增',           //新增按钮
        addRowButtonClick: null,        //新增事件
        triggerIcon: null,         //
        onSuccess: null,
        onError: null,
        onBeforeOpen: null,      //打开下拉框前事件，可以通过return false来阻止继续操作，利用这个参数可以用来调用其他函数，比如打开一个新窗口来选择值
        onButtonClick: null,      //右侧图标按钮事件，可以通过return false来阻止继续操作，利用这个参数可以用来调用其他函数，比如打开一个新窗口来选择值
        render: null,            //文本框显示html函数
        absolute: true,         //选择框是否在附加到body,并绝对定位
        cancelable: true,      //可取消选择
        css: null,            //附加css
        parms: null,         //ajax提交表单 
        renderItem: null,   //选项自定义函数
        autocomplete: false,  //自动完成 
        highLight: false,    //自动完成是否匹配字符高亮显示
        readonly: false,              //是否只读
        ajaxType: 'post',
        alwayShowInTop: false,      //下拉框是否一直显示在上方
        valueFieldCssClass: null
    };

    $.portalDefaults.ComboBoxString = {
        Search: "搜索"
    };

    //扩展方法
    $.portalMethos.ComboBox = $.portalMethos.ComboBox || {};


    $.portalui.controls.ComboBox = function (element, options)
{
        $.portalui.controls.ComboBox.base.constructor.call(this, element, options);
    };
    $.portalui.controls.ComboBox.portalExtend($.portalui.controls.Input, {
        __getType: function ()
{
            return 'ComboBox';
        },
        _extendMethods: function ()
{
            return $.portalMethos.ComboBox;
        },
        _init: function ()
{
            $.portalui.controls.ComboBox.base._init.call(this);
            var p = this.options;
            if (p.columns)
{
                p.isShowCheckBox = true;
            }
            if (p.isMultiSelect)
{
                p.isShowCheckBox = true;
            }
        },
        _render: function ()
{
            var g = this, p = this.options;
            g.data = p.data;
            g.inputText = null;
            g.select = null;
            g.textFieldID = "";
            g.valueFieldID = "";
            g.valueField = null; //隐藏域(保存值) 
            //文本框初始化
            if (this.element.tagName.toLowerCase() == "input")
{
                this.element.readOnly = true;
                g.inputText = $(this.element);
                g.textFieldID = this.element.id;
            }
            else if (this.element.tagName.toLowerCase() == "select")
{
                $(this.element).hide();
                g.select = $(this.element);
                p.isMultiSelect = false;
                p.isShowCheckBox = false;
                p.cancelable = false;
                g.textFieldID = this.element.id + "_txt";
                g.inputText = $('<input type="text" readonly="true"/>');
                g.inputText.attr("id", g.textFieldID).insertAfter($(this.element));
                g.inputText.attr("name", g.textFieldID).insertAfter($(this.element));
            }
            if (g.inputText[0].name == undefined) g.inputText[0].name = g.textFieldID;
            //隐藏域初始化
            g.valueField = null;
            if (p.valueFieldID)
{
                g.valueField = $("#" + p.valueFieldID + ":input,[name=" + p.valueFieldID + "]:input").filter("input:hidden");
                if (g.valueField.length == 0) g.valueField = $('<input type="hidden"/>');
                g.valueField[0].id = g.valueField[0].name = p.valueFieldID;
            }
            else
{
                g.valueField = $('<input type="hidden"/>');
                g.valueField[0].id = g.valueField[0].name = g.textFieldID + "_val";
            }
            if (p.valueFieldCssClass)
{
                g.valueField.addClass(p.valueFieldCssClass);
            }
            if (g.valueField[0].name == undefined) g.valueField[0].name = g.valueField[0].id;
            //update by superzoc 增加初始值
            if (p.initValue != null) g.valueField[0].value = p.initValue;
            g.valueField.attr("data-portalid", g.id);
            //开关
            g.link = $('<div class="m-trigger"><div class="m-trigger-icon"></div></div>');
            if (p.triggerIcon) g.link.find("div:first").addClass(p.triggerIcon);
            //下拉框
            g.selectBox = $('<div class="m-box-select" style="display:none"><div class="m-box-select-inner"><table cellpadding="0" cellspacing="0" border="0" class="m-box-select-table"></table></div></div>');
            g.selectBox.table = $("table:first", g.selectBox);
            g.selectBoxInner = g.selectBox.find(".m-box-select-inner:first");
            //外层
            g.wrapper = g.inputText.wrap('<div class="m-text m-text-combobox"></div>').parent();
            g.wrapper.append('<div class="m-text-l"></div><div class="m-text-r"></div>');
            g.wrapper.append(g.link);
            //添加个包裹，
            g.textwrapper = g.wrapper.wrap('<div class="m-text-wrapper"></div>').parent();

            if (p.absolute)
                g.selectBox.appendTo('body').addClass("m-box-select-absolute");
            else
                g.textwrapper.append(g.selectBox);

            g.textwrapper.append(g.valueField);
            g.inputText.addClass("m-text-field");
            if (p.isShowCheckBox && !g.select)
{
                $("table", g.selectBox).addClass("m-table-checkbox");
            } else
{
                p.isShowCheckBox = false;
                $("table", g.selectBox).addClass("m-table-nocheckbox");
            }
            //开关 事件
            g.link.hover(function ()
{
                if (p.disabled || p.readonly) return;
                this.className = "m-trigger-hover";
            }, function ()
{
                if (p.disabled || p.readonly) return;
                this.className = "m-trigger";
            }).mousedown(function ()
{
                if (p.disabled || p.readonly) return;
                this.className = "m-trigger-pressed";
            }).mouseup(function ()
{
                if (p.disabled || p.readonly) return;
                this.className = "m-trigger-hover";
            }).click(function ()
{
                if (p.disabled || p.readonly) return;
                if (g.trigger('buttonClick') == false) return false;
                if (g.trigger('beforeOpen') == false) return false;
                g._toggleSelectBox(g.selectBox.is(":visible"));
            });
            g.inputText.click(function ()
{
                if (p.disabled || p.readonly) return;
                if (g.trigger('beforeOpen') == false) return false;
                g._toggleSelectBox(g.selectBox.is(":visible"));
            }).blur(function ()
{
                if (p.disabled) return;
                g.wrapper.removeClass("m-text-focus");
            }).focus(function ()
{
                if (p.disabled || p.readonly) return;
                g.wrapper.addClass("m-text-focus");
            });
            g.wrapper.hover(function ()
{
                if (p.disabled || p.readonly) return;
                g.wrapper.addClass("m-text-over");
            }, function ()
{
                if (p.disabled || p.readonly) return;
                g.wrapper.removeClass("m-text-over");
            });
            g.resizing = false;
            g.selectBox.hover(null, function (e)
{
                if (p.hideOnLoseFocus && g.selectBox.is(":visible") && !g.boxToggling && !g.resizing)
{
                    g._toggleSelectBox(true);
                }
            });
            //下拉框内容初始化
            g.bulidContent();

            g.set(p);
            //下拉框宽度、高度初始化   
            if (p.selectBoxWidth)
{
                g.selectBox.width(p.selectBoxWidth);
            }
            else
{
                g.selectBox.css('width', g.wrapper.css('width'));
            }
            if (p.grid)
            {
                g.bind('show', function ()
                {
                    if (!g.grid)
                    {
                        g.setGrid(p.grid);
                        g.set('SelectBoxHeight', p.selectBoxHeight);
                    }
                });
            }
            g.updateSelectBoxPosition();
            $(document).bind("click.combobox", function (e)
{
                //修改点击空白处隐藏下拉框功能
                if (g.selectBox.is(":visible") && $((e.target || e.srcElement)).closest(".m-box-select, .m-text-combobox").length == 0)
{
                    g._toggleSelectBox(true);
                }
            });
        },
        destroy: function ()
{
            if (this.wrapper) this.wrapper.remove();
            if (this.selectBox) this.selectBox.remove();
            this.options = null;
            $.portalui.remove(this);
        },
        clear: function ()
{
            this._changeValue("", "");
            $("a.m-checkbox-checked", this.selectBox).removeClass("m-checkbox-checked");
            $("td.m-selected", this.selectBox).removeClass("m-selected");
            $(":checkbox", this.selectBox).each(function () { this.checked = false; });
            this.trigger('clear');
        },
        _setSelectBoxHeight: function (height)
{
            if (!height) return;
            var g = this, p = this.options;
            if (p.grid)
            {
                g.grid && g.grid.set('height', g.getGridHeight(height));
            } else if (!p.tree)
{
                var itemsleng = $("tr", g.selectBox.table).length;
                if (!p.selectBoxHeight && itemsleng < 8) p.selectBoxHeight = itemsleng * 30;
                if (p.selectBoxHeight)
{
                    if (itemsleng < 8)
{
                        g.selectBoxInner.height('auto');
                    } else
{
                        g.selectBoxInner.height(p.selectBoxHeight);
                    }
                }
            }
        },
        _setCss: function (css)
{
            if (css)
            {
                this.wrapper.addClass(css);
            }
        },
        //取消选择 
        _setCancelable: function (value)
{
            var g = this, p = this.options;
            if (!value && g.unselect)
            {
                g.unselect.remove();
                g.unselect = null;
            }
            if (!value && !g.unselect) return;
            g.unselect = $('<div class="m-trigger m-trigger-cancel"><div class="m-trigger-icon"></div></div>').hide();
            g.wrapper.hover(function ()
            {
                g.unselect.show();
            }, function ()
            {
                g.unselect.hide();
            })
            if (!p.disabled && !p.readonly && p.cancelable)
            {
                g.wrapper.append(g.unselect);
            }
            g.unselect.hover(function ()
            {
                this.className = "m-trigger-hover m-trigger-cancel";
            }, function ()
            {
                this.className = "m-trigger m-trigger-cancel";
            }).click(function ()
            {
                g.clear();
            });
        },
        _setDisabled: function (value)
{
            //禁用样式
            if (value)
{
                this.wrapper.addClass('m-text-disabled');
            } else
{
                this.wrapper.removeClass('m-text-disabled');
            }
        },
        _setReadonly: function (readonly)
{
            if (readonly)
{
                this.wrapper.addClass("m-text-readonly");
            } else
{
                this.wrapper.removeClass("m-text-readonly");
            }
        },
        _setLable: function (label)
{
            var g = this, p = this.options;
            if (label)
{
                if (g.labelwrapper)
{
                    g.labelwrapper.find(".m-text-label:first").html(label + ':&nbsp');
                }
                else
{
                    g.labelwrapper = g.textwrapper.wrap('<div class="m-labeltext"></div>').parent();
                    g.labelwrapper.prepend('<div class="m-text-label" style="float:left;display:inline;">' + label + ':&nbsp</div>');
                    g.textwrapper.css('float', 'left');
                }
                if (!p.labelWidth)
{
                    p.labelWidth = $('.m-text-label', g.labelwrapper).outerWidth();
                }
                else
{
                    $('.m-text-label', g.labelwrapper).outerWidth(p.labelWidth);
                }
                $('.m-text-label', g.labelwrapper).width(p.labelWidth);
                $('.m-text-label', g.labelwrapper).height(g.wrapper.height());
                g.labelwrapper.append('<br style="clear:both;" />');
                if (p.labelAlign)
{
                    $('.m-text-label', g.labelwrapper).css('text-align', p.labelAlign);
                }
                g.textwrapper.css({ display: 'inline' });
                g.labelwrapper.width(g.wrapper.outerWidth() + p.labelWidth + 2);
            }
        },
        _setWidth: function (value)
{
            var g = this, p = this.options;
            if (value > 20)
{
                g.wrapper.css({ width: value });
                g.inputText.css({ width: value - 20 });
                if (!p.selectBoxWidth)
                {
                    g.selectBox.css({ width: value });
                }
            }
        },
        _setHeight: function (value)
{
            var g = this;
            if (value > 10)
{
                g.wrapper.height(value);
                g.inputText.height(value - 2);
            }
        },
        _setResize: function (resize)
{
            var g = this, p = this.options;
            if (p.columns)
            {
                return;
            }
            //调整大小支持
            if (resize && $.fn.portalResizable)
{
                var handles = p.selectBoxHeight ? 'e' : 'se,s,e';
                g.selectBox.portalResizable({
                    handles: handles, onStartResize: function ()
                    {
                        g.resizing = true;
                        g.trigger('startResize');
                    }, onEndResize: function ()
                    {
                        g.resizing = false;
                        if (g.trigger('endResize') == false)
                            return false;
                    }, onStopResize: function (current, e)
                    {
                        if (g.grid)
                        {
                            if (current.newWidth)
                            {
                                g.selectBox.width(current.newWidth);
                            }
                            if (current.newHeight)
                            {
                                g.set({ selectBoxHeight: current.newHeight });
                            }
                            g.grid.refreshSize();
                            g.trigger('endResize');
                            return false;
                        }
                        return true;
                    }
                });
                g.selectBox.append("<div class='m-btn-nw-drop'></div>");
            }
        },
        //查找Text,适用多选和单选
        findTextByValue: function (value)
{
            var g = this, p = this.options;
            if (value == null) return "";
            var texts = "";
            var contain = function (checkvalue)
{
                var targetdata = value.toString().split(p.split);
                for (var i = 0; i < targetdata.length; i++)
{
                    if (targetdata[i] == checkvalue) return true;
                }
                return false;
            };
            //当combobox下拉一个grid时, 不能直接取data. 必须取grid的data. 
            //原写法$(g.data) 仅适用于无grid时的典型情形
            var d;
            if (g.options.grid && g.options.grid.data)
                d = g.options.grid.data.Rows;
            else
                d = g.data;
            $(d).each(function (i, item)
{
                var val = item[p.valueField];
                var txt = item[p.textField];
                if (contain(val))
{
                    texts += txt + p.split;
                }
            });
            if (texts.length > 0) texts = texts.substr(0, texts.length - 1);
            return texts;
        },
        //查找Value,适用多选和单选
        findValueByText: function (text)
{
            var g = this, p = this.options;
            if (!text && text == "") return "";
            var contain = function (checkvalue)
{
                var targetdata = text.toString().split(p.split);
                for (var i = 0; i < targetdata.length; i++)
{
                    if (targetdata[i] == checkvalue) return true;
                }
                return false;
            };
            var values = "";
            $(g.data).each(function (i, item)
{
                var val = item[p.valueField];
                var txt = item[p.textField];
                if (contain(txt))
{
                    values += val + p.split;
                }
            });
            if (values.length > 0) values = values.substr(0, values.length - 1);
            return values;
        },
        insertItem: function (data, index)
{
            var g = this, p = this.options;
            g.data = g.data || [];
            g.data.splice(index, 0, data);
            g.setData(g.data);
        },
        addItem: function (data)
{
            var g = this, p = this.options;
            g.insertItem(data, (g.data || []).length);
        },
        _setValue: function (value, text)
{
            var g = this, p = this.options;
            text = g.findTextByValue(value);
            if (p.tree)
{
                g.selectValueByTree(value);
            }
            else if (!p.isMultiSelect)
{
                g._changeValue(value, text);
                $("tr[value='" + value + "'] td", g.selectBox).addClass("m-selected");
                $("tr[value!='" + value + "'] td", g.selectBox).removeClass("m-selected");
            }
            else
{
                g._changeValue(value, text);
                if (value != null)
                {
                    var targetdata = value.toString().split(p.split);
                    $("table.m-table-checkbox :checkbox", g.selectBox).each(function () { this.checked = false; });
                    for (var i = 0; i < targetdata.length; i++)
                    {
                        $("table.m-table-checkbox tr[value=" + targetdata[i] + "] :checkbox", g.selectBox).each(function () { this.checked = true; });
                    }
                }
            }
        },
        selectValue: function (value)
{
            this._setValue(value);
        },
        bulidContent: function ()
{
            var g = this, p = this.options;
            this.clearContent();
            if (g.select)
{
                g.setSelect();
            }
            else if (p.tree)
{
                g.setTree(p.tree);
            }
        },
        reload: function ()
{
            var g = this, p = this.options;
            if (p.url)
{
                g.set('url', p.url);
            }
            else if (g.grid)
{
                g.grid.reload();
            }
        },
        _setUrl: function (url)
        {
            if (!url) return;
            var g = this, p = this.options;
            var parms = $.isFunction(p.parms) ? p.parms() : p.parms;
            $.ajax({
                type: p.ajaxType,
                url: url,
                data: parms,
                cache: false,
                dataType: 'json',
                success: function (data)
                {
                    g.setData(data);
                    g.trigger('success', [data]);
                },
                error: function (XMLHttpRequest, textStatus)
                {
                    g.trigger('error', [XMLHttpRequest, textStatus]);
                }
            });
        },
        setUrl: function (url)
        {
            return this._setUrl(url);
        },
        setParm: function (name, value)
        {
            if (!name) return;
            var g = this;
            var parms = g.get('parms');
            if (!parms) parms = {};
            parms[name] = value;
            g.set('parms', parms);
        },
        clearContent: function ()
{
            var g = this, p = this.options;
            $("table", g.selectBox).html("");
            //清除下拉框内容的时候重设高度
            g._setSelectBoxHeight(p.selectBoxHeight);
            //modify end
            //g.inputText.val("");
            //g.valueField.val(""); 
        },
        setSelect: function ()
{
            var g = this, p = this.options;
            this.clearContent();
            $('option', g.select).each(function (i)
{
                var val = $(this).val();
                var txt = $(this).html();
                var tr = $("<tr><td index='" + i + "' value='" + val + "' text='" + txt + "'>" + txt + "</td>");
                $("table.m-table-nocheckbox", g.selectBox).append(tr);
                $("td", tr).hover(function ()
{
                    $(this).addClass("m-over");
                }, function ()
{
                    $(this).removeClass("m-over");
                });
            });
            $('td:eq(' + g.select[0].selectedIndex + ')', g.selectBox).each(function ()
{
                if ($(this).hasClass("m-selected"))
{
                    g.selectBox.hide();
                    return;
                }
                $(".m-selected", g.selectBox).removeClass("m-selected");
                $(this).addClass("m-selected");
                if (g.select[0].selectedIndex != $(this).attr('index') && g.select[0].onchange)
{
                    g.select[0].selectedIndex = $(this).attr('index'); g.select[0].onchange();
                }
                var newIndex = parseInt($(this).attr('index'));
                g.select[0].selectedIndex = newIndex;
                g.select.trigger("change");
                g.selectBox.hide();
                var value = $(this).attr("value");
                var text = $(this).html();
                if (p.render)
{
                    g.inputText.val(p.render(value, text));
                }
                else
{
                    g.inputText.val(text);
                }
            });
            g._addClickEven();
        },
        _setData: function (data)
{
            this.setData(data);
        },
        setData: function (data)
{
            var g = this, p = this.options;
            if (g.select) return;
            if (!data || !data.length) data = [];
            if (g.data != data) g.data = data;
            this.clearContent();
            if (p.columns)
{
                g.selectBox.table.headrow = $("<tr class='m-table-headerow'><td width='18px'></td></tr>");
                g.selectBox.table.append(g.selectBox.table.headrow);
                g.selectBox.table.addClass("m-box-select-grid");
                for (var j = 0; j < p.columns.length; j++)
{
                    var headrow = $("<td columnindex='" + j + "' columnname='" + p.columns[j].name + "'>" + p.columns[j].header + "</td>");
                    if (p.columns[j].width)
{
                        headrow.width(p.columns[j].width);
                    }
                    g.selectBox.table.headrow.append(headrow);

                }
            }
            var out = [];
            if (p.emptyText && !g.emptyRow && (data.length == 0 || data[0][p.textField] != p.emptyText))
{
                g.emptyRow = {};
                g.emptyRow[p.textField] = p.emptyText;
                g.emptyRow[p.valueField] = null;
                data.splice(0, 0, g.emptyRow);
            }
            for (var i = 0; i < data.length; i++)
{
                var val = data[i][p.valueField];
                var txt = data[i][p.textField];
                if (!p.columns)
{
                    out.push("<tr value='" + val + "'>");
                    if (p.isShowCheckBox)
                    {
                        out.push("<td style='width:18px;'  index='" + i + "' value='" + val + "' text='" + txt + "' ><input type='checkbox' /></td>");
                    }
                    var itemHtml = txt;
                    if (p.renderItem)
                    {
                        itemHtml = p.renderItem.call(g, {
                            data: data[i],
                            value: val,
                            text: txt,
                            key: g.inputText.val()
                        });
                    } else if (p.autocomplete && p.highLight)
{
                        itemHtml = g._highLight(txt, g.inputText.val());
                    }
                    out.push("<td index='" + i + "' value='" + val + "' text='" + txt + "' align='left'>" + itemHtml + "</td></tr>");
                } else
{
                    out.push("<tr value='" + val + "'><td style='width:18px;'  index='" + i + "' value='" + val + "' text='" + txt + "' ><input type='checkbox' /></td>");
                    for (var j = 0; j < p.columns.length; j++)
                    {
                        var columnname = p.columns[j].name;
                        out.push("<td>" + data[i][columnname] + "</td>");
                    }
                    out.push('</tr>');
                }
            }
            if (!p.columns)
            {
                if (p.isShowCheckBox)
                {
                    $("table.m-table-checkbox", g.selectBox).append(out.join(''));
                } else
                {
                    $("table.m-table-nocheckbox", g.selectBox).append(out.join(''));
                }
            } else
            {
                g.selectBox.table.append(out.join(''));
            }
            if (p.addRowButton && p.addRowButtonClick && !g.addRowButton)
{
                g.addRowButton = $('<div class="m-box-select-add"><a href="javascript:void(0)" class="link"><div class="icon"></div></a></div>');
                g.addRowButton.find(".link").append(p.addRowButton).click(p.addRowButtonClick);
                g.selectBoxInner.after(g.addRowButton);
            }
            g.set('selectBoxHeight', p.selectBoxHeight);
            //自定义复选框支持
            if (p.isShowCheckBox && $.fn.portalCheckBox)
{
                $("table input:checkbox", g.selectBox).portalCheckBox();
            }
            $(".m-table-checkbox input:checkbox", g.selectBox).change(function ()
{
                if (this.checked && g.hasBind('beforeSelect'))
{
                    var parentTD = null;
                    if ($(this).parent().get(0).tagName.toLowerCase() == "div")
{
                        parentTD = $(this).parent().parent();
                    } else
{
                        parentTD = $(this).parent();
                    }
                    if (parentTD != null && g.trigger('beforeSelect', [parentTD.attr("value"), parentTD.attr("text")]) == false)
{
                        g.selectBox.slideToggle("fast");
                        return false;
                    }
                }
                if (!p.isMultiSelect)
{
                    if (this.checked)
{
                        $("input:checked", g.selectBox).not(this).each(function ()
{
                            this.checked = false;
                            $(".m-checkbox-checked", $(this).parent()).removeClass("m-checkbox-checked");
                        });
                        g.selectBox.slideToggle("fast");
                    }
                }
                g._checkboxUpdateValue();
            });
            $("table.m-table-nocheckbox td", g.selectBox).hover(function ()
{
                $(this).addClass("m-over");
            }, function ()
{
                $(this).removeClass("m-over");
            });
            g._addClickEven();
            //选择项初始化
            if (!p.autocomplete)
            {
                g.updateStyle();
            }
        },
        //树
        setTree: function (tree)
{
            var g = this, p = this.options;
            this.clearContent();
            g.selectBox.table.remove();
            if (tree.checkbox != false)
{
                tree.onCheck = function ()
{
                    var nodes = g.treeManager.getChecked();
                    var value = [];
                    var text = [];
                    $(nodes).each(function (i, node)
{
                        if (p.treeLeafOnly && node.data.children) return;
                        value.push(node.data[p.valueField]);
                        text.push(node.data[p.textField]);
                    });
                    g._changeValue(value.join(p.split), text.join(p.split));
                };
            }
            else
{
                tree.onSelect = function (node)
{
                    if (g.trigger('BeforeSelect'[node]) == false) return;
                    if (p.treeLeafOnly && node.data.children) return;
                    var value = node.data[p.valueField];
                    var text = node.data[p.textField];
                    g._changeValue(value, text);
                };
                tree.onCancelSelect = function (node)
{
                    g._changeValue("", "");
                };
            }
            tree.onAfterAppend = function (domnode, nodedata)
{
                if (!g.treeManager) return;
                var value = null;
                if (p.initValue) value = p.initValue;
                else if (g.valueField.val() != "") value = g.valueField.val();
                g.selectValueByTree(value);
            };
            g.tree = $("<ul></ul>");
            $("div:first", g.selectBox).append(g.tree);
            //新增下拉框中获取树对象的接口
            g.innerTree = g.tree.portalTree(tree);
            g.treeManager = g.tree.portalGetTreeManager();
        },
        //新增下拉框中获取树对象的接口
        getTree: function ()
        {
            return this.innerTree;
        },
        selectValueByTree: function (value)
{
            var g = this, p = this.options;
            if (value != null)
{
                var text = "";
                var valuelist = value.toString().split(p.split);
                $(valuelist).each(function (i, item)
{
                    g.treeManager.selectNode(item.toString());
                    text += g.treeManager.getTextByID(item);
                    if (i < valuelist.length - 1) text += p.split;
                });
                g._changeValue(value, text);
            }
        },
        //表格
        setGrid: function (grid)
{
            var g = this, p = this.options;
            if (g.grid) return;
            p.hideOnLoseFocus = p.hideGridOnLoseFocus ? true : false;
            this.clearContent();
            g.selectBox.addClass("m-box-select-lookup");
            g.selectBox.table.remove();
            var panel = $("div:first", g.selectBox);
            var conditionPanel = $("<div></div>").appendTo(panel);
            var gridPanel = $("<div></div>").appendTo(panel);
            g.conditionPanel = conditionPanel;
            //搜索框
            if (p.condition)
            {
                var conditionParm = $.extend({
                    labelWidth: 60,
                    space: 20,
                    width: p.selectBoxWidth
                }, p.condition);
                g.condition = conditionPanel.portalForm(conditionParm);
            } else
            {
                conditionPanel.remove();
            }
            //列表
            grid = $.extend({
                columnWidth: 120,
                alternatingRow: false,
                frozen: true,
                rownumbers: true,
                allowUnSelectRow: true
            }, grid, {
                width: "100%",
                height: g.getGridHeight(),
                inWindow: false,
                parms: p.parms,
                isChecked: function (rowdata)
                {
                    var value = g.getValue();
                    if (!value) return false;
                    if (!p.valueField || !rowdata[p.valueField]) return false;
                    return $.inArray(rowdata[p.valueField].toString(), value.split(p.split)) != -1;
                }
            });
            g.grid = g.gridManager = gridPanel.portalGrid(grid);
            g.grid.bind('afterShowData', function ()
{
                g.updateSelectBoxPosition();
            });
            var selecteds = [], onGridSelect = function ()
            {
                var value = [], text = [];
                $(selecteds).each(function (i, rowdata)
                {
                    value.push(rowdata[p.valueField]);
                    text.push(rowdata[p.textField]);
                });
                if (grid.checkbox)
                    g.selected = selecteds;
                else if (selecteds.length)
                    g.selected = selecteds[0];
                else
                    g.selected = null;
                g._changeValue(value.join(p.split), text.join(p.split));
                g.trigger('gridSelect', {
                    value: value.join(p.split),
                    text: text.join(p.split),
                    data: selecteds
                });
            }, removeSelected = function (rowdata)
            {
                for (var i = selecteds.length - 1; i >= 0; i--)
                {
                    if (selecteds[i][p.valueField] == rowdata[p.valueField])
                    {
                        selecteds.splice(i, 1);
                    }
                }
            }, addSelected = function (rowdata)
            {
                for (var i = selecteds.length - 1; i >= 0; i--)
                {
                    if (selecteds[i][p.valueField] == rowdata[p.valueField])
                    {
                        return;
                    }
                }
                selecteds.push(rowdata);
            };
            if (grid.checkbox)
{
                var onCheckRow = function (checked, rowdata)
                {
                    checked && addSelected(rowdata);
                    !checked && removeSelected(rowdata);
                };
                g.grid.bind('CheckAllRow', function (checked)
                {
                    $(g.grid.rows).each(function (i, rowdata)
                    {
                        onCheckRow(checked, rowdata);
                    });
                    onGridSelect();
                });
                g.grid.bind('CheckRow', function (checked, rowdata)
                {
                    onCheckRow(checked, rowdata);
                    onGridSelect();
                });
            }
            else
{
                g.grid.bind('SelectRow', function (rowdata)
                {
                    selecteds = [rowdata];
                    onGridSelect();
                    g._toggleSelectBox(true);
                });
                g.grid.bind('UnSelectRow', function ()
                {
                    selecteds = [];
                    onGridSelect();
                });
            }
            g.bind('show', function ()
            {
                g.grid.refreshSize();
            });
            g.bind("clear", function ()
            {
                selecteds = [];
                g.grid.selecteds = [];
                g.grid._showData();
            });
            if (p.condition)
            {
                var containerBtn1 = $('<li style="margin-right:9px"><div></div></li>');
                var containerBtn2 = $('<li style="margin-right:9px;float:right"><div></div></li>');
                $("ul:first", conditionPanel).append(containerBtn1).append(containerBtn2).after('<div class="m-clear"></div>');
                $("div", containerBtn1).portalButton({
                    text: p.Search, width: 40,
                    click: function ()
                    {
                        var rules = g.condition.toConditions();
                        g.grid.setParm(grid.conditionParmName || 'condition', $.portalui.toJSON(rules));
                        g.grid.reload();
                    }
                });
                $("div", containerBtn2).portalButton({
                    text: '关闭', width: 40,
                    click: function ()
                    {
                        g.selectBox.hide();
                    }
                });
            }
            g.grid.refreshSize();
        },
        getGridHeight: function (height)
        {
            var g = this, p = this.options;
            height = height || g.selectBox.height();
            height -= g.conditionPanel.height();
            return height;
        },
        _getValue: function ()
{
            return $(this.valueField).val();
        },
        getValue: function ()
{
            //获取值
            return this._getValue();
        },
        getSelected: function ()
{
            return this.selected;
        },
        getText: function ()
{
            return this.inputText.val();
        },
        setText: function (value)
        {
            this.inputText.val(value);
        },
        updateStyle: function ()
{
            var g = this, p = this.options;
            p.initValue = g._getValue();
            g._dataInit();
        },
        _dataInit: function ()
{
            var g = this, p = this.options;
            var value = null;
            if (p.initValue != null && p.initText != null)
{
                g._changeValue(p.initValue, p.initText);
            }
            //根据值来初始化
            if (p.initValue != null)
{
                value = p.initValue;
                if (p.tree)
{
                    if (value)
                        g.selectValueByTree(value);
                }
                else if (g.data)
{
                    var text = g.findTextByValue(value);
                    g._changeValue(value, text);
                }
            }
            else if (g.valueField.val() != "")
{
                value = g.valueField.val();
                if (p.tree)
{
                    if (value)
                        g.selectValueByTree(value);
                }
                else if (g.data)
{
                    var text = g.findTextByValue(value);
                    g._changeValue(value, text);
                }
            }
            if (!p.isShowCheckBox)
{
                $("table tr", g.selectBox).find("td:first").each(function ()
{
                    if (value != null && value == $(this).attr("value"))
{
                        $(this).addClass("m-selected");
                    } else
{
                        $(this).removeClass("m-selected");
                    }
                });
            }
            else
{
                $(":checkbox", g.selectBox).each(function ()
{
                    var parentTD = null;
                    var checkbox = $(this);
                    if (checkbox.parent().get(0).tagName.toLowerCase() == "div")
{
                        parentTD = checkbox.parent().parent();
                    } else
{
                        parentTD = checkbox.parent();
                    }
                    if (parentTD == null) return;
                    $(".m-checkbox", parentTD).removeClass("m-checkbox-checked");
                    checkbox[0].checked = false;
                    var valuearr = (value || "").toString().split(p.split);
                    $(valuearr).each(function (i, item)
{
                        if (value != null && item == parentTD.attr("value"))
{
                            $(".m-checkbox", parentTD).addClass("m-checkbox-checked");
                            checkbox[0].checked = true;
                        }
                    });
                });
            }
        },
        //设置值到 文本框和隐藏域
        _changeValue: function (newValue, newText)
{
            var g = this, p = this.options;
            g.valueField.val(newValue);
            if (p && p.render)
{
                g.inputText.val(p.render(newValue, newText));
            }
            else
{
                g.inputText.val(newText);
            }
            g.selectedValue = newValue;
            g.selectedText = newText;
            g.inputText.trigger("change").focus();
            g.trigger('selected', [newValue, newText]);
        },
        //更新选中的值(复选框)
        _checkboxUpdateValue: function ()
{
            var g = this, p = this.options;
            var valueStr = "";
            var textStr = "";
            $("input:checked", g.selectBox).each(function ()
{
                var parentTD = null;
                if ($(this).parent().get(0).tagName.toLowerCase() == "div")
{
                    parentTD = $(this).parent().parent();
                } else
{
                    parentTD = $(this).parent();
                }
                if (!parentTD) return;
                valueStr += parentTD.attr("value") + p.split;
                textStr += parentTD.attr("text") + p.split;
            });
            if (valueStr.length > 0) valueStr = valueStr.substr(0, valueStr.length - 1);
            if (textStr.length > 0) textStr = textStr.substr(0, textStr.length - 1);
            g._changeValue(valueStr, textStr);
        },
        _addClickEven: function ()
{
            var g = this, p = this.options;
            //选项点击
            $(".m-table-nocheckbox td", g.selectBox).click(function ()
{
                var value = $(this).attr("value");
                var index = parseInt($(this).attr('index'));
                var text = $(this).attr("text");
                if (g.hasBind('beforeSelect') && g.trigger('beforeSelect', [value, text]) == false)
{
                    if (p.slide) g.selectBox.slideToggle("fast");
                    else g.selectBox.hide();
                    return false;
                }
                if ($(this).hasClass("m-selected"))
{
                    if (p.slide) g.selectBox.slideToggle("fast");
                    else g.selectBox.hide();
                    return;
                }
                $(".m-selected", g.selectBox).removeClass("m-selected");
                $(this).addClass("m-selected");
                if (g.select)
{
                    if (g.select[0].selectedIndex != index)
{
                        g.select[0].selectedIndex = index;
                        g.select.trigger("change");
                    }
                }
                if (p.slide)
{
                    g.boxToggling = true;
                    g.selectBox.hide("fast", function ()
{
                        g.boxToggling = false;
                    })
                } else g.selectBox.hide();
                g._changeValue(value, text);
            });
        },
        updateSelectBoxPosition: function ()
{
            var g = this, p = this.options;
            if (p && p.absolute)
{
                var contentHeight = $(document).height();
                if (p.alwayShowInTop || Number(g.wrapper.offset().top + 1 + g.wrapper.outerHeight() + g.selectBox.height()) > contentHeight
            			&& contentHeight > Number(g.selectBox.height() + 1))
{
                    //若下拉框大小超过当前document下边框,且当前document上留白大于下拉内容高度,下拉内容向上展现
                    g.selectBox.css({ left: g.wrapper.offset().left, top: g.wrapper.offset().top - 1 - g.selectBox.height() });
                } else
{
                    g.selectBox.css({ left: g.wrapper.offset().left, top: g.wrapper.offset().top + 1 + g.wrapper.outerHeight() });
                }
            }
            else
{
                var topheight = g.wrapper.offset().top - $(window).scrollTop();
                var selfheight = g.selectBox.height() + textHeight + 4;
                if (topheight + selfheight > $(window).height() && topheight > selfheight)
{
                    g.selectBox.css("marginTop", -1 * (g.selectBox.height() + textHeight + 5));
                }
            }
        },
        _toggleSelectBox: function (isHide)
{
            var g = this, p = this.options;
            if (!g || !p) return;
            //避免同一界面弹出多个菜单的问题
            var managers = $.portalui.find($.portalui.controls.ComboBox);
            for (var i = 0, l = managers.length; i < l; i++)
            {
                var o = managers[i];
                if (o.id != g.id)
                {
                    if (o.selectBox.is(":visible") != null && o.selectBox.is(":visible"))
                    {
                        o.selectBox.hide();
                    }
                }
            }
            managers = $.portalui.find($.portalui.controls.DateEditor);
            for (var i = 0, l = managers.length; i < l; i++)
            {
                var o = managers[i];
                if (o.id != g.id)
                {
                    if (o.dateeditor.is(":visible") != null && o.dateeditor.is(":visible"))
                    {
                        o.dateeditor.hide();
                    }
                }
            }
            var textHeight = g.wrapper.height();
            g.boxToggling = true;
            if (isHide)
{
                if (p.slide)
{
                    g.selectBox.slideToggle('fast', function ()
{
                        g.boxToggling = false;
                    });
                }
                else
{
                    g.selectBox.hide();
                    g.boxToggling = false;
                }
            }
            else
{
                g.updateSelectBoxPosition();
                if (p.slide)
{
                    g.selectBox.slideToggle('fast', function ()
{
                        g.boxToggling = false;
                        if (!p.isShowCheckBox && $('td.m-selected', g.selectBox).length > 0)
{
                            var offSet = ($('td.m-selected', g.selectBox).offset().top - g.selectBox.offset().top);
                            $(".m-box-select-inner", g.selectBox).animate({ scrollTop: offSet });
                        }
                    });
                }
                else
{
                    g.selectBox.show();
                    g.boxToggling = false;
                    if (!g.tree && !g.grid && !p.isShowCheckBox && $('td.m-selected', g.selectBox).length > 0)
{
                        var offSet = ($('td.m-selected', g.selectBox).offset().top - g.selectBox.offset().top);
                        $(".m-box-select-inner", g.selectBox).animate({ scrollTop: offSet });
                    }
                }
            }
            g.isShowed = g.selectBox.is(":visible");
            g.trigger('toggle', [isHide]);
            g.trigger(isHide ? 'hide' : 'show');
        },
        _highLight: function (str, key)
{
            if (!str) return str;
            var index = str.indexOf(key);
            if (index == -1) return str;
            return str.substring(0, index) + "<span class='m-highLight'>" + key + "</span>" + str.substring(key.length + index);
        },
        _setAutocomplete: function (value)
        {
            var g = this, p = this.options;
            if (!value) return;
            g.inputText.removeAttr("readonly");
            var lastText = g.inputText.val();
            g.inputText.keyup(function ()
{
                if (this._acto)
                    clearTimeout(this._acto);
                this._acto = setTimeout(function ()
{
                    if (lastText == g.inputText.val()) return;
                    p.initValue = "";
                    g.valueField.val("");
                    if (p.url)
{
                        g.setParm('key', g.inputText.val());
                        g.set('url', p.url);
                        g.selectBox.show();
                    } else if (p.grid)
{
                        g.grid.setParm('key', g.inputText.val());
                        g.grid.reload();
                    }
                    lastText = g.inputText.val();
                    this._acto = null;
                }, 300);
            });
        }
    });

    $.portalui.controls.ComboBox.prototype.setValue = $.portalui.controls.ComboBox.prototype.selectValue;
    //设置文本框和隐藏控件的值
    $.portalui.controls.ComboBox.prototype.setInputValue = $.portalui.controls.ComboBox.prototype._changeValue;


})(jQuery);