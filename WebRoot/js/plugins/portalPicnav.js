/*顶部图片导航*/
(function ($)
{
    $.fn.portalPicNav = function (options)
    {
        return $.portalui.run.call(this, "portalPicNav", arguments);
    };
    $.fn.portalGetPicNavManager = function ()
    {
        return $.portalui.run.call(this, "portalGetPicNavManager", arguments);
    };

    $.portalDefaults.PicNav = {
        height:110, //高度，宽带自适应
        canwrap:true,   //是否支持收缩
        defaultwrap:false,   //默认是否展开
        onFold: function () { },
        onUnFold: function () { }
    };

    $.portalMethos.PicNav = {};

    $.portalui.controls.PicNav = function (element, options)
    {
        $.portalui.controls.PicNav.base.constructor.call(this, element, options);
    };

    $.portalui.controls.PicNav.portalExtend($.portalui.core.UIComponent, {
        __getType: function ()
        {
            return 'PicNav';
        },
        __idPrev: function ()
        {
            return 'PicNav';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.PicNav;
        },
        _init: function ()
        {
            var p = this.options;
            $.portalui.controls.PicNav.base._init.call(this);
            p.content = p.content || $(this.element).html();
        },
        _render: function ()
        {
            /**
             * items参数(运用于li标签中，作用li标签的属性)：
             *      title:文字标题，图片alt
             *      img:导航图片
             *      type:url/function/其它（空或不指定）
             *      href:url地址，当type=url时起作用
             *      function:js语句，当type=function时起作用
             * */
            var g = this,p = this.options;
            g.PicNav = $(this.element);

            g.PicNav.addClass("m-picnav");

            g._nav = $('<div id="test" style="height: ' + p.height + 'px"></div>');
            var _ul = $('<ul></ul>');

            g._nav.append(_ul);

            var ul = $(g.PicNav.children());
            var lis = $(ul.children());
            $.each(lis,function(i,__li){
                var item = $(__li);

                var _li = $('<li></li>');

                var _a;
                if(item.attr("type") == 'url'){
                    _a = $('<a onclick="javascript:void(0)" target="_blank" href="' + item.attr("href") + '"></a>');
                }else if(item.attr("type") == 'function'){
                    _a = $('<a onclick="' + item.attr("onclick") + '" href="javascript:void(0)"></a>');
                }else{
                    _a = $('<a onclick="javascript:void(0)" href="javascript:void(0)"></a>');
                }
                _li.append(_a);

                var _img = $('<img alt="' + item.attr("title") + '" src="' + item.attr("img") + '"/>');
                _a.append(_img);

                var _span = $('<span>' + item.attr("title") + '</span>');
                _a.append(_span);

                _ul.append(_li);
            });

            g.PicNav.empty();
            g.PicNav.append(g._nav);

            if(p.canwrap){
                if(p.defaultwrap){
                    g._nav.hide();
                    g._span = $('<span id="popflag" class="icon-down" style=" margin-top: 5px;"></span>');
                }else{
                    g._nav.show();
                    g._span = $('<span id="popflag" class="icon-up" style=" margin-top: 5px;"></span>');
                }
                g.PicNav.append(g._span);

                g._span.click(function(){
                    if(g._nav.is(":hidden")){
                        g.unfold();
                    }else{
                        g.fold();
                    }
                });
            }
        },
        unfold: function (){
            var g = this;p = this.options;

            g._nav.slideDown(300);
            g._span.removeClass("icon-down").addClass("icon-up");

            p.onUnFold();
        },
        fold: function (){
            var g = this;p = this.options;

            g._nav.slideUp(300);
            g._span.removeClass("icon-up").addClass("icon-down");

            p.onFold();
        },
        bind: function(_event,_function){
            var g = this;p = this.options;

            if(_event = 'UnFold')
                p.onUnFold = _function;
            else if(_event = 'Fold')
                p.onFold = _function;
        }
    })
})(jQuery);