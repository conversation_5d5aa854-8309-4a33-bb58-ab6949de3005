function onLoadPage(menuUrl,liobj)
{
  var objul=document.getElementById('g-navul');
  var a_array = objul.getElementsByTagName('li');
  var menu=document.getElementById('g-menu');
  for(var i=0;i<a_array.length;i++)
  {
      if(a_array[i]==liobj)
	  {
	  	liobj.className ='on';
	  	//menuUrl 等于null 表示首页：选择首页时：刷新首页：iframe;
	  	if(menuUrl=='null'){
	  		var s = document.getElementById('myIfram').src;
	  		document.getElementById('myIfram').src=s;
	  	}
	  	
	  	if(i==0){
	  		document.getElementById('g-main').className='g-main_c'
	  		document.getElementById('g-side').style.display='none';
	  		$('.main_tab').show();
	  		$('.main_tab2').hide();
	  	}else{
	  		showMenu(i);
	  		document.getElementById('f-close').className='f-close_a'
		   	document.getElementById('g-side').className='g-side_a'
			 document.getElementById('g-menu').style.display='block';
	  		document.getElementById('f-close').className='f-close_a'
	  		document.getElementById('g-main').className='g-main_b';
	  		document.getElementById('g-side').style.display='block';
	  		$('.main_tab').hide();
	  		$('.main_tab2').show();
	  		//$('.main_tab2').find('iframe').attr('src',menuUrl);
	  		$('.main_tab2').find('iframe').attr('src',window.location.protocol+"//"+window.location.host+"/EOM/"+menuUrl);
	  	}
	  }
	  else if(a_array[i].className != "split")
	  {
	  	a_array[i].className ='';
	  }
  } 
}

function showSubMenuPage(menuUrl,itemobj)
{
var menuitem=document.getElementById('g-menu');
  var ulArray = menuitem.getElementsByTagName("UL");
   
   if(ulArray.length<=0)
   		return;	
	var menuUL=ulArray[0];
    for(var i=0;i<menuUL.childNodes.length;i++)
  {
	  if(menuUL.childNodes[i].tagName=='LI' && menuUL.childNodes[i].className!='on1')
	 		 menuUL.childNodes[i].className ='on';
			 
	  if(menuUL.childNodes[i]==itemobj || menuUL.childNodes[i]==itemobj.parentNode)
	  {
		 
	  	if(i<menuUL.childNodes.length)
		{
			
			if(typeof(menuUL.childNodes[i+1]) != "undefined" && menuUL.childNodes[i+1].tagName=='DL')
			{
			
				if(menuUL.childNodes[i+1].style.display=="none")
				{
					menuUL.childNodes[i].className ='on1';//??????????
					menuUL.childNodes[i+1].style.display="block";
					
				}
				else
				{
					menuUL.childNodes[i+1].style.display="none";
					menuUL.childNodes[i].className ='g-nav';
				}
			}
			else
			{
				//alert(menuUL.childNodes[i].tagName)
				if(menuUL.childNodes[i].tagName=='LI')
				{	
					//alert(i)
					if(itemobj==menuUL.childNodes[i])
						menuUL.childNodes[i].className ='on2';//??????????
						
				}
				//$('.main_tab2').find('iframe').attr('src',menuUrl);
				$('.main_tab2').find('iframe').attr('src',window.location.protocol+"//"+window.location.host+"/EOM/"+menuUrl);
			}
		}
	  }
  }
}

function showTreeMenu()
{
   if(document.getElementById('f-close').className=='f-close_a')
   {
   	document.getElementById('f-close').className='f-close_b'
   	document.getElementById('g-side').className='g-side_b'
   	document.getElementById('g-main').className='g-main_a'
	  document.getElementById('g-menu').style.display='none';
	}else
	{
   	document.getElementById('f-close').className='f-close_a'
   	document.getElementById('g-side').className='g-side_a'
   	document.getElementById('g-main').className='g-main_b'
	document.getElementById('g-menu').style.display='block';
	} 
}