/*面板*/
(function ($)
{

    $.fn.portalPanel = function (options)
    {
        return $.portalui.run.call(this, "portalPanel", arguments);
    };

    $.portalDefaults.Panel = {
        width: 400,
        height : 300,
        title: 'Panel',
        content: null,      //内容
        url: null,          //远程内容Url
        frameName: null,     //创建iframe时 作为iframe的name和id 
        data: null,          //可用于传递到iframe的数据
        showClose: false,    //是否显示关闭按钮
        showToggle: false,    //是否显示收缩按钮
        showMore: false,     //是否显示更多按钮
        moreUrl: null,               //更多跳转链接
        icon: null,          //左侧按钮
        onClose:null,       //关闭前事件
        onClosed:null,      //关闭事件
        onLoaded:null           //url模式 加载完事件
    };

    $.portalMethos.Panel = {};

    $.portalui.controls.Panel = function (element, options)
    {
        $.portalui.controls.Panel.base.constructor.call(this, element, options);
    };
    $.portalui.controls.Panel.portalExtend($.portalui.core.UIComponent, {
        __getType: function ()
        {
            return 'Panel';
        },
        __idPrev: function ()
        {
            return 'Panel';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.Panel;
        },
        _init: function ()
        {
            var g = this, p = this.options;
            $.portalui.controls.Panel.base._init.call(this);
            p.content = p.content || $(g.element).html(); 
        },
        _render: function ()
        {
            var g = this, p = this.options; 
            g.panel = $(g.element).addClass("m-panel").html("");
            g.panel.append('<div class="m-panel-header"><span></span></div><div class="m-panel-content"></div>');
             
            g.set(p);
 
            g.panel.bind("click.panel", function (e)
            { 
                var obj = (e.target || e.srcElement), jobj = $(obj);
                if (jobj.hasClass("m-panel-header-toggle"))
                {
                    g.toggle();
                } else if (jobj.hasClass("m-panel-header-close"))
                {
                    g.close();
                }
            });
        },
        _setChildren: function(children)
        {
            var g = this, p = this.options;
            var tagNames = {
                input : ["textbox", "combobox", "select"] 
            };
            var PluginNameMatchs  = 
            {
                "grid" : "portalGrid",
                "tree":"portalTree",
                "form":"portalForm",
                "tab":"portalTab"
            }; 
            if (!children || !children.length) return;
            for (var i = 0; i < children.length; i++)
            {
                var child = children[i], type = child.type;
                var tagName = tagNames[type] || "div"; 
                var plugin = PluginNameMatchs[type];
                if (!plugin) continue;
                var element = document.createElement(tagName);
                g.panel.find(".m-panel-content").append(element);
                var childOp = $.extend({},child);
                childOp.type = null;
                $(element)[plugin](childOp);
            }
        },
        collapse: function ()
        {
            var g = this, p = this.options;
            var toggle = g.panel.find(".m-panel-header .m-panel-header-toggle:first");
            if (toggle.hasClass("m-panel-header-toggle-hide")) return;
            g.toggle();
        },
        expand: function ()
        {
            var g = this, p = this.options;
            var toggle = g.panel.find(".m-panel-header .m-panel-header-toggle:first");
            if (!toggle.hasClass("m-panel-header-toggle-hide")) return;
            g.toggle();
        },
        toggle : function()
        {
            var g = this, p = this.options;
            var toggle = g.panel.find(".m-panel-header .m-panel-header-toggle:first");
            if (toggle.hasClass("m-panel-header-toggle-hide"))
            {
                toggle.removeClass("m-panel-header-toggle-hide");
            } else
            {
                toggle.addClass("m-panel-header-toggle-hide");
            }
            g.panel.find(".m-panel-content:first").toggle("normal");
        },
        _setShowToggle:function(v)
        {
            var g = this, p = this.options;
            var header = g.panel.find(".m-panel-header:first");
            if (v)
            {
                var toggle = $("<div class='m-panel-header-toggle'></div>");
                toggle.appendTo(header);
            } else
            {
                header.find(".m-panel-header-toggle").remove();
            }
        },
        _setContent: function (v)
        {
            var g = this, p = this.options;
            var content = g.panel.find(".m-panel-content:first");
            if (v)
            {
                content.html(v);
            }
        },
        _setUrl: function (url)
        {
            var g = this, p = this.options;
            var content = g.panel.find(".m-panel-content:first");
            if (url)
            {
                g.jiframe = $("<iframe frameborder='0'></iframe>");
                var framename = p.frameName ? p.frameName : "portalpanel" + new Date().getTime();
                g.jiframe.attr("name", framename);
                g.jiframe.attr("id", framename);
                content.prepend(g.jiframe); 

                setTimeout(function ()
                {
                    if (content.find(".m-panel-loading:first").length == 0)
                        content.append("<div class='m-panel-loading' style='display:block;'></div>");
                    var iframeloading = $(".m-panel-loading:first", content);
                    g.jiframe[0].panel = g;//增加窗口对panel对象的引用
                    /*
                    可以在子窗口这样使用：
                    var panel = frameElement.panel;
                    var panelData = dialog.get('data');//获取data参数
                    panel.set('title','新标题'); //设置标题
                    panel.close();//关闭dialog 
                    */
                    g.jiframe.attr("src", p.url).bind('load.panel', function ()
                    {
                        iframeloading.hide();
                        g.trigger('loaded');
                    });
                    g.frame = window.frames[g.jiframe.attr("name")];
                }, 0); 
            }
        },
        _setShowMore:function(v)
        {
            var g = this, p = this.options;
            var header = g.panel.find(".m-panel-header:first");
            if (v){
                var a = $('<a class="m-panel-moreimg" onclick="javascript:void(0)" href="' + p.moreUrl + '">更多</a>');
                a.appendTo(header);
            }else{
                header.find(".m-panel-moreimg").remove();
            }
        },
        _setShowClose: function (v)
        {
            var g = this, p = this.options;
            var header = g.panel.find(".m-panel-header:first");
            if (v)
            {
                var btn = $("<div class='m-panel-header-close'></div>");
                btn.appendTo(header);
            } else
            {
                header.find(".m-panel-header-close").remove();
            }
        },
        close:function()
        {
            var g = this, p = this.options;
            if (g.trigger('close') == false) return;
            g.panel.remove();
            g.trigger('closed');
        }, 
        show: function ()
        {
            this.panel.show();
        },
        _setIcon : function(url)
        {
            var g = this;
            var header = g.panel.find(".m-panel-header:first");
            if (!url)
            {
                header.css('padding-left:','0');
                g.panel.find('img').remove();
            } else
            {
                g.panel.append('<img class="panelicon" src="' + url + '" />');
            }
        }, 
        _setWidth: function (value)
        { 
            value && this.panel.width(value);
        },
        _setHeight: function (value)
        { 
            var g = this, p = this.options;
            var header = g.panel.find(".m-panel-header:first");
            this.panel.find(".m-panel-content:first").height(value - header.height());
        },
        _setTitle: function (value)
        {
            this.panel.find(".m-panel-header span:first").text(value);
        } 
    }); 


})(jQuery);