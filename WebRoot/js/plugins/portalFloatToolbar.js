/*顶部浮动工具栏*/
(function ($)
{
    $.fn.portalFloatToolbar = function (options)
    {
        return $.portalui.run.call(this, "portalFloatToolbar", arguments);
    };
    $.fn.portalGetFloatToolbarManager = function ()
    {
        return $.portalui.run.call(this, "portalGetFloatToolbarManager", arguments);
    };

    $.portalDefaults.FloatToolbar = {
        region:'r_c'  //定义浮动工具栏位置，可用的值有：l_t, l_c, l_b, r_t, r_c, r_b
    };

    $.portalMethos.FloatToolbar = {};

    $.portalui.controls.FloatToolbar = function (element, options)
    {
        $.portalui.controls.FloatToolbar.base.constructor.call(this, element, options);
    };

    $.portalui.controls.FloatToolbar.portalExtend($.portalui.core.UIComponent, {
        __getType: function ()
        {
            return 'FloatToolbar';
        },
        __idPrev: function ()
        {
            return 'FloatToolbar';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.FloatToolbar;
        },
        _init: function ()
        {
            var p = this.options;
            $.portalui.controls.FloatToolbar.base._init.call(this);
            p.content = p.content || $(this.element).html();
        },
        _render: function ()
        {
            var g = this,p = this.options;
            g.FloatToolbar = $(this.element);

            g.FloatToolbar.addClass('m_floatbar');
            g.FloatToolbar.addClass('m_floatbar_' + p.region);

            var uls = $(g.FloatToolbar.children());
            if(uls.size() != 1){
                alert('格式不正确！');
                return;
            }

            uls.addClass('nav');
            var lis = uls.children();
            $.each(lis,function(i,__li){
                $(__li).find('div').addClass('mar_t10');
                if(i == lis.size() - 1){
                    $(__li).addClass('bor0');
                }
            });
        }
    })
})(jQuery);