/*单选框*/
(function ($)
{

    $.fn.portalRadio = function ()
    {
        return $.portalui.run.call(this, "portalRadio", arguments);
    };

    $.fn.portalGetRadioManager = function ()
    {
        return $.portalui.run.call(this, "portalGetRadioManager", arguments);
    };

    $.portalDefaults.Radio = { disabled: false };

    $.portalMethos.Radio = {};

    $.portalui.controls.Radio = function (element, options)
    {
        $.portalui.controls.Radio.base.constructor.call(this, element, options);
    };
    $.portalui.controls.Radio.portalExtend($.portalui.controls.Input, {
        __getType: function ()
        {
            return 'Radio';
        },
        __idPrev: function ()
        {
            return 'Radio';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.Radio;
        },
        _render: function ()
        {
            var g = this, p = this.options;
            g.input = $(this.element);
            g.link = $('<a href="javascript:void(0)" class="m-radio"></a>');
            g.wrapper = g.input.addClass('m-hidden').wrap('<div class="m-radio-wrapper"></div>').parent();
            g.wrapper.prepend(g.link);
            g.input.change(function ()
            {
                if (this.checked)
                {
                    g.link.addClass('m-radio-checked');
                }
                else
                {
                    g.link.removeClass('m-radio-checked');
                }
                return true;
            });
            g.link.click(function ()
            {
                g._doclick();
            });
            g.wrapper.hover(function ()
            {
                if (!p.disabled)
                    $(this).addClass("m-over");
            }, function ()
            {
                $(this).removeClass("m-over");
            });
            this.element.checked && g.link.addClass('m-radio-checked');

            if (this.element.id)
            {
                $("label[for=" + this.element.id + "]").click(function ()
                {
                    g._doclick();
                });
            }
            g.set(p);
        },
        setValue: function (value)
        {
            var g = this, p = this.options;
            if (!value)
            {
                g.input[0].checked = false;
                g.link.removeClass('m-radio-checked');
            }
            else
            {
                g.input[0].checked = true;
                g.link.addClass('m-radio-checked');
            }
        },
        getValue: function ()
        {
            return this.input[0].checked;
        },
        setEnabled: function ()
        {
            this.input.attr('disabled', false);
            this.wrapper.removeClass("m-disabled");
            this.options.disabled = false;
        },
        setDisabled: function ()
        {
            this.input.attr('disabled', true);
            this.wrapper.addClass("m-disabled");
            this.options.disabled = true;
        },
        updateStyle: function ()
        {
            if (this.input.attr('disabled'))
            {
                this.wrapper.addClass("m-disabled");
                this.options.disabled = true;
            }
            if (this.input[0].checked)
            {
                this.link.addClass('m-checkbox-checked');
            }
            else
            {
                this.link.removeClass('m-checkbox-checked');
            }
        },
        _doclick: function ()
        {
            var g = this, p = this.options;
            if (g.input.attr('disabled')) { return false; }
            g.input.trigger('click').trigger('change');
            var formEle;
            if (g.input[0].form) formEle = g.input[0].form;
            else formEle = document;
            $("input:radio[name=" + g.input[0].name + "]", formEle).not(g.input).trigger("change");
            return false;
        }
    });


})(jQuery);