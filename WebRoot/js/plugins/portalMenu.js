/*顶部菜单*/
(function ($)
{
    $.fn.portalMenu = function (options)
    {
        return $.portalui.run.call(this, "portalMenu", arguments);
    };
    $.fn.portalGetMenuManager = function ()
    {
        return $.portalui.run.call(this, "portalGetMenuManager", arguments);
    };

    $.portalDefaults.Menu = {
        itemwidth:140,  //顶级菜单，每项宽度
        width: 0,   //组件宽度，默认为0，表示填满父容器
        top: 0,     //组件相对于父容器的top位置，默认为0
        left: 0,     //组件相对于父容器的left位置，默认为0
        showicons: false,    //是否显示图标，默认为false，不显示
        callback: undefined      //点击“我的”时的回调函数
    };

    $.portalMethos.Menu = {};

    $.portalui.controls.Menu = function (element, options)
    {
        $.portalui.controls.Menu.base.constructor.call(this, element, options);
    };

    $.portalui.controls.Menu.portalExtend($.portalui.core.UIComponent, {
        __getType: function ()
        {
            return 'Menu';
        },
        __idPrev: function ()
        {
            return 'Menu';
        },
        _extendMethods: function ()
        {
            return $.portalMethos.Menu;
        },
        _init: function ()
        {
            var p = this.options;
            $.portalui.controls.Menu.base._init.call(this);
            p.content = p.content || $(this.element).html();
        },
        _render: function ()
        {
            /**
             * items参数(运用于li标签中，作用li标签的属性)：
             *      title:菜单标题
             *      type:url/function/其它（空或不指定）
             *      href:url地址，当type=url时起作用
             *      function:js语句，当type=function时起作用
             * */
            var g = this,p = this.options;
            g.Menu = $(this.element);

            var uls = $(g.Menu.children());

            if(uls.size() != 1){
                alert('格式不正确！');
                return;
            }

            var _menu =  $('<div class="m-nav"></div>');
            var _ul = $('<ul></ul>');
            _menu.append(_ul);

            var lis = uls.children();
            $.each(lis,function(i,__li){ //一级
                var item = $(__li);

                var _li = $('<li class="menu" style="position:relative;;width: ' + p.itemwidth + 'px"></li>');

                var _span = $('<span class="btnsel" style="display: none;"></span>');
                _li.append(_span);
                _li.append(g._createAtag(item));

                _li.append(g._appendSecondLevel(item.html()));      //调用第二层封装

                //事件绑定
                _li.mouseover(function(){
                    _span.css('display','block');
                    _li.children('.popdiv').show();

                    _li.css('background','#8ec320');
                    _li.css('color','#fff');
                    _li.css('text-decoration','none');

                });
                _li.mouseout(function(){
                    _span.css('display','none');
                    _li.children('.popdiv').hide();

                    _li.css('background','');
                    _li.css('color','#fff');
                    _li.css('text-decoration','none');
                });

                _ul.append(_li);
            });

            if(p.showicons){            //显示我的图标
                var _li = $('<li class="my"></li>');
                var _span = $('<span style="display: block;"></span>');
                var _a = $('<a href="javascript:void(0)">我的</a>');

                _li.click(function(){
                    if(_span.is(":visible")){
                        if(p.callback != undefined){
                            p.callback(true);
                        }
                        _span.hide();
                    }else{
                        if(p.callback != undefined){
                            p.callback(false);
                        }
                        _span.show();
                    }
                }); //点击我的时，执行动画

                _li.append(_span);
                _li.append((_a));
                _ul.append(_li);
            }

            g.Menu.empty();
            g.Menu.append(_menu);
        },
        _appendSecondLevel:function(ul){
            var g = this,p = this.options;

            if(ul == ""){
                return "";
            }else{
                var _div1 = $('<div class="popdiv" style="position:absolute;display: none;"></div>');
                var _div2 = $('<div class="menu-main"></div>');
                _div1.append(_div2);

                var lis = $(ul).children();
                $.each(lis,function(i,_li){ //二级
                    var _dl = $('<dl></dl>');
                    var _dt = $('<dt></dt>');

                    _dt.append(g._createAtag($(_li),true));
                    _dl.append(_dt);

                    _dl.append(g._appendThirdLevel($(_li).html()));

                    _div2.append(_dl);
                });

                return _div1;
            }
        },
        _appendThirdLevel:function(ul){
            var g = this,p = this.options;

            if(ul == ""){
                return "";
            }else{
                var _dd = $('<dd></dd>');

                var lis = $(ul).children();
                $.each(lis,function(i,_li){ //三级
                    _dd.append(g._createAtag($(_li),true));
                })

                return  _dd;
            }
        },
        _createAtag:function(item,mustA){
            var _a;
            if(item.attr("type") == 'url'){
                _a = $('<a onclick="javascript:void(0)" href="' + item.attr("href") + '">' + item.attr("title") + '</a>');
            }else if(item.attr("type") == 'function'){
                _a = $('<a onclick="' + item.attr("onclick") + '" href="javascript:void(0)">' + item.attr("title") + '</a>');
            }else{
                if(mustA)
                    _a = $('<a onclick="" href="">' + item.attr("title") + '</a>');
                else
                    _a = item.attr("title");
            }

            return _a;
        }
    })
})(jQuery);