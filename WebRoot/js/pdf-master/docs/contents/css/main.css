body {
}
.starter-template {
  padding: 0 15px;
}
.navbar-brand {
  padding: 4px 15px;
}
.navbar-brand img {
  height: 42px;
}
.navbar {
  border-color: #e5e7e8;
}
.navbar-default .navbar-nav > .active > a,
.navbar-default .navbar-nav > .active > a:hover,
.navbar-default .navbar-nav > .active > a:focus  {
  background-color: #fff;
  border: 1px solid #e5e7e8;
  border-width: 0 1px;
  position: relative;
  top: 1px;
}

footer {
  padding-top: 40px;
  padding-bottom: 40px;
  margin-top: 100px;
  color: #777;
  text-align: center;
  border-top: 1px solid #E5E5E5;
}

/* code styling */

code {
  font-family: 'Anonymous Pro', monospace;
  font-size: 0.85em;
  color: #000;
}

pre code {
  display: block;
  line-height: 1.1;
}

p code {
  padding: 0.1em 0.3em 0.2em;
  border-radius: 0.3em;
  position: relative;
  top: -0.15em;
  background: #444;
  color: #fff;
  white-space: nowrap;
}

/* syntax hl stuff */

code.lang-markdown {
  color: #424242;
}

code.lang-markdown .header,
code.lang-markdown .strong {
  font-weight: bold;
}

code.lang-markdown .emphasis {
  font-style: italic;
}

code.lang-markdown .horizontal_rule,
code.lang-markdown .link_label,
code.lang-markdown .code,
code.lang-markdown .header,
code.lang-markdown .link_url {
  color: #555;
}

code.lang-markdown .blockquote,
code.lang-markdown .bullet {
  color: #bbb;
}

/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
.tomorrow-comment, pre .comment, pre .title {
  color: #8e908c;
}

.tomorrow-red, pre .variable, pre .attribute, pre .tag, pre .regexp, pre .ruby .constant, pre .xml .tag .title, pre .xml .pi, pre .xml .doctype, pre .html .doctype, pre .css .id, pre .css .class, pre .css .pseudo {
  color: #c82829;
}

.tomorrow-orange, pre .number, pre .preprocessor, pre .built_in, pre .literal, pre .params, pre .constant {
  color: #f5871f;
}

.tomorrow-yellow, pre .class, pre .ruby .class .title, pre .css .rules .attribute {
  color: #eab700;
}

.tomorrow-green, pre .string, pre .value, pre .inheritance, pre .header, pre .ruby .symbol, pre .xml .cdata {
  color: #718c00;
}

.tomorrow-aqua, pre .css .hexcolor {
  color: #3e999f;
}

.tomorrow-blue, pre .function, pre .python .decorator, pre .python .title, pre .ruby .function .title, pre .ruby .title .keyword, pre .perl .sub, pre .javascript .title, pre .coffeescript .title {
  color: #4271ae;
}

.tomorrow-purple, pre .keyword, pre .javascript .function {
  color: #8959a8;
}
