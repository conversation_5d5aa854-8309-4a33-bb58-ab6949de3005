# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ÕÕ¡Õ­Õ¸ÖÕ¤ Õ§Õ»
previous_label=ÕÕ¡Õ­Õ¸ÖÕ¤Õ¨
next.title=ÕÕ¡Õ»Õ¸ÖÕ¤ Õ§Õ»
next_label=ÕÕ¡Õ»Õ¸ÖÕ¤Õ¨

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Õ§Õ»
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages={{pagesCount}}-Õ«Ö\u0020
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}}-Õ¨ {{pagesCount}})-Õ«Ö

zoom_out.title=ÕÕ¸ÖÖÕ¡ÖÕ¶Õ¥Õ¬
zoom_out_label=ÕÕ¸ÖÖÕ¡ÖÕ¶Õ¥Õ¬
zoom_in.title=Ô½Õ¸Õ·Õ¸ÖÕ¡ÖÕ¶Õ¥Õ¬
zoom_in_label=Ô½Õ¸Õ·Õ¸ÖÕ¡ÖÕ¶Õ¥Õ¬
zoom.title=Ô½Õ¸Õ·Õ¸ÖÕ¡ÖÕ¸ÖÕ´
presentation_mode.title=Ô±Õ¶ÖÕ¶Õ¥Õ¬ Õ¶Õ¥ÖÕ¯Õ¡ÕµÕ¡ÖÕ´Õ¡Õ¶ Õ¥Õ²Õ¡Õ¶Õ¡Õ¯Õ«Õ¶
presentation_mode_label=ÕÕ¥ÖÕ¯Õ¡ÕµÕ¡ÖÕ´Õ¡Õ¶ Õ¥Õ²Õ¡Õ¶Õ¡Õ¯
open_file.title=Ô²Õ¡ÖÕ¥Õ¬ Õ¶Õ«Õ·ÖÕ¨
open_file_label=Ô²Õ¡ÖÕ¥Õ¬
print.title=ÕÕºÕ¥Õ¬
print_label=ÕÕºÕ¥Õ¬
download.title=Ô²Õ¥Õ¼Õ¶Õ¥Õ¬
download_label=Ô²Õ¥Õ¼Õ¶Õ¥Õ¬
bookmark.title=Ô¸Õ¶Õ©Õ¡ÖÕ«Õ¯ Õ¿Õ¥Õ½ÖÕ¸Õ¾ (ÕºÕ¡Õ¿Õ³Õ§Õ¶Õ¥Õ¬ Õ¯Õ¡Õ´ Õ¢Õ¡ÖÕ¥Õ¬ Õ¶Õ¸Ö ÕºÕ¡Õ¿Õ¸ÖÕ°Õ¡Õ¶Õ¸ÖÕ´)
bookmark_label=Ô¸Õ¶Õ©Õ¡ÖÕ«Õ¯ Õ¿Õ¥Õ½Ö

# Secondary toolbar and context menu
tools.title=Ô³Õ¸ÖÕ®Õ«ÖÕ¶Õ¥Ö
tools_label=Ô³Õ¸ÖÕ®Õ«ÖÕ¶Õ¥Ö
first_page.title=Ô³Õ¶Õ¡Õ¬ Õ¤Õ§ÕºÕ« Õ¡Õ¼Õ¡Õ»Õ«Õ¶ Õ§Õ»
first_page.label=Ô³Õ¶Õ¡Õ¬ Õ¤Õ§ÕºÕ« Õ¡Õ¼Õ¡Õ»Õ«Õ¶ Õ§Õ»
first_page_label=Ô³Õ¶Õ¡Õ¬ Õ¤Õ§ÕºÕ« Õ¡Õ¼Õ¡Õ»Õ«Õ¶ Õ§Õ»
last_page.title=Ô³Õ¶Õ¡Õ¬ Õ¤Õ§ÕºÕ« Õ¾Õ¥ÖÕ»Õ«Õ¶ Õ§Õ»
last_page.label=Ô³Õ¶Õ¡Õ¬ Õ¤Õ§ÕºÕ« Õ¾Õ¥ÖÕ»Õ«Õ¶ Õ§Õ»
last_page_label=Ô³Õ¶Õ¡Õ¬ Õ¤Õ§ÕºÕ« Õ¾Õ¥ÖÕ»Õ«Õ¶ Õ§Õ»
page_rotate_cw.title=ÕÕ¿Õ¿Õ¥Õ¬ ÕªÕ¡Õ´Õ¡ÖÕ¸ÕµÖÕ« Õ½Õ¬Õ¡ÖÕ« Õ¸ÖÕ²Õ²Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢
page_rotate_cw.label=ÕÕ¿Õ¿Õ¥Õ¬ ÕªÕ¡Õ´Õ¡ÖÕ¸ÕµÖÕ« Õ½Õ¬Õ¡ÖÕ« Õ¸ÖÕ²Õ²Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢
page_rotate_cw_label=ÕÕ¿Õ¿Õ¥Õ¬ ÕªÕ¡Õ´Õ¡ÖÕ¸ÕµÖÕ« Õ½Õ¬Õ¡ÖÕ« Õ¸ÖÕ²Õ²Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢
page_rotate_ccw.title=ÕÕ¿Õ¿Õ¥Õ¬ ÕªÕ¡Õ´Õ¡ÖÕ¸ÕµÖÕ« Õ½Õ¬Õ¡ÖÕ« Õ°Õ¡Õ¯Õ¡Õ¼Õ¡Õ¯ Õ¸ÖÕ²Õ²Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢
page_rotate_ccw.label=ÕÕ¿Õ¿Õ¥Õ¬ ÕªÕ¡Õ´Õ¡ÖÕ¸ÕµÖÕ« Õ½Õ¬Õ¡ÖÕ« Õ°Õ¡Õ¯Õ¡Õ¼Õ¡Õ¯ Õ¸ÖÕ²Õ²Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢
page_rotate_ccw_label=ÕÕ¿Õ¿Õ¥Õ¬ ÕªÕ¡Õ´Õ¡ÖÕ¸ÕµÖÕ« Õ½Õ¬Õ¡ÖÕ« Õ°Õ¡Õ¯Õ¡Õ¼Õ¡Õ¯ Õ¸ÖÕ²Õ²Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢

cursor_text_select_tool.title=ÕÕ«Õ¡ÖÕ¶Õ¥Õ¬ Õ£ÖÕ¸ÕµÕ© Õ¨Õ¶Õ¿ÖÕ¥Õ¬Õ¸Ö Õ£Õ¸ÖÕ®Õ«ÖÕ¨
cursor_text_select_tool_label=Ô³ÖÕ¸ÖÕ¡Õ®Ö Õ¨Õ¶Õ¿ÖÕ¥Õ¬Õ¸Ö Õ£Õ¸ÖÕ®Õ«Ö
cursor_hand_tool.title=ÕÕ«Õ¡ÖÕ¶Õ¥Õ¬ Õ±Õ¥Õ¼ÖÕ« Õ£Õ¸ÖÕ®Õ«ÖÕ¨
cursor_hand_tool_label=ÕÕ¥Õ¼ÖÕ« Õ£Õ¸ÖÕ®Õ«Ö

scroll_vertical.title=Ô±ÖÕ£Õ¿Õ¡Õ£Õ¸ÖÕ®Õ¥Õ¬ Õ¸ÖÕ²Õ²Õ¡Õ°Õ¡ÕµÕ¥Õ¡Ö Õ¸Õ¬Õ¸ÖÕ¸ÖÕ´
scroll_vertical_label=ÕÖÕ²Õ²Õ¡Õ°Õ¡ÕµÕ¥Õ¡Ö Õ¸Õ¬Õ¸ÖÕ¸ÖÕ´
scroll_horizontal.title=Ô±ÖÕ£Õ¿Õ¡Õ£Õ¸ÖÕ®Õ¥Õ¬ Õ°Õ¸ÖÕ«Õ¦Õ¸Õ¶Õ¡Õ¯Õ¡Õ¶ Õ¸Õ¬Õ¸ÖÕ¸ÖÕ´
scroll_horizontal_label=ÕÕ¸ÖÕ«Õ¦Õ¸Õ¶Õ¡Õ¯Õ¡Õ¶ Õ¸Õ¬Õ¸ÖÕ¸ÖÕ´
scroll_wrapped.title=Ô±ÖÕ£Õ¿Õ¡Õ£Õ¸ÖÕ®Õ¥Õ¬ ÖÕ¡Õ©Õ¡Õ©Õ¸ÖÕ¡Õ® Õ¸Õ¬Õ¸ÖÕ¸ÖÕ´
scroll_wrapped_label=ÕÕ¡Õ©Õ¡Õ©Õ¸ÖÕ¡Õ® Õ¸Õ¬Õ¸ÖÕ¸ÖÕ´

spread_none.title=ÕÕ« Õ´Õ«Õ¡ÖÕ§Ö Õ§Õ»Õ« Õ¯Õ¸Õ¶Õ¿Õ¥ÖÕ½Õ¿Õ¸ÖÕ´
spread_none_label=ÕÕ¯Õ¡Õµ Õ¯Õ¸Õ¶Õ¿Õ¥ÖÕ½Õ¿
spread_odd.title=ÕÕ«Õ¡ÖÕ§Ö Õ§Õ»Õ« Õ¯Õ¸Õ¶Õ¿Õ¥ÖÕ½Õ¿Õ«Õ¶ Õ½Õ¯Õ½Õ¥Õ¬Õ¸Õ¾Õ Õ¯Õ¥Õ¶Õ¿ Õ°Õ¡Õ´Õ¡ÖÕ¡Õ¯Õ¡Õ¬Õ¸ÖÕ¡Õ® Õ§Õ»Õ¥ÖÕ¸Õ¾
spread_odd_label=ÕÕ¡ÖÕ¡ÖÖÕ«Õ¶Õ¡Õ¯ Õ¯Õ¸Õ¶Õ¿Õ¥ÖÕ½Õ¿
spread_even.title=ÕÕ«Õ¡ÖÕ§Ö Õ§Õ»Õ« Õ¯Õ¸Õ¶Õ¿Õ¥ÖÕ½Õ¿Õ«Õ¶ Õ½Õ¯Õ½Õ¥Õ¬Õ¸Õ¾Õ Õ¦Õ¸ÕµÕ£ Õ°Õ¡Õ´Õ¡ÖÕ¡Õ¯Õ¡Õ¬Õ¸ÖÕ¡Õ® Õ§Õ»Õ¥ÖÕ¸Õ¾

# Document properties dialog box
document_properties.title=ÕÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ« Õ°Õ¡Õ¿Õ¯Õ¸ÖÕ©Õ«ÖÕ¶Õ¶Õ¥ÖÕ¨â¦
document_properties_label=ÕÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ« ÕµÕ¡Õ¿Õ¯Õ¸ÖÕ©Õ«ÖÕ¶Õ¶Õ¥ÖÕ¨â¦
document_properties_file_name=ÕÕ«Õ·ÖÕ« Õ¡Õ¶Õ¸ÖÕ¶Õ¨â¤
document_properties_file_size=ÕÕ«Õ·Ö Õ¹Õ¡ÖÕ¨.
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} Ô¿Ô² ({{size_b}} Õ¢Õ¡ÕµÕ©)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÕÔ² ({{size_b}} Õ¢Õ¡ÕµÕ©)
document_properties_title=ÕÕ¥ÖÕ¶Õ¡Õ£Õ«Ö
document_properties_author=ÕÕ¥Õ²Õ«Õ¶Õ¡Õ¯â¤
document_properties_subject=Õ¡Õ¼Õ¡ÖÕ¯Õ¡Õµ
document_properties_keywords=ÕÕ«Õ´Õ¶Õ¡Õ¢Õ¡Õ¼Õ¥Ö
document_properties_creation_date=ÕÕ¿Õ¥Õ²Õ®Õ´Õ¡Õ¶ Õ¡Õ´Õ½Õ¡Õ©Õ«Ö
document_properties_modification_date=ÕÕ¸ÖÕ¸Õ­Õ¸ÖÕ©Õ¥Õ¡Õ¶ Õ¡Õ´Õ½Õ¡Õ©Õ«Ö.
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=ÕÕ¿Õ¥Õ²Õ®Õ¸Õ²
document_properties_producer=PDF-Õ« Ô±ÖÕ¿Õ¡Õ¤ÖÕ¸Õ²Õ¨.
document_properties_version=PDF-Õ« Õ¿Õ¡ÖÕ¢Õ¥ÖÕ¡Õ¯Õ¨.
document_properties_page_count=Ô·Õ»Õ¥ÖÕ« ÖÕ¡Õ¶Õ¡Õ¯Õ¨.
document_properties_page_size=Ô·Õ»Õ« Õ¹Õ¡ÖÕ¨.
document_properties_page_size_unit_inches=Õ¸ÖÕ´
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=Õ¸ÖÕ²Õ²Õ¡Õ±Õ«Õ£
document_properties_page_size_orientation_landscape=Õ°Õ¸ÖÕ«Õ¦Õ¸Õ¶Õ¡Õ¯Õ¡Õ¶
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ÕÕ¡Õ´Õ¡Õ¯
document_properties_page_size_name_legal=Ô±ÖÖÕ«Õ¶Õ¡Õ¯Õ¡Õ¶
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Ô±ÖÕ¡Õ£ Õ¾Õ¥Õ¢ Õ¤Õ«Õ¿Õ¸ÖÕ´â¤
document_properties_linearized_yes=Ô±ÕµÕ¸
document_properties_linearized_no=ÕÕ¹
document_properties_close=ÕÕ¡Õ¯Õ¥Õ¬

print_progress_message=ÕÕ¡Õ­Õ¡ÕºÕ¡Õ¿ÖÕ¡Õ½Õ¿Õ¸ÖÕ´ Õ§ ÖÕ¡Õ½Õ¿Õ¡Õ©Õ¸ÖÕ²Õ©Õ¨ Õ¿ÕºÕ¥Õ¬Õ¸ÖÕ¶â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÕÕ¥Õ²Õ¡ÖÕ¯Õ¥Õ¬

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ÕÕ¸Õ­Õ¡ÖÕ¯Õ¥Õ¬ Õ¯Õ¸Õ²Õ¡ÕµÕ«Õ¶ Õ¾Õ¡Õ°Õ¡Õ¶Õ¡Õ¯Õ¨
toggle_sidebar_notification.title=ÕÕ¸Õ­Õ¡ÖÕ¯Õ¥Õ¬ Õ¯Õ¸Õ²Õ¡ÕµÕ«Õ¶ Õ¾Õ¡Õ°Õ¡Õ¶Õ¡Õ¯Õ¨ (ÖÕ¡Õ½Õ¿Õ¡Õ©Õ¸ÖÕ²Õ©Õ¨ ÕºÕ¡ÖÕ¸ÖÕ¶Õ¡Õ¯Õ¸ÖÕ´ Õ§ Õ¸ÖÖÕ¸ÖÕ¡Õ£Õ«Õ®/Õ¯ÖÕ¸ÖÕ¤)
toggle_sidebar_label=ÕÕ¸Õ­Õ¡ÖÕ¯Õ¥Õ¬ Õ¯Õ¸Õ²Õ¡ÕµÕ«Õ¶ Õ¾Õ¡Õ°Õ¡Õ¶Õ¡Õ¯Õ¨
document_outline.title=ÕÕ¸ÖÖÕ¡Õ¤ÖÕ¥Õ¬ ÖÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ« Õ¸ÖÖÕ¸ÖÕ¡Õ£Õ«Õ®Õ¨ (Õ¯ÖÕ¯Õ¶Õ¡Õ¯Õ« Õ½Õ¥Õ²Õ´Õ§ÖÕ Õ´Õ«Õ¡ÖÕ¸ÖÕ¶Õ¥ÖÕ¨ Õ¨Õ¶Õ¤Õ¡ÖÕ±Õ¡Õ¯Õ¥Õ¬Õ¸Ö/Õ¯Õ¸Õ®Õ¯Õ¥Õ¬Õ¸Ö Õ°Õ¡Õ´Õ¡Ö)
document_outline_label=ÕÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ« Õ¸ÖÖÕ¸ÖÕ¡Õ£Õ«Õ®
attachments.title=ÕÕ¸ÖÖÕ¡Õ¤ÖÕ¥Õ¬ Õ¯ÖÕ¸ÖÕ¤Õ¶Õ¥ÖÕ¨
attachments_label=Ô¿ÖÕ¸ÖÕ¤Õ¶Õ¥Ö
thumbs.title=ÕÕ¸ÖÖÕ¡Õ¤ÖÕ¥Õ¬ Õ´Õ¡Õ¶ÖÕ¡ÕºÕ¡Õ¿Õ¯Õ¥ÖÕ¨
thumbs_label=ÕÕ¡Õ¶ÖÕ¡ÕºÕ¡Õ¿Õ¯Õ¥Ö
findbar.title=Ô³Õ¿Õ¶Õ¥Õ¬ ÖÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ¸ÖÕ´
findbar_label=ÕÖÕ¸Õ¶Õ¸ÖÕ´

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Ô·Õ» {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ô·Õ»Õ¨ {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Ô·Õ»Õ« Õ´Õ¡Õ¶ÖÕ¡ÕºÕ¡Õ¿Õ¯Õ¥ÖÕ¨ {{page}}

# Find panel button title and messages
find_input.title=ÕÖÕ¸Õ¶Õ¸ÖÕ´
find_input.placeholder=Ô³Õ¿Õ¶Õ¥Õ¬ ÖÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ¸ÖÕ´â¦
find_previous.title=Ô³Õ¿Õ¶Õ¥Õ¬ Õ¡ÖÕ¿Õ¡ÕµÕ¡ÕµÕ¿Õ¸ÖÕ©Õ¥Õ¡Õ¶ Õ¶Õ¡Õ­Õ¸ÖÕ¤ Õ¡ÖÕ¿Õ¡ÕµÕ¡ÕµÕ¿Õ¸ÖÕ©Õ«ÖÕ¶Õ¨
find_previous_label=ÕÕ¡Õ­Õ¸ÖÕ¤Õ¨
find_next.title=Ô³Õ¿Õ«Ö Õ¡ÖÕ¿Õ¡ÕµÕ¡ÕµÕ¿Õ¸ÖÕ©Õ¥Õ¡Õ¶ ÕµÕ¡Õ»Õ¸ÖÕ¤ Õ¡ÖÕ¿Õ¡ÕµÕ¡ÕµÕ¿Õ¸ÖÕ©Õ«ÖÕ¶Õ¨
find_next_label=ÕÕ¡Õ»Õ¸ÖÕ¤Õ¨
find_highlight=Ô³Õ¸ÖÕ¶Õ¡Õ¶Õ·Õ¥Õ¬ Õ¢Õ¸Õ¬Õ¸ÖÕ¨
find_match_case_label=ÕÕ¡Õ·Õ¸ÖÕ« Õ¡Õ¼Õ¶Õ¥Õ¬ Õ°Õ¡Õ¶Õ£Õ¡Õ´Õ¡Õ¶ÖÕ¨
find_entire_word_label=Ô±Õ´Õ¢Õ¸Õ²Õ» Õ¢Õ¡Õ¼Õ¥ÖÕ¨
find_reached_top=ÕÕ¡Õ½Õ¥Õ¬ Õ¥Ö ÖÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ« Õ¾Õ¥ÖÕ¥ÖÕ«Õ¶,Õ·Õ¡ÖÕ¸ÖÕ¶Õ¡Õ¯Õ¥Õ¬ Õ¶Õ¥ÖÖÕ¥ÖÕ«Ö
find_reached_bottom=ÕÕ¡Õ½Õ¥Õ¬ Õ§Ö ÖÕ¡Õ½Õ¿Õ¡Õ©Õ²Õ©Õ« Õ¾Õ¥ÖÕ»Õ«Õ¶, Õ·Õ¡ÖÕ¸ÖÕ¶Õ¡Õ¯Õ¥Õ¬ Õ¾Õ¥ÖÕ¥ÖÕ«Ö
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ Õ°Õ¸Õ£Õ¶Õ¡Õ¯Õ«(Õ¨Õ¶Õ¤Õ°Õ¡Õ¶Õ¸ÖÖ) ]}
find_match_count[one]={{current}} {{total}}-Õ« Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ«Ö
find_match_count[two]={{current}} {{total}}-Õ« Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ«Ö
find_match_count[few]={{current}} {{total}}-Õ« Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ«Ö
find_match_count[many]={{current}} {{total}}-Õ« Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ«Ö
find_match_count[other]={{current}} {{total}}-Õ« Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ«Ö
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ Õ°Õ¸Õ£Õ¶Õ¡Õ¯Õ« (Õ½Õ¡Õ°Õ´Õ¡Õ¶Õ¨) ]}
find_match_count_limit[zero]=Ô±ÖÕ¥Õ¬Õ«Õ¶ ÖÕ¡Õ¶ {{limit}} Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ¨
find_match_count_limit[one]=Ô±ÖÕ¥Õ¬Õ«Õ¶ ÖÕ¡Õ¶ {{limit}} Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¨
find_match_count_limit[two]=Ô±ÖÕ¥Õ¬Õ«Õ¶ ÖÕ¡Õ¶ {{limit}} Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ¨
find_match_count_limit[few]=Ô±ÖÕ¥Õ¬Õ«Õ¶ ÖÕ¡Õ¶ {{limit}} Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ¨
find_match_count_limit[many]=Ô±ÖÕ¥Õ¬Õ«Õ¶ ÖÕ¡Õ¶ {{limit}} Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ¨
find_match_count_limit[other]=Ô±ÖÕ¥Õ¬Õ«Õ¶ ÖÕ¡Õ¶ {{limit}} Õ°Õ¡Õ´Õ¨Õ¶Õ¯Õ¶Õ¸ÖÕ´Õ¶Õ¥ÖÕ¨
find_not_found=Ô±ÖÕ¿Õ¡ÕµÕ¡ÕµÕ¿Õ¸ÖÕ©Õ«ÖÕ¶Õ¨ Õ¹Õ£Õ¿Õ¶Õ¸ÖÕ¥Ö

# Error panel labels
error_more_info=Ô±ÖÕ¥Õ¬Õ« Õ·Õ¡Õ¿ Õ¿Õ¥Õ²Õ¥Õ¯Õ¸ÖÕ©Õ«ÖÕ¶
error_less_info=ÕÕ«Õ¹ Õ¿Õ¥Õ²Õ¥Õ¯Õ¸ÖÕ©Õ«ÖÕ¶
error_close=ÕÕ¡Õ¯Õ¥Õ¬
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (Õ¯Õ¡Õ¼Õ¸ÖÖÕ¸ÖÕ´Õ¨. {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ô³ÖÕ¸ÖÕ©Õ«ÖÕ¶Õ¨. {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ÕÕ¥Õ²Õ». {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Õ¶Õ«Õ·Öâ¤ {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=ÕÕ¸Õ²Õ¨. {{line}}
rendering_error=ÕÕ­Õ¡Õ¬ Õ§ Õ¿Õ¥Õ²Õ« Õ¸ÖÕ¶Õ¥ÖÕ¥Õ¬ Õ§Õ»Õ« Õ´Õ¥Õ¯Õ¶Õ¡Õ¢Õ¡Õ¶Õ´Õ¡Õ¶ ÕªÕ¡Õ´Õ¡Õ¶Õ¡Õ¯

# Predefined zoom values
page_scale_width=Ô·Õ»Õ« Õ¬Õ¡ÕµÕ¶ÖÕ¨
page_scale_fit=ÕÕ¡ÖÕ´Õ¡ÖÕ¥ÖÕ¶Õ¥Õ¬ Õ§Õ»Õ¨
page_scale_auto=Ô»Õ¶ÖÕ¶Õ¡Õ·Õ­Õ¡Õ¿ Õ­Õ¸Õ·Õ¸ÖÕ¡ÖÕ¸ÖÕ´
page_scale_actual=Ô»ÖÕ¡Õ¯Õ¡Õ¶ Õ¹Õ¡ÖÕ¨
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ÕÕ­Õ¡Õ¬
loading_error=PDF Õ¶Õ«Õ·ÖÕ¨ Õ¢Õ¡ÖÕ¥Õ¬Õ«Õ½ Õ½Õ­Õ¡Õ¬ Õ§ Õ¿Õ¥Õ²Õ« Õ¸ÖÕ¶Õ¥ÖÕ¥Õ¬Ö
invalid_file_error=ÕÕ­Õ¡Õ¬ Õ¯Õ¡Õ´ Õ¾Õ¶Õ¡Õ½Õ¸ÖÕ¡Õ® PDF Õ¶Õ«Õ·ÖÖ
missing_file_error=PDF Õ¶Õ«Õ·ÖÕ¨ Õ¢Õ¡ÖÕ¡Õ¯Õ¡Õ«ÖÕ´ Õ§Ö
unexpected_response_error=ÕÕºÕ¡Õ½Õ¡ÖÕ¯Õ«Õ¹Õ« Õ¡Õ¶Õ½ÕºÕ¡Õ½Õ¥Õ¬Õ« ÕºÕ¡Õ¿Õ¡Õ½Õ­Õ¡Õ¶Ö

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Ô¾Õ¡Õ¶Õ¸Õ©Õ¸ÖÕ©Õ«ÖÕ¶]
password_label=ÕÕ¸ÖÕ¿ÖÕ¡Õ£ÖÕ§Ö  Õ£Õ¡Õ²Õ¿Õ¶Õ¡Õ¢Õ¡Õ¼Õ¨ Õ¡ÕµÕ½ PDF Õ¶Õ«Õ·ÖÕ¨ Õ¢Õ¡ÖÕ¥Õ¬Õ¸Ö Õ°Õ¡Õ´Õ¡Ö
password_invalid=Ô³Õ¡Õ²Õ¿Õ¶Õ¡Õ¢Õ¡Õ¼Õ¨ Õ½Õ­Õ¡Õ¬ Õ§: Ô¿ÖÕ¯Õ«Õ¶ ÖÕ¸ÖÕ±Õ§Ö:
password_ok=Ô¼Õ¡Ö
password_cancel=ÕÕ¥Õ²Õ¡ÖÕ¯Õ¥Õ¬

printing_not_supported=Ô¶Õ£Õ¸ÖÕ·Õ¡ÖÕ¸ÖÕ´. ÕÕºÕ¥Õ¬Õ¨ Õ¡Õ´Õ¢Õ¸Õ²Õ»Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢ Õ¹Õ« Õ¡Õ»Õ¡Õ¯ÖÕ¸ÖÕ¸ÖÕ´ Õ¦Õ¶Õ¶Õ¡ÖÕ¯Õ«Õ¹Õ« Õ¯Õ¸Õ²Õ´Õ«ÖÖ
printing_not_ready=Ô¶Õ£Õ¸ÖÕ·Õ¡ÖÕ¸ÖÕ´. PDFÖÕ¨ Õ¡Õ´Õ¢Õ¸Õ²Õ»Õ¸ÖÕ©Õ¥Õ¡Õ´Õ¢ Õ¹Õ« Õ¢Õ¥Õ¼Õ¶Õ¡ÖÕ¸ÖÕ¸ÖÕ¥Õ¬ Õ¿ÕºÕ¥Õ¬Õ¸Ö Õ°Õ¡Õ´Õ¡ÖÖ
web_fonts_disabled=ÕÕ¥Õ¢-Õ¿Õ¡Õ¼Õ¡Õ¿Õ¥Õ½Õ¡Õ¯Õ¶Õ¥ÖÕ¨ Õ¡Õ¶Õ»Õ¡Õ¿Õ¸ÖÕ¡Õ® Õ¥Õ¶. Õ°Õ¶Õ¡ÖÕ¡ÖÕ¸Ö Õ¹Õ§ Õ¡ÖÕ£Õ¿Õ¡Õ£Õ¸ÖÕ®Õ¥Õ¬ Õ¶Õ¥ÖÕ¯Õ¡Õ¼Õ¸ÖÖÕ¸ÖÕ¡Õ® PDF Õ¿Õ¡Õ¼Õ¡Õ¿Õ¥Õ½Õ¡Õ¯Õ¶Õ¥ÖÕ¨Ö
