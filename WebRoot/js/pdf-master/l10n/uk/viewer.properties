# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ÐÐ¾Ð¿ÐµÑÐµÐ´Ð½Ñ ÑÑÐ¾ÑÑÐ½ÐºÐ°
previous_label=ÐÐ¾Ð¿ÐµÑÐµÐ´Ð½Ñ
next.title=ÐÐ°ÑÑÑÐ¿Ð½Ð° ÑÑÐ¾ÑÑÐ½ÐºÐ°
next_label=ÐÐ°ÑÑÑÐ¿Ð½Ð°

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Ð¡ÑÐ¾ÑÑÐ½ÐºÐ°
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=ÑÐ· {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} ÑÐ· {{pagesCount}})

zoom_out.title=ÐÐ¼ÐµÐ½ÑÐ¸ÑÐ¸
zoom_out_label=ÐÐ¼ÐµÐ½ÑÐ¸ÑÐ¸
zoom_in.title=ÐÐ±ÑÐ»ÑÑÐ¸ÑÐ¸
zoom_in_label=ÐÐ±ÑÐ»ÑÑÐ¸ÑÐ¸
zoom.title=ÐÐ°ÑÑÑÐ°Ð±
presentation_mode.title=ÐÐµÑÐµÐ¹ÑÐ¸ Ð² ÑÐµÐ¶Ð¸Ð¼ Ð¿ÑÐµÐ·ÐµÐ½ÑÐ°ÑÑÑ
presentation_mode_label=Ð ÐµÐ¶Ð¸Ð¼ Ð¿ÑÐµÐ·ÐµÐ½ÑÐ°ÑÑÑ
open_file.title=ÐÑÐ´ÐºÑÐ¸ÑÐ¸ ÑÐ°Ð¹Ð»
open_file_label=ÐÑÐ´ÐºÑÐ¸ÑÐ¸
print.title=ÐÑÑÐº
print_label=ÐÑÑÐº
download.title=ÐÐ°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸
download_label=ÐÐ°Ð²Ð°Ð½ÑÐ°Ð¶Ð¸ÑÐ¸
bookmark.title=ÐÐ¾ÑÐ¾ÑÐ½Ð¸Ð¹ Ð²Ð¸Ð³Ð»ÑÐ´ (ÐºÐ¾Ð¿ÑÑÐ²Ð°ÑÐ¸ ÑÐ¸ Ð²ÑÐ´ÐºÑÐ¸ÑÐ¸ Ð² Ð½Ð¾Ð²Ð¾Ð¼Ñ Ð²ÑÐºÐ½Ñ)
bookmark_label=ÐÐ¾ÑÐ¾ÑÐ½Ð¸Ð¹ Ð²Ð¸Ð³Ð»ÑÐ´

# Secondary toolbar and context menu
tools.title=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½ÑÐ¸
tools_label=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½ÑÐ¸
first_page.title=ÐÐ° Ð¿ÐµÑÑÑ ÑÑÐ¾ÑÑÐ½ÐºÑ
first_page.label=ÐÐ° Ð¿ÐµÑÑÑ ÑÑÐ¾ÑÑÐ½ÐºÑ
first_page_label=ÐÐ° Ð¿ÐµÑÑÑ ÑÑÐ¾ÑÑÐ½ÐºÑ
last_page.title=ÐÐ° Ð¾ÑÑÐ°Ð½Ð½Ñ ÑÑÐ¾ÑÑÐ½ÐºÑ
last_page.label=ÐÐ° Ð¾ÑÑÐ°Ð½Ð½Ñ ÑÑÐ¾ÑÑÐ½ÐºÑ
last_page_label=ÐÐ° Ð¾ÑÑÐ°Ð½Ð½Ñ ÑÑÐ¾ÑÑÐ½ÐºÑ
page_rotate_cw.title=ÐÐ¾Ð²ÐµÑÐ½ÑÑÐ¸ Ð·Ð° Ð³Ð¾Ð´Ð¸Ð½Ð½Ð¸ÐºÐ¾Ð²Ð¾Ñ ÑÑÑÑÐ»ÐºÐ¾Ñ
page_rotate_cw.label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÐ¸ Ð·Ð° Ð³Ð¾Ð´Ð¸Ð½Ð½Ð¸ÐºÐ¾Ð²Ð¾Ñ ÑÑÑÑÐ»ÐºÐ¾Ñ
page_rotate_cw_label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÐ¸ Ð·Ð° Ð³Ð¾Ð´Ð¸Ð½Ð½Ð¸ÐºÐ¾Ð²Ð¾Ñ ÑÑÑÑÐ»ÐºÐ¾Ñ
page_rotate_ccw.title=ÐÐ¾Ð²ÐµÑÐ½ÑÑÐ¸ Ð¿ÑÐ¾ÑÐ¸ Ð³Ð¾Ð´Ð¸Ð½Ð½Ð¸ÐºÐ¾Ð²Ð¾Ñ ÑÑÑÑÐ»ÐºÐ¸
page_rotate_ccw.label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÐ¸ Ð¿ÑÐ¾ÑÐ¸ Ð³Ð¾Ð´Ð¸Ð½Ð½Ð¸ÐºÐ¾Ð²Ð¾Ñ ÑÑÑÑÐ»ÐºÐ¸
page_rotate_ccw_label=ÐÐ¾Ð²ÐµÑÐ½ÑÑÐ¸ Ð¿ÑÐ¾ÑÐ¸ Ð³Ð¾Ð´Ð¸Ð½Ð½Ð¸ÐºÐ¾Ð²Ð¾Ñ ÑÑÑÑÐ»ÐºÐ¸

cursor_text_select_tool.title=Ð£Ð²ÑÐ¼ÐºÐ½ÑÑÐ¸ ÑÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ Ð²Ð¸Ð±Ð¾ÑÑ ÑÐµÐºÑÑÑ
cursor_text_select_tool_label=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ Ð²Ð¸Ð±Ð¾ÑÑ ÑÐµÐºÑÑÑ
cursor_hand_tool.title=Ð£Ð²ÑÐ¼ÐºÐ½ÑÑÐ¸ ÑÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ "Ð ÑÐºÐ°"
cursor_hand_tool_label=ÐÐ½ÑÑÑÑÐ¼ÐµÐ½Ñ "Ð ÑÐºÐ°"

scroll_vertical.title=ÐÐ¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ Ð²ÐµÑÑÐ¸ÐºÐ°Ð»ÑÐ½Ðµ Ð¿ÑÐ¾ÐºÑÑÑÑÐ²Ð°Ð½Ð½Ñ
scroll_vertical_label=ÐÐµÑÑÐ¸ÐºÐ°Ð»ÑÐ½Ðµ Ð¿ÑÐ¾ÐºÑÑÑÑÐ²Ð°Ð½Ð½Ñ
scroll_horizontal.title=ÐÐ¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ Ð³Ð¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»ÑÐ½Ðµ Ð¿ÑÐ¾ÐºÑÑÑÑÐ²Ð°Ð½Ð½Ñ
scroll_horizontal_label=ÐÐ¾ÑÐ¸Ð·Ð¾Ð½ÑÐ°Ð»ÑÐ½Ðµ Ð¿ÑÐ¾ÐºÑÑÑÑÐ²Ð°Ð½Ð½Ñ
scroll_wrapped.title=ÐÐ¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ Ð¼Ð°ÑÑÑÐ°Ð±Ð¾Ð²Ð°Ð½Ðµ Ð¿ÑÐ¾ÐºÑÑÑÑÐ²Ð°Ð½Ð½Ñ
scroll_wrapped_label=ÐÐ°ÑÑÑÐ°Ð±Ð¾Ð²Ð°Ð½Ðµ Ð¿ÑÐ¾ÐºÑÑÑÑÐ²Ð°Ð½Ð½Ñ

spread_none.title=ÐÐµ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ¾Ð²ÑÐ²Ð°ÑÐ¸ ÑÐ¾Ð·Ð³Ð¾ÑÐ½ÑÑÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸
spread_none_label=ÐÐµÐ· ÑÐ¾Ð·Ð³Ð¾ÑÐ½ÑÑÐ¸Ñ ÑÑÐ¾ÑÑÐ½Ð¾Ðº
spread_odd.title=Ð Ð¾Ð·Ð³Ð¾ÑÐ½ÑÑÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð¿Ð¾ÑÐ¸Ð½Ð°ÑÑÑÑÑ Ð· Ð½ÐµÐ¿Ð°ÑÐ½Ð¸Ñ Ð½Ð¾Ð¼ÐµÑÑÐ²
spread_odd_label=ÐÐµÐ¿Ð°ÑÐ½Ñ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð·Ð»ÑÐ²Ð°
spread_even.title=Ð Ð¾Ð·Ð³Ð¾ÑÐ½ÑÑÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð¿Ð¾ÑÐ¸Ð½Ð°ÑÑÑÑÑ Ð· Ð¿Ð°ÑÐ½Ð¸Ñ Ð½Ð¾Ð¼ÐµÑÑÐ²
spread_even_label=ÐÐ°ÑÐ½Ñ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ Ð·Ð»ÑÐ²Ð°

# Document properties dialog box
document_properties.title=ÐÐ»Ð°ÑÑÐ¸Ð²Ð¾ÑÑÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_label=ÐÐ»Ð°ÑÑÐ¸Ð²Ð¾ÑÑÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°â¦
document_properties_file_name=ÐÐ°Ð·Ð²Ð° ÑÐ°Ð¹Ð»Ð°:
document_properties_file_size=Ð Ð¾Ð·Ð¼ÑÑ ÑÐ°Ð¹Ð»Ð°:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} ÐÐ ({{size_b}} bytes)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÐÐ ({{size_b}} bytes)
document_properties_title=ÐÐ°Ð³Ð¾Ð»Ð¾Ð²Ð¾Ðº:
document_properties_author=ÐÐ²ÑÐ¾Ñ:
document_properties_subject=Ð¢ÐµÐ¼Ð°:
document_properties_keywords=ÐÐ»ÑÑÐ¾Ð²Ñ ÑÐ»Ð¾Ð²Ð°:
document_properties_creation_date=ÐÐ°ÑÐ° ÑÑÐ²Ð¾ÑÐµÐ½Ð½Ñ:
document_properties_modification_date=ÐÐ°ÑÐ° Ð·Ð¼ÑÐ½Ð¸:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}, {{time}}
document_properties_creator=Ð¡ÑÐ²Ð¾ÑÐµÐ½Ð¾:
document_properties_producer=ÐÐ¸ÑÐ¾Ð±Ð½Ð¸Ðº PDF:
document_properties_version=ÐÐµÑÑÑÑ PDF:
document_properties_page_count=ÐÑÐ»ÑÐºÑÑÑÑ ÑÑÐ¾ÑÑÐ½Ð¾Ðº:
document_properties_page_size=Ð Ð¾Ð·Ð¼ÑÑ ÑÑÐ¾ÑÑÐ½ÐºÐ¸:
document_properties_page_size_unit_inches=Ð´ÑÐ¹Ð¼ÑÐ²
document_properties_page_size_unit_millimeters=Ð¼Ð¼
document_properties_page_size_orientation_portrait=ÐºÐ½Ð¸Ð¶ÐºÐ¾Ð²Ð°
document_properties_page_size_orientation_landscape=Ð°Ð»ÑÐ±Ð¾Ð¼Ð½Ð°
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Ð¨Ð²Ð¸Ð´ÐºÐ¸Ð¹ Ð¿ÐµÑÐµÐ³Ð»ÑÐ´ Ð² ÐÐ½ÑÐµÑÐ½ÐµÑÑ:
document_properties_linearized_yes=Ð¢Ð°Ðº
document_properties_linearized_no=ÐÑ
document_properties_close=ÐÐ°ÐºÑÐ¸ÑÐ¸

print_progress_message=ÐÑÐ´Ð³Ð¾ÑÐ¾Ð²ÐºÐ° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑ Ð´Ð¾ Ð´ÑÑÐºÑâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=Ð¡ÐºÐ°ÑÑÐ²Ð°ÑÐ¸

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=ÐÑÑÐ½Ð° Ð¿Ð°Ð½ÐµÐ»Ñ
toggle_sidebar_notification.title=ÐÐµÑÐµÐ¼ÐºÐ½ÑÑÐ¸ Ð±ÑÑÐ½Ñ Ð¿Ð°Ð½ÐµÐ»Ñ (Ð´Ð¾ÐºÑÐ¼ÐµÐ½Ñ Ð¼Ð°Ñ Ð²Ð¼ÑÑÑ/Ð²ÐºÐ»Ð°Ð´ÐµÐ½Ð½Ñ)
toggle_sidebar_label=ÐÐµÑÐµÐ¼ÐºÐ½ÑÑÐ¸ Ð±ÑÑÐ½Ñ Ð¿Ð°Ð½ÐµÐ»Ñ
document_outline.title=ÐÐ¾ÐºÐ°Ð·Ð°ÑÐ¸ ÑÑÐµÐ¼Ñ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑ (Ð¿Ð¾Ð´Ð²ÑÐ¹Ð½Ð¸Ð¹ ÐºÐ»ÑÐº Ð´Ð»Ñ ÑÐ¾Ð·Ð³Ð¾ÑÑÐ°Ð½Ð½Ñ/Ð·Ð³Ð¾ÑÑÐ°Ð½Ð½Ñ ÐµÐ»ÐµÐ¼ÐµÐ½ÑÑÐ²)
document_outline_label=Ð¡ÑÐµÐ¼Ð° Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÐ°
attachments.title=ÐÐ¾ÐºÐ°Ð·Ð°ÑÐ¸ Ð¿ÑÐ¸ÐºÑÑÐ¿Ð»ÐµÐ½Ð½Ñ
attachments_label=ÐÑÐ¸ÐºÑÑÐ¿Ð»ÐµÐ½Ð½Ñ
thumbs.title=ÐÐ¾ÐºÐ°Ð·ÑÐ²Ð°ÑÐ¸ ÐµÑÐºÑÐ·Ð¸
thumbs_label=ÐÑÐºÑÐ·Ð¸
findbar.title=ÐÐ½Ð°Ð¹ÑÐ¸ Ð² Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑ
findbar_label=ÐÐ¾ÑÑÐº

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Ð¡ÑÐ¾ÑÑÐ½ÐºÐ° {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Ð¡ÑÐ¾ÑÑÐ½ÐºÐ° {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ÐÑÐºÑÐ· ÑÑÐ¾ÑÑÐ½ÐºÐ¸ {{page}}

# Find panel button title and messages
find_input.title=ÐÐ½Ð°Ð¹ÑÐ¸
find_input.placeholder=ÐÐ½Ð°Ð¹ÑÐ¸ Ð² Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑâ¦
find_previous.title=ÐÐ½Ð°Ð¹ÑÐ¸ Ð¿Ð¾Ð¿ÐµÑÐµÐ´Ð½Ñ Ð²ÑÐ¾Ð´Ð¶ÐµÐ½Ð½Ñ ÑÑÐ°Ð·Ð¸
find_previous_label=ÐÐ¾Ð¿ÐµÑÐµÐ´Ð½Ñ
find_next.title=ÐÐ½Ð°Ð¹ÑÐ¸ Ð½Ð°ÑÑÑÐ¿Ð½Ðµ Ð²ÑÐ¾Ð´Ð¶ÐµÐ½Ð½Ñ ÑÑÐ°Ð·Ð¸
find_next_label=ÐÐ°ÑÑÑÐ¿Ð½Ðµ
find_highlight=ÐÑÐ´ÑÐ²ÑÑÐ¸ÑÐ¸ Ð²ÑÐµ
find_match_case_label=Ð ÑÑÐ°ÑÑÐ²Ð°Ð½Ð½ÑÐ¼ ÑÐµÐ³ÑÑÑÑÑ
find_entire_word_label=Ð¦ÑÐ»Ñ ÑÐ»Ð¾Ð²Ð°
find_reached_top=ÐÐ¾ÑÑÐ³Ð½ÑÑÐ¾ Ð¿Ð¾ÑÐ°ÑÐºÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑ, Ð¿ÑÐ¾Ð´Ð¾Ð²Ð¶ÐµÐ½Ð¾ Ð· ÐºÑÐ½ÑÑ
find_reached_bottom=ÐÐ¾ÑÑÐ³Ð½ÑÑÐ¾ ÐºÑÐ½ÑÑ Ð´Ð¾ÐºÑÐ¼ÐµÐ½ÑÑ, Ð¿ÑÐ¾Ð´Ð¾Ð²Ð¶ÐµÐ½Ð¾ Ð· Ð¿Ð¾ÑÐ°ÑÐºÑ
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]={{current}} Ð·Ð±ÑÐ³ ÑÐ· {{total}}
find_match_count[two]={{current}} Ð·Ð±ÑÐ³Ð¸ Ð· {{total}}
find_match_count[few]={{current}} Ð·Ð±ÑÐ³ÑÐ² ÑÐ· {{total}}
find_match_count[many]={{current}} Ð·Ð±ÑÐ³ÑÐ² ÑÐ· {{total}}
find_match_count[other]={{current}} Ð·Ð±ÑÐ³ÑÐ² ÑÐ· {{total}}
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=ÐÐ¾Ð½Ð°Ð´ {{limit}} Ð·Ð±ÑÐ³ÑÐ²
find_match_count_limit[one]=ÐÑÐ»ÑÑÐµ, Ð½ÑÐ¶ {{limit}} Ð·Ð±ÑÐ³
find_match_count_limit[two]=ÐÑÐ»ÑÑÐµ, Ð½ÑÐ¶ {{limit}} Ð·Ð±ÑÐ³Ð¸
find_match_count_limit[few]=ÐÑÐ»ÑÑÐµ, Ð½ÑÐ¶ {{limit}} Ð·Ð±ÑÐ³ÑÐ²
find_match_count_limit[many]=ÐÐ¾Ð½Ð°Ð´ {{limit}} Ð·Ð±ÑÐ³ÑÐ²
find_match_count_limit[other]=ÐÐ¾Ð½Ð°Ð´ {{limit}} Ð·Ð±ÑÐ³ÑÐ²
find_not_found=Ð¤ÑÐ°Ð·Ñ Ð½Ðµ Ð·Ð½Ð°Ð¹Ð´ÐµÐ½Ð¾

# Error panel labels
error_more_info=ÐÑÐ»ÑÑÐµ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ
error_less_info=ÐÐµÐ½ÑÐµ ÑÐ½ÑÐ¾ÑÐ¼Ð°ÑÑÑ
error_close=ÐÐ°ÐºÑÐ¸ÑÐ¸
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ÐÐ¾Ð²ÑÐ´Ð¾Ð¼Ð»ÐµÐ½Ð½Ñ: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Ð¡ÑÐµÐº: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ð¤Ð°Ð¹Ð»: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Ð ÑÐ´Ð¾Ðº: {{line}}
rendering_error=ÐÑÐ´ ÑÐ°Ñ Ð²Ð¸Ð²ÐµÐ´ÐµÐ½Ð½Ñ ÑÑÐ¾ÑÑÐ½ÐºÐ¸ ÑÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ°.

# Predefined zoom values
page_scale_width=ÐÐ° ÑÐ¸ÑÐ¸Ð½Ð¾Ñ
page_scale_fit=ÐÐ¼ÑÑÑÐ¸ÑÐ¸
page_scale_auto=ÐÐ²ÑÐ¾Ð¼Ð°ÑÑÑÐ°Ð±
page_scale_actual=ÐÑÐ¹ÑÐ½Ð¸Ð¹ ÑÐ¾Ð·Ð¼ÑÑ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=ÐÐ¾Ð¼Ð¸Ð»ÐºÐ°
loading_error=ÐÑÐ´ ÑÐ°Ñ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð½Ñ PDF ÑÑÐ°Ð»Ð°ÑÑ Ð¿Ð¾Ð¼Ð¸Ð»ÐºÐ°.
invalid_file_error=ÐÐµÐ´ÑÐ¹ÑÐ½Ð¸Ð¹ Ð°Ð±Ð¾ Ð¿Ð¾ÑÐºÐ¾Ð´Ð¶ÐµÐ½Ð¸Ð¹ PDF-ÑÐ°Ð¹Ð».
missing_file_error=ÐÑÐ´ÑÑÑÐ½ÑÐ¹ PDF-ÑÐ°Ð¹Ð».
unexpected_response_error=ÐÐµÐ¾ÑÑÐºÑÐ²Ð°Ð½Ð° Ð²ÑÐ´Ð¿Ð¾Ð²ÑÐ´Ñ ÑÐµÑÐ²ÐµÑÐ°.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}}, {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}}-Ð°Ð½Ð½Ð¾ÑÐ°ÑÑÑ]
password_label=ÐÐ²ÐµÐ´ÑÑÑ Ð¿Ð°ÑÐ¾Ð»Ñ Ð´Ð»Ñ Ð²ÑÐ´ÐºÑÐ¸ÑÑÑ ÑÑÐ¾Ð³Ð¾ PDF-ÑÐ°Ð¹Ð»Ð°.
password_invalid=ÐÐµÐ²ÑÑÐ½Ð¸Ð¹ Ð¿Ð°ÑÐ¾Ð»Ñ. Ð¡Ð¿ÑÐ¾Ð±ÑÐ¹ÑÐµ ÑÐµ.
password_ok=ÐÐ°ÑÐ°Ð·Ð´
password_cancel=Ð¡ÐºÐ°ÑÑÐ²Ð°ÑÐ¸

printing_not_supported=ÐÐ¾Ð¿ÐµÑÐµÐ´Ð¶ÐµÐ½Ð½Ñ: Ð¦ÐµÐ¹ Ð±ÑÐ°ÑÐ·ÐµÑ Ð½Ðµ Ð¿Ð¾Ð²Ð½ÑÑÑÑ Ð¿ÑÐ´ÑÑÐ¸Ð¼ÑÑ Ð´ÑÑÐº.
printing_not_ready=ÐÐ¾Ð¿ÐµÑÐµÐ´Ð¶ÐµÐ½Ð½Ñ: PDF Ð½Ðµ Ð¿Ð¾Ð²Ð½ÑÑÑÑ Ð·Ð°Ð²Ð°Ð½ÑÐ°Ð¶ÐµÐ½Ð¸Ð¹ Ð´Ð»Ñ Ð´ÑÑÐºÑ.
web_fonts_disabled=ÐÐµÐ±-ÑÑÐ¸ÑÑÐ¸ Ð²Ð¸Ð¼ÐºÐ½ÐµÐ½Ð¾: Ð½ÐµÐ¼Ð¾Ð¶Ð»Ð¸Ð²Ð¾ Ð²Ð¸ÐºÐ¾ÑÐ¸ÑÑÐ°ÑÐ¸ Ð²Ð±ÑÐ´Ð¾Ð²Ð°Ð½Ñ Ñ PDF ÑÑÐ¸ÑÑÐ¸.
