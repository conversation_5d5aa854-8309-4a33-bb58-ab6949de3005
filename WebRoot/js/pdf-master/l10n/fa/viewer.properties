# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ØµÙØ­ÙÙ ÙØ¨ÙÛ
previous_label=ÙØ¨ÙÛ
next.title=ØµÙØ­ÙÙ Ø¨Ø¹Ø¯Û
next_label=Ø¨Ø¹Ø¯Û

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ØµÙØ­Ù
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=Ø§Ø² {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}}Ø§Ø² {{pagesCount}})

zoom_out.title=Ú©ÙÚÚ©âÙÙØ§ÛÛ
zoom_out_label=Ú©ÙÚÚ©âÙÙØ§ÛÛ
zoom_in.title=Ø¨Ø²Ø±Ú¯âÙÙØ§ÛÛ
zoom_in_label=Ø¨Ø²Ø±Ú¯âÙÙØ§ÛÛ
zoom.title=Ø²ÙÙ
presentation_mode.title=ØªØºÛÛØ± Ø¨Ù Ø­Ø§ÙØª Ø§Ø±Ø§Ø¦Ù
presentation_mode_label=Ø­Ø§ÙØª Ø§Ø±Ø§Ø¦Ù
open_file.title=Ø¨Ø§Ø² Ú©Ø±Ø¯Ù Ù¾Ø±ÙÙØ¯Ù
open_file_label=Ø¨Ø§Ø² Ú©Ø±Ø¯Ù
print.title=ÚØ§Ù¾
print_label=ÚØ§Ù¾
download.title=Ø¨Ø§Ø±Ú¯ÛØ±Û
download_label=Ø¨Ø§Ø±Ú¯ÛØ±Û
bookmark.title=ÙÙØ§Û ÙØ¹ÙÛ (Ø±ÙÙÙØ´Øª Ù ÛØ§ ÙØ´Ø§Ù Ø¯Ø§Ø¯Ù Ø¯Ø± Ù¾ÙØ¬Ø±Ù Ø¬Ø¯ÛØ¯)
bookmark_label=ÙÙØ§Û ÙØ¹ÙÛ

# Secondary toolbar and context menu
tools.title=Ø§Ø¨Ø²Ø§Ø±ÙØ§
tools_label=Ø§Ø¨Ø²Ø§Ø±ÙØ§
first_page.title=Ø¨Ø±Ù Ø¨Ù Ø§ÙÙÛÙ ØµÙØ­Ù
first_page.label=Ø¨Ø±Ù ÛÙ Ø§ÙÙÛÙ ØµÙØ­Ù
first_page_label=Ø¨Ø±Ù Ø¨Ù Ø§ÙÙÛÙ ØµÙØ­Ù
last_page.title=Ø¨Ø±Ù Ø¨Ù Ø¢Ø®Ø±ÛÙ ØµÙØ­Ù
last_page.label=Ø¨Ø±Ù Ø¨Ù Ø¢Ø®Ø±ÛÙ ØµÙØ­Ù
last_page_label=Ø¨Ø±Ù Ø¨Ù Ø¢Ø®Ø±ÛÙ ØµÙØ­Ù
page_rotate_cw.title=ÚØ±Ø®Ø´ Ø³Ø§Ø¹ØªÚ¯Ø±Ø¯
page_rotate_cw.label=ÚØ±Ø®Ø´ Ø³Ø§Ø¹ØªÚ¯Ø±Ø¯
page_rotate_cw_label=ÚØ±Ø®Ø´ Ø³Ø§Ø¹ØªÚ¯Ø±Ø¯
page_rotate_ccw.title=ÚØ±Ø®Ø´ Ù¾Ø§Ø¯ Ø³Ø§Ø¹ØªÚ¯Ø±Ø¯
page_rotate_ccw.label=ÚØ±Ø®Ø´ Ù¾Ø§Ø¯ Ø³Ø§Ø¹ØªÚ¯Ø±Ø¯
page_rotate_ccw_label=ÚØ±Ø®Ø´ Ù¾Ø§Ø¯ Ø³Ø§Ø¹ØªÚ¯Ø±Ø¯

cursor_text_select_tool.title=ÙØ¹Ø§Ù Ú©Ø±Ø¯Ù Ø§Ø¨Ø²Ø§Ø±Ù Ø§ÙØªØ®Ø§Ø¨Ù ÙØªÙ
cursor_text_select_tool_label=Ø§Ø¨Ø²Ø§Ø±Ù Ø§ÙØªØ®Ø§Ø¨Ù ÙØªÙ
cursor_hand_tool.title=ÙØ¹Ø§Ù Ú©Ø±Ø¯Ù Ø§Ø¨Ø²Ø§Ø±Ù Ø¯Ø³Øª
cursor_hand_tool_label=Ø§Ø¨Ø²Ø§Ø± Ø¯Ø³Øª

scroll_vertical.title=Ø§Ø³ØªÙØ§Ø¯Ù Ø§Ø² Ù¾ÛÙØ§ÛØ´ Ø¹ÙÙØ¯Û
scroll_vertical_label=Ù¾ÛÙØ§ÛØ´ Ø¹ÙÙØ¯Û
scroll_horizontal.title=Ø§Ø³ØªÙØ§Ø¯Ù Ø§Ø² Ù¾ÛÙØ§ÛØ´ Ø§ÙÙÛ
scroll_horizontal_label=Ù¾ÛÙØ§ÛØ´ Ø§ÙÙÛ


# Document properties dialog box
document_properties.title=Ø®ØµÙØµÛØ§Øª Ø³ÙØ¯...
document_properties_label=Ø®ØµÙØµÛØ§Øª Ø³ÙØ¯...
document_properties_file_name=ÙØ§Ù ÙØ§ÛÙ:
document_properties_file_size=Ø­Ø¬Ù Ù¾Ø±ÙÙØ¯Ù:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} Ú©ÛÙÙØ¨Ø§ÛØª ({{size_b}} Ø¨Ø§ÛØª)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} ÙÚ¯Ø§Ø¨Ø§ÛØª ({{size_b}} Ø¨Ø§ÛØª)
document_properties_title=Ø¹ÙÙØ§Ù:
document_properties_author=ÙÙÛØ³ÙØ¯Ù:
document_properties_subject=ÙÙØ¶ÙØ¹:
document_properties_keywords=Ú©ÙÛØ¯ÙØ§ÚÙâÙØ§:
document_properties_creation_date=ØªØ§Ø±ÛØ® Ø§ÛØ¬Ø§Ø¯:
document_properties_modification_date=ØªØ§Ø±ÛØ® ÙÛØ±Ø§ÛØ´:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}}Ø {{time}}
document_properties_creator=Ø§ÛØ¬Ø§Ø¯ Ú©ÙÙØ¯Ù:
document_properties_producer=Ø§ÛØ¬Ø§Ø¯ Ú©ÙÙØ¯Ù PDF:
document_properties_version=ÙØ³Ø®Ù PDF:
document_properties_page_count=ØªØ¹Ø¯Ø§Ø¯ ØµÙØ­Ø§Øª:
document_properties_page_size=Ø§ÙØ¯Ø§Ø²Ù ØµÙØ­Ù:
document_properties_page_size_unit_inches=Ø§ÛÙÚ
document_properties_page_size_unit_millimeters=ÙÛÙÛâÙØªØ±
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=ÙØ§ÙÙ
document_properties_page_size_name_legal=Ø­ÙÙÙÛ
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized_yes=Ø¨ÙÙ
document_properties_linearized_no=Ø®ÛØ±
document_properties_close=Ø¨Ø³ØªÙ

print_progress_message=Ø¢ÙØ§Ø¯Ù Ø³Ø§Ø²Û ÙØ¯Ø§Ø±Ú© Ø¨Ø±Ø§Û ÚØ§Ù¾ Ú©Ø±Ø¯Ùâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=ÙØºÙ

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Ø¨Ø§Ø² Ù Ø¨Ø³ØªÙ Ú©Ø±Ø¯Ù ÙÙØ§Ø± Ú©ÙØ§Ø±Û
toggle_sidebar_notification.title=ØªØºÛÛØ± ÙØ¶Ø¹ÛØª ÙÙØ§Ø± Ú©ÙØ§Ø±Û (Ø³ÙØ¯ Ø­Ø§ÙÛ Ø·Ø±Ø­/Ù¾ÛÙØ³Øª Ø§Ø³Øª)
toggle_sidebar_label=ØªØºÛÛØ±Ø­Ø§ÙØª ÙÙØ§Ø±Ú©ÙØ§Ø±Û
document_outline.title=ÙÙØ§ÛØ´ Ø±Ø¦ÙØ³ ÙØ·Ø§ÙØ¨ ÙØ¯Ø§Ø±Ú©(Ø¨Ø±Ø§Û Ø¨Ø§Ø²Ø´Ø¯Ù/Ø¬ÙØ¹ Ø´Ø¯Ù ÙÙÙ ÙÙØ§Ø±Ø¯ Ø¯ÙØ¨Ø§Ø± Ú©ÙÛÚ© Ú©ÙÛØ¯)
document_outline_label=Ø·Ø±Ø­ ÙÙØ´ØªØ§Ø±
attachments.title=ÙÙØ§ÛØ´ Ù¾ÛÙØ³ØªâÙØ§
attachments_label=Ù¾ÛÙØ³ØªâÙØ§
thumbs.title=ÙÙØ§ÛØ´ ØªØµØ§ÙÛØ± Ø¨ÙØ¯Ø§ÙÚ¯Ø´ØªÛ
thumbs_label=ØªØµØ§ÙÛØ± Ø¨ÙØ¯Ø§ÙÚ¯Ø´ØªÛ
findbar.title=Ø¬Ø³ØªØ¬Ù Ø¯Ø± Ø³ÙØ¯
findbar_label=Ù¾ÛØ¯Ø§ Ú©Ø±Ø¯Ù

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=ØµÙØ­Ù {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=ØªØµÙÛØ± Ø¨ÙØ¯â Ø§ÙÚ¯Ø´ØªÛ ØµÙØ­Ù {{page}}

# Find panel button title and messages
find_input.title=Ù¾ÛØ¯Ø§ Ú©Ø±Ø¯Ù
find_input.placeholder=Ù¾ÛØ¯Ø§ Ú©Ø±Ø¯Ù Ø¯Ø± Ø³ÙØ¯â¦
find_previous.title=Ù¾ÛØ¯Ø§ Ú©Ø±Ø¯Ù Ø±Ø®Ø¯Ø§Ø¯ ÙØ¨ÙÛ Ø¹Ø¨Ø§Ø±Øª
find_previous_label=ÙØ¨ÙÛ
find_next.title=Ù¾ÛØ¯Ø§ Ú©Ø±Ø¯Ù Ø±Ø®Ø¯Ø§Ø¯ Ø¨Ø¹Ø¯Û Ø¹Ø¨Ø§Ø±Øª
find_next_label=Ø¨Ø¹Ø¯Û
find_highlight=Ø¨Ø±Ø¬Ø³ØªÙ Ù ÙØ§ÛÙØ§ÛØª Ú©Ø±Ø¯Ù ÙÙÙ ÙÙØ§Ø±Ø¯
find_match_case_label=ØªØ·Ø¨ÛÙ Ú©ÙÚÚ©Û Ù Ø¨Ø²Ø±Ú¯Û Ø­Ø±ÙÙ
find_entire_word_label=ØªÙØ§Ù Ú©ÙÙÙâÙØ§
find_reached_top=Ø¨Ù Ø¨Ø§ÙØ§Û ØµÙØ­Ù Ø±Ø³ÛØ¯ÛÙØ Ø§Ø² Ù¾Ø§ÛÛÙ Ø§Ø¯Ø§ÙÙ ÙÛâØ¯ÙÛÙ
find_reached_bottom=Ø¨Ù Ø¢Ø®Ø± ØµÙØ­Ù Ø±Ø³ÛØ¯ÛÙØ Ø§Ø² Ø¨Ø§ÙØ§ Ø§Ø¯Ø§ÙÙ ÙÛâØ¯ÙÛÙ
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count[one]={{current}} Ø§Ø² {{total}} ÙØ·Ø§Ø¨ÙØª Ø¯Ø§Ø±Ø¯
find_match_count[two]={{current}} Ø§Ø² {{total}} ÙØ·Ø§Ø¨ÙØª Ø¯Ø§Ø±Ø¯
find_match_count[few]={{current}} Ø§Ø² {{total}} ÙØ·Ø§Ø¨ÙØª Ø¯Ø§Ø±Ø¯
find_match_count[many]={{current}} Ø§Ø² {{total}} ÙØ·Ø§Ø¨ÙØª Ø¯Ø§Ø±Ø¯
find_match_count[other]={{current}} Ø§Ø² {{total}} ÙØ·Ø§Ø¨ÙØª Ø¯Ø§Ø±Ø¯
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_not_found=Ø¹Ø¨Ø§Ø±Øª Ù¾ÛØ¯Ø§ ÙØ´Ø¯

# Error panel labels
error_more_info=Ø§Ø·ÙØ§Ø¹Ø§Øª Ø¨ÛØ´ØªØ±
error_less_info=Ø§Ø·ÙØ§Ø¹Ø§Øª Ú©ÙØªØ±
error_close=Ø¨Ø³ØªÙ
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=âPDF.js ÙØ±ÚÙ{{version}} â(Ø³Ø§Ø®Øª: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Ù¾ÛØ§Ù: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=ØªÙØ¯Ù: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Ù¾Ø±ÙÙØ¯Ù: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=Ø³Ø·Ø±: {{line}}
rendering_error=ÙÙÚ¯Ø§Ù Ø¨Ø§Ø±Ú¯ÛØ±Û ØµÙØ­Ù Ø®Ø·Ø§ÛÛ Ø±Ø® Ø¯Ø§Ø¯.

# Predefined zoom values
page_scale_width=Ø¹Ø±Ø¶ ØµÙØ­Ù
page_scale_fit=Ø§ÙØ¯Ø§Ø²Ù Ú©Ø±Ø¯Ù ØµÙØ­Ù
page_scale_auto=Ø¨Ø²Ø±Ú¯ÙÙØ§ÛÛ Ø®ÙØ¯Ú©Ø§Ø±
page_scale_actual=Ø§ÙØ¯Ø§Ø²Ù ÙØ§ÙØ¹Ûâ
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=Ø®Ø·Ø§
loading_error=ÙÙÚ¯Ø§Ù Ø¨Ø§Ø±Ú¯ÛØ±Û Ù¾Ø±ÙÙØ¯Ù PDF Ø®Ø·Ø§ÛÛ Ø±Ø® Ø¯Ø§Ø¯.
invalid_file_error=Ù¾Ø±ÙÙØ¯Ù PDF ÙØ§ÙØ¹ØªØ¨Ø± ÛØ§ÙØ¹ÛÙØ¨ ÙÛâØ¨Ø§Ø´Ø¯.
missing_file_error=Ù¾Ø±ÙÙØ¯Ù PDF ÛØ§ÙØª ÙØ´Ø¯.
unexpected_response_error=Ù¾Ø§Ø³Ø® Ù¾ÛØ´ Ø¨ÛÙÛ ÙØ´Ø¯Ù Ø³Ø±ÙØ±

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} Annotation]
password_label=Ø¬ÙØª Ø¨Ø§Ø² Ú©Ø±Ø¯Ù Ù¾Ø±ÙÙØ¯Ù PDF Ú¯Ø°Ø±ÙØ§ÚÙ Ø±Ø§ ÙØ§Ø±Ø¯ ÙÙØ§Ø¦ÛØ¯.
password_invalid=Ú¯Ø°Ø±ÙØ§ÚÙ ÙØ§ÙØ¹ØªØ¨Ø±. ÙØ·ÙØ§ ÙØ¬Ø¯Ø¯Ø§ ØªÙØ§Ø´ Ú©ÙÛØ¯.
password_ok=ØªØ£ÛÛØ¯
password_cancel=ÙØºÙ

printing_not_supported=ÙØ´Ø¯Ø§Ø±: ÙØ§Ø¨ÙÛØª ÚØ§Ù¾ Ø¨ÙâØ·ÙØ± Ú©Ø§ÙÙ Ø¯Ø± Ø§ÛÙ ÙØ±ÙØ±Ú¯Ø± Ù¾Ø´ØªÛØ¨Ø§ÙÛ ÙÙÛâØ´ÙØ¯.
printing_not_ready=Ø§Ø®Ø·Ø§Ø±: Ù¾Ø±ÙÙØ¯Ù PDF Ø¨Ø·ÙØ± Ú©Ø§ÙÙ Ø¨Ø§Ø±Ú¯ÛØ±Û ÙØ´Ø¯Ù Ù Ø§ÙÚ©Ø§Ù ÚØ§Ù¾ ÙØ¬ÙØ¯ ÙØ¯Ø§Ø±Ø¯.
web_fonts_disabled=ÙÙÙØª ÙØ§Û ØªØ­Øª ÙØ¨ ØºÛØ± ÙØ¹Ø§Ù Ø´Ø¯Ù Ø§ÙØ¯: Ø§ÙÚ©Ø§Ù Ø§Ø³ØªÙØ§Ø¯Ù Ø§Ø² ÙÙØ§ÛØ´ Ø¯ÙÙØ¯Ù Ø¯Ø§Ø®ÙÛ PDF ÙØ¬ÙØ¯ ÙØ¯Ø§Ø±Ø¯.
