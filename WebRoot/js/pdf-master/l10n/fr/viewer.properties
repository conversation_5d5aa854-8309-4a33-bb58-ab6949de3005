# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Page prÃ©cÃ©dente
previous_label=PrÃ©cÃ©dent
next.title=Page suivante
next_label=Suivant

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=Page
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=sur {{pagesCount}}
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=({{pageNumber}} sur {{pagesCount}})

zoom_out.title=Zoom arriÃ¨re
zoom_out_label=Zoom arriÃ¨re
zoom_in.title=Zoom avant
zoom_in_label=Zoom avant
zoom.title=Zoom
presentation_mode.title=Basculer en mode prÃ©sentation
presentation_mode_label=Mode prÃ©sentation
open_file.title=Ouvrir le fichier
open_file_label=Ouvrir le fichier
print.title=Imprimer
print_label=Imprimer
download.title=TÃ©lÃ©charger
download_label=TÃ©lÃ©charger
bookmark.title=Affichage courant (copier ou ouvrir dans une nouvelle fenÃªtre)
bookmark_label=Affichage actuel

# Secondary toolbar and context menu
tools.title=Outils
tools_label=Outils
first_page.title=Aller Ã  la premiÃ¨re page
first_page.label=Aller Ã  la premiÃ¨re page
first_page_label=Aller Ã  la premiÃ¨re page
last_page.title=Aller Ã  la derniÃ¨re page
last_page.label=Aller Ã  la derniÃ¨re page
last_page_label=Aller Ã  la derniÃ¨re page
page_rotate_cw.title=Rotation horaire
page_rotate_cw.label=Rotation horaire
page_rotate_cw_label=Rotation horaire
page_rotate_ccw.title=Rotation antihoraire
page_rotate_ccw.label=Rotation antihoraire
page_rotate_ccw_label=Rotation antihoraire

cursor_text_select_tool.title=Activer lâoutil de sÃ©lection de texte
cursor_text_select_tool_label=Outil de sÃ©lection de texte
cursor_hand_tool.title=Activer lâoutil main
cursor_hand_tool_label=Outil main

scroll_vertical.title=Utiliser le dÃ©filement vertical
scroll_vertical_label=DÃ©filement vertical
scroll_horizontal.title=Utiliser le dÃ©filement horizontal
scroll_horizontal_label=DÃ©filement horizontal
scroll_wrapped.title=Utiliser le dÃ©filement par bloc
scroll_wrapped_label=DÃ©filement par bloc

spread_none.title=Ne pas afficher les pages deux Ã  deux
spread_none_label=Pas de double affichage
spread_odd.title=Afficher les pages par deux, impaires Ã  gauche
spread_odd_label=Doubles pages, impaires Ã  gauche
spread_even.title=Afficher les pages par deux, paires Ã  gauche
spread_even_label=Doubles pages, paires Ã  gauche

# Document properties dialog box
document_properties.title=PropriÃ©tÃ©s du documentâ¦
document_properties_label=PropriÃ©tÃ©s du documentâ¦
document_properties_file_name=Nom du fichierÂ :
document_properties_file_size=Taille du fichierÂ :
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}}Â Ko ({{size_b}} octets)
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}}Â Mo ({{size_b}} octets)
document_properties_title=TitreÂ :
document_properties_author=AuteurÂ :
document_properties_subject=SujetÂ :
document_properties_keywords=Mots-clÃ©sÂ :
document_properties_creation_date=Date de crÃ©ationÂ :
document_properties_modification_date=ModifiÃ© leÂ :
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}} Ã  {{time}}
document_properties_creator=CrÃ©Ã© parÂ :
document_properties_producer=Outil de conversion PDFÂ :
document_properties_version=Version PDFÂ :
document_properties_page_count=Nombre de pagesÂ :
document_properties_page_size=Taille de la pageÂ :
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=portrait
document_properties_page_size_orientation_landscape=paysage
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=lettre
document_properties_page_size_name_legal=document juridique
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}}Â {{unit}} ({{orientation}})
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}}Â {{unit}} ({{name}}, {{orientation}})
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=Affichage rapide des pages webÂ :
document_properties_linearized_yes=Oui
document_properties_linearized_no=Non
document_properties_close=Fermer

print_progress_message=PrÃ©paration du document pour lâimpressionâ¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}Â %
print_progress_close=Annuler

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Afficher/Masquer le panneau latÃ©ral
toggle_sidebar_notification.title=Afficher/Masquer le panneau latÃ©ral (le document contient des signets/piÃ¨ces jointes)
toggle_sidebar_label=Afficher/Masquer le panneau latÃ©ral
document_outline.title=Afficher les signets du document (double-cliquer pour dÃ©velopper/rÃ©duire tous les Ã©lÃ©ments)
document_outline_label=Signets du document
attachments.title=Afficher les piÃ¨ces jointes
attachments_label=PiÃ¨ces jointes
thumbs.title=Afficher les vignettes
thumbs_label=Vignettes
findbar.title=Rechercher dans le document
findbar_label=Rechercher

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=Page {{page}}
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Page {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=Vignette de la page {{page}}

# Find panel button title and messages
find_input.title=Rechercher
find_input.placeholder=Rechercher dans le documentâ¦
find_previous.title=Trouver lâoccurrence prÃ©cÃ©dente de lâexpression
find_previous_label=PrÃ©cÃ©dent
find_next.title=Trouver la prochaine occurrence de lâexpression
find_next_label=Suivant
find_highlight=Tout surligner
find_match_case_label=Respecter la casse
find_entire_word_label=Mots entiers
find_reached_top=Haut de la page atteint, poursuite depuis la fin
find_reached_bottom=Bas de la page atteint, poursuite au dÃ©but
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]=Occurrence {{current}} sur {{total}}
find_match_count[two]=Occurrence {{current}} sur {{total}}
find_match_count[few]=Occurrence {{current}} sur {{total}}
find_match_count[many]=Occurrence {{current}} sur {{total}}
find_match_count[other]=Occurrence {{current}} sur {{total}}
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=Plus de {{limit}} correspondances
find_match_count_limit[one]=Plus de {{limit}} correspondance
find_match_count_limit[two]=Plus de {{limit}} correspondances
find_match_count_limit[few]=Plus de {{limit}} correspondances
find_match_count_limit[many]=Plus de {{limit}} correspondances
find_match_count_limit[other]=Plus de {{limit}} correspondances
find_not_found=Expression non trouvÃ©e

# Error panel labels
error_more_info=Plus dâinformations
error_less_info=Moins dâinformations
error_close=Fermer
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (identifiant de compilationÂ : {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=MessageÂ : {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=PileÂ : {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=FichierÂ : {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=LigneÂ : {{line}}
rendering_error=Une erreur sâest produite lors de lâaffichage de la page.

# Predefined zoom values
page_scale_width=Pleine largeur
page_scale_fit=Page entiÃ¨re
page_scale_auto=Zoom automatique
page_scale_actual=Taille rÃ©elle
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}Â %

# Loading indicator messages
loading_error_indicator=Erreur
loading_error=Une erreur sâest produite lors du chargement du fichier PDF.
invalid_file_error=Fichier PDF invalide ou corrompu.
missing_file_error=Fichier PDF manquant.
unexpected_response_error=RÃ©ponse inattendue du serveur.

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}} Ã  {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Annotation {{type}}]
password_label=Veuillez saisir le mot de passe pour ouvrir ce fichier PDF.
password_invalid=Mot de passe incorrect. Veuillez rÃ©essayer.
password_ok=OK
password_cancel=Annuler

printing_not_supported=AttentionÂ : lâimpression nâest pas totalement prise en charge par ce navigateur.
printing_not_ready=AttentionÂ : le PDF nâest pas entiÃ¨rement chargÃ© pour pouvoir lâimprimer.
web_fonts_disabled=Les polices web sont dÃ©sactivÃ©esÂ : impossible dâutiliser les polices intÃ©grÃ©es au PDF.
