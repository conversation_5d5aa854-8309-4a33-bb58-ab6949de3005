# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=XÃ«t wi jiitu
previous_label=Bi jiitu
next.title=XÃ«t wi ci topp
next_label=Bi ci topp

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.

zoom_out.title=WÃ Ã±Ã±i
zoom_out_label=WÃ Ã±Ã±i
zoom_in.title=Yaatal
zoom_in_label=Yaatal
zoom.title=YambalaÅ
presentation_mode.title=WaÃ±arÃ±il ci anamu wone
presentation_mode_label=Anamu Wone
open_file.title=Ubbi benn dencukaay
open_file_label=Ubbi
print.title=MÃ³ol
print_label=MÃ³ol
download.title=Yeb yi
download_label=Yeb yi
bookmark.title=Wone bi taxaw (duppi walla ubbi palanteer bu bees)
bookmark_label=Wone bi feeÃ±

# Secondary toolbar and context menu


# Document properties dialog box
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_title=Bopp:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.

# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
thumbs.title=Wone nataal yu ndaw yi
thumbs_label=Nataal yu ndaw yi
findbar.title=Gis ci biir jukki bi
findbar_label=Wut

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=XÃ«t {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=WiÃ±et bu xÃ«t {{page}}

# Find panel button title and messages
find_previous.title=Seet beneen kaddu bu ni mel te jiitu
find_previous_label=Bi jiitu
find_next.title=Seet beneen kaddu bu ni mel
find_next_label=Bi ci topp
find_highlight=Melaxal lÃ©pp
find_match_case_label=SÃ mm jÃ«mmalin wi
find_reached_top=Jot naÃ±u ndorteel xÃ«t wi, kontine dale ko ci suuf
find_reached_bottom=Jot naÃ±u jeexitalu xÃ«t wi, kontine ci ndorte
find_not_found=GisiÃ±u kaddu gi

# Error panel labels
error_more_info=Xibaar yu gÃ«n bari
error_less_info=Xibaar yu gÃ«n bari
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=Bataaxal: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Juug: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Dencukaay: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=RÃ«ddÂ : {{line}}
rendering_error=Am njumte bu am bi xÃ«t bi di wonewu.

# Predefined zoom values
page_scale_width=Yaatuwaay bu mÃ«t
page_scale_fit=XÃ«t lÃ«mm
page_scale_auto=YambalaÅ ci saa si
page_scale_actual=Dayo bi am
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=Njumte
loading_error=Am na njumte ci yebum dencukaay PDF bi.
invalid_file_error=Dencukaay PDF bi baaxul walla mu sankar.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[Karmat {{type}}]
password_ok=OK
password_cancel=Neenal

printing_not_supported=Artu: Joowkat bii nanguwul lool mool.
