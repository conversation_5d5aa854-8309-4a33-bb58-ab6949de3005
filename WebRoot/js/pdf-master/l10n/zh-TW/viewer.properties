# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=ä¸ä¸é 
previous_label=ä¸ä¸é 
next.title=ä¸ä¸é 
next_label=ä¸ä¸é 

# LOCALIZATION NOTE (page.title): The tooltip for the pageNumber input.
page.title=ç¬¬
# LOCALIZATION NOTE (of_pages): "{{pagesCount}}" will be replaced by a number
# representing the total number of pages in the document.
of_pages=é ï¼å± {{pagesCount}} é 
# LOCALIZATION NOTE (page_of_pages): "{{pageNumber}}" and "{{pagesCount}}"
# will be replaced by a number representing the currently visible page,
# respectively a number representing the total number of pages in the document.
page_of_pages=ï¼ç¬¬ {{pageNumber}} é ï¼å± {{pagesCount}} é ï¼

zoom_out.title=ç¸®å°
zoom_out_label=ç¸®å°
zoom_in.title=æ¾å¤§
zoom_in_label=æ¾å¤§
zoom.title=ç¸®æ¾
presentation_mode.title=åæè³ç°¡å ±æ¨¡å¼
presentation_mode_label=ç°¡å ±æ¨¡å¼
open_file.title=éåæªæ¡
open_file_label=éå
print.title=åå°
print_label=åå°
download.title=ä¸è¼
download_label=ä¸è¼
bookmark.title=ç®åæª¢è¦çå§å®¹ï¼è¤è£½æéåæ¼æ°è¦çªï¼
bookmark_label=ç®åæª¢è¦

# Secondary toolbar and context menu
tools.title=å·¥å·
tools_label=å·¥å·
first_page.title=è·³å°ç¬¬ä¸é 
first_page.label=è·³å°ç¬¬ä¸é 
first_page_label=è·³å°ç¬¬ä¸é 
last_page.title=è·³å°æå¾ä¸é 
last_page.label=è·³å°æå¾ä¸é 
last_page_label=è·³å°æå¾ä¸é 
page_rotate_cw.title=é æéæè½
page_rotate_cw.label=é æéæè½
page_rotate_cw_label=é æéæè½
page_rotate_ccw.title=éæéæè½
page_rotate_ccw.label=éæéæè½
page_rotate_ccw_label=éæéæè½

cursor_text_select_tool.title=éåæå­é¸æå·¥å·
cursor_text_select_tool_label=æå­é¸æå·¥å·
cursor_hand_tool.title=éåé é¢ç§»åå·¥å·
cursor_hand_tool_label=é é¢ç§»åå·¥å·

scroll_vertical.title=ä½¿ç¨åç´æ²åçé¢
scroll_vertical_label=åç´æ²å
scroll_horizontal.title=ä½¿ç¨æ°´å¹³æ²åçé¢
scroll_horizontal_label=æ°´å¹³æ²å
scroll_wrapped.title=ä½¿ç¨å¤é æ²åçé¢
scroll_wrapped_label=å¤é æ²å

spread_none.title=ä¸è¦é²è¡è·¨é é¡¯ç¤º
spread_none_label=ä¸è·¨é 
spread_odd.title=å¾å¥æ¸é éå§è·¨é 
spread_odd_label=å¥æ¸è·¨é 
spread_even.title=å¾å¶æ¸é éå§è·¨é 
spread_even_label=å¶æ¸è·¨é 

# Document properties dialog box
document_properties.title=æä»¶å§å®¹â¦
document_properties_label=æä»¶å§å®¹â¦
document_properties_file_name=æªæ¡åç¨±:
document_properties_file_size=æªæ¡å¤§å°:
# LOCALIZATION NOTE (document_properties_kb): "{{size_kb}}" and "{{size_b}}"
# will be replaced by the PDF file size in kilobytes, respectively in bytes.
document_properties_kb={{size_kb}} KBï¼{{size_b}} ä½åçµï¼
# LOCALIZATION NOTE (document_properties_mb): "{{size_mb}}" and "{{size_b}}"
# will be replaced by the PDF file size in megabytes, respectively in bytes.
document_properties_mb={{size_mb}} MBï¼{{size_b}} ä½åçµï¼
document_properties_title=æ¨é¡:
document_properties_author=ä½è:
document_properties_subject=ä¸»æ¨:
document_properties_keywords=ééµå­:
document_properties_creation_date=å»ºç«æ¥æ:
document_properties_modification_date=ä¿®æ¹æ¥æ:
# LOCALIZATION NOTE (document_properties_date_string): "{{date}}" and "{{time}}"
# will be replaced by the creation/modification date, and time, of the PDF file.
document_properties_date_string={{date}} {{time}}
document_properties_creator=å»ºç«è:
document_properties_producer=PDF ç¢çå¨:
document_properties_version=PDF çæ¬:
document_properties_page_count=é æ¸:
document_properties_page_size=é é¢å¤§å°:
document_properties_page_size_unit_inches=in
document_properties_page_size_unit_millimeters=mm
document_properties_page_size_orientation_portrait=åç´
document_properties_page_size_orientation_landscape=æ°´å¹³
document_properties_page_size_name_a3=A3
document_properties_page_size_name_a4=A4
document_properties_page_size_name_letter=Letter
document_properties_page_size_name_legal=Legal
# LOCALIZATION NOTE (document_properties_page_size_dimension_string):
# "{{width}}", "{{height}}", {{unit}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement and orientation, of the (current) page.
document_properties_page_size_dimension_string={{width}} Ã {{height}} {{unit}}ï¼{{orientation}}ï¼
# LOCALIZATION NOTE (document_properties_page_size_dimension_name_string):
# "{{width}}", "{{height}}", {{unit}}, {{name}}, and {{orientation}} will be replaced by
# the size, respectively their unit of measurement, name, and orientation, of the (current) page.
document_properties_page_size_dimension_name_string={{width}} Ã {{height}} {{unit}}ï¼{{name}}ï¼{{orientation}}ï¼
# LOCALIZATION NOTE (document_properties_linearized): The linearization status of
# the document; usually called "Fast Web View" in English locales of Adobe software.
document_properties_linearized=å¿«é Web æª¢è¦:
document_properties_linearized_yes=æ¯
document_properties_linearized_no=å¦
document_properties_close=éé

print_progress_message=æ­£å¨æºååå°æä»¶â¦
# LOCALIZATION NOTE (print_progress_percent): "{{progress}}" will be replaced by
# a numerical per cent value.
print_progress_percent={{progress}}%
print_progress_close=åæ¶

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=åæå´éæ¬
toggle_sidebar_notification.title=åæå´éæï¼æä»¶åå«å¤§ç¶±æéä»¶ï¼
toggle_sidebar_label=åæå´éæ¬
document_outline.title=é¡¯ç¤ºæä»¶å¤§ç¶±ï¼éæå±é/æºçææé ç®ï¼
document_outline_label=æä»¶å¤§ç¶±
attachments.title=é¡¯ç¤ºéä»¶
attachments_label=éä»¶
thumbs.title=é¡¯ç¤ºç¸®å
thumbs_label=ç¸®å
findbar.title=å¨æä»¶ä¸­å°æ¾
findbar_label=å°æ¾

# LOCALIZATION NOTE (page_canvas): "{{page}}" will be replaced by the page number.
page_canvas=ç¬¬ {{page}} é 
# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=ç¬¬ {{page}} é 
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=é  {{page}} çç¸®å

# Find panel button title and messages
find_input.title=å°æ¾
find_input.placeholder=å¨æä»¶ä¸­æå°â¦
find_previous.title=å°æ¾æå­åæ¬¡åºç¾çä½ç½®
find_previous_label=ä¸ä¸å
find_next.title=å°æ¾æå­ä¸æ¬¡åºç¾çä½ç½®
find_next_label=ä¸ä¸å
find_highlight=å¨é¨å¼·èª¿æ¨ç¤º
find_match_case_label=ååå¤§å°å¯«
find_entire_word_label=ç¬¦åæ´åå­
find_reached_top=å·²æå°è³æä»¶é ç«¯ï¼èªåºç«¯ç¹¼çºæå°
find_reached_bottom=å·²æå°è³æä»¶åºç«¯ï¼èªé ç«¯ç¹¼çºæå°
# LOCALIZATION NOTE (find_match_count): The supported plural forms are
# [one|two|few|many|other], with [other] as the default value.
# "{{current}}" and "{{total}}" will be replaced by a number representing the
# index of the currently active find result, respectively a number representing
# the total number of matches in the document.
find_match_count={[ plural(total) ]}
find_match_count[one]=ç¬¬ {{current}} ç­ï¼å±æ¾å° {{total}} ç­
find_match_count[two]=ç¬¬ {{current}} ç­ï¼å±æ¾å° {{total}} ç­
find_match_count[few]=ç¬¬ {{current}} ç­ï¼å±æ¾å° {{total}} ç­
find_match_count[many]=ç¬¬ {{current}} ç­ï¼å±æ¾å° {{total}} ç­
find_match_count[other]=ç¬¬ {{current}} ç­ï¼å±æ¾å° {{total}} ç­
# LOCALIZATION NOTE (find_match_count_limit): The supported plural forms are
# [zero|one|two|few|many|other], with [other] as the default value.
# "{{limit}}" will be replaced by a numerical value.
find_match_count_limit={[ plural(limit) ]}
find_match_count_limit[zero]=æ¾å°è¶é {{limit}} ç­
find_match_count_limit[one]=æ¾å°è¶é {{limit}} ç­
find_match_count_limit[two]=æ¾å°è¶é {{limit}} ç­
find_match_count_limit[few]=æ¾å°è¶é {{limit}} ç­
find_match_count_limit[many]=æ¾å°è¶é {{limit}} ç­
find_match_count_limit[other]=æ¾å°è¶é {{limit}} ç­
find_not_found=æ¾ä¸å°æå®æå­

# Error panel labels
error_more_info=æ´å¤è³è¨
error_less_info=æ´å°è³è¨
error_close=éé
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=è¨æ¯: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=å ç: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=æªæ¡: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=è¡: {{line}}
rendering_error=æç¹ªé é¢æç¼çé¯èª¤ã

# Predefined zoom values
page_scale_width=é é¢å¯¬åº¦
page_scale_fit=ç¸®æ¾è³é é¢å¤§å°
page_scale_auto=èªåç¸®æ¾
page_scale_actual=å¯¦éå¤§å°
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.
page_scale_percent={{scale}}%

# Loading indicator messages
loading_error_indicator=é¯èª¤
loading_error=è¼å¥ PDF æç¼çé¯èª¤ã
invalid_file_error=ç¡æææ¯æç PDF æªæ¡ã
missing_file_error=æ¾ä¸å° PDF æªæ¡ã
unexpected_response_error=ä¼ºæå¨åææªé æçå§å®¹ã

# LOCALIZATION NOTE (annotation_date_string): "{{date}}" and "{{time}}" will be
# replaced by the modification date, and time, of the annotation.
annotation_date_string={{date}} {{time}}

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} è¨»è§£]
password_label=è«è¼¸å¥ç¨ä¾éåæ­¤ PDF æªæ¡çå¯ç¢¼ã
password_invalid=å¯ç¢¼ä¸æ­£ç¢ºï¼è«åè©¦ä¸æ¬¡ã
password_ok=ç¢ºå®
password_cancel=åæ¶

printing_not_supported=è­¦å: æ­¤çè¦½å¨æªå®æ´æ¯æ´åå°åè½ã
printing_not_ready=è­¦å: æ­¤ PDF æªå®æä¸è¼ä»¥ä¾åå°ã
web_fonts_disabled=å·²åç¨ç¶²è·¯å­å (Web fonts): ç¡æ³ä½¿ç¨ PDF å§åµå­åã
