<div id="chromeFileAccessOverlay" class="container hidden">
  <div class="dialog">
    <div class="row">
      <!-- The extension icon (PDF.js logo) will be shown at the left, to help
           users with recognizing which checkbox they have to click when they
           visit chrome://extensions.
      -->
      <p id="chrome-pdfjs-logo-bg" style="
          display: block;
          padding-left: 60px;
          min-height: 48px;
          background-size: 48px;
          background-repeat: no-repeat;
          font-size: 14px;
          line-height: 1.8em;
          word-break: break-all;">
        Click on
        "<span id="chrome-file-access-label">Allow access to file URLs</span>"
        at
        <a id="chrome-link-to-extensions-page">chrome://extensions</a>
        <br>
        to view <span id="chrome-url-of-local-file">this PDF file.</span>
      </p>
    </div>
    <div class="row">
      <p>
        or select the file again:
        <input type="file" id="chrome-file-fallback" accept=".pdf">
      </p>
    </div>
  </div>
</div>
