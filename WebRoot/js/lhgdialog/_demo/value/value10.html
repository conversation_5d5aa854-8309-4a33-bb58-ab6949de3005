<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<style type="text/css">
body{margin:0;padding:0;}
input{border:1px solid #ddd;background:#f3f3f3;cursor:pointer;padding:2px 8px;_padding:0 5px;font-size:12px;margin:0;}
	</style>
	<script type="text/javascript">
var api = frameElement.api, W = api.opener;

function getValue()
{
    alert( W.document.getElementById('txt4').value );
};
	</script>
</head>

<body>
<p style="text-align:center;margin-top:40px;"><input id="btn1" type="button" value="组件调用页面文本框的值»" onclick="getValue();"/></p>
<p style="text-align:center;"><input id="btn2" type="button" value="组件调用页面的函数»" onclick="W.AA();"/></p>
</body>
</html>