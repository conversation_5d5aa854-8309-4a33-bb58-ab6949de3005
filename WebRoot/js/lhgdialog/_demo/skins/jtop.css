@charset "utf-8";
/*
 * lhgdialog JTop皮肤
 * (C) 2009-2011 【一丝冰凉】, http://t.qq.com/yisibl, QQ:50167214
 * This is licensed under the GNU LGPL, version 2.1 or later.
 * For details, see: http://creativecommons.org/licenses/LGPL/2.1/
 */
/*==================制作新皮肤从这里开始复制==================*/
/*基本重置*/
body{ _margin:0; }/*IE6 BUG*/
.ui_lock_scroll{ *overflow:hidden; }
.ui_lock_scroll body{ overflow:hidden; }

/*结构层*/
.ui_content,.ui_title,.ui_buttons input{ font:12px/1.333 tahoma,arial,\5b8b\4f53,sans-serif; }
table.ui_border,table.ui_dialog{ width:auto;border-spacing:0;*border-collapse:collapse; }
.ui_border td,.ui_dialog td{ padding:0; }
.ui_dialog{ background:#FFF; }

/*标题部分开始*/
.ui_title{ overflow:hidden;text-overflow:ellipsis;white-space:nowrap;display:block;cursor:move;background:#DDD;-moz-user-select:none;-webkit-user-select:none;padding:0 100px 0 0; }
.ui_title_buttons{ position:absolute;cursor:pointer;font-size:0;letter-spacing:-.5em; }

/*对齐自适应*/
.ui_main{ min-width:6em;min-width:0\9;text-align:center; }
.ui_content{ display:inline-block;*display:inline;zoom:1;text-align:left; }
.ui_content.ui_state_full{ display:block;width:100%;height:100%;margin:0;padding:0!important; }
.ui_content.ui_state_full iframe{ border-radius:0 0 5px 5px; }
.ui_loading{ width:96px;height:32px;text-align:left;text-indent:-9999em;overflow:hidden;background:url(icons/loading.gif) no-repeat center center; }
.ui_icon_bg{ margin:20px 0 20px 15px; }

/*标题纯CSS按钮开始 min 最小化，max最大化，res还原，rese恢复，close关闭*/
.ui_min,.ui_max,.ui_close,.ui_res{ position:relative;text-decoration:none;letter-spacing:normal;text-align:center;display:inline-block;*display:inline;zoom:1;vertical-align:top;font-family:tahoma,arial,\5b8b\4f53,sans-serif; }
.ui_min b,.ui_max b,.ui_res_t,.ui_res_b{ display:block;position:absolute;overflow:hidden;cursor:pointer; }
.ui_close{ font-weight:500;text-decoration:none;outline:0 none; }
.ui_close:hover{ text-decoration:none; }

/*Tips部分*/
.ui_state_tips .ui_main{ min-width:3em; }
.ui_state_tips .ui_content{ margin-top:-2px;padding:8px 10px!important; }
.ui_state_tips .ui_icon_bg{ margin:5px 0 6px 9px; }
.ui_state_tips .ui_title,.ui_state_tips .ui_title_buttons,.ui_res{ display:none; } /* 还原按钮也在这里隐藏，这样可节省代码，注间这段一定要写在上面那段代码的下面*/

#ldg_lockmask{ background:#DCE2F1;filter:alpha(opacity=60);opacity:.6; }
/*==================制作新皮肤到这里结束复制==================*/

/*样式层开始*/
.ui_dialog{ border:1px solid #0097BD;border-radius:5px;box-shadow:0 1px 6px rgba(0,0,0,.5);
-moz-transition:-moz-box-shadow linear .2s;
-webkit-transition: -webkit-box-shadow linear .2s;
transition: -webkit-box-shadow linear .2s; }
.ui_state_lock .ui_dialog{ box-shadow:0 3px 10px rgba(0,0,0,.5); }/*锁屏时遮罩*/
.ui_state_drag .ui_dialog,.ui_state_lock.ui_state_drag .ui_dialog{ box-shadow:none; }/*拖动时隐藏阴影，通过css3实现渐变动画*/

.ui_lt,.ui_rt,.ui_lb,.ui_rb{ width:0;height:0;*width:1px;*height:1px; }/*边框宽度*/
.ui_rb{ display:block;width:12px;height:12px;position:absolute;bottom:0;right:0;background:none; } /*重新显示右下角拖动，设为负值会造成浏览器显示滚动条*/

/*标题栏样式*/
.ui_title_bar{ position:relative;height:100%;border-bottom:1px solid #0096be; }/*外线*/
.ui_title{ height:26px;line-height:26px;font-size:14px;font-weight:bold;color:#DDD;border-radius:4px 4px 0 0;padding-left:5px;border-bottom:1px solid #3fc7f5;
background:linear-gradient(top,#00c8ef,#00a6c8);
background:-webkit-gradient(linear,0% 0%, 0% 100%,from(#00c8ef),to(#00a6c8)); 
background:-moz-linear-gradient(top,#00c8ef,#00a6c8); 
background:-o-linear-gradient(top,#00c8ef,#00a6c8);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#00c8ef',endColorstr='#00a6c8');*background:#DDD;/*IE haslayout 勿删除*/ }
 
.ui_state_focus .ui_title{ color:#FFF; }
.ui_state_drag .ui_title{ border-bottom:1px solid #82d8f5;
background:linear-gradient(top,#00a6c8,#00caed); 
background:-webkit-gradient(linear,0% 0%, 0% 100%,from(#00a6c8),to(#00caed));
background:-moz-linear-gradient(top,#00a6cf,#00caed); 
background:-o-linear-gradient(top,#00c8ef,#00a6c8);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#00a6c8',endColorstr='#00caed'); }

/*标题纯CSS按钮定位部分*/
.ui_title_buttons{ top:3px;right:5px; }
.ui_min,.ui_max,.ui_close,.ui_res{ color:#FFF;font-size:18px;width:22px;height:22px;line-height:18px; }
.ui_min_b{ top:10px;left:6px;width:10px;height:2px;border-bottom:2px solid #FFF; }
.ui_max_b{ top:6px;left:6px;width:9px;height:6px; }
.ui_res_t,.ui_res_b{ top:9px;left:5px;width:9px;height:4px; }
.ui_res_b{ top:5px;left:7px; }
.ui_res_t,.ui_res_b,.ui_max_b { border:1px solid #FFF;border-top-width:3px; }
.ui_res_t{ background:#00b7dd; }
.ui_min:hover b,.ui_max:hover b,.ui_res:hover b{ border-color:#555; }
.ui_close{ vertical-align:baseline;_line-height:21px; }
.ui_close:hover,.ui_close:focus { color:#bf160b; }

/*底部按钮样式*/
.ui_buttons{ white-space:nowrap;padding:4px 8px;text-align:right;background-color:#F6F6F6;border-top:solid 1px #DADEE5; }
.ui_buttons input::-moz-focus-inner{ border:0;padding:0;margin:0; }

.ui_buttons input{ padding:3px 10px 3px 12px;padding:5px 10px 2px 12px\0;*padding:4px 10px 2px 10px;margin-left:6px;cursor:pointer;display:inline-block; 
text-align:center;line-height:1;height:23px;letter-spacing:3px;overflow:visible;color:#333;border:solid 1px #999;border-radius:3px;border-radius:0\9;background:#DDD;
background:linear-gradient(top,#FAFAFA,#E4E4E4);
background:-moz-linear-gradient(top,#FAFAFA,#E4E4E4);
background:-webkit-gradient(linear,0% 0%,0% 100%,from(#FAFAFA),to(#E4E4E4));
background:-o-linear-gradient(top,#FAFAFA,#E4E4E4);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#FAFAFA',endColorstr='#E4E4E4');
text-shadow:0 1px 1px rgba(255,255,255,1);box-shadow:0 1px 0 rgba(255,255,255,.7),0 -1px 0 rgba(0,0,0,.09);
-moz-transition:-moz-box-shadow linear .2s;-webkit-transition:-webkit-box-shadow linear .2s;transition:box-shadow linear .2s; }
.ui_buttons input:focus{ outline:0 none;box-shadow:0 0 3px #0e78c9; }
.ui_buttons input:hover{ color:#000;border-color:#666;box-shadow:none; }
.ui_buttons input:active{ border-color:#666;
background:linear-gradient(top,#FAFAFA,#E4E4E4);
background:-moz-linear-gradient(top,#FAFAFA,#E4E4E4);
background:-webkit-gradient(linear,0% 0%,0% 100%,from(#FAFAFA),to(#E4E4E4));
background:-o-linear-gradient(top,#FAFAFA,#E4E4E4);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#FAFAFA',endColorstr='#E4E4E4'); }
.ui_buttons input[disabled]{ cursor:default;color:#666;background:#DDD;border:solid 1px #999;filter:alpha(opacity=50);opacity:.5;box-shadow:none; }

input.ui_state_highlight{ color:#FFF;border:solid 1px #1c6a9e;text-shadow:0 -1px 1px #1c6a9e;background:#2288cc;
background:linear-gradient(top,#33bbee,#2288cc);
background:-moz-linear-gradient(top,#33bbee,#2288cc);
background:-webkit-gradient(linear,0% 0%,0% 100%,from(#33bbee),to(#2288cc));
background:-o-linear-gradient(top,#33bbee,#2288cc);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee',endColorstr='#2288cc'); }
input.ui_state_highlight:hover{ color:#FFF;border-color:#555; }
input.ui_state_highlight:active{ border-color:#1c6a9e;
background:linear-gradient(top,#33bbee,#2288cc);
background:-moz-linear-gradient(top,#33bbee,#2288cc);
background:-webkit-gradient(linear,0% 0%,0% 100%,from(#33bbee),to(#2288cc));
background:-o-linear-gradient(top,#33bbee,#2288cc);
filter:progid:DXImageTransform.Microsoft.gradient(startColorstr='#33bbee',endColorstr='#2288cc'); }

/*Tips 部分开始*/
.ui_state_tips .ui_title_bar{ border:0 none; }
.ui_state_tips .ui_dialog{ border-color:#40B3FF;background-color:#E5F5FF;box-shadow:2px 2px 3px rgba(0,0,0,.2); }
.green .ui_state_tips .ui_dialog{ border-color:#4DBF00;background-color:#F0FFE5;box-shadow:2px 2px 3px rgba(0,0,0,.2); }
.red .ui_state_tips .ui_dialog{ border-color:#FF8080;background-color:#FFF2F2;box-shadow:2px 2px 3px rgba(0,0,0,.2); }
