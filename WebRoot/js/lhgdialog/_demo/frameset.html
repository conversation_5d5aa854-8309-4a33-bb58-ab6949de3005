<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<title>框架示例 - LHGDIALOG</title>
    <meta name="keywords" content="lhgdialog,dialog,弹出窗口,js窗口,js弹出窗口,js库,对话框,alert,提示,警告,确认,提问,ajax,tip,confirm,open,prompt,lhgcore,javascript,jquery,window,clientside,control,open source,LGPL,dhtml,html,xhtml,plugins"/>
    <meta name="description" content="lhgdialog是一功能强大的简单迷你并且高效的弹出窗口插件,基于网络访问并且兼容IE 6.0+,Firefox 3.0+,Opera 9.6+,Chrome 1.0+,Safari 3.22+."/>
    <meta name="copyright" content="lhgcore.com"/>
	<link rel="icon" href="../favicon.ico" type="image/x-icon"/>
	<link rel="shortcut icon" href="../favicon.ico" type="image/x-icon"/>
	<link href="../_doc/common.css" type="text/css" rel="stylesheet"/>
	<link href="../_doc/prettify/prettify.css" type="text/css" rel="stylesheet"/>
</head>

<body style="background-color:#fff;">
    <div class="container" style="background-color:#eee;">
	    <div class="header">
		    <div class="hd_logo"><a href="../index.html"><img border="0" src="../_doc/images/hd_logo.gif" alt="lhgdialog"/></a></div>
			<div class="hd_nav">
			    <a href="../index.html">首页</a> | <a href="../_doc/api.html">API文档</a> | <a href="demo.html">基础示例</a> | <a href="value.html">传值示例</a> | <a href="animate.html">动画示例</a> | 框架示例 | <a href="../_doc/update.html">更新记录</a>
			</div>
		</div>
		<div class="line">&nbsp;</div>
		<div class="cbody" style="height:400px;">
		    <iframe frameborder="1" style="width:100%;height:100%;" src="frameset/frm_demo.html"></iframe>
		</div>
		<div style="padding:5px 20px;font-weight:bold;color:#f00;">相关窗口调用代码请打开源文件自行查看</div>
		<div class="line">&nbsp;</div>
		<div class="footer">
		    <div class="ft_copy">Copyright &copy <a href="http://www.lhgcore.com/" target="_blank">lhgcore.com</a>. All rights reserved. | 豫ICP备06002782号 | <a href="http://t.qq.com/lhgcore" target="_blank">官方微博</a></div>
		</div>
	</div>
</body>
</html>