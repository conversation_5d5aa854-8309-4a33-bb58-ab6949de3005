<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
	<style type="text/css">
body{margin:0;padding:0;}
	</style>
	<script type="text/javascript">
var api = frameElement.api, W = api.opener;

function opchild()
{
    W.$.dialog({id:'SLHG1976D',content:'我是最后一个锁屏窗口:-)',lock:true,parent:api});
}
	</script>
</head>

<body>
<p style="color:#F00;text-align:center;margin-top:30px;">
    <button id="child" onclick="opchild();">打开最后一个锁屏窗口</button>
</p>
</body>
</html>