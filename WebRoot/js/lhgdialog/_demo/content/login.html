<form id="login-form" action="?login" method="post">
<p><label>帐号：<input id="login-form-username" name="username" type="text"></label></p>
<p><label>密码：<input id="login-form-password" name="password" type="password"></label></p>
</form>
<!-- IE8 style 标签写在HTML片段下方才能生效 -->
<style>
#login-form p { padding:5px;  }
#login-form input { width:15em; padding:4px; border:1px solid #CCC; }
#login-form input:focus { border-color:#426DC9; }
#login-form .login-form-error { background:#FFFBFC; border-color:#F00 !important; }
</style>
<!--
	1、 标准script的标签将会被执行，这里要注意此处定义的变量会污染全局
    2、 本页编码要与对话框所在页面编码保持一致
-->
<script type="text/javascript">
var api = lhgdialog.focus, //返回当前最顶层窗口实例对象
	$$ = function(id){return document.getElementById(id)},
	form = $$('login-form'),
	username = $$('login-form-username'),
	password = $$('login-form-password');

// 操作对话框
api.title('系统登录')
	// 自定义按钮
	.button(
		{
			name: '登录',
			callback: function(){
				if (check(username) && check(password)) form.submit();
				return false;
			},
			focus: true
		},
		{
			name: '取消'
		}
		/*, 更多按钮.. */
	)
	// 锁屏
	.lock();
	
username.focus();

// 表单验证
var check = function (input) {
	if (input.value === '') {
		inputError(input);
		input.focus();
		return false;
	} else {
		return true;
	};
};

// 输入错误提示
var inputError = function (input) {
	clearTimeout(inputError.timer);
	var num = 0;
	var fn = function () {
		inputError.timer = setTimeout(function () {
			input.className = input.className === '' ? 'login-form-error' : '';
			if (num === 5) {
				input.className === '';
			} else {
				fn(num ++);
			};
		}, 150);
	};
	fn();
};
</script>