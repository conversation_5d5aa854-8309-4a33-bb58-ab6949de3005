<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
    <title>窗口组件首页 - LHGDIALOG</title>
    <meta name="keywords" content="lhgdialog,dialog,弹出窗口,js窗口,js弹出窗口,js库,对话框,alert,提示,警告,确认,提问,ajax,tip,confirm,open,prompt,lhgcore,javascript,jquery,window,clientside,control,open source,LGPL,dhtml,html,xhtml,plugins"/>
    <meta name="description" content="lhgdialog是一功能强大的简单迷你并且高效的弹出窗口插件,基于网络访问并且兼容IE 6.0+,Firefox 3.0+,Opera 9.6+,Chrome 1.0+,Safari 3.22+."/>
    <meta name="copyright" content="lhgcore.com"/>
    <link rel="icon" href="favicon.ico" type="image/x-icon"/>
    <link rel="shortcut icon" href="favicon.ico" type="image/x-icon"/>
    <link href="_doc/common.css" type="text/css" rel="stylesheet"/>
</head>

<body>
    <div class="container">
	    <div class="header">
		    <div class="hd_logo"><a href="index.html"><img border="0" src="_doc/images/hd_logo.gif" alt="lhgdialog"/></a></div>
			<div class="hd_nav">
			    首页 | <a href="_doc/api.html">API文档</a> | <a href="_demo/demo.html">基础示例</a> | <a href="_demo/value.html">传值示例</a> | <a href="_demo/animate.html">动画示例</a> | <a href="_demo/frameset.html">框架示例</a> | <a href="_doc/update.html">更新记录</a>
			</div>
		</div>
		<div class="line">&nbsp;</div>
		<div class="content">
		    <div class="right">
			    <div style="margin:0;" class="ldg_box ldg_box_ui">
				    <h2 class="box_title">最新下载</h2>
					<p style="text-align:center;padding:10px 10px 20px;font-size:14px;"><a style="padding:5px 10px;" href="http://code.google.com/p/lhgdialog/downloads/list" target="_blank">lhgDialog Google Code 项目主页</a></p>
				</div>
			    <div class="ldg_box ldg_box_ui">
				    <h2 class="box_title">官方微博</h2>
					<p style="text-align:center;padding:10px;font-size:14px;"><a style="padding:5px 10px;" href="http://t.qq.com/lhgcore" target="_blank">http://t.qq.com/lhgcore</a></p>
					<p style="margin-bottom:5px;">组件的发布，更新，以及其它各种相关信息都将在这里发布，请您及时关注！</p>
				</div>
				<div class="ldg_box ldg_box_ui">
				    <h2 class="box_title">联系方式</h2>
					<p>如果你对lhgDialog有什么意见建议可以用下面任意一种联系方式找到作者。</p>
					<p>官方论坛：<a href="http://bbs.lhgcore.com" target="_blank">lhgcore 组件论坛</a></p>
					<p>QQ群：<em style="color:#00f">31868246</em> (推荐方式)</p>
					<p>邮箱地址：<a href="mailto:<EMAIL>"><EMAIL></a></p>
					<p style="margin-bottom:5px;color:#990">邮箱用于咨询商业授权与商业定制版事宜或您捐赠后以此来联系我们。</p>
				</div>
				<div class="ldg_box ldg_box_ui">
				    <h2 class="box_title">捐赠方式</h2>
					<p>支付宝账号：<span><EMAIL></span></p>
					<p>财付通账号：<span><EMAIL></span></p>
					<p style="margin-bottom:5px;">我们坚持永远开源和免费使用，我们不是最好的，但是我们会努力做的更好，只因有你支持！</p>
				</div>
				<div class="ldg_box ldg_box_ui">
				    <h2 class="box_title">问题反馈</h2>
					<p style="margin-bottom:5px;">提交BUG必备项：<br/>1. 浏览器名称，版本<br/>2. lhgDialog版本号（只支持4+版本）<br/>3. 简明扼要的描述信息 <br/>4. 建议提取一份BUG DEMO（纯html页面）</p>
				</div>
				<div class="ldg_box ldg_box_ui">
				    <h2 class="box_title">其它组件</h2>
					<p style="text-align:center;padding:10px 10px 20px;font-size:14px;"><a style="padding:5px 10px;" href="http://code.google.com/p/lhgcalendar/downloads/list" target="_blank">lhgCalendar Google Code 项目主页</a></p>
				</div>
			</div>
			<div class="left">
			    <div class="ldg_box">
				    lhgDialog是一个功能强大且兼容面广的对话框组件，它拥有精致的界面与友好的接口
				</div>
				<div class="lt_syn">
				    <h2>为大型弹窗定制</h2>
				    <p>本组件主要以iframe方式加载单独页面为主的弹出窗口，由其适用于后台管理和webOS类项目使用，独立的内容页更方便管理，页面的也不易受其它页面的影响，而且内容页可以是静态或动态的任何一种文件。</p>
			        <h2>强大灵活的接口</h2>
				    <p>由于本组件主要是用来制作大型的窗口，页面和窗口间的交互肯定会非常频繁，所以本组件提供了丰富的交互控制接口，可以非常方便的进行页面间的数据的传输。</p>
			        <h2>细致的用户体验</h2>
				    <p>智能无限级跨框架弹出，如果不是在输入状态，它支持Esc快捷键关闭；智能给按钮添加焦点；黄金比例垂直居中；采用九宫格的布局结构，css类钩子丰富，可以定制类似桌面软件般精致的皮肤...</p>
			        <h2>跨平台兼容特性</h2>
				    <p>兼容：IE6+、Firefox、Chrome、Safari、Opera以及iPad等移动设备。并且IE6下也能支持现代浏览器的静止定位(fixed)、覆盖下拉控件、alpha通道png背景。</p>
				</div>
			    <div class="ldg_box">
				    商业授权 & 商业版定制 & 承接各种JS插件定制开发
				</div>
				<div class="lt_syn">
				    <h2>lhgDialog 采用LGPL开源协议：</h2>
					<ul>
					    <li>如果您不对 lhgDialog 程序代码进行任何修改，直接调用组件，可以以任意方式自由使用：开源、非开源、商业及非商业。</li>
						<li>如果您对 lhgDialog 程序代码进行任何的修改或者衍生，涉及修改部分的额外代码和衍生的代码都必须采用 LGPL 协议开放源代码。</li>
						<li>无论您对 lhgDialog 程序代码如何修改，都必须在程序文件头部声明版权信息的注释（包括压缩版）。</li>
					</ul>
					<h2 style="color:#000;">LGPL协议原文：<a style="font-weight:normal;" href="_doc/license.txt" target="_blank">GNU Lesser General Public License</a></h2>
					<h2>商业授权</h2>
					<ul>
					    <li>您可以将 lhgDialog 程序直接使用在自己的商业或者非商业网站或者软件产品中。</li>
						<li>您可以对 lhgDialog 进行修改和美化，可以去除 lhgDialog 版权注释或改变程序名称，无需公开您修改或美化过的 lhgDialog 程序与界面。</li>
						<li>商业授权每个公司只需要购买一次，而不限制产品域名。适用于 lhgDialog 现有版本和所有后续版本，永久有效。</li>
						<li>您享有反映和提出意见的优先权，相关意见将被作为首要考虑。</li>
					</ul>
					<p style="font-size:14px;padding:5px 10px;"><a href="_doc/commercial_license.doc" target="_blank">商业授权全文</a> | 授权价格：199元 | 支付方式：支付宝 & 财付通<br/>
					    支付宝账号：<span style="color:#00f;"><EMAIL></span> | 财付通账号：<span style="color:#00f;"><EMAIL></span>
					</p>
				</div>
			</div>
		</div>
		<div class="line">&nbsp;</div>
		<div class="footer">
		    <div class="ft_copy">Copyright &copy <a href="http://bbs.lhgcore.com/lhg4/index.html" target="_blank">lhgcore.com</a>. All rights reserved. | 豫ICP备06002782号 | <a href="http://t.qq.com/lhgcore" target="_blank">官方微博</a></div>
		</div>
	</div>
<!-- JiaThis Button BEGIN -->
<script type="text/javascript" src="http://v1.jiathis.com/code/jiathis_r.js?move=0" charset="utf-8"></script>
<!-- JiaThis Button END -->
</body>
</html>