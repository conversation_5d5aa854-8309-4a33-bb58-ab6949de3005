# Copyright 2012 Mozilla Foundation
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.

# Main toolbar buttons (tooltips and alt text for images)
previous.title=Trang TrÆ°á»c
previous_label=TrÆ°á»c
next.title=Trang Sau
next_label=Tiáº¿p

# LOCALIZATION NOTE (page_label, page_of):
# These strings are concatenated to form the "Page: X of Y" string.
# Do not translate "{{pageCount}}", it will be substituted with a number
# representing the total number of pages.
page_label=Trang:
page_of=thuá»c vá» {{pageCount}}

zoom_out.title=Thu nhá»
zoom_out_label=Thu nhá»
zoom_in.title=PhÃ³ng to
zoom_in_label=PhÃ³ng to
zoom.title=Thu phÃ³ng
presentation_mode.title=Chuyá»n sang cháº¿ Äá» trÃ¬nh chiáº¿u
presentation_mode_label=Cháº¿ Äá» trÃ¬nh chiáº¿u
open_file.title=Má» Táº­p Tin
open_file_label=Má» táº­p tin
print.title=In
print_label=In
download.title=Táº£i xuá»ng
download_label=Táº£i xuá»ng
bookmark.title=GÃ³c nhÃ¬n hiá»n táº¡i (copy hoáº·c má» trong cá»­a sá» má»i)
bookmark_label=Cháº¿ Äá» xem hiá»n táº¡i

# Secondary toolbar and context menu
tools.title=CÃ´ng cá»¥
page_rotate_cw.title=Xoay theo chiá»u kim Äá»ng há»
page_rotate_cw.label=Xoay theo chiá»u kim Äá»ng há»
page_rotate_cw_label=Xoay theo chiá»u kim Äá»ng há»
page_rotate_ccw.title=Xoay ngÆ°á»£c chiá»u kim Äá»ng há»
page_rotate_ccw.label=Xoay ngÆ°á»£c chiá»u kim Äá»ng há»
page_rotate_ccw_label=Xoay ngÆ°á»£c chiá»u kim Äá»ng há»


# Document properties dialog box
document_properties_file_size=KÃ­ch thÆ°á»c táº­p tin:
document_properties_title=TiÃªu Äá»:
document_properties_author=TÃ¡c giáº£:
document_properties_subject=Chá»§ Äá»:
document_properties_keywords=Tá»« khÃ³a:
document_properties_creation_date=NgÃ y táº¡o:
document_properties_modification_date=NgÃ y sá»­a Äá»i:
document_properties_producer=NhÃ  sáº£n xuáº¥t PDF:
document_properties_version=PhiÃªn báº£n PDF:
document_properties_page_count=Tá»ng sá» trang:
document_properties_close=ÃÃ³ng

# Tooltips and alt text for side panel toolbar buttons
# (the _label strings are alt text for the buttons, the .title strings are
# tooltips)
toggle_sidebar.title=Báº­t/Táº¯t Thanh Lá»
toggle_sidebar_label=Báº­t/Táº¯t Thanh Lá»
outline.title=Hiá»n thá» báº£n phÃ¡c tÃ i liá»u
outline_label=Báº£n phÃ¡c há»a TÃ i liá»u
thumbs.title=Hiá»n thá» Thumbnails
thumbs_label=Thumbnails (hÃ¬nh biá»u diá»n nhá»)
findbar.title=TÃ¬m trong tÃ i liá»u
findbar_label=TÃ¬m

# Thumbnails panel item (tooltip and alt text for images)
# LOCALIZATION NOTE (thumb_page_title): "{{page}}" will be replaced by the page
# number.
thumb_page_title=Trang {{page}}
# LOCALIZATION NOTE (thumb_page_canvas): "{{page}}" will be replaced by the page
# number.
thumb_page_canvas=HÃ¬nh áº£nh thu nhá» cá»§a trang {{page}}

# Find panel button title and messages
find_label=TÃ¬m kiáº¿m:
find_previous.title=TÃ¬m cá»¥m tá»« á» pháº§n trÆ°á»c
find_previous_label=TrÆ°á»c
find_next.title=TÃ¬m cá»¥m tá»« á» pháº§n sau
find_next_label=Tiáº¿p
find_highlight=TÃ´ sÃ¡ng táº¥t cáº£
find_match_case_label=PhÃ¢n biá»t chá»¯ hoa, chá»¯ thÆ°á»ng
find_reached_top=ÄÃ£ Äáº¿n pháº§n Äáº§u tÃ i liá»u, quay trá» láº¡i tá»« cuá»i
find_reached_bottom=ÄÃ£ Äáº¿n pháº§n cuá»i cá»§a tÃ i liá»u, quay trá» láº¡i tá»« Äáº§u
find_not_found=KhÃ´ng tÃ¬m tháº¥y cá»¥m tá»«

# Error panel labels
error_more_info=ThÃ´ng tin thÃªm
error_less_info=Hiá»n thá» Ã­t thÃ´ng tin hÆ¡n
error_close=ÄÃ³ng
# LOCALIZATION NOTE (error_version_info): "{{version}}" and "{{build}}" will be
# replaced by the PDF.JS version and build ID.
error_version_info=PDF.js v{{version}} (build: {{build}})
# LOCALIZATION NOTE (error_message): "{{message}}" will be replaced by an
# english string describing the error.
error_message=ThÃ´ng Äiá»p: {{message}}
# LOCALIZATION NOTE (error_stack): "{{stack}}" will be replaced with a stack
# trace.
error_stack=Stack: {{stack}}
# LOCALIZATION NOTE (error_file): "{{file}}" will be replaced with a filename
error_file=Tá»p: {{file}}
# LOCALIZATION NOTE (error_line): "{{line}}" will be replaced with a line number
error_line=DÃ²ng: {{line}}
rendering_error=Lá»i khi hiá»n thá» trang.

# Predefined zoom values
page_scale_width=Chiá»u rá»ng trang
page_scale_fit=Äá» vá»«a cá»§a trang
page_scale_auto=Tá»± Äá»ng thu/phÃ³ng
page_scale_actual=KÃ­ch thÆ°á»c thá»±c
# LOCALIZATION NOTE (page_scale_percent): "{{scale}}" will be replaced by a
# numerical scale value.

# Loading indicator messages
loading_error_indicator=Lá»i
loading_error=Lá»i khi táº£i tÃ i liá»u PDF.
invalid_file_error=Táº­p tin PDF há»ng hoáº·c khÃ´ng há»£p lá».
missing_file_error=Thiáº¿u táº­p tin PDF.

# LOCALIZATION NOTE (text_annotation_type.alt): This is used as a tooltip.
# "{{type}}" will be replaced with an annotation type from a list defined in
# the PDF spec (32000-1:2008 Table 169 â Annotation types).
# Some common types are e.g.: "Check", "Text", "Comment", "Note"
text_annotation_type.alt=[{{type}} ChÃº thÃ­ch]
password_label=Nháº­p máº­t kháº©u Äá» má» táº­p tin PDF nÃ y.
password_invalid=Máº­t kháº©u khÃ´ng ÄÃºng. Vui lÃ²ng thá»­ láº¡i.
password_ok=OK
password_cancel=Há»§y bá»

printing_not_supported=Cáº£nh bÃ¡o: In áº¥n khÃ´ng ÄÆ°á»£c há» trá»£ Äáº§y Äá»§ á» trÃ¬nh duyá»t nÃ y.
printing_not_ready=Cáº£nh bÃ¡o: PDF chÆ°a ÄÆ°á»£c táº£i háº¿t Äá» in.
web_fonts_disabled=PhÃ´ng chá»¯ Web bá» vÃ´ hiá»u hÃ³a: khÃ´ng thá» sá»­ dá»¥ng cÃ¡c phÃ´ng chá»¯ PDF ÄÆ°á»£c nhÃºng.
document_colors_disabled=TÃ i liá»u PDF khÃ´ng ÄÆ°á»£c cho phÃ©p dÃ¹ng mÃ u riÃªng: 'Cho phÃ©p trang chá»n mÃ u riÃªng' ÄÃ£ bá» táº¯t trÃªn trÃ¬nh duyá»t.
