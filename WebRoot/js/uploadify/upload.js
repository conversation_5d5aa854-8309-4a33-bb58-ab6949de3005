;(function($){
	var setting = {
	    actionUrl   :"",
	    multi       :false,
	    auto        :true,
	    height      : 21,
	    width       : 90,
	    fileTypeDesc:"任意格式",
	    fileTypeExts:"*.*",
	    buttonClass :"m-button",
	    entityName  :"upload",
	    fileSizeLimit:"10MB"
	};
	
    function uploadify_onSelectError(file, errorCode, errorMsg) {
        var msgText = "上传失败\n";
        switch (errorCode) {
            case SWFUpload.QUEUE_ERROR.QUEUE_LIMIT_EXCEEDED:
                msgText += "每次最多上传 3个文件";
                break;
            case SWFUpload.QUEUE_ERROR.FILE_EXCEEDS_SIZE_LIMIT:
                msgText += "文件大小超过限制( " + this.settings.fileSizeLimit + " )";
                break;
            case SWFUpload.QUEUE_ERROR.ZERO_BYTE_FILE:
                msgText += "文件大小为0";
                break;
            case SWFUpload.QUEUE_ERROR.INVALID_FILETYPE:
                msgText += "文件格式不正确，仅限 " + this.settings.fileTypeExts;
                break;
            default:
                msgText += "错误代码：" + errorCode + "\n" + errorMsg;
        }
        alert(msgText);
        //var data = {state:"400",title:"上传过程出错",message:msgText};
        //_show(data);
    };
    function uploadify_onUploadError(file, errorCode, errorMsg, errorString){
        if (errorCode == SWFUpload.UPLOAD_ERROR.FILE_CANCELLED
            || errorCode == SWFUpload.UPLOAD_ERROR.UPLOAD_STOPPED) {
            return;
        }
        var msgText = "上传失败\n";
        switch (errorCode) {
            case SWFUpload.UPLOAD_ERROR.HTTP_ERROR:
                msgText += "HTTP 错误\n" + errorMsg;
                break;
            case SWFUpload.UPLOAD_ERROR.MISSING_UPLOAD_URL:
                msgText += "上传文件丢失，请重新上传";
                break;
            case SWFUpload.UPLOAD_ERROR.IO_ERROR:
                msgText += "IO错误";
                break;
            case SWFUpload.UPLOAD_ERROR.SECURITY_ERROR:
                msgText += "安全性错误\n" + errorMsg;
                break;
            case SWFUpload.UPLOAD_ERROR.UPLOAD_LIMIT_EXCEEDED:
                msgText += "每次最多上传 " + this.settings.uploadLimit + "个";
                break;
            case SWFUpload.UPLOAD_ERROR.UPLOAD_FAILED:
                msgText += errorMsg;
                break;
            case SWFUpload.UPLOAD_ERROR.SPECIFIED_FILE_ID_NOT_FOUND:
                msgText += "找不到指定文件，请重新操作";
                break;
            case SWFUpload.UPLOAD_ERROR.FILE_VALIDATION_FAILED:
                msgText += "参数错误";
                break;
            default:
                msgText += "文件:" + file.name + "\n错误码:" + errorCode + "\n"
                + errorMsg + "\n" + errorString;
        }
        var data = {state:"400",title:"上传过程出错",message:msgText};
        //_show(data);
        //return parameters;
        alert(msgText);
    };
    
    function uploadify_onUploadProgress(file, bytesUploaded, bytesTotal, 
    		totalBytesUploaded, totalBytesTotal){
    	/*alert(file.name);
    	alert(bytesUploaded);
    	alert(totalBytesUploaded);*/
    	
    	$("#divMsg").html((bytesUploaded/totalBytesUploaded)*100+"%");
    }
    function uploadify_onCancel(file,s,d,data){
    	$('#'+file.id).find('.data').html(' - 删除文件');
    	
    }
    var methods = {
    	init:function(options){
    		return this.each(function(){
                var o =$.extend(o, setting);
                if (options) $.extend(o,options);
                
                var divContent='<div class="uploadify-queue" id="fileupload-queue"><div><input id="file'+ o.entityName+'" type="file">';
                $(this).append(divContent);
                
                //上传按钮
                var uploadBtn;
                if(o.auto){
                	uploadBtn="<div id='uploadify-oprupload' class='opr-button'><input type='button' style='height: "+o.height+"px;' class='"+o.buttonClass+"' onclick='$('#file"+o.entityName+"').uploadify('cancel')' value='取消上传' /></div>";
                }else{
                	uploadBtn="<div id='uploadify-oprupload' class='opr-button'><input  class='"+o.buttonClass+"' style='height: "+o.height+"px;' type='button' onclick='$('#file"+o.entityName+"').uploadify('upload','*')' value='开始上传' />" +
            		"&nbsp;<input type='button' class='"+o.buttonClass+"' style='height: "+o.height+"px;' onclick='$('#file"+o.entityName+"').uploadify('cancel')' value='取消上传' /></div>"; 
                }
                $("#file"+o.entityName).uploadify({
                	auto          : o.auto,  //设置为true当选择文件后就直接上传了，为false需要点击上传按钮才上传 。
                    height        : 24, //设置浏览按钮的高度 ，默认值
                    width         : 100,  //设置文件浏览按钮的宽度。
                    //buttonClass   : o.buttonClass,  //按钮样式
                    queueID      : "fileupload-queue",
                    multi         : o.multi, //设置为true时可以上传多个文件。
                    fileSizeLimit : o.fileSizeLimit,  //上传文件的大小限制,如果设置为0则表示无限制
                    fileObjName   : 'uploadify',   //文件上传对象的名称
                    progressData  : 'percentage',  //上传显示%样式
                    fileTypeDesc  : o.fileTypeDesc,
                    fileTypeExts  : o.fileTypeExts,
                    buttonText    : '选择上传文件',
                    swf           : 'js/uploadify/uploadify.swf',
                    uploader      : o.actionUrl,  //请求action
                    uploadLimit   : 0,  //最大上传文件数量，如果达到或超出此限制将会触发onUploadError事件
                    removeCompleted: false, //上传完成后自动删除队列 
                    overrideEvents:['onDialogClose','onSelectError','onUploadError'],
    		        onFallback    :function(){ //检测FLASH失败调用
    		            alert("您未安装FLASH控件，无法上传图片！请安装FLASH控件后再试。");
    		        },
                    onUploadStart   :function(file){  //当文件即将开始上传时立即触发
                        
                    },
                    onInit :function(){ //首次初始化Uploadify结束时触发。
                    	//清理按钮样式 重新添加自定义样式
                        $("div#"+$(this).attr("id")+"-button").removeClass("uploadify-button");
                        $("div#"+$(this).attr("id")+"-button").addClass(o.buttonClass);
                        //添加操作功能按钮
                    	//$("#file"+o.entityName).before(uploadBtn);
                    	//$("#file"+o.entityName+"-queue").hide();
                    	//这里是占位DIV并没有用处
                    	$("#uploadify-oprupload").before("<div id='divMsg' style='height: "+(o.height+20)+"px;  width:80%'>&nbsp;</div>");
                    },
                    onUploadError   : uploadify_onUploadError,    //上传错误时候
                    onSelectError   : uploadify_onSelectError,    //选择文件
                    onUploadProgress: uploadify_onUploadProgress,  //上传过程中
                    onCancel        : uploadify_onCancel,   //取消上传
                    onUploadSuccess : function(file, data, response) {  //当文件上传成功时触发
                    	
                    	if(data=="ERROR"){
     		            	$('#'+file.id).find('.data').html(' - 上传失败');
     		            	alert("FTP链接故障！");
     		            	return;
     		            }
     		            $('#'+file.id).find('.data').html(' - 上传完毕');
                        var json = eval('(' + data + ')');    
                        if(json && json.state == "200"){
                            //alert("附件ID"+json.fileId);
                        	$('#'+file.id).append("<input type='hidden' name='attachmentId' value='"+json.fileId+"'/>");
                            //$("#upload").append("<input type='text' ltype='text'   readonly='readonly' value='"+json.name+"'/><input type='hidden' name='attachmentId' value='"+json.fileId+"'/><br/>");
                        }else{
                           
                        }

                   	 	var cancel=$("#"+file.id + " .cancel a");
                   	    if (cancel) {
	                   	    cancel.on('click',function () {
		                   	    //点击取消时，删除文件
	                   	    	deleteAttachment(json.fileId);
	                   	    	delete swfuploadify.queueData.files[file.id];
                   	    })}
                    }
                    
                });
            });
    	}
    } 
    
    $.fn.uploadTip = function( method ) {
    	
        if ( methods[method] ) {
            return methods[method].apply( this, Array.prototype.slice.call( arguments, 1 ));
        } else if ( typeof method === 'object' || ! method ) {
        	
            return methods.init.apply( this, arguments );
        } else {
            $.error( 'Method ' +  method + ' does not exist on jQuery.multiselect2side' );
        }
    };
})(jQuery)