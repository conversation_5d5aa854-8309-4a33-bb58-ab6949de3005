;(function($){
	//默认值
	var defaults={
		fileID: 'uploadify',  //上传控件名称
		buttonText: "选择文件",  //按钮名称
		formData:{},
		isopr: true, //是否有操作区域
		auto: true,  //是否自动上传
		queueSizeLimit:99,  //允许上传数量
		swf  : 'javascript/uploadify/uploadify.swf',  //引入uploadify.swf    
        uploader: 'jsp/attachments/AttsHandleAction!uploadFile.action', //请求路径   
        deler: 'jsp/attachments/AttsHandleAction!deleteUploadify.action' //删除文件ACTION
	}

	jQuery.myUploadify = {          
			onCancel:function(deler,id,type) { 
			 if(confirm("删除是不可恢复的，你确认要删除吗?")){
			   $.post(deler,{id:id}, function (text, status) {
				   if(status=="success"){
					 //alert(text);
					 if(text=="YES"){
						 if(type=="one"){
							 $("#uploadify-res").empty();
						 }else{
						    $("#tr_"+id).remove();
						 }
						 var ids=$("#uploadify-resIds").val();
						 var arr= ids.split("|");
						 var newids="";
						 for(var i = 0;i<arr.length;i++){
							 if(id!=arr[i]){
								 newids+="|"+arr[i];
							 }
						 }
						 $("#uploadify-resIds").val(newids)
					 }
				   }
			   });   
			 }
		    },          
		    res:function() {          
		        var ids=$("#uploadify-resIds").val();
		        return ids;
		    }         
	};
	
	var options;

	$.fn.loadmultiUploadify=function(options){
		options= $.extend(defaults,options);
		var sethtml="<div style=\"width:100%;\"><div style=\"float: left; \"><input type=\"file\" id=\""+options.fileID+"\" name=\""+options.fileID+"\">" +
				"<input type=\"hidden\" id=\"uploadify-resIds\" /></div>" +
		"<div>&nbsp;&nbsp;选择文件（单个文件大小限制10MB）" ;
		if(options.isopr){
			sethtml+="&nbsp;&nbsp;&nbsp;&nbsp;<a href=\"javascript:$('#"+options.fileID+"').uploadify('upload','*')\">开始上传</a>&nbsp;&nbsp;" +
			"<a href=\"javascript:$('#"+options.fileID+"').uploadify('cancel')\">取消所有上传</a>";
		}
		sethtml+="</div></div><div id=\"fileQueue\"></div>";
		this.html(sethtml);
		if(options.queueSizeLimit==1){
			//当上传数量限制为1时
			this.append("<div id=\"uploadify-res\" style=\"width:100%;float:left\"></div>");
		}else{
			//上传成功展示table
		    this.append("<div style=\"width:100%;float:left\"><table class=\"uploadify-table\" id=\"uploadify-table\" cellspacing=\"0\" > <tr> " +
		    		"<th>文件名称</th> <th>上传人</th> <th>上传时间</th> <th>操作</th></tr> </table></div> ");
		}
		 
		$("#"+options.fileID).uploadify({    
		        'debug': false, //开启调试  
		        'auto' : options.auto, //是否自动上传     
		        'swf'  : options.swf,  //引入uploadify.swf    
		        'uploader': options.uploader,//请求路径    
		        'queueID': 'fileQueue',//队列id,用来展示上传进度的    
		        'width' : '65',  //按钮宽度    
		        'height': '24',  //按钮高度  
		        'queueSizeLimit' : options.queueSizeLimit,  //同时上传文件的个数    
		        'fileTypeDesc'   : '文件大小不超过10MB',    //可选择文件类型说明  
		        'fileTypeExts'   : '*.*', //控制可上传文件的扩展名    
		        'multi'          : true,  //允许多文件上传    
		        'buttonText'     : options.buttonText,//按钮上的文字    
		        'fileSizeLimit' : '10MB', //设置单个文件大小限制     
		        'fileObjName' : 'uploadify',  //<input type="file"/>的name   
		        'formData'	: options.formData , //指定上传文件附带的其他数据。也动态设置。可通过getParameter()获取 
		        'method' : 'post',    
		        'removeCompleted' : true,//上传完成后自动删除队列 
		        'overrideEvents' : [ 'onDialogClose', 'onUploadError', 'onSelectError'],   
		        'onFallback':function(){      
		            alert("您未安装FLASH控件，无法上传图片！请安装FLASH控件后再试。");      
		        }, 
		         //返回一个错误，选择文件的时候触发
		        'onSelectError':function(file, errorCode, errorMsg){
		            switch(errorCode) {
		                case -100:
		                    alert("上传的文件数量已经超出系统限制的"+$("#"+options.fileID).uploadify('settings','queueSizeLimit')+"个文件！");
		                    break;
		                case -110:
		                    alert("文件 ["+file.name+"] 大小超出系统限制的"+$("#"+options.fileID).uploadify('settings','fileSizeLimit')+"大小！");
		                    break;
		                case -120:
		                    alert("文件 ["+file.name+"] 大小异常！");
		                    break;
		                case -130:
		                    alert("文件 ["+file.name+"] 类型不正确！");
		                    break;
		            }
		        },
		        'onSelect':function(file, errorCode, errorMsg){
		           // 手工取消不弹出提示
			        if (errorCode == SWFUpload.UPLOAD_ERROR.FILE_CANCELLED
			                || errorCode == SWFUpload.UPLOAD_ERROR.UPLOAD_STOPPED) {
			            return;
			        }
		        },
		        'onUploadError' : function(file, errorCode, errorMsg, errorString){
			         // 手工取消不弹出提示
			        if (errorCode == SWFUpload.UPLOAD_ERROR.FILE_CANCELLED
			                || errorCode == SWFUpload.UPLOAD_ERROR.UPLOAD_STOPPED) {
			            return;
			        }
			        var msgText = "上传失败\n";
			        switch (errorCode) {
			            case SWFUpload.UPLOAD_ERROR.HTTP_ERROR:
			                msgText += "HTTP 错误\n" + errorMsg;
			                break;
			            case SWFUpload.UPLOAD_ERROR.MISSING_UPLOAD_URL:
			                msgText += "上传文件丢失，请重新上传";
			                break;
			            case SWFUpload.UPLOAD_ERROR.IO_ERROR:
			                msgText += "IO错误";
			                break;
			            case SWFUpload.UPLOAD_ERROR.SECURITY_ERROR:
			                msgText += "安全性错误\n" + errorMsg;
			                break;
			            case SWFUpload.UPLOAD_ERROR.UPLOAD_LIMIT_EXCEEDED:
			                msgText += "每次最多上传 " + this.settings.uploadLimit + "个";
			                break;
			            case SWFUpload.UPLOAD_ERROR.UPLOAD_FAILED:
			                msgText += errorMsg;
			                break;
			            case SWFUpload.UPLOAD_ERROR.SPECIFIED_FILE_ID_NOT_FOUND:
			                msgText += "找不到指定文件，请重新操作";
			                break;
			            case SWFUpload.UPLOAD_ERROR.FILE_VALIDATION_FAILED:
			                msgText += "参数错误";
			                break;
			            default:
			                msgText += "文件:" + file.name + "\n错误码:" + errorCode + "\n"+ errorMsg + "\n" + errorString;
			        }
			        alert(msgText);
		        },
		        //检测FLASH失败调用
		        'onFallback':function(){
		            alert("您未安装FLASH控件，无法上传图片！请安装FLASH控件后再试。");
		        },  
		        'onUploadSuccess' : function(file, data, response){//单个文件上传成功触发    
		        	//data就是action中返回来的数据    
		            if(data=="FTP ERROR"){
		            	$('#'+file.id).find('.data').html(' - 上传失败');
		            	alert("FTP链接故障！");
		            	return;
		            }
		            $('#'+file.id).find('.data').html(' - 上传完毕');
		            var res=eval('('+data+')')
		            var ids=$("#uploadify-resIds").val();
		            $("#uploadify-resIds").val(ids+"|"+res[0].id);
		            if(options.queueSizeLimit>1){
		            	$("#uploadify-table").append("<tr id=\"tr_"+res[0].id+"\"> <td>"+res[0].attachName+"</td> <td>"+res[0].uploadPerson+"</td> '" +
		            			"<td>"+res[0].createDate.substr(0,10)+"</td>" +
		            					" <td><a href=\"javascript:void(0)\" onClick=\"$.myUploadify.onCancel('"+options.deler+"','"+res[0].id+"','more')\" >删除</a></td> </tr> ");
		            }else{
		            	$("#uploadify-res").append("<label>"+res[0].attachName+"</label>&nbsp;&nbsp;<a href=\"javascript:void(0)\" onClick=\"$.myUploadify.onCancel('"+options.deler+"','"+res[0].id+"','one')\" >删除</a>");
		            }
		        },
		        'onCancel': function(file){
		        	
		        }
		 });  
		
		
	}
	
	
})(jQuery)