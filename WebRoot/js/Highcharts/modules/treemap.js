/*
 Highcharts JS v4.2.6 (2016-08-02)

 (c) 2014 Highsoft AS
 Authors: <AUTHORS>

 License: www.highcharts.com/license
*/
(function(f){typeof module==="object"&&module.exports?module.exports=f:f(Highcharts)})(function(f){var i=f.seriesTypes,B=f.map,m=f.merge,t=f.extend,u=f.extendClass,v=f.getOptions().plotOptions,w=function(){},k=f.each,r=f.grep,j=f.pick,n=f.Series,C=f.stableSort,x=f.Color,D=function(a,b,c){var d,c=c||this;for(d in a)a.hasOwnProperty(d)&&b.call(c,a[d],d,a)},y=function(a,b,c,d){d=d||this;a=a||[];k(a,function(e,h){c=b.call(d,c,e,h,a)});return c},q=function(a,b,c){c=c||this;a=b.call(c,a);a!==!1&&q(a,b,
c)};v.treemap=m(v.scatter,{showInLegend:!1,marker:!1,borderColor:"#E0E0E0",borderWidth:1,dataLabels:{enabled:!0,defer:!1,verticalAlign:"middle",formatter:function(){return this.point.name||this.point.id},inside:!0},tooltip:{headerFormat:"",pointFormat:"<b>{point.name}</b>: {point.value}</b><br/>"},layoutAlgorithm:"sliceAndDice",layoutStartingDirection:"vertical",alternateStartingDirection:!1,levelIsConstant:!0,opacity:0.15,states:{hover:{borderColor:"#A0A0A0",brightness:i.heatmap?0:0.1,opacity:0.75,
shadow:!1}},drillUpButton:{position:{align:"right",x:-10,y:10}}});i.treemap=u(i.scatter,m({pointAttrToOptions:{},pointArrayMap:["value"],axisTypes:i.heatmap?["xAxis","yAxis","colorAxis"]:["xAxis","yAxis"],optionalAxis:"colorAxis",getSymbol:w,parallelArrays:["x","y","value","colorValue"],colorKey:"colorValue",translateColors:i.heatmap&&i.heatmap.prototype.translateColors},{type:"treemap",trackerGroups:["group","dataLabelsGroup"],pointClass:u(f.Point,{setVisible:i.pie.prototype.pointClass.prototype.setVisible}),
getListOfParents:function(a,b){var c=y(a,function(a,c,b){c=j(c.parent,"");a[c]===void 0&&(a[c]=[]);a[c].push(b);return a},{});D(c,function(a,c,h){c!==""&&f.inArray(c,b)===-1&&(k(a,function(a){h[""].push(a)}),delete h[c])});return c},getTree:function(){var a,b=this;a=B(this.data,function(a){return a.id});a=b.getListOfParents(this.data,a);b.nodeMap=[];a=b.buildNode("",-1,0,a,null);q(this.nodeMap[this.rootNode],function(a){var d=!1,e=a.parent;a.visible=!0;if(e||e==="")d=b.nodeMap[e];return d});q(this.nodeMap[this.rootNode].children,
function(a){var b=!1;k(a,function(a){a.visible=!0;a.children.length&&(b=(b||[]).concat(a.children))});return b});this.setTreeValues(a);return a},init:function(a,b){n.prototype.init.call(this,a,b);this.options.allowDrillToNode&&this.drillTo()},buildNode:function(a,b,c,d,e){var h=this,g=[],z=h.points[b],A;k(d[a]||[],function(b){A=h.buildNode(h.points[b].id,b,c+1,d,a);g.push(A)});b={id:a,i:b,children:g,level:c,parent:e,visible:!1};h.nodeMap[b.id]=b;if(z)z.node=b;return b},setTreeValues:function(a){var b=
this,c=b.options,d=0,e=[],h,g=b.points[a.i];k(a.children,function(a){a=b.setTreeValues(a);e.push(a);a.ignore?q(a.children,function(a){var c=!1;k(a,function(a){t(a,{ignore:!0,isLeaf:!1,visible:!1});a.children.length&&(c=(c||[]).concat(a.children))});return c}):d+=a.val});C(e,function(a,c){return a.sortIndex-c.sortIndex});h=j(g&&g.options.value,d);if(g)g.value=h;t(a,{children:e,childrenTotal:d,ignore:!(j(g&&g.visible,!0)&&h>0),isLeaf:a.visible&&!d,levelDynamic:c.levelIsConstant?a.level:a.level-b.nodeMap[b.rootNode].level,
name:j(g&&g.name,""),sortIndex:j(g&&g.sortIndex,-h),val:h});return a},calculateChildrenAreas:function(a,b){var c=this,d=c.options,e=this.levelMap[a.levelDynamic+1],h=j(c[e&&e.layoutAlgorithm]&&e.layoutAlgorithm,d.layoutAlgorithm),g=d.alternateStartingDirection,f=[],d=r(a.children,function(a){return!a.ignore});if(e&&e.layoutStartingDirection)b.direction=e.layoutStartingDirection==="vertical"?0:1;f=c[h](b,d);k(d,function(a,d){var e=f[d];a.values=m(e,{val:a.childrenTotal,direction:g?1-b.direction:b.direction});
a.pointValues=m(e,{x:e.x/c.axisRatio,width:e.width/c.axisRatio});a.children.length&&c.calculateChildrenAreas(a,a.values)})},setPointValues:function(){var a=this.xAxis,b=this.yAxis;k(this.points,function(c){var d=c.node,e=d.pointValues,h,g;e&&d.visible?(d=Math.round(a.translate(e.x,0,0,0,1)),h=Math.round(a.translate(e.x+e.width,0,0,0,1)),g=Math.round(b.translate(e.y,0,0,0,1)),e=Math.round(b.translate(e.y+e.height,0,0,0,1)),c.shapeType="rect",c.shapeArgs={x:Math.min(d,h),y:Math.min(g,e),width:Math.abs(h-
d),height:Math.abs(e-g)},c.plotX=c.shapeArgs.x+c.shapeArgs.width/2,c.plotY=c.shapeArgs.y+c.shapeArgs.height/2):(delete c.plotX,delete c.plotY)})},setColorRecursive:function(a,b){var c=this,d,e;if(a){d=c.points[a.i];e=c.levelMap[a.levelDynamic];b=j(d&&d.options.color,e&&e.color,b);if(d)d.color=b;a.children.length&&k(a.children,function(a){c.setColorRecursive(a,b)})}},algorithmGroup:function(a,b,c,d){this.height=a;this.width=b;this.plot=d;this.startDirection=this.direction=c;this.lH=this.nH=this.lW=
this.nW=this.total=0;this.elArr=[];this.lP={total:0,lH:0,nH:0,lW:0,nW:0,nR:0,lR:0,aspectRatio:function(a,c){return Math.max(a/c,c/a)}};this.addElement=function(a){this.lP.total=this.elArr[this.elArr.length-1];this.total+=a;this.direction===0?(this.lW=this.nW,this.lP.lH=this.lP.total/this.lW,this.lP.lR=this.lP.aspectRatio(this.lW,this.lP.lH),this.nW=this.total/this.height,this.lP.nH=this.lP.total/this.nW,this.lP.nR=this.lP.aspectRatio(this.nW,this.lP.nH)):(this.lH=this.nH,this.lP.lW=this.lP.total/
this.lH,this.lP.lR=this.lP.aspectRatio(this.lP.lW,this.lH),this.nH=this.total/this.width,this.lP.nW=this.lP.total/this.nH,this.lP.nR=this.lP.aspectRatio(this.lP.nW,this.nH));this.elArr.push(a)};this.reset=function(){this.lW=this.nW=0;this.elArr=[];this.total=0}},algorithmCalcPoints:function(a,b,c,d){var e,h,g,f,j=c.lW,s=c.lH,l=c.plot,i,o=0,p=c.elArr.length-1;b?(j=c.nW,s=c.nH):i=c.elArr[c.elArr.length-1];k(c.elArr,function(a){if(b||o<p)c.direction===0?(e=l.x,h=l.y,g=j,f=a/g):(e=l.x,h=l.y,f=s,g=a/f),
d.push({x:e,y:h,width:g,height:f}),c.direction===0?l.y+=f:l.x+=g;o+=1});c.reset();c.direction===0?c.width-=j:c.height-=s;l.y=l.parent.y+(l.parent.height-c.height);l.x=l.parent.x+(l.parent.width-c.width);if(a)c.direction=1-c.direction;b||c.addElement(i)},algorithmLowAspectRatio:function(a,b,c){var d=[],e=this,h,g={x:b.x,y:b.y,parent:b},f=0,j=c.length-1,i=new this.algorithmGroup(b.height,b.width,b.direction,g);k(c,function(c){h=b.width*b.height*(c.val/b.val);i.addElement(h);i.lP.nR>i.lP.lR&&e.algorithmCalcPoints(a,
!1,i,d,g);f===j&&e.algorithmCalcPoints(a,!0,i,d,g);f+=1});return d},algorithmFill:function(a,b,c){var d=[],e,f=b.direction,g=b.x,i=b.y,j=b.width,m=b.height,l,n,o,p;k(c,function(c){e=b.width*b.height*(c.val/b.val);l=g;n=i;f===0?(p=m,o=e/p,j-=o,g+=o):(o=j,p=e/o,m-=p,i+=p);d.push({x:l,y:n,width:o,height:p});a&&(f=1-f)});return d},strip:function(a,b){return this.algorithmLowAspectRatio(!1,a,b)},squarified:function(a,b){return this.algorithmLowAspectRatio(!0,a,b)},sliceAndDice:function(a,b){return this.algorithmFill(!0,
a,b)},stripes:function(a,b){return this.algorithmFill(!1,a,b)},translate:function(){var a,b;n.prototype.translate.call(this);this.rootNode=j(this.options.rootId,"");this.levelMap=y(this.options.levels,function(a,b){a[b.level]=b;return a},{});b=this.tree=this.getTree();this.axisRatio=this.xAxis.len/this.yAxis.len;this.nodeMap[""].pointValues=a={x:0,y:0,width:100,height:100};this.nodeMap[""].values=a=m(a,{width:a.width*this.axisRatio,direction:this.options.layoutStartingDirection==="vertical"?0:1,val:b.val});
this.calculateChildrenAreas(b,a);this.colorAxis?this.translateColors():this.options.colorByPoint||this.setColorRecursive(this.tree,void 0);if(this.options.allowDrillToNode)b=this.nodeMap[this.rootNode].pointValues,this.xAxis.setExtremes(b.x,b.x+b.width,!1),this.yAxis.setExtremes(b.y,b.y+b.height,!1),this.xAxis.setScale(),this.yAxis.setScale();this.setPointValues()},drawDataLabels:function(){var a=this,b=r(a.points,function(a){return a.node.visible}),c,d;k(b,function(b){d=a.levelMap[b.node.levelDynamic];
c={style:{}};if(!b.node.isLeaf)c.enabled=!1;if(d&&d.dataLabels)c=m(c,d.dataLabels),a._hasPointLabels=!0;if(b.shapeArgs)c.style.width=b.shapeArgs.width,b.dataLabel&&b.dataLabel.css({width:b.shapeArgs.width+"px"});b.dlOptions=m(c,b.options.dataLabels)});n.prototype.drawDataLabels.call(this)},alignDataLabel:i.column.prototype.alignDataLabel,pointAttribs:function(a,b){var c=this.levelMap[a.node.levelDynamic]||{},d=this.options,e=b&&d.states[b]||{},c={stroke:a.borderColor||c.borderColor||e.borderColor||
d.borderColor,"stroke-width":j(a.borderWidth,c.borderWidth,e.borderWidth,d.borderWidth),dashstyle:a.borderDashStyle||c.borderDashStyle||e.borderDashStyle||d.borderDashStyle,fill:a.color||this.color,zIndex:b==="hover"?1:0};if(a.node.level<=this.nodeMap[this.rootNode].level)c.fill="none",c["stroke-width"]=0;else if(a.node.isLeaf){if(b)c.fill=x(c.fill).brighten(e.brightness).get()}else j(d.interactByLeaf,!d.allowDrillToNode)?c.fill="none":(d=j(e.opacity,d.opacity),c.fill=x(c.fill).setOpacity(d).get());
return c},drawPoints:function(){var a=this,b=r(a.points,function(a){return a.node.visible});k(b,function(c){var b="levelGroup-"+c.node.levelDynamic;a[b]||(a[b]=a.chart.renderer.g(b).attr({zIndex:1E3-c.node.levelDynamic}).add(a.group));c.group=a[b];b=a.pointAttribs(c);c.pointAttr={"":b,hover:a.pointAttribs(c,"hover"),select:{}};b=parseInt(b["stroke-width"],10)%2/2;c.shapeArgs.x-=b;c.shapeArgs.y-=b});i.column.prototype.drawPoints.call(this);a.options.allowDrillToNode&&k(b,function(b){var d;if(b.graphic)d=
b.drillId=a.options.interactByLeaf?a.drillToByLeaf(b):a.drillToByGroup(b),b.graphic.css({cursor:d?"pointer":"default"})})},drillTo:function(){var a=this;f.addEvent(a,"click",function(b){var b=b.point,c=b.drillId,d;c&&(d=a.nodeMap[a.rootNode].name||a.rootNode,b.setState(""),a.drillToNode(c),a.showDrillUpButton(d))})},drillToByGroup:function(a){var b=!1;if(a.node.level-this.nodeMap[this.rootNode].level===1&&!a.node.isLeaf)b=a.id;return b},drillToByLeaf:function(a){var b=!1;if(a.node.parent!==this.rootNode&&
a.node.isLeaf)for(a=a.node;!b;)if(a=this.nodeMap[a.parent],a.parent===this.rootNode)b=a.id;return b},drillUp:function(){var a=null;this.rootNode&&(a=this.nodeMap[this.rootNode],a=a.parent!==null?this.nodeMap[a.parent]:this.nodeMap[""]);if(a!==null)this.drillToNode(a.id),a.id===""?this.drillUpButton=this.drillUpButton.destroy():(a=this.nodeMap[a.parent],this.showDrillUpButton(a.name||a.id))},drillToNode:function(a){this.options.rootId=a;this.isDirty=!0;this.chart.redraw()},showDrillUpButton:function(a){var b=
this,a=a||"< Back",c=b.options.drillUpButton,d,e;if(c.text)a=c.text;this.drillUpButton?this.drillUpButton.attr({text:a}).align():(e=(d=c.theme)&&d.states,this.drillUpButton=this.chart.renderer.button(a,null,null,function(){b.drillUp()},d,e&&e.hover,e&&e.select).attr({align:c.position.align,zIndex:9}).add().align(c.position,!1,c.relativeTo||"plotBox"))},buildKDTree:w,drawLegendSymbol:f.LegendSymbolMixin.drawRectangle,getExtremes:function(){n.prototype.getExtremes.call(this,this.colorValueData);this.valueMin=
this.dataMin;this.valueMax=this.dataMax;n.prototype.getExtremes.call(this)},getExtremesFromAll:!0,bindAxes:function(){var a={endOnTick:!1,gridLineWidth:0,lineWidth:0,min:0,dataMin:0,minPadding:0,max:100,dataMax:100,maxPadding:0,startOnTick:!1,title:null,tickPositions:[]};n.prototype.bindAxes.call(this);f.extend(this.yAxis.options,a);f.extend(this.xAxis.options,a)}}))});
