(function(){
    //定义一些默认参数
    var _options={
        default_word:"default hello"               
    };
 
    var jsonArray="";
    //定义一些api
    var _plugin_htmlToJson = {
    	jsonFunc:function(TabId){
    		jsonArray="[";
    		//文本框转换JSON
    		$("#"+TabId+" :input[type='text']").each(function(){ 
    			var $input=$(this);
    			jsonArray+="{\"CN\":\""+$input.attr("cn")+"\",\"VALUE\":\""+$input.val()+"\",\"TEXT\":\""+$input.val()+"\",\"CODE\":\""+$input.attr("id")+"\",\"TYPE\":\"TEXT\",\"sp\":\""+$input.attr("sp")+"\"},";
    		}); 
    		//密码框转换JSON
    		$("#"+TabId+" :input[type='password']").each(function(){ 
    			var $input=$(this);
    			jsonArray+="{\"CN\":\""+$input.attr("cn")+"\",\"VALUE\":\""+$input.val()+"\",\"TEXT\":\""+$input.val()+"\",\"CODE\":\""+$input.attr("id")+"\",\"TYPE\":\"PASSWORD\"},";
    		}); 
    		//下拉框转换JSON
    		$("#"+TabId+" select").each(function(){ 
    			var $select=$(this);
    			jsonArray+="{\"CN\":\""+$select.attr("cn")+"\",\"VALUE\":\""+$select.val()+"\",\"TEXT\":\""+$select.find("option:selected").text()+"\",\"CODE\":\""+$select.attr("id")+"\",\"TYPE\":\"SELECT\"},";
    		}); 
    		//checkbox转换JSON
    		$("#"+TabId+" input[type=checkbox]:checked").each(function(){ 
    			var $check=$(this);
    			jsonArray+="{\"CN\":\""+$check.attr("cn")+"\",\"VALUE\":\""+$check.val()+"\",\"TEXT\":\""+$check.next().text()+"\",\"CODE\":\""+$check.attr("name")+"\",\"TYPE\":\"CHECKBOX\"},";
    			
    		}); 
    		//radio转换JSON
    		$("#"+TabId+" input[type=radio]:checked").each(function(){ 
    			var $check=$(this);
    			jsonArray+="{\"CN\":\""+$check.attr("cn")+"\",\"VALUE\":\""+$check.val()+"\",\"TEXT\":\""+$check.next().text()+"\",\"CODE\":\""+$check.attr("name")+"\",\"TYPE\":\"RADIO\"},";
    			
    		}); 
    		//textarea转换JSON
    		$("#"+TabId+" textarea").each(function(){ 
    			var $textarea=$(this);
    			jsonArray+="{\"CN\":\""+$textarea.attr("cn")+"\",\"VALUE\":\""+$textarea.text().replace(/\ +/g,"").replace(/[\r\n]/g,"")+"\",\"TEXT\":\""+$textarea.text().replace(/\ +/g,"").replace(/[\r\n]/g,"")+"\",\"CODE\":\""+$textarea.attr("id")+"\",\"TYPE\":\"TEXTAREA\"},";
    			
    		}); 
    		//获取jsonArray去掉最后一个，符号
    		jsonArray=jsonArray.substring(0, jsonArray.length-1);
    		jsonArray+="]";
    		return jsonArray;
    	},
    	//读取XML数据库填充页面文本
    	LoadXML:function(xmlString){
    		var xmlDoc=null;
            //判断浏览器的类型
            //支持IE浏览器 
            if(!window.DOMParser && window.ActiveXObject){   //window.DOMParser 判断是否是非ie浏览器
                var xmlDomVersions = ['MSXML.2.DOMDocument.6.0','MSXML.2.DOMDocument.3.0','Microsoft.XMLDOM'];
                for(var i=0;i<xmlDomVersions.length;i++){
                    try{
                        xmlDoc = new ActiveXObject(xmlDomVersions[i]);
                        xmlDoc.async = false;
                        xmlDoc.loadXML(xmlString); //loadXML方法载入xml字符串
                        break;
                    }catch(e){
                    }
                }
            }
            //支持Mozilla浏览器
            else if(window.DOMParser && document.implementation && document.implementation.createDocument){
                try{
                    /* DOMParser 对象解析 XML 文本并返回一个 XML Document 对象。
                     * 要使用 DOMParser，使用不带参数的构造函数来实例化它，然后调用其 parseFromString() 方法
                     * parseFromString(text, contentType) 参数text:要解析的 XML 标记 参数contentType文本的内容类型
                     * 可能是 "text/xml" 、"application/xml" 或 "application/xhtml+xml" 中的一个。注意，不支持 "text/html"。
                     */
                    domParser = new  DOMParser();
                    xmlDoc = domParser.parseFromString(xmlString, 'text/xml');
                }catch(e){
                }
            }
            else{
                return null;
            }
            
            var nodeList= xmlDoc.getElementsByTagName("CONTENT"); // IE  
            
            for ( var i=0;i<nodeList.length;i++) {
            	 var row = nodeList[i].childNodes.length;
            	 for ( var j = 0; j < row; j++) {
            		  var name = nodeList[i].childNodes[j].tagName;  //动态得到节点名称
            		  //var text=nodeList[i].getElementsByTagName(name)[0].childNodes[0].nodeValue;  //获取文本
            		  var V=nodeList[i].childNodes[j].attributes.getNamedItem("VALUE").value;  //获取xml节点的value
            		  var T=nodeList[i].childNodes[j].attributes.getNamedItem("TYPE").value;  //获取页面元素类型
            		  try{
            			  switch (T) {
            			    case "TEXT":
            			    	$("#"+name).val(V);
            			        break;
            			    case "SELECT":
            			    	$("#"+name).val(V);
            			        break;
            			    case "PASSWORD":
            			    	$("#"+name).val(V);
            			        break;
            			    case "CHECKBOX":
            			    	 $('input:checkbox[name='+name+'][value='+V+']').attr('checked','true');
            			        break;
            			    case "RADIO":
            			    	 $('input:radio[name='+name+'][value='+V+']').attr('checked','true');
            			        break;
            			    case "TEXTAREA":
            			    	$("#"+name).text(V);
            			        break;
            			    default:
            			    	$("#"+name).val(V);
            			 }
            		  }catch(e){
                      }
            		 
				 }
			}
            //return xmlDoc;
    	},
    	//读取JSON填充页面文本
    	LoadJosn:function(Josn){
    		for(var i=0,l=Josn.length;i<l;i++){
    			  var T=Josn[i].TYPE;
    			  var V=Json[i].VALUE;
    			  var N=Json[i].ID;
    			  try{
        			  switch (T) {
        			    case "TEXT":
        			    	$("#"+N).val(V);
        			        break;
        			    case "SELECT":
        			    	$("#"+N).val(V);
        			        break;
        			    case "PASSWORD":
        			    	$("#"+N).val(V);
        			        break;
        			    case "CHECKBOX":
        			    	 $('input:checkbox[name='+N+'][value='+V+']').attr('checked','true');
        			        break;
        			    case "RADIO":
        			    	 $('input:radio[name='+N+'][value='+V+']').attr('checked','true');
        			        break;
        			    case "TEXTAREA":
        			    	$("#"+N).text(V);
        			        break;
        			    default:
        			    	$("#"+N).val(V);
        			 }
        		  }catch(e){
                  }
    		}
    	}
    };
    //确定插件的名称
    this.HTJSON = _plugin_htmlToJson;
})();