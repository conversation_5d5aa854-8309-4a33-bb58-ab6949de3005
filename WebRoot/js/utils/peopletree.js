/*******************************************************************************
 * 人员树js
 *******************************************************************************/
var url;
/**
 * 人员树1：只有接收人页面
 */
function peopleTreeOne(df){
	   url = "http://localhost:8080/EOM/jsp/basetype/producttype/tonyonUserOne.jsp?type="+df.type+"&chkstyle="+df.chkstyle+"&defaults="+df.defaults+"&checkboxSelectOne="+df.checkboxSelectOne;
       layer.open({
			type: 2,  
			skin: 'head-class',
			area: ['600px', '600px'],
        	maxmin: true,
			anim:-1,
  			shift:-1,
			scrollbar: false,
			title: "人员列表",
			content:url ,
		}); 
}
/**
 * 人员树2：有接收人和抄送人
 */
function peopleTreeTwo(df){
	   url = "http://localhost:8080/EOM/jsp/basetype/producttype/tonyonUserTwo.jsp?type="+df.type+"&chkstyle="+df.chkstyle+"&defaults="+df.defaults+"&checkboxSelectOne="+df.checkboxSelectOne;
    layer.open({
			type: 2,  
			skin: 'head-class',
			area: ['600px', '600px'],
     	    maxmin: true,
			anim:-1,
			shift:-1,
			scrollbar: false,
			title: "人员列表",
			content:url ,
		}); 
}
    /**
     * 人员树3:查询某个角色所属人员
     */
function peopleTreeThree(df){
	   url = "http://localhost:8080/EOM/jsp/basetype/producttype/tonyonUserThree.jsp?type="+df.type+"&chkstyle="+df.chkstyle+"&defaults="+df.defaults+"&checkboxSelectOne="+df.checkboxSelectOne+"&ROLE="+df.ROLE;
 layer.open({
			type: 2,  
			skin: 'head-class',
			area: ['400px', '600px'],
  	        maxmin: true,
			anim:-1,
			shift:-1,
			scrollbar: false,
			title: "人员列表",
			content:url ,
		}); 
}

