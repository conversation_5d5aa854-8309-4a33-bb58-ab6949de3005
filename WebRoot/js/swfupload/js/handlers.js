/* This is an example of how to cancel all the files queued up.  It's made somewhat generic.  Just pass your SWFUpload
 object in to this method and it loops through cancelling the uploads. */
function cancelQueue(instance) {
	instance.stopUpload();
	var stats;

	do {
		stats = instance.getStats();
		instance.cancelUpload();
	} while (stats.files_queued !== 0);

}


function fileDialogStart() {
	/* I don't need to do anything here */
}
function fileQueued(file) {
	try {
		// You might include code here that prevents the form from being
		// submitted while the upload is in
		// progress. Then you'll want to put code in the Queue Complete handler
		// to "unblock" the form
		var progress = new FileProgress(file,
				this.customSettings.progressTarget);
		progress.setStatus("队列中...",file);
		//给删除按钮添加动点击事件  事件功能为取消队列
		progress.quXiao(this);
		//progress.toggleCancel(true, this);
	} catch (ex) {
		this.debug(ex);
	}

}

bytesToSize = function(bytes) {
	if (bytes === 0)
		return '0 B';

	var k = 1024;

	sizes = [ 'B', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB' ];

	i = Math.floor(Math.log(bytes) / Math.log(k));

	return Math.floor(bytes / Math.pow(k, i)) + ' ' + sizes[i];
	// toPrecision(3) 后面保留一位小数，如1.0GB //return (bytes / Math.pow(k,
	// i)).toPrecision(3) + ' ' + sizes[i];
};


function fileQueued (file){
	var zd=bytesToSize(file.size);
	var progress = new FileProgress(file,
			this.customSettings.progressTarget);
	progress.fileSize(zd);
}


function fileQueueError(file, errorCode, message) {
	try {
		if (errorCode === SWFUpload.QUEUE_ERROR.QUEUE_LIMIT_EXCEEDED) {
			alert("您试图上传太多的文件"
					+ (message === 0 ? "已经达到上传数量限制了" : "现在最多上传能"
							+ (message > 1 ? " " + message + "个文件" : "1个文件")));
			return;
		}

		var progress = new FileProgress(file,
				this.customSettings.progressTarget);
		progress.setError();
		//progress.toggleCancel(false);

		switch (errorCode) {
		case SWFUpload.QUEUE_ERROR.FILE_EXCEEDS_SIZE_LIMIT:
			progress.setStatus("上传文件过大",file);
			this.debug("Error Code: File too big, File name: " + file.name
					+ ", File size: " + file.size + ", Message: " + message);
			break;
		case SWFUpload.QUEUE_ERROR.ZERO_BYTE_FILE:
			progress.setStatus("无法上传空文件文件",file);
			this.debug("Error Code: Zero byte file, File name: " + file.name
					+ ", File size: " + file.size + ", Message: " + message);
			break;
		case SWFUpload.QUEUE_ERROR.INVALID_FILETYPE:
			progress.setStatus("无法识别上传文件类型",file);
			this.debug("Error Code: Invalid File Type, File name: " + file.name
					+ ", File size: " + file.size + ", Message: " + message);
			break;
		case SWFUpload.QUEUE_ERROR.QUEUE_LIMIT_EXCEEDED:
			alert("您试图上传太多的文件"
					+ (message === 0 ? "已经达到上传数量限制了" : "现在最多上传能"
							+ (message > 1 ? " " + message + "个文件" : "1个文件")));
			break;
		default:
			if (file !== null) {
				progress.setStatus("上传错误",file);
			}
			this.debug("Error Code: " + errorCode + ", File name: " + file.name
					+ ", File size: " + file.size + ", Message: " + message);
			break;
		}
	} catch (ex) {
		this.debug(ex);
	}
}
// 当文件选取完毕且选取的文件经过处理后（指添加到上传队列），会立即触发该事件。可以在该事件中调用this.startUpload()方法来实现文件的自动上传
function fileDialogComplete(numFilesSelected, numFilesQueued) {
	try {
	/*	if (this.getStats().files_queued > 0) {
			document.getElementById(this.customSettings.cancelButtonId).disabled = false;
		}
*/
		/* I want auto start and I can do that here */
	   this.startUpload();
	} catch (ex) {
		this.debug(ex);
	}
}
// 当文件即将上传时会触发该事件,该事件给了你在文件上传前的最后一次机会来验证文件信息、增加要随之上传的附加信息或做其他工作。可以通过返回false来取消本次文件的上传

function uploadStart(file) {
	try {
		/*
		 * I don't want to do any file validation or anything, I'll just update
		 * the UI and return true to indicate that the upload should start
		 */
		var progress = new FileProgress(file,
				this.customSettings.progressTarget);
		progress.setStatus("正在上传...",file);
		//progress.toggleCancel(true, this);
	} catch (ex) {
	}

	return true;
}
// 该事件会在文件的上传过程中反复触发，可以利用该事件来实现上传进度条
function uploadProgress(file, bytesLoaded, bytesTotal) {

	try {
		var percent = Math.ceil((bytesLoaded / bytesTotal) * 100);

		var progress = new FileProgress(file,
				this.customSettings.progressTarget);
		progress.setProgress(percent);
		
		progress.setStatus("正在上传",file);
		
		var progress = new FileProgress(file,
				this.customSettings.progressTarget);
		progress.uploadedSize(percent);
	} catch (ex) {
		this.debug(ex);
	}
}

function uploadSuccess(file, serverData) {
	try {
		var progress = new FileProgress(file,
				this.customSettings.progressTarget);
		
		
		
		if (serverData == '0') {
			progress.setError();
			progress.setStatus("上传失败 请联系管理员",file);
			//progress.toggleCancel(false);
			
		} else {
			//提供上传id给 
			progress.setComplete(serverData);
			//progress.shancu(file.id,serverData);
			progress.setStatus("完成",file);
			progress.shanCu(this,serverData,this.customSettings,file);
			attachmentId.push(serverData);

			//显示下载按钮
			progress.anniu(true,serverData,this.customSettings);
		}

	} catch (ex) {
		this.debug(ex);
	}
}

function uploadComplete(file) {
	try {
		/*
		 * I want the next upload to continue automatically so I'll call
		 * startUpload here
		 
		if (this.getStats().files_queued === 0) {
			document.getElementById(this.customSettings.cancelButtonId).disabled = true;
		} else {
			this.startUpload();
		}*/
	} catch (ex) {
		this.debug(ex);
	}

}
// 文件上传被中断或是文件没有成功上传时会触发该事件。停止、取消文件上传或是在uploadStart事件中返回false都会引发这个事件，但是如果某个文件被取消了但仍然还在队列中则不会触发该事件

function uploadError(file, errorCode, message) {
	try {
		var progress = new FileProgress(file,
				this.customSettings.progressTarget);
		//progress.toggleCancel(false);
		

		switch (errorCode) {
		case SWFUpload.UPLOAD_ERROR.HTTP_ERROR:
			progress.setStatus("上传失败 请重新上传 ",file);
			this.debug("Error Code: HTTP Error, File name: " + file.name
					+ ", Message: " + message);
			break;
		case SWFUpload.UPLOAD_ERROR.MISSING_UPLOAD_URL:
			progress.setStatus("上传地址错误 请联系系统管理员",file);
			this.debug("Error Code: No backend file, File name: " + file.name
					+ ", Message: " + message);
			break;
		case SWFUpload.UPLOAD_ERROR.UPLOAD_FAILED:
			progress.setStatus("上传出现错误",file);
			this.debug("Error Code: Upload Failed, File name: " + file.name
					+ ", File size: " + file.size + ", Message: " + message);
			break;
		case SWFUpload.UPLOAD_ERROR.IO_ERROR:
			progress.setStatus("读取或传输文件时发生错误",file);
			this.debug("Error Code: IO Error, File name: " + file.name
					+ ", Message: " + message);
			break;
		case SWFUpload.UPLOAD_ERROR.SECURITY_ERROR:
			progress.setStatus("上传受到了安全方面的限制",file);
			this.debug("Error Code: Security Error, File name: " + file.name
					+ ", Message: " + message);
			break;
		case SWFUpload.UPLOAD_ERROR.UPLOAD_LIMIT_EXCEEDED:
			progress.setStatus("上传的文件数量超过了允许的最大值",file);
			this.debug("Error Code: Upload Limit Exceeded, File name: "
					+ file.name + ", File size: " + file.size + ", Message: "
					+ message);
			break;
		case SWFUpload.UPLOAD_ERROR.SPECIFIED_FILE_ID_NOT_FOUND:
			progress.setStatus("上传出现错误",file);
			this.debug("Error Code: The file was not found, File name: "
					+ file.name + ", File size: " + file.size + ", Message: "
					+ message);
			break;
		case SWFUpload.UPLOAD_ERROR.FILE_VALIDATION_FAILED:
			progress.setStatus("上传文件验证失败",file);
			this.debug("Error Code: File Validation Failed, File name: "
					+ file.name + ", File size: " + file.size + ", Message: "
					+ message);
			break;
		case SWFUpload.UPLOAD_ERROR.FILE_CANCELLED:
			
			progress.setStatus("上传被取消了",file);
			break;

		case SWFUpload.UPLOAD_ERROR.UPLOAD_STOPPED:
			progress.setStatus("上传被终止了",file);
			break;
		default:
			progress.setStatus("上传失败 ",file);
			this.debug("Error Code: " + errorCode + ", File name: " + file.name
					+ ", File size: " + file.size + ", Message: " + message);
			break;
		}
		progress.setError();

	} catch (ex) {
		this.debug(ex);
	}
}