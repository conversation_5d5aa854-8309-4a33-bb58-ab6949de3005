document.write("<script type='text/javascript' src='js/swfupload/js/swfupload.js'></script>");
document.write("<script type='text/javascript' src='js/swfupload/js/swfupload.queue.js'></script>");
document.write("<script type='text/javascript' src='js/swfupload/js/fileprogress.js'></script>");
document.write("<script type='text/javascript' src='js/swfupload/js/handlers.js'></script>");
document.write("<script type='text/javascript' src='js/js/json2.js'></script>");
document.write("<link href='js/swfupload/css/shangchuan.css' rel='stylesheet'	type='text/css' />");

document.write("<link href='skins/default/css/new_order.css' rel='stylesheet'	type='text/css' />");
var swfu ;
var attachmentId=[];
$(function(){
	var td="		<div id=\"box_schedule\" class=\"box_schedule\"></div><a style=\"margin-right: 90%; \" id=\"swfu-shangChuan\">附件上传</a>" +
			"<div class=\"clear\">" +
			"</div><div style=\"border-top:1px solid #DBEBFB;\">" +
			"<div id=\"uploadfile\" style=\"height:auto;\"></div></div>" +
			"";
	var SC = document.getElementById("swfuploadSC");
	    SC.innerHTML=td;
});
	//surface 删除表链接的 地址
	//id 当前 环节id
    //Known 查询当前id 的上传附件的地址
    //disabled 为true时Flash按钮将变为禁用状态，点击也不会触发任何行为
	//load  为true时 强行关闭页面则会删除附件
	//Rdisable  为true时 关闭初始化加载的附件的 删除按钮
	swfstart=function (surface ,id,Known,disabled,load,Rdisable,isDete){
		
		 var swfuOption = {//swfupload选项
				    upload_url : window.location.protocol+"//"+window.location.host+"/EOM/"+"jsp/base/ftpHandleAction!uploadFile.action", //接收上传的服务端url
				    flash_url : "js/swfupload/js/swfupload.swf",//swfupload压缩包解压后swfupload.swf的url
				    button_placeholder_id : "swfu-shangChuan",//上传按钮占位符的id
				    button_image_url :window.location.protocol+"//"+window.location.host+"/EOM/"+"js/swfupload/images/type_new.png",
				    file_size_limit : "20480 MB",//用户可以选择的文件大小，有效的单位有B、KB、MB、GB，若无单位默认为KB
				    //file_upload_limit:6,
				    button_width: 62, //按钮宽度
				     //上传文件的名称
					file_post_name: "file",
					// 事件处理
					file_dialog_start_handler : fileDialogStart,
					file_queued_handler : fileQueued,
					file_queue_error_handler : fileQueueError,
					preserve_relative_urls:false,
					file_dialog_complete_handler : fileDialogComplete,
					upload_start_handler : uploadStart,
					upload_progress_handler : uploadProgress,
					upload_error_handler : uploadError,
					upload_success_handler : uploadSuccess,
					upload_complete_handler : uploadComplete,
					custom_settings : {
						progressTarget : "box_schedule",
						//删除地址
						deleUrl:window.location.protocol+"//"+window.location.host+"/EOM/"+"jsp/base/ftpHandleAction!deleteFile.action",
						//下载地址
						download:window.location.protocol+"//"+window.location.host+"/EOM/"+"jsp/base/ftpHandleAction!attachmentUpload.action",
						//删除表链接地址	
						delesurface:surface	
					},
					button_disabled:disabled,
				    button_height: 24, //按钮高度
				    button_text: '<span class="btn-txt" style="color:#ffffff !important;">选择文件</span>',//按钮文字
				    button_text_style: '.btn-txt{font-size:12px;text-align:center; font-family:"微软雅黑";color:#ffffff !important;}',
				    button_text_top_padding: 1,
				    button_text_left_padding: 1,
						debug: false
				};
				 swfu = new SWFUpload(swfuOption);//初始化并将swfupload按钮替换swfupload占位
				 this.display(id,Known,surface,disabled,Rdisable,isDete);
				 if(load){
				   window.onbeforeunload = function() //author: meizz   
			        {   
			            	  // Accidentalclosure(attachmentId);  //当页面关闭时，删除附件
			        };
				 }
	};
		//根据统一资源定位器查询 
		//提交后的删除  
		// disabled 禁掉删除按钮
        display=function(id,url,surface,disabled,Rdisable,isDete){
        	if(!!id){
        	 $.ajax({
	             type: "post",
	             url: url,
	             data: {"id":id},
	             dataType: "text",
	             success: function(data){
	            	 var custom_settings = {
	     				progressTarget : "box_schedule",
	     				//cancelButtonId : "btnCancel1"
	     				deleUrl:window.location.protocol+"//"+window.location.host+"/EOM/"+"jsp/base/ftpHandleAction!deleteFile.action",
	     				download:window.location.protocol+"//"+window.location.host+"/EOM/"+"jsp/base/ftpHandleAction!attachmentUpload.action",
	     					//删除表链接地址	
							delesurface:surface	
	     			};
	            	 if(!!data){
	            	 var json =JSON.parse(data);
	            	 for ( var i = 0; i < json.length; i++) {
	            		 if(json[i].id!=null){
						var file={"id":json[i].id,"name":json[i].name,"uploadDate":json[i].uploadDate};
						var progress = new FileProgress(file,custom_settings.progressTarget);
						progress.setStatus("完成",file);
						progress.setProgress(100);
						//显示下载按钮
						progress.anniu(true,json[i].id,custom_settings);
						var  isR = false;
						
						if(isDete!=undefined){
							if(isDete==true){
								if(json[i].isB==1){
									progress.shanCu(true,json[i].id,custom_settings,file);
									isR=true;
								}
							}
						}
						if(!isR){
							//如果disabled 为true  禁掉删除按钮
							if(disabled==json[i].userid){
							     progress.shanCu(true,json[i].id,custom_settings);
							}else{	
								progress.shanCu(false,json[i].id,custom_settings,file);
							}
						}
						
					   }
	            	 }
	            	 }
	             },
	             error:function (XMLHttpRequest, textStatus, errorThrow){
	            	 alert("附件获取失败");
	             }
	         });
			// var progress = new FileProgress(file,"box_schedule");
        	}
        };

        //意外关闭时删除已上传文件
        //attachmentId
        Accidentalclosure=function(attachmentId){
        	for (key in attachmentId) {
        		//删除附件
    			$.ajax({
    				type : "post",
    				url : window.location.protocol+"//"+window.location.host+"/EOM/"+"jsp/base/ftpHandleAction!deleteFile.action",
    				data : {
    					"id" : attachmentId[key]
    				},
    				async:false,
    				dataType : "text",
    				success : function(data) {
    				},
    				error : function(XMLHttpRequest, textStatus, errorThrow) {
    					alert("附件删除失败");
    				}
    			});
			}
        	
        };
