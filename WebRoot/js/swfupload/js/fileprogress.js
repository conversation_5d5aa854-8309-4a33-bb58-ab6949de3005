/*
	A simple class for displaying file information and progress
	Note: This is a demonstration only and not part of SWFUpload.
	Note: Some have had problems adapting this class in IE7. It may not be suitable for your application.
 */

// Constructor
// file is a SWFUpload file object
// targetID is the HTML element id attribute that the FileProgress HTML structure will be added to.
// Instantiating a new FileProgress object with an existing file will reuse/update the existing DOM elements
function FileProgress(file, targetID) {
	// 文件id
	this.fileProgressID = file.id;

	this.opacity = 100;
	this.height = 0;
	this.up_progress = document.getElementById(this.fileProgressID);
	if (!this.up_progress) {
		//up_progress
		this.up_progress = document.createElement("div");
		this.up_progress.className="up_progress"; 
		this.up_progress.id = this.fileProgressID;
		this.up_progress.style.float="left";
		this.up_progress.style.marginLeft="10px";
		this.up_progress.style.marginTop="10px";
		//progress_bg
		this.progress_bg=document.createElement("div");
		this.progress_bg.className="progress_bg";
		
		
		
		//progress_all
		this.progress_all=document.createElement("div");
		this.progress_all.className="progress_all";
		
		//progress_allcon
		this.progress_allcon=document.createElement("div");
		this.progress_allcon.className="progress_allcon";
		
		//progress_allcon_img_div
		this.progress_allcon_img_div=document.createElement("div");
		this.progress_allcon_img_div.style.paddingTop="8px";
		this.progress_allcon_img_div.style.paddingLeft="8px";
		
		//progress_allcon_img
		this.progress_allcon_img=document.createElement("img");
		this.progress_allcon_img.src="js/swfupload/images/gb__.png";
		
		
		
		//pr_con
		this.pr_con=document.createElement("div");
		this.pr_con.className="pr_con";
		
		//pr_title
		this.pr_title=document.createElement("p");
		this.pr_title.className="pr_title";
		//up_date
		this.pr_title_span=document.createElement("span");
		
		//up_date
		this.up_date=document.createElement("span");
		this.up_date.className="up_date";
		
		//pr_all
		this.pr_all=document.createElement("p");
		this.pr_all.className="pr_all";

		//pr_all_span
		this.pr_all_span=document.createElement("span");
		
		//pr_all_span1
		this.pr_all_span1=document.createElement("span");
		this.pr_all_span1.style.padding="0 6px";
		
		
		//pr_all_span2_pr_blue
		this.pr_all_span2_pr_blue=document.createElement("span");
		this.pr_all_span2_pr_blue.className="pr_blue";
		
		//pr_all_span3
		this.pr_all_span3=document.createElement("span");
		
		//pr_dow
		this.pr_dow=document.createElement("div");
		this.pr_dow.className="pr_dow";
		this.pr_dow.title="下载";
		//pr_dow_img
		this.pr_dow_img=document.createElement("img");
		this.pr_dow_img.src="js/swfupload/images/up__.png";
		
		//pr_dle
		this.pr_dle=document.createElement("div");
		this.pr_dle.className="pr_dle";
		this.pr_dle.title="删除";

		//pr_dle_img
		this.pr_dle_img=document.createElement("img");
		this.pr_dle_img.src="js/swfupload/images/del__.png";
		
		this.up_progress.appendChild(this.progress_bg);
		this.up_progress.appendChild(this.progress_all);
		this.progress_all.appendChild(this.progress_allcon);
		
		this.progress_allcon_img_div.appendChild(this.progress_allcon_img);
		this.progress_allcon.appendChild(this.progress_allcon_img_div);
		
		
		this.progress_allcon.appendChild(this.pr_con);
		
		this.pr_con.appendChild(this.pr_title);
		this.pr_title.appendChild(this.pr_title_span);
		this.pr_title.appendChild(this.up_date);
		
		this.pr_con.appendChild(this.pr_all);
		this.pr_all.appendChild(this.pr_all_span);
		this.pr_all.appendChild(this.pr_all_span1);
		this.pr_all.appendChild(this.pr_all_span2_pr_blue);
		this.pr_all.appendChild(this.pr_all_span3);

		this.pr_con.appendChild(this.pr_dow);
		this.pr_dow.appendChild(this.pr_dow_img);

		this.pr_con.appendChild(this.pr_dle);
		this.pr_dle.appendChild(this.pr_dle_img);
		document.getElementById(targetID).appendChild(this.up_progress);
		
	

	}

	this.height = this.up_progress.offsetHeight;
	
}



// 状态
FileProgress.prototype.setStatus = function(status, file) {
	var name = file.name;
	 var nameLength=name.replace(/[^\x00-\xff]/g,"01").length;

	if(nameLength>9){
		name=name.substring(0, 9)+ "...";
	}
	this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[1].childNodes[3].innerHTML = ""+status;
	this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[0].title = file.name;
	this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[0].innerHTML = name ;
	if(file.uploadDate!=null){
		var date=new Date(file.uploadDate);
	    var month = date.getMonth() + 1;
	    var strDate = date.getDate();
	    if (month >= 1 && month <= 9) {
	        month = "0" + month;
	    }
	    if (strDate >= 0 && strDate <= 9) {
	        strDate = "0" + strDate;
	    }
	    var currentdate = date.getFullYear() + "/" + month + "/" + strDate
	            + " "
	    var currentdate1 = date.getFullYear() + "/" + month + "/" + strDate
        + " " + date.getHours() + ":" + date.getMinutes()
        + ":" + date.getSeconds();
	    if(currentdate.indexOf("NaN")==-1){
	    	this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[1].innerHTML=currentdate;
			this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[1].title=currentdate1;
	    }else{
			var str =file.uploadDate;
					str = str.substring(1,str.indexOf(" ")+1);
					str=str.replace(/-/g,"/");
		    this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[1].innerHTML=str;
			this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[1].title=file.uploadDate.replace(/-/g,"/");
	    }
		
	}else{
			var date=new Date();
		    var month = date.getMonth() + 1;
		    var strDate = date.getDate();
		    if (month >= 1 && month <= 9) {
		        month = "0" + month;
		    }
		    if (strDate >= 0 && strDate <= 9) {
		        strDate = "0" + strDate;
		    }
		    var currentdate = date.getFullYear() + "/" + month + "/" + strDate
		            + " "
		    var currentdate1 = date.getFullYear() + "/" + month + "/" + strDate
            + " " + date.getHours() + ":" + date.getMinutes()
            + ":" + date.getSeconds();
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[1].innerHTML=currentdate;
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[0].childNodes[1].title=currentdate1;
	}
};





//添加当前文件大小
FileProgress.prototype.fileSize=function(size){
	this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[1].childNodes[0].innerHTML = " "+size+" ";
	this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[1].childNodes[1].innerHTML = " |";



};
//添加已经上传的大小
FileProgress.prototype.uploadedSize=function(size){
	this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[1].childNodes[2].innerHTML = size+"% ";
};



FileProgress.prototype.setError = function() {
	var oSelf = this;
	setTimeout(function() {
		oSelf.disappear();
	}, 5000);
};

// 取消队列
FileProgress.prototype.quXiao = function(swfUploadInstance) {

	if (swfUploadInstance) {
		var fileID = this.fileProgressID;
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[3].onclick = function() {
			swfUploadInstance.cancelUpload(fileID);
			return false;
		};
	}
};

// 删除上传数据 点击事件付给 删除按钮
FileProgress.prototype.shanCu = function(swfUploadInstance, id, customSettings,
		file) {
	if (swfUploadInstance) {
		var fileID = this.fileProgressID;
		
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[3].onclick = function() {

			// ajax 请求后台删除当前文件
		if(window.confirm("你确定要删除附件！")){
			// 删除管连
			$.ajax({
				type : "post",
				url : customSettings.delesurface,
				data : {
					"id" : id
				},
				async : false,
				dataType : "text",
				error : function(XMLHttpRequest, textStatus, errorThrow) {
					alert("删除失败");
				}
			});
			// 删除附件
			$.ajax({
				type : "post",
				url : customSettings.deleUrl,
				data : {
					"id" : id
				},
				async : false,
				dataType : "text",
				success : function(data) {
					alert(data);
					var progressd = new FileProgress(file,
							customSettings.progressTarget);
					for (key in attachmentId) {
						if (attachmentId[key] == id) {
							attachmentId.splice(key, 1);
						}
						;
					}
					progressd.setError();
					progressd.setStatus("删除成功", file);

				},
				error : function(XMLHttpRequest, textStatus, errorThrow) {
					alert("删除失败");
				}
			});
		}
			return false;
		};
	} else {
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[3].style.visibility = "hidden";
		return false;
	}
};

/*
 * //Show/Hide the cancel button FileProgress.prototype.toggleCancel = function
 * (show, swfUploadInstance) { this.schedule.style.visibility = show ? "visible" :
 * "hidden"; if (swfUploadInstance) { var fileID = this.fileProgressID;
 * debugger;
 * this.schedule.childNodes[1].childNodes[2].childNodes[0].childNodes[0].onclick =
 * function () { swfUploadInstance.cancelUpload(fileID); return false; }; } };
 */

// 进度条
FileProgress.prototype.setProgress = function(percentage) {

	this.up_progress.childNodes[0].style.width = percentage + "%";
};

// 下载按钮的隐藏或者显示
FileProgress.prototype.anniu = function(status, id, customSettings) {
	if (status) {
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[2].style.visibility = 'visible';

		// <a href=""></a>

		// var ahref=document.createElement("a");
		// ahref.href=customSettings.download+"?id="+id;
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[2].onclick = function(){
			$.ajax({
				type : "post",
				url :customSettings.download,
				data : { "id" : id},
				async : false,
				error : function(XMLHttpRequest, textStatus, errorThrow) {
					alert("下载失败");
				},success:function(data){
					if("error"==data){
						alert("附件下载失败！附件不存在或被删除了");
					}else{
						window.location.href=customSettings.download + "?par=1&id=" + id;
					}
				}
			});
			return false;
		};
		/*this.schedule.childNodes[1].childNodes[2].childNodes[1].childNodes[0].href = customSettings.download
				+ "?id=" + id;*/
		
		

	} else {
		this.up_progress.childNodes[1].childNodes[0].childNodes[1].childNodes[2].style.visibility = 'hidden';

	}

};
// 提供id
FileProgress.prototype.setComplete = function(id) {
	this.up_progress.childNodes[1].id = id;
};

// // 隐藏
FileProgress.prototype.disappear = function() {
	this.up_progress.style.display = "none";
	this.up_progress.style.height = "0px";

};
