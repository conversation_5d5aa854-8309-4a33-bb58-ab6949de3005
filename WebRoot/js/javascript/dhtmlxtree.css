.defaultTreeTable{
			margin : 0px;
			padding : 0px;
			border : 0px;
}
.containerTableStyle { overflow : auto; position:relative; top:0; font-size : 12px; -khtml-user-select: none;}
.containerTableStyleRTL span { direction: rtl; unicode-bidi: bidi-override;  }
.containerTableStyleRTL { direction: rtl; overflow : auto; position:relative; top:0; font-size : 12px;}
.standartTreeRow {	font-family : Verdana, Geneva, Arial, Helvetica, sans-serif; 	font-size : 12px; -moz-user-select: none;  }
.selectedTreeRow{ background-color : navy; color:white; font-family : Verdana, Geneva, Arial, Helvetica, sans-serif; 		font-size : 12px;  -moz-user-select: none; }
.dragAndDropRow{ background-color : navy; color:white; }
.standartTreeRow_lor{	text-decoration:underline; background-color : #FFFFF0; font-family : Verdana, Geneva, Arial, Helvetica, sans-serif; 	font-size : 12px; -moz-user-select: none; }
.selectedTreeRow_lor{   text-decoration:underline; background-color : navy; color:white; font-family : Verdana, Geneva, Arial, Helvetica, sans-serif; 		font-size : 12px;  -moz-user-select: none; }

.standartTreeImage{ width:18px; height:18px;  overflow:hidden; border:0; padding:0; margin:0;
font-size:1px; }
.hiddenRow { width:1px;   overflow:hidden;  }
.dragSpanDiv,.dragSpanDiv td{ 	font-size : 12px; 	background-color:white; z-index:999; }
.a_dhx_hidden_input{ position:absolute;  top:-1px; left:-1px; width:1px; height:1px; border:none; background:none; }
.a_dhx_hidden_input{ position:absolute;  top:-1px; left:-1px; width:1px; height:1px; border:none; background:none; }

.selectionBox{
background-color: #FFFFCC;
}
.selectionBar {
	top:0;
	background-color: Black;
	position:absolute;
	overflow:hidden;
	height: 2px;
	z-index : 11;
}

.intreeeditRow{
  font-size:8pt; height:16px; border:1px solid silver; padding:0; margin:0;
  margin-left:4px;
  -moz-user-select:  text;   
  -khtml-user-select: text;
}
.dhx_tree_textSign{
   font-size:8pt;
   font-family:monospace;
   width:21px;
   color:black;
   padding:0px;
   margin:0px;
   cursor:pointer;
   text-align: center;
}
.dhx_tree_opacity{
    opacity:0;
    -moz-opacity:0;
    filter:alpha(opacity=0);
}
.dhx_bg_img_fix{
width:18px;
height:18px;
background-repeat: no-repeat;
background-position: center;
background-position-x: center;
background-position-y: center;
}

.dhxtree_dhx_black, .dhxtree_dhx_skyblue{
	background:white;
	color:black;
}
*html .dhxtree_dhx_skyblue .standartTreeRow, *html .dhxtree_dhx_skyblue .standartTreeRow_lor{
	border-right:0px solid red;
	border-left:0px solid red;
}
*html .dhxtree_dhx_skyblue span.standartTreeRow, *html .dhxtree_dhx_skyblue span.standartTreeRow_lor{
	margin-left:1px;	
}

.dhxtree_dhx_skyblue .standartTreeRow, .dhxtree_dhx_skyblue .standartTreeRow_lor{
	border-right:1px solid transparent;
	border-left: 1px solid transparent;
	font-family:Tahoma;
	font-size:11px !important;
	overflow:hidden;
	padding:0px 0px 0px 0px;
}
.dhxtree_dhx_skyblue .selectedTreeRow_lor, .dhxtree_dhx_skyblue .selectedTreeRow{
	background-color:white;
	background-image:url(imgs/sky_blue_sel_tree.png);
	background-repeat:repeat-x;
	border:1px solid #FFB951;
	color:black;

	line-height:17px;
	
	font-size:11px !important;
	font-family:Tahoma;
	overflow:hidden;
}
html > body /**/ .dhxtree_dhx_skyblue .selectedTreeRow, html > body /**/ .dhxtree_dhx_skyblue .selectedTreeRow_lor{
	padding:1px 0px 1px 0px;
	line-height:normal;
	display:inline-block !ie;
	height:13px;
}
body:nth-of-type(1) .dhxtree_dhx_skyblue span.selectedTreeRow, body:nth-of-type(1) .dhxtree_dhx_skyblue span.selectedTreeRow_lor{
	padding:1px 0px 1px 0px;
  	display:inline-block;
  	padding-top:0px;
  	height:13px;
}
body:nth-of-type(1) .dhxtree_dhx_skyblue span.standartTreeRow, body:nth-of-type(1) .dhxtree_dhx_skyblue span.standartTreeRow_lor{
  	display:inline-block;
  	height:14px;
}

.dhxtree_dhx_web .selectedTreeRow_lor, .dhxtree_dhx_web .selectedTreeRow{
	background-color:transparent;
	
}
.dhxtree_dhx_web span.selectedTreeRow_lor , .dhxtree_dhx_web span.selectedTreeRow{
	background-color:#ACDAF0;
	color:black;
}

.dhxtree_dhx_web td.standartTreeRow, .dhxtree_dhx_web td.selectedTreeRow{
	padding-left:2px;
}
.dhxtree_dhx_web span.standartTreeRow, .dhxtree_dhx_web span.selectedTreeRow{
	padding-left:3px !important;
}



.dhxtree_dhx_web .standartTreeRow, .dhxtree_dhx_web .standartTreeRow, .dhxtree_dhx_web .selectedTreeRow_lor, .dhxtree_dhx_web .selectedTreeRow{
	font-size:12px;
	font-family:Tahoma;
	overflow:hidden;
}
