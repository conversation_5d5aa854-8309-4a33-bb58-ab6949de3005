html,body,ul,li,div,a{margin:0;padding:0;font-size:12px;}
.CRselectBox {
	cursor:pointer;
	display:block;
	width: 80px; height: 30px;
}
.CRselectBoxHover {
	background:#FFFFFF url(../img/select_box_off.gif) no-repeat right center;
	border:1px solid #999;
}
.CRselectBox a.CRselectValue {
	display:block;
	margin:1px 1px 2px;
	padding:1px 20px 2px 4px;
	white-space:nowrap;
	color:#000;	
	overflow:hidden;
	width:100px;
}
.CRselectBoxOptions {
	background:#FFFFFF;
	border:1px solid #999;
	margin-left:-10px;
	list-style:none;
	overflow-y:auto;
	z-index:1000;
	position: absolute;
	width:80px;height:60px;display:none;
	overflow:hidden;
}
.CRselectBoxOptions a{
	//color:#000;
	display:block;
	height:20px;
	line-height:5px;
	padding-left:4px;
	background:#fff;	
	overflow:hidden;
	white-space:nowrap;
}
.CRselectBoxOptions a:hover{
	background:#bbb
}
.CRselectBoxOptions a.selected{
	background:#bbb
}
.CRselectBox a { 
	outline: none; 
	text-decoration:none;
}
.CRselectBox a:focus { 
	outline: none; 
	text-decoration:none;
} 