/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

@media print
{
	.cke_skin_v2 .cke_toolbox
	{
		display: none;
	}
}

.cke_skin_v2 .cke_browser_webkit .cke_toolbox,
.cke_skin_v2 .cke_browser_webkit .cke_toolbox > span
{
	white-space: normal;
}

.cke_skin_v2 .cke_toolbox
{
	clear: both;
	/* Define the padding-bottom otherwise the collapser button will not be clickable #4932*/
	padding-bottom: 1px;
}

.cke_skin_v2 a.cke_toolbox_collapser,
.cke_skin_v2 a:hover.cke_toolbox_collapser
{
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-position: 3px -739px; /* +3px +4px */
	background-repeat: no-repeat;
	width: 11px;
	height: 11px;
	float: right;
	border: outset 1px;
	margin: 11px 2px 2px;
	cursor: pointer;
}

.cke_skin_v2 a.cke_toolbox_collapser span
{
	display: none;
}

.cke_skin_v2 .cke_hc a.cke_toolbox_collapser span
{
	font-size: 10px;
	font-weight: bold;
	font-family: Arial;
	display: inline;
}

.cke_skin_v2 .cke_rtl a.cke_toolbox_collapser,
.cke_skin_v2 .cke_rtl a:hover.cke_toolbox_collapser
{
	float: left;
}

.cke_skin_v2 a.cke_toolbox_collapser_min,
.cke_skin_v2 a:hover.cke_toolbox_collapser_min
{
	/* arrowleft.gif*/
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-position: 4px -760px; /* +4px +3px */
	background-repeat: no-repeat;
	margin: 2px;
}

.cke_skin_v2 .cke_rtl a.cke_toolbox_collapser_min,
.cke_skin_v2 .cke_rtl a:hover.cke_toolbox_collapser_min
{
	/* arrowright.gif*/
	background-position: 4px -781px; /* +2px +3px */
}

.cke_skin_v2 .cke_toolbar
{
	padding-top: 1px;
	padding-bottom: 1px;
	display: inline-block;
	float: left;
}

.cke_skin_v2 .cke_rtl .cke_toolbar
{
	float: right;
}

.cke_skin_v2 .cke_toolgroup
{
	display: inline-block;
	float: left;
}

.cke_skin_v2 .cke_browser_ie .cke_toolgroup
{
	#float: none;
	#display: inline;
}

.cke_skin_v2 .cke_rtl .cke_toolgroup
{
	float: right;
}

.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_toolgroup
{
	#float: none;
}

.cke_skin_v2 .cke_separator
{
	display: inline-block;
	float: left;
	border-left: solid 1px #999;
	margin: 4px 2px;
	height: 16px;
}

.cke_skin_v2 .cke_browser_ie .cke_separator
{
	#float: none;
	#display: inline;
}

.cke_skin_v2 .cke_rtl .cke_separator
{
	float: right;
}

.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_separator
{
	#float: none;
}

.cke_skin_v2 .cke_break
{
	clear: left;
}

.cke_skin_v2 .cke_rtl .cke_break
{
	clear: right;
}

.cke_skin_v2 .cke_toolbar_start
{
	display: inline-block;
	background-image: url(images/toolbar_start.gif);
	background-repeat: no-repeat;
	margin: 4px 2px 0 2px;
	width: 3px;
	height: 20px;
	float: left;
}

.cke_skin_v2 .cke_browser_ie .cke_toolbar_start
{
	#float: none;
	#display: inline;
}

.cke_skin_v2 .cke_rtl .cke_toolbar_start
{
	float: right;
}

.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_toolbar_start
{
	#float: none;
}

.cke_skin_v2 .cke_toolbar_end
{
	display: inline-block;
	float: left;
}

.cke_skin_v2 .cke_browser_ie .cke_toolbar_end
{
	#float: none;
	#display: inline
}

.cke_skin_v2 .cke_rtl .cke_toolbar_end
{
    float: right;
}

.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_toolbar_end
{
	#float: none;
}

/*#7181*/
.cke_skin_v2 .cke_browser_ie9 .cke_rtl .cke_separator,
.cke_skin_v2 .cke_browser_ie9 .cke_rtl .cke_toolbar_start,
.cke_skin_v2 .cke_browser_ie9 .cke_rtl .cke_toolbar_end
{
	vertical-align:middle;
}

.cke_skin_v2 .cke_button a,
.cke_skin_v2 .cke_button a:hover,
.cke_skin_v2 .cke_button a:focus,
.cke_skin_v2 .cke_button a:active,
.cke_skin_v2 .cke_button a.cke_off
{
	border: solid 1px #efefde;
	display: inline-block;
	border-radius: 3px;
	outline: none;
	padding-top: 2px;
	padding-left: 4px;
	padding-right: 4px;
	padding-bottom: 2px;
	height: 18px;
	cursor: default;
}

.cke_skin_v2 .cke_button a,
.cke_skin_v2 .cke_button a.cke_off
{
	background-color: #efefde;
	filter: alpha(opacity=70); /* IE */
	opacity: 0.70; /* Safari, Opera and Mozilla */
	-moz-border-radius: 3px;
	-webkit-border-radius: 3px;
}

.cke_skin_v2 .cke_hc .cke_button a,
.cke_skin_v2 .cke_hc .cke_button a.cke_off
{
	opacity: 1.0;
	filter: alpha(opacity=100);
}

.cke_skin_v2 .cke_button a.cke_on
{
	border: solid 1px #316ac5;
	background-color: #a3d7ff;
	filter: alpha(opacity=100); /* IE */
	opacity: 1; /* Safari, Opera and Mozilla */
}

.cke_skin_v2 .cke_button a.cke_disabled *
{
	filter: alpha(opacity=30); /* IE */
	opacity: 0.3; /* Safari, Opera and Mozilla */
}

/* IE with zoom != 100% will distort the icons otherwise #4821 */
.cke_skin_v2 .cke_browser_ie .cke_button a.cke_disabled *,
.cke_skin_v2 .cke_browser_ie a:hover.cke_button .cke_disabled *
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale'), alpha(opacity=30);
}
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_button a.cke_disabled *,
.cke_skin_v2 .cke_browser_ie .cke_rtl a:hover.cke_button .cke_disabled *
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale'), alpha(opacity=30);
}
.cke_skin_v2 .cke_browser_ie6 .cke_button a.cke_disabled *,
.cke_skin_v2 .cke_browser_ie6 a:hover.cke_button .cke_disabled *,
.cke_skin_v2 .cke_browser_ie .cke_button.cke_noalphafix a.cke_disabled *
{
	filter: alpha(opacity=30);
}

.cke_skin_v2 .cke_hc .cke_button a.cke_disabled *,
.cke_skin_v2 .cke_browser_ie.cke_hc a:hover.cke_button .cke_disabled *
{
	filter: alpha(opacity=60);
	opacity: 0.6;
}

.cke_skin_v2 .cke_button a:hover,
.cke_skin_v2 .cke_button a:focus,
.cke_skin_v2 .cke_button a:active	/* IE */
{
	border: solid 1px #316ac5;
	background-color: #dff1ff;
	padding: 2px 4px;
}

.cke_skin_v2 .cke_hc .cke_button a:hover,
.cke_skin_v2 .cke_hc .cke_button a:focus,
.cke_skin_v2 .cke_hc .cke_button a:active	/* IE */
{
	padding: 0 2px !important;
	border-width: 3px;
}

.cke_skin_v2 .cke_button .cke_icon
{
	background-image: url(icons.png);
	background-position: 100px;
	background-repeat:no-repeat;
	margin-top:1px;
	width: 16px;
	height: 16px;
	display: inline-block;
	cursor: default;
}

.cke_skin_v2 .cke_rtl .cke_button .cke_icon
{
	background-image: url(icons_rtl.png);
}

/* IE with zoom != 100% will distort the icons otherwise #4821 */
.cke_skin_v2 .cke_browser_ie .cke_button .cke_icon
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale');
}
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_button .cke_icon
{
	filter: progid:DXImageTransform.Microsoft.AlphaImageLoader(sizingMethod='scale');
}
.cke_skin_v2 .cke_browser_ie6 .cke_button .cke_icon,
.cke_skin_v2 .cke_browser_ie6 .cke_rtl .cke_button .cke_icon,
.cke_skin_v2 .cke_browser_ie .cke_button.cke_noalphafix .cke_icon,
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_button.cke_noalphafix .cke_icon
{
	filter: ;
}

.cke_skin_v2 .cke_button .cke_label
{
	cursor: default;
	display: none;
	padding-left: 3px;
	vertical-align:middle;
}

.cke_skin_v2 .cke_hc .cke_button .cke_label
{
	padding: 0;
	display: inline-block;
}

.cke_skin_v2 .cke_hc .cke_button .cke_icon
{
	display: none;
}

.cke_skin_v2 .cke_accessibility
{
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	overflow: hidden;
}

.cke_skin_v2 .cke_button .cke_buttonarrow
{
	display: inline-block;
	height: 17px;
	width: 8px;
	background-position: 2px -717px;
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-repeat: no-repeat;
	cursor: default;
}

.cke_skin_v2 .cke_rtl .cke_button .cke_buttonarrow
{
	background-position: 0 -717px;
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
}

/*** Firefox 2 ***/

.cke_skin_v2 .cke_browser_gecko18 .cke_toolbar,
.cke_skin_v2 .cke_browser_gecko18 .cke_button a,
.cke_skin_v2 .cke_browser_gecko18 .cke_button a.cke_off,
.cke_skin_v2 .cke_browser_gecko18 .cke_button .cke_icon,
.cke_skin_v2 .cke_browser_gecko18 .cke_button .cke_buttonarrow,
.cke_skin_v2 .cke_browser_gecko18 .cke_separator,
.cke_skin_v2 .cke_browser_gecko18 .cke_toolbar_start
{
	display: block;
	float: left;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_hc .cke_button .cke_icon
{
	display: none;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_toolbar,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_button a,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_button a.cke_off,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_button .cke_icon,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_button .cke_buttonarrow,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_separator,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_toolbar_start
{
	float: right;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_button .cke_label,
.cke_skin_v2 .cke_browser_gecko18 .cke_break
{
	float: left;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_rtl span.cke_inline_label
{
	float: right;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_button .cke_label,
.cke_skin_v2 .cke_browser_gecko18 .cke_rtl .cke_break
{
	float: right;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_separator
{
	margin-top: 4px;
}

.cke_skin_v2 .cke_browser_gecko18 .cke_button .cke_label
{
	padding-top: 3px;
}

/*** IE6 ***/

.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_toolgroup,
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_separator,
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_toolbar_start,
.cke_skin_v2 .cke_browser_ie .cke_rtl .cke_toolbar_end
{
	float: none;
}

/*** IE8 ***/

.cke_skin_v2 .cke_browser_ie8 .cke_toolbar,
.cke_skin_v2 .cke_browser_ie8 .cke_toolgroup
{
	vertical-align: top;
}

.cke_skin_v2 .cke_browser_iequirks.cke_browser_ie8 .cke_toolbar,
.cke_skin_v2 .cke_browser_iequirks.cke_browser_ie8 .cke_toolgroup
{
	/* revert previous */
	vertical-align: baseline;
}

/* Fix cursor shape consistency on toolbar combos (#4031) */
.cke_skin_v2 .cke_browser_ie .cke_rcombo,
.cke_skin_v2 .cke_browser_ie .cke_rcombo *
{
	cursor: default;
}
