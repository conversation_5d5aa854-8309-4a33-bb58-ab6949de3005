/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

/**
 * Styles for the "templates" plugin.
 */

.cke_skin_v2 .cke_tpl_list
{
	border: #dcdcdc 2px solid;
	background-color: #ffffff;
	overflow: auto;
	width: 100%;
	height: 220px;
}

.cke_skin_v2 .cke_tpl_item
{
	margin: 5px;
	padding: 7px;
	border: #eeeeee 1px solid;
	*width: 88%;
}

.cke_skin_v2 .cke_tpl_preview
{
	border-collapse: separate;
	text-indent:0;
	width: 100%;
}
.cke_skin_v2 .cke_tpl_preview td
{
	padding: 2px;
	vertical-align: middle;
}
.cke_skin_v2 .cke_tpl_preview .cke_tpl_preview_img
{
	width: 100px;
}
.cke_skin_v2 .cke_tpl_preview span
{
	white-space: normal;
}

.cke_skin_v2 .cke_tpl_title
{
	font-weight: bold;
}

.cke_skin_v2 .cke_tpl_list a:active .cke_tpl_item,
.cke_skin_v2 .cke_tpl_list a:hover .cke_tpl_item,
.cke_skin_v2 .cke_tpl_list a:focus .cke_tpl_item
{
	border: #ff9933 1px solid !important;
	background-color: #fffacd !important;
}

.cke_skin_v2 .cke_tpl_list a:active *,
.cke_skin_v2 .cke_tpl_list a:hover *,
.cke_skin_v2 .cke_tpl_list a:focus *
{
	cursor: pointer;
}

/* IE6 contextual selectors childs won't get :hover transition until,
	the hover style of the link itself contains certain CSS declarations.*/
.cke_skin_v2 .cke_browser_ie6 .cke_tpl_list a:active,
.cke_skin_v2 .cke_browser_ie6 .cke_tpl_list a:hover,
.cke_skin_v2 .cke_browser_ie6 .cke_tpl_list a:focus
{
	background-position: 0 0;
}

.cke_skin_v2 .cke_tpl_list a:active .cke_tpl_item,
.cke_skin_v2 .cke_tpl_list a:hover .cke_tpl_item,
.cke_skin_v2 .cke_tpl_list a:focus .cke_tpl_item
{
	border-width: 3px;
}

.cke_skin_v2 .cke_tpl_empty, .cke_tpl_loading
{
	text-align: center;
	padding: 5px;
}
