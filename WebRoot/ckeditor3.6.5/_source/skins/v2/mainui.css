/*
Copyright (c) 2003-2012, CKSource - <PERSON><PERSON>. All rights reserved.
For licensing, see LICENSE.html or http://ckeditor.com/license
*/

.cke_skin_v2 .cke_editor
{
	display: inline-table;
	width: 100%;
}
.cke_skin_v2 .cke_browser_webkit .cke_editor
{
	display: table;	/* #6684 */
}

.cke_skin_v2
{
	display: block;
}

.cke_skin_v2 span.cke_browser_webkit,
.cke_skin_v2 span.cke_browser_gecko18
{
	display: block;
}

.cke_skin_v2 .cke_wrapper
{
	display: block;
}

.cke_skin_v2 .cke_browser_opera .cke_editor
{
	display: table;
}

.cke_skin_v2 .cke_top,
.cke_skin_v2 .cke_bottom,
 .cke_shared .cke_skin_v2
{
	background-color: #efefde;
}

.cke_skin_v2 .cke_top
{
    border-top: solid 1px #fafaf5;
    border-left: solid 1px #fafaf5;
    border-right: solid 1px #696969;
    border-bottom: solid 2px #696969;
}

.cke_skin_v2 .cke_rtl .cke_top
{
    border-left: solid 1px #696969;
    border-right: solid 1px #fafaf5;
}

.cke_skin_v2 .cke_bottom
{
    border-left: solid 1px #696969;
    border-right: solid 1px #696969;
    border-bottom: solid 1px #696969;
}

.cke_skin_v2 .cke_contents
{
	border: solid 1px #696969;
	/* Prevent background content from penetrate through when switching between editing modes. (#4918) */
	background-color: white;
}

.cke_skin_v2 .cke_focus
{
	outline: auto 5px -webkit-focus-ring-color;
}

.cke_skin_v2 textarea.cke_source
{
	font-family: 'Courier New' , Monospace;
	font-size: small;
	background-color: #fff;
	white-space: pre;
}

.cke_skin_v2 .cke_browser_iequirks textarea.cke_source
{
	/* For IE6+Quirks only */
	_white-space: normal;
}

.cke_skin_v2 .cke_resizer
{
	width: 12px;
	height: 12px;
	margin-top: 16px;
	display: block;
	float: right;
	/* resizer.gif*/
	background-image: url(images/sprites.png);
	_background-image: url(images/sprites_ie6.png);
	background-position: 0 -801px;
	background-repeat: no-repeat;
	cursor: se-resize;
}

/* Adobe AIR doesn't support *-resize cursor shape. */
.cke_skin_v2 .cke_browser_air .cke_resizer,
.cke_skin_v2 .cke_browser_air .cke_rtl .cke_resizer
{
	cursor: move;
}

.cke_skin_v2 .cke_resizer_rtl
{
	cursor: sw-resize;
	/* resizer_rtl.gif*/
	background-position: 0 -828px;
	float: left;
}

.cke_skin_v2 .cke_resizer_horizontal,
.cke_skin_v2 .cke_rtl .cke_resizer_horizontal
{
	cursor: e-resize;
}

.cke_skin_v2 .cke_resizer_vertical,
.cke_skin_v2 .cke_rtl .cke_resizer_vertical
{
	cursor: n-resize;
}

.cke_skin_v2 .cke_maximized .cke_resizer
{
	display: none;
}

.cke_skin_v2 .cke_browser_ie6 .cke_contents textarea,
.cke_skin_v2 .cke_browser_ie7 .cke_contents textarea
{
	position: absolute;
}

/* All voice labels are not displayed. */
.cke_skin_v2 .cke_voice_label
{
	display: none;
}

.cke_skin_v2 legend.cke_voice_label
{
	display: none;
}

.cke_skin_v2 .cke_browser_ie legend.cke_voice_label
{
	position: absolute;
	display: block;
	width: 0;
	height: 0;
	overflow: hidden;
}
