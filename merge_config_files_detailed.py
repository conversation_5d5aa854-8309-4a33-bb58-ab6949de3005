#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重新合并85和86两套配置文件到merge目录
对所有差异配置增加详细注释描述
"""

import os
import shutil
import xml.etree.ElementTree as ET
from xml.dom import minidom
import re

def create_merge_directory():
    """创建merge目录"""
    merge_dir = "config/merge"
    if os.path.exists(merge_dir):
        shutil.rmtree(merge_dir)
    os.makedirs(merge_dir, exist_ok=True)
    return merge_dir

def merge_properties_files_detailed(file1_path, file2_path, output_path):
    """详细合并properties文件，显示所有差异"""
    props1 = {}
    props2 = {}
    comments1 = {}
    comments2 = {}
    
    # 读取第一个文件
    try:
        with open(file1_path, 'r', encoding='utf-8') as f:
            lines1 = f.readlines()
        
        current_comment = []
        for line in lines1:
            line = line.rstrip()
            if line.startswith('#') or line.startswith('!'):
                current_comment.append(line)
            elif '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                props1[key] = value.strip()
                if current_comment:
                    comments1[key] = '\n'.join(current_comment)
                    current_comment = []
            else:
                current_comment = []
    except Exception as e:
        print(f"读取文件 {file1_path} 失败: {e}")
    
    # 读取第二个文件
    try:
        with open(file2_path, 'r', encoding='utf-8') as f:
            lines2 = f.readlines()
        
        current_comment = []
        for line in lines2:
            line = line.rstrip()
            if line.startswith('#') or line.startswith('!'):
                current_comment.append(line)
            elif '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                props2[key] = value.strip()
                if current_comment:
                    comments2[key] = '\n'.join(current_comment)
                    current_comment = []
            else:
                current_comment = []
    except Exception as e:
        print(f"读取文件 {file2_path} 失败: {e}")
    
    # 合并配置
    all_keys = set(props1.keys()) | set(props2.keys())
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# EOM系统配置文件 - 详细合并版本\n")
        f.write("# 此文件由85和86环境配置合并生成\n")
        f.write("# 合并规则: 保留所有配置，详细标注差异\n")
        f.write("# 生成时间: 自动生成\n\n")
        
        for key in sorted(all_keys):
            # 写入原始注释
            if key in comments1:
                f.write(f"{comments1[key]}\n")
            elif key in comments2:
                f.write(f"{comments2[key]}\n")
            
            # 处理配置项
            if key in props1 and key in props2:
                if props1[key] != props2[key]:
                    f.write(f"# ========== 配置差异 ==========\n")
                    f.write(f"# 配置项: {key}\n")
                    f.write(f"# 85环境值: {props1[key]}\n")
                    f.write(f"# 86环境值: {props2[key]}\n")
                    f.write(f"# 差异分析: {analyze_property_difference(key, props1[key], props2[key])}\n")
                    f.write(f"# ================================\n")
                    f.write(f"# 当前采用85环境配置\n")
                    f.write(f"{key}={props1[key]}\n")
                    f.write(f"# 如需使用86环境配置，请取消下行注释并注释上行\n")
                    f.write(f"# {key}={props2[key]}\n")
                else:
                    f.write(f"# 两环境配置一致\n")
                    f.write(f"{key}={props1[key]}\n")
            elif key in props1:
                f.write(f"# ========== 环境独有配置 ==========\n")
                f.write(f"# 仅在85环境中存在: {key}\n")
                f.write(f"# 配置值: {props1[key]}\n")
                f.write(f"# 用途分析: {analyze_property_purpose(key, props1[key])}\n")
                f.write(f"# ===================================\n")
                f.write(f"{key}={props1[key]}\n")
            else:
                f.write(f"# ========== 环境独有配置 ==========\n")
                f.write(f"# 仅在86环境中存在: {key}\n")
                f.write(f"# 配置值: {props2[key]}\n")
                f.write(f"# 用途分析: {analyze_property_purpose(key, props2[key])}\n")
                f.write(f"# ===================================\n")
                f.write(f"# {key}={props2[key]}\n")
                f.write(f"# 注意: 此配置仅在86环境中存在，默认注释\n")
            
            f.write("\n")

def analyze_property_difference(key, value1, value2):
    """分析配置差异的原因"""
    key_lower = key.lower()
    
    # 服务器地址差异
    if '*************' in value1 and '*************' in value2:
        return '服务器IP地址差异，85环境指向.85服务器，86环境指向.86服务器'
    elif '*************' in value1 and '*************' in value2:
        return '服务器IP地址差异，配置相反'
    
    # 支付环境差异
    if '*************' in value1 and '**************' in value2:
        return '支付环境差异，85环境使用生产支付(.41)，86环境使用测试支付(.139)'
    elif '**************' in value1 and '*************' in value2:
        return '支付环境差异，配置相反'
    
    # 路径差异
    if '/FTPUpLoad/' in value1 and '/UploadFiles/' in value2:
        return '文件路径结构差异，85环境使用FTPUpLoad目录，86环境使用UploadFiles目录'
    elif '/UploadFiles/' in value1 and '/FTPUpLoad/' in value2:
        return '文件路径结构差异，配置相反'
    
    # 开关配置
    if value1 in ['start', 'close', 'open', 'true', 'false'] or value2 in ['start', 'close', 'open', 'true', 'false']:
        return f'功能开关差异，85环境={value1}，86环境={value2}'
    
    # 数值差异
    if value1.isdigit() and value2.isdigit():
        return f'数值配置差异，85环境={value1}，86环境={value2}'
    
    # 其他差异
    return f'配置值不同，可能是环境特定配置'

def analyze_property_purpose(key, value):
    """分析配置项的用途"""
    key_lower = key.lower()
    
    if 'url' in key_lower or 'address' in key_lower:
        return '接口地址配置'
    elif 'path' in key_lower or 'dir' in key_lower:
        return '文件路径配置'
    elif 'switch' in key_lower:
        return '功能开关配置'
    elif 'key' in key_lower or 'password' in key_lower:
        return '认证配置'
    elif 'pay' in key_lower:
        return '支付相关配置'
    elif 'ftp' in key_lower:
        return 'FTP服务配置'
    else:
        return '业务配置'

def parse_xml_with_structure(file_path):
    """解析XML文件并保留结构信息"""
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        return tree, root
    except Exception as e:
        print(f"解析XML文件 {file_path} 失败: {e}")
        return None, None

def get_element_signature(element):
    """获取元素的详细签名"""
    attrs = []
    for key, value in element.attrib.items():
        attrs.append(f'{key}="{value}"')
    
    children = []
    for child in element:
        if child.tag == 'property':
            prop_name = child.get('name', '')
            prop_value = child.get('value', child.get('ref', ''))
            children.append(f'property[{prop_name}={prop_value}]')
        elif child.tag == 'result':
            result_name = child.get('name', 'success')
            result_value = child.text or ''
            children.append(f'result[{result_name}={result_value.strip()}]')
        else:
            children.append(child.tag)
    
    return {
        'attributes': attrs,
        'children': children,
        'attr_count': len(element.attrib),
        'child_count': len(list(element))
    }

def merge_xml_files_detailed(file1_path, file2_path, output_path):
    """详细合并XML文件，显示所有差异"""
    try:
        # 直接读取文件内容
        with open(file1_path, 'r', encoding='utf-8') as f:
            content1 = f.read()
        with open(file2_path, 'r', encoding='utf-8') as f:
            content2 = f.read()

        # 解析XML结构
        tree1, root1 = parse_xml_with_structure(file1_path)
        tree2, root2 = parse_xml_with_structure(file2_path)

        if not tree1 or not tree2:
            # 如果解析失败，选择较大的文件
            if os.path.getsize(file1_path) >= os.path.getsize(file2_path):
                shutil.copy2(file1_path, output_path)
            else:
                shutil.copy2(file2_path, output_path)
            return

        # 收集两个文件中的所有元素
        elements1 = {}
        elements2 = {}

        def collect_elements(root, elements_dict):
            for child in root:
                # 移除命名空间前缀
                tag_name = child.tag.split('}')[-1] if '}' in child.tag else child.tag
                if tag_name in ['bean', 'action', 'package', 'constant']:
                    key = f"{tag_name}#{child.get('id') or child.get('name', '')}"
                    elements_dict[key] = child

        collect_elements(root1, elements1)
        collect_elements(root2, elements2)

        # 开始构建合并后的内容
        merged_lines = []

        # 提取并复制文件头部（包括XML声明和根元素开始标签）
        header_match = re.search(r'(.*?<[^>]*(?:beans|struts)[^>]*>)', content1, re.DOTALL)
        if header_match:
            header_lines = header_match.group(1).split('\n')
            merged_lines.extend(header_lines)

        # 添加合并说明注释
        merged_lines.extend([
            '',
            '    <!-- EOM系统配置文件 - 详细合并版本 -->',
            '    <!-- 此文件由85和86环境配置合并生成 -->',
            '    <!-- 合并规则: 保留所有配置，详细标注差异 -->',
            '    <!-- 生成时间: 自动生成 -->',
            ''
        ])

        # 合并元素
        all_keys = set(elements1.keys()) | set(elements2.keys())

        for key in sorted(all_keys):
            if key in elements1 and key in elements2:
                elem1 = elements1[key]
                elem2 = elements2[key]

                sig1 = get_element_signature(elem1)
                sig2 = get_element_signature(elem2)

                # 添加差异分析注释
                merged_lines.extend([
                    '',
                    '    <!-- ========== 配置差异分析 ========== -->',
                    f'    <!-- 配置项: {key} -->',
                    f'    <!-- 85环境: {sig1["attr_count"]}个属性, {sig1["child_count"]}个子元素 -->',
                    f'    <!-- 86环境: {sig2["attr_count"]}个属性, {sig2["child_count"]}个子元素 -->',
                    f'    <!-- 属性差异: {compare_attributes(elem1.attrib, elem2.attrib)} -->',
                    f'    <!-- 子元素差异: {compare_children(elem1, elem2)} -->',
                    '    <!-- ======================================= -->',
                    '    <!-- 当前采用85环境配置 -->',
                    ''
                ])

                # 从原文件中提取元素的原始文本
                elem_id = elem1.get('id') or elem1.get('name', '')
                if elem_id:
                    # 在原文件中查找这个元素的原始文本
                    tag_name = elem1.tag.split('}')[-1] if '}' in elem1.tag else elem1.tag
                    pattern = rf'<{tag_name}[^>]*(?:id|name)="{re.escape(elem_id)}"[^>]*>.*?</{tag_name}>'
                    match = re.search(pattern, content1, re.DOTALL)
                    if match:
                        elem_text = match.group(0)
                        # 添加缩进
                        elem_lines = elem_text.split('\n')
                        for line in elem_lines:
                            if line.strip():
                                merged_lines.append('    ' + line.strip())

                # 如果86环境配置不同，添加注释说明
                if sig1 != sig2:
                    merged_lines.extend([
                        '',
                        '    <!-- 86环境替代配置 (已注释): -->',
                        f'    <!-- 属性: {", ".join(sig2["attributes"])} -->',
                        f'    <!-- 子元素: {", ".join(sig2["children"])} -->'
                    ])

            elif key in elements1:
                # 仅在85中存在
                merged_lines.extend([
                    '',
                    '    <!-- ========== 85环境独有配置 ========== -->',
                    f'    <!-- 配置项: {key} -->',
                    f'    <!-- 说明: 此配置仅在85环境中存在 -->',
                    f'    <!-- 用途: {analyze_element_purpose(elements1[key])} -->',
                    '    <!-- ===================================== -->',
                    ''
                ])

                # 从原文件中提取元素的原始文本
                elem = elements1[key]
                elem_id = elem.get('id') or elem.get('name', '')
                if elem_id:
                    tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                    pattern = rf'<{tag_name}[^>]*(?:id|name)="{re.escape(elem_id)}"[^>]*>.*?</{tag_name}>'
                    match = re.search(pattern, content1, re.DOTALL)
                    if match:
                        elem_text = match.group(0)
                        elem_lines = elem_text.split('\n')
                        for line in elem_lines:
                            if line.strip():
                                merged_lines.append('    ' + line.strip())

            else:
                # 仅在86中存在
                merged_lines.extend([
                    '',
                    '    <!-- ========== 86环境独有配置 ========== -->',
                    f'    <!-- 配置项: {key} -->',
                    f'    <!-- 说明: 此配置仅在86环境中存在 -->',
                    f'    <!-- 用途: {analyze_element_purpose(elements2[key])} -->',
                    '    <!-- 状态: 默认注释，如需启用请取消注释 -->',
                    '    <!-- ===================================== -->',
                    ''
                ])

                # 将86环境独有的配置作为注释添加
                elem = elements2[key]
                elem_id = elem.get('id') or elem.get('name', '')
                if elem_id:
                    tag_name = elem.tag.split('}')[-1] if '}' in elem.tag else elem.tag
                    pattern = rf'<{tag_name}[^>]*(?:id|name)="{re.escape(elem_id)}"[^>]*>.*?</{tag_name}>'
                    match = re.search(pattern, content2, re.DOTALL)
                    if match:
                        elem_text = match.group(0)
                        elem_lines = elem_text.split('\n')
                        for line in elem_lines:
                            if line.strip():
                                merged_lines.append('    <!-- ' + line.strip() + ' -->')

        # 添加根元素结束标签
        root_tag = root1.tag.split('}')[-1] if '}' in root1.tag else root1.tag
        merged_lines.extend(['', f'</{root_tag}>'])

        # 写入合并后的XML文件
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(merged_lines))

    except Exception as e:
        print(f"合并XML文件 {output_path} 失败: {e}")
        # 如果合并失败，复制较大的文件
        if os.path.exists(file1_path) and os.path.exists(file2_path):
            if os.path.getsize(file1_path) >= os.path.getsize(file2_path):
                shutil.copy2(file1_path, output_path)
            else:
                shutil.copy2(file2_path, output_path)

def compare_attributes(attrs1, attrs2):
    """比较元素属性"""
    if attrs1 == attrs2:
        return "属性相同"
    
    diff_attrs = []
    all_keys = set(attrs1.keys()) | set(attrs2.keys())
    
    for key in all_keys:
        if key in attrs1 and key in attrs2:
            if attrs1[key] != attrs2[key]:
                diff_attrs.append(f"{key}(85:{attrs1[key]} vs 86:{attrs2[key]})")
        elif key in attrs1:
            diff_attrs.append(f"{key}(仅85环境)")
        else:
            diff_attrs.append(f"{key}(仅86环境)")
    
    return "; ".join(diff_attrs) if diff_attrs else "属性相同"

def compare_children(elem1, elem2):
    """比较子元素"""
    children1 = {f"{child.tag}#{child.get('name', child.get('ref', ''))}" for child in elem1}
    children2 = {f"{child.tag}#{child.get('name', child.get('ref', ''))}" for child in elem2}
    
    if children1 == children2:
        return "子元素相同"
    
    only_1 = children1 - children2
    only_2 = children2 - children1
    
    diff_parts = []
    if only_1:
        diff_parts.append(f"仅85环境: {', '.join(only_1)}")
    if only_2:
        diff_parts.append(f"仅86环境: {', '.join(only_2)}")
    
    return "; ".join(diff_parts) if diff_parts else "子元素相同"

def analyze_element_purpose(element):
    """分析元素的用途"""
    element_id = element.get('id') or element.get('name', '')
    element_class = element.get('class', '')
    
    if 'Action' in element_class:
        if 'pay' in element_id.lower():
            return '支付相关Action'
        elif 'app' in element_id.lower():
            return 'APP接口Action'
        elif 'contract' in element_id.lower():
            return '合同管理Action'
        elif 'oms' in element_id.lower():
            return '订单管理Action'
        else:
            return '业务Action控制器'
    elif element.tag == 'action':
        return 'Struts Action映射'
    elif element.tag == 'bean':
        return 'Spring Bean配置'
    else:
        return '配置组件'

def merge_configurations_detailed():
    """详细合并配置文件"""
    
    config_85_dir = "config/85"
    config_86_dir = "config/86"
    merge_dir = create_merge_directory()
    
    # 配置文件列表
    config_files = [
        'FtpConfig.properties',
        'WebService-config.properties',
        'applicationContext-action.xml',
        'applicationContext-jpbm.xml',
        'applicationContext-manager.xml',
        'struts.xml'
    ]
    
    print(f"开始详细合并配置文件到 {merge_dir} 目录...")
    
    merge_summary = []
    
    for config_file in config_files:
        file1_path = os.path.join(config_85_dir, config_file)
        file2_path = os.path.join(config_86_dir, config_file)
        output_path = os.path.join(merge_dir, config_file)
        
        if not os.path.exists(file1_path) and not os.path.exists(file2_path):
            print(f"⚠️  {config_file}: 两个环境都不存在此文件")
            continue
        elif not os.path.exists(file1_path):
            print(f"📋 {config_file}: 仅86环境存在，直接复制")
            shutil.copy2(file2_path, output_path)
            merge_summary.append({
                'file': config_file,
                'status': '仅86环境存在',
                'action': '直接复制'
            })
        elif not os.path.exists(file2_path):
            print(f"📋 {config_file}: 仅85环境存在，直接复制")
            shutil.copy2(file1_path, output_path)
            merge_summary.append({
                'file': config_file,
                'status': '仅85环境存在',
                'action': '直接复制'
            })
        else:
            print(f"🔄 {config_file}: 详细合并两个环境的配置")
            
            if config_file.endswith('.properties'):
                merge_properties_files_detailed(file1_path, file2_path, output_path)
                merge_summary.append({
                    'file': config_file,
                    'status': '两环境都存在',
                    'action': 'Properties详细合并'
                })
            elif config_file.endswith('.xml'):
                merge_xml_files_detailed(file1_path, file2_path, output_path)
                merge_summary.append({
                    'file': config_file,
                    'status': '两环境都存在',
                    'action': 'XML详细合并'
                })
    
    # 生成详细合并报告
    generate_detailed_merge_report(merge_dir, merge_summary)
    
    print(f"\n✅ 详细配置文件合并完成！")
    print(f"📁 输出目录: {merge_dir}")
    print(f"📄 合并文件: {len(merge_summary)} 个")

def generate_detailed_merge_report(merge_dir, merge_summary):
    """生成详细合并报告"""
    report_path = os.path.join(merge_dir, "详细合并报告.md")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# EOM配置文件详细合并报告\n\n")
        f.write("## 📋 合并说明\n\n")
        f.write("本目录包含了85和86环境配置文件的详细合并结果：\n\n")
        f.write("### 🔧 详细合并规则\n\n")
        f.write("1. **Properties文件**: \n")
        f.write("   - 保留所有配置项\n")
        f.write("   - 差异项详细标注两环境的不同值\n")
        f.write("   - 提供差异分析和用途说明\n")
        f.write("   - 86环境独有配置默认注释\n\n")
        f.write("2. **XML文件**: \n")
        f.write("   - 保留所有Bean/Action配置\n")
        f.write("   - 详细对比属性和子元素差异\n")
        f.write("   - 标注配置来源环境\n")
        f.write("   - 86环境独有配置作为注释保留\n\n")
        f.write("3. **差异标注**: \n")
        f.write("   - 详细的差异分析注释\n")
        f.write("   - 配置用途和影响说明\n")
        f.write("   - 环境切换指导\n\n")
        
        f.write("### 📊 合并结果\n\n")
        f.write("| 配置文件 | 状态 | 处理方式 |\n")
        f.write("|----------|------|----------|\n")
        
        for item in merge_summary:
            f.write(f"| {item['file']} | {item['status']} | {item['action']} |\n")
        
        f.write(f"\n### 💡 使用指南\n\n")
        f.write("#### 🔧 配置切换\n")
        f.write("1. **Properties文件**: 根据注释说明取消相应行的注释\n")
        f.write("2. **XML文件**: 根据注释说明启用86环境配置\n")
        f.write("3. **环境适配**: 根据目标环境调整服务器地址等配置\n\n")
        
        f.write("#### ⚠️ 注意事项\n")
        f.write("1. **服务器地址**: 注意85(.85)和86(.86)服务器地址差异\n")
        f.write("2. **支付环境**: 注意生产(.41)和测试(.139)支付环境差异\n")
        f.write("3. **文件路径**: 注意FTPUpLoad和UploadFiles路径差异\n")
        f.write("4. **功能开关**: 注意各种功能开关的环境差异\n\n")
        
        f.write("#### 🚀 部署建议\n")
        f.write("1. **测试验证**: 在测试环境充分验证配置正确性\n")
        f.write("2. **备份配置**: 部署前备份原始配置文件\n")
        f.write("3. **分步部署**: 建议分模块逐步部署验证\n")
        f.write("4. **监控检查**: 部署后监控系统运行状态\n\n")
        
        f.write("---\n\n")
        f.write("*此报告由详细合并脚本自动生成，包含完整的差异分析*\n")

if __name__ == "__main__":
    merge_configurations_detailed()
