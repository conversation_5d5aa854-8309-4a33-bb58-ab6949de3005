package com.xinxinsoft.entity.commonSingManagement;

import java.io.Serializable;
import java.util.Date;
import java.util.HashSet;
import java.util.Set;

import com.google.gson.annotations.Expose;
import com.xinxinsoft.entity.enclosure.Attachment;


/**
 * 
 * 订单表：
 * 
 * <AUTHOR>
 *
 * @Date  2016,9,6 
 * @version 1.0
 */
public class OrderForm implements Serializable{

	public static final String	ORDERFORM	= "ORDERFORM";
	
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;


	/**
	 * 紧急状况
	 */
	public static String EMERGENCY_SITUATION="es";
	
	
	/**
	 * 请求类型
	 */
	public static final String  REQUEST_TYPE="rt";
	/**
	 * 地市
	 */
	public static final String  CITY="city";
	/**
	 * 区域
	 */
	public static final String  REGION="region";
	/**
	 * 渠道
	 */
	public static final String  CHANNEL="channel";
	
	
	
	/**
	 * 提交方式
	 * web  :W
	 * 手机端 M
	 */
	private String submissionMode;
	
	/**
	 * 类型
	 * 专线:DI
	 * 通用单：CS
	 */
	private String type;
	
	/**
	 * 完结
	 */
    @Expose
	public String isFinish;
	
    /**
     * boss 系统返回的值的id
     */
	public String sOrderId;
	
	
   
    /**
	 * 需求名称
	 */
    @Expose
	private String demandName;
	
	

	
	
	/**
	 * 订单经理id
	 * 
	 */
	@Expose
	private Integer userId;

	/**
	 * 订单经理
	 */
	@Expose
	private String userName;
	

	/**
	 * 订单ID	
	 */
    @Expose
	private String orderId;
	/**
	 * 编号	
	 */
    @Expose
	private String orderNumber ;
	/**
	 * 标题	
	 */
    @Expose
	private String orderTitle;
	/**
	 * 集团客户ID	
	 */
    @Expose
	private String groupCustomerId;
	/**
	 * 个人客户ID	
	 */
	private String individualCustomerId;
	/**
	 * 业务类型
	 */
	private String bCode;


	

	
	/**
	 * 产品类型：
	 */
	@Expose
	private String pCode;
	
	/**
	 * 订单类型
	 */
	@Expose
	private String orderType;
	/**
	 * 需求描述
	 */
	@Expose
	private String orderReqDescription;
	/**
	 * 需求描述HTML
	 */
	@Expose
	private String orderReqDescriptionHTML;
	/**
	 * 需求时限-->订单要求完成时间：
	 */
	@Expose
	private Date orderReqTimeLimit;
	
	/**
	 * 环节处理时限：
	 */
	private String linkTimeLimit;
	
	/**
	 * 起草人
	 */
	@Expose
	private String draftman;
	/**
	 * 起草人ID
	 */
	@Expose
	private String draftmanId;
	/**
	 * 起草时间
	 */
	@Expose
	private Date draftTime;
	/**
	 * 修改时间
	 */
	private Date updateTime;
	/**
	 * 操作类型：1表示开通：2变更：3故障：4报修：
	 */
	@Expose
	private String operationType;
	/**
	 * 签约情况: 0表示未签约：1表示已签约：
	 */
	private String signedStatus;
	/**
	 * 是否为加急通用单：0表示不是，1表示是
	 */
	private String emergency;

	/**
	 * 状态：删除 -1，已完成 1（通用，专业归档修改），待处理（进行中） 0 ,
	 * 专业订单: 退回 2 ，  已处理 3，结束 -2
	 */
	@Expose
	private String state;
	/**
	 * 提交状态：发送：1，草稿：0
	 */
	private String transmitState;
	
	/**
	 * 父级订单号：
	 */
	@Expose
	private String parentOrderNumber;
	
	/***
	 * 附件：
	 */
	@Expose
	private Set<Attachment> attachment = new HashSet<Attachment>();
	/**
	 * 备注
	 */
	@Expose
	private String remarks;
	
	/**
	 * boss 
	 */
	private String bossFormNo;
	/**
	 * 0, 1 已归档（通用、专业归档都修改为1）
	 */
	private Integer orderStatus;
	/**
	 * 订单完成时间：
	 */
	private Date orderCompletionTime;
	
	/**
	 * 客户联系电话：
	 */
	private String orderTelephone;
	
	/**
	 * 客户联系人名称：
	 */
	private String customerLinkman;
	
	/**
	 * BOSS状态。 （1：发给BOSS，2：BOSS完成；0：未发给BOSS发生错误；目前只对专用订单；
	 */
	private Integer bossState;
	
	/**
	 * 订单标识：0：需求单，1 ：正常订单；
	 */
	private Integer orderTypeIdent;
	@Expose
	private String zpcode;
	
	@Expose
	private String systemDeptID;//部门ID
	
	@Expose
	private String prantsystemDeptID;//区县公司ID
	
	@Expose
	private String systemCompanyID;//分公司ID
	
	@Expose
	private String inttype;//区分是否通专融合订单

	public String getZpcode() {
		return zpcode;
	}
	public void setZpcode(String zpcode) {
		this.zpcode = zpcode;
	}
	public String getIsFinish() {
		return isFinish;
	}
	public void setIsFinish(String isFinish) {
		this.isFinish = isFinish;
	}
	
	public String getDemandName() {
		return demandName;
	}
	public void setDemandName(String demandName) {
		this.demandName = demandName;
	}
	public String getRemarks() {
		return remarks;
	}
	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	
	public Integer getUserId() {
		return userId;
	}
	public void setUserId(Integer userId) {
		this.userId = userId;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getType() {
		return type;
	}
	public void setType(String type) {
		this.type = type;
	}
	public String getSubmissionMode() {
		return submissionMode;
	}
	public void setSubmissionMode(String submissionMode) {
		this.submissionMode = submissionMode;
	}

	public String getOrderId() {
		return orderId;
	}
	public void setOrderId(String orderId) {
		this.orderId = orderId;
	}
	public String getOrderNumber() {
		return orderNumber;
	}
	public void setOrderNumber(String orderNumber) {
		this.orderNumber = orderNumber;
	}
	public String getOrderTitle() {
		return orderTitle;
	}
	public void setOrderTitle(String orderTitle) {
		this.orderTitle = orderTitle;
	}

	public String getOrderType() {
		return orderType;
	}
	public void setOrderType(String orderType) {
		this.orderType = orderType;
	}
	public String getOrderReqDescription() {
		return orderReqDescription;
	}
	public void setOrderReqDescription(String orderReqDescription) {
		this.orderReqDescription = orderReqDescription;
	}
	
	public Date getOrderReqTimeLimit() {
		return orderReqTimeLimit;
	}
	public void setOrderReqTimeLimit(Date orderReqTimeLimit) {
		this.orderReqTimeLimit = orderReqTimeLimit;
	}
	public String getDraftman() {
		return draftman;
	}
	public void setDraftman(String draftman) {
		this.draftman = draftman;
	}
	public Date getDraftTime() {
		return draftTime;
	}
	public void setDraftTime(Date draftTime) {
		this.draftTime = draftTime;
	}
	
	public String getGroupCustomerId() {
		return groupCustomerId;
	}
	public void setGroupCustomerId(String groupCustomerId) {
		this.groupCustomerId = groupCustomerId;
	}
	
	public String getIndividualCustomerId() {
		return individualCustomerId;
	}
	public void setIndividualCustomerId(String individualCustomerId) {
		this.individualCustomerId = individualCustomerId;
	}
	public Date getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(Date updateTime) {
		this.updateTime = updateTime;
	}
	public String getDraftmanId() {
		return draftmanId;
	}
	public void setDraftmanId(String draftmanId) {
		this.draftmanId = draftmanId;
	}
	public String getOrderReqDescriptionHTML() {
		return orderReqDescriptionHTML;
	}
	public void setOrderReqDescriptionHTML(String orderReqDescriptionHTML) {
		this.orderReqDescriptionHTML = orderReqDescriptionHTML;
	}
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getOperationType() {
		return operationType;
	}
	public void setOperationType(String operationType) {
		this.operationType = operationType;
	}
	public String getSignedStatus() {
		return signedStatus;
	}
	public void setSignedStatus(String signedStatus) {
		this.signedStatus = signedStatus;
	}
	public String getEmergency() {
		return emergency;
	}
	public void setEmergency(String emergency) {
		this.emergency = emergency;
	}
	public Set<Attachment> getAttachment() {
		return attachment;
	}
	public void setAttachment(Set<Attachment> attachment) {
		this.attachment = attachment;
	}
	public String getTransmitState() {
		return transmitState;
	}
	public void setTransmitState(String transmitState) {
		this.transmitState = transmitState;
	}
	public String getParentOrderNumber() {
		return parentOrderNumber;
	}
	public void setParentOrderNumber(String parentOrderNumber) {
		this.parentOrderNumber = parentOrderNumber;
	}
	public String getbCode() {
		return bCode;
	}
	public void setbCode(String bCode) {
		this.bCode = bCode;
	}
	public String getpCode() {
		return pCode;
	}
	public void setpCode(String pCode) {
		this.pCode = pCode;
	}
	public String getLinkTimeLimit() {
		return linkTimeLimit;
	}
	public void setLinkTimeLimit(String linkTimeLimit) {
		this.linkTimeLimit = linkTimeLimit;
	}
	public String getBossFormNo() {
		return bossFormNo;
	}
	public void setBossFormNo(String bossFormNo) {
		this.bossFormNo = bossFormNo;
	}
	public Integer getOrderStatus() {
		return orderStatus;
	}
	public void setOrderStatus(Integer orderStatus) {
		this.orderStatus = orderStatus;
	}
	
	public String getsOrderId() {
		return sOrderId;
	}
	public void setsOrderId(String sOrderId) {
		this.sOrderId = sOrderId;
	}
	public Date getOrderCompletionTime() {
		return orderCompletionTime;
	}
	public void setOrderCompletionTime(Date orderCompletionTime) {
		this.orderCompletionTime = orderCompletionTime;
	}
	public String getOrderTelephone() {
		return orderTelephone;
	}
	public void setOrderTelephone(String orderTelephone) {
		this.orderTelephone = orderTelephone;
	}
	public String getCustomerLinkman() {
		return customerLinkman;
	}
	public void setCustomerLinkman(String customerLinkman) {
		this.customerLinkman = customerLinkman;
	}
	public Integer getBossState() {
		return bossState;
	}
	public void setBossState(Integer bossState) {
		this.bossState = bossState;
	}
	public Integer getOrderTypeIdent() {
		return orderTypeIdent;
	}
	public void setOrderTypeIdent(Integer orderTypeIdent) {
		this.orderTypeIdent = orderTypeIdent;
	}
	public String getSystemDeptID() {
		return systemDeptID;
	}
	public void setSystemDeptID(String systemDeptID) {
		this.systemDeptID = systemDeptID;
	}
	public String getPrantsystemDeptID() {
		return prantsystemDeptID;
	}
	public void setPrantsystemDeptID(String prantsystemDeptID) {
		this.prantsystemDeptID = prantsystemDeptID;
	}
	public String getSystemCompanyID() {
		return systemCompanyID;
	}
	public void setSystemCompanyID(String systemCompanyID) {
		this.systemCompanyID = systemCompanyID;
	}
	public String getInttype() {
		return inttype;
	}
	public void setInttype(String inttype) {
		this.inttype = inttype;
	}
}
