package com.xinxinsoft.entity.manualInvApply;

import java.io.Serializable;

import com.google.gson.annotations.Expose;

public class ManualInvApplyDet implements Serializable{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	   * 编号
	   */
	  @Expose private String id;
	  /**
	   * 工单编号
	   */
	  @Expose private String orderNo;
	  /**
	   * 明细流水号
	   */
	  @Expose private String orderDetNo;
	  /**
	   * 发票接收号码
	   */
	  @Expose private String recPhone;
	  /**
	   * 用户名称 最多输入21个汉字
	   */
	  @Expose private String userName;
	  /**
	   * 电话号码
	   */
	  @Expose private String userPhone;
	  /**
	   * 帐号号码
	   */
	  @Expose private String contrctNo;
	  /**
	   * 帐号类型
	   */
	  @Expose private String contrctType;
	  /**
	   * 项目名称
	   */
	  @Expose private String projectName;
	  /**
	   * 项目名称-补充项
	   */
	  @Expose private String projectNameDetail;
	  /**
	   * 数量
	   */
		  @Expose private String quantity;
	  /**
	   * 单价
	   */
	  @Expose private String unitPrice;
	  /**
	   * 单位 默认： 元
	   */
	  @Expose private String unit;
	  /**
	   * 总价 单价*数量
	   */
	  @Expose private String totalPrice;
	  /**
	   * 行业名称 默认 电信业 ；其他
	   */
	  @Expose private String industryName;
	  /**
	   * 客户地址
	   */
	  @Expose private String acctAddress;
	  
	  /**
	   * 备注数量 只有项目名称为通信服务费时，才是“**月通信服务费”
	   */
	  @Expose private String memoin;
	  
	  /**
	   * 备注 只有项目名称为通信服务费时，才是“**月通信服务费”，其他项目名称则只有一个输入框
	   */
	  @Expose private String memo;
	  /**
	   * 制表人
	   */
	  @Expose private String lister;
	  /**
	   * 收款人
	   */
	  @Expose private String payee;
	  
	  /**
	   * 申请人boss工号
	   */
	  @Expose private String bossNo;
	  
	  /**
	   * 明细状态 0已申请未开票 1已冲正 2已开票 -1已作废 
	   */
	  @Expose private int state;
	  
	  @Expose private String bossStatus;
	  /**
	   * 是否存送
	   */
	  @Expose private String whether;//1是，-1否

		/**
		 * 月结发票关联账期
		 */
	  @Expose private String monthly;//1是，-1否

	public String getId() {
		return id;
	}
	public void setId(String id) {
		this.id = id;
	}
	public String getOrderNo() {
		return orderNo;
	}
	public void setOrderNo(String orderNo) {
		this.orderNo = orderNo;
	}
	public String getOrderDetNo() {
		return orderDetNo;
	}
	public void setOrderDetNo(String orderDetNo) {
		this.orderDetNo = orderDetNo;
	}
	public String getRecPhone() {
		return recPhone;
	}
	public void setRecPhone(String recPhone) {
		this.recPhone = recPhone;
	}
	public String getUserName() {
		return userName;
	}
	public void setUserName(String userName) {
		this.userName = userName;
	}
	public String getUserPhone() {
		return userPhone;
	}
	public void setUserPhone(String userPhone) {
		this.userPhone = userPhone;
	}
	public String getContrctNo() {
		return contrctNo;
	}
	public void setContrctNo(String contrctNo) {
		this.contrctNo = contrctNo;
	}
	public String getContrctType() {
		return contrctType;
	}
	public void setContrctType(String contrctType) {
		this.contrctType = contrctType;
	}
	public String getProjectName() {
		return projectName;
	}
	public void setProjectName(String projectName) {
		this.projectName = projectName;
	}
	public String getQuantity() {
		return quantity;
	}
	public void setQuantity(String quantity) {
		this.quantity = quantity;
	}
	public String getUnitPrice() {
		return unitPrice;
	}
	public void setUnitPrice(String unitPrice) {
		this.unitPrice = unitPrice;
	}
	public String getUnit() {
		return unit;
	}
	public void setUnit(String unit) {
		this.unit = unit;
	}
	public String getTotalPrice() {
		return totalPrice;
	}
	public void setTotalPrice(String totalPrice) {
		this.totalPrice = totalPrice;
	}
	public String getIndustryName() {
		return industryName;
	}
	public void setIndustryName(String industryName) {
		this.industryName = industryName;
	}
	public String getMemo() {
		return memo;
	}
	public void setMemo(String memo) {
		this.memo = memo;
	}
	public String getLister() {
		return lister;
	}
	public void setLister(String lister) {
		this.lister = lister;
	}
	public String getPayee() {
		return payee;
	}
	public void setPayee(String payee) {
		this.payee = payee;
	}
	public int getState() {
		return state;
	}
	public void setState(int state) {
		this.state = state;
	}
	public String getProjectNameDetail() {
		return projectNameDetail;
	}
	public void setProjectNameDetail(String projectNameDetail) {
		this.projectNameDetail = projectNameDetail;
	}
	public String getAcctAddress() {
		return acctAddress;
	}
	public void setAcctAddress(String acctAddress) {
		this.acctAddress = acctAddress;
	}
	public String getBossNo() {
		return bossNo;
	}
	public void setBossNo(String bossNo) {
		this.bossNo = bossNo;
	}
	public String getMemoin() {
		return memoin;
	}
	public void setMemoin(String memoin) {
		this.memoin = memoin;
	}
	public String getBossStatus() {
		return bossStatus;
	}
	public void setBossStatus(String bossStatus) {
		this.bossStatus = bossStatus;
	}
	public String getWhether() {
		return whether;
	}
	public void setWhether(String whether) {
		this.whether = whether;
	}
	public String getMonthly() {
		return monthly;
	}

	public void setMonthly(String monthly) {
		this.monthly = monthly;
	}
}
