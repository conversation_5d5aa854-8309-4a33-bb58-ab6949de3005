<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE hibernate-mapping PUBLIC "-//Hibernate/Hibernate Mapping DTD 3.0//EN"
        "http://hibernate.sourceforge.net/hibernate-mapping-3.0.dtd" >

<hibernate-mapping package="com.xinxinsoft.entity.SIMHighFrequency">
    <class name="com.xinxinsoft.entity.SIMHighFrequency.SIMHighFrequencyInfo" table="SIM_HIGHFREQUENCYINFO">
        <id name="id">
            <column name="id"></column>
            <generator class="uuid"/>
        </id>
        <property name="region" column="REGION" type="java.lang.String" length="25"/>
        <property name="companyCode" column="COMPANY_CODE" type="java.lang.String"/>
        <property name="groupCode" column="GROUP_CODE" type="java.lang.String" length="25"/>
        <property name="groupName" column="GROUP_NAME" type="java.lang.String"/>
        <property name="groupGrade" column="GROUP_GRADE" type="java.lang.String" length="25"/>
        <property name="phoneNo" column="PHONE_NO" type="java.lang.String" length="25"/>
        <property name="callingNo" column="CALLING_NO" type="java.lang.String" length="25"/>
        <property name="dispersion" column="DISPERSION" type="java.lang.String"/>
        <property name="startTime" column="START_TIME" type="java.util.Date"/>
        <property name="rel_phone_no" column="REL_PHONE_NO" type="java.lang.String"/>
        <property name="gu_phone_no" column="GU_PHONE_NO" type="java.lang.String"/>
        <property name="prod_prod_prcid" column="PROD_PROD_PRCID" type="java.lang.String"/>
        <property name="prod_prod_prcname" column="PROD_PROD_PRCNAME" type="java.lang.String"/>
        <property name="rn" column="RN" type="java.lang.String"/>
        <property name="importTime" column="IMPORT_TIME" type="java.util.Date"/>
        <property name="cardState" column="CARD_STATE" type="java.lang.String"/>
        <property name="dispatchState" column="DISPATCH_STATE" type="java.lang.String"/>
        <property name="employeeName" column="EMPLOYEE_NAME" type="java.lang.String"/>

    </class>
</hibernate-mapping>