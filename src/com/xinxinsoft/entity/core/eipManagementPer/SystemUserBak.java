package com.xinxinsoft.entity.core.eipManagementPer;

import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import com.google.gson.annotations.Expose;
import com.xinxinsoft.entity.core.Role;

/**
 * 登录用户
 * <AUTHOR>
 * @date 2016-8-10
 */
public class SystemUserBak {

	/**用户编号*/
	@Expose private int rowNo;
	
	/***/
	private String employeeNo;
	
	/**登录名*/
	@Expose private String loginName;
	
	/**登录密码*/
	private String loginPwd;
	
	/**姓名*/
	@Expose private String employeeName;
	
	/**邮箱*/
	private String mail;
	
	/**员工职责*/
	private String employeeDuty;
	
	/**员工等级*/
	private String employeeLevel;
	
	/***/
	private String employeeOrder;
	
	/**手机*/
	private String mobile;
	
	/**短号*/
	private String shortCode;
	
	/**员工状态（0:正常 ，1：测试 ，2：锁定，3：未启用）*/
	@Expose  private int employeeStatus;
	
/*	*//**employeeTypeNo*//*
	private int employeeTypeNo;*/
	
	/**areaCode*/
	private String areaCode;
	
	/**employeeWorkNo*/
	private String employeeWorkNo;
	
	/**adSyncId*/
	private String adSyncId;
	
	/**身份证*/
	private String employeeIdCode;
	
	/**生日*/
	private Date birthday;
	
	/**入职日期*/
	@Expose  private Date employDate;
	
	/**dispatchStatus*/
	private String dispatchStatus;
	
	/**登记类别*/
	private String registerType;
	
	/**mlsLoginName*/
	private String mlsLoginName;
	
	/**tel*/
	private String tel;
	
	/**地址*/
	private String address;
	/**地址*/
	private String loginState;//空值为从未登录过，1为已经登录过
	/**
	 * insert 插入时间：
	 */
	private Date insertTime ;
	
	/**部门*/
	@Expose private List<SystemDeptBak> systemDept;
	
	
	/**角色*/
	@Expose private Set<Role> roles = new HashSet<Role>();
	/**
	 * 工号编号
	 */
	private String operNo;
	/**
	 * 工号名称
	 */
	private String operName;
	/**
	 * 用户头像：
	 */
	private String userHeadImg;
	/**
	 * 4A密码
	 */
	private String passWord4A;
	/**
	 * 4A帐号状态：1正常；0加锁
	 */
	private String status4A;
	/**
	 * 4A端帐号标识
	 */
	private String userId4A;
	/**
	 * 4A组织机构
	 */
	private String orgId4A;
	/**
	 * 4A生效时间
	 */
	private String effectdate4A;
	/**
	 * 4A失效时间
	 */
	private String expiredate4A;
	/**
	 * 4A备注
	 */
	private String remark4A;
	
	/**
	 *boss 工号；
	 */
	private String bossUserName;
	private String eip_UpdateTime;
	/**
	 * SMAP统一用户唯一标识
	 */
	private String smapOID;
	/**SMAP统一用户的UID标识
	 * 
	 */
	private String smapUID;
	/**
	 * 员工类型
	 */
	private Integer userType;
	/**
	 * 员工类型名称
	 */
	private String userTypeName;
	public String getEip_UpdateTime() {
		return eip_UpdateTime;
	}

	public void setEip_UpdateTime(String eip_UpdateTime) {
		this.eip_UpdateTime = eip_UpdateTime;
	}

	public String getOrgId4A() {
		return orgId4A;
	}

	public void setOrgId4A(String orgId4A) {
		this.orgId4A = orgId4A;
	}

	public String getEffectdate4A() {
		return effectdate4A;
	}

	public void setEffectdate4A(String effectdate4a) {
		effectdate4A = effectdate4a;
	}

	public String getExpiredate4A() {
		return expiredate4A;
	}

	public void setExpiredate4A(String expiredate4a) {
		expiredate4A = expiredate4a;
	}

	public String getRemark4A() {
		return remark4A;
	}

	public void setRemark4A(String remark4a) {
		remark4A = remark4a;
	}

	public String getPassWord4A() {
		return passWord4A;
	}

	public void setPassWord4A(String passWord4A) {
		this.passWord4A = passWord4A;
	}

	public String getStatus4A() {
		return status4A;
	}

	public void setStatus4A(String status4a) {
		status4A = status4a;
	}

	public String getUserId4A() {
		return userId4A;
	}

	public void setUserId4A(String userId4A) {
		this.userId4A = userId4A;
	}

	public String getOperNo() {
		return operNo;
	}

	public void setOperNo(String operNo) {
		this.operNo = operNo;
	}

	public String getOperName() {
		return operName;
	}

	public void setOperName(String operName) {
		this.operName = operName;
	}

	public int getRowNo() {
		return rowNo;
	}

	public void setRowNo(int rowNo) {
		this.rowNo = rowNo;
	}

	public String getEmployeeNo() {
		return employeeNo;
	}

	public void setEmployeeNo(String employeeNo) {
		this.employeeNo = employeeNo;
	}

	public String getLoginName() {
		return loginName;
	}

	public void setLoginName(String loginName) {
		this.loginName = loginName;
	}

	public String getLoginPwd() {
		return loginPwd;
	}

	public void setLoginPwd(String loginPwd) {
		this.loginPwd = loginPwd;
	}

	public String getEmployeeName() {
		return employeeName;
	}

	public void setEmployeeName(String employeeName) {
		this.employeeName = employeeName;
	}

	public String getMail() {
		return mail;
	}

	public void setMail(String mail) {
		this.mail = mail;
	}

	public String getEmployeeDuty() {
		return employeeDuty;
	}

	public void setEmployeeDuty(String employeeDuty) {
		this.employeeDuty = employeeDuty;
	}

	public String getEmployeeLevel() {
		return employeeLevel;
	}

	public void setEmployeeLevel(String employeeLevel) {
		this.employeeLevel = employeeLevel;
	}

	public String getEmployeeOrder() {
		return employeeOrder;
	}

	public void setEmployeeOrder(String employeeOrder) {
		this.employeeOrder = employeeOrder;
	}

	public String getMobile() {
		return mobile;
	}

	public void setMobile(String mobile) {
		this.mobile = mobile;
	}

	public String getShortCode() {
		return shortCode;
	}

	public void setShortCode(String shortCode) {
		this.shortCode = shortCode;
	}

	public int getEmployeeStatus() {
		return employeeStatus;
	}

	public void setEmployeeStatus(int employeeStatus) {
		this.employeeStatus = employeeStatus;
	}


	public String getAreaCode() {
		return areaCode;
	}

	public void setAreaCode(String areaCode) {
		this.areaCode = areaCode;
	}

	public String getEmployeeWorkNo() {
		return employeeWorkNo;
	}

	public void setEmployeeWorkNo(String employeeWorkNo) {
		this.employeeWorkNo = employeeWorkNo;
	}

	public String getAdSyncId() {
		return adSyncId;
	}

	public void setAdSyncId(String adSyncId) {
		this.adSyncId = adSyncId;
	}

	public String getEmployeeIdCode() {
		return employeeIdCode;
	}

	public void setEmployeeIdCode(String employeeIdCode) {
		this.employeeIdCode = employeeIdCode;
	}

	public Date getBirthday() {
		return birthday;
	}

	public void setBirthday(Date birthday) {
		this.birthday = birthday;
	}

	public Date getEmployDate() {
		return employDate;
	}

	public void setEmployDate(Date employDate) {
		this.employDate = employDate;
	}

	public String getDispatchStatus() {
		return dispatchStatus;
	}

	public void setDispatchStatus(String dispatchStatus) {
		this.dispatchStatus = dispatchStatus;
	}

	public String getRegisterType() {
		return registerType;
	}

	public void setRegisterType(String registerType) {
		this.registerType = registerType;
	}

	public String getMlsLoginName() {
		return mlsLoginName;
	}

	public void setMlsLoginName(String mlsLoginName) {
		this.mlsLoginName = mlsLoginName;
	}

	public String getTel() {
		return tel;
	}

	public void setTel(String tel) {
		this.tel = tel;
	}

	public String getAddress() {
		return address;
	}

	public void setAddress(String address) {
		this.address = address;
	}

	public Set<Role> getRoles() {
		return roles;
	}

	public void setRoles(Set<Role> roles) {
		this.roles = roles;
	}

	public List<SystemDeptBak> getSystemDept() {
		return systemDept;
	}

	public void setSystemDept(List<SystemDeptBak> systemDept) {
		this.systemDept = systemDept;
	}

	public String getLoginState() {
		return loginState;
	}

	public void setLoginState(String loginState) {
		this.loginState = loginState;
	}

	public String getUserHeadImg() {
		return userHeadImg;
	}

	public void setUserHeadImg(String userHeadImg) {
		this.userHeadImg = userHeadImg;
	}

	public String getBossUserName() {
		return bossUserName;
	}

	public void setBossUserName(String bossUserName) {
		this.bossUserName = bossUserName;
	}

	public Date getInsertTime() {
		return insertTime;
	}

	public void setInsertTime(Date insertTime) {
		this.insertTime = insertTime;
	}

	public String getSmapOID() {
		return smapOID;
	}

	public void setSmapOID(String smapOID) {
		this.smapOID = smapOID;
	}

	public String getSmapUID() {
		return smapUID;
	}

	public void setSmapUID(String smapUID) {
		this.smapUID = smapUID;
	}

	public Integer getUserType() {
		return userType;
	}

	public void setUserType(Integer userType) {
		this.userType = userType;
	}

	public String getUserTypeName() {
		return userTypeName;
	}

	public void setUserTypeName(String userTypeName) {
		this.userTypeName = userTypeName;
	}

	
}
