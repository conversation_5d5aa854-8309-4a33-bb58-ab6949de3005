package com.xinxinsoft.entity.customer;

import java.io.Serializable; 
import java.util.Date;

import com.google.gson.annotations.Expose;

/**
 * 集团联系信息
 * <AUTHOR>
 *
 */
public class GroupContactInfo implements Serializable {
 
	private static final long serialVersionUID = 554223159008180282L; 
	/**
	 * 集团客户资料UUID;
	 */
	private String groupUUID;
	/**
	 * 集团编码
	 */
	private String groupCode;
	/**
	 * 证件类型
	 */
	private String identType;


	/**
	 * 2021.10.8新增
	 * 注册资金
	 */
	private String registeredCapital;
	/**
	 * 2021.10.8新增
	 * 组织机构类型
	 */
	private String organizationType;


	/**
	 * 证件地址
	 */
	private String identAddress; 
	
	/**
	 * 营业区
	 */
	private String businessArea;
	/**
	 * 集团名称
	 */
	private String groupName;
	/**
	 * 证件号码
	 */
	private String identNumber;
	/**
	 * 证件有效期
	 */
	private String identValidDate;

	/**
	 * 证件成立时间 2021.10.12新增
	 */
	private String formed;
	/**
	 * 证件失效时间 2021.10.12新增
	 */
	private String expirationTime;



	/**
	 * 客户地址
	 */
	private String customerAddress; 
	/**
	 * 附件id
	 */
	private String groupAttId;
	/**
	 * 联系人名称  法人名称
	 */
	private String contactsName;
	/**
	 * 联系人地址
	 */
	private String contactsAddr;  
	/**
	 * 签证机关
	 */
	private String visaOffice;    
	/**
	 * 联系人固定电话
	 */
	private String contactFixedTelephone;
	/**
	 * 联系人移动电话
	 */
	private String contactMobilePhone; 
	/**
	 * 	国籍
	 */
	private String resp_nation;
	/**
	 * 民族
	 */
	private String resp_nation_id; 
	/**
	 * 性别
	 */
	private String sex;
	/**
	 * 状态
	 */
	private String state; 
	
	/**
	 * 客户电子邮箱
	 */
	private String customerEmail;
	/**
	 * 客户创建时间
	 */
	private Date createDate;
	/**
	 * 客户用户等级
	 */
	private String  userLevel;
	/**
	 * 客户描述
	 */
	private String describe; 
	/**
	 * 客户所属城市：
	 */
	private String city;
	@Expose
	private String user_name;//客户经理工号
	@Expose
	private String chinese_name;//客户经理名称
	@Expose
	private String mobile_phone;//客户经理电话
	@Expose
	private String unitType; //集团类型（01：小微集团，02:正常集团）
	@Expose
	private String longitude;//经度
	@Expose
	private String latitude;//纬度
	@Expose
	private String portrait;    //是否需要人像比对     1:是    0：否
	@Expose
	private String portraitId;  //人像比对流水号
	@Expose
	private String agentPersonPhone;  //经办人手机号

	public String getAgentPersonPhone() {
		return agentPersonPhone;
	}

	public void setAgentPersonPhone(String agentPersonPhone) {
		this.agentPersonPhone = agentPersonPhone;
	}

	public String getGroupUUID() {
		return groupUUID;
	}
	public void setGroupUUID(String groupUUID) {
		this.groupUUID = groupUUID;
	}
	public String getGroupCode() {
		return groupCode;
	}
	public void setGroupCode(String groupCode) {
		this.groupCode = groupCode;
	}
	public String getIdentType() {
		return identType;
	}
	public void setIdentType(String identType) {
		this.identType = identType;
	}
	public String getIdentAddress() {
		return identAddress;
	}
	public void setIdentAddress(String identAddress) {
		this.identAddress = identAddress;
	}
	public String getBusinessArea() {
		return businessArea;
	}
	public void setBusinessArea(String businessArea) {
		this.businessArea = businessArea;
	}
	public String getGroupName() {
		return groupName;
	}
	public void setGroupName(String groupName) {
		this.groupName = groupName;
	}
	public String getIdentNumber() {
		return identNumber;
	}
	public void setIdentNumber(String identNumber) {
		this.identNumber = identNumber;
	}
	 
	public String getCustomerAddress() {
		return customerAddress;
	}
	public void setCustomerAddress(String customerAddress) {
		this.customerAddress = customerAddress;
	}
	public String getGroupAttId() {
		return groupAttId;
	}
	public void setGroupAttId(String groupAttId) {
		this.groupAttId = groupAttId;
	}
	public String getContactsName() {
		return contactsName;
	}
	public void setContactsName(String contactsName) {
		this.contactsName = contactsName;
	}
	public String getContactsAddr() {
		return contactsAddr;
	}
	public void setContactsAddr(String contactsAddr) {
		this.contactsAddr = contactsAddr;
	}
	 
	public String getVisaOffice() {
		return visaOffice;
	}
	public void setVisaOffice(String visaOffice) {
		this.visaOffice = visaOffice;
	}
	public String getContactFixedTelephone() {
		return contactFixedTelephone;
	}
	public void setContactFixedTelephone(String contactFixedTelephone) {
		this.contactFixedTelephone = contactFixedTelephone;
	}
	public String getContactMobilePhone() {
		return contactMobilePhone;
	}
	public void setContactMobilePhone(String contactMobilePhone) {
		this.contactMobilePhone = contactMobilePhone;
	}

	public String getResp_nation() {
		return resp_nation;
	}
	public void setResp_nation(String resp_nation) {
		this.resp_nation = resp_nation;
	}
	public String getResp_nation_id() {
		return resp_nation_id;
	}
	public void setResp_nation_id(String resp_nation_id) {
		this.resp_nation_id = resp_nation_id;
	}
	
	public String getState() {
		return state;
	}
	public void setState(String state) {
		this.state = state;
	}
	public String getSex() {
		return sex;
	}
	public void setSex(String sex) {
		this.sex = sex;
	}
	public String getCustomerEmail() {
		return customerEmail;
	}
	public void setCustomerEmail(String customerEmail) {
		this.customerEmail = customerEmail;
	}
	public Date getCreateDate() {
		return createDate;
	}
	public void setCreateDate(Date createDate) {
		this.createDate = createDate;
	}
	public String getUserLevel() {
		return userLevel;
	}
	public void setUserLevel(String userLevel) {
		this.userLevel = userLevel;
	}
	public String getDescribe() {
		return describe;
	}
	public void setDescribe(String describe) {
		this.describe = describe;
	}
	public String getCity() {
		return city;
	}
	public void setCity(String city) {
		this.city = city;
	}
	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	public String getUser_name() {
		return user_name;
	}
	public void setUser_name(String user_name) {
		this.user_name = user_name;
	}
	public String getChinese_name() {
		return chinese_name;
	}
	public void setChinese_name(String chinese_name) {
		this.chinese_name = chinese_name;
	}
	public String getMobile_phone() {
		return mobile_phone;
	}
	public void setMobile_phone(String mobile_phone) {
		this.mobile_phone = mobile_phone;
	}
	public String getLongitude() {
		return longitude;
	}
	public void setLongitude(String longitude) {
		this.longitude = longitude;
	}
	public String getLatitude() {
		return latitude;
	}
	public void setLatitude(String latitude) {
		this.latitude = latitude;
	}

	public String getUnitType() {
		return unitType;
	}

	public void setUnitType(String unitType) {
		this.unitType = unitType;
	}

	public String getPortrait() {
		return portrait;
	}

	public void setPortrait(String portrait) {
		this.portrait = portrait;
	}

	public String getPortraitId() {
		return portraitId;
	}

	public void setPortraitId(String portraitId) {
		this.portraitId = portraitId;
	}


//	public String getLegalRepresentative() {
//		return legalRepresentative;
//	}
//
//	public void setLegalRepresentative(String legalRepresentative) {
//		this.legalRepresentative = legalRepresentative;
//	}

	public String getRegisteredCapital() {
		return registeredCapital;
	}

	public void setRegisteredCapital(String registeredCapital) {
		this.registeredCapital = registeredCapital;
	}

	public String getOrganizationType() {
		return organizationType;
	}

	public void setOrganizationType(String organizationType) {
		this.organizationType = organizationType;
	}

	public String getFormed() {
		return formed;
	}

	public void setFormed(String formed) {
		this.formed = formed;
	}

	public String getExpirationTime() {
		return expirationTime;
	}

	public void setExpirationTime(String expirationTime) {
		this.expirationTime = expirationTime;
	}

	public String getIdentValidDate() {
		return identValidDate;
	}

	public void setIdentValidDate(String identValidDate) {
		this.identValidDate = identValidDate;
	}

	@Override
	public String toString() {
		return "GroupContactInfo{" +
				"groupUUID='" + groupUUID + '\'' +
				", groupCode='" + groupCode + '\'' +
				", identType='" + identType + '\'' +
				", registeredCapital='" + registeredCapital + '\'' +
				", organizationType='" + organizationType + '\'' +
				", identAddress='" + identAddress + '\'' +
				", businessArea='" + businessArea + '\'' +
				", groupName='" + groupName + '\'' +
				", identNumber='" + identNumber + '\'' +
				", identValidDate='" + identValidDate + '\'' +
				", formed='" + formed + '\'' +
				", expirationTime='" + expirationTime + '\'' +
				", customerAddress='" + customerAddress + '\'' +
				", groupAttId='" + groupAttId + '\'' +
				", contactsName='" + contactsName + '\'' +
				", contactsAddr='" + contactsAddr + '\'' +
				", visaOffice='" + visaOffice + '\'' +
				", contactFixedTelephone='" + contactFixedTelephone + '\'' +
				", contactMobilePhone='" + contactMobilePhone + '\'' +
				", resp_nation='" + resp_nation + '\'' +
				", resp_nation_id='" + resp_nation_id + '\'' +
				", sex='" + sex + '\'' +
				", state='" + state + '\'' +
				", customerEmail='" + customerEmail + '\'' +
				", createDate=" + createDate +
				", userLevel='" + userLevel + '\'' +
				", describe='" + describe + '\'' +
				", city='" + city + '\'' +
				", user_name='" + user_name + '\'' +
				", chinese_name='" + chinese_name + '\'' +
				", mobile_phone='" + mobile_phone + '\'' +
				", unitType='" + unitType + '\'' +
				", longitude='" + longitude + '\'' +
				", latitude='" + latitude + '\'' +
				", portrait='" + portrait + '\'' +
				", portraitId='" + portraitId + '\'' +
				'}';
	}
}
