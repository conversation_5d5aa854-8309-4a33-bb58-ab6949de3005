package com.xinxinsoft.entity.terminalActivityAdministration;

import com.google.gson.annotations.Expose;

import java.io.Serializable;

public class TerminalOrderManufactor implements Serializable {
    @Expose
    private String id;//(编号)
    @Expose
    private String terminalBrand;//(编号)
    @Expose
    private String terminalCode;//(编号)
    @Expose
    private String terminalModel;//(编号)
    @Expose
    private String terminalModelCode;//(编号)
    @Expose
    private String terminalSerialNumber;//(编号)
    @Expose
    private String distributionMethod;//(编号)
    @Expose
    private String logisticsNo;//(编号)



    @Expose
    private String state;//(编号)
    @Expose
    private String entryTime;//(编号)
    @Expose
    private String enteredBy;//(编号)
    @Expose
    private String updateTime;//(编号)
    @Expose
    private String updatedBy;//(编号)
    @Expose
    private String orderNo;//(编号)

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getTerminalBrand() {
        return terminalBrand;
    }

    public void setTerminalBrand(String terminalBrand) {
        this.terminalBrand = terminalBrand;
    }

    public String getTerminalCode() {
        return terminalCode;
    }

    public void setTerminalCode(String terminalCode) {
        this.terminalCode = terminalCode;
    }

    public String getTerminalModel() {
        return terminalModel;
    }

    public void setTerminalModel(String terminalModel) {
        this.terminalModel = terminalModel;
    }

    public String getTerminalModelCode() {
        return terminalModelCode;
    }

    public void setTerminalModelCode(String terminalModelCode) {
        this.terminalModelCode = terminalModelCode;
    }

    public String getTerminalSerialNumber() {
        return terminalSerialNumber;
    }

    public void setTerminalSerialNumber(String terminalSerialNumber) {
        this.terminalSerialNumber = terminalSerialNumber;
    }

    public String getDistributionMethod() {
        return distributionMethod;
    }

    public void setDistributionMethod(String distributionMethod) {
        this.distributionMethod = distributionMethod;
    }

    public String getLogisticsNo() {
        return logisticsNo;
    }

    public void setLogisticsNo(String logisticsNo) {
        this.logisticsNo = logisticsNo;
    }

    public String getState() {
        return state;
    }

    public void setState(String state) {
        this.state = state;
    }

    public String getEntryTime() {
        return entryTime;
    }

    public void setEntryTime(String entryTime) {
        this.entryTime = entryTime;
    }

    public String getEnteredBy() {
        return enteredBy;
    }

    public void setEnteredBy(String enteredBy) {
        this.enteredBy = enteredBy;
    }

    public String getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(String updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdatedBy() {
        return updatedBy;
    }

    public void setUpdatedBy(String updatedBy) {
        this.updatedBy = updatedBy;
    }

    public String getOrderNo() {
        return orderNo;
    }

    public void setOrderNo(String orderNo) {
        this.orderNo = orderNo;
    }
}
