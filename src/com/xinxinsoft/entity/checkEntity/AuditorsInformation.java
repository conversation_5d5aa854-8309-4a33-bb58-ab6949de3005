package com.xinxinsoft.entity.checkEntity;

import java.io.Serializable;

import com.google.gson.annotations.Expose;

public class AuditorsInformation implements Serializable{

	@Expose
	private String uuid;
	
	@Expose
	private String city;//地市
	
	@Expose
	private String county;//区县
	
	@Expose
	private String department;//部门
	
	@Expose
	private String name;//姓名
	
	@Expose
	private String postname;//岗位名称
	
	@Expose
	private String phonenumber;//电话号码
	
	@Expose
	private String EIPaccount;//EIP账户
	
	@Expose
	private String account4A;//4A账户
	
	@Expose
	private String BOOSJobnumber;//boos工号
	
	@Expose
	private String remarks;//备注

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getCity() {
		return city;
	}

	public void setCity(String city) {
		this.city = city;
	}

	public String getCounty() {
		return county;
	}

	public void setCounty(String county) {
		this.county = county;
	}

	public String getDepartment() {
		return department;
	}

	public void setDepartment(String department) {
		this.department = department;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getPostname() {
		return postname;
	}

	public void setPostname(String postname) {
		this.postname = postname;
	}

	public String getPhonenumber() {
		return phonenumber;
	}

	public void setPhonenumber(String phonenumber) {
		this.phonenumber = phonenumber;
	}

	public String getEIPaccount() {
		return EIPaccount;
	}

	public void setEIPaccount(String eIPaccount) {
		EIPaccount = eIPaccount;
	}

	public String getAccount4A() {
		return account4A;
	}

	public void setAccount4A(String account4a) {
		account4A = account4a;
	}

	public String getBOOSJobnumber() {
		return BOOSJobnumber;
	}

	public void setBOOSJobnumber(String bOOSJobnumber) {
		BOOSJobnumber = bOOSJobnumber;
	}

	public String getRemarks() {
		return remarks;
	}

	public void setRemarks(String remarks) {
		this.remarks = remarks;
	}
	
}
