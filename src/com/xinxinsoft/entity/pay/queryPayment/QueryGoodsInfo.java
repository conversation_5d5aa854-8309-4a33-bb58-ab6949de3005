package com.xinxinsoft.entity.pay.queryPayment;

import java.io.Serializable;

/***
 * 查询 商品实体类
 */
public class QueryGoodsInfo implements Serializable {

    private String uuid;
    private String orderid;
    private String   id;//	商品编号	Y	String	36	商品编号
    private String  name;//		商品名称	Y	string	36	商品名称
    private String  quantity;//		商品数量	N	string	36	商品数量
    private String  rule;//		定价规则编号	N	string	36	定价规则编号
    private String price;//		价格	N	string	36	价格
    private String  copyright;//		版权号	N	string	36	版权号

    public String getUuid() {
        return uuid;
    }

    public void setUuid(String uuid) {
        this.uuid = uuid;
    }

    public String getOrderid() {
        return orderid;
    }

    public void setOrderid(String orderid) {
        this.orderid = orderid;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getQuantity() {
        return quantity;
    }

    public void setQuantity(String quantity) {
        this.quantity = quantity;
    }

    public String getRule() {
        return rule;
    }

    public void setRule(String rule) {
        this.rule = rule;
    }

    public String getPrice() {
        return price;
    }

    public void setPrice(String price) {
        this.price = price;
    }

    public String getCopyright() {
        return copyright;
    }

    public void setCopyright(String copyright) {
        this.copyright = copyright;
    }
}
