package com.xinxinsoft.entity.receiveApply;

import com.google.gson.annotations.Expose;

/**
 * 流程流转
 * 
 * <AUTHOR>
 * 
 */
public class ReceiveApplyTask {
	@Expose
	private String	uuid;					// id
	@Expose
	private String	process;				// 流程ID
	@Expose
	private String	creator;				// 发起人
	@Expose
	private String	creatorNo;				// 发起人工号
	@Expose
	private String	creatDate;				// 发起时间
	@Expose
	private String	planDate;				// 预计处理时间
	@Expose
	private String	oper;					// 操作人
	@Expose
	private String	operNo;				// 操作人工号
	@Expose
	private String	operDate;				// 操作时间
	@Expose
	private String	spendTime;				// 耗时
	@Expose
	private String	replyContent;			// 回复内容
	@Expose
	private String	status;				// 状态 0退回 1处理中 2处理完成
	@Expose
	private String	type;					// 标识是抄送还是审核；抄送：CS；审核：SH
	@Expose
	private String	expectedCompletionTime; // 预计完成时间
	@Expose
	private String	role; // 预计完成时间

	public String getUuid() {
		return uuid;
	}

	public void setUuid(String uuid) {
		this.uuid = uuid;
	}

	public String getProcess() {
		return process;
	}

	public void setProcess(String process) {
		this.process = process;
	}

	public String getCreator() {
		return creator;
	}

	public void setCreator(String creator) {
		this.creator = creator;
	}

	public String getCreatorNo() {
		return creatorNo;
	}

	public void setCreatorNo(String creatorNo) {
		this.creatorNo = creatorNo;
	}

	public String getCreatDate() {
		return creatDate;
	}

	public void setCreatDate(String creatDate) {
		this.creatDate = creatDate;
	}

	public String getPlanDate() {
		return planDate;
	}

	public void setPlanDate(String planDate) {
		this.planDate = planDate;
	}

	public String getOper() {
		return oper;
	}

	public void setOper(String oper) {
		this.oper = oper;
	}

	public String getOperNo() {
		return operNo;
	}

	public void setOperNo(String operNo) {
		this.operNo = operNo;
	}

	public String getOperDate() {
		return operDate;
	}

	public void setOperDate(String operDate) {
		this.operDate = operDate;
	}

	public String getSpendTime() {
		return spendTime;
	}

	public void setSpendTime(String spendTime) {
		this.spendTime = spendTime;
	}

	public String getReplyContent() {
		return replyContent;
	}

	public void setReplyContent(String replyContent) {
		this.replyContent = replyContent;
	}

	public String getStatus() {
		return status;
	}

	public void setStatus(String status) {
		this.status = status;
	}

	public String getType() {
		return type;
	}

	public void setType(String type) {
		this.type = type;
	}

	public String getExpectedCompletionTime() {
		return expectedCompletionTime;
	}

	public void setExpectedCompletionTime(String expectedCompletionTime) {
		this.expectedCompletionTime = expectedCompletionTime;
	}

	public String getRole() {
		return role;
	}

	public void setRole(String role) {
		this.role = role;
	}

}
