package com.xinxinsoft.task;



import com.xinxinsoft.entity.riskClosedLoop.RiskData;
import com.xinxinsoft.entity.riskClosedLoop.RiskDataNew;
import com.xinxinsoft.entity.riskClosedLoop.RiskRecord;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService;

import com.xinxinsoft.utils.DateUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

public class PreinvApplyRiskDataTask {

    private static final Logger logger = LoggerFactory.getLogger(PreinvApplyRiskDataTask.class);

    private static String AUDITWORKFILE_URL = Config.getString("AUDITWORKFILE_URL");

    //    @Resource(name = "RiskClosedLoopService")
    private RiskClosedLoopService riskClosedLoopService;

    public RiskClosedLoopService getRiskClosedLoopService() {
        return riskClosedLoopService;
    }

    public void setRiskClosedLoopService(RiskClosedLoopService riskClosedLoopService) {
        this.riskClosedLoopService = riskClosedLoopService;
    }

    public String getUnlockedNumber() {
        return  DateUtil.convertDateToString(new Date(),"yyyyMMddHHmmssSSS");
    }


    /**
     * 读取预开票风险数据文件
     *
     * @param fileUrl
     * @Author: Leo
     * @Date: 2024/8/9 15:49
     * @return：
     */
    private List<RiskData> readTxt(String fileUrl) {
        List<RiskData> list = new ArrayList<>();
        logger.info("----》读取文件：" + fileUrl);
        File file = new File(fileUrl);
        if (!file.exists()) {
            logger.info("----》文件不存在：" + fileUrl);
            return null;
        }
        if (file.exists()) {
            try (BufferedReader br = new BufferedReader(new FileReader(file))) {
                String line;
                Date date = new Date();
                while ((line = br.readLine()) != null) {
                    String[] item = line.split("\\|");
                    RiskData riskData = new RiskData();
                    riskData.setRiskCode(item[0]);
                    riskData.setCompanyCode(item[1]);
                    riskData.setCountyNo(item[2]);
                    riskData.setGroupCode(item[3]);
                    riskData.setGroupName(item[4]);
                    riskData.setBossRowNo(item[5]);
                    riskData.setStaffName(item[6]);
                    riskData.setStaffPhone(item[7]);
                    riskData.setCheckpoint(item[8]);
                    riskData.setOrderId(item[9]);
                    riskData.setCreateDate(date);
                    riskData.setRiskId("FX" + getUnlockedNumber());
                    if (item[8].equals("E99.998")) { //锁定集团
                        riskData.setBossRowLock("0");
                        riskData.setBossLockDate(date);
                    }
                    if (item[8].equals("E99.999")) {
                        riskData.setGroupLock("0");
                        riskData.setGroupLockDate(date);
                    }
                    riskData.setDataStatus("0");// 数据状态（有效==>0/无效==>1）2.审批 3.退回 -2.作废数据 ==
                    riskData.setDataSources("2");// 数据来源（系统==>0/导入==>1/稽核==>2）
                    list.add(riskData);
                    Thread.sleep(10);
                }
                br.close();
                logger.info("---->读取完文件完成，数据量【" + list.size() + "】" + fileUrl);
            } catch (IOException e) {
                e.printStackTrace();
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
        }
        return list;
    }

    /**
     * 处理list中某些字段的重复数据
     * @param list
     * @Author: Leo
     * @Date: 2024/8/9 15:17
     * @return：
     */
    private List<RiskData> doRepeatValue(List<RiskData> list){
        //处理list
        List<RiskData> unlockedList=new ArrayList<>();
        for (int i = 0; i < list.size(); i++) {
            String riskNotes="";
            for (int j = i+1; j < list.size(); j++) {
                //检测是否有重复
                if(list.get(i).getGroupCode().equals(list.get(j).getGroupCode()) && list.get(i).getCheckpoint().equals(list.get(j).getCheckpoint()) && list.get(i).getBossRowNo().equals(list.get(j).getBossRowNo())){
                    riskNotes=list.get(j).getOrderId();
                    unlockedList.add(list.get(j));
                }
            }
            //更新list对象的备注信息
            list.get(i).setRiskNotes(riskNotes);
        }
        //删除重复的list中的项
        for (int i = 0; i < unlockedList.size(); i++) {
            list.remove(unlockedList.get(i));
        }
        return list;
    }

    /**
     * 处理读取到风险管控的数据信息入库
     *
     * @param list
     * @Author: Leo
     * @Date: 2024/8/9 15:40
     * @return：
     */
    private void handleRiskData(List<RiskData> list){
        // 更新风险控制限制的明细表数据
        if (list.size()>0){
            for (RiskData riskData : list ) {
                List<RiskDataNew> riskDataNews = riskClosedLoopService.getRiskDataNewByCode(riskData.getRiskCode(), riskData.getOrderId());
                if (riskDataNews.size() > 0) {
                    for (RiskDataNew riskDataNew : riskDataNews) {
                        if (riskDataNew.equals(riskData.getCheckpoint())) {
                            riskDataNew.setRiskCode(riskData.getRiskCode());
                            riskDataNew.setGroupCode(riskData.getGroupCode());
                            riskDataNew.setBossRowNo(riskData.getBossRowNo());
                            riskDataNew.setCheckpoint(riskData.getCheckpoint());
                            riskDataNew.setOrderId(riskData.getOrderId());
                            riskDataNew.setCreateDate(riskData.getCreateDate());
                            riskClosedLoopService.updateRiskDataNew(riskDataNew);
                        }
                    }
                } else {
                    RiskDataNew riskDataNew = new RiskDataNew();
                    riskDataNew.setRiskCode(riskData.getRiskCode());
                    riskDataNew.setGroupCode(riskData.getGroupCode());
                    riskDataNew.setBossRowNo(riskData.getBossRowNo());
                    riskDataNew.setCheckpoint(riskData.getCheckpoint());
                    riskDataNew.setOrderId(riskData.getOrderId());
                    riskDataNew.setCreateDate(riskData.getCreateDate());
                    riskClosedLoopService.addRiskDataNew(riskDataNew);
                }
            }
        }
        //更新风险控制工单信息表限制
        List<RiskData> clearList= doRepeatValue(list);
        logger.info("---->去掉重发数据后的风控锁定数据:"+clearList.size());
        for(RiskData riskData:clearList){
            List<RiskData>  riskByInformation=riskClosedLoopService.findRiskByInformations(riskData.getGroupCode(),null, riskData.getCheckpoint());
            if(riskByInformation.size()>0) {
                for (int i = 0; i < riskByInformation.size(); i++) {
                    if(i==0){
                        RiskData riskData1 = riskByInformation.get(0);
                        riskData1.setCompanyCode(riskData.getCompanyCode());
                        riskData1.setCountyNo(riskData.getCountyNo());
                        riskData1.setGroupCode(riskData.getGroupCode());
                        riskData1.setGroupName(riskData.getGroupName());
                        riskData1.setBossRowNo(riskData.getBossRowNo());
                        riskData1.setStaffName(riskData.getStaffName());
                        riskData1.setStaffPhone(riskData.getStaffPhone());
                        riskData1.setCheckpoint(riskData.getCheckpoint());
                        riskData1.setOrderId(riskData.getOrderId());
                        riskData1.setCreateDate(riskData.getCreateDate());
                        riskData1.setGroupLockDate(riskData.getGroupLockDate());
                        riskData1.setBossLockDate(riskData.getBossLockDate());
                        riskData1.setRiskUnlockTime(riskData.getRiskUnlockTime());
//                String riskNotess = riskData1.getRiskNotes()+riskData.getRiskNotes();
//                if(riskNotess.length() > 800){
//                    riskNotess= riskNotess.substring(0,800);
//                }
                        riskData1.setRiskNotes(riskData1.getRiskNotes());
                        riskClosedLoopService.upDateRiskData(riskData1);
                    }else {
                        RiskData riskData2 = riskByInformation.get(i);
                        riskData2.setRiskCondition("重复数据系统解锁");
                        riskData2.setDataStatus("1");//DATASTATUS
                        //riskData2.setRiskUnlockTime("");//清空限制时间
                        riskClosedLoopService.upDateRiskData(riskData2);
                    }
                }

            }else{
                riskClosedLoopService.addRiskData(riskData);
            }
        }

    }

    /**
     * 导入预开票文件 每天导入前先删除数据库原有数据
     * 稽核平台每天2点传递文件到ftp分为4个文件，分别未普票和专票的未结清的998和999的限制
     * @Author: Leo
     * @Date: 2024/8/9 15:50
     * @return：
     */
    public void importPreinvApplyRiskData(){
        logger.info("---->开启执行导入预开票风险管控文件"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
        //对未结清的---->998锁工号和999锁集团
        String WJQPT_998="fapiao_wjqpt998_";
        String WJQZP_998="fapiao_wjqzp998_";
        String WJQPT_999="fapiao_wjqpt999_";
        String WJQZP_999="fapiao_wjqzp999_";
        String dataStr=DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYYMMDD);
        List<RiskData> allList=new ArrayList<>();
        List<RiskData> WJQPT_998s=readTxt(AUDITWORKFILE_URL + WJQPT_998+ dataStr+".txt");
        List<RiskData> WJQZP_998s=readTxt(AUDITWORKFILE_URL + WJQZP_998+ dataStr+".txt");
        List<RiskData> WJQPT_999s=readTxt(AUDITWORKFILE_URL + WJQPT_999+ dataStr+".txt");
        List<RiskData> WJQZP_999s=readTxt(AUDITWORKFILE_URL + WJQZP_999+ dataStr+".txt");
        allList.addAll(WJQPT_998s);
        allList.addAll(WJQZP_998s);
        allList.addAll(WJQPT_999s);
        allList.addAll(WJQZP_999s);
        logger.info("----->读取全量文件数量："+allList.size());
        handleRiskData(allList);
        logger.info("---->完成执行导入预开票风险管控文件"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));

    }

    //解除预开票解锁集团风险管理数据
    public void renewfindRiskStateBygroupCode() {
        try {
            logger.info("---->开启执行解除预开票解锁集团风险管理数据"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
            String dataStr=DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYYMMDD);
            String fileUrl = AUDITWORKFILE_URL+"fapiao_yjqunit999_" + dataStr + ".txt";
            File fileObj = new File(fileUrl);
            logger.info("---->文件地址为" + fileUrl);
            if (!fileObj.exists()) {
                logger.info("--->renewfindRiskStateBygroupCode方法，获取文件不存在！");
                return;
            }
            FileInputStream fis = new FileInputStream(fileObj);
            BufferedReader br = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
            String line;
            Date date=new Date();
            while ((line = br.readLine()) != null) {
                String[] item = line.split("\\|");
                List<RiskData> risk = riskClosedLoopService.findRiskByInformations(item[0],null,"E99.999");
                if (risk.size()>0) {
                    for (int i = 0; i < risk.size(); i++) {
                        RiskData riskData = risk.get(i);
                        riskData.setRiskCondition("导入稽核文件解锁");
                        riskData.setDataStatus("1");//设置状态为无效状态
                        riskData.setRiskUnlockTime("");//清空限制时间
                        riskClosedLoopService.upDateRiskData(riskData);
                    }
                }
                riskClosedLoopService.delRiskDataNewByWhere(item[0],null,"E99.999");
                //解除记录信息添加
                RiskRecord record = new RiskRecord();
                record.setUnitId(item[0]);
                record.setTime(date);
                riskClosedLoopService.addRiskRecord(record);
            }
            logger.info("---->完成执行解除预开票解锁集团风险管理数据"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
            fis.close();
            br.close();
        } catch (Exception e) {
            logger.error("更新预开票风险管控文件方法失败" + e.getMessage(), e);
        }
    }
    /**
     * 解除预开票解锁工号数据
     *
     * @Author: Leo
     * @Date: 2024/8/9 16:34
     * @return：
     */
    public void renewfindRiskStateByBossRow() {
        try {
            logger.info("---->开启执行解除预开票解锁工号数据文件"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
            String dataStr=DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYYMMDD);
            String fileUrl = AUDITWORKFILE_URL+"fapiao_yjqlogin998_" + dataStr + ".txt";
            File fileObj = new File(fileUrl);
            logger.info("---->文件地址为" + fileUrl);
            if (!fileObj.exists()) {
                logger.info("--->renewfindRiskStateBygroupCode方法，获取文件不存在！");
                return;
            }
            FileInputStream fis = new FileInputStream(fileObj);
            BufferedReader br = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
            String line;
            Date date=new Date();
            while ((line = br.readLine()) != null) {
                String[] item = line.split("\\|");
                List<RiskData> risk = riskClosedLoopService.findRiskByInformations(item[2],item[0],"E99.998");
                if (risk.size()>0) {
                    for (int i = 0; i < risk.size(); i++) {
                        RiskData riskData = risk.get(i);
                        riskData.setRiskCondition("导入稽核文件解锁");
                        riskData.setDataStatus("1");//DATASTATUS
                        riskData.setRiskUnlockTime("");//清空限制时间
                        riskClosedLoopService.upDateRiskData(riskData);
                    }
                }
                riskClosedLoopService.delRiskDataNewByWhere(null,item[0],"E99.998");
                //解除记录信息添加
                RiskRecord record = new RiskRecord();
                record.setRecord(item[0]);
                record.setUnitId(item[2]);
                record.setTime(date);
                riskClosedLoopService.addRiskRecord(record);
            }
            logger.info("---->完成执行解除预开票解锁工号数据文件"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
            fis.close();
            br.close();
        } catch (Exception e) {
            logger.error("renewfindRiskStateByBossRow数据执行异常" + e.getMessage(), e);
        }
    }

    /**
     * 临时解除限制到期数据恢复job
     *
     * @Author: Leo
     * @Date: 2024/8/9 16:39
     * @return：
     */
    public void upDataRiskStateByTxt() {
        try {
            logger.info("---->预开票解锁工号风险管理加锁数据方法开始执行"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
            List<RiskData> riskData = riskClosedLoopService.upDataRiskStateByTxt();
            for (int i = 0; i < riskData.size(); i++) {
                RiskData riskByCheckpoint = riskData.get(i);
                riskClosedLoopService.upDataRisk(riskByCheckpoint.getId(), "-2");//作废
                //复制工单
                RiskData riskDataNew = new RiskData();
                //riskDataNew.setRiskId("FX" + getUnlockedNumber());
                riskDataNew.setRiskCode(riskByCheckpoint.getRiskCode());
                riskDataNew.setCompanyCode(riskByCheckpoint.getCompanyCode());
                riskDataNew.setCompany(riskByCheckpoint.getCompany());
                riskDataNew.setCountyNo(riskByCheckpoint.getCountyNo());
                riskDataNew.setCounty(riskByCheckpoint.getCounty());
                riskDataNew.setGroupCode(riskByCheckpoint.getGroupCode());
                riskDataNew.setGroupName(riskByCheckpoint.getGroupName());
                riskDataNew.setBossRowNo(riskByCheckpoint.getBossRowNo());
                riskDataNew.setStaffName(riskByCheckpoint.getStaffName());
                riskDataNew.setStaffPhone(riskByCheckpoint.getStaffPhone());
                riskDataNew.setCheckpoint(riskByCheckpoint.getCheckpoint());
                riskDataNew.setDataSource(riskByCheckpoint.getDataSource());
                riskDataNew.setCreateDate(new Date());
                riskDataNew.setRectificationDate(riskByCheckpoint.getRectificationDate());
                riskDataNew.setGroupLock(riskByCheckpoint.getGroupLock());
                riskDataNew.setGroupLockDate(riskByCheckpoint.getGroupLockDate());
                riskDataNew.setBossRowLock(riskByCheckpoint.getBossRowLock());
                riskDataNew.setBossLockDate(riskByCheckpoint.getBossLockDate());
                riskDataNew.setOrderId(riskByCheckpoint.getOrderId());
                riskDataNew.setDataStatus("0");
                riskDataNew.setDataSources(riskByCheckpoint.getDataSources());
                riskDataNew.setRiskCondition("");
                riskDataNew.setRiskReason("");
                riskDataNew.setRiskUnlockTime("");
                riskClosedLoopService.addRiskData(riskDataNew);
                Thread.sleep(100);
            }
            logger.info("---->完成预开票解锁工号风险管理加锁数据方法开始执行"+DateUtil.convertDateToString(new Date(),DateUtil.DATE_FORMAT_YYYY_MM_DD_HH_MM_SS));
        } catch (Exception e) {
            logger.error("upDataRiskStateByTxt方法失败" + e.getMessage(), e);
        }
    }

}
