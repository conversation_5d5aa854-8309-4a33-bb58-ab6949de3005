package com.xinxinsoft.task;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.xinxinsoft.entity.executeJobLog.JobLog;
import com.xinxinsoft.service.appOpenService.OMSService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import com.xinxinsoft.service.ums.UnitInfoService;
import com.xinxinsoft.utils.SftpUtils;
import com.xinxinsoft.utils.common.FileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

/**
 * 指南针建档集团数据
 * SftpTask
 * gcy
 * 2020年9月23日 10:51:00
 * @version v1.0
 */
public class CompassFilingGroupTask {
    private static final String host = "*************";//ip
    private static final int port = 22;//22 port
    private static final String username = "qt_data";//user
    //private static final String password = "k%p$4J3@";//password
    private static final String password = "AsfV_29M";//替换新密码
    private UnitInfoService unitInfoService;
    private JobLogServicer jobLogServicer;

    public UnitInfoService getUnitInfoService() {
        return unitInfoService;
    }

    public void setUnitInfoService(UnitInfoService unitInfoService) {
        this.unitInfoService = unitInfoService;
    }

    public JobLogServicer getJobLogServicer() {
        return jobLogServicer;
    }

    public void setJobLogServicer(JobLogServicer jobLogServicer) {
        this.jobLogServicer = jobLogServicer;
    }

    private static final Logger logger = LoggerFactory.getLogger(CompassFilingGroupTask.class);


    /**
     * 指南针建档集团数据
     */
    public void CompassFilingGroup(){

        logger.info("指南针建档集团推送SFTP开始");
        Date start_time=new Date();
        /*连接SFTP服务器*/
        Session session = SftpUtils.getSession(host, port, username, password);
        ChannelSftp channel = SftpUtils.getConnect(session);
        JobLog jobLog = new JobLog();
        try{
            start_time = new Date();
            jobLog.setStartTime(new Date());
            jobLog.setJobName("Class:SftpTask Method:CompassFilingGroup 指南针建档");

            /**
             * 改sql
             * 指南针建档集团数据
             */
            List<Map<String,String>> obj = unitInfoService.groupContactInfo_list();

            /*SFTP服务器固定文件夹*/
            String filDirectory ="/data/work1/CRM/";
            /*需要创建的新目录*/
            String createDirectory =SftpUtils.dateOfThePreviousDayTwo()+"/temp";
            /*内容文件名*/
            //换成d131232
            String contentFileName="d131232"+SftpUtils.dateOfThePreviousDay()+".d.avl";
            /*校验文件名*/
            //换成d131232
            String checkFileName = "d131232"+SftpUtils.dateOfThePreviousDay()+".d.chk";
            /*创建本地文件夹*/
            //换文件夹名字
//            String ftpUrl = FileUpload.getFtpURL()+"CompassFiling/"+FileUpload.getDateToString("yyyyMMdd")+"/";
            String ftpUrl = "eomapp_new0_LC/UploadFile/CompassFiling/"+FileUpload.getDateToString("yyyyMMdd")+"/";
            /*获取文件夹路径*/
            File headPath = new File(ftpUrl);
            /*判断文件夹是否创建，没有创建则创建新文件夹*/
            if(!headPath.exists()){
                headPath.mkdirs();
            }
            /*创建数据文件*/
            SftpUtils.creatTxtFile(contentFileName,ftpUrl);
            if(obj.size()>0){
                /*写入数据文件头部信息*/
                //writeTxtFile("订单号&BOSS流水&实例号&产品编码&地市&区县&集团280&产品名称&资费ID&定价名称&提交时间&创建人",contentFileName,ftpUrl);//头部三个集团编码、操作时间、操作工号
                SftpUtils.writeTxtFile("集团编码&操作时间&操作工号",contentFileName,ftpUrl);
                //String d ="ORDER_SN&BOSS_NO&PHONE_NO&PROD_ID&COMPANY_NAME&COUNTY_NAME&UNIT_ID&PROD_NAME&PROD_PRCID&PRC_NAME&CREATE_DATE&EMPLOYEE_NAME";
                /*根据查询出来的数据循环写入数据文件内容信息*/
                for(int i=0;i<obj.size();i++){
                    //写入三个
                    String group_code =obj.get(i).get("GROUP_CODE");
                    String user_name =obj.get(i).get("USER_NAME");
                    String create_date =obj.get(i).get("CREATE_DATE");
                    SftpUtils.writeTxtFile(group_code+"&"+user_name+"&"+create_date,contentFileName,ftpUrl);
                }
            }
            /*for(int i1=0;i1<100;i1++){
                writeTxtFile("43214<==>4321432<==>4354561<==>56456465<==>43214321<==>43214321<==>43214321<==>吃的撒吃的撒<==>43214321<==>吃的撒吃的撒<==>4321432<==>吃的撒吃的撒",contentFileName,ftpUrl);
            }*/
            /*查询本地生成的数据文件的大小*/
            File file =new File(ftpUrl+contentFileName);
            String length = SftpUtils.getFileLength(file);
            /*创建校验文件*/
            SftpUtils.creatTxtFile(checkFileName,ftpUrl);
            /*校验文件写入数据文件的名字*/
            SftpUtils.writeTxtFile(contentFileName,checkFileName,ftpUrl);
            /*校验文件写入数据文件的大小*/
            SftpUtils.writeTxtFile(length,checkFileName,ftpUrl);
            /*校验文件写入数据文件的数据周期（创建年月日）*/
            SftpUtils.writeTxtFile(SftpUtils.dateOfThePreviousDay(),checkFileName,ftpUrl);
            /*校验文件写入数据文件创建时间（年月日时分秒）*/
            SftpUtils.writeTxtFile(SftpUtils.getStringDatethree(),checkFileName,ftpUrl);
            /*把文件装入List方便循环推送*/
            List<File> files = new ArrayList<>();
            files.add(new File(ftpUrl+contentFileName));
            files.add(new File(ftpUrl+checkFileName));
            for(int j=0;j<files.size();j++){
                if(files.get(j).exists()){
                    /*内容文件流*/
                    InputStream inti=new FileInputStream(files.get(j).getPath());
                    /*在SFTP服务器创建目录（有则不创建）*/
                    //sf.mkdir(filDirectory, createDirectory, channel);
                    SftpUtils.mkdir(filDirectory, createDirectory, channel);
                    /*上传文件到SFTP服务器*/
                    //sf.upload(filDirectory+createDirectory,inti,files.get(j).getName(), channel);
                    SftpUtils.upload(filDirectory+createDirectory,inti,files.get(j).getName(), channel);
                }else{
                    logger.info("CompassFilingGroupTask定时器CompassFilingGroup方法未找到名为:"+files.get(j).getName()+"的附件,地址是:"+files.get(j).getPath(),"请核查");
                    throw new Exception("CompassFilingGroupTask定时器CompassFilingGroup方法未找到名为:"+files.get(j).getName()+"的附件,地址是:"+files.get(j).getPath()+"请核查");
                }
            }
            /*下载SFTP附件*/
            /*sf.download("/data/temp/test/", "download.txt", "D:/temp/haha/","download.txt", channel);*/
            /*删除SFTP附件*/
            /*sf.delete("/data/temp/test/", "delete.txt", channel);*/
            jobLog.setIsError(false);
            jobLog.setEndTime(new Date());
            jobLogServicer.save(jobLog);
            Date end_time = new Date();
            logger.info("指南针建档集团数据SFTP正常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
        }catch(Exception e){
            e.printStackTrace();
            jobLog.setIsError(true);
            jobLog.setEndTime(new Date());
            jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
            jobLogServicer.save(jobLog);
            Date end_time = new Date();
            logger.info("指南针建档集团数据SFTP异常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
        }finally{
            SftpUtils.disconnect(channel,session);
        }
    }

}
