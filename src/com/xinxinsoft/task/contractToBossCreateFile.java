package com.xinxinsoft.task;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.apache.commons.net.ftp.FTPClient;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;

import com.xinxinsoft.entity.contract.ContractFTPCSVRecord;
import com.xinxinsoft.service.contractToOrderCSVService.ContractToOrderCSVService;
import com.xinxinsoft.service.contractToOrderCSVService.contractToBossFileDataService;
import com.xinxinsoft.utils.FtpUtil;
import com.xinxinsoft.utils.LwFTPUploadTxt;
import com.xinxinsoft.utils.common.FileUpload;
/**
 * 
 * contractToBossFileDataService数据生成的本地文件
 * 
 * <AUTHOR>
 *
 * 2018年11月16日 下午3:52:45
 */
public class contractToBossCreateFile {
	private contractToBossFileDataService contractToBossFileDataService;//数据
	
	public contractToBossFileDataService getContractToBossFileDataService() {
		return contractToBossFileDataService;
	}

	public void setContractToBossFileDataService(
			contractToBossFileDataService contractToBossFileDataService) {
		this.contractToBossFileDataService = contractToBossFileDataService;
	}

	/**
     * 写一行数据
     * @param row 数据列表
     * @param csvWriter
     * @throws IOException
     */
    private static void writeRow(List<Object> row, BufferedWriter csvWriter) throws IOException {
        for (Object data : row) {
            StringBuffer sb = new StringBuffer();
            String rowStr = sb.append("\"").append(data).append("\",").toString();
            //csvWriter.write(new String(new byte[] { (byte) 0xEF, (byte) 0xBB,(byte) 0xBF }));
            csvWriter.write(rowStr);
        }
        csvWriter.newLine();
    }
    
    /**
     * 创建文件
     */
	public void createFile() {
//	   List<Map<String, String>> list = contractToBossFileDataService.getData();
	   List<Map<String, String>> list = new ArrayList<Map<String,String>>();
	   Map<String, String> map = new HashMap<String, String>();
	   map.put("ORDERNUMBER","1");
	   map.put("DEMANDNAME","2");
	   map.put("BOSSFORMNO","3");
	   map.put("CONTRACTNUMBER","4");
	   map.put("PAYCYCLE","5");
	   map.put("STARTDATE","6");
	   map.put("ENDDATE","7");
	   list.add(map);
	   System.out.println("数据个数"+list.size());
	   String filePath = FileUpload.getContractCsv()+getStringDatetwo(new Date())+"/"; //文件路径
       Object[] head = { "订单编号", "订单名称", "boos工号", "合同编号","缴费周期","合同开始时间","合同结束时间"}; //表格头
       List<Object> headList = Arrays.asList(head);
       List<Object> m2 = null;
       long length1 =0;
       int count = 0;
       if(list.size()%1000000 >0) {
    	   count=list.size()/1000000 + 1;
       }else{
    	   count=list.size()/1000000;
       }
       String fileName ="";
       for(int i = 0;i<count;i++) {
    	   int start = i*1000000;
    	   int end = start + 1000000;
    	   if(end > list.size()) {
    		   end = list.size();
    	   }
    	   String namecount="";
    	   if((i+1)<10){
    		   namecount="0"+(i+1);
    	   }else{
    		   namecount=(i+1)+"";
    	   }
    	   fileName = "ZHZT_ORDER_CONTRACT_0000000000"+namecount+"_"+getStringDatetwo(new Date())+".CSV";//文件名称
    	   List<List<Object>> dataList = new ArrayList<List<Object>>();//数据
    	   for(int start1 = start;start1 < end;start1++){
    		   String ordernumber = list.get(start1).get("ORDERNUMBER");//订单编号
	           String demandName = list.get(start1).get("DEMANDNAME");//订单名称
	           String bossFormNo = list.get(start1).get("BOSSFORMNO");//boos工号
	           String CONTRACTNUMBER = list.get(start1).get("CONTRACTNUMBER");//合同编号
	           String payCycle = list.get(start1).get("PAYCYCLE");//缴费周期
	           String startDate = list.get(start1).get("STARTDATE");//合同开始时间
	           String endDate = list.get(start1).get("ENDDATE");//合同结束时间
	           if(ordernumber==null){
	        	   ordernumber="";
	           }
	           if(demandName==null){
	        	   demandName="";
	           }
	           if(CONTRACTNUMBER==null){
	        	   CONTRACTNUMBER="";
	           }
	           if(bossFormNo==null){
	        	   bossFormNo="";
	           }
	           if(payCycle==null){
	        	   payCycle="";
	           }
	           if(startDate==null){
	        	   startDate="";
	           }
	           if(endDate==null){
	        	   endDate="";
	           }
	           m2 = new ArrayList<Object>();
	           m2.add(ordernumber);
	           m2.add(demandName);
	           m2.add(CONTRACTNUMBER);
	           m2.add(payCycle);
	           m2.add(startDate);
	           m2.add(endDate);
	           m2.add(bossFormNo);
	           dataList.add(m2);
    	   }
    	   File csvFile = null;
           BufferedWriter csvWtriter = null;
           try {
               csvFile = new File(filePath + fileName);
               System.out.println("地址："+csvFile);
               File parent = csvFile.getParentFile();
               if (parent != null && !parent.exists()) {
                   parent.mkdirs();
               }
               csvFile.createNewFile();
               csvWtriter = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(csvFile), "UTF-8"), 1024);//GB2312使正确读取分隔符","
               writeRow(headList, csvWtriter);//写入文件头部
               for (List<Object> row : dataList) {//写入文件内容
                   writeRow(row, csvWtriter);
               }
               csvWtriter.flush();
               if (csvFile.exists() && csvFile.isFile()) {
                   String name = csvFile.getName();
                   length1 = csvFile.length();
                   System.out.println("文件"+name+"的大小是："+csvFile.length());
               }
               System.out.println("这是文件条数："+list.size());
           } catch (Exception e) {
               e.printStackTrace();
           }
       }
	}
	 /**
		 * 日期转换
		 * 
		 * @param currentTime
		 * @return
		 */
		public static String getStringDatetwo(Date currentTime) {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
		    Date date=new Date();  
		    Calendar calendar = Calendar.getInstance();  
		    calendar.setTime(date);  
		    calendar.add(Calendar.DAY_OF_MONTH, -1);  
		    date = calendar.getTime();
		    String dateString = formatter.format(date);
			return dateString;
		}
		/**
		 * test
		 * @param args
		 */
		public static void main(String[] args) {
			ApplicationContext context = new ClassPathXmlApplicationContext(new String[] {"applicationContext.xml"});
			contractToBossCreateFile service = context.getBean(contractToBossCreateFile.class);
			service.createFile();
		}
}
