package com.xinxinsoft.task;

import com.xinxinsoft.entity.executeJobLog.JobLog;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import com.xinxinsoft.service.smsPush.SmsPushService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.Date;

public class AttachmentSpaceReminderTask{
    private static final Logger logger = LoggerFactory.getLogger(AttachmentSpaceReminderTask.class);
    private AttachmentService attachmentService;
    private SmsPushService pushService;
    private JobLogServicer jobLogServicer;
    public AttachmentService getAttachmentService() {
        return attachmentService;
    }
    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }
    public SmsPushService getPushService() {
        return pushService;
    }
    public void setPushService(SmsPushService pushService) {
        this.pushService = pushService;
    }
    public JobLogServicer getJobLogServicer() {
        return jobLogServicer;
    }
    public void setJobLogServicer(JobLogServicer jobLogServicer) {
        this.jobLogServicer = jobLogServicer;
    }

    public void SMSReminder(){
        logger.info("一键下单数据推送SFTP开始");
        Date start_time=new Date();
        JobLog jobLog = new JobLog();
        try {
            jobLog.setStartTime(new Date());
            jobLog.setJobName("Class:AttachmentSpaceReminderTask Method:SMSReminder 文件夹大小监视Task");
            StorageCfg storageCfg = attachmentService.queryStorageCfg();
            long all = getSize(new File(storageCfg.getFileName()));
            String sq = getPrintSize(all);
            String[] str = sq.split("_");
            if ("GB".equals(str[1])) {
                Double du = Double.parseDouble(storageCfg.getAllSpace())-Double.parseDouble(str[0]);
                if (du<=2048) {
                    pushService.savePush_0_0001("张松", "订单系统附件空间不足2G剩余"+du/Double.parseDouble("1024")+"G请更换文件空间", "18708175855");
                    pushService.savePush_0_0001("张唯", "订单系统附件空间不足2G剩余"+du/Double.parseDouble("1024")+"G请更换文件空间", "15828232036");
                }else if(du<=51200){
                    pushService.savePush_0_0001("张松", "订单系统附件空间不足50G剩余"+du/Double.parseDouble("1024")+"G请申请新的空间", "18708175855");
                    pushService.savePush_0_0001("张唯", "订单系统附件空间不足50G剩余"+du/Double.parseDouble("1024")+"G请申请新的空间", "15828232036");
                }
            }
            if ("B".equals(str[1])) {
                storageCfg.setFreeSpace(String.valueOf(Double.parseDouble(str[0])/1024));
            }else if("KB".equals(str[1])){
                storageCfg.setFreeSpace(String.valueOf(Double.parseDouble(str[0])/1024));
            }else if("MB".equals(str[1])){
                storageCfg.setFreeSpace(str[0]);
            }else if("GB".equals(str[1])){
                storageCfg.setFreeSpace(String.valueOf(Double.parseDouble(str[0])*1024));
            }
            attachmentService.addStorageCfgEntity(storageCfg);
            jobLog.setIsError(false);
            jobLog.setEndTime(new Date());
            jobLogServicer.save(jobLog);
            Date end_time = new Date();
            logger.info("文件夹大小监视Task正常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
        }catch (Exception e){
            e.printStackTrace();
            jobLog.setIsError(true);
            jobLog.setEndTime(new Date());
            jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
            jobLogServicer.save(jobLog);
            Date end_time = new Date();
            logger.info("文件夹大小监视Task异常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
        }
    }

    public static void main(String[] args) {
        try {
            long all = getSize(new File("F:\\4K壁纸"));
            String sq = getPrintSize(all);
            String[] str = sq.split("_");
            if ("GB".equals(str[1])) {
                Double du = Double.parseDouble("2095104")-Double.parseDouble(str[0]);
                /*if(du<2048) {
                    System.out.println("张松你好订单系统附件空间不足2G请更换文件空间");
                    System.out.println("张唯你好订单系统附件空间不足2G请更换文件空间");
                }else if(du<51200){
                    System.out.println("张松你好订单系统附件空间不足50G请申请新的空间");
                    System.out.println("张唯你好订单系统附件空间不足50G请申请新的空间");
                }*/
                if(du<=2095099.19) {
                    System.out.println("张松你好订单系统附件空间不足2G剩余"+du/Double.parseDouble("1024")+"G请更换文件空间");
                    System.out.println("张唯你好订单系统附件空间不足2G剩余"+du/Double.parseDouble("1024")+"G请更换文件空间");
                }else if(du<=51200){
                    System.out.println("张松你好订单系统附件空间不足50G剩余"+du/Double.parseDouble("1024")+"G请申请新的空间");
                    System.out.println("张唯你好订单系统附件空间不足50G剩余"+du/Double.parseDouble("1024")+"G请申请新的空间");
                }
            }
            String size="";
            if ("B".equals(str[1])) {
                size= String.valueOf(Double.parseDouble(str[0])/1024);
            }else if("KB".equals(str[1])){
                size=String.valueOf(Double.parseDouble(str[0])/1024);
            }else if("MB".equals(str[1])){
                size=str[0];
            }else if("GB".equals(str[1])){
                size=String.valueOf(Double.parseDouble(str[0])*1024);
            }
            System.out.println(sq);
            System.out.println("已用空间大小："+str[0]);
            System.out.println("已用空间大小："+size+"MB");
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    //判断文件夹大小
    public static long getSize(File file){
        //判断文件是否存在
        if(file.exists()){
            if (!file.isFile()) {
                //获取文件大小
                File[] fl = file.listFiles();
                long ss = 0;
                for (File f : fl)
                    ss += getSize(f);
                return ss;
            } else {
                long ss = file.length();
                return ss;
            }
        }else{
            return 0;
        }
    }

    public static String getPrintSize(long size) {
        //如果字节数少于1024，则直接以B为单位，否则先除于1024，后3位因太少无意义
        if (size < 1024) {
            return String.valueOf(size) + "_B";
        } else {
            size = size / 1024;
        }
        //如果原字节数除于1024之后，少于1024，则可以直接以KB作为单位
        //因为还没有到达要使用另一个单位的时候
        //接下去以此类推
        if (size < 1024) {
            return String.valueOf(size) + "_KB";
        } else {
            size = size / 1024;
        }

        if (size < 1024) {
            //因为如果以MB为单位的话，要保留最后1位小数，
            //因此，把此数乘以100之后再取余
            size = size * 100;
            return String.valueOf((size / 100)) + "."
                    + String.valueOf((size % 100)) + "_MB";
        } else {
            //否则如果要以GB为单位的，先除于1024再作同样的处理
            size = size * 100 / 1024;
            return String.valueOf((size / 100)) + "."
                    + String.valueOf((size % 100)) + "_GB";
        }
    }
}
