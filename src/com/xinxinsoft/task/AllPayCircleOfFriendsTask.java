package com.xinxinsoft.task;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.xinxinsoft.entity.abnormalNumber.AbnormalNumberAllot;
import com.xinxinsoft.entity.abnormalNumber.AbnormalNumberOrder;
import com.xinxinsoft.entity.abnormalNumber.AbnormalNumberTableInfo;
import com.xinxinsoft.service.abnormalNumberService.AbnormalNumberService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import com.xinxinsoft.utils.SftpUtils;
import com.xinxinsoft.utils.common.FileUpload;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 统付亲友圈数据导入
 */
public class AllPayCircleOfFriendsTask {
    private static final Logger logger = LoggerFactory.getLogger(AllPayCircleOfFriendsTask.class);


    /**
     * 获取年月日时间戳
     *
     * @return
     */
    public static String getUnlockedNumber() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        return formatter.format(new Date());
    }

    public static String getUnlockedNumberTwo() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(new Date());
        return dateString;
    }


    /**
     * 获取当前时间前一个月字符串
     *
     * @return
     */
    public String getMonth() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");// 格式化日期
        date.setMonth(date.getMonth() - 1);
        return sdf.format(date);
    }

    /**
     * 获取当前时间前2个月字符串
     *
     * @return
     */
    public String getMonthTwo() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");// 格式化日期
        date.setMonth(date.getMonth() - 2);
        return sdf.format(date);
    }


    /**
     * 时间转换
     */
    public Date formatToDate(String strDate) throws ParseException {
        Date date = null;
        if (strDate != null && !"".equals(strDate)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = format.parse(strDate);
        }
        return date;
    }

    protected String getStringDate(Date currentTime, String format) {
        SimpleDateFormat formatter;
        if (format == null || "".equals(format)) {
            formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        } else {
            formatter = new SimpleDateFormat(format);
        }
        String dateString = formatter.format(currentTime);
        return dateString;
    }


    /**
     * FTP文件下载 数据入库
     */
    public void downloadFtpFile() {
        try {
            logger.info("亲朋圈号码FTPS文件下载开始=============================");
            long start = System.currentTimeMillis();
            Session zqddxt = SftpUtils.getSession("10.113.252.72", 22, "zqddxt", "H62KE@Xf");
            logger.info("链接FTPS=============================");
            ChannelSftp connect = SftpUtils.getConnect(zqddxt);
            //data2/interface/month/other/dm_grp_ygqpq_mx_202305.txt
            String pathOne = "/data2/interface/month/other/";//ftps路径
            String fileName = "dm_grp_ygqpq_mx_" + getMonth();//文件名字
            String ext = fileName + ".txt";//文件名字
            String pathTwo = "/eomapp_new0_LC/UploadFile/AllPayFtpFile/";//106本地路径
            try {
                logger.info("下载 " + pathOne + ext + " FTPS文件=============================");
                SftpUtils.download(pathOne, ext, pathTwo, ext, connect);
            } catch (Exception e) {
                logger.error(ext + " FTPS访问错误 " + e.getMessage(), e);
            }
            SftpUtils.disconnect(connect, zqddxt);
            logger.info(ext + "sftp文件下载完成");

            File fileObj = new File(pathTwo + ext);
            logger.info("地址为 " + pathTwo + ext);
            // 如果文件不存在，直接返回
            if (!fileObj.exists()) {
                logger.info(ext + "---文件不存在！");
                return;
            }

            FileInputStream fis = new FileInputStream(fileObj);
            BufferedReader br = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
            if (br.readLine() == null) {
                logger.info(ext + "---文件数据为空！");
                return;
            }


            //异常数据入库
            String line;
            int i = 0;
            //链接oracle数据库
            String url = "********************************************";
            String user = "ZQDDXT";
            String password = "zE=e7h%H";
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection con = DriverManager.getConnection(url, user, password);
            // 关闭事务自动提交
            con.setAutoCommit(false);

            //清空表数据
            PreparedStatement ps = con.prepareStatement("TRUNCATE TABLE BPMS_ALLPAY_CIRCLEOFFRIENDS");
            ps.execute();
            con.commit();

            Long startTime = System.currentTimeMillis();
            String sql = "insert into BPMS_ALLPAY_CIRCLEOFFRIENDS (UUID,OTHER_PHONE,EMPLOYEE_PHONE,FRIEND_PHONE) values (sys_guid(),?,?,?)";
            PreparedStatement pst = con.prepareStatement(sql);

            while ((line = br.readLine()) != null) {
                String[] item = line.split("\\|");
                i++;
                pst.setString(1, item[0]);
                pst.setString(2, item[1]);
                pst.setString(3, item[2]);
                pst.addBatch();
                if ((i + 1) % 10000 == 0) {
                    // 执行批次
                    pst.executeBatch();
                    // 清空批次
                    pst.clearBatch();
                }
            }

            // 执行最后的不足1000条数据的批次
            pst.executeBatch();
            // 语句执行完毕，提交本事务
            con.commit();
            Long endTime = System.currentTimeMillis();
            System.out.println("用时：" + (endTime - startTime));
            pst.close();
            con.close();
            br.close();// 关闭文件
            long end = System.currentTimeMillis();
            logger.info("downloadFtpFile方法执行耗时" + (end - start) / 1000 + "s");
        } catch (Exception e) {
            logger.error("导入亲朋圈号码数据错误==>" + e.getMessage(), e);
        }
    }


    //导入测试
    @Test
    public void test() throws Exception {
        String pathTwo = "C:/Users/<USER>/Desktop/dm_grp_ygqpq_mx_202305.txt";//本地路径
        try {
            long start = System.currentTimeMillis();
            File fileObj = new File(pathTwo);
            logger.info("地址为" + pathTwo);
            // 如果文件不存在，直接返回
            if (!fileObj.exists()) {
                logger.info(pathTwo + "---文件不存在！");
                return;
            }
            FileInputStream fis = new FileInputStream(fileObj);
            BufferedReader br = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
            if (br.readLine() == null) {
                logger.info(pathTwo + "---文件数据为空！");
                return;
            }


            //异常数据入库
            String line;
            int i = 0;
            //链接oracle数据库
            String url = "************************************"; //本地
            String user = "C##ZHANG";
            String password = "123456";
            Class.forName("oracle.jdbc.driver.OracleDriver");
            Connection con = DriverManager.getConnection(url, user, password);
            // 关闭事务自动提交
            con.setAutoCommit(false);
            Long startTime = System.currentTimeMillis();

            //清空表数据
            PreparedStatement ps = con.prepareStatement("TRUNCATE TABLE BPMS_ALLPAY_CIRCLEOFFRIENDS");
            ps.execute();
            con.commit();

            String sql = "insert into BPMS_ALLPAY_CIRCLEOFFRIENDS (UUID,OTHER_PHONE,EMPLOYEE_PHONE,FRIEND_PHONE) values (sys_guid(),?,?,?)";
            PreparedStatement pst = con.prepareStatement(sql);


            while ((line = br.readLine()) != null) {
                String[] item = line.split("\\|");
                i++;
                pst.setString(1, item[0]);
                pst.setString(2, item[1]);
                pst.setString(3, item[2]);
                pst.addBatch();
                if ((i + 1) % 10000 == 0) {
                    // 执行批次
                    pst.executeBatch();
                    // 清空批次
                    pst.clearBatch();
                }
            }

            // 执行最后的不足1000条数据的批次
            pst.executeBatch();
            // 语句执行完毕，提交本事务
            con.commit();
            Long endTime = System.currentTimeMillis();
            System.out.println("用时：" + (endTime - startTime));
            pst.close();
            con.close();
            br.close();// 关闭文件
            long end = System.currentTimeMillis();
            logger.info("arrearsRecord方法执行耗时" + (end - start) / 1000 + "s");
        } catch (Exception e) {
            logger.error("导入" + pathTwo + "数据错误==>" + e.getMessage(), e);
        }
    }
}
