package com.xinxinsoft.task;

import com.jcraft.jsch.*;
import com.xinxinsoft.entity.executeJobLog.JobLog;
import com.xinxinsoft.service.appOpenService.OMSService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import com.xinxinsoft.utils.common.FileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;

public class OneClickOrderTask {
    private static final String host = "*************";//ip
    private static final int port = 22;//22 port
    private static final String username = "qt_data";//user
    //private static final String password = "k%p$4J3@";
    private static final String password = "AsfV_29M";//替换新密码
    private static final Logger logger = LoggerFactory.getLogger(OneClickOrderTask.class);
    private JobLogServicer jobLogServicer;
    private OMSService omsService;
    public JobLogServicer getJobLogServicer() {
        return jobLogServicer;
    }
    public void setJobLogServicer(JobLogServicer jobLogServicer) {
        this.jobLogServicer = jobLogServicer;
    }
    public OMSService getOmsService() {
        return omsService;
    }
    public void setOmsService(OMSService omsService) {
        this.omsService = omsService;
    }
    /**
     * 一键下单附件上传
     */
    public void oneClickOrderFileUpload(){
        logger.info("一键下单数据推送SFTP开始");
        Date start_time=new Date();
        OneClickOrderTask sf = new OneClickOrderTask();
        /*连接SFTP服务器*/
        Session session = sf.getSession(host, port, username, password);
        ChannelSftp channel = sf.getConnect(session);
        JobLog jobLog = new JobLog();
        try{
            start_time = new Date();
            jobLog.setStartTime(new Date());
            jobLog.setJobName("Class:oneClickOrderTask Method:oneClickOrderFileUpload 推送一键下单分析数据");
            List<Map<String,String>> obj =omsService.getOrder_om_list();
            /*SFTP服务器固定文件夹*/
                String filDirectory ="/data/work1/CRM/";
            /*需要创建的新目录*/
            String createDirectory =dateOfThePreviousDayTwo()+"/temp";
            /*内容文件名*/
            String contentFileName="d131209"+dateOfThePreviousDay()+".d.avl";
            /*校验文件名*/
            String checkFileName = "d131209"+dateOfThePreviousDay()+".d.chk";
            /*创建本地文件夹*/
//            String ftpUrl = FileUpload.getFtpURL()+"oneClickOrder/"+FileUpload.getDateToString("yyyyMMdd")+"/";
            String ftpUrl = "eomapp_new0_LC/UploadFile/oneClickOrder/"+FileUpload.getDateToString("yyyyMMdd")+"/";

            /*获取文件夹路径*/
            File headPath = new File(ftpUrl);
            /*判断文件夹是否创建，没有创建则创建新文件夹*/
            if(!headPath.exists()){
                headPath.mkdirs();
            }
            /*创建数据文件*/
            creatTxtFile(contentFileName,ftpUrl);
            if(obj.size()>0){
                /*写入数据文件头部信息*/
                writeTxtFile("订单号&BOSS流水&实例号&产品编码&地市&区县&集团280&产品名称&资费ID&定价名称&提交时间&创建人",contentFileName,ftpUrl);
                //String d ="ORDER_SN&BOSS_NO&PHONE_NO&PROD_ID&COMPANY_NAME&COUNTY_NAME&UNIT_ID&PROD_NAME&PROD_PRCID&PRC_NAME&CREATE_DATE&EMPLOYEE_NAME";
                /*根据查询出来的数据循环写入数据文件内容信息*/
                for(int i=0;i<obj.size();i++){
                    String order_sn =obj.get(i).get("ORDER_SN");
                    String boss_no =obj.get(i).get("BOSS_NO");
                    String phone_no =obj.get(i).get("PHONE_NO");
                    String prod_id =obj.get(i).get("PROD_ID");
                    String company_name =obj.get(i).get("COMPANY_NAME");
                    String county_name =obj.get(i).get("COUNTY_NAME");
                    String UNIT_ID =obj.get(i).get("UNIT_ID");
                    String prod_name =obj.get(i).get("PROD_NAME");
                    String prod_prcid =obj.get(i).get("PROD_PRCID");
                    String prc_name =obj.get(i).get("PRC_NAME");
                    String create_date =obj.get(i).get("CREATE_DATE");
                    String employee_name =obj.get(i).get("EMPLOYEE_NAME");
                    writeTxtFile(order_sn+"&"+boss_no+"&"+phone_no+"&"+prod_id+"&"+company_name+"&"+county_name+"&"+UNIT_ID+"&"+prod_name+
                            "&"+prod_prcid+"&"+prc_name+"&"+create_date+"&"+employee_name,contentFileName,ftpUrl);
                }
            }
            /*for(int i1=0;i1<100;i1++){
                writeTxtFile("43214<==>4321432<==>4354561<==>56456465<==>43214321<==>43214321<==>43214321<==>吃的撒吃的撒<==>43214321<==>吃的撒吃的撒<==>4321432<==>吃的撒吃的撒",contentFileName,ftpUrl);
            }*/
            /*查询本地生成的数据文件的大小*/
            File file =new File(ftpUrl+contentFileName);
            String length = getFileLength(file);
            /*创建校验文件*/
            creatTxtFile(checkFileName,ftpUrl);
            /*校验文件写入数据文件的名字*/
            writeTxtFile(contentFileName,checkFileName,ftpUrl);
            /*校验文件写入数据文件的大小*/
            writeTxtFile(length,checkFileName,ftpUrl);
            /*校验文件写入数据文件的数据周期（创建年月日）*/
            writeTxtFile(dateOfThePreviousDay(),checkFileName,ftpUrl);
            /*校验文件写入数据文件创建时间（年月日时分秒）*/
            writeTxtFile(getStringDatethree(),checkFileName,ftpUrl);
            /*把文件装入List方便循环推送*/
            List<File> files = new ArrayList<>();
            files.add(new File(ftpUrl+contentFileName));
            files.add(new File(ftpUrl+checkFileName));
            for(int j=0;j<files.size();j++){
                if(files.get(j).exists()){
                    /*内容文件流*/
                    InputStream inti=new FileInputStream(files.get(j).getPath());
                    /*在SFTP服务器创建目录（有则不创建）*/
                    sf.mkdir(filDirectory, createDirectory, channel);
                    /*上传文件到SFTP服务器*/
                    sf.upload(filDirectory+createDirectory,inti,files.get(j).getName(), channel);
                }else{
                    logger.info("oneClickOrderTask定时器fileUpload方法未找到名为:"+files.get(j).getName()+"的附件,地址是:"+files.get(j).getPath(),"请核查");
                    throw new Exception("oneClickOrderTask定时器fileUpload方法未找到名为:"+files.get(j).getName()+"的附件,地址是:"+files.get(j).getPath()+"请核查");
                }
            }
            /*下载SFTP附件*/
            /*sf.download("/data/temp/test/", "download.txt", "D:/temp/haha/","download.txt", channel);*/
            /*删除SFTP附件*/
            /*sf.delete("/data/temp/test/", "delete.txt", channel);*/
            jobLog.setIsError(false);
            jobLog.setEndTime(new Date());
            jobLogServicer.save(jobLog);
            Date end_time = new Date();
            logger.info("一键下单数据推送SFTP正常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
        }catch(Exception e){
            e.printStackTrace();
            jobLog.setIsError(true);
            jobLog.setEndTime(new Date());
            jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
            jobLogServicer.save(jobLog);
            Date end_time = new Date();
            logger.info("一键下单数据推送SFTP异常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
        }finally{
            sf.disconnect(channel,session);
        }
    }
    /**
     * 1. 第一步:连接sftp服务器,先获取Session
     * @param host
     *            主机
     * @param port
     *            端口
     * @param username
     *            用户名
     * @param password
     *            密码
     * @return
     */
    public Session getSession(String host, int port, String username, String password) {
        Session session = null;
        try {
            JSch jsch = new JSch();
            session =jsch.getSession(username, host, port);
            logger.info("Session创建");
            session.setPassword(password);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            session.setConfig(sshConfig);
            session.connect();
            logger.info("Session连接");
        } catch (Exception e) {
            e.printStackTrace();
            if (session!= null && session.isConnected()){
                session.disconnect();
            }
        }
        return session;
    }


    /**
     * 2.第二步: 连接sftp服务器,再获取链接
     * @return
     */
    public ChannelSftp getConnect(Session session) {
        ChannelSftp sftp = null;
        try {
            if(session == null){
                logger.info("Can't Create Connect,Because session is null");
                return sftp;
            }
            Channel channel = session.openChannel("sftp");
            logger.info("开启渠道.");
            channel.connect();
            sftp = (ChannelSftp) channel;
            logger.info("连接SFTP服务器： " + session.getHost()+":"+session.getPort());
        } catch (Exception e) {
            e.printStackTrace();
            if (sftp!= null && sftp.isConnected()){
                sftp.disconnect();
            }
        }
        return sftp;
    }

    /**
     * 3.第三步:关闭 channel和session
     * @param channel
     */
    public void disconnect(Channel channel , Session session) {
        try {
            if (channel!= null && channel.isConnected()){
                channel.disconnect();
                logger.info("关闭渠道");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (session!= null && session.isConnected()){
                session.disconnect();
                logger.info("关闭session");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * 上传文件到远端服务器,如果在同一目录下,文件名相同会自动替换
     * 如果上传一半,网络原因中断,那服务器上会有一半大小的文件,请重新上传.
     * @param destDirectory
     *            远端服务器要上传的目录  : /data/temp/test/
     * @param srcDirectory
     *            本地要上传的目录 : D:/test/
     * @param srcFileName
     *            本地要上传的文件 : upload.txt
     * @param sftp
     */
    public void upload(String destDirectory, String srcDirectory, String srcFileName, ChannelSftp sftp) throws Exception{
        try {
            sftp.cd(destDirectory);
            File file = new File(srcDirectory+srcFileName);
            if(!file.exists()){
                throw new Exception(srcDirectory+srcFileName+" is not exists");
            }
            logger.info("上传本地文件"+srcDirectory+srcFileName+"到远端服务器"+destDirectory+" 开始");
            sftp.put(new FileInputStream(file), file.getName());
            logger.info("上传本地文件"+srcDirectory+srcFileName+"到远端服务器"+destDirectory+" 结束");
            //sftp.put("D:/application/eclipse64ee/workspace/SFTP/src/com/testdemo/www/ftp/SFTPTooL.java","/data/temp/test");//将本地目录的文件直接上传到服务器上
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 上传流到远端服务器,如果在同一目录下,文件名相同会自动替换
     * 如果上传一半,网络原因中断,那服务器上会有一半大小的文件,请重新上传.
     * @param destDirectory
     *            远端服务器要上传的目录  : /data/temp/test/
     * @param srcStream
     *            本地要上传的流 : D:/test/
     * @param srcFileName
     *            本地指定到远端服务器要生成的文件名 : upload.txt
     * @param sftp
     */
    public void upload(String destDirectory, InputStream srcStream, String srcFileName, ChannelSftp sftp) throws Exception{
        try {
            sftp.cd(destDirectory);
            if(srcStream == null){
                throw new Exception("流为空,"+srcFileName+" is not exists");
            }
            logger.info("上传流"+srcFileName+"到远端服务器"+destDirectory+" 开始");
            sftp.put(srcStream, srcFileName);
            logger.info("上传流"+srcFileName+"到远端服务器"+destDirectory+" 结束");
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上下载文件
     *
     * @param remoteDirectory
     *            在远端服务器上要下载的目录 :/data/temp/test/
     * @param remoteFile
     *            在远端服务器上要下载的文件名 :　download.txt
     * @param localDirectory
     *            本地所在文件夹 : D:/test/
     * @param localFile
     *            本地将要生成的的文件名 : download.txt
     * @param sftp 链接
     */
    public void download(String remoteDirectory, String remoteFile,String localDirectory, String localFile, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(remoteDirectory);
            File file = new File(localDirectory);
            if(!file.exists())
                file.mkdirs();
            File saveFile = new File(localDirectory,localFile);
            logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+localFile+" 开始");
            sftp.get(remoteFile, new FileOutputStream(saveFile));
            logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+localFile+" 结束");
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上批量下载文件到本地文件夹
     *
     * @param remoteDirectory
     *            在远端服务器上要下载的目录 :/data/temp/test/
     * @param remoteFile
     *            在远端服务器上要下载的文件名 :　*.txt
     * @param localDirectory
     *            本地所在文件夹 : D:/test/
     * @param sftp 链接
     */
    public void download(String remoteDirectory, String remoteFile,String localDirectory, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(remoteDirectory);
            File file = new File(localDirectory);
            if(!file.exists())
                file.mkdirs();
            logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+" 开始");
            sftp.get(remoteFile, localDirectory);
            logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+" 结束");
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上删除文件(仅能删除文件,不能删目录)
     *
     * @param directory
     *            在远端服务器上,要删除文件所在目录 : /data/temp/test/
     * @param deleteFile
     *            在远端服务器上,要删除的文件
     * @param sftp 链接
     */
    public void delete(String directory, String deleteFile, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(directory);
            sftp.rm(deleteFile);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上的指定文件夹下创建新的目录(多层次)
     * @param directory
     *            远端服务器上,要创建文件所在目录 : /data/temp/test/
     * @param folderPath
     *            远端服务器上,要创建的文件夹名 : ( 可以为多层次,形如  good 或  test2/good/ok )
     * @param sftp 链接
     */
    public void mkdir(String directory, String folderPath, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(directory);//切换目录,如果目录不存在就会报错
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        String[] folders = folderPath.split("/");
        for(String currentFolder :folders){
            try{
                sftp.ls(currentFolder);//展示目录,如果文件夹不存在就会报错
                sftp.cd(currentFolder);
            }catch(Exception e){
                sftp.mkdir(currentFolder);//即然不存在,就创建该文件夹
                sftp.cd(currentFolder);
                logger.info(currentFolder+" is no exists, make the dir success");
            }
        }
    }

    /**
     * 日期转换
     * @param currentTime
     * @return
     */
    public static String dateOfThePreviousDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,-1);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }
    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatethree() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(new Date());
        return dateString;
    }

    /**
     * 创建文件
     *
     * @throws IOException
     */
    public static boolean creatTxtFile(String name,String path) throws IOException {
        boolean flag = false;
        File filename = new File(path + name);
        if (!filename.exists()) {
            filename.createNewFile();
            flag = true;
        }
        return flag;
    }
    /**
     * 写文件
     *
     * @param newStr
     *       新内容
     * @param name
     *      文件名称
     * @param name
     *      文件地址
     * @throws IOException
     */
    public static boolean writeTxtFile(String newStr,String name,String path) throws IOException {
        //先读取原有文件内容，然后进行写入操作
        boolean flag = false;
        String filein = newStr + "\r\n";
        String temp = "";
        FileInputStream fis = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        FileOutputStream fos = null;
        PrintWriter pw = null;
        try {
            // 文件路径
            File file = new File(path + name);
            // 将文件读入输入流
            fis = new FileInputStream(file);
            isr = new InputStreamReader(fis);
            br = new BufferedReader(isr);
            StringBuffer buf = new StringBuffer();
            // 保存该文件原有的内容
            for (int j = 1; (temp = br.readLine()) != null; j++) {
                buf = buf.append(temp);
                // System.getProperty("line.separator")
                // 行与行之间的分隔符 相当于“\n”
                buf = buf.append(System.getProperty("line.separator"));
            }
            buf.append(filein);
            fos = new FileOutputStream(file);
            pw = new PrintWriter(fos);
            pw.write(buf.toString().toCharArray());
            pw.flush();
            flag = true;
        } catch (IOException e1) {
            e1.printStackTrace();
        } finally {
            if (pw != null) {
                pw.close();
            }
            if (fos != null) {
                fos.close();
            }
            if (br != null) {
                br.close();
            }
            if (isr != null) {
                isr.close();
            }
            if (fis != null) {
                fis.close();
            }
        }
        return flag;
    }

    /**
     * 获取文件大小(字节byte)
     * 方式一：file.length()
     */
    public static String  getFileLength(File file){
        long fileLength = 0L;
        if(file.exists() && file.isFile()){
            fileLength = file.length();
        }
        logger.info("文件"+file.getName()+"的大小为:"+fileLength+"byte");
        return fileLength+"";
    }

    public String getDate(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

    /**
     * 日期转换
     * @param currentTime
     * @return
     */
    public static String dateOfThePreviousDayTwo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,0);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }
}
