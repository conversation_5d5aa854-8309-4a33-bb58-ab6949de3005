package com.xinxinsoft.task;

import java.io.*;
import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.xinxinsoft.entity.GroupAccount.GroupHipAccount;
import com.xinxinsoft.entity.GroupAccount.GroupRelations;
import com.xinxinsoft.entity.PreinvApply.ValuableCardDet;
import com.xinxinsoft.entity.claimForFundModel.ClaimReplaceModelDet;
import com.xinxinsoft.entity.claimForFunds.*;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.sendComms.appExternalService;
import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.sendComms.claimFundsService.GroupAccountSrv;
import com.xinxinsoft.service.claimForFunds.claimForFundModelService;
import com.xinxinsoft.service.groupAccountService.GroupAccountService;
import com.xinxinsoft.service.valuableCard.ValuableCardService;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import com.xinxinsoft.entity.executeJobLog.JobLog;
import com.xinxinsoft.entity.claimForFunds.Taxpayer;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;

public class ClaimForFundsTask {
	private static final Logger logger = LoggerFactory.getLogger(ClaimForFundsTask.class);
	private static final String host = "*************";//ip
	private static final int port = 22;//22 port
	private static final String username = "qt_data";//user
	private static final String password = "AsfV_29M";//password
	@Resource(name="claimForFundModelService")
	private claimForFundModelService modelService;
	@Resource(name = "ValuableCardService")
	private ValuableCardService valuableCardService;
	@Resource(name = "GroupAccountService")
	private GroupAccountService groupAccountService;
	private ClaimForFundsService claimForFundsService;
	private Bpms_riskoff_service taskService;
	private JobLogServicer 			jobLogServicer;
	public ClaimForFundsService getClaimForFundsService() {
		return claimForFundsService;
	}

	public void setClaimForFundsService(ClaimForFundsService claimForFundsService) {
		this.claimForFundsService = claimForFundsService;
	}
	public Bpms_riskoff_service getTaskService() {
		return taskService;
	}
	public void setTaskService(Bpms_riskoff_service taskService) {
		this.taskService = taskService;
	}
	public JobLogServicer getJobLogServicer() {
		return jobLogServicer;
	}

	public void setJobLogServicer(JobLogServicer jobLogServicer) {
		this.jobLogServicer = jobLogServicer;
	}

	/**
	 * @Description TODO B库成员数据更新 每天11：15执行（0 15 23 * * ?）
	 * <AUTHOR>
	 * @Date 2022/7/7 11:35
	 **/
	public void BLibraryDownload(){
		Date startTime=new Date();
		logger.info("B库成员数据更新开始");
		JobLog jobLog = new JobLog();
		jobLog.setStartTime(new Date());
		jobLog.setJobName("Class:SftpTask Week:BLibraryDownload B库成员更新");
		Session zqddxt = SftpUtils.getSession("*************", 22, "zqddxt", "H62KE@Xf");
		ChannelSftp connect = SftpUtils.getConnect(zqddxt);
		logger.info("链接到FTPS: *************:22 ");
		String ftpPath = "/data2/interface/day/other/";//ftps路径
		String path = "/eomapp_new0_LC/UploadFile/BLibraryDownload/";//服务器路径

		String ext = "dm_clyy_real_group_user_"+getPreviousWednesday()+".txt";//文件名字

		File pathFile = new File(path + ext);
		//文件已存在并且不为空时  不在进行下载
		if (pathFile.exists() && pathFile.length()>0){
			logger.info("B库成员数据更新结束,ftp文件已存在！");
			jobLog.setIsError(false);
			jobLog.setEndTime(new Date());
			jobLog.setErrorMsg("B库成员数据更新异常，ftp文件已存在！");
			jobLogServicer.save(jobLog);
		}else {
			try{
				//删除掉服务器上的空文件
				if (pathFile.exists()){
					pathFile.delete();
				}
				//在远端服务器上 下载文件
				SftpUtils.download(ftpPath, ext, path, ext, connect);

				//重新获取服务器文件
				pathFile = new File(path + ext);
				if (pathFile.exists()){
					FileInputStream fis = new FileInputStream(pathFile);
					BufferedReader br = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
					String line;
					String[] strs = null;

					while((line = br.readLine())!=null){//使用readLine方法，一次读一行
						strs = line.split("\\|");
						try{
							if (strs!=null && strs.length==13){
								BLibMender bLibMender = new BLibMender();
								bLibMender.setPhoneNo(strs[0]);
								bLibMender.setGroupCode(strs[1]);
								bLibMender.setCompanyCode(strs[2]);
								bLibMender.setCountyCode(strs[3]);
								bLibMender.setArceCode(strs[4]);
								bLibMender.setServerNo(strs[5]);
								bLibMender.setServerName(strs[6]);
								bLibMender.setYwFlag(Integer.valueOf(strs[9]));
								bLibMender.setRealFlag(Integer.valueOf(strs[10]));
								bLibMender.setTaxFlag(Integer.valueOf(strs[11]));
								bLibMender.setDiyCode(strs[12]);
								claimForFundsService.saveBLibMender(bLibMender);
							}
						}catch (Exception e){
							logger.info("B库成员插入数据:"+line+",发生异常："+e.getMessage());
							e.printStackTrace();
						}
					}
					int delectNum = claimForFundsService.delectBLib();
					logger.info("B库成员历史数据删除完成,删除条数："+delectNum+" 条！");

					jobLog.setIsError(false);
					jobLog.setEndTime(new Date());
					jobLog.setErrorMsg("B库成员数据更新完成！");
					br.close();
				}else {
					logger.info("B库成员数据更新结束,未获取到："+ftpPath + ext+" 文件！");
					jobLog.setIsError(false);
					jobLog.setEndTime(new Date());
					jobLog.setErrorMsg("B库成员数据更新结束,未获取到："+ftpPath + ext+" 文件！");
					jobLogServicer.save(jobLog);
				}
				Date endTime = new Date();
				logger.info("B库成员数据更新结束，用时:"+(endTime.getTime() - startTime.getTime())+"毫秒");
			}catch (Exception e){
				logger.info("B库成员数据更新异常："+e.getMessage());
				e.printStackTrace();
				jobLog.setIsError(true);
				jobLog.setEndTime(new Date());
				jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
				jobLogServicer.save(jobLog);
			}finally{
				SftpUtils.disconnect(connect, zqddxt);
			}
		}
	}



	/**
	 * @Description TODO 推送政企资金认领记录信息到SFTP
	 * <AUTHOR>
	 * @Date 2022/5/17 11:38
	 **/
	public void capitalInformationFiling() {
		logger.info("资金认领推送政企资金记录信息到SFTP开始");
		Date start_time = new Date();
		ClaimForFundsTask claim = new ClaimForFundsTask();
		Session session = null;
		ChannelSftp channel = null;
		JobLog jobLog = new JobLog();

		try {
			jobLog.setStartTime(new Date());
			jobLog.setJobName("Class:SftpTask Method:capitalInformationFiling 政企资金信息");
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(new Date());
			calendar.add(Calendar.MONTH,-1);
			SimpleDateFormat simpleDateFormat =  new SimpleDateFormat( "yyyyMM");
			String dateOfTheMonth = simpleDateFormat.format(calendar.getTime());
			String dateStr = dateOfTheMonthString();

			List<Map<String, Object>> bickerObj = this.claimForFundsService.getCapitalBickerFiling(dateOfTheMonth);
			List<Map<String, Object>> recordObj = this.claimForFundsService.getCapitalRecordFiling(dateOfTheMonth);
			List<Map<String, Object>> useObj = this.claimForFundsService.getCapitalUseFiling(dateOfTheMonth);
			String filDirectory = "/data/work5/JFDATA/";
			String createDirectory = dateOfTheMonthString();
			String ftpUrl = "/eomapp_new0_LC/capital/" + dateOfTheMonthString() + "/";
			File headPath = new File(ftpUrl);
			if (!headPath.exists()) {
				headPath.mkdirs();
			}

			String bickerCheckFileName = "capitall_03047_" + dateStr + ".chk";
			creatTxtFile(bickerCheckFileName, ftpUrl);
			String bickerFileName = "capital_03047_" + dateStr + ".txt";
			creatTxtFile(bickerFileName, ftpUrl);
			logger.info("写入资金银行到账流水信息开始，写入总数:" + bickerObj.size());
			Date bickerStateTime = new Date();
			if (!bickerObj.isEmpty()) {
				writeTxtFile("记录行号&转账流水号&本方账户&对方账户&对方户名&资金到账时间&转账金额&用途&所在地市", ftpUrl + bickerFileName);

				for(int i = 0; i < bickerObj.size(); ++i) {
					String serialno = String.valueOf((bickerObj.get(i)).get("SERIALNO"));
					String accnumber = String.valueOf((bickerObj.get(i)).get("ACCNUMBER"));
					String otheraccnumber = String.valueOf((bickerObj.get(i)).get("OTHERACCNUMBER"));
					String othername = String.valueOf((bickerObj.get(i)).get("OTHERNAME"));
					String trandate = String.valueOf((bickerObj.get(i)).get("TRANDATE"));
					String amount = String.valueOf((bickerObj.get(i)).get("AMOUNT"));
					String usememo = String.valueOf((bickerObj.get(i)).get("USEMEMO")).replace("&", "-");
					String companyname = String.valueOf((bickerObj.get(i)).get("COMPANYNAME"));
					writeTxtFile(i + "&" + serialno + "&" + accnumber + "&" + otheraccnumber + "&" + othername + "&" + trandate + "&" + amount + "&" + usememo + "&" + companyname, ftpUrl + bickerFileName);
				}
			}

			Date bickerEndTime = new Date();
			logger.info("写入资金银行到账流水信息结束，用时:" + (bickerEndTime.getTime() - bickerStateTime.getTime()) + "毫秒");
			File bickerFile = new File(ftpUrl + bickerFileName);
			String bickerLength = getFileLength(bickerFile);
			writeTxtFile(bickerFileName + "|" + bickerLength + "|" + dateStr + "|" + getStringDatethree(new Date()), ftpUrl + bickerCheckFileName);
			String recordCheckFileName = "capitall_03048_" + dateStr + ".chk";
			creatTxtFile(recordCheckFileName, ftpUrl);
			String recordFileName = "capital_03048_" + dateStr + ".txt";
			creatTxtFile(recordFileName, ftpUrl);
			logger.info("写入资金认领记录信息开始，写入总数:" + recordObj.size());
			Date recordStateDate = new Date();
			if (!recordObj.isEmpty()) {
				//2025-05-06 18:57:13新增字段 资金转账场景（二级）
				writeTxtFile("记录行号&转账流水号&认领流水号&认领日期&集团客户标识&集团客户名称&客户经理工号&申请金额&所在地市&认领类型&审批工单号&映射关系建立环节&映射关系期限&映射关系开始时间&映射关系结束时间&资金转账场景（一级）&资金转账场景备注&一级审核人员&一级审核人员职位&一级审核结果&一级审核时间&二级审核人员&二级审核人员职位&二级审核结果&二级审核时间&三级审核人员&三级审核人员职位&三级审核结果&三级审核时间&四级审核人员&四级审核人员职位&四级审核结果&四级审核时间&五级审核人员&五级审核人员职位&五级审核结果&五级审核时间&资金转账场景（二级）", ftpUrl + recordFileName);

				for(int i = 0; i < recordObj.size(); ++i) {
					String serialno = String.valueOf((recordObj.get(i)).get("SERIALNO"));
					String batchno = String.valueOf((recordObj.get(i)).get("BATCHNO"));
					String pusDate = String.valueOf((recordObj.get(i)).get("PUS_DATE"));
					String groupcode = String.valueOf((recordObj.get(i)).get("GROUPCODE"));
					String groupname = String.valueOf((recordObj.get(i)).get("GROUPNAME"));
					String pushbossusername = String.valueOf((recordObj.get(i)).get("PUSHBOSSUSERNAME"));
					String amount = String.valueOf((recordObj.get(i)).get("AMOUNT"));
					String companyname = String.valueOf((recordObj.get(i)).get("COMPANYNAME"));
					String applyNo = String.valueOf((recordObj.get(i)).get("APPLYNO"));

					String relationshipBuilding = "1";
					String accountBankType = String.valueOf((recordObj.get(i)).get("ACCOUNT_BANKTYPE"));
					String durationOfTheRelationship = "1".equals(accountBankType) ? "1" : "2";
					String accountStartTime = "";
					String accountEndTime = "";
					if (StringUtils.isEmpty(String.valueOf((recordObj.get(i)).get("ACCOUNT_STARTTIME")))) {
						try {
							String[] dates = String.valueOf((recordObj.get(i)).get("ACCOUNT_STARTTIME")).split(" - ");
							accountStartTime = dates[0].trim();
							accountEndTime = dates[1].trim();
						} catch (Exception e) {
							e.printStackTrace();
							logger.error("同步大数据数据解析异常：" + e.getMessage());
						}
					}

					String transferScenario = "";
//					1、客户自有账户 对应集团侧  为本集团缴费   ---》二级 101
//					2、客户个人账户   对应集团侧 个人为企业付费  ---》二级302
//					3、财政统一支付  对应集团侧 财政拨款  ----》二级 401
//					4、上下级单位、股权单位 对应集团侧 总分机构或关联关系 ---》二级201
//					5、三方协议  对应集团侧 合作关系   --->二级502
					//关系类型  （1客户自有账户，2财政统一支付，3上下级单位，4股权关系，5三方协议，6个人账户，7三方接入）
					//1-为本集团缴费2-总分机构或关联关系3-个人为企业付费4-财政拨款5-合作关系6-其他
					String transferScenarioLv2="";
					String transferScenarioMome = "";
					switch (accountBankType) {
						case "1":
							transferScenario = "1";
							transferScenarioLv2="资金转账方和资金使用方名称完全一致";
							break;
						case "3":
							transferScenario = "2";
							transferScenarioLv2="总分关系（包含母子关系、上下级等）";
							break;
						case "4":
							transferScenario = "2";
							transferScenarioLv2="总分关系（包含母子关系、上下级等）";
							break;
						case "6":
							transferScenario = "3";
							transferScenarioLv2="关键人、联系人";
							break;
						case "2":
							transferScenario = "4";
							transferScenarioLv2="财政拨款";
							break;
						case "5":
							transferScenario = "5";
							transferScenarioLv2="无三方协议且无关联关系";
							break;
						default:
							transferScenario = "6";
							transferScenarioLv2="其他关系";
							transferScenarioMome = "本集团缴费，接口同步电子合同审批流程，系统自动完成银行账户与缴费码关联";
					}

					String task = String.valueOf((recordObj.get(i)).get("TASK"));
					String claimType = "1";
					try {
						if (StringUtils.isEmpty(task)) {
							task = "&&&&&&&&&&&&&&&&&&&";
						} else {
							StringBuilder deduplicationTask = new StringBuilder();
							String[] split = task.split("&");
							List<List<String>> groupedAndDeduplicated = new ArrayList();

							for(int j = 0; j < split.length; j += 4) {
								List<String> currentGroup = Arrays.asList(split).subList(j, Math.min(j + 4, split.length));
								groupedAndDeduplicated.add(currentGroup);
							}

							List<List<String>> uniqueGroups = new ArrayList();
							Set<String> seen = new HashSet();

							for(List<String> group : groupedAndDeduplicated) {
								if (group.size() >= 2) {
									String key = group.get(0) + "|" + group.get(1);
									if (!seen.contains(key)) {
										seen.add(key);
										uniqueGroups.add(group);
									}
								}
							}

							int uniqueGroupsSize = uniqueGroups.size();

							for(int a = 0; a < uniqueGroupsSize; ++a) {
								List<String> group = uniqueGroups.get(a);
								deduplicationTask.append(group.get(0)).append("&").append(group.get(1)).append("&").append(group.get(2)).append("&").append(group.get(3)).append("&");
							}

							while(uniqueGroupsSize < 5) {
								deduplicationTask.append("&&&&");
								++uniqueGroupsSize;
							}

							task = deduplicationTask.substring(0, deduplicationTask.length() - 1);
							if (!"null".equals(applyNo) && StringUtils.isNotEmpty(applyNo) && task.contains(":")){
								claimType = "0";
							}
						}
					} catch (Exception e) {
						logger.error("同步大数据流程数据解析异常：{}", e.getMessage());
						task = "&&&&&&&&&&&&&&&&&&&";
					}
//					记录行号i
//					&转账流水号serialno
//					&认领流水号batchno
//					&认领日期pusDate
//					&集团客户标识groupcode
//					&集团客户名称groupname
//					&客户经理工号pushbossusername
//					&申请金额amount
//					&所在地市companyname
//					&认领类型claimType
//					&审批工单号applyNo
//					&映射关系建立环节relationshipBuilding
//					&映射关系期限durationOfTheRelationship
//					&映射关系开始时间accountStartTime
//					&映射关系结束时间accountEndTime
//					&资金转账场景（一级）transferScenario
//					&资金转账场景备注transferScenarioMome
//					&一级审核人员task&一级审核人员职位&一级审核结果&一级审核时间&二级审核人员&二级审核人员职位&二级审核结果&二级审核时间&三级审核人员&三级审核人员职位&三级审核结果&三级审核时间&四级审核人员&四级审核人员职位&四级审核结果&四级审核时间&五级审核人员&五级审核人员职位&五级审核结果&五级审核时间
//					&资金转账场景（二级）
					writeTxtFile(i + "&" + serialno + "&" + batchno + "&" + pusDate + "&" + groupcode + "&" + groupname + "&" + pushbossusername + "&" + amount + "&" + companyname + "&" + claimType + "&" + applyNo + "&" + relationshipBuilding + "&" + durationOfTheRelationship + "&" + accountStartTime + "&" + accountEndTime + "&" + transferScenario + "&" + transferScenarioMome + "&" + task+"&"+transferScenarioLv2, ftpUrl + recordFileName);
				}
			}

			Date recordEndDate = new Date();
			logger.info("写入资金认领记录信息结束，用时:" + (recordEndDate.getTime() - recordStateDate.getTime()) + "毫秒");
			File recordFile = new File(ftpUrl + recordFileName);
			String recordLength = getFileLength(recordFile);
			writeTxtFile(recordFileName + "|" + recordLength + "|" + dateStr + "|" + getStringDatethree(new Date()), ftpUrl + recordCheckFileName);
			String useCheckFileName = "capitall_03049_" + dateStr + ".chk";
			creatTxtFile(useCheckFileName, ftpUrl);
			String useFileName = "capital_03049_" + dateStr + ".txt";
			creatTxtFile(useFileName, ftpUrl);
			logger.info("写入资金使用缴费信息开始，写入总数:" + useObj.size());
			Date useStateDate = new Date();
			if (!useObj.isEmpty()) {
				//2025-05-06 18:57:13新增字段 资金转账场景（二级）
				writeTxtFile("记录行号&认领流水号&业务流水号&BOSS侧业务受理时间&集团客户标识&集团客户名称&BOSS侧使用金额&缴费方式&所在地市&审批工单号&映射关系建立环节&映射关系期限&映射关系开始时间&映射关系结束时间&资金转账场景（一级）&资金转账场景备注&一级审核人员&一级审核人员职位&一级审核结果&一级审核时间&二级审核人员&二级审核人员职位&二级审核结果&二级审核时间&三级审核人员&三级审核人员职位&三级审核结果&三级审核时间&四级审核人员&四级审核人员职位&四级审核结果&四级审核时间&五级审核人员&五级审核人员职位&五级审核结果&五级审核时间&资金转账场景（二级）", ftpUrl + useFileName);

				for(int i = 0; i < useObj.size(); ++i) {
					String batchno = String.valueOf((useObj.get(i)).get("BATCHNO"));
					String moneyno = String.valueOf((useObj.get(i)).get("MONEYNO"));
					String createdate = String.valueOf((useObj.get(i)).get("CREATEDATE"));
					String groupcode = String.valueOf((useObj.get(i)).get("GROUPCODE"));
					String groupname = String.valueOf((useObj.get(i)).get("GROUPNAME"));
					String amount = String.valueOf((useObj.get(i)).get("AMOUNT"));
					String usetype = "银行转账";
					String companyName =String.valueOf(useObj.get(i).get("COMPANYNAME"));
					String applyNo = String.valueOf((useObj.get(i)).get("APPLYNO"));
					String relationshipBuilding = "1";
					String accountBankType = String.valueOf((useObj.get(i)).get("ACCOUNT_BANKTYPE"));
					String durationOfTheRelationship = "1".equals(accountBankType) ? "1" : "2";
					String accountStartTime = "";
					String accountEndTime = "";
					if (StringUtils.isEmpty(String.valueOf((useObj.get(i)).get("ACCOUNT_STARTTIME")))) {
						try {
							String[] dates = String.valueOf((useObj.get(i)).get("ACCOUNT_STARTTIME")).split(" - ");
							accountStartTime = dates[0].trim();
							accountEndTime = dates[1].trim();
						} catch (Exception e) {
							e.printStackTrace();
							logger.error("同步大数据数据解析异常：" + e.getMessage());
						}
					}

					String transferScenario = "";
					String transferScenarioLv2="";
					String transferScenarioMome = "";
					switch (accountBankType) {
						case "1":
							transferScenario = "1";
							transferScenarioLv2="资金转账方和资金使用方名称完全一致";
							break;
						case "3":
							transferScenario = "2";
							transferScenarioLv2="总分关系（包含母子关系、上下级等）";
							break;
						case "4":
							transferScenario = "2";
							transferScenarioLv2="总分关系（包含母子关系、上下级等）";
							break;
						case "6":
							transferScenario = "3";
							transferScenarioLv2="关键人、联系人";
							break;
						case "2":
							transferScenario = "4";
							transferScenarioLv2="财政拨款";
							break;
						case "5":
							transferScenario = "5";
							transferScenarioLv2="无三方协议且无关联关系";
							break;
						default:
							transferScenario = "6";
							transferScenarioLv2="其他关系";
							transferScenarioMome = "本集团缴费，接口同步电子合同审批流程，系统自动完成银行账户与缴费码关联";
					}

					String task = String.valueOf((useObj.get(i)).get("TASK"));

					try {
						if (StringUtils.isEmpty(task)) {
							task = "&&&&&&&&&&&&&&&&&&&";
						} else {
							StringBuilder deduplicationTask = new StringBuilder();
							String[] split = task.split("&");
							List<List<String>> groupedAndDeduplicated = new ArrayList();

							for(int j = 0; j < split.length; j += 4) {
								List<String> currentGroup = Arrays.asList(split).subList(j, Math.min(j + 4, split.length));
								groupedAndDeduplicated.add(currentGroup);
							}

							List<List<String>> uniqueGroups = new ArrayList();
							Set<String> seen = new HashSet();

							for(List<String> group : groupedAndDeduplicated) {
								if (group.size() >= 2) {
									String key = group.get(0) + "|" + group.get(1);
									if (!seen.contains(key)) {
										seen.add(key);
										uniqueGroups.add(group);
									}
								}
							}

							int uniqueGroupsSize = uniqueGroups.size();

							for(int a = 0; a < uniqueGroupsSize; ++a) {
								List<String> group = uniqueGroups.get(a);
								deduplicationTask.append(group.get(0)).append("&").append(group.get(1)).append("&").append(group.get(2)).append("&").append(group.get(3)).append("&");
							}

							while(uniqueGroupsSize < 5) {
								deduplicationTask.append("&&&&");
								++uniqueGroupsSize;
							}

							task = deduplicationTask.substring(0, deduplicationTask.length() - 1);
						}
					} catch (Exception e) {
						logger.error("同步大数据流程数据解析异常：{}", e.getMessage());
						task = "&&&&&&&&&&&&&&&&&&&";
					}

					writeTxtFile(i + "&" + batchno + "&" + moneyno + "&" + createdate + "&" + groupcode + "&" + groupname + "&" + amount + "&" + usetype +"&" + companyName + "&" + applyNo + "&" + relationshipBuilding + "&" + durationOfTheRelationship + "&" + accountStartTime + "&" + accountEndTime + "&" + transferScenario + "&" + transferScenarioMome + "&" + task+"&"+transferScenarioLv2, ftpUrl + useFileName);
				}
			}

			Date useEndDate = new Date();
			logger.info("写入资金使用缴费信息结束 ，用时:" + (useEndDate.getTime() - useStateDate.getTime()) + "毫秒");
			File useFile = new File(ftpUrl + useFileName);
			String useLength = getFileLength(useFile);
			writeTxtFile(useFileName + "|" + useLength + "|" + dateStr + "|" + getStringDatethree(new Date()), ftpUrl + useCheckFileName);
			List<File> files = new ArrayList();
			files.add(new File(ftpUrl + bickerFileName));
			files.add(new File(ftpUrl + bickerCheckFileName));
			files.add(new File(ftpUrl + recordFileName));
			files.add(new File(ftpUrl + recordCheckFileName));
			files.add(new File(ftpUrl + useFileName));
			files.add(new File(ftpUrl + useCheckFileName));
			session = claim.getSession("*************", 22, "qt_data", "AsfV_29M");
			channel = claim.getConnect(session);
			for(File file : files) {
				if (!file.exists()) {
					logger.info("ClaimForFundsTask定时器capitalInformationFiling方法未找到名为:" + file.getName() + "的附件,地址是:" + file.getPath(), "请核查");
					throw new Exception("ClaimForFundsTask定时器capitalInformationFiling方法未找到名为:" + file.getName() + "的附件,地址是:" + file.getPath() + "请核查");
				}
				InputStream inti = Files.newInputStream(Paths.get(file.getPath()));
				claim.mkdir(filDirectory, createDirectory, channel);
				claim.upload(filDirectory + createDirectory, inti, file.getName(), channel);
				logger.info(file.getName() + "上传成功！");
			}

			jobLog.setIsError(false);
			jobLog.setEndTime(new Date());
			this.jobLogServicer.save(jobLog);
			Date end_time = new Date();
			logger.info("资金认领推送资金信息到SFTP结束，用时:" + (end_time.getTime() - start_time.getTime()) + "毫秒");
		} catch (Exception e) {
			e.printStackTrace();
			jobLog.setIsError(true);
			jobLog.setEndTime(new Date());
			jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "") + ":" + e.getLocalizedMessage());
			this.jobLogServicer.save(jobLog);
			Date end_time = new Date();
			logger.info("资金认领推送资金信息到SFTP异常结束，用时:" + (end_time.getTime() - start_time.getTime()) + "毫秒");
		} finally {
			claim.disconnect(channel, session);
		}
	}

	public void paymentCodeData(){
		logger.info("资金认领推送缴费码映射关系到SFTP开始");
		Date start_time=new Date();
		ClaimForFundsTask claim = new ClaimForFundsTask();
		/*连接SFTP服务器*/
		Session session = claim.getSession(host, port, username, password);
		ChannelSftp channel = claim.getConnect(session);
		JobLog jobLog = new JobLog();

		try{
			jobLog.setStartTime(new Date());
			jobLog.setJobName("Class:SftpTask Method:paymentCodeData 政企资金缴费码关系信息");
			Calendar calendar = Calendar.getInstance();
			calendar.setTime(new Date());
			calendar.add(Calendar.DAY_OF_YEAR,-1);

			SimpleDateFormat simpleDateFormat =  new SimpleDateFormat( "yyyyMMdd");
			String dateStr = simpleDateFormat.format(calendar.getTime());
			String newDdateStr = simpleDateFormat.format(new Date());
			//缴费码映射关系
			List<Map<String,Object>> paymentCodeData =claimForFundsService.getPaymentCodeData(dateStr);

			//SFTP服务器固定文件夹
			String filDirectory ="/data/work1/CRM/";

			String createDirectory =newDdateStr+"/temp";

			//创建本地文件夹路径
			//String ftpUrl = FileUpload.getFtpURL()+"paymentCodeData/"+newDdateStr+"/";
			String ftpUrl = "/eomapp_new0_LC/UploadFile/paymentCodeData/"+newDdateStr+"/";
			//获取文件夹
			File headPath = new File(ftpUrl);
			//判断文件夹是否创建，没有创建则创建新文件夹
			if(!headPath.exists()){
				headPath.mkdirs();
			}


			//校验文件名
			String paymentCodeDataCheckFileName = "d131634"+dateStr+".d.chk";
			//创建校验文件
			creatTxtFile(paymentCodeDataCheckFileName,ftpUrl);

			//数据文件
			String paymentCodeDataFileName="d131634"+dateStr+".d.avl";
			//创建数据文件
			creatTxtFile(paymentCodeDataFileName,ftpUrl);

			//写入数据
			logger.info("写入缴费码映射关系开始，写入总数:"+paymentCodeData.size());
			Date stateTime=new Date();
			if(paymentCodeData.size()>0){
				//写入数据文件头部信息
				writeTxtFile("记录行号&对方账户&帐户标识&集团客户标识&创建时间&状态",ftpUrl+paymentCodeDataFileName);
				//根据查询出来的数据循环写入数据文件内容信息
				for(int i=0;i<paymentCodeData.size();i++){
					String accountNumber =String.valueOf(paymentCodeData.get(i).get("ACCOUNT_NUMBER"));
					String contractNo =String.valueOf(paymentCodeData.get(i).get("CONTRACT_NO"));
					String groupCode =String.valueOf(paymentCodeData.get(i).get("GROUP_CODE"));
					String createDate =String.valueOf(paymentCodeData.get(i).get("CREATE_DATE"));
					writeTxtFile((i+1)+"&"+accountNumber+"&"+contractNo+"&"+groupCode+"&"+createDate+"&1",ftpUrl+paymentCodeDataFileName);
				}
			}
			Date endTime = new Date();
			logger.info("写入缴费码映射关系结束，用时:"+(endTime.getTime() - stateTime.getTime())+"毫秒");

			//查询本地生成的数据文件的大小
			File bickerFile = new File(ftpUrl+paymentCodeDataFileName);
			String bickerLength = getFileLength(bickerFile);
			//+"|"+dateStr+"|"+getStringDatethree(new Date())
			//校验文件写入数据 校验文件格式：数据文件名称|数据文件大小（按字节）|数据周期（yyyymmdd）|数据上传时间（yyyymmddhh24miss）
			writeTxtFile(paymentCodeDataFileName,ftpUrl+paymentCodeDataCheckFileName);
			writeTxtFile(bickerLength,ftpUrl+paymentCodeDataCheckFileName);
			writeTxtFile(dateStr,ftpUrl+paymentCodeDataCheckFileName);
			writeTxtFile(getStringDatethree(new Date()),ftpUrl+paymentCodeDataCheckFileName);

			//把文件装入List方便循环推送
			List<File> files = new ArrayList<>();
			files.add(new File(ftpUrl+paymentCodeDataFileName));
			files.add(new File(ftpUrl+paymentCodeDataCheckFileName));
			for(int j=0;j<files.size();j++){
				if(files.get(j).exists()){
					//内容文件流
					InputStream inti=new FileInputStream(files.get(j).getPath());
					//在SFTP服务器创建目录（有则不创建）
					claim.mkdir(filDirectory, createDirectory, channel);
					//上传文件到SFTP服务器
					claim.upload(filDirectory+createDirectory,inti,files.get(j).getName(), channel);
					logger.info(files.get(j).getName()+"上传成功！");
				}else{
					logger.info("ClaimForFundsTask定时器paymentCodeData方法未找到名为:"+files.get(j).getName()+"的附件,地址是:"+files.get(j).getPath(),"请核查");
					throw new Exception("ClaimForFundsTask定时器paymentCodeData方法未找到名为:"+files.get(j).getName()+"的附件,地址是:"+files.get(j).getPath()+"请核查");
				}
			}

			jobLog.setIsError(false);
			jobLog.setEndTime(new Date());
			jobLogServicer.save(jobLog);
			Date end_time = new Date();
			logger.info("资金认领推送缴费码映射关系结束，用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
		}catch(Exception e){
			e.printStackTrace();
			jobLog.setIsError(true);
			jobLog.setEndTime(new Date());
			jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
			jobLogServicer.save(jobLog);
			Date end_time = new Date();
			logger.info("资金认领推送缴费码映射关系异常结束，用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
		}finally{
			claim.disconnect(channel,session);
		}
	}

	/**
	 * @Description TODO 资金认领发票信息自动推送
	 * <AUTHOR>
	 * @Date 2022/5/17 11:34
	 **/
	public void PaymentInvoicing(){
		List<InvoiceMiddle> list = claimForFundsService.getNotPushedInvoice();
		logger.info("资金认领发票信息推送开始："+getStringDatethree(new Date())+",本次推送总数为："+list.size());
		for (InvoiceMiddle invoiceMiddles : list){
			MoneyApplyDet det = claimForFundsService.getMoneyApplyDetId(invoiceMiddles.getAssociationNumber());
			MoneyApply apply = claimForFundsService.getMoneyApplyByApplyNo(det.getApplyNo());
			MoneyTotal total = claimForFundsService.getMoneyTotal(apply.getMoneyTotal_id());
			if (det==null){
				logger.info("发票信息异常，查询对应明细工单失败:"+invoiceMiddles.getId());
			}else {
				List<Map<String,Object>> PushUser = claimForFundsService.getCountyByUserID(String.valueOf(total.getPushUserName()));
				if (PushUser.size()==0){
					logger.info("发票信息异常，查询对应申请人工号失败:"+det.getCreatorId());
				}else{
					String login_no = PushUser.get(0).get("BOSSUSERNAME").toString();
					if (login_no==null){
						logger.info("发票信息异常，查询对应申请人工号失败:"+det.getCreatorId());
					}else {
						Result sGetLoginjson = ClaimFundsOpenSrv.getInstance().sGetLoginMsg(login_no);
						logger.info("根据工号查询对应渠道信息:"+sGetLoginjson);
						if(ResultCode.SUCCESS.code()==sGetLoginjson.getCode()) {  //判断当前请求是否成功
							JSONObject SGetLoginObj = JSONObject.fromObject(sGetLoginjson.getData());
							JSONObject SGetLoginRoot = JSONObject.fromObject(SGetLoginObj.getString("ROOT"));
							if (SGetLoginRoot.getString("RETURN_CODE").equals("0")){
								JSONObject SGetLoginDate = JSONObject.fromObject(SGetLoginRoot.getString("OUT_DATA"));
								String groupId = SGetLoginDate.getString("GROUP_ID");
								String regionId = SGetLoginDate.getString("REGION_ID");
								String busiGroupId ="";
								if (SGetLoginDate.has("BUSI_GROUP_ID")){
									busiGroupId = SGetLoginDate.getString("BUSI_GROUP_ID");
								}else if (SGetLoginDate.has("BUSI_GROUP_LIST")){
									JSONArray group_list = JSONArray.fromObject(SGetLoginDate.getJSONArray("BUSI_GROUP_LIST"));
									JSONObject object = group_list.getJSONObject(0);
									busiGroupId = object.getString("GROUP_ID");
								}
								String phoneNo = "";
								Long contractNo = Long.valueOf(det.getContrctNo());
								String custName = det.getGroupName();
								String msgRecvPhone =invoiceMiddles.getMsgRecvPhone();
								String paySn = invoiceMiddles.getPaymentAccept();
								String ghfDz = invoiceMiddles.getAddress() +" "+invoiceMiddles.getPhoneNo();
								String ghfNsrsbh = invoiceMiddles.getTaxpayerId();
								String ghfYhzh =invoiceMiddles.getBankName() + " " + invoiceMiddles.getBankAccount();
								Result result = ClaimFundsOpenSrv.getInstance().paymentIvoicing(phoneNo,contractNo,custName,msgRecvPhone,paySn,
										ghfDz,ghfNsrsbh,ghfYhzh,regionId,busiGroupId,groupId,login_no);
								if(ResultCode.SUCCESS.code()==result.getCode()) {  //判断当前请求是否成功
									JSONObject obj = JSONObject.fromObject(result.getData());
									JSONObject root = JSONObject.fromObject(obj.getString("ROOT"));
									if (root.has("BODY")){
										JSONObject body = JSONObject.fromObject(root.getString("BODY"));
										if (body.getString("RETURN_CODE").equals("0")){
											invoiceMiddles.setBoosState("1");
										}else {
											invoiceMiddles.setBoosState("0");
											invoiceMiddles.setBossMsg(root.getString("RETURN_MSG"));
										}
									}else {
										invoiceMiddles.setBoosState("0");
										invoiceMiddles.setBossMsg(root.getString("RETURN_MSG"));
									}

								}else {
									invoiceMiddles.setBoosState("0");
									invoiceMiddles.setBossMsg(result.getMessage());
								}
							}else {
								invoiceMiddles.setBoosState("0");
								invoiceMiddles.setBossMsg(SGetLoginRoot.getString("RETURN_MSG"));
							}
						}else {
							invoiceMiddles.setBoosState("0");
							invoiceMiddles.setBossMsg(sGetLoginjson.getMessage());
						}
						claimForFundsService.updateInvoiceMiddle(invoiceMiddles);
					}
				}
			}
		}
		logger.info("资金认领发票信息推送结束："+getStringDatethree(new Date()));
	}

	/**
	 * @Description :资金认领统计超时未处理代办(每天9:00执行一次)
	 * <AUTHOR>
	 * @return: void
	 * @Date 2022/3/1 11:39
	 */
	public void AgentTimeout(){
		logger.info("资金认领代办处理超时统计开始");
		String CreatorDate = claimForFundsService.getTargetDate(-1);
		logger.info("本次超时代办信息统计时间为:"+CreatorDate+" 及以前的代办 ");
		List<Map<String,String>> mapList = claimForFundsService.getTimeoutDataWaitList(CreatorDate);
		for (Map<String,String> map:mapList){
			List<Map<String,String>> userList = claimForFundsService.getUserByRowno(String.valueOf(map.get("HANDLEUSERID")));
			if (userList.size()>0){
				for(int a = 0;a<userList.size();a++){
					if (userList.get(a).get("BOSSUSERNAME")!=null && userList.get(a).get("BOSSUSERNAME").length()>0){
						String msg = "亲爱的同事，您当前有"+String.valueOf(map.get("NUMBER"))+"条资金认领代办未处理，请及时处理。";
						try {
							String root = appExternalService.getInstance().cancelRecharge(msg,"order_message",userList.get(a).get("BOSSUSERNAME"));
							JSONObject object = JSONObject.fromObject(root);
							JSONObject respHead = object.getJSONObject("respHead");
							if (respHead.getString("code").equals("200")){
								logger.info("资金认领待办超时未处理信息提醒:推送人员:"+userList.get(a).get("EMPLOYEE_NAME")+" 推送信息:"+msg);
							}else {
								logger.info("资金认领待办超时未处理信息提醒:失败信息:"+respHead.getString("msg"));
							}
						} catch (Exception e) {
							logger.info("资金认领待办超时未处理接口异常提醒:"+e);
						}
					}else {
						logger.info("资金认领待办超时未处理信息提醒:管理员:"+userList.get(a).get("EMPLOYEE_NAME")+" 未配置BOSS工号,请核实!");
					}
				}
			}else {
				logger.info("资金认领待办超时未处理信息提醒:用户:"+String.valueOf(map.get("HANDLEUSERID"))+" 信息异常,未获取到对应用户信息,请核实!");
			}
		}
		logger.info("资金认领代办处理超时统计结束");
	}

	/**
	 * @Description :资金认领资金超时未认领统计提醒(每天8.30执行)
	 * <AUTHOR>
	 * @return: void
	 * @Date 2022/3/1 10:43
	 */
	public void TimeoutReminder(){
		logger.info("资金认领超时未认领信息统计开始");
		String stateCreatorDate = claimForFundsService.getTargetDate(-8);
		String endCreatorDate = claimForFundsService.getTargetDate(-1);
		logger.info("本次超时未认领信息统计时间为:"+stateCreatorDate+" 至 "+endCreatorDate);
		List<Map<String,String>> mapList = claimForFundsService.getTimeoutDataMoneyList(stateCreatorDate,endCreatorDate);
		for (Map<String,String> map:mapList){
			List<Map<String,String>> userList = claimForFundsService.getTimeoutDataUserList(map.get("COMPANYCODE"),"ROLE_ZJDSGLY");
			if (userList.size()>0){
				for(int a = 0;a<userList.size();a++){
					if (userList.get(a).get("BOSSUSERNAME")!=null && userList.get(a).get("BOSSUSERNAME").length()>0){
						String msg = "亲爱的同事，您所在地市【"+stateCreatorDate+"】至【"+endCreatorDate+"】存在"+String.valueOf(map.get("NUMBER"))+"条集团资金未认领数据，请及时通知进行资金认领。";
						try {
							String root = appExternalService.getInstance().cancelRecharge(msg,"order_message",userList.get(a).get("BOSSUSERNAME"));
							JSONObject object = JSONObject.fromObject(root);
							JSONObject respHead = object.getJSONObject("respHead");
							if (respHead.getString("code").equals("200")){
								logger.info("资金认领超时未认领信息推送成功:推送人员:"+userList.get(a).get("EMPLOYEE_NAME")+" 推送信息:"+msg);
							}else {
								logger.info("资金认领超时未认领信息推送失败:失败信息:"+respHead.getString("msg"));
							}
						} catch (Exception e) {
							logger.info("资金认领超时未认领接口异常提醒:"+e);
						}
					}else {
						logger.info("资金认领超时未认领信息提醒:管理员:"+userList.get(a).get("EMPLOYEE_NAME")+" 未配置BOSS工号,请核实!");
					}
				}
			}else {
				logger.info("资金认领超时未认领信息提醒:地市:"+map.get("COMPANYCODE")+" 未配置管理员,请核实!");
			}
		}
		logger.info("资金认领超时未认领信息统计结束");
	}

	/**
	 * @Description TODO 根据缴费模型有效期修改状态   每天1点执行
	 * <AUTHOR>
	 * @Date 2022/5/19 15:25
	 **/
	public void updateClaimFoundModel(){
		logger.info("资金缴费模型状态更新开始");
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		String dateString = formatter.format(new Date());
		int stateNumber = modelService.updateStartClaimFoundModel(dateString);
		logger.info("资金缴费模型未使用状态更新条数："+stateNumber);
		int endNumber = modelService.updateEndClaimFoundModel(dateString);
		logger.info("资金缴费模型使用中状态更新条数："+endNumber);
		logger.info("资金缴费模型状态更新结束");
	}

	/**
	 * @Description TODO 资金认领从FTP文件下载
	 * <AUTHOR>
	 * @return java.lang.String
	 * @Date 2022/9/2 15:45
	 **/
	public String downloadFtpFile(){
		logger.info("资金认领FTP文件下载开始=============================");
		FTPClient ftpc=new FTPClient();
		  //初始化并保存信息
        String server = "**************";
        int port = 21 ;
        String uname = "cmip" ;
        String password = "ipmc@1215" ;
        //String ext="TransferDetail"+"20200604";
        String ext="TransferDetail"+dateOfThePreviousDay();
        JobLog jobLog = new JobLog();
        try {
        	jobLog.setStartTime(new Date());
    	    jobLog.setJobName("Class:ClaimForFundsTask Method:downloadFtpFile 资金认领资金数据下载 每小时下载一次");
			logger.info("Class:ClaimForFundsTask Method:downloadFtpFile 资金认领资金数据下载 每小时下载一次"+new Date());
    	    String systemKey = FTPClientConfig.SYST_NT;
            String serverLanguageCode = "zh";
            FTPClientConfig conf = new FTPClientConfig(systemKey);
            conf.setServerLanguageCode(serverLanguageCode);
            conf.setDefaultDateFormatStr("yyyy-MM-dd");
        	ftpc.configure(conf);
        	ftpc.connect(server, port);
        	ftpc.login(uname, password);
            // 文件类型,默认是ASCII
        	ftpc.setFileType(FTPClient.BINARY_FILE_TYPE);
            // 设置被动模式
        	ftpc.enterLocalPassiveMode();
        	ftpc.setConnectTimeout(3000);
        	ftpc.setControlEncoding("GBK");
            // 响应信息
            int replyCode = ftpc.getReplyCode();
            if ((!FTPReply.isPositiveCompletion(replyCode))) {
                // 关闭Ftp连接
            	try {
                    if (ftpc.isConnected())
                    	ftpc.disconnect();
                } catch (Exception e) {
					logger.error("关闭FTP服务出错!"+e.getMessage(),e);
                    throw new Exception("关闭FTP服务出错!");
                }
                // 释放空间
            	ftpc = null;
				logger.info("登录FTP服务器失败,请检查![Server:" + server + "、"  + "User:" + uname + "、" + "Password:" + password);
                throw new Exception("登录FTP服务器失败,请检查![Server:" + server + "、"  + "User:" + uname + "、" + "Password:" + password);
            } else {
                 //logger.info("FTP登录成功=====》[Server:" + server + "、"  + "User:" + uname + "、" + "Password:" + password);
                 ftpc.changeWorkingDirectory("/order/");
                 List<FTPFile> ftpfiles = Arrays.asList(ftpc.listFiles());
                 if(ftpfiles==null || ftpfiles.size()==0) {
                	 logger.info("/下找不到文件");
                 }else{
                	 List<Moneytotal_Temporary> temporaryList = new ArrayList<Moneytotal_Temporary>();
                	 //循环从ftp拿到的文件
                	 for (FTPFile ftpFile : ftpfiles) {
                	 	//判断文件格式和文件名称
                         if (ftpFile.isFile()){
                        	 if(ftpFile.getName().indexOf(ext)>=0){
                        		 File file=new File("/eomapp_new0_LC/UploadFile/ClaimMoneyFile/financialsystenftp/"+ftpFile.getName());
								 logger.info("资金认领FTP文件下载文件名称====》》"+ftpFile.getName());
								 //判断当前文件是否已经存在
                        		 if(!file.exists()){
                        		 	//下载文件
		                            File localFile = new File("/eomapp_new0_LC/UploadFile/ClaimMoneyFile/financialsystenftp/"+ftpFile.getName());
		                            OutputStream os = new FileOutputStream(localFile);
		                            ftpc.retrieveFile(ftpFile.getName(), os);
		                            os.close();
		                            //读取文件
		 	                        FileInputStream fis = new FileInputStream(localFile);
		 	            			InputStreamReader isr = new InputStreamReader(fis, "GBK");//字符流
		 	            			BufferedReader br = new BufferedReader(isr);//缓冲
									Moneytotal_Temporary temporary = null;
		 	            			String line = null;
		 	            			String[] strs = null;
		 	            			//循环文件的每行数据
		 	            			while ((line = br.readLine()) != null) {//字符不等于空
		 	            				//根据分割符拆分字符串
		 	            				strs = line.split("\\<=>");
										temporary = new Moneytotal_Temporary();
										temporary.setSerialNo(strs[0]);
										temporary.setOtherAccNumber(strs[1]);//(对方账号)
										temporary.setOtherName(strs[2]);//(对方户名)
										temporary.setOtherBank(strs[3]);//(对方开户行)
										temporary.setAccNumber(strs[4]);//(公司账号)

										temporary.setTranDate(getStringDateFour(strs[5]));//strs[5]//(交易时间)
										temporary.setCreateDate(new Date());//strs[5]//(交易时间)

										temporary.setAmount(strs[6]);//(转账金额)
										temporary.setReceiverSCompany(strs[7]);
										temporary.setMemo(("null".equals(strs[8])?"无":strs[8]));//(摘要注释)
										temporary.setUseMemo(strs[9]);//(用途注释)

										temporary.setCompanyCode(strs[10]);

										if(strs.length>=12 && strs[11]!=null && !"".equals(strs[11])){
											temporary.setHour_min_second(strs[11]);
										}else{
											temporary.setHour_min_second("000000");
										}

										temporary.setVirtualAccount((strs.length>=13?strs[12]:""));

										temporary.setPaymentDate((strs.length>=14?strs[13]:""));
										temporary.setPrefectureCity((strs.length>=15?strs[14]:""));
										temporary.setDistrictCounty((strs.length>=16?strs[15]:""));
										temporary.setBusinesshallCode((strs.length>=17?strs[16]:""));
										temporary.setBusinesshallName((strs.length>=18?strs[17]:""));
										temporary.setGroupCode((strs.length>=19?strs[18]:""));
										temporary.setGroupName((strs.length>=20?strs[19]:""));
										temporary.setCustomerNumber((strs.length>=21?strs[20]:""));
										temporary.setCustomerName((strs.length>=22?strs[21]:""));
										temporary.setInsertState("0");
										temporaryList.add(temporary);
		 	            			}
 	            				}
 	            			}
                         }
	                 }
	                 ftpc.logout();
	                 ftpc.disconnect();
					 if(null != temporaryList && temporaryList.size() > 0){
						 Iterator<Moneytotal_Temporary> iterator = temporaryList.iterator();
						 while (iterator.hasNext()) {
							 Moneytotal_Temporary temporary1 = iterator.next();
							 if(temporaryList.indexOf(temporary1)!=temporaryList.lastIndexOf(temporary1) || claimForFundsService.getMoneytotalTemporary(temporary1.getSerialNo())!=null){
								 logger.info("资金认领FTP文件数据重复："+temporary1.getSerialNo());
							 }else {
								 claimForFundsService.saveMoneytotalTemporary(temporary1);
							 }
						 }
					 }
                 }
            }

			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
			Date date = new Date();
			Calendar cal = Calendar.getInstance();
			cal.setTime(date);
			cal.add(Calendar.MONTH, -1);

			String delectDate = sdf.format(cal.getTime());
			int deleteNum = claimForFundsService.deleteTemporary(delectDate);
			logger.info("Class:ClaimForFundsTask Method:downloadFtpFile 资金认领开始删除临时数据 删除："+delectDate+" 之前的数据，删除条数："+deleteNum);

            jobLog.setIsError(false);
			jobLog.setEndTime(new Date());
			jobLogServicer.save(jobLog);
			logger.info("资金认领FTP文件下载结束=============================");
            return "OK";
        } catch (Exception e) {  
        	jobLog.setIsError(true);
			jobLog.setEndTime(new Date());
			jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
			jobLogServicer.save(jobLog);
			logger.error("FTP访问错误2====》"+e.getMessage(),e);
        	e.printStackTrace();
        	return "NO";
        }  
	}


	/**
	 * @Description TODO 资金认领从临时表导入资金数据
	 * <AUTHOR>
	 * @Date 2022/9/2 15:45
	 **/
	public void getMoneytotalByTemporaary(){
		logger.info("开始从临时表导入资金信息");
		List<Moneytotal_Temporary> temporaryList = claimForFundsService.getMoneytotalTemporaryByState();
		logger.info("本次从临时表导入资金总条数为:"+temporaryList.size());
		JobLog jobLog = new JobLog();
		try {
			jobLog.setStartTime(new Date());
			jobLog.setJobName("Class:ClaimForFundsTask Method:downloadFtpFile 资金认领从临时表导入资金数据");
			logger.info("Class:ClaimForFundsTask Method:downloadFtpFile 资金认领从临时表导入资金数据 ："+new Date());
			int s = 0;
			ExecutorService executorService = Executors.newFixedThreadPool(5);
			for (Moneytotal_Temporary temporary:temporaryList){
				//查询资金数据是否已经入表
				List<MoneyTotal> moneyTotal= claimForFundsService.getMoneyTotalSerialNoList(temporary.getSerialNo());
				if (moneyTotal.size()==0){
					//调用财务接口接收数据
					Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("10",temporary.getSerialNo());
					logger.info("财务接口调用结果===>"+result.toString());
					if(ResultCode.SUCCESS.code()==result.getCode()){
						JSONObject res=JSONObject.fromObject(result.getData());
						if("success".equals(res.getString("code"))){

							temporary.setInsertState("3");
							claimForFundsService.updateTemporary(temporary);
							s++;
							if(s>9){
								s=0;
							}
							MoneyTotal m = new MoneyTotal();
							m.setSerialNo(temporary.getSerialNo());//(唯一标识号)
							m.setOtherAccNumber(temporary.getOtherAccNumber());//(对方账号)
							m.setOtherName(temporary.getOtherName());//(对方户名)
							m.setOtherBank(temporary.getOtherBank());//(对方开户行)
							m.setAccNumber(temporary.getAccNumber());//(公司账号)
							m.setTranDate(temporary.getTranDate());//strs[5]//(交易时间)
							m.setAmount(changeY2F(temporary.getAmount()));//(转账金额)
							m.setUseAmount("0");//(使用金额)
							m.setOverAmount(changeY2F(temporary.getAmount()));//(剩余金额)
							m.setSub_overAmount(changeY2F(temporary.getAmount()));
							m.setIs_sub_group(0);
							m.setReceiverSCompany(temporary.getReceiverSCompany());
							m.setMemo(temporary.getMemo());//(摘要注释)
							m.setUseMemo(temporary.getUseMemo());//(用途注释)
							m.setHour_min_second(temporary.getHour_min_second());

							if (temporary.getPaymentDate()!=null && !"".equals(temporary.getPaymentDate())){
								MoneytotalProvisional provisional = new MoneytotalProvisional();
								provisional.setSerialNo(temporary.getSerialNo());
								provisional.setPaymentDate(temporary.getPaymentDate());
								provisional.setPrefectureCity(temporary.getPrefectureCity());
								provisional.setDistrictCounty(temporary.getDistrictCounty());
								provisional.setBusinesshallCode(temporary.getBusinesshallCode());
								provisional.setBusinesshallName(temporary.getBusinesshallName());
								provisional.setGroupCode(temporary.getGroupCode());
								provisional.setGroupName(temporary.getGroupName());
								provisional.setCustomerNumber(temporary.getCustomerNumber());
								provisional.setCustomerName(temporary.getCustomerName());
								provisional = claimForFundsService.saveProvisional(provisional);
								if (provisional!=null){
									m.setProvisionalId(provisional.getId());
								}else {
									logger.info(getStringDatethree(new Date())+"存储："+temporary.getSerialNo()+"暂收款数据失败！");
									m.setProvisionalId(getStringDatethree(new Date())+"存储："+temporary.getSerialNo()+"暂收款数据失败！");
								}
							}

							List<Map<String,Object>> virtualList= null;
							if(temporary.getVirtualAccount()!=null && !"".equals(temporary.getVirtualAccount())){
								virtualList=claimForFundsService.getVirtualAccount(temporary.getVirtualAccount());
								m.setVirtualAccount(temporary.getVirtualAccount());
							}

							String companyCode="";
							String companyName="";
							String batchNo="";
							if(virtualList!=null&&virtualList.size()>0){
								List<Object[]> sone= claimForFundsService.findCodeByCompanyCode(virtualList.get(0).get("COMPANY_CODE").toString());
								if(sone!=null&&sone.size()>0){
									for(int j=0;j<sone.size();j++){
										companyCode=(String) sone.get(j)[0];
										companyName=(String) sone.get(j)[1];
										batchNo=(String) sone.get(j)[2];
									}
									m.setCompanyCode(companyCode);//(地市编码)
									m.setCompanyName(companyName);//(地市名称)
									m.setBatchNo(batchNo+getStringDatethree(new Date())+s);//(系统编号)
								}else{
									List<Object[]> sonet= claimForFundsService.findCodeByRowNo(temporary.getCompanyCode());
									for(int j=0;j<sonet.size();j++){
										companyCode=(String) sonet.get(j)[0];
										companyName=(String) sonet.get(j)[1];
										batchNo=(String) sonet.get(j)[2];
									}
									if("sky".equals(temporary.getCompanyCode())){
										m.setCompanyCode("00");//(地市编码)
										m.setCompanyName("省公司");//(地市名称)
										m.setBatchNo("SGS"+getStringDatethree(new Date())+s);//(系统编号)
									}else{
										m.setCompanyCode(companyCode);//(地市编码)
										m.setCompanyName(companyName);//(地市名称)
										m.setBatchNo(batchNo+getStringDatethree(new Date())+s);//(系统编号)
									}
								}
							}else{
								List<Object[]> sone= claimForFundsService.findCodeByRowNo(temporary.getCompanyCode());
								for(int j=0;j<sone.size();j++){
									companyCode=(String) sone.get(j)[0];
									companyName=(String) sone.get(j)[1];
									batchNo=(String) sone.get(j)[2];
								}
								if("sky".equals(temporary.getCompanyCode())){
									m.setCompanyCode("00");//(地市编码)
									m.setCompanyName("省公司");//(地市名称)
									m.setBatchNo("SGS"+getStringDatethree(new Date())+s);//(系统编号)
								}else{
									m.setCompanyCode(companyCode);//(地市编码)
									m.setCompanyName(companyName);//(地市名称)
									m.setBatchNo(batchNo+getStringDatethree(new Date())+s);//(系统编号)
								}
							}

							m.setCreateDate(new Date());//(创建时间)
							m.setState(0);//(状态)
							m.setIsThe(0);//记录申领次数；
							List<MoneyTotal> mt = claimForFundsService.getMoneyTotalTypeUserid(temporary.getOtherAccNumber());
							if(mt.size()==0){
								m.setType(0);
								m.setTypeUserId(0);
							}else{
								if(mt.get(0).getUserid()==null){
									m.setType(0);
									m.setTypeUserId(0);
								}else{
									m.setType(2);
									m.setTypeUserId(mt.get(0).getUserid());
									JSONObject obj=taskService.getInitial_message(temporary.getAmount(), "",mt.get(0).getUserid());
									if(obj!=null){
										taskService.setReminder_information_tbl(companyCode,"1","32", "资金认领", obj.toString(), "当前资金你已经认领过的");
									}else{
										logger.info("下载资金时提醒信息异常："+obj+"，用编号："+mt.get(0).getUserid());
									}
								}
							}

							//账户绑定集团信息
							JSONArray array= queryCustInfoByBank(temporary.getOtherAccNumber());
							//财务通用账户(********已删除财务通用账户数据)
							Map<String,String> accountMap = claimForFundsService.getUnlimitedAccountByNumber(temporary.getOtherAccNumber());
							//判断是否为财务通用账户  账户是否绑定集团
							if(accountMap==null && array.size()>0 ){
								//判断是否为自动认领
								if (array.size()==1 && "1".equals(array.getJSONObject(0).getString("BANK_ACCOUNT_STATUS"))){
									JSONObject json = array.getJSONObject(0);
									//查询代理集团信息
									logger.info("根据集团："+json.getString("CUST_CODE")+" 查询代理集团信息");
									List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDetByGroup(json.getString("CUST_CODE"));
									logger.info("集团："+json.getString("CUST_CODE")+" 代理总数为："+replaceModelDets.size());
									if(replaceModelDets.size()>0){
										//存在代理集团信息  进行预占
										List<Taxpayer> taxpayerList = new ArrayList<>();
										for (ClaimReplaceModelDet replaceModelDet : replaceModelDets) {
											List<Map<String, Object>> userLists = claimForFundsService.getCountyByUserID(replaceModelDet.getCreationRow().toString());
											if (userLists.size() > 0 && userLists.get(0).get("BOSSUSERNAME") != null) {
												String code = String.valueOf(userLists.get(0).get("COMPANY_CODE"));
												if ("00".equals(code)) {
													code = "01";
												}
												if (code.equals(m.getCompanyCode())) {
													Taxpayer taxpayer = new Taxpayer();
													taxpayer.setBatchNo(m.getBatchNo());
													taxpayer.setSerialNo(m.getSerialNo());
													taxpayer.setOtherAccNumber(m.getOtherAccNumber());
													taxpayer.setGroupCode(json.getString("CUST_CODE"));
													taxpayer.setGroupName(json.getString("CUST_NAME"));
													taxpayer.setUserId(replaceModelDet.getCreationRow());
													taxpayer.setUserBossName((String) userLists.get(0).get("BOSSUSERNAME"));
													taxpayer.setCreateDate(new Date());
													taxpayer.setState(0);
													taxpayer.setAccountState(3);
													taxpayer.setReplcaceId(replaceModelDet.getId());
													taxpayerList.add(taxpayer);
												}
											}
										}

										for (Taxpayer taxpayer:taxpayerList) {
											claimForFundsService.saveTaxpayer(taxpayer);
										}
										if (taxpayerList.size()>0){
											m.setState(6);
										}

									}else {
										//自动认领   账户名称和集团名称完成一致
										if(temporary.getOtherName().equals(json.getString("CUST_NAME"))){
											logger.info("根据【"+json.getString("BOSS")+"】查询用户");
											SystemUser user=claimForFundsService.querUsersByBossNo(json.getString("BOSS"));
											if(user!=null){
												logger.info("用户地市："+user.getSystemDept().get(0).getSystemCompany().getCompanyCode()+"，资金地市："+m.getCompanyCode());
												String code = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
												boolean flag;
												if ("00".equals(code)){
													flag = ("00".equals(m.getCompanyCode()) || "01".equals(m.getCompanyCode()));
												}else {
													flag = code.equals(m.getCompanyCode());
												}
												//集团归属用户地市和资金地市一致
												if (flag){
													Integer pushUserRow = user.getRowNo();
													String pushUserName = user.getBossUserName();
													try {
														List<Map<String, Object>> userMap = claimForFundsService.findDept(user.getRowNo());
														String county_name = userMap.get(0).get("TWODNAME").toString();
														String company_name = userMap.get(0).get("COMPANY_NAME").toString();
														List<Map<String, String>> sd = claimForFundsService.SelectZtreeByUId("ROLE_ZJRLSPGLY", company_name, county_name);
														if (sd.size() == 1) {
															if (!"".equals(sd.get(0).get("BOSSUSERNAME")) && sd.get(0).get("BOSSUSERNAME") != null && !"null".equals(sd.get(0).get("BOSSUSERNAME"))) {
																pushUserRow = Integer.parseInt(sd.get(0).get("ROWNO"));
																pushUserName = sd.get(0).get("BOSSUSERNAME");
															}
														}
													} catch (Exception e) {
														e.printStackTrace();
														pushUserRow = user.getRowNo();
														pushUserName = user.getBossUserName();
													}

													//推送认领信息到财务和boss
													Map<String,String> map=pushBossMethod(m,pushUserName,json.getString("CUST_CODE"));
													if("1".equals(map.get("code"))){
														m.setGroupCode(json.getString("CUST_CODE"));
														m.setGroupName(json.getString("CUST_NAME"));
														m.setUserid(user.getRowNo());
														m.setIsThe(m.getIsThe() + 1);
														m.setState(-3);
														m.setBoss_State(1); //设置BOSS状态为成功
														m.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
														m.setPushDate(new Date());
														m.setInformation("["+DateUtil.getDate()+"]系统认领成功");
														m.setPushUserName(pushUserRow);
														m.setPushBossUserName(pushUserName);
														JSONObject object = new JSONObject();
														object.put("unitName",json.getString("CUST_NAME"));		//自动划拨集团名称
														object.put("unitNo",json.getString("CUST_CODE"));			//自动划拨集团编号
														object.put("amount",String.valueOf((Double.parseDouble(m.getAmount())/100)));											//划拨金额
														if (!user.getMobile().isEmpty()){
															claimForFundsService.sendPushMessage(object.toString(),user.getMobile(),"20409150");//这里原本数据是20421151
															logger.info("资金自动认领短信提醒:短信信息"+object.toString()+",接收人手机号:"+user.getMobile());
														}else {
															logger.info("用户:"+user.getRowNo()+" 手机号异常,短信["+object.toString()+"]提醒失败!");
														}

														//自动缴费
														if (claimForFundsService.saveProcessList(m)!=null){
															temporary.setInsertState("1");
															claimForFundsService.updateMoneytotalTemporary(temporary);
															executorService.submit(new ClaimAutomaticPaymentThread(claimForFundsService,groupAccountService,taskService,m,user));
															continue;
														}
													}else{//失败
														if("2".equals(map.get("code"))){//资金入账BOSS失败
															Map<String,String> updateMap=updateIncomeState(m.getSerialNo(),m.getBatchNo(),user.getBossUserName());//结束预占，更改为未使用状态
															if("2".equals(updateMap.get("code"))){
																m.setInformation("["+DateUtil.getDate()+"]"+updateMap.get("msg"));
															}else if("3".equals(updateMap.get("code"))){
																m.setInformation("["+DateUtil.getDate()+"]"+updateMap.get("msg"));
															}else{
																m.setInformation("["+DateUtil.getDate()+"]"+map.get("msg"));
															}
														}else if("3".equals(map.get("code"))){//调用资金入账BOSS接口请求失败
															Map<String,String> updateMap=updateIncomeState(m.getSerialNo(),m.getBatchNo(),user.getBossUserName());//结束预占，更改为未使用状态
															if("2".equals(updateMap.get("code"))){
																m.setInformation("["+DateUtil.getDate()+"]"+updateMap.get("msg"));
															}else if("3".equals(updateMap.get("code"))){
																m.setInformation("["+DateUtil.getDate()+"]"+updateMap.get("msg"));
															}else{
																m.setInformation("["+DateUtil.getDate()+"]"+map.get("msg"));
															}
														}else if("4".equals(map.get("code"))){//财务预占失败
															m.setInformation("["+DateUtil.getDate()+"]"+map.get("msg"));
														}else if("5".equals(map.get("code"))){//请求财务预占接口请求失败
															m.setInformation("["+DateUtil.getDate()+"]"+map.get("msg"));
														}
													}
												}
											}
										}
									}
								}else {
									List<Taxpayer> taxpayerList = new ArrayList<>();
									//循环账户绑定集团
									for (int t = 0;t<array.size();t++){
										JSONObject json = array.getJSONObject(t);
										//查询集团是否有被代理
										logger.info("根据集团："+json.getString("CUST_CODE")+" 查询代理集团信息");
										List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDetByGroup(json.getString("CUST_CODE"));
										logger.info("集团："+json.getString("CUST_CODE")+" 代理总数为："+replaceModelDets.size());
										if(replaceModelDets.size()>0){
											//循环代理用户信息
											for (int r = 0;r<replaceModelDets.size();r++){
												ClaimReplaceModelDet replaceModelDet = replaceModelDets.get(r);
												List<Map<String,Object>> userLists=claimForFundsService.getCountyByUserID(replaceModelDet.getCreationRow().toString());
												if (userLists.size()>0 && userLists.get(0).get("BOSSUSERNAME")!=null){
													String code = String.valueOf(userLists.get(0).get("COMPANY_CODE"));
													if ("00".equals(code)){
														code = "01";
													}
													if (code.equals(m.getCompanyCode())){
														Taxpayer taxpayer = new Taxpayer();
														taxpayer.setBatchNo(m.getBatchNo());
														taxpayer.setSerialNo(m.getSerialNo());
														taxpayer.setOtherAccNumber(m.getOtherAccNumber());
														taxpayer.setGroupCode(json.getString("CUST_CODE"));
														taxpayer.setGroupName(json.getString("CUST_NAME"));
														taxpayer.setUserId(replaceModelDet.getCreationRow());
														taxpayer.setUserBossName((String) userLists.get(0).get("BOSSUSERNAME"));
														taxpayer.setCreateDate(new Date());
														taxpayer.setState(0);
														taxpayer.setAccountState(3);
														taxpayer.setReplcaceId(replaceModelDet.getId());
														taxpayerList.add(taxpayer);
													}
												}
											}
										}else {
											logger.info("集团未被代理 根据归属工号【"+json.getString("BOSS")+"】查询用户");
											SystemUser user=claimForFundsService.querUsersByBossNo(json.getString("BOSS"));
											if(user!=null){
												logger.info("用户地市："+user.getSystemDept().get(0).getSystemCompany().getCompanyCode()+"，资金地市："+m.getCompanyCode());
												String code = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
												if ("00".equals(code)){
													code = "01";
												}
												if (code.equals(m.getCompanyCode())){
													Taxpayer taxpayer = new Taxpayer();
													taxpayer.setBatchNo(m.getBatchNo());
													taxpayer.setSerialNo(m.getSerialNo());
													taxpayer.setOtherAccNumber(m.getOtherAccNumber());
													taxpayer.setGroupCode(json.getString("CUST_CODE"));
													taxpayer.setGroupName(json.getString("CUST_NAME"));
													taxpayer.setUserId(user.getRowNo());
													taxpayer.setUserBossName(user.getBossUserName());
													taxpayer.setCreateDate(new Date());
													taxpayer.setState(0);
													taxpayer.setAccountState(json.getInt("BANK_ACCOUNT_STATUS"));
													taxpayerList.add(taxpayer);
												}
											}
										}
									}

									for (Taxpayer taxpayer:taxpayerList) {
										claimForFundsService.saveTaxpayer(taxpayer);
									}
									if (taxpayerList.size()>0){
										m.setState(6);
									}
								}
							}
							if (claimForFundsService.saveProcessList(m)!=null){
								temporary.setInsertState("1");
								claimForFundsService.updateMoneytotalTemporary(temporary);
							}
						}else {
							temporary.setInsertState("-2");
							temporary.setInsertMsg("财务接口反馈异常:"+res.getString("info"));
							claimForFundsService.updateMoneytotalTemporary(temporary);
						}
					}else {
						temporary.setInsertState("-2");
						temporary.setInsertMsg("财务接口调用失败："+result.getMessage());
						claimForFundsService.updateMoneytotalTemporary(temporary);
					}
				}else {
					temporary.setInsertState("2");
					claimForFundsService.updateMoneytotalTemporary(temporary);
				}
			}

			jobLog.setIsError(false);
			jobLog.setEndTime(new Date());
			jobLogServicer.save(jobLog);
			logger.info("Class:ClaimForFundsTask Method:downloadFtpFile 资金认领从临时表导入资金信息结束 ："+new Date());
		}catch (Exception e){
			jobLog.setIsError(true);
			jobLog.setEndTime(new Date());
			jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+" : "+e.getLocalizedMessage());
			jobLogServicer.save(jobLog);
			logger.error("资金认领从临时表导入资金信息异常："+e.getMessage(),e);
			e.printStackTrace();
		}
	}

	/***
	 * 方法描述: 资金认领自动缴费(202411停止使用)
	 * @开发人员 TangXiao
	 * @版本编号 1.7
	 * @开发时间 2023/11/11 17:04
	 * @return com.xinxinsoft.entity.claimForFunds.MoneyTotal
	 **/
	private MoneyTotal automaticBillPayment(Moneytotal_Temporary temporary,MoneyTotal m,JSONObject json,SystemUser user){
		//判断是否需要自动缴费
		try{
			Integer amout = Integer.parseInt(m.getAmount());
			int num = 0;
			String PhoneNo = taskService.getNumber();
			Date CreateTime = new Date();
			String paymentValue = numberIsVerified(m.getMemo(),json.getString("CUST_CODE"),user.getBossUserName());
			if (!"".equals(paymentValue)){
				MoneyPeyMent peyMent = claimForFundsService.queryMoneyPeyMent(json.getString("CUST_CODE"));

				String IBM = "";
				List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
				for (Object[] objects : sone) {
					IBM = (String) objects[2];
				}
				MoneyApply myly = new MoneyApply();
				myly.setApplyNo(IBM+"ZD"+taskService.getNumber());
				myly.setTitle("基于客户备注"+(m.getMemo().contains("充值卡")?"充值卡":"缴费码")+"："+paymentValue+"信息，系统自动生成工单");
				myly.setApplyMemo("基于客户备注"+(m.getMemo().contains("充值卡")?"充值卡":"缴费码")+"："+paymentValue+"信息，系统自动生成工单");
				myly.setCreatorId(String.valueOf(user.getRowNo()));
				myly.setCreatorName(user.getEmployeeName());
				myly.setCreateDate(new Date());
				myly.setGroupCode(json.getString("CUST_CODE"));
				myly.setGroupName(json.getString("CUST_NAME"));
				myly.setOpType("1");
				myly.setSerialNo(m.getSerialNo());
				myly.setMoneyTotal_id(m.getId());

				if (m.getMemo().contains("充值卡")){
					ValuableCardDet valuableCardDet = claimForFundsService.getValuableCarCompareByDate(paymentValue);
					if (valuableCardDet!=null){
						Integer cardAmout = Integer.parseInt(valuableCardDet.getOrderPrice())-Integer.parseInt(valuableCardDet.getMoneyPayPrice());
						if (amout>cardAmout){
							amout = cardAmout;
						}
						Integer useAmout = 0;

						if (peyMent!=null && "1".equals(peyMent.getPayByCard())){
							do {
								MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
								moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
								moneyApplyDet.setApplyNo(myly.getApplyNo());
								moneyApplyDet.setSerialNo(m.getSerialNo());

								moneyApplyDet.setGroupCode(myly.getGroupCode());
								moneyApplyDet.setGroupName(myly.getGroupName());

								moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
								moneyApplyDet.setCreateDate(CreateTime);

								moneyApplyDet.setOpType("3");
								moneyApplyDet.setOrderType("2");

								moneyApplyDet.setContrctNo(paymentValue);
								moneyApplyDet.setUseType("06");
								moneyApplyDet.setLateFee("0");
								moneyApplyDet.setLateFeeMoney("0");
								moneyApplyDet.setInvNo(valuableCardDet.getInvNo());

								if (amout>2500000){
									moneyApplyDet.setAmount("2500000");
									amout-= 2500000;
								}else {
									moneyApplyDet.setAmount(String.valueOf(amout));
									amout = 0;
								}
								moneyApplyDet.setPhoneNo(PhoneNo);

								String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
								Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(user.getBossUserName(), json.getString("CUST_CODE"), "03", moneyApplyDet.getMoneyNo(), paymentValue,
										"G", moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount(), "0", user.getBossUserName(), m.getUseMemo(), "-",
										m.getOtherAccNumber(), m.getOtherName(), "", m.getMemo(), busi_fee, "06",
										moneyApplyDet.getMoneyNo(), "0", moneyApplyDet.getInvNo(), "","0","","","");
								logger.info(moneyApplyDet.getMoneyNo()+"自动缴费有价卡推送结果："+applyRes.toString());
								if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
									JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
									JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
									//循环推送申请工单中的明细记录，成功并记录成功和失败数据
									if("0".equals(ROOT.getString("RETURN_CODE"))){
										useAmout += Integer.parseInt(moneyApplyDet.getAmount());
										moneyApplyDet.setState("1");
										moneyApplyDet.setBossState("0");
										moneyApplyDet.setPushDate(new Date());
										JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
										if (OUT_DATA.has("PAYMENT_ACCEPT")){
											if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
												moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
												InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
												invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
												invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
												claimForFundsService.addInvoiceMiddle(invoiceMiddle);
											}
										}
									}else {
										moneyApplyDet.setState("-1");
										moneyApplyDet.setBossState("-1");
										moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG").length()<200?ROOT.getString("RETURN_MSG"):ROOT.getString("RETURN_MSG").substring(0,200));
									}
								}else{
									moneyApplyDet.setState("-1");
									moneyApplyDet.setBossState("-1");
									//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
									moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
								}
								claimForFundsService.addMoneyApplyDet(moneyApplyDet);

								num+=1;
							}while (amout>0);
							m.setUseAmount(String.valueOf(useAmout));//(使用金额)
							m.setOverAmount(String.valueOf((Integer.parseInt(m.getAmount())-useAmout)));//(剩余金额)

							myly.setLateFeeMoney("0");
							myly.setApplyAmount(String.valueOf(useAmout));
							myly.setState("0");
							claimForFundsService.addMoneyApply(myly);
							updateValuableCard(valuableCardDet.getInvNo(),paymentValue,String.valueOf(useAmout),"SAVA");
						}else {
							myly.setLateFeeMoney("0");
							myly.setApplyAmount(String.valueOf(amout));
							myly.setState("-2");
							MoneyApply my = claimForFundsService.addMoneyApply(myly);

							do {
								MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
								moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
								moneyApplyDet.setApplyNo(my.getApplyNo());
								moneyApplyDet.setSerialNo(m.getSerialNo());

								moneyApplyDet.setGroupCode(my.getGroupCode());
								moneyApplyDet.setGroupName(my.getGroupName());

								moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
								moneyApplyDet.setCreateDate(CreateTime);

								moneyApplyDet.setOpType("3");
								moneyApplyDet.setOrderType("2");
								moneyApplyDet.setState("-1");		//未完成：审批中状态

								moneyApplyDet.setContrctNo(paymentValue);
								moneyApplyDet.setUseType("06");
								moneyApplyDet.setLateFee("0");
								moneyApplyDet.setLateFeeMoney("0");
								moneyApplyDet.setInvNo(valuableCardDet.getInvNo());

								if (amout>2500000){
									moneyApplyDet.setAmount("2500000");
									amout-= 2500000;
								}else {
									moneyApplyDet.setAmount(String.valueOf(amout));
									amout = 0;
								}
								moneyApplyDet.setPhoneNo(PhoneNo);
								claimForFundsService.addMoneyApplyDet(moneyApplyDet);

								num+=1;
							}while (amout>0);
						}
					}
				}else {
					Result result = ClaimFundsOpenSrv.getInstance().sOweFeeQry("",paymentValue,user.getBossUserName(),"1");
					if(ResultCode.SUCCESS.code()==result.getCode()){
						JSONObject resDateTwo=JSONObject.fromObject(result.getData());
						JSONObject rootDateTwo=JSONObject.fromObject(resDateTwo.get("ROOT"));
						if(rootDateTwo.getInt("RETURN_CODE")==0){
							if(rootDateTwo.has("OUT_DATA")){
								JSONObject outDataTwo = rootDateTwo.getJSONObject("OUT_DATA");
								Integer latefeeMoney = outDataTwo.getInt("DELAY_FEE");
								Integer oweFee = outDataTwo.getInt("OWE_FEE");

								JSONObject preinvInfo = null;
								Result preInvResult = ClaimFundsOpenSrv.getInstance().s8000ValidPreInv("",paymentValue,user.getBossUserName());
								if(ResultCode.SUCCESS.code()==preInvResult.getCode()){
									JSONObject resDate=JSONObject.fromObject(preInvResult.getData());
									JSONObject rootDate=JSONObject.fromObject(resDate.get("ROOT"));
									if(rootDate.getInt("RETURN_CODE")==0){
										if(rootDate.has("OUT_DATA")){
											JSONObject outData = rootDate.getJSONObject("OUT_DATA");
											JSONObject preinvList = outData.getJSONObject("PREINV_LIST");
											preinvInfo = preinvList.getJSONObject("PREINV_INFO");
											if (preinvInfo.isArray()){
												JSONArray array = preinvInfo.getJSONArray("PREINV_INFO");
												for (int i = 0; i < array.size(); i++) {
													if ("1".equals( array.getJSONObject(i).getString("PRE_INV_STATE"))){
														preinvInfo = array.getJSONObject(i);
														break;
													}
												}
											}
										}
									}
								}


								if (preinvInfo!=null){
									if (amout>preinvInfo.getInt("PRE_SHOULD_PAY")){
										amout = preinvInfo.getInt("PRE_SHOULD_PAY");
									}

									if (latefeeMoney>0 || peyMent==null || "0".equals(peyMent.getPayByPreinvoicing())){
										myly.setLateFeeMoney(String.valueOf(latefeeMoney));
										myly.setApplyAmount(String.valueOf(amout));
										myly.setState("-2");
										MoneyApply my = claimForFundsService.addMoneyApply(myly);

										do {
											MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
											moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
											moneyApplyDet.setApplyNo(my.getApplyNo());
											moneyApplyDet.setSerialNo(m.getSerialNo());

											moneyApplyDet.setGroupCode(my.getGroupCode());
											moneyApplyDet.setGroupName(my.getGroupName());

											moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
											moneyApplyDet.setCreateDate(CreateTime);

											moneyApplyDet.setOpType("3");
											moneyApplyDet.setOrderType("2");
											moneyApplyDet.setState("-1");		//未完成：审批中状态

											moneyApplyDet.setContrctNo(paymentValue);
											moneyApplyDet.setUseType("05");
											moneyApplyDet.setLateFee((latefeeMoney>0?"1.00":"0"));
											moneyApplyDet.setLateFeeMoney(String.valueOf(latefeeMoney));
											moneyApplyDet.setInvNo(preinvInfo.getString("PRE_ORDER_ID"));

											if (amout>9999900){
												moneyApplyDet.setAmount("9999900");
												amout-= 9999900;
											}else {
												moneyApplyDet.setAmount(String.valueOf(amout));
												amout = 0;
											}
											moneyApplyDet.setPhoneNo(PhoneNo);
											claimForFundsService.addMoneyApplyDet(moneyApplyDet);

											num+=1;
										}while (amout>0);
									}else {
										Integer useAmout = 0;
										do {
											MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
											moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
											moneyApplyDet.setApplyNo(myly.getApplyNo());
											moneyApplyDet.setSerialNo(m.getSerialNo());

											moneyApplyDet.setGroupCode(myly.getGroupCode());
											moneyApplyDet.setGroupName(myly.getGroupName());

											moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
											moneyApplyDet.setCreateDate(CreateTime);

											moneyApplyDet.setOpType("3");
											moneyApplyDet.setOrderType("2");

											moneyApplyDet.setContrctNo(paymentValue);
											moneyApplyDet.setUseType("05");
											moneyApplyDet.setLateFee("0");
											moneyApplyDet.setLateFeeMoney("0");
											moneyApplyDet.setInvNo(preinvInfo.getString("PRE_ORDER_ID"));

											if (amout>9999900){
												moneyApplyDet.setAmount("9999900");
												amout-= 9999900;
											}else {
												moneyApplyDet.setAmount(String.valueOf(amout));
												amout = 0;
											}
											moneyApplyDet.setPhoneNo(PhoneNo);

											String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
											Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(user.getBossUserName(), json.getString("CUST_CODE"), "03", moneyApplyDet.getMoneyNo(), paymentValue,
													"G", moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount(), "0", user.getBossUserName(), m.getUseMemo(), "-",
													m.getOtherAccNumber(), m.getOtherName(), "", m.getMemo(), busi_fee, "05",
													moneyApplyDet.getMoneyNo(), moneyApplyDet.getLateFee(), moneyApplyDet.getInvNo(), "","0","","","");
											logger.info(moneyApplyDet.getMoneyNo()+"自动缴费预开票推送结果："+applyRes.toString());
											if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
												JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
												JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
												//循环推送申请工单中的明细记录，成功并记录成功和失败数据
												if("0".equals(ROOT.getString("RETURN_CODE"))){
													useAmout += Integer.parseInt(moneyApplyDet.getAmount());
													moneyApplyDet.setState("1");
													moneyApplyDet.setBossState("0");
													moneyApplyDet.setPushDate(new Date());
													JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
													if (OUT_DATA.has("PAYMENT_ACCEPT")){
														if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
															moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
															InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
															invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
															invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
															claimForFundsService.addInvoiceMiddle(invoiceMiddle);
														}
													}
												}else {
													moneyApplyDet.setState("-1");
													moneyApplyDet.setBossState("-1");
													moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG").length()<200?ROOT.getString("RETURN_MSG"):ROOT.getString("RETURN_MSG").substring(0,200));
												}
											}else{
												moneyApplyDet.setState("-1");
												moneyApplyDet.setBossState("-1");
												//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
												moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
											}
											claimForFundsService.addMoneyApplyDet(moneyApplyDet);

											num+=1;
										}while (amout>0);
										m.setUseAmount(String.valueOf(useAmout));//(使用金额)
										m.setOverAmount(String.valueOf((Integer.parseInt(m.getAmount())-useAmout)));//(剩余金额)

										myly.setLateFeeMoney("0");
										myly.setApplyAmount(String.valueOf(useAmout));
										myly.setState("0");
										claimForFundsService.addMoneyApply(myly);
									}
								}else {
									if (oweFee>0){
										if (amout>oweFee){
											amout = oweFee;
										}

										if (latefeeMoney>0 || peyMent==null ||"0".equals(peyMent.getPayByArrears())){
											myly.setLateFeeMoney(String.valueOf(latefeeMoney));
											myly.setApplyAmount(String.valueOf(amout));
											myly.setState("-2");
											MoneyApply my = claimForFundsService.addMoneyApply(myly);

											do {
												MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
												moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
												moneyApplyDet.setApplyNo(my.getApplyNo());
												moneyApplyDet.setSerialNo(m.getSerialNo());

												moneyApplyDet.setGroupCode(my.getGroupCode());
												moneyApplyDet.setGroupName(my.getGroupName());

												moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
												moneyApplyDet.setCreateDate(CreateTime);

												moneyApplyDet.setOpType("3");
												moneyApplyDet.setOrderType("2");
												moneyApplyDet.setState("-1");		//未完成：审批中状态

												moneyApplyDet.setContrctNo(paymentValue);
												moneyApplyDet.setUseType("1");
												moneyApplyDet.setLateFee((latefeeMoney>0?"1.00":"0"));
												moneyApplyDet.setLateFeeMoney(String.valueOf(latefeeMoney));
												moneyApplyDet.setInvNo("");

												if (amout>9999900){
													moneyApplyDet.setAmount("9999900");
													amout-= 9999900;
												}else {
													moneyApplyDet.setAmount(String.valueOf(amout));
													amout = 0;
												}
												moneyApplyDet.setPhoneNo(PhoneNo);
												claimForFundsService.addMoneyApplyDet(moneyApplyDet);

												num+=1;
											}while (amout>0);
										}else {
											Integer useAmout = 0;
											do {
												MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
												moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
												moneyApplyDet.setApplyNo(myly.getApplyNo());
												moneyApplyDet.setSerialNo(m.getSerialNo());

												moneyApplyDet.setGroupCode(myly.getGroupCode());
												moneyApplyDet.setGroupName(myly.getGroupName());

												moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
												moneyApplyDet.setCreateDate(CreateTime);

												moneyApplyDet.setOpType("3");
												moneyApplyDet.setOrderType("2");

												moneyApplyDet.setContrctNo(paymentValue);
												moneyApplyDet.setUseType("1");
												moneyApplyDet.setLateFee("0");
												moneyApplyDet.setLateFeeMoney("0");
												moneyApplyDet.setInvNo("");

												if (amout>9999900){
													moneyApplyDet.setAmount("9999900");
													amout-= 9999900;
												}else {
													moneyApplyDet.setAmount(String.valueOf(amout));
													amout = 0;
												}
												moneyApplyDet.setPhoneNo(PhoneNo);

												String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
												Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(user.getBossUserName(), json.getString("CUST_CODE"), "03", moneyApplyDet.getMoneyNo(), paymentValue,
														"G", moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount(), "0", user.getBossUserName(), m.getUseMemo(), "-",
														m.getOtherAccNumber(), m.getOtherName(), "", m.getMemo(), busi_fee, "00",
														moneyApplyDet.getMoneyNo(), moneyApplyDet.getLateFee(), moneyApplyDet.getInvNo(), "","0","","","");
												logger.info(moneyApplyDet.getMoneyNo()+"自动缴费推送结果："+applyRes.toString());
												if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
													JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
													JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
													//循环推送申请工单中的明细记录，成功并记录成功和失败数据
													if("0".equals(ROOT.getString("RETURN_CODE"))){
														useAmout += Integer.parseInt(moneyApplyDet.getAmount());
														moneyApplyDet.setState("1");
														moneyApplyDet.setBossState("0");
														moneyApplyDet.setPushDate(new Date());
														JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
														if (OUT_DATA.has("PAYMENT_ACCEPT")){
															if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
																moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
																InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
																invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
																invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
																claimForFundsService.addInvoiceMiddle(invoiceMiddle);
															}
														}
													}else {
														moneyApplyDet.setState("-1");
														moneyApplyDet.setBossState("-1");
														moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG").length()<200?ROOT.getString("RETURN_MSG"):ROOT.getString("RETURN_MSG").substring(0,200));
													}
												}else{
													moneyApplyDet.setState("-1");
													moneyApplyDet.setBossState("-1");
													//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
													moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
												}
												claimForFundsService.addMoneyApplyDet(moneyApplyDet);

												num+=1;
											}while (amout>0);
											m.setUseAmount(String.valueOf(useAmout));//(使用金额)
											m.setOverAmount(String.valueOf((Integer.parseInt(m.getAmount())-useAmout)));//(剩余金额)

											myly.setLateFeeMoney("0");
											myly.setApplyAmount(String.valueOf(useAmout));
											myly.setState("0");
											claimForFundsService.addMoneyApply(myly);
										}
									}else {
										if (latefeeMoney>0 || peyMent==null ||"0".equals(peyMent.getPayByPredeposit())){
											myly.setLateFeeMoney(String.valueOf(latefeeMoney));
											myly.setApplyAmount(String.valueOf(amout));
											myly.setState("-2");
											MoneyApply my = claimForFundsService.addMoneyApply(myly);

											do {
												MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
												moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
												moneyApplyDet.setApplyNo(my.getApplyNo());
												moneyApplyDet.setSerialNo(m.getSerialNo());

												moneyApplyDet.setGroupCode(my.getGroupCode());
												moneyApplyDet.setGroupName(my.getGroupName());

												moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
												moneyApplyDet.setCreateDate(CreateTime);

												moneyApplyDet.setOpType("3");
												moneyApplyDet.setOrderType("2");
												moneyApplyDet.setState("-1");		//未完成：审批中状态

												moneyApplyDet.setContrctNo(paymentValue);
												moneyApplyDet.setUseType("1");
												moneyApplyDet.setLateFee((latefeeMoney>0?"1.00":"0"));
												moneyApplyDet.setLateFeeMoney(String.valueOf(latefeeMoney));
												moneyApplyDet.setInvNo("");

												if (amout>9999900){
													moneyApplyDet.setAmount("9999900");
													amout-= 9999900;
												}else {
													moneyApplyDet.setAmount(String.valueOf(amout));
													amout = 0;
												}
												moneyApplyDet.setPhoneNo(PhoneNo);
												claimForFundsService.addMoneyApplyDet(moneyApplyDet);

												num+=1;
											}while (amout>0);
										}else {
											Integer useAmout = 0;
											do {
												MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
												moneyApplyDet.setMoneyNo("JTZD"+PhoneNo+ num);
												moneyApplyDet.setApplyNo(myly.getApplyNo());
												moneyApplyDet.setSerialNo(m.getSerialNo());

												moneyApplyDet.setGroupCode(myly.getGroupCode());
												moneyApplyDet.setGroupName(myly.getGroupName());

												moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
												moneyApplyDet.setCreateDate(CreateTime);

												moneyApplyDet.setOpType("3");
												moneyApplyDet.setOrderType("2");

												moneyApplyDet.setContrctNo(paymentValue);
												moneyApplyDet.setUseType("1");
												moneyApplyDet.setLateFee("0");
												moneyApplyDet.setLateFeeMoney("0");
												moneyApplyDet.setInvNo("");

												if (amout>9999900){
													moneyApplyDet.setAmount("9999900");
													amout-= 9999900;
												}else {
													moneyApplyDet.setAmount(String.valueOf(amout));
													amout = 0;
												}
												moneyApplyDet.setPhoneNo(PhoneNo);

												String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
												Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(user.getBossUserName(), json.getString("CUST_CODE"), "03", moneyApplyDet.getMoneyNo(), paymentValue,
														"G", moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount(), "0", user.getBossUserName(), m.getUseMemo(), "-",
														m.getOtherAccNumber(), m.getOtherName(), "", m.getMemo(), busi_fee, "00",
														moneyApplyDet.getMoneyNo(), moneyApplyDet.getLateFee(), moneyApplyDet.getInvNo(), "","0","","","");
												logger.info(moneyApplyDet.getMoneyNo()+"自动缴费推送结果："+applyRes.toString());
												if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
													JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
													JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
													//循环推送申请工单中的明细记录，成功并记录成功和失败数据
													if("0".equals(ROOT.getString("RETURN_CODE"))){
														useAmout += Integer.parseInt(moneyApplyDet.getAmount());
														moneyApplyDet.setState("1");
														moneyApplyDet.setBossState("0");
														moneyApplyDet.setPushDate(new Date());
														JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
														if (OUT_DATA.has("PAYMENT_ACCEPT")){
															if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
																moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
																InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
																invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
																invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
																claimForFundsService.addInvoiceMiddle(invoiceMiddle);
															}
														}
													}else {
														moneyApplyDet.setState("-1");
														moneyApplyDet.setBossState("-1");
														moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG").length()<200?ROOT.getString("RETURN_MSG"):ROOT.getString("RETURN_MSG").substring(0,200));
													}
												}else{
													moneyApplyDet.setState("-1");
													moneyApplyDet.setBossState("-1");
													//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
													moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
												}
												claimForFundsService.addMoneyApplyDet(moneyApplyDet);

												num+=1;
											}while (amout>0);
											m.setUseAmount(String.valueOf(useAmout));//(使用金额)
											m.setOverAmount(String.valueOf((Integer.parseInt(m.getAmount())-useAmout)));//(剩余金额)

											myly.setLateFeeMoney("0");
											myly.setApplyAmount(String.valueOf(useAmout));
											myly.setState("0");
											claimForFundsService.addMoneyApply(myly);
										}
									}
								}
							}
						}
					}
				}
			}
		}catch (Exception e){
			m.setState(-3);
			m.setInformation("自动认领缴费失败："+e.getMessage());
		}
		return m;
	}


	public Map<String,String> pushBossMethod(MoneyTotal moneyTotal,String pushUserName,String groupCode){
		//用于判断当前资金是否推送财务状态为暂挂
		Map<String,String> map = new HashMap<>();
		Result IncomeResult = ClaimFundsOpenSrv.getInstance().updateIncomeState("5", moneyTotal.getSerialNo());
		if (ResultCode.SUCCESS.code() == IncomeResult.getCode()) {//判断当前请求是否成功
			JSONObject res = JSONObject.fromObject(IncomeResult.getData());
			//调整财务的状态为已使用状态。
			if ("success".equals(res.getString("code"))) {
				String op_fee = BigDecimal.valueOf(Long.parseLong(moneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
				Result accountRes = ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(pushUserName, groupCode,
						moneyTotal.getBatchNo()==null?"":moneyTotal.getBatchNo(),
						moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber(),
						moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName(),
						moneyTotal.getUseMemo()==null?"":moneyTotal.getUseMemo(),
						moneyTotal.getSerialNo()==null?"无":moneyTotal.getSerialNo(),
						op_fee);
				logger.info("job调用资金入账接口反馈===>" + accountRes.toString());
				if (ResultCode.SUCCESS.code() == accountRes.getCode()) {  //判断当前请求是否成功
					JSONObject accountResObj = JSONObject.fromObject(accountRes.getData());
					//推送BOSS账户资金结果判断
					if ("0".equals(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_CODE"))) {
						map.put("code","1");
						map.put("msg","操作成功");
					} else {
						map.put("code","2");
						map.put("msg","资金入账接口反馈异常【" + JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_MSG") + "】");
					}
				} else {
					map.put("code","3");
					map.put("msg","调用BOSS资金入账接口异常【" + accountRes.getMessage() + "】");
				}
			}else{
				map.put("code","4");
				map.put("msg","调用财务稽核接口状态调整反馈异常【" + res.getString("code") + "】");
			}
		}else{
			map.put("code","5");
			map.put("msg","调用财务系统接口异常【" + IncomeResult.getMessage() + "】");

		}
		return map;
	}

	public String numberIsVerified(String paymentStr,String groupCode,String bossUserName){
		try {
			int paymentIndex = paymentStr.indexOf("充值卡");
			if (paymentIndex==-1){
				paymentIndex = paymentStr.indexOf("缴费码");
				if (paymentIndex==-1){
					return "";
				}
			}
			String paymentValueStr = paymentStr.substring(paymentIndex);

			int firstIndex = -1;
			int lastIndex = -1;

			Pattern firstPattern = Pattern.compile("\\d");
			Matcher firstMatcher = firstPattern.matcher(paymentValueStr);
			if (firstMatcher.find()) {
				firstIndex = firstMatcher.start();
			}

			Pattern lastPattern = Pattern.compile("\\d(?=\\D*$)");
			Matcher lastMatcher = lastPattern.matcher(paymentValueStr);
			if (lastMatcher.find()) {
				lastIndex = lastMatcher.start()+1;
			}

			String paymentValue = "";
			if (firstIndex>-1 && lastIndex>10){
				paymentValue = paymentValueStr.substring(firstIndex,lastIndex);
			}

			if (!"".equals(paymentValue)){
				Result result = ClaimFundsOpenSrv.getInstance().getUnitInfo(bossUserName,groupCode);
				if (result.getCode()==ResultCode.SUCCESS.code()){
					JSONObject json = JSONObject.fromObject(result.getData());
					JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
					if ("OK".equals(root.getString("RETURN_MSG"))) {
						JSONObject outData = root.getJSONObject("OUT_DATA");
						JSONObject info = outData.getJSONObject("INFO");
						JSONArray contractList = info.getJSONArray("CONTRACT_LIST");
						if (contractList!=null){
							for (int i = 0; i < contractList.size(); i++) {
								if (contractList.getJSONObject(i).getString("CONTRACT_NO").equals(paymentValue)){
									return paymentValue;
								}
							}
							return "";
						}else {
							logger.info("资金自动缴费账户校验失败：集团："+groupCode+"未查询到对应账户信息!");
							return "";
						}
					} else {
						logger.info("资金自动缴费账户校验接口调用失败："+root.getString("PROMPT_MSG"));
						return "";
					}
				}else {
					logger.info("资金自动缴费账户校验接口调用失败："+result.getMessage());
					return "";
				}
			}else {
				return "";
			}
		}catch (Exception e){
			logger.info("资金自动缴费账户校验异常："+e.getMessage(),e);
			e.printStackTrace();
			return "";
		}
	}

	public Map<String,String> updateIncomeState(String serialNo,String batchNo,String username){
		Map<String,String> map = new HashMap<>();
		Result IncomeResult = ClaimFundsOpenSrv.getInstance().updateIncomeState("0", serialNo);
		if (ResultCode.SUCCESS.code() == IncomeResult.getCode()) {//判断当前请求是否成功
			JSONObject res = JSONObject.fromObject(IncomeResult.getData());
			//调整财务的状态为已使用状态。
			if ("success".equals(res.getString("code"))) {
				map.put("code","1");
				map.put("msg","解除预占成功,更改状态为未使用");
			}else{
				map.put("code","2");
				map.put("msg","调用财务稽核接口状态调整反馈异常【" + res.getString("code") + "】");
			}
		}else{
			map.put("code","3");
			map.put("msg","调用财务系统接口异常【" + IncomeResult.getMessage() + "】");
		}
		return map;
	}


	public JSONArray queryCustInfoByBank(String bankAccount){
		JSONArray group = new JSONArray();
		try{
			if (bankAccount!=null && !"".equals(bankAccount)){
				JSONObject json = GroupAccountSrv.getInstance().QueryGroupAccountByAccount(bankAccount);
				JSONObject hander = json.getJSONObject("HEADER");
				JSONObject response = hander.getJSONObject("RESPONSE");
				if (response.has("CODE") && "0000".equals(response.getString("CODE")) && json.has("RESULT")) {
					group = json.getJSONArray("RESULT");
					if (group.size() > 0) {
						Iterator<JSONObject> iter = group.iterator();
						while (iter.hasNext()) {
							JSONObject result = iter.next();
							if (result.getString("REAL_TYPE").equals("1")){
								SystemUser user = claimForFundsService.querUsersByBossNo(result.getString("BOSS"));
								if (user == null) {
									iter.remove();
								} else {
									if (claimForFundsService.queryGroupCustomerById(result.getString("CUST_CODE")) == null) {
										GroupCustomer findDBCustomer = new GroupCustomer();
										findDBCustomer.setGroupCoding(result.getString("CUST_CODE"));
										findDBCustomer.setGroupName(result.getString("CUST_NAME"));
										findDBCustomer.setGroupLevel(result.getString("CUST_VALUE"));
										findDBCustomer.setContactAddress(result.getString("CUST_ADDR"));
										findDBCustomer.setCity(result.getString("CITY"));
										findDBCustomer.setUser_name(user.getBossUserName());
										findDBCustomer.setChinese_name(user.getEmployeeName());
										findDBCustomer.setMobile_phone(user.getMobile());
										claimForFundsService.savaGroupCustomer(findDBCustomer);
										logger.info("添加集团成功！");
									}
								}
							}
						}
					}
				}
			}
			return group;
		}catch (Exception e){
			e.printStackTrace();
			logger.info("资金自动认领错误信息："+e.getMessage());
			return group;
		}
	}

	/**
	 * @Description TODO 根据账户和有价卡流水修改有价卡信息
	 * <AUTHOR>
	 * @param invNo         有价卡流水
	 * @param contrctNo     账户号码
	 * @param Amount        金额
	 * @param updateType    修改类型 缴纳金额：SAVA，冲正金额：其他
	 * @return java.lang.Boolean
	 * @Date 2022/12/2 10:28
	 **/
	public Boolean updateValuableCard(String invNo,String contrctNo,String Amount,String updateType){
		try {
			ValuableCardDet valuableCard = valuableCardService.getValuableCardDetByInvNo(invNo,contrctNo);
			if (valuableCard!=null){
				int cardAmount = Integer.parseInt(valuableCard.getOrderPrice());            //申请金额
				int cardMoneyPayPrice = Integer.parseInt(valuableCard.getMoneyPayPrice());  //资金认领已缴纳金额
				Integer newCardMoneyPayPrice = 0;
				if ("SAVA".equals(updateType)){
					newCardMoneyPayPrice = cardMoneyPayPrice+Integer.parseInt(Amount);
				}else {
					newCardMoneyPayPrice = cardMoneyPayPrice-Integer.parseInt(Amount);
				}
				if (cardAmount>=newCardMoneyPayPrice){
					valuableCard.setMoneyPayPrice(newCardMoneyPayPrice.toString());
					return valuableCardService.updateValuableCardDet(valuableCard)!=null;
				}else {
					return false;
				}
			}else {
				return false;
			}
		}catch (Exception e){
			logger.info("有价卡:"+invNo+"金额修改失败："+e.getMessage(),e);
			e.printStackTrace();
			return false;
		}
	}

	/**  
     * 将元为单位的转换为分 替换小数点，支持以逗号区分的金额 
     *  
     * @param amount 
     * @return 
     */  
    public static String changeY2F(String amount){  
        String currency =  amount.replaceAll("\\$|\\￥|\\,", "");  //处理包含, ￥ 或者$的金额  
        int index = currency.indexOf(".");  
        int length = currency.length();  
        Long amLong = 0l;  
        if(index == -1){  
            amLong = Long.valueOf(currency+"00");  
        }else if(length - index >= 3){  
            amLong = Long.valueOf((currency.substring(0, index+3)).replace(".", ""));  
        }else if(length - index == 2){  
            amLong = Long.valueOf((currency.substring(0, index+2)).replace(".", "")+0);  
        }else{  
            amLong = Long.valueOf((currency.substring(0, index+1)).replace(".", "")+"00");  
        }  
        return amLong.toString();  
    }  
    
    /**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatethree(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	/**
	 * 
	 * @params
	 */
	public static Date getStringDateFour(String currentTime){
		String dateStrings =currentTime.substring(0,4)+"-"+currentTime.substring(4,6)+"-"+currentTime.substring(6,8); 
		Date dateString=null;
		try {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			dateString = formatter.parse(dateStrings);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @return
	 */
	public static String dateOfThePreviousDay() {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE,0);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
		String dateString = formatter.format(calendar.getTime());
		return dateString;
	}

	public static String dateOfTheMonthString() {
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE,0);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
		String dateString = formatter.format(calendar.getTime());
		return dateString;
	}

	/**
	 * 1. 第一步:连接sftp服务器,先获取Session
	 * @param host
	 *            主机
	 * @param port
	 *            端口
	 * @param username
	 *            用户名
	 * @param password
	 *            密码
	 * @return
	 */
	public Session getSession(String host, int port, String username, String password) {
		Session session = null;
		try {
			JSch jsch = new JSch();
			session =jsch.getSession(username, host, port);
			logger.info("Session创建");
			session.setPassword(password);
			Properties sshConfig = new Properties();
			sshConfig.put("StrictHostKeyChecking", "no");
			session.setConfig(sshConfig);
			session.connect();
			logger.info("Session连接");
		} catch (Exception e) {
			e.printStackTrace();
			if (session!= null && session.isConnected()){
				session.disconnect();
			}
		}
		return session;
	}

	/**
	 * 2.第二步: 连接sftp服务器,再获取链接
	 * @return
	 */
	public ChannelSftp getConnect(Session session) {
		ChannelSftp sftp = null;
		try {
			if(session == null){
				logger.info("Can't Create Connect,Because session is null");
				return sftp;
			}
			Channel channel = session.openChannel("sftp");
			logger.info("开启渠道.");
			channel.connect();
			sftp = (ChannelSftp) channel;
			logger.info("连接SFTP服务器： " + session.getHost()+":"+session.getPort());
		} catch (Exception e) {
			e.printStackTrace();
			if (sftp!= null && sftp.isConnected()){
				sftp.disconnect();
			}
		}
		return sftp;
	}

	/**
	 * 3.第三步:关闭 channel和session
	 * @param channel
	 */
	public void disconnect(Channel channel , Session session) {
		try {
			if (channel!= null && channel.isConnected()){
				channel.disconnect();
				logger.info("关闭渠道");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
		try {
			if (session!= null && session.isConnected()){
				session.disconnect();
				logger.info("关闭session");
			}
		} catch (Exception e) {
			e.printStackTrace();
		}
	}



	/**
	 * 上传文件到远端服务器,如果在同一目录下,文件名相同会自动替换
	 * 如果上传一半,网络原因中断,那服务器上会有一半大小的文件,请重新上传.
	 * @param destDirectory
	 *            远端服务器要上传的目录  : /data/temp/test/
	 * @param srcDirectory
	 *            本地要上传的目录 : D:/test/
	 * @param srcFileName
	 *            本地要上传的文件 : upload.txt
	 * @param sftp
	 */
	public void upload(String destDirectory, String srcDirectory, String srcFileName, ChannelSftp sftp) throws Exception{
		try {
			sftp.cd(destDirectory);
			File file = new File(srcDirectory+srcFileName);
			if(!file.exists()){
				throw new Exception(srcDirectory+srcFileName+" is not exists");
			}
			logger.info("上传本地文件"+srcDirectory+srcFileName+"到远端服务器"+destDirectory+" 开始");
			sftp.put(new FileInputStream(file), file.getName());
			logger.info("上传本地文件"+srcDirectory+srcFileName+"到远端服务器"+destDirectory+" 结束");
			//sftp.put("D:/application/eclipse64ee/workspace/SFTP/src/com/testdemo/www/ftp/SFTPTooL.java","/data/temp/test");//将本地目录的文件直接上传到服务器上
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	/**
	 * 上传流到远端服务器,如果在同一目录下,文件名相同会自动替换
	 * 如果上传一半,网络原因中断,那服务器上会有一半大小的文件,请重新上传.
	 * @param destDirectory
	 *            远端服务器要上传的目录  : /data/temp/test/
	 * @param srcStream
	 *            本地要上传的流 : D:/test/
	 * @param srcFileName
	 *            本地指定到远端服务器要生成的文件名 : upload.txt
	 * @param sftp
	 */
	public void upload(String destDirectory, InputStream srcStream, String srcFileName, ChannelSftp sftp) throws Exception{
		try {
			sftp.cd(destDirectory);
			if(srcStream == null){
				throw new Exception("流为空,"+srcFileName+" is not exists");
			}
			logger.info("上传流"+srcFileName+"到远端服务器"+destDirectory+" 开始");
			sftp.put(srcStream, srcFileName);
			logger.info("上传流"+srcFileName+"到远端服务器"+destDirectory+" 结束");
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	/**
	 * 在远端服务器上下载文件
	 *
	 * @param remoteDirectory
	 *            在远端服务器上要下载的目录 :/data/temp/test/
	 * @param remoteFile
	 *            在远端服务器上要下载的文件名 :　download.txt
	 * @param localDirectory
	 *            本地所在文件夹 : D:/test/
	 * @param localFile
	 *            本地将要生成的的文件名 : download.txt
	 * @param sftp 链接
	 */
	public void download(String remoteDirectory, String remoteFile,String localDirectory, String localFile, ChannelSftp sftp)  throws Exception{
		try {
			sftp.cd(remoteDirectory);
			File file = new File(localDirectory);
			if(!file.exists())
				file.mkdirs();
			File saveFile = new File(localDirectory,localFile);
			logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+localFile+" 开始");
			sftp.get(remoteFile, new FileOutputStream(saveFile));
			logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+localFile+" 结束");
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	/**
	 * 在远端服务器上批量下载文件到本地文件夹
	 *
	 * @param remoteDirectory
	 *            在远端服务器上要下载的目录 :/data/temp/test/
	 * @param remoteFile
	 *            在远端服务器上要下载的文件名 :　*.txt
	 * @param localDirectory
	 *            本地所在文件夹 : D:/test/
	 * @param sftp 链接
	 */
	public void download(String remoteDirectory, String remoteFile,String localDirectory, ChannelSftp sftp)  throws Exception{
		try {
			sftp.cd(remoteDirectory);
			File file = new File(localDirectory);
			if(!file.exists())
				file.mkdirs();
			logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+" 开始");
			sftp.get(remoteFile, localDirectory);
			logger.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+" 结束");
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	/**
	 * 在远端服务器上删除文件(仅能删除文件,不能删目录)
	 *
	 * @param directory
	 *            在远端服务器上,要删除文件所在目录 : /data/temp/test/
	 * @param deleteFile
	 *            在远端服务器上,要删除的文件
	 * @param sftp 链接
	 */
	public void delete(String directory, String deleteFile, ChannelSftp sftp)  throws Exception{
		try {
			sftp.cd(directory);
			sftp.rm(deleteFile);
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
	}

	/**
	 * 在远端服务器上的指定文件夹下创建新的目录(多层次)
	 * @param directory
	 *            远端服务器上,要创建文件所在目录 : /data/temp/test/
	 * @param folderPath
	 *            远端服务器上,要创建的文件夹名 : ( 可以为多层次,形如  good 或  test2/good/ok )
	 * @param sftp 链接
	 */
	public void mkdir(String directory, String folderPath, ChannelSftp sftp)  throws Exception{
		try {
			sftp.cd(directory);//切换目录,如果目录不存在就会报错
		} catch (Exception e) {
			e.printStackTrace();
			throw e;
		}
		String[] folders = folderPath.split("/");
		for(String currentFolder :folders){
			try{
				sftp.ls(currentFolder);//展示目录,如果文件夹不存在就会报错
				sftp.cd(currentFolder);
			}catch(Exception e){
				sftp.mkdir(currentFolder);//即然不存在,就创建该文件夹
				sftp.cd(currentFolder);
				logger.info(currentFolder+" is no exists, make the dir success");
			}
		}
	}

	/**
	 * 创建文件
	 *
	 * @throws IOException
	 */
	public static boolean creatTxtFile(String name,String path) throws IOException {
		boolean flag = false;
		File filename = new File(path + name);
		if (!filename.exists()) {
			filename.createNewFile();
			flag = true;
		}
		return flag;
	}

	/**
	 * 写文件
	 *
	 * @param newStr
	 *       新内容
	 * @param name
	 *      文件名称
	 * @param name
	 *      文件地址
	 * @throws IOException
	 */
	public static boolean writeTxtFile(String newStr,String name,String path) throws IOException {
		//先读取原有文件内容，然后进行写入操作
		boolean flag = false;
		String filein = newStr + "\r\n";
		String temp = "";
		FileInputStream fis = null;
		InputStreamReader isr = null;
		BufferedReader br = null;
		FileOutputStream fos = null;
		PrintWriter pw = null;
		try {
			// 文件路径
			File file = new File(path + name);
			// 将文件读入输入流
			fis = new FileInputStream(file);
			isr = new InputStreamReader(fis);
			br = new BufferedReader(isr);
			StringBuffer buf = new StringBuffer();
			// 保存该文件原有的内容
			for (int j = 1; (temp = br.readLine()) != null; j++) {
				buf = buf.append(temp);
				// System.getProperty("line.separator")
				// 行与行之间的分隔符 相当于“\n”
				buf = buf.append(System.getProperty("line.separator"));
			}
			buf.append(filein);
			fos = new FileOutputStream(file);
			pw = new PrintWriter(fos);
			pw.write(buf.toString().toCharArray());
			pw.flush();
			flag = true;
		} catch (IOException e1) {
			e1.printStackTrace();
		} finally {
			if (pw != null) {
				pw.close();
			}
			if (fos != null) {
				fos.close();
			}
			if (br != null) {
				br.close();
			}
			if (isr != null) {
				isr.close();
			}
			if (fis != null) {
				fis.close();
			}
		}
		return flag;
	}

	public static void  writeTxtFile(String content,String fileName){
		try {
			RandomAccessFile randomAccessFile = new RandomAccessFile(fileName,"rw");
			long fileLength = randomAccessFile.length();

			randomAccessFile.seek(fileLength);
			String str = content+"\r\n";
			randomAccessFile.writeBytes(new String(str.getBytes(StandardCharsets.UTF_8),StandardCharsets.ISO_8859_1));
			randomAccessFile.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 获取文件大小(字节byte)
	 * 方式一：file.length()
	 */
	public static String  getFileLength(File file){
		long fileLength = 0L;
		if(file.exists() && file.isFile()){
			fileLength = file.length();
		}
		logger.info("文件"+file.getName()+"的大小为:"+fileLength+"byte");
		return fileLength+"";
	}

	public String getDate(){
		SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		Date date = new Date();
		Calendar calendar = Calendar.getInstance();
		calendar.add(Calendar.DATE, -1);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
		String dateString = formatter.format(calendar.getTime());
		return dateString;
	}

	/**
	 * 获取当前时间的上个月时间
	 */
	public String findTime() {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMM");
		Date date = new Date();
		Calendar cal = Calendar.getInstance();
		cal.setTime(date);
		cal.add(Calendar.MONTH, -1);
		return sdf.format(cal.getTime());
	}

	/**
	 * @Description TODO 获取前一个星期三的日期
	 * <AUTHOR>
	 * @return java.lang.String
	 * @Date 2022/11/3 16:28
	 **/
	public String getPreviousWednesday(){
		SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyyMMdd");
		Calendar calendar = Calendar.getInstance();
		if (calendar.get(Calendar.DAY_OF_WEEK)== 4 || calendar.get(Calendar.DAY_OF_WEEK)== 2){
			return simpleDateFormat.format(calendar.getTime());
		}else {
			switch (calendar.get(Calendar.DAY_OF_WEEK)){
				case 3:
				case 5:
					calendar.add(Calendar.DAY_OF_WEEK, -1);
					break;
				case 6:
					calendar.add(Calendar.DAY_OF_WEEK, -2);
					break;
				case 7:
					calendar.add(Calendar.DAY_OF_WEEK, -3);
					break;
				case 1:
					calendar.add(Calendar.DAY_OF_WEEK, -4);
					break;
			}
			return simpleDateFormat.format(calendar.getTime());
		}
	}
	
}
