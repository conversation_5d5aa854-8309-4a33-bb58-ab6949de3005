package com.xinxinsoft.task;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStreamReader;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.log4j.Logger;

import sun.net.www.protocol.ftp.FtpURLConnection;

import com.xinxinsoft.entity.auditWorksheet.AuditWorksheet;
import com.xinxinsoft.entity.contractUniformity.Con_OrderForm;
import com.xinxinsoft.entity.contractUniformity.ContractInfo;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.executeJobLog.JobLog;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.AuditWorksheetService.AuditWorksheetService;
import com.xinxinsoft.service.contractUniformityService.ContractUniformityService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.common.FileUpload;

public class AuditWorksheetTask {
	private static final Logger logger = Logger.getLogger(AuditWorksheetTask.class);
	private AuditWorksheetService auditWorksheetService;
	private WaitTaskService	service;		
	private SystemUserService systemUserService;
	private ContractUniformityService contractUniformityService;
	private JobLogServicer 			jobLogServicer;
	public AuditWorksheetService getAuditWorksheetService() {
		return auditWorksheetService;
	}

	public void setAuditWorksheetService(AuditWorksheetService auditWorksheetService) {
		this.auditWorksheetService = auditWorksheetService;
	}

	public WaitTaskService getService() {
		return service;
	}

	public void setService(WaitTaskService service) {
		this.service = service;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public ContractUniformityService getContractUniformityService() {
		return contractUniformityService;
	}

	public void setContractUniformityService(
			ContractUniformityService contractUniformityService) {
		this.contractUniformityService = contractUniformityService;
	}

	public JobLogServicer getJobLogServicer() {
		return jobLogServicer;
	}

	public void setJobLogServicer(JobLogServicer jobLogServicer) {
		this.jobLogServicer = jobLogServicer;
	}

	public void downloadFileOpenSvc() {  
		JobLog jobLog = new JobLog();
        try {
    		jobLog.setStartTime(new Date());
    		jobLog.setJobName("Class:AuditWorksheetTask Method:downloadFileOpenSvc 稽核每天读取文件方法，每天22:50执行");
        	//File file = new File("G:\\auto_zq_20190813.txt");
            File file = new File(FileUpload.getAuditWorksheet()+"auto_zq_"+FileUpload.getDateToString("yyyyMMdd")+".txt");
            FileInputStream fis = new FileInputStream(file);
			InputStreamReader isr = new InputStreamReader(fis, "UTF-8");//字符流
			BufferedReader br = new BufferedReader(isr);//缓冲
			String line = null;
			String[] strs = null;
			while ((line = br.readLine()) != null) {//字符不等于空
				strs = line.split("&");
				AuditWorksheet aws= new AuditWorksheet();
				aws.setBillName(strs[0]);
				aws.setBossNo(strs[1]);//==========合同一致性  BOSS编码和专线号以-为分隔符
				aws.setTypeName(strs[8]);
				aws.setTypeCode(strs[7]);
				aws.setNumber4A(strs[6]);
				aws.setNumber4APhone(strs[4]);
				aws.setNumber4AName(strs[5]);
				aws.setStaff_login(strs[22]);
				aws.setStaff_name(strs[23]);
				aws.setStaff_phone(strs[24]);
				aws.setDynamicDetails(strs[28]);
				aws.setOrder_login_no(strs[25]);
				aws.setOrder_login_name(strs[26]);
				aws.setOrder_login_phone(strs[27]);
				if("-".equals(strs[29])){
					aws.setErr_detail(null);
				}else{
					aws.setErr_detail(strs[29]);
				}
				String IBM="";
				//根据BOSS工号和手机号模糊查询是否存在用户。
				SystemUser user = null;
				if("htyz".equals(strs[7])){
					if(!"-".equals(strs[2])){
						user=auditWorksheetService.getUserRowNo(strs[2]);
					}
				}else{
					if(!"-".equals(strs[25])){
						user=auditWorksheetService.getUserRowNo(strs[25]);
					}
					if(user==null){
						user=auditWorksheetService.getUser(strs[22],strs[24]);
						if(user==null){
							user=auditWorksheetService.getUser(strs[2],strs[3]);
						}
					}
				}
				if(user !=null){
					aws.setUserNo(user.getRowNo()+"");
					aws.setUserName(user.getEmployeeName());
					//用户部门查询
					List<Object[]> sone= auditWorksheetService.getbumen(user.getRowNo());
					for(int i=0;i<sone.size();i++){
						IBM=(String) sone.get(i)[2];
					}
					aws.setBossRowNo(user.getBossUserName());
					aws.setBossPhone(user.getMobile());
				}else{
					//获取地市编码信息，并处理对应为订单系统的编码
					String companyCode="";
					String oldgroupid  = strs[20];
					if("2".equals(oldgroupid)){
						companyCode="01";
					}else{
						companyCode = auditWorksheetService.getcomPanyCode(oldgroupid);
					}
					List<Map<String, String>> listUser =auditWorksheetService.SelectZtreeByUId("ROLE_AUMG",companyCode);
					if(listUser.size()==0){
						logger.info("未查询到接收人,请查询BOSS工号或者号码，并且确认"+strs[21]+"有稽核管理员人员");
					}else{
						int randNum=0;
						//查询稽核人员得到稽核人员数量确认待办分批方式
						if(listUser.size()>1){//稽核人员大于1则随机分批
							Random rand = new Random();
							randNum = rand.nextInt(listUser.size());
						}
						aws.setUserNo(listUser.get(randNum).get("id"));
						aws.setUserName(listUser.get(randNum).get("name"));
						List<Object[]> sone= auditWorksheetService.getbumen(Integer.parseInt(listUser.get(randNum).get("id")));
						for(int i=0;i<sone.size();i++){
							IBM=(String) sone.get(i)[2];
						}
					}
					logger.info("查询稽核管理员人员有"+listUser.size()+"个");
				}
				//生成稽核工单单号
				String sateTime=getStringDatetwo(new Date());
				aws.setBillsNo(IBM+""+sateTime);
				aws.setBranchOfficeCode(strs[20]);
				aws.setBranchOfficeName(strs[21]);
				aws.setOrderNo(strs[19]);
				aws.setSenderName(strs[5]);
				aws.setCompanyCode(strs[9]);
				aws.setCompanyName(strs[10]);
				aws.setCounty(strs[11]);
				aws.setGroupCode(strs[12]);
				aws.setGroupName(strs[13]);
				aws.setOrderDate(formatForDate(strs[14]));
				aws.setOrderMemo(strs[15]);
				aws.setCheckNo(strs[16]);
				aws.setCheckpoint(strs[17]);
				aws.setCheckseq(strs[18]);
				aws.setCreateDate(new Date());
				aws.setState(-2);
				aws.setUpdateDate(new Date());
				AuditWorksheet auditWorksheet= auditWorksheetService.add(aws);
				if(auditWorksheet !=null){ 
					   //获取待办接收人信息
					SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(auditWorksheet.getUserNo()));
					if("htyz".equals(auditWorksheet.getTypeCode())){
						String s[]=auditWorksheet.getBossNo().split("-");
						String bossNo=s[0];
						String specialityNumber=s[1];
						returnContract(auditWorksheet,USER,bossNo,specialityNumber);
					}else{
						daibantwo(auditWorksheet,USER);
					}
				}
			}
			br.close();//关闭文件
			jobLog.setIsError(false);
			jobLog.setEndTime(new Date());	
        }catch (Exception e) {
        	logger.error("稽核job错误信息："+e.getMessage(),e);
        	e.printStackTrace();
        	jobLog.setIsError(true);
			jobLog.setEndTime(new Date());
			jobLog.setErrorMsg(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
        }  
        jobLogServicer.save(jobLog);
    }


	public void downloadFileOpenSvcTwo() {
		try {
			File file = new File("E:\\27.txt");
			FileInputStream fis = new FileInputStream(file);
			InputStreamReader isr = new InputStreamReader(fis, "UTF-8");//字符流
			BufferedReader br = new BufferedReader(isr);//缓冲
			String line = null;
			String[] strs = null;
			while ((line = br.readLine()) != null) {//字符不等于空
				strs = line.split("&");
				AuditWorksheet aws= new AuditWorksheet();
				aws.setBillName(strs[0]);
				aws.setBossNo(strs[1]);//==========合同一致性  BOSS编码和专线号以-为分隔符
				aws.setTypeName(strs[8]);
				aws.setTypeCode(strs[7]);
				aws.setNumber4A(strs[6]);
				aws.setNumber4APhone(strs[4]);
				aws.setNumber4AName(strs[5]);
				aws.setStaff_login(strs[22]);
				aws.setStaff_name(strs[23]);
				aws.setStaff_phone(strs[24]);
				aws.setDynamicDetails(strs[28]);
				aws.setOrder_login_no(strs[25]);
				aws.setOrder_login_name(strs[26]);
				aws.setOrder_login_phone(strs[27]);
				if("-".equals(strs[29])){
					aws.setErr_detail(null);
				}else{
					aws.setErr_detail(strs[29]);
				}
				String IBM="";
				//根据BOSS工号和手机号模糊查询是否存在用户。
				SystemUser user = null;
				if("htyz".equals(strs[7])){
					if(!"-".equals(strs[2])){
						user=auditWorksheetService.getUserRowNo(strs[2]);
					}
				}else{
					if(!"-".equals(strs[25])){
						user=auditWorksheetService.getUserRowNo(strs[25]);
					}
					if(user==null){
						user=auditWorksheetService.getUser(strs[22],strs[24]);
						if(user==null){
							user=auditWorksheetService.getUser(strs[2],strs[3]);
						}
					}
				}
				if(user !=null){
					aws.setUserNo(user.getRowNo()+"");
					aws.setUserName(user.getEmployeeName());
					//用户部门查询
					List<Object[]> sone= auditWorksheetService.getbumen(user.getRowNo());
					for(int i=0;i<sone.size();i++){
						IBM=(String) sone.get(i)[2];
					}
					aws.setBossRowNo(user.getBossUserName());
					aws.setBossPhone(user.getMobile());
				}else{
					//获取地市编码信息，并处理对应为订单系统的编码
					String companyCode="";
					String oldgroupid  = strs[20];
					if("2".equals(oldgroupid)){
						companyCode="01";
					}else{
						companyCode = auditWorksheetService.getcomPanyCode(oldgroupid);
					}
					List<Map<String, String>> listUser =auditWorksheetService.SelectZtreeByUId("ROLE_AUMG",companyCode);
					if(listUser.size()==0){
						logger.info("未查询到接收人,请查询BOSS工号或者号码，并且确认"+strs[21]+"有稽核管理员人员");
					}else{
						int randNum=0;
						//查询稽核人员得到稽核人员数量确认待办分批方式
						if(listUser.size()>1){//稽核人员大于1则随机分批
							Random rand = new Random();
							randNum = rand.nextInt(listUser.size());
						}
						aws.setUserNo(listUser.get(randNum).get("id"));
						aws.setUserName(listUser.get(randNum).get("name"));
						List<Object[]> sone= auditWorksheetService.getbumen(Integer.parseInt(listUser.get(randNum).get("id")));
						for(int i=0;i<sone.size();i++){
							IBM=(String) sone.get(i)[2];
						}
					}
					logger.info("查询稽核管理员人员有"+listUser.size()+"个");
				}
				//生成稽核工单单号
				String sateTime=getStringDatetwo(new Date());
				aws.setBillsNo(IBM+""+sateTime);
				aws.setBranchOfficeCode(strs[20]);
				aws.setBranchOfficeName(strs[21]);
				aws.setOrderNo(strs[19]);
				aws.setSenderName(strs[5]);
				aws.setCompanyCode(strs[9]);
				aws.setCompanyName(strs[10]);
				aws.setCounty(strs[11]);
				aws.setGroupCode(strs[12]);
				aws.setGroupName(strs[13]);
				aws.setOrderDate(formatForDate(strs[14]));
				aws.setOrderMemo(strs[15]);
				aws.setCheckNo(strs[16]);
				aws.setCheckpoint(strs[17]);
				aws.setCheckseq(strs[18]);
				aws.setCreateDate(new Date());
				aws.setState(-2);
				aws.setUpdateDate(new Date());
				AuditWorksheet auditWorksheet= auditWorksheetService.add(aws);
				if(auditWorksheet !=null){
					//获取待办接收人信息
					SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(auditWorksheet.getUserNo()));
					if("htyz".equals(auditWorksheet.getTypeCode())){
						String s[]=auditWorksheet.getBossNo().split("-");
						String bossNo=s[0];
						String specialityNumber=s[1];
						returnContract(auditWorksheet,USER,bossNo,specialityNumber);
					}else{
						daibantwo(auditWorksheet,USER);
					}
				}
			}
			br.close();//关闭文件
		}catch (Exception e) {
			logger.error("稽核job错误信息："+e.getMessage(),e);
			e.printStackTrace();
		}
	}


	public static void main(String[] args) {
		AuditWorksheetTask task = new AuditWorksheetTask();
		task.downloadFileOpenSvcTwo();
	}

	
	// 提交待办生成
	public void daibantwo(AuditWorksheet auditWorksheet,SystemUser user) {
		WaitTask wt = new WaitTask();
		wt.setName("[稽核工单]" + auditWorksheet.getBillName());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/auditWorksheet/auditWorksheetTwo.jsp?id="+auditWorksheet.getUUID());
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(user.getRowNo());
		wt.setHandleUserName(user.getEmployeeName());
		wt.setHandleLoginName(user.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(AuditWorksheet.AUDITWORKSHEET);
		wt.setTaskId(auditWorksheet.getUUID());
		int i = service.saveWait(wt);
		System.out.println("这是代办返回的数据："+i);
	}


	// 提交待办生成
	public void daibanCopy() {
		WaitTask wt = new WaitTask();
		wt.setName("[稽核工单]" +"");
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/auditWorksheet/auditWorksheetTwo.jsp?id="+"1111111");
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(48861);
		wt.setHandleUserName("李志");
		wt.setHandleLoginName("lizhi");
		wt.setCreateUserId(48861);
		wt.setCreateUserName("lizhi");
		wt.setCreateLoginName("lizhi");
		wt.setCode(AuditWorksheet.AUDITWORKSHEET);
		wt.setTaskId("12345");
		int i = service.saveWait(wt);
		System.out.println("这是代办返回的数据："+i);
	}
	
	private void returnContract(AuditWorksheet auditWorksheet,SystemUser user,String bossNo,String specialityNumber){
		Con_OrderForm con_OrderForm= contractUniformityService.getCon_OrderForm(bossNo,specialityNumber);
		ContractInfo contractInfo = contractUniformityService.getContractUnifor(con_OrderForm.getContractId());
		WaitTask wt = new WaitTask();
		wt.setName("[稽核-合同]" + auditWorksheet.getBillName());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/auditWorksheet/reviseContractApply.jsp?id=" +contractInfo.getId()+"&bossNo=" + bossNo+ "&specialityNumber=" + specialityNumber+ 
				"&auditWorksheetid=" + auditWorksheet.getUUID());
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(user.getRowNo());
		wt.setHandleUserName(user.getEmployeeName());
		wt.setHandleLoginName(user.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(AuditWorksheet.AUDITWORKSHEET);
		wt.setTaskId(auditWorksheet.getUUID());
		service.saveWait(wt);
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatetwo(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @param strDate
	 * @return
	 * @throws ParseException
	 */
	public Date formatForDate(String Date) throws ParseException {
		Date date = null;
		if (Date != null && !"".equals(Date)) {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			date = format.parse(Date);
		}
		return date;
	}
}
