package com.xinxinsoft.task;

import java.io.*;
import java.net.ConnectException;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.ResourceBundle;

import javax.xml.ws.WebServiceException;

import com.xinxinsoft.entity.EIPSyncLogs.Logs_AuditInters;
import org.apache.log4j.Logger;
import org.dom4j.Attribute;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;

import com.xinxinsoft.entity.EIPSyncLogs.EIPSyncLogs;
import com.xinxinsoft.service.IBossService.IBossByNoService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.utils.DateUtil;

public class AuditintersLogsTask {
	private static final Logger logger = Logger
			.getLogger(AuditintersLogsTask.class);
	private IBossByNoService ibbnService  ;
	private  SystemUserService userService ;
	

	private static ResourceBundle s = ResourceBundle.getBundle("WebService-config");
	private static String REQ_SERVER_ADDRESS = s.getString("REQ_SERVER_ADDRESS");
	public IBossByNoService getIbbnService() {
		return ibbnService;
	}
	public void setIbbnService(IBossByNoService ibbnService) {
		this.ibbnService = ibbnService;
	}
	public SystemUserService getUserService() {
		return userService;
	}
	public void setUserService(SystemUserService userService) {
		this.userService = userService;
	}

	public void run() {
		logger.info("------------------"+DateUtil.getDate()+"---------------");
		run_AuditLogs();
	}

	private void run_AuditLogs(){
        List<Logs_AuditInters> listobj = ibbnService.queryLogs_AuditInters();
        logger.info("开始执行审计日志推送：总条数====》"+listobj.size());
        List<Logs_AuditInters> newListObj = new ArrayList<>();
        Logs_AuditInters logsAuditInters=null;
        for (int i = 0; i < listobj.size(); i++) {
            try {
                logsAuditInters = listobj.get(i);
                String result =  this.getSoap_Post(logsAuditInters.getLogsText(),logsAuditInters.getRandomNo());
                logsAuditInters.setRetResult(result);
                newListObj.add(logsAuditInters);
            }catch (Throwable e){
                logger.error("审计日志推送异常：{}",e);
            }
        }
        ibbnService.executeBatchUpdate(newListObj);
        logger.info("执行更新条数====》"+newListObj.size());
    };

	private  void run_AuditLogs_old(){
		EIPSyncLogs esl = new EIPSyncLogs();
		esl.setStartDate(DateUtil.getDate());
		List<Object[]> listobj = ibbnService.queryXmlModes();
		int count =0 ,fla=0;
		for(Object[] obj:listobj){
			try {
				getSoapInputStream_Post(obj[2].toString(),obj[4].toString());
				count++;
			}catch (ConnectException e) {
			    logger.debug("run_AuditLogs=>ConnectException:{}",e);
				fla++;
				logger.error("请求服务器超时或失败！");
			}catch (Exception e) {
                logger.debug("run_AuditLogs=>Exception:{}",e);
				fla++;
			}finally{
				 try {
					 esl.setStatus(1);
					 esl.setMsg("请求服务器超时或失败次数:"+fla+",审计日志总条数："+listobj.size()+"，审计日志推送成功条数："+count);
					 esl.setEndDate(DateUtil.getDate());
					 //userService.queryEIPSyncLogs(esl);
					//logger.info("审计日报添queryEIPSyncLogs加成功");
					// logger.error("请求服务器超时或失败次数:"+fla+",审计日志总条数："+listobj.size()+"，审计日志推送成功条数："+count);
				} catch (Exception e) {
					logger.info("审计日报添加失败");
				}
			}
		

		}
	}

    /**
     * 发送soap请求到服务器，并接受返回数据
     *
     * @param soap
     * @param randomNo
     * @return 0成功，1失败
     * @throws Exception
     */
    private  String getSoap_Post(String soap,String randomNo) throws Exception {
        // WebService服务的地址
        //
        String  line ="";
        StringBuffer sb = new StringBuffer();
        if (soap != null) {
            URL urlObject = new URL(REQ_SERVER_ADDRESS);
            HttpURLConnection conn = (HttpURLConnection) urlObject.openConnection();
                // 是否具有输入参数
                conn.setDoInput(true);
                // 是否输出输入参数
                conn.setDoOutput(true);
                // 发POST请求
                conn.setRequestMethod("POST");
                // 设置请求头（注意一定是xml格式）
                conn.setRequestProperty("content-type","text/xml;charset=utf-8");

                 conn.connect();//连接
                 conn.getOutputStream().write(soap.getBytes());
                 conn.getOutputStream().flush();
                 conn.getOutputStream().close();

                InputStreamReader inputStreamReader = new InputStreamReader(conn.getInputStream(), "utf-8");
                BufferedReader bufferedReader = new BufferedReader(inputStreamReader);
                    while ((line = bufferedReader.readLine()) != null) {
                        sb.append(line);
                    }
                    bufferedReader.close();
                    conn.getInputStream().close();

                try {
                    String R = parseResponseSoap("result", "", sb.toString().trim());
                    return R;
                } catch (Exception e) {
                    logger.error("接口调用成功，返回值解析失败，返回值：" + sb + ",Exception:===",e);
                    e.printStackTrace();
                    return sb.toString();
                }finally {
                    conn.disconnect();
                }
        }
        return sb.toString();

    }
	/**
	 * 发送soap请求到服务器，并接受返回数据
	 *
	 * @param soap
	 * @param randomNo
	 * @return 0成功，1失败
	 * @throws Exception
	 */
	private  String getSoapInputStream_Post(String soap,String randomNo) throws Exception {
		// WebService服务的地址
		URL url;
			if (soap != null) {
				url = new URL(REQ_SERVER_ADDRESS);
				HttpURLConnection conn = (HttpURLConnection) url
						.openConnection();
				// 是否具有输入参数
				conn.setDoInput(true);
				// 是否输出输入参数
				conn.setDoOutput(true);
				// 发POST请求
				conn.setRequestMethod("POST");
				// 设置请求头（注意一定是xml格式）
				conn.setRequestProperty("content-type",
						"text/xml;charset=utf-8");
				// 获得一个输出流
				OutputStream out = conn.getOutputStream();
				out.write(soap.getBytes());

				// 获得服务端响应状态码
				int code = conn.getResponseCode();
				StringBuffer sb = new StringBuffer();
				if (code == 200) {
					// 获得一个输入流，读取服务端响应的数据
					InputStream is = conn.getInputStream();
					byte[] b = new byte[1024];
					int len = 0;

					while ((len = is.read(b)) != -1) {
						String s = new String(b, 0, len, "utf-8");
						sb.append(s);
					}
					is.close();
				}

				out.close();
				if (new String(sb) != null && !"".equals(new String(sb))) {
					try{
						String R=parseResponseSoap("result","",sb.toString().trim());
						return R;
					}catch(Exception e){

						logger.info("接口调用成功，返回值解析失败，返回值：" + sb+",Exception:===");
						e.printStackTrace();
						return sb.toString();
					}
				}
				//logger.error("服务端响应数据为：" + sb.toString());
                return  sb.toString();
			}

			return  null;

	}

	/**
     * 解析返回报文
     * @param node 标记所在节点
     * @param attr 标记所在属性
     * @param soap 报文
     * @return 标记值
     * @throws
     */
    public static String parseResponseSoap(String node, String attr, String soap)  {
        //然后用SOAPMessage 和 SOAPBody
        Document personDoc;
        try {
            personDoc = new SAXReader().read(new StringReader(soap));
            Element rootElt = personDoc.getRootElement(); // 获取根节点
            @SuppressWarnings("rawtypes")
			Iterator body = rootElt.elementIterator("Body");
            while (body.hasNext()) {
                Element recordEless = (Element) body.next();
                return nextSubElement(node,attr,recordEless);
            }
        } catch (DocumentException e) {
            throw new WebServiceException("解析返回报文失败", e);
        }
        return "";
    }
    
    /**
     * 递归方法，查找本节点是否有标记信息，如果没有就查找下一层，
     * 在下一层里同样查找本层节点，只要找到值，就层层返回。
     * @param node 节点标签名
     * @param attr 节点属性值
     * @param el 当前节点对象
     * @return 目标值
     */
    public static String nextSubElement(String node, String attr, Element el) {
        if (el.getName().equals(node)) {
            //说明 找到了目标节点
            //属性值为空说明取标签内容
            if (attr.equals("")) {
                @SuppressWarnings("rawtypes")
				Iterator sub2 = el.elementIterator();
                //有子节点说明标签内容不是单一值，需要拿到查询结果
                if (sub2.hasNext()) {
                    while (sub2.hasNext()) {
                        @SuppressWarnings("unused")
						Element s2 = (Element) sub2.next();
                        //如果返回的不是单一的标记值，而是查询结果，有些麻烦，
                        //查询结果应当是list<map>格式，但是map的key值不好确定，是标签名作为key还是属性值作为key
                        //todo
                    }
                } else {
                    return  el.getText();
                }

            } else {
                Attribute attrbute = el.attribute(attr);
                return attrbute.getText();
            }
        } else {
            @SuppressWarnings("rawtypes")
			Iterator sub2 = el.elementIterator();
            while (sub2.hasNext()) {
                Element sub = (Element) sub2.next();
                return nextSubElement(node, attr, sub);
            }
        }
        return "";
    }
	 
}
