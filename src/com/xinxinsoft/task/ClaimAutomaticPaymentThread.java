package com.xinxinsoft.task;

import com.xinxinsoft.entity.GroupAccount.GroupHipAccount;
import com.xinxinsoft.entity.GroupAccount.GroupRelations;
import com.xinxinsoft.entity.claimForFunds.*;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.sendComms.DateTime;
import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.groupAccountService.GroupAccountService;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.math.BigDecimal;
import java.util.*;

/**
 * @方法描述 资金认领自动缴费
 * @开发人员 TangXiao
 * @创建时间 2024/11/22 10:49
 * @版本编号 1.0
 */
public class ClaimAutomaticPaymentThread implements Runnable {
    private static final Logger logger = LoggerFactory.getLogger(ClaimAutomaticPaymentThread.class);

    private final ClaimForFundsService claimForFundsService;
    private final GroupAccountService groupAccountService;
    private final Bpms_riskoff_service taskService;

    private final MoneyTotal moneyTotal;
    private final SystemUser user;

    public ClaimAutomaticPaymentThread(ClaimForFundsService claimForFundsService, GroupAccountService groupAccountService, Bpms_riskoff_service taskService, MoneyTotal moneyTotal, SystemUser user) {
        this.claimForFundsService = claimForFundsService;
        this.groupAccountService = groupAccountService;
        this.taskService = taskService;
        this.moneyTotal = moneyTotal;
        this.user = user;
    }

    @Override
    public void run() {
        BigDecimal overAmount = new BigDecimal(moneyTotal.getOverAmount());
        BigDecimal payAmount = BigDecimal.ZERO;
        MoneyApply moneyApply = createMoneyApply(moneyTotal, user);
        try {
            MoneyPeyMent peyMent = claimForFundsService.queryMoneyPeyMent(moneyTotal.getGroupCode());
            GroupRelations groupRelations = groupAccountService.queryGroupRelations(moneyTotal.getGroupCode(), moneyTotal.getOtherAccNumber());
            if (peyMent != null && groupRelations!=null){

				JSONArray preList = getPreList();
                if ("1".equals(peyMent.getPayByPreinvoicing()) && overAmount.compareTo(BigDecimal.ZERO)>0){
                    if (preList!=null){
                        for (int i = 0; i < preList.size(); i++) {
                            if (overAmount.compareTo(BigDecimal.ZERO)<=0){
                                break;
                            }
                            JSONObject jsonObject = preList.getJSONObject(i);
                            String contractNo = jsonObject.getString("CONTRACT_NO");
                            JSONObject preinvInfo = null;
                            Result preInvResult = ClaimFundsOpenSrv.getInstance().s8000ValidPreInv("",contractNo,moneyTotal.getPushBossUserName());
                            if(ResultCode.SUCCESS.code()==preInvResult.getCode()){
                                JSONObject resDate=JSONObject.fromObject(preInvResult.getData());
                                JSONObject rootDate=JSONObject.fromObject(resDate.get("ROOT"));
                                if(rootDate.getInt("RETURN_CODE")==0){
                                    if(rootDate.has("OUT_DATA")){
                                        JSONObject outData = rootDate.getJSONObject("OUT_DATA");
                                        JSONObject preinvList = outData.getJSONObject("PREINV_LIST");
                                        preinvInfo = preinvList.getJSONObject("PREINV_INFO");
                                        if (preinvInfo.isArray()){
                                            JSONArray array = preinvInfo.getJSONArray("PREINV_INFO");
                                            for (int a = 0; a < array.size(); a++) {
                                                if ("1".equals( array.getJSONObject(a).getString("PRE_INV_STATE"))){
                                                    preinvInfo = array.getJSONObject(a);
                                                    break;
                                                }
                                            }
                                        }
                                    }
                                }
                            }

                            BigDecimal latefeeMoney = new BigDecimal(0);
                            Result result = ClaimFundsOpenSrv.getInstance().sOweFeeQry("",contractNo,moneyTotal.getPushBossUserName(),"1");
                            if(ResultCode.SUCCESS.code()==result.getCode()){
                                JSONObject resDateTwo=JSONObject.fromObject(result.getData());
                                JSONObject rootDateTwo=JSONObject.fromObject(resDateTwo.get("ROOT"));
                                if(rootDateTwo.getInt("RETURN_CODE")==0){
                                    if(rootDateTwo.has("OUT_DATA")){
                                        JSONObject outDataTwo = rootDateTwo.getJSONObject("OUT_DATA");
                                        latefeeMoney = new BigDecimal(outDataTwo.getString("DELAY_FEE"));
                                    }
                                }
                            }

                            if (preinvInfo!=null){
                                String phoneNo = taskService.getNumber();
                                Date createTime = new Date();
                                BigDecimal amount = new BigDecimal(preinvInfo.getString("PRE_SHOULD_PAY"));
                                int num = 0;
                                do {
                                    if (overAmount.compareTo(BigDecimal.ZERO)<=0){
                                        break;
                                    }

                                    String payType = "0";
                                    MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                                    moneyApplyDet.setMoneyNo("JTZD"+phoneNo+ num);
                                    moneyApplyDet.setApplyNo(moneyApply.getApplyNo());
                                    moneyApplyDet.setSerialNo(moneyTotal.getSerialNo());

                                    moneyApplyDet.setGroupCode(moneyTotal.getGroupCode());
                                    moneyApplyDet.setGroupName(moneyTotal.getGroupName());

                                    moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
                                    moneyApplyDet.setCreateDate(createTime);

                                    moneyApplyDet.setOpType("3");
                                    moneyApplyDet.setOrderType("2");

                                    moneyApplyDet.setContrctNo(contractNo);
                                    moneyApplyDet.setUseType("05");
                                    moneyApplyDet.setLateFee("0");
                                    moneyApplyDet.setLateFeeMoney(latefeeMoney.toPlainString());
                                    moneyApplyDet.setInvNo(preinvInfo.getString("PRE_ORDER_ID"));
                                    if ("04".equals(preinvInfo.getString("PRE_INV_TYPE"))){
                                        payType = "22";
                                        moneyApplyDet.setSpecialLineNo(preinvInfo.containsKey("PRE_PHONE_NO")?preinvInfo.getString("PRE_PHONE_NO"):"");
                                        moneyApplyDet.setUserIdNo(preinvInfo.containsKey("PRE_ID_NO")?preinvInfo.getString("PRE_ID_NO"):"");
                                    }else {
                                        moneyApplyDet.setSpecialLineNo("");
                                        moneyApplyDet.setUserIdNo("");
                                    }

                                    if (amount.compareTo(new BigDecimal("9999900")) >= 0){
                                        if (overAmount.compareTo(new BigDecimal("9999900")) >= 0){
                                            moneyApplyDet.setAmount("9999900");
                                            amount = amount.subtract(new BigDecimal("9999900"));
                                        }else {
                                            moneyApplyDet.setAmount(overAmount.toPlainString());
                                            amount = BigDecimal.ZERO;
                                        }
                                    }else {
                                        if (overAmount.compareTo(amount) >= 0){
                                            moneyApplyDet.setAmount(amount.toPlainString());
                                            amount = BigDecimal.ZERO;
                                        }else {
                                            moneyApplyDet.setAmount(overAmount.toPlainString());
                                            amount = BigDecimal.ZERO;
                                        }
                                    }
                                    moneyApplyDet.setPhoneNo(phoneNo);
                                    moneyApplyDet.setBeginCycle(preinvInfo.getString("PRE_BEGIN_CYCLE"));

                                    String busiFee =BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toPlainString();
                                    Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(user.getBossUserName(), moneyTotal.getGroupCode(), "03", moneyApplyDet.getMoneyNo(), contractNo,
                                            "G", moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount(), "0", user.getBossUserName(), moneyTotal.getUseMemo(), "-",
                                            moneyTotal.getOtherAccNumber(), moneyTotal.getOtherName(), moneyApplyDet.getSpecialLineNo(), moneyTotal.getMemo(), busiFee, "05",
                                            moneyApplyDet.getMoneyNo(), moneyApplyDet.getLateFee(), moneyApplyDet.getInvNo(), "",payType,moneyApplyDet.getUserIdNo(),"","");
                                    logger.info(moneyApplyDet.getMoneyNo()+"自动缴费预开票推送结果："+applyRes.toString());
                                    if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
                                        JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
                                        JSONObject root = JSONObject.fromObject(applyObj.getString("ROOT"));
                                        //循环推送申请工单中的明细记录，成功并记录成功和失败数据
                                        if("0".equals(root.getString("RETURN_CODE"))){
                                            payAmount = payAmount.add(new BigDecimal(moneyApplyDet.getAmount()));
                                            overAmount = overAmount.subtract(new BigDecimal(moneyApplyDet.getAmount()));

                                            moneyApplyDet.setState("1");
                                            moneyApplyDet.setBossState("0");
                                            moneyApplyDet.setPushDate(new Date());
                                            JSONObject outData = JSONObject.fromObject(root.getString("OUT_DATA"));
                                            if (outData.has("PAYMENT_ACCEPT")){
                                                if (!outData.getString("PAYMENT_ACCEPT").isEmpty()){
                                                    moneyApplyDet.setPaymentAccept(outData.getString("PAYMENT_ACCEPT"));
                                                    InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
                                                    invoiceMiddle.setPaymentAccept(outData.getString("PAYMENT_ACCEPT"));
                                                    invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
                                                    claimForFundsService.addInvoiceMiddle(invoiceMiddle);
                                                }
                                            }
                                            claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                                        }else {
                                            moneyApplyDet.setState("4");
                                            moneyApplyDet.setBossState("-1");
                                            moneyApplyDet.setBossMsg(root.getString("RETURN_MSG").length()<200?root.getString("RETURN_MSG"):root.getString("RETURN_MSG").substring(0,200));
                                            claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                                            break;
                                        }
                                    }else{
                                        moneyApplyDet.setState("4");
                                        moneyApplyDet.setBossState("-1");
                                        //判断BOSS反馈信息，如果大于数据库字段长度则截取存储
                                        moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
                                        claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                                        break;
                                    }

                                    num+=1;
                                }while (amount.compareTo(BigDecimal.ZERO)>0);
                            }
                        }
                    }
                }else {
                    if (preList!=null && !preList.isEmpty()){
                        moneyTotal.setState(1);
                        moneyTotal.setInformation("集团存在预开票未结清，但因未开启预开票自动缴费配置，无法完成缴费");
                        throw new Exception("集团存在预开票未结清，但因未开启预开票自动缴费配置，无法完成缴费");
                    }
                }

                if("1".equals(peyMent.getPayByArrears()) && overAmount.compareTo(BigDecimal.ZERO)>0){
                    List<GroupHipAccount> groupHipAccountList = groupAccountService.queryGroupHipAccountByHip(groupRelations.getId());

                    List<GroupHipAccount> groupHipAccounts = new ArrayList<>();
                    for (GroupHipAccount groupHipAccount:groupHipAccountList) {
                        if (!"CMIOT账户".equals(groupHipAccount.getContractType())
                                &&!"成员个人账户".equals(groupHipAccount.getContractType())
                                &&!"省外跨区账户".equals(groupHipAccount.getContractType())){
                            groupHipAccounts.add(groupHipAccount);
                        }
                    }

                    if (!groupHipAccounts.isEmpty() && groupHipAccounts.size()<10){
                        for (GroupHipAccount groupHipAccount:groupHipAccounts){
                            if (overAmount.compareTo(BigDecimal.ZERO)<=0){
                                break;
                            }

                            BigDecimal latefeeMoney = new BigDecimal(0);
                            BigDecimal oweFee = new BigDecimal(0);
                            Result result = ClaimFundsOpenSrv.getInstance().sOweFeeQry("",groupHipAccount.getContractNo(),moneyTotal.getPushBossUserName(),"1");
                            if(ResultCode.SUCCESS.code()==result.getCode()){
                                JSONObject resDateTwo=JSONObject.fromObject(result.getData());
                                JSONObject rootDateTwo=JSONObject.fromObject(resDateTwo.get("ROOT"));
                                if(rootDateTwo.getInt("RETURN_CODE")==0){
                                    if(rootDateTwo.has("OUT_DATA")){
                                        JSONObject outDataTwo = rootDateTwo.getJSONObject("OUT_DATA");
                                        latefeeMoney = new BigDecimal(outDataTwo.getString("DELAY_FEE"));
                                        oweFee =new BigDecimal(outDataTwo.getString("OWE_FEE"));
                                    }
                                }
                            }

                            if (oweFee.compareTo(BigDecimal.ZERO)>0){
                                String phoneNo = taskService.getNumber();
                                Date createTime = new Date();
                                int num = 0;
                                do {
                                    if (overAmount.compareTo(BigDecimal.ZERO)<=0){
                                        break;
                                    }

                                    MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                                    moneyApplyDet.setMoneyNo("JTZD"+phoneNo+ num);
                                    moneyApplyDet.setApplyNo(moneyApply.getApplyNo());
                                    moneyApplyDet.setSerialNo(moneyTotal.getSerialNo());

                                    moneyApplyDet.setGroupCode(moneyTotal.getGroupCode());
                                    moneyApplyDet.setGroupName(moneyTotal.getGroupName());

                                    moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
                                    moneyApplyDet.setCreateDate(createTime);

                                    moneyApplyDet.setOpType("3");
                                    moneyApplyDet.setOrderType("2");

                                    moneyApplyDet.setContrctNo(groupHipAccount.getContractNo());
                                    moneyApplyDet.setUseType("1");
                                    moneyApplyDet.setLateFee("0");
                                    moneyApplyDet.setLateFeeMoney(String.valueOf(latefeeMoney));

                                    if (oweFee.compareTo(new BigDecimal("9999900"))>=0){
                                        if (overAmount.compareTo(new BigDecimal("9999900"))>0){
                                            moneyApplyDet.setAmount("9999900");
                                            oweFee = oweFee.subtract(new BigDecimal("9999900"));
                                        }else {
                                            moneyApplyDet.setAmount(String.valueOf(overAmount));
                                            oweFee = BigDecimal.ZERO;
                                        }
                                    }else {
                                        if (overAmount.compareTo(oweFee)>=0){
                                            moneyApplyDet.setAmount(String.valueOf(oweFee));
                                            oweFee = BigDecimal.ZERO;
                                        }else {
                                            moneyApplyDet.setAmount(String.valueOf(overAmount));
                                            oweFee = BigDecimal.ZERO;
                                        }
                                    }
                                    moneyApplyDet.setPhoneNo(phoneNo);

                                    String busiFee =BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toPlainString();
                                    Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(user.getBossUserName(), moneyTotal.getGroupCode(), "03", moneyApplyDet.getMoneyNo(), moneyApplyDet.getContrctNo(),
                                            "G", moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount(), "0", user.getBossUserName(), moneyTotal.getUseMemo(), "-",
                                            moneyTotal.getOtherAccNumber(), moneyTotal.getOtherName(), moneyApplyDet.getSpecialLineNo(), moneyTotal.getMemo(), busiFee, "00",
                                            moneyApplyDet.getMoneyNo(), moneyApplyDet.getLateFee(), "", "","0","","","");
                                    logger.info(moneyApplyDet.getMoneyNo()+"自动缴费预开票推送结果："+applyRes.toString());
                                    if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
                                        JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
                                        JSONObject root = JSONObject.fromObject(applyObj.getString("ROOT"));
                                        //循环推送申请工单中的明细记录，成功并记录成功和失败数据
                                        if("0".equals(root.getString("RETURN_CODE"))){
                                            payAmount = payAmount.add(new BigDecimal(moneyApplyDet.getAmount()));
                                            overAmount = overAmount.subtract(new BigDecimal(moneyApplyDet.getAmount()));

                                            moneyApplyDet.setState("1");
                                            moneyApplyDet.setBossState("0");
                                            moneyApplyDet.setPushDate(new Date());
                                            JSONObject outData = JSONObject.fromObject(root.getString("OUT_DATA"));
                                            if (outData.has("PAYMENT_ACCEPT")){
                                                if (!outData.getString("PAYMENT_ACCEPT").isEmpty()){
                                                    moneyApplyDet.setPaymentAccept(outData.getString("PAYMENT_ACCEPT"));
                                                    InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
                                                    invoiceMiddle.setPaymentAccept(outData.getString("PAYMENT_ACCEPT"));
                                                    invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
                                                    claimForFundsService.addInvoiceMiddle(invoiceMiddle);
                                                }
                                            }
                                            claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                                        }else {
                                            moneyApplyDet.setState("4");
                                            moneyApplyDet.setBossState("-1");
                                            moneyApplyDet.setBossMsg(root.getString("RETURN_MSG").length()<200?root.getString("RETURN_MSG"):root.getString("RETURN_MSG").substring(0,200));
                                            claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                                            break;
                                        }
                                    }else{
                                        moneyApplyDet.setState("4");
                                        moneyApplyDet.setBossState("-1");
                                        //判断BOSS反馈信息，如果大于数据库字段长度则截取存储
                                        moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
                                        claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                                        break;
                                    }

                                    num+=1;
                                }while (oweFee.compareTo(BigDecimal.ZERO)>0);
                            }
                        }
                    }
                }
            }
            moneyTotal.setState(1);
            moneyTotal.setUseAmount(payAmount.add(new BigDecimal(moneyTotal.getUseAmount())).toPlainString());//(使用金额)
            moneyTotal.setOverAmount(overAmount.toPlainString());//(剩余金额)

            moneyApply.setLateFeeMoney("0");
            moneyApply.setApplyAmount(payAmount.toPlainString());
            moneyApply.setState("0");
            claimForFundsService.addMoneyApply(moneyApply);
        }catch (Exception e){
            e.printStackTrace();
            logger.error("自动缴费失败："+e.getMessage());
            moneyTotal.setInformation("自动缴费失败："+e.getMessage());
        }finally {
            if (claimForFundsService.saveProcessList(moneyTotal)==null){
                logger.error("资金认领自动缴费数据保存失败："+moneyTotal.getSerialNo());
            }
        }
    }

    private JSONArray getPreList() {
        Result result = ClaimFundsOpenSrv.getInstance().iPreinvApplyRecdQry(moneyTotal.getGroupCode(),user.getBossUserName());
        if(ResultCode.SUCCESS.code() == result.getCode()){
            JSONObject resObj = JSONObject.fromObject(result.getData());
            JSONObject rootObj = resObj.getJSONObject("ROOT");
            if ("0".equals(rootObj.getString("RETURN_CODE"))) {
                JSONObject outData = rootObj.getJSONObject("OUT_DATA");
                if (outData.has("INV_APPLY_LIST") && outData.getInt("COUNT")>0){
                   return outData.getJSONArray("INV_APPLY_LIST");
                }
            }else {
                moneyTotal.setState(1);
                moneyTotal.setInformation("自动缴费失败：集团预开票信息失败"+result.getMessage());
            }
        }else {
            moneyTotal.setState(1);
            moneyTotal.setInformation("自动缴费失败：集团预开票信息失败"+result.getMessage());
        }
        return null;
    }

    private MoneyApply createMoneyApply(MoneyTotal moneyTotal, SystemUser user) {
        String ibm = "";
        List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
        for (Object[] objects : sone) {
            ibm = (String) objects[2];
        }
        MoneyApply myly = new MoneyApply();
        myly.setApplyNo(ibm+"ZD"+taskService.getNumber());
        myly.setTitle("系统自动缴费工单");
        myly.setApplyMemo("系统于进行"+getDateStr(new Date(),"yyyy-MM-dd HH:mm:ss")+"自动缴费生成工单");
        myly.setCreatorId(String.valueOf(user.getRowNo()));
        myly.setCreatorName(user.getEmployeeName());
        myly.setCreateDate(new Date());
        myly.setGroupCode(moneyTotal.getGroupCode());
        myly.setGroupName(moneyTotal.getGroupName());
        myly.setOpType("1");
        myly.setSerialNo(moneyTotal.getSerialNo());
        myly.setMoneyTotal_id(moneyTotal.getId());
        return myly;
    }

    private String getDateStr(Date date,String pattern){
        return DateTime.formatDate(date, pattern);
    }
}
