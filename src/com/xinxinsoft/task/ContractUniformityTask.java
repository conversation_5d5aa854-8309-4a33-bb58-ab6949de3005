package com.xinxinsoft.task;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.log4j.Logger;

import com.xinxinsoft.action.arrearsModule.UploadUtil;
import com.xinxinsoft.action.contractUniformityAction.ContractUniformityAction;
import com.xinxinsoft.action.contractUniformityAction.ContractUniformityThread;
import com.xinxinsoft.action.contractUniformityAction.GroupBossNoThread;
import com.xinxinsoft.entity.claimForFunds.MoneyTotal;
import com.xinxinsoft.entity.contractUniformity.BossForm;
import com.xinxinsoft.entity.executeJobLog.JobLog;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.contractUniformityService.ContractUniformityService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import com.xinxinsoft.utils.common.FileUpload;

public class ContractUniformityTask {
	
	private static final Logger logger = Logger.getLogger(ContractUniformityTask.class);
	private ContractUniformityService contractUniformityService;
	private JobLogServicer 			jobLogServicer;
	public ContractUniformityService getContractUniformityService() {
		return contractUniformityService;
	}

	public void setContractUniformityService(
			ContractUniformityService contractUniformityService) {
		this.contractUniformityService = contractUniformityService;
	}

	public JobLogServicer getJobLogServicer() {
		return jobLogServicer;
	}

	public void setJobLogServicer(JobLogServicer jobLogServicer) {
		this.jobLogServicer = jobLogServicer;
	}

	/**
	 * 导入bossform数据
	 */
	public void syncBossFormData(){
		JobLog jobLog = new JobLog();
		jobLog.setStartTime(new Date());
		jobLog.setIsError(false);
		jobLog.setEndTime(new Date());	
		Long startTime = System.currentTimeMillis();
		Calendar c = Calendar.getInstance();
		c.add(Calendar.DATE, -1);
		Date m = c.getTime();
		String time = new SimpleDateFormat("yyyyMMdd").format(m);
		String Path = FileUpload.getContractinfoURL()+"contract_"+time+ ".txt";
		jobLog.setJobName("Class:ContractUniformityTask Method:syncBossFormData 导入bossform数据_"+Path);
		jobLogServicer.save(jobLog); 
		//绝对路径或相对路径都可以，这里是绝对路径，写入文件时演示相对路径
		File file = new File(Path); //要读取以上路径的input。txt文件
		if(file==null || !file.exists()){
			return;
		}
		int length = 500000;
		List<Long> list = UploadUtil.getPOS(file, length);
		logger.info("线程个数："+list.size());
		for (int i = 0; i < list.size(); i++){
			Long startTimetwo = System.currentTimeMillis();
			long pos = list.get(i);
			//System.out.println(pos);
			ContractUniformityThread contractUniformityThread = new ContractUniformityThread(pos, file,
					contractUniformityService, length);
			contractUniformityThread.start();
			try {
				Thread.sleep(10);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}
			Long endTimetwo = System.currentTimeMillis();
			logger.info("第"+i+"个线程用时：" + (endTimetwo - startTimetwo));
		}	
		Long endTime = System.currentTimeMillis();
		logger.info("总用时：" + (endTime - startTime));
	}
	
	/**
	 * 导入groupbossno数据
	 */
	public void syncGroupBossData() {
		Long startTime = System.currentTimeMillis();
		Calendar c = Calendar.getInstance();
		c.add(Calendar.DATE, -1);
		Date m = c.getTime();
		String time = new SimpleDateFormat("yyyyMMdd").format(m);
		//String pathname = "F://testTxt/ayue.csv"; //
		//绝对路径或相对路径都可以，这里是绝对路径，写入文件时演示相对路径
		String Path =FileUpload.getContractinfoURL()+"有效全量集团明细"+time+".csv";
		File file = new File(Path); //要读取以上路径的input。txt文件
		int length = 500000;
		List<Long> list = UploadUtil.getPOS(file, length);
		logger.info("线程个数："+list.size());
		for (int i = 0; i < list.size(); i++) {
			Long startTimetwo = System.currentTimeMillis();
			long pos = list.get(i);
			//System.out.println(pos);
			GroupBossNoThread groupBossNoThread = new GroupBossNoThread(pos, file,
					contractUniformityService, length);
			groupBossNoThread.start();
			/*try {
				Thread.sleep(10);
			} catch (InterruptedException e) {
				e.printStackTrace();
			}*/
			Long endTimetwo = System.currentTimeMillis();
			logger.info("第"+i+"个线程用时：" + (endTimetwo - startTimetwo));
		}	
		Long endTime = System.currentTimeMillis();
		logger.info("总用时：" + (endTime - startTime)+"--线程个数："+list.size());
	}
	
	/**
	 * http://localhost:8080/EOM/ContractUniformityAction_updateBossfrom.action
	 */
	public void updateBossfrom() {
		Long startTime = System.currentTimeMillis();
		String s = contractUniformityService.updateBossfrom();
		if(s.equals("false")){
			List<BossForm> list = contractUniformityService.getBossfrom();
			if(list.size()>0){
				for(int i=0;i<list.size();i++){
					if(list.get(i).getUNIT_ID()!=null){
						List<Map<String, String>> listUser =contractUniformityService.SelectZtreeByUId("ROLE_REPM",list.get(i).getCompanyCode());
						if(listUser.size()==0){
							logger.info("合同一致性未查询到订单管理员接收人，公司是"+list.get(i).getCompanyName());
						}else{
							int randNum=0;
							if(listUser.size()>1){
								Random rand = new Random();
								randNum = rand.nextInt(listUser.size());
							}
							list.get(i).setUserId(listUser.get(randNum).get("id"));
							list.get(i).setUserName(listUser.get(randNum).get("name"));
							contractUniformityService.updateBossfromEntity(list.get(i));
						}
					}
				}
			}
			list.clear();
			logger.info("调用存储过程成功"+s);
		}else{
			logger.info("调用存储过程失败"+s);
		}
		Long endTime = System.currentTimeMillis();
		logger.info("总用时：" + (endTime - startTime));
	}
}
