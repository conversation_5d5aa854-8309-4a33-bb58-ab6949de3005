package com.xinxinsoft.task;

import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.oms.*;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.StructureOfPersonnelService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.oms.OmsSellOrderService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.easyh.JSONHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.SimpleDateFormat;
import java.util.*;

public class OmsOrderManagerSignInTask {

    private static final Logger logger = LoggerFactory.getLogger(OmsOrderManagerSignInTask.class);
    private OmsSellOrderService omsSellOrderService;
    private SystemUserService systemUserService;//系统人员工具
    private StructureOfPersonnelService structureOfPersonnelService;
    private WaitTaskService service;//待办
    private Bpms_riskoff_service taskService;
    public OmsSellOrderService getOmsSellOrderService() {
        return omsSellOrderService;
    }

    public void setOmsSellOrderService(OmsSellOrderService omsSellOrderService) {
        this.omsSellOrderService = omsSellOrderService;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public StructureOfPersonnelService getStructureOfPersonnelService() {
        return structureOfPersonnelService;
    }

    public void setStructureOfPersonnelService(StructureOfPersonnelService structureOfPersonnelService) {
        this.structureOfPersonnelService = structureOfPersonnelService;
    }

    public void OmsTimeoutTemindTesk(){
        try {
            logger.info("进入【预受理】定时推送超时提醒");
            Integer count = 0;      //成功的工单数
            Integer number = 0;     //失败的工单数
            List<Map<String,Object>> orderList = omsSellOrderService.QueryOrderByLinkStatus("");
            for (Map<String,Object> obj:orderList){
                logger.info("超时工单:"+obj.get("TITLE")+",超时时间:"+obj.get("PRETREATMENT_DATE"));
                SystemUser usertwo = systemUserService.getByUserInfoRowNo(Integer.parseInt(obj.get("OPER_NO").toString()));
                if (usertwo.getMobile()==null||usertwo.getMobile().equals("")){
                    logger.info("环节处理人的电话为空,推送提醒信息失败:"+usertwo.getRowNo());
                    number+=1;
                }else {
                    omsSellOrderService.saveddyjPush_0_0001(obj.get("CREATOR_NAME").toString(),obj.get("CREATOR_DATE").toString(),obj.get("TITLE").toString(),usertwo.getMobile());
                    count+=1;
                }
            }
            logger.info("【预受理】定时推送超时提醒接收,共有:"+orderList.size()+"条需要推送,成功:"+count+"条,失败:"+number+"条");
        }catch (Exception e){
            logger.error("【预受理】定时推送超时提醒异常!:",e.getMessage());
        }
    }

    public void deleteOrderManagerSignIn(){
        try {
            logger.info("进入【预受理】定时修改接单能力");
            Integer count = omsSellOrderService.deleteOrderManagerSignIn();
            logger.info("【预受理】修改条数："+count);
        }catch (Exception e){
            logger.error("【预受理】定时修改接单能力异常",e.getMessage());
        }
    }

    public void randomOrderManager(){
        try {
            logger.info("进入【预受理】轮询工单分配到对应的打卡订单经理手中");
            String formats = "HH:mm:ss";
            Date date = new Date();
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat(formats);
            String dateString = simpleDateFormat.format(date);
            Date nowTime = new SimpleDateFormat(formats).parse(dateString);
            TimeConfig timeConfig = omsSellOrderService.getTimeConfig("ORDER_TASK");
            Date startTime = new SimpleDateFormat(formats).parse(timeConfig.getStartTime());
            Date endTime = new SimpleDateFormat(formats).parse(timeConfig.getEndTime());
            boolean effectiveDate = isEffectiveDate(nowTime,startTime,endTime);
            if(effectiveDate){
                List<Map<String,Object>> orderList = omsSellOrderService.selectListMapOrder();
                if(null != orderList){
                    int temp = 0 ;
                    for (Map<String, Object> stringObjectMap : orderList) {
                        try {
                            String orderNo = stringObjectMap.get("ORDER_NO").toString();
                            String orderCreateNo = stringObjectMap.get("ORDER_CREATE_NO").toString();
                            String isSupport = stringObjectMap.get("IS_SUPPORT").toString();//是否分配市综调
                            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(orderCreateNo));
                            if(user==null){
                                continue;
                            }
                            //根据角色code查询角色下面的人员（区分地市和区县）
                            List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
                            if(userlistMap==null||userlistMap.size()==0){
                                continue;
                            }
                            List<Map<String, String>> listMap = new ArrayList<>();
                            Map<String, String> map = new HashMap<>();
                            String managerId = "";
                            if (userlistMap.size() > 0) {
                                if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {//获取地市区县订单经理
                                    logger.info("这是区县" + "是否需要市综调中心值：" + isSupport);
                                    if (isSupport.equals("2")) {
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignInJob("ROLE_ODMR", user, userlistMap, "", "D");
                                    } else {
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignInJob("ROLE_ODMR", user, userlistMap, "", "Q");
                                    }
                                } else {
                                    if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                                        logger.info("这是省公司");
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignInJob("ROLE_ODMR", user, userlistMap, "", "S");
                                    } else {
                                        logger.info("这是市公司");
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignInJob("ROLE_ODMR", user, userlistMap, "", "D");
                                    }
                                }
                            }
                            logger.info("这里是查询有多少订单经理存在："+listMap.size()+";客户经理："+user.getRowNo()+"；需求单号："+orderNo);
                            if (null != listMap && !listMap.isEmpty()) {//分配分数最少的订单经理
                                temp++;
                                int[] arry = new int[listMap.size()];
                                for (int i = 0; i < listMap.size(); i++) {
                                    Object rowno = listMap.get(i).get("ROWNO");
                                    int count = omsSellOrderService.getOmsSellOrderScoreAndNowByUserId(Integer.valueOf(rowno.toString()));
                                    logger.info("这里是查询当前订单经理今天受理的需求单条数："+count);
                                    arry[i] = count;
                                    map.put(String.valueOf(Integer.valueOf(rowno.toString())), String.valueOf(count));
                                }
                                logger.info("这里是判断当前订单经理是否都没有接收需求单："+arry.length);
                                if (arry.length == 0) {
                                    managerId = listMap.get(new Random().nextInt(listMap.size())).get("ROWNO") + "";
                                } else {
                                    int min = arry[0];
                                    for (int i = 0; i < arry.length; i++) {
                                        if (arry[i] < min) {
                                            min = arry[i];
                                        }
                                    }
                                    for (Map.Entry entry : map.entrySet()) {
                                        if (String.valueOf(min).equals(entry.getValue())) {
                                            managerId = String.valueOf(entry.getKey());
                                        }
                                    }
                                }
                            /*int count = omsSellOrderService.getCountNowByUserId(Integer.valueOf(managerId));//获取当天订单经理分配条数
                            if(count < 10 ){*/
                                OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
                                if (oms.getOperateNo() == null) {
                                    SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(managerId));// 获取下一步处理人信息
                                    oms.setOperateName(USER.getEmployeeName());
                                    oms.setOperateNo(String.valueOf(USER.getRowNo()));
                                    oms.setOperateDate(new Date());
                                    oms.setOperateHandleDate(new Date());
                                    omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                                    OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                                    link.setOper_date(new Date());
                                    link.setOper_name(USER.getEmployeeName());
                                    link.setOper_no(USER.getRowNo());
                                    link.setStatus(1);
                                    omsSellOrderService.saveOrupdateOmsOrderLink(link);

                                    String IBM = "";
                                    List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
                                    for (int i = 0; i < sone.size(); i++) {
                                        IBM = (String) sone.get(i)[2];
                                    }
                                    OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(), "3");
                                    List<OmsOrderLink> existence = omsSellOrderService.getOmsOrderLinkByCode("3", "工单确认", oms.getOrderNo());
                                    if (existence == null || existence.size() == 0) {
                                        OmsOrderLink nextStepLink = new OmsOrderLink();
                                        nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                                        nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                                        nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                                        nextStepLink.setOper_name(USER.getEmployeeName());//操作人
                                        nextStepLink.setOper_no(USER.getRowNo());//操作人工号
                                        nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                                        nextStepLink.setStatus(0);//状态(状态根据环节确定)
                                        nextStepLink.setLinkCode("3");//环节编码或者固定的环节编码
                                        nextStepLink.setLinkName("工单确认");//环节名称
                                        nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                                        nextStepLink.setPretreatment_date(this.getTargetDate(new Date(), Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                                        nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                                        oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                                        omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                                        oms.setLinkOrderNo(oms.getLinkOrderNo());
                                        omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                                        OmsLinkDialogue dig = new OmsLinkDialogue();
                                        dig.setCreator_name(user.getEmployeeName());
                                        dig.setCreator_no(user.getRowNo());
                                        dig.setCreator_date(new Date());
                                        dig.setOper_name(USER.getEmployeeName());
                                        dig.setOper_no(USER.getRowNo());
                                        dig.setOper_date(new Date());
                                        dig.setStatus(0);//1已处理，0未处理
                                        dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                                        dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                                        dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                                        OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                                        commitOmsSellOrderData(oms, Integer.parseInt(managerId), user, "", rdig.getId());
                                    }
                                }
                                //}
                            }
                        }catch (Exception e){
                            logger.error("【预受理】轮询工单分配for循环异常:"+"工单编码是："+stringObjectMap.get("ORDER_NO").toString()+
                                    "；创建人："+stringObjectMap.get("ORDER_CREATE_NO").toString()+";"+e.getMessage(),e);
                        }
                    }
                    logger.info("【预受理】轮询工单分配条数："+temp);
                }
            }
        }catch (Exception e){
            logger.error("【预受理】轮询工单分配到对应的打卡订单经理手中异常"+e.getMessage(),e);
        }
    }
    /**
     *  需求单生成待办给订单经理
     * @param order 需求单
     * @param userid 分配人员ID
     */
    public void commitOmsSellOrderData(OmsSellOrder order,Integer userid, SystemUser user,String isContract,String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + order.getTitle());//待办名称
        waitTask.setCreationTime(new Date());//代办生成时间
        waitTask.setUrl("jsp/demandOrder/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask);
    }


    public static boolean judgmentDate(String date1, String date2) throws Exception {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-M-d HH:mm:ss");
        Date start = sdf.parse(date1);
        Date end = sdf.parse(date2);
        long cha = end.getTime() - start.getTime();
        if(cha<0){
            return false;
        }
        double result = cha * 1.0 / (1000 * 60 * 60);
        if(result<=24){
            return true;
        }else{
            return false;
        }
    }
    /**
     * 判断当前时间是否在[startTime, endTime]区间，注意时间格式要一致
     *
     * @param nowTime 当前时间
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return
     */
    public static boolean isEffectiveDate(Date nowTime, Date startTime, Date endTime) {
        if (nowTime.getTime() == startTime.getTime()
                || nowTime.getTime() == endTime.getTime()) {
            return true;
        }
        Calendar date = Calendar.getInstance();
        date.setTime(nowTime);

        Calendar begin = Calendar.getInstance();
        begin.setTime(startTime);

        Calendar end = Calendar.getInstance();
        end.setTime(endTime);

        if (date.after(begin) && date.before(end)) {
            return true;
        } else {
            return false;
        }
    }
    /**
     * @Description: 获取指定小时后的日期
     * @Param: [date:起始时间, hours:小时数]
     * @return: java.util.Date
     * @Author: TX
     * @Date: 2021/10/8 16:42
     */
    public Date getTargetDate(Date date,Integer hours){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //判断当前日期是否为周末,是的话获取后一天0点
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.set(Calendar. HOUR_OF_DAY , 0);
            calendar.set(Calendar. MINUTE , 0);
            calendar.set(Calendar. SECOND , 0);
            calendar.set(Calendar. MILLISECOND , 0);
            calendar.add(Calendar. DAY_OF_MONTH , 1);
        }
        //判断配置时间是否超过一天
        while (hours>=24){
            //当前时间加一天,并判断加一天后是否未周末,如果是继续加一天
            calendar.add(Calendar.DAY_OF_MONTH, +1);
            while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
                calendar.add(Calendar.DAY_OF_MONTH, +1);
            }
            hours-=24;
        }
        //未超过一天(24小时)直接加到当前时间的上,并判断加上后时间是否为周末,如果是再加一天
        calendar.add(Calendar.HOUR_OF_DAY, +hours);
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.add(Calendar.DAY_OF_MONTH, +1);
        }
        return calendar.getTime();
    }
}
