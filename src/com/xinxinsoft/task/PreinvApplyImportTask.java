package com.xinxinsoft.task;

import java.io.*;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.ResourceBundle;


import com.xinxinsoft.entity.PreinvApply.ImportPreinvApply;
import com.xinxinsoft.entity.PreinvApply.PreinvApplyDet;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.PreinvApply.PreinvApplyService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 预开票文件导入
 *
 * <AUTHOR>
 */
public class PreinvApplyImportTask {
    private static final Logger logger = LoggerFactory.getLogger(PreinvApplyImportTask.class);
    private static ResourceBundle s = ResourceBundle.getBundle("WebService-config");
    private static String PREINVAPPLY_UPLOAD = s.getString("PREINVAPPLY_UPLOAD");
    private static int OVERDUE_DAY = Integer.valueOf(s.getString("OVERDUE_DAY"));
    private PreinvApplyService preinvApplyService;
    private WaitTaskService service;
    private SystemUserService systemUserService;

    public void setPreinvApplyService(PreinvApplyService preinvApplyService) {
        this.preinvApplyService = preinvApplyService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    /**
     * gcy 查找文件夹
     *
     * @param file
     * @param isRecursive 是否递归子文件夹
     */
    public List<String> filterFileType(File file, boolean isRecursive) {
        List<String> list = new ArrayList<>();
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                if (f.isFile()) {
                    if (f.getName().endsWith(".txt")) {
                        list.add(f.getName());
                    }
                } else if (isRecursive && f.isDirectory()) {
                    filterFileType(f, true);
                }
            }
        }
        return list;
    }

    //获取指定文件夹下的所有文件名
    public void getAllFileName(String path, List<String> listFileName) {
        File file = new File(path);
        String[] names = file.list();
        if (names != null) {
            String[] completNames = new String[names.length];
            for (int i = 0; i < names.length; i++) {
                completNames[i] = names[i];
                listFileName.add(completNames[i]);
            }
        }
    }

    /**
     * 导入预开票文件 每天导入前先删除数据库原有数据
     * gcy 2020-01-22 Gai
     *
     * @throws IOException
     * @throws ParseException
     */
    public void importPreinvApply() throws IOException {
        logger.info("导入预开票文件方法开始执行");

        long start = System.currentTimeMillis();
        List<ImportPreinvApply> lists = preinvApplyService.findByImportPreinvApply();// 查询所有导入账户信息
        if (lists.size() != 0) {
            preinvApplyService.deleteAll();// 删除数据
        }

        List<String> list1 = new ArrayList<>();
        getAllFileName(PREINVAPPLY_UPLOAD, list1);
        //getAllFileName("D:\\20210121\\",list1);
        //File file = new File("D:\\20210121");
        //System.out.println("路径为"+list1);
        if (!list1.isEmpty()) {
            for (String s : list1) { //循环多个路径
                //if (s.contains("PREINV_DATA_")) {
                if (s.contains("PREINV_DATA_") && s.contains(getTime())) {
                    File fileObj = new File(PREINVAPPLY_UPLOAD + s);
                    logger.info("地址为" + PREINVAPPLY_UPLOAD + s);
                    //File fileObj = new File("D:\\20210121\\"+s);
                    //logger.info("地址为"+"D:\\20210121\\"+s);
                    // 如果文件不存在，直接返回
                    if (!fileObj.exists()) {
                        logger.info("importPreinvApply方法---文件不存在！");
                        return;
                    }
                    FileInputStream fis = new FileInputStream(fileObj);
                    BufferedReader bf = new BufferedReader(new InputStreamReader(fis, "GBK"));
                    String line = null;
                    List<ImportPreinvApply> list = new ArrayList<ImportPreinvApply>();
                    while ((line = bf.readLine()) != null) {
                        String[] item = line.split("&");
                        try {
                            ImportPreinvApply importPreinvApply = new ImportPreinvApply();
                            importPreinvApply.setOrder_id(item[0]);//订单编号<->订单流水0
                            importPreinvApply.setContract_no(item[1]);//账户ID<->账户号码1
                            importPreinvApply.setAccount_type(item[2]);//账户类型2
                            importPreinvApply.setProduct_name(item[3]);//产品名称3
                            importPreinvApply.setBusiness_number(item[4]);//业务号码/成员号码4
                            importPreinvApply.setInv_code(item[5]);//发票代码5
                            importPreinvApply.setInv_number(item[6]);//发票号码6
                            importPreinvApply.setTotalAmount(item[7]);//工单总金额7
                            importPreinvApply.setAmount_cashed(item[8]);//实际回款金额8
                            importPreinvApply.setLastCollDate(formatDate(item[9]));//最后回款日期9
                            importPreinvApply.setRemainingOutAmount(item[10]);//剩余欠缴金额10
                            importPreinvApply.setDrawer(item[11]);//开票人11
                            importPreinvApply.setLogin_no(item[12]);//开票工号12
                            importPreinvApply.setInvoicedAmount(item[13]);//开票金额13
                            importPreinvApply.setUpdate_time(formatDate(item[14]));//操作日期14
                            importPreinvApply.setOrder_state(item[15]);//订单状态15
                            importPreinvApply.setRedState(item[16]);//冲红状态16
                            list.add(importPreinvApply);
                        } catch (Exception e) {
                            File f = new File(PREINVAPPLY_UPLOAD + getTime() + "log.txt");
                            //File f = new File("D:\\20210121\\"+getTime()+"log.txt");
                            //用FileOutputSteam包装文件，并设置文件可追加
                            OutputStream out = new FileOutputStream(f, true);
                            String a = "";
                            for (int i = 0; i < item.length; i++) {
                                a += item[i] + "|";
                            }
                            out.write(a.getBytes()); //向文件中写入数据
                            out.write('\r'); // \r\n表示换行
                            out.write('\n');
                            out.close(); //关闭输出流
                            System.out.println("写入成功！");
                        }
                    }
                    preinvApplyService.addImportPreinvApply(list);// 新增导入预开票信息
                    long end = System.currentTimeMillis();
                    logger.info("importPreinvApply方法执行耗时" + (end - start) / 1000 + "s");
                }
            }
        } else {
            logger.info("没有文件！");
        }
    }


    /**
     * 根据导入的数据修改数据库原有数据
     */
    public void updatepreinvApplyDet() {
        /*
         * List<ImportPreinvApply> lists =
         * preinvApplyService.findByImportPreinvApply();
         * System.out.println("数量1" + lists.size()); // 查询所有导入账户信息
         * List<PreinvApplyDet> plist =
         * preinvApplyService.findByPreinvApplyDets();// 查询所有预开票信息 for
         * (ImportPreinvApply importPreinvApply : lists) { for (PreinvApplyDet
         * preinvApplyDet : plist) { if
         * (importPreinvApply.getOrder_id().equals(preinvApplyDet.getInvNo())) {
         * System.out.println("订单编号：" + preinvApplyDet.getInvNo());
         * preinvApplyDet.setInvState(importPreinvApply.getOrder_state());//
         * 订单状态
         * preinvApplyDet.setRealRecAmout(importPreinvApply.getPayed_owd()); //
         * 实际回收金额
         * preinvApplyDet.setRealRecDate(importPreinvApply.getUpdate_time()); //
         * 实际回收时间 preinvApplyService.updatePreinvApplyDet(preinvApplyDet); //
         * 修改账户信息 } } }
         */
        long start = System.currentTimeMillis();
        preinvApplyService.updatePreinvApplyDetByProc();
        long end = System.currentTimeMillis();
        System.out.println("updatepreinvApplyDet方法执行耗时" + (end - start) / 1000 + "s");
    }

    /**
     * 改变逾期状态
     */
    public void updateOver() {
        List<PreinvApplyDet> list = preinvApplyService.findByNotInvalid();// 根据预开票信息查询账户信息
        for (PreinvApplyDet preinvApplyDet : list) {
            int money; // 实际回收金额
            int amout; // 开票金额
            if (preinvApplyDet.getRealRecAmout() == null || "".equals(preinvApplyDet.getRealRecAmout())) {
                money = 0;
            } else {
                money = Integer.valueOf(preinvApplyDet.getRealRecAmout());
            }
            amout = Integer.valueOf(preinvApplyDet.getInvAmout());
            // 实际回收金额小于开票金额，计算逾期
            if (money < amout) {
                // 计划回收时间和当前时间的天数差
                int days = difference(preinvApplyDet.getRecDate(), new Date());
                if (days >= OVERDUE_DAY) {
                    preinvApplyDet.setIsOver("1");// 改为已逾期
                }
                // System.out.println("改变状态");
                preinvApplyService.updatePreinvApplyDet(preinvApplyDet); // 修改账户信息
            }
        }
    }

    /**
     * 逾期生成告警待办
     */

    public void saveWait() {
        List<PreinvApplyDet> list = preinvApplyService.findByNotInvalid();// 根据预开票信息查询账户信息
        System.out.println("逾期告警待办数量：" + list.size());
        for (PreinvApplyDet preinvApplyDet : list) {
            int money; // 实际回收金额
            int amout; // 开票金额
            if (preinvApplyDet.getRealRecAmout() == null || "".equals(preinvApplyDet.getRealRecAmout())) {
                money = 0;
            } else {
                money = Integer.valueOf(preinvApplyDet.getRealRecAmout());
            }
            amout = Integer.valueOf(preinvApplyDet.getInvAmout());
            // 实际回收金额小于开票金额
            if (money < amout) {
                int days = difference(preinvApplyDet.getRecDate(), new Date());
                SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(preinvApplyDet.getOpInvNo()));
                if (days >= OVERDUE_DAY) {
                    System.out.println("逾期账户号码:" + preinvApplyDet.getContrctNo());
                    WaitTask waitTask = new WaitTask();
                    waitTask.setName("[预开票逾期告警]" + preinvApplyDet.getContrctNo());// 待办名称
                    waitTask.setCreationTime(new Date());// 代办生成时间
                    waitTask.setUrl("jsp/preinvApply/overduePreinvApplayDet.jsp?id=" + preinvApplyDet.getUuid());
                    waitTask.setState(waitTask.HANDLE);// 状态为待处理
                    waitTask.setHandleUserId(user.getRowNo());// 处理人id
                    waitTask.setHandleUserName(user.getEmployeeName());// 处理人名称
                    waitTask.setHandleLoginName(user.getLoginName());// 处理人登录名
                    waitTask.setCreateUserId(-1);// 创建人id
                    waitTask.setCreateUserName("系统管理员");// 创建人名称
                    waitTask.setCreateLoginName("web_test");// 创建人登录名
                    waitTask.setCode(PreinvApplyDet.PREINVAPPLYDET);// 标识

                    service.saveWait(waitTask);
                }
            }
        }
    }

    /**
     * 获取当前时间前一天字符串
     */
    public String getTime() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMdd");// 格式化日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        date = (Date) calendar.getTime();
        return sdf.format(date);

    }

    /**
     * 计算两个时间的差值 返回天数
     */
    public int difference(Date date1, Date date2) {
        int days = (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24));
        return days;

    }

    /**
     * 日期转换2
     *
     * @param strDate
     * @return
     * @throws ParseException
     */
    public Date formatDate(String strDate) throws ParseException {
        Date date = null;

        if (strDate != null && !"".equals(strDate)) {
            StringBuilder sb = new StringBuilder(strDate);
            sb.insert(4, "-");
            sb.insert(7, "-");
            sb.insert(10, " ");
            sb.insert(13, ":");
            sb.insert(16, ":");
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = format.parse(sb.toString());
        }
        return date;
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/24 16:16
     * @Description 获取当天日期
     **/
    private static String getCurrentDate() {
        // 获取当前日期
        Calendar calendar = Calendar.getInstance();
        // 将日期减去一天
        calendar.add(Calendar.DAY_OF_YEAR, -1);
        // 定义日期格式
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMMdd");
        // 格式化日期,返回结果
        return dateFormat.format(calendar.getTime());
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/24 16:16
     * @Description 定期清理申请后长期未开票预开票订单
     **/
    public void updateData() {
        try {
            logger.info("定期清理申请后长期未开票预开票订单开始更新=============================");
            long start = System.currentTimeMillis();
            String path = "/EOMAPP/UploadFiles/PreinvData/balPreinvApply_" + getCurrentDate() + ".dat";//85本地路径
            logger.info("地址为：" + path);
            File fileObj = new File(path);
            // 如果文件不存在，直接返回
            if (!fileObj.exists()) {
                logger.info(path + "---文件不存在！");
                return;
            }

            FileInputStream fis = new FileInputStream(fileObj);
            BufferedReader br = new BufferedReader(new InputStreamReader(fis, "UTF-8"));

            String line;
            while ((line = br.readLine()) != null) {
                preinvApplyService.updateInvState(line);
            }
            br.close();// 关闭文件
            long end = System.currentTimeMillis();
            logger.info("updateData方法执行耗时" + (end - start) / 1000 + "s");
        } catch (Exception e) {
            logger.error("定期清理申请后长期未开票预开票订单更新数据错误==>" + e.getMessage(), e);
        }
    }
}
