package com.xinxinsoft.service.holiday;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.text.SimpleDateFormat; 
import java.util.Calendar;
import java.util.Date; 
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Properties; 
import java.util.Random; 
//import java.util.concurrent.Callable;
import java.util.concurrent.CopyOnWriteArrayList;   
//import java.util.concurrent.CountDownLatch;
//import java.util.concurrent.ExecutorService;
//import java.util.concurrent.Executors;
//import java.util.concurrent.FutureTask;

import javax.servlet.http.HttpServletResponse;

import jxl.Cell;
import jxl.CellType;
import jxl.CellView;
import jxl.DateCell;
import jxl.Sheet;
import jxl.Workbook;
import jxl.write.DateFormat;
import jxl.write.DateTime;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.transform.Transformers;

import com.google.gson.GsonBuilder;   
import com.xinxinsoft.entity.commonSingManagement.OrderForm;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.holiday.Holiday;
import com.xinxinsoft.entity.holiday.TimeConsume; 
import com.xinxinsoft.utils.common.DefaultDao;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.PagePO;
import com.xinxinsoft.utils.easyh.PageRequest;
import com.xinxinsoft.utils.easyh.PageRequest.Like;
import com.xinxinsoft.utils.easyh.PageResponse;
//import com.xinxinsoft.Threads.Tickets;

import org.apache.poi.hssf.usermodel.HSSFWorkbook; 

/**
 * 更新耗时服务
 * <AUTHOR>
 *
 */
public class HolidayService extends DefaultDao<Holiday>{
	private static int ADD_HOURS = 8;
	private static int WORK_START_HOUR = 9;
	private static int WORK_START_MINUTE = 0;
	private static int WORK_END_HOUR = 17;
	private static int WORK_END_MINUTE = 0;
	private int count = 0;//批量更新数量
	
	static{
		InputStream inStream = HolidayService.class.getClassLoader().getResourceAsStream("WebService-config.properties");
		Properties props = new Properties();
		try {
			props.load(inStream);
			ADD_HOURS = Integer.parseInt(props.getProperty("ADD_HOURS"));
			WORK_START_HOUR = Integer.parseInt(props.getProperty("WORK_START_HOUR"));
			WORK_END_HOUR = Integer.parseInt(props.getProperty("WORK_END_HOUR"));
			WORK_END_MINUTE = Integer.parseInt(props.getProperty("WORK_END_MINUTE"));
			WORK_START_MINUTE = Integer.parseInt(props.getProperty("WORK_START_MINUTE"));
		} catch (IOException e) {
			e.printStackTrace();
		}
	}
	
	/**
	 * 根据年份查询订单表中未完成订单
	 */
	@SuppressWarnings("unchecked")
	public List<OrderForm> findOrdersByStateAndYear(String state,String year){
		List<OrderForm> orderFormList = (List<OrderForm>)getSession().createQuery("FROM OrderForm "+" where state=?"+" and to_char(draftTime,'yyyy')=?").setString(0,state).setString(1, year).list();
		return orderFormList;
	}
	/*@SuppressWarnings("unchecked")
	public List<OrderForm> findOrdersByStateAndYear(String state){
		List<OrderForm> orderFormList = (List<OrderForm>)getSession().createQuery("FROM OrderForm "+" where state=?").setString(0,state).list();
		return orderFormList;
	}*/
	/**
	 * 查询所有订单的年份
	 */
	@SuppressWarnings("unchecked")
	public List<String> findOrderYear() {
		String sql = "select distinct to_char(drafttime, 'yyyy') from singorderform where drafttime is not null";
		return getSession().createSQLQuery(sql).list();
	}
	
	/**
	 * 计算两个日期之间的天数
	 */
	public static int getDaysInterval(Date fDate,Date tDate){
		if(fDate.after(tDate)){
			throw new IllegalArgumentException("日期顺序错误");
		}
		Calendar fCalendar = Calendar.getInstance();
		Calendar tCalendar = Calendar.getInstance();
		fCalendar.setTime(fDate);
		tCalendar.setTime(tDate);
		
		int fYear = fCalendar.get(Calendar.YEAR);
		int tYear = tCalendar.get(Calendar.YEAR);
		
		int days = 0;//统计天数
		days -= fCalendar.get(Calendar.DAY_OF_YEAR);
		days += tCalendar.get(Calendar.DAY_OF_YEAR);
		
		for(int i=0;i<Math.abs(tYear - fYear);i++){
			days += fCalendar.getActualMaximum(Calendar.DAY_OF_YEAR);
			fCalendar.add(Calendar.YEAR, 1);
		}
		return days;
	}

	/**
	 * 计算创建时间与当前时间之间的节假日天数
	 */
	public int dateCount(String orderDate,String currentDate) {
		String sql = "select count(*) from gholiday where datev between to_date(?,'yyyy-mm-dd') and to_date(?,'yyyy-mm-dd')";
		Object obj = getSession().createSQLQuery(sql).setString(0, orderDate).setString(1, currentDate).uniqueResult();
		return  Integer.parseInt(obj.toString());
	}
	/**
	 * 查询所有法定节假日日期
	 */
	@SuppressWarnings("unchecked")
	public List<String> findAllHolidaysByYear(){
		String sql = "SELECT to_char(DATEV,'yyyyMMdd') FROM gholiday";
		return getSession().createSQLQuery(sql).list();
	}
	/**
	 * 获取所有节假日年份
	 */
	@SuppressWarnings("unchecked")
	public List<String> findAllHolidayYears(){
		String sql = "SELECT distinct to_char(DATEV,'yyyy') FROM gholiday";
		return getSession().createSQLQuery(sql).list();
	}
	/**
	 * 根据年份查询当年节假日
	 */
	@SuppressWarnings("unchecked")
	public List<String> getAllHolidayByYear(String year){
		String sql = "SELECT to_char(DATEV,'yyyyMMdd') FROM gholiday  where TO_CHAR(DATEV,'yyyy')=?";
		return getSession().createSQLQuery(sql).setString(0, year).list();
	}
	/**
	 * 根据日期判断导入日期是否存在
	 */
	@SuppressWarnings("rawtypes")
	public boolean judgeDateIsExist(String year){
		String sql = "SELECT * FROM gholiday where to_char(DATEV,'yyyyMMdd')=?";
		List list = getSession().createSQLQuery(sql).setString(0, year).list();
		return list.size() > 0 ? true : false;
	}
	
	/**
	 * 归档后更新耗时表
	 * @param orderForm
	 */
	public void orderFormFinish2TimeConSume(OrderForm orderForm){
		Date orderFinsihDate = orderForm.getOrderCompletionTime();
		List<String> holidayList = this.findAllHolidaysByYear();
		
		if(null == orderForm.getOrderCompletionTime()){
			orderForm.setOrderCompletionTime(new Date());
		}
		TimeConsume timeConsume = (TimeConsume)getSession().createQuery("FROM TimeConsume WHERE orderFormId=? and orderFormNum=?").setString(0, orderForm.getOrderId()).setString(1, orderForm.getOrderNumber()).uniqueResult();
		if(timeConsume != null){
			getSession().delete(timeConsume);
		}
		timeConsume = judgCount(orderForm, orderFinsihDate,holidayList);
		timeConsume.setOrderFormId(orderForm.getOrderId());
		timeConsume.setOrderFormNum(orderForm.getOrderNumber());
		timeConsume.setState(1);
		getSession().save(timeConsume);
		getSession().flush();
		getSession().clear();
	}
	/**
	 * 更新耗时表:count+工作日
	 * @param orderForm  订单信息对象
	 * @param currentDate	结束日期：当前日期或订单完成日期
	 * @param holidayList	节假日列表
	 * @param state	设置订单状态：0未完成，1已完成
	 */
	public void updateTimeConsumePlusWorkDayHours(OrderForm orderForm,Date currentDate,List<String> holidayList,int state){
		TimeConsume timeConsume = (TimeConsume)getSession().createQuery("FROM TimeConsume WHERE orderFormId=? and orderFormNum=?").setString(0, orderForm.getOrderId()).setString(1, orderForm.getOrderNumber()).uniqueResult();
		if(timeConsume != null ){
			/*if(holidayList.contains(sdf.format(currentDate))){
				return;
			}*/
			if(timeConsume.getState() == 0){
				if(state == 1){
					TimeConsume newTimeConsume = judgCount(orderForm, currentDate,holidayList);
					timeConsume.setCount(newTimeConsume.getCount());
				}else if(state == 0){
					timeConsume.setCount(timeConsume.getCount() + ADD_HOURS * 60);
				}
				timeConsume.setState(state);
				getSession().update(timeConsume);
				count++;
			}
		}else{ 
			timeConsume = judgCount(orderForm, currentDate,holidayList);
			timeConsume.setOrderFormId(orderForm.getOrderId());
			timeConsume.setOrderFormNum(orderForm.getOrderNumber());
			timeConsume.setState(state);
			getSession().save(timeConsume);
			count++;
		}
		if(count % 100 == 0){
			getSession().flush();
			getSession().clear();
			count = 0;
		}
	}
	/**
	 * 判断耗时
	 * @param orderForm
	 * @param currentDate
	 * @return
	 */
	@SuppressWarnings("deprecation")
	private TimeConsume judgCount(OrderForm orderForm, Date currentDate,List<String> holidayList) {
		TimeConsume timeConsume;
		SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
		// 计算之间的节假日
		String orderFormDraftTimeStr = sdf.format(orderForm.getDraftTime());
		int holidays = dateCount(orderFormDraftTimeStr, sdf.format(currentDate));
		Date date = orderForm.getDraftTime();
		// 计算创建时间与当前日期之间的天数
		int diffDays = getDaysInterval(orderForm.getDraftTime(), currentDate) + 1;
		
		timeConsume = new TimeConsume();
		//如果订单在节假日生成
		if(holidayList.contains(orderFormDraftTimeStr)){
			if(holidayList.contains(sdf.format(currentDate))){
				timeConsume.setCount((diffDays-holidays)*ADD_HOURS * 60);
			}else{
				timeConsume = setDayCount(currentDate, sdf, holidays, date,diffDays,true);
			}
		}else{
			if(holidayList.contains(sdf.format(currentDate))){
				//起草相关时间
				int hour = date.getHours();
				int minute = date.getMinutes();
				if((hour>WORK_START_HOUR) || (hour == WORK_START_HOUR && minute > WORK_START_MINUTE)){
					if((hour<WORK_END_HOUR) || (hour == WORK_END_HOUR && minute <= WORK_END_MINUTE)){
						int dayCount = WORK_END_HOUR * 60 + WORK_END_MINUTE - hour * 60 - minute;
						timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60 + dayCount);
					}else if((hour > WORK_END_HOUR) || (hour == WORK_END_HOUR && minute > WORK_END_MINUTE)){
						timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60);
					}
				}else if((hour < WORK_START_HOUR) || (hour == WORK_START_HOUR && minute <= WORK_START_MINUTE)){
					timeConsume.setCount((diffDays-holidays)*ADD_HOURS * 60);
				}
			}else{
				timeConsume = setDayCount(currentDate, sdf, holidays, date,diffDays,false);
			}
		}
		return timeConsume;
	}
	@SuppressWarnings("deprecation")
	private TimeConsume setDayCount(Date currentDate, SimpleDateFormat sdf,
			int holidays, Date date, int diffDays ,boolean flag) {
		TimeConsume timeConsume;
		int diffDay;
		//起草相关时间
		int hour = date.getHours();
		int minute = date.getMinutes();
		//完成相关时间
		int hourFinish = currentDate.getHours();
		int minuteFinish = currentDate.getMinutes();
		
		int dayCount = 0;
		timeConsume = new TimeConsume();
		
		//判断是否在工作时间
		if((hourFinish > WORK_END_HOUR) || (hourFinish == WORK_END_HOUR && minuteFinish >= WORK_END_MINUTE)){
			if(flag){
				timeConsume.setCount((diffDays-holidays)*ADD_HOURS * 60);
				return timeConsume;
			}
			if((hour>WORK_START_HOUR) || (hour == WORK_START_HOUR && minute > WORK_START_MINUTE)){
				if((hour<WORK_END_HOUR) || (hour == WORK_END_HOUR && minute <= WORK_END_MINUTE)){
					dayCount = WORK_END_HOUR * 60 + WORK_END_MINUTE - hour * 60 - minute;//当天耗时
					timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60 + dayCount);
				}else if((hour > WORK_END_HOUR) || (hour == WORK_END_HOUR && minute > WORK_END_MINUTE)){
					timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60);
				}
			}else if((hour < WORK_START_HOUR) || (hour == WORK_START_HOUR && minute <= WORK_START_MINUTE)){
				timeConsume.setCount((diffDays-holidays)*ADD_HOURS * 60);
			}
		}else if((hourFinish < WORK_START_HOUR) || (hourFinish == WORK_START_HOUR && minuteFinish <= WORK_START_MINUTE)){
			if(flag){
				timeConsume.setCount((diffDays-holidays - 1)*ADD_HOURS * 60);
				return timeConsume;
			}
			if((hour>WORK_START_HOUR) || (hour == WORK_START_HOUR && minute > WORK_START_MINUTE)){
				if((diffDays-holidays-2) <= 0){
					diffDay = 0;
				}else{
					diffDay = diffDays-holidays-2;
				}
				if((hour<WORK_END_HOUR) || (hour == WORK_END_HOUR && minute <= WORK_END_MINUTE)){
					dayCount = WORK_END_HOUR * 60 + WORK_END_MINUTE - hour * 60 - minute;
					timeConsume.setCount(diffDay*ADD_HOURS * 60 + dayCount);
				}else if((hour > WORK_END_HOUR) || (hour == WORK_END_HOUR && minute > WORK_END_MINUTE)){
					timeConsume.setCount(diffDay*ADD_HOURS * 60);
				}
			}else if((hour < WORK_START_HOUR) || (hour == WORK_START_HOUR && minute <= WORK_START_MINUTE)){
				timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60);
			}
		}else{
			if(flag){
				dayCount = Math.abs((hourFinish * 60 + minuteFinish) - (WORK_START_HOUR * 60 + WORK_START_MINUTE));
				timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60 + dayCount);
				return timeConsume;
			}
			if((hour>WORK_START_HOUR) || (hour == WORK_START_HOUR && minute > WORK_START_MINUTE)){
				if((hour<WORK_END_HOUR) || (hour == WORK_END_HOUR && minute <= WORK_END_MINUTE)){
					if(sdf.format(date).equals(sdf.format(currentDate))){
						dayCount = Math.abs((hourFinish * 60 + minuteFinish) - (hour * 60 + minute));
						timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60 + dayCount);
					}else{
						dayCount = Math.abs((hourFinish * 60 + minuteFinish - WORK_START_HOUR * 60 - WORK_START_MINUTE)) + Math.abs(WORK_END_HOUR * 60 + WORK_END_MINUTE - hour * 60 - minute);
						if((diffDays-holidays-2) <= 0){
							diffDay = 0;
						}else{
							diffDay = diffDays-holidays-2;
						}
						timeConsume.setCount(diffDay*ADD_HOURS * 60 + dayCount);
					}
				}else if((hour > WORK_END_HOUR) || (hour == WORK_END_HOUR && minute > WORK_END_MINUTE)){
					dayCount = Math.abs(( hourFinish * 60 + minuteFinish - WORK_START_HOUR * 60 - WORK_START_MINUTE));
					if((diffDays-holidays-2) <= 0){
						diffDay = 0;
					}else{
						diffDay = diffDays-holidays-2;
					}
					timeConsume.setCount(diffDay*ADD_HOURS * 60 + dayCount);
				}
			}else if((hour < WORK_START_HOUR) || (hour == WORK_START_HOUR && minute <= WORK_START_MINUTE)){
				dayCount = Math.abs((hourFinish * 60 + minuteFinish - WORK_START_HOUR * 60 - WORK_START_MINUTE));
				timeConsume.setCount((diffDays-holidays-1)*ADD_HOURS * 60 + dayCount);
			}
		}
		return timeConsume;
	}
	public String getPageInfo(PageRequest req) throws Exception {
		req.setRows(req.getInteger("pagesize"));
		req.setQuery("from Holiday h");
		req.setQueryCount("select count(*) from Holiday h");
		req.appendQueryWhere(" where 1 = 1");
		req.useHql(true);
		req.setTargetClass(Map.class);
		
		if(req.addParamter("startDate", String.class,true,Like.none)){
			if(req.addParamter("endDate", String.class,true,Like.none)){
				req.appendQueryWhere(" and h.dateV between to_date(?,'yyyy-MM-dd') and to_date(?,'yyyy-mm-dd') ");
			}else{
				req.appendQueryWhere(" and h.dateV=to_date(?,'yyyy-MM-dd')");
			}
		}else{
			if(req.addParamter("endDate", String.class,true,Like.none)){
				req.appendQueryWhere(" and h.dateV=to_date(?,'yyyy-MM-dd') ");
			}
		}
		if(req.addParamter("name", String.class,true,Like.full)){
			req.appendQueryWhere(" and h.name like ?");
		}
		PageResponse response = grid(req);
		String json = response.toJson(new GsonBuilder().setDateFormat("yyyy-MM-dd"));
		return json;
	}
	
	/**
	 * 
	 * @param req
	 */
	@SuppressWarnings("unchecked")
	protected PageResponse grid(PageRequest req) throws Exception{
		PageResponse res = new PageResponse();
		PagePO page = req.getPagePo();
		if(req.isHql()){
			page.setData(findByPage(page.getCpage(), page.getShowNumber(), req.getQuery(), req.getParameters()));
			page.setTotNumber(getTotalCount(req.getQueryCount(), req.getParameters()));
		}
		else if(req.isAutofill()){
			page.setData(findBySqlPage_autofill(req.getTargetClass(), page.getCpage(), page.getShowNumber(), req.getQuery(), req.getParameters()));
			page.setTotNumber(getSqlTotalCount(req.getQueryCount(), req.getParameters()));
		}
		else{
			page.setData(findBySqlPage(req.getTargetClass(), page.getCpage(), page.getShowNumber(), req.getQuery(), req.getParameters()));
			page.setTotNumber(getSqlTotalCount(req.getQueryCount(), req.getParameters()));
		}
		
		res.setPagePo(page);
		return res;
	}
	/**
	 * 根据id获取节假日
	 */
	public Holiday getEntityById(String id){
		return this.findById(Holiday.class, id);
	}
	/**
	 * 删除节假日
	 */
	public void deleteEntity(Holiday entity){
		this.delete(entity);
	}
	public void updateEntity(Holiday entity) {
		this.update(entity);
	}
	/**
	 * 获取week
	 * @param date
	 * @return
	 */
	public String getWeekFromDate(Date date){
		Calendar calendar = Calendar.getInstance();
		calendar.setTime(date);
		int week = calendar.get(Calendar.DAY_OF_WEEK);
		String weekStr = "";
		switch (week) {
		case 1:
			weekStr = "星期日";
			break;
		case 2:
			weekStr = "星期一";
			break;
		case 3:
			weekStr = "星期二";
			break;
		case 4:
			weekStr = "星期三";
			break;
		case 5:
			weekStr = "星期四";
			break;
		case 6:
			weekStr = "星期五";
			break;
		case 7:
			weekStr = "星期六";
			break;
		default:
			weekStr = "";
			break;
		}
		return weekStr;
	}
	/**
	 * 添加节假日
	 * @param holiday
	 */
	public void addEntity(Holiday entity) {
		this.save(entity);
	}
	
	/**
	 * 通过读取配置文件获取节假日服务启用状态
	 */
	public String getHolidayTaskState(){
		InputStream is = HolidayService.class.getClassLoader().getResourceAsStream("WebService-config.properties");
		Properties props = new Properties();
		String state = "0";
		try {
			props.load(is);
			state = props.getProperty("CONTROL_HOLIDAYTASK_STATE","0");
		} catch (IOException e) {
			e.printStackTrace();
		}
		return state;
	}
	/**
	 * 清空数据库表数据
	 */
	public void clearHoliday(){
		String sql = "delete Holiday";
		this.getSession().createQuery(sql).executeUpdate();
	}
	/**
	 * 获取所有数据
	 * @return
	 */
	@SuppressWarnings("rawtypes")
	public List getAll(){
		List list =  this.getSession().createSQLQuery("select * from gholiday").addEntity(Holiday.class).list();
		return list;
	}
	/**
	 * 保存所有数据
	 */
	public void saveAll(List<Holiday> entities){
		this.getHibernateTemplate().saveOrUpdateAll(entities);
	}
	/**
	 * 导出Excel数据
	 * @param path 文件路径
	 * @param list	数据内容
	 */
	@SuppressWarnings("rawtypes")
	public void exportExcel(String path,List list){
		WritableWorkbook book = null;
		String[] info = {"日期","名称","星期","农历"};
		HttpServletResponse response = null;
		try{
			File file = new File(path);
			if (!file.exists()) {
				file.createNewFile();
			}
			response = ServletActionContext.getResponse();
			book = Workbook.createWorkbook(file);
			//生成名为sheet1的工作表，参数0表示第一页
			WritableSheet sheet = book.createSheet("sheet1", 0);
			//表头导航
			for(int j=0;j<info.length;j++){
				Label label = new Label(j,0,info[j]);
				sheet.addCell(label);
			}
			//添加数据
			for(int i = 0;i<list.size();i++){
				CellView cellView = new CellView();
				cellView.setAutosize(true);
				sheet.setColumnView(0, cellView);
				Holiday holiday = (Holiday)list.get(i);
				Date day= holiday.getDateV();
				DateFormat df=new DateFormat("yyyy/MM/dd");
				DateTime cell=new DateTime(0, i+1,day,new WritableCellFormat(df));
				sheet.addCell(cell);
				sheet.addCell(new Label(1, i+1, holiday.getName()));
				sheet.addCell(new Label(2, i+1, holiday.getWeek()));
				sheet.addCell(new Label(3, i+1, holiday.getLunarCalendar()));
			}
			//定稿数据并关闭文件
			book.write();
			book.close();
			
			byte[] data = FileUtil.toByteArray2(path);
			String name = "节假日数据表";
			String fileName = URLEncoder.encode(name, "UTF-8");
			response.reset();
			response.setHeader("Content-Disposition", "attachment;filename=\""+fileName + ".xls"+"\"");
			response.addHeader("Content-Length", ""+data.length);
			response.setContentType("application/octet-stream;charset=UTF-8");
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
			outputStream.write(data);
			outputStream.flush();
			outputStream.close();
			File fe = new File(path);
			fe.delete();
		}catch(Exception e){
			e.printStackTrace();
			File file = new File(path);
			if(file.exists() && !file.isDirectory()){
				file.delete();
			}
			try {
				response.getWriter().write("<script>alert('导出Excel失败!');history.go(-1);");
				response.getWriter().flush();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
		}
	}
	
	@SuppressWarnings("rawtypes")
	public void batchImport(){
		List holidayList = null;
		Session session =null;
		org.hibernate.Transaction transaction = null;
		try {
			boolean flag = false;
			//查询出所有数据
			holidayList = this.getAll();
			
			session = this.getSession();
			transaction = session.beginTransaction();
			//清空数据
			this.clearHoliday();
				if(!flag){
					Holiday holiday = new Holiday();
					holiday.setName("ssdsfsdwwwwww");
					holiday.setWeek("33sdfdswwww");
					holiday.setLunarCalendar("ssssttttttsdfwwwwwwsdf");
					holiday.setDateV(new Date());
					this.addEntity(holiday);
			}
		} catch (Exception e) {
			e.printStackTrace();
			//清空数据
			this.clearHoliday();
			//回滚数据
			if(holidayList.size()>0 && holidayList != null){
				transaction.rollback();
			}
		}finally{
			if(transaction.isActive()){
				transaction.commit();
			}
			if(session != null){
				session.close();
			}
			
		}
	}
	@SuppressWarnings({ "unused", "rawtypes" })
	public void batchDataImport(File uploadify,HttpServletResponse response) {
		String result = "ERROR";
		InputStream inputStream = null;
		List holidayList = null;
		Session session =null;
		Transaction transaction = null;
		try {
			inputStream = new FileInputStream(uploadify);
			Workbook workbook = Workbook.getWorkbook(inputStream);
			Sheet sheet = workbook.getSheet(0);
			Field[] fields = Holiday.class.getDeclaredFields();
			boolean flag = false;
			//查询出所有数据
			holidayList = this.getAll();
			session = this.getSessionFactory().getCurrentSession();
			transaction = session.beginTransaction();
			//清空数据
			this.clearHoliday();
			//循环行
			if(sheet.getRows()<=1){
				response.setContentType("text/html;charset=UTF-8");
				try {
					response.getWriter().write("FORMAT_ERROR");
					response.getWriter().flush();
					response.getWriter().close();
				} catch (IOException e1) {
					e1.printStackTrace();
				}
				return;
			}
			for(int i=1;i<sheet.getRows();i++){
				Cell cell = null;
				Holiday entity = new Holiday();
				//循环列
				for(int j=0;j<sheet.getColumns();j++){
					cell = sheet.getCell(j,i);
					fields[j+1].setAccessible(true);
					if(cell.getType() == CellType.DATE){
						DateCell dateCell = (DateCell)cell;
						fields[j+1].set(entity, dateCell.getDate());
					}else{
						if(!"".equals(cell.getContents())){
							fields[j+1].set(entity, cell.getContents());
						}else{
							flag = true;
							result = "FORMAT_ERROR";
							throw new Exception();
						}
					}
				}
				if(!flag){
					addEntity(entity);
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			response.setContentType("text/html;charset=UTF-8");
			try {
				response.getWriter().write(result);
				response.getWriter().flush();
				response.getWriter().close();
			} catch (IOException e1) {
				e1.printStackTrace();
			}
			//清空数据
			this.clearHoliday();
			//回滚数据
			transaction.rollback();
		} 
	}
	/**
	 *  批量导入用户信息数据
	 * @param uploadify
	 * @param response
	 */
	public void batchUsersDaoImport(File uploadify,String suffix, HttpServletResponse response) {
		String result = "ERROR";
		Integer notAddcount=0,addCount=0;
		StringBuilder stringBuilder = new StringBuilder();
        stringBuilder.append("[");
		try{
			 FileInputStream fis = new FileInputStream(uploadify);
			 org.apache.poi.ss.usermodel.Workbook workbook = null;
			 //判断excel的两种格式xls,xlsx
			 if(suffix.toLowerCase().endsWith("xlsx")){
				 		workbook = new XSSFWorkbook(fis);
			 }else if(suffix.toLowerCase().endsWith("xls")){
			 			workbook = new HSSFWorkbook(fis);
			 }
			 //得到sheet的总数  
			int numberOfSheets = workbook.getNumberOfSheets();
			  System.out.println("一共"+numberOfSheets+"个sheet");
			  CopyOnWriteArrayList<HashMap<String,String>> mapdepts = findSystemDepts();	//所有部门信息
				CopyOnWriteArrayList<Integer>  depetno =  findSystemDeptsNo();				//查询所有部门编号
				CopyOnWriteArrayList<Integer> userno =  findSystemUserNo();					//查询所有用户编号
			 for(int i=0; i < numberOfSheets; i++){
				  //得到第i个sheet  
			     org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(i);
				 System.out.println(sheet.getSheetName()+"  sheet"); 
				 //得到行的迭代器  
				  Iterator<org.apache.poi.ss.usermodel.Row> rowIterator = sheet.iterator();
				
				  int rowCount=0;
				  Boolean isR = false ,isDept = false; 
				 //循环每一行
				 while (rowIterator.hasNext())
				  { 
					 rowCount++;
					//得到一行对象
			         org.apache.poi.ss.usermodel.Row row = rowIterator.next();
			       //得到列对象 
					 Iterator<org.apache.poi.ss.usermodel.Cell> cellIterator = row.cellIterator();
					 SystemUser user = new SystemUser();//用户信息
					 SystemDept dept = new SystemDept();//部门信息
					 int columnCount=0;
					 String paras="";
					 //循环每一列
					 isDept= cellIterator(cellIterator, paras, columnCount, rowCount, user, dept, isDept, mapdepts, depetno, userno);
					 
					 if(rowCount>1){
						  user.setLoginPwd("21218cca77804d2ba1922c33e0151105"); 
							  isR=isDbDates(user.getLoginName(), user.getMobile());
							  if(!isR){ 
								  saveUsers4A(user.getLoginName(),user.getEmployeeName(),user.getMobile());//报存Users_4A表中
								  user.setEmployDate(new Date());
								  saveUser(user);///保存数据
								  if(isDept){
										saveDept(dept);//保存部门
										//保存在表示是订单系统新建部门
										saveDeptTest(dept.getDepartmentName(),dept.getDepartmentNo(),dept.getDepartmentParentNo(),dept.getCompanyCode());
										HashMap<String,String>  mapd = new HashMap<String, String>();
										mapd.put("COMPANY_CODE", dept.getCompanyCode());
										mapd.put("DEPARTMENT_NO", String.valueOf(dept.getDepartmentNo()));
										mapd.put("DEPARTMENT_LEVEL", String.valueOf(dept.getDepartmentLevel()));
										mapd.put("DEPARTMENT_ORDER", dept.getDepartmentOrder());
										mapd.put("DEPARTMENT_PARENT_NO", String.valueOf(dept.getDepartmentParentNo()));
										mapd.put("DEPARTMENT_NAME", dept.getDepartmentName());
										mapdepts.add(mapd);
								  }
								  saveDeptOrUser(dept.getDepartmentNo(),user.getRowNo());//添加用户和部门关系表
								  saveUserOrRole(user.getRowNo());// 添加用户为普通用户权限，
								  saveTestUser(user.getRowNo(),user.getLoginName());//存储在指定的 systemtestusers 表中，保证晚上更新eip账号不会同该表中的用户信息
								  addCount++;
								  System.out.print(rowCount+"==");
                                 /// stringBuilder.append("{"+user.getLoginName()+","+user.getMobile()+"}");
							  } else{
								  notAddcount++;
								  logger.error(rowCount+" 行未添加数据："+user.getLoginName()+"<=>"+user.getMobile());
                                  stringBuilder.append(""+user.getLoginName()+"__"+user.getMobile()+",");
							  }
							  
					  }
				  }
			 }
			 fis.close();
			 logger.info(addCount+":实际添加=================未添加行数:"+notAddcount);
            stringBuilder.append("]");
            try {
                response.setContentType("text/html;charset=UTF-8");
                response.getWriter().write(stringBuilder.toString());
                response.getWriter().flush();
                response.getWriter().close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
		} catch(Exception e){
			e.printStackTrace();
			try {
                response.setContentType("text/html;charset=UTF-8");
                response.getWriter().write(result);
				response.getWriter().flush();
				response.getWriter().close();
			} catch (IOException e1) {
				e1.printStackTrace();
			} 
			 throw new RuntimeException("事务回滚，自定义"); 
		}
		 
	}
	
	/**
	 * 循环每一列
	 * @param cellIterator
	 * @param paras
	 * @param columnCount
	 * @param rowCount
	 * @param user
	 * @param dept
	 * @param isDept
	 * @param mapdepts
	 * @param depetno
	 * @param userno
	 */
	public Boolean cellIterator(Iterator<org.apache.poi.ss.usermodel.Cell> cellIterator,String paras,int columnCount,int rowCount,SystemUser user,SystemDept dept,Boolean isDept,CopyOnWriteArrayList<HashMap<String,String>> mapdepts,
			CopyOnWriteArrayList<Integer>  depetno,CopyOnWriteArrayList<Integer> userno){
		 //循环每一列
		  while (cellIterator.hasNext()){
			//得到单元格对象
			   org.apache.poi.ss.usermodel.Cell cell = cellIterator.next(); 
			   columnCount++;
			   //检查数据类型  
			   switch (cell.getCellTypeEnum()) {
	            case NUMERIC:
	            	//数据类型转换避免 正常数据出现#.##### 
	            	java.text.DecimalFormat formatter = new java.text.DecimalFormat("########");
	            	String str = formatter.format(cell.getNumericCellValue());
	            	paras =((str+"").trim());
	                break;
	            case STRING:
	            	paras=((cell.getStringCellValue()+"").trim());
	                break;
	            case FORMULA:
	            	paras="" ;
	            	 
	                break;
	            case BLANK:
	            	paras=("");
	            	 
	                break;
	            case BOOLEAN:
	            	paras=(""+cell.getBooleanCellValue());
	            	 
	                break;
	            case ERROR:
	            	paras=("" + cell.getErrorCellValue());
	            	 
	                break;
	            default:
	            	 
	               break;
			   } 
			
			 if(rowCount>1){
			  switch (columnCount) {
				case 1:
					user.setLoginName(paras);
					break;
				case 2:
					user.setMobile(paras);
					break;
				case 3:
					user.setEmployeeName(paras);
					break;
				case 4:
					break;
				case 5: 
					isDept = setParas(paras, user, mapdepts, columnCount, rowCount, depetno, userno, dept);
					break;
				case 6: 
					user.setBossUserName(paras);
					break;
				default:
					break;
				}
			 }
		  } 
		  return isDept;
	}
	/**
	 * 随机生成
	 * @param min 最小值
	 * @param max 最大值
	 * @return
	 */
	public static int randInt(int min, int max) {

	    // NOTE: Usually this should be a field rather than a method
	    // variable so that it is not re-seeded every call.
	    Random rand = new Random();
 
	    // nextInt is normally exclusive of the top value,
	    // so add 1 to make it inclusive
	    int randomNum = rand.nextInt((max - min) + 1) + min;

	    return randomNum;
	} 
	 
	/**
	 * 
	 * @param paras
	 * @param user
	 * @param mapdepts
	 * @param columnCount
	 * @param rowCount
	 */
	public synchronized Boolean setParas(String paras,SystemUser user,CopyOnWriteArrayList<HashMap<String,String>> mapdepts,Integer columnCount,Integer rowCount,List<Integer>  depetno,List<Integer> userno,SystemDept dept){
		 Boolean isR = false;
			 	String compaycode = "";
				String[] deptstr = paras.split("/");
				Integer deptno =null;
				String deptName = "";
				Integer level= 1; 
					for (int j = 0; j < deptstr.length; j++) {
						if(j==0){
							compaycode= isDeptsByCode(deptstr[j], mapdepts);
						}
						Integer deptno_1= isDepts(deptstr[j],mapdepts,deptno,compaycode); 
						level=j;
						if((deptno_1)!=null){
							deptName=deptstr[j];
							deptno =  deptno_1;
						}  else{
							break;
						}
					} 
					Integer user_no = randInt(133300,*********);
					Boolean isUserno = true;
					while(isUserno){ 
						if(userno.contains(java.math.BigDecimal.valueOf(user_no))){
							user_no = randInt(133300,*********);
							isUserno= true;
						}else{
							isUserno = false;
						}
					}
					userno.add(user_no);
					user.setRowNo(user_no); 
					dept.setDepartmentNo(deptno);
					
					Integer deptno_2 = isDepts("合作伙伴（政企）",mapdepts,deptno,compaycode);
					if(deptno_2!=null){
						dept.setDepartmentNo(deptno_2);
					}else{
						if(!"合作伙伴（政企）".equals(deptName.trim())){
							dept.setDepartmentOrder("666666");
							dept.setDepartmentName("合作伙伴（政企）");
							dept.setCreated(new Date());
							dept.setDepartmentLevel(level);
							dept.setDepartmentParentNo(deptno); 
							Integer dept_no = randInt(143300,*********);
							Boolean isDeptno = true;
							while(isDeptno){ 
								if(depetno.contains(java.math.BigDecimal.valueOf(dept_no))){
									dept_no = randInt(143300,*********);
									isDeptno= true;
								}else{
									isDeptno = false;
								}
							}
							depetno.add(dept_no);
							dept.setDepartmentNo(dept_no);
							dept.setVisible(1);
							dept.setCompanyCode(compaycode);
							isR=true;
						} 
					}
					
			
		return isR;
	}
	/**
	 * 查询全部 部门NO
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public CopyOnWriteArrayList<Integer> findSystemDeptsNo() {
		String sql = "select t.department_no from afr_systemdept t"; 
		return new CopyOnWriteArrayList<>( getSession().createSQLQuery(sql).list());
	}
	/**
	 * 查询全部 用户NO
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public CopyOnWriteArrayList<Integer> findSystemUserNo() {
		String sql = "select t.rowno from afr_systemuser t";
		return new CopyOnWriteArrayList<>( getSession().createSQLQuery(sql).list());
	}
	/**
	 * 根据名称部门编号验证
	 * @param name
	 * @param mapdepts
	 * @param depar_no
	 * @return
	 */
	public Integer isDepts(String name,CopyOnWriteArrayList<HashMap<String,String>> mapdepts,Integer depar_no,String compaycode){
		 for (HashMap<String, String> map : mapdepts) {
			 if((depar_no)==null){	
				if(map.get("DEPARTMENT_NAME").trim().equals(name)&& compaycode.equals(map.get("COMPANY_CODE").trim())){
					Object ob = map.get("DEPARTMENT_NO"); 
					return Integer.parseInt((ob.toString()+"").trim());
				}
			 }else{
				 Object obp = map.get("DEPARTMENT_PARENT_NO"); 
				 if(compaycode.equals(map.get("COMPANY_CODE"))&& (depar_no)==(Integer.parseInt(obp+""))  && map.get("DEPARTMENT_NAME").trim().equals(name.trim())   ){
					 Object ob = map.get("DEPARTMENT_NO"); 
						return Integer.parseInt((ob.toString()+"").trim());
					}
			 }
		}
		 return null;
	}
	/**
	 * 根据名称部门编号验证
	 * @param name
	 * @param mapdepts
	 * @param depar_no
	 * @return
	 */
	public String isDeptsByCode(String name,CopyOnWriteArrayList<HashMap<String,String>> mapdepts ){
		 for (HashMap<String, String> map : mapdepts) { 
				if(map.get("DEPARTMENT_NAME").equals(name) && "0".equals(String.valueOf(map.get("DEPARTMENT_LEVEL")))){
					return map.get("COMPANY_CODE");
				}
			 }
		 return "";
	}
	
	/**
	 * 保存  新建部门信息
	 * @param department_name
	 * @param department_no
	 * @param department_parent_no
	 * @param company_code
	 */
	public void saveDeptTest(String department_name, Integer department_no,Integer department_parent_no,String company_code){
		String sql = " insert into AFR_DEPT_TEST (DEPARTMENT_NAME,DEPARTMENT_NO,DEPARTMENT_PARENT_NO,COMPANY_CODE) values (?, ?, ?, ?)";
		getSession().createSQLQuery(sql).setParameter(0, department_name).setParameter(1, department_no).setParameter(2, department_parent_no).setParameter(3, company_code).executeUpdate();
	}
	
	/**
	 * 查出所有部门信息
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public  CopyOnWriteArrayList<HashMap<String,String>> findSystemDepts() {
		String sql = "select t.department_name,t.department_no,t.department_level,t.company_code,t.department_order,t.department_parent_no from afr_systemdept t ";
		return new CopyOnWriteArrayList<>(getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list());
	}
	
	/**
	 * 模拟用户信息保存
	 * @param user
	 */
	public  void saveUser(SystemUser user){
			Session session = this.getSession();
			session.merge(user);
			session.flush();
	}
	/**
	 * 模拟部门保存方法
	 * @param dept
	 */
	public  Boolean  saveDept(SystemDept dept){
		String sqlcount="select count(0) from afr_systemdept where department_name=? and department_no=? ";
		Object oj = getSession().createSQLQuery(sqlcount).setString(0, dept.getDepartmentName()).setInteger(1, dept.getDepartmentNo()).uniqueResult();
		if(Integer.parseInt(oj+"")==0){
			Session session = this.getSession();
			session.merge(dept);
			session.flush();
			return true;
		}
		return false;
	}
	//模拟用户部门关系保存方法
	public  Boolean saveDeptOrUser(Integer departmentNo, int rowNo) {
		String sqlcount="select count(0) from AFR_SYSTEM_DEPT_USER where ROWNO=? and DEPARTMENT_NO=?";
		Object oj = getSession().createSQLQuery(sqlcount).setInteger(0, rowNo).setInteger(1, departmentNo).uniqueResult();
		if(Integer.parseInt(oj+"")==0){
			String sql = "insert into AFR_SYSTEM_DEPT_USER (ROWNO, DEPARTMENT_NO, ORDERNO, OID, ISMAINDPT, EIP_UPDATETIME, INSERTTIME, DANGQIAN) values (?, ?, '666666', null, 'true', null, sysdate, null)";
			getSession().createSQLQuery(sql).setParameter(0, rowNo).setParameter(1, departmentNo).executeUpdate();
			return true;
		}
		return false;
	}
	 /**
	  * 模拟用户权限保存
	  * @param rowNo
	  */
	public   Boolean saveUserOrRole(Integer rowNo) {
		String sqlcount="select count(0) from system_user_role where ROW_NO=? and ROLE_ID='2' ";
		Object oj = getSession().createSQLQuery(sqlcount).setInteger(0, rowNo).uniqueResult();
		if(Integer.parseInt(oj+"")==0){
			String sql = "insert into system_user_role (ROW_NO, ROLE_ID ) values (?, ? )";
			getSession().createSQLQuery(sql).setInteger(0, rowNo).setInteger(1, 2).executeUpdate();
			return true;
		}
		return false;
	}
	/**
	 * 模拟用户信息表存储	
	 * @param rowNo
	 * @param login_name
	 */
	public   void saveTestUser(Integer rowNo,String login_name) {
			String sql = "insert into systemtestusers (TESTID, TESTNAME ) values (?, ? )";
			getSession().createSQLQuery(sql).setInteger(0, rowNo).setString(1, login_name).executeUpdate();
	}
	/**
	 * 模拟4A和eip关系
	 * @param login_name
	 * @param eName
	 * @param phone
	 * @return
	 */
	public   void saveUsers4A(String login_name,String eName,String phone ) {
			String sql = "insert into USERS_4A (LOGIN_ACCT, LOGIN_NAME, LOGIN_PWD, USER_ID, USER_NAME, MOBILE, ACCT_STATUS, EMAILS) values (?, ?, null, ?, ?, ?, '1', null)";
			getSession().createSQLQuery(sql).setString(0, login_name).setString(1, login_name).setString(2, login_name).setString(3, eName).setString(4, phone).executeUpdate();
		
	}
	
	/**
	 * 验证数据表中是否已经存在用户信息：
	 * @param login_name
	 * @param phone
	 * @return
	 */
	public  Boolean isDbDates(String login_name,String phone){  
			//查询4A表是否存在：
			String sqlcount="select count(0) from USERS_4A where LOGIN_ACCT=? or mobile=?";
			Object oj = getSession().createSQLQuery(sqlcount).setString(0, login_name).setParameter(1, phone).uniqueResult();
			
			if(Integer.parseInt(oj+"")>0){
				return true;
			}
			///查询系统模拟保留表是否存在
			String sqlcount_tes="select count(0) from systemtestusers where TESTNAME=? ";
			Object oj_tes = getSession().createSQLQuery(sqlcount_tes).setString(0, login_name).uniqueResult();
			if(Integer.parseInt(oj_tes+"")>0){
				return true;
			}
			//查询用户表是否存在
			String sqlcount_u="select count(0) from AFR_SYSTEMUSER where login_name=? or mobile=? ";
			Object oj_u = getSession().createSQLQuery(sqlcount_u).setParameter(0, login_name).setParameter(1, phone).uniqueResult();
			if(Integer.parseInt(oj_u+"")>0){ 
				return true;
			}
		return false;
	} 
//	public void batchUsersDaoImport(File uploadify,String suffix, HttpServletResponse response) {
//		String result = "ERROR";
//		try{
//			 FileInputStream fis = new FileInputStream(uploadify);
//			 org.apache.poi.ss.usermodel.Workbook workbook = null;
//			 //判断excel的两种格式xls,xlsx
//			 if(suffix.toLowerCase().endsWith("xlsx")){
//				 		workbook = new XSSFWorkbook(fis);
//			 }else if(suffix.toLowerCase().endsWith("xls")){
//			 			workbook = new HSSFWorkbook(fis);
//			 }
//			 //得到sheet的总数  
//			int numberOfSheets = workbook.getNumberOfSheets();
//			  System.out.println("一共"+numberOfSheets+"个sheet");
//			  CopyOnWriteArrayList<HashMap<String,String>> mapdepts = findSystemDepts();
//				CopyOnWriteArrayList<Integer>  depetno =  findSystemDeptsNo();
//				CopyOnWriteArrayList<Integer> userno =  findSystemUserNo(); 
//					  //得到第i个sheet  
//				     org.apache.poi.ss.usermodel.Sheet sheet = workbook.getSheetAt(0);
//					 System.out.println(sheet.getSheetName()+"  sheet"); 
//					 //得到行的迭代器   
//			 int POOL_NUM = 4;
//				   ExecutorService executorService = Executors.newFixedThreadPool(5);  
//				   // 创建两个个计数器
//			        CountDownLatch latch = new CountDownLatch(POOL_NUM); 
//			        // 循环创建线程
//			    int start = sheet.getLastRowNum();
//			        for(int i = 0; i<POOL_NUM; i++)  
//			        {   
//			            executorService.submit(new Thread(new FutureTask<Object>(new Tickets<Object>(latch, sheet,  1 , start, this, mapdepts, depetno, userno))));
//			        } 
//			       latch.await(); 
//				
//			 fis.close();
//			 
//		} catch(Exception e){
//			e.printStackTrace();
//			response.setContentType("text/html;charset=UTF-8");
//			try {
//				response.getWriter().write(result);
//				response.getWriter().flush();
//				response.getWriter().close();
//			} catch (IOException e1) {
//				e1.printStackTrace();
//			} 
//			 throw new RuntimeException("事务回滚，自定义"); 
//		}
//		 
//	}
}
 

