package com.xinxinsoft.service.countShare;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import jxl.CellView;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.NumberFormat;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;

import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.hibernate.Criteria;
import org.hibernate.criterion.CriteriaSpecification;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.Projection;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.impl.CriteriaImpl;
import org.hibernate.transform.Transformers;
import org.springframework.util.Assert;

import com.xinxinsoft.entity.basetype.BusinessType;
import com.xinxinsoft.entity.basetype.ProductType;
import com.xinxinsoft.entity.commonSingManagement.OrderForm;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.BeanUtils;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.Page;
import com.xinxinsoft.utils.PageConfig;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
/**
 * 
 * <AUTHOR>
 *
 */
public class CountShareService extends BaseService{

	public PageResponse getlist(Map<String,String> map,PageRequest page,SystemUser user, HttpServletRequest httpServletRequest) {
		List<SystemDept> deptList = user.getSystemDept();
		  String companyIBM = deptList.get(0).getSystemCompany().getCompayIBM();//公司首字母缩写简称
		 // String departmentName = deptList.get(0).getDepartmentName();
		 // Integer departmentLevel = deptList.get(0).getDepartmentLevel();
		  String sql=""; 
		  String sqlparams= "";
		  String sqlNNparams= "";
		  if(!StringUtils.isEmpty(map.get("company"))&&!"".equals(map.get("company"))&& map.get("company")!=null){
			  companyIBM=map.get("company");
		  }
		  if(StringUtils.isEmpty(companyIBM)){
			  companyIBM=queryCompanyIBM(user.getRowNo());
		 }
		  String params1 = "";
		  if(!StringUtils.isEmpty(map.get("likename"))){
			  params1 = " and  nn.company_name like '%"+map.get("likename").trim()+"%'";
			  //--根据公司统计	
			  if("start".equals(audit)){
					if("1".equals(map.get("auditP"))){
						 if("1".equals(map.get("levelQuery"))){
							String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
							String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
					    	CommLogs.requOrderquery(unl, cun, "0", "公司名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));
						 }else if("2".equals(map.get("levelQuery"))){
							 String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
								String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
						    	CommLogs.requOrderquery(unl, cun, "0", "区县公司名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));

						 }else if("3".equals(map.get("levelQuery"))){
							 String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
								String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
						    	CommLogs.requOrderquery(unl, cun, "0", "人员名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));
						 }
					}
				}
		  }
		  String type="";
		  
		  if("1".equals(map.get("levelQuery"))){
			  if(!"SGS".equals(companyIBM)){
				  params1+=" and nn.company_ibm = '"+companyIBM+"' ";
			  }
			  type="1";
			  sqlNNparams=" nn.COMPANY_NAME,nn.company_ibm ";
			  sqlparams=" sorder.COMPANY_NAME,sorder.company_ibm ";
		  }else if("2".equals(map.get("levelQuery"))){
			  params1+=" and nn.company_ibm = '"+companyIBM+"' ";
			  type="2";
			  sqlNNparams=" nn.twodname,nn.twodnameno,nn.COMPANY_NAME,nn.company_ibm ";
			  sqlparams=" sorder.twodname,sorder.twodnameno,sorder.COMPANY_NAME,sorder.company_ibm ";
		  }else if("3".equals(map.get("levelQuery"))){
			  if(!"".equals(map.get("twoDName"))&& map.get("twoDName")!=null && !StringUtils.isEmpty(map.get("twoDName")) ){
				  params1+=" and nn.company_ibm = '"+companyIBM+"' and nn.twodnameno='"+map.get("twoDName").trim()+"' ";
			  }else{
				  params1+=" and nn.company_ibm = '"+companyIBM+"' ";
			  }
			  type="3";
			  sqlNNparams=" nn.draftmanId,nn.twodname,nn.twodnameno,nn.EMPLOYEE_NAME,nn.COMPANY_NAME,nn.company_ibm ";
			  sqlparams=" sorder.draftmanId,sorder.twodname,sorder.twodnameno,sorder.EMPLOYEE_NAME,sorder.COMPANY_NAME,sorder.company_ibm ";
		  }
		  sql=" select distinct "+sqlNNparams+","
			  		+ "sum(nn.orderInitiation) as \"orderInitiation\","
			  		+ "sum(nn.completionQuantity) as \"completionQuantity\","
			  		+ "sum(nn.timeoutCompletion) as \"timeoutCompletion\" ,"
			  		+ "sum(nn.finishOnTime) as \"finishOnTime\" ,"
			  		+ "sum(nn.incompleteQuantity) as \"incompleteQuantity\","
			  		+ "sum(nn.timeoutTimeout) as \"timeoutTimeout\"  from (  "+ 
					  " ( select  distinct "+sqlparams+",  count(0) as  orderInitiation, 0 as completionQuantity,  0 as timeoutCompletion, 0 as finishOnTime, 0 as incompleteQuantity,0 as timeoutTimeout   from  SINGORDERFORM_mv sorder     "+ 
					  " group by "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select distinct "+sqlparams+",0, count(0) ,  0,  0 , 0, 0 from  SINGORDERFORM_mv sorder   where sorder.state = '1'  "
					  + "group by "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select  distinct "+sqlparams+", 0, 0 ,  count(0),  0 , 0, 0   from  SINGORDERFORM_mv sorder  where   sorder.state = '1'  and (  sorder.ordercompletiontime > sorder.orderreqtimelimit  )  "
					  + "group by  "+sqlparams+") "+ 
					  " union all "+ 
					  " (select  distinct "+sqlparams+", 0, 0 ,  0,  count(0) , 0, 0  from  SINGORDERFORM_mv sorder  where sorder.state = '1' and (  sorder.ordercompletiontime < sorder.orderreqtimelimit  or sorder.orderreqtimelimit>sysdate )  "
					  + " group by  "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select   distinct "+sqlparams+" , 0, 0 ,  0,  0 ,count(0), 0  from SINGORDERFORM_mv sorder where  sorder.state = '0' "
					  + "group by "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select distinct "+sqlparams+" ,  0, 0 , 0, 0 ,0, count(0)  from SINGORDERFORM_mv sorder   where  sorder.state = '0'  and sorder.orderreqtimelimit<sysdate "
					  + "group by "+sqlparams+" ) "+ 
					  " ) nn right outer join AFR_SYSTEMCOMPANY afc on afc.company_ibm = nn.company_ibm  where afc.company_state = '1'  and  nn.company_name is not null "+params1+"  group by "+sqlNNparams+"";
		  return getMapNoByPars(sql, page,type);
		  //			  if("SGS".equals(companyIBM)){
//				  if(departmentLevel<1 && departmentName.indexOf("分公司领导")<0 && departmentName.indexOf("分公司")>0){
//					  sql=" select distinct nn.draftmanId,nn.EMPLOYEE_NAME,nn.DEPARTMENT_NAME,nn.DEPARTMENT_NO,nn.COMPANY_NAME,"
//					  		+ "sum(nn.orderInitiation) as \"orderInitiation\","
//					  		+ "sum(nn.completionQuantity) as \"completionQuantity\","
//					  		+ "sum(nn.timeoutCompletion) as \"timeoutCompletion\" ,"
//					  		+ "sum(nn.finishOnTime) as \"finishOnTime\" ,"
//					  		+ "sum(nn.incompleteQuantity) as \"incompleteQuantity\","
//					  		+ "sum(nn.timeoutTimeout) as \"timeoutTimeout\"  from (  "+ 
//							  " ( select  distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME,  count(0) as  orderInitiation, 0 as completionQuantity,  0as timeoutCompletion, 0 as finishOnTime, 0 as incompleteQuantity,0 as timeoutTimeout   from  SINGORDERFORM_mv sorder     "+ 
//							  " group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " union all "+ 
//							  " (select distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME,0, count(0) ,  0,  0 , 0, 0 from  SINGORDERFORM_mv sorder   where sorder.state = '1'   "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " union all "+ 
//							  " (select  distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME, 0, 0 ,  count(0),  0 , 0, 0   from  SINGORDERFORM_mv sorder  where   sorder.state = '1'  and (  sorder.ordercompletiontime > sorder.orderreqtimelimit  )   "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME) "+ 
//							  " union all "+ 
//							  " (select  distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME, 0, 0 ,  0,  count(0) , 0, 0  from  SINGORDERFORM_mv sorder  where sorder.state = '1' and (  sorder.ordercompletiontime < sorder.orderreqtimelimit  or sorder.orderreqtimelimit>sysdate )   "
//							  + " group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " union all "+ 
//							  " (select   distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME , 0, 0 ,  0,  0 ,count(0), 0  from SINGORDERFORM_mv sorder where  sorder.state = '0' "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME) "+ 
//							  " union all "+ 
//							  " (select distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ,  0, 0 , 0, 0 ,0, count(0)  from SINGORDERFORM_mv sorder   where  sorder.state = '0'  and sorder.orderreqtimelimit<sysdate     "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " ) nn right outer join AFR_SYSTEMCOMPANY afc on afc.company_ibm = nn.company_ibm where afc.company_state = '1'  and  nn.company_name is not null and nn.DEPARTMENT_NO='"+deptList.get(0).getDepartmentParentNo()+"'  group by nn.draftmanId,nn.EMPLOYEE_NAME,nn.DEPARTMENT_NAME,nn.DEPARTMENT_NO,nn.COMPANY_NAME ";
//					  return getMapNoBy(sql, page);
//				  }else{
		  //--根据公司统计	
//		  sql=" select distinct nn.COMPANY_NAME,nn.company_ibm,"
//		  		+ "sum(nn.orderInitiation) as \"orderInitiation\","
//		  		+ "sum(nn.completionQuantity) as \"completionQuantity\","
//		  		+ "sum(nn.timeoutCompletion) as \"timeoutCompletion\" ,"
//		  		+ "sum(nn.finishOnTime) as \"finishOnTime\" ,"
//		  		+ "sum(nn.incompleteQuantity) as \"incompleteQuantity\","
//		  		+ "sum(nn.timeoutTimeout) as \"timeoutTimeout\"  from (  "+ 
//				  " ( select  distinct sorder.COMPANY_NAME,sorder.company_ibm,  count(0) as  orderInitiation, 0 as completionQuantity,  0as timeoutCompletion, 0 as finishOnTime, 0 as incompleteQuantity,0 as timeoutTimeout   from  SINGORDERFORM_mv sorder     "+ 
//				  " group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//				  " union all "+ 
//				  " (select distinct sorder.COMPANY_NAME,sorder.company_ibm,0, count(0) ,  0,  0 , 0, 0 from  SINGORDERFORM_mv sorder   where sorder.state = '1'  "
//				  + "group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//				  " union all "+ 
//				  " (select  distinct sorder.COMPANY_NAME,sorder.company_ibm, 0, 0 ,  count(0),  0 , 0, 0   from  SINGORDERFORM_mv sorder  where   sorder.state = '1'  and (  sorder.ordercompletiontime > sorder.orderreqtimelimit  )  "
//				  + "group by  sorder.COMPANY_NAME,sorder.company_ibm) "+ 
//				  " union all "+ 
//				  " (select  distinct sorder.COMPANY_NAME,sorder.company_ibm, 0, 0 ,  0,  count(0) , 0, 0  from  SINGORDERFORM_mv sorder  where sorder.state = '1' and (  sorder.ordercompletiontime < sorder.orderreqtimelimit  or sorder.orderreqtimelimit>sysdate )  "
//				  + " group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//				  " union all "+ 
//				  " (select distinct sorder.COMPANY_NAME,sorder.company_ibm , 0, 0 ,  0,  0 ,count(0), 0  from SINGORDERFORM_mv sorder where  sorder.state = '0'  "
//				  + "group by  sorder.COMPANY_NAME,sorder.company_ibm) "+ 
//				  " union all "+ 
//				  " (select distinct sorder.COMPANY_NAME,sorder.company_ibm ,  0, 0 , 0, 0 ,0, count(0)  from SINGORDERFORM_mv sorder   where  sorder.state = '0'  and sorder.orderreqtimelimit<sysdate "
//				  + "group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//				  " ) nn right outer join AFR_SYSTEMCOMPANY afc on afc.company_ibm = nn.company_ibm where afc.company_state = '1'   and  nn.company_name is not null  group by nn.COMPANY_NAME,nn.company_ibm ";
//		  return getMapNoBy(sql, page);
//				  }
//			  }else{
//				  if(departmentLevel<1 && departmentName.indexOf("分公司领导")<0 && departmentName.indexOf("分公司")>0){
//					  sql=" select distinct nn.draftmanId,nn.EMPLOYEE_NAME,nn.DEPARTMENT_NAME,nn.DEPARTMENT_NO,nn.COMPANY_NAME,"
//					  		+ "sum(nn.orderInitiation) as \"orderInitiation\","
//					  		+ "sum(nn.completionQuantity) as \"completionQuantity\","
//					  		+ "sum(nn.timeoutCompletion) as \"timeoutCompletion\" ,"
//					  		+ "sum(nn.finishOnTime) as \"finishOnTime\" ,"
//					  		+ "sum(nn.incompleteQuantity) as \"incompleteQuantity\","
//					  		+ "sum(nn.timeoutTimeout) as \"timeoutTimeout\"  from (  "+ 
//							  " ( select  distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME,  count(0) as  orderInitiation, 0 as completionQuantity,  0as timeoutCompletion, 0 as finishOnTime, 0 as incompleteQuantity,0 as timeoutTimeout   from  SINGORDERFORM_mv sorder     "+ 
//							  " group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " union all "+ 
//							  " (select distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME,0, count(0) ,  0,  0 , 0, 0 from  SINGORDERFORM_mv sorder   where sorder.state = '1'   "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " union all "+ 
//							  " (select  distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME, 0, 0 ,  count(0),  0 , 0, 0   from  SINGORDERFORM_mv sorder  where   sorder.state = '1'  and (  sorder.ordercompletiontime > sorder.orderreqtimelimit  )   "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME) "+ 
//							  " union all "+ 
//							  " (select  distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME, 0, 0 ,  0,  count(0) , 0, 0  from  SINGORDERFORM_mv sorder  where sorder.state = '1' and (  sorder.ordercompletiontime < sorder.orderreqtimelimit  or sorder.orderreqtimelimit>sysdate )   "
//							  + " group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " union all "+ 
//							  " (select   distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME , 0, 0 ,  0,  0 ,count(0), 0  from SINGORDERFORM_mv sorder where  sorder.state = '0'  "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME) "+ 
//							  " union all "+ 
//							  " (select distinct sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ,  0, 0 , 0, 0 ,0, count(0)  from SINGORDERFORM_mv sorder   where  sorder.state = '0'  and sorder.orderreqtimelimit<sysdate     "
//							  + "group by  sorder.draftmanId,sorder.EMPLOYEE_NAME,sorder.DEPARTMENT_NAME,sorder.DEPARTMENT_NO,sorder.COMPANY_NAME ) "+ 
//							  " ) nn right outer join AFR_SYSTEMCOMPANY afc on afc.company_ibm = nn.company_ibm where afc.company_state = '1'  and  nn.company_name is not null and nn.DEPARTMENT_NO='"+deptList.get(0).getDepartmentParentNo()+"'  group by nn.draftmanId,nn.EMPLOYEE_NAME,nn.DEPARTMENT_NAME,nn.DEPARTMENT_NO,nn.COMPANY_NAME ";
//					  return getMapNoBy(sql, page);
//				  }else{
					  //--根据区县统计 现在是全部区县，正式时应该得到当前登录人员的分公司，然后根据登录人查询当前员分公司下所有区县 如（条件为sorder.company_ibm='LSH'  （乐山分公司））
//					  sql=" select distinct nn.COMPANY_NAME,nn.company_ibm,"
//						  		+ "sum(nn.orderInitiation) as \"orderInitiation\","
//						  		+ "sum(nn.completionQuantity) as \"completionQuantity\","
//						  		+ "sum(nn.timeoutCompletion) as \"timeoutCompletion\" ,"
//						  		+ "sum(nn.finishOnTime) as \"finishOnTime\" ,"
//						  		+ "sum(nn.incompleteQuantity) as \"incompleteQuantity\","
//						  		+ "sum(nn.timeoutTimeout) as \"timeoutTimeout\"  from (  "+ 
//								  " ( select  distinct sorder.COMPANY_NAME,sorder.company_ibm,  count(0) as  orderInitiation, 0 as completionQuantity,  0as timeoutCompletion, 0 as finishOnTime, 0 as incompleteQuantity,0 as timeoutTimeout   from  SINGORDERFORM_mv sorder     "+ 
//								  " group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//								  " union all "+ 
//								  " (select distinct sorder.COMPANY_NAME,sorder.company_ibm,0, count(0) ,  0,  0 , 0, 0 from  SINGORDERFORM_mv sorder   where sorder.state = '1'  "
//								  + "group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//								  " union all "+ 
//								  " (select  distinct sorder.COMPANY_NAME,sorder.company_ibm, 0, 0 ,  count(0),  0 , 0, 0   from  SINGORDERFORM_mv sorder  where   sorder.state = '1'  and (  sorder.ordercompletiontime > sorder.orderreqtimelimit  )  "
//								  + "group by  sorder.COMPANY_NAME,sorder.company_ibm) "+ 
//								  " union all "+ 
//								  " (select  distinct sorder.COMPANY_NAME,sorder.company_ibm, 0, 0 ,  0,  count(0) , 0, 0  from  SINGORDERFORM_mv sorder  where sorder.state = '1' and (  sorder.ordercompletiontime < sorder.orderreqtimelimit  or sorder.orderreqtimelimit>sysdate )  "
//								  + " group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//								  " union all "+ 
//								  " (select   distinct sorder.COMPANY_NAME,sorder.company_ibm , 0, 0 ,  0,  0 ,count(0), 0  from SINGORDERFORM_mv sorder where  sorder.state = '0'  "
//								  + "group by  sorder.COMPANY_NAME,sorder.company_ibm) "+ 
//								  " union all "+ 
//								  " (select distinct sorder.COMPANY_NAME,sorder.company_ibm ,  0, 0 , 0, 0 ,0, count(0)  from SINGORDERFORM_mv sorder   where  sorder.state = '0'  and sorder.orderreqtimelimit<sysdate "
//								  + "group by  sorder.COMPANY_NAME,sorder.company_ibm ) "+ 
//								  " ) nn right outer join AFR_SYSTEMCOMPANY afc on afc.company_ibm = nn.company_ibm where afc.company_state = '1'  and  nn.company_name is not null and  nn.company_ibm = '"+companyIBM.trim()+"'  group by nn.COMPANY_NAME,nn.company_ibm ";
//						  return getMapNoBy(sql, page);
//				  }
//			  }
		  
	}
	public LayuiPage getlist(Map<String,String> map,LayuiPage page,SystemUser user, HttpServletRequest httpServletRequest) {
		List<SystemDept> deptList = user.getSystemDept();
		  String companyIBM = deptList.get(0).getSystemCompany().getCompayIBM();//公司首字母缩写简称
		 // String departmentName = deptList.get(0).getDepartmentName();
		 // Integer departmentLevel = deptList.get(0).getDepartmentLevel();
		  String sql=""; 
		  String sqlparams= "";
		  String sqlNNparams= "";
		  if(!StringUtils.isEmpty(map.get("company"))&&!"".equals(map.get("company"))&& map.get("company")!=null){
			  companyIBM=map.get("company");
		  }
		  if(StringUtils.isEmpty(companyIBM)){
			  companyIBM=queryCompanyIBM(user.getRowNo());
		 }
		  String params1 = "";
		  if(!StringUtils.isEmpty(map.get("likename"))){
			  params1 = " and  nn.company_name like '%"+map.get("likename").trim()+"%'";
			  //--根据公司统计	
			  if("start".equals(audit)){
					if("1".equals(map.get("auditP"))){
						 if("1".equals(map.get("levelQuery"))){
							String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
							String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
					    	CommLogs.requOrderquery(unl, cun, "0", "公司名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));
						 }else if("2".equals(map.get("levelQuery"))){
							 String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
								String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
						    	CommLogs.requOrderquery(unl, cun, "0", "区县公司名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));

						 }else if("3".equals(map.get("levelQuery"))){
							 String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
								String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
						    	CommLogs.requOrderquery(unl, cun, "0", "人员名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));
						 }
					}
				}
		  }
		  String type="";
		  
		  if("1".equals(map.get("levelQuery"))){
			  if(!"SGS".equals(companyIBM)){
				  params1+=" and nn.company_ibm = '"+companyIBM+"' ";
			  }
			  type="1";
			  sqlNNparams=" nn.COMPANY_NAME,nn.company_ibm ";
			  sqlparams=" sorder.COMPANY_NAME,sorder.company_ibm ";
		  }else if("2".equals(map.get("levelQuery"))){
			  params1+=" and nn.company_ibm = '"+companyIBM+"' ";
			  type="2";
			  sqlNNparams=" nn.twodname,nn.twodnameno,nn.COMPANY_NAME,nn.company_ibm ";
			  sqlparams=" sorder.twodname,sorder.twodnameno,sorder.COMPANY_NAME,sorder.company_ibm ";
		  }else if("3".equals(map.get("levelQuery"))){
			  if(!"".equals(map.get("twoDName"))&& map.get("twoDName")!=null && !StringUtils.isEmpty(map.get("twoDName")) ){
				  params1+=" and nn.company_ibm = '"+companyIBM+"' and nn.twodnameno='"+map.get("twoDName").trim()+"' ";
			  }else{
				  params1+=" and nn.company_ibm = '"+companyIBM+"' ";
			  }
			  type="3";
			  sqlNNparams=" nn.draftmanId,nn.twodname,nn.twodnameno,nn.EMPLOYEE_NAME,nn.COMPANY_NAME,nn.company_ibm ";
			  sqlparams=" sorder.draftmanId,sorder.twodname,sorder.twodnameno,sorder.EMPLOYEE_NAME,sorder.COMPANY_NAME,sorder.company_ibm ";
		  }
		  sql=" select distinct "+sqlNNparams+","
			  		+ "sum(nn.orderInitiation) as \"orderInitiation\","
			  		+ "sum(nn.completionQuantity) as \"completionQuantity\","
			  		+ "sum(nn.timeoutCompletion) as \"timeoutCompletion\" ,"
			  		+ "sum(nn.finishOnTime) as \"finishOnTime\" ,"
			  		+ "sum(nn.incompleteQuantity) as \"incompleteQuantity\","
			  		+ "sum(nn.timeoutTimeout) as \"timeoutTimeout\"  from (  "+ 
					  " ( select  distinct "+sqlparams+",  count(0) as  orderInitiation, 0 as completionQuantity,  0 as timeoutCompletion, 0 as finishOnTime, 0 as incompleteQuantity,0 as timeoutTimeout   from  SINGORDERFORM_mv sorder     "+ 
					  " group by "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select distinct "+sqlparams+",0, count(0) ,  0,  0 , 0, 0 from  SINGORDERFORM_mv sorder   where sorder.state = '1'  "
					  + "group by "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select  distinct "+sqlparams+", 0, 0 ,  count(0),  0 , 0, 0   from  SINGORDERFORM_mv sorder  where   sorder.state = '1'  and (  sorder.ordercompletiontime > sorder.orderreqtimelimit  )  "
					  + "group by  "+sqlparams+") "+ 
					  " union all "+ 
					  " (select  distinct "+sqlparams+", 0, 0 ,  0,  count(0) , 0, 0  from  SINGORDERFORM_mv sorder  where sorder.state = '1' and (  sorder.ordercompletiontime < sorder.orderreqtimelimit  or sorder.orderreqtimelimit>sysdate )  "
					  + " group by  "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select   distinct "+sqlparams+" , 0, 0 ,  0,  0 ,count(0), 0  from SINGORDERFORM_mv sorder where  sorder.state = '0' "
					  + "group by "+sqlparams+" ) "+ 
					  " union all "+ 
					  " (select distinct "+sqlparams+" ,  0, 0 , 0, 0 ,0, count(0)  from SINGORDERFORM_mv sorder   where  sorder.state = '0'  and sorder.orderreqtimelimit<sysdate "
					  + "group by "+sqlparams+" ) "+ 
					  " ) nn right outer join AFR_SYSTEMCOMPANY afc on afc.company_ibm = nn.company_ibm  where afc.company_state = '1'  and  nn.company_name is not null "+params1+"  group by "+sqlNNparams+"";
		  page.setData(getPageList(sql, null, page));
		  page.setCount(getCount("select count(0) from ("+sql+")"));
		  page.setCode(0);
		  return page;
	}
	/**
	 * 根据用户Id查询公司首拼字母：
	 * @param userId
	 * @return
	 */
	public String queryCompanyIBM(Integer userId){
		String sql="select usr.company_ibm from (select  ausr.rowno,ausr.employee_no,sc.company_name,sc.company_ibm,sc.company_code from  afr_systemuser ausr inner join afr_system_dept_user asdu on asdu.rowno=ausr.rowno inner join afr_systemdept sd on sd.department_no = asdu.department_no inner join afr_systemcompany sc on sc.company_code=sd.company_code  ) usr  where usr.rowno=?";
		@SuppressWarnings("unchecked")
		List<Object[]> lisobj=  getSession().createSQLQuery(sql).setInteger(0, userId).list();
		String retStr="";
		for(Object obj : lisobj){
			retStr= obj.toString();
		}
		return  retStr;
	
	}
	/**
	 * 根据用户Id查询公司编号：
	 * @param userId
	 * @return
	 */
	public String queryCompanyCode(Integer userId){
		String sql="select usr.company_code from (select  ausr.rowno,ausr.employee_no,sc.company_name,sc.company_ibm,sc.company_code from  afr_systemuser ausr inner join afr_system_dept_user asdu on asdu.rowno=ausr.rowno inner join afr_systemdept sd on sd.department_no = asdu.department_no inner join afr_systemcompany sc on sc.company_code=sd.company_code  ) usr  where usr.rowno=?";
		@SuppressWarnings("unchecked")
		List<Object[]> lisobj=  getSession().createSQLQuery(sql).setInteger(0, userId).list();
		String retStr="";
		for(Object obj : lisobj){
			retStr= obj.toString();
		}
		return  retStr;
	}
	/**
	 * 根据用户Id查询公司编号：
	 * @param userId
	 * @return
	 */
	public String queryCompanyCode(Integer userId,String companyIbm){
		String sql="select usr.company_code from (select  ausr.rowno,ausr.employee_no,sc.company_name,sc.company_ibm,sc.company_code from  afr_systemuser ausr inner join afr_system_dept_user asdu on asdu.rowno=ausr.rowno inner join afr_systemdept sd on sd.department_no = asdu.department_no inner join afr_systemcompany sc on sc.company_code=sd.company_code where sc.company_ibm=? ) usr  where usr.rowno=?";
		@SuppressWarnings("unchecked")
		List<Object[]> lisobj=  getSession().createSQLQuery(sql).setString(0, companyIbm).setInteger(1, userId).list();
		String retStr="";
		for(Object obj : lisobj){
			retStr= obj.toString();
		}
		return  retStr;
	}
	
	@SuppressWarnings("unchecked")
	public List<Map<Object,Object>> queryPCompany(){
		String sql="select t.company_name as \"companyName\",t.company_code as \"companyCode\",t.company_short_name as \"companyShortName\",t.company_ibm as \"companyIBM\" from afr_systemcompany t";
		List<Map<Object,Object>> deptList  = this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		return deptList;
	}
	@SuppressWarnings("unchecked")
	public List<Map<Object,Object>> queryPCompanyByIBM(String companyIBM){
		String sql="select t.company_name as \"companyName\",t.company_code as \"companyCode\",t.company_short_name as \"companyShortName\",t.company_ibm as \"companyIBM\" from afr_systemcompany t where t.company_ibm=?";
		List<Map<Object,Object>> deptList  = this.getSession().createSQLQuery(sql).setString(0, companyIBM).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		return deptList;
	}

	/**
	 * 权限获取：
	 * @param user
	 * @return
	 */
	public String queryQxiang(SystemUser user) {
		String deptStr="";
		 List<SystemDept> deptList = user.getSystemDept();
		 deptStr = deptList.get(0).getSystemCompany().getCompayIBM();
		 String departmentName = deptList.get(0).getDepartmentName();
		 Integer departmentLevel = deptList.get(0).getDepartmentLevel();
		 Integer depNo= deptList.get(0).getDepartmentNo();
		 if(StringUtils.isEmpty(deptStr)){
			 deptStr=queryCompanyIBM(user.getRowNo());
		 }
		Integer resut=0;
		 if("SGS".equals(deptStr)){
			  if(departmentLevel==1 || departmentLevel>1){
				  if(departmentName.indexOf("分公司领导")<0){
					  if(departmentName.indexOf("分公司")>0){
						  resut=3;
					  }else{
						  resut=1;
					  }
				  }else{
					  resut=1;
				  }
			  }else{
				  resut=1;
			  }
		 }else{
			 if(departmentLevel==1 || departmentLevel>1){
				  if(departmentName.indexOf("分公司领导")<0){
					  if(departmentName.indexOf("分公司")>0){
						  resut=3;
					  }else{
						  resut=2;
					  }
				  }else{
					  resut=2;
				  }
			  }else{
				  resut=2;
			  }
		 }
		 String str ="{deptStr:'"+deptStr+"',result:'"+resut+"',depNo:'"+depNo+"'}";
		return str;
	}
	
	private  ResourceBundle s = ResourceBundle .getBundle("WebService-config");
	private String audit = s.getString("AUDIT_INTERS_ORDER_SWITCH");
	
	 /** 
	  *  
	  * @param httpServletRequest 
	 * @Description: 生成excel并导出到（本地） 
	  *
	  */  
	public void exportExcelToJxl(List<Map<String,Object>> mapList,String type, HttpServletRequest httpServletRequest){ 
		HttpServletResponse  response=ServletActionContext.getResponse();
		
		String excelFile = FileUpload.getFtpURL()+"exportExcelToJxl.xls";
		File f = new File(FileUpload.getFtpURL());
		if(!f.exists()&&!f.isDirectory()){
			f.mkdirs();
		}
		String exportName="exportData_"+FileUpload.getDateToString("yyyyMMdd");
		// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
        try {
			WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
			 // 2、新建工作表(sheet)对象，并声明其属于第几页
	        WritableSheet firstSheet = writeBook.createSheet("统计分析", 1);// 第一个参数为工作簿的名称，第二个参数为页数
	        if("QXRY".endsWith(type)){
	        	 String[] headers = new String[]{"地区公司名称","区县公司名称","名称","订单发起量","完成量","超时完成量","按时完成量","未完成量","未完成超时量"};  
	        	 //-----------------------------------------------区县：
	        	 for(int i = 0; i<headers.length;i++){  
		        		// 3、创建单元格(Label)对象，
		        		Label label0 = null;//new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
		        		WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
		        		firstSheet.addCell(label0);
		        	}  
	        	 for(int i=0;i<mapList.size();i++){
	        		 Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
	        		 firstSheet.addCell(label1); 
	        		 Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("TWODNAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
	        		 firstSheet.addCell(label2); 
	        		 Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("EMPLOYEE_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
	        		 firstSheet.addCell(label3); 
	        		 firstSheet.addCell(setParams(3,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("orderInitiation")))));
	        		 firstSheet.addCell(setParams(4,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("completionQuantity"))))); 
	        		 firstSheet.addCell(setParams(5,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("timeoutCompletion"))))); 
	        		 firstSheet.addCell(setParams(6,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("finishOnTime"))))); 
	        		 firstSheet.addCell(setParams(7,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("incompleteQuantity"))))); 
	        		 firstSheet.addCell(setParams(8,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("timeoutTimeout"))))); 
	        	 
	        		 
	        	 }
	        }else if("FGS".equals(type)){
	        	 String[] headers = new String[]{"地区公司名称","区县公司名称","订单发起量","完成量","超时完成量","按时完成量","未完成量","未完成超时量"};
	        	 //-----------------------------------------------分公司：
	        	 for(int i = 0; i<headers.length;i++){  
		        		// 3、创建单元格(Label)对象，
		        		Label label0 = null;//new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
		        		WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
		        		firstSheet.addCell(label0);
		        	}  
	        	 for(int i=0;i<mapList.size();i++){
	        		 Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
	        		 firstSheet.addCell(label1); 
	        		 Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("TWODNAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
	        		 firstSheet.addCell(label2); 
	        		 firstSheet.addCell(setParams(2,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("orderInitiation")))));
	        		 firstSheet.addCell(setParams(3,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("completionQuantity"))))); 
	        		 firstSheet.addCell(setParams(4,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("timeoutCompletion"))))); 
	        		 firstSheet.addCell(setParams(5,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("finishOnTime"))))); 
	        		 firstSheet.addCell(setParams(6,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("incompleteQuantity"))))); 
	        		 firstSheet.addCell(setParams(7,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("timeoutTimeout"))))); 
	        	 
	        		 
	        	 }
	        }else if("ORDER".equals(type)){
	        	 String[] headers = new String[]{"订单发起人","订单名称","订单编号","订单生成时间","操作类型","业务类型","业务产品","需求单位名称","签约情况","订单要求完成时间","当前处理人",};  
	        	 //-----------------------------------------------区县：
	        	 for(int i = 0; i<headers.length;i++){  
		        		// 3、创建单元格(Label)对象，
		        		Label label0 = null;//new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
		        		WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
		        		firstSheet.addCell(label0);
		        	}  
	        	 for(int i=0;i<mapList.size();i++){
	        		 Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("orderSponsor")));
	        		 firstSheet.addCell(label1); 
	        		 Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("orderName")));
	        		 firstSheet.addCell(label2); 
	        		 Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("orderNumber")));
	        		 firstSheet.addCell(label3); 
	        		 Label label4 = new Label(3, i+1,String.valueOf(mapList.get(i).get("orderGenerationTime")));
	        		 firstSheet.addCell(label4); 
	        		 Label label5 = new Label(4, i+1,String.valueOf(mapList.get(i).get("operationType")));
	        		 firstSheet.addCell(label5); 
	        		 Label label6 = new Label(5, i+1,String.valueOf(mapList.get(i).get("businessType")));
	        		 firstSheet.addCell(label6); 
	        		 Label label7 = new Label(6, i+1,String.valueOf(mapList.get(i).get("productType")));
	        		 firstSheet.addCell(label7); 
	        		 Label label8 = new Label(7, i+1,String.valueOf(mapList.get(i).get("demandUnitName")));
	        		 firstSheet.addCell(label8); 
	        		 Label label9 = new Label(8, i+1,String.valueOf(mapList.get(i).get("signedStatus")));
	        		 firstSheet.addCell(label9); 
	        		 Label label10 = new Label(9, i+1,String.valueOf(mapList.get(i).get("orderCompletionTime")));
	        		 firstSheet.addCell(label10); 
	        		 Label label11 = new Label(10, i+1,String.valueOf(mapList.get(i).get("currentHandlingPerson")));
	        		 firstSheet.addCell(label11); 
	        	 }
	        }else{
	        	String[] headers = new String[]{"名称","订单发起量","完成量","超时完成量","按时完成量","未完成量","未完成超时量"}; 
	        	//-----------------------------省公司：
	        	for(int i = 0; i<headers.length;i++){  
	        		// 3、创建单元格(Label)对象，
	        		Label label0 = null;//new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
	        		WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
				       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
				       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
				       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
				       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
				       
				       CellView navCellView = new CellView();  
				       navCellView.setAutosize(true); //设置自动大小
				       navCellView.setSize(18);
				       
				       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
				       firstSheet.setColumnView(i, navCellView); //设置col显示样式
				       firstSheet.setRowView(i, 400, false); //设置行高
	        		firstSheet.addCell(label0);
	        	}  
	        	
	        	 for(int i=0;i<mapList.size();i++){
	        		 Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
	        		 firstSheet.addCell(label1); 
	        		 firstSheet.addCell(setParams(1,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("orderInitiation")))));
	        		 firstSheet.addCell(setParams(2,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("completionQuantity"))))); 
	        		 firstSheet.addCell(setParams(3,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("timeoutCompletion"))))); 
	        		 firstSheet.addCell(setParams(4,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("finishOnTime"))))); 
	        		 firstSheet.addCell(setParams(5,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("incompleteQuantity"))))); 
	        		 firstSheet.addCell(setParams(6,i+1,Integer.parseInt(String.valueOf(mapList.get(i).get("timeoutTimeout"))))); 
	        	 }
	        }
	        // 4、打开流，开始写文件
	        writeBook.write();
	        // 5、关闭流
	        writeBook.close();

	        byte[] data = FileUtil.toByteArray2(excelFile); 
	        String   fileName = URLEncoder.encode(exportName, "UTF-8");  
		    response.reset();  
		    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName +".xls" +"\"");  
		    response.addHeader("Content-Length", "" + data.length);  
		    response.setContentType("application/octet-stream;charset=UTF-8");  
		    OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());  
		    outputStream.write(data);  
		    outputStream.flush();  
		    outputStream.close();
		    response.flushBuffer();
		    File fe = new File(excelFile);
		    fe.delete();
		    if("start".equals(audit)){
				String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
				String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
		    	CommLogs.requOrderquery(unl, cun, "0", fileName +".xls", "", "", "", "", "", "10", DateUtil.getIpAddr(httpServletRequest));
		    }
		} catch (Exception e) {
			e.printStackTrace();
			 File fe = new File(excelFile);
			 if(f.exists()&&f.isDirectory()){
					fe.delete();
				}
			  try {
					response.getWriter().write("<script>alert('导出Excel失败');history.go(-1);</script>");
					response.getWriter().flush(); 
				} catch (IOException e1) {
					e1.printStackTrace();
				} 
		}
	}
	
	/** 
	 *  
	 * @Description: 生成excel并导出到（本地） 
	 *
	 */  
	public void exportExcelToCsJxl(List<Map<String,Object>> mapList, HttpServletRequest httpServletRequest){ 
		HttpServletResponse  response=ServletActionContext.getResponse();
		
		String excelFile = FileUpload.getFtpURL()+"exportExcelToOrderJxl.xls";
		File f = new File(FileUpload.getFtpURL());
		if(!f.exists()&&!f.isDirectory()){
			f.mkdirs();
		}
		String exportName="exportData_"+FileUpload.getDateToString("yyyyMMdd");
		// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
		try {
			WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
			// 2、新建工作表(sheet)对象，并声明其属于第几页
			WritableSheet firstSheet = writeBook.createSheet("统计分析", 1);// 第一个参数为工作簿的名称，第二个参数为页数
		
				String[] headers = new String[]{"订单名称","订单编号","创建人","订单生成时间","要求完成时间","订单完成时间","业务类型","产品类型","当前处理人","签约情况","是否归档","满意度","相应速度","流转量"};
				//-----------------------------------------------分公司：
				for(int i = 0; i<headers.length;i++){  
					// 3、创建单元格(Label)对象，
					Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
					WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
				       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
				       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
				       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
				       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
				       
				       CellView navCellView = new CellView();  
				       navCellView.setAutosize(true); //设置自动大小
				       navCellView.setSize(18);
				       
				       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
				       firstSheet.setColumnView(i, navCellView); //设置col显示样式
				       firstSheet.setRowView(i, 400, false); //设置行高
					firstSheet.addCell(label0);
				}  
				for(int i=0;i<mapList.size();i++){
					Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("orderName")));
					firstSheet.addCell(label1); 
					Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("orderNumber")));
					firstSheet.addCell(label2); 
					Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("orderCreater")));
					firstSheet.addCell(label3); 
					Label label4 = new Label(3, i+1,String.valueOf(mapList.get(i).get("orderLunTime")));
					firstSheet.addCell(label4); 
					Label label5 = new Label(4, i+1,String.valueOf(mapList.get(i).get("completionLimitTime")));
					firstSheet.addCell(label5); 
					Label label6 = new Label(5, i+1,("null".equals(String.valueOf(mapList.get(i).get("ordercompletiontime")))?"--":String.valueOf(mapList.get(i).get("ordercompletiontime"))));
					firstSheet.addCell(label6); 
					Label label7 = new Label(6, i+1,String.valueOf(mapList.get(i).get("businessType")));
					firstSheet.addCell(label7); 
					Label label8 = new Label(7, i+1,String.valueOf(mapList.get(i).get("productType")));
					firstSheet.addCell(label8); 
					Label label9 = new Label(8, i+1,("null".equals(String.valueOf(mapList.get(i).get("acceptperson")))?"--":String.valueOf(mapList.get(i).get("acceptperson"))));
					firstSheet.addCell(label9); 
					Label label10 = new Label(9, i+1,String.valueOf(mapList.get(i).get("signedStatus")));
					firstSheet.addCell(label10); 
					Label label11 = new Label(10, i+1,String.valueOf(mapList.get(i).get("isFiled")));
					firstSheet.addCell(label11); 
					firstSheet.addCell(setParams(11,i+1,Integer.parseInt(mapList.get(i).get("satisfaction").toString()))); 
					firstSheet.addCell(setParams(12,i+1,Integer.parseInt(mapList.get(i).get("correspondingSpeed").toString()))); 
					firstSheet.addCell(setParams(13,i+1,Integer.parseInt(mapList.get(i).get("lzl").toString()))); 
				}
			// 4、打开流，开始写文件
			writeBook.write();
			// 5、关闭流
			writeBook.close();
			
			byte[] data = FileUtil.toByteArray2(excelFile); 
			String   fileName = URLEncoder.encode(exportName, "UTF-8");  
			response.reset();  
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName +".xls" +"\"");  
			response.addHeader("Content-Length", "" + data.length);  
			response.setContentType("application/octet-stream;charset=UTF-8");  
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());  
			outputStream.write(data);  
			outputStream.flush();  
			outputStream.close();
			response.flushBuffer();
			File fe = new File(excelFile);
			fe.delete();
		} catch (Exception e) {
			e.printStackTrace();
			File fe = new File(excelFile);
			if(f.exists()&&f.isDirectory()){
				fe.delete();
			}
			try {
				response.getWriter().write("<script>alert('导出Excel失败');history.go(-1);</script>");
				response.getWriter().flush(); 
			} catch (IOException e1) {
				e1.printStackTrace();
			} 
		}
	}
	/** 
	 *  
	 * @Description: 生成excel并导出到（本地） 
	 *
	 */  
	public void exportExcelOverTimeToCsJxl(List<Map<String,Object>> mapList, HttpServletRequest httpServletRequest){ 
		HttpServletResponse  response=ServletActionContext.getResponse();
		
		String excelFile = FileUpload.getFtpURL()+"exportExcelToOrderJxl.xls";
		File f = new File(FileUpload.getFtpURL());
		if(!f.exists()&&!f.isDirectory()){
			f.mkdirs();
		}
		String exportName="exportData_"+FileUpload.getDateToString("yyyyMMdd");
		// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
		try {
			WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
			// 2、新建工作表(sheet)对象，并声明其属于第几页
			WritableSheet firstSheet = writeBook.createSheet("统计分析", 1);// 第一个参数为工作簿的名称，第二个参数为页数
			String[] headers = new String[]{"订单编号","订单名称","所属部门","创建人","处理人","订单创建时间","订单环节预处理时间","订单环节完成时间","业务类型","产品类型","是否归档"};
			//-----------------------------------------------分公司：
			for(int i = 0; i<headers.length;i++){  
				// 3、创建单元格(Label)对象，
				Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
				WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
				WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
				wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
				wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
				wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
				
				CellView navCellView = new CellView();  
				navCellView.setAutosize(true); //设置自动大小
				navCellView.setSize(18);
				
				label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
				firstSheet.setColumnView(i, navCellView); //设置col显示样式
				firstSheet.setRowView(i, 400, false); //设置行高
				firstSheet.addCell(label0);
			}  
			for(int i=0;i<mapList.size();i++){
				Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("orderNumber")));
				firstSheet.addCell(label1); 
				Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("orderName")));
				firstSheet.addCell(label2); 
				Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("FullPath")));
				firstSheet.addCell(label3); 
				Label label4 = new Label(3, i+1,String.valueOf(mapList.get(i).get("EMPLOYEE_NAME")));
				firstSheet.addCell(label4); 
				Label label5 = new Label(4, i+1,String.valueOf(mapList.get(i).get("orderHandler")));
				firstSheet.addCell(label5); 
				Label label6 = new Label(5, i+1,String.valueOf(mapList.get(i).get("DRAFTTIME")));
				firstSheet.addCell(label6); 
				Label label7 = new Label(6, i+1,String.valueOf(mapList.get(i).get("completionLimitTime")));
				firstSheet.addCell(label7); 
				Label label8 = new Label(7, i+1,String.valueOf(mapList.get(i).get("completionTime")));
				firstSheet.addCell(label8); 
				Label label9 = new Label(8, i+1,String.valueOf(mapList.get(i).get("businessType")));
				firstSheet.addCell(label9); 
				Label label10 = new Label(9, i+1,String.valueOf(mapList.get(i).get("productType")));
				firstSheet.addCell(label10); 
				Label label11 = new Label(10, i+1,String.valueOf(mapList.get(i).get("ISFEILD")));
				firstSheet.addCell(label11); 
			}
			// 4、打开流，开始写文件
			writeBook.write();
			// 5、关闭流
			writeBook.close();
			byte[] data = FileUtil.toByteArray2(excelFile); 
			String   fileName = URLEncoder.encode(exportName, "UTF-8");  
			response.reset();  
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName +".xls" +"\"");  
			response.addHeader("Content-Length", "" + data.length);  
			response.setContentType("application/octet-stream;charset=UTF-8");  
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());  
			outputStream.write(data);  
			outputStream.flush();  
			outputStream.close();
			response.flushBuffer();
			File fe = new File(excelFile);
			fe.delete();
			if("start".equals(audit)){
				String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
				String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
		    	CommLogs.requOrderquery(unl, cun, "0", fileName +".xls", "", "", "", "", "", "12", DateUtil.getIpAddr(httpServletRequest));
		    }
		} catch (Exception e) {
			e.printStackTrace();
			File fe = new File(excelFile);
			if(f.exists()&&f.isDirectory()){
				fe.delete();
			}
			try {
				response.getWriter().write("<script>alert('导出Excel失败');history.go(-1);</script>");
				response.getWriter().flush(); 
			} catch (IOException e1) {
				e1.printStackTrace();
			} 
		}
	}
	 /**
	  *  参数格式化
	  * @param c
	  * @param r
	  * @param num
	  * @return
	  */
	private  jxl.write.Number setParams(int c,int r,int num){
		NumberFormat nf = new NumberFormat("0");    //设置数字格式
		 WritableCellFormat wcfN = new jxl.write.WritableCellFormat(nf); //设置表单格式    
		 jxl.write.Number labelNF = new jxl.write.Number(c, r, num, wcfN); //格式化数值
		 return labelNF;
	}

	public PageResponse getOrderlist(Map<String, String> map, PageRequest page,SystemUser user) {
		List<SystemDept> deptList = user.getSystemDept();
		  String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();
		  String sql=""; 
		  String pars="";
		  if(!StringUtils.isEmpty(map.get("companyCode"))){
				companyCode=map.get("companyCode");
			if(!StringUtils.isEmpty(map.get("likename"))){
				 		pars="and ofm.company_code='"+companyCode+"' and ofm.ordertitle like '%"+map.get("likename").trim()+"%'";
				
			}else{
					pars="and ofm.company_code='"+companyCode+"'	";
			}
		  }else{
			  if(StringUtils.isEmpty(companyCode)){
				  companyCode=queryCompanyCode(user.getRowNo());
			 }
			  if("00".equals(companyCode)){
					  if(!StringUtils.isEmpty(map.get("likename"))){
						 pars=" and  ofm.ordertitle like '%"+map.get("likename").trim()+"%' ";
					  }else{
						pars=" ";
					 }
				  }else{
					  if(!StringUtils.isEmpty(map.get("likename"))){
						  pars="and ofm.company_code='"+companyCode+"' and ofm.ordertitle like '%"+map.get("likename").trim()+"%'";
					 }else{
						 pars="and ofm.company_code='"+companyCode+"' ";
					  }
				}
		  }
		  
		  if(!"".equals(map.get("starDate"))&&map.get("starDate")!=null && !StringUtils.isEmpty(map.get("starDate"))){
			  if(!"".equals(map.get("endDate"))&&map.get("endDate")!=null && !StringUtils.isEmpty(map.get("endDate"))){
				  pars+=" and ofm.drafttime >= '"+String.valueOf(map.get("starDate"))+"' "
				  		+ " and ofm.drafttime <= '"+String.valueOf(map.get("endDate"))+"' ";
			  }else{
				  pars+=" and ofm.drafttime >= '"+String.valueOf(map.get("starDate"))+"' ";
			  }
		  }else{
			  if(!"".equals(map.get("endDate"))&&map.get("endDate")!=null && !StringUtils.isEmpty(map.get("endDate"))){
				  pars+= " and ofm.drafttime <= '"+String.valueOf(map.get("endDate"))+"' ";
			  }
		  }
		  
		  if("1".equals(map.get("isFiledSel"))){
			  pars +=" and ofm.orderstatus='是' ";
		  }else if("0".equals(map.get("isFiledSel"))){
			  pars +=" and ofm.orderstatus='否' ";
		  }
		  
		  sql="select ofm.uuid as \"orderId\" ,ofm.ordertitle as \"orderName\",ofm.ordernumber as \"orderNumber\",ofm.drafttime as \"orderLunTime\",ofm.draftmanname as \"orderCreater\",ofm.orderreqtimelimit as \"completionLimitTime\", "+
					"ofm.bcode as \"businessType\",ofm.pcode as \"productType\",ofm.signedstatus as \"signedStatus\",ofm.orderstatus as \"isFiled\",ofm.ratingvalue as \"satisfaction\",ofm.ratingvalue1 as \"correspondingSpeed\" "+
					",ofm.acceptperson as \"acceptperson\",ofm.ordercompletiontime as \"ordercompletiontime\",ofm.lzl as \"lzl\" from orderform_mv ofm "
					+ "where 1=1 "+pars+" order by drafttime desc";
		  return getMapNoBy(sql, page);
	}
	
	/**
	 * 分页条件查询：
	 * @param map
	 * @param page
	 * @param user
	 * @return
	 */
	public PageResponse getOrderlists(Map<String, String> map, PageRequest page,HttpServletRequest httpServletRequest) {
		String sql=""; 
		if(!StringUtils.isEmpty(map.get("likename"))){
			sql ="select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
							" to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
							+ " getbcodename(sof.Bcode) as \"businessType\","+
							  " getpcodename(sof.Pcode) as \"productType\","
							+ "(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					    " case when sof.signedstatus is not null then "+
					     " case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					        		 " else '已签约'end  "+
					         " else '--' end as \"signedStatus\""+
					    " ,to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					    " to_char((select HANDLEUSERID from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid)) as \"currentHandlingPersonId\","+
					    " (select HANDLEUSERNAME from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid) as \"currentHandlingPerson\""+
					    " ,to_char('1') as \"type\" ,case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
					    " from singorderform sof where sof.draftmanid='"+map.get("userId").trim()+"' and sof.parentordernumber is not null and sof.type='DI' and sof.state not in ('-1','2') and sof.ordertitle like '%"+map.get("likename").trim()+"%' "+
				" union all "+
					    " select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
					    "  to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
					    + 
					    " getbcodename(sof.Bcode) as \"businessType\","+
						  " getpcodename(sof.Pcode) as \"productType\","
					    + "(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					     " case when sof.signedstatus is not null then "+
					    " case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					        		 "  else '已签约'end  "+
					         "  else '--' end as \"signedStatus\","+
					    "  to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					    " (select ACCEPTPERSONID from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
					    		" where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPersonId\","+
					           " (select ACCEPTPERSON from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
							  "  where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPerson\","+
					          " to_char('0') as \"type\" ,case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
					 " from singorderform sof  where sof.draftmanid='"+map.get("userId").trim()+"'and sof.type='CS' and sof.state != '-1' and  (sof.transmitstate='1' or sof.transmitstate is null) and sof.ordertitle like '%"+map.get("likename").trim()+"%'";
			
			if("start".equals(audit)){
				if("1".equals(map.get("auditP"))){
					String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
					String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
			    	CommLogs.requOrderquery(unl, cun, "0", "订单名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));
				}
			}
		}else{
			sql ="select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
							" to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
							+ " getbcodename(sof.Bcode) as \"businessType\","+
							  " getpcodename(sof.Pcode) as \"productType\""
							+ ",(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					    " case when sof.signedstatus is not null then "+
					     " case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					        		 " else '已签约'end  "+
					         " else '--' end as \"signedStatus\""+
					    " ,to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					    " to_char((select HANDLEUSERID from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid)) as \"currentHandlingPersonId\","+
					    " (select HANDLEUSERNAME from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid) as \"currentHandlingPerson\""+
					    " ,to_char('1') as \"type\",case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
					    " from singorderform sof where sof.draftmanid='"+map.get("userId").trim()+"' and sof.parentordernumber is not null and sof.type='DI' and sof.state not in ('-1','2')  "+
				" union all "+
						" select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
						"  to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
						+ " getbcodename(sof.Bcode) as \"businessType\","+
						  " getpcodename(sof.Pcode) as \"productType\","
						+ "(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
						     " case when sof.signedstatus is not null then "+
						    " case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
						        		 "  else '已签约'end  "+
						         "  else '--' end as \"signedStatus\","+
						    "  to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
						    " (select ACCEPTPERSONID from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
						    		" where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPersonId\","+
						           " (select ACCEPTPERSON from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
								  "  where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPerson\","+
						          " to_char('0') as \"type\" ,case when sof.state='1' then '已处理' else '未处理' end as \"state\""+
						 " from singorderform sof  where sof.draftmanid='"+map.get("userId").trim()+"'and sof.type='CS' and sof.state != '-1' and  (sof.transmitstate='1' or sof.transmitstate is null) ";
		}
		return getMapNoBy(sql, page);
	}
	/**
	 * 分页条件查询（修改）：
	 * @param map
	 * @param page
	 * @param user
	 * @return
	 */
	public LayuiPage getOrderlists(Map<String, String> map, LayuiPage page,HttpServletRequest httpServletRequest) {
		String sql=""; 
		if(!StringUtils.isEmpty(map.get("likename"))){
			sql ="select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
					" to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
					+ " getbcodename(sof.Bcode) as \"businessType\","+
					" getpcodename(sof.Pcode) as \"productType\","
					+ "(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					" case when sof.signedstatus is not null the	n "+
					" case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					" else '已签约'end  "+
					" else '--' end as \"signedStatus\""+
					" ,to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					" to_char((select HANDLEUSERID from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid)) as \"currentHandlingPersonId\","+
					" (select HANDLEUSERNAME from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid) as \"currentHandlingPerson\""+
					" ,to_char('1') as \"type\" ,case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
					" from singorderform sof where sof.draftmanid='"+map.get("userId").trim()+"' and sof.parentordernumber is not null and sof.type='DI' and sof.state not in ('-1','2') and sof.ordertitle like '%"+map.get("likename").trim()+"%' "+
					" union all "+
					" select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
					"  to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
					+ 
					" getbcodename(sof.Bcode) as \"businessType\","+
					" getpcodename(sof.Pcode) as \"productType\","
					+ "(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					" case when sof.signedstatus is not null then "+
					" case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					"  else '已签约'end  "+
					"  else '--' end as \"signedStatus\","+
					"  to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					" (select ACCEPTPERSONID from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
					" where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPersonId\","+
					" (select ACCEPTPERSON from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
					"  where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPerson\","+
					" to_char('0') as \"type\" ,case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
					" from singorderform sof  where sof.draftmanid='"+map.get("userId").trim()+"'and sof.type='CS' and sof.state != '-1' and  (sof.transmitstate='1' or sof.transmitstate is null) and sof.ordertitle like '%"+map.get("likename").trim()+"%'";
			
			if("start".equals(audit)){
				if("1".equals(map.get("auditP"))){
					String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
					String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
					CommLogs.requOrderquery(unl, cun, "0", "订单名称:"+map.get("likename").trim(), "", "", "", "", "", "5", DateUtil.getIpAddr(httpServletRequest));
				}
			}
		}else{
			sql ="select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
					" to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
					+ " getbcodename(sof.Bcode) as \"businessType\","+
					" getpcodename(sof.Pcode) as \"productType\""
					+ ",(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					" case when sof.signedstatus is not null then "+
					" case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					" else '已签约'end  "+
					" else '--' end as \"signedStatus\""+
					" ,to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					" to_char((select HANDLEUSERID from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid)) as \"currentHandlingPersonId\","+
					" (select HANDLEUSERNAME from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid) as \"currentHandlingPerson\""+
					" ,to_char('1') as \"type\",case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
					" from singorderform sof where sof.draftmanid='"+map.get("userId").trim()+"' and sof.parentordernumber is not null and sof.type='DI' and sof.state not in ('-1','2')  "+
					" union all "+
					" select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.ordertitle as \"orderName\",sof.ordernumber as \"orderNumber\","+
					"  to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
					+ " getbcodename(sof.Bcode) as \"businessType\","+
					" getpcodename(sof.Pcode) as \"productType\","
					+ "(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					" case when sof.signedstatus is not null then "+
					" case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					"  else '已签约'end  "+
					"  else '--' end as \"signedStatus\","+
					"  to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					" (select ACCEPTPERSONID from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
					" where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPersonId\","+
					" (select ACCEPTPERSON from ( select stl.proid,sp.orderid,stl.acceptpersonid,stl.acceptperson,stl.state,to_char(stl.launchtime,'yyyy-mm-dd hh24:mi:ss'),ROW_NUMBER() OVER(PARTITION BY stl.proid ORDER BY stl.launchtime DESC) as \"num\" from singprocess sp left join singtasklist stl on stl.proid=sp.uuid  where stl.state <> '-1' order by stl.launchtime desc)"+
					"  where \"num\"=1 and ORDERID=sof.uuid) as \"currentHandlingPerson\","+
					" to_char('0') as \"type\" ,case when sof.state='1' then '已处理' else '未处理' end as \"state\""+
					" from singorderform sof  where sof.draftmanid='"+map.get("userId").trim()+"'and sof.type='CS' and sof.state != '-1' and  (sof.transmitstate='1' or sof.transmitstate is null) ";
		}
		page.setData(getPageList(sql, null, page));
		page.setCount(getCount("select count(0) from ("+sql+")"));
		page.setCode(0);
		return page;
	}
	/**
	 *  根据ID业务类型查询
	 * @param id
	 * @return
	 */
	public String queryBusinessById(String id){
		BusinessType  bt = new BusinessType();
		bt= (BusinessType) this.getSession().createCriteria(BusinessType.class).add(Restrictions.eq("bCode", id)).uniqueResult();
		if(bt!=null){
			return bt.getBusinessName();
		}else{
			return "";
		}
	}
	
	/**
	 *  根据ID产品类型查询
	 * @param id
	 * @return
	 */
	public String queryByOrderTypeId(String id){
		ProductType  pt=new ProductType();
		pt =(ProductType) this.getSession().createCriteria(ProductType.class).add(Restrictions.eq("pCode", id)).uniqueResult();
		if(pt!=null){
			return pt.getProductName();
		}else{
			return "";
		}
	}
	
	/**
	 * 参数
	 * @param mapList
	 * @return
	 */
	public List<Map<String, Object>> setParmas(List<Map<String, Object>> mapList){
		for (int i = 0; i < mapList.size(); i++) {
			String bname="";
			String pname="";
			String[] st = null;
			String[] st1 = null;
			if(!"--".equals((String)mapList.get(i).get("businessType"))){
				if(!StringUtils.isEmpty((String)mapList.get(i).get("businessType"))){
					 st= mapList.get(i).get("businessType").toString().split("[;|,]");
				}
				if(st!=null){
					for(int j=0;j<st.length;j++){
						
						if(j==0){
							bname+=queryBusinessById(st[j]) ;
						}else{
							bname+=","+queryBusinessById(st[j]) ;
						}
					}
				}
				mapList.get(i).put("businessType", bname);
			}else{
				mapList.get(i).put("businessType", "--");
			}
			
			if(!StringUtils.isEmpty((String)mapList.get(i).get("productType"))){
			 st1= mapList.get(i).get("productType").toString().split("[;|,]");
			}
			if(st1!=null){
			for(int j=0;j<st1.length;j++){
				if(j==0){
					pname+=queryByOrderTypeId(st1[j]) ;
				}else{
					pname+=","+queryByOrderTypeId(st1[j])  ;
				}
			}
			}
			mapList.get(i).put("productType", pname);
		}
		
		return mapList;
	}

	/** 
	 *  
	 * @Description: 生成excel并导出到（本地） 
	 *
	 */  
	public void exportExcelToOrderDJxl(List<Map<String,Object>> mapList,String type, HttpServletRequest httpServletRequest){ 
		HttpServletResponse  response=ServletActionContext.getResponse();
		
		String excelFile = FileUpload.getFtpURL()+"exportExcelToOrderJxl.xls";
		File f = new File(FileUpload.getFtpURL());
		if(!f.exists()&&!f.isDirectory()){
			f.mkdirs();
		}
		String exportName="exportData_"+FileUpload.getDateToString("yyyyMMdd");
		// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
		try {
			WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
			// 2、新建工作表(sheet)对象，并声明其属于第几页
			WritableSheet firstSheet = writeBook.createSheet("Excel", 1);// 第一个参数为工作簿的名称，第二个参数为页数
		
			if("type_1".equals(type)){
				String[] headers = new String[]{"地区公司名称","订单总数量","订单完成量","订单未完成量"};
				//-----------------------------------------------分公司：
				for(int i = 0; i<headers.length;i++){  
					// 3、创建单元格(Label)对象，
					Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
					WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
				       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
				       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
				       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
				       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
				       
				       CellView navCellView = new CellView();  
				       navCellView.setAutosize(true); //设置自动大小
				       navCellView.setSize(18);
				       
				       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
				       firstSheet.setColumnView(i, navCellView); //设置col显示样式
				       firstSheet.setRowView(i, 400, false); //设置行高
					firstSheet.addCell(label0);
				}  
				for(int i=0;i<mapList.size();i++){
					Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));
					firstSheet.addCell(label1); 
					firstSheet.addCell(setParams(1,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
					firstSheet.addCell(setParams(2,i+1,Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
					firstSheet.addCell(setParams(3,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
				}
			}else if("type_2".equals(type)){
				 String[] headers = new String[]{"地区公司名称","区县公司名称","订单总数量","订单完成量","订单未完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));
						firstSheet.addCell(label2);
						firstSheet.addCell(setParams(2,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(4,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
					}
			}else if("type_3".equals(type)){
				 String[] headers = new String[]{"地区公司名称","区县公司名称","订单发起人","订单总数量","订单完成量","订单未完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label2);
						Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("PERSONNEL")));
						firstSheet.addCell(label3);
						firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(4,i+1,Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(5,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
					}
			}else if("type_4".equals(type)){
				 String[] headers = new String[]{"地区公司名称","订单总数量","订单按时完成量","未完成或未按时完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						firstSheet.addCell(setParams(1,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(2,i+1,Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(3,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
					}
			}else if("type_5".equals(type)){
				 String[] headers = new String[]{"地区公司名称","区县公司名称","订单总数量","订单按时完成量","未完成或未按时完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label2);
						firstSheet.addCell(setParams(2,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(4,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
					}
			}else if("type_6".equals(type)){
				 String[] headers = new String[]{"地区公司名称","区县公司名称","订单发起人","订单总数量","订单按时完成量","未完成或未按时完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label2);
						Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("PERSONNEL")));
						firstSheet.addCell(label3);
						firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(4,i+1,Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(5,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
					}
			}
			
			// 4、打开流，开始写文件
			writeBook.write();
			// 5、关闭流
			writeBook.close();
			
			byte[] data = FileUtil.toByteArray2(excelFile); 
			String   fileName = URLEncoder.encode(exportName, "UTF-8");  
			response.reset();  
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName +".xls" +"\"");  
			response.addHeader("Content-Length", "" + data.length);  
			response.setContentType("application/octet-stream;charset=UTF-8");  
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());  
			outputStream.write(data);  
			outputStream.flush();  
			outputStream.close();
			response.flushBuffer();
			File fe = new File(excelFile);
			fe.delete();
		} catch (Exception e) {
			e.printStackTrace();
			File fe = new File(excelFile);
			if(f.exists()&&f.isDirectory()){
				fe.delete();
			}
			try {
				response.getWriter().write("<script>alert('导出Excel失败');history.go(-1);</script>");
				response.getWriter().flush(); 
			} catch (IOException e1) {
				e1.printStackTrace();
			} 
		}
	}
	/** 
	 *  
	 * @Description: 生成excel并导出到（本地） 
	 *
	 */  
	public void exportExcelToOrderDByTypeJxl(List<Map<String,Object>> mapList,String type, HttpServletRequest httpServletRequest){ 
		HttpServletResponse  response=ServletActionContext.getResponse();
		
		String excelFile = FileUpload.getFtpURL()+"exportExcelToOrderJxl.xls";
		File f = new File(FileUpload.getFtpURL());
		if(!f.exists()&&!f.isDirectory()){
			f.mkdirs();
		}
		String exportName="exportData_"+FileUpload.getDateToString("yyyyMMdd");
		// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
		try {
			WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
			// 2、新建工作表(sheet)对象，并声明其属于第几页
			WritableSheet firstSheet = writeBook.createSheet("Excel", 1);// 第一个参数为工作簿的名称，第二个参数为页数
			
			if("type_1".equals(type)){
				String[] headers = new String[]{"地区公司名称","产品类型","订单总数量","订单完成量","订单未完成量"};
				//-----------------------------------------------分公司：
				for(int i = 0; i<headers.length;i++){  
					// 3、创建单元格(Label)对象，
					Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
					WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
				       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
				       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
				       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
				       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
				       
				       CellView navCellView = new CellView();  
				       navCellView.setAutosize(true); //设置自动大小
				       navCellView.setSize(18);
				       
				       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
				       firstSheet.setColumnView(i, navCellView); //设置col显示样式
				       firstSheet.setRowView(i, 400, false); //设置行高
					firstSheet.addCell(label0);
				}  
				for(int i=0;i<mapList.size();i++){
					Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));
					firstSheet.addCell(label1); 
					Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("PRODUCTNAME")));
					firstSheet.addCell(label2); 
					firstSheet.addCell(setParams(2,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
					firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
					firstSheet.addCell(setParams(4,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
				}
			}else if("type_2".equals(type)){
				 String[] headers = new String[]{"地区公司名称","产品类型","区县公司名称","订单总数量","订单完成量","订单未完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("PRODUCTNAME")));
						firstSheet.addCell(label2); 
						Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));
						firstSheet.addCell(label3);
						firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(4,i+1,Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(5,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
					}
			}else if("type_3".equals(type)){
				 String[] headers = new String[]{"地区公司名称","产品类型","区县公司名称","订单发起人","订单总数量","订单完成量","订单未完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label2);
						Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("PRODUCTNAME")));
						firstSheet.addCell(label3); 
						Label label4 = new Label(3, i+1,String.valueOf(mapList.get(i).get("PERSONNEL")));
						firstSheet.addCell(label4);
						firstSheet.addCell(setParams(4,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(5,i+1,Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(6,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("COMPLETCOUNT").toString()))); 
					}
			}else if("type_4".equals(type)){
				 String[] headers = new String[]{"地区公司名称","产品类型","订单总数量","订单按时完成量","未完成或未按时完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("PRODUCTNAME")));
						firstSheet.addCell(label2); 
						firstSheet.addCell(setParams(2,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(4,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
					}
			}else if("type_5".equals(type)){
				 String[] headers = new String[]{"地区公司名称","产品类型","区县公司名称","订单总数量","订单按时完成量","未完成或未按时完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("PRODUCTNAME")));
						firstSheet.addCell(label2); 
						Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label3);
						firstSheet.addCell(setParams(3,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(4,i+1,Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(5,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
					}
			}else if("type_6".equals(type)){
				 String[] headers = new String[]{"地区公司名称","产品类型","区县公司名称","订单发起人","订单总数量","订单按时完成量","未完成或未按时完成量"};
					//-----------------------------------------------分公司：
					for(int i = 0; i<headers.length;i++){  
						// 3、创建单元格(Label)对象，
						Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
					       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
					       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
					       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
					       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
					       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
					       
					       CellView navCellView = new CellView();  
					       navCellView.setAutosize(true); //设置自动大小
					       navCellView.setSize(18);
					       
					       label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
					       firstSheet.setColumnView(i, navCellView); //设置col显示样式
					       firstSheet.setRowView(i, 400, false); //设置行高
						firstSheet.addCell(label0);
					}  
					for(int i=0;i<mapList.size();i++){
						Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("COMPANY")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label1); 
						Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("PRODUCTNAME")));
						firstSheet.addCell(label2); 
						Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("DEPARTMENT")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
						firstSheet.addCell(label3);
						Label label4 = new Label(3, i+1,String.valueOf(mapList.get(i).get("PERSONNEL")));
						firstSheet.addCell(label4);
						firstSheet.addCell(setParams(4,i+1,Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))); 
						firstSheet.addCell(setParams(5,i+1,Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
						firstSheet.addCell(setParams(6,i+1,(Integer.parseInt(mapList.get(i).get("ORDERCOUNT").toString()))-Integer.parseInt(mapList.get(i).get("ONTIMECOMPLETCOUNT").toString()))); 
					}
			}
			// 4、打开流，开始写文件
			writeBook.write();
			// 5、关闭流
			writeBook.close();
			
			byte[] data = FileUtil.toByteArray2(excelFile); 
			String   fileName = URLEncoder.encode(exportName, "UTF-8");  
			response.reset();  
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName +".xls" +"\"");  
			response.addHeader("Content-Length", "" + data.length);  
			response.setContentType("application/octet-stream;charset=UTF-8");  
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());  
			outputStream.write(data);  
			outputStream.flush();  
			outputStream.close();
			response.flushBuffer();
			File fe = new File(excelFile);
			fe.delete();
		} catch (Exception e) {
			e.printStackTrace();
			File fe = new File(excelFile);
			if(f.exists()&&f.isDirectory()){
				fe.delete();
			}
			try {
				response.getWriter().write("<script>alert('导出Excel失败');history.go(-1);</script>");
				response.getWriter().flush(); 
			} catch (IOException e1) {
				e1.printStackTrace();
			} 
		}
	}
	/**
	 * 专用： 
	 * @param ma
	 * @param type
	 * @param ot
	 * @param pt
	 */
	public void exportExcelSepcialOrder(List<Map<String, Object>> ma,String type,String ot,String pt, HttpServletRequest httpServletRequest) {
		
		HttpServletResponse  response=ServletActionContext.getResponse();
				
				String excelFile = FileUpload.getFtpURL()+"exportExcelToOrderJxl.xls";
				File f = new File(FileUpload.getFtpURL());
				if(!f.exists()&&!f.isDirectory()){
					f.mkdirs();
				}
				String exportName="exportData_"+FileUpload.getDateToString("yyyyMMdd");
				// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
				try {
					WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
					// 2、新建工作表(sheet)对象，并声明其属于第几页
					WritableSheet firstSheet = writeBook.createSheet("Excel", 1);// 第一个参数为工作簿的名称，第二个参数为页数
					//String[] headers = new String[]{};
					List<String> liss = new ArrayList<String>();
						
					if("1".equals(type)){
						liss.add("公司名称");
						List<Map<String,String>> clname= queryOTAndPT(ot,pt);
						for(int i=0;i<clname.size();i++){
							Map<String,String> objects= clname.get(i);
							liss.add(objects.get("LINKNAME").trim()+"(小时)");
						}
						for(int i = 0; i<liss.size();i++){  
							// 3、创建单元格(Label)对象，
							Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
						       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
						       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
						       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
						       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
						       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
						       
						       CellView navCellView = new CellView();  
						       navCellView.setAutosize(true); //设置自动大小
						       navCellView.setSize(18);
						       
						       label0 = new Label(i,0,liss.get(i),wcfTitle); //Label(col,row,str);   
						       firstSheet.setColumnView(i, navCellView); //设置col显示样式
						       firstSheet.setRowView(i, 400, false); //设置行高
							firstSheet.addCell(label0);
						}  
						for(int i=0;i<ma.size();i++){
							Label label1 = new Label(0, i+1,String.valueOf(ma.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label1); 
							for(int j=0;j<clname.size();j++){
								Map<String,String> objects= clname.get(j);
								//Label label1 = new Label(0, i+1,String.valueOf(ma.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
								firstSheet.addCell(new Label(j+1, i+1,(String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim()))).equals("null")?"0":String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim())))); 
								//firstSheet.addCell(setParams(j+1,i+1,Float.parseFloat(String.valueOf())); 
							}
						}
						
					}else if("2".equals(type)){
						liss.add("公司名称");
						liss.add("区县名称");
						List<Map<String,String>> clname= queryOTAndPT(ot,pt);
						for(int i=0;i<clname.size();i++){
							Map<String,String> objects= clname.get(i);
							liss.add(objects.get("LINKNAME").trim()+"(小时)");
						}
						for(int i = 0; i<liss.size();i++){  
							// 3、创建单元格(Label)对象，
							Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
						       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
						       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
						       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
						       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
						       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
						       
						       CellView navCellView = new CellView();  
						       navCellView.setAutosize(true); //设置自动大小
						       navCellView.setSize(18);
						       
						       label0 = new Label(i,0,liss.get(i),wcfTitle); //Label(col,row,str);   
						       firstSheet.setColumnView(i, navCellView); //设置col显示样式
						       firstSheet.setRowView(i, 400, false); //设置行高
							firstSheet.addCell(label0);
						}  
						for(int i=0;i<ma.size();i++){
							Label label1 = new Label(0, i+1,String.valueOf(ma.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label1); 
							Label label2 = new Label(1, i+1,String.valueOf(ma.get(i).get("TWODNAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label2); 
							int cl = 2;
							for(int j=0;j<clname.size();j++){
								Map<String,String> objects= clname.get(j);
								firstSheet.addCell(new Label(j+cl, i+1,(String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim()))).equals("null")?"0":String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim())))); 
								//firstSheet.addCell(setParams(j+cl,i+1,Integer.parseInt(String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim()))))); 
							}
						}
						
					}else if("3".equals(type)){
						liss.add("公司名称");
						liss.add("区县名称");
						liss.add("创建人");
						List<Map<String,String>> clname= queryOTAndPT(ot,pt);
						for(int i=0;i<clname.size();i++){
							Map<String,String> objects= clname.get(i);
							liss.add(objects.get("LINKNAME").trim()+"(小时)");
						}
						for(int i = 0; i<liss.size();i++){  
							// 3、创建单元格(Label)对象，
							Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
						       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
						       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
						       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
						       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
						       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
						       
						       CellView navCellView = new CellView();  
						       navCellView.setAutosize(true); //设置自动大小
						       navCellView.setSize(18);
						       
						       label0 = new Label(i,0,liss.get(i),wcfTitle); //Label(col,row,str);   
						       firstSheet.setColumnView(i, navCellView); //设置col显示样式
						       firstSheet.setRowView(i, 400, false); //设置行高
							firstSheet.addCell(label0);
						}  
						for(int i=0;i<ma.size();i++){
							Label label1 = new Label(0, i+1,String.valueOf(ma.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label1); 
							Label label2 = new Label(1, i+1,String.valueOf(ma.get(i).get("TWODNAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label2); 
							Label label3 = new Label(2, i+1,String.valueOf(ma.get(i).get("EMPLOYEE_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label3); 
							int cl = 3;
							for(int j=0;j<clname.size();j++){
								Map<String,String> objects= clname.get(j);
								firstSheet.addCell(new Label(j+cl, i+1,(String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim()))).equals("null")?"0":String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim())))); 
								//firstSheet.addCell(setParams(j+cl,i+1,Integer.parseInt(String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim()))))); 
							}
						}
						
					}else if("4".equals(type)){
						liss.add("公司名称");
						liss.add("区县名称");
						liss.add("创建人");
						liss.add("订单名称");
						liss.add("订单编号");
						List<Map<String,String>> clname= queryOTAndPT(ot,pt);
						for(int i=0;i<clname.size();i++){
							Map<String,String> objects= clname.get(i);
							liss.add(objects.get("LINKNAME").trim()+"(小时)");
						}
						for(int i = 0; i<liss.size();i++){  
							// 3、创建单元格(Label)对象，
							Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
						       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
						       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
						       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
						       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
						       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
						       
						       CellView navCellView = new CellView();  
						       navCellView.setAutosize(true); //设置自动大小
						       navCellView.setSize(18);
						       
						       label0 = new Label(i,0,liss.get(i),wcfTitle); //Label(col,row,str);   
						       firstSheet.setColumnView(i, navCellView); //设置col显示样式
						       firstSheet.setRowView(i, 400, false); //设置行高
							firstSheet.addCell(label0);
						}  
						for(int i=0;i<ma.size();i++){
							Label label1 = new Label(0, i+1,String.valueOf(ma.get(i).get("COMPANY_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label1); 
							Label label2 = new Label(1, i+1,String.valueOf(ma.get(i).get("TWODNAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label2); 
							Label label3 = new Label(2, i+1,String.valueOf(ma.get(i).get("EMPLOYEE_NAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label3); 
							Label label4 = new Label(3, i+1,String.valueOf(ma.get(i).get("DEMANDNAME")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label4); 
							Label label5 = new Label(4, i+1,String.valueOf(ma.get(i).get("ORDERNUMBER")));// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
							firstSheet.addCell(label5); 
							int cl = 5;
							for(int j=0;j<clname.size();j++){
								Map<String,String> objects= clname.get(j);
								firstSheet.addCell(new Label(j+cl, i+1,(String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim()))).equals("null")?"0":String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim())))); 
								//firstSheet.addCell(setParams(j+cl,i+1,Integer.parseInt(String.valueOf(ma.get(i).get(objects.get("LINKTEMPCODE").trim()))))); 
							}
						}
						
					}else if("5".equals(type)){
						String[] hea = new String[]{"订单发起人","订单名称","订单编号","订单生成时间","操作类型","业务类型","业务产品","需求单位名称","签约情况","订单要求完成时间","当前处理人",};  
			        	 //-----------------------------------------------区县：
			        	 for(int i = 0; i<hea.length;i++){  
				        		// 3、创建单元格(Label)对象，
				        		Label label0 = null;//new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
				        		WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
							       WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
							       wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
							       wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
							       wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
							       wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
							       
							       CellView navCellView = new CellView();  
							       navCellView.setAutosize(true); //设置自动大小
							       navCellView.setSize(18);
							       
							       label0 = new Label(i,0,hea[i],wcfTitle); //Label(col,row,str);   
							       firstSheet.setColumnView(i, navCellView); //设置col显示样式
							       firstSheet.setRowView(i, 400, false); //设置行高
				        		firstSheet.addCell(label0);
				        	}  
			        	 for(int i=0;i<ma.size();i++){
			        		 Label label1 = new Label(0, i+1,String.valueOf(ma.get(i).get("orderSponsor")));
			        		 firstSheet.addCell(label1); 
			        		 Label label2 = new Label(1, i+1,String.valueOf(ma.get(i).get("orderName")));
			        		 firstSheet.addCell(label2); 
			        		 Label label3 = new Label(2, i+1,String.valueOf(ma.get(i).get("orderNumber")));
			        		 firstSheet.addCell(label3); 
			        		 Label label4 = new Label(3, i+1,String.valueOf(ma.get(i).get("orderGenerationTime")));
			        		 firstSheet.addCell(label4); 
			        		 Label label5 = new Label(4, i+1,String.valueOf(ma.get(i).get("operationType")));
			        		 firstSheet.addCell(label5); 
			        		 Label label6 = new Label(5, i+1,String.valueOf(ma.get(i).get("businessType")));
			        		 firstSheet.addCell(label6); 
			        		 Label label7 = new Label(6, i+1,String.valueOf(ma.get(i).get("productType")));
			        		 firstSheet.addCell(label7); 
			        		 Label label8 = new Label(7, i+1,String.valueOf(ma.get(i).get("demandUnitName")));
			        		 firstSheet.addCell(label8); 
			        		 Label label9 = new Label(8, i+1,String.valueOf(ma.get(i).get("signedStatus")));
			        		 firstSheet.addCell(label9); 
			        		 Label label10 = new Label(9, i+1,String.valueOf(ma.get(i).get("orderCompletionTime")));
			        		 firstSheet.addCell(label10); 
			        		 Label label11 = new Label(10, i+1,String.valueOf(ma.get(i).get("currentHandlingPerson")));
			        		 firstSheet.addCell(label11); 
			        	 }
					}
		
		// 4、打开流，开始写文件
					writeBook.write();
					// 5、关闭流
					writeBook.close();
					
					byte[] data = FileUtil.toByteArray2(excelFile); 
					String   fileName = URLEncoder.encode(exportName, "UTF-8");  
					response.reset();  
					response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName +".xls" +"\"");  
					response.addHeader("Content-Length", "" + data.length);  
					response.setContentType("application/octet-stream;charset=UTF-8");  
					OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());  
					outputStream.write(data);  
					outputStream.flush();  
					outputStream.close();
					response.flushBuffer();
					File fe = new File(excelFile);
					fe.delete();
					if("start".equals(audit)){
						String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
						String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
				    	CommLogs.requOrderquery(unl, cun, "0", fileName +".xls", "", "", "", "", "", "14", DateUtil.getIpAddr(httpServletRequest));
				    }
				} catch (Exception e) {
					e.printStackTrace();
					File fe = new File(excelFile);
					if(f.exists()&&f.isDirectory()){
						fe.delete();
					}
					try {
						response.getWriter().write("<script>alert('导出Excel失败');history.go(-1);</script>");
						response.getWriter().flush(); 
					} catch (IOException e1) {
						e1.printStackTrace();
					} 
				}
	}
	
	/** 
	 *  
	 * @Description:统计分析app登陆次数 生成excel并导出到（本地） 
	 *
	 */  
	public void exportExcelAppLoginCsJxl(List<Map<String,Object>> mapList, HttpServletRequest httpServletRequest){ 
		HttpServletResponse  response=ServletActionContext.getResponse();
		
		String excelFile = FileUpload.getFtpURL()+"exportExcelToOrderJxl.xls";
		File f = new File(FileUpload.getFtpURL());
		if(!f.exists()&&!f.isDirectory()){
			f.mkdirs();
		}
		String exportName="exportData_"+FileUpload.getDateToString("yyyyMMdd");
		// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
		try {
			WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
			// 2、新建工作表(sheet)对象，并声明其属于第几页
			WritableSheet firstSheet = writeBook.createSheet("统计分析", 1);// 第一个参数为工作簿的名称，第二个参数为页数
			String[] headers = new String[]{"分公司","登录次数","电话","角色2","区县/省重客","姓名","角色","账号"};
			//-----------------------------------------------分公司：
			for(int i = 0; i<headers.length;i++){  
				// 3、创建单元格(Label)对象，
				Label label0 =null;// new Label(i, 0,headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
				WritableFont   wf2   =   new   WritableFont(WritableFont.ARIAL,10,WritableFont.BOLD,false,UnderlineStyle.NO_UNDERLINE,jxl.format.Colour.BLACK); // 定义格式 字体 下划线 斜体 粗体 颜色
				WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				wcfTitle.setBackground(jxl.format.Colour.IVORY);  //象牙白
				wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN,jxl.format.Colour.BLACK); //BorderLineStyle边框
				wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); //设置垂直对齐
				wcfTitle.setAlignment(Alignment.CENTRE); //设置垂直对齐
				
				CellView navCellView = new CellView();  
				navCellView.setAutosize(true); //设置自动大小
				navCellView.setSize(18);
				
				label0 = new Label(i,0,headers[i],wcfTitle); //Label(col,row,str);   
				firstSheet.setColumnView(i, navCellView); //设置col显示样式
				firstSheet.setRowView(i, 400, false); //设置行高
				firstSheet.addCell(label0);
			}  
			for(int i=0;i<mapList.size();i++){
				Label label1 = new Label(0, i+1,String.valueOf(mapList.get(i).get("companyName")));
				firstSheet.addCell(label1); 
				Label label2 = new Label(1, i+1,String.valueOf(mapList.get(i).get("count")));
				firstSheet.addCell(label2); 
				Label label3 = new Label(2, i+1,String.valueOf(mapList.get(i).get("mobile")));
				firstSheet.addCell(label3);
				Label label4 = new Label(3, i+1,String.valueOf(mapList.get(i).get("role2")));
				firstSheet.addCell(label4); 
				Label label5 = new Label(4, i+1,String.valueOf(mapList.get(i).get("quxian")));
				firstSheet.addCell(label5); 
				Label label6 = new Label(5, i+1,String.valueOf(mapList.get(i).get("role")));
				firstSheet.addCell(label6); 
				Label label7 = new Label(6, i+1,String.valueOf(mapList.get(i).get("name")));
				firstSheet.addCell(label7); 
				Label label8 = new Label(7, i+1,String.valueOf(mapList.get(i).get("loginName")));
				firstSheet.addCell(label8); 
			
			}
			// 4、打开流，开始写文件
			writeBook.write();
			// 5、关闭流
			writeBook.close();
			byte[] data = FileUtil.toByteArray2(excelFile); 
			String   fileName = URLEncoder.encode(exportName, "UTF-8");  
			response.reset();  
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName +".xls" +"\"");  
			response.addHeader("Content-Length", "" + data.length);  
			response.setContentType("application/octet-stream;charset=UTF-8");  
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());  
			outputStream.write(data);  
			outputStream.flush();  
			outputStream.close();
			response.flushBuffer();
			File fe = new File(excelFile);
			fe.delete();
			if("start".equals(audit)){
				String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
				String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
		    	CommLogs.requOrderquery(unl, cun, "0", fileName +".xls", "", "", "", "", "", "12", DateUtil.getIpAddr(httpServletRequest));
		    }
		} catch (Exception e) {
			e.printStackTrace();
			File fe = new File(excelFile);
			if(f.exists()&&f.isDirectory()){
				fe.delete();
			}
			try {
				response.getWriter().write("<script>alert('导出Excel失败');history.go(-1);</script>");
				response.getWriter().flush(); 
			} catch (IOException e1) {
				e1.printStackTrace();
			} 
		}
	}
	public List<Map<String,String>> queryOTAndPT(String otp, String ptp) {
		String sql =" select t2.linkname,t2.linktempcode from linktemplate t1 right join linktemplate t2 on t2.parentlinktempcode=t1.linktempcode where t2.state='1' and t1.pflowcode in ( select t.pflowcode from  productflow t where t.pcode=? and t.otcode=?)";
		List<Map<String,String>> list=this.getSession().createSQLQuery(sql).setParameter(0, ptp).setParameter(1, otp).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		return list;
	}
}
