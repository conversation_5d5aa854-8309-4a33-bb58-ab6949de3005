package com.xinxinsoft.service.MarketActivitiesService;

import com.xinxinsoft.entity.MarketActivities.*;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.ESBReqMsgUtil;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.hibernate.Query;
import org.hibernate.Session;

import org.hibernate.transform.Transformers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

public class MarketActivitiesService extends BaseService {
    //发布环境
    private static final String ESB_URL_172 = "http://**************:51000/esbWS/rest/";
    //发布环境
    private static final String ESB_URL_51 = " http://*************:52000/esbWS/rest/";
    //正式环境
    private static final String ESB_URL_38 = "http://*************:51000/esbWS/rest/";

    private static final Logger logger = LoggerFactory.getLogger(MarketActivitiesService.class);

    private static Boolean isES = false;

    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }

    //保存存量统存集团客户库
    public MarketStockSystemGroup addClientLibrary(MarketStockSystemGroup mssg) {
        try {
            Assert.notNull(mssg); //判断是否为空
            Object merge = this.getSession().merge(mssg);
            mssg = (MarketStockSystemGroup) merge;
            return mssg;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    //清空存量统存集团客户库
    public void deleteClientLibrary() {
        String sql = "truncate table BPMS_MARKET_STOCKSYSTEMGROUP";
        this.getSession().createSQLQuery(sql).executeUpdate();
    }

    public LayuiPage findQuotaIniallzation(LayuiPage page, SystemUser user) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "'";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "SELECT * FROM BPMS_MARKET_QUOTAINIALLZATION WHERE VERSION_NUMBER =(SELECT MAX(VERSION_NUMBER) FROM BPMS_MARKET_QUOTAINIALLZATION) ";
//        if (list.get(0).get("COMPANY_NAME") != "") {
//            sql += "AND ATTRIBUTIONCOMPANY_NAME = '" + list.get(0).get("COMPANY_NAME") + "'";
//        }
        sql += "AND ATTRIBUTIONCOMPANY_NAME = '省公司' ";
        sql += "ORDER BY COMPANY_CODE";
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询活动代码   返回layuipage
     *
     * @param page       LayuiPage对象
     * @param ActiveCode 活动代码
     * @param ActiveType 活动类型
     * @return LayuiPage对象
     * @auther TX
     * @date 2021-5-13
     */
    public LayuiPage findActiveCodeByPage(LayuiPage page, String ActiveCode, String ActiveType, String transferMonth) {
        String sql = "SELECT * FROM BPMS_MARKET_ACTIVECODE WHERE ACTIVE_TYPE='" + ActiveType + "'";
        if (!"".equals(ActiveCode) && ActiveCode != null) {
            sql += "AND ACTIVE_CODE = '" + ActiveCode + "' ";
        }
        if (!"".equals(transferMonth) && transferMonth != null) {
            sql += "AND TRANSFER_MONTH = '" + transferMonth + "' ";
        }
        page.setCount(getCount("select count(0) from ( " + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public List<Map<String, String>> findActiveCodeByPageNew(String ActiveType, String month, String market_proportion) {
        String sql = "SELECT * FROM BPMS_MARKET_ACTIVECODE WHERE ACTIVE_TYPE='" + ActiveType + "'";
        if (!"".equals(month) && month != null) {
            sql += "AND TRANSFER_MONTH = '" + month + "' ";
        }
        if (!"".equals(market_proportion) && market_proportion != null) {
            sql += "AND ACTIVE_RATE = '" + market_proportion + "' ";
        }
//        page.setCount(getCount("select count(0) from ( " + sql + ")"));
//        if (page.getCount() > 0) {
//            page.setData(getPageList(sql, null, page));
//        }
        logger.info("活动代码sql="+sql);
        List<Map<String, String>> lis = this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return lis;
    }

    /**
     * 根据编号查询活动代码
     *
     * @param ACTIVE_CODE 代码编号
     * @return MarketActiveCode
     * @auther TX
     * @date 2021-5-17
     */
    public MarketActiveCode getMarketActiveCodeByActiveCode(String ACTIVE_CODE) {
        String sql = "SELECT * FROM BPMS_MARKET_ACTIVECODE WHERE ACTIVE_CODE=?";
        MarketActiveCode marketActiveCode = (MarketActiveCode) getSession().createSQLQuery(sql).addEntity(MarketActiveCode.class)
                .setString(0, ACTIVE_CODE).uniqueResult();
        return marketActiveCode;
    }

    /**
     * 根据编号查询工单明细
     *
     * @param ID 工单明细编号
     * @return MarketQuotasWorkDet
     * @auther TX
     * @date 2021-5-17
     */
    public MarketQuotasWorkDet QueryMarketQuotasWorkDetById(String ID) {
        String sql = "SELECT * FROM BPMS_MARKET_QUOTASWORKDET WHERE UUID = ?";
        MarketQuotasWorkDet marketQuotasWorkDet = (MarketQuotasWorkDet) getSession().createSQLQuery(sql).addEntity(MarketQuotasWorkDet.class)
                .setString(0, ID).uniqueResult();
        return marketQuotasWorkDet;
    }

    /**
     * 根据编号查询配额信息
     *
     * @param COMPANY_CODE 代码编号
     * @return MarketQuotaIniallzation
     * @auther TX
     * @date 2021-5-17
     */
    public MarketQuotaIniallzation getMarketQuotaIniallzationByCompanyCode(String COMPANY_CODE) {
        String sql = "SELECT * FROM BPMS_MARKET_QUOTAINIALLZATION WHERE COMPANY_CODE = ?";
        MarketQuotaIniallzation marketQuotaIniallzation = (MarketQuotaIniallzation) getSession().createSQLQuery(sql).addEntity(MarketQuotaIniallzation.class)
                .setString(0, COMPANY_CODE).uniqueResult();
        return marketQuotaIniallzation;
    }

    /**
     * 根据部门名称查询配额信息
     *
     * @param COMPANY_NAME 部门名称
     * @return MarketQuotaIniallzation
     * @auther TX
     * @date 2021-5-17
     */
    public MarketQuotaIniallzation getMarketQuotaIniallzationByCompanyName(String COMPANY_NAME) {
        String sql = "SELECT * FROM BPMS_MARKET_QUOTAINIALLZATION WHERE VERSION_NUMBER =(SELECT MAX(VERSION_NUMBER) FROM BPMS_MARKET_QUOTAINIALLZATION) AND COMPANY_NAME = ?";
        MarketQuotaIniallzation marketQuotaIniallzation = (MarketQuotaIniallzation) getSession().createSQLQuery(sql).addEntity(MarketQuotaIniallzation.class)
                .setString(0, COMPANY_NAME).uniqueResult();
        return marketQuotaIniallzation;
    }

    /**
     * 根据用户查询VW_USERINFO视图
     * @param user
     * @return
     */
    public List<Map<String,Object>> getVwUser(SystemUser user) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '"+user.getRowNo()+"' ";
        List<Map<String,Object>> list =getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return list;
    }

    /**
     * 查询最新配额版本号
     *
     * @return VERSION_NUMBER
     * @auther TX
     * @date 2021-5-17
     */
    public String GetMarketQuotaInialVersionNumber() {
        String sql = "SELECT MAX(VERSION_NUMBER) FROM BPMS_MARKET_QUOTAINIALLZATION";
        String VERSION_NUMBER = getSession().createSQLQuery(sql).setCacheable(true).uniqueResult() + "";
        return VERSION_NUMBER;
    }

    /**
     * 根据编号查询分公司下属所有的配额信息
     *
     * @param COMPANY_CODE 代码编号
     * @return List<MarketQuotaIniallzation>
     * @auther TX
     * @date 2021-5-17
     */
    public List<MarketQuotaIniallzation> getMarketQuotaIniallzationListByCompanyCode(String COMPANY_CODE) {
        String sql = "SELECT * FROM BPMS_MARKET_QUOTAINIALLZATION WHERE ATTRIBUTIONCOMPANY_CODE = ?";
        return getSession().createSQLQuery(sql).addEntity(MarketQuotaIniallzation.class).setString(0, COMPANY_CODE).list();
    }

    /**
     * 查询除省公司外所有地市公司
     *
     * @return List集合
     * @auther TX
     * @date 2021-5-17
     */
    public List<Map<String, String>> getCompanyList() {
        String company = "SELECT DISTINCT COMPANY_NAME,COMPANY_CODE from VW_USERINFO WHERE COMPANY_CODE!='00' ORDER BY COMPANY_CODE ";
        return getSession().createSQLQuery(company).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 根据用户id获取当前用户所在地市的首字母缩写
     *
     * @return List集合
     * @auther TX
     * @date 2021-5-17
     */
    public List<Object[]> getbumen(int userID) {
        String sql = "select " + "case when dn1.DEPARTMENT_LEVEL=1 " + "THEN dn1.DEPARTMENT_NAME " + "else dn2.DEPARTMENT_NAME " + "end," + "as2.COMPANY_NAME," + "as2.COMPANY_IBM "
                + "from  AFR_SYSTEMDEPT dn1 " + "left join AFR_SYSTEMDEPT dn2 " + "on " + "dn1.DEPARTMENT_PARENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEMDEPT as1 " + "on "
                + "as1.DEPARTMENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEM_DEPT_USER asdu " + "on " + "asdu.DEPARTMENT_NO=dn1.DEPARTMENT_NO " + "left join AFR_SYSTEMCOMPANY as2 " + "on "
                + "as2.COMPANY_CODE=dn2.COMPANY_CODE " + "where asdu.ROWNO=?";
        Query query = getSession().createSQLQuery(sql);
        query.setInteger(0, userID);
        List<Object[]> s = query.list();
        return s;
    }

    /**
     * 新建活动代码
     *
     * @param marketActiveCode 活动代码对象
     * @return MarketActiveCode对象
     * @auther TX
     * @date 2021-5-13
     */
    public MarketActiveCode addMarketActiveCode(MarketActiveCode marketActiveCode) {
        try {
            Session session = this.getSession();
            session.save(marketActiveCode);
            return marketActiveCode;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新建配额工单
     *
     * @param marketQuotasWorkOrder 工单对象
     * @return marketQuotasWorkOrder  对象
     * @auther TX
     * @date 2021-5-25
     */
    public MarketQuotasWorkOrder addMarketQuotasWorkOrder(MarketQuotasWorkOrder marketQuotasWorkOrder) {
        try {
            Session session = this.getSession();
            session.save(marketQuotasWorkOrder);
            return marketQuotasWorkOrder;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新建配额工单明细
     *
     * @param marketQuotasWorkDet 明细对象
     * @return marketQuotasWorkDet  对象
     * @auther TX
     * @date 2021-5-25
     */
    public MarketQuotasWorkDet addMarketQuotasWorkDet(MarketQuotasWorkDet marketQuotasWorkDet) {
        try {
            Session session = this.getSession();
            session.save(marketQuotasWorkDet);
            return marketQuotasWorkDet;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新建地市配额
     *
     * @param marketQuotaIniallzation 配额对象
     * @return marketQuotaIniallzation  对象
     * @auther TX
     * @date 2021-5-25
     */
    public MarketQuotaIniallzation addMarketQuotaIniallzation(MarketQuotaIniallzation marketQuotaIniallzation) {
        try {
            Session session = this.getSession();
            session.save(marketQuotaIniallzation);
            return marketQuotaIniallzation;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 修改地市配额
     *
     * @param marketQuotaIniallzation 配额对象
     * @return marketQuotaIniallzation  对象
     * @auther TX
     * @date 2021-5-25
     */
    public MarketQuotaIniallzation UpdateMarketQuotaIniallzation(MarketQuotaIniallzation marketQuotaIniallzation) {
        try {
            if (marketQuotaIniallzation != null) {
                Session session = this.getSession();
                session.update(marketQuotaIniallzation);
            }
            return marketQuotaIniallzation;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存附件信息到中间表
     *
     * @param sa 附件对象
     * @return 附件对象
     * @tangxiao
     */
    public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
        if (sa.getId() == null) {
            String sql = "select  * from SingleAndAttachment t where t.orderid=? and t.attachmentid=? and t.link=?";
            Object count = getSession().createSQLQuery(sql).setString(0, sa.getOrderID()).setString(1, sa.getAttachmentId()).setString(2, sa.getLink()).uniqueResult();
            if (null == count) {
                Session session = this.getSession();
                session.saveOrUpdate(sa);
                session.flush();
                return sa;
            } else {
                return null;
            }
        } else {
            Session session = this.getSession();
            session.saveOrUpdate(sa);
            session.flush();
            return sa;
        }
    }


    /**
     * 修改活动代码
     *
     * @param marketActiveCode 活动代码对象
     * @return 对象
     * @auther TX
     * @date 2021-5-13
     */
    public MarketActiveCode updateMarketActiveCode(MarketActiveCode marketActiveCode) {
        try {
            if (marketActiveCode != null) {
                Session session = this.getSession();
                session.update(marketActiveCode);
            }
            return marketActiveCode;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存导入的Excel数据(业务包配额)
     *
     * @param ma
     * @return
     */
    public MarketActiveCode saveExcelData(MarketActiveCode ma) {
        try {
            Session session = this.getSession();
            session.save(ma);
            return ma;
        } catch (Exception e) {
            logger.info("添加数据错误：" + e);
            return null;
        }
    }

    /**
     * 根据用户编号查询权限id
     */
    public List<Map<String, Object>> findByRowNo(int rowNo) {
        String hql = "select ROLE_ID from SYSTEM_USER_ROLE where row_no=?";
        List<Map<String, Object>> list = getSession().createSQLQuery(hql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setInteger(0, rowNo).list();
        return list;
    }

    /**
     * 根绝角色名称查询该角色下所有用户
     *
     * @param RoleName 角色名称（如：系统管理员）
     * @return List<Map < String, Object>>
     * @auther TX
     * @date 2021-5-13
     */
    public List<Map<String, Object>> FindUserByRoleName(String RoleName) {
        String sql = "select scom.company_name ,aser.ROWNO,aser.login_name ,aser.employee_name ,sro.cname ,aser.mobile from afr_systemuser aser \n" +
                "left join SYSTEMTESTUSERS sy on sy.TESTID=aser.rowno\n" +
                "left  join afr_system_dept_user du on du.rowno=aser.rowno\n" +
                "left join system_user_role usle on usle.row_no=aser.rowno\n" +
                "left join system_role sro on sro.id=usle.role_id\n" +
                "left join vw_userinfo v on v.rowno=aser.rowno\n" +
                "left join afr_systemdept de on de.department_no=du.department_no\n" +
                "left join afr_systemcompany scom on scom.company_code=de.company_code\n" +
                "where sro.cname='" + RoleName + "'";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 根绝工单编号查询工单信息
     *
     * @param OrderNo 工单编号
     * @return MarketQuotasWorkOrder
     * @auther TX
     * @date 2021-5-13
     */
    public MarketQuotasWorkOrder QueryMarketQuotasWorkOrderByOrderNo(String OrderNo) {
        String sql = "SELECT * FROM BPMS_MARKET_QUOTASWORKORDER WHERE ORDER_NO = ?";
        MarketQuotasWorkOrder marketQuotasWorkOrder = (MarketQuotasWorkOrder) getSession().createSQLQuery(sql).addEntity(MarketQuotasWorkOrder.class)
                .setString(0, OrderNo).uniqueResult();
        return marketQuotasWorkOrder;
    }

    public List<MarketQuotaIniallzation> QueryQuotaIniallzationByUserRowNo(String RowNo, String VersionNumber) {
        String sql = "SELECT * from BPMS_MARKET_QUOTAINIALLZATION WHERE ATTRIBUTIONCOMPANY_NAME = (SELECT COMPANY_NAME FROM VW_USERINFO WHERE ROWNO = ? ) AND VERSION_NUMBER = ?";
        return getSession().createSQLQuery(sql).addEntity(MarketQuotaIniallzation.class).setString(0, RowNo).setString(1, VersionNumber).list();
    }

    /**
     * 根据工单编号查询工单下所有明细信息
     *
     * @param ORDER_NO 代码编号
     * @return List<MarketQuotasWorkDet>
     * @auther TX
     * @date 2021-5-17
     */
    public List<MarketQuotasWorkDet> QueryMarketQuotasWorkDetListByOrderNo(String ORDER_NO) {
        String sql = "SELECT * FROM BPMS_MARKET_QUOTASWORKDET WHERE ORDER_NO = ?";
        return getSession().createSQLQuery(sql).addEntity(MarketQuotasWorkDet.class).setString(0, ORDER_NO).list();

    }

    /**
     * 根绝公司名称查询该部门的分公司
     *
     * @param NAME 公司名称
     * @return List<Map < String, Object>>
     * @auther TX
     * @date 2021-5-13
     */
    public List<Map<String, Object>> QueryCountyName(String NAME) {
        String sql = "SELECT DISTINCT COUNTY_NAME,COUNTY_NO from VW_USERINFO WHERE COMPANY_NAME='" + NAME + "' ORDER BY COUNTY_NO";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 营销活动配额工单 分页查询
     *
     * @param page
     * @param orderNo     工单ID
     * @param creatorName 创建人
     * @return
     */
    public LayuiPage queryQuotasWorkOrder(LayuiPage page, String orderNo, String creatorName, String orderState, String companyName, String orderName) {
        String sql = "SELECT m.ORDER_NO,m.ORDER_NAME,m.TOTAL_AMOUNT,m.COMPANY_NAME,c.CREATOR_NAME,c.CREATOR_DATE,c.ORDER_STATE,c.PUSH_STATE FROM BPMS_MARKET_QUOTASWORKORDER m LEFT JOIN BAMS_CUST_REPAIRORDER c on m.ORDER_NO=c.ORDER_NO where 1=1";

        if (orderNo != null && !"".equals(orderNo)) {
            sql += " and c.ORDER_NO='" + orderNo + "'";
        }

        if (creatorName != null && !"".equals(creatorName)) {
            sql += " and c.CREATOR_NAME like '%" + creatorName + "%'";
        }

        if (orderState != null && !"".equals(orderState)) {
            sql += " and c.ORDER_STATE ='" + orderState + "'";
        }

        if (companyName != null && !"".equals(companyName)) {
            sql += " and m.COMPANY_NAME ='" + companyName + "'";
        }

        if (orderName != null && !"".equals(orderName)) {
            sql += " and m.ORDER_NAME ='" + orderName + "'";
        }

        //System.out.println("营销活动配额工单 分页查询sql=="+sql);

        page.setCount(getCount("select count(0) from ( " + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 根据工单ID查询 MarketQuotasWorkOrder 表数据
     *
     * @param orderNo
     * @return
     */
    public Map<String, Object> queryQuotasWorkOrderByOrderNo(String orderNo) {
        StringBuffer sbSql = new StringBuffer();
        sbSql.append("select * from BPMS_MARKET_QUOTASWORKORDER where order_No=?");
        List<Map<String, Object>> lis = this.getSession().createSQLQuery(sbSql.toString()).setParameter(0, orderNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return lis.size() > 0 ? (Map<String, Object>) lis.get(0) : null;
    }

    /**
     * 根据工单ID查询 RepairOrder 表数据
     *
     * @param orderNo
     * @return
     */
    public Map<String, Object> queryRepairOrderByOrderNo(String orderNo) {
        StringBuffer sbSql = new StringBuffer();
        sbSql.append("select * from BAMS_CUST_REPAIRORDER where order_No=?");
        List<Map<String, Object>> lis = this.getSession().createSQLQuery(sbSql.toString()).setParameter(0, orderNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return lis.size() > 0 ? (Map<String, Object>) lis.get(0) : null;
    }


    /**
     * 根据工单ID查询 RepairOrder 表数据
     *
     * @return
     */
    public List<Map<String, String>> findByMarketStockSystemGroup(String account_no, String unit_id) {
        //1	阿坝	理县分公司	宋华丽	ucbb17	**********	理县薛成中小学	B2	0.4	************** 	11500	4600	4600	********	********
        StringBuffer sbSql = new StringBuffer();
        sbSql.append("select * from BPMS_MARKET_STOCKSYSTEMGROUP where SERIAL_NUMBER='0' and ACCOUNT_NO=? and UNIT_ID=? ");
        List<Map<String, String>> lis = this.getSession().createSQLQuery(sbSql.toString()).setString(0, account_no).setString(1, unit_id).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return lis;
    }

    /**
     * 添加营销合同工单
     *
     * @param order
     * @return
     */
    public MarketActivityWorkOrder addMarketActivityWorkOrder(MarketActivityWorkOrder order) {
        try {
            Session session = this.getSession();
            session.save(order);
            return order;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 修改营销合同工单
     *
     * @param order
     * @return
     */
    public MarketActivityWorkOrder UpdateMarketActivityWorkOrder(MarketActivityWorkOrder order) {
        try {
            if (order != null) {
                Session session = this.getSession();
                session.update(order);
            }
            return order;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public MarketStockSystemGroup UpdateMarketStockSystemGroup(MarketStockSystemGroup marketStockSystemGroup) {
        try {
            if (marketStockSystemGroup != null) {
                Session session = this.getSession();
                session.update(marketStockSystemGroup);
            }
            return marketStockSystemGroup;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public LayuiPage findMarketActivityWorkOrderByPage(LayuiPage page, String ActiveType, String unit_id, String order_no, String rowno) {
        String sql = "SELECT * FROM BPMS_MARKET_ACTIVITYWORKORDER WHERE ORDER_CATEGORY='" + ActiveType + "' and USER_ROWNO='" + rowno + "'";
        if (unit_id != null && !"".equals(unit_id)) {
            sql += " and UNIT_ID='" + unit_id + "'";
        }
        if (order_no != null && !"".equals(order_no)) {
            sql += " and ORDER_NO like '%" + order_no + "%'";
        }
        page.setCount(getCount("select count(0) from ( " + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 根据账户查询存量统存集团客户数据
     *
     * @return
     */
    public MarketStockSystemGroup findMarketStockBySerialNumber(String unified_payment_account) {
        //System.out.println("账户为===="+unified_payment_account);
        String sql = "select * from BPMS_MARKET_STOCKSYSTEMGROUP WHERE UNIFIED_PAYMENT_ACCOUNT= ?";
        MarketStockSystemGroup marketQuotasWorkOrder = (MarketStockSystemGroup) getSession().createSQLQuery(sql).addEntity(MarketStockSystemGroup.class)
                .setString(0, unified_payment_account).uniqueResult();
        return marketQuotasWorkOrder;
    }

    /**
     * 更改存量统存集团客户数据为已使用
     */
    public Integer upDataMarketStockSystemGroup(String unified_payment_account) {
        String sql = "update  BPMS_MARKET_STOCKSYSTEMGROUP t set t.Serial_number='1' where t.UNIFIED_PAYMENT_ACCOUNT=? ";
        int count = this.getSession().createSQLQuery(sql).setParameter(0, unified_payment_account).executeUpdate();
        return count;
    }


    /**
     * 更改存量统存集团客户数据为未使用
     */
    public Integer upDataMarketSystemGroup(String unified_payment_account) {
        String sql = "update  BPMS_MARKET_STOCKSYSTEMGROUP t set t.Serial_number='0' where t.UNIFIED_PAYMENT_ACCOUNT=? ";
        int count = this.getSession().createSQLQuery(sql).setParameter(0, unified_payment_account).executeUpdate();
        return count;
    }

    /**
     * 添加环节表数据
     *
     * @param link
     * @return
     */
    public MarketWorkOrderLink addMarketWorkOrderLink(MarketWorkOrderLink link) {
        try {
            Session session = this.getSession();
            session.save(link);
            return link;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据环节id查询环节信息
     *
     * @return
     */
    public MarketWorkOrderLink getMarketWorkOrderLink(String id) {
        String sql = "select * from BPMS_MARKET_WORKORDERLINK where id=?";
        MarketWorkOrderLink marketWorkOrderLink = (MarketWorkOrderLink) getSession().createSQLQuery(sql).addEntity(MarketWorkOrderLink.class)
                .setString(0, id).uniqueResult();
        return marketWorkOrderLink;
    }

    /**
     * 根据环节关联的营销活动id查询
     *
     * @param id
     * @return
     */
    public MarketWorkOrderLink getMarketLinkOrderLink(String id) {
        String sql = "select * from BPMS_MARKET_WORKORDERLINK where MARKET_WORK_ID=?";
        MarketWorkOrderLink marketWorkOrderLink = (MarketWorkOrderLink) getSession().createSQLQuery(sql).addEntity(MarketWorkOrderLink.class)
                .setString(0, id).uniqueResult();
        return marketWorkOrderLink;
    }


    public List<MarketWorkOrderLink> getMarketLinkById(String id) {
        String sql = "select * from BPMS_MARKET_WORKORDERLINK where MARKET_WORK_ID=?";
        List<MarketWorkOrderLink> list = getSession().createSQLQuery(sql).addEntity(MarketWorkOrderLink.class).setString(0, id).list();
        return list;
    }

    /**
     * 更改环节
     */
    public MarketWorkOrderLink upDataMarketWorkOrderLink(MarketWorkOrderLink marketWorkOrderLink) {
//        String sql ="update  BPMS_MARKET_WORKORDERLINK t set t.LINK_STATE='1' where t.ID=? ";
//        int count = this.getSession().createSQLQuery(sql).setParameter(0, id).executeUpdate();
//        return count;
        try {
            if (marketWorkOrderLink != null) {
                Session session = this.getSession();
                session.update(marketWorkOrderLink);
            }
            return marketWorkOrderLink;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据id查询营销活动工单
     *
     * @param id
     * @return
     */
    public MarketActivityWorkOrder getMarketActivityWorkOrder(String id) {
        String sql = "SELECT * FROM BPMS_MARKET_ACTIVITYWORKORDER where id=?";
        MarketActivityWorkOrder marketActivityWorkOrder = (MarketActivityWorkOrder) getSession().createSQLQuery(sql).addEntity(MarketActivityWorkOrder.class)
                .setString(0, id).uniqueResult();
        return marketActivityWorkOrder;
    }

    /**
     * 根据营销合同id查询关连的环节信息
     *
     * @param id
     * @return
     */
    public List<Map<String, String>> findMarketActiviLinktyById(String id) {
        String sql = "select * from BPMS_MARKET_WORKORDERLINK where MARKET_WORK_ID=?";
        List<Map<String, String>> list = getSession().createSQLQuery(sql).setString(0, id).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return list;
    }

    /**
     * 根据集团280编号查询集团效益评估统计信息
     *
     * @return
     */
    public MarketGroupStatistics getMarketGroupStatistics(String unit_no) {
        String sql = "SELECT * FROM BPMS_MARKET_GROUPSTATISTICS where UNIT_ID=?";
        MarketGroupStatistics marketGroupStatistics = (MarketGroupStatistics) getSession().createSQLQuery(sql).addEntity(MarketGroupStatistics.class)
                .setString(0, unit_no).uniqueResult();
        return marketGroupStatistics;
    }


    /**
     * s4035IntChk接口文档.v2
     * service_no 服务号码
     * login_no 工号
     * active_code 营销活动代码
     * phone 电话号码
     */
    public Result IntChkQueryFloat(String login_no, String active_code, String phone) {
        logger.info("运行号码校验");
        Result r = new Result();
        JSONObject bodyContent = new JSONObject();
        bodyContent.put("MASTER_SERV_ID", "1001");//1001:普通号码 1002:宽带号码
        //bodyContent.put("SERVICE_NO", "13890938211");
        bodyContent.put("SERVICE_NO", phone);
        JSONObject commonContent = new JSONObject();
        JSONObject content = new JSONObject();
        content.put("PROVINCE_GROUP", "10008");//省代码
        content.put("LOGIN_NO", login_no);//工号
        // content.put("LOGIN_NO", "aagh38");//工号
        content.put("PHONE_NO", phone);//服务号码 19212801692
        content.put("MEANS_ID", active_code);//营销活动代码
        // content.put("MEANS_ID", "AZ540419");//营销活动代码
        //content.put("ACT_ID", "AZ54");//活动大类
        content.put("ACT_ID", active_code.substring(0, 4));//活动大类

        content.put("CHANNEL_TYPE", "54");//渠道类型
        content.put("TERMI_BUSI_TYPE", "");//终端比例类型
        commonContent.put("OPR_INFO", content);
        bodyContent.put("REQUEST_INFO", commonContent);
        //String paras = ESBReqMsgUtil.packMsgByRoute("10", "13890938211", bodyContent);
        String paras = ESBReqMsgUtil.packMsgByRoute("10", phone, bodyContent);
        if (isES) {
            logger.info("运行号码校验正式环境");
            try {
                //查询接口直接对接正式环境
                //正式服务器
                logger.info("营销活动号码校验请求参数===》"+paras);
                Result result = HttpURLConnectClientFactory.responseByCharset(ESB_URL_38 + "com_sitech_marketsvc_comp_inter_s4035_IP4035IntChkCoSvc_chk", paras, "UTF-8");
                logger.info("营销活动号码校验返回参数===》"+result);
                JSONObject json = JSONObject.fromObject(result.getData());
                //JSONObject dataOne = JSONObject.fromObject(json.getString("data"));
                //JSONObject json = JSONObject.fromObject("{\"ROOT\":{\"RETURN_MSG\":\"OK\",\"RETURN_CODE\":0,\"USER_MSG\":\"OK\",\"OUT_DATA\":{\"MEANS\":{\"MEAN\":{\"ACT_CLASS\":\"A\",\"PAY_TYPE\":\"\",\"RUN_CODE\":\"\",\"AREA_BIG\":\"N\",\"CONTRACT_FLAG\":\"\",\"INNET_TYPE\":\"1\",\"PAY_MAX_FEE\":\"********\",\"SMS_MSG\":\"【订购提醒】尊敬的客户，您好！您已成功订购：【meansName】。全心全意，只为您十分满意。【中国移动】\",\"HBD_INFO\":\"N\",\"MEANS_DESC\":\"【该活动包含基础业务包和促销业务包，均为当月生效，业务包内容以集团综合通信业务包协议约定为准】\",\"SMALL_PAY\":\"N\",\"IS_ACTLIB\":\"\",\"TERMINAL_ALLOWANCE\":\"0\",\"MEANS_NAME\":\"集客综合通信业务包C6\",\"IS_MEANS_MONEY\":\"\",\"MSG\":\"【订购提醒】尊敬的客户，您好！您已成功订购：【meansName】。全心全意，只为您十分满意。【中国移动】\",\"A10\":{\"MONTH_RETURN\":[{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"0\",\"EFF_TYPE\":\"3\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"1\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"Y177\",\"MONTH_TYPE\":\"0\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"gh/1\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"99\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"0.00\",\"STAGES_SPACE\":\"0\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"0\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"03030\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"1\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"\",\"FEE_NAME\":\"C-营销活动本金\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"3\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"2\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"101\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"3\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"102\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"1\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"4\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"103\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"2\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"5\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"104\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"3\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"6\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"105\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"4\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"7\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"106\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"5\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"}],\"MODIFY_FLAG\":\"\",\"ATTR_CTRL\":{\"IS_MODIFY\":{}}},\"IS_CREDIT_CARD\":\"0\",\"PAY_MIN_FEE\":\"1\",\"IS_CHECK_IMEI\":\"0\",\"A12\":{\"SUPERPOSTION_CHANGE\":\"\",\"SUPERPOSTION_SUB\":\"\",\"END_DATE\":\"\",\"START_DATE\":\"\",\"IS_BAG\":\"N\"},\"FUND_FLAG\":\"Y\",\"SPECIAL_NUM\":\"N\",\"A17\":{\"CHECK_FLAG\":\"N\",\"PASS_FLAG\":\"Y\",\"MEAN_ALL\":{\"LIMIT_INFO\":[{\"TAR_NAME\":\"办理号码\",\"TEAM_NO\":\"\",\"LIMIT_TAR\":\"0\",\"LIMIT_NAME\":\"ABC1类集团用户才能参加此活动\",\"SPECIAL_NOTE\":\"\",\"PASS_FLAG\":\"Y\",\"LIMIT_RULE\":\"\",\"LIMIT_VALUE\":\"A|B|C1\",\"LIMIT_SERIAL\":\"F99862851000\",\"LIMIT_TYPE\":\"08\",\"LIMIT_CODE\":\"6009\",\"CODE_NAME\":\"6-集团限制类\",\"NOTE\":\"ABC1类集团用户才能参加此活动\",\"LIMIT_LEVEL\":\"2\"},{\"TAR_NAME\":\"办理号码\",\"TEAM_NO\":\"\",\"LIMIT_TAR\":\"0\",\"LIMIT_NAME\":\"指定用户群才能参加活动\",\"SPECIAL_NOTE\":\"\",\"PASS_FLAG\":\"Y\",\"LIMIT_RULE\":\"\",\"LIMIT_VALUE\":\"99111970\",\"LIMIT_SERIAL\":\"F99862851001\",\"LIMIT_TYPE\":\"04\",\"LIMIT_CODE\":\"7001\",\"CODE_NAME\":\"7-目标用户群限制\",\"NOTE\":\"指定用户群才能参加活动\",\"LIMIT_LEVEL\":\"2\"},{\"TAR_NAME\":\"办理号码\",\"TEAM_NO\":\"\",\"LIMIT_TAR\":\"0\",\"LIMIT_NAME\":\"某些工号才能办理\",\"SPECIAL_NOTE\":\"\",\"PASS_FLAG\":\"Y\",\"LIMIT_RULE\":\"\",\"LIMIT_VALUE\":\"108723200\",\"LIMIT_SERIAL\":\"F99862851002\",\"LIMIT_TYPE\":\"08\",\"LIMIT_CODE\":\"7101\",\"CODE_NAME\":\"7-目标用户群限制\",\"NOTE\":\"某些工号才能办理\",\"LIMIT_LEVEL\":\"2\"}],\"PASS_FLAG\":\"Y\"}},\"MEANS_ID\":\"AZ540419\",\"PAY_MODE\":\"1\",\"INNET_VALUE\":\"0\",\"SERIAL_ID\":\"AZ540419121\",\"SEND_FLAG\":\"Y\",\"CONTRACT_TYPE\":\"\",\"CONTRACTCODE\":\"\",\"INNET_DATE\":\"\",\"BILL_TYPE\":\"\",\"FEE_ALLOWANCE\":\"0\",\"MEANS_MONEY\":\"\",\"FLOAD_BASE\":\"1\",\"RESOURCE_USE\":\"\"},\"MARKETPACKAGE\":{}},\"ACTION\":{\"ACTION_DATE\":\"2019-01-01至2030-01-01\",\"PROVIDE_TYPE\":\"\",\"MKT_DICTION\":\"\",\"SEND_START_TIME\":\"\",\"ACTION_NAME\":\"其他\",\"SEND_END_TIME\":\"\",\"ACTION_ID\":\"AZ54\",\"ACTION_DESC\":\"\"},\"PASS_FLAG\":\"Y\"},\"DETAIL_MSG\":\"OK\",\"RUN_IP\":\"**************\",\"PROMPT_MSG\":\"\"}}");
                JSONObject root_ = JSONObject.fromObject(json.getString("ROOT"));
                //logger.info("营销活动号码校验返回参数获取ROOT===》"+root_);
                JSONObject out_data = JSONObject.fromObject(root_.getString("OUT_DATA"));
                //System.out.println("out_data=="+out_data);
                //logger.info("营销活动号码校验返回参数获取PASS_FLAG===》"+out_data.getString("PASS_FLAG"));
                if (out_data.getString("PASS_FLAG").equals("Y")) {

                    JSONObject means = JSONObject.fromObject(out_data.getString("MEANS"));
                    JSONObject mean = JSONObject.fromObject(means.getString("MEAN"));
                    boolean a17s = mean.containsKey("A17");
                    List objects = new ArrayList<>();

                    if(a17s){
                        JSONObject a17 = JSONObject.fromObject(mean.getString("A17"));
                        JSONObject mean_all = JSONObject.fromObject(a17.getString("MEAN_ALL"));
                        JSONArray limit_info = mean_all.getJSONArray("LIMIT_INFO");

                        for (int i = 0; i < limit_info.size(); i++) {
                            JSONObject jsonObject = JSONObject.fromObject(limit_info.get(i));
                            String note = jsonObject.getString("NOTE");
                            objects.add(note);
                        }
                    }


                    //Object a101 = mean.get("A10");
                    JSONObject a10 = mean.getJSONObject("A10");
                    Object month_return1 = a10.get("MONTH_RETURN");
                    //JSONObject.fromObject(mean.getJSONObject("A10"));
                    //判断JSON是否是数组
                    if(month_return1 instanceof JSONArray){
                        logger.info("这是数组："+month_return1.toString());
                        //为数组的时候
                        //JSONObject a10 = JSONObject.fromObject(mean.getString("A10"));
                        JSONArray month_return = a10.getJSONArray("MONTH_RETURN");
                        JSONObject jsonObject1 = new JSONObject();
                        //System.out.println("month_return=="+month_return);
                        for (int i = 0; i < month_return.size(); i++) {
                            JSONObject jsonObject = JSONObject.fromObject(month_return.get(i));
                            if (jsonObject.getString("MONTH_TYPE").equals("0")) {
                                jsonObject1 = jsonObject;
                                break;
                            }
                        }
                        if (jsonObject1.get("PAY_MODE").equals("1")) {
                            //System.out.println("浮动缴费");
                            JSONObject data = new JSONObject();
                            data.put("PAY_MAX_FEE", jsonObject1.get("PAY_MAX_FEE"));
                            data.put("PAY_MIN_FEE", jsonObject1.get("PAY_MIN_FEE"));
                            data.put("FLOAD_BASE", jsonObject1.get("FLOAD_BASE"));
                            data.put("PHONE", phone);
                            //String [] y = new String [] {"1","2","3","4","5","6","7","8"};
                            data.put("AUTHORITY_LIST", objects);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("YES");
                            r.setData(data);
                            //PAY_MIN_FEE 最小值
                            //PAY_MAX_FEE 最大值
                            //FLOAD_BASE 该数的整数倍
                        } else {
                            //TOTAL_MONEY
                            JSONObject data = new JSONObject();
                            data.put("TOTAL_MONEY",jsonObject1.get("TOTAL_MONEY"));
                            data.put("PHONE", phone);
                            //data.put("TOTAL_MONEY", "1000");
                            //System.out.println("不是浮动缴费");
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("NO");
                            r.setData(data);
                        }
                    }else if(month_return1 instanceof JSONObject) {
                        logger.info("这是对象：" + month_return1.toString());
                        //不为数组
                        //JSONObject a10 = JSONObject.fromObject(mean.getString("A10"));
                        JSONObject jsonObject1 = a10.getJSONObject("MONTH_RETURN");
                        //JSONArray month_return = a10.getJSONArray("MONTH_RETURN");
                        if (jsonObject1.get("PAY_MODE").equals("1")) {
                            //System.out.println("浮动缴费");
                            JSONObject data = new JSONObject();
                            data.put("PAY_MAX_FEE", jsonObject1.get("PAY_MAX_FEE"));
                            data.put("PAY_MIN_FEE", jsonObject1.get("PAY_MIN_FEE"));
                            data.put("FLOAD_BASE", jsonObject1.get("FLOAD_BASE"));
                            data.put("PHONE", phone);
                            //String [] y = new String [] {"1","2","3","4","5","6","7","8"};
                            data.put("AUTHORITY_LIST", objects);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("YES");
                            r.setData(data);
                            //PAY_MIN_FEE 最小值
                            //PAY_MAX_FEE 最大值
                            //FLOAD_BASE 该数的整数倍
                        } else {
                            //TOTAL_MONEY
                            JSONObject data = new JSONObject();
                            data.put("TOTAL_MONEY",jsonObject1.get("TOTAL_MONEY"));
                            data.put("PHONE", phone);
                            //data.put("TOTAL_MONEY", "1000");
                            //System.out.println("不是浮动缴费");
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("NO");
                            r.setData(data);
                        }
                    }
                    //System.out.println("===>:" + jsonObject1);//已这个数据为准
                } else {
                    JSONObject mean_all = JSONObject.fromObject(out_data.getString("MEAN_ALL"));
                    JSONArray limit_info = mean_all.getJSONArray("LIMIT_INFO");
                    String message="";
                    for (int i = 0; i < limit_info.size(); i++) {
                        JSONObject jsonObject = JSONObject.fromObject(limit_info.get(i));
                        if(jsonObject.getString("PASS_FLAG").equals("N")){
                            String limit_name = jsonObject.getString("LIMIT_NAME");
                            message+=","+limit_name;
                        }
                    }
                    r.setCode(ResultCode.FAIL);
                    r.setMessage(message);
                    r.setData("号码审核失败");
                }
            } catch (Exception e) {
                r.setCode(ResultCode.INTERNAL_SERVER_ERROR);
                r.setMessage("NO");
                r.setData(e.getMessage());
            }
        } else {
            logger.info("运行号码校验模拟环境");
            try {
                //查询接口直接对接正式环境
                //String resultStr = CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_172 + "com_sitech_marketsvc_comp_inter_s4035_IP4035IntChkCoSvc_chk", paras);//s4035IntChk p4035intchk
                //Result result = HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
                //JSONObject json = JSONObject.fromObject(result.getData());
                //JSONObject jsonObject2 = JSONObject.fromObject("{\"code\":200,\"message\":\"SUCCESS\",\"data\":\"{\\\"ROOT\\\":{\\\"RETURN_MSG\\\":\\\"OK\\\",\\\"PROMPT_MSG\\\":\\\"\\\",\\\"OUT_DATA\\\":{\\\"MEAN_ALL\\\":{\\\"LIMIT_INFO\\\":[{\\\"SPECIAL_NOTE\\\":\\\"\\\",\\\"LIMIT_RULE\\\":\\\"\\\",\\\"TEAM_NO\\\":\\\"\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_VALUE\\\":\\\"********\\\",\\\"LIMIT_CODE\\\":\\\"7001\\\",\\\"CODE_NAME\\\":\\\"7-目标用户群限制\\\",\\\"LIMIT_NAME\\\":\\\"指定用户群才能参加活动\\\",\\\"NOTE\\\":\\\"指定用户群才能参加活动\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"PASS_FLAG\\\":\\\"N\\\",\\\"LIMIT_TYPE\\\":\\\"04\\\",\\\"LIMIT_SERIAL\\\":\\\"F99862850100\\\"},{\\\"SPECIAL_NOTE\\\":\\\"\\\",\\\"LIMIT_RULE\\\":\\\"\\\",\\\"TEAM_NO\\\":\\\"\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_VALUE\\\":\\\"A|B|C1\\\",\\\"LIMIT_CODE\\\":\\\"6009\\\",\\\"CODE_NAME\\\":\\\"6-集团限制类\\\",\\\"LIMIT_NAME\\\":\\\"ABC1类集团用户才能参加此活动\\\",\\\"NOTE\\\":\\\"ABC1类集团用户才能参加此活动\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"PASS_FLAG\\\":\\\"N\\\",\\\"LIMIT_TYPE\\\":\\\"08\\\",\\\"LIMIT_SERIAL\\\":\\\"F99862850101\\\"},{\\\"SPECIAL_NOTE\\\":\\\"\\\",\\\"LIMIT_RULE\\\":\\\"\\\",\\\"TEAM_NO\\\":\\\"\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_VALUE\\\":\\\"108723200\\\",\\\"LIMIT_CODE\\\":\\\"7101\\\",\\\"CODE_NAME\\\":\\\"7-目标用户群限制\\\",\\\"LIMIT_NAME\\\":\\\"某些工号才能办理\\\",\\\"NOTE\\\":\\\"某些工号才能办理\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"PASS_FLAG\\\":\\\"N\\\",\\\"LIMIT_TYPE\\\":\\\"08\\\",\\\"LIMIT_SERIAL\\\":\\\"F99862850102\\\"}],\\\"PASS_FLAG\\\":\\\"N\\\"},\\\"PASS_FLAG\\\":\\\"N\\\"},\\\"RETURN_CODE\\\":0,\\\"USER_MSG\\\":\\\"OK\\\",\\\"DETAIL_MSG\\\":\\\"OK\\\",\\\"RUN_IP\\\":\\\"**************\\\"}}\"}");
                //JSONObject data1 = JSONObject.fromObject(jsonObject2.getJSONObject("data"));
                //JSONObject json1 = JSONObject.fromObject("{\"ROOT\":{\"RETURN_MSG\":\"OK\",\"RETURN_CODE\":0,\"USER_MSG\":\"OK\",\"OUT_DATA\":{\"MEANS\":{\"MEAN\":{\"ACT_CLASS\":\"A\",\"PAY_TYPE\":\"\",\"RUN_CODE\":\"\",\"AREA_BIG\":\"N\",\"CONTRACT_FLAG\":\"\",\"INNET_TYPE\":\"1\",\"PAY_MAX_FEE\":\"********\",\"SMS_MSG\":\"【订购提醒】尊敬的客户，您好！您已成功订购：【meansName】。全心全意，只为您十分满意。【中国移动】\",\"HBD_INFO\":\"N\",\"MEANS_DESC\":\"【该活动包含基础业务包和促销业务包，均为当月生效，业务包内容以集团综合通信业务包协议约定为准】\",\"SMALL_PAY\":\"N\",\"IS_ACTLIB\":\"\",\"TERMINAL_ALLOWANCE\":\"0\",\"MEANS_NAME\":\"集客综合通信业务包C6\",\"IS_MEANS_MONEY\":\"\",\"MSG\":\"【订购提醒】尊敬的客户，您好！您已成功订购：【meansName】。全心全意，只为您十分满意。【中国移动】\",\"A10\":{\"MONTH_RETURN\":[{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"0\",\"EFF_TYPE\":\"3\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"1\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"Y177\",\"MONTH_TYPE\":\"0\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"gh/1\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"99\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"0.00\",\"STAGES_SPACE\":\"0\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"0\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"03030\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"1\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"\",\"FEE_NAME\":\"C-营销活动本金\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"3\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"2\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"101\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"3\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"102\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"1\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"4\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"103\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"2\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"5\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"104\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"3\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"6\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"105\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"4\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"},{\"CONTAIN_TWO_RULE\":\"0\",\"COST_TWO_LIST\":\"\",\"FAV_RATE\":\"100\",\"EFF_TYPE\":\"2\",\"STAGES_MONEY\":\"0\",\"COUNSTR\":\"\",\"PAY_MAX_FEE\":\"********\",\"FAILURE_MONTH\":\"\",\"FLOAT_BITE\":\"5\",\"STAGES_MONEY_SHOW\":\"\",\"ACCOUNT_WAY\":\"\",\"INDEX\":\"7\",\"EFF_DATE\":\"\",\"FEE_CODE\":\"\",\"MONTH_TYPE\":\"1\",\"GIVE_FEE_TYPE\":\"0\",\"GIVE_FEE_CODE\":\"Y180\",\"COST_TYPE\":\"\",\"COST_ONE_LIST\":\"\",\"STAGES_CYCLE\":\"\",\"CONSUMER_END_DATE\":\"\",\"PAY_MONEY\":\"\",\"STAGES_SPACE\":\"\",\"SPACE_MONTH\":\"1\",\"FEE_TYPE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"FLOAD_BASE\":\"1\",\"STAGES_END_MONTH\":\"\",\"PAY_RULE\":\"\",\"OFFSET_TYPE\":\"0\",\"GIVING_WAY\":\"1\",\"ACCOUNT_TYPE\":\"\",\"DETAIL_CODE\":\"\",\"PAYCOST_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONTH\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"COST_DICT\":\"\",\"ELEMENT_BATCH_NO\":\"106\",\"IS_COMPLEX\":\"Y\",\"STAGES_MONTH_NAME\":\"0\",\"ADD_FEE_FLAG\":\"N\",\"CONSUMER_MIX\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"GIVE_MONEY\":\"\",\"PAY_MIN_FEE\":\"1\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"LIMIT_C4\":\"\",\"LIMIT_C3\":\"\",\"LIMIT_C6\":\"\",\"LIMIT_C5\":\"\",\"TO_ACCOUNT_MAX\":\"\",\"TOTAL_MONEY\":\"0\",\"BTN_A1065\":\"\",\"CONSUMER_MAX\":\"\",\"LIMIT_C8\":\"\",\"BTN_A1066\":\"\",\"LIMIT_C7\":\"\",\"PAY_MODE\":\"1\",\"OFFSET_MONTH\":\"5\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"FEE_NAME\":\"\",\"CONSUMER_BATE\":\"\",\"ARRIVE_FLAG\":\"0\"}],\"MODIFY_FLAG\":\"\",\"ATTR_CTRL\":{\"IS_MODIFY\":{}}},\"IS_CREDIT_CARD\":\"0\",\"PAY_MIN_FEE\":\"1\",\"IS_CHECK_IMEI\":\"0\",\"A12\":{\"SUPERPOSTION_CHANGE\":\"\",\"SUPERPOSTION_SUB\":\"\",\"END_DATE\":\"\",\"START_DATE\":\"\",\"IS_BAG\":\"N\"},\"FUND_FLAG\":\"Y\",\"SPECIAL_NUM\":\"N\",\"A17\":{\"CHECK_FLAG\":\"N\",\"PASS_FLAG\":\"Y\",\"MEAN_ALL\":{\"LIMIT_INFO\":[{\"TAR_NAME\":\"办理号码\",\"TEAM_NO\":\"\",\"LIMIT_TAR\":\"0\",\"LIMIT_NAME\":\"ABC1类集团用户才能参加此活动\",\"SPECIAL_NOTE\":\"\",\"PASS_FLAG\":\"Y\",\"LIMIT_RULE\":\"\",\"LIMIT_VALUE\":\"A|B|C1\",\"LIMIT_SERIAL\":\"F99862851000\",\"LIMIT_TYPE\":\"08\",\"LIMIT_CODE\":\"6009\",\"CODE_NAME\":\"6-集团限制类\",\"NOTE\":\"ABC1类集团用户才能参加此活动\",\"LIMIT_LEVEL\":\"2\"},{\"TAR_NAME\":\"办理号码\",\"TEAM_NO\":\"\",\"LIMIT_TAR\":\"0\",\"LIMIT_NAME\":\"指定用户群才能参加活动\",\"SPECIAL_NOTE\":\"\",\"PASS_FLAG\":\"Y\",\"LIMIT_RULE\":\"\",\"LIMIT_VALUE\":\"99111970\",\"LIMIT_SERIAL\":\"F99862851001\",\"LIMIT_TYPE\":\"04\",\"LIMIT_CODE\":\"7001\",\"CODE_NAME\":\"7-目标用户群限制\",\"NOTE\":\"指定用户群才能参加活动\",\"LIMIT_LEVEL\":\"2\"},{\"TAR_NAME\":\"办理号码\",\"TEAM_NO\":\"\",\"LIMIT_TAR\":\"0\",\"LIMIT_NAME\":\"某些工号才能办理\",\"SPECIAL_NOTE\":\"\",\"PASS_FLAG\":\"Y\",\"LIMIT_RULE\":\"\",\"LIMIT_VALUE\":\"108723200\",\"LIMIT_SERIAL\":\"F99862851002\",\"LIMIT_TYPE\":\"08\",\"LIMIT_CODE\":\"7101\",\"CODE_NAME\":\"7-目标用户群限制\",\"NOTE\":\"某些工号才能办理\",\"LIMIT_LEVEL\":\"2\"}],\"PASS_FLAG\":\"Y\"}},\"MEANS_ID\":\"AZ540419\",\"PAY_MODE\":\"1\",\"INNET_VALUE\":\"0\",\"SERIAL_ID\":\"AZ540419121\",\"SEND_FLAG\":\"Y\",\"CONTRACT_TYPE\":\"\",\"CONTRACTCODE\":\"\",\"INNET_DATE\":\"\",\"BILL_TYPE\":\"\",\"FEE_ALLOWANCE\":\"0\",\"MEANS_MONEY\":\"\",\"FLOAD_BASE\":\"1\",\"RESOURCE_USE\":\"\"},\"MARKETPACKAGE\":{}},\"ACTION\":{\"ACTION_DATE\":\"2019-01-01至2030-01-01\",\"PROVIDE_TYPE\":\"\",\"MKT_DICTION\":\"\",\"SEND_START_TIME\":\"\",\"ACTION_NAME\":\"其他\",\"SEND_END_TIME\":\"\",\"ACTION_ID\":\"AZ54\",\"ACTION_DESC\":\"\"},\"PASS_FLAG\":\"Y\"},\"DETAIL_MSG\":\"OK\",\"RUN_IP\":\"**************\",\"PROMPT_MSG\":\"\"}}");
                //JSONObject json1 = JSONObject.fromObject("{\"ROOT\":{\"RETURN_MSG\":\"OK\",\"PROMPT_MSG\":\"\",\"OUT_DATA\":{\"MEAN_ALL\":{\"LIMIT_INFO\":[{\"SPECIAL_NOTE\":\"\",\"LIMIT_RULE\":\"\",\"TEAM_NO\":\"\",\"LIMIT_LEVEL\":\"2\",\"LIMIT_VALUE\":\"108723091\",\"LIMIT_CODE\":\"7101\",\"CODE_NAME\":\"7-目标用户群限制\",\"LIMIT_NAME\":\"某些工号才能办理\",\"NOTE\":\"108723091集合中的工号才能办理\",\"LIMIT_TAR\":\"0\",\"TAR_NAME\":\"办理号码\",\"PASS_FLAG\":\"Y\",\"LIMIT_TYPE\":\"08\",\"LIMIT_SERIAL\":\"F9947380011602\"},{\"SPECIAL_NOTE\":\"\",\"LIMIT_RULE\":\"\",\"TEAM_NO\":\"\",\"LIMIT_LEVEL\":\"2\",\"LIMIT_VALUE\":\"N\",\"LIMIT_CODE\":\"6005\",\"CODE_NAME\":\"6-集团限制类\",\"LIMIT_NAME\":\"ABC类集团用户才能参加此活动\",\"NOTE\":\"只有ABC类集团用户才能参加该营销活动\",\"LIMIT_TAR\":\"0\",\"TAR_NAME\":\"办理号码\",\"PASS_FLAG\":\"Y\",\"LIMIT_TYPE\":\"08\",\"LIMIT_SERIAL\":\"F9947380011603\"},{\"SPECIAL_NOTE\":\"\",\"LIMIT_RULE\":\"\",\"TEAM_NO\":\"\",\"LIMIT_LEVEL\":\"2\",\"LIMIT_VALUE\":\"^AZ100016^1^0\",\"LIMIT_CODE\":\"4028\",\"CODE_NAME\":\"4-与营销活动关系\",\"LIMIT_NAME\":\"当月参加了某类或某些或任一营销活动达到一定次数后不能再参加\",\"NOTE\":\"当月仅允许办理1次\",\"LIMIT_TAR\":\"0\",\"TAR_NAME\":\"办理号码\",\"PASS_FLAG\":\"N\",\"LIMIT_TYPE\":\"02\",\"LIMIT_SERIAL\":\"F9947380011604\"}],\"PASS_FLAG\":\"N\"},\"PASS_FLAG\":\"N\"},\"RETURN_CODE\":0,\"USER_MSG\":\"OK\",\"DETAIL_MSG\":\"OK\",\"RUN_IP\":\"*************\"}}");
                //System.out.println("数据为=="+json1);
                JSONObject json1 = JSONObject.fromObject("{\"ROOT\":{\"RETURN_MSG\":\"OK\",\"PROMPT_MSG\":\"\",\"OUT_DATA\":{\"MEANS\":{\"MARKETPACKAGE\":{},\"MEAN\":{\"CONTRACTCODE\":\"\",\"SMS_MSG\":\"【订购提醒】尊敬的客户，您好！您已成功订购：【meansName】。全心全意，只为您十分满意。【中国移动】\",\"IS_CREDIT_CARD\":\"0\",\"MEANS_DESC\":\"【该活动包含基础业务包和促销业务包，均为当月生效，业务包内容以集团综合通信业务包协议约定为准】\",\"SEND_FLAG\":\"Y\",\"IS_ACTLIB\":\"\",\"ACT_CLASS\":\"A\",\"FEE_ALLOWANCE\":\"0\",\"TERMINAL_ALLOWANCE\":\"0\",\"CONTRACT_TYPE\":\"\",\"PAY_MAX_FEE\":\"********\",\"MSG\":\"【订购提醒】尊敬的客户，您好！您已成功订购：【meansName】。全心全意，只为您十分满意。【中国移动】\",\"FUND_FLAG\":\"Y\",\"INNET_DATE\":\"\",\"INNET_VALUE\":\"0\",\"MEANS_ID\":\"AZ540410\",\"IS_CHECK_IMEI\":\"0\",\"SERIAL_ID\":\"AZ540410119\",\"SPECIAL_NUM\":\"N\",\"RUN_CODE\":\"\",\"BILL_TYPE\":\"\",\"MEANS_NAME\":\"集客综合通信业务包A1\",\"AREA_BIG\":\"N\",\"SMALL_PAY\":\"N\",\"CONTRACT_FLAG\":\"\",\"FLOAD_BASE\":\"1\",\"PAY_MODE\":\"1\",\"PAY_TYPE\":\"\",\"INNET_TYPE\":\"1\",\"RESOURCE_USE\":\"\",\"A10\":{\"ATTR_CTRL\":{\"IS_MODIFY\":{}},\"MONTH_RETURN\":[{\"COST_DICT\":\"\",\"STAGES_CYCLE\":\"99\",\"COST_TYPE\":\"gh/1\",\"FEE_TYPE\":\"0\",\"TOTAL_MONEY\":\"0.00\",\"STAGES_END_MONTH\":\"\",\"FAILURE_MONTH\":\"\",\"STAGES_SPACE\":\"0\",\"INDEX\":\"1\",\"COST_TWO_LIST\":\"\",\"STAGES_MONTH\":\"1\",\"EFF_DATE\":\"\",\"GIVE_FEE_NAME\":\"\",\"CONSUMER_END_DATE\":\"\",\"GIVE_FEE_TYPE\":\"0\",\"PAY_MODE\":\"1\",\"COST_ONE_LIST\":\"\",\"ARRIVE_FLAG\":\"0\",\"PAY_MONEY\":\"0.00\",\"CONTAIN_TWO_RULE\":\"0\",\"SPACE_MONTH\":\"1\",\"FAV_RATE\":\"0\",\"IS_COMPLEX\":\"Y\",\"TO_ACCOUNT_MAX\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONEY_SHOW\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"ACCOUNT_TYPE\":\"\",\"COUNSTR\":\"\",\"ADD_FEE_FLAG\":\"N\",\"STAGES_MONEY\":\"0\",\"PAY_MAX_FEE\":\"********\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_MONEY\":\"\",\"CONSUMER_MAX\":\"\",\"ACCOUNT_WAY\":\"\",\"ELEMENT_BATCH_NO\":\"1\",\"CONSUMER_BATE\":\"\",\"STAGES_MONTH_NAME\":\"0\",\"PAY_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"FEE_CODE\":\"Y177\",\"LIMIT_C8\":\"\",\"GIVE_FEE_CODE\":\"Y180\",\"LIMIT_C7\":\"\",\"MONTH_TYPE\":\"0\",\"OFFSET_TYPE\":\"0\",\"PAYCOST_RULE\":\"\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"OFFSET_MONTH\":\"\",\"GIVING_WAY\":\"1\",\"LIMIT_C6\":\"\",\"CONSUMER_MIX\":\"\",\"LIMIT_C5\":\"\",\"LIMIT_C4\":\"\",\"EFF_TYPE\":\"3\",\"FLOAD_BASE\":\"1\",\"LIMIT_C3\":\"\",\"FEE_NAME\":\"C-营销活动本金\",\"FLOAT_BITE\":\"\",\"TO_ACCOUNT_PRO\":\"\",\"DETAIL_CODE\":\"03030\",\"PAY_MIN_FEE\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"BTN_A1066\":\"\",\"FAILURE_DATE\":\"\",\"BTN_A1065\":\"\"},{\"COST_DICT\":\"\",\"STAGES_CYCLE\":\"\",\"COST_TYPE\":\"\",\"FEE_TYPE\":\"\",\"TOTAL_MONEY\":\"0\",\"STAGES_END_MONTH\":\"\",\"FAILURE_MONTH\":\"\",\"STAGES_SPACE\":\"\",\"INDEX\":\"2\",\"COST_TWO_LIST\":\"\",\"STAGES_MONTH\":\"1\",\"EFF_DATE\":\"\",\"GIVE_FEE_NAME\":\"h-存话费送预存\",\"CONSUMER_END_DATE\":\"\",\"GIVE_FEE_TYPE\":\"0\",\"PAY_MODE\":\"1\",\"COST_ONE_LIST\":\"\",\"ARRIVE_FLAG\":\"0\",\"PAY_MONEY\":\"\",\"CONTAIN_TWO_RULE\":\"0\",\"SPACE_MONTH\":\"1\",\"FAV_RATE\":\"100\",\"IS_COMPLEX\":\"Y\",\"TO_ACCOUNT_MAX\":\"\",\"CONSUMER_TO\":\"\",\"STAGES_MONEY_SHOW\":\"\",\"GIVE_DETAIL_CODE\":\"03300\",\"ACCOUNT_TYPE\":\"\",\"COUNSTR\":\"\",\"ADD_FEE_FLAG\":\"N\",\"STAGES_MONEY\":\"\",\"PAY_MAX_FEE\":\"********\",\"TO_ACCOUNT_MIX\":\"\",\"GIVE_MONEY\":\"\",\"CONSUMER_MAX\":\"\",\"ACCOUNT_WAY\":\"\",\"ELEMENT_BATCH_NO\":\"101\",\"CONSUMER_BATE\":\"\",\"STAGES_MONTH_NAME\":\"0\",\"PAY_RULE\":\"\",\"CONSUMER_STR_DATE\":\"\",\"FEE_CODE\":\"\",\"LIMIT_C8\":\"\",\"GIVE_FEE_CODE\":\"Y180\",\"LIMIT_C7\":\"\",\"MONTH_TYPE\":\"1\",\"OFFSET_TYPE\":\"0\",\"PAYCOST_RULE\":\"\",\"LIMIT_C2\":\"\",\"LIMIT_C1\":\"\",\"OFFSET_MONTH\":\"\",\"GIVING_WAY\":\"1\",\"LIMIT_C6\":\"\",\"CONSUMER_MIX\":\"\",\"LIMIT_C5\":\"\",\"LIMIT_C4\":\"\",\"EFF_TYPE\":\"3\",\"FLOAD_BASE\":\"1\",\"LIMIT_C3\":\"\",\"FEE_NAME\":\"\",\"FLOAT_BITE\":\"10\",\"TO_ACCOUNT_PRO\":\"\",\"DETAIL_CODE\":\"\",\"PAY_MIN_FEE\":\"1\",\"CONTAIN_ONE_RULE\":\"\",\"BTN_A1066\":\"\",\"FAILURE_DATE\":\"2099-12-31\",\"BTN_A1065\":\"\"}],\"MODIFY_FLAG\":\"\"},\"A12\":{\"SUPERPOSTION_SUB\":\"\",\"START_DATE\":\"\",\"SUPERPOSTION_CHANGE\":\"\",\"END_DATE\":\"\",\"IS_BAG\":\"N\"},\"PAY_MIN_FEE\":\"1\",\"HBD_INFO\":\"N\",\"A17\":{\"MEAN_ALL\":{\"LIMIT_INFO\":[{\"SPECIAL_NOTE\":\"\",\"LIMIT_RULE\":\"\",\"TEAM_NO\":\"\",\"LIMIT_LEVEL\":\"2\",\"LIMIT_VALUE\":\"********\",\"LIMIT_CODE\":\"7001\",\"CODE_NAME\":\"7-目标用户群限制\",\"LIMIT_NAME\":\"指定用户群才能参加活动\",\"NOTE\":\"指定用户群才能参加活动\",\"LIMIT_TAR\":\"0\",\"TAR_NAME\":\"办理号码\",\"PASS_FLAG\":\"Y\",\"LIMIT_TYPE\":\"04\",\"LIMIT_SERIAL\":\"F99862850100\"},{\"SPECIAL_NOTE\":\"\",\"LIMIT_RULE\":\"\",\"TEAM_NO\":\"\",\"LIMIT_LEVEL\":\"2\",\"LIMIT_VALUE\":\"A|B|C1\",\"LIMIT_CODE\":\"6009\",\"CODE_NAME\":\"6-集团限制类\",\"LIMIT_NAME\":\"ABC1类集团用户才能参加此活动\",\"NOTE\":\"ABC1类集团用户才能参加此活动\",\"LIMIT_TAR\":\"0\",\"TAR_NAME\":\"办理号码\",\"PASS_FLAG\":\"Y\",\"LIMIT_TYPE\":\"08\",\"LIMIT_SERIAL\":\"F99862850101\"},{\"SPECIAL_NOTE\":\"\",\"LIMIT_RULE\":\"\",\"TEAM_NO\":\"\",\"LIMIT_LEVEL\":\"2\",\"LIMIT_VALUE\":\"108723200\",\"LIMIT_CODE\":\"7101\",\"CODE_NAME\":\"7-目标用户群限制\",\"LIMIT_NAME\":\"某些工号才能办理\",\"NOTE\":\"某些工号才能办理\",\"LIMIT_TAR\":\"0\",\"TAR_NAME\":\"办理号码\",\"PASS_FLAG\":\"Y\",\"LIMIT_TYPE\":\"08\",\"LIMIT_SERIAL\":\"F99862850102\"}],\"PASS_FLAG\":\"Y\"},\"CHECK_FLAG\":\"N\",\"PASS_FLAG\":\"Y\"}}},\"PASS_FLAG\":\"Y\",\"ACTION\":{\"SEND_START_TIME\":\"\",\"SEND_END_TIME\":\"\",\"ACTION_DATE\":\"2019-01-01至2030-01-01\",\"PROVIDE_TYPE\":\"\",\"ACTION_NAME\":\"其他\",\"ACTION_DESC\":\"\",\"MKT_DICTION\":\"\",\"ACTION_ID\":\"AZ54\"}},\"RETURN_CODE\":0,\"USER_MSG\":\"OK\",\"DETAIL_MSG\":\"OK\",\"RUN_IP\":\"*************\"}}");
                JSONObject root_ = JSONObject.fromObject(json1.getString("ROOT"));
                //System.out.println("ROOT=="+root_);
                JSONObject out_data = JSONObject.fromObject(root_.getString("OUT_DATA"));
                //System.out.println("PASS_FLAG=="+out_data.getString("PASS_FLAG"));
                //System.out.println("out_data=="+out_data);
                if (out_data.getString("PASS_FLAG").equals("Y")) {

                    JSONObject means = JSONObject.fromObject(out_data.getString("MEANS"));
                    JSONObject mean = JSONObject.fromObject(means.getString("MEAN"));
                    boolean a17s = mean.containsKey("A17");
                    List objects = new ArrayList<>();

                    if(a17s){
                        JSONObject a17 = JSONObject.fromObject(mean.getString("A17"));
                        JSONObject mean_all = JSONObject.fromObject(a17.getString("MEAN_ALL"));
                        JSONArray limit_info = mean_all.getJSONArray("LIMIT_INFO");

                        for (int i = 0; i < limit_info.size(); i++) {
                            JSONObject jsonObject = JSONObject.fromObject(limit_info.get(i));
                            String note = jsonObject.getString("NOTE");
                            objects.add(note);
                        }
                    }


                    //Object a101 = mean.get("A10");
                    JSONObject a10 = mean.getJSONObject("A10");
                    Object month_return1 = a10.get("MONTH_RETURN");
                    //JSONObject.fromObject(mean.getJSONObject("A10"));
                    //判断JSON是否是数组
                    if(month_return1 instanceof JSONArray){
                        logger.info("这是数组："+month_return1.toString());
                        //为数组的时候
                        //JSONObject a10 = JSONObject.fromObject(mean.getString("A10"));
                        JSONArray month_return = a10.getJSONArray("MONTH_RETURN");
                        JSONObject jsonObject1 = new JSONObject();
                        //System.out.println("month_return=="+month_return);
                        for (int i = 0; i < month_return.size(); i++) {
                            JSONObject jsonObject = JSONObject.fromObject(month_return.get(i));
                            if (jsonObject.getString("MONTH_TYPE").equals("0")) {
                                jsonObject1 = jsonObject;
                                break;
                            }
                        }
                        if (jsonObject1.get("PAY_MODE").equals("1")) {
                            //System.out.println("浮动缴费");
                            JSONObject data = new JSONObject();
                            data.put("PAY_MAX_FEE", jsonObject1.get("PAY_MAX_FEE"));
                            data.put("PAY_MIN_FEE", jsonObject1.get("PAY_MIN_FEE"));
                            data.put("FLOAD_BASE", jsonObject1.get("FLOAD_BASE"));
                            data.put("PHONE", phone);
                            //String [] y = new String [] {"1","2","3","4","5","6","7","8"};
                            data.put("AUTHORITY_LIST", objects);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("YES");
                            r.setData(data);
                            //PAY_MIN_FEE 最小值
                            //PAY_MAX_FEE 最大值
                            //FLOAD_BASE 该数的整数倍
                        } else {
                            //TOTAL_MONEY
                            JSONObject data = new JSONObject();
                            data.put("TOTAL_MONEY",jsonObject1.get("TOTAL_MONEY"));
                            data.put("PHONE", phone);
                            //data.put("TOTAL_MONEY", "1000");
                            //System.out.println("不是浮动缴费");
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("NO");
                            r.setData(data);
                        }
                    }else if(month_return1 instanceof JSONObject) {
                        logger.info("这是对象：" + month_return1.toString());
                        //不为数组
                        //JSONObject a10 = JSONObject.fromObject(mean.getString("A10"));
                        JSONObject jsonObject1 = a10.getJSONObject("MONTH_RETURN");
                        //JSONArray month_return = a10.getJSONArray("MONTH_RETURN");
                        if (jsonObject1.get("PAY_MODE").equals("1")) {
                            //System.out.println("浮动缴费");
                            JSONObject data = new JSONObject();
                            data.put("PAY_MAX_FEE", jsonObject1.get("PAY_MAX_FEE"));
                            data.put("PAY_MIN_FEE", jsonObject1.get("PAY_MIN_FEE"));
                            data.put("FLOAD_BASE", jsonObject1.get("FLOAD_BASE"));
                            data.put("PHONE", phone);
                            //String [] y = new String [] {"1","2","3","4","5","6","7","8"};
                            data.put("AUTHORITY_LIST", objects);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("YES");
                            r.setData(data);
                            //PAY_MIN_FEE 最小值
                            //PAY_MAX_FEE 最大值
                            //FLOAD_BASE 该数的整数倍
                        } else {
                            //TOTAL_MONEY
                            JSONObject data = new JSONObject();
                            data.put("TOTAL_MONEY",jsonObject1.get("TOTAL_MONEY"));
                            data.put("PHONE", phone);
                            //data.put("TOTAL_MONEY", "1000");
                            //System.out.println("不是浮动缴费");
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("NO");
                            r.setData(data);
                        }
                    }
                    //System.out.println("===>:" + jsonObject1);//已这个数据为准
                } else {
                    JSONObject mean_all = JSONObject.fromObject(out_data.getString("MEAN_ALL"));
                    JSONArray limit_info = mean_all.getJSONArray("LIMIT_INFO");
                    String message="";
                    for (int i = 0; i < limit_info.size(); i++) {
                        JSONObject jsonObject = JSONObject.fromObject(limit_info.get(i));
                        if(jsonObject.getString("PASS_FLAG").equals("N")){
                            String limit_name = jsonObject.getString("LIMIT_NAME");
                            message+=","+limit_name;
                        }
                    }
                    r.setCode(ResultCode.FAIL);
                    r.setMessage(message);
                    r.setData("号码审核失败");
                }
            } catch (Exception e) {
                r.setCode(ResultCode.INTERNAL_SERVER_ERROR);
                r.setMessage("NO");
                r.setData(e.getMessage());
            }
        }
        //System.out.println(r);
        return r;
    }


    /**
     * s4035Cfm
     *
     * @param phone
     * @param login_no
     * @return
     */
    public Result cfmMarketingActivitiesHandle(String phone, String pay_mode, String one_deposit_amount, String active_code, String login_no) {

        JSONObject bodyContent = new JSONObject();
        bodyContent.put("MASTER_SERV_ID", "1001");//1001:普通号码 1002:宽带号码
        bodyContent.put("SERVICE_NO", phone);//服务号码
        JSONObject commonContent = new JSONObject();
        JSONObject content = new JSONObject();
        content.put("PROVINCE_GROUP", "10008");//省代码
        content.put("LOGIN_NO", login_no);//工号
        //content.put("LOGIN_NO", "aagh38");//工号
        content.put("SERVICE_NO", phone);//服务号码
        content.put("MEANS_ID", active_code);//营销活动代码
        content.put("ACT_ID", active_code.substring(0, 4));//活动大类
        content.put("CHANNEL_TYPE", "54");//渠道类型
        content.put("TERMI_BUSI_TYPE", "");//终端比例类型
        if (pay_mode.equals("是")) {
            content.put("STAGES_FLAG", "Y");//判断是否是浮动缴费标识，是浮动缴费活动就传值并值为Y，不是则不传或者值传N
            content.put("STAGES_FEE", one_deposit_amount);//浮动缴费金额
        } else {
            content.put("STAGES_FLAG", "N");//判断是否是浮动缴费标识，是浮动缴费活动就传值并值为Y，不是则不传或者值传N
            content.put("STAGES_FEE", "");//浮动缴费金额
        }
        content.put("PARAMS", "");//参数
        content.put("RESOURCE_NO", "");//串号
        content.put("RESOURCE_CODE", "");//机型
        content.put("BATCH_NO", "");//活动配置批次号

        commonContent.put("OPR_INFO", content);
        bodyContent.put("REQUEST_INFO", commonContent);
        String paras = ESBReqMsgUtil.packMsgByRoute("10", phone, bodyContent);
        //System.out.println("入参为===>:" + paras);
        if (isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38 + "com_sitech_marketsvc_comp_inter_s4035_IP4035IntCfmCoSvc_p4035IntCfm", paras, "UTF-8");
        }
        //查询接口直接对接正式环境
        String resultStr = CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_172 + "com_sitech_marketsvc_comp_inter_s4035_IP4035IntCfmCoSvc_p4035IntCfm", "{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241637655678245\",\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\",\"ROUTING\":{\"ROUTE_KEY\":\"10\",\"ROUTE_VALUE\":\"***********\"}},\"BODY\":{\"MASTER_SERV_ID\":\"1001\",\"SERVICE_NO\":\"***********\",\"REQUEST_INFO\":{\"OPR_INFO\":{\"PROVINCE_GROUP\":\"10008\",\"LOGIN_NO\":\"iaeNa1\",\"SERVICE_NO\":\"***********\",\"MEANS_ID\":\"AZ540410\",\"ACT_ID\":\"AZ54\",\"CHANNEL_TYPE\":\"54\",\"TERMI_BUSI_TYPE\":\"\",\"STAGES_FLAG\":\"Y\",\"STAGES_FEE\":\"100\",\"PARAMS\":\"\",\"RESOURCE_NO\":\"\",\"RESOURCE_CODE\":\"\",\"BATCH_NO\":\"\"}}}}}\n" +
                "\n");
        System.out.println("获取返回===>:" + resultStr);
        Result result = HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
        System.out.println("===>:" + result);
//        if(result.getCode()==200){
//            JSONObject json = JSONObject.fromObject(result.getData());
//            if(json.getString("RETURN_CODE").equals("0")){
//                JSONObject data = new JSONObject();
//                data.put("out_data",json.getString("OUT_DATA"));
//                r.setCode(ResultCode.SUCCESS);
//                r.setMessage("YES");
//                r.setData(data);
//            }else {
//                JSONObject data = new JSONObject();
//                data.put("return_msg",json.getString("RETURN_MSG"));
//                r.setCode(ResultCode.SUCCESS);
//                r.setMessage("NO");
//                r.setData(data);
//            }
//        }else {
//            r.setCode(ResultCode.FAIL);
//            r.setMessage("NO");
//            r.setData("调用接口失败");
//        }
        return result;
    }


    /**
     * 获取集团效益评估
     */
    public List<Map<String, String>> getGroupStatistics() {
        //System.out.println("欠费"+id_no);
        String sql = "SELECT * FROM BPMS_MARKET_GROUPSTATISTICS";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 添加集团效益评估统计信息
     */
    public void addGroupStatistics(MarketGroupStatistics marketGroupStatistics) {
        try {
            Session session = this.getSession();
            session.save(marketGroupStatistics);
            session.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加集团效益评估统计信息
     */
    public void addGroupStatistics(List<MarketGroupStatistics> marketGroupStatistics) {
        for (MarketGroupStatistics marketGroupStatistic : marketGroupStatistics) {
            try {
                Session session = this.getSession();
                session.save(marketGroupStatistic);
                session.flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }


    /**
     * 删除集团效益评估统计信息
     */
    public void deleteGroupStatistics() {
        String sql = "truncate table BPMS_MARKET_GROUPSTATISTICS";
        this.getSession().createSQLQuery(sql).executeUpdate();
    }

    /**
     * 保存营销活动推送信息
     */
    public void addMarketBossWorkOrder(MarketBossWorkOrder marketBossWorkOrder) {
        try {
            Session session = this.getSession();
            session.save(marketBossWorkOrder);
            session.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 根据人员查询权限
     * @param user
     * @return
     */
    public List<Map<String,String>> getUserPowers(SystemUser user){
        try {
            String sql = "select * from VW_USERINFO where ROWNO=? and ISMAINDPT='true'";
            return getSession().createSQLQuery(sql).setInteger(0, user.getRowNo()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return null;
        }
    }
}
