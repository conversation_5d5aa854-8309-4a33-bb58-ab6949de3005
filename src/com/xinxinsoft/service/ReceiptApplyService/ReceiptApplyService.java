package com.xinxinsoft.service.ReceiptApplyService;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.util.Collection;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;

import com.xinxinsoft.entity.ICT.ICTApplication;
import jxl.CellView;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.apache.struts2.ServletActionContext;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;

import com.google.gson.annotations.Expose;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.contractUniformity.BossForm;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.manualInvApply.ManualInvApplyDet;
import com.xinxinsoft.entity.receiptApplys.ReceiptApply;
import com.xinxinsoft.entity.receiptApplys.ReceiptApplyDet;
import com.xinxinsoft.entity.receiptApplys.ReceiptApplyProcess;
import com.xinxinsoft.entity.receiptApplys.ReceiptApplyTask;
import com.xinxinsoft.entity.transfer.TransferAccountInformation;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.transfer.TransferInformation;
import com.xinxinsoft.entity.transfer.TransferProcess;
import com.xinxinsoft.entity.transfer.TransferTask;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

public class ReceiptApplyService extends BaseService {


    /**
     * 根据流程id查询任务表信息
     *
     * @param processId
     * @return
     */
    public ReceiptApplyTask getTaskList(String processId) {
        String sql = "select * from ReceiptApplyTask where uuid=?";
        ReceiptApplyTask OrderContent = (ReceiptApplyTask) getSession().createSQLQuery(sql).addEntity(ReceiptApplyTask.class).setString(0, processId).uniqueResult();
        return OrderContent;
    }

    /**
     * 修改任务表信息
     *
     * @param receiptApplyTask
     * @return
     */
    public ReceiptApplyTask updateTask(ReceiptApplyTask receiptApplyTask) {
        Session session = this.getSession();
        session.update(receiptApplyTask);
        return receiptApplyTask;
    }

    /**
     * 保存任务信息表
     *
     * @param taskList
     * @return
     */
    public ReceiptApplyTask saveTaskList(ReceiptApplyTask taskList) {
        try {
            if (taskList != null) {
                Session session = this.getSession();
                session.save(taskList);
            }
            return taskList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;

        }
    }


    /**
     * 修改主表信息
     *
     * @param receiptApply
     * @return
     */
    public ReceiptApply updateReceiptApply(ReceiptApply receiptApply) {
        try {
            if (receiptApply != null) {
                Session session = this.getSession();
                session.update(receiptApply);
            }
            return receiptApply;
        } catch (Exception e) {
            e.printStackTrace();
            return null;

        }
    }

    /**
     * 保存流程信息表
     *
     * @param processList
     * @return
     */
    public ReceiptApplyProcess saveProcessList(ReceiptApplyProcess processList) {
        try {
            if (processList != null) {
                Session session = this.getSession();
                session.save(processList);
            }
            return processList;
        } catch (Exception e) {
            e.printStackTrace();
            return null;

        }
    }

    /**
     * 查询流程id
     *
     * @param id
     */
    public ReceiptApplyProcess getReceiptApplyProcess(String id) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApplyProcess where transferId=?";
        ReceiptApplyProcess receiptApplyProcess = (ReceiptApplyProcess) this.getSession().createSQLQuery(sql).addEntity(ReceiptApplyProcess.class).setString(0, id).uniqueResult();
        return receiptApplyProcess;
    }

    public ReceiptApply getReceiptApply(String id) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApply where id=?";
        ReceiptApply OrderContent = (ReceiptApply) getSession().createSQLQuery(sql).addEntity(ReceiptApply.class).setString(0, id).uniqueResult();
        return OrderContent;
    }

    public TransferCitiesData getTransferCitiesData(String code, String dangqianrenwu) {
        String sqlone = "select * from TransferCitiesData where citiesCode =? and nodeName=? and processNmae='ReceiptApply' ";
        TransferCitiesData transferCitiesData = (TransferCitiesData) this.getSession().createSQLQuery(sqlone).addEntity(TransferCitiesData.class).setString(0, code).setString(1, dangqianrenwu).uniqueResult();
        return transferCitiesData;
    }

    public List<SingleAndAttachment> getSingleAndAttachment(String orderId2) {
        // TODO Auto-generated method stub
        String sql = "from SingleAndAttachment s where s.orderID=?";
        List<SingleAndAttachment> or = getSession().createQuery(sql).setString(0, orderId2).list();
        return or;
    }

    public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
        if (sa.getId() == null) {
            String sql = "select  * from SingleAndAttachment t where t.orderid=? and t.attachmentid=? and t.link=?";
            Object count = getSession().createSQLQuery(sql).setString(0, sa.getOrderID()).setString(1, sa.getAttachmentId()).setString(2, sa.getLink()).uniqueResult();
            if (null == count) {
                Session session = this.getSession();
                session.saveOrUpdate(sa);
                session.flush();
                return sa;
            } else {
                return null;
            }
        } else {
            Session session = this.getSession();
            session.saveOrUpdate(sa);
            session.flush();
            return sa;
        }
    }

    public ReceiptApplyDet addReceiptApplyDet(ReceiptApplyDet receiptApplyDet) {
        // TODO Auto-generated method stub
        try {
            Session session = this.getSession();
            session.save(receiptApplyDet);
            return receiptApplyDet;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public ReceiptApply addReceiptApply(ReceiptApply receiptApply) {
        // TODO Auto-generated method stub
        try {
            Session session = this.getSession();
            session.save(receiptApply);
            return receiptApply;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<Object[]> getbumen(int userID) {
        String sql = "select " + "case when dn1.DEPARTMENT_LEVEL=1 " + "THEN dn1.DEPARTMENT_NAME " + "else dn2.DEPARTMENT_NAME " + "end," + "as2.COMPANY_NAME," + "as2.COMPANY_IBM "
                + "from  AFR_SYSTEMDEPT dn1 " + "left join AFR_SYSTEMDEPT dn2 " + "on " + "dn1.DEPARTMENT_PARENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEMDEPT as1 " + "on "
                + "as1.DEPARTMENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEM_DEPT_USER asdu " + "on " + "asdu.DEPARTMENT_NO=dn1.DEPARTMENT_NO " + "left join AFR_SYSTEMCOMPANY as2 " + "on "
                + "as2.COMPANY_CODE=dn2.COMPANY_CODE " + "where asdu.ROWNO=?";
        Query query = getSession().createSQLQuery(sql);
        query.setInteger(0, userID);
        List<Object[]> s = query.list();
        return s;
    }

    /**
     * 获取附件消息
     */
    public List<Map<String, String>> fuJian(String id, String biaoshi) {
        String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from ReceiptApply  o  "
                + " left join  SingleAndAttachment oa  on o.id=OA.orderID " + " LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.id=? and oa.link=?";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, id).setString(1, biaoshi).list();
    }

    public TransferTask getReceiptApplyTask(String transferId, String rolename) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApplyTask where role='" + rolename + "' and process='" + transferId + "' Order By CREATDATE DESC ";
        return (TransferTask) getSession().createSQLQuery(sql).addEntity(TransferTask.class).list().get(0);
    }

    /**
     * 根据用户编号查询相关信息
     *
     * @param id
     * @return
     */
    public Map<String, Object> findByRowNo(String id) {
        String sql = "select * from ReceiptApply where id=?";
        return (Map<String, Object>) getSession().createSQLQuery(sql).setString(0, id).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list().get(0);
    }

    /**
     * 根据id查询转账对象
     *
     * @param id
     * @return
     */
    public ReceiptApply getReceiptApplyNumber(String id) {
        String sql = "select * from ReceiptApply where id=?";
        return (ReceiptApply) getSession().createSQLQuery(sql).addEntity(ReceiptApply.class).setString(0, id).uniqueResult();
    }

    public List<ReceiptApplyDet> getReceiptApplyDet(
            String applyNO) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApplyDet where applyNO=?";
        return getSession().createSQLQuery(sql).addEntity(ReceiptApplyDet.class).setString(0, applyNO).list();
    }

    public List<ReceiptApplyDet> getReceiptApplyDetBoss(
            String applyNO) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApplyDet where applyNO=? and bossStatus!='1'";
        return getSession().createSQLQuery(sql).addEntity(ReceiptApplyDet.class).setString(0, applyNO).list();
    }

    public ReceiptApplyDet getReceiptApplyDetId(
            String id) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApplyDet where id=?";
        return (ReceiptApplyDet) getSession().createSQLQuery(sql).addEntity(ReceiptApplyDet.class).setString(0, id).uniqueResult();
    }

    public ReceiptApplyDet getReceiptApplyDetSerialNo(
            String serialNo) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApplyDet where serialNo=?";
        return (ReceiptApplyDet) getSession().createSQLQuery(sql).addEntity(ReceiptApplyDet.class).setString(0, serialNo).uniqueResult();
    }

    /**
     * 根据订单id 查询跟踪记录
     *
     * @param id
     * @return
     */
    public List<ReceiptApplyTask> processtracking(String id) {
        String sql = "select a.uuid as uuid," + "a.process as process," + "a.creator as creator," + "a.creatorNo as creatorNo," + "a.creatdate as creatdate," + "a.plandate as plandate,"
                + "a.oper as oper," + "a.operno as operno," + "a.operdate as operdate," + "a.spendtime as spendtime," + "a.replycontent as replycontent," + "a.STATUS as STATUS," + "a.type as type,"
                + "a.role as role," + "a.expectedCompletionTime as expectedCompletionTime," + "a.role as role from ReceiptApplyTask a " + "where a.PROCESS='" + id + "' order by a.creatDate desc";
        return getSession().createSQLQuery(sql).addEntity(ReceiptApplyTask.class).list();
    }

    /**
     * 手机端查询数据
     */
    public LayuiPage findReceiptApplyList(LayuiPage page, String number,
                                          String title, String groupCode, String state,
                                          String lostEffectDate, String lostEffectDatetwo, int rowNo,
                                          Integer tabindex) {
        String sql = "";
        if (tabindex == 0) {
            //System.out.println("0");
            sql += "select * from ReceiptApply b inner join RECEIPTAPPLYPROCESS s on s.TransferId=b.id " +
                    "inner join RECEIPTAPPLYTASK k on k.PROCESS=b.id " +
                    "WHERE k.status='1' AND  b.STATE!='0' AND k.OperNo='" + rowNo + "'";
        } else if (tabindex == 1) {
            //System.out.println("1");
            sql += "select * from ReceiptApply b where b.creator='" + rowNo + "'";
        } else if (tabindex == 2) {
            //System.out.println("2");
            sql += "SELECT * from (select "
                    + "r.ID,"
                    + "r.applyNO,"
                    + "r.applyTitle,"
                    + "r.applyMemo,"
                    + "r.creator,"
                    + "r.creatorName,"
                    + "r.companyCode,"
                    + "r.region,"
                    + "r.createDate,"
                    + "r.state,"
                    + "r.UNIT_ID,"
                    + "r.applyType,"
                    + "r.amount,"
                    + "r.tax_Rate,"
                    + "r.isTotal,"
                    + "r.policyFile,"
                    + "r.unifyFile,"
                    + "r.totalAmount,"
                    + "r.contrctType,"
                    + "r.bossStatus, "
                    + "r.HANDLER_ID "
                    /*+ ","
					+ "r.branchFileUrl,"
					+ "r.customerLetterFileUrl,"
					+ "r.systemFileUrl,"
					+ "r.productsFileUrl "*/
                    + "from ReceiptApply r left JOIN ReceiptApplyTask ma on ma.process=r.id where ma.CREATORNO='" + rowNo + "' GROUP BY "
                    + "r.ID,"
                    + "r.applyNO,"
                    + "r.applyTitle,"
                    + "r.applyMemo,"
                    + "r.creator,"
                    + "r.creatorName,"
                    + "r.companyCode,"
                    + "r.region,"
                    + "r.createDate,"
                    + "r.state,"
                    + "r.UNIT_ID,"
                    + "r.applyType,"
                    + "r.amount,"
                    + "r.tax_Rate,"
                    + "r.isTotal,"
                    + "r.policyFile,"
                    + "r.unifyFile,"
                    + "r.totalAmount,"
                    + "r.contrctType,"
                    + "r.bossStatus, "
                    + "r.HANDLER_ID "
                    /*+ ","
					+ "r.branchFileUrl,"
					+ "r.customerLetterFileUrl,"
					+ "r.systemFileUrl,"
					+ "r.productsFileUrl"*/
                    + ") b  where 1=1";
        }
        if (number != null && number.length() > 0) {
            sql += " and b.applyNO like '%" + number + "%'";
        }
        if (title != null && title.length() > 0) {
            sql += " and b.applyTitle like '%" + title + "%'";
        }
        if (state != null && state.length() > 0) {
            sql += " and b.state='" + state + "'";
        }
        if (groupCode != null && groupCode.length() > 0) {
            sql += " and b.UNIT_ID like '%" + groupCode + "%'";
        }
        if (lostEffectDate != null && lostEffectDate.length() > 0) {
            if (lostEffectDatetwo != null && lostEffectDatetwo.length() > 0) {
                sql += " and b.createDate >= to_date('" + lostEffectDate + "','yyyy-mm-dd') and b.createDate <= to_date('" + lostEffectDatetwo + "','yyyy-mm-dd')";
            }
        }
        sql += " order by b.createDate desc";
        //System.out.println("这是正负补收查询sql=======:" + sql);
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public LayuiPage findReceiptApplyPhone(LayuiPage page, int rowNo) {
        String sql = "SELECT * from WAITTASK WHERE (CODE='RECEIPTAPPLY' OR CODE='ZFBS') AND STATE='0' AND HANDLEUSERID= '" + rowNo + "'";
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public LayuiPage queryReceiptApply(LayuiPage page, String number,
                                    String title, String groupCode, String state,
                                    String lostEffectDate, String lostEffectDatetwo, String rowNo,
                                    Integer tabindex) {
        // TODO Auto-generated method stub
        String sql = "";
        String countSql = "";
        if (tabindex == 0) {
            sql += "select * from ReceiptApply b inner join RECEIPTAPPLYPROCESS s on s.TransferId=b.id " +
                    "inner join RECEIPTAPPLYTASK k on k.PROCESS=b.id " +
                    "WHERE k.status='1' AND k.OperNo='" + rowNo + "'";
//            countSql += "select * from ReceiptApply b inner join RECEIPTAPPLYPROCESS s on s.TransferId=b.id  " +
//                    " inner join RECEIPTAPPLYTASK k on k.PROCESS=b.id "+
//                    " WHERE k.status='1' AND k.OperNo='" + rowNo + "'";
        } else if (tabindex == 1) {
            sql += "select * from ReceiptApply b where b.creator='" + rowNo + "'";
//            countSql += "select count(*) from ReceiptApply b where b.creator='" + rowNo + "'";
        } else if (tabindex == 2) {
            sql += "SELECT * from (select "
                    + "r.ID,"
                    + "r.applyNO,"
                    + "r.applyTitle,"
                    + "r.applyMemo,"
                    + "r.creator,"
                    + "r.creatorName,"
                    + "r.companyCode,"
                    + "r.region,"
                    + "r.createDate,"
                    + "r.state,"
                    + "r.UNIT_ID,"
                    + "r.applyType,"
                    + "r.amount,"
                    + "r.tax_Rate,"
                    + "r.isTotal,"
                    + "r.policyFile,"
                    + "r.unifyFile,"
                    + "r.totalAmount,"
                    + "r.contrctType,"
                    + "r.bossStatus, "
                    + "r.HANDLER_ID "
                    /*+ ","
					+ "r.branchFileUrl,"
					+ "r.customerLetterFileUrl,"
					+ "r.systemFileUrl,"
					+ "r.productsFileUrl "*/
                    + "from ReceiptApply r left JOIN ReceiptApplyTask ma on ma.process=r.id where ma.OPERNO='" + rowNo + "' GROUP BY "
                    + "r.ID,"
                    + "r.applyNO,"
                    + "r.applyTitle,"
                    + "r.applyMemo,"
                    + "r.creator,"
                    + "r.creatorName,"
                    + "r.companyCode,"
                    + "r.region,"
                    + "r.createDate,"
                    + "r.state,"
                    + "r.UNIT_ID,"
                    + "r.applyType,"
                    + "r.amount,"
                    + "r.tax_Rate,"
                    + "r.isTotal,"
                    + "r.policyFile,"
                    + "r.unifyFile,"
                    + "r.totalAmount,"
                    + "r.contrctType,"
                    + "r.bossStatus, "
                    + "r.HANDLER_ID "
                    /*+ ","
					+ "r.branchFileUrl,"
					+ "r.customerLetterFileUrl,"
					+ "r.systemFileUrl,"
					+ "r.productsFileUrl"*/
                    + ") b  where 1=1";
//            countSql += "SELECT count(*) from (select "
//                    + "r.ID,"
//                    + "r.applyNO,"
//                    + "r.applyTitle,"
//                    + "r.applyMemo,"
//                    + "r.creator,"
//                    + "r.creatorName,"
//                    + "r.companyCode,"
//                    + "r.region,"
//                    + "r.createDate,"
//                    + "r.state,"
//                    + "r.UNIT_ID,"
//                    + "r.applyType,"
//                    + "r.amount,"
//                    + "r.tax_Rate,"
//                    + "r.isTotal,"
//                    + "r.policyFile,"
//                    + "r.unifyFile,"
//                    + "r.totalAmount,"
//                    + "r.contrctType,"
//                    + "r.bossStatus, "
//                    + "r.HANDLER_ID "
//					/*+ ","
//					+ "r.branchFileUrl,"
//					+ "r.customerLetterFileUrl,"
//					+ "r.systemFileUrl,"
//					+ "r.productsFileUrl "*/
//                    + "from ReceiptApply r left JOIN ReceiptApplyTask ma on ma.process=r.id where ma.CREATORNO='" + rowNo + "' GROUP BY "
//                    + "r.ID,"
//                    + "r.applyNO,"
//                    + "r.applyTitle,"
//                    + "r.applyMemo,"
//                    + "r.creator,"
//                    + "r.creatorName,"
//                    + "r.companyCode,"
//                    + "r.region,"
//                    + "r.createDate,"
//                    + "r.state,"
//                    + "r.UNIT_ID,"
//                    + "r.applyType,"
//                    + "r.amount,"
//                    + "r.tax_Rate,"
//                    + "r.isTotal,"
//                    + "r.policyFile,"
//                    + "r.unifyFile,"
//                    + "r.totalAmount,"
//                    + "r.contrctType,"
//                    + "r.bossStatus, "
//                    + "r.HANDLER_ID "
//					/*+ ","
//					+ "r.branchFileUrl,"
//					+ "r.customerLetterFileUrl,"
//					+ "r.systemFileUrl,"
//					+ "r.productsFileUrl"*/
//                    + ") b where 1=1";
        }


        if (number != null && number.length() > 0) {
            sql += " and b.applyNO like '%" + number + "%'";
            //countSql += " and b.applyNO like '%" + number + "%'";
        }

        if (title != null && title.length() > 0) {
            sql += " and b.applyTitle like '%" + title + "%'";
            //countSql += " and b.applyTitle like '%" + title + "%'";
        }

        if (state != null && state.length() > 0) {
            sql += " and b.state='" + state + "'";
            //countSql += " and b.state='" + state + "'";
        }

        if (groupCode != null && groupCode.length() > 0) {
            sql += " and b.UNIT_ID like '%" + groupCode + "%'";
            //countSql += " and b.UNIT_ID like '%" + groupCode + "%'";
        }
        if (lostEffectDate != null && lostEffectDate.length() > 0) {
            if (lostEffectDatetwo != null && lostEffectDatetwo.length() > 0) {
                sql += " and b.createDate >= to_date('" + lostEffectDate + "','yyyy-mm-dd') and b.createDate <= to_date('" + lostEffectDatetwo + "','yyyy-mm-dd')";
                //countSql += " and b.createDate >= to_date('" + lostEffectDate + "','yyyy-mm-dd') and b.createDate <= to_date('" + lostEffectDatetwo + "','yyyy-mm-dd')";
            }
        }
        sql += " order by b.createDate desc";
        System.out.println("这是正负补收的查询sql=======:" + sql);
        page.setCount(getCount("select count(0) from ( " + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public String queryReceiptApplyTwo(LayuiPage page, String number,
                                       String title, String groupCode, String state,
                                       String lostEffectDate, String lostEffectDatetwo, SystemUser user,
                                       Integer tabindex, String companyCodes) {
        // TODO Auto-generated method stub
        String sql = "";
        String countSql = "";
        List<SystemDept> deptList = user.getSystemDept();
        String code = deptList.get(0).getSystemCompany().getCompanyCode();
        if (code.equals("00")) {
            sql += "select * from ReceiptApply b where 1=1";
            countSql += "select count(*) from ReceiptApply b where 1=1";
        } else {
            int companyCode = Integer.parseInt(code);
            sql += "select * from ReceiptApply b where b.companyCode='" + companyCode + "'";
            countSql += "select count(*) from ReceiptApply b where b.companyCode='" + companyCode + "'";
        }
        if (companyCodes != null && companyCodes.length() > 0) {
            sql += " and b.companyCode='" + companyCodes + "'";
            countSql += " and b.companyCode='" + companyCodes + "'";
        }
        if (number != null && number.length() > 0) {
            sql += " and b.applyNO like '%" + number + "%'";
            countSql += " and b.applyNO like '%" + number + "%'";
        }
        if (title != null && title.length() > 0) {
            sql += " and b.applyTitle like '%" + title + "%'";
            countSql += " and b.applyTitle like '%" + title + "%'";
        }
        if (state != null && state.length() > 0) {
            if (state.equals("1") || state.equals("5")) {
                sql += " and b.state='1' or b.state='5'";
                countSql += " and b.state='1' or b.state='5'";
            } else {
                sql += " and b.state='" + state + "'";
                countSql += " and b.state='" + state + "'";
            }
        }
        if (groupCode != null && groupCode.length() > 0) {
            sql += " and b.UNIT_ID like '%" + groupCode + "%'";
            countSql += " and b.UNIT_ID like '%" + groupCode + "%'";
        }
        if (lostEffectDate != null && lostEffectDate.length() > 0) {
            if (lostEffectDatetwo != null && lostEffectDatetwo.length() > 0) {
                sql += " and b.createDate >= to_date('" + lostEffectDate + "','yyyy-mm-dd') and b.createDate <= to_date('" + lostEffectDatetwo + "','yyyy-mm-dd')";
                countSql += " and b.createDate >= to_date('" + lostEffectDate + "','yyyy-mm-dd') and b.createDate <= to_date('" + lostEffectDatetwo + "','yyyy-mm-dd')";
            }
        }
        sql += " order by b.createDate desc";
        page.setData(getSession()
                .createSQLQuery(sql)
                .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                .setFirstResult(page.getPageNo())//索引开始位置
                .setMaxResults(page.getPageSize())//数据条数
                .list());

        //查询总条数
        page.setCount(Integer.valueOf(getSession()
                .createSQLQuery(countSql)
                .setCacheable(true)
                .uniqueResult() + "")
        );

        Collection c = getPageList(sql, ReceiptApply.class, page);
        page.setData(c);
        String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page);
        return json;
    }

    public LayuiPage findQueryReceiptApply(LayuiPage page, String number,
                                           String title, String groupCode, String state,
                                           String lostEffectDate, String lostEffectDatetwo, SystemUser user,
                                           Integer tabindex, String companyCodes,boolean flag) {
        String sql = "SELECT\n" +
                "\ty.id,\n" +
                "\ty.applyNO,\n" +
                "\ty.applyTitle,\n" +
                "\ty.createDate,\n" +
                "\ty.unit_id,\n" +
                "\ty.applyType,\n" +
                "\ty.totalAmount,\n" +
                "\ty.tax_Rate,\n" +
                "\ty.state,\n" +
                "CASE\n" +
                "\t\t\n" +
                "\t\tWHEN COALESCE( MAX( TO_CHAR( t.BOSSSTATUS )), '0' ) = '1' THEN\n" +
                "\t\t'成功' \n" +
                "\t\tWHEN COALESCE( MAX( TO_CHAR( t.BOSSSTATUS )), '0' ) = '0' THEN\n" +
                "\t\t'未推送' ELSE '部分成功' \n" +
                "\tEND AS BOSSSTATUS \n" +
                "FROM\n" +
                "\tReceiptApply y\n" +
                "\tLEFT JOIN RECEIPTAPPLYDET t ON t.applyNO = y.applyNO ";
        List<SystemDept> deptList = user.getSystemDept();
        String code = deptList.get(0).getSystemCompany().getCompanyCode();
        if(flag){
            if (code.equals("00")) { //省公司
                sql += " where 1=1";
            } else {
                int companyCode = Integer.parseInt(code);
                sql += " where y.companyCode='" + companyCode + "'";
            }
        }else {
            sql += " where y.creator='" + user.getRowNo() + "'";
        }
        if (companyCodes != null && companyCodes.length() > 0) {
            sql += " and y.companyCode='" + companyCodes + "'";
        }
        if (number != null && number.length() > 0) {
            sql += " and y.applyNO like '%" + number + "%'";
        }
        if (title != null && title.length() > 0) {
            sql += " and y.applyTitle like '%" + title + "%'";
        }
        if (state != null && state.length() > 0) {
            if (state.equals("1") || state.equals("5")) {
                sql += " and y.state='1' or y.state='5'";
            } else {
                sql += " and y.state='" + state + "'";
            }
        }
        if (groupCode != null && groupCode.length() > 0) {
            sql += " and y.UNIT_ID like '%" + groupCode + "%'";
        }
        if (lostEffectDate != null && lostEffectDate.length() > 0) {
            if (lostEffectDatetwo != null && lostEffectDatetwo.length() > 0) {
                sql += " and y.createDate >= to_date('" + lostEffectDate + "','yyyy-mm-dd') and y.createDate <= to_date('" + lostEffectDatetwo + "','yyyy-mm-dd')";
            }
        }
        sql += " GROUP BY\n" +
                "\ty.applyNO,\n" +
                "\ty.applyTitle,\n" +
                "\ty.createDate,\n" +
                "\ty.unit_id,\n" +
                "\ty.applyType,\n" +
                "\ty.totalAmount,\n" +
                "\ty.tax_Rate,\n" +
                "\ty.id,\n" +
                "\ty.state ";
        System.out.println("请求的参数为=="+sql);
        page.setCount(getCount("select count(0) from ( " + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public String queryReceiptApplyDet(LayuiPage page, String serialNo, String applyNO, String contrctNo, String productNo,
                                       SystemUser user, String companyCodes,boolean flag) {
        // TODO Auto-generated method stub
        String sql = "";
        String countSql = "";
        List<SystemDept> deptList = user.getSystemDept();
        String code = deptList.get(0).getSystemCompany().getCompanyCode();
        if(flag){
            if (code.equals("00")) { //省公司
                sql += "select p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where 1=1";
                countSql += "select count(*) from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where 1=1";
            } else {
                int companyCode = Integer.parseInt(code);
                sql += "select p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.companyCode='" + companyCode + "'";
                countSql += "select count(*) from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.companyCode='" + companyCode + "'";
            }
        }else {
            sql += "select p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.creator='" + user.getRowNo() + "'";
            countSql += "select count(*) from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.creator='" + user.getRowNo() + "'";
        }
//        if (code.equals("00")) {
//            sql += "select p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where 1=1";
//            countSql += "select count(*) from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where 1=1";
//        } else {
//            int companyCode = Integer.parseInt(code);
//            sql += "select p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.companyCode='" + companyCode + "'";
//            countSql += "select count(*) from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.companyCode='" + companyCode + "'";
//        }
        if (companyCodes != null && companyCodes.length() > 0) {
            sql += " and re.companyCode='" + companyCodes + "'";
            countSql += " and re.companyCode='" + companyCodes + "'";
        }
        if (applyNO != null && applyNO.length() > 0) {
            sql += " and p.applyNO like '%" + applyNO + "%'";
            countSql += " and p.applyNO like '%" + applyNO + "%'";
        }
        if (serialNo != null && serialNo.length() > 0) {
            sql += " and p.serialNo like '%" + serialNo + "%'";
            countSql += " and p.serialNo like '%" + serialNo + "%'";
        }

        if (contrctNo != null && contrctNo.length() > 0) {
            sql += " and p.contrctNo like '%" + contrctNo + "%'";
            countSql += " and p.contrctNo like '%" + contrctNo + "%'";
        }

        if (productNo != null && productNo.length() > 0) {
            sql += " and p.productNo like '%" + productNo + "%'";
            countSql += " and p.productNo like '%" + productNo + "%'";
        }

        sql += " order by p.createDate desc";
        page.setData(getSession()
                .createSQLQuery(sql)
                .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                .setFirstResult(page.getPageNo())//索引开始位置
                .setMaxResults(page.getPageSize())//数据条数
                .list());

        //查询总条数
        page.setCount(Integer.valueOf(getSession()
                .createSQLQuery(countSql)
                .setCacheable(true)
                .uniqueResult() + "")
        );

        Collection c = getPageList(sql, ReceiptApplyDet.class, page);
        page.setData(c);
        String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page);
        return json;
    }


    public void downloFujian(String id) throws IOException {
        String sql = "from Attachment where attachmentId = ?";
        Attachment attachment = (Attachment) getSession().createQuery(sql).setString(0, id).uniqueResult();


        File file = new File(FileUpload.getFtpURL() + attachment.getAttachmentUrl());
        HttpServletResponse response = ServletActionContext.getResponse();
        OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
        if (!file.exists()) {
            outputStream.write("file not found".getBytes());
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
            return;
        }
        byte[] data = FileUtil.toByteArray2(FileUpload.getFtpURL() + attachment.getAttachmentUrl());
        String fileName = URLEncoder.encode(attachment.getRealName(), "UTF-8");
        response.reset();
        response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
        response.addHeader("Content-Length", "" + data.length);
        response.setContentType("application/octet-stream;charset=UTF-8");
        outputStream.write(data);
        outputStream.flush();
        outputStream.close();
        response.flushBuffer();
    }

    public String getFileRsName(String nowName) {
        // TODO Auto-generated method stub
        String sql = "SELECT REALNAME FROM ATTACHMENT where ATTACHMENTURL = ?";
        return getSession().createSQLQuery(sql).setParameter(0, nowName).uniqueResult().toString();
    }

    public String getCompanyRegion(String companyCode) {
        String sql = "select REGION_CODE from afr_systemcompany where company_Code=?";
        String str = (String) getSession().createSQLQuery(sql).setString(0, companyCode).uniqueResult();
        return str;
    }

    public ReceiptApplyDet updateReceiptApplyDet(ReceiptApplyDet receiptApplyDet) {
        // TODO Auto-generated method stub
        Session session = this.getSession();
        session.update(receiptApplyDet);
        return receiptApplyDet;
    }

    public List<ReceiptApplyDet> getReceiptApplyDetBossStatus(
            String orderNo) {
        // TODO Auto-generated method stub
        String sql = "from ReceiptApplyDet where applyNO=? and bossStatus is null";
        return getSession().createQuery(sql).setString(0, orderNo).list();
    }

    public ReceiptApply getReceiptApplyApplyNO(String applyNO) {
        // TODO Auto-generated method stub
        String sql = "select * from ReceiptApply where applyNO=?";
        return (ReceiptApply) getSession().createSQLQuery(sql).addEntity(ReceiptApply.class).setString(0, applyNO).uniqueResult();
    }

    public TransferTask getTransferTask(String transferId, String rolename) {
        // TODO Auto-generated method stub
        String sql = "select * from TRANSFERTASK where role='" + rolename + "' and process='" + transferId + "' Order By CREATDATE DESC ";
        return (TransferTask) getSession().createSQLQuery(sql).addEntity(TransferTask.class).list().get(0);
    }

    public void downloadExcel(List<Map<String, Object>> mapList) throws IOException, WriteException {
        HttpServletResponse response = ServletActionContext.getResponse();
        String excelFile = FileUpload.getFtpURL() + "receiptApply.xls";
        File file = new File(FileUpload.getFtpURL());
        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        String exportName = "ExportData_" + FileUpload.getDateToString("yyyyMMdd");
        try {
            // 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
            WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
            // 2、新建工作表(sheet)对象，并声明其属于第几页
            WritableSheet firstSheet = writeBook.createSheet("正负补收账户信息", 1);// 第一个参数为工作簿的名称，第二个参数为页数
            String[] headers = new String[]{"工单名称", "工单流水号", "账户流水号", "帐户号码", "帐号类型", "服务号码", "金额(元)", "集团280", "创建日期", "创建人","推送boss状态"};
            for (int i = 0; i < headers.length; i++) {
                // 3、创建单元格(Label)对象
                Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
                WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,
                        jxl.format.Colour.BLACK); // 定义格式
                WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE,
                        jxl.format.Colour.BLACK); // 定义格式
                // 标题栏 // 颜色
                WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
                wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
                wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
                wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfTitle.setAlignment(Alignment.CENTRE); // 设置垂直对齐
                // 内容栏
                WritableCellFormat wcfContent = new WritableCellFormat(wf3);
                wcfContent.setVerticalAlignment(VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐
                CellView navCellView = new CellView();
                navCellView.setSize(80 * 50);
                label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
                firstSheet.setColumnView(i, navCellView); // 设置col显示样式
                firstSheet.setRowView(i, 400, false); // 设置行高
                firstSheet.addCell(label0);
                if (mapList.size() > 0) {
                    for (int i1 = 0; i1 < mapList.size(); i1++) {
                        //工单流水号
                        //账户流水号
                        //帐号号码
                        //帐号类型
                        //服务号码
                        //金额(元)
                        //创建日期
                        //推送boss状态
                        Label title = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("APPLYTITLE")), wcfContent);
                        firstSheet.addCell(title);//工单名称
                        Label applyNO = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("APPLYNO")), wcfContent);
                        firstSheet.addCell(applyNO);//工单流水号
                        Label serialNo = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("SERIALNO")), wcfContent);
                        firstSheet.addCell(serialNo);//账户流水号
                        Label contrctNo = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("CONTRCTNO")), wcfContent);
                        firstSheet.addCell(contrctNo);//帐户号码
                        Label contrctType = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("CONTRCTTYPE")), wcfContent);
                        firstSheet.addCell(contrctType);//帐号类型
                        Label productNo = new Label(5, i1 + 1, String.valueOf(mapList.get(i1).get("PRODUCTNO")), wcfContent);
                        firstSheet.addCell(productNo);//服务号码
                        Label amount = new Label(6, i1 + 1, String.valueOf(mapList.get(i1).get("AMOUNT")), wcfContent);
                        firstSheet.addCell(amount);//金额(元)
                        Label groupCode = new Label(7, i1 + 1, String.valueOf(mapList.get(i1).get("UNIT_ID")), wcfContent);
                        firstSheet.addCell(groupCode);//集团280
                        Label createDate = new Label(8, i1 + 1, String.valueOf(mapList.get(i1).get("CREATEDATE")), wcfContent);
                        firstSheet.addCell(createDate);//创建日期
                        Label userName = new Label(9, i1 + 1, String.valueOf(mapList.get(i1).get("CREATORNAME")), wcfContent);
                        firstSheet.addCell(userName);//创建人
                        if("".equals(String.valueOf(mapList.get(i1).get("BOSSSTATUS"))) || null==String.valueOf(mapList.get(i1).get("BOSSSTATUS"))){
                            Label bossStatus = new Label(10, i1 + 1, "未推送", wcfContent);
                            firstSheet.addCell(bossStatus);//推送boss状态
                        }else if("0".equals(String.valueOf(mapList.get(i1).get("BOSSSTATUS")))){
                            Label bossStatus = new Label(10, i1 + 1, "推送成功", wcfContent);
                            firstSheet.addCell(bossStatus);//推送boss状态
                        }else {
                            Label bossStatus = new Label(10, i1 + 1, "推送失败", wcfContent);
                            firstSheet.addCell(bossStatus);//推送boss状态
                        }
                    }
                }
            }
            // 4、打开流，开始写文件
            writeBook.write();
            // 5、关闭流
            writeBook.close();

            byte[] data = FileUtil.toByteArray2(excelFile);
            String fileName = URLEncoder.encode(exportName, "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xls" + "\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
            File fe = new File(excelFile);
            fe.delete();
        } finally {
            File fe = new File(excelFile);
            if (file.exists() && file.isDirectory()) {
                fe.delete();
            }
        }
    }

    /**
     * @param serialNo
     * @param applyNO
     * @param contrctNo
     * @param productNo
     * @param companyCodes
     * @param user
     * @return
     */
    public List<Map<String, Object>> getReceiptApplyDetExcel(String serialNo,
                                                             String applyNO, String contrctNo, String productNo,
                                                             String companyCodes, SystemUser user,boolean flag) {
        // TODO Auto-generated method stub
        String sql = "";
        List<SystemDept> deptList = user.getSystemDept();
        String code = deptList.get(0).getSystemCompany().getCompanyCode();
        if(flag){
            if (code.equals("00")) { //省公司
                sql += "select re.applyTitle,re.creatorName,re.UNIT_ID,p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where 1=1";

            } else {
                int companyCode = Integer.parseInt(code);
                sql += "select re.applyTitle,re.creatorName,re.UNIT_ID,p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.companyCode='" + companyCode + "'";

            }
        }else {
            sql += "select re.applyTitle,re.applyTitle,re.applyTitle,p.* from RECEIPTAPPLYDET p LEFT JOIN RECEIPTAPPLY re on RE.APPLYNO=p.APPLYNO where re.creator='" + user.getRowNo() + "'";
        }
        if (applyNO != null && applyNO.length() > 0) {
            sql += " and p.applyNO like '%" + applyNO + "%'";
        }
        if (serialNo != null && serialNo.length() > 0) {
            sql += " and p.serialNo like '%" + serialNo + "%'";
        }

        if (contrctNo != null && contrctNo.length() > 0) {
            sql += " and p.contrctNo like '%" + contrctNo + "%'";
        }

        if (productNo != null && productNo.length() > 0) {
            sql += " and p.productNo like '%" + productNo + "%'";
        }
        sql += " order by p.createDate desc";
//        System.out.println("展示=="+sql);
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 根据登录人名称以及状态查询待办信息
     *
     * @param name
     * @param page
     * @return
     */
    public String findByState(String name, LayuiPage page) {
        String sql = "select * from (select name,waitid,taskid,createUserName,creationTime "
                + "from WaitTask where handleLoginName='" + name + "' and state=0 and code='RECEIPTAPPLY')";
        String countSql = "select count(0) from (select name,waitid,taskid,createUserName,creationTime "
                + "from WaitTask where handleLoginName='" + name + "' and state=0 and code='RECEIPTAPPLY')";
        /*return getMap(sql, page);*/
        page.setData(getSession()
                .createSQLQuery(sql)
                .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                .setFirstResult(page.getPageNo())//索引开始位置
                .setMaxResults(page.getPageSize())//数据条数
                .list());

        //查询总条数
        page.setCount(Integer.valueOf(getSession()
                .createSQLQuery(countSql)
                .setCacheable(true)
                .uniqueResult() + "")
        );

        Collection c = getPageList(sql, null, page);
        page.setData(c);
        String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page);
        return json;
    }

    /**
     * 根据登录人名称以及状态查询待办信息
     *
     * @param id
     * @return
     */
    public WaitTask getWaitTask(String id) {
        String sql = "select * from WaitTask where taskid='" + id + "' and state=0 and code='RECEIPTAPPLY'";
        return (WaitTask) getSession().createSQLQuery(sql).addEntity(WaitTask.class).uniqueResult();
    }

    // 根据用户姓名查询相关信息
    public SystemUser findByUserName(String name) {
        String hql = "from SystemUser s where s.loginName = ?  and s.employeeStatus = 0 ";
        List<SystemUser> userInfoList = this.getSession().createQuery(hql).setString(0, name).list();
        if (userInfoList.size() > 0) {
            return userInfoList.get(0);
        } else {
            return null;
        }
    }

    public String getContracNo(LayuiPage page, String contrctNo) {
        String sql = "";
        String countSql = "";
        sql = "select ac.*,ap.applyTitle from ReceiptApplyDet ac INNER JOIN ReceiptApply ap  "
                + "on ac.applyNO=ap.applyNO where ac.contrctNo='" + contrctNo + "' ";
        countSql = "select count(0) from ReceiptApplyDet ac INNER JOIN ReceiptApply ap  "
                + "on ac.applyNO=ap.applyNO where ac.contrctNo='" + contrctNo + "' ";
        page.setData(getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult(page.getPageNo())//索引开始位置
                .setMaxResults(page.getPageSize()).list());
        //查询总条数
        page.setCount(Integer.valueOf(getSession().createSQLQuery(countSql).setCacheable(true).uniqueResult() + ""));
        String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page);
        return json;
    }

    public ReceiptApplyProcess getPid(String id) {
        String sql = "SELECT * FROM RECEIPTAPPLYPROCESS WHERE transferId='" + id + "'";
        return (ReceiptApplyProcess) getSession().createSQLQuery(sql).addEntity(ReceiptApplyProcess.class).uniqueResult();
    }

    public List<ReceiptApply> getFindByMonth(String creator,String contrctNo){
        String sql="SELECT ra.* " +
                "FROM ReceiptApply ra " +
                "INNER JOIN RECEIPTAPPLYDET rad ON ra.applyNO = rad.applyNO " +
                "WHERE ra.creator = ? " +
                "AND rad.contrctNo= ? "+
                "AND ra.applyType= '-1' "+
                "AND EXTRACT(MONTH FROM rad.createDate) = EXTRACT(MONTH FROM SYSDATE) " +
                "AND EXTRACT(YEAR FROM rad.createDate) = EXTRACT(YEAR FROM SYSDATE)";
        return getSession().createSQLQuery(sql).addEntity(ReceiptApply.class).setString(0,creator).setString(1,contrctNo).list();
    }

    public List findRoleByRowNo(int rowNo) {
        String hql = "select ROLE_ID from SYSTEM_USER_ROLE where row_no=?";
        List list = getSession().createSQLQuery(hql).setInteger(0, rowNo).list();
        return list;
    }

    public List<Map<String,String>> getFindByYear(String contrctNo){
        String sql="SELECT  y.createDate,  y.APPLYTYPE,  t.AMOUNT,  y.creatorName,  y.applyNO,  y.STATE FROM  RECEIPTAPPLY y  JOIN RECEIPTAPPLYDET t ON t.APPLYNO = y.APPLYNO WHERE  t.contrctNo = ?  AND y.CREATEDATE >= ADD_MONTHS(  SYSDATE, - 3)  AND y.STATE IN (0,1)";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, contrctNo).list();
    }
}
