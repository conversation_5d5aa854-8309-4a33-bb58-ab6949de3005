package com.xinxinsoft.service.PreinvApply;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import javax.servlet.http.HttpServletResponse;

import com.xinxinsoft.entity.ICT.ICTApplication;
import com.xinxinsoft.entity.PreinvApply.*;
import com.xinxinsoft.entity.activityPreinvApply.ActivityPreinvApply;
import com.xinxinsoft.entity.fourtOpening.forcedOrder;
import com.xinxinsoft.entity.manualInvApply.ManualInvApply;
import com.xinxinsoft.entity.preinvApplyMarking.PreinvApplyMarking;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.ESBReqMsgUtil;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.result.Result;
import jxl.CellView;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.codec.binary.Base64;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.jbpm.api.identity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

/**
 * 预开票申请
 *
 * <AUTHOR>
 */
public class PreinvApplyService extends BaseService {

    //发布环境
    private static final String ESB_URL_172 = Config.getString("TEST_ESBWS_URL");
    //正式环境
    private static final String ESB_URL_38 =Config.getString("ESBWS_URL");
    private static Boolean isES = false;

    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp()) ||"*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }

    private static final Logger logger = LoggerFactory.getLogger(PreinvApplyService.class);

    private final Map<String,String> keyNumberMap = new ConcurrentHashMap<>();

    /**
     * @return
     */
    synchronized public String getNumber() {
        String dataStr = getDataStr();
        String minNum = dataStr.substring(0,12);
        if(!keyNumberMap.containsValue(minNum)) {
            if (keyNumberMap.size()>0)keyNumberMap.clear();
            keyNumberMap.put(dataStr,minNum);
        }else{
            if (keyNumberMap.containsKey(dataStr)) {
                do {
                    dataStr = getDataStr();
                } while (keyNumberMap.containsKey(dataStr));
            }
            keyNumberMap.put(dataStr,minNum);
        }
        return dataStr;
    }

    public String getDataStr(){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return formatter.format(new Date());
    }

    /**
     * 新增开票信息
     */
    public PreinvApply addPreinvApply(PreinvApply preinvApply) {
        try {
            Session session = this.getSession();
            session.save(preinvApply);
            session.flush();
            return preinvApply;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新增流程信息
     *
     * @param preinvApplyFlow
     * @return
     */
    public PreinvApplyFlow addPreinvApplyFlow(PreinvApplyFlow preinvApplyFlow) {
        try {
            Session session = this.getSession();
            session.save(preinvApplyFlow);
            session.flush();
            return preinvApplyFlow;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新增任务信息
     *
     * @param preinvApplyTask
     * @return
     */
    public PreinvApplyTask addPreinvApplyTask(PreinvApplyTask preinvApplyTask) {
        try {
            Session session = this.getSession();
            session.save(preinvApplyTask);
            session.flush();
            return preinvApplyTask;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新增开票明细信息
     */
    public PreinvApplyDet addPreinvApplyDet(PreinvApplyDet preinvApplyDet) {
        try {
            Session session = this.getSession();
            session.save(preinvApplyDet);
            session.flush();
            return preinvApplyDet;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新增账期信息
     */
    public ApplyCycleDet addappApplyCycleDet(ApplyCycleDet applyCycleDet) {
        try {
            Session session = this.getSession();
            session.save(applyCycleDet);
            session.flush();
            return applyCycleDet;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 找出账户的最大账期和最小账期
     */
    public Object[] findCycle(String invNo) {

        String sql = "select max(cycleYM),min(cycleYM) from ApplyCycleDet where invNo=?";
        return (Object[]) getSession().createSQLQuery(sql).setParameter(0, invNo).uniqueResult();
    }

    /**
     * 保存附件信息
     */
    public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
        if (sa.getId() == null) {
            String sql = "select  * from SingleAndAttachment t where t.orderid=? and t.attachmentid=? and t.link=?";
            Object count = getSession().createSQLQuery(sql).setString(0, sa.getOrderID()).setString(1, sa.getAttachmentId())
                    .setString(2, sa.getLink()).uniqueResult();
            if (null == count) {
                Session session = this.getSession();
                session.saveOrUpdate(sa);
                session.flush();
                return sa;
            } else {
                return null;
            }
        } else {
            Session session = this.getSession();
            session.saveOrUpdate(sa);
            session.flush();
            return sa;
        }
    }

    /**
     * 根据任务id查询任务表信息
     *
     * @param taskId
     * @return
     */
    public PreinvApplyTask getTaskList(String taskId) {
        String sql = "select * from PreinvApplyTask where id=?";
        PreinvApplyTask OrderContent = (PreinvApplyTask) getSession().createSQLQuery(sql).addEntity(PreinvApplyTask.class).setString(0, taskId)
                .uniqueResult();
        return OrderContent;
    }

    /**
     * 根据编号查询预开票信息
     *
     * @params batchNO
     */
    public PreinvApply getPreinvApplyByBachNo(String batchNO) {
        String sql = "select * from PreinvApply where batchno=?";
        return (PreinvApply) getSession().createSQLQuery(sql).addEntity(PreinvApply.class).setString(0, batchNO).uniqueResult();
    }

    /**
     * 根据id查询流程表信息
     *
     * @params id
     */
    public PreinvApplyFlow getPreinvApplyFlow(String id) {
        String sql = "select * from PreinvApplyFlow where flowId=?";
        PreinvApplyFlow billsFlow = (PreinvApplyFlow) this.getSession().createSQLQuery(sql).addEntity(PreinvApplyFlow.class).setString(0, id)
                .uniqueResult();
        return billsFlow;
    }

    /**
     * 修改流程表信息
     *
     * @param preinvApplyFlow
     * @return
     */
    public PreinvApplyFlow updatePreinvApplyFlow(PreinvApplyFlow preinvApplyFlow) {
        try {
            Session session = this.getSession();
            session.update(preinvApplyFlow);
            session.flush();
            return preinvApplyFlow;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 修改任务表信息
     *
     * @params
     */
    public PreinvApplyTask updatePreinvApplyTask(PreinvApplyTask pTask) {
        try {
            Session session = this.getSession();
            session.update(pTask);
            session.flush();
            return pTask;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 修改开票信息
     */
    public PreinvApply updatePreinvApply(PreinvApply pTask) {
        try {
            Session session = this.getSession();
            session.update(pTask);
            session.flush();
            return pTask;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public PreinvApply deletePreinvApply(PreinvApply pTask) {
        try {
            Session session = this.getSession();
            session.delete(pTask);
            session.flush();
            return pTask;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 修改账户信息
     */
    public PreinvApplyDet updatePreinvApplyDet(PreinvApplyDet pTask) {
        try {
            Session session = this.getSession();
            session.update(pTask);
            session.flush();
            return pTask;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 查询开票信息并分页显示
     *
     * @params
     */
    public String findByPage(PageVO pageVO, String batchNo, String appTitle, String groupCode, String startTime, String endTime, String startState,
                             String state) {
        String sql = "select * from PreinvApply where 1=1 ";
        String sqlCount = "select count(1) from PreinvApply where 1=1 ";
        if (state.equals("0")) {
            sql += " and startState in('0','2','-3')";
            sqlCount += " and startState in('0','2','-3')";
        } else {
            sql += " and startState='1'";
            sqlCount += " and startState ='1'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (startState != null && !"".equals(startState)) {
            sql += " and startState='" + startState + "'";
            sqlCount += " and startState='" + startState + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询开票信息并分页显示
     *
     * @params
     */
    public LayuiPage findByPage(LayuiPage page, String batchNo, String appTitle, String groupCode, String startTime, String endTime,
                                String startState, String state, Integer level, SystemUser user) {
        //String sql = "select * from PreinvApply where 1=1 and CREATORID='" + user.getRowNo() + "'";
        // String sqlCount = "select count(uuid) from PreinvApply where 1=1 ";
        //sql += " and startState in('0','2')";
        try {
            String sql = "";
            if ("1".equals(state)) {
                sql += "select * from PreinvApply where 1=1 and creatorId='" + user.getRowNo() + "'";
                //sql += "and BATCHNO IN(SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW WHERE FLOWID IN(SELECT DISTINCT  FLOWID FROM PREINVAPPLYTASK where CREATORNO='" + user.getRowNo() + "'))";
                sql += " and startState in('0','1','-1','-2','-3','7','3')";
            } else if ("2".equals(state)) {
                sql += "select * from PreinvApply where 1=1 ";
                sql += "and BATCHNO IN(SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW where FLOWID IN(SELECT DISTINCT  FLOWID FROM PREINVAPPLYTASK where DEALNO='" + user.getRowNo() + "'))";
                sql += " and startState in('0','1','-1','-2','-3','7','3')";
            } else {
                sql += "select * from PreinvApply where 1=1 and HANDLER_ID='" + user.getRowNo() + "'";
                sql += " and startState in('0','2','3')";
            }
            if (!"".equals(startState) && startState != null && !"0".equals(startState)) {
                sql += " and startState ='" + startState + "'";
            }
            if (batchNo != null && !"".equals(batchNo)) {
                sql += " and batchNo='" + batchNo + "'";
                // sqlCount += " and batchNo='" + batchNo + "'";
            }
            if (appTitle != null && !"".equals(appTitle)) {
                sql += " and appTitle like '%" + appTitle + "%'";
                // sqlCount += " and appTitle like '%" + appTitle + "%'";
            }
            if (groupCode != null && !"".equals(groupCode)) {
                sql += " and groupCode='" + groupCode + "'";
                // sqlCount += " and groupCode='" + groupCode + "'";
            }
            if (endTime != null && !"".equals(endTime)) {
                sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
                // sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59"
                // + "','yyyy-MM-dd HH24:mi:ss')";
            }
            if (startTime != null && !"".equals(startTime)) {
                sql += " and createDate>=TO_DATE('" + startTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
                // sqlCount += " and startState='" + startState + "'";
            }
            //sql = getLevelSql(sql, "creatorId", null, level, user.getRowNo());PreinvApplyCorrect
            System.out.println("预开票查询SQL：" + sql);
//			System.out.println("select count(0) from (" + sql + ")");
            page.setCount(getCount("select count(0) from (" + sql + ")"));
            System.out.println("查询数据条数" + page.getCount());
            if (page.getCount() > 0) {
                //System.out.println("数据大于一条");
                Collection con = getPageList(sql, null, page);
                //System.out.println(con.size()+con.toString());
                page.setData(getPageList(sql, null, page));
            }
            return page;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 查询当前地市下所有预开票信息
     *
     * @params
     */
    public String findByPageForBatchoffice(PageVO pageVO, String branchOffice, String batchNo, String appTitle, String groupCode, String startTime,
                                           String endTime, String startState, String state) {
        String sql = "select * from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
        String sqlCount = "select count(1) from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
        if (state.equals("0")) {
            sql += " and startState in('0','2','-3')";
            sqlCount += " and startState in('0','2','-3')";
        } else {
            sql += " and startState='1'";
            sqlCount += " and startState ='1'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (startState != null && !"".equals(startState)) {
            sql += " and startState='" + startState + "'";
            sqlCount += " and startState='" + startState + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询当前地市下所有预开票信息
     *
     * @params
     */
    public LayuiPage findByPageForBatchoffice(LayuiPage page, String branchOffice, String batchNo, String appTitle, String groupCode,
                                              String startTime, String endTime, String startState, String state) {
        String sql = "select * from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
        String sqlCount = "select count(1) from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
        if (state.equals("0")) {
            sql += " and startState in('0','2','-3')";
            sqlCount += " and startState in('0','2','-3')";
        } else {
            sql += " and startState='1'";
            sqlCount += " and startState ='1'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (startState != null && !"".equals(startState)) {
            sql += " and startState='" + startState + "'";
            sqlCount += " and startState='" + startState + "'";
        }

        page.setCount(getCount(sqlCount));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询当前用户所有预开票信息
     *
     * @params
     */
    public String findByPageForUser(PageVO pageVO, String creatorId, String batchNo, String appTitle, String groupCode, String startTime,
                                    String endTime, String startState, String state) {
        String sql = "select * from PreinvApply where 1=1 and creatorId= '" + creatorId + "'";
        String sqlCount = "select count(1) from PreinvApply where 1=1 and creatorId= '" + creatorId + "'";
        if (state.equals("0")) {
            sql += " and startState in('0','2','-3')";
            sqlCount += " and startState in('0','2','-3')";
        } else {
            sql += " and startState='1'";
            sqlCount += " and startState ='1'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (startState != null && !"".equals(startState)) {
            sql += " and startState='" + startState + "'";
            sqlCount += " and startState='" + startState + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询当前用户所有预开票信息
     *
     * @params
     */
    public LayuiPage findByPageForUser(LayuiPage page, String creatorId, String batchNo, String appTitle, String groupCode, String startTime,
                                       String endTime, String startState, String state) {
        String sql = "select * from PreinvApply where 1=1 and creatorId= '" + creatorId + "'";
        String sqlCount = "select count(1) from PreinvApply where 1=1 and creatorId= '" + creatorId + "'";
        if (state.equals("0")) {
            sql += " and startState in('0','2','-3')";
            sqlCount += " and startState in('0','2','-3')";
        } else {
            sql += " and startState='1'";
            sqlCount += " and startState ='1'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (startState != null && !"".equals(startState)) {
            sql += " and startState='" + startState + "'";
            sqlCount += " and startState='" + startState + "'";
        }
        page.setCount(getCount(sqlCount));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询冲正订单信息并分页显示
     *
     * @params
     */
    public String findToPage(PageVO pageVO, String batchNo, String appTitle, String groupCode, String startTime, String endTime,
                             String reverseSatate, String status) {
        String sql = "select * from PreinvApply where 1=1 ";
        String sqlCount = "select count(1) from PreinvApply where 1=1 ";
        if (status.equals("0")) {
            sql += " and reverseSatate in('-4','4','6')";
            sqlCount += " and reverseSatate in('-4','4','6')";
        } else {
            sql += " and reverseSatate='5'";
            sqlCount += " and reverseSatate ='5'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (reverseSatate != null && !"".equals(reverseSatate)) {
            sql += " and reverseSatate='" + reverseSatate + "'";
            sqlCount += " and reverseSatate='" + reverseSatate + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询冲正订单信息并分页显示
     *
     * @params
     */
    public LayuiPage findToPage(LayuiPage page, String batchNo, String appTitle, String groupCode, String startTime, String endTime,
                                String reverseSatate, String status) {
        String sql = "";
        if ("1".equals(status)) {
            sql = "select *  from( select * from PreinvApply where 1=1 ";
            sql += "and BATCHNO in (SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW WHERE FLOWID ";
            sql += "IN(SELECT DISTINCT FLOWID FROM PREINVAPPLYTASK WHERE 1=1))";
            sql += " and reverseSatate in('5','-1','-4','7','4','6')";
        } else if ("2".equals(status)) {
            sql = "select *  from( select * from PreinvApply where 1=1 ";
            sql += "and BATCHNO in (SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW WHERE FLOWID ";
            sql += "IN(SELECT DISTINCT FLOWID FROM PREINVAPPLYTASK WHERE 1=1 ))";
            sql += " and reverseSatate in('5','-1','-4','7','4','6')";
        } else {
            sql = "select * from PreinvApply where 1=1 ";
            sql += " and reverseSatate in('4','6')";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (reverseSatate != null && !"".equals(reverseSatate)) {
            sql += " and reverseSatate='" + reverseSatate + "'";
        }
        if ("1".equals(status) || "2".equals(status)) {
            sql += ")";
        }
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询当前地市下所有冲正订单信息
     *
     * @params
     */
    public String findByPageToBatchoffice(PageVO pageVO, String branchOffice, String batchNo, String appTitle, String groupCode, String startTime,
                                          String endTime, String reverseSatate, String status) {
        String sql = "select * from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
        String sqlCount = "select count(1) from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
        if (status.equals("0")) {
            sql += " and reverseSatate in('-4','4','6')";
            sqlCount += " and reverseSatate in('-4','4','6')";
//			sql += " and reverseSatate in('-2','0')";
//			sqlCount += " and reverseSatate in('-2','0')";
        } else {
            sql += " and reverseSatate='5'";
            sqlCount += " and reverseSatate ='5'";
//			sql += " and reverseSatate in('2','-1','1')";
//			sqlCount += " and reverseSatate in('2','-1','1')";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (reverseSatate != null && !"".equals(reverseSatate)) {
            sql += " and reverseSatate='" + reverseSatate + "'";
            sqlCount += " and reverseSatate='" + reverseSatate + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询当前地市下所有冲正订单信息
     *
     * @params ********
     */
    public LayuiPage findByPageToBatchoffice(LayuiPage page, String branchOffice, String batchNo, String appTitle, String groupCode,
                                             String startTime, String endTime, String reverseSatate, String status) {
        String sql = "";
        if ("1".equals(status)) {
            sql = "select *  from( select * from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
            sql += "and BATCHNO in (SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW WHERE FLOWID ";
            sql += "IN(SELECT DISTINCT FLOWID FROM PREINVAPPLYTASK WHERE 1=1))";
            sql += " and reverseSatate in('5','-1','-4','7')";
        } else if ("2".equals(status)) {
            sql = "select *  from( select * from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
            sql += "and BATCHNO in (SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW WHERE FLOWID ";
            sql += "IN(SELECT DISTINCT FLOWID FROM PREINVAPPLYTASK WHERE 1=1 ))";
            sql += " and reverseSatate in('5','-1','-4','7')";
        } else {
            sql = "select * from PreinvApply where 1=1 and branchOffice= '" + branchOffice + "'";
            sql += " and reverseSatate in('4','6')";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (reverseSatate != null && !"".equals(reverseSatate)) {
            sql += " and reverseSatate='" + reverseSatate + "'";
        }
        if ("1".equals(status) || "2".equals(status)) {
            sql += ")";
        }
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询当前用户所有冲正订单信息
     *
     * @params
     */
    public String findByPageToUser(PageVO pageVO, String creatorId, String batchNo, String appTitle, String groupCode, String startTime,
                                   String endTime, String reverseSatate, String status) {
        String sql = "select * from PreinvApply where 1=1 and creatorId= '" + creatorId + "'";
        String sqlCount = "select count(1) from PreinvApply where 1=1 and creatorId= '" + creatorId + "'";
        if (status.equals("0")) {
            sql += " and reverseSatate in('-4','4','6')";
            sqlCount += " and reverseSatate in('-4','4','6')";
        } else {
            sql += " and reverseSatate='5'";
            sqlCount += " and reverseSatate ='5'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
            sqlCount += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
            sqlCount += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
            sqlCount += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (reverseSatate != null && !"".equals(reverseSatate)) {
            sql += " and reverseSatate='" + reverseSatate + "'";
            sqlCount += " and reverseSatate='" + reverseSatate + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询预开票申请和预开票冲正工单
     */
    public LayuiPage findToPreInvoicingList(LayuiPage page, String creatorId, String batchNo, String appTitle, String groupCode, String startTime,
                                            String endTime, String startState, String reverseSatate, String status) {
        String sql = "";
        if ("1".equals(status)) {
            //我创建
            sql += " select * from PreinvApply p where 1=1 and p.creatorId= '" + creatorId + "'";
        } else if ("2".equals(status)) {
            //我经手
            sql += "SELECT DISTINCT " +
                    " p.*  " +
                    "FROM " +
                    " PreinvApply p " +
                    " LEFT JOIN preinvapplyflow f ON p.BATCHNO = f.BATCHNO " +
                    " LEFT JOIN preinvapplytask k ON k.flowid = f.flowid  " +
                    "WHERE " +
                    " k.DEALNO = '" + creatorId + "'";
        } else {
            //我代办
//            sql += " select * from PreinvApply where 1=1 and HANDLER_ID= '" + creatorId + "'";
//            sql += " and startState in('0','2','3')";
//            sql += " OR reverseSatate in('4','6') AND reverseSatate != '5'";
            sql += "select * from PreinvApply p " +
                    "inner join preinvapplyflow f on f.batchNo=p.batchNo " +
                    "inner join preinvapplytask k on k.flowid=f.flowid " +
                    "WHERE k.state='0' AND k.dealNo= '" + creatorId + "'";
            //            sql = "SELECT * from WAITTASK WHERE (CODE='PREINVAPPLY' OR CODE='YKP') AND STATE='0' AND HANDLEUSERID= '" + creatorId + "'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and p.batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and p.appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and p.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and p.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and p.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (startState != null && !"".equals(startState)) {
            sql += " and p.startState='" + startState + "'";
        }
        if (reverseSatate != null && !"".equals(reverseSatate)) {
            sql += " and p.reverseSatate='" + reverseSatate + "'";
        }

        sql += "  ORDER BY p.CREATEDATE DESC";
        // System.out.println("预开票申请和冲正sql==" + sql);
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public LayuiPage findToPreInvoicingPhone(LayuiPage page, String creatorId) {
        String sql = "SELECT * from WAITTASK WHERE (CODE='PREINVAPPLY' OR CODE='YKP') AND STATE='0' AND HANDLEUSERID= '" + creatorId + "'";
        // System.out.println("预开票申请和冲正sql==" + sql);
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询当前用户所有冲正订单信息
     *
     * @params
     */
    public LayuiPage findByPageToUser(LayuiPage page, String creatorId, String batchNo, String appTitle, String groupCode, String startTime,
                                      String endTime, String reverseSatate, String status) {
        String sql = "";
        if ("1".equals(status)) {
            sql = "select *  from( select * from PreinvApply where 1=1 ";
            sql += "and BATCHNO in (SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW WHERE FLOWID ";
            sql += "IN(SELECT DISTINCT FLOWID FROM PREINVAPPLYTASK WHERE 1=1 and creatorId= '" + creatorId + "'))";
            sql += " and reverseSatate in('5','-1','-4','7')";
        } else if ("2".equals(status)) {
            sql = "select *  from( select * from PreinvApply where 1=1 ";
            sql += "and BATCHNO in (SELECT DISTINCT  BATCHNO FROM PREINVAPPLYFLOW WHERE FLOWID ";
            sql += "IN(SELECT DISTINCT FLOWID FROM PREINVAPPLYTASK WHERE 1=1 and DEALNO= '" + creatorId + "'))";
            sql += " and reverseSatate in('5','-1','-4','7')";
        } else {
            sql = "select * from PreinvApply where 1=1 and creatorId= '" + creatorId + "'";
            sql += " and reverseSatate in('4','6')";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and batchNo='" + batchNo + "'";
        }
        if (appTitle != null && !"".equals(appTitle)) {
            sql += " and appTitle like '%" + appTitle + "%'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (reverseSatate != null && !"".equals(reverseSatate)) {
            sql += " and reverseSatate='" + reverseSatate + "'";
        }
        if ("1".equals(status) || "2".equals(status)) {
            sql += ")";
        }
        System.out.println("预开票冲正sql==" + sql);
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public List<Object[]> getbumen(int userID) {
        String sql = "select " + "case when dn1.DEPARTMENT_LEVEL=1 " + "THEN dn1.DEPARTMENT_NAME " + "else dn2.DEPARTMENT_NAME " + "end,"
                + "as2.COMPANY_NAME," + "as2.COMPANY_IBM " + "from  AFR_SYSTEMDEPT dn1 " + "left join AFR_SYSTEMDEPT dn2 " + "on "
                + "dn1.DEPARTMENT_PARENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEMDEPT as1 " + "on " + "as1.DEPARTMENT_NO=dn2.DEPARTMENT_NO "
                + "left join AFR_SYSTEM_DEPT_USER asdu " + "on " + "asdu.DEPARTMENT_NO=dn1.DEPARTMENT_NO " + "left join AFR_SYSTEMCOMPANY as2 "
                + "on " + "as2.COMPANY_CODE=dn2.COMPANY_CODE " + "where asdu.ROWNO=?";
        Query query = getSession().createSQLQuery(sql);
        query.setInteger(0, userID);
        List<Object[]> s = query.list();
        return s;
    }

    /**
     * 根据id查询开票信息
     *
     * @params
     */
    public PreinvApply findById(String id) {
        String sql = "select * from PreinvApply where uuid=?";
        PreinvApply preinvApply = (PreinvApply) getSession().createSQLQuery(sql).addEntity(PreinvApply.class).setString(0, id).uniqueResult();
        return preinvApply;
    }

    /**
     * 根据发票编码查询账期信息
     */
    public List<Map<String, Object>> findByInvNo(String invNo) {
        String sql = "select * from ApplyCycleDet where invNo=?";
        List<Map<String, Object>> list = getSession().createSQLQuery(sql).setString(0, invNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                .list();
        return list;
    }

    public List<ApplyCycleDet> findToInvNo(String invNo) {
        String sql = "select * from ApplyCycleDet where invNo=?";
        List<ApplyCycleDet> list = getSession().createSQLQuery(sql).addEntity(ApplyCycleDet.class).setString(0, invNo).list();
        return list;
    }

    /**
     * 根据订单id 查询跟踪记录
     *
     * @param id
     * @return
     */
    public List<PreinvApplyTask> processtracking(String id) {
        String sql = "select * from PreinvApplyTask  where flowId= ? order by createDate asc";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyTask.class).setString(0, id).list();
    }

    /**
     * 获取附件消息
     */
    public List<Map<String, String>> fuJian(String id, String biaoshi) {
//        String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from PreinvApply  o  "
//                + " left join  SingleAndAttachment oa  on o.uuid=OA.orderID "
//                + " LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.uuid=? and oa.link=?";

        String sql = "select DISTINCT ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\", ah.attachmentname as \"attachmentname\",vw.EMPLOYEE_NAME as \"userName\", oa.createDate " +
                "from SingleAndAttachment oa  LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID LEFT JOIN VW_USERINFO vw on vw.ROWNO = ah.UPLOADUSER " +
                "where oa.ORDERID= ? and oa.link= ? ";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, id).setString(1, biaoshi).list();
    }

    /**
     * 根据工单编号查询流程信息
     *
     * @params
     */
    public PreinvApplyFlow findbyPreinvBatchNo(String batchNo, String state) {
        /*
         * String sql = "select * from PreinvApplyFlow where batchNo=? "; return
         * (PreinvApplyFlow)
         * getSession().createSQLQuery(sql).addEntity(PreinvApplyFlow
         * .class).setString(0, batchNo).uniqueResult();
         */
        String sql = "select * from PreinvApplyFlow where batchNo = ? ";
        return (PreinvApplyFlow) getSession().createSQLQuery(sql).addEntity(PreinvApplyFlow.class).setString(0, batchNo).list().get(0);
    }

    /**
     * 将开票数据推送至BOSS
     */
    public Map<String, Object> savePreinvApply(PreinvApply preinvApply) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        Map<String, Object> map = new HashMap<>();
        boolean flag = true;
        String message = "";
        String url = ESB_URL_172 + "sPreInvApply";
        if(isES){
            url = ESB_URL_38 + "sPreInvApply";
        }
        List<PreinvApplyDet> list = this.findByUUID(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
        String bossMessage = "";// 记录boss报文消息
        StringBuilder default_contract_no = new StringBuilder();
        if (list.size() > 1) {
            for (int a = 0;a<list.size();a++) {
                PreinvApplyDet preinvApplyDet = list.get(a);
                JSONObject object = new JSONObject();
                if (preinvApply.getOprType().equals("1")) {
                    object.put("OLD_ORDER_ID", preinvApplyDet.getOldInvNo());// 原订单流水
                }

                if (preinvApplyDet.getInvType().equals("03") || preinvApplyDet.getInvType().equals("04")) {
                    object.put("APPLY_FEE", preinvApplyDet.getInvAmout());
                }

                object.put("ORDER_ID", preinvApplyDet.getInvNo());// 订单流水
                object.put("UNIT_ID", preinvApply.getGroupCode());// 集团编码
                if (StringUtil.isNotNull(preinvApply.getTaxName())) {
                    object.put("UNIT_NAME", decryptLayers(preinvApply.getTaxName()));// 纳税人名称
                } else {
                    object.put("UNIT_NAME", decryptLayers(preinvApply.getGroupName()));// 集团名称
                }

                object.put("CONTRACT_NO", preinvApplyDet.getContrctNo());// 账户号码
                object.put("PHONE_NO", preinvApplyDet.getPhone());// 用户号码
                object.put("INV_TYPE", preinvApplyDet.getInvType());// 发票类型
                object.put("BEGIN_CYCLE", preinvApplyDet.getBeginCycle());// 账期开始年月
                object.put("END_CYCLE", preinvApplyDet.getEndCycle());// 账期结束年月
                object.put("OPR_TYPE", preinvApply.getOprType());// 状态
                object.put("LOGIN_NO", preinvApplyDet.getBossNo());// 工号
                object.put("TAX_PAYER", preinvApply.getTaxPayer());// 纳税人识别号
                object.put("TAX_ADDRESS", decryptLayers(preinvApply.getTaxAddress()));// 纳税人地址
                object.put("TAX_PHONE", preinvApply.getTaxPhone());// 纳税人电话
                object.put("TAX_BANK_NAME", preinvApply.getTaxBankName());// 纳税人银行名称
                object.put("TAX_BANK_ACCOUNT", preinvApply.getTaxBankAccount());// 纳税人银行账户
                object.put("BATCH_NO", preinvApply.getBatchNo());
                object.put("BATCH_FLAG", preinvApply.getInvoice_limit());//是否突破限制
                object.put("TAX_RATE", preinvApplyDet.getTaxRate());//税率
                object.put("EXPECT_REPAY_TIME", formatter.format(preinvApplyDet.getRecDate()));//计划回收时间
                logger.info("是否突破限制==>" + preinvApply.getInvoice_limit());

                String json= ESBReqMsgUtil.packMsgByRoute("12",preinvApplyDet.getContrctNo(),object);
                logger.info("预开票接口入参数据" + json);
                String jsonString;
                if (isES){
                    jsonString = UrlConnection.responseGBK(url, json);
                }else {
                    jsonString = CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172+"sPreInvApply", json);
                }
                JSONObject jsons = JSONObject.fromObject(jsonString);
                logger.info("预开票接口返回数据" + jsonString);

                // 调用接口成功
                if (jsons.getInt("Status") == 1) {
                    String object2 = jsons.getString("res");
                    JSONObject objects = JSONObject.fromObject(object2);
                    JSONObject root = (JSONObject) objects.get("ROOT");
                    long returnCode = root.getLong("RETURN_CODE");
                    if (returnCode != 0L) {
                        preinvApplyDet.setCommitType("1");//提交状态为失败
                        preinvApplyDet.setErrorMessage(root.getString("DETAIL_MSG"));
                        updatePreinvApplyDet(preinvApplyDet);// 修改
                    } else {
                        String str = root.getJSONObject("OUT_DATA").optString("DEFAULT_CONTRACT_NO", null);
                        if (str != null) {
                            default_contract_no.append("申请账户:" + list.get(0).getContrctNo()).append("，对应默认账户:").append(str).append("<br />");
                        }
                        preinvApplyDet.setCommitType("0");// 提交状态为成功
                        updatePreinvApplyDet(preinvApplyDet);// 修改
                    }
                }
            }

            List<PreinvApplyDet> listTwo = this.findByUUIDTwo(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
            if (listTwo.size() < list.size()) {
                map.put("flag", "YES");
                map.put("message", "操作成功");
                if (!"".equals(default_contract_no.toString())) {
                    map.put("default_contract_no", default_contract_no.toString());
                }
            } else if (listTwo.size() == list.size()) {
                map.put("flag", "NO");
                map.put("message", "操作失败");
            }
        } else {
            JSONObject object = new JSONObject();
            if (preinvApply.getOprType().equals("1")) {
                object.put("OLD_ORDER_ID", list.get(0).getOldInvNo());// 原订单流水
            }
            if (list.get(0).getInvType().equals("03") || list.get(0).getInvType().equals("04")) {
                object.put("APPLY_FEE", list.get(0).getInvAmout());
            }
            object.put("ORDER_ID", list.get(0).getInvNo());// 订单流水
            object.put("UNIT_ID", preinvApply.getGroupCode());// 集团编码
            if (StringUtil.isNotNull(preinvApply.getTaxName())) {
                object.put("UNIT_NAME", decryptLayers(preinvApply.getTaxName()));// 纳税人名称
            } else {
                object.put("UNIT_NAME", decryptLayers(preinvApply.getGroupName()));// 集团名称
            }
            object.put("CONTRACT_NO", list.get(0).getContrctNo());// 账户号码
            object.put("PHONE_NO", list.get(0).getPhone());// 用户号码
            object.put("INV_TYPE", list.get(0).getInvType());// 发票类型
            object.put("BEGIN_CYCLE", list.get(0).getBeginCycle());// 账期开始年月
            object.put("END_CYCLE", list.get(0).getEndCycle());// 账期结束年月
            object.put("OPR_TYPE", preinvApply.getOprType());// 状态
            object.put("LOGIN_NO", list.get(0).getBossNo());// 工号
            object.put("TAX_PAYER", preinvApply.getTaxPayer());// 纳税人识别号
            object.put("TAX_ADDRESS",  decryptLayers(preinvApply.getTaxAddress()));// 纳税人地址
            object.put("TAX_PHONE", preinvApply.getTaxPhone());// 纳税人电话
            object.put("TAX_BANK_NAME", preinvApply.getTaxBankName());// 纳税人银行名称
            object.put("TAX_BANK_ACCOUNT", preinvApply.getTaxBankAccount());// 纳税人银行账户
            object.put("BATCH_NO", preinvApply.getBatchNo());
            object.put("BATCH_FLAG", preinvApply.getInvoice_limit());//是否突破限制 0.否 1.是
            object.put("TAX_RATE", list.get(0).getTaxRate());//税率
            object.put("EXPECT_REPAY_TIME", formatter.format(list.get(0).getRecDate()));//计划回收时间
            logger.info("是否突破限制==>" + preinvApply.getInvoice_limit());
            String json= ESBReqMsgUtil.packMsgByRoute("12",list.get(0).getContrctNo(),object);
            logger.info("预开票接口入参数据" + json);
            String jsonString;
            if (isES){
                jsonString = UrlConnection.responseGBK(url, json);
            }else {
                jsonString = CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172+"sPreInvApply", json);
            }
            logger.info("预开票接口返回数据" + jsonString);
            JSONObject jsons = JSONObject.fromObject(jsonString);
            // 调用接口成功
            if (jsons.getInt("Status") == 1) {
                String object2 = jsons.getString("res");
                JSONObject objects = JSONObject.fromObject(object2);
                JSONObject root = (JSONObject) objects.get("ROOT");
                long returnCode = root.getLong("RETURN_CODE");
                if (returnCode != 0L) {
                    list.get(0).setCommitType("1");//提交状态为失败
                    list.get(0).setErrorMessage(root.getString("DETAIL_MSG"));
                    updatePreinvApplyDet(list.get(0));// 修改
                    map.put("flag", "NO");
                    map.put("message", root.getString("DETAIL_MSG"));
                } else {
                    String str = root.getJSONObject("OUT_DATA").optString("DEFAULT_CONTRACT_NO", null);
                    if (str != null) {
                        default_contract_no.append("申请账户:" + list.get(0).getContrctNo()).append("，对应默认账户:").append(str).append("<br />");
                        map.put("default_contract_no", default_contract_no.toString());
                    }
                    list.get(0).setCommitType("0");// 提交状态为成功
                    updatePreinvApplyDet(list.get(0));// 修改
                    map.put("flag", "YES");
                    map.put("message", "操作成功");
                }
            }
        }
        return map;
    }


    public String directLineConvergenceAccount(String contrctNo){
        JSONObject object = new JSONObject();
        object.put("CONTRACT_NO", contrctNo);
        String json= ESBReqMsgUtil.packMsgByRoute("12",contrctNo,object);
        logger.info("入参--->"+json);
        String jsonString;
        if (isES){
            jsonString = UrlConnection.responseGBK(ESB_URL_38+"com_sitech_custsvc_comp_inter_IP1124CoSvc_p1124PreBillChkL", json);
            logger.info("出参--->"+jsonString);
            return jsonString;

        }
        jsonString = CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172+"com_sitech_custsvc_comp_inter_IP1124CoSvc_p1124PreBillChkL", json);
        logger.info("出参--->"+jsonString);
        return jsonString;
    }

    /**
     * 根据申请工单编码查询账户信息
     *
     * @params
     */
    public List<PreinvApplyDet> findByUUID(String batchNo) {
        String sql = "select * from PreinvApplyDet where batchNo=? order by beginCycle";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, batchNo).list();
    }

    /**
     * 根据申请工单编码查询账户信息
     *
     * @params
     */
    public List<PreinvApplyDet> findByUUIDThree(String batchNo) {
        String sql = "select * from PreinvApplyDet where batchNo=? and commitType is null";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, batchNo).list();
    }

    /**
     * 根据账户号码查询申请工单
     *
     * @param contrctNo 账户号码
     * @return 预开票申请明细
     */
    public List<PreinvApply> findByPreinvApplyContrctNo(String contrctNo) {
        String sql = "select * from PreinvApply where batchNo in (" + "select p.batchno from PreinvApplyDet p where contrctNo=?"
                + ") and oprType='0'";
        return getSession().createSQLQuery(sql).addEntity(PreinvApply.class).setString(0, contrctNo).list();
    }

    /**
     * 查询账户信息当前状态
     *
     * @return 申请未开票 1， 申请已开票 2 已冲正 3 订单发票已开具， 且费用结清 X 冲正订单（发票状态以发票记录表为准）Y
     * 开票时，实际已结清欠费，不再走该流程
     */
    public String findPreinvApplyDetState(String groupCode, PreinvApplyDet preinvApplyDet) {
        String url = ESB_URL_172 + "sPreBillStateQry";
        if(isES){
            url = ESB_URL_38 + "sPreBillStateQry";
        }
        String message = "NO";
        JSONObject object = new JSONObject();
        object.put("ORDER_ID", preinvApplyDet.getInvNo());// 订单流水
        object.put("CONTRACT_NO", preinvApplyDet.getContrctNo());// 账户号码
        object.put("PHONE_NO", preinvApplyDet.getPhone());// 电话号码
        object.put("UNIT_ID", groupCode);// 集团编码
        String json = setParamObj(object, preinvApplyDet.getContrctNo());
        String jsonString = UrlConnection.responseGBK(url, json);
        System.out.println(jsonString);
        JSONObject jsonObject = JSONObject.fromObject(jsonString);
        if (jsonObject.getInt("Status") == 1) {
            String object2 = jsonObject.getString("res");
            System.out.println(object2);
            JSONObject objects = JSONObject.fromObject(object2);
            System.out.println(objects);
            JSONObject obj_ = (JSONObject) objects.get("ROOT");
            System.out.println(obj_);
            long returnCode = obj_.getLong("RETURN_CODE");
            JSONObject jObject = obj_.getJSONObject("OUT_DATA");
            if (returnCode == 0) {
                message = jObject.get("ORDER_STATE").toString();
            }
        } else {
            message = "BOOS接口访问失败";
        }
        return message;
    }

    /**
     * 验证开票信息是否可作废
     */
    public String verifyPreinvApply(PreinvApply preinvApply) {
        String url = ESB_URL_172 + "sPreBillStateQry";
        if(isES){
            url = ESB_URL_38 + "sPreBillStateQry";
        }
        List<PreinvApplyDet> list = this.findByUUID(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
        String message = "NO";
        for (PreinvApplyDet preinvApplyDet : list) {
            JSONObject object = new JSONObject();
            object.put("ORDER_ID", preinvApplyDet.getInvNo());// 订单流水
            object.put("CONTRACT_NO", preinvApplyDet.getContrctNo());// 账户号码
            object.put("PHONE_NO", preinvApplyDet.getPhone());// 电话号码
            object.put("UNIT_ID", preinvApply.getGroupCode());// 集团编码
            String json = setParamObj(object, preinvApplyDet.getContrctNo());
            String jsonString = UrlConnection.responseGBK(url, json);
            System.out.println(jsonString);
            JSONObject jsonObject = JSONObject.fromObject(jsonString);
            if (jsonObject.getInt("Status") == 1) {
                String object2 = jsonObject.getString("res");
                System.out.println(object2);
                JSONObject objects = JSONObject.fromObject(object2);
                System.out.println(objects);
                JSONObject obj_ = (JSONObject) objects.get("ROOT");
                System.out.println(obj_);
                long returnCode = obj_.getLong("RETURN_CODE");
                JSONObject jObject = obj_.getJSONObject("OUT_DATA");
                if (returnCode == 0) {
                    if (jObject.get("ORDER_STATE").toString().equals("0")) {
                        message = "Y";
                    } else {
                        message = "YES";
                        break;
                    }
                } else {
                    message = "YES";
                    break;
                }
            } else {
                message = "ERROR";
                break;
            }
        }
        return message;
    }

    /**
     * 检查是否可添加
     *
     * @param preinvApplyDet 预开票申请明细
     * @param groupCode      集团编码
     * @return 0-不能添加 ERROR-访问失败
     */
    public String verifyAddPreinvApply(PreinvApplyDet preinvApplyDet, String groupCode) {
        String url = ESB_URL_172 + "sPreBillStateQry";
        if(isES){
            url = ESB_URL_38 + "sPreBillStateQry";
        }

        String message = "NO";
        JSONObject object = new JSONObject();
        object.put("ORDER_ID", preinvApplyDet.getInvNo());// 订单流水
        object.put("CONTRACT_NO", preinvApplyDet.getContrctNo());// 账户号码
        object.put("PHONE_NO", preinvApplyDet.getPhone());// 电话号码
        object.put("UNIT_ID", groupCode);// 集团编码
        String json = setParamObj(object, preinvApplyDet.getContrctNo());
        logger.info("检查是否可添加入参:" + json);
        String jsonString = UrlConnection.responseGBK(url, json);
        logger.info("检查是否可添加:" + jsonString);
        JSONObject jsonObject = JSONObject.fromObject(jsonString);
        if (jsonObject.getInt("Status") == 1) {
            String object2 = jsonObject.getString("res");
            System.out.println(object2);
            JSONObject objects = JSONObject.fromObject(object2);
            System.out.println(objects);
            JSONObject obj_ = (JSONObject) objects.get("ROOT");
            System.out.println(obj_);
            long returnCode = obj_.getLong("RETURN_CODE");
            JSONObject jObject = obj_.getJSONObject("OUT_DATA");
            if (returnCode == 0) {
                if (jObject.get("ORDER_STATE").toString().equals("0")) {
                    message = "No";
                } else {
                    message = "YES";
                }
            } else {
                message = "No1";
            }
        } else {
            message = "ERROR";
        }
        return message;
    }

    /**
     * 调用boss接口查询账期信息
     */
    public String queryAccountAndTypeMatch(String contrctNo) {
        String url = ESB_URL_172 + "com_sitech_custsvc_comp_inter_IP1124CoSvc_p1124PreBillChkL";
        if(isES){
            url = ESB_URL_38 + "com_sitech_custsvc_comp_inter_IP1124CoSvc_p1124PreBillChkL";
        }
        JSONObject object = new JSONObject();
        object.put("CONTRACT_NO", Long.parseLong(contrctNo));
        String json = setParamObj(object, contrctNo);
        logger.info("入参数===>:"+json);
        String jsonString = UrlConnection.responseGBK(url, json);
        logger.info("返回参数===>:"+jsonString);
        return jsonString;
    }


    /**
     * 调用boss接口查询账期信息
     */
    public Result findApplyCycleDet(String contrctNo) {
        JSONObject object = new JSONObject();
        object.put("CONTRACT_NO", Long.parseLong(contrctNo));

        String paras=ESBReqMsgUtil.packMsgByRoute("12",contrctNo,object);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"sPreBillInfoQry",paras,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_172+"sPreBillInfoQry", paras);
        logger.info("查询账期信息返回===>:"+resultStr);
        return HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }

    /**
     * 调用boss接口查询账户信息
     */
    public String findPreinvApplyDet(String groupCode, String bossNo, String phone, String contractNo, String busiType) {
        String url = ESB_URL_172 + "sTaxpayerNumberQry";
        if(isES){
            url = ESB_URL_38 + "sTaxpayerNumberQry";
        }

        JSONObject object = new JSONObject();
        System.out.println("账户号码：" + contractNo);
        if (contractNo == null || "".equals(contractNo)) {
            object.put("LOGIN_NO", bossNo);
        } else {
            object.put("CONTRACT_NO", contractNo);
        }
        object.put("UNIT_ID", Long.parseLong(groupCode));
        object.put("BUSI_TYPE", busiType);
        String json = setParamObjBossNo(object, bossNo);
        logger.info("账户信息入参==" + json);
        String jsonString = UrlConnection.responseGBK(url, json);
        logger.info("账户信息返回==" + jsonString);
        return jsonString.toString();
    }

    public Result queryPreinvApplyDet(String groupCode, String bossNo, String phone, String contractNo, String busiType) {
        JSONObject object = new JSONObject();
        if (contractNo == null || "".equals(contractNo)) {
            object.put("LOGIN_NO", bossNo);
        } else {
            object.put("CONTRACT_NO", contractNo);
        }
        object.put("UNIT_ID", Long.parseLong(groupCode));
        object.put("BUSI_TYPE", busiType);

        String paras=ESBReqMsgUtil.packMsgByRoute("14",bossNo,object);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"sTaxpayerNumberQry",paras,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_172+"sTaxpayerNumberQry", paras);
        logger.info("账户信息返回===>:"+resultStr);
        return HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }

    public Result findPreinvApplyGroupMember(String groupCode, String bossNo,String contractNo) {
        JSONObject object = new JSONObject();
        JSONArray grpCons = new JSONArray();
        JSONObject grpConsObj = new JSONObject();
        object.put("SUB_FLAG", "1");
        object.put("CONTRACT_NO", contractNo);
        object.put("CONTRACT_TYPE", "");
        object.put("UNIT_ID",groupCode);
        grpCons .add(object);
        grpConsObj.put("GRP_CONS",grpCons);
        String paras=ESBReqMsgUtil.packMsgByRoute("14",bossNo,grpConsObj);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"com_sitech_custmng_atom_inter_IQryAcConInfoSvc_checkGrpCons",paras,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_172+"com_sitech_custmng_atom_inter_IQryAcConInfoSvc_checkGrpCons", paras);
        logger.info("返回信息===>:"+resultStr);
        return HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }

    /**
     * @Description 预开充值专票可开具税率查询
     * <AUTHOR> 
     * @param serviceNumber     服务号码
     * @param contractNo        账户号码
     * @param prodId            资费代码(可为空)
     * @return com.xinxinsoft.utils.result.Result
     * @Date 2024/01/29 14:59 
     **/
    public Result queryTaxRate(String serviceNumber,String contractNo,String prodId){
        JSONObject object = new JSONObject();
        if (prodId != null && !"".equals(prodId)) {
            object.put("PROD_ID", prodId);          //资费代码
        }

        object.put("PHONE_NO", serviceNumber);      //服务号码
        object.put("CONTRACT_NO", contractNo);      //账户号码

        String paras=ESBReqMsgUtil.packMsgByRoute("12",contractNo,object);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"com_sitech_acctmgr_deal_inter_IInvoiceTaxRate_queryTaxRate",paras,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_172+"com_sitech_acctmgr_deal_inter_IInvoiceTaxRate_queryTaxRate", paras);
        logger.info("查询账期信息返回===>:"+resultStr);
        return HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }

    /**
     * json 数据格式化：
     *
     * @param body
     * @param contractNO
     * @return
     */
    protected String setParamObj(JSONObject body, String contractNO) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "12");
        routing.put("ROUTE_VALUE", contractNO);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        System.out.println(root.toString());
        return root.toString();
    }

    protected String setParamObj1(JSONObject body, String phone) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "10");
        routing.put("ROUTE_VALUE", phone);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        System.out.println(root.toString());
        return root.toString();
    }

    /**
     * 根据用户编号查询权限id
     */
    public List findByRowNo(int rowNo) {
        String hql = "select ROLE_ID from SYSTEM_USER_ROLE where row_no=?";
        List list = getSession().createSQLQuery(hql).setInteger(0, rowNo).list();
        return list;
    }

    /**
     * 根据公司编号获取公司名称
     *
     * @param companyCode
     * @return
     */
    public String findCompanyName(String companyCode) {
        String sql = "select COMPANY_NAME from AFR_SYSTEMCOMPANY where COMPANY_CODE=?";
        return getSession().createSQLQuery(sql).setString(0, companyCode).uniqueResult().toString();
    }

    /**
     * 根据用户编号获取公司编号
     *
     * @param rowNo
     * @return
     */
    public List findCodeByRowNo(int rowNo) {
        String sql = "select COMPANY_CODE from AFR_SYSTEMDEPT  ass inner join AFR_SYSTEM_DEPT_USER asdu on ass.DEPARTMENT_NO=asdu.DEPARTMENT_NO where asdu.ROWNO=?";
        return getSession().createSQLQuery(sql).setInteger(0, rowNo).list();
    }

    /**
     * 查询所有预开票集团编码
     */
    public List<PreinvApply> findToGroupCode(String groupCode) {
        String sql = "select * from PreinvApply where oprType='0' and groupCode=?";
        return getSession().createSQLQuery(sql).addEntity(PreinvApply.class).setString(0, groupCode).list();
    }

    /**
     * 根据280和工单编号查询工单信息
     *
     * @params
     */
    public List<Map<String, Object>> findByGroup(String groupCoding, String groupName) {
        String sql = "select * from PreinvApply c where oprType=0 and startState in('1','7') and preivType !='3'";
        if (groupCoding != null && !"".equals(groupCoding)) {
            sql += " and c.groupCode like '%" + groupCoding + "%'";
        }
        if (groupName != null && !"".equals(groupName)) {
            sql += " and c.batchNo like '%" + groupName + "%'";
        }
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    // 根据工单id查询附件信息
    public List<SingleAndAttachment> findbyOrderId(String id) {
        String sql = "select  * from SingleAndAttachment where ORDERID=?";
        return getSession().createSQLQuery(sql).addEntity(SingleAndAttachment.class).setString(0, id).list();
    }

    /**
     * 根据角色查询角色下人员信息 PS：修改去重复（afs.employee_name||'('||sd.department_name||')'）
     *
     * @param id   角色ID
     * @param user 登录用户信息
     * @return
     */
    public List<Map<String, String>> SelectZtreeByUId(String id, SystemUser user) {
        if (!StringUtils.isEmpty(id)) {
            List<SystemDept> sd = user.getSystemDept();

            String code = "(";
            for (int i = 0; i < sd.size(); i++) {
                code += "'" + sd.get(i).getCompanyCode() + "',";
            }
            code += "'000')";
            String sqlone = "select * from system_role where ID =?";
            Role systemRole = (Role) this.getSession().createSQLQuery(sqlone).addEntity(Role.class).setInteger(0, Integer.valueOf(id)).uniqueResult();
            String sql = "";
            if (systemRole.getCname().contains("省公司")) {
                sql += "select distinct afs.ROWNO as \"id\",afs.employee_name as \"name\",'false' as \"isParent\" ,'false' as \"nocheck\" from AFR_systemuser afs  inner join system_user_role sur on sur.row_no=afs.rowno inner join system_role sr on sr.id=sur.role_id"
                        + "  inner join afr_system_dept_user asdu on asdu.rowno=afs.rowno "
                        + "  inner join afr_systemdept sd on sd.department_no = asdu.department_no "
                        + "  inner join afr_systemcompany sc on sc.company_code=sd.company_code  " + "  where sr.id=? and afs.employee_status='0' ";
            } else {
                sql += "select distinct afs.ROWNO as \"id\",afs.employee_name as \"name\",'false' as \"isParent\" ,'false' as \"nocheck\" from AFR_systemuser afs  inner join system_user_role sur on sur.row_no=afs.rowno inner join system_role sr on sr.id=sur.role_id"
                        + "  inner join afr_system_dept_user asdu on asdu.rowno=afs.rowno "
                        + "  inner join afr_systemdept sd on sd.department_no = asdu.department_no "
                        + "  inner join afr_systemcompany sc on sc.company_code=sd.company_code  "
                        + "  where sr.id=? and afs.employee_status='0'"
                        + "  and sc.company_code in " + code + "";
            }
            // System.out.println("查询SQL==》"+sql);
            return this.getSession().createSQLQuery(sql).addScalar("isParent", Hibernate.STRING).addScalar("id", Hibernate.STRING)
                    .addScalar("name", Hibernate.STRING).setInteger(0, Integer.valueOf(id)).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                    .list();
        } else {
            return null;
        }
    }

    /**
     * 新增导入预开票数据
     *
     * @params
     */

    public void addImportPreinvApply(List<ImportPreinvApply> list) {
        for (ImportPreinvApply importPreinvApply : list) {
            try {
                Session session = this.getSession();
                session.save(importPreinvApply);
                session.flush();
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 查询所有导入账户信息
     */

    public List<ImportPreinvApply> findByImportPreinvApply() {
        String hql = "from ImportPreinvApply";
        return getSession().createQuery(hql).list();
    }

    /**
     * 删除导入预开票信息
     */

    public void deleteImportPreinvApply(ImportPreinvApply importPreinvApply) {
        try {
            Session session = this.getSession();
            session.delete(importPreinvApply);
            session.flush();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public void deleteAll() {
        String sql = "delete from IMPORTPREINVAPPLY where 1 = 1";
        this.getSession().createSQLQuery(sql).executeUpdate();
    }

    /**
     * 查询所有账户信息
     */
    public List<PreinvApplyDet> findByPreinvApplyDets() {
        String hql = "from PreinvApplyDet";
        return getSession().createQuery(hql).list();
    }

    /**
     * 查询所有预开票信息
     */
    public List<PreinvApply> findByPreinvApply() {
        String hql = "from PreinvApply";
        return getSession().createQuery(hql).list();
    }

    /**
     * 查询所有台账信息
     *
     * @params
     */
    @Deprecated
    public String findToPreinvApplyDet(PageVO pageVO, String contractNo, String groupCode, String state, String startTime, String endTime) {
        String sql = "select pad.uuid,pad.invState,pa.BATCHNO," + "(select PAT.DEALNAME from PREINVAPPLYFLOW paf "
                + "INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID " + "where "
                + "paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司政企部经理' ) as dealName," + "(select PAT.DEALNAME from PREINVAPPLYFLOW paf "
                + "INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID where paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司领导' ) as dealName1,"
                + "pa.BRANCHOFFICE," + "pa.APPTYPE," + "PA.GROUPCODE," + "PA.GROUPNAME," + "TAXPAYER," + "PA.PHONE," + "PA.CREATORNAME,"
                + "PA.APPMEMO," + "PA.CREATORID," + "pad.INVTYPE," + "pad.PACTNAME," + "PAd.CONTRCTNO," + "PAD.CONTRCTTYPE," + "PAD.PRODUCTNAME,"
                + "PAD.RECDATE," + "pad.REALRECDATE," + "PAD.REALRECAMOUT," + "pad.INVAMOUT, " + "PAD.ISOVER ,"
                + "pa.OPRTYPE from PREINVAPPLYDET pad " + "INNER JOIN PREINVAPPLY pa on pad.BATCHNO=pa.BATCHNO where 1=1 ";
        String sqlCount = "select count(*) from PREINVAPPLYDET pad INNER JOIN PREINVAPPLY pa on pad.BATCHNO=pa.BATCHNO where 1=1 ";
        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.contractNo='" + contractNo + "'";
            sqlCount += " and pad.contractNo='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
            sqlCount += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) {
            sql += " and pad.invState='" + state + "'";
            sqlCount += " and pad.invState='" + state + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 台账查询公用sql
     * gcy 添加 字段
     * BALANCE //剩余欠缴金额
     * PAYMENT //回款状态 0--未回款，1--已回款，2--部分回款
     * ORDERSTATE //订单状态
     *
     * @return
     */
    private String getBaseSql() {
        /*
         * String sql = "select pad.uuid,pad.invState,pa.BATCHNO, pad.invNo," +
         * "(select temp from (select PAT.DEALNAME temp,rownum from PREINVAPPLYFLOW paf INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID where paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司政企部经理' ) t where  rownum =1) as dealName,"
         * +
         * "(select temp from (select PAT.DEALNAME temp,rownum from PREINVAPPLYFLOW paf INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID where paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司领导' ) t where  rownum =1) as dealName1,"
         * + "pa.BRANCHOFFICE," + "pa.APPTYPE," + "PA.GROUPCODE," +
         * "PA.GROUPNAME," + "TAXPAYER," + "PA.PHONE," + "PA.CREATORNAME," +
         * "PA.APPMEMO," + "PA.CREATORID," + "pad.INVTYPE," + "pad.PACTNAME," +
         * "PAd.CONTRCTNO," + "PAD.CONTRCTTYPE," + "PAD.PRODUCTNAME," +
         * "PAD.RECDATE," + "pad.REALRECDATE," + "PAD.REALRECAMOUT," +
         * "pad.INVAMOUT, " + "PAD.ISOVER ," +
         * "pa.OPRTYPE from PREINVAPPLYDET pad " +
         * "INNER JOIN PREINVAPPLY pa on pad.BATCHNO=pa.BATCHNO where 1=1 ";
         */

//		StringBuilder sBuilder = new StringBuilder();
//		sBuilder.append(" SELECT distinct");
//		sBuilder.append(" PAD.UUID,PAD.INVSTATE,PA.BATCHNO, PAD.INVNO, ");
//		sBuilder.append(" PAT.CREATORNAME  AS DEALNAME,  PAT2.CREATORNAME AS DEALNAME1, ");
//		sBuilder.append(" PA.BRANCHOFFICE,PA.APPTYPE,PA.GROUPCODE,PA.GROUPNAME,TAXPAYER,");
//		sBuilder.append(" PA.PHONE,PA.CREATORNAME,PA.APPMEMO,PA.CREATORID,PAD.INVTYPE,PAD.PACTNAME,PAD.CONTRCTNO,");
//		sBuilder.append(" PAD.CONTRCTTYPE,PAD.PRODUCTNAME,PAD.RECDATE,PAD.REALRECDATE,PAD.REALRECAMOUT,PAD.INVAMOUT, PAD.ISOVER ,PA.OPRTYPE ");
//		sBuilder.append(" FROM PREINVAPPLYDET PAD ");
//		sBuilder.append(" INNER JOIN PREINVAPPLY PA ON PA.BATCHNO=PAD.BATCHNO ");
//		sBuilder.append(" INNER JOIN PREINVAPPLYFLOW PAF ON PAF .BATCHNO = PAD.BATCHNO");
//		sBuilder.append("  LEFT JOIN (SELECT FLOWID, TASKNAME, CREATORNAME FROM PREINVAPPLYTASK WHERE TASKNAME = '市公司政企部经理' ) PAT  ON PAT.FLOWID = PAF.FLOWID");
//		sBuilder.append("  LEFT JOIN (SELECT FLOWID, TASKNAME, CREATORNAME FROM PREINVAPPLYTASK WHERE TASKNAME = '市公司领导' ) PAT2  ON PAT2.FLOWID = PAF.FLOWID");
//		sBuilder.append("  WHERE 1=1 ");
//		return sBuilder.toString();
        StringBuilder sBuilder = new StringBuilder();
        sBuilder.append(" SELECT distinct");
        sBuilder.append(" PAD.UUID,PAD.INVSTATE,PA.BATCHNO, PAD.INVNO, PAD.OPINVNO,PAD.BALANCE,PAD.PAYMENT,");
        sBuilder.append(" PA.BRANCHOFFICE,PA.APPTYPE,PA.GROUPCODE,PA.GROUPNAME,TAXPAYER,");
        sBuilder.append(" PAD.PHONE,PA.CREATORNAME,PA.APPMEMO,PA.CREATORID,PAD.INVTYPE,PAD.PACTNAME,PAD.CONTRCTNO, PAD.INVOICE_NO,PAD.INVOICE_CODE,PAD.OPINVDATE, ");
        sBuilder.append(" PAD.CONTRCTTYPE,PAD.PRODUCTNAME,PAD.ORDERSTATE,PAD.RECDATE,PAD.REALRECDATE,PAD.REALRECAMOUT,PAD.INVAMOUT, PAD.ISOVER ,PA.OPRTYPE, ");
        sBuilder.append(" PA.CREATEDATE,PA.PREIVTYPE,PA.COUNTRY,PAD.BOSSNO,PAD.BEGINCYCLE,PAD.ENDCYCLE,vw.BOSSUSERNAME,PAD.COMMITTYPE,PA.STARTSTATE,PA.REVERSESATATE ");
        sBuilder.append(" FROM PREINVAPPLYDET PAD ");
        sBuilder.append(" INNER JOIN PREINVAPPLY PA ON PA.BATCHNO=PAD.BATCHNO ");
        sBuilder.append(" INNER JOIN PREINVAPPLYFLOW PAF ON PAF .BATCHNO = PAD.BATCHNO");
        sBuilder.append(" INNER JOIN VW_USERINFO vw on vw.ROWNO= PA.CREATORID ");
        sBuilder.append("  WHERE 1=1 ");
        return sBuilder.toString();
    }

    /**
     * 查询所有台账信息(2)
     * 正在使用
     *
     * @params
     */
    public LayuiPage findToPreinvApplyDet(LayuiPage page, String contractNo, String groupCode, String state, String startTime, String endTime,
                                          String invNo, String batchNo) {
        String sql = getBaseSql();

        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.CONTRCTNO='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) { //订单状态
            //PAD.ORDERSTATE
            if ("0".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("1".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("2".equals(state)) {
                sql += " and (pad.ORDERSTATE='2' or pad.ORDERSTATE='X') ";
            } else if ("3".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            }

        }
        if (invNo != null && !"".equals(invNo)) {
            sql += " and pad.invNo = '" + invNo + "'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and pa.BATCHNO = '" + batchNo + "'";
        }
        sql += " ORDER BY PA.CREATEDATE DESC ";
        System.out.println("预开票查询所有台账信息sql==" + sql);
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询当前地市下所有台账信息
     */
    @Deprecated
    public String findToPreinvApplyDetForBatchoffice(PageVO pageVO, String batchOffice, String contractNo, String groupCode, String state,
                                                     String startTime, String endTime) {
        String sql = "select pad.uuid,pad.invState,pa.BATCHNO,(select PAT.DEALNAME from PREINVAPPLYFLOW paf INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID where paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司政企部经理' ) as dealName,(select PAT.DEALNAME from PREINVAPPLYFLOW paf INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID where paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司领导' ) as dealName1,pa.BRANCHOFFICE,pa.APPTYPE,PA.GROUPCODE,PA.GROUPNAME,TAXPAYER,PA.PHONE,PA.CREATORNAME,PA.APPMEMO,PA.CREATORID,pad.INVTYPE,"
                + "pad.PACTNAME,PAd.CONTRCTNO,PAD.CONTRCTTYPE,PAD.PRODUCTNAME,PAD.RECDATE,pad.REALRECDATE,PAD.REALRECAMOUT,pad.INVAMOUT, PAD.ISOVER ,pa.OPRTYPE from PREINVAPPLYDET pad INNER JOIN PREINVAPPLY pa on pad.BATCHNO=pa.BATCHNO where 1=1 ";
        String sqlCount = "select count(*) from PREINVAPPLYDET pad INNER JOIN PREINVAPPLY pa on pad.BATCHNO=pa.BATCHNO where pa.branchOffice= '"
                + batchOffice + "'";

        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.contractNo='" + contractNo + "'";
            sqlCount += " and pad.contractNo='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
            sqlCount += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) {
            sql += " and pad.invState='" + state + "'";
            sqlCount += " and pad.invState='" + state + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询当前地市下所有台账信息(2)
     */

    public LayuiPage findToPreinvApplyDetForBatchoffice(LayuiPage page, String batchOffice, String contractNo, String groupCode, String state,
                                                        String startTime, String endTime, String invNo, String batchNo) {

        String sql = getBaseSql();

        sql += " and pa.branchOffice= '" + batchOffice + "' ";
        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.CONTRCTNO='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) { //订单状态
            //PAD.ORDERSTATE
            if ("0".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("1".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("2".equals(state)) {
                sql += " and (pad.ORDERSTATE='2' or pad.ORDERSTATE='X') ";
            } else if ("3".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            }

        }
        if (invNo != null && !"".equals(invNo)) {
            sql += " and pad.invNo = '" + invNo + "'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and pa.batchNo = '" + batchNo + "'";
        }
        sql += " ORDER BY PA.CREATEDATE DESC ";
        System.out.println("预开票地市台账sql==" + sql);
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询当前用户所有台账信息
     */
    @Deprecated
    public String findToPreinvApplyDetForUser(PageVO pageVO, String creatorId, String contractNo, String groupCode, String state, String startTime,
                                              String endTime) {
        String sql = "select pad.uuid,pad.invState,pa.BATCHNO,(select PAT.DEALNAME from PREINVAPPLYFLOW paf INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID where paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司政企部经理' ) as dealName,(select PAT.DEALNAME from PREINVAPPLYFLOW paf INNER JOIN PREINVAPPLYTASK pat on PAF.FLOWID=PAT.FLOWID where paf.BATCHNO=pa.BATCHNO and pat.TASKNAME='市公司领导' ) as dealName1,pa.BRANCHOFFICE,pa.APPTYPE,PA.GROUPCODE,PA.GROUPNAME,TAXPAYER,PA.PHONE,PA.CREATORNAME,PA.APPMEMO,PA.CREATORID,pad.INVTYPE,"
                + "pad.PACTNAME,PAd.CONTRCTNO,PAD.CONTRCTTYPE,PAD.PRODUCTNAME,PAD.RECDATE,pad.REALRECDATE,PAD.REALRECAMOUT,pad.INVAMOUT, PAD.ISOVER ,pa.OPRTYPE from PREINVAPPLYDET pad INNER JOIN PREINVAPPLY pa on pad.BATCHNO=pa.BATCHNO where 1=1 ";
        String sqlCount = "select count(*) from PREINVAPPLYDET pad INNER JOIN PREINVAPPLY pa on pad.BATCHNO=pa.BATCHNO where pa.creatorId= '"
                + creatorId + "'";
        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.contractNo='" + contractNo + "'";
            sqlCount += " and pad.contractNo='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
            sqlCount += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
            sqlCount += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
            sqlCount += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) {
            sql += " and pad.invState='" + state + "'";
            sqlCount += " and pad.invState='" + state + "'";
        }
        Query query = getSession().createSQLQuery(sql);
        Query queryTwo = getSession().createSQLQuery(sqlCount);
        int i = Integer.valueOf(queryTwo.uniqueResult().toString());
        pageVO.setRecords(i);
        pageVO.setRows(query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult((pageVO.getPage() - 1) * pageVO.getPageSize())
                .setMaxResults(pageVO.getPageSize()).list());
        PageResponse pageResponse = new PageResponse(pageVO.getPage(), pageVO.getTotal(), pageVO.getRecords(), pageVO.getRows());
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(pageResponse);
    }

    /**
     * 查询当前用户所有台账信息(2)
     */
    public LayuiPage findToPreinvApplyDetForUser(LayuiPage page, String creatorId, String contractNo, String groupCode, String state,
                                                 String startTime, String endTime, String invNo, String batchNo) {

        String sql = getBaseSql();
        sql += " and pa.creatorId= '" + creatorId + "' ";
        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.CONTRCTNO='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) { //订单状态
            //PAD.ORDERSTATE
            if ("0".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("1".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("2".equals(state)) {
                sql += " and (pad.ORDERSTATE='2' or pad.ORDERSTATE='X') ";
            } else if ("3".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            }

        }
        if (invNo != null && !"".equals(invNo)) {
            sql += " and pad.invNo = '" + invNo + "'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and pa.batchNo = '" + batchNo + "'";
        }
        sql += " ORDER BY PA.CREATEDATE DESC ";
        System.out.println("预开票个人台账信息sql==" + sql);
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 查询所有台账信息
     */

    public List<Map<String, Object>> findAll(String contractNo, String groupCode, String state, String startTime, String endTime, String invNo
            , String batchNo) {

        String sql = getBaseSql();

        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.contrctNo='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) { //订单状态
            //PAD.ORDERSTATE
            if ("0".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("1".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("2".equals(state)) {
                sql += " and (pad.ORDERSTATE='2' or pad.ORDERSTATE='X') ";
            } else if ("3".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            }

        }
        if (invNo != null && !"".equals(invNo)) {
            sql += " and pad.invNo = '" + invNo + "'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and pa.batchNo = '" + batchNo + "'";
        }
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 查询地市下所有台账信息
     *
     * @params
     */
    public List<Map<String, Object>> findAllByBranoffice(String batchOffice, String contractNo, String groupCode, String state, String startTime,
                                                         String endTime, String invNo, String batchNo) {

        String sql = getBaseSql();
        sql += " and pa.branchOffice= '" + batchOffice + "' ";
        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.contrctNo='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) { //订单状态
            //PAD.ORDERSTATE
            if ("0".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("1".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("2".equals(state)) {
                sql += " and (pad.ORDERSTATE='2' or pad.ORDERSTATE='X') ";
            } else if ("3".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            }

        }
        if (invNo != null && !"".equals(invNo)) {
            sql += " and pad.invNo = '" + invNo + "'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and pa.batchNo = '" + batchNo + "'";
        }
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 查询当前用户所有台账信息
     *
     * @params
     */
    public List<Map<String, Object>> findAllByCreatorId(String creatorId, String contractNo, String groupCode, String state, String startTime,
                                                        String endTime, String invNo, String batchNo) {

        String sql = getBaseSql();
        sql += " and pa.creatorId= '" + creatorId + "' ";
        if (contractNo != null && !"".equals(contractNo)) {
            sql += " and pad.contrctNo='" + contractNo + "'";
        }
        if (groupCode != null && !"".equals(groupCode)) {
            sql += " and pa.groupCode='" + groupCode + "'";
        }
        if (startTime != null && !"".equals(startTime)) {
            sql += " and pa.createDate>=TO_DATE('" + startTime + "','yyyy-MM-dd')";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " and pa.createDate<=TO_DATE('" + endTime + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss')";
        }
        if (state != null && !"".equals(state)) { //订单状态
            //PAD.ORDERSTATE
            if ("0".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("1".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            } else if ("2".equals(state)) {
                sql += " and (pad.ORDERSTATE='2' or pad.ORDERSTATE='X') ";
            } else if ("3".equals(state)) {
                sql += " and pad.ORDERSTATE='" + state + "'";
            }

        }
        if (invNo != null && !"".equals(invNo)) {
            sql += " and pad.invNo = '" + invNo + "'";
        }
        if (batchNo != null && !"".equals(batchNo)) {
            sql += " and pa.batchNo = '" + batchNo + "'";
        }
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 下载为excel文件
     *
     * @throws IOException
     * @throws WriteException
     * @throws ParseException
     * @params
     */

    public void uploadExcel(List<Map<String, Object>> mapList) throws IOException, WriteException, ParseException {
        HttpServletResponse response = ServletActionContext.getResponse();
        String excelFile = FileUpload.getFtpURL() + "exportExcelToJxl.xls";
        File file = new File(FileUpload.getFtpURL());
        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        String exportName = "ExportData_" + FileUpload.getDateToString("yyyyMMdd");
        try {
            // 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
            WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
            // 2、新建工作表(sheet)对象，并声明其属于第几页
            WritableSheet firstSheet = writeBook.createSheet("预开票台账信息", 1);// 第一个参数为工作簿的名称，第二个参数为页数

            String[] headers = new String[]{"申请工单编码", "账户流水", "集团编码", "集团名称", "分公司", "区县", "申请时间",
                    "客户经理", "客户经理工号", "业务类型", "发票类型", "开始账期", "结束账期", "合同名称", "账户ID", "服务号码",
                    "业务号码", "账户类型", "产品名称", "发票状态", "订单状态", "开票金额", "实际回款金额", "剩余欠缴金额",

                    "发票号码", "发票代码", "开票时间", "计划回款日期",
                    "实际回款日期", "逾期天数", "申请原因", "开票工号", "逾期原因", "工单类别", "工单状态", "推送状态", "申请类型"};//订单状态20 24 25 26
            for (int i = 0; i < headers.length; i++) {
                // 3、创建单元格(Label)对象
                Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
                WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE,
                        jxl.format.Colour.BLACK); // 定义格式
                WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE,
                        jxl.format.Colour.BLACK); // 定义格式
                // 标题栏 // 颜色
                WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
                wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
                wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
                wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfTitle.setAlignment(Alignment.CENTRE); // 设置垂直对齐
                // 内容栏
                WritableCellFormat wcfContent = new WritableCellFormat(wf3);
                wcfContent.setVerticalAlignment(VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐

                CellView navCellView = new CellView();
                navCellView.setSize(80 * 50);

                label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
                firstSheet.setColumnView(i, navCellView); // 设置col显示样式
                firstSheet.setRowView(i, 400, false); // 设置行高
                firstSheet.addCell(label0);

                if (mapList.size() > 0) {
                    for (int i1 = 0; i1 < mapList.size(); i1++) {
                        if (mapList.get(i1).get("BATCHNO") != null) {// 申请工单编码
                            Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("BATCHNO")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(0, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("INVNO") != null) {// 账户流水
                            Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("INVNO")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(1, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("GROUPCODE") != null) {// 集团编码
                            Label label = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPCODE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(2, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("GROUPNAME") != null) {// 集团名称
                            Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPNAME")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(3, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("BRANCHOFFICE") != null) {// 分公司
                            Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("BRANCHOFFICE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(4, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("COUNTRY") != null) {// 区县
                            Label label = new Label(5, i1 + 1, String.valueOf(mapList.get(i1).get("COUNTRY")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(5, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CREATEDATE") != null) {// 申请入表时间（申请预开票订单时间）
                            Label label = new Label(6, i1 + 1, substringDate(String.valueOf(mapList.get(i1).get("CREATEDATE"))), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(6, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CREATORNAME") != null) {// 客户经理
                            Label label = new Label(7, i1 + 1, String.valueOf(mapList.get(i1).get("CREATORNAME")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(7, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("BOSSUSERNAME") != null) {// 客户经理工号
                            Label label = new Label(8, i1 + 1, String.valueOf(mapList.get(i1).get("BOSSUSERNAME")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(8, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("APPTYPE") != null) {// 业务类型
                            Label label = new Label(9, i1 + 1, String.valueOf(mapList.get(i1).get("APPTYPE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(9, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("INVTYPE") != null) {// 发票类型
                            String state = mapList.get(i1).get("INVTYPE").toString();
                            String value = "";
                            if ("01".equals(state)) {
                                value = "增值税电子发票";
                            } else if ("02".equals(state)) {
                                value = "增值税专用发票";
                            } else if ("03".equals(state)) {
                                value = "未缴费未出账电子发票";
                            } else if ("04".equals(state)) {
                                value = "专线预开增值税预存发票";
                            }
                            Label label = new Label(10, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(10, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("BEGINCYCLE") != null) {// 开始账期
                            Label label = new Label(11, i1 + 1, String.valueOf(mapList.get(i1).get("BEGINCYCLE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(11, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("ENDCYCLE") != null) {// 结束账期
                            Label label = new Label(12, i1 + 1, String.valueOf(mapList.get(i1).get("ENDCYCLE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(12, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("PACTNAME") != null) {// 合同名称
                            Label label = new Label(13, i1 + 1, String.valueOf(mapList.get(i1).get("PACTNAME")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(13, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CONTRCTNO") != null) {// 账户ID
                            Label label = new Label(14, i1 + 1, String.valueOf(mapList.get(i1).get("CONTRCTNO")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(14, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("PHONE") != null) {// 服务号码
                            Label label = new Label(15, i1 + 1, String.valueOf(mapList.get(i1).get("PHONE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(15, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("PHONE") != null) {// 业务号码String.valueOf(mapList.get(i1).get("PHONE"))
                            Label label = new Label(16, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(16, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CONTRCTTYPE") != null) {// 账户类型
                            Label label = new Label(17, i1 + 1, String.valueOf(mapList.get(i1).get("CONTRCTTYPE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(17, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("PRODUCTNAME") != null) {// 产品名称
                            Label label = new Label(18, i1 + 1, String.valueOf(mapList.get(i1).get("PRODUCTNAME")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(18, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("INVSTATE") != null) {// 发票处理状态
                            if (mapList.get(i1).get("INVSTATE").toString().equals("0")) {
                                Label label = new Label(19, i1 + 1, "未开票", wcfContent);
                                firstSheet.addCell(label);
                            } else if (mapList.get(i1).get("INVSTATE").toString().equals("1")) {
                                Label label = new Label(19, i1 + 1, "已开票", wcfContent);
                                firstSheet.addCell(label);
                            } else if (mapList.get(i1).get("INVSTATE").toString().equals("2")) {
                                Label label = new Label(19, i1 + 1, "已冲销", wcfContent);
                                firstSheet.addCell(label);
                            } else if (mapList.get(i1).get("INVSTATE").toString().equals("3")) {
                                Label label = new Label(19, i1 + 1, "已结清", wcfContent);
                                firstSheet.addCell(label);
                            }
                        } else {
                            Label label = new Label(19, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("ORDERSTATE") != null) {// 订单状态
                            if (mapList.get(i1).get("ORDERSTATE").toString().equals("0")) {
                                Label label = new Label(20, i1 + 1, "已申请未开具", wcfContent);
                                firstSheet.addCell(label);
                            } else if (mapList.get(i1).get("ORDERSTATE").toString().equals("1")) {
                                Label label = new Label(20, i1 + 1, "已开具未结清", wcfContent);
                                firstSheet.addCell(label);
                            } else if (mapList.get(i1).get("ORDERSTATE").toString().equals("2") || mapList.get(i1).get("ORDERSTATE").toString().equals("X")) {
                                Label label = new Label(20, i1 + 1, "已冲正", wcfContent);
                                firstSheet.addCell(label);
                            } else if (mapList.get(i1).get("ORDERSTATE").toString().equals("3")) {
                                Label label = new Label(20, i1 + 1, "已结清", wcfContent);
                                firstSheet.addCell(label);
                            }
                        } else {
                            Label label = new Label(20, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("INVAMOUT") != null) {// 开票金额
                            Label label = new Label(21, i1 + 1, String.valueOf(mapList.get(i1).get("INVAMOUT")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(21, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("REALRECAMOUT") != null) {// 实际回款金额
                            Label label = new Label(22, i1 + 1, String.valueOf(mapList.get(i1).get("REALRECAMOUT")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(22, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("INVAMOUT") != null && mapList.get(i1).get("REALRECAMOUT") != null) {// 剩余欠缴金额
                            Double amout = Double.valueOf(mapList.get(i1).get("INVAMOUT").toString());// 开票金额
                            Double reAmout = Double.valueOf(mapList.get(i1).get("REALRECAMOUT").toString());// 实际回收金额
                            if (amout - reAmout < 0) {
                                Label label = new Label(23, i1 + 1, "无", wcfContent);
                                firstSheet.addCell(label);
                            } else {
                                Label label = new Label(23, i1 + 1, String.valueOf(amout - reAmout), wcfContent);
                                firstSheet.addCell(label);
                            }
                        } else {
                            Label label = new Label(23, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("INVOICE_NO") != null) { //发票号码
                            Label label = new Label(24, i1 + 1, String.valueOf(mapList.get(i1).get("INVOICE_NO")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(24, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("INVOICE_CODE") != null) { //发票代码
                            Label label = new Label(25, i1 + 1, String.valueOf(mapList.get(i1).get("INVOICE_CODE")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(25, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("OPINVDATE") != null) { //开票时间
                            Label label = new Label(26, i1 + 1, substringDate(String.valueOf(mapList.get(i1).get("OPINVDATE"))), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(26, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("RECDATE") != null) {// 计划回款日期
                            Label label = new Label(27, i1 + 1, substringDate(String.valueOf(mapList.get(i1).get("RECDATE"))), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(27, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("REALRECDATE") != null) {// 实际回款日期
                            Label label = new Label(28, i1 + 1, substringDate(String.valueOf(mapList.get(i1).get("REALRECDATE"))), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(28, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("RECDATE") != null && mapList.get(i1).get("REALRECDATE") != null) {// 预期天数
                            Date recDate = formatForDate(mapList.get(i1).get("RECDATE").toString());// 计划回收时间
                            Date realrecdate = formatForDate(mapList.get(i1).get("REALRECDATE").toString());// 实际回收时间
                            Label label = new Label(29, i1 + 1, differentDaysByMillisecond(recDate, realrecdate), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(29, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("APPMEMO") != null) {// 申请原因
                            Label label = new Label(30, i1 + 1, String.valueOf(mapList.get(i1).get("APPMEMO")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(30, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("OPINVNO") != null) {// 开票人工号
                            Label label = new Label(31, i1 + 1, String.valueOf(mapList.get(i1).get("OPINVNO")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(31, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("OVERDUEMEMO") != null) {// 逾期原因
                            Label label = new Label(32, i1 + 1, String.valueOf(mapList.get(i1).get("OVERDUEMEMO")), wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(32, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("OPRTYPE") != null) {// 工单类别
                            String state = mapList.get(i1).get("OPRTYPE").toString();
                            String value = "";
                            if ("0".equals(state)) {
                                value = "使用工单";
                            } else if ("1".equals(state)) {
                                value = "冲正工单";
                            } else {
                                value = "";
                            }
                            Label label = new Label(33, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(33, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("OPRTYPE") != null) {// 工单状态
                            String state = mapList.get(i1).get("OPRTYPE").toString();
                            String value = "";
                            if ("0".equals(state)) {
                                if (mapList.get(i1).get("STARTSTATE") != null) {
                                    if ("0".equals(mapList.get(i1).get("STARTSTATE").toString()) || "2".equals(mapList.get(i1).get("STARTSTATE").toString())) {
                                        value = "审批中";
                                    } else if ("1".equals(mapList.get(i1).get("STARTSTATE").toString())) {
                                        value = "已完成";
                                    } else if ("-1".equals(mapList.get(i1).get("STARTSTATE").toString())) {
                                        value = "作废";
                                    } else if ("-3".equals(mapList.get(i1).get("STARTSTATE").toString()) || "7".equals(mapList.get(i1).get("STARTSTATE").toString())) {
                                        value = "部分成功";
                                    } else if ("3".equals(mapList.get(i1).get("STARTSTATE").toString())) {
                                        value = "审批完成待推送";
                                    }
                                }
                            } else if ("1".equals(state)) {
                                if (mapList.get(i1).get("REVERSESATATE") != null) {
                                    if ("4".equals(mapList.get(i1).get("REVERSESATATE").toString()) || "6".equals(mapList.get(i1).get("REVERSESATATE").toString())) {
                                        value = "审批中";
                                    } else if ("5".equals(mapList.get(i1).get("REVERSESATATE").toString())) {
                                        value = "已完成";
                                    } else if ("-1".equals(mapList.get(i1).get("REVERSESATATE").toString())) {
                                        value = "作废";
                                    } else if ("-4".equals(mapList.get(i1).get("REVERSESATATE").toString()) || "7".equals(mapList.get(i1).get("REVERSESATATE").toString())) {
                                        value = "部分成功";
                                    }
                                }
                            } else {
                                value = "";
                            }
                            Label label = new Label(34, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(34, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("COMMITTYPE") != null) {// 推送状态
                            String state = mapList.get(i1).get("COMMITTYPE").toString();
                            String value = "";
                            if ("0".equals(state)) {
                                value = "成功";
                            } else if ("1".equals(state)) {
                                value = "失败";
                            } else {
                                value = "未推送";
                            }
                            Label label = new Label(35, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(35, i1 + 1, "未推送", wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("PREIVTYPE") != null) {// 发票类型
                            Object obj = mapList.get(i1).get("PREIVTYPE");
                            String preivType = "";
                            if (null != obj) {
                                preivType = obj.toString();
                            }
                            String value = "";
                            if ("1".equals(preivType)) {
                                value = "普通发票";
                            } else if ("2".equals(preivType)) {
                                value = "物联网预开票";
                            } else if ("3".equals(preivType)) {
                                value = "有价卡";
                            } else {
                                value = "";
                            }
                            Label label = new Label(36, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        } else {
                            Label label = new Label(36, i1 + 1, "", wcfContent);
                            firstSheet.addCell(label);
                        }
                    }
                }
            }
            // 4、打开流，开始写文件
            writeBook.write();
            // 5、关闭流
            writeBook.close();

            byte[] data = FileUtil.toByteArray2(excelFile);
            String fileName = URLEncoder.encode(exportName, "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xls" + "\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
            File fe = new File(excelFile);
            fe.delete();
        } finally {
            File fe = new File(excelFile);
            if (file.exists() && file.isDirectory()) {
                fe.delete();
            }
        }
    }

    /**
     * 计算两个时间相差天数
     */
    public String differentDaysByMillisecond(Date date1, Date date2) {
        int days = (int) ((date2.getTime() - date1.getTime()) / (1000 * 3600 * 24));
        return String.valueOf(days);
    }

    /**
     * 日期转换2
     *
     * @param strDate
     * @return
     * @throws ParseException
     */
    public Date formatForDate(String strDate) throws ParseException {
        Date date = null;
        if (strDate != null && !"".equals(strDate)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = format.parse(strDate);
        }
        return date;
    }

    /**
     * 日期转换
     *
     * @params
     */
    public static String substringDate(String date) {
        String newDate = date.substring(0, 10);
        return newDate;
    }

    /**
     * 查询冲正订单
     *
     * @params
     */
    public Object findbyOldid(String id) {
        String sql = "select count(1) from preinvapply where oldid=? and reverseSatate=5";
        return getSession().createSQLQuery(sql).setString(0, id).uniqueResult();
    }

    /**
     * 查询提交成功的账户信息
     */
    public List<PreinvApplyDet> commitPreinvapplyDet(String batchNo) {
        String sql = "select * from preinvapplydet where batchno=? and commitType='0'";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, batchNo).list();
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatetwo(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 根据uuid查询账户信息
     */
    public PreinvApplyDet findPreinvApplyDetByUUid(String id) {
        String sql = "from PreinvApplyDet where uuid=?";
        return (PreinvApplyDet) getSession().createQuery(sql).setString(0, id).uniqueResult();
    }

    /**
     * 根据账户号码查询账期信息
     */
    public List<ApplyCycleDet> findByContrctNo(String contrctNo, String batchNo) {
        String sql = "select * from ApplyCycleDet where invno in(select invno  from PreinvApplyDet where contrctNo=? and batchNo=?)";
        return getSession().createSQLQuery(sql).addEntity(ApplyCycleDet.class).setString(0, contrctNo).setString(1, batchNo).list();
    }

    /**
     * 查询所有未冲正开票信息
     */
    public List<PreinvApplyDet> findByNotInvalid() {
        String sql = "select * from preinvapplydet where batchno in(select batchno from preinvapply where oldId is null)";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).list();
    }

    public Map<String, Object> reverseInvNo(PreinvApply preinvApply, PreinvApplyDet preinvApplyDet) {
        Map<String, Object> map = new HashMap();
        String flag = "YES";
        String message = "";
        String url = ESB_URL_172 + "sPreInvApply";
        if(isES){
            url = ESB_URL_38 + "sPreInvApply";
        }
        JSONObject object = new JSONObject();
        object.put("OLD_ORDER_ID", preinvApplyDet.getOldInvNo());
        if (preinvApplyDet.getInvType().equals("03")) {
            object.put("APPLY_FEE", preinvApplyDet.getInvAmout());
        }
        object.put("ORDER_ID", preinvApplyDet.getInvNo());
        object.put("UNIT_ID", preinvApply.getGroupCode());
        if (StringUtil.isNotNull(preinvApply.getTaxName())) {
            object.put("UNIT_NAME", decryptLayers(preinvApply.getTaxName()));// 纳税人名称
        } else {
            object.put("UNIT_NAME", decryptLayers(preinvApply.getGroupName()));// 集团名称
        }
        object.put("CONTRACT_NO", preinvApplyDet.getContrctNo());
        object.put("PHONE_NO", preinvApplyDet.getPhone());
        object.put("INV_TYPE", preinvApplyDet.getInvType());
        object.put("BEGIN_CYCLE", preinvApplyDet.getBeginCycle());
        object.put("END_CYCLE", preinvApplyDet.getEndCycle());
        object.put("OPR_TYPE", "1");
        object.put("LOGIN_NO", preinvApplyDet.getBossNo());
        object.put("TAX_PAYER", preinvApply.getTaxPayer());
        object.put("TAX_ADDRESS", decryptLayers(preinvApply.getTaxAddress()));
        object.put("TAX_PHONE", preinvApply.getTaxPhone());
        object.put("TAX_BANK_NAME", preinvApply.getTaxBankName());
        object.put("TAX_BANK_ACCOUNT", preinvApply.getTaxBankAccount());
        object.put("BATCH_NO", preinvApply.getBatchNo());
        String json = this.setParamObj(object, preinvApplyDet.getContrctNo());
        System.out.println("发票冲正：" + json);
        String jsonString = UrlConnection.responseGBK(url, json);
        System.out.println("发票冲正" + jsonString);
        JSONObject jsons = JSONObject.fromObject(jsonString);
        if (jsons.getInt("Status") == 1) {
            System.out.println(jsons.toString());
            String object2 = jsons.getString("res");
            System.out.println(object2.toString());
            JSONObject objects = JSONObject.fromObject(object2);
            System.out.println(objects);
            JSONObject root = (JSONObject) objects.get("ROOT");
            System.out.println(root.toString());
            long returnCode = root.getLong("RETURN_CODE");
            System.out.println(returnCode);
            if (returnCode != 0L) {
                preinvApplyDet.setCommitType("1");
                message = "账户号码为:" + preinvApplyDet.getContrctNo() + "的信息提交BOSS失败,失败原因为：" + root.getString("DETAIL_MSG");
                flag = "Y";
                this.updatePreinvApplyDet(preinvApplyDet);
            } else {
                preinvApplyDet.setCommitType("0");
                this.updatePreinvApplyDet(preinvApplyDet);
            }
        } else {
            flag = "ERROR";
        }

        if ("Y".equals(flag)) {
            map.put("flag", flag);
            map.put("message", message);
        } else if ("ERROR".equals(flag)) {
            map.put("flag", flag);
            map.put("message", "访问BOSS接口失败");
        } else {
            map.put("flag", flag);
            map.put("message", "操作成功");
        }

        System.out.println(JSONHelper.Serialize(map));
        return map;
    }

    public PreinvApplyDet findForInvNo(String invNo) {
        String sql = "select * from preinvapplyDet where invno=?";
        return (PreinvApplyDet) this.getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, invNo).uniqueResult();
    }

    public List<PreinvApplyDet> findForInvNoList(String invNo) {
        String sql = "select * from preinvapplyDet where invno=?";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, invNo).list();
    }

    /**
     * 存储过程更新账户信息
     *
     * @return
     */
    public Integer updatePreinvApplyDetByProc() {
        return this.getSession().createSQLQuery("{Call syn_PreinvApplyDet()}").executeUpdate();
    }

    /**
     * 根据账户uuid查询账户信息
     *
     * @param jsonArray JSONArray对象
     * @return
     */
    public List<PreinvApplyDet> findApplyDetsByUuid(JSONArray jsonArray) {
        StringBuilder sBuilder = new StringBuilder();
        sBuilder.append("select * from preinvApplyDet where uuid in (");
        for (Object json : jsonArray) {
            sBuilder.append("'");
            sBuilder.append(json.toString());
            sBuilder.append("',");
        }
        String sql = sBuilder.substring(0, sBuilder.length() - 1);
        sql += ")";

        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).list();

    }

    /**
     * 根据登录人名称以及状态查询待办信息
     *
     * @param name 登录人名称
     * @param page PageRequest对象
     * @return
     */
    public PageResponse findByLoginName(String name, PageRequest page) {
        String sql = "select * from (select name,waitid,taskid,createUserName,creationTime,'HANDLEPREINVAPPLY' as typey  from WaitTask where handleLoginName='"
                + name
                + "' and state=0 and code='PREINVAPPLY' and URL like '%handlePreinvApply%' "
                + "union all select name,waitid,taskid,createUserName,creationTime,'COMPLETEPREINVAPPLY' as typey from WaitTask where handleLoginName='"
                + name + "' and state=0 and code='PREINVAPPLY' and URL like '%completePreinvApplay%')";
        return getMap(sql, page);
    }

    /**
     * 查询任务id
     *
     * @param id     流程ID
     * @param userid 处理人工号
     */
    public PreinvApplyTask getPreinvApplyTaskById(String flowId, int userId) {
        String sql = "select * from preinvApplytask where flowId=? and dealNo=? and state='0'";
        PreinvApplyTask preinvApplyTask = (PreinvApplyTask) this.getSession().createSQLQuery(sql).addEntity(PreinvApplyTask.class)
                .setString(0, flowId).setInteger(1, userId).uniqueResult();
        return preinvApplyTask;
    }

    /**
     * 根据工单编号查询预开票工单
     *
     * @param batchNo
     * @return
     */
    @SuppressWarnings("unchecked")
    public Map<String, Object> findByBatchNo(String batchNo) {
        String sql = "select * from preinvApply where batchNo = ?";
        return (Map<String, Object>) getSession().createSQLQuery(sql).setString(0, batchNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                .list().get(0);

    }

    /**
     * 根据角色名获取角色信息
     *
     * @param rolename
     * @return
     */
    public Role getRoleName(String rolename) {
        String sqlone = "select * from system_role where name =?";
        Role systemRole = (Role) this.getSession().createSQLQuery(sqlone).addEntity(Role.class).setString(0, rolename).uniqueResult();
        return systemRole;
    }

    public PreinvApply getPreinvApply(String contactNo) {
        // TODO Auto-generated method stub
        String sql = "select pa.* from PreinvApply pa left join PreinvApplyDet pad on pad.batchNo=pa.batchNo where pa.startState='0' and pad.contrctNo=? and pa.oprType='0'";
        PreinvApply preinvApply = (PreinvApply) this.getSession().createSQLQuery(sql).addEntity(PreinvApply.class).setString(0, contactNo).uniqueResult();
        return preinvApply;
    }

    public List<PreinvApplyDet> findByCommitType(String batchNo) {
        String sql = "select * from PreinvApplyDet where batchNo=? and commitType='1'";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, batchNo).list();
    }

    public List<PreinvApplyDet> findByCommitTypeTwo(String batchNo) {
        String sql = "select * from PreinvApplyDet where batchNo=? and commitType='0'";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, batchNo).list();
    }

    /**
     * 将开票数据推送至BOSS
     */
    public Map<String, Object> savePreinvApplyTwo(PreinvApply preinvApply) {
        Map<String, Object> map = new HashMap<>();
        String url = ESB_URL_172 + "sPreInvApply";
        if(isES){
            url = ESB_URL_38 + "sPreInvApply";
        }
        List<PreinvApplyDet> list = this.findByUUIDTwo(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
        for (int a = 0;a<list.size();a++) {
            PreinvApplyDet preinvApplyDet = list.get(a);
            JSONObject object = new JSONObject();
            if (preinvApply.getOprType().equals("1")) {
                object.put("OLD_ORDER_ID", preinvApplyDet.getOldInvNo());// 原订单流水
            }
            if (preinvApplyDet.getInvType().equals("03") || preinvApplyDet.getInvType().equals("04")) {
                object.put("APPLY_FEE", preinvApplyDet.getInvAmout());
            }
            object.put("ORDER_ID", preinvApplyDet.getInvNo());// 订单流水
            object.put("UNIT_ID", preinvApply.getGroupCode());// 集团编码
            if (StringUtil.isNotNull(preinvApply.getTaxName())) {
                object.put("UNIT_NAME", decryptLayers(preinvApply.getTaxName()));// 纳税人名称
            } else {
                object.put("UNIT_NAME", decryptLayers(preinvApply.getGroupName()));// 集团名称
            }
            object.put("CONTRACT_NO", preinvApplyDet.getContrctNo());// 账户号码
            object.put("PHONE_NO", preinvApplyDet.getPhone());// 用户号码
            object.put("INV_TYPE", preinvApplyDet.getInvType());// 发票类型
            object.put("BEGIN_CYCLE", preinvApplyDet.getBeginCycle());// 账期开始年月
            object.put("END_CYCLE", preinvApplyDet.getEndCycle());// 账期结束年月
            object.put("OPR_TYPE", preinvApply.getOprType());// 状态
            object.put("LOGIN_NO", preinvApplyDet.getBossNo());// 工号
            object.put("TAX_PAYER", preinvApply.getTaxPayer());// 纳税人识别号
            object.put("TAX_ADDRESS", decryptLayers(preinvApply.getTaxAddress()));// 纳税人地址
            object.put("TAX_PHONE", preinvApply.getTaxPhone());// 纳税人电话
            object.put("TAX_BANK_NAME", preinvApply.getTaxBankName());// 纳税人银行名称
            object.put("TAX_BANK_ACCOUNT", preinvApply.getTaxBankAccount());// 纳税人银行账户
            object.put("BATCH_NO", preinvApply.getBatchNo());
            //String json = setParamObj(object, preinvApplyDet.getContrctNo());
            String json= ESBReqMsgUtil.packMsgByRoute("12",preinvApplyDet.getContrctNo(),object);

            logger.info("预开票接口入参数据" + json);
            String jsonString = UrlConnection.responseGBK(url, json);
            //String jsonString ="{'Status':1,'res':{'ROOT':{'RETURN_CODE':'0','DETAIL_MSG':'测试'}}}";
            JSONObject jsons = JSONObject.fromObject(jsonString);
            logger.info("预开票接口返回数据" + jsonString);
            // 调用接口成功
            if (jsons.getInt("Status") == 1) {
                String object2 = jsons.getString("res");
                JSONObject objects = JSONObject.fromObject(object2);
                JSONObject root = (JSONObject) objects.get("ROOT");
                long returnCode = root.getLong("RETURN_CODE");
                if (returnCode != 0L) {
                    preinvApplyDet.setCommitType("1");//提交状态为失败
                    preinvApplyDet.setErrorMessage(root.getString("DETAIL_MSG"));
                    updatePreinvApplyDet(preinvApplyDet);// 修改
                } else {
                    preinvApplyDet.setCommitType("0");// 提交状态为成功
                    updatePreinvApplyDet(preinvApplyDet);// 修改
                }
            }
        }
        map.put("flag", "YES");
        map.put("message", "操作成功");
        return map;
    }

    /**
     * 根据申请工单编码查询账户信息
     *
     * @params
     */
    public List<PreinvApplyDet> findByUUIDTwo(String batchNo) {
        String sql = "select * from PreinvApplyDet where batchNo=? and commitType='1' order by beginCycle";
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).setString(0, batchNo).list();
    }

    /**
     * 根据id查询开票信息
     *
     * @params
     */
    public PreinvApply findByBatchNoTwo(String batchNo) {
        String sql = "select * from PreinvApply where batchNo=?";
        PreinvApply preinvApply = (PreinvApply) getSession().createSQLQuery(sql).addEntity(PreinvApply.class).setString(0, batchNo).uniqueResult();
        return preinvApply;
    }

    public String getContracNo(LayuiPage page, String contrctNo) {
        String sql = "";
        String countSql = "";
        sql = "select ac.*,ap.appTitle from PreinvApplyDet ac INNER JOIN PreinvApply ap  "
                + "on ac.batchNo=ap.batchNo where ac.contrctNo='" + contrctNo + "' ";
        countSql = "select count(0) from PreinvApplyDet ac INNER JOIN PreinvApply ap  "
                + "on ac.batchNo=ap.batchNo where ac.contrctNo='" + contrctNo + "' ";
        page.setData(getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setFirstResult(page.getPageNo())//索引开始位置
                .setMaxResults(page.getPageSize()).list());
        //查询总条数
        page.setCount(Integer.valueOf(getSession().createSQLQuery(countSql).setCacheable(true).uniqueResult() + ""));
        String json = JSONHelper.SerializeWithNeedAnnotationDateFormat(page);
        return json;
    }

    public TransferCitiesData getTransferCitiesData(String code, String dangqianrenwu) {
        System.out.println("这是我请求的配置金额的service");
        String sqlone = "select * from TransferCitiesData where citiesCode =? and nodeName=? and processNmae='PreinvApply' ";
        TransferCitiesData transferCitiesData = (TransferCitiesData) this.getSession().createSQLQuery(sqlone).addEntity(TransferCitiesData.class).setString(0, code).setString(1, dangqianrenwu).uniqueResult();
        return transferCitiesData;
    }

    /**
     * 新增开票明细信息
     */
    public InternetOfThingsDet addInternetOfThingsDet(InternetOfThingsDet iot) {
        try {
            Session session = this.getSession();
            session.save(iot);
            session.flush();
            return iot;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新增有价卡明细信息
     */
    public ValuableCardDet addValuableCardDet(ValuableCardDet vcd) {
        try {
            Session session = this.getSession();
            session.save(vcd);
            session.flush();
            return vcd;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 新增有价卡明细信息
     */
    public ValuableCardDet updateValuableCardDet(ValuableCardDet vcd) {
        try {
            Session session = this.getSession();
            session.save(vcd);
            session.flush();
            return vcd;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据申请工单编码查询账户信息
     *
     * @params
     */
    public List<InternetOfThingsDet> getInternetOfThingsDetByApplyNo(String batchNo) {
        String sql = "select * from InternetOfThingsDet where apply_no=?";
        return getSession().createSQLQuery(sql).addEntity(InternetOfThingsDet.class).setString(0, batchNo).list();
    }

    /**
     * 根据申请工单编码查询账户信息
     *
     * @params
     */
    public List<ValuableCardDet> getValuableCardDetByApplyNo(String batchNo) {
        String sql = "SELECT * FROM VALUABLECARDDET WHERE APPLY_NO=?";
        return getSession().createSQLQuery(sql).addEntity(ValuableCardDet.class).setString(0, batchNo).list();
    }

    /**
     * 根据申请工单编码查询账户信息
     *
     * @params
     */
    public List<Map<String, String>> getInternetOfThingsDetMap(String batchNo) {
        String sql = "select acct_id,acct_name from InternetOfThingsDet where apply_no=? group by acct_id,acct_name";
        return getSession().createSQLQuery(sql).setString(0, batchNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 根据申请工单编码查询账户信息
     *
     * @params
     */
    public List<InternetOfThingsDet> getInternetOfThingsDetMapList(String batchNo, String acct_id) {
        String sql = "select * from InternetOfThingsDet where apply_no=? and acct_id=?";
        return getSession().createSQLQuery(sql).addEntity(InternetOfThingsDet.class).setString(0, batchNo).setString(1, acct_id).list();
    }

    /**
     * 将物联网预开票数据推送至BOSS
     */
    public Map<String, Object> sWlwIssueInvApply(PreinvApply preinvApply, List<InternetOfThingsDet> listDet, SystemUser user) {
        //String ESB_URL_172 = "http://**************:51000/esbWS/rest/";
        Map<String, Object> map = new HashMap<>();
        String url = ESB_URL_172 + "sWlwIssueInvApply";
        if(isES){
            url = ESB_URL_38 + "sWlwIssueInvApply";
        }
        JSONObject object = new JSONObject();
        JSONObject taxObject = new JSONObject();
        JSONArray orderArry = new JSONArray();
        JSONArray taxArry = new JSONArray();
        object.put("ORDER_ID", preinvApply.getBatchNo());// 甩单系统申请/作废流水号
        if ("1".equals(preinvApply.getOprType())) {//冲正
            object.put("REL_ORDER_ID", preinvApply.getOldId());// 作废时关联的原甩单系统订单流水号
            object.put("APP_TYPE", "1");//非空，0订单申请开具；  1订单申请作废
        } else {//申请
            object.put("REL_ORDER_ID", "");// 作废时关联的原甩单系统订单流水号
            object.put("APP_TYPE", "0");//非空，0订单申请开具；  1订单申请作废
        }
        object.put("CUSTOMER_ID", listDet.get(0).getCustomer_id());// 客户ID
        object.put("CUSTOMER_TYPE", listDet.get(0).getCustomer_type());// 客户类型
        object.put("FRANK", listDet.get(0).getFrank());// 发票标记
        object.put("VOUCHER_TYPE", listDet.get(0).getVoucherType());// 发票类型
        object.put("PREP_VOUCHER", "1");// 预开标记
        object.put("CHANNEL", "01");// 操作来源
        object.put("BIZ_TYPE", "0601");// 业务类型代码
        object.put("HOME_PROV", "208");// 省代码
        object.put("LOGIN_NO", user.getBossUserName());// 操作工号
        object.put("OPR_SEQ", listDet.get(0).getOpr_seq());// 本次操作流水号
        object.put("ORDER_SEQ", listDet.get(0).getOrder_seq());// 订单号
        object.put("CUST_ID", listDet.get(0).getCust_id());// 客户ID
        //object.put("REMARK", preinvApply.getAppMemo());// 开票备注
        object.put("REMARK", preinvApply.getRemarks());// 开票备注
        List<Map<String, String>> mapList = getInternetOfThingsDetMap(preinvApply.getBatchNo());
        for (int j = 0; j < mapList.size(); j++) {
            JSONObject orderObject = new JSONObject();
            orderObject.put("ACCT_ID", mapList.get(j).get("ACCT_ID"));//账户ID
            orderObject.put("ACCT_NAME", mapList.get(j).get("ACCT_NAME"));//账户名称
            List<InternetOfThingsDet> list = getInternetOfThingsDetMapList(preinvApply.getBatchNo(), mapList.get(j).get("ACCT_ID"));
            JSONArray chargeArry = new JSONArray();
            for (int i = 0; i < list.size(); i++) {
                JSONObject obj = new JSONObject();
                obj.put("VALIDBILLCYC", list.get(i).getValidbillcyc());
                obj.put("PAYMENT_TIME", list.get(i).getPayment_time() == null ? "" : list.get(i).getPayment_time());
                obj.put("PAYMENT_SEQ", list.get(i).getPayment_seq() == null ? "" : list.get(i).getPayment_seq());
                obj.put("SER_NAME", list.get(i).getSer_name());
                obj.put("TAX_RATE", list.get(i).getTax_rate());
                if (!list.get(i).getSplit_include_tax_amt().equals(list.get(i).getInclude_tax_amt())
                        && Long.parseLong(list.get(i).getSplit_include_tax_amt()) < Long.parseLong(list.get(i).getInclude_tax_amt())) {
                    String money = monerToChange(list.get(i).getTax_rate(), list.get(i).getSplit_include_tax_amt());
                    String exMoney = Long.parseLong(list.get(i).getSplit_include_tax_amt()) - Long.parseLong(money) + "";
                    obj.put("TAX_FEE", money);//税额
                    obj.put("INCLUDE_TAX_AMT", list.get(i).getSplit_include_tax_amt());//含税费用金额(单位:分)
                    obj.put("EXCLUDED_TAX_AMT", exMoney);//不含税费用金额(单位:分)
                } else {
                    obj.put("TAX_FEE", list.get(i).getTax_fee());
                    obj.put("INCLUDE_TAX_AMT", list.get(i).getInclude_tax_amt());
                    obj.put("EXCLUDED_TAX_AMT", list.get(i).getExcluded_tax_amt());
                }
                chargeArry.add(obj);
            }
            orderObject.put("CHARGE_INFO", chargeArry);
            orderArry.add(orderObject);
        }
        if (preinvApply.getTaxName() != null) {
            taxObject.put("P_NAME", preinvApply.getTaxName());
//            taxObject.put("IDENTIFY_NUM", preinvApply.getTaxPayer());
//            taxObject.put("ADDRESS_INFO", preinvApply.getTaxAddress());
//            taxObject.put("PHONE_NUMBER", preinvApply.getTaxPhone());
//            taxObject.put("BANK_ID", preinvApply.getTaxBankName() + " " + preinvApply.getTaxBankAccount());
            if (preinvApply.getTaxPayer() == null || preinvApply.getTaxPayer().equals("")) {
                taxObject.put("IDENTIFY_NUM", "");
            } else {
                taxObject.put("IDENTIFY_NUM", preinvApply.getTaxPayer());
            }
            if (preinvApply.getTaxAddress() == null || preinvApply.getTaxAddress().equals("")) {
                taxObject.put("ADDRESS_INFO", "");
            } else {
                taxObject.put("ADDRESS_INFO", preinvApply.getTaxAddress());
            }
            if (preinvApply.getTaxPhone() == null || preinvApply.getTaxPhone().equals("")) {
                taxObject.put("PHONE_NUMBER", "");
            } else {
                taxObject.put("PHONE_NUMBER", preinvApply.getTaxPhone());
            }
            if (preinvApply.getTaxBankName() == null || preinvApply.getTaxBankName().equals("")) {
                taxObject.put("BANK_ID", "");
            } else {
                taxObject.put("BANK_ID", preinvApply.getTaxBankName() + " " + preinvApply.getTaxBankAccount());
            }
            taxArry.add(taxObject);
            object.put("TAXPAYER_INFO", taxArry);// 纳税人资质信息
        }
        object.put("ORDER_INFO", orderArry);// 订单流水
        String json = setParamObjBossNo(object, user.getBossUserName());
        logger.info("物联网预开票接口推送URL：" + url);
        logger.info("物联网预开票接口推送参数：" + json);
        //String jsonString = CMCC1000OpenService.getInstance().bdcesPatamss(url,json);
        String jsonString = UrlConnection.responseGBK(url, json);
        logger.info("物联网预开票接口返回参数：" + jsonString);
        JSONObject jsonObj = JSONObject.fromObject(jsonString);
        String resObj = jsonObj.getString("res");
        JSONObject bodyObj = JSONObject.fromObject(resObj);
        JSONObject rootObj = JSONObject.fromObject(bodyObj.getString("ROOT"));

        if ("0".equals(rootObj.getString("RETURN_CODE"))) {
            map.put("flag", "YES");
            map.put("message", "操作成功");
        } else {
            map.put("flag", "NO");
            map.put("message", rootObj.getString("RETURN_MSG"));
        }
        return map;
    }

    /**
     * 将有价卡预开票数据推送至BOSS
     */
    public Map<String, Object> saveBossValuableCardDet(PreinvApply preinvApply, List<ValuableCardDet> listDet, SystemUser user, String orgInfo) {
        //String ESB_URL_172= "http://**************:51000/esbWS/rest/";
        Map<String, Object> map = new HashMap<>();
        String url = ESB_URL_172 + "com_sitech_custsvc_comp_inter_IP4794CoSvc_p4794PreOrdChg";
        if(isES){
            url = ESB_URL_38 + "com_sitech_custsvc_comp_inter_IP4794CoSvc_p4794PreOrdChg";
        }
        JSONObject object = new JSONObject();
        object.put("LOGIN_NO", user.getBossUserName());//操作工号
        object.put("THROWORDER_ACCEPT", preinvApply.getBatchNo());//甩单流水号
        object.put("UNIT_ID", preinvApply.getGroupCode());//集团编号
        object.put("ORDER_PRICE", listDet.get(0).getOrderPrice());//甩单金额，单位为分
        object.put("SERVICE_NO", listDet.get(0).getServiceNo());//服务号码
        object.put("LOADING_ACCOUNT_NO", "");//装维人员装维人员在装维系统帐号
        object.put("LOADING_GROUP_ID", orgInfo);//装维人员对应结队渠道编号
        object.put("LOADING_PHONE", "");//装维手机号
        object.put("CUST_NAME", listDet.get(0).getCustName());//客户姓名
        object.put("INV_CUST_NAME", preinvApply.getGroupName());//成集团名称
        object.put("ID_ICCID", listDet.get(0).getIdIccid());//证件号码
        object.put("INV_CONTRACT_NO", "");//办理业务的号码对应的账户
        object.put("TAX_ID", preinvApply.getTaxPayer());//纳税人标识号
        String json = setParamObjBossNo(object, user.getBossUserName());
        logger.info("有价卡预开票接口推送参数：" + json);
        //String jsonString = CMCC1000OpenService.getInstance().bdcesPatamss(url,json);
        String jsonString = UrlConnection.responseGBK(url, json);
        JSONObject jsonObj = JSONObject.fromObject(jsonString);
        logger.info("有价卡预开票接口返回参数：" + jsonString);
        String resObj = jsonObj.getString("res");
        JSONObject bodyObj = JSONObject.fromObject(resObj);
        JSONObject rootObj = JSONObject.fromObject(bodyObj.getString("ROOT"));
        if ("0".equals(rootObj.getString("RETURN_CODE"))) {
            map.put("flag", "YES");
            map.put("message", rootObj.getString("OUT_DATA"));
        } else {
            map.put("flag", "NO");
            map.put("message", rootObj.getString("RETURN_MSG"));
        }
        return map;
    }

    /**
     * 将有价卡预开票数据推送至BOSS
     */
    public Map<String, Object> saveBossGroupOrGroupId(String groupId, String groupCode, SystemUser user) {
        //String ESB_URL_172= "http://**************:51000/esbWS/rest/";
        Map<String, Object> map = new HashMap<>();
        String url = ESB_URL_172 + "com_sitech_custsvc_comp_inter_IP4794AoSvc_qryGroupSellFlag";
        if(isES){
            url = ESB_URL_38 + "com_sitech_custsvc_comp_inter_IP4794AoSvc_qryGroupSellFlag";
        }
        JSONObject object = new JSONObject();
        object.put("LOADING_GROUP_ID", groupId);//操作工号
        object.put("UNIT_ID", groupCode);//甩单流水号
        String json = setParamObjBossNo(object, user.getBossUserName());
        logger.info("有价卡预开票查询接口推送参数：" + json);
        //String jsonString = CMCC1000OpenService.getInstance().bdcesPatamss(url,json);
        String jsonString = UrlConnection.responseGBK(url, json);
        JSONObject jsonObj = JSONObject.fromObject(jsonString);
        logger.info("有价卡预开票查询接口返回参数：" + jsonString);
        String resObj = jsonObj.getString("res");
        JSONObject bodyObj = JSONObject.fromObject(resObj);
        JSONObject rootObj = JSONObject.fromObject(bodyObj.getString("ROOT"));
        if ("0".equals(rootObj.getString("RETURN_CODE"))) {
            JSONObject outData = JSONObject.fromObject(rootObj.get("OUT_DATA"));
            if (!"0".equals(outData.getString("SELL_FLAG"))) {
                map.put("flag", "YES");
                map.put("message", "操作成功");
            } else {
                map.put("flag", "NO");
                map.put("message", "集团账号下还有余额未用完,不能再进行该集团的申请");
            }
        } else {
            map.put("flag", "NO");
            map.put("message", rootObj.getString("RETURN_MSG"));
        }
        return map;
    }

    //计算税额
    public static String monerToChange(String taxRate, String taxMoney) {
        double rate = Double.parseDouble(taxRate) / 100;
        double money = Double.parseDouble(changeF2Y(taxMoney));
        String taxFee = format(money - money / (1 + rate)) + "";
        NumberFormat format = NumberFormat.getInstance();
        Number numberFormat = null;
        try {
            numberFormat = format.parse(taxFee);
        } catch (ParseException e) {
            e.printStackTrace();
        }
        double temp = numberFormat.doubleValue() * 100.0;
        format.setGroupingUsed(false);
        //设置返回数的小数部分所允许的最大位数
        format.setMaximumFractionDigits(0);
        String amount = format.format(temp);
        return amount;
    }

    /**
     * 分转元，转换为bigDecimal在toString
     *
     * @return
     */
    public static String changeF2Y(String price) {
        return BigDecimal.valueOf(Long.valueOf(price)).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).toString();
    }

    /**
     * 使用DecimalFormat,保留小数点后两位
     */
    public static String format(double value) {
        DecimalFormat df = new DecimalFormat("0.00");
        df.setRoundingMode(RoundingMode.HALF_UP);
        return df.format(value);
    }

    /**
     * 设置路由参数；根据BOSS工号
     *
     * @param body
     * @param bossNo
     * @return
     */
    protected String setParamObjBossNo(JSONObject body, String bossNo) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "14");
        routing.put("ROUTE_VALUE", bossNo);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        return root.toString();
    }

    /**
     * 更新数据（全部数据）
     *
     * @return
     */
    public String updatePreinvApply() {
        try {
            List<Map<String, String>> list = this.getCompay();
            SimpleDateFormat format = new SimpleDateFormat("YYYY-MM-dd");
            Integer newyear = Integer.parseInt(format.format(new Date()).substring(0, 4));
            Integer newmonth = Integer.parseInt(format.format(new Date()).substring(5, 7));
            PreinvApplyUpdate preinvApplyUpdate;
            for (int i = 0; i < list.size(); i++) {
                int year = 2018;
                Boolean falg = true;
                while (falg) {
                    for (int month = 1; month <= 12; month++) {
                        preinvApplyUpdate = new PreinvApplyUpdate();
                        String selectSql = "SELECT distinct PAD.UUID,PAD.INVAMOUT,PAD.PAYMENT,PAD.INVSTATE\n" +
                                "FROM PREINVAPPLYDET PAD\n" +
                                "INNER JOIN PREINVAPPLY PA ON PA.BATCHNO=PAD.BATCHNO \n" +
                                "WHERE  PAD.PAYMENT IS NOT NULL\n" +
                                "AND PA.BRANCHOFFICE='" + list.get(i).get("COMPANY_NAME") + "'";
                        if (month < 10) {
                            System.out.println(year + "-0" + month);
                            selectSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-0" + month + "'";
                            preinvApplyUpdate.setUpdate_date(year + "-0" + month);
                        } else {
                            System.out.println(year + "-" + month);
                            selectSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-" + month + "'";
                            preinvApplyUpdate.setUpdate_date(year + "-" + month);
                        }
                        List<Map<String, String>> selectList = getSession().createSQLQuery(selectSql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
                        Integer total_number = selectList.size();
                        Double total_amount = 0.00;
                        Integer return_number = 0;
                        Double return_amount = 0.00;
                        Integer Impact_number = 0;
                        Double Impact_amount = 0.00;
                        for (int f = 0; f < selectList.size(); f++) {
                            Double integer = Double.parseDouble(selectList.get(f).get("INVAMOUT"));
                            total_amount += integer;
                            if ("1".equals(selectList.get(f).get("PAYMENT"))) {    //已回款
                                return_number += 1;
                                return_amount += integer;
                            }

                            if ("2".equals(selectList.get(f).get("INVSTATE"))) {    //已冲销
                                Impact_number += 1;
                                Impact_amount += integer;
                            }

                        }
                        Integer Clean_number = return_number + Impact_number;
                        Double Clean_amount = Impact_amount + return_amount;
                        preinvApplyUpdate.setCities_unit(list.get(i).get("COMPANY_NAME"));
                        preinvApplyUpdate.setTotal_number(total_number.toString());
                        preinvApplyUpdate.setTotal_amount(total_amount.toString());
                        preinvApplyUpdate.setReturn_number(return_number.toString());
                        preinvApplyUpdate.setReturn_amount(return_amount.toString());
                        preinvApplyUpdate.setImpact_number(Impact_number.toString());
                        preinvApplyUpdate.setImpact_amount(Impact_amount.toString());
                        preinvApplyUpdate.setClean_number(Clean_number.toString());
                        preinvApplyUpdate.setClean_amount(Clean_amount.toString());
                        preinvApplyUpdate.setCities_shi("省公司");
                        if (updatePreinvApplysave(preinvApplyUpdate) == null) {
                            logger.info("导入" + list.get(i).get("COMPANY_NAME") + month + "月数据失败了！");
                        }
                        List<Map<String, String>> COUNTY = getCOUNTY(list.get(i).get("COMPANY_NAME"));
                        for (int count = 0; count < COUNTY.size(); count++) {
                            preinvApplyUpdate = new PreinvApplyUpdate();
                            String countSql = "SELECT distinct PAD.UUID,PAD.INVAMOUT,PAD.PAYMENT,PAD.INVSTATE\n" +
                                    "FROM PREINVAPPLYDET PAD\n" +
                                    "INNER JOIN PREINVAPPLY PA ON PA.BATCHNO=PAD.BATCHNO\n" +
                                    "WHERE  PAD.PAYMENT IS NOT NULL\n" +
                                    "AND PA.COUNTRY='" + COUNTY.get(count).get("COUNTY_NAME") + "'";
                            if (month < 10) {
                                countSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-0" + month + "'";
                                preinvApplyUpdate.setUpdate_date(year + "-0" + month);
                            } else {
                                countSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-" + month + "'";
                                preinvApplyUpdate.setUpdate_date(year + "-" + month);
                            }
                            List<Map<String, String>> countList = getSession().createSQLQuery(countSql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
                            total_number = countList.size();
                            total_amount = 0.00;
                            return_number = 0;
                            return_amount = 0.00;
                            Impact_number = 0;
                            Impact_amount = 0.00;
                            for (int c = 0; c < countList.size(); c++) {
                                Double integer = Double.parseDouble(countList.get(c).get("INVAMOUT"));
                                total_amount += integer;
                                if ("1".equals(countList.get(c).get("PAYMENT"))) {    //已回款
                                    return_number += 1;
                                    return_amount += integer;
                                }

                                if ("2".equals(countList.get(c).get("INVSTATE"))) {    //已冲销
                                    Impact_number += 1;
                                    Impact_amount += integer;
                                }

                            }
                            Clean_number = return_number + Impact_number;
                            Clean_amount = Impact_amount + return_amount;
                            preinvApplyUpdate.setCities_unit(COUNTY.get(count).get("COUNTY_NAME"));
                            preinvApplyUpdate.setTotal_number(total_number.toString());
                            preinvApplyUpdate.setTotal_amount(total_amount.toString());
                            preinvApplyUpdate.setReturn_number(return_number.toString());
                            preinvApplyUpdate.setReturn_amount(return_amount.toString());
                            preinvApplyUpdate.setImpact_number(Impact_number.toString());
                            preinvApplyUpdate.setImpact_amount(Impact_amount.toString());
                            preinvApplyUpdate.setClean_number(Clean_number.toString());
                            preinvApplyUpdate.setClean_amount(Clean_amount.toString());
                            preinvApplyUpdate.setCities_shi(list.get(i).get("COMPANY_NAME"));
                            if (updatePreinvApplysave(preinvApplyUpdate) == null) {
                                logger.info("导入" + COUNTY.get(count).get("COUNTY_NAME") + month + "月数据失败了！");
                            }
                        }
                        if (year >= newyear && month >= newmonth) {
                            falg = false;
                            break;
                        }
                    }
                    year++;
                }
            }
            return "OK";
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("预开票统计出错！");
            return "NO";
        }
    }

    /**
     * 数据更新(更新前一年的数据)
     *
     * @return
     */
    public String updatePreinvApplyTwo() {
        try {
            List<Map<String, String>> list = getCompay();
            SimpleDateFormat format = new SimpleDateFormat("YYYY-MM-dd");
            Integer newyear = Integer.parseInt(format.format(new Date()).substring(0, 4));
            Integer newmonth = Integer.parseInt(format.format(new Date()).substring(5, 7));
            PreinvApplyUpdate preinvApplyUpdate;
            for (int i = 0; i < list.size(); i++) {
                int year = newyear - 1;
                Boolean falg = true;
                Boolean falgsave;
                while (falg) {
                    for (int month = 1; month <= 12; month++) {
                        falgsave = false;
                        String sql = "SELECT * from BPMS_DATA_UPDATEPREINV WHERE CITIES_UNIT='" + list.get(i).get("COMPANY_NAME") + "' AND CITIES_SHI = '省公司'";
                        String selectSql = "SELECT distinct PAD.UUID,PAD.INVAMOUT,PAD.PAYMENT,PAD.INVSTATE\n" +
                                "FROM PREINVAPPLYDET PAD\n" +
                                "INNER JOIN PREINVAPPLY PA ON PA.BATCHNO=PAD.BATCHNO \n" +
                                "WHERE  PAD.PAYMENT IS NOT NULL\n" +
                                "AND PA.BRANCHOFFICE='" + list.get(i).get("COMPANY_NAME") + "'";
                        if (month < 10) {
                            selectSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-0" + month + "'";
                            sql += "AND UPDATE_DATE='" + year + "-0" + month + "'";
                        } else {
                            selectSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-" + month + "'";
                            sql += "AND UPDATE_DATE='" + year + "-" + month + "'";
                        }

                        preinvApplyUpdate = (PreinvApplyUpdate) this.getSession().createSQLQuery(sql).addEntity(PreinvApplyUpdate.class).uniqueResult();
                        if (preinvApplyUpdate == null) {
                            falgsave = true;
                            preinvApplyUpdate = new PreinvApplyUpdate();
                            preinvApplyUpdate.setCities_unit(list.get(i).get("COMPANY_NAME"));
                            preinvApplyUpdate.setCities_shi("省公司");
                            if (month < 10) {
                                preinvApplyUpdate.setUpdate_date(year + "-0" + month);
                            } else {
                                preinvApplyUpdate.setUpdate_date(year + "-" + month);
                            }
                        }
                        List<Map<String, String>> selectList = getSession().createSQLQuery(selectSql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
                        Integer total_number = selectList.size();
                        Double total_amount = 0.00;
                        Integer return_number = 0;
                        Double return_amount = 0.00;
                        Integer Impact_number = 0;
                        Double Impact_amount = 0.00;
                        for (int f = 0; f < selectList.size(); f++) {
                            Double integer = Double.parseDouble(selectList.get(f).get("INVAMOUT"));
                            total_amount += integer;
                            if ("1".equals(selectList.get(f).get("PAYMENT"))) {    //已回款
                                return_number += 1;
                                return_amount += integer;
                            }
                            if ("2".equals(selectList.get(f).get("INVSTATE"))) {    //已冲销
                                Impact_number += 1;
                                Impact_amount += integer;
                            }
                        }
                        Integer Clean_number = return_number + Impact_number;
                        Double Clean_amount = Impact_amount + return_amount;
                        preinvApplyUpdate.setTotal_number(total_number.toString());
                        preinvApplyUpdate.setTotal_amount(total_amount.toString());
                        preinvApplyUpdate.setReturn_number(return_number.toString());
                        preinvApplyUpdate.setReturn_amount(return_amount.toString());
                        preinvApplyUpdate.setImpact_number(Impact_number.toString());
                        preinvApplyUpdate.setImpact_amount(Impact_amount.toString());
                        preinvApplyUpdate.setClean_number(Clean_number.toString());
                        preinvApplyUpdate.setClean_amount(Clean_amount.toString());
                        if (falgsave) {
                            if (updatePreinvApplysave(preinvApplyUpdate) == null) {
                                logger.info("导入" + list.get(i).get("COMPANY_NAME") + month + "月数据失败了！");
                            }
                        } else {
                            if (updatePreinvApply(preinvApplyUpdate) == null) {
                                logger.info("修改" + list.get(i).get("COMPANY_NAME") + month + "月数据失败了！");
                            }
                        }
                        List<Map<String, String>> COUNTY = getCOUNTY(list.get(i).get("COMPANY_NAME"));
                        for (int count = 0; count < COUNTY.size(); count++) {
                            String countySql = "SELECT * from BPMS_DATA_UPDATEPREINV WHERE CITIES_UNIT='" + COUNTY.get(count).get("COUNTY_NAME") + "' AND CITIES_SHI='" + list.get(i).get("COMPANY_NAME") + "'";
                            String countSql = "SELECT distinct PAD.UUID,PAD.INVAMOUT,PAD.PAYMENT,PAD.INVSTATE\n" +
                                    "FROM PREINVAPPLYDET PAD\n" +
                                    "INNER JOIN PREINVAPPLY PA ON PA.BATCHNO=PAD.BATCHNO\n" +
                                    "WHERE  PAD.PAYMENT IS NOT NULL\n" +
                                    "AND PA.COUNTRY='" + COUNTY.get(count).get("COUNTY_NAME") + "'";
                            if (month < 10) {
                                countSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-0" + month + "'";
                                countySql += "AND UPDATE_DATE='" + year + "-0" + month + "'";
                            } else {
                                countSql += "AND to_char(PA.CREATEDATE,'yyyy-mm')='" + year + "-" + month + "'";
                                countySql += "AND UPDATE_DATE='" + year + "-" + month + "'";
                            }
                            falgsave = false;
                            preinvApplyUpdate = (PreinvApplyUpdate) this.getSession().createSQLQuery(countySql).addEntity(PreinvApplyUpdate.class).uniqueResult();
                            if (preinvApplyUpdate == null) {
                                falgsave = true;
                                preinvApplyUpdate = new PreinvApplyUpdate();
                                preinvApplyUpdate.setCities_unit(COUNTY.get(count).get("COUNTY_NAME"));
                                preinvApplyUpdate.setCities_shi(list.get(i).get("COMPANY_NAME"));
                                if (month < 10) {
                                    preinvApplyUpdate.setUpdate_date(year + "-0" + month);
                                } else {
                                    preinvApplyUpdate.setUpdate_date(year + "-" + month);
                                }
                            }
                            List<Map<String, String>> countList = getSession().createSQLQuery(countSql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
                            total_number = countList.size();
                            total_amount = 0.00;
                            return_number = 0;
                            return_amount = 0.00;
                            Impact_number = 0;
                            Impact_amount = 0.00;
                            for (int c = 0; c < countList.size(); c++) {
                                Double integer = Double.parseDouble(countList.get(c).get("INVAMOUT"));
                                total_amount += integer;
                                if ("1".equals(countList.get(c).get("PAYMENT"))) {    //已回款
                                    return_number += 1;
                                    return_amount += integer;
                                }

                                if ("2".equals(countList.get(c).get("INVSTATE"))) {    //已冲销
                                    Impact_number += 1;
                                    Impact_amount += integer;
                                }

                            }
                            Clean_number = return_number + Impact_number;
                            Clean_amount = Impact_amount + return_amount;
                            preinvApplyUpdate.setTotal_number(total_number.toString());
                            preinvApplyUpdate.setTotal_amount(total_amount.toString());
                            preinvApplyUpdate.setReturn_number(return_number.toString());
                            preinvApplyUpdate.setReturn_amount(return_amount.toString());
                            preinvApplyUpdate.setImpact_number(Impact_number.toString());
                            preinvApplyUpdate.setImpact_amount(Impact_amount.toString());
                            preinvApplyUpdate.setClean_number(Clean_number.toString());
                            preinvApplyUpdate.setClean_amount(Clean_amount.toString());
                            if (falgsave) {
                                if (updatePreinvApplysave(preinvApplyUpdate) == null) {
                                    logger.info("导入" + COUNTY.get(count).get("COUNTY_NAME") + month + "月数据失败了！");
                                }
                            } else {
                                if (updatePreinvApply(preinvApplyUpdate) == null) {
                                    logger.info("修改" + COUNTY.get(count).get("COUNTY_NAME") + month + "月数据失败了！");
                                }
                            }
                        }
                        if (year >= newyear && month >= newmonth) {
                            falg = false;
                            break;
                        }
                    }
                    year++;
                }
            }
            return "OK";
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("预开票统计出错！" + e);
            return "NO";
        }
    }

    public List<Map<String, String>> getCompay() {
        String company = "SELECT DISTINCT COMPANY_NAME from VW_USERINFO";
        return getSession().createSQLQuery(company).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public List<Map<String, String>> getCOUNTY(String COMPANY_NAME) {
        String company = "SELECT DISTINCT COUNTY_NAME from VW_USERINFO WHERE COMPANY_NAME='" + COMPANY_NAME + "'";
        return getSession().createSQLQuery(company).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }


    public PreinvApplyUpdate updatePreinvApplysave(PreinvApplyUpdate sa) {
        if (sa != null) {
            Session session = this.getSession();
            session.save(sa);
            session.flush();
            return sa;
        } else {
            return null;
        }
    }

    public PreinvApplyUpdate updatePreinvApply(PreinvApplyUpdate sa) {
        try {
            Session session = this.getSession();
            session.update(sa);
            session.flush();
            return sa;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public LayuiPage showPreinvApplyDate(String start_Time, String end_Time, String cities_unit, String cities_shi, LayuiPage page, Boolean flag, SystemUser user) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        Boolean falgtwo = true;
        if ((cities_unit == null || "null".equals(cities_unit)) && (cities_shi == null || "null".equals(cities_shi))) {
            if (flag) {   //是否为订单管理员
                sql += "SELECT CITIES_UNIT,SUM(TOTAL_NUMBER) AS TOTAL_NUMBER,SUM(TOTAL_AMOUNT) AS TOTAL_AMOUNT,SUM(CLEAN_NUMBER) AS CLEAN_NUMBER,SUM(CLEAN_AMOUNT) AS CLEAN_AMOUNT,SUM(RETURN_NUMBER) AS RETURN_NUMBER,SUM(RETURN_AMOUNT) AS RETURN_AMOUNT,SUM(IMPACT_NUMBER) AS IMPACT_NUMBER,SUM(IMPACT_AMOUNT) AS IMPACT_AMOUNT \n" +
                        "from BPMS_DATA_UPDATEPREINV WHERE CITIES_SHI='省公司'";
            } else {
                if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                    sql += "SELECT CITIES_UNIT,SUM(TOTAL_NUMBER) AS TOTAL_NUMBER,SUM(TOTAL_AMOUNT) AS TOTAL_AMOUNT,SUM(CLEAN_NUMBER) AS CLEAN_NUMBER,SUM(CLEAN_AMOUNT) AS CLEAN_AMOUNT,SUM(RETURN_NUMBER) AS RETURN_NUMBER,SUM(RETURN_AMOUNT) AS RETURN_AMOUNT,SUM(IMPACT_NUMBER) AS IMPACT_NUMBER,SUM(IMPACT_AMOUNT) AS IMPACT_AMOUNT \n" +
                            "from BPMS_DATA_UPDATEPREINV  WHERE CITIES_SHI='省公司'";
                } else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                    sql += "SELECT CITIES_UNIT,SUM(TOTAL_NUMBER) AS TOTAL_NUMBER,SUM(TOTAL_AMOUNT) AS TOTAL_AMOUNT,SUM(CLEAN_NUMBER) AS CLEAN_NUMBER,SUM(CLEAN_AMOUNT) AS CLEAN_AMOUNT,SUM(RETURN_NUMBER) AS RETURN_NUMBER,SUM(RETURN_AMOUNT) AS RETURN_AMOUNT,SUM(IMPACT_NUMBER) AS IMPACT_NUMBER,SUM(IMPACT_AMOUNT) AS IMPACT_AMOUNT \n" +
                            "from BPMS_DATA_UPDATEPREINV " +
                            "WHERE CITIES_SHI='" + list.get(0).get("COUNTY_NAME").toString() + "'";
                } else {
                    falgtwo = false;
                    sql += "SELECT * from BPMS_DATA_UPDATEPREINV \n" +
                            "WHERE CITIES_unit='" + list.get(0).get("COUNTY_NAME").toString() + "'";
                }
            }
            if (!"".equals(start_Time) && start_Time != null) {
                sql += " AND TO_DATE(UPDATE_DATE,'yyyy-mm')>=TO_DATE('" + start_Time + "','yyyy-mm')";
            }
            if (!"".equals(end_Time) && end_Time != null) {
                sql += " AND TO_DATE(UPDATE_DATE,'yyyy-mm')<=TO_DATE('" + end_Time + "','yyyy-mm')";
            }
            if (falgtwo) {
                sql += " GROUP BY CITIES_UNIT";
            } else {
                sql += " ORDER BY CITIES_UNIT,UPDATE_DATE DESC";
            }
        } else {
            sql += "SELECT * from BPMS_DATA_UPDATEPREINV WHERE 1=1";
            if (!"".equals(cities_shi) && cities_shi != null && cities_shi != "null") {
                sql += " AND CITIES_SHI='" + cities_shi + "'";
            }
            if (!"".equals(cities_unit) && cities_unit != null && cities_unit != "null") {
                sql += " AND CITIES_UNIT='" + cities_unit + "'";
            }
            if (!"".equals(start_Time) && start_Time != null && start_Time != "null") {
                sql += " AND TO_DATE(UPDATE_DATE,'yyyy-mm')>=TO_DATE('" + start_Time + "','yyyy-mm')";
            }
            if (!"".equals(end_Time) && end_Time != null && end_Time != "null") {
                sql += " AND TO_DATE(UPDATE_DATE,'yyyy-mm')<=TO_DATE('" + end_Time + "','yyyy-mm')";
            }
            sql += " ORDER BY CITIES_UNIT,UPDATE_DATE DESC";
        }
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public String getUserCompany(SystemUser user) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
            return "省公司";
        } else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
            return "市公司";
        } else {
            return "区县公司";
        }
    }

    /**
     * 根据用户查询VW_USERINFO视图
     *
     * @param user
     * @return
     */
    public List<Map<String, Object>> getVwUser(SystemUser user) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return list;
    }

    public PreinvApplyFlow getPid(String batchNo) {
        String sql = "SELECT * FROM PREINVAPPLYFLOW WHERE batchNo='" + batchNo + "'";
        return (PreinvApplyFlow) getSession().createSQLQuery(sql).addEntity(PreinvApplyFlow.class).uniqueResult();
    }

    public List<PreinvApplyFlow> getPids(String batchNo) {
        String sql = "SELECT * FROM PREINVAPPLYFLOW WHERE batchNo=? ";
//        return (PreinvApplyFlow) getSession().createSQLQuery(sql).addEntity(PreinvApplyFlow.class).uniqueResult();
        return getSession().createSQLQuery(sql).addEntity(PreinvApplyFlow.class).setString(0, batchNo).list();
    }

    /**
     * 根据登录人名称以及状态查询待办信息
     *
     * @param id
     * @return
     */
    public WaitTask getWaitTask(String id) {
        String sql = "select * from WaitTask where URL like '%" + id + "%' and state=0 and code='PREINVAPPLY'";
        return (WaitTask) getSession().createSQLQuery(sql).addEntity(WaitTask.class).uniqueResult();
    }

    //根据oldId查询工单
    public PreinvApply getPreinvApplyByoldId(String id) {
        String sql = "SELECT * FROM PREINVAPPLY WHERE OLDID='" + id + "'";
        //return null;
        return (PreinvApply) getSession().createSQLQuery(sql).addEntity(PreinvApply.class).uniqueResult();
    }

    //select * from PREINVAPPLYdet where INVNO='KP20200627133725734'
    //select * from PREINVAPPLYdet where OLDINVNO='KP20200627133725734

    public PreinvApply getPreinvApplyByoldIdNew(String id) {
        //NJ20191203213231844
        String sql = "SELECT * FROM PREINVAPPLY WHERE BATCHNO='" + id + "'";
        return (PreinvApply) getSession().createSQLQuery(sql).addEntity(PreinvApply.class).uniqueResult();
    }

    //省公司查询
    public LayuiPage pagingQuery(LayuiPage page) {
        Collection collection = new ArrayList();
        //预开票总金额（2021年至今）
        String sql1 = "select sum(p.Appamout) 总金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l1 = getSession().createSQLQuery(sql1).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql2 = "select count(0) 申请条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice\n";
        List<Map<String, Object>> l2 = getSession().createSQLQuery(sql2).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //预开票金额统计  6月以内
        String sql3 = "select sum(p.Appamout) 六个月以内金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l3 = getSession().createSQLQuery(sql3).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql4 = "select count(0) 六个月以内申请条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l4 = getSession().createSQLQuery(sql4).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //6月以上  6个月以上指的是从2021年至今
        String sql5 = "select sum(p.Appamout) 六个月以上总金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l5 = getSession().createSQLQuery(sql5).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql6 = "select count(0) 六个月以上总条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice\n";
        List<Map<String, Object>> l6 = getSession().createSQLQuery(sql6).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //历史订单金额（初始-2020年12月31日）
        String sql7 = "select sum(p.Appamout) 历史订单金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice\n";
        List<Map<String, Object>> l7 = getSession().createSQLQuery(sql7).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql8 = "select count(0) 历史订单条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l8 = getSession().createSQLQuery(sql8).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //上月新增预开票金额
        String sql9 = "select sum(p.Appamout) 上月新增预开票金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l9 = getSession().createSQLQuery(sql9).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql10 = "select count(0) 上月新增预开票申请条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "\tand p.Startstate in ('1','7') \n" +
                "\tgroup by p.Branchoffice\n" +
                "\torder by p.Branchoffice";
        List<Map<String, Object>> l10 = getSession().createSQLQuery(sql10).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //2022年新增预开票金额（2022年1月至今）
        String sql11 = "select sum(p.Appamout) 二二年新增预开票金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l11 = getSession().createSQLQuery(sql11).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql12 = "select count(0)二二年新增预开票条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l12 = getSession().createSQLQuery(sql12).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        for (int i = 0; i < 23; i++) {
            HashMap<String, Object> map = new HashMap();
            if (l1.size() != 0) {
                if (l1.size() == 23) {
                    map.putAll(l1.get(i));
                } else {
                    for (int i1 = 0; i1 < l1.size(); i1++) {
                        if (l1.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l1.get(i1));
                        }
                    }
                }
            } else {
                map.put("总金额", null);
            }

            if (l2.size() != 0) {
                if (l2.size() == 23) {
                    map.putAll(l2.get(i));
                } else {
                    for (int i1 = 0; i1 < l2.size(); i1++) {
                        if (l2.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l2.get(i1));
                        }
                    }
                }
            } else {
                map.put("申请条数", null);
            }

            if (l3.size() != 0) {
                if (l3.size() == 23) {
                    map.putAll(l3.get(i));
                } else {
                    for (int i1 = 0; i1 < l3.size(); i1++) {
                        if (l3.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l3.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内金额", null);
            }

            if (l4.size() != 0) {
                if (l4.size() == 23) {
                    map.putAll(l4.get(i));
                } else {
                    for (int i1 = 0; i1 < l4.size(); i1++) {
                        if (l4.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l4.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内申请条数", null);
            }

            if (l5.size() != 0) {
                if (l5.size() == 23) {
                    map.putAll(l5.get(i));
                } else {
                    for (int i1 = 0; i1 < l5.size(); i1++) {
                        if (l5.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l5.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总金额", null);
            }

            if (l6.size() != 0) {
                if (l6.size() == 23) {
                    map.putAll(l6.get(i));
                } else {
                    for (int i1 = 0; i1 < l6.size(); i1++) {
                        if (l6.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l6.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总条数", null);
            }

            if (l7.size() != 0) {
                if (l7.size() == 23) {
                    map.putAll(l7.get(i));
                } else {
                    for (int i1 = 0; i1 < l7.size(); i1++) {
                        if (l7.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l7.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单金额", null);
            }

            if (l8.size() != 0) {
                if (l8.size() == 23) {
                    map.putAll(l8.get(i));
                } else {
                    for (int i1 = 0; i1 < l8.size(); i1++) {
                        if (l8.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l8.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单条数", null);
            }

            if (l9.size() != 0) {
                if (l9.size() == 23) {
                    map.putAll(l9.get(i));
                } else {
                    for (int i1 = 0; i1 < l9.size(); i1++) {
                        if (l9.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l9.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票金额", null);
            }

            if (l10.size() != 0) {
                if (l10.size() == 23) {
                    map.putAll(l10.get(i));
                } else {
                    for (int i1 = 0; i1 < l10.size(); i1++) {
                        if (l10.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l10.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票申请条数", null);
            }

            if (l11.size() != 0) {
                if (l11.size() == 23) {
                    map.putAll(l11.get(i));
                } else {
                    for (int i1 = 0; i1 < l11.size(); i1++) {
                        if (l11.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l11.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票金额", null);
            }

            if (l12.size() != 0) {
                if (l12.size() == 23) {
                    map.putAll(l12.get(i));
                } else {
                    for (int i1 = 0; i1 < l12.size(); i1++) {
                        if (l12.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l12.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票条数", null);
            }


            collection.add(map);
        }

        page.setCount(collection.size());
        if (page.getCount() > 0) {
            page.setData(collection);
        }
        return page;
    }

    //分公司查询
    public LayuiPage pagingQueryTwo(LayuiPage page, String code) {
        Collection collection = new ArrayList();
        //预开票总金额（2021年至今）
        String sql1 = "select sum(p.Appamout) 总金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l1 = getSession().createSQLQuery(sql1).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql2 = "select count(0) 申请条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l2 = getSession().createSQLQuery(sql2).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //预开票金额统计  6月以内
        String sql3 = "select sum(p.Appamout) 六个月以内金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l3 = getSession().createSQLQuery(sql3).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql4 = "select count(0) 六个月以内申请条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l4 = getSession().createSQLQuery(sql4).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //6月以上  6个月以上指的是从2021年至今
        String sql5 = "select sum(p.Appamout) 六个月以上总金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l5 = getSession().createSQLQuery(sql5).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql6 = "select count(0) 六个月以上总条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l6 = getSession().createSQLQuery(sql6).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //历史订单金额（初始-2020年12月31日）
        String sql7 = "select sum(p.Appamout) 历史订单金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l7 = getSession().createSQLQuery(sql7).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql8 = "select count(0) 历史订单条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l8 = getSession().createSQLQuery(sql8).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //上月新增预开票金额
        String sql9 = "select sum(p.Appamout) 上月新增预开票金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l9 = getSession().createSQLQuery(sql9).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql10 = "select count(0) 上月新增预开票申请条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "\tand p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l10 = getSession().createSQLQuery(sql10).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //2022年新增预开票金额（2022年1月至今）
        String sql11 = "select sum(p.Appamout) 二二年新增预开票金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l11 = getSession().createSQLQuery(sql11).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql12 = "select count(0)二二年新增预开票条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l12 = getSession().createSQLQuery(sql12).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();


        for (int i = 0; i < l1.size(); i++) {
            HashMap<String, Object> map = new HashMap();
            if (l1.size() != 0) {
//                if (l1.size() == 23) {
                map.putAll(l1.get(i));
//                } else {
//                    for (int i1 = 0; i1 < l1.size(); i1++) {
//                        if (l1.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
//                            map.putAll(l1.get(i1));
//                        }
//                    }
//                }
            } else {
                map.put("总金额", null);
            }

            if (l2.size() != 0) {
                if (l2.size() == l1.size()) {
                    map.putAll(l2.get(i));
                } else {
                    for (int i1 = 0; i1 < l2.size(); i1++) {
                        if (l2.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l2.get(i1));
                        }
                    }
                }
            } else {
                map.put("申请条数", null);
            }

            if (l3.size() != 0) {
                if (l3.size() == l1.size()) {
                    map.putAll(l3.get(i));
                } else {
                    for (int i1 = 0; i1 < l3.size(); i1++) {
                        if (l3.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l3.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内金额", null);
            }

            if (l4.size() != 0) {
                if (l4.size() == l1.size()) {
                    map.putAll(l4.get(i));
                } else {
                    for (int i1 = 0; i1 < l4.size(); i1++) {
                        if (l4.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l4.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内申请条数", null);
            }

            if (l5.size() != 0) {
                if (l5.size() == l1.size()) {
                    map.putAll(l5.get(i));
                } else {
                    for (int i1 = 0; i1 < l5.size(); i1++) {
                        if (l5.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l5.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总金额", null);
            }

            if (l6.size() != 0) {
                if (l6.size() == l1.size()) {
                    map.putAll(l6.get(i));
                } else {
                    for (int i1 = 0; i1 < l6.size(); i1++) {
                        if (l6.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l6.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总条数", null);
            }

            if (l7.size() != 0) {
                if (l7.size() == l1.size()) {
                    map.putAll(l7.get(i));
                } else {
                    for (int i1 = 0; i1 < l7.size(); i1++) {
                        if (l7.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l7.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单金额", null);
            }

            if (l8.size() != 0) {
                if (l8.size() == l1.size()) {
                    map.putAll(l8.get(i));
                } else {
                    for (int i1 = 0; i1 < l8.size(); i1++) {
                        if (l8.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l8.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单条数", null);
            }

            if (l9.size() != 0) {
                if (l9.size() == l1.size()) {
                    map.putAll(l9.get(i));
                } else {
                    for (int i1 = 0; i1 < l9.size(); i1++) {
                        if (l9.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l9.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票金额", null);
            }

            if (l10.size() != 0) {
                if (l10.size() == l1.size()) {
                    map.putAll(l10.get(i));
                } else {
                    for (int i1 = 0; i1 < l10.size(); i1++) {
                        if (l10.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l10.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票申请条数", null);
            }

            if (l11.size() != 0) {
                if (l11.size() == l1.size()) {
                    map.putAll(l11.get(i));
                } else {
                    for (int i1 = 0; i1 < l11.size(); i1++) {
                        if (l11.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l11.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票金额", null);
            }

            if (l12.size() != 0) {
                if (l12.size() == l1.size()) {
                    map.putAll(l12.get(i));
                } else {
                    for (int i1 = 0; i1 < l12.size(); i1++) {
                        if (l12.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l12.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票条数", null);
            }


            collection.add(map);
        }

        page.setCount(collection.size());
        if (page.getCount() > 0) {
            page.setData(collection);
        }
        return page;
    }

    //省公司导出
    public List<Map<String, Object>> getExportData() {
        List<Map<String, Object>> list = new ArrayList<>();

        //预开票总金额（2021年至今）
        String sql1 = "select sum(p.Appamout) 总金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l1 = getSession().createSQLQuery(sql1).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql2 = "select count(0) 申请条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice\n";
        List<Map<String, Object>> l2 = getSession().createSQLQuery(sql2).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //预开票金额统计  6月以内
        String sql3 = "select sum(p.Appamout) 六个月以内金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l3 = getSession().createSQLQuery(sql3).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql4 = "select count(0) 六个月以内申请条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l4 = getSession().createSQLQuery(sql4).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //6月以上  6个月以上指的是从2021年至今
        String sql5 = "select sum(p.Appamout) 六个月以上总金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l5 = getSession().createSQLQuery(sql5).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql6 = "select count(0) 六个月以上总条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice\n";
        List<Map<String, Object>> l6 = getSession().createSQLQuery(sql6).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //历史订单金额（初始-2020年12月31日）
        String sql7 = "select sum(p.Appamout) 历史订单金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice\n";
        List<Map<String, Object>> l7 = getSession().createSQLQuery(sql7).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql8 = "select count(0) 历史订单条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l8 = getSession().createSQLQuery(sql8).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //上月新增预开票金额
        String sql9 = "select sum(p.Appamout) 上月新增预开票金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l9 = getSession().createSQLQuery(sql9).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql10 = "select count(0) 上月新增预开票申请条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "\tand p.Startstate in ('1','7') \n" +
                "\tgroup by p.Branchoffice\n" +
                "\torder by p.Branchoffice";
        List<Map<String, Object>> l10 = getSession().createSQLQuery(sql10).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //2022年新增预开票金额（2022年1月至今）
        String sql11 = "select sum(p.Appamout) 二二年新增预开票金额,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l11 = getSession().createSQLQuery(sql11).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql12 = "select count(0)二二年新增预开票条数,p.Branchoffice from PREINVAPPLY p\n" +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                "group by p.Branchoffice\n" +
                "order by p.Branchoffice";
        List<Map<String, Object>> l12 = getSession().createSQLQuery(sql12).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        for (int i = 0; i < 23; i++) {
            HashMap<String, Object> map = new HashMap();
            if (l1.size() != 0) {
                if (l1.size() == 23) {
                    map.putAll(l1.get(i));
                } else {
                    for (int i1 = 0; i1 < l1.size(); i1++) {
                        if (l1.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l1.get(i1));
                        }
                    }
                }
            } else {
                map.put("总金额", null);
            }

            if (l2.size() != 0) {
                if (l2.size() == 23) {
                    map.putAll(l2.get(i));
                } else {
                    for (int i1 = 0; i1 < l2.size(); i1++) {
                        if (l2.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l2.get(i1));
                        }
                    }
                }
            } else {
                map.put("申请条数", null);
            }

            if (l3.size() != 0) {
                if (l3.size() == 23) {
                    map.putAll(l3.get(i));
                } else {
                    for (int i1 = 0; i1 < l3.size(); i1++) {
                        if (l3.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l3.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内金额", null);
            }

            if (l4.size() != 0) {
                if (l4.size() == 23) {
                    map.putAll(l4.get(i));
                } else {
                    for (int i1 = 0; i1 < l4.size(); i1++) {
                        if (l4.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l4.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内申请条数", null);
            }

            if (l5.size() != 0) {
                if (l5.size() == 23) {
                    map.putAll(l5.get(i));
                } else {
                    for (int i1 = 0; i1 < l5.size(); i1++) {
                        if (l5.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l5.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总金额", null);
            }

            if (l6.size() != 0) {
                if (l6.size() == 23) {
                    map.putAll(l6.get(i));
                } else {
                    for (int i1 = 0; i1 < l6.size(); i1++) {
                        if (l6.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l6.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总条数", null);
            }

            if (l7.size() != 0) {
                if (l7.size() == 23) {
                    map.putAll(l7.get(i));
                } else {
                    for (int i1 = 0; i1 < l7.size(); i1++) {
                        if (l7.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l7.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单金额", null);
            }

            if (l8.size() != 0) {
                if (l8.size() == 23) {
                    map.putAll(l8.get(i));
                } else {
                    for (int i1 = 0; i1 < l8.size(); i1++) {
                        if (l8.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l8.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单条数", null);
            }

            if (l9.size() != 0) {
                if (l9.size() == 23) {
                    map.putAll(l9.get(i));
                } else {
                    for (int i1 = 0; i1 < l9.size(); i1++) {
                        if (l9.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l9.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票金额", null);
            }

            if (l10.size() != 0) {
                if (l10.size() == 23) {
                    map.putAll(l10.get(i));
                } else {
                    for (int i1 = 0; i1 < l10.size(); i1++) {
                        if (l10.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l10.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票申请条数", null);
            }

            if (l11.size() != 0) {
                if (l11.size() == 23) {
                    map.putAll(l11.get(i));
                } else {
                    for (int i1 = 0; i1 < l11.size(); i1++) {
                        if (l11.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l11.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票金额", null);
            }

            if (l12.size() != 0) {
                if (l12.size() == 23) {
                    map.putAll(l12.get(i));
                } else {
                    for (int i1 = 0; i1 < l12.size(); i1++) {
                        if (l12.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l12.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票条数", null);
            }


            list.add(map);
        }


        return list;
    }

    //分公司导出
    public List<Map<String, Object>> getExportDataTwo(String code) {
        List<Map<String, Object>> list = new ArrayList<>();
        //预开票总金额（2021年至今）
        String sql1 = "select sum(p.Appamout) 总金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l1 = getSession().createSQLQuery(sql1).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql2 = "select count(0) 申请条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l2 = getSession().createSQLQuery(sql2).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //预开票金额统计  6月以内
        String sql3 = "select sum(p.Appamout) 六个月以内金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l3 = getSession().createSQLQuery(sql3).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql4 = "select count(0) 六个月以内申请条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l4 = getSession().createSQLQuery(sql4).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //6月以上  6个月以上指的是从2021年至今
        String sql5 = "select sum(p.Appamout) 六个月以上总金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l5 = getSession().createSQLQuery(sql5).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql6 = "select count(0) 六个月以上总条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getSixMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l6 = getSession().createSQLQuery(sql6).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //历史订单金额（初始-2020年12月31日）
        String sql7 = "select sum(p.Appamout) 历史订单金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l7 = getSession().createSQLQuery(sql7).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql8 = "select count(0) 历史订单条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('2021-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "  --and p.CREATEDATE > to_Date('2020-09-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l8 = getSession().createSQLQuery(sql8).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //上月新增预开票金额
        String sql9 = "select sum(p.Appamout) 上月新增预开票金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l9 = getSession().createSQLQuery(sql9).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql10 = "select count(0) 上月新增预开票申请条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('" + getOneMonth() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "\tand p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l10 = getSession().createSQLQuery(sql10).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

        //2022年新增预开票金额（2022年1月至今）
        String sql11 = "select sum(p.Appamout) 二二年新增预开票金额,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l11 = getSession().createSQLQuery(sql11).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql12 = "select count(0)二二年新增预开票条数,v.county_name as BRANCHOFFICE from PREINVAPPLY p\n" +
                "inner join vw_userinfo v on v.ROWNO=p.CREATORID " +
                "where p.CREATEDATE < to_Date('" + getTime() + "','yyyy-mm-dd hh24:mi:ss')\n" +
                "  and p.CREATEDATE > to_Date('2022-01-01','yyyy-mm-dd hh24:mi:ss')\n" +
                "and p.Startstate in ('1','7') \n" +
                " and v.COMPANY_code='" + code + "'" +
                "group by v.county_name,v.COMPANY_NAME";
        List<Map<String, Object>> l12 = getSession().createSQLQuery(sql12).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();


        for (int i = 0; i < l1.size(); i++) {
            HashMap<String, Object> map = new HashMap();
            if (l1.size() != 0) {
//                if (l1.size() == 23) {
                map.putAll(l1.get(i));
//                } else {
//                    for (int i1 = 0; i1 < l1.size(); i1++) {
//                        if (l1.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
//                            map.putAll(l1.get(i1));
//                        }
//                    }
//                }
            } else {
                map.put("总金额", null);
            }

            if (l2.size() != 0) {
                if (l2.size() == l1.size()) {
                    map.putAll(l2.get(i));
                } else {
                    for (int i1 = 0; i1 < l2.size(); i1++) {
                        if (l2.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l2.get(i1));
                        }
                    }
                }
            } else {
                map.put("申请条数", null);
            }

            if (l3.size() != 0) {
                if (l3.size() == l1.size()) {
                    map.putAll(l3.get(i));
                } else {
                    for (int i1 = 0; i1 < l3.size(); i1++) {
                        if (l3.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l3.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内金额", null);
            }

            if (l4.size() != 0) {
                if (l4.size() == l1.size()) {
                    map.putAll(l4.get(i));
                } else {
                    for (int i1 = 0; i1 < l4.size(); i1++) {
                        if (l4.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l4.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以内申请条数", null);
            }

            if (l5.size() != 0) {
                if (l5.size() == l1.size()) {
                    map.putAll(l5.get(i));
                } else {
                    for (int i1 = 0; i1 < l5.size(); i1++) {
                        if (l5.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l5.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总金额", null);
            }

            if (l6.size() != 0) {
                if (l6.size() == l1.size()) {
                    map.putAll(l6.get(i));
                } else {
                    for (int i1 = 0; i1 < l6.size(); i1++) {
                        if (l6.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l6.get(i1));
                        }
                    }
                }
            } else {
                map.put("六个月以上总条数", null);
            }

            if (l7.size() != 0) {
                if (l7.size() == l1.size()) {
                    map.putAll(l7.get(i));
                } else {
                    for (int i1 = 0; i1 < l7.size(); i1++) {
                        if (l7.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l7.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单金额", null);
            }

            if (l8.size() != 0) {
                if (l8.size() == l1.size()) {
                    map.putAll(l8.get(i));
                } else {
                    for (int i1 = 0; i1 < l8.size(); i1++) {
                        if (l8.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l8.get(i1));
                        }
                    }
                }
            } else {
                map.put("历史订单条数", null);
            }

            if (l9.size() != 0) {
                if (l9.size() == l1.size()) {
                    map.putAll(l9.get(i));
                } else {
                    for (int i1 = 0; i1 < l9.size(); i1++) {
                        if (l9.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l9.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票金额", null);
            }

            if (l10.size() != 0) {
                if (l10.size() == l1.size()) {
                    map.putAll(l10.get(i));
                } else {
                    for (int i1 = 0; i1 < l10.size(); i1++) {
                        if (l10.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l10.get(i1));
                        }
                    }
                }
            } else {
                map.put("本月新增预开票申请条数", null);
            }

            if (l11.size() != 0) {
                if (l11.size() == l1.size()) {
                    map.putAll(l11.get(i));
                } else {
                    for (int i1 = 0; i1 < l11.size(); i1++) {
                        if (l11.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l11.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票金额", null);
            }

            if (l12.size() != 0) {
                if (l12.size() == l1.size()) {
                    map.putAll(l12.get(i));
                } else {
                    for (int i1 = 0; i1 < l12.size(); i1++) {
                        if (l12.get(i1).get("BRANCHOFFICE").equals(map.get("BRANCHOFFICE"))) {
                            map.putAll(l12.get(i1));
                        }
                    }
                }
            } else {
                map.put("二二年新增预开票条数", null);
            }


            list.add(map);
        }
        return list;
    }

    //导出数据
    public void exportData(List<Map<String, Object>> mapList) throws IOException, WriteException {
        HttpServletResponse response = ServletActionContext.getResponse();
        String excelFile = FileUpload.getFtpURL() + "exportExcelToJxl.xls";
        //String excelFile = "C:/Users/<USER>/Desktop/exportExcelToJxl.xlsx";
        File file = new File(FileUpload.getFtpURL());

        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        String exportName = "预开票统计_" + FileUpload.getDateToString("yyyy_MM_dd");
        try {
            WritableWorkbook wb = Workbook.createWorkbook(new File(excelFile));
            WritableSheet firstSheet = wb.createSheet("预开票统计", 1);
            String[] headers = new String[]{"单位", "总金额(2021年至今)", "申请条数(2021年至今)", "6个月以内金额", "6个月以内申请条数", "6个月以上总金额", "6个月以上申请条数", "历史订单金额(初始-2020年12月31日)", "订单条数(初始-2020年12月31日)", "本月新增预开票金额", "本月新增预开票申请条数", "2022年新增预开票金额(2022年1月至今)", "2022年新增预开票金额(2022年1月至今)申请条数"};
            if (headers != null) {
                for (int i = 0; i < headers.length; i++) {
                    // 3、创建单元格(Label)对象
                    Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
                    WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                    WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                    // 标题栏 // 颜色
                    WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
                    wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
                    wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
                    wcfTitle.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                    wcfTitle.setAlignment(jxl.format.Alignment.CENTRE); // 设置垂直对齐
                    // 内容栏
                    WritableCellFormat wcfContent = new WritableCellFormat(wf3);
                    wcfContent.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                    wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐

                    CellView navCellView = new CellView();
                    navCellView.setSize(150 * 50);

                    label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
                    firstSheet.setColumnView(i, navCellView); // 设置col显示样式
                    firstSheet.setRowView(i, 400, false); // 设置行高
                    firstSheet.addCell(label0);

                    if (mapList.size() > 0) {
                        for (int i1 = 0; i1 < mapList.size(); i1++) {
                            if (mapList.get(i1).get("BRANCHOFFICE") != null) {
                                Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("BRANCHOFFICE")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("总金额") != null) {
                                Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("总金额")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("申请条数") != null) {
                                Label label = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("申请条数")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("六个月以内金额") != null) {
                                Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("六个月以内金额")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("六个月以内申请条数") != null) {
                                Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("六个月以内申请条数")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("六个月以上总金额") != null) {
                                Label label = new Label(5, i1 + 1, String.valueOf(mapList.get(i1).get("六个月以上总金额")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("六个月以上总条数") != null) {
                                Label label = new Label(6, i1 + 1, String.valueOf(mapList.get(i1).get("六个月以上总条数")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("历史订单金额") != null) {
                                Label label = new Label(7, i1 + 1, String.valueOf(mapList.get(i1).get("历史订单金额")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("历史订单条数") != null) {
                                Label label = new Label(8, i1 + 1, String.valueOf(mapList.get(i1).get("历史订单条数")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("上月新增预开票金额") != null) {
                                Label label = new Label(9, i1 + 1, String.valueOf(mapList.get(i1).get("上月新增预开票金额")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("上月新增预开票申请条数") != null) {
                                Label label = new Label(10, i1 + 1, String.valueOf(mapList.get(i1).get("上月新增预开票申请条数")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("二二年新增预开票金额") != null) {
                                Label label = new Label(11, i1 + 1, String.valueOf(mapList.get(i1).get("二二年新增预开票金额")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("二二年新增预开票条数") != null) {
                                Label label = new Label(12, i1 + 1, String.valueOf(mapList.get(i1).get("二二年新增预开票条数")), wcfContent);
                                firstSheet.addCell(label);
                            }
                        }
                    }
                }
            }
            wb.write();// 打开流 开始写文件
            wb.close();// 关闭流
            byte[] data = FileUtil.toByteArray2(excelFile);
            String fileName = URLEncoder.encode(exportName, "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".csv" + "\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
            File fe = new File(excelFile);
            fe.delete();
        } finally {
            File fe = new File(excelFile);
            if (file.exists() && file.isDirectory()) {
                fe.delete();
            }
        }
    }


    /**
     * 获取当前月1号字符串
     */
    public static String getTime() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");// 格式化日期
        return sdf.format(date) + "-01";
    }

    /**
     * 获取往前1个月的1号字符串
     *
     * @return
     */
    public static String getOneMonth() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");// 格式化日期
        date.setMonth(date.getMonth() - 1);
        return sdf.format(date) + "-01";
    }

    /**
     * 获取6个月前的1号字符串
     *
     * @return
     */
    public static String getSixMonth() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM");// 格式化日期
        date.setMonth(date.getMonth() - 6);
        return sdf.format(date) + "-01";
    }

    public Result com_sitech_acctmgr_inter_invoice_IPreInvApplyNumQry_qry(String contract_no) {
        JSONObject root = new JSONObject();
        root.put("CONTRACT_NO", contract_no);
        String json = ESBReqMsgUtil.packMsgByRoute("12", contract_no, root);
        System.out.println("包装后拼接的参数为==" + json);
        if (isES) {
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38 + "com_sitech_acctmgr_inter_invoice_IPreInvApplyNumQry_qry", json, "UTF-8");
        }
        //本地测试
        String resultStr = CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172 + "com_sitech_acctmgr_inter_invoice_IPreInvApplyNumQry_qry", json);
        return HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }


    public List<PreinvApply> getPreinvApplys() {
        // TODO Auto-generated method stub
        String sql = "select * from PreinvApply WHERE REGEXP_LIKE(taxAddress, '[一-龥]') ";
        return this.getSession().createSQLQuery(sql).addEntity(PreinvApply.class).list();
    }

    public List<ActivityPreinvApply> getActivityApplys() {
        String sql = "select * from ActivityPreinvApply ";
        return this.getSession().createSQLQuery(sql).addEntity(ActivityPreinvApply.class).list();
    }

    /**
     * 修改开票信息
     */
    public ActivityPreinvApply updatePreinvApply(ActivityPreinvApply apply) {
        try {
            Session session = this.getSession();
            session.update(apply);
            session.flush();
            return apply;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ICTApplication> getICTApplications() {
        String sql = "select * from ICTApplication ";
        return this.getSession().createSQLQuery(sql).addEntity(ICTApplication.class).list();
    }

    public ICTApplication updateICTApplication(ICTApplication ict) {
        try {
            Session session = this.getSession();
            session.update(ict);
            return ict;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }


    public ManualInvApply updateManualInvApply(ManualInvApply miy) {
        try {
            Session session = this.getSession();
            session.update(miy);
            return miy;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<ManualInvApply> getManualInvApplys() {
        String sql = "select * from ManualInvApply ";
        return this.getSession().createSQLQuery(sql).addEntity(ManualInvApply.class).list();
    }

    /*
     * <AUTHOR>
     * @Date 2023/11/24 15:12
     * @Description 更新预开票明细状态
     **/
    public void updateInvState(String invNo) {
        String sql = "UPDATE PREINVAPPLYDET SET INVSTATE = '2' WHERE INVNO = ?";
        Session session = this.getSession();
        session.createSQLQuery(sql).setString(0, invNo).executeUpdate();
        session.flush();
    }

    /*
     * <AUTHOR>
     * @Date 2023/12/6 11:06
     * @Description 预开票支撑人员分页查询
     **/
    public LayuiPage getStaffManagementList(LayuiPage page, String bossNo) {
        String sql = " SELECT * FROM STAFF_MANAGEMENT  ";

        //统一信用代码
        if (bossNo != null && !"".equals(bossNo)) {
            sql += " WHERE BOSS_NO = '" + bossNo + "' ";
        }

        sql += "  ORDER BY CREATE_DATE DESC ";
        page.setCount(getCount("select count(0) from (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /*
     * <AUTHOR>
     * @Date 2023/12/6 15:45
     * @Description 添加或修改支撑人员数据
     **/
    public StaffManagement saveOrUpdateStaff(StaffManagement sta) {
        Session session = this.getSession();
        session.saveOrUpdate(sta);
        session.flush();
        return sta;
    }

    /*
     * <AUTHOR>
     * @Date 2023/12/6 16:18
     * @Description 删除支撑人员信息
     **/
    public void deleteStaff(String id) {
        String sql = "DELETE FROM STAFF_MANAGEMENT WHERE id =?";
        Session session = this.getSession();
        session.createSQLQuery(sql).setString(0, id).executeUpdate();
        session.flush();
    }

    /*
     * <AUTHOR>
     * @Date 2023/12/7 15:48
     * @Description 查询支撑人员
     **/
    public StaffManagement getStaff(String bossNo) {
        String sql = "select * from STAFF_MANAGEMENT where boss_no = ?";
        return (StaffManagement) getSession().createSQLQuery(sql).addEntity(StaffManagement.class).setString(0, bossNo).uniqueResult();
    }

    /*
     * <AUTHOR>
     * @Date 2023/12/7 15:48
     * @Description 查询用户信息
     **/
    public SystemUser getUser(String bossNo) {
        String sql = "select * from afr_systemuser where bossusername=?";
        return (SystemUser) getSession().createSQLQuery(sql).addEntity(SystemUser.class).setString(0, bossNo).uniqueResult();
    }

    public List<PreinvApplyMarking> queryTheMarkingWorkOrderAccordingToTheUser(String userRow){
        String sql = "select  * from PREINVAPPLYMARKING pr where pr.STATE in('0','3') and pr.APPLICANT_ID=? ";
        return this.getSession().createSQLQuery(sql).addEntity(PreinvApplyMarking.class).setString(0,userRow).list();
    }

    /**
     * 账户关系查询
     * 2024/10/29 11:30
     */
    public  Result getContractOrPhoneNo(String contractNo,String phoneNo){
        JSONObject busiInfoContent = new JSONObject();
        busiInfoContent.put("SVC_NAME","conChk4Order");
        busiInfoContent.put("CONTRACT_NO",contractNo);
        busiInfoContent.put("PHONE_NO",phoneNo);
        JSONObject bodyContent=new JSONObject();
        bodyContent.put("BUSI_INFO",busiInfoContent);
        // 使用工具类封装请求报文
        String paras= ESBReqMsgUtil.packMsgByRoute("12",contractNo,bodyContent);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"com_sitech_ordersvc_person_atom_inter_s1018_IDynamicSqlQryAoSvc_qry",paras,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_38+"com_sitech_ordersvc_person_atom_inter_s1018_IDynamicSqlQryAoSvc_qry", paras);
        Result result=HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
        logger.info("===>:"+result);
        return  result;
    }
//Input length must be multiple of 8 when decrypting with padded cipher
    public static void main(String[] args) throws Exception {
        String str=".";
        /*for(int i=0;i<5;i++){
            str= DataCompilationUtil.encryptFromBase64(str);
            System.out.println(str);
        }*/
        //String str=new String(decrypt(KEY.getBytes(), Base64.decodeBase64("#")), "UTF-8");
        System.out.println("这是解密以后的数据："+decryptLayers("123132"));
    }

    // 判断字符串中是否包含中文字符
    public static boolean containsChinese(String str) {
        // 正则表达式匹配中文字符（包括常见的汉字）
        String regex = "[\u4e00-\u9fa5]";
        return str.matches(".*" + regex + ".*");
    }

    // 解密函数，逐层解密，直到发现中文字符
    public static String decryptLayers(String encryptedData) {
        if(encryptedData!=null&&!"".equals(encryptedData)) {
            String decryptedData = encryptedData;
            if (containsChinese(decryptedData)) {
                return encryptedData;
            } else {
                //if(encryptedData.length()>5){
                while (true) {
                    // 解密当前层
                    decryptedData = DataCompilationUtil.decryptFromBase64((decryptedData));
                    if (decryptedData != null && !"".equals(decryptedData)) {
                        System.out.println(decryptedData);
                        if (!decryptFromBase64(decryptedData)) {
                            break;
                        } else {
                            // 判断解密后的数据是否包含中文
                            if (containsChinese(decryptedData)) {
                                // 如果包含中文字符，停止解密
                                break;
                            }
                        }
                    }
                }
                return decryptedData;
            /*}else{
                return encryptedData;
            }*/
            }
        }else{
            return "";
        }



        /*try {
            String str=new String(decrypt(KEY.getBytes(), Base64.decodeBase64(decryptedData)), "UTF-8");
            if(str!=null&&!"".equals(str)){
                if (decryptFromBase64(decryptedData)) {
                    if (containsChinese(decryptedData)) {
                        return decryptedData;
                    } else {
                        while (true) {
                            // 解密当前层
                            decryptedData = DataCompilationUtil.decryptFromBase64((decryptedData));
                            // 判断解密后的数据是否包含中文
                            if (containsChinese(decryptedData)) {
                                // 如果包含中文字符，停止解密
                                break;
                            }
                        }
                        return decryptedData;
                    }
                } else {
                    System.out.println("这是特殊字符进入");
                    return encryptedData;
                }
            }else{
                return encryptedData;
            }
        } catch (Exception e) {
            return encryptedData;
        }*/
    }

    private static final String ALGORITHM = "DESede";
    private static final String KEY = "l0m8qnucCOw67IaJl0m8qnuc";
    public static boolean decryptFromBase64(String src){
        try {
            if(src!=null&&!"".equals(src)){
                String str= new String(decrypt(KEY.getBytes(), Base64.decodeBase64(src)), "UTF-8");
                if(str==null||"".equals(str)){
                    return false;
                }else{
                    return true;
                }
            }else{
                return false;
            }
        } catch (Exception ex) {
            logger.error("字段解密错误"+ex.getMessage());
            return false;
        }
    }

    public static byte[] decrypt(byte[] keybyte, byte[] src) throws Exception {
        // 生成密钥
        SecretKey deskey = new SecretKeySpec(keybyte, ALGORITHM);
        // 解密
        Cipher c1 = Cipher.getInstance(ALGORITHM);
        c1.init(Cipher.DECRYPT_MODE, deskey);
        return c1.doFinal(src);
    }
}