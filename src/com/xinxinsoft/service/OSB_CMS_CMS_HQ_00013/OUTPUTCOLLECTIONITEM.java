package com.xinxinsoft.service.OSB_CMS_CMS_HQ_00013;

import java.math.BigDecimal;
import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlSchemaType;
import javax.xml.bind.annotation.XmlType;
import javax.xml.datatype.XMLGregorianCalendar;

/**
 * <p>
 * Java class for OUTPUTCOLLECTION_ITEM complex type.
 * 
 * <p>
 * The following schema fragment specifies the expected content contained within
 * this class.
 * 
 * <pre>
 * &lt;complexType name="OUTPUTCOLLECTION_ITEM">
 *   &lt;complexContent>
 *     &lt;restriction base="{http://www.w3.org/2001/XMLSchema}anyType">
 *       &lt;sequence>
 *         &lt;element name="CONTRACT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTRACT_SERIAL_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTRACT_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PROVINCE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_COMPANY_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_COMPANY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_DEPT_DEPT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_USER_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_DISPLAY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTACT_TEL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CREATE_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="SIGNATORY_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="OTHER_SIGNATORY_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="CHECK_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="APPROVE_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="SIGNED_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="ARC_TIME" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="FINISH_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="STATUS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ARCHIVE_STATUS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DELIVE_STATUE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PAYMENT_INFO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="START_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="END_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="CONTRACT_PROPERTY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTRACT_SUBJECT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SUPERIOR_SIGNATURE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RETROACTIVE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SUP_AGREEMENT_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OLD_CONTRACT_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OLD_CONTRACT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="TRADE_ORDER" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SETTLEMENT_AUDIT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="INC_EXP_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTRACT_CATEGORY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTRACT_CATEGORY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SMALL_CATEGORY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="SMALL_CATEGORY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXPENSE_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RELATEND_CONTRACT_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RELATEND_CONTRACT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MODEL_ATTR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MODEL_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MODEL_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="MODEL_LEVEL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="POSTPONE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="POSTPONE_DURATION" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="POSTPONE_NUM" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="PRIORITY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXECUTOR" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXECUTOR_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXECUT_COMPANY_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXECUT_COMPANY_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXECUTE_DEPT_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXECUTE_DEPT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_PUR_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PO_PRJ_NUMBER" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="PO_PRJ_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="FRAMEWORK_CONTRACT_NO" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="FRAMEWORK_CONTRACT_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTRACT_CONTENT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CURRENCY" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="EXCHANGE_RATE" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="AMOUNT_TYPE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ORIGINAL_CURRENCY_AMOUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="AMOUNT_INCLUDING_TAX" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="AMOUNT_IMPROVE_INCLUDING_TAX" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="AMOUNT_EXCLUDING_TAX" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="AMOUNT_IMPROVE_EXCLUDING_TAX" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="TAX_AMOUNT" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="IMPROVE_TAX" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="PAYMENT_DEPOSIT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DEPOSIT" type="{http://www.w3.org/2001/XMLSchema}decimal"/>
 *         &lt;element name="OTHER_SOURCE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="OTHER_CHOOSE_MODEL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DEBIT_DESCRIPTION" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_SITE_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_SITE_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_CONTACTS" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_CONTACT_MODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="BANK_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="ACCOUNT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="RANCH_BANK" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="VENDOR_CREDENTIAL_URL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DISPUTE_FLAG" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="DISPUTE_DESCRIPTION" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="CONTRACT_URL" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="FRAMEWORK_PROTOCOL_CODE" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="FRAMEWORK_PROTOCOL_NAME" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *         &lt;element name="LAST_UPDATE_DATE" type="{http://www.w3.org/2001/XMLSchema}dateTime"/>
 *         &lt;element name="MANY_PROTOCAL" type="{http://soa.cmcc.com/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv}MANY_PROTOCAL"/>
 *         &lt;element name="OTHER_EXECUSE_DEPT" type="{http://soa.cmcc.com/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv}OTHER_EXECUSE_DEPT"/>
 *         &lt;element name="OTHER_EXECUSE_PERSON" type="{http://soa.cmcc.com/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv}OTHER_EXECUSE_PERSON"/>
 *         &lt;element name="REVENUE_DETAIL" type="{http://soa.cmcc.com/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv}REVENUE_DETAIL"/>
 *         &lt;element name="OUTPUT_EXT" type="{http://www.w3.org/2001/XMLSchema}string"/>
 *       &lt;/sequence>
 *     &lt;/restriction>
 *   &lt;/complexContent>
 * &lt;/complexType>
 * </pre>
 * 
 * 
 */
@XmlAccessorType(XmlAccessType.FIELD)
@XmlType(name = "OUTPUTCOLLECTION_ITEM", propOrder = { "contractname",
		"contractserialno", "contractno", "provincecode", "createcompanycode",
		"createcompanyname", "createdeptcode", "createdeptdept",
		"createusername", "createdisplayname", "contacttel", "createdate",
		"signatorydate", "othersignatorydate", "checkdate", "approvedate",
		"signeddate", "arctime", "finishdate", "status", "archivestatus",
		"delivestatue", "paymentinfo", "startdate", "enddate",
		"contractproperty", "contractsubject", "superiorsignature",
		"retroactive", "supagreementtype", "oldcontractno", "oldcontractname",
		"tradeorder", "settlementaudit", "incexptype", "contractcategory",
		"contractcategoryname", "smallcategory", "smallcategoryname",
		"expensetype", "relatendcontractno", "relatendcontractname",
		"modelattr", "modelno", "modelname", "modellevel", "postpone",
		"postponeduration", "postponenum", "priority", "executor",
		"executorname", "executcompanycode", "executcompanyname",
		"executedeptcode", "executedeptname", "vendorpurtype", "poprjnumber",
		"poprjname", "frameworkcontractno", "frameworkcontractname",
		"contractcontent", "currency", "exchangerate", "amounttype",
		"originalcurrencyamount", "amountincludingtax",
		"amountimproveincludingtax", "amountexcludingtax",
		"amountimproveexcludingtax", "taxamount", "improvetax",
		"paymentdeposit", "deposit", "othersource", "otherchoosemodel",
		"debitdescription", "vendorcode", "vendorname", "vendorsitecode",
		"vendorsitename", "vendorcontacts", "vendorcontactmode", "bankname",
		"account", "ranchbank", "vendorcredentialurl", "disputeflag",
		"disputedescription", "contracturl", "frameworkprotocolcode",
		"frameworkprotocolname", "lastupdatedate", "manyprotocal",
		"otherexecusedept", "otherexecuseperson", "revenuedetail", "outputext" })
public class OUTPUTCOLLECTIONITEM {

	@XmlElement(name = "CONTRACT_NAME", required = true, nillable = true)
	protected String contractname;
	@XmlElement(name = "CONTRACT_SERIAL_NO", required = true, nillable = true)
	protected String contractserialno;
	@XmlElement(name = "CONTRACT_NO", required = true, nillable = true)
	protected String contractno;
	@XmlElement(name = "PROVINCE_CODE", required = true, nillable = true)
	protected String provincecode;
	@XmlElement(name = "CREATE_COMPANY_CODE", required = true, nillable = true)
	protected String createcompanycode;
	@XmlElement(name = "CREATE_COMPANY_NAME", required = true, nillable = true)
	protected String createcompanyname;
	@XmlElement(name = "CREATE_DEPT_CODE", required = true, nillable = true)
	protected String createdeptcode;
	@XmlElement(name = "CREATE_DEPT_DEPT", required = true, nillable = true)
	protected String createdeptdept;
	@XmlElement(name = "CREATE_USER_NAME", required = true, nillable = true)
	protected String createusername;
	@XmlElement(name = "CREATE_DISPLAY_NAME", required = true, nillable = true)
	protected String createdisplayname;
	@XmlElement(name = "CONTACT_TEL", required = true, nillable = true)
	protected String contacttel;
	@XmlElement(name = "CREATE_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar createdate;
	@XmlElement(name = "SIGNATORY_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar signatorydate;
	@XmlElement(name = "OTHER_SIGNATORY_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar othersignatorydate;
	@XmlElement(name = "CHECK_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar checkdate;
	@XmlElement(name = "APPROVE_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar approvedate;
	@XmlElement(name = "SIGNED_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar signeddate;
	@XmlElement(name = "ARC_TIME", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar arctime;
	@XmlElement(name = "FINISH_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar finishdate;
	@XmlElement(name = "STATUS", required = true, nillable = true)
	protected String status;
	@XmlElement(name = "ARCHIVE_STATUS", required = true, nillable = true)
	protected String archivestatus;
	@XmlElement(name = "DELIVE_STATUE", required = true, nillable = true)
	protected String delivestatue;
	@XmlElement(name = "PAYMENT_INFO", required = true, nillable = true)
	protected String paymentinfo;
	@XmlElement(name = "START_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar startdate;
	@XmlElement(name = "END_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar enddate;
	@XmlElement(name = "CONTRACT_PROPERTY", required = true, nillable = true)
	protected String contractproperty;
	@XmlElement(name = "CONTRACT_SUBJECT", required = true, nillable = true)
	protected String contractsubject;
	@XmlElement(name = "SUPERIOR_SIGNATURE", required = true, nillable = true)
	protected String superiorsignature;
	@XmlElement(name = "RETROACTIVE", required = true, nillable = true)
	protected String retroactive;
	@XmlElement(name = "SUP_AGREEMENT_TYPE", required = true, nillable = true)
	protected String supagreementtype;
	@XmlElement(name = "OLD_CONTRACT_NO", required = true, nillable = true)
	protected String oldcontractno;
	@XmlElement(name = "OLD_CONTRACT_NAME", required = true, nillable = true)
	protected String oldcontractname;
	@XmlElement(name = "TRADE_ORDER", required = true, nillable = true)
	protected String tradeorder;
	@XmlElement(name = "SETTLEMENT_AUDIT", required = true, nillable = true)
	protected String settlementaudit;
	@XmlElement(name = "INC_EXP_TYPE", required = true, nillable = true)
	protected String incexptype;
	@XmlElement(name = "CONTRACT_CATEGORY", required = true, nillable = true)
	protected String contractcategory;
	@XmlElement(name = "CONTRACT_CATEGORY_NAME", required = true, nillable = true)
	protected String contractcategoryname;
	@XmlElement(name = "SMALL_CATEGORY", required = true, nillable = true)
	protected String smallcategory;
	@XmlElement(name = "SMALL_CATEGORY_NAME", required = true, nillable = true)
	protected String smallcategoryname;
	@XmlElement(name = "EXPENSE_TYPE", required = true, nillable = true)
	protected String expensetype;
	@XmlElement(name = "RELATEND_CONTRACT_NO", required = true, nillable = true)
	protected String relatendcontractno;
	@XmlElement(name = "RELATEND_CONTRACT_NAME", required = true, nillable = true)
	protected String relatendcontractname;
	@XmlElement(name = "MODEL_ATTR", required = true, nillable = true)
	protected String modelattr;
	@XmlElement(name = "MODEL_NO", required = true, nillable = true)
	protected String modelno;
	@XmlElement(name = "MODEL_NAME", required = true, nillable = true)
	protected String modelname;
	@XmlElement(name = "MODEL_LEVEL", required = true, nillable = true)
	protected String modellevel;
	@XmlElement(name = "POSTPONE", required = true, nillable = true)
	protected String postpone;
	@XmlElement(name = "POSTPONE_DURATION", required = true, nillable = true)
	protected BigDecimal postponeduration;
	@XmlElement(name = "POSTPONE_NUM", required = true, nillable = true)
	protected BigDecimal postponenum;
	@XmlElement(name = "PRIORITY", required = true, nillable = true)
	protected String priority;
	@XmlElement(name = "EXECUTOR", required = true, nillable = true)
	protected String executor;
	@XmlElement(name = "EXECUTOR_NAME", required = true, nillable = true)
	protected String executorname;
	@XmlElement(name = "EXECUT_COMPANY_CODE", required = true, nillable = true)
	protected String executcompanycode;
	@XmlElement(name = "EXECUT_COMPANY_NAME", required = true, nillable = true)
	protected String executcompanyname;
	@XmlElement(name = "EXECUTE_DEPT_CODE", required = true, nillable = true)
	protected String executedeptcode;
	@XmlElement(name = "EXECUTE_DEPT_NAME", required = true, nillable = true)
	protected String executedeptname;
	@XmlElement(name = "VENDOR_PUR_TYPE", required = true, nillable = true)
	protected String vendorpurtype;
	@XmlElement(name = "PO_PRJ_NUMBER", required = true, nillable = true)
	protected String poprjnumber;
	@XmlElement(name = "PO_PRJ_NAME", required = true, nillable = true)
	protected String poprjname;
	@XmlElement(name = "FRAMEWORK_CONTRACT_NO", required = true, nillable = true)
	protected String frameworkcontractno;
	@XmlElement(name = "FRAMEWORK_CONTRACT_NAME", required = true, nillable = true)
	protected String frameworkcontractname;
	@XmlElement(name = "CONTRACT_CONTENT", required = true, nillable = true)
	protected String contractcontent;
	@XmlElement(name = "CURRENCY", required = true, nillable = true)
	protected String currency;
	@XmlElement(name = "EXCHANGE_RATE", required = true, nillable = true)
	protected BigDecimal exchangerate;
	@XmlElement(name = "AMOUNT_TYPE", required = true, nillable = true)
	protected String amounttype;
	@XmlElement(name = "ORIGINAL_CURRENCY_AMOUNT", required = true, nillable = true)
	protected BigDecimal originalcurrencyamount;
	@XmlElement(name = "AMOUNT_INCLUDING_TAX", required = true, nillable = true)
	protected BigDecimal amountincludingtax;
	@XmlElement(name = "AMOUNT_IMPROVE_INCLUDING_TAX", required = true, nillable = true)
	protected BigDecimal amountimproveincludingtax;
	@XmlElement(name = "AMOUNT_EXCLUDING_TAX", required = true, nillable = true)
	protected BigDecimal amountexcludingtax;
	@XmlElement(name = "AMOUNT_IMPROVE_EXCLUDING_TAX", required = true, nillable = true)
	protected BigDecimal amountimproveexcludingtax;
	@XmlElement(name = "TAX_AMOUNT", required = true, nillable = true)
	protected BigDecimal taxamount;
	@XmlElement(name = "IMPROVE_TAX", required = true, nillable = true)
	protected BigDecimal improvetax;
	@XmlElement(name = "PAYMENT_DEPOSIT", required = true, nillable = true)
	protected String paymentdeposit;
	@XmlElement(name = "DEPOSIT", required = true, nillable = true)
	protected BigDecimal deposit;
	@XmlElement(name = "OTHER_SOURCE", required = true, nillable = true)
	protected String othersource;
	@XmlElement(name = "OTHER_CHOOSE_MODEL", required = true, nillable = true)
	protected String otherchoosemodel;
	@XmlElement(name = "DEBIT_DESCRIPTION", required = true, nillable = true)
	protected String debitdescription;
	@XmlElement(name = "VENDOR_CODE", required = true, nillable = true)
	protected String vendorcode;
	@XmlElement(name = "VENDOR_NAME", required = true, nillable = true)
	protected String vendorname;
	@XmlElement(name = "VENDOR_SITE_CODE", required = true, nillable = true)
	protected String vendorsitecode;
	@XmlElement(name = "VENDOR_SITE_NAME", required = true, nillable = true)
	protected String vendorsitename;
	@XmlElement(name = "VENDOR_CONTACTS", required = true, nillable = true)
	protected String vendorcontacts;
	@XmlElement(name = "VENDOR_CONTACT_MODE", required = true, nillable = true)
	protected String vendorcontactmode;
	@XmlElement(name = "BANK_NAME", required = true, nillable = true)
	protected String bankname;
	@XmlElement(name = "ACCOUNT", required = true, nillable = true)
	protected String account;
	@XmlElement(name = "RANCH_BANK", required = true, nillable = true)
	protected String ranchbank;
	@XmlElement(name = "VENDOR_CREDENTIAL_URL", required = true, nillable = true)
	protected String vendorcredentialurl;
	@XmlElement(name = "DISPUTE_FLAG", required = true, nillable = true)
	protected String disputeflag;
	@XmlElement(name = "DISPUTE_DESCRIPTION", required = true, nillable = true)
	protected String disputedescription;
	@XmlElement(name = "CONTRACT_URL", required = true, nillable = true)
	protected String contracturl;
	@XmlElement(name = "FRAMEWORK_PROTOCOL_CODE", required = true, nillable = true)
	protected String frameworkprotocolcode;
	@XmlElement(name = "FRAMEWORK_PROTOCOL_NAME", required = true, nillable = true)
	protected String frameworkprotocolname;
	@XmlElement(name = "LAST_UPDATE_DATE", required = true, nillable = true)
	@XmlSchemaType(name = "dateTime")
	protected XMLGregorianCalendar lastupdatedate;
	@XmlElement(name = "MANY_PROTOCAL", required = true)
	protected MANYPROTOCAL manyprotocal;
	@XmlElement(name = "OTHER_EXECUSE_DEPT", required = true)
	protected OTHEREXECUSEDEPT otherexecusedept;
	@XmlElement(name = "OTHER_EXECUSE_PERSON", required = true)
	protected OTHEREXECUSEPERSON otherexecuseperson;
	@XmlElement(name = "REVENUE_DETAIL", required = true)
	protected REVENUEDETAIL revenuedetail;
	@XmlElement(name = "OUTPUT_EXT", required = true, nillable = true)
	protected String outputext;

	/**
	 * Gets the value of the contractname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTNAME() {
		return contractname;
	}

	/**
	 * Sets the value of the contractname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTNAME(String value) {
		this.contractname = value;
	}

	/**
	 * Gets the value of the contractserialno property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTSERIALNO() {
		return contractserialno;
	}

	/**
	 * Sets the value of the contractserialno property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTSERIALNO(String value) {
		this.contractserialno = value;
	}

	/**
	 * Gets the value of the contractno property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTNO() {
		return contractno;
	}

	/**
	 * Sets the value of the contractno property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTNO(String value) {
		this.contractno = value;
	}

	/**
	 * Gets the value of the provincecode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPROVINCECODE() {
		return provincecode;
	}

	/**
	 * Sets the value of the provincecode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPROVINCECODE(String value) {
		this.provincecode = value;
	}

	/**
	 * Gets the value of the createcompanycode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCREATECOMPANYCODE() {
		return createcompanycode;
	}

	/**
	 * Sets the value of the createcompanycode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCREATECOMPANYCODE(String value) {
		this.createcompanycode = value;
	}

	/**
	 * Gets the value of the createcompanyname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCREATECOMPANYNAME() {
		return createcompanyname;
	}

	/**
	 * Sets the value of the createcompanyname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCREATECOMPANYNAME(String value) {
		this.createcompanyname = value;
	}

	/**
	 * Gets the value of the createdeptcode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCREATEDEPTCODE() {
		return createdeptcode;
	}

	/**
	 * Sets the value of the createdeptcode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCREATEDEPTCODE(String value) {
		this.createdeptcode = value;
	}

	/**
	 * Gets the value of the createdeptdept property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCREATEDEPTDEPT() {
		return createdeptdept;
	}

	/**
	 * Sets the value of the createdeptdept property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCREATEDEPTDEPT(String value) {
		this.createdeptdept = value;
	}

	/**
	 * Gets the value of the createusername property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCREATEUSERNAME() {
		return createusername;
	}

	/**
	 * Sets the value of the createusername property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCREATEUSERNAME(String value) {
		this.createusername = value;
	}

	/**
	 * Gets the value of the createdisplayname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCREATEDISPLAYNAME() {
		return createdisplayname;
	}

	/**
	 * Sets the value of the createdisplayname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCREATEDISPLAYNAME(String value) {
		this.createdisplayname = value;
	}

	/**
	 * Gets the value of the contacttel property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTACTTEL() {
		return contacttel;
	}

	/**
	 * Sets the value of the contacttel property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTACTTEL(String value) {
		this.contacttel = value;
	}

	/**
	 * Gets the value of the createdate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getCREATEDATE() {
		return createdate;
	}

	/**
	 * Sets the value of the createdate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setCREATEDATE(XMLGregorianCalendar value) {
		this.createdate = value;
	}

	/**
	 * Gets the value of the signatorydate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getSIGNATORYDATE() {
		return signatorydate;
	}

	/**
	 * Sets the value of the signatorydate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setSIGNATORYDATE(XMLGregorianCalendar value) {
		this.signatorydate = value;
	}

	/**
	 * Gets the value of the othersignatorydate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getOTHERSIGNATORYDATE() {
		return othersignatorydate;
	}

	/**
	 * Sets the value of the othersignatorydate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setOTHERSIGNATORYDATE(XMLGregorianCalendar value) {
		this.othersignatorydate = value;
	}

	/**
	 * Gets the value of the checkdate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getCHECKDATE() {
		return checkdate;
	}

	/**
	 * Sets the value of the checkdate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setCHECKDATE(XMLGregorianCalendar value) {
		this.checkdate = value;
	}

	/**
	 * Gets the value of the approvedate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getAPPROVEDATE() {
		return approvedate;
	}

	/**
	 * Sets the value of the approvedate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setAPPROVEDATE(XMLGregorianCalendar value) {
		this.approvedate = value;
	}

	/**
	 * Gets the value of the signeddate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getSIGNEDDATE() {
		return signeddate;
	}

	/**
	 * Sets the value of the signeddate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setSIGNEDDATE(XMLGregorianCalendar value) {
		this.signeddate = value;
	}

	/**
	 * Gets the value of the arctime property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getARCTIME() {
		return arctime;
	}

	/**
	 * Sets the value of the arctime property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setARCTIME(XMLGregorianCalendar value) {
		this.arctime = value;
	}

	/**
	 * Gets the value of the finishdate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getFINISHDATE() {
		return finishdate;
	}

	/**
	 * Sets the value of the finishdate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setFINISHDATE(XMLGregorianCalendar value) {
		this.finishdate = value;
	}

	/**
	 * Gets the value of the status property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSTATUS() {
		return status;
	}

	/**
	 * Sets the value of the status property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSTATUS(String value) {
		this.status = value;
	}

	/**
	 * Gets the value of the archivestatus property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getARCHIVESTATUS() {
		return archivestatus;
	}

	/**
	 * Sets the value of the archivestatus property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setARCHIVESTATUS(String value) {
		this.archivestatus = value;
	}

	/**
	 * Gets the value of the delivestatue property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDELIVESTATUE() {
		return delivestatue;
	}

	/**
	 * Sets the value of the delivestatue property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDELIVESTATUE(String value) {
		this.delivestatue = value;
	}

	/**
	 * Gets the value of the paymentinfo property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPAYMENTINFO() {
		return paymentinfo;
	}

	/**
	 * Sets the value of the paymentinfo property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPAYMENTINFO(String value) {
		this.paymentinfo = value;
	}

	/**
	 * Gets the value of the startdate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getSTARTDATE() {
		return startdate;
	}

	/**
	 * Sets the value of the startdate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setSTARTDATE(XMLGregorianCalendar value) {
		this.startdate = value;
	}

	/**
	 * Gets the value of the enddate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getENDDATE() {
		return enddate;
	}

	/**
	 * Sets the value of the enddate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setENDDATE(XMLGregorianCalendar value) {
		this.enddate = value;
	}

	/**
	 * Gets the value of the contractproperty property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTPROPERTY() {
		return contractproperty;
	}

	/**
	 * Sets the value of the contractproperty property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTPROPERTY(String value) {
		this.contractproperty = value;
	}

	/**
	 * Gets the value of the contractsubject property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTSUBJECT() {
		return contractsubject;
	}

	/**
	 * Sets the value of the contractsubject property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTSUBJECT(String value) {
		this.contractsubject = value;
	}

	/**
	 * Gets the value of the superiorsignature property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSUPERIORSIGNATURE() {
		return superiorsignature;
	}

	/**
	 * Sets the value of the superiorsignature property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSUPERIORSIGNATURE(String value) {
		this.superiorsignature = value;
	}

	/**
	 * Gets the value of the retroactive property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRETROACTIVE() {
		return retroactive;
	}

	/**
	 * Sets the value of the retroactive property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRETROACTIVE(String value) {
		this.retroactive = value;
	}

	/**
	 * Gets the value of the supagreementtype property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSUPAGREEMENTTYPE() {
		return supagreementtype;
	}

	/**
	 * Sets the value of the supagreementtype property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSUPAGREEMENTTYPE(String value) {
		this.supagreementtype = value;
	}

	/**
	 * Gets the value of the oldcontractno property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOLDCONTRACTNO() {
		return oldcontractno;
	}

	/**
	 * Sets the value of the oldcontractno property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOLDCONTRACTNO(String value) {
		this.oldcontractno = value;
	}

	/**
	 * Gets the value of the oldcontractname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOLDCONTRACTNAME() {
		return oldcontractname;
	}

	/**
	 * Sets the value of the oldcontractname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOLDCONTRACTNAME(String value) {
		this.oldcontractname = value;
	}

	/**
	 * Gets the value of the tradeorder property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getTRADEORDER() {
		return tradeorder;
	}

	/**
	 * Sets the value of the tradeorder property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setTRADEORDER(String value) {
		this.tradeorder = value;
	}

	/**
	 * Gets the value of the settlementaudit property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSETTLEMENTAUDIT() {
		return settlementaudit;
	}

	/**
	 * Sets the value of the settlementaudit property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSETTLEMENTAUDIT(String value) {
		this.settlementaudit = value;
	}

	/**
	 * Gets the value of the incexptype property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getINCEXPTYPE() {
		return incexptype;
	}

	/**
	 * Sets the value of the incexptype property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setINCEXPTYPE(String value) {
		this.incexptype = value;
	}

	/**
	 * Gets the value of the contractcategory property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTCATEGORY() {
		return contractcategory;
	}

	/**
	 * Sets the value of the contractcategory property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTCATEGORY(String value) {
		this.contractcategory = value;
	}

	/**
	 * Gets the value of the contractcategoryname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTCATEGORYNAME() {
		return contractcategoryname;
	}

	/**
	 * Sets the value of the contractcategoryname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTCATEGORYNAME(String value) {
		this.contractcategoryname = value;
	}

	/**
	 * Gets the value of the smallcategory property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSMALLCATEGORY() {
		return smallcategory;
	}

	/**
	 * Sets the value of the smallcategory property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSMALLCATEGORY(String value) {
		this.smallcategory = value;
	}

	/**
	 * Gets the value of the smallcategoryname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getSMALLCATEGORYNAME() {
		return smallcategoryname;
	}

	/**
	 * Sets the value of the smallcategoryname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setSMALLCATEGORYNAME(String value) {
		this.smallcategoryname = value;
	}

	/**
	 * Gets the value of the expensetype property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEXPENSETYPE() {
		return expensetype;
	}

	/**
	 * Sets the value of the expensetype property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEXPENSETYPE(String value) {
		this.expensetype = value;
	}

	/**
	 * Gets the value of the relatendcontractno property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRELATENDCONTRACTNO() {
		return relatendcontractno;
	}

	/**
	 * Sets the value of the relatendcontractno property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRELATENDCONTRACTNO(String value) {
		this.relatendcontractno = value;
	}

	/**
	 * Gets the value of the relatendcontractname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRELATENDCONTRACTNAME() {
		return relatendcontractname;
	}

	/**
	 * Sets the value of the relatendcontractname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRELATENDCONTRACTNAME(String value) {
		this.relatendcontractname = value;
	}

	/**
	 * Gets the value of the modelattr property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMODELATTR() {
		return modelattr;
	}

	/**
	 * Sets the value of the modelattr property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMODELATTR(String value) {
		this.modelattr = value;
	}

	/**
	 * Gets the value of the modelno property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMODELNO() {
		return modelno;
	}

	/**
	 * Sets the value of the modelno property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMODELNO(String value) {
		this.modelno = value;
	}

	/**
	 * Gets the value of the modelname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMODELNAME() {
		return modelname;
	}

	/**
	 * Sets the value of the modelname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMODELNAME(String value) {
		this.modelname = value;
	}

	/**
	 * Gets the value of the modellevel property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getMODELLEVEL() {
		return modellevel;
	}

	/**
	 * Sets the value of the modellevel property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setMODELLEVEL(String value) {
		this.modellevel = value;
	}

	/**
	 * Gets the value of the postpone property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPOSTPONE() {
		return postpone;
	}

	/**
	 * Sets the value of the postpone property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPOSTPONE(String value) {
		this.postpone = value;
	}

	/**
	 * Gets the value of the postponeduration property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getPOSTPONEDURATION() {
		return postponeduration;
	}

	/**
	 * Sets the value of the postponeduration property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setPOSTPONEDURATION(BigDecimal value) {
		this.postponeduration = value;
	}

	/**
	 * Gets the value of the postponenum property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getPOSTPONENUM() {
		return postponenum;
	}

	/**
	 * Sets the value of the postponenum property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setPOSTPONENUM(BigDecimal value) {
		this.postponenum = value;
	}

	/**
	 * Gets the value of the priority property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPRIORITY() {
		return priority;
	}

	/**
	 * Sets the value of the priority property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPRIORITY(String value) {
		this.priority = value;
	}

	/**
	 * Gets the value of the executor property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEXECUTOR() {
		return executor;
	}

	/**
	 * Sets the value of the executor property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEXECUTOR(String value) {
		this.executor = value;
	}

	/**
	 * Gets the value of the executorname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEXECUTORNAME() {
		return executorname;
	}

	/**
	 * Sets the value of the executorname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEXECUTORNAME(String value) {
		this.executorname = value;
	}

	/**
	 * Gets the value of the executcompanycode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEXECUTCOMPANYCODE() {
		return executcompanycode;
	}

	/**
	 * Sets the value of the executcompanycode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEXECUTCOMPANYCODE(String value) {
		this.executcompanycode = value;
	}

	/**
	 * Gets the value of the executcompanyname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEXECUTCOMPANYNAME() {
		return executcompanyname;
	}

	/**
	 * Sets the value of the executcompanyname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEXECUTCOMPANYNAME(String value) {
		this.executcompanyname = value;
	}

	/**
	 * Gets the value of the executedeptcode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEXECUTEDEPTCODE() {
		return executedeptcode;
	}

	/**
	 * Sets the value of the executedeptcode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEXECUTEDEPTCODE(String value) {
		this.executedeptcode = value;
	}

	/**
	 * Gets the value of the executedeptname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getEXECUTEDEPTNAME() {
		return executedeptname;
	}

	/**
	 * Sets the value of the executedeptname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setEXECUTEDEPTNAME(String value) {
		this.executedeptname = value;
	}

	/**
	 * Gets the value of the vendorpurtype property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORPURTYPE() {
		return vendorpurtype;
	}

	/**
	 * Sets the value of the vendorpurtype property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORPURTYPE(String value) {
		this.vendorpurtype = value;
	}

	/**
	 * Gets the value of the poprjnumber property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPOPRJNUMBER() {
		return poprjnumber;
	}

	/**
	 * Sets the value of the poprjnumber property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPOPRJNUMBER(String value) {
		this.poprjnumber = value;
	}

	/**
	 * Gets the value of the poprjname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPOPRJNAME() {
		return poprjname;
	}

	/**
	 * Sets the value of the poprjname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPOPRJNAME(String value) {
		this.poprjname = value;
	}

	/**
	 * Gets the value of the frameworkcontractno property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getFRAMEWORKCONTRACTNO() {
		return frameworkcontractno;
	}

	/**
	 * Sets the value of the frameworkcontractno property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setFRAMEWORKCONTRACTNO(String value) {
		this.frameworkcontractno = value;
	}

	/**
	 * Gets the value of the frameworkcontractname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getFRAMEWORKCONTRACTNAME() {
		return frameworkcontractname;
	}

	/**
	 * Sets the value of the frameworkcontractname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setFRAMEWORKCONTRACTNAME(String value) {
		this.frameworkcontractname = value;
	}

	/**
	 * Gets the value of the contractcontent property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTCONTENT() {
		return contractcontent;
	}

	/**
	 * Sets the value of the contractcontent property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTCONTENT(String value) {
		this.contractcontent = value;
	}

	/**
	 * Gets the value of the currency property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCURRENCY() {
		return currency;
	}

	/**
	 * Sets the value of the currency property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCURRENCY(String value) {
		this.currency = value;
	}

	/**
	 * Gets the value of the exchangerate property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getEXCHANGERATE() {
		return exchangerate;
	}

	/**
	 * Sets the value of the exchangerate property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setEXCHANGERATE(BigDecimal value) {
		this.exchangerate = value;
	}

	/**
	 * Gets the value of the amounttype property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getAMOUNTTYPE() {
		return amounttype;
	}

	/**
	 * Sets the value of the amounttype property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setAMOUNTTYPE(String value) {
		this.amounttype = value;
	}

	/**
	 * Gets the value of the originalcurrencyamount property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getORIGINALCURRENCYAMOUNT() {
		return originalcurrencyamount;
	}

	/**
	 * Sets the value of the originalcurrencyamount property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setORIGINALCURRENCYAMOUNT(BigDecimal value) {
		this.originalcurrencyamount = value;
	}

	/**
	 * Gets the value of the amountincludingtax property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getAMOUNTINCLUDINGTAX() {
		return amountincludingtax;
	}

	/**
	 * Sets the value of the amountincludingtax property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setAMOUNTINCLUDINGTAX(BigDecimal value) {
		this.amountincludingtax = value;
	}

	/**
	 * Gets the value of the amountimproveincludingtax property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getAMOUNTIMPROVEINCLUDINGTAX() {
		return amountimproveincludingtax;
	}

	/**
	 * Sets the value of the amountimproveincludingtax property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setAMOUNTIMPROVEINCLUDINGTAX(BigDecimal value) {
		this.amountimproveincludingtax = value;
	}

	/**
	 * Gets the value of the amountexcludingtax property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getAMOUNTEXCLUDINGTAX() {
		return amountexcludingtax;
	}

	/**
	 * Sets the value of the amountexcludingtax property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setAMOUNTEXCLUDINGTAX(BigDecimal value) {
		this.amountexcludingtax = value;
	}

	/**
	 * Gets the value of the amountimproveexcludingtax property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getAMOUNTIMPROVEEXCLUDINGTAX() {
		return amountimproveexcludingtax;
	}

	/**
	 * Sets the value of the amountimproveexcludingtax property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setAMOUNTIMPROVEEXCLUDINGTAX(BigDecimal value) {
		this.amountimproveexcludingtax = value;
	}

	/**
	 * Gets the value of the taxamount property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getTAXAMOUNT() {
		return taxamount;
	}

	/**
	 * Sets the value of the taxamount property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setTAXAMOUNT(BigDecimal value) {
		this.taxamount = value;
	}

	/**
	 * Gets the value of the improvetax property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getIMPROVETAX() {
		return improvetax;
	}

	/**
	 * Sets the value of the improvetax property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setIMPROVETAX(BigDecimal value) {
		this.improvetax = value;
	}

	/**
	 * Gets the value of the paymentdeposit property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getPAYMENTDEPOSIT() {
		return paymentdeposit;
	}

	/**
	 * Sets the value of the paymentdeposit property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setPAYMENTDEPOSIT(String value) {
		this.paymentdeposit = value;
	}

	/**
	 * Gets the value of the deposit property.
	 * 
	 * @return possible object is {@link BigDecimal }
	 * 
	 */
	public BigDecimal getDEPOSIT() {
		return deposit;
	}

	/**
	 * Sets the value of the deposit property.
	 * 
	 * @param value
	 *            allowed object is {@link BigDecimal }
	 * 
	 */
	public void setDEPOSIT(BigDecimal value) {
		this.deposit = value;
	}

	/**
	 * Gets the value of the othersource property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOTHERSOURCE() {
		return othersource;
	}

	/**
	 * Sets the value of the othersource property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOTHERSOURCE(String value) {
		this.othersource = value;
	}

	/**
	 * Gets the value of the otherchoosemodel property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOTHERCHOOSEMODEL() {
		return otherchoosemodel;
	}

	/**
	 * Sets the value of the otherchoosemodel property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOTHERCHOOSEMODEL(String value) {
		this.otherchoosemodel = value;
	}

	/**
	 * Gets the value of the debitdescription property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDEBITDESCRIPTION() {
		return debitdescription;
	}

	/**
	 * Sets the value of the debitdescription property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDEBITDESCRIPTION(String value) {
		this.debitdescription = value;
	}

	/**
	 * Gets the value of the vendorcode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORCODE() {
		return vendorcode;
	}

	/**
	 * Sets the value of the vendorcode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORCODE(String value) {
		this.vendorcode = value;
	}

	/**
	 * Gets the value of the vendorname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORNAME() {
		return vendorname;
	}

	/**
	 * Sets the value of the vendorname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORNAME(String value) {
		this.vendorname = value;
	}

	/**
	 * Gets the value of the vendorsitecode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORSITECODE() {
		return vendorsitecode;
	}

	/**
	 * Sets the value of the vendorsitecode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORSITECODE(String value) {
		this.vendorsitecode = value;
	}

	/**
	 * Gets the value of the vendorsitename property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORSITENAME() {
		return vendorsitename;
	}

	/**
	 * Sets the value of the vendorsitename property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORSITENAME(String value) {
		this.vendorsitename = value;
	}

	/**
	 * Gets the value of the vendorcontacts property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORCONTACTS() {
		return vendorcontacts;
	}

	/**
	 * Sets the value of the vendorcontacts property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORCONTACTS(String value) {
		this.vendorcontacts = value;
	}

	/**
	 * Gets the value of the vendorcontactmode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORCONTACTMODE() {
		return vendorcontactmode;
	}

	/**
	 * Sets the value of the vendorcontactmode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORCONTACTMODE(String value) {
		this.vendorcontactmode = value;
	}

	/**
	 * Gets the value of the bankname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getBANKNAME() {
		return bankname;
	}

	/**
	 * Sets the value of the bankname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setBANKNAME(String value) {
		this.bankname = value;
	}

	/**
	 * Gets the value of the account property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getACCOUNT() {
		return account;
	}

	/**
	 * Sets the value of the account property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setACCOUNT(String value) {
		this.account = value;
	}

	/**
	 * Gets the value of the ranchbank property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getRANCHBANK() {
		return ranchbank;
	}

	/**
	 * Sets the value of the ranchbank property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setRANCHBANK(String value) {
		this.ranchbank = value;
	}

	/**
	 * Gets the value of the vendorcredentialurl property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getVENDORCREDENTIALURL() {
		return vendorcredentialurl;
	}

	/**
	 * Sets the value of the vendorcredentialurl property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setVENDORCREDENTIALURL(String value) {
		this.vendorcredentialurl = value;
	}

	/**
	 * Gets the value of the disputeflag property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDISPUTEFLAG() {
		return disputeflag;
	}

	/**
	 * Sets the value of the disputeflag property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDISPUTEFLAG(String value) {
		this.disputeflag = value;
	}

	/**
	 * Gets the value of the disputedescription property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getDISPUTEDESCRIPTION() {
		return disputedescription;
	}

	/**
	 * Sets the value of the disputedescription property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setDISPUTEDESCRIPTION(String value) {
		this.disputedescription = value;
	}

	/**
	 * Gets the value of the contracturl property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getCONTRACTURL() {
		return contracturl;
	}

	/**
	 * Sets the value of the contracturl property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setCONTRACTURL(String value) {
		this.contracturl = value;
	}

	/**
	 * Gets the value of the frameworkprotocolcode property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getFRAMEWORKPROTOCOLCODE() {
		return frameworkprotocolcode;
	}

	/**
	 * Sets the value of the frameworkprotocolcode property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setFRAMEWORKPROTOCOLCODE(String value) {
		this.frameworkprotocolcode = value;
	}

	/**
	 * Gets the value of the frameworkprotocolname property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getFRAMEWORKPROTOCOLNAME() {
		return frameworkprotocolname;
	}

	/**
	 * Sets the value of the frameworkprotocolname property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setFRAMEWORKPROTOCOLNAME(String value) {
		this.frameworkprotocolname = value;
	}

	/**
	 * Gets the value of the lastupdatedate property.
	 * 
	 * @return possible object is {@link XMLGregorianCalendar }
	 * 
	 */
	public XMLGregorianCalendar getLASTUPDATEDATE() {
		return lastupdatedate;
	}

	/**
	 * Sets the value of the lastupdatedate property.
	 * 
	 * @param value
	 *            allowed object is {@link XMLGregorianCalendar }
	 * 
	 */
	public void setLASTUPDATEDATE(XMLGregorianCalendar value) {
		this.lastupdatedate = value;
	}

	/**
	 * Gets the value of the manyprotocal property.
	 * 
	 * @return possible object is {@link MANYPROTOCAL }
	 * 
	 */
	public MANYPROTOCAL getMANYPROTOCAL() {
		return manyprotocal;
	}

	/**
	 * Sets the value of the manyprotocal property.
	 * 
	 * @param value
	 *            allowed object is {@link MANYPROTOCAL }
	 * 
	 */
	public void setMANYPROTOCAL(MANYPROTOCAL value) {
		this.manyprotocal = value;
	}

	/**
	 * Gets the value of the otherexecusedept property.
	 * 
	 * @return possible object is {@link OTHEREXECUSEDEPT }
	 * 
	 */
	public OTHEREXECUSEDEPT getOTHEREXECUSEDEPT() {
		return otherexecusedept;
	}

	/**
	 * Sets the value of the otherexecusedept property.
	 * 
	 * @param value
	 *            allowed object is {@link OTHEREXECUSEDEPT }
	 * 
	 */
	public void setOTHEREXECUSEDEPT(OTHEREXECUSEDEPT value) {
		this.otherexecusedept = value;
	}

	/**
	 * Gets the value of the otherexecuseperson property.
	 * 
	 * @return possible object is {@link OTHEREXECUSEPERSON }
	 * 
	 */
	public OTHEREXECUSEPERSON getOTHEREXECUSEPERSON() {
		return otherexecuseperson;
	}

	/**
	 * Sets the value of the otherexecuseperson property.
	 * 
	 * @param value
	 *            allowed object is {@link OTHEREXECUSEPERSON }
	 * 
	 */
	public void setOTHEREXECUSEPERSON(OTHEREXECUSEPERSON value) {
		this.otherexecuseperson = value;
	}

	/**
	 * Gets the value of the revenuedetail property.
	 * 
	 * @return possible object is {@link REVENUEDETAIL }
	 * 
	 */
	public REVENUEDETAIL getREVENUEDETAIL() {
		return revenuedetail;
	}

	/**
	 * Sets the value of the revenuedetail property.
	 * 
	 * @param value
	 *            allowed object is {@link REVENUEDETAIL }
	 * 
	 */
	public void setREVENUEDETAIL(REVENUEDETAIL value) {
		this.revenuedetail = value;
	}

	/**
	 * Gets the value of the outputext property.
	 * 
	 * @return possible object is {@link String }
	 * 
	 */
	public String getOUTPUTEXT() {
		return outputext;
	}

	/**
	 * Sets the value of the outputext property.
	 * 
	 * @param value
	 *            allowed object is {@link String }
	 * 
	 */
	public void setOUTPUTEXT(String value) {
		this.outputext = value;
	}

}
