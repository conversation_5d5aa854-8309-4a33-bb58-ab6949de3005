package com.xinxinsoft.service.transfer;

import com.xinxinsoft.entity.productTariffElement.ProductInfo;
import com.xinxinsoft.entity.productTariffElement.TariffInfo;
import net.sf.ezmorph.object.DateMorpher;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.util.JSONUtils;
import org.apache.htrace.fasterxml.jackson.databind.ObjectMapper;

import java.io.IOException;
import java.util.Iterator;

public class TransferService {

}
