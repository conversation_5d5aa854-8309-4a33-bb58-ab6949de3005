package com.xinxinsoft.service.SRestoreDeadUser;

import com.xinxinsoft.entity.AllMemberPay.AllPayDesignDet;
import com.xinxinsoft.entity.SRestoreDeadUser.SRestoreDeadUser;
import com.xinxinsoft.entity.SRestoreDeadUser.SRestoreDeadUserDet;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.ESBReqMsgUtil;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import net.sf.json.JSONObject;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;

import java.util.List;
import java.util.Map;

public class SRestoreDeadUserService extends BaseService {

    //发布环境
    private static final String ESB_URL_172 = "http://**************:51000/esbWS/rest/";
    //正式环境
    private static final String ESB_URL_38 = "http://*************:51000/esbWS/rest/";


    private static Boolean isES = false;

    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }

    /**
     * 根据用户查询地市获取工单编号
     *
     * @param userID
     * @return
     */
    public List<Object[]> getbumen(int userID) {
        String sql = "select " + "case when dn1.DEPARTMENT_LEVEL=1 " + "THEN dn1.DEPARTMENT_NAME " + "else dn2.DEPARTMENT_NAME " + "end," + "as2.COMPANY_NAME," + "as2.COMPANY_IBM "
                + "from  AFR_SYSTEMDEPT dn1 " + "left join AFR_SYSTEMDEPT dn2 " + "on " + "dn1.DEPARTMENT_PARENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEMDEPT as1 " + "on "
                + "as1.DEPARTMENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEM_DEPT_USER asdu " + "on " + "asdu.DEPARTMENT_NO=dn1.DEPARTMENT_NO " + "left join AFR_SYSTEMCOMPANY as2 " + "on "
                + "as2.COMPANY_CODE=dn2.COMPANY_CODE " + "where asdu.ROWNO=?";
        Query query = getSession().createSQLQuery(sql);
        query.setInteger(0, userID);
        List<Object[]> s = query.list();
        return s;
    }


    /*
     * <AUTHOR>
     * @Date 2023/3/31 10:04
     * @Description  保存工单
     **/
    public SRestoreDeadUser addDeadUser(SRestoreDeadUser deadUser) {
        try {
            Session session = this.getSession();
            session.save(deadUser);
            return deadUser;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public SRestoreDeadUserDet addDeadUserDet(SRestoreDeadUserDet det) {
        try {
            Session session = this.getSession();
            session.save(det);
            return det;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存附件信息到中间表
     *
     * @param sa
     * @return
     */
    public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
        if (sa.getId() == null) {
            String sql = "select  * from SingleAndAttachment t where t.orderid=? and t.attachmentid=? and t.link=?";
            Object count = getSession().createSQLQuery(sql).setString(0, sa.getOrderID()).setString(1, sa.getAttachmentId()).setString(2, sa.getLink()).uniqueResult();
            if (null == count) {
                Session session = this.getSession();
                session.saveOrUpdate(sa);
                session.flush();
                return sa;
            } else {
                return null;
            }
        } else {
            Session session = this.getSession();
            session.saveOrUpdate(sa);
            session.flush();
            return sa;
        }
    }

    public LayuiPage findDeadUserPage(LayuiPage page, String orderNo, String orderName, String orderType, String stateCreatorDate, String endCreatorDate, String tableType, SystemUser user) {
        String sql;
        if (tableType.equals("0")) {//审批中
            sql = "SELECT * FROM SRESTORE_DEAD_USER WHERE CREATOR_ID = '" + user.getRowNo() + "' AND ORDER_TYPE = '1'";
        } else if (tableType.equals("1")) {//我创建
            sql = "select * from SRESTORE_DEAD_USER where CREATOR_ID = '" + user.getRowNo() + "'";
        } else if (tableType.equals("3")) {//待处理(app)
            sql = " SELECT TRI.*,ristask.ID as taskId FROM SRESTORE_DEAD_USER TRI INNER JOIN BPMS_RISKOFF_PROCESS rike ON TRI.ID = rike.BIZ_ID   INNER JOIN BPMS_RISKOFF_TASK ristask ON rike.PROCESS_SIGN = ristask.PROCESS_ID   WHERE ristask.OPER_NO = '" + user.getRowNo() + "' AND ristask.STATUS = '1' AND TRI.ORDER_TYPE in('1','2','3')";
        } else {//我经手
            sql = "SELECT * FROM SRESTORE_DEAD_USER WHERE ID IN ( SELECT DISTINCT BIZ_ID FROM BPMS_RISKOFF_PROCESS WHERE PROCESS_SIGN IN (SELECT DISTINCT PROCESS_ID FROM BPMS_RISKOFF_TASK  WHERE CREATOR_NO = '" + user.getRowNo() + "'))";
        }
        if (!"".equals(orderNo) && orderNo != null) {
            sql += "AND ORDER_NO = '" + orderNo + "' ";
        }
        if (!"".equals(orderName) && orderName != null) {
            sql += "AND ORDER_NAME like '%" + orderName + "%'";
        }
        if (!"".equals(orderType) && orderType != null) {
            sql += "AND ORDER_TYPE = '" + orderType + "' ";
        }
        if (!"".equals(stateCreatorDate) && stateCreatorDate != null) {
            sql += " AND CREATOR_DATE >= TO_DATE('" + stateCreatorDate + "','yyyy-MM-dd')";
        }
        if (!"".equals(endCreatorDate) && endCreatorDate != null) {
            sql += " AND CREATOR_DATE <= TO_DATE('" + endCreatorDate + "','yyyy-MM-dd')";
        }
        sql += "  ORDER BY CREATOR_DATE DESC";
        page.setCount(getCount("select count(0) from ( " + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/3 15:19
     * @Description  根据ID获取工单
     **/
    public SRestoreDeadUser getDeadUserOrder(String id) {
        String sql = "SELECT * from SRESTORE_DEAD_USER WHERE ID = ?";
        return (SRestoreDeadUser) getSession().createSQLQuery(sql).addEntity(SRestoreDeadUser.class).setString(0, id).uniqueResult();
    }

    /*
     * <AUTHOR>
     * @Date 2023/5/12 15:35
     * @Description 
     **/
    public SRestoreDeadUser getDeadUserOrderByOrderNo(String orderNo) {
        String sql = "SELECT * from SRESTORE_DEAD_USER WHERE order_No = ?";
        return (SRestoreDeadUser) getSession().createSQLQuery(sql).addEntity(SRestoreDeadUser.class).setString(0, orderNo).uniqueResult();
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/3 15:19
     * @Description  根据工单编号查询工单下所有明细信息
     **/
    public List<SRestoreDeadUserDet> getDeadUserDetList(String ORDER_NO) {
        String sql = "SELECT * FROM SRESTORE_DEAD_USER_DET WHERE ORDER_NO = ?";
        return getSession().createSQLQuery(sql).addEntity(SRestoreDeadUserDet.class).setString(0, ORDER_NO).list();
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/3 17:44
     * @Description  获取附件消息
     **/
    public List<Map<String, String>> fuJian(String id, String biaoshi) {
        String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from SRESTORE_DEAD_USER  o  "
                + " left join  SingleAndAttachment oa  on o.ID=OA.orderID " + " LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.ID=? and oa.link=?";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, id).setString(1, biaoshi).list();
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/3 17:43
     * @Description  通过手机号查询信息
     **/
    public SystemUser getUserByPhone(String Phone) {
        String hql = "from SystemUser s where s.mobile = ?  and s.employeeStatus = 0 ";

        List<SystemUser> userInfoList = this.getSession().createQuery(hql).setString(0, Phone).list();
        if (userInfoList.size() > 0) {
            return userInfoList.get(0);
        } else {
            return null;
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/3 17:43
     * @Description  根据登录人名称以及状态查询待办信息
     **/
    public WaitTask getWaitTask(String id) {
        String sql = "select * from WaitTask where taskid='" + id + "' and state=0";
        return (WaitTask) getSession().createSQLQuery(sql).addEntity(WaitTask.class).uniqueResult();
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/3 17:45
     * @Description  获取task
     **/
    public Bpms_riskoff_task getTask(String pid) {
        String sql = "select * from Bpms_riskoff_task where PROCESS_ID=? and STATUS='1'";
        return (Bpms_riskoff_task) getSession().createSQLQuery(sql).addEntity(Bpms_riskoff_task.class).setString(0, pid).uniqueResult();
    }


    public SRestoreDeadUser updateOrder(SRestoreDeadUser order) {
        //  Auto-generated method stub
        try {
            if (order != null) {
                Session session = this.getSession();
                session.update(order);
                session.flush();
            }
            return order;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/3/27 11:05
     * @Description  离网号码恢复boss库资料处理
     **/
    public Result SRestoreDeadUser(SRestoreDeadUserDet det, String bossNo) {
        JSONObject bodyContent = new JSONObject();
        bodyContent.put("UNIT_ID", det.getUnitId());//280编码
        bodyContent.put("PHONE_NO", det.getPhone_no());//电话号码
        bodyContent.put("ID_NO", det.getId_no());//客户ID
        String json = ESBReqMsgUtil.packMsgByRoute("14", bossNo, bodyContent);
        System.out.println("离网号码恢复boss库资料处理入参==" + json);
        if (isES) {
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38 + "sRestoreDeadUser", json, "UTF-8");
        }
        //本地测试
        String resultStr = CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172 + "sRestoreDeadUser", json);
        return HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/4 11:38
     * @Description  修改明细内容
     **/
    public SRestoreDeadUserDet updateDet(SRestoreDeadUserDet det) {
        //  Auto-generated method stub
        try {
            if (det != null) {
                Session session = this.getSession();
                session.update(det);
                session.flush();
            }
            return det;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Bpms_riskoff_process getPid(String id) {
        String sql = "SELECT * FROM BPMS_RISKOFF_PROCESS WHERE BIZ_ID='" + id + "'";
        return (Bpms_riskoff_process) getSession().createSQLQuery(sql).addEntity(Bpms_riskoff_process.class).uniqueResult();
    }
}
