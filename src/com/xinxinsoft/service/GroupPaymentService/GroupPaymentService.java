package com.xinxinsoft.service.GroupPaymentService;

import com.xinxinsoft.entity.GroupPayment.GroupPayment;
import com.xinxinsoft.entity.GroupPayment.GroupPaymentDet;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.page.LayuiPage;
import jxl.CellView;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.UnderlineStyle;
import jxl.write.*;
import org.apache.struts2.ServletActionContext;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.Boolean;
import java.net.URLEncoder;
import java.util.List;
import java.util.Map;

import static com.xinxinsoft.service.receiveApply.ReceiveApplyService.substringDate;

public class GroupPaymentService extends BaseService {

    /**
     *  保存 GroupPayment
     * @param groupPayment
     * @return
     */
    public GroupPayment updateGroupPayment(GroupPayment groupPayment) {
        try {
            Assert.notNull(groupPayment); //判断是否为空
            Object merge = this.getSession().merge(groupPayment);
            return  groupPayment;
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return null;
        }
    }

    public GroupPayment saveGroupPayment(GroupPayment groupPayment) {
        Session session = this.getSession();
        try {
            if (groupPayment != null) {
                session.save(groupPayment);
            }
            return groupPayment;
        }catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     *  保存 GroupPaymentDet
     * @param groupPaymentDet
     * @return
     */
    public GroupPaymentDet saveOrupdateGroupPaymentDet(GroupPaymentDet groupPaymentDet) {
        Assert.notNull(groupPaymentDet);//判断是否为空
        Object merge = this.getSession().merge(groupPaymentDet);
        return  groupPaymentDet;
    }

    public GroupPayment getGroupPayment(String id) {
        try {
            String sql = "SELECT * FROM GROUP_PAYMENT WHERE ID=?";
            return (GroupPayment)getSession().createSQLQuery(sql).addEntity(GroupPayment.class).setString(0,id).uniqueResult();
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return null;
        }
    }

    /**
     *
     * @return
     */
    public GroupPayment getGroupPaymentByNumber(String orderNumber){
        try {
            String sql = "SELECT * FROM GROUP_PAYMENT WHERE ORDER_NUMBER=?";
            return (GroupPayment)getSession().createSQLQuery(sql).addEntity(GroupPayment.class).setString(0,orderNumber).uniqueResult();
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return null;
        }
    }

    public List<GroupPaymentDet> getGroupPaymentDetList(String id) {
        try {
            String sql = "SELECT * FROM GROUP_PAYMENTDET WHERE PARENT_ID=?";
            return getSession().createSQLQuery(sql).addEntity(GroupPaymentDet.class).setString(0,id).list();
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return null;
        }
    }

    /**
     * 根据人员查询权限
     * @param user
     * @return
     */
    public List<Map<String,String>> getUserPowers(SystemUser user){
        try {
            String sql = "select * from VW_USERINFO where ROWNO=?";
            return getSession().createSQLQuery(sql).setInteger(0, user.getRowNo()).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            return null;
        }
    }

    /**
     * 获取附件消息
     */
    public List<Map<String, String>> getFiles(String id, String biaoshi) {
        String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from GROUP_PAYMENT  o  "
                + " left join  SingleAndAttachment oa  on o.id=OA.orderID " + " LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.id=? and oa.link=?";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, id).setString(1, biaoshi).list();
    }

    public List<SingleAndAttachment> getSingleAndAttachment(String orderId) {
        // TODO Auto-generated method stub
        String sql = "from SingleAndAttachment s where s.orderID=?";
        List<SingleAndAttachment> or = getSession().createQuery(sql).setString(0, orderId).list();
        return or;
    }

    public LayuiPage findGroupPayMentByPage(Integer type, LayuiPage page, SystemUser user,String orderNumber,String orderTitle,String groupCode,String groupName,String starTime,String endTime,String orderState){
        String sql = "";
        if (type.equals(0)){  //审批中
            sql = "SELECT * FROM GROUP_PAYMENT WHERE CREATOR_ID = '"+user.getRowNo()+"' AND ORDER_STATE = '1'";
        }else{
            if (type.equals(1)){    //我创建的
                sql = "SELECT * FROM GROUP_PAYMENT WHERE CREATOR_ID = '"+user.getRowNo()+"'";
            }else {     //我经手的
                sql = "SELECT distinct G.\"ID\",G.ORDER_NUMBER,G.ORDER_TITLE,G.GROUP_CODE,G.GROUP_NAME,G.ACCOUNT_NUMBER,G.STAR_TIME,G.END_TIME,G.CREATOR_NAME,G.CREATE_DATE,G.ORDER_STATE from GROUP_PAYMENT G \n" +
                        "LEFT JOIN BPMS_RISKOFF_PROCESS P ON G.\"ID\" = P.BIZ_ID\n" +
                        "LEFT JOIN BPMS_RISKOFF_TASK T ON T.PROCESS_ID = P.PROCESS_SIGN\n" +
                        "WHERE T.OPER_NO = '"+user.getRowNo()+"'";
            }
            if (orderState != null && !"".equals(orderState)){
                sql+="AND ORDER_STATE = '"+orderState+"'";
            }
        }
        if (orderNumber!=null&&!"".equals(orderNumber)){
            sql+="AND ORDER_NUMBER = '"+orderNumber+"'";
        }
        if (orderTitle!=null&&!"".equals(orderTitle)){
            sql+="AND ORDER_TITLE LIKE '%"+orderTitle+"%'";
        }
        if (groupCode!=null&&!"".equals(groupCode)){
            sql+="AND GROUP_CODE = '"+groupCode+"'";
        }
        if (groupName!=null&&!"".equals(groupName)){
            sql+="AND GROUP_NAME LIKE '%"+groupName+"%'";
        }
        if (starTime!=null&&!"".equals(starTime)){
            sql+="AND STAR_TIME = '"+starTime+"'";
        }
        if (endTime!=null&&!"".equals(endTime)){
            sql+="AND END_TIME = '"+endTime+"'";
        }
        sql+=(" ORDER BY CREATE_DATE DESC");
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 根据用户编号查询权限id
     */
    public List findByRowNo(int rowNo) {
        String hql = "select ROLE_ID from SYSTEM_USER_ROLE where row_no=?";
        List list = getSession().createSQLQuery(hql).setInteger(0, rowNo).list();
        return list;
    }

    public LayuiPage findGroupPayMentStatisticsByPage(Integer type, LayuiPage page, SystemUser user,String orderNumber,String orderTitle,String groupCode,String groupName,String starTime,String endTime,String orderState,Boolean flag){
         String sql = "";
        if (type.equals(0)){  //我创建的
            sql = "SELECT G.ORDER_NUMBER,G.ORDER_TITLE,G.GROUP_CODE,G.GROUP_NAME,G.ACCOUNT_NUMBER,G.STAR_TIME,G.END_TIME,G.CREATOR_NAME,G.CREATE_DATE,G.ORDER_STATE FROM GROUP_PAYMENT G WHERE CREATOR_ID = '"+user.getRowNo()+"'";
        }else{
           if (flag){       //是否为系统管理员
               sql = "SELECT G.ORDER_NUMBER,G.ORDER_TITLE,G.GROUP_CODE,G.GROUP_NAME,G.ACCOUNT_NUMBER,G.STAR_TIME,G.END_TIME,G.CREATOR_NAME,G.CREATE_DATE,G.ORDER_STATE FROM GROUP_PAYMENT G WHERE 1=1";
           }else {          //同地市的
                sql = "SELECT G.ORDER_NUMBER,G.ORDER_TITLE,G.GROUP_CODE,G.GROUP_NAME,G.ACCOUNT_NUMBER,G.STAR_TIME,G.END_TIME,G.CREATOR_NAME,G.CREATE_DATE,G.ORDER_STATE FROM GROUP_PAYMENT G \n" +
                        "LEFT JOIN VW_USERINFO V ON G.CREATOR_ID = V.ROWNO\n" +
                        "WHERE V.COMPANY_CODE IN (SELECT COMPANY_CODE from VW_USERINFO WHERE ROWNO = '"+user.getRowNo()+"') ";
           }
        }
        if (orderState != null && !"".equals(orderState)){
            sql+="AND ORDER_STATE = '"+orderState+"'";
        }
        if (orderNumber!=null&&!"".equals(orderNumber)){
            sql+="AND ORDER_NUMBER = '"+orderNumber+"'";
        }
        if (orderTitle!=null&&!"".equals(orderTitle)){
            sql+="AND ORDER_TITLE LIKE '%"+orderTitle+"%'";
        }
        if (groupCode!=null&&!"".equals(groupCode)){
            sql+="AND GROUP_CODE = '"+groupCode+"'";
        }
        if (groupName!=null&&!"".equals(groupName)){
            sql+="AND GROUP_NAME LIKE '%"+groupName+"%'";
        }
        if (starTime!=null&&!"".equals(starTime)){
            sql+="AND STAR_TIME = '"+starTime+"'";
        }
        if (endTime!=null&&!"".equals(endTime)){
            sql+="AND END_TIME = '"+endTime+"'";
        }
        sql+=(" ORDER BY CREATE_DATE DESC");
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public void getGroupPayMentStatisticsExcl(List<Map<String,Object>> mapList) throws IOException, WriteException {
        HttpServletResponse response = ServletActionContext.getResponse();
        String excelFile = FileUpload.getFtpURL() + "exportExcelToJxl.xls";
        File file = new File(FileUpload.getFtpURL());

        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        String exportName = "GroupPayMentStatisticsExcl_" + FileUpload.getDateToString("yyyy_MM_dd");
        try {
            WritableWorkbook wb = Workbook.createWorkbook(new File(excelFile));
            WritableSheet firstSheet = wb.createSheet("集团统付列表信息", 1);
            String[] headers = new String[]{"工单编号","工单标题","集团编码","集团名称","账户号码","开始时间","结束时间","创建人","创建时间","工单状态"};
            for (int i = 0; i < headers.length; i++) {
                // 3、创建单元格(Label)对象
                Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
                WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                // 标题栏 // 颜色
                WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
                wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
                wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
                wcfTitle.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfTitle.setAlignment(jxl.format.Alignment.CENTRE); // 设置垂直对齐
                // 内容栏
                WritableCellFormat wcfContent = new WritableCellFormat(wf3);
                wcfContent.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐

                CellView navCellView = new CellView();
                navCellView.setSize(150 * 50);

                label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
                firstSheet.setColumnView(i, navCellView); // 设置col显示样式
                firstSheet.setRowView(i, 400, false); // 设置行高
                firstSheet.addCell(label0);

                if (mapList.size() > 0) {
                    for (int i1 = 0; i1 < mapList.size(); i1++) {
                        if (mapList.get(i1).get("ORDER_NUMBER") != null) {
                            Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("ORDER_NUMBER")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("ORDER_TITLE") != null) {
                            Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("ORDER_TITLE")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("GROUP_CODE") != null) {
                            Label label = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("GROUP_CODE")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("GROUP_NAME") != null) {
                            Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("GROUP_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("ACCOUNT_NUMBER") != null) {
                            Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("ACCOUNT_NUMBER")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("STAR_TIME") != null) {
                            Label label = new Label(5, i1 + 1, String.valueOf(mapList.get(i1).get("STAR_TIME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("END_TIME") != null) {
                            Label label = new Label(6, i1 + 1, String.valueOf(mapList.get(i1).get("END_TIME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CREATOR_NAME") != null) {
                            Label label = new Label(7, i1 + 1, String.valueOf(mapList.get(i1).get("CREATOR_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CREATE_DATE") != null) {
                            Label label = new Label(8, i1 + 1, substringDate(String.valueOf(mapList.get(i1).get("CREATE_DATE"))), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("ORDER_STATE") != null) {
                            //-1作废，0退回，1审批中，2已审批,3已完成,4无纸化留存成功,5部分推送成功
                            String state = mapList.get(i1).get("ORDER_STATE").toString();
                            String value = "";
                            if ("-1".equals(state)) {
                                value = "作废";
                            } else if ("0".equals(state)) {
                                value = "退回";
                            } else if ("1".equals(state)) {
                                value = "审批中";
                            }else if ("2".equals(state)) {
                                value = "已审批";
                            }else if ("3".equals(state)) {
                                value = "已完成";
                            }else if ("4".equals(state)) {
                                value = "无纸化留存成功";
                            }else if ("5".equals(state)) {
                                value = "部分推送成功";
                            }
                            Label label = new Label(9, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        }

                    }
                }
            }
            wb.write();// 打开流 开始写文件
            wb.close();// 关闭流
            byte[] data = FileUtil.toByteArray2(excelFile);
            String fileName = URLEncoder.encode(exportName, "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xls" + "\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
            File fe = new File(excelFile);
            fe.delete();
        }finally{
            File fe = new File(excelFile);
            if (file.exists() && file.isDirectory()) {
                fe.delete();
            }
        }
    }

    public List<Map<String,Object>> findGroupPayMentStatisticsByList(Integer type, SystemUser user,String orderNumber,String orderTitle,String groupCode,String groupName,String starTime,String endTime,String orderState,Boolean flag){
        String sql = "";
        if (type.equals(0)){  //我创建的
            sql = "SELECT G.ORDER_NUMBER,G.ORDER_TITLE,G.GROUP_CODE,G.GROUP_NAME,G.ACCOUNT_NUMBER,G.STAR_TIME,G.END_TIME,G.CREATOR_NAME,G.CREATE_DATE,G.ORDER_STATE FROM GROUP_PAYMENT G WHERE CREATOR_ID = '"+user.getRowNo()+"'";
        }else{
            if (flag){       //是否为系统管理员
                sql = "SELECT G.ORDER_NUMBER,G.ORDER_TITLE,G.GROUP_CODE,G.GROUP_NAME,G.ACCOUNT_NUMBER,G.STAR_TIME,G.END_TIME,G.CREATOR_NAME,G.CREATE_DATE,G.ORDER_STATE FROM GROUP_PAYMENT G WHERE 1=1";
            }else {          //同地市的
                sql = "SELECT G.ORDER_NUMBER,G.ORDER_TITLE,G.GROUP_CODE,G.GROUP_NAME,G.ACCOUNT_NUMBER,G.STAR_TIME,G.END_TIME,G.CREATOR_NAME,G.CREATE_DATE,G.ORDER_STATE FROM GROUP_PAYMENT G \n" +
                        "LEFT JOIN VW_USERINFO V ON G.CREATOR_ID = V.ROWNO\n" +
                        "WHERE V.COMPANY_CODE IN (SELECT COMPANY_CODE from VW_USERINFO WHERE ROWNO = '"+user.getRowNo()+"') ";
            }
        }
        if (orderState != null && !"".equals(orderState)){
            sql+="AND ORDER_STATE = '"+orderState+"'";
        }
        if (orderNumber!=null&&!"".equals(orderNumber)){
            sql+="AND ORDER_NUMBER = '"+orderNumber+"'";
        }
        if (orderTitle!=null&&!"".equals(orderTitle)){
            sql+="AND ORDER_TITLE LIKE '%"+orderTitle+"%'";
        }
        if (groupCode!=null&&!"".equals(groupCode)){
            sql+="AND GROUP_CODE = '"+groupCode+"'";
        }
        if (groupName!=null&&!"".equals(groupName)){
            sql+="AND GROUP_NAME LIKE '%"+groupName+"%'";
        }
        if (starTime!=null&&!"".equals(starTime)){
            sql+="AND STAR_TIME = '"+starTime+"'";
        }
        if (endTime!=null&&!"".equals(endTime)){
            sql+="AND END_TIME = '"+endTime+"'";
        }
        sql+=(" ORDER BY CREATE_DATE DESC");
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();

    }
}
