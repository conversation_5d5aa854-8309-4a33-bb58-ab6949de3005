package com.xinxinsoft.service.oms;

import com.xinxinsoft.entity.contract.MobileCorporation;
import com.xinxinsoft.entity.contractUniformity.Con_OrderForm;
import com.xinxinsoft.entity.contractUniformity.ContractRenewal;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.oms.*;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.contractUniformity.ContractInfo;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.entity.pms.PmsProductInfo;
import com.xinxinsoft.entity.pms.PmsProductLabel;
import com.xinxinsoft.entity.smsPush.Push_0_0001;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import jxl.CellView;
import jxl.Workbook;
import jxl.format.UnderlineStyle;
import jxl.write.*;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import org.hibernate.*;
import org.hibernate.transform.Transformers;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.lang.Boolean;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.xinxinsoft.utils.page.LayuiPage;
import org.hibernate.util.JDBCExceptionReporter;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;

/**
 * 销售工单（客户经理需求单）
 */
public class OmsSellOrderService extends BaseService {

    /**
     * 根据ID查询数据
     *
     * @param orderId
     * @return
     */
    public OmsSellOrder getOmsSellOrderById(String orderId) {
        String sql = "select * from OMS_SELL_ORDER where ID = ? and DELETE_STATE = '" + OmsSellOrder.DELETE_FALSE + "'";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsSellOrder.class);
        sqlQuery.setParameter(0, orderId);
        List<OmsSellOrder> list = sqlQuery.list();
        if (null != list && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    /**
     * OmsOrderWorkbench（工单受理工作台）和Bpms_riskoff_task（风控审批流程表）
     *
     * @param orderId
     * @return
     */
    public Map<String, Object> getOmsOrderWorkbenchAndBpms_riskoff_taskByOmsSellOrderId(String orderId) {
        String sql = "select o.*,p.id as pid,p.biz_id,p.process_sign,p.creator_name,p.creator_no,p.gmt_modified,p.status,p.gmt_create,p.END_TIME,p.bak1 as REJECT,p.bak2,p.bak3,os.UNIT_ID as ORDER_UNIT_ID,os.UNIT_NAME as ORDER_UNIT_NAME,os.IS_UNIT as ORDER_IS_UNIT,os.TITLE as ORDER_TITLE,os.COMPANY_NO as ORDER_COMPANY_NO,os.DISCOUNT_RATE as ORDER_DISCOUNT_RATE,os.EXPECT_DATE as ORDER_EXPECT_DATE,os.COMPLETE_DATE as ORDER_COMPLETE_DATE," +
                "os.STATE as ORDER_STATE,os.MEMO as ORDER_MEMO,os.CONTRACT_ID as ORER_CONTRACT_ID,os.DESCRIPTION as ORDER_DESCRIPTION,os.CUSTOMER_NAME as ORDER_CUSTOMER_NAME,os.CREATE_DATE as ORDER_CREATE_DATE,os.CREATE_NAME as ORDER_CREATE_NAME ,os.CUSTOMER_PHONE as ORDER_CUSTOMER_PHONE,os.OPERATE_NAME as ORDER_OPERATE_NAME, " +
                "os.OPERATE_NO as ORDER_OPERATE_NO,os.CREATE_NO as ORDER_CREATE_NO,os.IS_URGENT,os.IS_SUPPORT,os.ID as ORDER_ID,os.VERSION_NUMBER from OMS_ORDER_WORKBENCH o INNER JOIN BPMS_RISKOFF_PROCESS p on o.id=p.BIZ_ID INNER JOIN OMS_SELL_ORDER os on o.ORDER_NO = os.ORDER_NO  where o.ID = ? ";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.setParameter(0, orderId);
        sqlQuery.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        List<Map<String, Object>> list = sqlQuery.list();
        if (null != list && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    public OmsSellOrder updateOmsSellOrder(String orderNo, String state) {
        OmsSellOrder omsSellOrder = getOmsSellOrderByOrderNo(orderNo);
        try {
            if (omsSellOrder != null) {
                omsSellOrder.setState(state);
                omsSellOrder.setModifyDate(new Date());
                Session session = this.getSession();
                session.update(omsSellOrder);
                return omsSellOrder;
            } else {
                return null;
            }
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public OmsSellOrder getOmsSellOrderByOrderNo(String orderNo) {
        String sql = "select * from OMS_SELL_ORDER where ORDER_NO = ? and DELETE_STATE = '" + OmsSellOrder.DELETE_FALSE + "'";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsSellOrder.class);
        sqlQuery.setParameter(0, orderNo);
        List<OmsSellOrder> list = sqlQuery.list();
        if (null != list && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    public OmsOrderLink getOmsOrderLinkByLinkOrderNo(String linkOrderNo) {
        String sql = "select * from OMS_ORDER_LINK where link_Order_No = ?";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsOrderLink.class);
        sqlQuery.setParameter(0, linkOrderNo);
        OmsOrderLink link= (OmsOrderLink)sqlQuery.uniqueResult();
        if (null != link) {
            return link;
        }
        return null;
    }

    public List<OmsOrderLink> getOmsOrderLinkByOrderNumberList(String orderNumber) {
        String sql = "select * from OMS_ORDER_LINK where order_Number = ? order by link_Code desc";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsOrderLink.class);
        sqlQuery.setParameter(0, orderNumber);
        List<OmsOrderLink> list = sqlQuery.list();
        if (null != list && !list.isEmpty()) {
            return list;
        }
        return null;
    }

    public OmsLinkDialogue getOmsLinkDialogueByLinkOrderNo(String linkOrderNo) {
        String sql = "select * from Oms_Link_Dialogue where link_Order_No = ? and status=0";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsLinkDialogue.class);
        sqlQuery.setParameter(0, linkOrderNo);
        List<OmsLinkDialogue> list = sqlQuery.list();
        if (null != list && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }

    public List<OmsLinkDialogue> getOmsLinkDialogueByLinkOrderNoList(String linkOrderNo) {
        String sql = "select * from Oms_Link_Dialogue where link_Order_No = ? order by creator_date desc";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsLinkDialogue.class);
        sqlQuery.setParameter(0, linkOrderNo);
        List<OmsLinkDialogue> list = sqlQuery.list();
        return list;
    }

    public String queryBossFormInfo(LayuiPage page, int rowNo,
                                    String contractName, String groupCode, String contractid) {
        // TODO Auto-generated method stub
        String sql = "select * from CONTRACTINFO c where  c.ContractType in('EC','PRC') and c.STATE in ('2','4') ";
        String countSql = "select count(*) from CONTRACTINFO c where  c.ContractType in('EC','PRC') and c.STATE in ('2','4') ";
        if (contractName != null && contractName.length() > 0) {
            sql += " and c.contractName like '%" + contractName + "%'";
            countSql += " and c.contractName like '%" + contractName + "%'";
        }

        if (contractid != null && contractid.length() > 0) {
            sql += " and c.contractId like '%" + contractid + "%'";
            countSql += " and c.contractId like '%" + contractid + "%'";
        }

        if (groupCode != null && groupCode.length() > 0) {
            sql += " and c.UNIT_ID like '%" + groupCode + "%'";
            countSql += " and c.UNIT_ID like '%" + groupCode + "%'";
        }
        sql += " order by c.createDate desc";
        page.setData(getSession()
                .createSQLQuery(sql)
                .setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                .setFirstResult(page.getPageNo())//索引开始位置
                .setMaxResults(page.getPageSize())//数据条数
                .list());

        //查询总条数
        page.setCount(Integer.valueOf(getSession()
                .createSQLQuery(countSql)
                .setCacheable(true)
                .uniqueResult() + "")
        );

        Collection c = getPageList(sql, ContractInfo.class, page);
        page.setData(c);
        String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page);
        System.out.println(json);
        return json;
    }

    public ContractInfo queryContractInfo(String id) {
        String sql = "select * from ContractInfo where id=?";
        return (ContractInfo) getSession().createSQLQuery(sql).addEntity(ContractInfo.class).setString(0, id).uniqueResult();
    }

    public ContractInfo getContractUnifor(String contractId) {
        // TODO Auto-generated method stub
        String sql = "select * from ContractInfo where contractId=?";
        ContractInfo contractInfo = (ContractInfo) getSession().createSQLQuery(sql).addEntity(ContractInfo.class).setString(0, contractId).uniqueResult();
        return contractInfo;
    }

    public MobileCorporation getMobileCorporationObj(String i) {
        // TODO Auto-generated method stub
        String sql = "select * from MOBILECORPORATION where COMPANY_CODE=?";
        MobileCorporation m = (MobileCorporation)this.getSession().createSQLQuery(sql).addEntity(MobileCorporation.class).setString(0, i).uniqueResult();
        return m;
    }

    public ContractRenewal getContractRenewal(String contractId){
        String sql = "select  * from Contract_Renewal where contractId=?";
        return (ContractRenewal)getSession().createSQLQuery(sql).addEntity(ContractRenewal.class).setString(0,contractId).uniqueResult();
    }

    /**
     * 保存 OmsOrderItem
     *
     * @return
     */
    public OmsSellOrder saveOrupdateOmsSellOrder(OmsSellOrder oms) {
        try {
            Assert.notNull(oms); //判断是否为空
            Object merge = this.getSession().merge(oms);
            oms = (OmsSellOrder) merge;
            return oms;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存 OmsOrderLink
     *
     * @return
     */
    public OmsOrderLink saveOrupdateOmsOrderLink(OmsOrderLink link) {
        try {
            Assert.notNull(link); //判断是否为空
            Object merge = this.getSession().merge(link);
            link = (OmsOrderLink) merge;
            return link;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存 OmsLinkDialogue
     * @return
     */
    public OmsLinkDialogue saveOrupdateOmsLinkDialogue(OmsLinkDialogue dig) {
        try {
            Assert.notNull(dig); //判断是否为空
            Object merge = this.getSession().merge(dig);
            dig = (OmsLinkDialogue) merge;
            return dig;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public OmsLinkDialogue updateOmsLinkDialogue(OmsLinkDialogue dig) {
        try {
            Session session = this.getSession();
            session.update(dig);
            session.flush();
            return dig;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 保存 OmsOrderItem
     *
     * @return
     */
    public OmsOrderProduct saveOrupdateOmsOrderProduct(OmsOrderProduct oms) {
        try {
            Assert.notNull(oms); //判断是否为空
            Object merge = this.getSession().merge(oms);
            oms = (OmsOrderProduct) merge;
            return oms;
        } catch (JDBCException e) {
            System.out.println("进入保存异常了");
            e.printStackTrace();
            return null;
        }
    }

    public PmsProdPriceInfo queryPmsProdPriceInfo(String prc_id) {
        String sql = "SELECT * FROM PMS_PROD_PRICE_INFO WHERE PRC_ID=?";
        return (PmsProdPriceInfo) getSession().createSQLQuery(sql).addEntity(PmsProdPriceInfo.class).setString(0, prc_id).uniqueResult();
    }

    public PmsProductInfo queryPmsProductInfo(String prod_id,String labelId) {
        String sql = "SELECT * FROM PMS_PRODUCT_INFO WHERE PROD_ID=? and LABEL_ID=?";
        return (PmsProductInfo) getSession().createSQLQuery(sql).addEntity(PmsProductInfo.class).setString(0, prod_id).setString(1, labelId).uniqueResult();
    }

    public PmsProductLabel queryPmsProductLabel(String label_id) {
        String sql = "SELECT * FROM PMS_PRODUCT_LABEL WHERE LABEL_ID=?";
        return (PmsProductLabel) getSession().createSQLQuery(sql).addEntity(PmsProductLabel.class).setString(0, label_id).uniqueResult();
    }

    /**
     * 保存附件信息
     */
    public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
        if (sa.getId() == null) {
            String sql = "select  * from SingleAndAttachment t where t.orderid=? and t.attachmentid=? and t.link=?";
            Object count = getSession().createSQLQuery(sql).setString(0, sa.getOrderID()).setString(1, sa.getAttachmentId()).setString(2, sa.getLink()).uniqueResult();
            if (null == count) {
                Session session = this.getSession();
                session.saveOrUpdate(sa);
                session.flush();
                return sa;
            } else {
                return null;
            }
        } else {
            Session session = this.getSession();
            session.saveOrUpdate(sa);
            session.flush();
            return sa;
        }
    }

    public List<SingleAndAttachment> getSingleAndAttachmentList(String orderId) {
        String sql = "select  * from SingleAndAttachment t where t.orderid=? ";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(SingleAndAttachment.class);
        sqlQuery.setParameter(0, orderId);
        List<SingleAndAttachment> list = sqlQuery.list();
        if (null != list && !list.isEmpty()) {
            return list;
        } else {
            return null;
        }
    }

    /**
     * @author: liyang
     * @date: 2021/10/27 10:22
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 需求单查询
     */
    public LayuiPage getOmsSellOrderList(Integer type, LayuiPage page, SystemUser user, String orderNo, String title, String unitId,
                                         String unitName, String startTime, String endTime,String state,String prcNo) {
        StringBuffer sql = new StringBuffer();
        //工单状态 0->待受理；1->受理中；2->建设中；3->已完成；4->已关闭；5->无效订单 6->已驳回
        if(type == 1){
            sql.append("SELECT OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID," +
                    "OMS.ORDER_NO,OMS.DEMAND_TYPE,OMS.IS_URGENT,OMS.IS_SUPPORT," +
                    "OMS.IS_UNIT,OMS.STATE,OMS.LINK_ORDER_NO,OMS.CREATE_NAME,OMS.OPERATE_NAME,OMS.CUSTOMER_NAME," +
                    "OMS.CUSTOMER_PHONE,OMS.COMPLETE_DATE,OMS.VERSION_NUMBER,OMS.COMPANY_NAME,OMS.COUNTY_NAME," +
                    "LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME " +
                    "FROM OMS_SELL_ORDER OMS LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO=OMS.ORDER_NO WHERE OMS.CREATE_NO='" + user.getRowNo() + "' ");
        }else if(type == 2){
            sql.append("SELECT OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID," +
                    "OMS.ORDER_NO,OMS.DEMAND_TYPE,OMS.IS_URGENT,OMS.IS_SUPPORT," +
                    "OMS.IS_UNIT,OMS.STATE,OMS.LINK_ORDER_NO,OMS.CREATE_NAME,OMS.OPERATE_NAME,OMS.CUSTOMER_NAME,"+
                    "OMS.CUSTOMER_PHONE,OMS.COMPLETE_DATE,OMS.VERSION_NUMBER,OMS.COMPANY_NAME,OMS.COUNTY_NAME," +
                    "LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME " +
                    "FROM OMS_SELL_ORDER OMS LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO=OMS.ORDER_NO WHERE OMS.OPERATE_NO='"+user.getRowNo() + "' ");
        }

        if (state != null && state.length() > 0) {
            sql.append(" AND OMS.state='" + state + "'");
        }/*else{
            sql.append(" and OMS.state in('1','-1','4','0') ");
        }*/

        if (orderNo != null && orderNo.length() > 0) {
            sql.append(" AND OMS.ORDER_NO LIKE '%" + orderNo + "%'");
        }
        if (title != null && title.length() > 0) {
            sql.append(" AND OMS.TITLE LIKE '%" + title + "%'");
        }
        if (unitId != null && unitId.length() > 0) {
            sql.append(" AND OMS.UNIT_ID LIKE '%" + unitId + "%'");
        }
        if (unitName != null && unitName.length() > 0) {
            sql.append(" AND OMS.UNIT_NAME LIKE '%" + unitName + "%'");
        }
        if (startTime != null && startTime.length() > 0) {
            sql.append(" AND to_char(OMS.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + startTime + " 00:00:00'");
        }
        if (endTime != null && endTime.length() > 0) {
            sql.append(" AND to_char(OMS.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'");
        }
        if (prcNo != null && prcNo.length() > 0) {
            sql.append(" AND OP.PRC_NO="+prcNo+"");
        }
        sql.append(" group by OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID," +
                "OMS.ORDER_NO,OMS.DEMAND_TYPE,OMS.IS_URGENT,OMS.IS_SUPPORT," +
                "OMS.IS_UNIT,OMS.STATE,OMS.LINK_ORDER_NO,OMS.CREATE_NAME,OMS.OPERATE_NAME,OMS.CUSTOMER_NAME," +
                "OMS.CUSTOMER_PHONE,OMS.COMPLETE_DATE,OMS.VERSION_NUMBER,OMS.COMPANY_NAME,OMS.COUNTY_NAME " +
                "ORDER BY OMS.CREATE_DATE DESC");
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql.toString() + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql.toString(), null, page));
        }
        return page;
    }

    public List<OmsOrderProduct> getOmsOrderProductList(String orderNo) {
        String sql = "SELECT * FROM OMS_ORDER_PRODUCT WHERE ORDER_NO=?";
        return getSession().createSQLQuery(sql).addEntity(OmsOrderProduct.class).setString(0, orderNo).list();
    }

    /**
     * 查询集团信息
     *
     * @param groupCoding
     * @return
     */
    public GroupCustomer getGroupCustomer(String groupCoding) {
        String sql = "select * from GroupCustomer c where c.groupCoding=?";
        return (GroupCustomer) getSession().createSQLQuery(sql).addEntity(GroupCustomer.class).setString(0, groupCoding).list().get(0);
    }

    /**
     * 查询工作台所有审批环节
     *
     * @return
     */
    public List<OmsOrderWorkbench> getOmsOrderWorkbenchList(String orderNo) {
        String sql = "SELECT * FROM OMS_ORDER_WORKBENCH WHERE ORDER_NO=?";
        return getSession().createSQLQuery(sql).addEntity(OmsOrderWorkbench.class).setString(0, orderNo).list();
    }

    public OmsOrderWorkbench getOmsOrderWorkbench(String id) {
        String sql = "SELECT * FROM OMS_ORDER_WORKBENCH WHERE id=?";
        return (OmsOrderWorkbench)getSession().createSQLQuery(sql).addEntity(OmsOrderWorkbench.class).setString(0, id).uniqueResult();
    }

    /**
     * 查询工作台审批环节的审批人
     */
    public Map<String, String> getApprovalUserList(String id, String type) {
        String sql = "SELECT LISTAGG(br.oper_name,'/') WITHIN GROUP (ORDER BY br.oper_name) SPUSER " +
                "from BPMS_RISKOFF_PROCESS bp INNER JOIN BPMS_RISKOFF_TASK br " +
                "on br.PROCESS_ID=bp.PROCESS_sign " +
                "WHERE bp.biz_id=? and br.status='1' and br.type = ? ";
        return (Map<String, String>) getSession().createSQLQuery(sql).setString(0, id).setString(1, type).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).uniqueResult();
    }

    public List<SystemUser> getRole(Integer roleId, String companyCode) {
        String sql = "select afs.*  from AFR_systemuser afs  inner join system_user_role sur on sur.row_no=afs.rowno inner join system_role sr on sr.id=sur.role_id" +
                " inner join afr_system_dept_user asdu on asdu.rowno=afs.rowno" +
                " inner join afr_systemdept sd on sd.department_no = asdu.department_no" +
                " inner join afr_systemcompany sc on sc.company_code=sd.company_code" +
                " where sr.id=? and afs.employee_status='0' and sc.company_code=?";
        return getSession().createSQLQuery(sql).addEntity(SystemUser.class).setInteger(0, roleId).setString(1, companyCode).list();
    }

    public Integer getOmsSellOrderByUserId(int rowNo) {
        String sql = "select count(0) from OMS_SELL_ORDER where operate_No=? and " +
                "to_date(to_char(create_Date,'yyyy-mm-dd hh:mi:ss'),'yyyy-mm-dd hh:mi:ss')<=(SELECT TRUNC(SYSDATE)+1-1/86400 FROM dual) " +
                "and to_date(to_char(create_Date,'yyyy-mm-dd hh:mi:ss'),'yyyy-mm-dd hh:mi:ss')>=(SELECT Trunc(SYSDATE) FROM dual)";
        return Integer.valueOf(getSession().createSQLQuery(sql).setString(0, rowNo + "").uniqueResult() + "");
    }

    public List<Map<String, String>> getCountOms(Integer roleId, String companyCode) {
        String sql = "select COUNT(oms.ID) as count,afs.ROWNO,afs.EMPLOYEE_NAME from  OMS_SELL_ORDER oms " +
                "RIGHT JOIN AFR_systemuser afs on oms.OPERATE_NO=afs.rowno " +
                "inner join system_user_role sur on sur.row_no=afs.rowno " +
                "inner join system_role sr on sr.id=sur.role_id inner join afr_system_dept_user asdu on asdu.rowno=afs.rowno " +
                "inner join afr_systemdept sd on sd.department_no = asdu.department_no " +
                "inner join afr_systemcompany sc on sc.company_code=sd.company_code " +
                "where sr.id=? and afs.employee_status='0' and sc.company_code=? GROUP BY afs.ROWNO,afs.EMPLOYEE_NAME ORDER BY COUNT(oms.ID) asc";
        return getSession().createSQLQuery(sql).setInteger(0, roleId).setString(1, companyCode).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public OmsOrderProduct getOmsOrderProduct(String id) {
        String sql = "SELECT * FROM OMS_ORDER_PRODUCT WHERE id=?";
        return (OmsOrderProduct) getSession().createSQLQuery(sql).addEntity(OmsOrderProduct.class).setString(0, id).uniqueResult();
    }

    /**
     * 查询产品
     *
     * @return
     */
    public List<Map<String, String>> getProduct(String identification) {
        String sql = "select P.LABEL_ID id," +
                "P.LABEL_NAME name," +
                "P.CATEGORY_ID parentId " +
                "from PMS_PRODUCT_LABEL P " +
                "inner JOIN (" +
                "select pt.PROD_ID,pt.PROD_NAME," +
                "pt.LABEL_ID,pt.UPDATE_DATE," +
                "pt.GROUP_TYPE,pt.MASTER_SERV_ID," +
                "pt.OP_CODE,pt.BRAND_ID " +
                "from PMS_PRODUCT_INFO pt " +
                "inner JOIN PMS_PROD_PRICE_INFO pi " +
                "on pi.PROD_ID=pt.PROD_ID " +
                "GROUP BY pt.PROD_ID,pt.PROD_NAME," +
                "pt.LABEL_ID,pt.UPDATE_DATE,pt.GROUP_TYPE," +
                "pt.MASTER_SERV_ID,pt.OP_CODE,pt.BRAND_ID) productInfo " +
                "on productInfo.LABEL_ID=p.LABEL_ID ";
                if("1".equals(identification)){
                    sql+="inner join PRODUCT_KEY_INFORMATION pki on p.LABEL_ID=pki.LABEL_ID ";
                }
        sql+="where p.STATUS='1' GROUP BY P.LABEL_ID,P.LABEL_NAME,P.CATEGORY_ID ";
        return this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 查询子产品
     *
     * @return
     */
    public List<Map<String, String>> getSubProducts(String code,String identification) {
        String sql = "select pt.PROD_ID id," +
                "pt.PROD_NAME name," +
                "pt.LABEL_ID parentId " +
                "from PMS_PRODUCT_INFO pt " +
                "inner JOIN PMS_PROD_PRICE_INFO pi " +
                "on pi.PROD_ID=pt.PROD_ID ";
                if("1".equals(identification)){
                    sql+="inner join PRODUCT_KEY_INFORMATION pki on pt.PROD_ID=pki.PRODUCT_CODE ";
                }
        sql+="where pt.LABEL_ID=? and pi.STATUS='1' and pt.STATUS='1' " +
                "GROUP BY pt.PROD_ID,pt.PROD_NAME,pt.LABEL_ID";
        return this.getSession().createSQLQuery(sql).setString(0, code).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 查询资费
     *
     * @return
     */
    public List<Map<String, String>> getProdPriceInfo(String code,String labelId,String identification) {
        String sql = "select P.PRC_ID id,P.PRC_NAME name,P.PROD_ID parentId " +
                "from PMS_PROD_PRICE_INFO P ";
        if("1".equals(identification)){
            sql+="inner join  PRICE_KEY_INFORMATION pki on P.PRC_ID=pki.PRICE_CODE ";
        }
        sql+="WHERE P.PROD_ID='" + code + "' and p.LABEL_ID='"+labelId+"' and p.STATUS='1'";
        return this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public List<Map<String, String>> fuJian(String id, String biaoshi) {
        String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\", ah.attachmentname as \"attachmentname\" " +
                "from SingleAndAttachment oa  LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID " +
                "where oa.ORDERID= ? and oa.link= ? ";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, id).setString(1, biaoshi).list();
    }

    /**
     * 根据角色查询角色下人员信息 PS：修改去重复（afs.employee_name||'('||sd.department_name||')'）
     *
     * @param id   角色ID
     * @param user 登录用户信息
     * @return
     */
    public List<Map<String, String>> SelectZtreeByUId(String id, SystemUser user) {
        if (!StringUtils.isEmpty(id)) {
            List<SystemDept> sd = user.getSystemDept();

            String code = "(";
            for (int i = 0; i < sd.size(); i++) {
                code += "'" + sd.get(i).getCompanyCode() + "',";
            }
            code += "'000')";
            String sqlone = "select * from system_role where ID =?";
            Role systemRole = (Role) this.getSession().createSQLQuery(sqlone).addEntity(Role.class).setInteger(0, Integer.valueOf(id)).uniqueResult();
            String sql = "";
            if (systemRole.getCname().contains("省公司")) {
                sql += "select distinct afs.ROWNO as \"id\",afs.employee_name as \"name\",'false' as \"isParent\" ,'false' as \"nocheck\" from AFR_systemuser afs  inner join system_user_role sur on sur.row_no=afs.rowno inner join system_role sr on sr.id=sur.role_id"
                        + "  inner join afr_system_dept_user asdu on asdu.rowno=afs.rowno "
                        + "  inner join afr_systemdept sd on sd.department_no = asdu.department_no "
                        + "  inner join afr_systemcompany sc on sc.company_code=sd.company_code  " + "  where sr.id=? and afs.employee_status='0' ";
            } else {
                sql += "select distinct afs.ROWNO as \"id\",afs.employee_name as \"name\",'false' as \"isParent\" ,'false' as \"nocheck\" from AFR_systemuser afs  inner join system_user_role sur on sur.row_no=afs.rowno inner join system_role sr on sr.id=sur.role_id"
                        + "  inner join afr_system_dept_user asdu on asdu.rowno=afs.rowno "
                        + "  inner join afr_systemdept sd on sd.department_no = asdu.department_no "
                        + "  inner join afr_systemcompany sc on sc.company_code=sd.company_code  "
                        + "  where sr.id=? and afs.employee_status='0'"
                        + "  and sc.company_code in " + code + "";
            }
            // System.out.println("查询SQL==》"+sql);
            return this.getSession().createSQLQuery(sql).addScalar("isParent", Hibernate.STRING).addScalar("id", Hibernate.STRING)
                    .addScalar("name", Hibernate.STRING).setInteger(0, Integer.valueOf(id)).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
                    .list();
        } else {
            return null;
        }
    }

    public PageResponse getApprovalList(PageRequest page, SystemUser user, String orderNo, String createUserName) {
        String sql = "select * from (select os.TITLE as ORDER_TITLE,os.ORDER_NO,OO.ID AS WORKBENCHID,oo.TITLE as WORKBENCHTITLE,b.status,b.bak1 as REJECT, bk.id as TASKID,bk.creator_no,bk.creator_name,bk.oper_name,bk.oper_no,bk.gmt_create,bk.type " +
                " from OMS_SELL_ORDER os LEFT JOIN OMS_ORDER_WORKBENCH oo on oo.ORDER_NO=os.ORDER_NO" +
                " LEFT JOIN BPMS_RISKOFF_PROCESS b on b.biz_id=oo.id" +
                " LEFT JOIN BPMS_RISKOFF_TASK bk on bk.process_id=b.Process_sign " +
                "where (b.status =1 or b.status =0 or b.status =2) and bk.status=1 and bk.oper_no='" + user.getRowNo() + "' ";
        if (orderNo != null && orderNo.length() > 0) {
            sql += " AND os.ORDER_NO LIKE '%" + orderNo + "%'";
        }
        if (createUserName != null && createUserName.length() > 0) {
            sql += " AND bk.creator_name LIKE '%" + createUserName + "%'";
        }
        sql += " group by bk.id,bk.creator_no,bk.creator_name,bk.gmt_create,bk.oper_name,bk.oper_no,bk.gmt_create,bk.type,b.status,b.bak1,os.TITLE,os.ORDER_NO,OO.ID,oo.TITLE order by bk.gmt_create desc)";
        return getMapNoBy(sql, page);
    }

    public PageResponse getAPPOmsSellOrderList(PageRequest page, SystemUser user, String orderNo, String title, String unitId, String type) {
        String sql = "SELECT * FROM OMS_SELL_ORDER WHERE (OPERATE_NO='" + user.getRowNo() + "' or CREATE_NO='" + user.getRowNo() + "') ";
        if (type != null && type.length() > 0) {
            if (type.equals("0")) {
                sql += " AND STATE IN('0','1','2') AND OPERATE_NO is null ";
            } else if (type.equals("1")) {
                sql += " AND STATE IN('0','1','2','6') AND OPERATE_NO is not null ";
            }
        }
        if (orderNo != null && orderNo.length() > 0) {
            sql += " AND ORDER_NO LIKE '%" + orderNo + "%'";
        }
        if (title != null && title.length() > 0) {
            sql += " AND TITLE LIKE '%" + title + "%'";
        }
        if (unitId != null && unitId.length() > 0) {
            sql += " AND UNIT_ID LIKE '%" + unitId + "%'";
        }
        sql += " ORDER BY CREATE_DATE DESC";
        return getMapNoBy(sql, page);
    }

    public OmsCustomDetails getCustomTemplateByProductId(String productId, String state) {
        String sql = "SELECT * FROM OMS_CUSTOM_DETAILS WHERE DELETE_STATE = '0' and PRODUCT_ID = ? and state = ? ";
        return (OmsCustomDetails) getSession().createSQLQuery(sql).addEntity(OmsCustomDetails.class).setString(0, productId).setString(1, state).uniqueResult();
    }

    public PageResponse getMyOmsSellOrderList(PageRequest page, SystemUser user, String orderNo, String title, String unitId, String type, String status) {
        String sql = "select * from (select os.ID,OS.ORDER_NO,OS.UNIT_ID,OS.UNIT_NAME,OS.TITLE,OS.OPERATE_NAME,OS.OPERATE_NO,OS.MEMO,OS.DESCRIPTION,OS.CREATE_NO,os.create_date,os.STATE,os.IS_URGENT  " +
                " from OMS_SELL_ORDER os " +
                "LEFT JOIN OMS_ORDER_WORKBENCH oo on oo.ORDER_NO=os.ORDER_NO LEFT JOIN BPMS_RISKOFF_PROCESS b on b.biz_id=oo.id " +
                "LEFT JOIN BPMS_RISKOFF_TASK bk on bk.process_id=b.Process_sign where 1=1 ";
        if ("1".equals(type)) {
            sql += " and os.create_no = '" + user.getRowNo() + "' ";
        } else {
            sql += " and bk.oper_no='" + user.getRowNo() + "' ";
        }
        if ("1".equals(status)) {
            sql += " AND os.STATE IN('3','4')";
        } else {
            sql += " AND os.STATE IN('0','1','2','6')";
        }
        if (orderNo != null && orderNo.length() > 0) {
            sql += " AND os.ORDER_NO LIKE '%" + orderNo + "%'";
        }
        if (title != null && title.length() > 0) {
            sql += " AND os.TITLE LIKE '%" + title + "%'";
        }
        if (unitId != null && unitId.length() > 0) {
            sql += " AND os.UNIT_ID LIKE '%" + unitId + "%'";
        }
        sql += "group by os.ID,OS.ORDER_NO,OS.UNIT_ID,OS.UNIT_NAME,OS.TITLE,OS.OPERATE_NAME,OS.OPERATE_NO,OS.MEMO,OS.DESCRIPTION,OS.CREATE_NO,os.create_date,os.STATE,os.IS_URGENT ";
        sql += "order by os.create_date desc)";
        return getMapNoBy(sql, page);
    }

    public OmsCustomDetails saveOrupdateOmsCustomDetails(OmsCustomDetails details) {
        try {
            Assert.notNull(details); //判断是否为空
            Object merge = this.getSession().merge(details);
            details = (OmsCustomDetails) merge;
            return details;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public Integer updateOmsSellOrderByOrderNo(String contractInfoId, String omsSellOrderId) {
        String sql = "update OMS_SELL_ORDER set CONTRACT_ID = ? where id = ? ";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.setParameter(0, contractInfoId);
        sqlQuery.setParameter(1, omsSellOrderId);
        return sqlQuery.executeUpdate();
    }

    public Con_OrderForm saveOrUpdateCon_OrderForm(Con_OrderForm cof) {
        try {
            Assert.notNull(cof); //判断是否为空
            Object merge = this.getSession().merge(cof);
            cof = (Con_OrderForm) merge;
            return cof;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<OmsOrderManagerSignIn> orderManagerSignIn(int rowNo) {
        String sql = "SELECT * FROM OMS_ORDER_MANAGER_SIGN_IN WHERE IS_DELETE = '0' and status = '0' and ROW_NO = ? ";
        return getSession().createSQLQuery(sql).addEntity(OmsOrderManagerSignIn.class).setInteger(0, rowNo).list();
    }

    public OmsOrderManagerSignIn saveOrUpdateOrderManagerSignIn(OmsOrderManagerSignIn signIn) {
        try {
            Assert.notNull(signIn); //判断是否为空
            Object merge = this.getSession().merge(signIn);
            signIn = (OmsOrderManagerSignIn) merge;
            return signIn;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /*public Integer deleteOrderManagerSignIn() {
        String sql = "update OMS_ORDER_MANAGER_SIGN_IN set IS_DELETE = '1',AFTER_SIGN_IN_DATE = '"+new Date()+"' where IS_DELETE='0' ";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        return sqlQuery.executeUpdate();
    }*/

    public Integer deleteOrderManagerSignIn() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String format = formatter.format(new Date());
        String sql = "update OMS_ORDER_MANAGER_SIGN_IN set IS_DELETE = '1',AFTER_SIGN_IN_DATE = to_date('"+format+"','yyyy-MM-dd hh24:mi:ss') where IS_DELETE='0' ";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        return sqlQuery.executeUpdate();
    }

    public Integer getOmsSellOrderScoreByUserId(int rowNo) {
        String sql = "select sum(SCORE) from OMS_SELL_ORDER where operate_No=? ";
        Object obj = getSession().createSQLQuery(sql).setInteger(0, rowNo).uniqueResult();
        Integer num = 0;
        if (null != obj) {
            num = Integer.valueOf(obj + "");
            ;
        }
        return num;
    }

    public Integer getOmsSellOrderScoreAndNowByUserId(int rowNo) {
        String sql = "select sum(SCORE) from OMS_SELL_ORDER where operate_No=? and " +
                "to_date(to_char(create_Date,'yyyy-mm-dd hh:mi:ss'),'yyyy-mm-dd hh:mi:ss')<=(SELECT TRUNC(SYSDATE)+1-1/86400 FROM dual) " +
                "and to_date(to_char(create_Date,'yyyy-mm-dd hh:mi:ss'),'yyyy-mm-dd hh:mi:ss')>=(SELECT Trunc(SYSDATE) FROM dual)";
        Object obj = getSession().createSQLQuery(sql).setString(0, rowNo + "").uniqueResult();
        Integer num = 0;
        if (null != obj) {
            num = Integer.valueOf(obj + "");
            ;
        }
        return num;
    }

    public Integer getCountNowByUserId(int rowNo) {
        String sql = "select count(*) from OMS_SELL_ORDER where operate_No=? and " +
                "to_date(to_char(create_Date,'yyyy-mm-dd hh:mi:ss'),'yyyy-mm-dd hh:mi:ss')<=(SELECT TRUNC(SYSDATE)+1-1/86400 FROM dual) " +
                "and to_date(to_char(create_Date,'yyyy-mm-dd hh:mi:ss'),'yyyy-mm-dd hh:mi:ss')>=(SELECT Trunc(SYSDATE) FROM dual)";
        return Integer.valueOf(getSession().createSQLQuery(sql).setString(0, rowNo + "").uniqueResult() + "");
    }

    public List<Map<String, Object>> selectProductList(String tableName) {
        String sql = "select * from " + tableName;
        return this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public List<PmsProductInfo> selectPmsProductInfoList(String label_id) {
        String sql = "SELECT * FROM PMS_PRODUCT_INFO WHERE LABEL_ID=? ";
        return getSession().createSQLQuery(sql).addEntity(PmsProductInfo.class).setString(0, label_id).list();
    }

    /**
     * 统计分析分析预受理明细
     *
     * @param pageintegratedQuery
     * @param user
     * @return
     */
    public LayuiPage getOmsSellOrderStatisticeList(LayuiPage page, SystemUser user, String state, String endTime, String starTime, String UNIT_ID,
                                                   String TITLE, String ORDER_NO, String COMPANY_NAME, Boolean flag,String prcNo) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (flag) {   //是否为订单管理员
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT OMS.DEMAND_TYPE,link.link_Name,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date," +
                        "OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE," +
                        "OMS.COMPANY_NAME,OMS.COUNTY_NAME,LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "left JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "where 1=1 and oms.VERSION_NUMBER='2' ";
            } else {
                sql += "SELECT OMS.DEMAND_TYPE,link.link_Name,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date," +
                        "OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE,OMS.COMPANY_NAME," +
                        "OMS.COUNTY_NAME,LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME  from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "left JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='2' ";
            }
        } else {
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT OMS.DEMAND_TYPE,link.link_Name,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date," +
                        "OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE,OMS.COMPANY_NAME," +
                        "OMS.COUNTY_NAME,LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME  from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "left JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "where 1=1 and oms.VERSION_NUMBER='2' ";
            } else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                sql += "SELECT OMS.DEMAND_TYPE,link.link_Name,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date," +
                        "OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE,OMS.COMPANY_NAME," +
                        "OMS.COUNTY_NAME,LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME  from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "left JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='2' ";
            } else {
                sql += "SELECT OMS.DEMAND_TYPE,link.link_Name,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date," +
                        "OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE,OMS.COMPANY_NAME," +
                        "OMS.COUNTY_NAME,LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME  from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "left JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "WHERE oms.COUNTY_NO = '" + list.get(0).get("COUNTY_NO").toString() + "' and oms.VERSION_NUMBER='2' ";
            }
        }
        if (state != null && !"".equals(state)) {
            sql += " AND oms.STATE ='" + state + "'";
        }else{
            sql += " AND oms.STATE !='-1'";
        }
        if (ORDER_NO != null && !"".equals(ORDER_NO)) {
            sql += " AND oms.ORDER_NO LIKE '%" + ORDER_NO + "%'";
        }
        if (TITLE != null && !"".equals(TITLE)) {
            sql += " AND oms.TITLE LIKE '%" + TITLE + "%'";
        }
        if (UNIT_ID != null && !"".equals(UNIT_ID)) {
            sql += " AND oms.UNIT_ID LIKE '%" + UNIT_ID + "%'";
        }
        if (COMPANY_NAME != null && !"".equals(COMPANY_NAME)&&COMPANY_NAME.length()>0) {
            sql += " AND oms.COMPANY_NO = '" + COMPANY_NAME + "'";
        }
        if (starTime != null && !"".equals(starTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + starTime + " 00:00:00'";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'";
        }
        if (prcNo != null && prcNo.length() > 0) {
            sql+=" AND OP.PRC_NO='"+prcNo+"'";
        }
        sql += (" group by " +
                "OMS.DEMAND_TYPE,link.link_Name,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE,OMS.COMPANY_NAME,OMS.COUNTY_NAME " +
                "ORDER BY oms.CREATE_DATE DESC");

        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 统计分析分析预受理明细
     *
     * @param page
     * @param user
     * @return
     */
    public LayuiPage getOmsSellOrderListVersionNumber(LayuiPage page, SystemUser user, String state, String endTime, String starTime, String UNIT_ID,
                                                      String TITLE, String ORDER_NO, String COMPANY_NAME, Boolean flag,String prcNo) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (flag) {//是否为订单管理员
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name," +
                        "oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                        ",LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "where 1=1 and oms.VERSION_NUMBER='1' ";
            } else {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name," +
                        "oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                        ",LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='1' ";
            }
        } else {
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name," +
                        "oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                        ",LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "where 1=1 and oms.VERSION_NUMBER='1' ";
            } else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name," +
                        "oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                        ",LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='1' ";
            } else {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name," +
                        "oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                        ",LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                        "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                        "WHERE oms.COUNTY_NO = '" + list.get(0).get("COUNTY_NO").toString() + "' and oms.VERSION_NUMBER='1' ";
            }
        }
        sql += " AND oms.STATE ='4'";
        if (ORDER_NO != null && !"".equals(ORDER_NO)) {
            sql += " AND oms.ORDER_NO LIKE '%" + ORDER_NO + "%'";
        }
        if (TITLE != null && !"".equals(TITLE)) {
            sql += " AND oms.TITLE LIKE '%" + TITLE + "%'";
        }
        if (UNIT_ID != null && !"".equals(UNIT_ID)) {
            sql += " AND oms.UNIT_ID LIKE '%" + UNIT_ID + "%'";
        }
        if (COMPANY_NAME != null && !"".equals(COMPANY_NAME)&&COMPANY_NAME.length()>0) {
            sql += " AND oms.COMPANY_NO = '" + COMPANY_NAME + "'";
        }
        if (starTime != null && !"".equals(starTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + starTime + " 00:00:00'";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'";
        }
        if (prcNo != null && prcNo.length() > 0) {
            sql+=" AND OP.PRC_NO='"+prcNo+"'";
        }
        sql += (" group by " +
                "oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                "ORDER BY oms.CREATE_DATE DESC");

        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 统计分析分析预受理明细-->导出
     *
     * @param user
     * @param state
     * @param endTime
     * @param starTime
     * @param UNIT_ID
     * @param TITLE
     * @param ORDER_NO
     * @return
     */
    public List<Map<String, Object>> getOmsSellOrderStatisticeExcel(SystemUser user, String state, String endTime, String starTime, String UNIT_ID, String TITLE, String ORDER_NO, String COMPANY_NAME, Boolean flag) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (flag) {   //是否为订单管理员
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT link.link_Name,link.oper_name,link.oper_date,oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        "INNER JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "where 1=1 and oms.VERSION_NUMBER='2' ";
            } else {
                sql += "SELECT link.link_Name,link.oper_name,link.oper_date,oms.DEMAND_TYPE, oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        "INNER JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='2' ";
            }
        } else {
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT link.link_Name,link.oper_name,link.oper_date,oms.DEMAND_TYPE, oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        " where 1=1 and oms.VERSION_NUMBER='2' ";
            } else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                sql += "SELECT link.link_Name,link.oper_name,link.oper_date,oms.DEMAND_TYPE, oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        "INNER JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='2' ";
            } else {
                sql += "SELECT link.link_Name,link.oper_name,link.oper_date,oms.DEMAND_TYPE, oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                        "INNER JOIN  OMS_ORDER_LINK link ON link.link_order_no = oms.link_order_no " +
                        "WHERE oms.COUNTY_NO = '" + list.get(0).get("COUNTY_NO").toString() + "' and oms.VERSION_NUMBER='2' ";
            }
        }
        if (state != null && !"".equals(state)) {
            sql += "AND oms.STATE ='" + state + "'";
        }
        if (ORDER_NO != null && !"".equals(ORDER_NO)) {
            sql += " AND oms.ORDER_NO LIKE '%" + ORDER_NO + "%'";
        }
        if (TITLE != null && !"".equals(TITLE)) {
            sql += " AND oms.TITLE LIKE '%" + TITLE + "%'";
        }
        if (UNIT_ID != null && !"".equals(UNIT_ID)) {
            sql += " AND oms.UNIT_ID LIKE '%" + UNIT_ID + "%'";
        }
        if (COMPANY_NAME != null && !"".equals(COMPANY_NAME)&&COMPANY_NAME.length()>0) {
            sql += " AND oms.COMPANY_NO = '" + COMPANY_NAME + "'";
        }
        if (starTime != null && !"".equals(starTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + starTime + " 00:00:00'";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'";
        }
        sql += (" group by " +
                "link.link_Name,link.oper_name,link.oper_date,oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                "ORDER BY oms.CREATE_DATE DESC");
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 统计分析分析预受理明细-->导出
     *
     * @param user
     * @param state
     * @param endTime
     * @param starTime
     * @param UNIT_ID
     * @param TITLE
     * @param ORDER_NO
     * @return
     */
    public List<Map<String, Object>> getOmsSellOrderStatisticeExcelTwo(SystemUser user, String state, String endTime, String starTime, String UNIT_ID, String TITLE, String ORDER_NO, String COMPANY_NAME, Boolean flag) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ISMAINDPT = 'true' AND ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (flag) {   //是否为订单管理员
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        "where 1=1 and oms.VERSION_NUMBER='1' ";
            } else {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='1' ";
            }
        } else {
            if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        " where 1=1 and oms.VERSION_NUMBER='1' ";
            } else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE from OMS_SELL_ORDER oms " +
                        "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "' and oms.VERSION_NUMBER='1' ";
            } else {
                sql += "SELECT oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                        "from OMS_SELL_ORDER oms " +
                        "WHERE oms.COUNTY_NO = '" + list.get(0).get("COUNTY_NO").toString() + "' and oms.VERSION_NUMBER='1' ";
            }
        }
        sql += " AND oms.STATE ='4'";
        if (ORDER_NO != null && !"".equals(ORDER_NO)) {
            sql += " AND oms.ORDER_NO LIKE '%" + ORDER_NO + "%'";
        }
        if (TITLE != null && !"".equals(TITLE)) {
            sql += " AND oms.TITLE LIKE '%" + TITLE + "%'";
        }
        if (UNIT_ID != null && !"".equals(UNIT_ID)) {
            sql += " AND oms.UNIT_ID LIKE '%" + UNIT_ID + "%'";
        }
        if (COMPANY_NAME != null && !"".equals(COMPANY_NAME)&&COMPANY_NAME.length()>0) {
            sql += " AND oms.COMPANY_NO = '" + COMPANY_NAME + "'";
        }
        if (starTime != null && !"".equals(starTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + starTime + " 00:00:00'";
        }
        if (endTime != null && !"".equals(endTime)) {
            sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'";
        }
        sql += (" group by " +
                "oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,OMS.MODIFY_DATE,OMS.COMPLETE_DATE " +
                "ORDER BY oms.CREATE_DATE DESC");
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
      * @Description: 获取两个时间的小时差
      * @Param: [endDate:结束时间, nowDate:开始时间]
      * @return: java.lang.String
      * @Author: TX
      * @Date: 2021/10/9 14:33
    */
    public String getDatePoor(String end, String now) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            Date endDate = sdf.parse(end);
            Date nowDate = sdf.parse(now);
            long nd = 86400000;
            long nh = 3600000;
            long diff = endDate.getTime() - nowDate.getTime();
            return String.valueOf(diff % nd / nh)+"小时";
        } catch (ParseException e) {
            return "日期格式错误:请联系管理员处理!";
        }
    }

    /**
     * 导出Excel
     *
     * @param mapList
     * @throws IOException
     * @throws WriteException
     */
    public void OmsSellOrderStatisticeExcel(List<Map<String, Object>> mapList,int type) throws IOException, WriteException {
        HttpServletResponse response = ServletActionContext.getResponse();
        String excelFile = FileUpload.getFtpURL() + "exportExcelToJxl.xls";
        File file = new File(FileUpload.getFtpURL());

        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        String exportName = "OmsSellOrderStatisticeExcel_" + FileUpload.getDateToString("yyyy_MM_dd");
        try {
            WritableWorkbook wb = Workbook.createWorkbook(new File(excelFile));
            WritableSheet firstSheet = wb.createSheet("统计分析 > 预受理统计管理", 1);
            String[] headers=new String[]{};
            if(type==0){
                headers = new String[]{"工单编号", "需求名称", "需求状态", "产品名称", "集团280", "集团名称", "是否为建档集团", "所属地市", "所属部门", "创建时间","工单总耗时", "订单经理", "创建人", "当前环节","需求类型","状态"};
            }else{
                headers = new String[]{"工单编号", "需求名称", "需求状态", "产品名称", "集团280", "集团名称", "是否为建档集团", "所属地市", "所属部门", "创建时间","工单总耗时", "订单经理", "创建人", "状态"};
            }
            for (int i = 0; i < headers.length; i++) {
                // 3、创建单元格(Label)对象
                Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
                WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                // 标题栏 // 颜色
                WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
                wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
                wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
                wcfTitle.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfTitle.setAlignment(jxl.format.Alignment.CENTRE); // 设置垂直对齐
                // 内容栏
                WritableCellFormat wcfContent = new WritableCellFormat(wf3);
                wcfContent.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐

                CellView navCellView = new CellView();
                navCellView.setSize(150 * 50);

                label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
                firstSheet.setColumnView(i, navCellView); // 设置col显示样式
                firstSheet.setRowView(i, 400, false); // 设置行高
                firstSheet.addCell(label0);

                if (mapList.size() > 0) {
                    for (int i1 = 0; i1 < mapList.size(); i1++) {
                        if (mapList.get(i1).get("ORDER_NO") != null) {
                            Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("ORDER_NO")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("TITLE") != null) {
                            Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("TITLE")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("IS_URGENT") != null) {
                            String IS_URGENT = mapList.get(i1).get("IS_URGENT").toString();
                            String value = "";
                            if ("2".equals(IS_URGENT)) {
                                value = "加急";
                            } else {
                                value = "正常";
                            }
                            Label label = new Label(2, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("PROD_NAME") != null) {
                            Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("PROD_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("UNIT_ID") != null) {
                            Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("UNIT_ID")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("UNIT_NAME") != null) {
                            Label label = new Label(5, i1 + 1, String.valueOf(mapList.get(i1).get("UNIT_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }

                        if (mapList.get(i1).get("IS_UNIT") != null) {
                            String ISUNIT = mapList.get(i1).get("IS_UNIT").toString();
                            String value = "";
                            if ("1".equals(ISUNIT)) {
                                value = "未建集团";
                            } else {
                                value = "已建集团";
                            }
                            Label label = new Label(6, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("COMPANY_NAME") != null) {
                            Label label = new Label(7, i1 + 1, String.valueOf(mapList.get(i1).get("COMPANY_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("COUNTY_NAME") != null) {
                            Label label = new Label(8, i1 + 1, String.valueOf(mapList.get(i1).get("COUNTY_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CREATE_DATE") != null) {
                            Label label = new Label(9, i1 + 1, String.valueOf(mapList.get(i1).get("CREATE_DATE")), wcfContent);
                            firstSheet.addCell(label);
                        }

                        if (mapList.get(i1).get("STATE") != null) {
                            String state1 = mapList.get(i1).get("STATE").toString();
                            if ("0".equals(state1)) {
                                Label label = new Label(10, i1 + 1, "工单未完成", wcfContent);
                                firstSheet.addCell(label);
                            }else{
                                if (mapList.get(i1).get("COMPLETE_DATE") != null) {
                                    Label label = new Label(10, i1 + 1, this.getDatePoor(String.valueOf(mapList.get(i1).get("COMPLETE_DATE")),String.valueOf(mapList.get(i1).get("CREATE_DATE"))), wcfContent);
                                    firstSheet.addCell(label);
                                }else {
                                    Label label = new Label(10, i1 + 1, "历史工单未统计耗时", wcfContent);
                                    firstSheet.addCell(label);
                                }
                            }
                        }else{
                            if (mapList.get(i1).get("MODIFY_DATE") != null) {
                                Label label = new Label(10, i1 + 1, "", wcfContent);
                                firstSheet.addCell(label);
                            }
                        }
                        if (mapList.get(i1).get("OPERATE_NAME") != null) {
                            Label label = new Label(11, i1 + 1, String.valueOf(mapList.get(i1).get("OPERATE_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("CREATE_NAME") != null) {
                            Label label = new Label(12, i1 + 1, String.valueOf(mapList.get(i1).get("CREATE_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if(type==0){
                            if (mapList.get(i1).get("LINK_NAME") != null) {
                                Label label = new Label(13, i1 + 1, String.valueOf(mapList.get(i1).get("LINK_NAME")), wcfContent);
                                firstSheet.addCell(label);
                            }
                            if (mapList.get(i1).get("DEMAND_TYPE") != null) {
                                String state1 = mapList.get(i1).get("DEMAND_TYPE").toString();
                                String value = "";
                                if ("0".equals(state1)) {
                                    value = "开通";
                                } else if ("1".equals(state1)) {
                                    value = "变更";
                                } else if ("2".equals(state1)) {
                                    value = "销户";
                                }
                                Label label = new Label(14, i1 + 1, value, wcfContent);
                                firstSheet.addCell(label);

                            }
                            if (mapList.get(i1).get("STATE") != null) {
                                String state1 = mapList.get(i1).get("STATE").toString();
                                String value = "";
                                if ("0".equals(state1)) {
                                    value = "未完成";
                                } else if ("1".equals(state1)) {
                                    value = "已完成";
                                } else if ("-1".equals(state1)) {
                                    value = "已作废";
                                }
                                Label label = new Label(15, i1 + 1, value, wcfContent);
                                firstSheet.addCell(label);
                            }
                        }else{
                            if (mapList.get(i1).get("STATE") != null) {
                                String state1 = mapList.get(i1).get("STATE").toString();
                                String value = "";
                                if ("0".equals(state1)) {
                                    value = "待受理";
                                } else if ("1".equals(state1)) {
                                    value = "受理中";
                                } else if ("2".equals(state1)) {
                                    value = "建设中";
                                } else if ("3".equals(state1)) {
                                    value = "已完成";
                                } else if ("4".equals(state1)) {
                                    value = "已归档";
                                } else if ("5".equals(state1)) {
                                    value = "已作废";
                                }else if( "6".equals(state1)){
                                    value = "已驳回";
                                }
                                Label label = new Label(13, i1 + 1, value, wcfContent);
                                firstSheet.addCell(label);
                            }
                        }
                    }
                }
            }
            wb.write();// 打开流 开始写文件
            wb.close();// 关闭流
            byte[] data = FileUtil.toByteArray2(excelFile);
            String fileName = URLEncoder.encode(exportName, "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xls" + "\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
            File fe = new File(excelFile);
            fe.delete();
        } finally {
            File fe = new File(excelFile);
            if (file.exists() && file.isDirectory()) {
                fe.delete();
            }
        }
    }

    /**
     * 预开票签到查询
     *
     * @param page
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @param rowNo     签到工号
     * @param user
     * @return
     * @tangxiao
     */
    public LayuiPage getOrderMangerSignin(LayuiPage page,String startTime,String endTime,String rowNo,SystemUser user,String CompanyName){
        String userInfo = "SELECT * from VW_USERINFO WHERE ROWNO = '"+user.getRowNo()+"' AND ISMAINDPT = 'true' ";
        List<Map<String,Object>> list =getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (list.get(0).get("COMPANY_NAME").equals("省公司")) {     //查询所有
            sql += "SELECT * from OMS_ORDER_MANAGER_SIGN_IN oms LEFT JOIN  VW_USERINFO v ON oms.ROW_NO = v.rowno where 1=1 AND v.ISMAINDPT = 'true' ";
        } else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
            sql += "SELECT * from OMS_ORDER_MANAGER_SIGN_IN oms LEFT JOIN  VW_USERINFO v ON oms.ROW_NO = v.rowno WHERE COMPANY_CODE = '" + list.get(0).get("COMPANY_CODE").toString() + "' AND v.ISMAINDPT = 'true' ";
        } else {
            sql += "SELECT * from OMS_ORDER_MANAGER_SIGN_IN oms LEFT JOIN  VW_USERINFO v ON oms.ROW_NO = v.rowno WHERE COUNTY_NO = '" + list.get(0).get("COUNTY_NO").toString() + "' AND v.ISMAINDPT = 'true' ";
        }
        if (rowNo!=null&&!"".equals(rowNo)){
            sql+="AND oms.ROW_NO ='"+ rowNo +"'";
        }
        if (CompanyName!=null&&!"".equals(CompanyName)){
            sql+="AND v.COMPANY_NAME ='"+ CompanyName +"'";
        }
        if (startTime!=null&&!"".equals(startTime)){
            sql += " AND to_char(BEFORE_SIGN_IN_DATE,'YYYY-MM-DD hh24:mi:ss') >= '"+startTime+" 00:00:00'";
        }
        if (startTime!=null&&!"".equals(startTime)){
            sql += " AND to_char(BEFORE_SIGN_IN_DATE,'YYYY-MM-DD hh24:mi:ss') <= '"+endTime+" 23:59:59'";
        }

        sql += ("ORDER BY BEFORE_SIGN_IN_DATE DESC");
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }
    public List<Map<String,Object>> getgetOrderMangerSigninExcel(String startTime,String endTime,String rowNo,SystemUser user,String CompanyName){
        String userInfo = "SELECT * from VW_USERINFO WHERE ROWNO = '"+user.getRowNo()+"' AND ISMAINDPT = 'true' ";
        List<Map<String,Object>> list =getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (list.get(0).get("COMPANY_NAME").equals("省公司")){     //查询所有
            sql+="SELECT * from OMS_ORDER_MANAGER_SIGN_IN oms LEFT JOIN  VW_USERINFO v ON oms.ROW_NO = v.rowno where 1=1 AND v.ISMAINDPT = 'true' ";
        }else if (!(list.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (list.get(0).get("COUNTY_NAME").toString()).contains("直属")){
            sql+="SELECT * from OMS_ORDER_MANAGER_SIGN_IN oms LEFT JOIN  VW_USERINFO v ON oms.ROW_NO = v.rowno WHERE COMPANY_CODE = '"+list.get(0).get("COMPANY_CODE").toString()+"' AND v.ISMAINDPT = 'true' ";
        }else {
            sql+="SELECT * from OMS_ORDER_MANAGER_SIGN_IN oms LEFT JOIN  VW_USERINFO v ON oms.ROW_NO = v.rowno WHERE COUNTY_NO = '"+list.get(0).get("COUNTY_NO").toString()+"' AND v.ISMAINDPT = 'true' ";
        }
        if (rowNo!=null&&!"".equals(rowNo)){
            sql+="AND oms.ROW_NO ='"+ rowNo +"'";
        }
        if (CompanyName!=null&&!"".equals(CompanyName)){
            sql+="AND v.COMPANY_NAME ='"+ CompanyName +"'";
        }
        if (startTime!=null&&!"".equals(startTime)){
            sql += " AND to_char(BEFORE_SIGN_IN_DATE,'YYYY-MM-DD hh24:mi:ss') >= '"+startTime+" 00:00:00'";
        }
        if (startTime!=null&&!"".equals(startTime)){
            sql += " AND to_char(BEFORE_SIGN_IN_DATE,'YYYY-MM-DD hh24:mi:ss') <= '"+endTime+" 23:59:59'";
        }

        sql+=("ORDER BY BEFORE_SIGN_IN_DATE DESC");
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 导出Excel
     * @param mapList
     * @throws IOException
     * @throws WriteException
     */
    public void OrderMangerSigninExcel(List<Map<String,Object>> mapList) throws IOException, WriteException {
        HttpServletResponse response = ServletActionContext.getResponse();
        String excelFile = FileUpload.getFtpURL() + "exportExcelToJxl.xls";
        File file = new File(FileUpload.getFtpURL());

        if (!file.exists() && !file.isDirectory()) {
            file.mkdir();
        }
        String exportName = "OmsSellOrderStatisticeExcel_" + FileUpload.getDateToString("yyyy_MM_dd");
        try {
            WritableWorkbook wb = Workbook.createWorkbook(new File(excelFile));
            WritableSheet firstSheet = wb.createSheet("统计分析 > 签到统计列表", 1);
            String[] headers = new String[]{"签到用户编号","签到用户名称","上班签到时间","下班签到时间","所属地市","所属区县","上下班状态"};
            for (int i = 0; i < headers.length; i++) {
                // 3、创建单元格(Label)对象
                Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
                WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
                // 标题栏 // 颜色
                WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
                wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
                wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
                wcfTitle.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfTitle.setAlignment(jxl.format.Alignment.CENTRE); // 设置垂直对齐
                // 内容栏
                WritableCellFormat wcfContent = new WritableCellFormat(wf3);
                wcfContent.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
                wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐

                CellView navCellView = new CellView();
                navCellView.setSize(150 * 50);

                label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
                firstSheet.setColumnView(i, navCellView); // 设置col显示样式
                firstSheet.setRowView(i, 400, false); // 设置行高
                firstSheet.addCell(label0);

                if (mapList.size() > 0) {
                    for (int i1 = 0; i1 < mapList.size(); i1++) {
                        if (mapList.get(i1).get("ROW_NO") != null) {
                            Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("ROW_NO")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("USER_NAME") != null) {
                            Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("USER_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("BEFORE_SIGN_IN_DATE") != null) {
                            Label label = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("BEFORE_SIGN_IN_DATE")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("AFTER_SIGN_IN_DATE") != null) {
                            Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("AFTER_SIGN_IN_DATE")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("COMPANY_NAME") != null) {
                            Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("COMPANY_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }
                        if (mapList.get(i1).get("COUNTY_NAME") != null) {
                            Label label = new Label(5, i1 + 1, String.valueOf(mapList.get(i1).get("COUNTY_NAME")), wcfContent);
                            firstSheet.addCell(label);
                        }

                        if (mapList.get(i1).get("STATUS") != null) {
                            String STATUS = mapList.get(i1).get("STATUS").toString();
                            String value = "";
                            if("0".equals(STATUS)){
                                value = "上班";
                            }else{
                                value = "下班";
                            }
                            Label label = new Label(6, i1 + 1, value, wcfContent);
                            firstSheet.addCell(label);
                        }
                    }
                }
            }
            wb.write();// 打开流 开始写文件
            wb.close();// 关闭流
            byte[] data = FileUtil.toByteArray2(excelFile);
            String fileName = URLEncoder.encode(exportName, "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xls" + "\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
            File fe = new File(excelFile);
            fe.delete();
        }finally{
            File fe = new File(excelFile);
            if (file.exists() && file.isDirectory()) {
                fe.delete();
            }
        }
    }

    /**
     * 查询地市
     *
     * @return
     * @tangxiao
     */
    public List<Map<String, Object>> getMangerSigninCompany() {
        String userInfo = "SELECT COMPANY_NAME,COMPANY_CODE from AFR_SYSTEMCOMPANY";
        return getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public List<Map<String, Object>> getOmsOrder(String id) {
        String userInfo = "SELECT distinct oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME from OMS_SELL_ORDER oms where oms.ID='" + id + "' ";
        return getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 日期转换
     *
     * @params
     */
    public static String substringDate(String date) {
        String newDate = date.substring(0, 10);
        return newDate;
    }

    public Integer deleteOmsCustomDetails() {
        String sql = "delete from  OMS_CUSTOM_DETAILS ";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        return sqlQuery.executeUpdate();
    }

    public ContractInfo saveOrupdateContractInfo(ContractInfo contractInfo) {
        try {
            Assert.notNull(contractInfo); //判断是否为空
            Object merge = this.getSession().merge(contractInfo);
            contractInfo = (ContractInfo) merge;
            return contractInfo;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<Map<String, Object>> getOmsSellOrderAndContractInfo(String id) {
        String sql = "select ci.ID,ci.PDFATTURL,ci.PDFURL,ci.CONTRACTTYPE from OMS_SELL_ORDER oso INNER JOIN ContractInfo  ci on oso.CONTRACT_ID=ci.ID where CONTRACTTYPE='PRC'";
        if (null != id && !"".equals(id)) {
            sql += " and CI.ID = '" + id + "'";
        }
        sql += "  GROUP BY ci.ID,ci.PDFATTURL,ci.PDFURL,ci.CONTRACTTYPE";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public List<Map<String, Object>> selectListMapOrder() {
        String sql = "select os.ORDER_NO,os.CREATE_NO as ORDER_CREATE_NO,os.IS_SUPPORT " +
                "from OMS_SELL_ORDER os where os.OPERATE_NO IS NULL and VERSION_NUMBER='2' AND os.IS_URGENT='1'";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public LayuiPage getAllotOmsSellOrderList(LayuiPage page, SystemUser user,String time, String endTime, String starTime, String UNIT_ID,
                                              String TITLE, String ORDER_NO, String COMPANY_NAME,String prcNo) {
        String userInfo = "SELECT * from VW_USERINFO WHERE ROWNO = '" + user.getRowNo() + "' AND ISMAINDPT = 'true' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
            sql += "SELECT oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit," +
                    "oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ," +
                    "OMS.IS_URGENT ,OMS.PROD_NAME,oms.operate_Date,oms.operate_Handle_Date,LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME  from OMS_SELL_ORDER oms " +
                    "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                    "where oms.OPERATE_NO IS NULL AND OMS.STATE!='-1' AND OMS.VERSION_NUMBER='2' ";
        } else {
            sql += "SELECT oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit," +
                    "oms.Create_date,OMS.state,oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ," +
                    "OMS.IS_URGENT ,OMS.PROD_NAME,oms.operate_Date,oms.operate_Handle_Date,LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME  from OMS_SELL_ORDER oms " +
                    "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                    "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "'  " +
                    "and OMS.OPERATE_NO IS NULL AND OMS.STATE!='-1' AND OMS.VERSION_NUMBER='2' ";
        }

        if (time != null && !"".equals(time)) {
            //sql += "AND oms.OPERATE_HANDLE_DATE  <= to_date('" + time + "', 'YYYY-MM-DD HH24:MI:SS')";
            sql += " AND TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') <=TO_DATE('"+time+"','YYYY-MM-DD HH24:MI:SS')";
        }
        if (ORDER_NO != null && !"".equals(ORDER_NO)) {
            sql += " AND oms.ORDER_NO LIKE '%" + ORDER_NO + "%'";
        }
        if (TITLE != null && !"".equals(TITLE)) {
            sql += " AND oms.TITLE LIKE '%" + TITLE + "%'";
        }
        if (UNIT_ID != null && !"".equals(UNIT_ID)) {
            sql += " AND oms.UNIT_ID LIKE '%" + UNIT_ID + "%'";
        }
        if (COMPANY_NAME != null && !"".equals(COMPANY_NAME)&&COMPANY_NAME.length()>0) {
            sql += " AND oms.COMPANY_NO = '" + COMPANY_NAME + "'";
        }
        if (starTime != null && !"".equals(starTime)) {
            //sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + starTime + " 00:00:00'";
            starTime=starTime+" 00:00:00";
            sql += " AND TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') >=TO_DATE('"+starTime+"','YYYY-MM-DD HH24:MI:SS')";
        }
        if (endTime != null && !"".equals(endTime)) {
            //sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'";
            endTime=endTime+" 23:59:59";
            sql += " AND TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') <=TO_DATE('"+endTime+"','YYYY-MM-DD HH24:MI:SS')";
        }

        if (prcNo != null && prcNo.length() > 0) {
            sql+=" AND OP.PRC_NO='"+prcNo+"'";
        }

        sql += (" GROUP BY oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state," +
                "oms.county_name,oms.COMPANY_NAME,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME," +
                "oms.operate_Date,oms.operate_Handle_Date ORDER BY oms.OPERATE_HANDLE_DATE DESC");
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public LayuiPage getAllotOmsSellOrderTwentyFourList(LayuiPage page, SystemUser user,String time, String endTime, String starTime, String UNIT_ID,
                                                        String TITLE, String ORDER_NO, String COMPANY_NAME,String prcNo) {
        Calendar calendar = Calendar.getInstance();//此时打印它获取的是系统当前时间
        calendar.add(Calendar.DATE, -1);    //得到前一天
        String twentyFourTime = String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", calendar.getTime());//前一天的当前时间
        String userInfo = "SELECT * from VW_USERINFO WHERE ROWNO = '" + user.getRowNo() + "' ";
        List<Map<String, Object>> list = getSession().createSQLQuery(userInfo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        String sql = "";
        if (list.get(0).get("COMPANY_NAME").equals("省公司")) {
            sql += "SELECT oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,oms.operate_Date,oms.operate_Handle_Date,OMS.COMPANY_NAME,OMS.COUNTY_NAME " +
                    ",LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                    "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                    "INNER JOIN OMS_ORDER_LINK link on link.LINK_ORDER_NO=oms.LINK_ORDER_NO "+
                    "INNER JOIN OMS_LINK_DIALOGUE ld on ld.LINK_ORDER_NO=link.LINK_ORDER_NO "+
                    "where TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') <=TO_DATE('"+twentyFourTime+"','YYYY-MM-DD HH24:MI:SS') " +
                    "AND link.LINK_CODE='3' AND link.STATUS='0' " +
                    "AND ld.OPER_NO=oms.OPERATE_NO " +
                    "AND ld.STATUS='0' AND OMS.STATE!='-1' AND OMS.VERSION_NUMBER='2' ";
        } else {
            sql += "SELECT oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date,OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,oms.operate_Date,oms.operate_Handle_Date,OMS.COMPANY_NAME,OMS.COUNTY_NAME  " +
                    ",LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME from OMS_SELL_ORDER oms " +
                    "LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO = OMS.ORDER_NO " +
                    "INNER JOIN OMS_ORDER_LINK link on link.LINK_ORDER_NO=oms.LINK_ORDER_NO "+
                    "INNER JOIN OMS_LINK_DIALOGUE ld on ld.LINK_ORDER_NO=link.LINK_ORDER_NO "+
                    "WHERE oms.COMPANY_NO = '" + list.get(0).get("COMPANY_CODE").toString() + "'  " +
                    "and TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') <=TO_DATE('"+twentyFourTime+"','YYYY-MM-DD HH24:MI:SS') " +
                    "AND link.LINK_CODE='3' AND link.STATUS='0' " +
                    "AND ld.OPER_NO=oms.OPERATE_NO " +
                    "AND ld.STATUS='0' AND OMS.STATE!='-1' AND OMS.VERSION_NUMBER='2' ";
        }
        /*if (time != null && !"".equals(time)) {
            //sql += "AND oms.OPERATE_HANDLE_DATE  <= to_date('" + time + "', 'YYYY-MM-DD HH24:MI:SS')";
            sql += " AND TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') <=TO_DATE('"+time+"','YYYY-MM-DD HH24:MI:SS')";
        }*/
        if (ORDER_NO != null && !"".equals(ORDER_NO)) {
            sql += " AND oms.ORDER_NO LIKE '%" + ORDER_NO + "%'";
        }
        if (TITLE != null && !"".equals(TITLE)) {
            sql += " AND oms.TITLE LIKE '%" + TITLE + "%'";
        }
        if (UNIT_ID != null && !"".equals(UNIT_ID)) {
            sql += " AND oms.UNIT_ID LIKE '%" + UNIT_ID + "%'";
        }
        if (COMPANY_NAME != null && !"".equals(COMPANY_NAME)&&COMPANY_NAME.length()>0) {
            sql += " AND oms.COMPANY_NO = '" + COMPANY_NAME + "'";
        }
        if (starTime != null && !"".equals(starTime)) {
            //sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + starTime + " 00:00:00'";
            starTime=starTime+" 00:00:00";
            sql += " AND TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') >=TO_DATE('"+starTime+"','YYYY-MM-DD HH24:MI:SS')";
        }
        if (endTime != null && !"".equals(endTime)) {
            //sql += " AND to_char(oms.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'";
            endTime=endTime+" 23:59:59";
            sql += " AND TO_DATE(TO_CHAR(oms.CREATE_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') <=TO_DATE('"+endTime+"','YYYY-MM-DD HH24:MI:SS')";
        }
        if (prcNo != null && prcNo.length() > 0) {
            sql+=" AND OP.PRC_NO='"+prcNo+"'";
        }
        sql += (" GROUP BY oms.DEMAND_TYPE,oms.ORDER_NO,OMS.ID,oms.TITLE,OMS.unit_ID,oms.unit_name,OMS.is_unit,oms.Create_date," +
                "OMS.state,OMS.OPERATE_NAME,OMS.CREATE_NAME ,OMS.IS_URGENT ,OMS.PROD_NAME,oms.operate_Date,oms.operate_Handle_Date," +
                "OMS.COMPANY_NAME,OMS.COUNTY_NAME  ORDER BY oms.OPERATE_HANDLE_DATE DESC");
        page.setCount(getCount("SELECT COUNT(0) FROM (" + sql + ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    public AllotOrderManager saveOrupdateAllotOrderManager(AllotOrderManager manager) {
        try {
            Assert.notNull(manager); //判断是否为空
            Object merge = this.getSession().merge(manager);
            manager = (AllotOrderManager) merge;
            return manager;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public FollowUpOrder saveFollowUpOrder(FollowUpOrder followUpOrder) {
        try {
            Assert.notNull(followUpOrder); //判断是否为空
            Object merge = this.getSession().merge(followUpOrder);
            followUpOrder = (FollowUpOrder) merge;
            return followUpOrder;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<FollowUpOrder> getFollowUpOrderByOrderNo(String orderNo) {
        String sql = "SELECT * FROM OMS_FOLLOW_UP_ORDER WHERE ORDER_NO= ? order by CREATE_DATE";
        return getSession().createSQLQuery(sql).addEntity(FollowUpOrder.class).setString(0, orderNo).list();
    }

    /**
     * gcy
     * 根据附件id查询附件路径
     *
     * @param id
     * @return
     */
    public Attachment getAttachment(String id) {
        String sql = "SELECT * FROM ATTACHMENT WHERE ATTACHMENTID=?";
        Attachment der = (Attachment) getSession().createSQLQuery(sql).
                addEntity(Attachment.class).setString(0, id).uniqueResult();
        return der;
    }

    /**
     * 根据用户id查询权限信息
     *
     * @param rowNo
     * @return
     */
    public List<Object> findByRowNo(int rowNo) {

        String hql = "select ROLE_ID from SYSTEM_USER_ROLE where row_no=?";
        List<Object> list = getSession().createSQLQuery(hql).setInteger(0, rowNo).list();
        return list;
    }

    public List<Map<String, Object>> selectListAtt(String orderId, String link) {
        String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from oms_sell_order  o  "
                + " left join  SingleAndAttachment oa  on o.id=OA.orderID " + " LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.id=? and oa.link=? ORDER BY ah.uploadDate ";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, orderId).setString(1, link).list();
    }

    public TimeConfig getTimeConfig(String logo) {
        String sql = "SELECT * FROM TIME_CONFIG WHERE LOGO = ?";
        TimeConfig der = (TimeConfig) getSession().createSQLQuery(sql).
                addEntity(TimeConfig.class).setString(0, logo).uniqueResult();
        return der;
    }

    public List<Map<String, String>> getContractInfoId() {
        String sql = "select c.id from OMS_SELL_ORDER s inner JOIN CONTRACTINFO c on c.ID=s.CONTRACT_ID where c.contractType='PRC' AND c.PDFATTURL like '%/EOMAPP/%' GROUP BY c.id";
        return (List<Map<String, String>>) getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public ContractInfo getContractInfoId(String id) {
        String sql = "select * from CONTRACTINFO where id=?";
        return (ContractInfo) getSession().createSQLQuery(sql).addEntity(ContractInfo.class).setString(0, id).uniqueResult();
    }

    public List<Map<String, Object>> getOmsOrderProductListByOrderNo(String orderNo) {
        String sql = "SELECT STATE,SCORE,SERIAL_NUMBER,ORDER_NO,LABEL_NO,PRC_NAME,PROD_NO,PRC_NO,HANDLE_DATE,TEMLATES FROM OMS_ORDER_PRODUCT WHERE ORDER_NO= ? ";
        return (List<Map<String, Object>>) getSession().createSQLQuery(sql).setString(0, orderNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    public PmsProdPriceInfo savePmsProdPriceInfo(PmsProdPriceInfo info) {
        try {
            Assert.notNull(info); //判断是否为空
            Object merge = this.getSession().merge(info);
            info = (PmsProdPriceInfo) merge;
            return info;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }
    public OmsSellOrder getOmsSellOrder(String orderNo,String grpOrdId) {
        String sql = "select * from OMS_SELL_ORDER where ORDER_NO=? and GRPORD_ID=?";
        return (OmsSellOrder)getSession().createSQLQuery(sql).addEntity(OmsSellOrder.class).setString(0,orderNo).setString(1,grpOrdId).uniqueResult();
    }
    /**
     * 根据fid获取评分评价
     *
     * @param fid
     * @return
     */
    public Map<String, Object> findByFid(String fid) {
        String sql = "select * from OMS_SCORE where FID=?";
        List<Map<String, Object>> lis = this.getSession().createSQLQuery(sql).setParameter(0, fid).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return lis.size() > 0 ? (Map<String, Object>) lis.get(0) : null;
    }

    /**
     * 保存评分评价
     *
     * @param os
     * @return
     */
    public OmsScore saveScore(OmsScore os) {
        try {
            Assert.notNull(os); //判断是否为空
            Object merge = this.getSession().merge(os);
            os = (OmsScore) merge;
            return os;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    /**
     * 根据id 获取 OmsSellOrder 信息
     *
     * @param fid
     * @return
     */
    public Map<String, Object> getOmsSellOrderById2(String fid) {
        String sql = "select * from OMS_SELL_ORDER where ID=?";
        List<Map<String, Object>> lis = this.getSession().createSQLQuery(sql).setParameter(0, fid).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        return lis.size() > 0 ? (Map<String, Object>) lis.get(0) : null;
    }

    /**
     * 修改工单状态为归档  
     */
    public void updateOmsSellOrderState(String fid) {
        String sql = "update OMS_SELL_ORDER set STATE = 4 where ID=?";
        getSession().createSQLQuery(sql).setParameter(0, fid).executeUpdate();
    }

    /**
     * 超时工单自动归档自动评分评价
     * @return
     */
    public List<Map<String, Object>> get5DaysOData() {
        String sql = "SELECT * FROM OMS_SELL_ORDER WHERE trunc( to_date( to_char( COMPLETEDATE, 'yyyy-MM-dd' ), 'yyyy-mm-dd' ) ) = trunc( SYSDATE - 5 )";
        List list = this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
        //System.out.println("list = " + list);
        return list;
    }

    public List<SingleAndAttachment> getSingleAndAttachment(String orderId2) {
        // TODO Auto-generated method stub
        String sql = "from SingleAndAttachment s where s.orderID=?";
        List<SingleAndAttachment> or = getSession().createQuery(sql).setString(0, orderId2).list();
        return or;
    }

    /**
     * @author: liyang
     * @date: 2021/9/1 10:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询待处理的子环节(子环节待办)
     */
    public PageResponse getOmsSellOrderPageBylink(PageRequest page, String userId, String groupCode, String isUrgent, String orderSort) {
        //String sql = "SELECT * FROM OMS_SELL_ORDER WHERE (OPERATE_NO='" + user.getRowNo() + "' or CREATE_NO='" + user.getRowNo() + "') ";
        String sql = "SELECT " +
                "OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID,OMS.ORDER_NO,OMS.DEMAND_TYPE,OMS.IS_URGENT,OMS.IS_SUPPORT," +
                "OML.LINK_NAME,OMD.CREATOR_DATE,OMD.ID AS DIALOGUELINKID " +
                "FROM OMS_SELL_ORDER OMS LEFT JOIN OMS_ORDER_LINK OML ON OML.ORDER_NUMBER = OMS.ORDER_NO " +
                "LEFT JOIN OMS_LINK_DIALOGUE OMD ON OMD.LINK_ORDER_NO=OML.LINK_ORDER_NO " +
                "WHERE OMD.OPER_NO='"+userId+"' AND OMD.STATUS='0' and OMS.VERSION_NUMBER='2' ";
        if (isUrgent != null && isUrgent.length() > 0) {
            sql += " AND oms.IS_URGENT='"+isUrgent+"' ";
        }
        if (groupCode != null && groupCode.length() > 0) {
            sql += " AND oms.UNIT_ID LIKE '%" + groupCode + "%' ";
        }
        sql+="GROUP BY OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID,OMS.ORDER_NO,OMS.DEMAND_TYPE,OMS.IS_URGENT,OMS.IS_SUPPORT," +
                "OML.LINK_NAME,OMD.CREATOR_DATE,OMD.ID ";
        if (orderSort != null && orderSort.length() > 0) {
            if("1".equals(orderSort)){
                sql += "ORDER BY OMD.CREATOR_DATE ASC ";
            }else{
                sql += "ORDER BY OMD.CREATOR_DATE DESC ";
            }
        }else{
            sql += "ORDER BY OMD.CREATOR_DATE DESC ";
        }
        return getMapNoBy(sql, page);
    }

    /**
     * @author: liyang
     * @date: 2021/9/1 10:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询我创建的，我经手的需求单
     */
    public PageResponse getMyDemandList(PageRequest page, String userId, String groupCode, String isUrgent,
                                        String tabDivide,String orderLink,String rangeTime) {
        String sql ="SELECT " +
                "OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID,OMS.ORDER_NO,OMS.DEMAND_TYPE," +
                "OMS.IS_URGENT,OMS.IS_SUPPORT,OML.LINK_NAME "+
                "FROM OMS_SELL_ORDER OMS LEFT JOIN OMS_ORDER_LINK OML ON OML.LINK_ORDER_NO=OMS.LINK_ORDER_NO ";
        if("1".equals(tabDivide)){//客户经理
            sql+="WHERE OMS.CREATE_NO='"+userId+"'  and OMS.VERSION_NUMBER='2' ";
        }else{//订单经理
            sql+="WHERE OMS.OPERATE_NO='"+userId+"'  and OMS.VERSION_NUMBER='2' ";
        }
        if (orderLink != null && orderLink.length() > 0) {
            sql+="AND OML.LINK_CODE='"+orderLink+"' ";
        }
        if(rangeTime != null && rangeTime.length() > 0){
            sql+="AND TO_DATE(TO_CHAR(OMS.CREATE_DATE,'YYYY-MM-DD'),'YYYY-MM-DD') >=TO_DATE('"+rangeTime+"','YYYY-MM-DD') ";
        }
        if (isUrgent != null && isUrgent.length() > 0) {
            sql += " AND OMS.IS_URGENT='"+isUrgent+"' ";
        }
        if (groupCode != null && groupCode.length() > 0) {
            sql += " AND OMS.UNIT_ID LIKE '%" + groupCode + "%' ";
        }
        sql+="GROUP BY OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID,OMS.ORDER_NO,OMS.DEMAND_TYPE," +
                "OMS.IS_URGENT,OMS.IS_SUPPORT,OML.LINK_NAME ORDER BY OMS.CREATE_DATE DESC";
        return getMapNoBy(sql, page);
    }

    /**
     * @author: liyang
     * @date: 2021/9/1 10:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询我的审批待审批列表
     */
    public PageResponse getOderMyApproval(PageRequest page, String userId, String userName, String isUrgent,String rangeTime) {
        String sql ="SELECT * FROM (SELECT OS.IS_URGENT,OS.IS_SUPPORT,OS.ID,OS.TITLE,OS.ORDER_NO,OO.TITLE AS WORKBENCHTITLE," +
                "B.STATUS,B.BAK1 AS REJECT, BK.ID AS TASKID,BK.CREATOR_NO,BK.CREATOR_NAME,BK.OPER_NAME,BK.OPER_NO,BK.GMT_CREATE,BK.TYPE, " +
                "OS.CREATE_DATE, " +
                "OS.DEMAND_TYPE," +
                "OO.ID AS WORKBENCHID  " +
                "FROM OMS_SELL_ORDER OS " +
                "LEFT JOIN OMS_ORDER_WORKBENCH OO ON OO.ORDER_NO=OS.ORDER_NO "+
                "LEFT JOIN BPMS_RISKOFF_PROCESS B ON B.BIZ_ID=OO.ID "+
                "LEFT JOIN BPMS_RISKOFF_TASK BK ON BK.PROCESS_ID=B.PROCESS_SIGN " +
                "WHERE BK.STATUS=1 AND BK.OPER_NO='" + userId + "'  and OS.VERSION_NUMBER='2' ";
        if(rangeTime != null && rangeTime.length() > 0){
            sql+="AND TO_DATE(TO_CHAR(OS.CREATE_DATE,'YYYY-MM-DD'),'YYYY-MM-DD')>=TO_DATE('"+rangeTime+"','YYYY-MM-DD') ";
        }
        if (isUrgent != null && isUrgent.length() > 0) {
            sql += "AND OS.IS_URGENT='"+isUrgent+"' ";
        }
        if (userName != null && userName.length() > 0) {
            sql += "AND OO.OPERATE_NAME LIKE '%" + userName + "%' ";
        }
        sql += "GROUP BY OS.IS_URGENT,OS.IS_SUPPORT,OS.ID,BK.ID,BK.CREATOR_NO,BK.CREATOR_NAME,BK.GMT_CREATE,BK.OPER_NAME,BK.OPER_NO," +
                "BK.GMT_CREATE,BK.TYPE,B.STATUS,B.BAK1,OS.TITLE,OS.ORDER_NO,OO.TITLE,OS.CREATE_DATE,OS.DEMAND_TYPE,OO.ID ORDER BY BK.GMT_CREATE DESC)";
        return getMapNoBy(sql, page);
    }

    /**
     * @author: liyang
     * @date: 2021/9/1 10:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询我的审批(我审批的，抄送我的)
     */
    public PageResponse getOderMyApprovalShOrCs(PageRequest page, String userId, String userName, String state,String orderState,
                                                String tabDivide,String rangeTime) {
        String sql="";
        if("1".equals(tabDivide)){
            sql +="SELECT * FROM (SELECT OO.TITLE as WORKBENCHTITLE,OS.ID,OS.TITLE,OS.CREATE_DATE," +
                    "OS.UNIT_NAME,OS.UNIT_ID,OS.ORDER_NO,OS.DEMAND_TYPE,OS.IS_URGENT,OS.IS_SUPPORT, " +
                    "OO.STATE AS WORKBENCHSTATE," +
                    "OS.STATE AS ORDERSTATE," +
                    "OO.CREATE_DATE AS WORKBENCHCREATEDATE ," +
                    "OO.ID AS WORKBENCHID  " +
                    "FROM OMS_SELL_ORDER OS " +
                    "LEFT JOIN OMS_ORDER_WORKBENCH OO ON OO.ORDER_NO=OS.ORDER_NO "+
                    "LEFT JOIN BPMS_RISKOFF_PROCESS B ON B.BIZ_ID=OO.ID "+
                    "LEFT JOIN BPMS_RISKOFF_TASK BK ON BK.PROCESS_ID=B.PROCESS_SIGN " +
                    "WHERE BK.OPER_NO='" + userId + "' AND BK.TYPE='SH' and OS.VERSION_NUMBER='2' ";
        }else if("2".equals(tabDivide)){
            sql +="SELECT * FROM (SELECT OO.TITLE as WORKBENCHTITLE,OS.ID,OS.TITLE,OS.CREATE_DATE," +
                    "OS.UNIT_NAME,OS.UNIT_ID,OS.ORDER_NO,OS.DEMAND_TYPE,OS.IS_URGENT,OS.IS_SUPPORT, "+
                    "OO.STATE AS WORKBENCHSTATE," +
                    "OS.STATE AS ORDERSTATE," +
                    "OO.CREATE_DATE AS WORKBENCHCREATEDATE ," +
                    "OO.ID AS WORKBENCHID  " +
                    "FROM OMS_SELL_ORDER OS " +
                    "LEFT JOIN OMS_ORDER_WORKBENCH OO ON OO.ORDER_NO=OS.ORDER_NO "+
                    "LEFT JOIN BPMS_RISKOFF_PROCESS B ON B.BIZ_ID=OO.ID "+
                    "LEFT JOIN BPMS_RISKOFF_TASK BK ON BK.PROCESS_ID=B.PROCESS_SIGN " +
                    "WHERE BK.OPER_NO='" + userId + "' AND BK.TYPE='CS' and OS.VERSION_NUMBER='2' ";
        }
        if(rangeTime != null && rangeTime.length() > 0){
            sql+="AND TO_DATE(TO_CHAR(OS.CREATE_DATE,'YYYY-MM-DD'),'YYYY-MM-DD') >=TO_DATE('"+rangeTime+"','YYYY-MM-DD') ";
        }
        if (state != null && state.length() > 0) {
            sql += "AND OO.STATE='"+state+"' ";
        }
        if (userName != null && userName.length() > 0) {
            sql += "AND OO.OPERATE_NAME LIKE '%" + userName + "%' ";
        }
        if(orderState != null && orderState.length() > 0){
            sql += "AND OS.STATE='"+orderState+"' ";
        }
        sql += "GROUP BY OO.TITLE,OS.ID,OS.TITLE,OS.CREATE_DATE,OS.UNIT_NAME,OS.UNIT_ID,OS.ORDER_NO,OS.DEMAND_TYPE,OS.IS_URGENT,OS.IS_SUPPORT," +
                "OO.STATE,OS.STATE,OO.CREATE_DATE,OO.ID " +
               "ORDER BY OS.CREATE_DATE DESC)";
        return getMapNoBy(sql, page);
    }

    public List<Map<String,Object>> getOmsProductApproveList(String[] prodId){
        String sql="SELECT APPROVE_NAME FROM OMS_PRODUCT_APPROVE where PROD_ID in(:prodId) and state=0 " +
                "group by APPROVE_NAME";
        Query query = getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP);
        query.setParameterList("prodId",prodId);
        List<Map<String,Object>> or=query.list();
        return or;
    }

    public OmsOrderProduct getOmsOrderProduct(String orderNo,String grpOrdId) {
        String sql = "select * from OMS_ORDER_PRODUCT where SERIAL_NUMBER=? and GRPORDID=?";
        return (OmsOrderProduct)getSession().createSQLQuery(sql).addEntity(OmsOrderProduct.class).setString(0,orderNo).setString(1,grpOrdId).uniqueResult();
    }

    public List<OmsOrderLink> getOmsOrderLinkByCode(String code,String name,String orderNo) {
        String sql = "select * from OMS_ORDER_LINK where link_Code = ? and order_Number=? and link_Name=?";
        return getSession().createSQLQuery(sql).addEntity(OmsOrderLink.class).setString(0,code).setString(1,orderNo).setString(2,name).list();
    }

    public List<Map<String,Object>> getOmsSellOrderByList() {
        String sql = "SELECT OMS.ORDER_NO,OMS.TITLE,LINK.OPER_NO,LINK.LINK_NAME FROM OMS_SELL_ORDER OMS " +
                "LEFT JOIN OMS_ORDER_LINK LINK ON OMS.LINK_ORDER_NO=LINK.LINK_ORDER_NO " +
                "WHERE TO_DATE(TO_CHAR(LINK.PRETREATMENT_DATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS') " +
                "<TO_DATE(TO_CHAR(SYSDATE,'YYYY-MM-DD HH24:MI:SS'),'YYYY-MM-DD HH24:MI:SS')) " +
                "AND OMS.STATE='0' " +
                "AND OMS.VERSION_NUMBER='2' " +
                "AND LINK.STATUS='0' " +
                "AND OMS.LINK_ORDER_NO IS NOT NULL";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }
    /**
     * @Description: 根据地市和环节编号查询超时时间
     * @Param: [company_no:地市编号, linkCode:环节编号]
     * @return: com.xinxinsoft.entity.oms.OmsPretreatmentDate
     * @Author: TX
     * @Date: 2021/10/8 13:51
     */
    public OmsPretreatmentDate QueryOmsPretreatmentDate(String company_no,String linkCode){
        String sql = "SELECT * from OMS_PRETREATMENT_DATE WHERE COMPANY_NO = ? AND LINK_CODE=?";
        return (OmsPretreatmentDate)getSession().createSQLQuery(sql).addEntity(OmsPretreatmentDate.class).setString(0,company_no).setString(1,linkCode).uniqueResult();
    }
    /**
      * @Description: 获取未完成的工单  并且有未完成的环节已经超时
      * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
      * @Author: TX
      * @Date: 2021/10/12 14:01
    */
    public List<Map<String,Object>> QueryOrderByLinkStatus(String linkOrderNo){
        SimpleDateFormat sdf = new SimpleDateFormat(" yyyy-MM-dd HH:mm:ss");
        String sql = "select o.TITLE,l.CREATOR_NAME,l.CREATOR_DATE,l.OPER_NAME,l.OPER_NO,l.PRETREATMENT_DATE from OMS_SELL_ORDER o LEFT JOIN OMS_ORDER_LINK l ON o.LINK_ORDER_NO = l.LINK_ORDER_NO " +
                "WHERE o.STATE = '0' AND l.STATUS = '0' AND l.PRETREATMENT_DATE <= to_date(?,'yyyy-mm-dd hh24:mi:ss')";
        if (!linkOrderNo.equals("")){
            sql += "AND l.LINK_ORDER_NO = ? ";
            return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0,sdf.format(new Date())).setString(1,linkOrderNo).list();
        }
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0,sdf.format(new Date())).list();
    }

    public void saveddyjPush_0_0001(String namevalue, String datevalue,String titlevalue, String phoneNumber) throws Exception {
        String str = "{\"createName\":\"namevalue\",\"createDate\":\"datevalue\",\"title\":\"titlevalue\"}";
        if (namevalue != null) {
            str = str.replace("namevalue", namevalue);
        }
        if (datevalue != null) {
            str = str.replace("datevalue", datevalue);
        }
        if (titlevalue!=null){
            str = str.replace("titlevalue", titlevalue);
        }
        logger.info("用户手机号:"+phoneNumber+"短信通知json:"+str);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date d = new Date();
        String dateStr = formatter.format(d);
        Push_0_0001 p = new Push_0_0001();
        p.setParameter(str);
        p.setPhone_No(phoneNumber);
        p.setSeq("00011611251615030001");
        p.setTemplateID("20407791");
        p.setInsert_Time(formatter.parse(dateStr));
        p.setSend_Flag("0");
        p.setOp_Code("add");
        p.setLogin_No("12345");
        this.getSession().save(p);

    }

    /**
     * @author: liyang
     * @date: 2021/9/1 10:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询待处理的子环节(子环节待办)
     */
    public LayuiPage getOmsSellOrderPageBylinkPC(LayuiPage page, SystemUser user, String orderNo, String title, String unitId,
                                                 String unitName, String startTime, String endTime,String prcNo) {
        String sql = "SELECT OMD.ID AS DIALOGUELINKID,OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID," +
                "OMS.ORDER_NO,OMS.DEMAND_TYPE,OMS.IS_URGENT,OMS.IS_SUPPORT,OML.LINK_NAME,OMD.CREATOR_DATE," +
                "OMS.IS_UNIT,OMS.STATE,OMS.LINK_ORDER_NO,OMS.CREATE_NAME,OMS.OPERATE_NAME,OMS.CUSTOMER_NAME," +
                "OMS.CUSTOMER_PHONE,OMS.COMPLETE_DATE,OMS.VERSION_NUMBER,OMS.COMPANY_NAME,OMS.COUNTY_NAME," +
                "LISTAGG(OP.PRC_NAME, ',') WITHIN GROUP (ORDER BY OP.PRC_NAME) AS PRCNAME " +
                "FROM OMS_SELL_ORDER OMS LEFT JOIN OMS_ORDER_PRODUCT OP ON OP.ORDER_NO=OMS.ORDER_NO " +
                "LEFT JOIN OMS_ORDER_LINK OML ON OML.ORDER_NUMBER = OMS.ORDER_NO " +
                "LEFT JOIN OMS_LINK_DIALOGUE OMD ON OMD.LINK_ORDER_NO=OML.LINK_ORDER_NO WHERE OMD.OPER_NO='"+user.getRowNo()+"' AND OMD.STATUS='0' and OMS.VERSION_NUMBER='3' ";
        if (orderNo != null && orderNo.length() > 0) {
            sql+=" AND OMS.ORDER_NO LIKE '%" + orderNo + "%'";
        }
        if (title != null && title.length() > 0) {
            sql+=" AND OMS.TITLE LIKE '%" + title + "%'";
        }
        if (unitId != null && unitId.length() > 0) {
            sql+=" AND OMS.UNIT_ID LIKE '%" + unitId + "%'";
        }
        if (unitName != null && unitName.length() > 0) {
            sql+=" AND OMS.UNIT_NAME LIKE '%" + unitName + "%'";
        }
        if (startTime != null && startTime.length() > 0) {
            sql+=" AND to_char(OMS.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') >= '" + startTime + " 00:00:00'";
        }
        if (endTime != null && endTime.length() > 0) {
            sql+=" AND to_char(OMS.CREATE_DATE,'YYYY-MM-DD hh24:mi:ss') <= '" + endTime + " 23:59:59'";
        }
        if (prcNo != null && prcNo.length() > 0) {
            sql+=" AND OP.PRC_NO='"+prcNo+"'";
        }
        sql+=" GROUP BY OMD.ID,OMS.ID,OMS.TITLE,OMS.CREATE_DATE,OMS.UNIT_NAME,OMS.UNIT_ID,OMS.ORDER_NO,OMS.DEMAND_TYPE,OMS.IS_URGENT,OMS.IS_SUPPORT," +
                "OML.LINK_NAME,OMD.CREATOR_DATE,OMS.IS_UNIT,OMS.STATE,OMS.LINK_ORDER_NO,OMS.CREATE_NAME,OMS.OPERATE_NAME,OMS.CUSTOMER_NAME," +
                "OMS.CUSTOMER_PHONE,OMS.COMPLETE_DATE,OMS.VERSION_NUMBER,OMS.COMPANY_NAME,OMS.COUNTY_NAME ORDER BY OMD.CREATOR_DATE DESC";
                page.setCount(getCount("SELECT COUNT(0) FROM (" + sql+ ")"));
        if (page.getCount() > 0) {
            page.setData(getPageList(sql, null, page));
        }
        return page;
    }

    /**
     * 保存 OmsOrderItem
     *
     * @return
     */
    public ContractInfo updateContractInfo(ContractInfo con) {
        try {
            Assert.notNull(con); //判断是否为空
            Object merge = this.getSession().merge(con);
            con = (ContractInfo) merge;
            return con;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public List<Map<String,String>> getOmsLinkDiaLogue(String orderNo){
        String sql = "select linkd.*,link.link_name from OMS_LINK_DIALOGUE linkd left join OMS_ORDER_LINK link on link.LINK_ORDER_NO=linkd.LINK_ORDER_NO " +
                "where link.ORDER_NUMBER=? and linkd.MESSAGE is not null order by linkd.CREATOR_DATE desc ";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0,orderNo).list();
    }

    public List<Map<String,String>> getOmsLinkDiaLogueIf(String orderNo){
        String sql = "select linkd.*,link.link_name from OMS_LINK_DIALOGUE linkd left join OMS_ORDER_LINK link on link.LINK_ORDER_NO=linkd.LINK_ORDER_NO " +
                "where link.ORDER_NUMBER=? and linkd.STATUS='0' " +
                "and linkd.OPER_ROLE='ROLE_CUMR' and link.LINK_NAME='工单确认' " +
                "and linkd.MESSAGE is not null order by linkd.CREATOR_DATE desc ";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0,orderNo).list();
    }

    public List<Map<String,String>> getVwUserinfoByRowno(String rowno) {
        String sql = "SELECT * from VW_USERINFO WHERE ROWNO =? AND ISMAINDPT = 'true'";
        return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0,rowno).list();
    }

    public Integer deleteOmsOrderLink(String id) {
        String sql = "delete from OMS_ORDER_LINK where id=?";
        return getSession().createSQLQuery(sql).setString(0,id).executeUpdate();
    }

    public OmsLinkDialogue getOmsLinkDialogueById(String id) {
        String sql = "select * from Oms_Link_Dialogue where id = ? and status=0";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsLinkDialogue.class);
        sqlQuery.setParameter(0, id);
        List<OmsLinkDialogue> list = sqlQuery.list();
        if (null != list && !list.isEmpty()) {
            return list.get(0);
        }
        return null;
    }


    public List<OmsLinkDialogue> getOmsLinkDialogueList(String id) {
        String sql = "select * from Oms_Link_Dialogue where link_Order_No = ? and status=0";
        SQLQuery sqlQuery = this.getSession().createSQLQuery(sql);
        sqlQuery.addEntity(OmsLinkDialogue.class);
        sqlQuery.setParameter(0, id);
        List<OmsLinkDialogue> list = sqlQuery.list();
        return list;
    }

    public OmsOrderProduct getOmsOrderProductByGrpOrdId(String grpOrdId) {
        String sql = "select * from OMS_ORDER_PRODUCT where GRPORDID=?";
        return (OmsOrderProduct)getSession().createSQLQuery(sql).addEntity(OmsOrderProduct.class).setString(0,grpOrdId).uniqueResult();
    }

    public List<OmsSellOrder> queryOmsSellOrderList(){
        String sql = "select * from Oms_Sell_Order where version_Number='2' and state='0'";
        return this.getSession().createSQLQuery(sql).addEntity(OmsSellOrder.class).list();
    }

    public List<OmsSellOrder> queryOmsSellOrderListTwo(){
        String sql = "select oms.* from Oms_Sell_Order oms left join oms_order_link link on link.Link_Order_No=oms.Link_Order_No where oms.version_Number='2' and oms.state='0' and link.link_code='6' and link.status=1";
        return this.getSession().createSQLQuery(sql).addEntity(OmsSellOrder.class).list();
    }

    public List<OmsSellOrder> queryOmsSellOrderListThree(){
        String sql = "select * from Oms_Sell_Order  where Link_Order_No not in( select Link_Order_No from  oms_order_link)";
        return this.getSession().createSQLQuery(sql).addEntity(OmsSellOrder.class).list();
    }

    /**
     * 查询子产品
     *
     * @return
     */
    public List<Map<String, String>> getPmsPriceinfo(String identification) {
        String sql = "select lower(sys_guid()) id,pt.NAME name " +
                "from PMS_PRICE_INFO pt ";
        if("1".equals(identification)){
            sql+="inner join PRODUCT_KEY_INFORMATION pki on pt.PROD_ID=pki.PRODUCT_CODE ";
        }
        sql+="GROUP BY pt.NAME";
        return this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

    /**
     * 查询资费
     *
     * @return
     */
    public List<Map<String, String>> getProdPmsPriceinfo(String code,String identification) {
        String sql = "select P.PRC_ID id,P.PRC_NAME name,P.PROD_ID parentId " +
                "from PMS_PRICE_INFO P ";
        if("1".equals(identification)){
            sql+="inner join  PRICE_KEY_INFORMATION pki on P.PRC_ID=pki.PRICE_CODE ";
        }
        sql+=" WHERE P.NAME='" + code + "'";
        logger.info("sql="+sql);
        return this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
    }

}
