package com.xinxinsoft.service.claimForFunds;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import com.xinxinsoft.entity.PreinvApply.InternetOfThingsDet;
import com.xinxinsoft.entity.PreinvApply.ValuableCardDet;
import com.xinxinsoft.entity.claimForFunds.*;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.smsPush.Push_0_0001;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.utils.common.FileUtil;
import jxl.CellView;
import jxl.Workbook;
import jxl.format.UnderlineStyle;
import jxl.write.*;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.http.client.methods.HttpPost;
import org.apache.struts2.ServletActionContext;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.Transaction;
import org.hibernate.transform.Transformers;

import com.xinxinsoft.entity.PreinvApply.PreinvApplyDet;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemCompany;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import org.springframework.util.Assert;

import javax.servlet.http.HttpServletResponse;

public class ClaimForFundsService extends BaseService{
	//private static final String ESB_URL="http://**************:51000/esbWS/rest/com_sitech_Government_atom_inter_IGovernmentCapitalSvc_";
	private static final String ESB_URL="http://*************:51000/esbWS/rest/com_sitech_Government_atom_inter_IGovernmentCapitalSvc_";

	private final Map<String,String> moneyTotalIdKeyMap = new ConcurrentHashMap<>();

	/**
	 * @Description TODO 资金id内存锁(防止重复操作)
	 * <AUTHOR>
	 * @param moneyTotalId	资金id
	 * @Date 2023/5/5 9:39
	 **/
	synchronized public Map<String, Object> setQuerymoneyTotalIdKey(String moneyTotalId) {
		Map<String, Object> mapJson = new HashMap<>();
		try{
			//获取当前时间戳
			String dateStr = getStringDatethree(new Date()).substring(0,12);
			//加锁，判断是否已经被锁定
			if (moneyTotalIdKeyMap.containsKey(moneyTotalId)){
				String keyDate = moneyTotalIdKeyMap.get(moneyTotalId);
				if (Double.parseDouble(dateStr)-Double.parseDouble(keyDate) > 3){
					moneyTotalIdKeyMap.put(moneyTotalId,dateStr);
				}else {
					mapJson.put("code",-1);
					mapJson.put("data",keyDate);
					mapJson.put("msg","亲爱的同事，您当前操作的资金系统正在处理中，请返回列表重新进入查看，如未成功，请3分钟后重新尝试!");
					return mapJson;
				}
			}else {
				moneyTotalIdKeyMap.put(moneyTotalId,dateStr);
			}

			mapJson.put("code",1);
			mapJson.put("data","");
			mapJson.put("msg","操作成功！");
			return mapJson;
		}catch (Exception e){
			e.printStackTrace();
			mapJson.put("code",-1);
			mapJson.put("data",e.getMessage());
			mapJson.put("msg","亲爱的同事，您当前操作的资金系统正在处理中，请返回列表重新进入查看，如未成功，请5分钟后重新尝试!");
			return mapJson;
		}
	}

	public void delQuerymoneyTotalIdKey(String moneyTotalId){
		try{
			moneyTotalIdKeyMap.remove(moneyTotalId);

			//解除锁定时间超过10分钟的资金（未正常解除，可能存在风险）
			Iterator<String> iter = moneyTotalIdKeyMap.keySet().iterator();
			String dateStr = getStringDatethree(new Date()).substring(0,12);
			while (iter.hasNext()){
				if (Double.parseDouble(dateStr)-Double.parseDouble(moneyTotalIdKeyMap.get(iter.next())) > 3){
					moneyTotalIdKeyMap.remove(iter.next());
				}
			}
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	public Map<String, Object> querymoneyTotalIdKey() {
		Map<String, Object> mapJson = new HashMap<>();
		mapJson.put("code",1);
		mapJson.put("data",moneyTotalIdKeyMap);
		mapJson.put("msg","当前保存资金条数为："+moneyTotalIdKeyMap.size());
		return mapJson;
	}





	/**
	 * 查询所有未被认领的资金
	 */
	public LayuiPage getClaimForFunds(String otherAccNumber,String otherName,  LayuiPage page,String companyCode,SystemUser user,String serialNo,String endCreatorDate,String stateCreatorDate) {
		// TODO Auto-generated method stub
		String sql = " SELECT " +
				" * " +
				" FROM " +
				" ( " +
				" ( " +
				" SELECT " +
				" * " +
				" FROM " +
				" MoneyTotal " +
				" WHERE " +
				" userid IS NULL " +
				" AND state = '0' " +
				" AND ID NOT IN ( " +
				" SELECT " +
				" moneyTotal_id " +
				" FROM " +
				" MoneyTotal_Delete_Record " +
				" WHERE " +
				" USER_ID = ' "+user.getRowNo()+" ' " +
				" ) " +
				" ) " +
				" UNION ALL " +
				" ( " +
				" SELECT DISTINCT " +
				" T .* " +
				" FROM " +
				" MONEYTOTAL T " +
				" INNER JOIN TAXPAYER x ON T .batchno = x.batch_no " +
				" WHERE " +
				" T .STATE = '6' AND x .STATE = '0'" +
				" ) " +
				" ) ";
		if (companyCode.equals("00")){
			sql +=" WHERE companyCode in ('00','01')";
		}else {
			sql +=" WHERE companyCode='"+companyCode+"' ";
		}
		if (otherAccNumber != null && !"".equals(otherAccNumber)) {
			sql += " and ( otherAccNumber LIKE '"+otherAccNumber+"%' or otherAccNumber = '"+otherAccNumber+"') ";
		}
		if (serialNo != null && !"".equals(serialNo)) {
			sql += " and ( serialno LIKE '"+serialNo+"%' or serialno = '"+serialNo+"') ";
		}
		if (otherName != null && !"".equals(otherName)) {
			sql += " and OTHERNAME LIKE '%"+otherName+"%' ";
		}
		if (stateCreatorDate != null && !"".equals(stateCreatorDate)) {
			sql += " AND TRANDATE >= TO_DATE('" + stateCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		if (endCreatorDate != null && !"".equals(endCreatorDate)) {
			sql += " AND TRANDATE <= TO_DATE('" + endCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}

		sql+=" ORDER BY STATE DESC, CREATEDATE DESC";
		page.setCount(getCount("select count(0) from (" + sql + ")"));
		if (page.getCount() > 0) {
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}

	/**
	 * @Description TODO 添加自动认领记录
	 * <AUTHOR>
	 * @param taxpayer 认领记录
	 * @return com.xinxinsoft.entity.taxpayer.Taxpayer
	 * @Date 2022/8/9 10:55
	 **/
	public Taxpayer saveTaxpayer(Taxpayer taxpayer) {
		try {
			Session session = this.getSession();
			if (taxpayer != null) {
				session.save(taxpayer);
				session.flush();
			}
			return taxpayer;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * @Description TODO 添加自动认领记录
	 * <AUTHOR>
	 * @param taxpayer 认领记录
	 * @return com.xinxinsoft.entity.taxpayer.Taxpayer
	 * @Date 2022/8/9 10:55
	 **/
	public Taxpayer saveTaxpayerList(Taxpayer taxpayer) {
		try {
			Session session = this.getSession();
			if (taxpayer != null) {
				session.save(taxpayer);
				session.flush();
			}
			return taxpayer;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * 新增资金暂收款数据
	 */
	public MoneytotalProvisional saveProvisional(MoneytotalProvisional provisional) {
		Session session = this.getSession();
		try {
			if (provisional != null) {
				session.save(provisional);
				session.flush();
			}
			return provisional;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * 新增资金认领池数据
	 */
	public MoneyTotal saveProcessList(MoneyTotal moneyTotal) {
		Session session = this.getSession();
		try {
			if (moneyTotal != null) {
				session.saveOrUpdate(moneyTotal);
				session.flush();
			}
			return moneyTotal;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * @Description TODO 添加资金临时数据
	 * <AUTHOR>
	 * @param temporary	对象
	 * @return com.xinxinsoft.entity.claimForFunds.Moneytotal_Temporary
	 * @Date 2022/9/2 11:54
	 **/
	public Moneytotal_Temporary saveMoneytotalTemporary(Moneytotal_Temporary temporary) {
		Session session = this.getSession();
		try {
			if (temporary != null) {
				session.save(temporary);
				session.flush();
			}
			return temporary;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * @Description :修改资金临时数据
	 * <AUTHOR>
	 * @param temporary: 对象
	 * @return: com.xinxinsoft.entity.claimForFunds.InvoiceMiddle
	 * @Date 2022/3/15 16:08
	 */
	public Moneytotal_Temporary updateMoneytotalTemporary(Moneytotal_Temporary temporary) {
		// TODO Auto-generated method stub
		try {
			if (temporary != null) {
				Session session = this.getSession();
				session.update(temporary);
			}
			return temporary;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * @Description TODO 保存或修改资金临时数据
	 * <AUTHOR>
	 * @param temporary 资金临时数据
	 * @return com.xinxinsoft.entity.claimForFunds.Moneytotal_Temporary
	 * @Date 2022/11/7 14:29
	 **/
	public Moneytotal_Temporary updateTemporary(Moneytotal_Temporary temporary){
		try {
			Assert.notNull(temporary); //判断是否为空
			Object merge = this.getSession().merge(temporary);
			return (Moneytotal_Temporary) merge;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * @Description TODO 删除资金临时表数据
	 * <AUTHOR>
	 * @param deleteDate 删除时间
	 * @return int
	 * @Date 2022/11/24 14:42
	 **/
	public int deleteTemporary(String deleteDate) {
		String sql = " DELETE MONEYTOTAL_ORARY WHERE INSERTSTATE  = '1' AND CREATEDATE < TO_DATE( ?, 'yyyy-MM-dd') ";
		return getSession().createSQLQuery(sql).setString(0,deleteDate).executeUpdate();
	}

	/**
	 * 新增资金删除记录
	 */
	public MoneyTotal_Delete_Record saveMoneyTotal_Delete_Record(MoneyTotal_Delete_Record deo) {
		Session session = this.getSession();
		try {
			if (deo != null) {
				session.save(deo);
				session.flush();
			}
			return deo;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	public List<MoneyTotal> queryMoneyTotalByUserId(Integer userId){
		String sql = "SELECT * FROM MoneyTotal WHERE USERID ='"+userId+"' and state='1' and OVERAMOUNT > '0' ";
		return getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).list();
	}

	/**
	 * 查询当前登录人已认领的资金
	 */
	public LayuiPage getUserClaimForFunds(String otherName,String otherAccNumber,String groupCode,String groupName, LayuiPage page, SystemUser user,String serialNo,String endCreatorDate,String stateCreatorDate,Integer type) {
		// TODO Auto-generated method stub
		String sql ="SELECT * from MoneyTotal where userid='"+user.getRowNo()+"' and state!='0' ";
		if (type.equals(1)){
			sql += " and OVERAMOUNT > '0' ";
		}else {
			sql += " and OVERAMOUNT = '0' ";
		}
		if (otherAccNumber != null && !"".equals(otherAccNumber)) {
			sql += " and ( OTHERACCNUMBER LIKE '"+otherAccNumber+"%' or OTHERACCNUMBER = '"+otherAccNumber+"') ";
		}
		if (otherName != null && !"".equals(otherName)) {
			sql += " and ( OTHERNAME LIKE '%"+otherName+"%' or OTHERNAME = '"+otherName+"') ";
		}
		if (groupCode != null && !"".equals(groupCode)) {
			sql += " and (GROUPCODE LIKE '"+groupCode+"%' or GROUPCODE = '"+groupCode+"') ";
		}
		if (groupName != null && !"".equals(groupName)) {
			sql += " and (GROUPNAME LIKE '%"+groupName+"%' or GROUPNAME = '"+groupName+"') ";
		}
		if (serialNo != null && !"".equals(serialNo)) {
			sql += " and (serialno LIKE '"+serialNo+"%' or serialno = '"+serialNo+"') ";
		}
		if (stateCreatorDate != null && !"".equals(stateCreatorDate)) {
			sql += " AND TRANDATE >= TO_DATE('" + stateCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		if (endCreatorDate != null && !"".equals(endCreatorDate)) {
			sql += " AND TRANDATE <= TO_DATE('" + endCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		sql+=" order by createDate desc";
		page.setCount(getCount("select count(0) from (" + sql + ")"));
		if (page.getCount() > 0) {
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}

	/**
	 * @Description TODO 根据流水查询资金（无限制查询）
	 * <AUTHOR>
	 * @param page			分页对象
	 * @param serialNo		资金财务流水号
	 * @return com.xinxinsoft.utils.page.LayuiPage
	 * @Date 2022/11/1 14:38
	 **/
	public LayuiPage getComprehensiveQry(LayuiPage page,String serialNo) {
		// TODO Auto-generated method stub
		String sql =" SELECT * FROM MONEYTOTAL WHERE 1 = 1  ";
		if (serialNo != null && !"".equals(serialNo)) {
			sql += " AND SERIALNO = '"+serialNo+"' ";
		}
		sql+=" ORDER BY CREATEDATE DESC ";
		page.setCount(getCount("select count(0) from (" + sql + ")"));
		if (page.getCount() > 0) {
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}

	/**
	 * 已完成资金
	 * @param number
	 * @param page
	 * @param user
	 * @return
	 */
	public PageResponse getCompleteClaimForFunds(String number,
												 PageRequest page, SystemUser user) {
		// TODO Auto-generated method stub
		String sql ="";
		if(number != null && !"".equals(number)){
			sql = "SELECT * from MoneyTotal where otherAccNumber='"+number+"' and state='2' and userid='"+user.getRowNo()+"' and OverAmount='0' order by createDate desc";
		}else{
			sql = "SELECT * from MoneyTotal where userid='"+user.getRowNo()+"' and state='2' and OverAmount='0' order by createDate desc";
		}
		return getMap(sql, page);
	}

	public List<Object[]> getbumen(int userID) {
		String sql = "select " + "case when dn1.DEPARTMENT_LEVEL=1 " + "THEN dn1.DEPARTMENT_NAME " + "else dn2.DEPARTMENT_NAME " + "end," + "as2.COMPANY_NAME," + "as2.COMPANY_IBM "
				+ "from  AFR_SYSTEMDEPT dn1 " + "left join AFR_SYSTEMDEPT dn2 " + "on " + "dn1.DEPARTMENT_PARENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEMDEPT as1 " + "on "
				+ "as1.DEPARTMENT_NO=dn2.DEPARTMENT_NO " + "left join AFR_SYSTEM_DEPT_USER asdu " + "on " + "asdu.DEPARTMENT_NO=dn1.DEPARTMENT_NO " + "left join AFR_SYSTEMCOMPANY as2 " + "on "
				+ "as2.COMPANY_CODE=dn2.COMPANY_CODE " + "where asdu.ROWNO=? AND asdu.ISMAINDPT = 'true' ";
		Query query = getSession().createSQLQuery(sql);
		query.setInteger(0, userID);
		List<Object[]> s = query.list();
		return s;
	}

	/**
	 * 保存附件信息
	 */
	public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
		if (sa.getId() == null) {
			String sql = "select  * from SingleAndAttachment t where t.orderid=? and t.attachmentid=? and t.link=?";
			Object count = getSession().createSQLQuery(sql).setString(0, sa.getOrderID()).setString(1, sa.getAttachmentId()).setString(2, sa.getLink()).uniqueResult();
			if (null == count) {
				Session session = this.getSession();
				session.saveOrUpdate(sa);
				session.flush();
				return sa;
			} else {
				return null;
			}
		} else {
			Session session = this.getSession();
			session.saveOrUpdate(sa);
			session.flush();
			return sa;
		}
	}

	public MoneyApply addMoneyApply(MoneyApply myly) {
		// TODO Auto-generated method stub
		try {
			if (myly != null) {
				Session session = this.getSession();
				session.save(myly);
			}
			return myly;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * @Description 添加资金移交记录表
	 * <AUTHOR>
	 * @param transferOfFunds 资金移交记录信息
	 * @return com.xinxinsoft.entity.claimForFunds.TransferOfFunds
	 * @Date 2023/12/14 15:55
	 **/
	public TransferOfFunds addTransferOfFunds(TransferOfFunds transferOfFunds) {
		// TODO Auto-generated method stub
		try {
			if (transferOfFunds != null) {
				Session session = this.getSession();
				session.save(transferOfFunds);
			}
			return transferOfFunds;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * 查询资金认领池数据
	 */
	public MoneyTotal getMoneyTotal(String id) {
		String sql = "select * from MoneyTotal where id=?";
		return (MoneyTotal)getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).setString(0, id).uniqueResult();
	}

	/**
	 * @Description TODO 查询资金临时表数据
	 * <AUTHOR>
	 * @param id 编号
	 * @return com.xinxinsoft.entity.claimForFunds.Moneytotal_Temporary
	 * @Date 2022/9/2 12:06
	 **/
	public Moneytotal_Temporary getMoneytotalTemporary(String id) {
		String sql = "SELECT * FROM MONEYTOTAL_ORARY WHERE SERIALNO = ?";
		return (Moneytotal_Temporary)getSession().createSQLQuery(sql).addEntity(Moneytotal_Temporary.class).setString(0, id).uniqueResult();
	}

	/**
	 * @Description TODO 根据状态查询资金临时表中未入表的数据
	 * <AUTHOR>
	 * @return com.xinxinsoft.entity.claimForFunds.Moneytotal_Temporary
	 * @Date 2022/9/2 14:50
	 **/
	public List<Moneytotal_Temporary> getMoneytotalTemporaryByState() {
		String sql = "SELECT * from MONEYTOTAL_ORARY WHERE INSERTSTATE = '0' ";
		return getSession().createSQLQuery(sql).addEntity(Moneytotal_Temporary.class).list();
	}

	public MoneyTotal getMoneyTotalSerialNoAndSubGroup(String serialNo,Integer is_sub_group) {
		String sql = "select * from MoneyTotal where serialNo=? and is_sub_group=?";
		return (MoneyTotal)getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).setString(0, serialNo).setParameter(1,is_sub_group).uniqueResult();
	}

	/**
	 * @Description TODO 查询自动认领未确认数据
	 * <AUTHOR>
	 * @param batchNo 资金编号
	 * @param groupCode 集团编号
	 * @return com.xinxinsoft.entity.taxpayer.Taxpayer
	 * @Date 2022/8/15 10:29
	 **/
	public Taxpayer getTaxpayerByBatchNo (String batchNo,String groupCode,Integer userRow) {
		String sql = "SELECT * from TAXPAYER WHERE BATCH_NO = ? AND GROUP_CODE = ? AND STATE = '0' AND USER_ID = ? ";
		return (Taxpayer)getSession().createSQLQuery(sql).addEntity(Taxpayer.class).setString(0, batchNo).setString(1, groupCode).setInteger(2,userRow).uniqueResult();
	}

	/**
	 * @Description TODO 根据用户和资金查询资金预占关系
	 * <AUTHOR>
	 * @param batchNo 资金编号
	 * @param groupCode 集团编号
	 * @return com.xinxinsoft.entity.taxpayer.Taxpayer
	 * @Date 2022/8/15 10:29
	 **/
	public List<Taxpayer> getTaxpayerByUserRow(String batchNo,String userRow) {
		String sql = "SELECT * from TAXPAYER WHERE BATCH_NO = ? AND USER_ID = ? AND STATE = '0' ";
		return getSession().createSQLQuery(sql).addEntity(Taxpayer.class).setString(0, batchNo).setString(1, userRow).list();
	}

	/**
	 * @Description TODO 查询自动认领未确认数据
	 * <AUTHOR>
	 * @param batchNo 资金编号
	 * @return com.xinxinsoft.entity.taxpayer.Taxpayer
	 * @Date 2022/8/15 10:29
	 **/
	public List<Taxpayer> getTaxpayerList(String batchNo) {
		String sql = "SELECT * from TAXPAYER WHERE BATCH_NO = ? AND STATE = '0' ";
		return getSession().createSQLQuery(sql).addEntity(Taxpayer.class).setString(0, batchNo).list();
	}

	/**
	 * @Description TODO 查询自动认领未确认数据
	 * <AUTHOR>
	 * @param batchNo 资金编号
	 * @return com.xinxinsoft.entity.taxpayer.Taxpayer
	 * @Date 2022/8/15 10:29
	 **/
	public List<Map<String, String>> getTaxpayerListTwo(String batchNo,String userId) {
		String sql = "SELECT t.*,v.EMPLOYEE_NAME from TAXPAYER t INNER JOIN VW_USERINFO v ON t.USER_ID = v.ROWNO WHERE v.ISMAINDPT = 'true' AND t.BATCH_NO = ? AND t.USER_ID = ? AND t.STATE = '0' ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, batchNo).setString(1,userId).list();
	}

	/**
	 * @Description TODO 查询自动认领未确认数据
	 * <AUTHOR>
	 * @param batchNo 资金编号
	 * @return com.xinxinsoft.entity.taxpayer.Taxpayer
	 * @Date 2022/8/15 10:29
	 **/
	public List<Map<String, String>> getTaxpayerListFour(String batchNo,String userId) {
		String sql = " SELECT t.*,v.EMPLOYEE_NAME from TAXPAYER t INNER JOIN VW_USERINFO v ON t.USER_ID = v.ROWNO WHERE v.ISMAINDPT = 'true' AND t.BATCH_NO = ? AND t.USER_ID != ? AND t.STATE = '0' ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, batchNo).setString(1,userId).list();
	}

	/**
	 * 查询资金认领池数据
	 */
	public MoneyTotal getMoneyTotalSerialNo(String serialNo) {
		String sql = "select * from MoneyTotal where serialNo=?";
		return (MoneyTotal)getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).setString(0, serialNo).uniqueResult();
	}

	/**
	 * 查询资金认领池数据
	 */
	public List<MoneyTotal> getMoneyTotalSerialNoList(String serialNo) {
		String sql = "select * from MoneyTotal where serialNo=?";
		return getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).setString(0, serialNo).list();
	}

	/**
	 * @Description: 查询子集团信息
	 * @Param: [serialNo]
	 * @return: java.util.List<com.xinxinsoft.entity.claimForFunds.MoneyTotal>
	 * @Author: TX
	 * @Date: 2021/11/28 11:31
	 */
	public List<MoneyTotal> getSubMoneyTotalList(String serialNo) {
		String sql = "select * from MoneyTotal where serialNo=? and is_sub_group='1'";
		return getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).setString(0, serialNo).list();
	}

	/**
	 * @Description: app查询用户
	 * @Param: [name, systemUser, role, Collection]
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Author: TX
	 * @Date: 2021/12/6 11:34
	 */
	public List<Map<String, String>> queryVwUserinfo(String name,Map<String, String> systemUser,Role role,String Collection) {
		String sql=" select DISTINCT v.ROWNO,v.EMPLOYEE_NAME,v.COUNTY_NAME,v.COMPANY_NAME,v.DEPARTMENT_NAME from VW_USERINFO v  " +
				" inner join system_user_role sur on sur.row_no=v.rowno  " +
				" inner join system_role sr on sr.id=sur.role_id  " +
				" LEFT  JOIN TOPCONTACTS t ON v.rowno=t.rowno " +
				" where v.ISMAINDPT='true' ";
		if (role!=null){
			sql+="AND sr.NAME = '"+role.getName()+"' ";
			if ("Q".equals(role.getAscription_level())){	//区县角色
				sql+=" AND v.COUNTY_NO = '"+systemUser.get("COUNTY_NO")+"' ";
			}else if ("D".equals(role.getAscription_level())){	//地市角色
				sql+=" AND v.COMPANY_CODE = '"+systemUser.get("COMPANY_CODE")+"' ";
			}
		}
		if (Collection!=null && "1".equals(Collection)){
			sql += " AND t.dangqian = '"+systemUser.get("ROWNO")+"' ";
		}
		if(name!=null && !"".equals(name)){
			sql += " AND v.EMPLOYEE_NAME LIKE '%"+name+"%' ";
		}
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description: 根据角色code查询角色(role)信息
	 * @Param: [RoleCode]
	 * @return: com.xinxinsoft.entity.core.Role
	 * @Author: TX
	 * @Date: 2021/12/6 11:34
	 */
	public Role getRoleByCode(String RoleCode){
		String sql = "SELECT * FROM SYSTEM_ROLE WHERE NAME = ? ";
		return (Role) getSession().createSQLQuery(sql).addEntity(Role.class).setString(0, RoleCode).uniqueResult();
	}

	/**
	 * @Description: 根据用户编号查询视图信息
	 * @Param: [userRow]
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Author: TX
	 * @Date: 2021/12/6 11:35
	 */
	public List<Map<String, String>> getVwUserinf(String userRow){
		String sql = "SELECT * FROM VW_USERINFO WHERE ROWNO = '"+userRow+"' AND ISMAINDPT='true' ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description: 根据地市编号查询区县公司编号名称,如果没有地市编号查询地市公司编号名称
	 * @Param: [COMPANY_CODE 地市编号]
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Author: TX
	 * @Date: 2021/12/6 15:34
	 */
	public List<Map<String, String>> getVwCompanyCode(String COMPANY_CODE){
		String sql = "SELECT DISTINCT COMPANY_CODE as CODE,COMPANY_NAME as NAME from VW_USERINFO";
		if (COMPANY_CODE!=""){
			sql = "SELECT DISTINCT COUNTY_NO as CODE ,COUNTY_NAME as NAME from VW_USERINFO WHERE COMPANY_CODE = '"+COMPANY_CODE+"' ";
		}
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * 用于强制执行提交到数据库，防止二次提交BOSS问题
	 * @return
	 */
	public MoneyApplyDet addMoneyApplyDet(MoneyApplyDet moneyApplyDet) {
		// TODO Auto-generated method stub
		try {
			if (moneyApplyDet != null) {
				Session session = this.getSession();
				session.save(moneyApplyDet);
			}
			return moneyApplyDet;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public MoneyPeyMent addMoneyPeyMent(MoneyPeyMent moneyPeyMent) {
		// TODO Auto-generated method stub
		try {
			if (moneyPeyMent != null) {
				Session session = this.getSession();
				session.save(moneyPeyMent);
			}
			return moneyPeyMent;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public MoneyApplyFlow addMoneyApplyFlow(MoneyApplyFlow moneyApplyFlow) {
		// TODO Auto-generated method stub
		try {
			if (moneyApplyFlow != null) {
				Session session = this.getSession();
				session.save(moneyApplyFlow);
			}
			return moneyApplyFlow;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * @Description TODO 添加日志信息
	 * <AUTHOR>
	 * @param pushLog 记录对象
	 * @return com.xinxinsoft.entity.claimForFunds.pushLog
	 * @Date 2022/9/6 10:58
	 **/
	public pushLog addpushLog(pushLog pushLog) {
		// TODO Auto-generated method stub
		try {
			if (pushLog != null) {
				Session session = this.getSession();
				session.save(pushLog);
			}
			return pushLog;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * @Description :添加发票中间信息
	 * <AUTHOR>
	 * @param invoiceMiddle:
	 * @return: com.xinxinsoft.entity.claimForFunds.InvoiceMiddle
	 * @Date 2022/3/15 16:08
	 */
	public InvoiceMiddle addInvoiceMiddle(InvoiceMiddle invoiceMiddle) {
		// TODO Auto-generated method stub
		try {
			if (invoiceMiddle != null) {
				Session session = this.getSession();
				session.save(invoiceMiddle);
			}
			return invoiceMiddle;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * @Description :添加发票中间信息
	 * <AUTHOR>
	 * @param invoiceMiddle:
	 * @return: com.xinxinsoft.entity.claimForFunds.InvoiceMiddle
	 * @Date 2022/3/15 16:08
	 */
	public InvoiceMiddle updateInvoiceMiddle(InvoiceMiddle invoiceMiddle) {
		// TODO Auto-generated method stub
		try {
			if (invoiceMiddle != null) {
				Session session = this.getSession();
				session.update(invoiceMiddle);
			}
			return invoiceMiddle;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public MoneyPeyMent updateMoneyPeyMent(MoneyPeyMent moneyPeyMent) {
		// TODO Auto-generated method stub
		try {
			if (moneyPeyMent != null) {
				Session session = this.getSession();
				session.update(moneyPeyMent);
			}
			return moneyPeyMent;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public MoneyApplyTask addMoneyApplyTask(MoneyApplyTask moneyApplyTask) {
		// TODO Auto-generated method stub
		try {
			if (moneyApplyTask != null) {
				Session session = this.getSession();
				session.save(moneyApplyTask);
			}
			return moneyApplyTask;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	public Taxpayer updateTaxpayer(Taxpayer taxpayer) {
		try {
			if (taxpayer != null) {
				Session session = this.getSession();
				session.update(taxpayer);
			}
			return taxpayer;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public MoneyTotal updateMoneyTotal(MoneyTotal mt) {
		// TODO Auto-generated method stub
		try {
			if (mt != null) {
				Session session = this.getSession();
				session.update(mt);
			}
			return mt;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 用于强制执行提交到数据库，防止二次提交BOSS问题
	 * @param mt
	 * @return
	 */
	public MoneyTotal updateMoneyTotalTwo(MoneyTotal mt) {
		// TODO Auto-generated method stub
		Transaction tx = null;
		Session session=null;
		try {
			if (mt != null) {
				session = this.getSessionFactory().openSession();
				tx=session.beginTransaction();
				session.update(mt);
				tx.commit();
			}
			return mt;
		}
		catch (Exception e) {
			e.printStackTrace();
			tx.rollback();
			return null;
		}finally {
			session.close();
		}
	}
	/**
	 *用于强制执行提交到数据库，防止二次提交BOSS问题
	 * @param mdt
	 * @return
	 */
	public MoneyApplyDet updateCommitMoneyApplyDet(MoneyApplyDet mdt) {
		// TODO Auto-generated method stub
		Transaction tx = null;
		Session session =null;
		try {

			if (mdt != null) {
				session = this.getSessionFactory().openSession();
				tx=session.beginTransaction();
				session.update(mdt);
				tx.commit();
			}
			return mdt;
		}
		catch (Exception e) {
			e.printStackTrace();
			tx.rollback();
			return null;
		}finally {
			session.close();
		}
	}
	public MoneyApplyDet updateMoneyApplyDet(MoneyApplyDet md) {
		// TODO Auto-generated method stub
		try {
			if (md != null) {
				Session session = this.getSession();
				session.update(md);
			}
			return md;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}



	/**
	 * 新增资金认领池数据
	 */
	public MoneyApplyTask getMoneyApplyTask(String id) {
		String sql = "select * from MoneyApplyTask where id=?";
		return (MoneyApplyTask)getSession().createSQLQuery(sql).addEntity(MoneyApplyTask.class).setString(0, id).uniqueResult();
	}

	public MoneyApplyTask updateMoneyApplyTask(MoneyApplyTask mtask) {
		// TODO Auto-generated method stub
		try {
			if (mtask != null) {
				Session session = this.getSession();
				session.update(mtask);
			}
			return mtask;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	public MoneyApply getMoneyApply(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApply where id=?";
		return (MoneyApply)getSession().createSQLQuery(sql).addEntity(MoneyApply.class).setString(0, id).uniqueResult();
	}

	public MoneyApplyFlow getMoneyApplyFlow(String pid) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyFlow where FlowId=?";
		return (MoneyApplyFlow)getSession().createSQLQuery(sql).addEntity(MoneyApplyFlow.class).setString(0, pid).uniqueResult();
	}

	public MoneyApplyFlow getMoneyApplyFlowTwo(String pid) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyFlow where ApplyNo=?";
		return (MoneyApplyFlow)getSession().createSQLQuery(sql).addEntity(MoneyApplyFlow.class).setString(0, pid).uniqueResult();
	}

	public MoneyApplyFlow updateMoneyApplyFlow(MoneyApplyFlow mFlow){
		// TODO Auto-generated method stub
		try {
			if (mFlow != null) {
				Session session = this.getSession();
				session.update(mFlow);
			}
			return mFlow;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	public MoneyApply updateMoneyApply(MoneyApply my) {
		// TODO Auto-generated method stub
		try {
			if (my != null) {
				Session session = this.getSession();
				session.update(my);
				session.flush();
			}
			return my;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * 根据订单id查询跟踪记录
	 *
	 * @param id
	 * @return
	 *//*
	public List<MoneyApplyTask> processtracking(String id) {
		String sql = "select a.id as uuid," + "a.FlowId as process," + "a.CreatorName as creator," + "a.CreatorNo as creatorNo," + "a.CreateDate as creatdate,"
				+ "a.DealName as oper," + "a.DealNo as operno," + "a.DealDate as operdate," + "a.TaskMemo as replycontent," + "a.state as STATUS"
				+ " from MoneyApplyTask a " + "where a.FlowId='" + id + "' order by a.CreateDate desc";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyTask.class).list();
	}*/

	/**
	 * 根据订单id查询跟踪记录
	 * @param id
	 * @return
	 */
	public List<MoneyApplyTask> processtracking(String id) {
		String sql = "select a.Id as Id," + "a.FlowId as FlowId," + "a.CreatorName as CreatorName," + "a.CreatorNo as CreatorNo," + "a.CreateDate as CreateDate,"
				+ "a.DealName as DealName," + "a.DealNo as DealNo," + "a.DealDate as DealDate," + "a.TaskMemo as TaskMemo," + "a.state as state," + "a.TaskName as TaskName," + "a.TaskId as TaskId"
				+ " from MoneyApplyTask a " + "where a.FlowId='" + id + "' order by a.CreateDate asc";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyTask.class).list();
	}

	public List<MoneyApplyDet> getMoneyApplyDet(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where applyNo='"+id+"' and (bossState is null or bossState='') ORDER BY beginCycle NULLS FIRST";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	/**
	 * 获取附件消息
	 */
	public List<Map<String, String>> fuJian(String id, String biaoshi) {
		String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from MoneyApply  o  "
				+ " left join  SingleAndAttachment oa  on o.id=OA.orderID " + " LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.id=? and oa.link=?";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, id).setString(1, biaoshi).list();
	}

	public PageResponse claimForFundsMoneyApplyUser(String groupCode, PageRequest page,
													SystemUser user){
		String sql ="";
		if(groupCode!=null && "".equals(groupCode)){
			sql ="SELECT * from MoneyApply where creatorId='"+user.getRowNo()+"' and opType='1' and state='1' and groupCode='"+groupCode+"' order by createDate desc";
		}else{
			sql ="SELECT * from MoneyApply where creatorId='"+user.getRowNo()+"' and opType='1' and state='1' order by createDate desc";
		}
		return getMap(sql, page);
	}

	public PageResponse completedMoneyApplyUser(String groupCode, PageRequest page,
												SystemUser user){
		String sql ="";
		if(groupCode!=null && "".equals(groupCode)){
			sql ="SELECT * from MoneyApply where creatorId='"+user.getRowNo()+"' and opType='1' and state='0' and groupCode='"+groupCode+"' order by createDate desc";
		}else{
			sql ="SELECT * from MoneyApply where creatorId='"+user.getRowNo()+"' and opType='1' and state='0' order by createDate desc";
		}
		return getMap(sql, page);
	}

	/**
	 * 调用boss接口查询账户信息
	 */
	public String getAccountQuery(String groupCode, String bossNo) {
		HttpPost post = new HttpPost("http://*************:8080/EOM/AuditWorksheetHttpAction_aaaaa.action");
		try{
			String url = ESB_URL + "getUnitInfo";
			JSONObject object = new JSONObject();
			object.put("LOGIN_NO",bossNo);
			object.put("UNIT_ID", Long.parseLong(groupCode));
			String json = setContractNoParamObj(object,bossNo);
			String jsonString = UrlConnection.responseUTF8(url, json.toString());
			System.out.println("账户查询参数====》"+json);
			return jsonString.toString();
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 调用boss接口推送流程申请信息
	 * @param moneyTotal
	 * @param may
	 */
	public String getReChargeUnitAccount(String groupCode, String bossNo, MoneyApply may, MoneyTotal moneyTotal,SystemUser user) {
		String url = ESB_URL + "reChargeUnitAccount";
		try{
			JSONObject object = new JSONObject();
			object.put("LOGIN_NO", user.getBossUserName());
			object.put("UNIT_ID", Long.parseLong(groupCode));
			object.put("OUT_SYS_ACCEPT",moneyTotal.getBatchNo());//资金池的系统编号
			object.put("BANK_ACCOUNT",moneyTotal.getOtherAccNumber());//资金池的对方账户
			object.put("BANK_ACCOUNT_NAME",moneyTotal.getOtherName());//资金池的对方户名
			object.put("BILL_NOTE",moneyTotal.getUseMemo());//自定义
			object.put("BANK_ACCEPT",moneyTotal.getSerialNo());//资金池的唯一标识
			object.put("PAY_TYPE","转账");//指定编码
			object.put("FEE_CHANNEL","甩单系统");//指定编码
			object.put("BUSI_TYPE","1");//使用还是冲正,1使用，2冲正
			object.put("OP_FEE",BigDecimal.valueOf(Long.valueOf(moneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString());//总金额
			object.put("CONTRACT_NAME","无");
			String json = setParamObj(object);
			logger.info("资金BOSS推送认领金额推送数据接口地址："+ESB_URL+"reChargeUnitAccount："+ json.toString());
			String jsonString = UrlConnection.responseUTF8(url, json.toString());
			return jsonString.toString();
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}

	}

	/**
	 * 调用boss接口推送流程申请信息里的单条账户或者个人信息
	 */
	public String getApplyForFunds(String groupCode,MoneyApply may, MoneyTotal moneyTotal,List<MoneyApplyDet> moneyApplyDet,SystemUser user) {
		String url = ESB_URL + "applyForFunds";
		//HttpPost post = new HttpPost("http://*************:8080/EOM/AuditWorksheetHttpAction_ccccc.action");
		try{
			JSONArray jsonList = new JSONArray();
			for (int i=0; i<moneyApplyDet.size();i++) {
				if("1".equals(may.getOrderType())){
					Map<String, Object> object = new HashMap<String, Object>();
					object.put("UNIT_ID", groupCode==null?"":Long.parseLong(groupCode)+"");
					object.put("CONTRACT_NO","");//账户ID
					object.put("OUT_SYS_ACCEPT",moneyApplyDet.get(i).getMoneyNo()==null?"":moneyApplyDet.get(i).getMoneyNo());//资金池的系统编号
					object.put("BANK_ACCOUNT",moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber());//资金池的对方账户
					object.put("BANK_ACCOUNT_NAME",moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName());//资金池的对方户名
					object.put("BILL_NOTE","0");//自定义
					object.put("PHONE_NO",moneyApplyDet.get(i).getPhoneNo()==null?"":moneyApplyDet.get(i).getPhoneNo());//个人申请就是电话号码
					if("1".equals(moneyApplyDet.get(i).getUseType())){//1.缴费 2.存送 3.终端 06有价开销售 05预开票
						object.put("BUSI_TYPE","00");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE",moneyApplyDet.get(i).getLateFee());//是否减免滞纳金
					}else if("2".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","02");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("06".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","06");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("05".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","05");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE",moneyApplyDet.get(i).getLateFee());//是否减免滞纳金
					}else{
						object.put("BUSI_TYPE","01");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}
					object.put("CONTRACT_NAME","0");//合同名称
					object.put("PURPOSE",may.getApplyMemo()==null?"无":may.getApplyMemo());//（资金用途）自定义
					object.put("BUSI_FEE",BigDecimal.valueOf(Long.valueOf(moneyApplyDet.get(i).getAmount())).divide(new BigDecimal(100)).toString());//金额（单个账户的金额）
					//object.put("APPLY_LOGIN",user.getBossUserName());//申请人（发起人）
					object.put("APPLY_LOGIN",user.getBossUserName()==null?"":user.getBossUserName());//申请人（发起人）
					object.put("APPLY_NOTE",may.getApplyMemo()==null?"无":may.getApplyMemo());//申请说明
					object.put("PROD_NAME","0");//产品名称
					object.put("PROD_NUM","0");//产品数量
					object.put("BUSI_FLAG","P");//标识集团还是个人认领  业务标识 G集团 P个人
					object.put("OP_TYPE","03");//操作类型（使用，冲证） 操作类型 03使用申请  04冲正申请
					object.put("OUT_BACK_ACCEPT","");//外系统流水，（冲正必填）
					if(moneyApplyDet.get(i).getInvNo()!=null&&!"".equals(moneyApplyDet.get(i).getInvNo())){
						String[] invNo = moneyApplyDet.get(i).getInvNo().split("_");
						object.put("PRE_INVOICE_ACCEPT",invNo[0]);//预开票流水
					}else{
						object.put("PRE_INVOICE_ACCEPT","");//预开票流水
					}
					object.put("RE_UNIT_ID","");
					//object.put("OUT_BACK_ACCEPT",moneyApplyDet.get(i).getOldMoneyNo());//冲正流水
					jsonList.add(object);
				}else{
					Map<String, Object> object = new HashMap<String, Object>();
					object.put("UNIT_ID", groupCode==null?"":Long.parseLong(groupCode)+"");
					object.put("CONTRACT_NO",moneyApplyDet.get(i).getContrctNo()==null?"":moneyApplyDet.get(i).getContrctNo());//账户ID
					object.put("OUT_SYS_ACCEPT",moneyApplyDet.get(i).getMoneyNo()==null?"":moneyApplyDet.get(i).getMoneyNo());//明细编码
					object.put("BANK_ACCOUNT",moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber());//资金池的对方账户
					object.put("BANK_ACCOUNT_NAME",moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName());//资金池的对方户名
					object.put("BILL_NOTE","0");//自定义
					object.put("PHONE_NO","");//个人申请就是电话号码
					if("1".equals(moneyApplyDet.get(i).getUseType())){//1.缴费 2.存送 3.终端 06有价开销售
						object.put("BUSI_TYPE","00");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE",moneyApplyDet.get(i).getLateFee());//是否减免滞纳金
					}else if("2".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","02");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("06".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","06");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("05".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","05");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE",moneyApplyDet.get(i).getLateFee());//是否减免滞纳金
					}else{
						object.put("BUSI_TYPE","01");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}
					object.put("CONTRACT_NAME","0");//合同名称
					object.put("PURPOSE",may.getApplyMemo()==null?"无":may.getApplyMemo());//（资金用途）自定义
					object.put("BUSI_FEE",BigDecimal.valueOf(Long.valueOf(moneyApplyDet.get(i).getAmount())).divide(new BigDecimal(100)).toString());//金额（单个账户的金额）
					//object.put("APPLY_LOGIN",user.getBossUserName());//申请人（发起人）
					object.put("APPLY_LOGIN",user.getBossUserName()==null?"":user.getBossUserName());//申请人（发起人）
					object.put("APPLY_NOTE",may.getApplyMemo()==null?"无":may.getApplyMemo());//申请说明
					object.put("PROD_NAME","0");//产品名称
					object.put("PROD_NUM","0");//产品数量
					object.put("BUSI_FLAG","G");//标识集团还是个人认领  业务标识 G集团 P个人
					object.put("OP_TYPE","03");//操作类型（使用，冲证） 操作类型 03使用申请  04冲正申请
					object.put("OUT_BACK_ACCEPT","");//外系统流水，（冲正必填）
					if(moneyApplyDet.get(i).getInvNo()!=null&&!"".equals(moneyApplyDet.get(i).getInvNo())){
						String[] invNo = moneyApplyDet.get(i).getInvNo().split("_");
						object.put("PRE_INVOICE_ACCEPT",invNo[0]);//预开票流水
					}else{
						object.put("PRE_INVOICE_ACCEPT","");//预开票流水
					}
					object.put("RE_UNIT_ID","");
					//object.put("OUT_BACK_ACCEPT",moneyApplyDet.get(i).getOldMoneyNo());//冲正流水
					jsonList.add(object);
				}
			}
			JSONObject root = new JSONObject();
			JSONObject root_ = new JSONObject();
			JSONObject header = new JSONObject();
			JSONObject routing = new JSONObject();
			JSONObject busiinfo = new JSONObject();
			routing.put("ROUTE_KEY", "15");
			routing.put("ROUTE_VALUE", "11");
			header.put("POOL_ID", "31");
			header.put("DB_ID", "");
			header.put("ENV_ID", "1");
			header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
			header.put("CHANNEL_ID", "155");
			header.put("USERNAME", "zqddxt");
			header.put("PASSWORD", "123456");
			header.put("ENDUSRLOGINID", "");
			header.put("ENDUSRIP", "");
			header.put("ROUTING", routing);
			root_.put("HEADER", header);
			//后加字段最后确认BOSS工号，现在测试用
			busiinfo.put("LOGIN_NO", user.getBossUserName());
			busiinfo.put("UNIT_ID", groupCode);
			busiinfo.put("BUSI_INFO", jsonList);
			root_.put("BODY", busiinfo);
			root.put("ROOT", root_);
			logger.info("资金使用记录推送信息："+root.toString());
			/*HttpClient client = new DefaultHttpClient();
			post.setEntity(new StringEntity(root.toString(), "UTF-8"));
			HttpResponse response = client.execute(post);
			HttpEntity entity = response.getEntity();
			String returnMsg = EntityUtils.toString(entity, "UTF-8");
			System.out.println(returnMsg);*/
			String jsonString = UrlConnection.responseUTF8(url, root.toString());
			return jsonString;
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}finally {
			/* 释放链接 */
			//post.releaseConnection();
		}
	}

	/**
	 * 冲正调用boss接口推送流程申请信息里的单条账户或者个人信息
	 */
	public String getCorrectApplyForFunds(String groupCode,MoneyApply may, MoneyTotal moneyTotal,List<MoneyApplyDet> moneyApplyDet,SystemUser user) {
		String url = ESB_URL + "applyForFunds";
		HttpPost post = new HttpPost("http://*************:8080/EOM/AuditWorksheetHttpAction_ccccc.action");
		try{
			JSONArray jsonList = new JSONArray();
			for (int i=0; i<moneyApplyDet.size();i++) {
				Map<String, Object> object = new HashMap<String, Object>();
				if("1".equals(may.getOrderType())){
					object.put("UNIT_ID", Long.parseLong(groupCode));
					object.put("CONTRACT_NO","");//账户ID
					object.put("OUT_SYS_ACCEPT",moneyApplyDet.get(i).getMoneyNo());//资金池的系统编号
					object.put("BANK_ACCOUNT",moneyTotal.getOtherAccNumber());//资金池的对方账户
					object.put("BANK_ACCOUNT_NAME",moneyTotal.getOtherName());//资金池的对方户名
					object.put("BILL_NOTE","0");//自定义
					object.put("PHONE_NO",moneyApplyDet.get(i).getPhoneNo());//个人申请就是电话号码
					if("1".equals(moneyApplyDet.get(i).getUseType())){//1.缴费 2.存送 3.终端 06有价开销售
						object.put("BUSI_TYPE","00");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE",moneyApplyDet.get(i).getLateFee());//是否减免滞纳金
					}else if("06".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","06");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("05".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","05");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("2".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","02");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else{
						object.put("BUSI_TYPE","01");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}
					object.put("CONTRACT_NAME",moneyApplyDet.get(i).getContrctName());//合同名称
					object.put("PURPOSE",may.getApplyMemo());//（资金用途）自定义
					object.put("BUSI_FEE",Integer.parseInt(moneyApplyDet.get(i).getAmount())*100);//金额（单个账户的金额）
					//object.put("APPLY_LOGIN",user.getBossUserName());//申请人（发起人）
					object.put("APPLY_LOGIN",user.getBossUserName());//申请人（发起人）
					object.put("APPLY_NOTE",may.getApplyMemo());//申请说明
					object.put("PROD_NAME","0");//产品名称
					object.put("PROD_NUM","0");//产品数量
					object.put("BUSI_FLAG","P");//标识集团还是个人认领  业务标识 G集团 P个人
					object.put("OP_TYPE","04");//操作类型（使用，冲证） 操作类型 03使用申请  04冲正申请
					object.put("OUT_BACK_ACCEPT",moneyApplyDet.get(i).getOldMoneyNo());//冲正流水
					object.put("RE_UNIT_ID","");
					jsonList.add(object);
				}else{
					object.put("UNIT_ID", Long.parseLong(groupCode));
					object.put("CONTRACT_NO",moneyApplyDet.get(i).getContrctNo());//账户ID
					object.put("OUT_SYS_ACCEPT",moneyApplyDet.get(i).getMoneyNo());//明细编码
					object.put("BANK_ACCOUNT",moneyTotal.getOtherAccNumber());//资金池的对方账户
					object.put("BANK_ACCOUNT_NAME",moneyTotal.getOtherName());//资金池的对方户名
					object.put("BILL_NOTE","0");//自定义
					object.put("PHONE_NO","");//个人申请就是电话号码
					if("1".equals(moneyApplyDet.get(i).getUseType())){//1.缴费 2.存送 3.终端 06有价开销售
						object.put("BUSI_TYPE","00");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE",moneyApplyDet.get(i).getLateFee());//是否减免滞纳金
					}else if("06".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","06");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("2".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","02");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else if("05".equals(moneyApplyDet.get(i).getUseType())){
						object.put("BUSI_TYPE","05");//业务类型（00是缴费，01是终端，02是存送;05预开票)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}else{
						object.put("BUSI_TYPE","01");//业务类型（00是缴费，01是终端，02是存送)
						object.put("DELAY_RATE","0");//是否减免滞纳金
					}
					object.put("CONTRACT_NAME","0");//合同名称
					object.put("PURPOSE",may.getApplyMemo());//（资金用途）自定义
					object.put("BUSI_FEE",Integer.parseInt(moneyApplyDet.get(i).getAmount())*100);//金额（单个账户的金额）
					//object.put("APPLY_LOGIN",user.getBossUserName());//申请人（发起人）
					object.put("APPLY_LOGIN",user.getBossUserName());//申请人（发起人）
					object.put("APPLY_NOTE",may.getApplyMemo());//申请说明
					object.put("PROD_NAME","0");//产品名称
					object.put("PROD_NUM","0");//产品数量
					object.put("BUSI_FLAG","G");//标识集团还是个人认领  业务标识 G集团 P个人
					object.put("OP_TYPE","04");//操作类型（使用，冲证） 操作类型 03使用申请  04冲正申请
					object.put("OUT_BACK_ACCEPT",moneyApplyDet.get(i).getOldMoneyNo());//冲正流水
					object.put("RE_UNIT_ID","");
					jsonList.add(object);
				}
			}
			JSONObject root = new JSONObject();
			JSONObject root_ = new JSONObject();
			JSONObject header = new JSONObject();
			JSONObject routing = new JSONObject();
			JSONObject busiinfo = new JSONObject();
			routing.put("ROUTE_KEY", "15");
			routing.put("ROUTE_VALUE", "11");
			header.put("POOL_ID", "31");
			header.put("DB_ID", "");
			header.put("ENV_ID", "1");
			header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
			header.put("CHANNEL_ID", "155");
			header.put("USERNAME", "zqddxt");
			header.put("PASSWORD", "123456");
			header.put("ENDUSRLOGINID", "");
			header.put("ENDUSRIP", "");
			header.put("ROUTING", routing);
			root_.put("HEADER", header);
			busiinfo.put("LOGIN_NO", user.getBossUserName());
			busiinfo.put("UNIT_ID", groupCode);
			busiinfo.put("BUSI_INFO", jsonList);
			root_.put("BODY", busiinfo);
			root.put("ROOT", root_);
			System.out.println(root.toString());
			System.out.println(root.toString());
			/*HttpClient client = new DefaultHttpClient();
			post.setEntity(new StringEntity(root.toString(), "UTF-8"));
			HttpResponse response = client.execute(post);
			HttpEntity entity = response.getEntity();
			String returnMsg = EntityUtils.toString(entity, "UTF-8");
			System.out.println(returnMsg);*/
			String jsonString = UrlConnection.responseUTF8(url, root.toString());
			return jsonString.toString();
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}finally {
			/* 释放链接 */
			post.releaseConnection();
		}
	}

	/**
	 * json 数据格式化：
	 *
	 * @param body
	 * @return
	 */
	protected String setParamObj(JSONObject body) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "15");
		routing.put("ROUTE_VALUE", "11");
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}


	/**
	 * json 数据格式化：
	 *
	 * @param body
	 * @return
	 */
	protected String setContractNoParamObj(JSONObject body,String bossNo) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "14");
		routing.put("ROUTE_VALUE", bossNo);
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}

	/**
	 * json 数据格式化：
	 *
	 * @param body
	 * @return
	 */
	protected String setParamObjList(JSONArray body) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "15");
		routing.put("ROUTE_VALUE", "11");
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}
	//======================================================下面是冲证代码==================================================
	/**
	 * 查询账户信息
	 * @param groupCode
	 * @param user
	 * @return
	 */
	public List<MoneyApplyDet> canItBeVerifiedMoneyApply(String groupCode,String orderType,String number,SystemUser user){
		/*String sql ="select * from MoneyApplyDet where groupCode='"+groupCode+"' and creatorId='"+user.getRowNo()+"' "
				+ "and orderType='"+orderType+"' and opType='1' and state='1' and applyno order by createDate desc";*/

		String sql ="select md.* from MoneyApplyDet md "
				+ "LEFT JOIN MONEYAPPLY my on my.APPLYNO=MD.APPLYNO  "
				+ "where md.creatorId='"+user.getRowNo()+"'  "
				+ "and md.opType='1' and md.state='1' "
				+ " ";

		if(groupCode != null && !"".equals(groupCode)&& !"null".equals(groupCode)){
			sql+=" and md.groupCode='"+groupCode+"'";
		}

		if(orderType != null && !"".equals(orderType)&& !"null".equals(orderType)){
			sql+=" and md.orderType='"+orderType+"'";
		}

		if(number != null && !"".equals(number)&& !"null".equals(number)){
			sql+=" and md.APPLYNO='"+number+"'";
		}
		sql +=" order by md.createDate desc";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	/**
	 * 查询账户信息
	 * @return
	 */
	public List<MoneyApplyDet> getMoneyApplyDetlist(String serialNo){
		String sql ="select * from MoneyApplyDet where serialNo='"+serialNo+"' and opType!='4' and oldMoneyNo is null order by createDate desc";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	/**
	 * 审批中的工单(使用和冲正)
	 * @param groupCode
	 * @param page
	 * @param user
	 * @return
	 */
	public LayuiPage canItBeVerifiedMoneyApplyUser(String groupCode,String optype, LayuiPage page,
												   SystemUser user){
		String sql ="SELECT * from MoneyApply where creatorId='"+user.getRowNo()+"' and opType='"+optype+"' and state='1'";
		if(groupCode!=null && !"".equals(groupCode)){
			sql +=" and groupCode='"+groupCode+"'";
		}
		sql+=" order by createDate desc";
		page.setCount(getCount("select count(0) from (" + sql + ")"));
		if (page.getCount() > 0) {
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}

	/**
	 * 审批冲正工单
	 * @param groupCode
	 * @param page
	 * @param user
	 * @return
	 */
	public LayuiPage approvalMoneyApplyUser(String groupCode,String optype, LayuiPage page,
											SystemUser user){
		String sql="SELECT * from (SELECT MP.ID,"
				+ "MP.APPLYNO,"
				+ "MP.TITLE,"
				+ "MP.APPLYMEMO,"
				+ "MP.APPLYAMOUNT,"
				+ "MP.CREATORID,"
				+ "MP.CREATORNAME,"
				+ "MP.CREATEDATE,"
				+ "MP.STATE,"
				+ "MP.ORDERTYPE,"
				+ "MP.GROUPCODE,"
				+ "MP.GROUPNAME,"
				+ "MP.OPTYPE "
				+ "FROM MONEYAPPLY MP LEFT JOIN MONEYAPPLYTASK MO ON MO.CREATORNO=MP.CREATORID "
				+ "WHERE MP.CREATORID='"+user.getRowNo()+"' AND OPTYPE='"+optype+"' "
				+ "GROUP BY "
				+ "MP.ID,"
				+ "MP.APPLYNO,"
				+ "MP.TITLE,"
				+ "MP.APPLYMEMO,"
				+ "MP.APPLYAMOUNT,"
				+ "MP.CREATORID,"
				+ "MP.CREATORNAME,"
				+ "MP.CREATEDATE,"
				+ "MP.STATE,"
				+ "MP.ORDERTYPE,"
				+ "MP.GROUPCODE,"
				+ "MP.GROUPNAME,"
				+ "MP.OPTYPE) asd ";
		if(groupCode!=null && !"".equals(groupCode)){
			sql+=" AND MP.GROUPCODE='"+groupCode+"'";
		}
		sql+=" ORDER BY asd.CREATEDATE DESC";
		page.setCount(getCount("select count(0) from (" + sql + ")"));
		if (page.getCount() > 0) {
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}

	/**
	 * 查询我创建的全部工单(使用和冲正)
	 * @param groupCode
	 * @param page
	 * @param user
	 * @return
	 */
	public LayuiPage completedCanItBeVerifiedMoneyApplyUser(String groupCode,String optype, LayuiPage page,
															SystemUser user){
		String sql ="SELECT * from MoneyApply where creatorId='"+user.getRowNo()+"' and opType='"+optype+"' and state<>'1'";
		if(groupCode!=null && !"".equals(groupCode)){
			sql +=" and groupCode='"+groupCode+"'";
		}
		sql+=" order by createDate desc";
		page.setCount(getCount("select count(0) from (" + sql + ")"));
		if (page.getCount() > 0) {
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}

	/**
	 * 查询账户详细
	 * @return
	 */
	public MoneyApplyDet getMoneyApplyDetId(String moneyNo) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where moneyNo='"+moneyNo+"'";
		return (MoneyApplyDet) getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).uniqueResult();
	}

	/**
	 * 查询申请工单详情
	 * @return
	 */
	public MoneyApply getMoneyApplyApplyNo(String id){
		String sql = "select * from MoneyApply where id='"+id+"'";
		return (MoneyApply) getSession().createSQLQuery(sql).addEntity(MoneyApply.class).uniqueResult();
	}

	/**
	 * 查询冲正账户状态
	 * @param user
	 * @param groupCode
	 * @param number
	 */
	public String qryUseRecordState(SystemUser user,String groupCode,String number){
		String url = ESB_URL + "qryUseRecordState";
		HttpPost post = new HttpPost("http://*************:8080/EOM/AuditWorksheetHttpAction_eeeee.action");
		try{
			JSONObject objecttwo = new JSONObject();
			JSONObject busiinfo = new JSONObject();
			objecttwo.put("LOGIN_NO",user.getBossUserName());
			objecttwo.put("APPLY_LOGIN",user.getBossUserName());
			objecttwo.put("OUT_SYS_ACCEPT",number);
			objecttwo.put("UNIT_ID",Long.parseLong(groupCode));
			busiinfo.put("BUSI_INFO", objecttwo);
			JSONObject root = new JSONObject();
			JSONObject root_ = new JSONObject();
			JSONObject header = new JSONObject();
			JSONObject routing = new JSONObject();
			routing.put("ROUTE_KEY", "15");
			routing.put("ROUTE_VALUE", "11");
			header.put("POOL_ID", "31");
			header.put("DB_ID", "");
			header.put("ENV_ID", "1");
			header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
			header.put("CHANNEL_ID", "155");
			header.put("USERNAME", "zqddxt");
			header.put("PASSWORD", "123456");
			header.put("ENDUSRLOGINID", "");
			header.put("ENDUSRIP", "");
			header.put("ROUTING", routing);
			root_.put("HEADER", header);
			root_.put("BODY", busiinfo);
			root.put("ROOT", root_);
			String jsonString = UrlConnection.responseUTF8(url, root.toString());
			return jsonString.toString();
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}finally{
			/* 释放链接 */
			post.releaseConnection();
		}

	}
	/**
	 * 资金退款
	 * @param number
	 * @return
	 */
	public String cancelRecharge (String number){
		String url = ESB_URL + "cancelRecharge";
		try{
			JSONObject objecttwo = new JSONObject();
			objecttwo.put("OUT_SYS_ACCEPT",number);
			JSONObject root = new JSONObject();
			JSONObject root_ = new JSONObject();
			JSONObject header = new JSONObject();
			JSONObject routing = new JSONObject();
			JSONObject busiinfo = new JSONObject();
			routing.put("ROUTE_KEY", "15");
			routing.put("ROUTE_VALUE", "11");
			header.put("POOL_ID", "31");
			header.put("DB_ID", "");
			header.put("ENV_ID", "1");
			header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
			header.put("CHANNEL_ID", "155");
			header.put("USERNAME", "zqddxt");
			header.put("PASSWORD", "123456");
			header.put("ENDUSRLOGINID", "");
			header.put("ENDUSRIP", "");
			header.put("ROUTING", routing);
			root_.put("HEADER", header);
			busiinfo.put("BUSI_INFO", objecttwo);
			root_.put("BODY", busiinfo);
			root.put("ROOT", root_);
			String jsonString = UrlConnection.responseUTF8(url, root.toString());
			return jsonString.toString();
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
	}

	public String updateIncomeState(String status,String serialNo){
		HttpPost post = new HttpPost("http://*************:8080/EOM/AuditWorksheetHttpAction_sssss.action");
		try{
			JSONObject object = new JSONObject();
			object.put("onlyCode",serialNo);
			object.put("orderStatus",status);
			String jsonString = UrlConnection.responseUTF8("http://**************/TAS/rest/incomerest/updateIncomeState", object.toString());
			logger.info("资金推送财务使用状态推送数据地址：http://**************/TAS/rest/incomerest/updateIncomeState:"+object.toString());
			return jsonString;
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}finally{
			/* 释放链接 */
			post.releaseConnection();
		}
	}

	public List<MoneyApplyDet> getMoneyApplyDetSerialNo(String id){
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where serialNo='"+id+"' and state='1'";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	public static FTPClient getFTPClient(){
		FTPClient ftpClient = new FTPClient();
		try {
			ftpClient = new FTPClient();
			ftpClient.connect("10.108.226.177", 21);// 连接FTP服务器
			ftpClient.login("root", "1234");// 登陆FTP服务器
			if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
				System.out.println("未连接到FTP，用户名或密码错误。");
				ftpClient.disconnect();
			} else {
				System.out.println("FTP连接成功。");
			}
		} catch (IOException e) {
			e.printStackTrace();
			System.out.println("FTP的端口错误,请正确配置。");
		}
		return ftpClient;
	}

	/*
	 * 从FTP服务器下载文件
	 * @param ftpHost FTP IP地址
	 * @param ftpUserName FTP 用户名
	 * @param ftpPassword FTP用户名密码
	 * @param ftpPort FTP端口
	 * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
	 * @param localPath 下载到本地的位置 格式：H:/download
	 * @param fileName 文件名称
	 */
     /*public String downloadFtpFile(String date,String pathName,String ext) {
    	try {
	    	if(pathName.startsWith("/")&&pathName.endsWith("/")){
	            String directory = pathName;
	            //更换目录到当前目录
	            FTPClient ftpClient = getFTPClient();
	            ftpClient.changeWorkingDirectory(directory);
	            FTPFile[] files = ftpClient.listFiles();
	            int s = 0;
	            for(FTPFile file:files){
	                if(file.isFile()){
	                	System.out.println("文件名："+file.getName());
	                    if(file.getName().indexOf(ext)>=0){
	                        ftpClient.enterLocalPassiveMode();
	                        ftpClient.changeWorkingDirectory("/"+file.getName());
	                        File localFile = new File("G:/aa/"+File.separatorChar+file.getName()+".txt");
	                        OutputStream os = new FileOutputStream(localFile);
	                        ftpClient.retrieveFile(file.getName(), os);
	                        os.close();
	                        ftpClient.logout();
	                        FileInputStream fis;
	            			fis = new FileInputStream(localFile);
	            			InputStreamReader isr = new InputStreamReader(fis);//字符流
	            			BufferedReader br = new BufferedReader(isr);//缓冲
	            			String line = null;
	            			String[] strs = null;
	            			while ((line = br.readLine()) != null) {//字符不等于空
	            				s++;
	        	            	if(s>9){
	        	            		s=0;
	        	            	}
	            				System.out.println(line);//输出行
	            				strs = line.split("\\<=>");
	            				MoneyTotal m = new MoneyTotal();
	            				m.setSerialNo(strs[0]);//(唯一标识号)
	            				m.setOtherAccNumber(strs[1]);//(对方账号)
	            				m.setOtherName(strs[2]);//(对方户名)
	            				m.setOtherBank(strs[3]);//(对方开户行)
	            				m.setAccNumber(strs[4]);//(公司账号)
	            				m.setTranDate(getStringDateFour(strs[5]));//strs[5]//(交易时间)
	            				m.setAmount(changeY2F(strs[6]));//(转账金额)
	            				m.setUseAmount("0");//(使用金额)
	            				m.setOverAmount(changeY2F(strs[6]));//(剩余金额)
	            				m.setReceiverSCompany(strs[7]);
	            				m.setMemo(strs[8]);//(摘要注释)
	            				m.setUseMemo(strs[9]);//(用途注释)
	    						List<Object[]> sone= findCodeByRowNo(strs[10]);
	    						String companyCode="";
	    						String companyName="";
	    						String batchNo="";
	    						for(int j=0;j<sone.size();j++){
	    							companyCode=(String) sone.get(j)[0];
	    							companyName=(String) sone.get(j)[1];
	    							batchNo=(String) sone.get(j)[2];
	    						}
	            				if("sky".equals(strs[10])){
	            					m.setCompanyCode("00");//(地市编码)
		            				m.setCompanyName("省公司");//(地市名称)
	            				}else{
	            					m.setCompanyCode(companyCode);//(地市编码)
		            				m.setCompanyName(companyName);//(地市名称)
	            				}
	            				m.setCreateDate(new Date());//(创建时间)
	            				m.setState(0);//(状态)
	            				m.setIsThe(0);//记录申领次数；
	            				if("sky".equals(strs[10])){
	            					m.setBatchNo("SGS"+getStringDatethree(new Date())+s);//(系统编号)
	            				}else{
	            					m.setBatchNo(batchNo+getStringDatethree(new Date())+s);//(系统编号)
	            				}
	            				saveProcessList(m);
	            			}
	            			br.close();//关闭文件
	                    }
	                }else if(file.isDirectory()){
	                	downloadFtpFile(date,directory+file.getName()+"/",ext);
	                }
	            }
	        }
	    	return "OK";
    	} catch (FileNotFoundException e) {
            e.printStackTrace();
            System.out.println("没有找到文件");
            return "没有找到文件";
        } catch (IOException e) {
            e.printStackTrace();
            System.out.println("文件读取错误");
            return "文件读取错误";
        }
    }*/


	/**
	 * 获取修改时间最新的文件名
	 * @return
	 */
	public String lastTimeFile(FTPClient ftpClient,String fileName,String ftpPath){
		List<FTPFile> list = new ArrayList<FTPFile>();
		if(!ftpPath.endsWith("/")){//判读路径是否以“/”结尾
			ftpPath += "/";
		}
		//获取相关文件名的文件组
		try {
			ftpClient.makeDirectory(ftpPath);
			ftpClient.changeWorkingDirectory(ftpPath);//改变ftp工作目录
			ftpClient.configure(new FTPClientConfig());
			FTPFile[] files = ftpClient.listFiles();//遍历
			for (FTPFile file : files) {
				if (file.isFile()) {
					if (file.getName().indexOf(fileName) >= 0) {//匹配文件
						list.add(file);
					}
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
		}

		String name = "";
		if(list.size() > 0) {
			int[] a = new int[list.size()];
			List<Date> l = new ArrayList<Date>();
			for(int i = 0;i<list.size();i++){
				a[i] = i;
				Date date1 = list.get(i).getTimestamp().getTime();
				l.add(date1);
			}
			for(int i = 0;i<l.size()-1;i++){
				for(int j=i+1;j<l.size();j++){
					if(l.get(i).before(l.get(j))){
						int temp = a[i];
						a[i] = a[j];
						a[j] = temp;
						Collections.swap(l,i,j);
					}
				}
			}
			name = list.get(a[0]).getName();
		}

		return name;
	}


	/**
	 * 上传txt文件
	 *
	 *            上传的文件
	 *            ftp目录
	 */
	public boolean uploadTxt() {
		FTPClient ftpClient = getFTPClient();
		File file = new File("/EOMAPP/UploadFiles/contracts/contractCsv/20190110/ZHZT_ORDER_REL.flag");
		boolean bl  = false;
		String path="/";
		if(!path.endsWith("/")){//判读路径是否以“/”结尾
			path += "/";
		}
		try {
			if (file != null) {
				ftpClient.makeDirectory(path);
				ftpClient.changeWorkingDirectory(path);
				FileInputStream input = new FileInputStream(file);
				ftpClient.storeFile(file.getName(), input);
				input.close();
				bl=true;
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			bl=false;
		}
		return bl;
	}


	/**
	 * 根据用户编号查询公司编码
	 */
	public List<Object[]> findCodeByRowNo(String groupid) {
		String sql = "select COMPANY_CODE,COMPANY_NAME,COMPANY_IBM from AFR_SYSTEMCOMPANY WHERE COMPANY_GROUPID=?";
		return getSession().createSQLQuery(sql).setString(0, groupid).list();
	}

	/**
	 * 日期转换
	 *
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatethree(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		return formatter.format(currentTime);
	}

	/**
	 * 将元为单位的转换为分 替换小数点，支持以逗号区分的金额
	 *
	 * @param amount
	 * @return
	 */
	public static String changeY2F(String amount){
		String currency =  amount.replaceAll("\\$|\\￥|\\,", "");  //处理包含, ￥ 或者$的金额
		int index = currency.indexOf(".");
		int length = currency.length();
		Long amLong = 0l;
		if(index == -1){
			amLong = Long.valueOf(currency+"00");
		}else if(length - index >= 3){
			amLong = Long.valueOf((currency.substring(0, index+3)).replace(".", ""));
		}else if(length - index == 2){
			amLong = Long.valueOf((currency.substring(0, index+2)).replace(".", "")+0);
		}else{
			amLong = Long.valueOf((currency.substring(0, index+1)).replace(".", "")+"00");
		}
		return amLong.toString();
	}

	/**
	 *
	 * @params
	 */
	public static Date getStringDateFour(String currentTime){
		String dateStrings =currentTime.substring(0,4)+"-"+currentTime.substring(4,6)+"-"+currentTime.substring(6,8);
		Date dateString=null;
		try {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
			dateString = formatter.parse(dateStrings);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return dateString;
	}

	public static FTPClient getBossFTPClient(){
		FTPClient ftpClient = new FTPClient();
		try {
			ftpClient = new FTPClient();
			ftpClient.connect("10.113.181.15", 22);// 连接FTP服务器
			ftpClient.login("baseapp", "6nYPNjb*");// 登陆FTP服务器
			if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
				System.out.println("未连接到FTP，用户名或密码错误。");
				ftpClient.disconnect();
			} else {
				System.out.println("FTP连接成功。");
			}
		} catch (IOException e) {
			e.printStackTrace();
			System.out.println("FTP的端口错误,请正确配置。");
		}
		return ftpClient;
	}
	/*
	 * 从FTP服务器下载文件
	 * @param ftpHost FTP IP地址
	 * @param ftpUserName FTP 用户名
	 * @param ftpPassword FTP用户名密码
	 * @param ftpPort FTP端口
	 * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
	 * @param localPath 下载到本地的位置 格式：H:/download
	 * @param fileName 文件名称
	 */
	public String downloadBossFtpFile(String date) {
		FTPClient ftpClient = null;
		try {
			ftpClient = getBossFTPClient();
			ftpClient.setControlEncoding("UTF-8");//中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory(FileUpload.getEsbDowlowndFtpUrl()+"/usedRecords_"+date);
			File localFile = new File(FileUpload.getEsbFtpUrl()+File.separatorChar+"usedRecords_"+date+".txt");
			OutputStream os = new FileOutputStream(localFile);
			ftpClient.retrieveFile("usedRecords_"+date, os);
			os.close();
			ftpClient.logout();
			FileInputStream fis;
			fis = new FileInputStream(localFile);
			InputStreamReader isr = new InputStreamReader(fis, "GBK");//字符流
			BufferedReader br = new BufferedReader(isr);//缓冲
			String line = null;
			String[] strs = null;
			while ((line = br.readLine()) != null) {//字符不等于空
				System.out.println(line);//输出行
				strs = line.split("\\<=>");
			}
			br.close();//关闭文件
			return "OK";
		} catch (FileNotFoundException e) {
			e.printStackTrace();
			System.out.println("没有找到文件");
			return "没有找到文件";
		} catch (IOException e) {
			e.printStackTrace();
			System.out.println("文件读取错误");
			return "文件读取错误";
		}
	}

	/**
	 * FTP文件上传测试
	 */
	public String downloadFtpFile(String date,String pathName,String ext){
		logger.info("匹配文件名称："+ext);
		FTPClient ftpc=new FTPClient();
		//初始化并保存信息
		String server = "*************";
		int port = 21 ;
		String uname = "cmip" ;
		String password = "cmipzqdd" ;
		try {
			String systemKey = FTPClientConfig.SYST_NT;
			String serverLanguageCode = "zh";
			FTPClientConfig conf = new FTPClientConfig(systemKey);
			conf.setServerLanguageCode(serverLanguageCode);
			conf.setDefaultDateFormatStr("yyyy-MM-dd");
			ftpc.configure(conf);
			ftpc.connect(server, port);
			ftpc.login(uname, password);
			// 文件类型,默认是ASCII
			ftpc.setFileType(FTPClient.BINARY_FILE_TYPE);
			// 设置被动模式
			ftpc.enterLocalPassiveMode();
			ftpc.setConnectTimeout(3000);
			ftpc.setControlEncoding("GBK");
			// 响应信息
			int replyCode = ftpc.getReplyCode();
			if ((!FTPReply.isPositiveCompletion(replyCode))) {
				// 关闭Ftp连接
				try {
					if (ftpc.isConnected())
						ftpc.disconnect();
				} catch (Exception e) {
					throw new Exception("关闭FTP服务出错!");
				}
				// 释放空间
				ftpc = null;
				throw new Exception("登录FTP服务器失败,请检查![Server:" + server + "、"  + "User:" + uname + "、" + "Password:" + password);
			} else {
				logger.info("FTP登录成功=====》[Server:" + server + "、"  + "User:" + uname + "、" + "Password:" + password);
				ftpc.changeWorkingDirectory("///");
				List<FTPFile> ftpfiles = Arrays.asList(ftpc.listFiles());
				if(ftpfiles==null || ftpfiles.size()==0) {
					logger.info("/下找不到文件");
				}else{
					int s = 0;
					for (FTPFile ftpFile : ftpfiles) {
						if (ftpFile.isFile()){
							if(ftpFile.getName().indexOf(ext)>=0){
								File file=new File("/EOMAPP/UploadFiles/financialsystenftp/"+ftpFile.getName());
								if(!file.exists()){
									logger.info("文件名称====》》"+ftpFile.getName());
									File localFile = new File("/EOMAPP/UploadFiles/financialsystenftp/"+ftpFile.getName());
									OutputStream os = new FileOutputStream(localFile);
									ftpc.retrieveFile(ftpFile.getName(), os);
									os.close();
									FileInputStream fis;
									fis = new FileInputStream(localFile);
									InputStreamReader isr = new InputStreamReader(fis, "GBK");//字符流
									BufferedReader br = new BufferedReader(isr);//缓冲
									logger.info("下载文件成功====》》"+ftpFile.getName());
									String line = null;
									String[] strs = null;
									while ((line = br.readLine()) != null) {//字符不等于空
										s++;
										if(s>9){
											s=0;
										}
										logger.info(line);
										strs = line.split("\\<=>");
										MoneyTotal moneyTotal= getMoneyTotalSerialNo(strs[0]);
										if(moneyTotal==null){
											MoneyTotal m = new MoneyTotal();
											m.setSerialNo(strs[0]);//(唯一标识号)
											m.setOtherAccNumber(strs[1]);//(对方账号)
											m.setOtherName(strs[2]);//(对方户名)
											m.setOtherBank(strs[3]);//(对方开户行)
											m.setAccNumber(strs[4]);//(公司账号)
											m.setTranDate(getStringDateFour(strs[5]));//strs[5]//(交易时间)
											m.setAmount(changeY2F(strs[6]));//(转账金额)
											m.setUseAmount("0");//(使用金额)
											m.setOverAmount(changeY2F(strs[6]));//(剩余金额)
											m.setReceiverSCompany(strs[7]);
											m.setMemo(strs[8]);//(摘要注释)
											m.setUseMemo(strs[9]);//(用途注释)
											List<Object[]> sone= findCodeByRowNo(strs[10]);
											String companyCode="";
											String companyName="";
											String batchNo="";
											for(int j=0;j<sone.size();j++){
												companyCode=(String) sone.get(j)[0];
												companyName=(String) sone.get(j)[1];
												batchNo=(String) sone.get(j)[2];
											}
											if("sky".equals(strs[10])){
												m.setCompanyCode("00");//(地市编码)
												m.setCompanyName("省公司");//(地市名称)
											}else{
												m.setCompanyCode(companyCode);//(地市编码)
												m.setCompanyName(companyName);//(地市名称)
											}
											m.setCreateDate(new Date());//(创建时间)
											m.setState(0);//(状态)
											m.setIsThe(0);//记录申领次数；
											if("sky".equals(strs[10])){
												m.setBatchNo("SGS"+getStringDatethree(new Date())+s);//(系统编号)
											}else{
												m.setBatchNo(batchNo+getStringDatethree(new Date())+s);//(系统编号)
											}
											saveProcessList(m);
										}

									}

								}
							}
						}
					}
					ftpc.logout();
					ftpc.disconnect();
				}
			}
			return "OK";
		} catch (Exception e) {
			logger.info("FTP访问错误====》"+e.getMessage());
			return "NO";
		}
	}


	/**
	 * 获取FTPClient对象
	 *
	 *            FTP主机服务器
	 *            FTP 登录密码
	 *            FTP登录用户名
	 *            FTP端口 默认为21
	 * @return
	 */
	public static FTPClient getFTPClienttwo(){
		FTPClient ftpClient = new FTPClient();
		try {
			ftpClient = new FTPClient();
			ftpClient.connect("127.0.0.1", 21);// 连接FTP服务器
			ftpClient.login("root", "1234");// 登陆FTP服务器
			if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
				System.out.println("未连接到FTP，用户名或密码错误。");
				ftpClient.disconnect();
			} else {
				System.out.println("FTP连接成功。");
			}
		} catch (IOException e) {
			e.printStackTrace();
			System.out.println("FTP的端口错误,请正确配置。");
		}
		return ftpClient;
	}

	/*
	 * 从FTP服务器下载文件
	 * @param ftpHost FTP IP地址
	 * @param ftpUserName FTP 用户名
	 * @param ftpPassword FTP用户名密码
	 * @param ftpPort FTP端口
	 * @param ftpPath FTP服务器中文件所在路径 格式： ftptest/aa
	 * @param localPath 下载到本地的位置 格式：H:/download
	 * @param fileName 文件名称
	 */
	public String downloadFtpFile() {
		FTPClient ftpClient = null;
		try {
			ftpClient = getFTPClienttwo();
			ftpClient.setControlEncoding("UTF-8");//中文支持
			ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);
			ftpClient.enterLocalPassiveMode();
			ftpClient.changeWorkingDirectory("/TransferDetail20190110_");

			List<FTPFile> ftpfiles = Arrays.asList(ftpClient.listFiles());

			File localFile = new File("F:/FTP/aa/"+File.separatorChar+"22.txt");
			OutputStream os = new FileOutputStream(localFile);
			ftpClient.retrieveFile("TransferDetail20190110_mPViDGjV", os);
			os.close();
			ftpClient.logout();
			//File file = new File("G:\\TransferDetail20181221_fRbwTVsy");
			FileInputStream fis;
			fis = new FileInputStream(localFile);
			InputStreamReader isr = new InputStreamReader(fis, "GBK");// 字符流
			BufferedReader br = new BufferedReader(isr);//缓冲
			String line = null;
			String[] strs = null;
			while ((line = br.readLine()) != null) {//字符不等于空
				System.out.println(line);//输出行
				strs = line.split("\\<=>");
				System.out.println(strs.length);
				MoneyTotal moneyTotal= getMoneyTotalSerialNo(strs[0]);
				if(moneyTotal==null){
					MoneyTotal m = new MoneyTotal();
					m.setSerialNo(strs[0]);//(唯一标识号)
					m.setOtherAccNumber(strs[1]);//(对方账号)
					m.setOtherName(strs[2]);//(对方户名)
					m.setOtherBank(strs[3]);//(对方开户行)
					m.setAccNumber(strs[4]);//(公司账号)
					m.setTranDate(getStringDateFour(strs[5]));//strs[5]//(交易时间)
					m.setAmount(changeY2F(strs[6]));//(转账金额)
					m.setUseAmount("0");//(使用金额)
					m.setOverAmount(changeY2F(strs[6]));//(剩余金额)
					m.setReceiverSCompany(strs[7]);
					m.setMemo(strs[8]);//(摘要注释)
					m.setUseMemo(strs[9]);//(用途注释)
					List<Object[]> sone= findCodeByRowNo(strs[10]);
					String companyCode="";
					String companyName="";
					String batchNo="";
					for(int j=0;j<sone.size();j++){
						companyCode=(String) sone.get(j)[0];
						companyName=(String) sone.get(j)[1];
						batchNo=(String) sone.get(j)[2];
					}
					if("sky".equals(strs[10])){
						m.setCompanyCode("00");//(地市编码)
						m.setCompanyName("省公司");//(地市名称)
					}else{
						m.setCompanyCode(companyCode);//(地市编码)
						m.setCompanyName(companyName);//(地市名称)
					}
					m.setCreateDate(new Date());//(创建时间)
					m.setState(0);//(状态)
					m.setIsThe(0);//记录申领次数；
					if("sky".equals(strs[10])){
						m.setBatchNo("SGS"+getStringDatethree(new Date()));//(系统编号)
					}else{
						m.setBatchNo(batchNo+getStringDatethree(new Date()));//(系统编号)
					}
					saveProcessList(m);
				}else{
					return "NO";
				}
				/*System.out.println(strs[8]+"woshi");*/
			}
			br.close();// 关闭文件
			return "OK";
		} catch (FileNotFoundException e) {
			System.out.println("没有找到文件");
			e.printStackTrace();
			return "NO";
		} catch (IOException e) {
			e.printStackTrace();
			System.out.println("文件读取错误。");
			e.printStackTrace();
			return "NO";
		}
	}

	public String balanceState(String json){
		try{
			String jsonString = UrlConnection.responseUTF8("http://10.101.11.238:8080/TAS/service/balanceState",json);
			return jsonString;
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
	}

	public SystemUser querUsers(String mobile) throws ParseException {
		// String hql =
		// "from SystemUser s where s.loginName = ? and s.loginPwd = ? and s.employeeStatus <> -1";
		String hql = "from SystemUser s where s.mobile = ?  and s.employeeStatus = '0' ";

		SystemUser userInfo = (SystemUser) this.getSession().createQuery(hql).setString(0, mobile).uniqueResult();
		if (userInfo != null) {
			// 将部门信息添加到user中
			userInfo.setSystemDept(getDeptListByRowNo(userInfo.getRowNo()));
		}
		return userInfo;
	}

	/**
	 * 根据用户Id获取部门列表
	 *
	 * @param rowNo
	 * @return
	 * @throws ParseException
	 */
	public List<SystemDept> getDeptListByRowNo(Integer rowNo) throws ParseException {
		String sql = "select Department_No,Department_Name,Department_Type_No,Department_Level,Created,Modified,Childs_Count,Area_Code,Department_Order,Mail,Department_Parent_No,Visible,Company_Code from AFR_systemdept where Department_No in(select Department_No from AFR_system_dept_user where RowNo = ?)";
		List<Object[]> deptObjs = this.getSession().createSQLQuery(sql).setInteger(0, rowNo).list();
		List<SystemDept> deptList = new ArrayList<SystemDept>();
		for (Object[] object : deptObjs) {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

			SystemDept dept = new SystemDept();
			dept.setDepartmentNo(Integer.valueOf(object[0].toString()));
			dept.setDepartmentName(object[1].toString());
			// dept.setDepartmentTypeNo(Integer.valueOf(object[2].toString()));
			dept.setDepartmentLevel(Integer.valueOf(object[3].toString()));
			if (object[4] != null) {
				dept.setCreated(sdf.parse(object[4].toString()));
			}
			if (object[5] != null) {

				dept.setModified(sdf.parse(object[5].toString()));
			}
			// dept.setChildsCount(Integer.valueOf(object[6].toString()));
			if (object[7] != null) {
				dept.setAreaCode(object[7].toString());
			}
			dept.setDepartmentOrder(object[8].toString());
			if (object[10] != null)
				dept.setDepartmentParentNo(Integer.valueOf(object[10].toString()));
			dept.setVisible(Integer.valueOf(object[11].toString()));
			if (object[12] != null) {
				dept.setCompanyCode(object[12].toString());
				String sqlCompany = "select * from AFR_systemcompany where Company_Code = ?";
				SystemCompany company = new SystemCompany();// 公司
				Object[] companyObj = (Object[]) this.getSession().createSQLQuery(sqlCompany).setString(0, object[12].toString()).uniqueResult();
				// 往部门中添加公司信息
				company.setCompanyName(companyObj[0].toString());
				company.setCompanyCode(companyObj[1].toString());
				company.setCompanyShortName(companyObj[2].toString());
				dept.setSystemCompany(company);

			}
			deptList.add(dept);

		}
		return deptList;
	}

	/**
	 * 根据登录人名称以及状态查询待办信息
	 *
	 * @param name
	 * @return
	 */
	public PageResponse findByCompanyCode(String name,String money,String companyCode,String tranDate,PageRequest page,SystemUser user,String AmountSorting,String CreateDateSorting) {
		String sql = " SELECT " +
				" * " +
				" FROM " +
				" ( " +
				" ( " +
				" SELECT " +
				" * " +
				" FROM " +
				" MoneyTotal " +
				" WHERE " +
				" userid IS NULL " +
				" AND state = '0' " +
				" AND ID NOT IN ( " +
				" SELECT " +
				" moneyTotal_id " +
				" FROM " +
				" MoneyTotal_Delete_Record " +
				" WHERE " +
				" USER_ID = ' "+user.getRowNo()+" ' " +
				" ) " +
				" ) " +
				" UNION ALL " +
				" ( " +
				" SELECT DISTINCT " +
				" T .* " +
				" FROM " +
				" MONEYTOTAL T " +
				" INNER JOIN TAXPAYER x ON T .batchno = x.batch_no " +
				" WHERE x .STATE = '0' AND T .STATE = '6' " +
				" ) " +
				" ) ";
		if (companyCode.equals("00")){
			sql +=" WHERE companyCode in ('00','01')";
		}else {
			sql +=" WHERE companyCode='"+companyCode+"' ";
		}
		if (name != null && !"".equals(name)&& !"null".equals(name)){  //是否输入账户名称
			sql+=" AND otherName like '%"+name+"%' ";
		} else if (money != null && !"".equals(money)&& !"null".equals(money)){	//是否输入账户金额
			sql+=" AND amount like '"+money+"' ";
		}

		if (tranDate != null && !"".equals(tranDate)&& !"null".equals(tranDate)){	//是否输入时间
			sql+=" AND to_char(tranDate,'yyyy-mm-dd') = '"+tranDate+"' ";
		}
		sql+=" ORDER BY STATE DESC , to_char(CREATEDATE,'yyyy-mm-dd') "+CreateDateSorting+",TO_NUMBER(OVERAMOUNT) "+AmountSorting;
		return getMapNoBy(sql, page);
	}

	/**
	 * 根据登录人名称以及状态查询待办信息
	 * @param name
	 * @return
	 */
	public PageResponse findByUser(String name,String money,String tranDate,Integer rowNo,String type,PageRequest page,String AmountSorting,String CreateDateSorting) {
		String sql = "SELECT * from MoneyTotal where userid='"+rowNo+"' and state in ('1','3','4')";
		if ("2".equals(type)){		//查询还有余额的资金
			sql+=" AND OVERAMOUNT > 0 ";
		}else {						//查询没有余额的资金
			sql+=" AND OVERAMOUNT <= 0 ";
		}
		if(name != null && !"".equals(name)&& !"null".equals(name)){
			sql += " AND (otherName like '%"+name+"%' OR otherName = '"+name+"' )";
		}else if(money != null && !"".equals(money)&& !"null".equals(money)){
			sql += " AND amount like '"+money+"' ";
		}

		if(tranDate != null && !"".equals(tranDate)&& !"null".equals(tranDate)){
			sql+=" AND to_char(tranDate,'yyyy-mm-dd') = '"+tranDate+"' ";
		}
		sql+=" ORDER BY to_char(CREATEDATE,'yyyy-mm-dd') "+CreateDateSorting+",TO_NUMBER(OVERAMOUNT) "+AmountSorting;
		return getMapNoBy(sql, page);
	}

	/**
	 * 查询未审批的工单
	 *
	 * @return
	 */
	public PageResponse getNotApprovedState(String groupCode,String opType,String AmountSorting,String CreateDateSorting,Integer rowNo, PageRequest page){
		String sql ="select DISTINCT m.ID,m.TITLE,bk.creator_no as CREATORID,bk.creator_name as CREATORNAME,m.APPLYAMOUNT"
				+ ",m.GROUPCODE,m.GROUPNAME,m.STATE,bk.gmt_create as CREATEDATE,sa.orderID,m.OPTYPE "
				+ "from MoneyApply m LEFT JOIN BPMS_RISKOFF_PROCESS b on b.BIZ_ID=m.ID "
				+ "LEFT JOIN BPMS_RISKOFF_TASK bk on bk.process_id=b.Process_sign LEFT JOIN SingleAndAttachment sa on m.id=sa.orderID "
				+ "where bk.status=1 and bk.oper_no='"+rowNo+"' ";
		if(groupCode != null && !"".equals(groupCode)&& !"null".equals(groupCode)){
			sql+=" AND m.GROUPCODE = '"+groupCode+"' ";
		}
		if(opType != null && !"".equals(opType)&& !"null".equals(opType)){
			sql+=" AND m.OPTYPE = '"+opType+"' ";
		}
		//sql+=" group by m.ID,m.TITLE,bk.creator_no,bk.creator_name,m.APPLYAMOUNT,m.GROUPCODE,m.GROUPNAME,m.STATE,bk.gmt_create,sa.orderID,m.OPTYPE ";
		sql+=" order by to_char(bk.gmt_create,'yyyy-mm-dd') "+CreateDateSorting+" , TO_NUMBER(m.APPLYAMOUNT) "+AmountSorting;
		return getMapNoBy(sql, page);
	}

	/**
	 * 查询我的所有工单
	 * @return
	 */
	public PageResponse getAllApproved(String groupName,Integer rowNo, PageRequest page){
		String sql ="select * from(select m.ID,m.TITLE,m.CREATORID,m.CREATORNAME,m.APPLYAMOUNT,m.GROUPCODE,m.GROUPNAME,m.STATE,m.CREATEDATE,null as ATTACHMENTID,m.ma_type,m.orderType "
				+ "from MoneyApply m LEFT JOIN BPMS_RISKOFF_PROCESS b on b.BIZ_ID=m.ID "
				+ "LEFT JOIN BPMS_RISKOFF_TASK bk on bk.process_id=b.Process_sign "
				+ "where bk.oper_no='"+rowNo+"' ";
		if(groupName != null && !"".equals(groupName)&& !"null".equals(groupName)){
			sql+=" and m.GROUPNAME like '%"+groupName+"%'";
		}
		sql+=" group by m.ID,m.TITLE,m.CREATORID,m.CREATORNAME,m.APPLYAMOUNT,m.GROUPCODE,m.GROUPNAME,m.STATE,m.CREATEDATE,m.ma_type,m.orderType order by m.CREATEDATE desc)";
		return getMapNoBy(sql, page);
	}

	/**
	 * 查询我的所有工单
	 * @return
	 */
	public PageResponse getApprovedState(String groupName,Integer rowNo, PageRequest page){
		String sql ="select m.ID,m.TITLE,m.CREATORID,m.CREATORNAME,m.APPLYAMOUNT,m.GROUPCODE,m.GROUPNAME,m.STATE,m.CREATEDATE,null as ATTACHMENTID,m.ma_type,m.orderType "
				+ "from MoneyApply m "
				+ "where m.CREATORID='"+rowNo+"'";
		if(groupName != null && !"".equals(groupName)&& !"null".equals(groupName)){
			sql+=" and m.GROUPNAME like '%"+groupName+"%'";
		}
		sql+=" order by m.CREATEDATE desc";
		return getMapNoBy(sql, page);
	}

	/**
	 * @Description TODO 查询推荐集团数据  你认领过的
	 * <AUTHOR>
	 * @param otherAccNumber 银行账户
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/8/9 16:19
	 **/
	public List<Map<String, String>> getMoneyTotalGroup(String otherAccNumber){
		String sql = "SELECT GROUPCODE,GROUPNAME FROM MONEYTOTAL WHERE OTHERACCNUMBER='"+otherAccNumber+"' AND GROUPCODE IS NOT NULL";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * 根据对方账户查询数据库有没有已被认领过的
	 * @param otherAccNumber
	 * @return
	 */
	public List<MoneyTotal> getMoneyTotalTypeUserid(String otherAccNumber){
		String sql = "select * from MoneyTotal where otherAccNumber='"+otherAccNumber+"' and userid is not null";
		return getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).list();
	}

	public List<PreinvApplyDet> getPreinvApplyDet(String contractNo) {
		// TODO Auto-generated method stub
		String sql = "select * from PreinvApplyDet where contrctNo='"+contractNo+"' and commitType='0' and (realRecAmout is null or realRecAmout='0')";
		System.out.println("查询账期信息SQL：====="+sql);
		return getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).list();
	}

	public PreinvApplyDet getPreinvApplyDetByInvNo(String invNo) {
		// TODO Auto-generated method stub
		String sql = "select * from PreinvApplyDet where invNo='"+invNo+"'";
		return (PreinvApplyDet) getSession().createSQLQuery(sql).addEntity(PreinvApplyDet.class).uniqueResult();
	}
	/**
	 *预开票明细更新
	 */
	public PreinvApplyDet updatePreinvApplyDet(PreinvApplyDet pd) {
		Session session = this.getSession();
		try {
			if (pd != null) {
				session.update(pd);
				session.flush();
			}
			return pd;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * 根据角色查询角色下人员信息 PS：修改去重复（afs.employee_name||'('||sd.department_name||')'）
	 *
	 *            角色ID
	 *            登录用户信息
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Map<String, String>> SelectZtreeByUId(String role,String company_name, String county_name) {
		String sql = "select vw.ROWNO,VW.employee_name,VW.BOSSUSERNAME from VW_USERINFO vw "
				+ "inner join system_user_role sur on sur.ROW_NO=vw.ROWNO "
				+ "inner join system_role sr on sr.id=sur.role_id "
				+ "WHERE sr.name='"+role+"' "
				+ "and VW.company_name='"+company_name+"' "
				+ "and VW.county_name='"+county_name+"' "
				//+ "and vw.BOSSUSERNAME is not null "
				+ "GROUP BY VW.ROWNO,VW.employee_name,vw.BOSSUSERNAME";
		return (List<Map<String, String>>) getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * 根据用户编号查询部门信息
	 *
	 * @param rowNo
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Map<String, Object>> findDept(int rowNo) {
		String sql = "select u.rowno," + "u.employee_name," + "u.login_name," + "dtmp.twoDName," + "cy.company_name " + "from afr_systemuser u " + "inner join afr_system_dept_user adu "
				+ "on u.rowno=adu.rowno " + "inner join " + "(select LEVEL lv,dpt.department_no,dpt.department_name," + "(case when substr(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),"
				+ "instr(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),';',1,2)+1,"
				+ "(instr(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),';',1,3)-instr(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),';',1,2))-1) "
				+ "is null then dpt.department_name else substr(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),"
				+ "instr(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),';',1,2)+1,(instr(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),';',1,3)-instr"
				+ "(SYS_CONNECT_BY_PATH(dpt.department_name, ';'),';',1,2))-1)end) " + "twoDName,dpt.company_code from afr_systemdept dpt where dpt.department_no<>1 "
				+ "START WITH dpt.department_parent_no =0  CONNECT BY PRIOR dpt.department_no = dpt.department_parent_no  " + "ORDER SIBLINGS BY dpt.department_name) "
				+ "dtmp on adu.department_no=dtmp.department_no inner join afr_systemcompany cy on cy.company_code=dtmp.company_code where u.rowno=?";
		//System.out.println("sql=" + sql);
		return (List<Map<String, Object>>) getSession().createSQLQuery(sql).setInteger(0, rowNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * 新增资金认领池数据
	 */
	public MoneyApplySubGroup saveMoneyApplySubGroup(MoneyApplySubGroup mas) {
		Session session = this.getSession();
		try {
			if (mas != null) {
				session.save(mas);
				session.flush();
			}
			return mas;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	public MoneyTotal saveMoneyTotal(MoneyTotal moneyTotal) {
		Transaction tx = null;
		Session session =null;
		try {
			if (moneyTotal != null) {
				session = this.getSessionFactory().openSession();
				tx=session.beginTransaction();
				session.save(moneyTotal);
				tx.commit();
			}
			return moneyTotal;
		} catch (Exception e) {
			e.printStackTrace();
			tx.rollback();
			return null;
		}finally {
			session.close();
		}
	}

	public List<MoneyApplySubGroup> getMoneyApplySubGroup(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplySubGroup where moneyApply_id='"+id+"'";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplySubGroup.class).list();
	}

	public List<MoneyApplySubGroup> getMoneyApplySubGroupByBoss_State(String id,String bossState) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplySubGroup where moneyApply_id='"+id+"' and boss_State ='"+bossState+"' ";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplySubGroup.class).list();
	}

	/**
	 * @Description TODO 根据ID删除资金
	 * <AUTHOR>
	 * @param moneyTotal_id	资金ID
	 * @Date 2022/7/7 11:24
	 **/
	public void deleteMoneyTotal(String moneyTotal_id) {
		String sql = "delete from MoneyTotal where id='"+moneyTotal_id+"'";
		getSession().createSQLQuery(sql).executeUpdate();
	}

	/**
	 * @Description TODO 更新B库成员
	 * <AUTHOR>
	 * @Date 2022/7/7 11:25
	 **/
	public BLibMender saveBLibMender(BLibMender bLibMender) {
		Session session = this.getSession();
		try {
			if (bLibMender != null) {
				session.saveOrUpdate(bLibMender);
				session.flush();
			}
			return bLibMender;
		}
		catch (Exception e) {
			e.printStackTrace();
			return null;

		}
	}

	/**
	 * @Description TODO 删除B库历史数据（保留最新批次（批次字段：DIY_CODE）的数据）
	 * <AUTHOR>
	 * @return int	删除数
	 * @Date 2022/11/9 15:54
	 **/
	public int delectBLib(){
		try{
			String diySql = " SELECT DISTINCT DIY_CODE FROM BPMS_BLIB_MENDER  ORDER BY DIY_CODE DESC ";
			List<Map<String, String>> diyList = getSession().createSQLQuery(diySql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
			if (diyList.size()>1){
				String deleteSql = "DELETE BPMS_BLIB_MENDER WHERE DIY_CODE != ? ";
				return getSession().createSQLQuery(deleteSql).setString(0, diyList.get(0).get("DIY_CODE")).executeUpdate();
			}else {
				return 0;
			}
		}catch (Exception e){
			e.printStackTrace();
			return -1;
		}
	}

	public MoneyApplySubGroup updateMoneyApplySubGroup(MoneyApplySubGroup sub) {
		Transaction transaction = null;
		Session session =null;
		try {
			session = this.getSessionFactory().openSession();
			transaction=session.beginTransaction();
			session.update(sub);
			transaction.commit();
			return sub;
		}catch (Exception e){
			e.printStackTrace();
			transaction.rollback();
			return null;
		}finally {
			session.close();
		}
	}


	/**
	 * 冲正调用boss接口推送流程申请信息里的单条账户或者个人信息
	 */
	public String claimOfCorrectionFund(String groupCode, MoneyTotal moneyTotal,List<MoneyApplyDet> moneyApplyDet,SystemUser user) {
		try{
			String url = ESB_URL + "applyForFunds";
			JSONArray jsonList = new JSONArray();
			for (int i=0; i<moneyApplyDet.size();i++) {
				Map<String, Object> object = new HashMap<>();
				object.put("UNIT_ID", groupCode==null?"":Long.parseLong(groupCode));
				object.put("CONTRACT_NO","1".equals(moneyApplyDet.get(i).getOrderType())?"":moneyApplyDet.get(i).getContrctNo());//账户ID
				object.put("OUT_SYS_ACCEPT",moneyApplyDet.get(i).getOldMoneyNo());//明细编号
				object.put("BANK_ACCOUNT",moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber());//资金池的对方账户
				object.put("BANK_ACCOUNT_NAME",moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName());//资金池的对方户名
				object.put("BILL_NOTE","0");//自定义
				object.put("PHONE_NO","2".equals(moneyApplyDet.get(i).getOrderType())?"":moneyApplyDet.get(i).getContrctNo());//个人申请就是电话号码
				if("1".equals(moneyApplyDet.get(i).getUseType())){//1.缴费 2.存送 3.终端 06有价开销售
					object.put("BUSI_TYPE","00");//业务类型（00是缴费，01是终端，02是存送)
					object.put("DELAY_RATE",moneyApplyDet.get(i).getLateFee());//是否减免滞纳金
				}else if("06".equals(moneyApplyDet.get(i).getUseType())){
					object.put("BUSI_TYPE","06");//业务类型（00是缴费，01是终端，02是存送)
					object.put("DELAY_RATE","0");//是否减免滞纳金
				}else if("05".equals(moneyApplyDet.get(i).getUseType())){
					object.put("BUSI_TYPE","05");//业务类型（00是缴费，01是终端，02是存送;05预开票)
					object.put("DELAY_RATE","0");//是否减免滞纳金
				}else if("2".equals(moneyApplyDet.get(i).getUseType())){
					object.put("BUSI_TYPE","02");//业务类型（00是缴费，01是终端，02是存送)
					object.put("DELAY_RATE","0");//是否减免滞纳金
				}else{
					object.put("BUSI_TYPE","01");//业务类型（00是缴费，01是终端，02是存送)
					object.put("DELAY_RATE","0");//是否减免滞纳金
				}
				object.put("CONTRACT_NAME","0");//合同名称
				object.put("PURPOSE","资金冲正");//（资金用途）自定义
				object.put("BUSI_FEE",BigDecimal.valueOf(Long.valueOf(moneyApplyDet.get(i).getAmount())).divide(new BigDecimal(100)).toString());//金额（单个账户的金额）
				object.put("APPLY_LOGIN",user.getBossUserName()==null?"":user.getBossUserName());//申请人（发起人）
				object.put("APPLY_NOTE","资金冲正统一描述");//申请说明
				object.put("PROD_NAME","0");//产品名称
				object.put("PROD_NUM","0");//产品数量
				object.put("BUSI_FLAG","1".equals(moneyApplyDet.get(i).getOrderType())?"P":"G");//标识集团还是个人认领  业务标识 G集团 P个人
				object.put("OP_TYPE","04");//操作类型（使用，冲证） 操作类型 03使用申请  04冲正申请
				object.put("OUT_BACK_ACCEPT",moneyApplyDet.get(i).getMoneyNo()==null?"":moneyApplyDet.get(i).getMoneyNo());//冲正流水
				object.put("PRE_INVOICE_ACCEPT","");//预开票流水
				object.put("RE_UNIT_ID","");
				if ("06".equals(moneyApplyDet.get(i).getUseType())){			//冲正预开票时加上以下字段
					object.put("APPLY_ACCEPT",moneyApplyDet.get(i).getOldMoneyNo());			//使用申请流水号
					object.put("BACK_ACCEPT",moneyApplyDet.get(i).getMoneyNo()==null?"":moneyApplyDet.get(i).getMoneyNo());			//当前冲正操作流水号
					object.put("BACK_FEE",BigDecimal.valueOf(Long.valueOf(moneyApplyDet.get(i).getAmount())).divide(new BigDecimal(100)).toString());				//冲正金额
					object.put("OP_NOTE",user.getBossUserName());				//操作备注
					url = ESB_URL + "washNotUsedCapital";
				}
				jsonList.add(object);
			}
			JSONObject root = new JSONObject();
			JSONObject root_ = new JSONObject();
			JSONObject header = new JSONObject();
			JSONObject routing = new JSONObject();
			JSONObject busiinfo = new JSONObject();
			routing.put("ROUTE_KEY", "15");
			routing.put("ROUTE_VALUE", "11");
			header.put("POOL_ID", "31");
			header.put("DB_ID", "");
			header.put("ENV_ID", "1");
			header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
			header.put("CHANNEL_ID", "155");
			header.put("USERNAME", "zqddxt");
			header.put("PASSWORD", "123456");
			header.put("ENDUSRLOGINID", "");
			header.put("ENDUSRIP", "");
			header.put("ROUTING", routing);
			root_.put("HEADER", header);
			busiinfo.put("LOGIN_NO", user.getBossUserName());
			busiinfo.put("UNIT_ID", groupCode);
			busiinfo.put("BUSI_INFO", jsonList);
			root_.put("BODY", busiinfo);
			root.put("ROOT", root_);
			logger.info("资金认领冲正报文："+root.toString());

//			String resultStr = CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(root));
//			String s = HttpURLConnectClientFactory.analyticParamsByResult(resultStr).toString();
//			return s;

			String jsonString = UrlConnection.responseUTF8(url, root.toString());
			return jsonString.toString();
		}catch(Exception e){
			e.printStackTrace();
			return null;
		}
	}

	public MoneyApplyDet getMoneyApplyDetByNumber(String number) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where moneyNo='"+number+"' ";
		return (MoneyApplyDet)getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).uniqueResult();
	}

	public List<MoneyApplyDet> getMoneyApplyDetByApplyNo(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where applyNo='"+id+"' ORDER BY beginCycle NULLS FIRST";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	public List<MoneyApplyDet> getMoneyApplyDetByBossState(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where applyNo='"+id+"' and bossState='1'";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	public List<MoneyApplyDet> getMoneyApplyDetByApplyNoTwo(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where applyNo='"+id+"' and state = '5'";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	public MoneyApplyDet getMoneyApplyDetById(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where id='"+id+"'";
		return (MoneyApplyDet)getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).uniqueResult();
	}

	public MoneyApply getMoneyApplyByApplyNo(String applyNo) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApply where applyNo=?";
		return (MoneyApply)getSession().createSQLQuery(sql).addEntity(MoneyApply.class).setString(0, applyNo).uniqueResult();
	}

	public List<MoneyApplyDet> getMoneyApplyDetbystate(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from MoneyApplyDet where applyNo='"+id+"' and state ='5'";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}
	/**
	 * 查询明细冲正
	 * @param id
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Bpms_riskoff_task> getPublicEntityTask(String id){
		String sql = "select * from Bpms_riskoff_task where process_id=? order by gmt_create desc";
		List<Bpms_riskoff_task> pet =getSession().createSQLQuery(sql)
				.addEntity(Bpms_riskoff_task.class).setString(0,id).list();
		return pet;
	}

	/**
	 * @Description TODO 根据账户号码查询有价卡信息
	 * <AUTHOR>
	 * @param invContractNo	账户号码
	 * @return java.util.List<com.xinxinsoft.entity.PreinvApply.ValuableCardDet>
	 * @Date 2023/6/8 15:15
	 **/
	public List<ValuableCardDet> getValuableCarCompare(String invContractNo) {
		String sql = " SELECT * FROM VALUABLECARDDET WHERE INV_CONTRACT_NO = ? AND BOSS_STATE= '0' AND TO_NUMBER(MONEYPAY_PRICE) < TO_NUMBER(ORDER_PRICE) ";
		return getSession().createSQLQuery(sql).addEntity(ValuableCardDet.class).setString(0,invContractNo).list();
	}

	/**
	 * @Description TODO 根据账户号码查询第一条有价卡信息
	 * <AUTHOR>
	 * @param invContractNo	账户号码
	 * @return java.util.List<com.xinxinsoft.entity.PreinvApply.ValuableCardDet>
	 * @Date 2023/6/8 15:15
	 **/
	public ValuableCardDet getValuableCarCompareByDate(String invContractNo) {
		String sql = " SELECT * FROM ( SELECT * FROM VALUABLECARDDET WHERE INV_CONTRACT_NO = ? AND BOSS_STATE = '0' AND TO_NUMBER( MONEYPAY_PRICE ) < TO_NUMBER( ORDER_PRICE )  ORDER BY INV_NO) WHERE ROWNUM = 1 ";
		return (ValuableCardDet) getSession().createSQLQuery(sql).addEntity(ValuableCardDet.class).setString(0, invContractNo).uniqueResult();
	}

	/**
	 * @Description TODO 根据CMIOT物联网账户查询预开票记录
	 * <AUTHOR>
	 * @param contractNo	CMIOT物联网账户
	 * @return java.util.List<com.xinxinsoft.entity.PreinvApply.InternetOfThingsDet>
	 * @Date 2023/6/8 15:17
	 **/
	public List<Map<String,String>> queryInternetOfThingsDetList(String contractNo){
		String sql = " select a1.UUID,a1.INV_NO,a1.VALIDBILLCYC,a1.ACCT_ID,a1.ACCT_NAME,a1.include_tax_amt,a1.collection_tax_amt from INTERNETOFTHINGSDET a1 inner join PREINVAPPLY a2 on a1.apply_no = a2.BATCHNO where a1.ACCT_ID = ? and a2.OPRTYPE = '0' and a2.STARTSTATE = '1' AND TO_NUMBER(a1.include_tax_amt) > TO_NUMBER(a1.collection_tax_amt) ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0,contractNo).list();

	}

	/**
	 * @Description TODO 根据账户和预开票流水查询物联网预开票信息
	 * <AUTHOR>
	 * @param uuid	预开票流水
	 * @return com.xinxinsoft.entity.PreinvApply.InternetOfThingsDet
	 * @Date 2023/6/9 15:50
	 **/
	public InternetOfThingsDet queryInternetOfThingsDet(String uuid){
		String sql = " select a1.* from INTERNETOFTHINGSDET a1 inner join PREINVAPPLY a2 on a1.apply_no = a2.BATCHNO where a2.OPRTYPE = '0' and a2.STARTSTATE = '1' and a1.UUID = ? ";
		return (InternetOfThingsDet) getSession().createSQLQuery(sql).addEntity(InternetOfThingsDet.class).setString(0, uuid).uniqueResult();
	}

	/**
	 * @Description TODO 修改物联网预开票信息
	 * <AUTHOR>
	 * @param internetOfThingsDet 物联网预开票对象
	 * @return com.xinxinsoft.entity.PreinvApply.InternetOfThingsDet
	 * @Date 2023/6/9 15:50
	 **/
	public InternetOfThingsDet updateInternetOfThingsDet(InternetOfThingsDet internetOfThingsDet) {
		try {
			Assert.notNull(internetOfThingsDet); //判断是否为空
			Object merge = this.getSession().merge(internetOfThingsDet);
			return internetOfThingsDet;
		} catch (Exception e) {
			logger.error(e.getMessage(), e);
			return null;
		}
	};


		/**
         * @Description TODO 根据预开票流水查询缴费记录
         * <AUTHOR>
         * @param invNo 预开票流水
         * @return java.util.List<com.xinxinsoft.entity.claimForFunds.MoneyApplyDet>
         * @Date 2023/6/8 15:16
         **/
	public List<MoneyApplyDet> getMoneyApplyByinvNo(String invNo) {
		String sql = "select * from MoneyApplyDet where invNo = ? ";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).setString(0, invNo).list();
	}

	/**
	 * @author: liyang
	 * @date: 2021/3/4 14:24
	 * @Version: 1.0
	 * @param:
	 * @return:
	 * @Description: TODO 资金认领冲正改造,app端查询所有使用的明细信息
	 */
	public List<MoneyApplyDet> getMoneyApplyDetlistReform(String serialNo){
		String sql ="select * from MoneyApplyDet where serialNo='"+serialNo+"' and opType='1' and state='1' and oldMoneyNo is null order by createDate desc";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	/**
	 * @Description: 根据资金流水和集团查询所有可冲正明细
	 * @Param: [serialNo, unit]
	 * @return: java.util.List<com.xinxinsoft.entity.claimForFunds.MoneyApplyDet>
	 * @Author: TX
	 * @Date: 2021/11/24 18:40
	 */
	public List<MoneyApplyDet> getMoneyApplyDetlistReformTwo(String serialNo,String unit){
		String sql ="select * from MoneyApplyDet where serialNo='"+serialNo+"' and groupcode='"+unit+"' and state='1' and bossState='0' and parentId is null  and oldMoneyNo is null order by createDate desc";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	/**
	 * @Description: 根据资金流水和集团查询所有使用明细(审批中,已完成,冲正中)
	 * @Param: [serialNo, unit]
	 * @return: java.util.List<com.xinxinsoft.entity.claimForFunds.MoneyApplyDet>
	 * @Author: TX
	 * @Date: 2021/11/24 18:40
	 */
	public List<MoneyApplyDet> getImpactMoneyApplyDetlist(String serialNo,String unit){
		String sql ="select * from MoneyApplyDet where serialNo= ? and groupcode= ? and state IN (-1,1,2,5) and parentId is null order by state,createDate desc";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).setString(0, serialNo).setString(1, unit).list();
	}

	public List<Map<String,String>> inquireAboutTheFundPaymentHistory(String phoneNumber,String paymentSlippage){
		String sql = " select d.CONTRCTNO,d.PAYMENTACCEPT,d.AMOUNT,to_char(d.PUSHDATE,'yyyy-MM-dd HH24:mi:ss') as PUSHDATE,v.EMPLOYEE_NAME as PUSHUSER " +
				" from MoneyApplyDet d left join VW_USERINFO v on d.CREATORID = v.ROWNO " +
				"where d.STATE = '1' and d.PARENTID is null and d.PAYMENTACCEPT is not null and v.ISMAINDPT = 'true' and d.CONTRCTNO = ? ";
		if (paymentSlippage!=null && !"".equals(paymentSlippage)){
			sql += " and d.PAYMENTACCEPT = '"+paymentSlippage+"' ";
		}
		sql += " order by d.PUSHDATE desc ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0,phoneNumber).list();
	}

	/**
	 * @Description: 根据资金流水号和集团编号查询明细信息
	 * @Param: [serialNo]
	 * @return: java.util.List<com.xinxinsoft.entity.claimForFunds.MoneyApplyDet>
	 * @Author: TX
	 * @Date: 2021/11/25 9:48
	 */
	public List<Map<String,String>> getAllMoneyApplyDet(String serialNo,String unit,String type){
		String sql ="SELECT MONEYNO,APPLYNO,SERIALNO,OPTYPE,ORDERTYPE,CONTRCTNO,USETYPE,AMOUNT,LATEFEEMONEY,LATEFEE,CREATEDATE,STATE,OLDMONEYNO,PHONENO,INVNO,BOSSSTATE,BOSSMSG FROM MONEYAPPLYDET where serialNo='"+serialNo+"' and groupcode='"+unit+"' ";
		if (type.equals("1")){	//查询已完成并且推送成功的明细
			sql+=" AND STATE = '1' AND BOSSSTATE = '0'";
		}else if (type.equals("2")){	//查询所有使用工单
			sql+=" AND parentId IS NULL OR parentId = ''";
		}else if (type.equals("3")){	//查询已完成并且推送成功的使用明细
			sql+=" AND STATE = '1' AND BOSSSTATE = '0' AND parentId IS NULL OR parentId = ''";
		}else if (type.equals("4")){	//查询已完成并且推送成功的使用明细(可冲正的)
			sql+=" AND STATE = '1' AND BOSSSTATE = '0' AND parentId IS NULL OR parentId = '' AND OLDMONEYNO IS NULL OR OLDMONEYNO = ''";
		}else {
			sql +=" AND STATE IN (-1,1,5) AND parentId IS NULL OR parentId = '' ";
		}
		sql+=" ORDER BY CREATEDATE DESC";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description: app根据用户查询工单信息
	 * @Param: [systemUser, opType, AmountSorting, CreateDateSorting, type, page]
	 * @return: com.xinxinsoft.utils.page.PageResponse
	 * @Author: TX
	 * @Date: 2021/11/26 19:55
	 */
	public PageResponse getAllMoneyApply(SystemUser systemUser,String opType,String AmountSorting,String CreateDateSorting,String type,String groupCode,PageRequest page){
		String sql = "SELECT DISTINCT m.\"ID\",m.APPLYNO,m.MONEYTOTAL_ID,m.SERIALNO,m.GROUPCODE,m.GROUPNAME,m.TITLE,m.APPLYMEMO,m.APPLYAMOUNT,m.OPTYPE,m.CREATEDATE,m.CREATORNAME,m.STATE FROM MONEYAPPLY m " +
				" INNER JOIN BPMS_RISKOFF_PROCESS p on m.\"ID\" = p.BIZ_ID " +
				" INNER JOIN BPMS_RISKOFF_TASK t ON p.PROCESS_SIGN = t.PROCESS_ID WHERE 1=1 ";
		if (type.equals("1")){				//我经手的
			sql+=" AND m.CREATORID != '"+systemUser.getRowNo()+"' AND t.OPER_NO = '"+systemUser.getRowNo()+"' ";
		}else if (type.equals("2")){		//我初建的( 审批中 被驳回  部分提交成功 状态)
			sql+=" AND m.CREATORID = '"+systemUser.getRowNo()+"' AND m.STATE in (1,2,3) ";
		}else if (type.equals("3")){		//我初建的( 已完成 已作废 状态)
			sql+=" AND m.CREATORID = '"+systemUser.getRowNo()+"' AND m.STATE in (0,-1) ";
		}else {
			sql+=" AND m.CREATORID = '"+systemUser.getRowNo()+"' ";
		}
		if (opType!=null&&!"".equals(opType)){
			sql += " AND m.OPTYPE = '"+opType+"' ";
		}
		if (groupCode!=null&&!"".equals(groupCode)){
			sql += " AND m.GROUPCODE = '"+groupCode+"' ";
		}
		sql+=" ORDER BY to_char(m.CREATEDATE,'yyyy-mm-dd') "+CreateDateSorting+" ,TO_NUMBER(m.APPLYAMOUNT)"+AmountSorting;
		return getMapNoBy(sql, page);
	}

	/**
	 * @Description :根据用户编号和查询条件查询用户工单信息
	 * <AUTHOR>
	 * @param page: 分组对象
	 * @param type: 查询类型
	 * @param userRow: 处理用户编号
	 * @param applyNo: 工单编号
	 * @param groupCode: 集团编号
	 * @param opType: 工单类型
	 * @param orderState: 工单状态
	 * @param stateCreatorDate: 工单创建开始时间
	 * @param endCreatorDate: 工单创建结束时间
	 * @return: com.xinxinsoft.utils.page.LayuiPage
	 * @Date 2022/2/10 11:06
	 */
	public LayuiPage getMoneyApplyAllList(LayuiPage page,String type,String userRow,String applyNo,String groupCode,String opType,String orderState,String stateCreatorDate,String endCreatorDate,String serialNo,String groupName){
		String sql = " select DISTINCT m.ID,m.APPLYNO,m.TITLE,m.APPLYMEMO,m.APPLYAMOUNT,m.CREATORID,m.CREATORNAME,m.CREATEDATE,m.STATE,m.ORDERTYPE,m.GROUPCODE,m.GROUPNAME,m.OPTYPE,m.SERIALNO,m.APPROVALNUMBER,m.MA_TYPE,m.MONEYTOTAL_ID,m.SUBGROUP_AMOUNT,m.BEFORE_APPLYNO,m.LATEFEEMONEY from MONEYAPPLY m ";
		if ("0".equals(type)){		//查询待办
			sql=" SELECT DISTINCT m.ID,m.APPLYNO,m.TITLE,m.APPLYMEMO,m.APPLYAMOUNT,m.CREATORID,m.CREATORNAME,m.CREATEDATE,m.STATE,m.ORDERTYPE,m.GROUPCODE,m.GROUPNAME,m.OPTYPE,m.SERIALNO,m.APPROVALNUMBER,m.MA_TYPE,m.MONEYTOTAL_ID,m.SUBGROUP_AMOUNT,m.BEFORE_APPLYNO,m.LATEFEEMONEY,tak.url,tak.WAITID from WAITTASK tak INNER JOIN MONEYAPPLY m ON tak.TASKID = m.ID " +
					" WHERE tak.CODE = 'MONEYTOTAL' AND tak.STATE = '0' AND tak.HANDLEUSERID = '"+userRow+"' ";
		}else if ("1".equals(type)){	//我创建审批中的工单(审批中,被退回,部分推送成功)
			sql+=" WHERE m.CREATORID = '"+userRow+"' AND m.STATE = -2 ";
		}else if ("2".equals(type)){	//我创建审批中的工单(审批中,被退回,部分推送成功)
			sql+=" WHERE m.CREATORID = '"+userRow+"' AND m.STATE IN (1,2,3)";
		}else if ("3".equals(type)){	//我经手的工单
			sql+=" INNER JOIN BPMS_RISKOFF_PROCESS p ON m.ID = p.BIZ_ID " +
					"INNER JOIN BPMS_RISKOFF_TASK t ON p.PROCESS_SIGN = t.PROCESS_ID " +
					"WHERE m.CREATORID != '"+userRow+"' AND t.OPER_NO = '"+userRow+"' ";
		}else if ("4".equals(type)){	//我创建已完成的工单(完成,作废)
			sql+=" WHERE m.CREATORID = '"+userRow+"' AND m.STATE NOT IN (1,2,3)";
		}else {
			sql+=" WHERE 1=1 ";
		}
		if (applyNo!=null && !"".equals(applyNo)){
			sql += " AND m.APPLYNO = '"+applyNo+"' ";
		}
		if (serialNo!=null && !"".equals(serialNo)){
			sql += " AND m.SERIALNO = '"+serialNo+"' ";
		}
		if (groupCode!=null && !"".equals(groupCode)){
			sql += " AND m.GROUPCODE = '"+groupCode+"' ";
		}
		if (groupName!=null && !"".equals(groupName)){
			sql += " AND m.GROUPNAME like '%"+groupName+"%' ";
		}
		if (opType!=null && !"".equals(opType)){
			sql += " AND m.OPTYPE = '"+opType+"' ";
		}
		if (orderState!=null && !"".equals(orderState)){
			sql += " AND m.STATE = '"+orderState+"' ";
		}
		if (stateCreatorDate != null && !"".equals(stateCreatorDate)) {
			sql += " AND m.CREATEDATE >= TO_DATE('" + stateCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		if (endCreatorDate != null && !"".equals(endCreatorDate)) {
			sql += " AND m.CREATEDATE <= TO_DATE('" + endCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		sql+=" ORDER BY m.CREATEDATE DESC";
		page.setCount(getCount("select count(0) from (" + sql + ")"));
		if (page.getCount() > 0) {
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}

	/**
	 * @author: liyang
	 * @date: 2021/3/4 14:24
	 * @Version: 1.0
	 * @param:
	 * @return:
	 * @Description: TODO 资金认领冲正改造,app端查询所有使用的明细信息
	 */
	public List<MoneyApplyDet> getMoneyApplyDetlistInReform(String serialNo){
		String sql ="select * from MoneyApplyDet where serialNo='"+serialNo+"' and opType='2' and state='5'";
		return getSession().createSQLQuery(sql).addEntity(MoneyApplyDet.class).list();
	}

	public List<MoneyApply> getMoneyApplyList(String[] detArray){
		List<MoneyApply> list = new ArrayList<>();
		for(String number:detArray){
			String moneyApplyDetSql ="select * from MoneyApplyDet where moneyNo='"+number+"'";
			MoneyApplyDet moneyApplyDet= (MoneyApplyDet)getSession().createSQLQuery(moneyApplyDetSql).addEntity(MoneyApplyDet.class).list().get(0);
			if(moneyApplyDet!=null){
				String moneyApplySql ="select * from MoneyApply where applyNo='"+moneyApplyDet.getApplyNo()+"'";
				MoneyApply moneyApply= (MoneyApply)getSession().createSQLQuery(moneyApplySql).addEntity(MoneyApply.class).uniqueResult();
				if(moneyApply!=null){
					list.add(moneyApply);
				}
			}
		}
		return list;
	}

	/**
	 * @author: tx
	 * @date: 2021/6/8 14:2
	 * @param:	serialNo 流水号
	 * @return:	List<MoneyApply> 集合
	 * @Description: TODO 资金认领查询资金下所有工单
	 */
	public List<MoneyApply> GetMoneyApplyListBySerialNo(String serialNo){
		String sql ="SELECT * FROM MONEYAPPLY WHERE SERIALNO = ? AND opType='1' ORDER BY CREATEDATE DESC";
		return getSession().createSQLQuery(sql).addEntity(MoneyApply.class).setString(0, serialNo).list();
	}

	public TransferCitiesData getTransferCitiesData(String code, String dangqianrenwu){
		String sqlone = "select * from TransferCitiesData where citiesCode =? and nodeName=? and processNmae='claimForFundsTwo' ";
		TransferCitiesData transferCitiesData = (TransferCitiesData)this.getSession().createSQLQuery(sqlone).addEntity(TransferCitiesData.class).setString(0, code).setString(1, dangqianrenwu).uniqueResult();
		return transferCitiesData;
	}
	public LateFeeMoneyData getLateFeeMoneyData(String code, String dangqianrenwu){
		String sqlone = "select * from LateFeeMoneyData where citiesCode =? and nodeName=? and processNmae='claimForFundsTwo' ";
		LateFeeMoneyData lateFeeMoneyData = (LateFeeMoneyData)this.getSession().createSQLQuery(sqlone).addEntity(LateFeeMoneyData.class).setString(0, code).setString(1, dangqianrenwu).uniqueResult();
		return lateFeeMoneyData;
	}

	/**
	 * 根据用户编号获取所属地市名称
	 * @param ROWNO
	 * @return
	 */
	public List<Map<String,Object>> getCountyByUserID(String ROWNO){
		String sql = "SELECT * from VW_USERINFO WHERE ROWNO=? ORDER BY ISMAINDPT DESC ";
		return getSession().createSQLQuery(sql).setString(0,ROWNO).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * 查询虚拟账户
	 * @param ROWNO
	 * @return
	 */
	public List<Map<String,Object>> getVirtualAccount(String number){
		String sql = "SELECT * from VIRTUAL_ACCOUNT WHERE ACCOUNT_NUMBER='"+number+"' AND STATE='1'";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * 查询账户和集团关联关系
	 * @param ROWNO
	 * @return
	 */
	public List<Map<String,Object>> getGroupContractno(String number){
		String sql = "SELECT * from GROUP_CONTRACTNO WHERE OPPOSITE_ACCOUNT='"+number+"' AND STATE='1'";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	public SystemUser querUsersByBossNo(String bossNo){
		// String hql =
		// "from SystemUser s where s.loginName = ? and s.loginPwd = ? and s.employeeStatus <> -1";
		try {
			String hql = "from SystemUser s where s.bossUserName = ?  and s.employeeStatus = '0' ";

			SystemUser userInfo = (SystemUser) this.getSession().createQuery(hql).setString(0, bossNo).uniqueResult();
			if (userInfo != null) {
				// 将部门信息添加到user中
				userInfo.setSystemDept(getDeptListByRowNo(userInfo.getRowNo()));
			}
			return userInfo;
		}catch (Exception e){
			e.printStackTrace();
			return null;
		}
	}

	public List<Object[]> findCodeByCompanyCode(String groupid) {
		String sql = "select COMPANY_CODE,COMPANY_NAME,COMPANY_IBM from AFR_SYSTEMCOMPANY WHERE COMPANY_CODE=?";
		return getSession().createSQLQuery(sql).setString(0, groupid).list();
	}

	/**
	 * 根据用户id查询权限信息
	 *
	 * @param rowNo
	 * @return
	 */
	public List<Object> findByRowNo(int rowNo) {

		String hql = "select ROLE_ID from SYSTEM_USER_ROLE where row_no=?";
		List<Object> list = getSession().createSQLQuery(hql).setInteger(0, rowNo).list();
		return list;
	}

	/**
	 * @Description TODO
	 * <AUTHOR>
	 * @param page					分页对象
	 * @param SystemUser			当前登陆用户
	 * @param flag					是否为管理员
	 * @param groupCode				集团编号
	 * @param groupName				集团名称
	 * @param serialNo				资金唯一流水
	 * @param postDate				查询类型（0：明细列表，1：工单列表，2：资金列表）
	 * @param stateCreatorDate		创建时间范围：开始时间
	 * @param endCreatorDate		创建时间范围：结束时间
	 * @param countSelect			地市或区县
	 * @param otherName				账户名称
	 * @param otherAccNumber		账户号码
	 * @param userName				认领人员名称
	 * @param applyNo				工单编号
	 * @param opType				工单操作类型
	 * @param orderState			工单类型
	 * @param useType				使用类型
	 * @param contrctNo				账户号码
	 * @return com.xinxinsoft.utils.page.LayuiPage
	 * @Date 2023/1/10 10:23
	 **/
	public LayuiPage queryMoneyTotalList(LayuiPage page,Map<String,String> SystemUser,boolean flag,String groupCode, String groupName,String serialNo,String postDate,
										 String stateCreatorDate,String endCreatorDate,String countSelect,String otherName,String otherAccNumber,String userName,String applyNo,
										 String opType,String orderState,String useType,String contrctNo){
		String sql = "";
		if ("0".equals(postDate)){
			sql = " SELECT d.MONEYNO,d.APPLYNO,d.SERIALNO,d.GROUPCODE,d.GROUPNAME,d.STATE,d.ORDERTYPE,d.CONTRCTNO,d.USETYPE,d.AMOUNT,v.DEPARTMENT_NAME,v.EMPLOYEE_NAME,v.COMPANY_NAME," +
					" v.COUNTY_NAME,v.BOSSUSERNAME,d.PARENTID,d.CREATEDATE FROM MONEYAPPLYDET d INNER JOIN VW_USERINFO v ON d.CREATORID = v.ROWNO  ";
		}else if ("1".equals(postDate)){
			sql = " select DISTINCT m.ID,m.APPLYNO,m.TITLE,m.APPLYMEMO,m.APPLYAMOUNT,m.CREATORID,m.CREATORNAME,m.CREATEDATE,m.STATE,m.ORDERTYPE,m.GROUPCODE, " +
					" m.GROUPNAME,m.OPTYPE,m.SERIALNO,m.APPROVALNUMBER,m.MA_TYPE,m.MONEYTOTAL_ID,m.SUBGROUP_AMOUNT,m.BEFORE_APPLYNO,m.LATEFEEMONEY ,v.DEPARTMENT_NAME,v.EMPLOYEE_NAME" +
					",v.COMPANY_NAME,v.COUNTY_NAME,v.BOSSUSERNAME from MONEYAPPLY m INNER JOIN VW_USERINFO v ON m.CREATORID = v.ROWNO";
		}else {
			sql = " SELECT t.ID,t.SERIALNO,t.OTHERACCNUMBER,t.OTHERNAME,t.GROUPCODE,t.GROUPNAME,t.AMOUNT,t.USEAMOUNT,t.OVERAMOUNT,t.TRANDATE AS CREATEDATE,t.PUS_DATE," +
					"v.EMPLOYEE_NAME,v.COMPANY_NAME,v.COUNTY_NAME,v.BOSSUSERNAME  FROM MONEYTOTAL t  INNER JOIN VW_USERINFO v ON t.USERID = v.ROWNO ";
		}
		if (flag){		//订单管理员
			if (SystemUser.get("COMPANY_NAME").equals("省公司")) {
				//省公司
				sql+=" WHERE v.ISMAINDPT = 'true' ";
				if (countSelect!=null&&!"".equals(countSelect)){
					sql+=" AND v.COMPANY_CODE = '"+countSelect+"' ";
				}
			} else if (!(SystemUser.get("COUNTY_NAME")).contains("分公司") || (SystemUser.get("COUNTY_NAME")).contains("直属")) {
				//地市公司
				sql+=" WHERE v.ISMAINDPT = 'true' AND v.COMPANY_CODE = '"+String.valueOf(SystemUser.get("COMPANY_CODE"))+"' ";
				if (countSelect!=null&&!"".equals(countSelect)){
					sql+=" AND v.COUNTY_NO = '"+countSelect+"' ";
				}
			}else {
				sql+=" WHERE v.ISMAINDPT = 'true' AND v.COUNTY_NO = '"+String.valueOf(SystemUser.get("COUNTY_NO"))+"' ";
			}
		}else {		//非订单管理员
			sql+=" WHERE v.ISMAINDPT = 'true' AND v.ROWNO = '"+String.valueOf(SystemUser.get("ROWNO"))+"' ";
		}
		if (groupCode!=null&&!"".equals(groupCode)){
			sql+=" AND GROUPCODE LIKE '"+groupCode+"%' ";
		}
		if (groupName!=null&&!"".equals(groupName)){
			sql+=" AND GROUPNAME LIKE '%"+groupName+"%' ";
		}
		if (serialNo!=null&&!"".equals(serialNo)){
			sql+=" AND SERIALNO LIKE '"+serialNo+"%' ";
		}


		if (otherName!=null&&!"".equals(otherName)){
			sql+=" AND t.OTHERNAME LIKE '%"+otherName+"%' ";
		}
		if (otherAccNumber!=null&&!"".equals(otherAccNumber)){
			sql+=" AND t.OTHERACCNUMBER LIKE '"+otherAccNumber+"%' ";
		}
		if (userName!=null&&!"".equals(userName)){
			sql+=" AND V.EMPLOYEE_NAME LIKE '%"+userName+"%' ";
		}


		if (applyNo!=null&&!"".equals(applyNo)){
			sql+=" AND m.APPLYNO LIKE '"+applyNo+"%' ";
		}
		if (opType!=null&&!"".equals(opType)){
			sql+=" AND m.OPTYPE = '"+opType+"' ";
		}
		if (orderState!=null&&!"".equals(orderState)){
			sql+=" AND m.STATE = '"+orderState+"' ";
		}

		if (useType!=null&&!"".equals(useType)){
			sql+=" AND d.USETYPE = '"+useType+"' ";
		}
		if (contrctNo!=null&&!"".equals(contrctNo)){
			sql+=" AND d.CONTRCTNO LIKE '"+contrctNo+"%' ";
		}


		if (stateCreatorDate != null && !"".equals(stateCreatorDate)) {
			sql += " AND CREATEDATE >= TO_DATE('" + stateCreatorDate + " 00:00:00" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		if (endCreatorDate != null && !"".equals(endCreatorDate)) {
			sql += " AND CREATEDATE <= TO_DATE('" + endCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		sql+=" ORDER BY CREATEDATE DESC ";
		page.setCount(Long.parseLong(String.valueOf(this.getSession().createSQLQuery("select count(0) from ( "+sql.toString()+" )").uniqueResult())));
		//判断是否有数据
		if(page.getCount() > 0) {
			Query queryLists = getSession().createSQLQuery(sql.toString())
					.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
					.setFirstResult(page.getPageNo())
					.setMaxResults(page.getPageSize());
			page.setData(queryLists.list());
		}
		page.setCode(0);
		return  page;
	}

	/**
	 * @Description: 资金认领导出数据查询
	 * @Param: [SystemUser, flag, otherName, otherAccNumber, groupCode, groupName, serialNo, userName, countSelect, stateCreatorDate, endCreatorDate]
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 * @Author: TX
	 * @Date: 2022/1/5 16:10
	 */
	public List<Map<String,Object>> queryMoneyTotalMap(Map<String,String> SystemUser,boolean flag,String groupCode, String groupName,String serialNo,String postDate,
													   String stateCreatorDate,String endCreatorDate,String countSelect,String otherName,String otherAccNumber,String userName,String applyNo,
													   String opType,String orderState,String useType,String contrctNo){
		String sql = "";
		if ("0".equals(postDate)){
			sql = " SELECT d.MONEYNO,d.APPLYNO,d.SERIALNO,d.GROUPCODE,d.GROUPNAME,d.STATE,d.ORDERTYPE,d.CONTRCTNO,d.USETYPE,d.AMOUNT,v.DEPARTMENT_NAME,v.EMPLOYEE_NAME,v.COMPANY_NAME," +
					" v.COUNTY_NAME,v.BOSSUSERNAME,d.PARENTID,d.CREATEDATE FROM MONEYAPPLYDET d INNER JOIN VW_USERINFO v ON d.CREATORID = v.ROWNO  ";
		}else if ("1".equals(postDate)){
			sql = " select DISTINCT m.ID,m.APPLYNO,m.TITLE,m.APPLYMEMO,m.APPLYAMOUNT,m.CREATORID,m.CREATORNAME,m.CREATEDATE,m.STATE,m.ORDERTYPE,m.GROUPCODE, " +
					" m.GROUPNAME,m.OPTYPE,m.SERIALNO,m.APPROVALNUMBER,m.MA_TYPE,m.MONEYTOTAL_ID,m.SUBGROUP_AMOUNT,m.BEFORE_APPLYNO,m.LATEFEEMONEY ,v.DEPARTMENT_NAME,v.EMPLOYEE_NAME" +
					",v.COMPANY_NAME,v.COUNTY_NAME,v.BOSSUSERNAME from MONEYAPPLY m INNER JOIN VW_USERINFO v ON m.CREATORID = v.ROWNO";
		}else {
			sql = " SELECT t.ID,t.SERIALNO,t.OTHERACCNUMBER,t.OTHERNAME,t.GROUPCODE,t.GROUPNAME,t.AMOUNT,t.USEAMOUNT,t.OVERAMOUNT,t.TRANDATE AS CREATEDATE,t.PUS_DATE," +
					"v.EMPLOYEE_NAME,v.COMPANY_NAME,v.COUNTY_NAME,v.BOSSUSERNAME  FROM MONEYTOTAL t  INNER JOIN VW_USERINFO v ON t.USERID = v.ROWNO ";
		}
		if (flag){		//订单管理员
			if (SystemUser.get("COMPANY_NAME").equals("省公司")) {
				//省公司
				sql+=" WHERE v.ISMAINDPT = 'true' ";
				if (countSelect!=null&&!"".equals(countSelect)){
					sql+=" AND v.COMPANY_CODE = '"+countSelect+"' ";
				}
			} else if (!(SystemUser.get("COUNTY_NAME")).contains("分公司") || (SystemUser.get("COUNTY_NAME")).contains("直属")) {
				//地市公司
				sql+=" WHERE v.ISMAINDPT = 'true' AND v.COMPANY_CODE = '"+String.valueOf(SystemUser.get("COMPANY_CODE"))+"' ";
				if (countSelect!=null&&!"".equals(countSelect)){
					sql+=" AND v.COUNTY_NO = '"+countSelect+"' ";
				}
			}else {
				sql+=" WHERE v.ISMAINDPT = 'true' AND v.COUNTY_NO = '"+String.valueOf(SystemUser.get("COUNTY_NO"))+"' ";
			}
		}else {		//非订单管理员
			sql+=" WHERE v.ISMAINDPT = 'true' AND v.ROWNO = '"+String.valueOf(SystemUser.get("ROWNO"))+"' ";
		}
		if (groupCode!=null&&!"".equals(groupCode)){
			sql+=" AND GROUPCODE LIKE '"+groupCode+"%' ";
		}
		if (groupName!=null&&!"".equals(groupName)){
			sql+=" AND GROUPNAME LIKE '%"+groupName+"%' ";
		}
		if (serialNo!=null&&!"".equals(serialNo)){
			sql+=" AND SERIALNO LIKE '"+serialNo+"%' ";
		}


		if (otherName!=null&&!"".equals(otherName)){
			sql+=" AND t.OTHERNAME LIKE '%"+otherName+"%' ";
		}
		if (otherAccNumber!=null&&!"".equals(otherAccNumber)){
			sql+=" AND t.OTHERACCNUMBER LIKE '"+otherAccNumber+"%' ";
		}
		if (userName!=null&&!"".equals(userName)){
			sql+=" AND V.EMPLOYEE_NAME LIKE '%"+userName+"%' ";
		}


		if (applyNo!=null&&!"".equals(applyNo)){
			sql+=" AND m.APPLYNO LIKE '"+applyNo+"%' ";
		}
		if (opType!=null&&!"".equals(opType)){
			sql+=" AND m.OPTYPE = '"+opType+"' ";
		}
		if (orderState!=null&&!"".equals(orderState)){
			sql+=" AND m.STATE = '"+orderState+"' ";
		}

		if (useType!=null&&!"".equals(useType)){
			sql+=" AND d.USETYPE = '"+useType+"' ";
		}
		if (contrctNo!=null&&!"".equals(contrctNo)){
			sql+=" AND d.CONTRCTNO LIKE '"+contrctNo+"%' ";
		}


		if (stateCreatorDate != null && !"".equals(stateCreatorDate)) {
			sql += " AND CREATEDATE >= TO_DATE('" + stateCreatorDate + " 00:00:00" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		if (endCreatorDate != null && !"".equals(endCreatorDate)) {
			sql += " AND CREATEDATE <= TO_DATE('" + endCreatorDate + " 23:59:59" + "','yyyy-MM-dd HH24:mi:ss') ";
		}
		sql+=" ORDER BY CREATEDATE DESC ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description: 资金认领列表导出数据
	 * @return: void
	 * @Author: TX
	 * @Date: 2022/1/5 16:06
	 */
	public void MoneyTotalListExcel(List<Map<String, Object>> mapList,String postDate) throws IOException, WriteException {
		HttpServletResponse response = ServletActionContext.getResponse();
		String excelFile = FileUpload.getFtpURL() + "exportExcelToJxl.xls";
		File file = new File(FileUpload.getFtpURL());

		if (!file.exists() && !file.isDirectory()) {
			file.mkdir();
		}
		String exportName = "MoneyTotalListExcel_" + FileUpload.getDateToString("yyyy_MM_dd");
		try {
			WritableWorkbook wb = Workbook.createWorkbook(new File(excelFile));
			WritableSheet firstSheet = wb.createSheet("资金认领 > 认领资金统计", 1);
			String[] headers;
			if (postDate.equals("0")){
				headers= new String[]{"明细编码", "工单编号", "集团280", "集团名称", "唯一流水", "账户类型", "账户号码", "缴费类型", "缴费金额", "明细类型", "明细状态","创建日期", "创建人", "创建工号", "所属分公司", "所属部门"};
			}else if (postDate.equals("1")){
				headers= new String[]{"工单编码", "工单标题", "唯一流水", "集团280", "集团名称", "工单类型", "工单金额", "创建日期", "创建人", "创建工号","所属分公司", "所属部门", "工单状态"};
			}else {
				headers= new String[]{"唯一流水", "对方户名", "对方账号", "集团编号", "集团名称", "认领总金额", "已使用金额", "剩余金额", "交易时间", "认领时间","认领人", "认领人工号", "所属分公司", "所属部门"};
			}
			for (int i = 0; i < headers.length; i++) {
				// 3、创建单元格(Label)对象
				Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
				WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
				WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
				// 标题栏 // 颜色
				WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
				wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
				wcfTitle.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
				wcfTitle.setAlignment(jxl.format.Alignment.CENTRE); // 设置垂直对齐
				// 内容栏
				WritableCellFormat wcfContent = new WritableCellFormat(wf3);
				wcfContent.setVerticalAlignment(jxl.format.VerticalAlignment.CENTRE); // 设置垂直对齐
				wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐

				CellView navCellView = new CellView();
				navCellView.setSize(150 * 50);

				label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
				firstSheet.setColumnView(i, navCellView); // 设置col显示样式
				firstSheet.setRowView(i, 400, false); // 设置行高
				firstSheet.addCell(label0);
				if (mapList.size() > 0) {
					for (int i1 = 0; i1 < mapList.size(); i1++) {
						if (postDate.equals("0")){
							if (mapList.get(i1).get("MONEYNO") != null) {
								Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("MONEYNO")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("APPLYNO") != null) {
								Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("APPLYNO")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("GROUPCODE") != null) {
								Label label = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPCODE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("GROUPNAME") != null) {
								Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPNAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("SERIALNO") != null) {
								Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("SERIALNO")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("ORDERTYPE") != null) {
								String orderType = mapList.get(i1).get("ORDERTYPE").toString();
								String value = "";
								if ("1".equals(orderType)) {
									value = "个人业务";
								} else if ("2".equals(orderType)) {
									value = "集团业务";
								} else{
									value = "-----";
								}
								Label label = new Label(5, i1 + 1, value, wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("CONTRCTNO") != null) {
								Label label = new Label(6, i1 + 1, String.valueOf(mapList.get(i1).get("CONTRCTNO")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("USETYPE") != null) {
								String useType = mapList.get(i1).get("USETYPE").toString();
								String value = "";
								if ("01".equals(useType)) {
									value = "异地缴费";
								} else if ("1".equals(useType)) {
									value = "缴费";
								} else if ("2".equals(useType)) {
									value = "存送";
								} else if ("3".equals(useType)) {
									value = "终端";
								} else if ("05".equals(useType)) {
									value = "预开票";
								} else if ("05A".equals(useType)) {
									value = "专线预开票";
								} else if ("06".equals(useType)) {
									value = "有价卡";
								} else if ("07".equals(useType)) {
									value = "物联网";
								} else if ("08".equals(useType)) {
									value = "ICT设备销售";
								} else if ("09".equals(useType)) {
									value = "ICT终端销售";
								} else if ("10".equals(useType)) {
									value = "ICT软件销售";
								} else{
									value = "-----";
								}
								Label label = new Label(7, i1 + 1, value, wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("AMOUNT") != null) {
								String amount = mapList.get(i1).get("AMOUNT").toString();
								Double value = Double.parseDouble(amount)/100;
								Label label = new Label(8, i1 + 1, value.toString()+"元", wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("PARENTID") != null) {
								String parentId = mapList.get(i1).get("PARENTID").toString();
								String value = "";
								 if ("".equals(parentId)) {
									value = "使用明细";
								} else{
									value = "冲正明细";
								}
								Label label = new Label(9, i1 + 1, value, wcfContent);
								firstSheet.addCell(label);
							}else {
								Label label = new Label(9, i1 + 1, "使用明细", wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("STATE") != null) {
								String state = mapList.get(i1).get("STATE").toString();
								String value = "";
								if ("1".equals(state)) {
									value = "已完成";
								} else if ("-1".equals(state)) {
									value = "审核中";
								} else if ("3".equals(state)) {
									value = "已冲正";
								} else if ("4".equals(state)) {
									value = "已作废";
								} else if ("5".equals(state)) {
									value = "冲正中";
								} else{
									value = "-----";
								}
								Label label = new Label(10, i1 + 1, value, wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("CREATEDATE") != null) {
								Label label = new Label(11, i1 + 1, String.valueOf(mapList.get(i1).get("CREATEDATE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("EMPLOYEE_NAME") != null) {
								Label label = new Label(12, i1 + 1, String.valueOf(mapList.get(i1).get("EMPLOYEE_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("BOSSUSERNAME") != null) {
								Label label = new Label(13, i1 + 1, String.valueOf(mapList.get(i1).get("BOSSUSERNAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("COMPANY_NAME") != null) {
								Label label = new Label(14, i1 + 1, String.valueOf(mapList.get(i1).get("COMPANY_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("COUNTY_NAME") != null) {
								Label label = new Label(15, i1 + 1, String.valueOf(mapList.get(i1).get("COUNTY_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
						}else if (postDate.equals("1")){
							if (mapList.get(i1).get("APPLYNO") != null) {
								Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("APPLYNO")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("TITLE") != null) {
								Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("TITLE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("SERIALNO") != null) {
								Label label = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("SERIALNO")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("GROUPCODE") != null) {
								Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPCODE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("GROUPNAME") != null) {
								Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPNAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("OPTYPE") != null) {
								String opType = mapList.get(i1).get("OPTYPE").toString();
								String value = "";
								if ("1".equals(opType)) {
									value = "使用工单";
								}else if("2".equals(opType)){
									value = "冲正工单";
								}else if("3".equals(opType)){
									value = "认领工单";
								}else if("4".equals(opType)){
									value = "跨月冲正";
								} else{
									value = "-----";
								}
								Label label = new Label(5, i1 + 1, value, wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("APPLYAMOUNT") != null) {
								String applyAmount = mapList.get(i1).get("APPLYAMOUNT").toString();
								Double value = Double.parseDouble(applyAmount)/100;
								Label label = new Label(6, i1 + 1, value+"元", wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("CREATEDATE") != null) {
								Label label = new Label(7, i1 + 1, String.valueOf(mapList.get(i1).get("CREATEDATE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("EMPLOYEE_NAME") != null) {
								Label label = new Label(8, i1 + 1, String.valueOf(mapList.get(i1).get("EMPLOYEE_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("BOSSUSERNAME") != null) {
								Label label = new Label(9, i1 + 1, String.valueOf(mapList.get(i1).get("BOSSUSERNAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("COMPANY_NAME") != null) {
								Label label = new Label(10, i1 + 1, String.valueOf(mapList.get(i1).get("COMPANY_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("COUNTY_NAME") != null) {
								Label label = new Label(11, i1 + 1, String.valueOf(mapList.get(i1).get("COUNTY_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("STATE") != null) {
								String state = mapList.get(i1).get("STATE").toString();
								String value = "";
								if ("1".equals(state)) {
									value = "审核中";
								}else if("-1".equals(state)){
									value = "已作废";
								}else if("2".equals(state)){
									value = "被退回";
								}else if("3".equals(state)){
									value = "待完成";
								}else if("0".equals(state)){
									value = "已完成";
								} else{
									value = "-----";
								}
								Label label = new Label(12, i1 + 1, value, wcfContent);
								firstSheet.addCell(label);
							}
						}else {
							if (mapList.get(i1).get("SERIALNO") != null) {
								Label label = new Label(0, i1 + 1, String.valueOf(mapList.get(i1).get("SERIALNO")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("OTHERACCNUMBER") != null) {
								Label label = new Label(1, i1 + 1, String.valueOf(mapList.get(i1).get("OTHERACCNUMBER")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("OTHERNAME") != null) {
								Label label = new Label(2, i1 + 1, String.valueOf(mapList.get(i1).get("OTHERNAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("GROUPCODE") != null) {
								Label label = new Label(3, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPCODE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("GROUPNAME") != null) {
								Label label = new Label(4, i1 + 1, String.valueOf(mapList.get(i1).get("GROUPNAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("AMOUNT") != null) {
								Label label = new Label(5, i1 + 1,BigDecimal.valueOf(Long.valueOf(mapList.get(i1).get("AMOUNT").toString())).divide(new BigDecimal(100))+" 元", wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("USEAMOUNT") != null) {
								Label label = new Label(6, i1 + 1,BigDecimal.valueOf(Long.valueOf(mapList.get(i1).get("USEAMOUNT").toString())).divide(new BigDecimal(100))+" 元", wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("OVERAMOUNT") != null) {
								Label label = new Label(7, i1 + 1,BigDecimal.valueOf(Long.valueOf(mapList.get(i1).get("OVERAMOUNT").toString())).divide(new BigDecimal(100))+" 元", wcfContent);
								firstSheet.addCell(label);
							}

							if (mapList.get(i1).get("TRANDATE") != null) {
								Label label = new Label(8, i1 + 1, String.valueOf(mapList.get(i1).get("TRANDATE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("PUS_DATE") != null) {
								Label label = new Label(9, i1 + 1, String.valueOf(mapList.get(i1).get("PUS_DATE")), wcfContent);
								firstSheet.addCell(label);
							}

							if (mapList.get(i1).get("EMPLOYEE_NAME") != null) {
								Label label = new Label(10, i1 + 1, String.valueOf(mapList.get(i1).get("EMPLOYEE_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("BOSSUSERNAME") != null) {
								Label label = new Label(11, i1 + 1, String.valueOf(mapList.get(i1).get("BOSSUSERNAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("COMPANY_NAME") != null) {
								Label label = new Label(12, i1 + 1, String.valueOf(mapList.get(i1).get("COMPANY_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
							if (mapList.get(i1).get("COUNTY_NAME") != null) {
								Label label = new Label(13, i1 + 1, String.valueOf(mapList.get(i1).get("COUNTY_NAME")), wcfContent);
								firstSheet.addCell(label);
							}
						}
					}
				}
			}
			wb.write();// 打开流 开始写文件
			wb.close();// 关闭流
			byte[] data = FileUtil.toByteArray2(excelFile);
			String fileName = URLEncoder.encode(exportName, "UTF-8");
			response.reset();
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xls" + "\"");
			response.addHeader("Content-Length", "" + data.length);
			response.setContentType("application/octet-stream;charset=UTF-8");
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
			outputStream.write(data);
			outputStream.flush();
			outputStream.close();
			response.flushBuffer();
			File fe = new File(excelFile);
			fe.delete();
		} finally {
			File fe = new File(excelFile);
			if (file.exists() && file.isDirectory()) {
				fe.delete();
			}
		}
	}

	/**
	 * @Description :根据用户名称查询用户信息
	 * <AUTHOR>
	 * @param name: 用户名称
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/2/17 16:44
	 */
	public List<Map<String,String>> getUserByName(String name){
		String sql = "SELECT * from VW_USERINFO WHERE EMPLOYEE_NAME LIKE '%"+name+"%'";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description :根据用户编号查询用户消息
	 * <AUTHOR>
	 * @param rowNo: 用户编号
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/3/1 11:25
	 */
	public List<Map<String,String>> getUserByRowno(String rowNo){
		String sql = "SELECT * from VW_USERINFO WHERE ROWNO = '"+rowNo+"' AND ISMAINDPT = 'true'";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description :根据BOSS工号查询用户消息
	 * <AUTHOR>
	 * @param bossUserName: 用户编号
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/3/1 11:25
	 */
	public List<Map<String,Object>> getUserByBossUserName(String bossUserName){
		String sql = "SELECT * from VW_USERINFO WHERE BOSSUSERNAME = '"+bossUserName+"' AND ISMAINDPT = 'true'";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}


	/**
	 * @Description :统计资金标准某个时间段未认领的工单
	 * <AUTHOR>
	 * @param stateCreatorDate: 开始时间
	 * @param endCreatorDate: 结束时间
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/2/28 17:32
	 */
	public List<Map<String,String>> getTimeoutDataMoneyList(String stateCreatorDate,String endCreatorDate){
		String sql = "SELECT COMPANYCODE,COUNT(0) as \"NUMBER\" from MONEYTOTAL WHERE STATE = '0' AND CREATEDATE >= TO_DATE('" + stateCreatorDate + " 00:00:00','yyyy-MM-dd HH24:mi:ss') AND CREATEDATE <= TO_DATE('" + endCreatorDate + " 23:59:59','yyyy-MM-dd HH24:mi:ss') GROUP BY COMPANYCODE ORDER BY COMPANYCODE";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description :根据时间查询当前资金认领存在多少未处理代办
	 * <AUTHOR>
	 * @param CreatorDate: 统计时间
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/3/1 11:14
	 */
	public List<Map<String,String>> getTimeoutDataWaitList(String CreatorDate){
			String sql = "SELECT HANDLEUSERID,COUNT(0) as \"NUMBER\" from WAITTASK WHERE CODE = 'MONEYTOTAL' AND STATE = '0' AND CREATIONTIME <= TO_DATE('"+CreatorDate+" 23:59:59','yyyy-MM-dd HH24:mi:ss') GROUP BY HANDLEUSERID";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description :根据集团编号和电话号码查询集团对应关系
	 * <AUTHOR>
	 * @param groupCode: 集团280编号
	 * @param phone: 个人电话号码
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/3/15 10:38
	 */
	public List<Map<String,String>> queryGroupAssociation(String groupCode,String phone){
		String sql = "select * from BPMS_BLIB_MENDER WHERE GROUP_CODE = '"+groupCode+"' AND PHONE_NO = '"+phone+"' ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description :根据角色名称和地市编号查询用户
	 * <AUTHOR>
	 * @param companyCode: 地市编号
	 * @param roleNmae: 角色名称
	 * @return: java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/2/28 17:59
	 */
	public List<Map<String,String>> getTimeoutDataUserList(String companyCode,String roleNmae){
		String sql = " select DISTINCT v.ROWNO,v.EMPLOYEE_NAME,v.COUNTY_NAME,v.COMPANY_NAME,v.DEPARTMENT_NAME,v.BOSSUSERNAME from VW_USERINFO v " +
				" inner join system_user_role sur on sur.row_no=v.rowno " +
				" inner join system_role sr on sr.id=sur.role_id " +
				" LEFT  JOIN TOPCONTACTS t ON v.rowno=t.rowno where v.ISMAINDPT='true' ";
		if (roleNmae!=null){
			sql+=" AND sr.NAME = '"+roleNmae+"' ";
		}
		if (companyCode!=null){
			sql+=" AND v.COMPANY_CODE = '"+companyCode+"' ";
		}
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description TODO 查询需要推送  并且还未推送的发票信息(缴费流水号 电子发票接收号码不能为空)
	 * <AUTHOR>
	 * @return java.util.List<com.xinxinsoft.entity.claimForFunds.InvoiceMiddle>
	 * @Date 2022/3/29 14:18
	 **/
	public List<InvoiceMiddle> getNotPushedInvoice(){
		String sql = "SELECT * FROM BPMS_INVOICE_MIDDLE WHERE PRINT_TYPE = '1' AND PAYMENT_ACCEPT IS NOT NULL AND MSG_RECV_PHONE IS NOT NULL AND BOOSSTATE is NULL ";
		return getSession().createSQLQuery(sql).addEntity(InvoiceMiddle.class).list();
	}

	/**
	 * @Description TODO 查询政企类资金银行到账流水信息——>ftp
	 * <AUTHOR>
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/5/17 10:11 
	 **/
	public List<Map<String,Object>> getCapitalBickerFiling(String dateStr){
		String sql = " SELECT tal.SERIALNO,tal.ACCNUMBER,tal.OTHERACCNUMBER,tal.OTHERNAME,tal.TRANDATE,tal.AMOUNT,tal.USEMEMO,tal.COMPANYNAME " +
				" FROM BPMS_GROUP_RELATIONS rel INNER JOIN MONEYTOTAL tal ON rel.ACCOUNT_NUMBER = tal.OTHERACCNUMBER AND rel.GROUP_CODE = tal.GROUPCODE " +
				" WHERE to_char(tal.TRANDATE,'yyyyMM')='"+dateStr+"' AND rel.SCOPE_BUSINESS = '1' ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description TODO 查询政企资金认领记录信息——>ftpe
	 * <AUTHOR>
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/5/17 11:38
	 **/
	public List<Map<String,Object>> getCapitalRecordFiling(String dateStr){
		String sql = " WITH  " +
				" AccDate AS ( " +
				"    SELECT " +
				"        uuid, " +
				"        group_code, " +
				"        account_number " +
				"    FROM ( " +
				"        SELECT " +
				"            acc.uuid, " +
				"            acc.group_code, " +
				"            det.account_number, " +
				"            ROW_NUMBER() OVER (PARTITION BY acc.group_code, det.account_number ORDER BY acc.create_date DESC) AS rn " +
				"        FROM " +
				"            BPMS_GROUP_ACCOUNT acc " +
				"            INNER JOIN BPMS_GROUP_ACCOUNT_DET det ON acc.apply_no = det.apply_no " +
				"        WHERE " +
				"            acc.apply_state = 1 " +
				"    ) " +
				"    WHERE " +
				"        rn = 1 " +
				" ), " +
				" TotalDate AS ( " +
				"    SELECT " +
				"        SERIALNO, " +
				"        BATCHNO, " +
				"        CASE " +
				"            WHEN to_char(PUS_DATE, 'YYYY-MM-DD hh24:mi:ss') IS NULL THEN " +
				"                to_char(createdate, 'YYYY-MM-DD hh24:mi:ss') " +
				"            ELSE " +
				"                to_char(PUS_DATE, 'YYYY-MM-DD hh24:mi:ss') " +
				"        END AS PUS_DATE, " +
				"        GROUPCODE, " +
				"        GROUPNAME, " +
				"        PUSHBOSSUSERNAME, " +
				"        AMOUNT, " +
				"        COMPANYNAME, " +
				"		 OTHERACCNUMBER " +
				"    FROM " +
				"        MONEYTOTAL " +
				"    WHERE " +
				"        to_char(TRANDATE, 'yyyyMM') = '"+dateStr+"' " +
				"        AND state = '1' " +
				" ) " +
				" SELECT " +
				"    pd.SERIALNO, " +
				"    pd.BATCHNO, " +
				"    pd.PUS_DATE, " +
				"    pd.GROUPCODE, " +
				"    pd.GROUPNAME, " +
				"    pd.PUSHBOSSUSERNAME, " +
				"    pd.AMOUNT, " +
				"    pd.COMPANYNAME, " +
				"    ly.APPLYNO, " +
				"    rel.ACCOUNT_BANKTYPE, " +
				"    rel.ACCOUNT_STARTTIME, " +
				"    CASE " +
				"        WHEN ly.APPLYNO IS NOT NULL THEN " +
				"            LISTAGG(k.OPER_NAME || '&' || k.NAME || '&1&' || to_char(k.OPER_DATE, 'YYYYMMDDHH24MISS'), '&') WITHIN GROUP (ORDER BY k.GMT_CREATE) " +
				"        ELSE '' " +
				"    END AS TASK " +
				" FROM " +
				"    BPMS_GROUP_RELATIONS rel " +
				"    INNER JOIN AccDate acc ON acc.group_code = rel.group_code AND acc.account_number = rel.account_number " +
				"    INNER JOIN TotalDate pd ON rel.ACCOUNT_NUMBER = pd.OTHERACCNUMBER AND rel.GROUP_CODE = pd.GROUPCODE " +
				"    LEFT JOIN MONEYAPPLY ly ON ly.SERIALNO = pd.SERIALNO AND ly.OPTYPE = '3' " +
				"    LEFT JOIN Bpms_riskoff_process brp ON brp.biz_id = acc.uuid " +
				"    LEFT JOIN Bpms_riskoff_task k ON k.process_id = brp.process_sign AND k.NAME NOT IN ('起草人', '客户经理') " +
				" WHERE " +
				"    rel.SCOPE_BUSINESS = '1' " +
				" GROUP BY " +
				"    pd.SERIALNO, " +
				"    pd.BATCHNO, " +
				"    pd.PUS_DATE, " +
				"    pd.GROUPCODE, " +
				"    pd.GROUPNAME, " +
				"    pd.PUSHBOSSUSERNAME, " +
				"    pd.AMOUNT, " +
				"    pd.COMPANYNAME, " +
				"    ly.APPLYNO, " +
				"    rel.ACCOUNT_BANKTYPE, " +
				"    rel.ACCOUNT_STARTTIME ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description TODO 查询政企资金认领使用缴费信息——>ftp//level1AuditResults
	 * <AUTHOR>
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.String>>
	 * @Date 2022/5/17 11:38
	 **/
	public List<Map<String,Object>> getCapitalUseFiling(String dateStr){
		String sql = " WITH AccDate AS ( " +
				" SELECT " +
				"  uuid, " +
				"  group_code, " +
				"  account_number  " +
				" FROM " +
				"  ( " +
				"  SELECT " +
				"   acc.uuid, " +
				"   acc.group_code, " +
				"   det.account_number, " +
				"   ROW_NUMBER ( ) OVER ( PARTITION BY acc.group_code, det.account_number ORDER BY acc.create_date DESC ) AS rn  " +
				"  FROM " +
				"   BPMS_GROUP_ACCOUNT acc " +
				"   INNER JOIN BPMS_GROUP_ACCOUNT_DET det ON acc.apply_no = det.apply_no  " +
				"  WHERE " +
				"   acc.apply_state = 1  " +
				"  )  " +
				" WHERE " +
				"  rn = 1  " +
				" ), " +
				" TotalDate AS ( " +
				" SELECT " +
				"  SERIALNO, " +
				"  BATCHNO, " +
				" CASE " +
				"   WHEN to_char( PUS_DATE, 'YYYY-MM-DD hh24:mi:ss' ) IS NULL THEN " +
				"   to_char( createdate, 'YYYY-MM-DD hh24:mi:ss' ) ELSE to_char( PUS_DATE, 'YYYY-MM-DD hh24:mi:ss' )  " +
				"  END AS PUS_DATE, " +
				"  GROUPCODE, " +
				"  GROUPNAME, " +
				"  PUSHBOSSUSERNAME, " +
				"  AMOUNT, " +
				"  COMPANYNAME, " +
				"  OTHERACCNUMBER  " +
				" FROM " +
				"  MONEYTOTAL  " +
				" WHERE " +
				"  to_char( TRANDATE, 'yyyyMM' ) = '"+dateStr+"'  " +
				"  AND state = '1'  " +
				" ) SELECT " +
				" pd.BATCHNO, " +
				" det.MONEYNO, " +
				" det.CREATEDATE, " +
				" pd.GROUPCODE, " +
				" pd.GROUPNAME, " +
				" det.AMOUNT, " +
				" det.USETYPE, " +
				" ly.APPLYNO, " +
				" rel.ACCOUNT_BANKTYPE, " +
				" rel.ACCOUNT_STARTTIME, " +
				" CASE " +
				"  WHEN ly.APPLYNO IS NOT NULL THEN " +
				"  LISTAGG ( k.OPER_NAME || '&' || k.NAME || '&1&' || to_char( k.OPER_DATE, 'YYYYMMDDHH24MISS' ), '&' ) WITHIN GROUP ( ORDER BY k.GMT_CREATE ) ELSE ''  " +
				" END AS TASK  " +
				" FROM " +
				" BPMS_GROUP_RELATIONS rel " +
				" INNER JOIN AccDate acc ON acc.group_code = rel.group_code  " +
				" AND acc.account_number = rel.account_number " +
				" INNER JOIN TotalDate pd ON rel.ACCOUNT_NUMBER = pd.OTHERACCNUMBER  " +
				" AND rel.GROUP_CODE = pd.GROUPCODE " +
				" INNER JOIN MONEYAPPLY ly ON ly.SERIALNO = pd.SERIALNO  " +
				" AND ( ly.OPTYPE = '1' OR ly.MA_TYPE = '3' )  " +
				" AND ly.state = '0' " +
				" INNER JOIN MONEYAPPLYDET det ON ly.APPLYNO = det.APPLYNO  " +
				" AND pd.SERIALNO = det.SERIALNO " +
				" LEFT JOIN Bpms_riskoff_process brp ON brp.biz_id = acc.uuid " +
				" LEFT JOIN Bpms_riskoff_task k ON k.process_id = brp.process_sign  " +
				" AND k.NAME NOT IN ( '起草人', '客户经理' )  " +
				" WHERE " +
				" rel.SCOPE_BUSINESS = '1'  " +
				" GROUP BY " +
				" pd.BATCHNO, " +
				" det.MONEYNO, " +
				" det.CREATEDATE, " +
				" pd.GROUPCODE, " +
				" pd.GROUPNAME, " +
				" det.AMOUNT, " +
				" det.USETYPE, " +
				" ly.APPLYNO, " +
				" rel.ACCOUNT_BANKTYPE, " +
				" rel.ACCOUNT_STARTTIME ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}



	/**
	 * @Description 根据日期查询缴费码映射关系
	 * <AUTHOR>
	 * @param dateStr 日期字符串
	 * @return java.util.List<java.util.Map<java.lang.String,java.lang.Object>>
	 * @Date 2023/10/7 10:12
	 **/
	public List<Map<String,Object>> getPaymentCodeData(String dateStr){
		String sql = " SELECT r.ACCOUNT_NUMBER,r.GROUP_CODE,h.CREATE_DATE,COALESCE(h.PHONE_NO, h.CONTRACT_NO) as CONTRACT_NO " +
				" FROM BPMS_GROUP_RELATIONS r inner join BPMS_GROUP_HIPACCOUNT h ON r.UUID = h.RELATIONS_HIP " +
				" WHERE r.ORDER_STATE = '0' and SCOPE_BUSINESS = '1' ";
		if (!"********".equals(dateStr)){
			sql+=" and to_char(r.CREATE_DATE,'yyyyMMdd')='"+dateStr+"' ";
		}
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	/**
	 * @Description :根据当前日期获取指定时间后的日期
	 * <AUTHOR>
	 * @param hours: 天数
	 * @return: java.lang.String
	 * @Date 2022/2/28 17:46
	 */
	public String getTargetDate(Integer hours){
		Calendar   cal   =   Calendar.getInstance();
		cal.add(Calendar.DATE,   hours);
		return new SimpleDateFormat( "yyyy-MM-dd ").format(cal.getTime());
	}

	/**
	 * @Description TODO 根据集团编号查询集团
	 * <AUTHOR>
	 * @param groupCoding 集团编号
	 * @return com.xinxinsoft.entity.groupcustomer.GroupCustomer
	 * @Date 2022/6/10 10:05
	 **/
	public GroupCustomer queryGroupCustomerById(String groupCoding){
		String sql = " select * from GroupCustomer where groupCoding=? ";
		return (GroupCustomer) getSession().createSQLQuery(sql).addEntity(GroupCustomer.class).setString(0, groupCoding).uniqueResult();
	}

	/**
	 * @Description TODO 保存集团信息
	 * <AUTHOR>
	 * @param gc 集团对象
	 * @return com.xinxinsoft.entity.groupcustomer.GroupCustomer
	 * @Date 2022/7/12 9:52
	 **/
	public GroupCustomer savaGroupCustomer(GroupCustomer gc){
		try{
			Session session = this.getSession();
			session.saveOrUpdate(gc);
			session.flush();
			return gc;
		}catch (Exception e){
			return null;
		}
	}

	/**
	 * @Description TODO 根据暂收款编号查询暂收款信息
	 *
	 *
	 * <AUTHOR>
	 * @param ProvisionalId	暂收款编号
	 * @return com.xinxinsoft.entity.claimForFunds.MoneytotalProvisional
	 * @Date 2022/7/12 9:54
	 **/
	public MoneytotalProvisional getProvisionalByCode(String ProvisionalId){
		String sql = " SELECT * from MONEYTOTAL_PROVISIONAL WHERE UUID = ? ";
		return (MoneytotalProvisional) getSession().createSQLQuery(sql).addEntity(MoneytotalProvisional.class).setString(0, ProvisionalId).uniqueResult();
	}

	/**
	 * @Description TODO 根据银行账户查询是否为移动通用账户
	 * <AUTHOR>
	 * @param AccountNumber 银行账户
	 * @return java.util.Map<java.lang.String,java.lang.String>
	 * @Date 2022/10/18 14:21
	 **/
	public Map<String,String> getUnlimitedAccountByNumber(String AccountNumber){
		String sql = " SELECT * FROM MONEY_UNLIMITED_ACCOUNT WHERE ACCOUNT_NUMBER = ? ";
		return  (Map<String, String>) this.getSession().createSQLQuery(sql).setParameter(0, AccountNumber).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).uniqueResult();
	}

	/**
	 * @Description 根据集团编号查询自动缴费配置信息
	 * <AUTHOR>
	 * @param groupCode	集团编号
	 * @return com.xinxinsoft.entity.claimForFunds.MoneyPeyMent
	 * @Date 2024/04/17 16:09
	 **/
	public MoneyPeyMent queryMoneyPeyMent(String groupCode){
		String sql = " select * from MONEYPEYMENT where GROUPCODE = ? ";
		return (MoneyPeyMent) getSession().createSQLQuery(sql).addEntity(MoneyPeyMent.class).setString(0, groupCode).uniqueResult();
	}

	/**
	 * @Description TODO 根据流水和编号删除预占信息
	 * <AUTHOR>
	 * @param batchNo	资金编号
	 * @param serialNo	资金流水
	 * @return int
	 * @Date 2023/1/5 11:36
	 **/
	public int deleteTaxpayer(String batchNo,String serialNo) {
		String sql = " DELETE TAXPAYER WHERE BATCH_NO = ? AND SERIAL_NO = ? ";
		return getSession().createSQLQuery(sql).setString(0,batchNo).setString(1,serialNo).executeUpdate();
	}

	/**
	 *
	 * @param honorcode
	 * @param phone
	 * @throws ParseException
	 */
	public void sendPushMessage(String honorcode, String phone,String TemplateID) throws ParseException {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Date d = new Date();
		String dateStr = formatter.format(d);
		Push_0_0001 p = new Push_0_0001();
		p.setParameter(honorcode);
		p.setPhone_No(phone);
		p.setSeq("00011611251615030001");
		p.setTemplateID(TemplateID);
		p.setInsert_Time(formatter.parse(dateStr));
		p.setSend_Flag("0");
		p.setOp_Code("add");
		p.setLogin_No("12345");
		p.setSend_Time(formatter.parse(dateStr));
		this.getSession().save(p);
	}

	/**
	 * 方法描述: 快捷缴费有价卡查询
	 * @开发人员 TangXiao
	 * @版本编号 1.1
	 * @开发时间 2024/6/18
	 * @param groupCode 集团编号
	 * @param whetherOrNotToPayAValueCard 查询类型【1.1版本已舍弃】
	 * @return List<Map<String,String>>
	 **/
	public List<Map<String, Object>> valuableCardInquiry(String groupCode, String whetherOrNotToPayAValueCard) {
		String sql = "SELECT " +
				"    tab1.CONTRACT_NO AS CONTRACT_NO, " +
				"    COALESCE( tab1.VALUABLECARDAMOUNT, 0 ) AS VALUABLECARDAMOUNT, " +
				"    0 AS PREINVAMOUNT " +
				" FROM " +
				"    ( " +
				"        SELECT " +
				"            vd.INV_CONTRACT_NO AS CONTRACT_NO, " +
				"            sum( TO_NUMBER( vd.ORDER_PRICE ) - TO_NUMBER( vd.MONEYPAY_PRICE ) ) AS VALUABLECARDAMOUNT " +
				"        FROM " +
				"            VALUABLECARDDET vd " +
				"                INNER JOIN VALUABLECARD v ON v.BATCHNO = vd.APPLY_NO " +
				"        WHERE " +
				"                v.GROUPCODE = ? " +
				"          AND vd.BOSS_STATE = '0' " +
				"          AND TO_NUMBER( vd.MONEYPAY_PRICE ) < TO_NUMBER( vd.ORDER_PRICE ) " +
				"        GROUP BY " +
				"            vd.INV_CONTRACT_NO " +
				"    ) tab1 ";
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, groupCode).list();
	}

	public MoneyTotal queryMoneyTotalByGroupCode(String groupCode){
		String sql = " SELECT * FROM MONEYTOTAL WHERE GROUPCODE = ? and OVERAMOUNT>0 and STATE = 1 ORDER BY PUS_DATE FETCH FIRST 1 ROW ONLY ";
		return (MoneyTotal) getSession().createSQLQuery(sql).addEntity(MoneyTotal.class).setString(0, groupCode).uniqueResult();
	}
}
