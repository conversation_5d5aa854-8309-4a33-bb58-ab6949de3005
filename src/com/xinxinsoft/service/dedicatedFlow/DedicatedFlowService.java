package com.xinxinsoft.service.dedicatedFlow;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.Reader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Clob;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.Collator;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.Callable;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

import net.sf.json.JSONArray;

import org.apache.commons.lang.StringEscapeUtils;
import org.apache.commons.lang.StringUtils;
import org.hibernate.Criteria;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.criterion.Order;
import org.hibernate.criterion.ProjectionList;
import org.hibernate.criterion.Projections;
import org.hibernate.criterion.Restrictions;
import org.hibernate.transform.Transformers;
import org.springframework.context.ApplicationContext;
import org.springframework.context.support.ClassPathXmlApplicationContext;
import org.springframework.orm.hibernate3.SessionFactoryUtils;

import com.sun.star.uno.RuntimeException;
import com.xinxinsoft.action.dedicatedFlow.CoutomerAcceptanceAction;
import com.xinxinsoft.entity.basetype.BusinessType;
import com.xinxinsoft.entity.basetype.ProductType;
import com.xinxinsoft.entity.boss.RESULT_DATA;
import com.xinxinsoft.entity.boss.StartPreOrderOut;
import com.xinxinsoft.entity.commonSingManagement.OrderForm;
import com.xinxinsoft.entity.commonSingManagement.OrderFormOrOrdertask;
import com.xinxinsoft.entity.commonSingManagement.OrderSheet;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.dedicatedFlow.BossTache;
import com.xinxinsoft.entity.dedicatedFlow.CustomerAcceptance;
import com.xinxinsoft.entity.dedicatedFlow.ImsChange;
import com.xinxinsoft.entity.dedicatedFlow.ImsTranslate;
import com.xinxinsoft.entity.dedicatedFlow.InternetChange;
import com.xinxinsoft.entity.dedicatedFlow.InternetThings;
import com.xinxinsoft.entity.dedicatedFlow.Opinion;
import com.xinxinsoft.entity.dedicatedFlow.OrderInformation;
import com.xinxinsoft.entity.dedicatedFlow.OrderStages;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.ims.ImsFormInfo;
import com.xinxinsoft.entity.messages.MessagesXml;
import com.xinxinsoft.entity.order.OrderDetail;
import com.xinxinsoft.entity.ordertask.OrderTask;
import com.xinxinsoft.entity.processLink.LinkTemplate;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.sendComms.EOMAxisService;
import com.xinxinsoft.sendComms.Params;
import com.xinxinsoft.service.commonSingManagement.CommonSingleService;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.service.core.json.JSONObject;
import com.xinxinsoft.service.processLink.LinkTemplateService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

/***
 * 
 * <AUTHOR> 
 */
public class DedicatedFlowService extends BaseService {
	// 定义一个全局变量：
	private static String orderId;

	/**
	 * 根据实体保存或更新：
	 * 
	 * @param oi
	 * @return
	 */
	public OrderForm saveOrUpdateOrder(OrderForm oi) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(oi);
			return oi;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public OrderForm saveOrder(OrderForm oi) {
		try {
			Session session = this.getSession();
			session.save(oi);
			return oi;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public OrderFormOrOrdertask setOrderFormOrOrdertask(OrderFormOrOrdertask oi) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(oi);
			return oi;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
//	public OrderForm saveOrUpdateOrder(String orderId) {
//		try {
//			String sql = "update SingOrderForm ";
//			return oi;
//		} catch (Exception e) {
//			e.printStackTrace();
//			return null;
//		}
//	}
	
	/*public void saveOrUpdateOrder(String orderId){
		String sql = "UPDATE SINGORDERFORM SET TRANSMITSTATE='1' WHERE ORDERID=? ";
		this.getSession().createSQLQuery(sql).setString(0, orderId).executeUpdate();
	}*/

	/**
	 * 根据订单查询id ：
	 * 
	 * @param oid
	 *            订单ID
	 * @param userId
	 *            当前登录的用户Id
	 * @return
	 */
	// , Integer userId
	public String selectStagesInfoJSON(String oid) {
		try {
			Map<String, Object> pageInfo = new HashMap<String, Object>();
			orderId = oid;
			List<Object> orderList = selectOrderById(oid);
			// Collections.sort(orderList,Collator.getInstance(java.util.Locale.CHINA));//注意：是根据的
			List<Object> stagesList = selectStagesByOid(oid);
			pageInfo.put("order", orderList);
			pageInfo.put("stages", stagesList);
			com.xinxinsoft.service.core.json.JSONObject object = new JSONObject( pageInfo);
			return object.toString();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/***
	 * 实体转换成Map
	 * 
	 * @param entityClass
	 *            实体：
	 * @param userId
	 *            当前用户Id ，可以null
	 * @param iS
	 *            true 表示实体字段等于null就不加载到json中，false 相反
	 * @param iSId
	 *            判断是否根据Id 加载其它实体数据：只对子环节有用:linkId，其它为false;
	 * @return
	 */
	@SuppressWarnings("static-access")
	private Map<String, Object> setEntityToMap(Object entityClass,
			Integer userId, Boolean iS, Boolean iSId) {
		try {
			if (entityClass != null) {
				Map<String, Object> dataMap = new HashMap<String, Object>();
				Field[] field = entityClass.getClass().getDeclaredFields(); // 获取实体类的所有属性，返回Field数组
				for (int j = 0; j < field.length; j++) { // 遍历所有属性
					if (field[j].toString().indexOf(" static final ") == -1
							&& field[j].toString().indexOf(" static ") == -1) {
						String name = field[j].getName(); // 获取属性的名字
						String getName = "";
						if ("pCode".equals(name)) {
							getName = name;
						} else if ("bCode".equals(name)) {
							getName = name;
						} else if ("sOrderId".equals(name)) {
							getName = name;
						} else {
							getName = name.substring(0, 1).toUpperCase()
									+ name.substring(1); // 将属性的首字符大写，方便构造get，set方法
						}

						String type = field[j].getGenericType().toString(); // 获取属性的类型
						if (type.equals("class java.lang.String")) { // 如果type是类类型，则前面包含"class "，后面跟类名
							Method m = entityClass.getClass().getMethod(
									"get" + getName);
							String value = (String) m.invoke(entityClass); // 调用getter方法获取属性值
							if ("linkId".equalsIgnoreCase(name)) {
								if (iSId) {
									dataMap.put("stages",selectStages(value, this.orderId));
								} else {
									dataMap.put(name, value);
								}
							} else if ("GroupCustomerId".equalsIgnoreCase(name)) {
								if (!StringUtils.isEmpty(value)) {
									// 订单 信息里的客户集团 信息：
									dataMap.put("groupCustomer",
											selectEntityById(value));
								}
							}
							
							else if ("twoStageId".equalsIgnoreCase(name)) {
								if (!StringUtils.isEmpty(value)) {
									dataMap.put("twoLink",selectPareLinkById(value, false));
									// dataMap.put("twoLink",
									// selectTwoLinkById(value));
								}
							} else if ("ParentLinkTempCode".equalsIgnoreCase(name)) {
								if (!"1".equals(value)) {
									dataMap.put("pLink",
											selectPareLinkById(value, true));
									// dataMap.put("twoLink",
									// selectTwoLinkById(value));
								}
							} else if ("StageState".equalsIgnoreCase(name)) {
								if ("1".equals(value)) {
									dataMap.put("stageState", "完成");
								} else if ("0".equals(value)) {
									dataMap.put("stageState", "进行中");
								} else {
									dataMap.put("stageState", "未开始");
								}
							} else {
								// 是否不为true ,如果为true,传的实体空字段就不添加到json：
								if (iS) {
									// 判断实体的字段是空值就不添加：
									if (!StringUtils.isEmpty(value)) {
										dataMap.put(name, value);
									}
								} else {
									dataMap.put(name, value);
								}
							}
						}
						if ("class java.lang.Integer".equals(type)) {
							Method m = entityClass.getClass().getMethod(
									"get" + getName);
							Integer value = (Integer) m.invoke(entityClass);
							dataMap.put(name, value);
						}
						if ("int".equals(type)) {
							if (!"size".equalsIgnoreCase(getName)) {
								Method m = entityClass.getClass().getMethod(
										"get" + getName);
								Integer value = (Integer) m.invoke(entityClass);
								dataMap.put(name, value);
							}
						}

						if (type.equals("class java.util.Date")) {
							Method m = entityClass.getClass().getMethod(
									"get" + getName);
							java.sql.Timestamp value = (Timestamp) m.invoke(entityClass);
							if (value != null) {
								dataMap.put(name, StringToString(value));
							} else {
								dataMap.put(name, "");
							}

						}
					}
				}
				return dataMap;
			} else {
				return null;
			}
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 根据 订单id 查询orderStages表：
	 * 
	 * @param oid
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public List<Object> selectStagesByOid(String oid) {
		String sql = "select t.uuid,"
				+ "t.orderid,"
				+ "t.twostageid,"
				+ "t.expectedcompletiontime"
				+ ",t.actualcompletiontime,"
				+ "t.stagestate,"
				+ "t.actionuser,"
				+ "t.actionuserphone,t.isbossvalue,t.opertime from orderstages t inner join linktemplate lt on lt.linktempcode=t.twostageid  left join linktemplate p on p.linktempcode=lt.parentlinktempcode where t.orderid=? order by p.linksort asc,lt.linksort asc";
		List<OrderStages> orderStages = this.getSession()
				.createSQLQuery(sql).addEntity(OrderStages.class)
				.setString(0, oid).list();
		List<Object> retList = new ArrayList<Object>();
		for (int i = 0; i < orderStages.size(); i++) {
			retList.add(setEntityToMap(orderStages.get(i), null, false, false));
		}
		return retList;
	}
	
	/**
	 * 根据订单编号查询工作台，显示环节信息
	 * @param oid
	 * @return
	 */
	public List<Map<String, Object>> selectStagesByLink(String oid) {
		String sql = "select "
				+ "t.actualcompletiontime,"
				+ "t.stagestate,"
				+ "t.actionuser,"
				+ "t.actionuserphone,t.opertime,lt.linkname,lt.linksort from orderstages t inner join linktemplate lt on lt.linktempcode=t.twostageid  left join linktemplate p on p.linktempcode=lt.parentlinktempcode where t.orderid=? and t.stagestate in ('0','1') order by lt.linksort asc";
		return this.getSession() .createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, oid).list();
	}

	private List<Object> selectPareLinkById(String pid, boolean isP) {

		LinkTemplate lt = new LinkTemplate();
		//String hql="from LinkTemplate t where t.linkid=? and parentLink=1 and state=1";
		if (isP) {
			lt = (LinkTemplate) this.getSession()
					.createCriteria(LinkTemplate.class)
					.add(Restrictions.eq("linkTempCode", pid))
					.add(Restrictions.eq("parentLinkTempCode", "1"))
					.add(Restrictions.eq("state", 1)).uniqueResult();
		} else {
			lt = (LinkTemplate) this.getSession()
					.createCriteria(LinkTemplate.class)
					.add(Restrictions.eq("linkTempCode", pid))
					.add(Restrictions.eq("state", 1)).uniqueResult();
		}
		List<Object> retList = new ArrayList<Object>();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap = setEntityToMap(lt, null, false, false);
		retList.add(dataMap);
		return retList;
	}

	/**
	 * 内部时间转换：默认格式：yyyy-MM-dd
	 * 
	 * @param string
	 *            时间参数：?
	 * @param str
	 *            格式参数：yyyy-MM-dd h:m:s ，yyyyMMdd等：
	 * @return
	 */
	public String StringToString(Timestamp time) {
		DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		return sdf.format(time);
	}

	/**
	 * 根据订单id查询实体：和用户userId 查询是客户经理还是订单经理等：
	 * 
	 * @param value
	 * @return
	 */
	private List<Object> selectOrderById(String value) {
		OrderForm lisObj = (OrderForm) this.getSession()
				.createCriteria(OrderForm.class)
				.add(Restrictions.eq("orderId", value))
				// .add(Restrictions.or(Restrictions.eq("userId", userId),
				// Restrictions.eq("draftmanId", userId + "")))
				.uniqueResult();
		List<Object> retList = new ArrayList<Object>();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap = setEntityToMap(lisObj, null, false, false);
		retList.add(dataMap);
		return retList;
	}

	/**
	 * 根据订单Id和二级阶段Id 查询：
	 * 
	 * @param twoStageId
	 * @param orderId
	 * @return
	 */
	private List<Object> selectStages(String twoStageId, String orderId) {
		OrderStages oStagesList = (OrderStages) this.getSession()
				.createCriteria(OrderStages.class)
				.add(Restrictions.eq("orderId", orderId))
				.add(Restrictions.eq("twoStageId", twoStageId)).uniqueResult();
		List<Object> retList = new ArrayList<Object>();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap = setEntityToMap(oStagesList, null, false, false);
		retList.add(dataMap);
		return retList;
	}

	/**
	 * 根据客户集团用户ID 查询：
	 * 
	 * @param id
	 * @return
	 */
	private List<Object> selectEntityById(String id) {
		GroupCustomer gc = (GroupCustomer) this.getSession()
				.createCriteria(GroupCustomer.class)
				.add(Restrictions.eq("groupId", id)).uniqueResult();
		List<Object> retList1 = new ArrayList<Object>();
		Map<String, Object> dataMap = new HashMap<String, Object>();
		dataMap = setEntityToMap(gc, null, false, false);
		retList1.add(dataMap);
		return retList1;
	}

	public GroupCustomer queryGroupCustomer(String groupId){
		GroupCustomer gc = (GroupCustomer) this.getSession()
			.createCriteria(GroupCustomer.class)
			.add(Restrictions.eq("groupId", groupId)).uniqueResult(); 
		return gc;
	}
	
	public PageResponse dolist(PageRequest page, String name, SystemUser USER,
			String number,String state) {
		/*
		 * String sql =
		 * " select  x.uuid  as \"orderId\" ,x.draftmanId as \"draftmanId\" ," +
		 * "x.userId as \"userId\",x.state as \"state\",	x.demandName as \"demandName\","
		 * +
		 * "	x.orderTitle as \"orderTitle\",x.userName  as \"userName\",x.draftTime as \"draftTime\" "
		 * +
		 * " from ((select * from SingOrderForm  o where 1=1   and o.draftmanId='"
		 * + USER.getRowNo() + "'" + " and o.\"TYPE\"='DI'   " +
		 * "and o.PARENTORDERNUMBER  is not  null  " + ")" + "union ALL" +
		 * "(select * from SingOrderForm  o where 1=1   and   o.userId='" +
		 * USER.getRowNo() + "'" + "and o.\"TYPE\"='DI'  " + " and o.state='1' "
		 * + "and o.PARENTORDERNUMBER  is not  null  " + ") ) x  ";
		 */
		String sql = "SELECT * from ( ("
				+ "		SELECT"
				+ "			o.uuid AS \"orderId\","
				+ "   o.draftmanId AS \"draftmanId\","
				+ "   o.orderNumber AS \"orderNumber\","
				+

				" o.userId AS \"userId\","
				+ "  o.state AS \"state\","
				+ " o.demandName AS \"demandName\","
				+ "	o.orderTitle AS \"orderTitle\","
				+ "   o.userName AS \"userName\","
				+ "o.bossformno ,"
				+ "   o.draftTime AS \"draftTime\","
				+ " o.TRANSMITSTATE as \"transmitState\", 1 as  \"f\""
				+ "		FROM"
				+ "			SingOrderForm o"
				+ "		WHERE"
				+ "			1 = 1"
				+ "		AND (o.userId = '"
				+ USER.getRowNo()
				+ "'     or o.draftmanId = '"
				+ USER.getRowNo()
				+ "')"
				+ "		AND o.\"TYPE\" = 'DI'"
				+ "		AND( o.state = '1' or o.state = '3') "
				+ "		AND o.PARENTORDERNUMBER IS NOT NULL"
				+ " ) "
				+ " ) x where 1=1 AND (x.\"transmitState\" <> '0' or x.\"transmitState\" is null)   ";

		if (name != null && !"".equals(name)) {
			sql += " and x.\"orderTitle\" like '%" + name + "%'  ";
		}
		if (number != null && !"".equals(number)) {
			sql += " and x.\"orderNumber\" like '%" + number + "%'  ";
		}

		if(!StringUtils.isEmpty(state)){
			
			sql +=" and x.\"state\"= '"+state+"'";
		}
		sql += " order by x.\"draftTime\" DESC ";
		/*
		 * List<OrderForm>
		 * of=getSession().createQuery("from OrderForm").setFirstResult
		 * (page.getStartNumber()).setMaxResults( page.getPageSize()).list();
		 */

		/*
		 * sql += "  and  (o.draftmanId='" + USER.getRowNo() + "' or o.userId='"
		 * + USER.getRowNo() +
		 * "') and o.\"TYPE\"='专'   and o.PARENTORDERNUMBER  is null  order by o.draftTime DESC "
		 * ;8
		 */
		return getMap(sql, page);
	}

	/**
	 * 根据id 查询
	 * 
	 * @param id
	 * @return
	 */
	public OrderForm queryId(String id) {
		String sql = "from OrderForm o  where o.orderId=? ";
		return (OrderForm) getSession().createQuery(sql).setString(0, id)
				.uniqueResult();
	}
	
	
	public OrderTask queryIdtwo(String id) {
		String sql = "from OrderTask o  where o.id=? ";
		return (OrderTask) getSession().createQuery(sql).setString(0, id)
				.uniqueResult();
	}
	
	public OrderForm queryOrderByNum(String id,String cid) {
		String sql = "from OrderForm o  where o.orderNumber=? and o.orderTypeIdent=? and o.type=? and o.draftmanId=?";
		return (OrderForm) getSession().createQuery(sql).setString(0, id).setString(1,"1").setString(2, "DI").setString(3, cid)
				.uniqueResult();
	}

	public OrderForm queryOrderByNum(String id) {
		String sql = "from OrderForm o  where o.orderNumber=? and o.orderTypeIdent=? and o.type=? and o.state <> -1";
		return (OrderForm) getSession().createQuery(sql).setString(0, id).setString(1,"1").setString(2, "DI")
				.uniqueResult();
	}

	public List<Map<String, String>> fuJian(String id) {
		String sql = "select distinct  ah.ATTACHMENTID as \"id\",ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from SingOrderForm  o  "
				+ " inner join  SingleAndAttachment oa  on o.parentordernumber=OA.orderID "
				+ " inner JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.parentordernumber=?";

		return getSession().createSQLQuery(sql)
				.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
				.setString(0, id).list();
	}

	// 列表展示 展示 当前订单经理的已经通过的订单
	public PageResponse eaDolist(OrderForm order, Integer employeeNo,
			PageRequest page) {
		String sql = "SELECT * from SingOrderForm o  where o.type='DI'  and o.USERID='"
				+ employeeNo + "' ";
		return getPage(OrderForm.class, sql, page);
	}

	/**
	 * 保存 意见信息
	 * 
	 * @param opinion2
	 */
	public Opinion saveOpinion(Opinion opinion2) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(opinion2);
			session.flush();
			return opinion2;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public List<LinkTemplate> querylink(String pCode, String operationType) {
		String sql = "select * from LINKTEMPLATE l  "
				+ " LEFT JOIN  Dictionary_table b "
				+ " on l.OPERATIONTYPE =b.DICTIONARYID "
				+ "where l.PARENTLINK <> '1' and l.STATE='1' and l.PCODE=?  and b.CODE=? ";
		return getSession().createSQLQuery(sql).addEntity(LinkTemplate.class)
				.setString(0, pCode).setString(1, operationType).list();
	}

	/**
	 * 保存
	 * 
	 * @param ss
	 */
	public OrderStages saveOrderStages(OrderStages ss) {

		try {
			Session session = this.getSession();
			session.saveOrUpdate(ss);
			session.flush();
			return ss;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 根据订单id 删除工作台数据
	 * 
	 * @param orderId2
	 */
	public void delectlink(String orderId2) {
		String sql = "delete from ORDERSTAGES s where s.ORDERID=?";
		Session session = this.getSession();
		session.createSQLQuery(sql).setString(0, orderId2).executeUpdate();
		session.flush();
	}

	/**
	 * 更改工作台状态根据工作台id 来修改
	 * 
	 * @param orderId2
	 * @param string
	 */
	public void updateOrder(String orderId2, String uuid, String sta) {
		String sql = "UPDATE ORDERSTAGES	o SET o.STAGESTATE = ? WHERE o.ORDERID = ? "
				+ "   " + "and o.uuid= ? ";
		getSession().createSQLQuery(sql).setString(0, sta)
				.setString(1, orderId2).setString(2, uuid).executeUpdate();

	}

	/**
	 * 更改工作台状态根据环节模板id 来判断
	 * 
	 * @param orderId2
	 * @param linkId
	 */
	public void updateOrderlinkId(String orderId2, String linkId, String sta) {
		String sql = "UPDATE ORDERSTAGES	o SET o.STAGESTATE = ? WHERE o.ORDERID = ? "
				+ "   " + "and o.TWOSTAGEID= ? ";
		getSession().createSQLQuery(sql).setString(0, sta)
				.setString(1, orderId2).setString(2, linkId).executeUpdate();

	}

	/**
	 * 更改工作台的时间根据环节模板id
	 * 
	 * @param orderId2
	 * @param linkId
	 */
	public void updateOrderdate(String orderId, String linkId) {
		String sql = "update ORDERSTAGES o set o.ACTUALCOMPLETIONTIME=? where o.ORDERID=? and o.TWOSTAGEID=? ";
		/*
		 * String sql =
		 * "UPDATE ORDERSTAGES	o SET o.STAGESTATE = ? WHERE o.ORDERID = ? " +
		 * "   " + "and o.TWOSTAGEID= ? ";
		 */
		getSession().createSQLQuery(sql).setDate(0, new Date())
				.setString(1, orderId).setString(2, linkId).executeUpdate();

	}

	/**
	 * 根据工作台id 来修改状态
	 * 
	 * @param orderId2
	 * @param linkId
	 */
	public void updateOrderdateid(String ORDERSTAGESid) {
		String sql = "update ORDERSTAGES o set o.ACTUALCOMPLETIONTIME=? where o.uuid=?";
		/*
		 * String sql =
		 * "UPDATE ORDERSTAGES	o SET o.STAGESTATE = ? WHERE o.ORDERID = ? " +
		 * "   " + "and o.TWOSTAGEID= ? ";
		 */
		getSession().createSQLQuery(sql).setDate(0, new Date())
				.setString(1, ORDERSTAGESid).executeUpdate();

	}

	/**
	 * 根据父级id 删除子订单
	 * 
	 * @param id
	 */
	public void delectOrder(String id) {
		String sql = "delete from SingOrderForm s where s.PARENTORDERNUMBER=?";
		Session session = this.getSession();
		session.createSQLQuery(sql).setString(0, id).executeUpdate();
		session.flush();
	}

	/**
	 * 获取当前 子订单的 父订单的 产品类型
	 * 
	 * @param id
	 * @return
	 */
	public List<Map<String, String>> queryProduectBName(String id) {
		String sql = "select s.PCODE ,PR.PRODUCTNAME,s.uuid  from  SINGORDERFORM s  "
				+ "LEFT JOIN PRODUCTTYPE pr on  s.PCODE=PR.PCODE    "
				+ "where s.PARENTORDERNUMBER=?";

		return getSession().createSQLQuery(sql)
				.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
				.setString(0, id).list();
	}

	/**
	 * 获取当前 子订单的 产品类型
	 * 
	 * @param id
	 * @return
	 */
	public List<Map<String, String>> queryProduectName(String id) {
		String sql = "select s.PCODE ,PR.PRODUCTNAME  from  SINGORDERFORM s  "
				+ "LEFT JOIN PRODUCTTYPE pr on  s.PCODE=PR.PCODE    "
				+ "where s.UUID=?";

		return getSession().createSQLQuery(sql)
				.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
				.setString(0, id).list();
	}
	
	/**
	 * 业务 和 产品；
	 * 
	 * @param id
	 * @return
	 */
	public List<Map<String, String>> queryBaAndProtypeName(String id) {
		String sql = "select s.PCODE ,PR.PRODUCTNAME,s.uuid as productid,b.businessid,b.BCODE,b.BUSINESSNAME  from  SINGORDERFORM s  LEFT JOIN PRODUCTTYPE pr on  s.PCODE=PR.PCODE inner join businesstype b on s.bcode=b.bcode  where s.UUID=?";

		return getSession().createSQLQuery(sql)
				.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
				.setString(0, id).list();
	}

	/**
	 * BOSS查询环节
	 */
	public List<OrderStages> bossQueryOrderStagesById(String id) {
		try {
			return (List<OrderStages>) getSession()
					.createSQLQuery(
							"select * from OrderStages l where  l.orderId=?")
					.addEntity(OrderStages.class).setString(0, id).list();
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 根据BOSS返回结果修改数据
	 */
	public void updateOrderStagesBoss(OrderStages s) {
		getSession().update(s);
	}

	public StartPreOrderOut saveStartPreOrder(StartPreOrderOut orderOut) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(orderOut);
			session.flush();
			return orderOut;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 
	 * @param getpCode
	 *            根据产品编码查询 产品类型
	 * @return
	 */
	public ProductType queryProductType(String getpCode) {
		String sql = "select * from PRODUCTTYPE pr where PR.PCODE=?";
		return (ProductType) getSession().createSQLQuery(sql)
				.addEntity(ProductType.class).setString(0, getpCode)
				.uniqueResult();
	}

	public List<Map<String, String>> queryIdList(String id) {
		String sql = "select s.OPERATIONTYPE,s.uuid \"id\", GR.groupCoding,s.ORDERTITLE,s.ORDERNUMBER,GR.groupName,GR.contacts,GR.contactPhone,"
				+ " s.draftman "
				+ " ,suer.mobile,"
				+ " s.userName,"
				+ " suses.mobile as \"MOBILE1\" "
				+ " from  SINGORDERFORM s "
				+ " LEFT JOIN  GroupCustomer gr "
				+ " on s.GROUPCUSTOMERID=GR.GROUPID"
				+ " left join AFR_SYSTEMUSER suer"
				+ " on suer.rowno=s.draftmanId"
				+ " LEFT JOIN AFR_SYSTEMUSER suses"
				+ " on suses.rowno=s.userId" + " where s.PARENTORDERNUMBER=? ";
		Session session = getSession();
		List<Map<String, String>> lo = session.createSQLQuery(sql)
				.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
				.setString(0, id).list();
		/*
		 * Clob ss=(Clob) session.createSQLQuery(
		 * "select s.ORDERREQDESCRIPTION from SINGORDERFORM s where s.UUID=?"
		 * ).setString(0, lo.get(0).get("id")).uniqueResult(); String content
		 * =ClobToString(ss);//3.将news对象中的clob类型的content转化为String字符串
		 * 
		 * List<Map<String, String>> ss=new List<Map<String,String>>() { }; for
		 * (Map<String, String> map : lo) {
		 * 
		 * } Map<String, String> ms=new HashMap<String, String>();
		 * ms.put("ORDERREQDESCRIPTION", content); lo.add(ms);
		 */

		// session.flush();
		return lo;
	}

	public OrderInformation saveOrderinfor(OrderInformation orderinfor) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(orderinfor);
			return orderinfor;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 根据子订单 获取父id的订单
	 */
	public OrderForm queryFId(String id) {
		String sql = " select * from SINGORDERFORM s where s.UUID=(select sis.parentOrderNumber from SINGORDERFORM sis where sis.uuid=?) ";
		// Object s= this.getSession().createSQLQuery(sql).setParameter(0,
		// id).uniqueResult();
		return (OrderForm) getSession().createSQLQuery(sql)
				.addEntity(OrderForm.class).setString(0, id).uniqueResult();

	}

	public OrderInformation queryOrderInformation(String id) {
		String sql = "select * from ORDERINFORMATION  o where o.ORDERID=? ";
		return (OrderInformation) getSession().createSQLQuery(sql)
				.addEntity(OrderInformation.class).setString(0, id)
				.uniqueResult();
	}

	public static String ClobToString(Clob clob) {
		String clobStr = "";
		Reader is = null;
		try {
			is = clob.getCharacterStream();
			// 得到流
			BufferedReader br = new BufferedReader(is);
			String s = null;
			s = br.readLine();
			StringBuffer sb = new StringBuffer();
			// 执行循环将字符串全部取出赋值给StringBuffer，由StringBuffer转成String
			while (s != null) {
				sb.append(s);
				s = br.readLine();
			}
			clobStr = sb.toString();
		} catch (IOException e) {
			e.printStackTrace();
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return clobStr;
	}

	/*
	 * private InternetThings things; private InternetChange change; private
	 * ImsChange imsChange; private ImsTranslate translate;
	 */
	public InternetThings saveThings(InternetThings things) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(things);
			session.flush();
			return things;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public ImsTranslate saveimTranslate(ImsTranslate translate) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(translate);
			session.flush();
			return translate;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public ImsChange saveimsChange(ImsChange imsChange) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(imsChange);
			session.flush();
			return imsChange;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public InternetChange savechange(InternetChange change) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(change);
			session.flush();
			return change;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	// select * from ImsChange ic where
	// IC.orderid='8ae69aee589c07af01589c39383e0024'
	// select * from InternetChange cc where
	// cc.orderid='8ae69aee589c07af01589c39383e0024'
	// select * from InternetThings ic where
	// IC.orderid='8ae69aee589c07af01589c39383e0024'
	// select * from ImsTranslate ic where
	// IC.orderid='8ae69aee589c07af01589c39383e0024'

	public ImsChange queryImsChange(String id) {
		String sql = "	select * from ImsChange ic where IC.orderid=? ";
		return (ImsChange) getSession().createSQLQuery(sql)
				.addEntity(ImsChange.class).setString(0, id).uniqueResult();
	}

	public InternetChange queryInternetChange(String id) {
		String sql = "	select * from InternetChange cc where cc.orderid=?";
		return (InternetChange) getSession().createSQLQuery(sql)
				.addEntity(InternetChange.class).setString(0, id)
				.uniqueResult();
	}

	public InternetThings queryInternetThings(String id) {
		String sql = "	select * from InternetThings ic where IC.orderid=? ";
		return (InternetThings) getSession().createSQLQuery(sql)
				.addEntity(InternetThings.class).setString(0, id)
				.uniqueResult();
	}

	public ImsTranslate queryImsTranslate(String id) {
		String sql = "	select * from ImsTranslate ic where IC.orderid=? ";
		return (ImsTranslate) getSession().createSQLQuery(sql)
				.addEntity(ImsTranslate.class).setString(0, id).uniqueResult();
	}

	public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
			if(sa.getId()==null){
				String sql = "select  * from SingleAndAttachment t where t.orderid=? and t.attachmentid=? and t.link=?";
				Object count= getSession().createSQLQuery(sql).setString(0, sa.getOrderID()).setString(1, sa.getAttachmentId()).setString(2, sa.getLink()).uniqueResult();
				if(null==count){
					Session session = this.getSession();
					session.saveOrUpdate(sa);
					session.flush();
					return sa;
				}else{
					return null;
				}
			}else{
				Session session = this.getSession();
				session.saveOrUpdate(sa);
				session.flush();
				return sa;
			}
	}

	/**
	 * 根据环节id 获取环节
	 * 
	 * @param id
	 * @return
	 */
	public LinkTemplate querylinkId(String id) {
		String sql = "select * from LINKTEMPLATE l where l.LINKTEMPCODE=? ";
		return (LinkTemplate) getSession().createSQLQuery(sql)
				.addEntity(LinkTemplate.class).setString(0, id).uniqueResult();
	}
//	public LinkTemplate querylinkId(String id) {
//		String sql = "select * from LINKTEMPLATE l where l.LINKID=? ";
//		return (LinkTemplate) getSession().createSQLQuery(sql)
//				.addEntity(LinkTemplate.class).setString(0, id).uniqueResult();
//	}

	/**
	 * 客户验收
	 * 
	 * @param acceptance
	 * @return
	 */
	public CustomerAcceptance saveAcceptance(CustomerAcceptance acceptance) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(acceptance);
			session.flush();
			return acceptance;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public CustomerAcceptance queryAccept(String id) {
		String sql = "select * from CUSTOMERACCEPTANCE  c where c.ORDERID=?";
		return (CustomerAcceptance) getSession().createSQLQuery(sql)
				.addEntity(CustomerAcceptance.class).setString(0, id)
				.uniqueResult();
	}

	public OrderSheet saveOrderSheet(OrderSheet orderSheet) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(orderSheet);
			session.flush();
			return orderSheet;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public BusinessType queryBcode(String PRODUCTID) {
		String sql = "SELECT * FROM BUSINESSTYPE  B LEFT JOIN PRODUCTTYPE PR  ON PR.BUSINESSID = B.BUSINESSID  WHERE  PR.PCODE=? ";
		return (BusinessType) getSession().createSQLQuery(sql)
				.addEntity(BusinessType.class).setString(0, PRODUCTID)
				.uniqueResult();
	}

	/**
	 * 查看评分
	 */
	public OrderSheet querySheet(String id) {
		String sql = "select * from ORDERSHEET where ORDERID=? ";
		return (OrderSheet) getSession().createSQLQuery(sql)
				.addEntity(OrderSheet.class).setString(0, id).uniqueResult();
	}

	public PageResponse deleteList(OrderForm order, String orderNumber,String name, PageRequest page){

	
		String sql = "SELECT * from " + "	 (" + "		(" + "			SELECT"
				+ "				o.uuid AS \"orderId\","
				+ "				o.draftmanId AS \"draftmanId\","
				+ "				o.orderNumber AS \"orderNumber\","
				+ "				o.userId AS \"userId\"," + "				o.state AS \"state\","
				+ "				o.demandName AS \"demandName\","
				+ "				o.orderTitle AS \"orderTitle\","
				+ "				o.userName AS \"userName\","
				+ "				o.draftTime AS \"draftTime\","
				+ "				o.TRANSMITSTATE AS \"transmitState\","
				+ "				0 AS \"f\"" + "			FROM " + "				SingOrderForm o"
				+ "			WHERE " + "				1 = 1" + "			AND o.\"TYPE\" = 'DI'"
				+ "			AND o.PARENTORDERNUMBER  is not NULL" + "		)"
				+ "		UNION ALL" + "			(" + "				SELECT"
				+ "					o.uuid AS \"orderId\","
				+ "					o.draftmanId AS \"draftmanId\","
				+ "					o.orderNumber AS \"orderNumber\","
				+ "					o.userId AS \"userId\"," + "					o.state AS \"state\","
				+ "					o.demandName AS \"demandName\","
				+ "					o.orderTitle AS \"orderTitle\","
				+ "					o.userName AS \"userName\","
				+ "					o.draftTime AS \"draftTime\","
				+ "					o.TRANSMITSTATE AS \"transmitState\","
				+ "					1 AS \"f\"" + "				FROM" + "					SingOrderForm o"
				+ "				WHERE" + "					1 = 1" + "				AND o.\"TYPE\" = 'CS'"
				+ "			)" + "	) x" + " WHERE " + "	1 = 1";
		if (orderNumber != null && !"".equals(orderNumber)) {
			String[] stroun = orderNumber.split(",");
			String ordernum ="";
		if(stroun.length>0){
			for (int i = 0; i < stroun.length; i++) {
				if(i==0){
					ordernum+="'"+stroun[i]+"'";
				}else {
					ordernum+=",'"+stroun[i]+"'";
				}
			}
		}
			sql += " 		and x.\"orderNumber\" in (" + ordernum + ")";
		}
		if (name != null && !"".equals(name)) {
			sql += " 	and x.\"orderTitle\" like '%" + name + "%'";

		}
		sql += "ORDER BY x.\"state\" desc,x.\"draftTime\" DESC";
		return getMap(sql, page);
	}

	public PageResponse demandList(String demandNumber, String name,
			PageRequest page, SystemUser user) {
		String sql = "	SELECT " + "	o.uuid AS \"orderId\", "
				+ "	o.draftmanId AS \"draftmanId\", "
				+ "	o.orderNumber AS \"orderNumber\", "
				+ "	o.userId AS \"userId\", " + "	o.state AS \"state\", "
				+ "	o.demandName AS \"demandName\", "
				+ "	o.orderTitle AS \"orderTitle\", "
				+ "	o.userName AS \"userName\", "
				+ "	o.draftTime AS \"draftTime\", "
				+ "	o.TRANSMITSTATE AS \"transmitState\" " + " from "
				+ "	SingOrderForm o " + " WHERE " + "	1 = 1 "
				+ " AND o.draftmanId = '" + user.getRowNo() + "' "
				+ " AND o.\"TYPE\" = 'DI' and o.state != '-1' "
				+ " AND o.PARENTORDERNUMBER IS NULL AND o.TRANSMITSTATE<>0";
		if (demandNumber != null && !"".equals(demandNumber)) {
			sql += " 	and o.ORDERNUMBER like '%" + demandNumber + "%' ";
		}
		if (name != null && !"".equals(name)) {
			sql += " 	and o.DEMANDNAME like '%" + name + "%'";

		}
		sql += " ORDER BY o.draftTime DESC";
		return getMap(sql, page);
	}
	
	//需求单草稿
	public PageResponse demandListone(String demandNumber, String name,
			PageRequest page, SystemUser user) {
		String sql = "	SELECT " + "	o.uuid AS \"orderId\", "
				+ "	o.draftmanId AS \"draftmanId\", "
				+ "	o.orderNumber AS \"orderNumber\", "
				+ "	o.userId AS \"userId\", " + "	o.state AS \"state\", "
				+ "	o.demandName AS \"demandName\", "
				+ "	o.orderTitle AS \"orderTitle\", "
				+ "	o.userName AS \"userName\", "
				+ "	o.draftTime AS \"draftTime\", "
				+ "	o.TRANSMITSTATE AS \"transmitState\",o.PARENTORDERNUMBER AS \"orid\" " + " from "
				+ "	SingOrderForm o " + " WHERE " + "	1 = 1 "
				+ " AND o.draftmanId = '" + user.getRowNo() + "' "
				+ " AND o.\"TYPE\" = 'DI' and o.state != '-1' "
				+ " AND o.PARENTORDERNUMBER IS NOT NULL AND o.TRANSMITSTATE=0";
		if (demandNumber != null && !"".equals(demandNumber)) {
			sql += " 	and o.ORDERNUMBER like '%" + demandNumber + "%' ";
		}
		if (name != null && !"".equals(name)) {
			sql += " 	and o.DEMANDNAME like '%" + name + "%'";

		}
		sql += " ORDER BY o.draftTime DESC";
		return getMap(sql, page);
	}

	public String queryOrderUser(String id, int userId) {
		String sql = "select count(*) from SINGORDERFORM so where SO.uuid=? and SO.USERID=? ";
		return getSession().createSQLQuery(sql).setString(0, id)
				.setInteger(1, userId).uniqueResult().toString();
	}

	public Map<String, String> queryBasics(Integer id) {
		String sql = "SELECT"
				+ "  asm.COMPANY_NAME, "
				+ "sd.DEPARTMENT_NAME "
				+ " FROM "
				+ "AFR_SYSTEMUSER ss "
				+ "LEFT JOIN AFR_SYSTEM_DEPT_USER sdu ON ss.ROWNO = SDU.ROWNO "
				+ "left join AFR_SYSTEMDEPT sd on sdu.DEPARTMENT_NO=sd.DEPARTMENT_NO "
				+ "LEFT JOIN AFR_SYSTEMCOMPANY asm ON asm.COMPANY_CODE = sd.COMPANY_CODE "
				+ "where SS.ROWNO=?";
		return (Map<String, String>) getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setInteger(0, id).uniqueResult();
	}

	public List<LinkTemplate> queryProcFlow(String pCode, String operationType) {
		String sql = "select * from linktemplate lt1 where lt1.parentlinktempcode in (select lt.linktempcode from  linktemplate lt inner join  productflow pf on pf.pflowcode=lt.pflowcode  where pf.pcode=? and pf.otcode=?)";
		return getSession().createSQLQuery(sql).addEntity(LinkTemplate.class).setString(0, pCode).setString(1, operationType).list();
	}
	
	
	public Integer queryDeleteOrderStates(String oid){
		String sql = "delete from orderstages t where t.orderid=?";
		return getSession().createSQLQuery(sql).setString(0, oid).executeUpdate();
	}

	/**
	 * 
	 * @param otp 操作类型code
	 * @param ptp 产品类型code 
	 * @return
	 */
	public List<Map<Object,Object>> queryOTAndPT(String otp, String ptp) {
		String sql =" select t2.linkname,t2.linksort,t2.linktempcode from linktemplate t1 right join linktemplate t2 on t2.parentlinktempcode=t1.linktempcode where t2.state='1'and t1.pflowcode in ( select t.pflowcode from  productflow t where t.pcode=? and t.otcode=?)";
		List<Map<Object,Object>> list=this.getSession().createSQLQuery(sql).setParameter(0, ptp).setParameter(1, otp).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		return list;
	}
	public List<Map<Object,Object>> queryPType() {
		String sql ="select t.productname as pname,t.pcode from producttype t where t.appliestype ='DI' ";
		List<Map<Object,Object>> list=this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		return list;
	}

	public PageResponse querySpeialOrder(Map<String, String> map,PageRequest page) {
		String sql ="";
		String sqlparam="";
		if(map.get("company_ibm")!=null){
			sqlparam+=" and mv.COMPANY_CODE='"+map.get("company_ibm")+"' ";
			/*if(Integer.parseInt(map.get("company_ibm"))<10){
				sqlparam+=" and mv.COMPANY_NAME='0"+Integer.parseInt(map.get("company_ibm"))+"' ";
			}else{
				sqlparam+=" and mv.COMPANY_NAME='"+Integer.parseInt(map.get("company_ibm"))+"' ";
			}*/
			
		}
		if(map.get("deptno")!=null){
			sqlparam+="  and mv.TWODNO='"+map.get("deptno").trim()+"'";
		}
		if(map.get("otp")!=null){
			sqlparam+="	 and mv.operationtype='"+map.get("otp").trim()+"' ";
		}
		if(map.get("ptp")!=null){
			sqlparam+="	 and mv.pcode='"+map.get("ptp").trim()+"'";
		}
		
		if(map.get("statTime")!=null && map.get("endTime")!=null && !"".equals(map.get("statTime"))&& !"".equals(map.get("endTime"))){
			sqlparam+=" and (to_char(mv.drafttime,'yyyy-mm-dd')  BETWEEN '"+map.get("statTime").trim()+"' AND '"+map.get("endTime").trim()+"') ";
		}
		
		
		String sqlpr =" select t2.linktempcode from linktemplate t1 right join linktemplate t2 on t2.parentlinktempcode=t1.linktempcode where t2.state='1' and t1.pflowcode in ( select t.pflowcode from  productflow t where t.pcode=? and t.otcode=?)";
		List<Map<String,String>> list=this.getSession().createSQLQuery(sqlpr).setParameter(0, map.get("ptp")).setParameter(1, map.get("otp")).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		String parsin = "";
		List lisp = new ArrayList();
		for(int i=0;i<list.size();i++){
			Map<String,String> objects= list.get(i);
			lisp.add(objects.get("LINKTEMPCODE").trim());
			 if(i==0){
				 parsin+="'"+objects.get("LINKTEMPCODE").trim()+"' "+objects.get("LINKTEMPCODE").trim();
			 }else{
				 parsin+=",'"+objects.get("LINKTEMPCODE").trim()+"' "+objects.get("LINKTEMPCODE").trim();
			 }
		}
		
		if("1".equals(map.get("leval"))){
			lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
//			lisp.add("twostageid");
//			lisp.add("PCODE");
//			lisp.add("OPERATIONTYPE");
//			lisp.add("COMPANY_IBM");
//			lisp.add("HS");
			sql ="select * from (select "
					+ "mv.company_name,"
					+ "mv.company_code,"
					+ "mv.company_ibm,"
					+ "mv.twostageid,"
					+ "mv.pcode,"
					+ "mv.operationtype,"
					+" substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end))  as hs "
				//	+ "case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
				//		" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs "
						+ " from specialorderform_mv mv"
						+ " where 1=1 "+sqlparam
						+"group by  mv.company_name,mv.company_code,mv.company_ibm,mv.twostageid,mv.pcode,mv.operationtype  order by mv.company_code asc"
						+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
			
	    } else if("2".equals(map.get("leval"))){
	    	lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
			//lisp.add("TWOSTAGEID");
			lisp.add("PCODE");
			lisp.add("TWODNAME");
			//lisp.add("OPERATIONTYPE");
			//lisp.add("HS");
			lisp.add("COMPANY_IBM");
			lisp.add("TWODNO");
	    	sql ="select * from (select mv.company_name,mv.company_code,mv.company_ibm,mv.twodname,mv.twostageid,mv.pcode,mv.operationtype, mv.twodno,"
	    			+" substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end)) as hs "	//+ "case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
					//" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs  "
					+ "from specialorderform_mv mv"
					+ " where 1=1 "+sqlparam
					+"group by  mv.company_name,mv.company_code,mv.company_ibm,mv.twodname,mv.twostageid,mv.pcode, mv.twodno,mv.operationtype order by mv.company_code asc"
					+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
		
	    } else if("3".equals(map.get("leval"))){
	    	lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
			//lisp.add("TWOSTAGEID");
			lisp.add("PCODE");
			lisp.add("OPERATIONTYPE");
			//lisp.add("HS");
			lisp.add("COMPANY_IBM");
			lisp.add("TWODNAME");
			lisp.add("ROWNO");
			lisp.add("EMPLOYEE_NAME");
			lisp.add("TWODNO");
	    	sql ="select * from (select  mv.rowno,mv.employee_name,mv.company_ibm,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode,mv.operationtype, mv.twodno,"
	    			+" substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end)) as hs "	//+ " case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
					//" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs "
					+ " from specialorderform_mv mv"
					+ " where 1=1 "+sqlparam
					+"group by   mv.rowno,mv.employee_name,mv.company_ibm,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode ,mv.twodno,mv.operationtype order by mv.company_code asc"
					+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
	    }else if("4".equals(map.get("leval"))){
	    	lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
			//lisp.add("TWOSTAGEID");
			lisp.add("PCODE");
			lisp.add("OPERATIONTYPE");
			//lisp.add("HS");
			lisp.add("COMPANY_IBM");
			lisp.add("TWODNAME");
			lisp.add("ROWNO");
			lisp.add("EMPLOYEE_NAME");
			lisp.add("DEMANDNAME");
			lisp.add("ORDERNUMBER");
			lisp.add("UUID");
			lisp.add("TWODNO");
	    	sql ="select * from (select mv.demandname,mv.ordernumber,mv.company_ibm,mv.uuid,mv.rowno,mv.employee_name,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode,mv.operationtype, mv.twodno,"
	    			+"substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end)) as hs "//+ "case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
					//" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs "
					+ " from specialorderform_mv mv"
					+ " where 1=1 "+sqlparam
					+"group by  mv.demandname,mv.ordernumber,mv.company_ibm,mv.rowno,mv.uuid,mv.employee_name,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode, mv.twodno,mv.operationtype order by mv.company_code asc"
					+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
	    }
		
	String sqlcou = "select count(0) from ("+sql+")";
			return getMaponeBy(sql, page,sqlcou,lisp);
		
	}
	public LayuiPage querySpeialOrder(Map<String, String> map,LayuiPage page) {
		String sql ="";
		String sqlparam="";
		if(map.get("company_ibm")!=null){
			sqlparam+=" and mv.COMPANY_CODE='"+map.get("company_ibm")+"' ";
			/*if(Integer.parseInt(map.get("company_ibm"))<10){
				sqlparam+=" and mv.COMPANY_NAME='0"+Integer.parseInt(map.get("company_ibm"))+"' ";
			}else{
				sqlparam+=" and mv.COMPANY_NAME='"+Integer.parseInt(map.get("company_ibm"))+"' ";
			}*/
			
		}
		if(map.get("deptno")!=null){
			sqlparam+="  and mv.TWODNO='"+map.get("deptno").trim()+"'";
		}
		if(map.get("otp")!=null){
			sqlparam+="	 and mv.operationtype='"+map.get("otp").trim()+"' ";
		}
		if(map.get("ptp")!=null){
			sqlparam+="	 and mv.pcode='"+map.get("ptp").trim()+"'";
		}
		
		if(map.get("statTime")!=null && map.get("endTime")!=null && !"".equals(map.get("statTime"))&& !"".equals(map.get("endTime"))){
			sqlparam+=" and (to_char(mv.drafttime,'yyyy-mm-dd')  BETWEEN '"+map.get("statTime").trim()+"' AND '"+map.get("endTime").trim()+"') ";
		}
		
		
		String sqlpr =" select t2.linktempcode from linktemplate t1 right join linktemplate t2 on t2.parentlinktempcode=t1.linktempcode where t2.state='1' and t1.pflowcode in ( select t.pflowcode from  productflow t where t.pcode=? and t.otcode=?)";
		List<Map<String,String>> list=this.getSession().createSQLQuery(sqlpr).setParameter(0, map.get("ptp")).setParameter(1, map.get("otp")).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		String parsin = "";
		List lisp = new ArrayList();
		for(int i=0;i<list.size();i++){
			Map<String,String> objects= list.get(i);
			lisp.add(objects.get("LINKTEMPCODE").trim());
			if(i==0){
				parsin+="'"+objects.get("LINKTEMPCODE").trim()+"' "+objects.get("LINKTEMPCODE").trim();
			}else{
				parsin+=",'"+objects.get("LINKTEMPCODE").trim()+"' "+objects.get("LINKTEMPCODE").trim();
			}
		}
		
		if("1".equals(map.get("leval"))){
			lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
//			lisp.add("twostageid");
//			lisp.add("PCODE");
//			lisp.add("OPERATIONTYPE");
//			lisp.add("COMPANY_IBM");
//			lisp.add("HS");
			sql ="select * from (select "
					+ "mv.company_name,"
					+ "mv.company_code,"
					+ "mv.company_ibm,"
					+ "mv.twostageid,"
					+ "mv.pcode,"
					+ "mv.operationtype,"
					+" substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end))  as hs "
					//	+ "case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
					//		" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs "
					+ " from specialorderform_mv mv"
					+ " where 1=1 "+sqlparam
					+"group by  mv.company_name,mv.company_code,mv.company_ibm,mv.twostageid,mv.pcode,mv.operationtype  order by mv.company_code asc"
					+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
			
		} else if("2".equals(map.get("leval"))){
			lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
			//lisp.add("TWOSTAGEID");
			lisp.add("PCODE");
			lisp.add("TWODNAME");
			//lisp.add("OPERATIONTYPE");
			//lisp.add("HS");
			lisp.add("COMPANY_IBM");
			lisp.add("TWODNO");
			sql ="select * from (select mv.company_name,mv.company_code,mv.company_ibm,mv.twodname,mv.twostageid,mv.pcode,mv.operationtype, mv.twodno,"
					+" substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end)) as hs "	//+ "case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
					//" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs  "
					+ "from specialorderform_mv mv"
					+ " where 1=1 "+sqlparam
					+"group by  mv.company_name,mv.company_code,mv.company_ibm,mv.twodname,mv.twostageid,mv.pcode, mv.twodno,mv.operationtype order by mv.company_code asc"
					+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
			
		} else if("3".equals(map.get("leval"))){
			lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
			//lisp.add("TWOSTAGEID");
			lisp.add("PCODE");
			lisp.add("OPERATIONTYPE");
			//lisp.add("HS");
			lisp.add("COMPANY_IBM");
			lisp.add("TWODNAME");
			lisp.add("ROWNO");
			lisp.add("EMPLOYEE_NAME");
			lisp.add("TWODNO");
			sql ="select * from (select  mv.rowno,mv.employee_name,mv.company_ibm,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode,mv.operationtype, mv.twodno,"
					+" substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end)) as hs "	//+ " case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
					//" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs "
					+ " from specialorderform_mv mv"
					+ " where 1=1 "+sqlparam
					+"group by   mv.rowno,mv.employee_name,mv.company_ibm,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode ,mv.twodno,mv.operationtype order by mv.company_code asc"
					+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
		}else if("4".equals(map.get("leval"))){
			lisp.add("COMPANY_NAME");
			lisp.add("COMPANY_CODE");
			//lisp.add("TWOSTAGEID");
			lisp.add("PCODE");
			lisp.add("OPERATIONTYPE");
			//lisp.add("HS");
			lisp.add("COMPANY_IBM");
			lisp.add("TWODNAME");
			lisp.add("ROWNO");
			lisp.add("EMPLOYEE_NAME");
			lisp.add("DEMANDNAME");
			lisp.add("ORDERNUMBER");
			lisp.add("UUID");
			lisp.add("TWODNO");
			sql ="select * from (select mv.demandname,mv.ordernumber,mv.company_ibm,mv.uuid,mv.rowno,mv.employee_name,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode,mv.operationtype, mv.twodno,"
					+"substr(avg(mv.hour),0, (case when instr(avg(mv.hour),'.',1,1)=0 then length(avg(mv.hour)) else instr(avg(mv.hour),'.',1,1)+2 end)) as hs "//+ "case when avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) is null "+
					//" then 0 else avg(ceil((To_date(mv.actualcompletiontime , 'yyyy-mm-dd hh24-mi-ss') - To_date(mv.opertime , 'yyyy-mm-dd hh24-mi-ss')) * 24)) end hs "
					+ " from specialorderform_mv mv"
					+ " where 1=1 "+sqlparam
					+"group by  mv.demandname,mv.ordernumber,mv.company_ibm,mv.rowno,mv.uuid,mv.employee_name,mv.company_name,mv.company_code,mv.twodname,mv.twostageid,mv.pcode, mv.twodno,mv.operationtype order by mv.company_code asc"
					+ " ) pivot (avg(hs) for twostageid in ("+parsin+")) "	;
		}
		
		String sqlcou = "select count(0) from ("+sql+")";
		page.setCount(getCount(sqlcou));
		
		if(page.getCount()>0){
			page.setData(getPageList(sql, null, page));
		}
		return page;
		
	}
	
	public PageResponse getOrderlists(Map<String, String> map, PageRequest page) {
		String sql=""; 
			sql ="select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.demandname as \"orderName\",sof.ordernumber as \"orderNumber\","+
							" to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
							+ " getbcodename(sof.Bcode) as \"businessType\","+
							  " getpcodename(sof.Pcode) as \"productType\""
							+ ",(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
					    " case when sof.signedstatus is not null then "+
					     " case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
					        		 " else '已签约'end  "+
					         " else '--' end as \"signedStatus\""+
					    " ,to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
					    " to_char((select HANDLEUSERID from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid)) as \"currentHandlingPersonId\","+
					    " (select HANDLEUSERNAME from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid) as \"currentHandlingPerson\""+
					    " ,to_char('1') as \"type\",case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
					    " from singorderform sof where sof.ordernumber='"+map.get("ordernumber").trim()+"' and sof.parentordernumber is not null and sof.ordertypeident='1' and sof.type='DI' ";
				
		return getMapNoBy(sql, page);
	}
	public LayuiPage getOrderlists(Map<String, String> map, LayuiPage page) {
		String sql=""; 
		sql ="select sof.uuid as \"orderId\",sof.draftmanid,sof.draftman as \"orderSponsor\",sof.demandname as \"orderName\",sof.ordernumber as \"orderNumber\","+
				" to_char(sof.drafttime,'yyyy-mm-dd hh24:mi:ss') as \"orderGenerationTime\",(select DIC.NAME as \"name\" from DICTIONARY_TABLE dic where DIC.TYPE='OT' and dic.code=sof.operationtype) as \"operationType\","
				+ " getbcodename(sof.Bcode) as \"businessType\","+
				" getpcodename(sof.Pcode) as \"productType\""
				+ ",(select gc.Groupname from GroupCustomer gc where gc.groupid=sof.groupcustomerid) as \"demandUnitName\","+
				" case when sof.signedstatus is not null then "+
				" case when sof.signedstatus='SSTNotSigned _0' then '未签约'"+
				" else '已签约'end  "+
				" else '--' end as \"signedStatus\""+
				" ,to_char(sof.orderreqtimelimit,'yyyy-mm-dd hh24:mi:ss') as \"orderCompletionTime\","+
				" to_char((select HANDLEUSERID from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid)) as \"currentHandlingPersonId\","+
				" (select HANDLEUSERNAME from (select wt.handleuserid,wt.handleusername,wt.taskid,ROW_NUMBER() OVER(PARTITION BY wt.taskid ORDER BY wt.creationTime DESC) as \"num\" from waittask wt where wt.code='DL') where \"num\"=1 and TASKID= sof.uuid) as \"currentHandlingPerson\""+
				" ,to_char('1') as \"type\",case when sof.state='1' then '已处理' else '未处理' end as \"state\" "+
				" from singorderform sof where sof.ordernumber='"+map.get("ordernumber").trim()+"' and sof.parentordernumber is not null and sof.ordertypeident='1' and sof.type='DI' ";
		page.setCount(getCount("select count(0) from ("+sql+")"));
		if(page.getCount()>0){
			page.setData(getPageList(sql, null, page));
		}
		return page;
	}
	
	public LinkTemplate getLinkTempLate(String linkId) {
		// TODO Auto-generated method stub
		String sql = "select * from LINKTEMPLATE where LINKTEMPCODE=?";
		return (LinkTemplate) getSession().createSQLQuery(sql).addEntity(LinkTemplate.class).setString(0, linkId).uniqueResult();
	} 
	
	//根据code查询角色
	public Role getRoleByNname(String code){
		String sql = "select * from system_role where name =?";
		Role systemRole = (Role)this.getSession().createSQLQuery(sql).addEntity(Role.class).setString(0, code).uniqueResult();
		return systemRole;
	}
	
	public void saveObjone(String opinionRole, String date, String opinion,
			String orderId, String employeeName, int rowNo,
			String linkNameCode, String zhuangtai) {
		// TODO Auto-generated method stub
		String zhuangtaione="进行中";
		String sql = "UPDATE OPINION O SET O.role='"+opinionRole+"',o.creationTime=to_date('"+date+"','yyyy-mm-dd HH24:MI:SS'),o.opinion='"+opinion+"'"
				+ ",o.whetherThrough='"+zhuangtai+"' where o.orderId=? and o.personnel=? and o.personnelNo=? and o.identification=? and whetherThrough=?";
		this.getSession().createSQLQuery(sql).setString(0, orderId).setString(1, employeeName).setInteger(2, rowNo)
					.setString(3, linkNameCode).setString(4, zhuangtaione).executeUpdate();
	}
	
	public void saveObjtwo(String opinionRole, String date, String opinion,
			String orderId, String employeeName, int rowNo,
			String linkNameCode, String zhuangtai) {
		// TODO Auto-generated method stub
		String sql = "UPDATE OPINION O SET O.role='"+opinionRole+"',o.creationTime=to_date('"+date+"','yyyy-mm-dd HH24:MI:SS'),o.opinion='"+opinion+"'"
				+ ",o.whetherThrough='"+zhuangtai+"' where o.orderId='"+orderId+"' "
						+ "and o.identification='"+linkNameCode+"' and whetherThrough='进行中' and role='"+opinionRole+"'";
		this.getSession().createSQLQuery(sql).executeUpdate();
	}
	
	public void saveObj(Object obj)throws Exception{
		this.getSession().save(obj);
	}

	public Opinion getOpinion(String orderId2, String upgrouplevel) {
		// TODO Auto-generated method stub
		String sql = "select * from OPINION where ROLE='【订单经理】' and "
				+ "IDENTIFICATION='"+upgrouplevel+"' and WHETHERTHROUGH='进行中' and ORDERID='"+orderId2+"'";
		return (Opinion) getSession().createSQLQuery(sql).addEntity(Opinion.class).uniqueResult();
	}
	/**
	 * 根据id查询imsforminfo
	 * @param id
	 * @return
	 */
	public CustomerAcceptance findImsFormInfoById(String id) throws Exception {
		String sql = "from CustomerAcceptance o  where o.orderId=? ";
		return (CustomerAcceptance) getSession().createQuery(sql).setString(0, id)
				.uniqueResult();
	}
	
	/**
	 * 客户验收
	 * 
	 * @param acceptance
	 * @return
	 */
	public void saveAcceptanceone(String acceptance,String orderID) {
		String sql = "UPDATE CustomerAcceptance o SET o.applyAcceptanceInterpretation='"+acceptance+"'  where o.orderId=? ";
		this.getSession().createQuery(sql).setString(0,orderID).executeUpdate();
	}

	public WaitTask getWaitTask(String imsbusinessacceptance, String orderId2){
		String sql = "select * from WAITTASK where TASKID='"+orderId2+"' and LINKNAMECODE='"+imsbusinessacceptance+"' and STATE=0";
		return (WaitTask) getSession().createSQLQuery(sql).addEntity(WaitTask.class).uniqueResult();
	}

	public OrderForm queryOrderformById(String params) {
		String sql = "select * from singorderform sf where sf.uuid=? and sf.ordertypeident='1'";
		return (OrderForm) getSession().createSQLQuery(sql).addEntity(OrderForm.class).setParameter(0, params).uniqueResult();
	}

	/**
	 * 根据订单编号和boss编号查询：
	 * @param orderNumber
	 * @param bossFormNo
	 * @return
	 */
/*	public Object queryMessagesXmlByNumAndBossNo(String orderNumber, String bossFormNo) {
		String sql = " select mx.ordernumber,mx.bossformno,dbms_lob.substr(mx.messtextxml),mx.updatetime,mx.ratingvalue from messagesxml mx   where mx.ordernumber=? and mx.bossformno=? and  ( mx.ratingvalue1 != '0' or mx.ratingvalue1 is null) ";
		return getSession().createSQLQuery(sql).setParameter(0, orderNumber).setParameter(1, bossFormNo).uniqueResult();
	}
	
	public List<Map<Object, Object>> queryMessagesXmlByNumAndBossNo_1(String orderNumber, String bossFormNo){
		String sql = " select mx.ordernumber,mx.bossformno,dbms_lob.substr(mx.messtextxml) as MESSTEXTXML,mx.updatetime,mx.ratingvalue from messagesxml mx   where mx.ordernumber=? and mx.bossformno=? and  ( mx.ratingvalue1 != '0' or mx.ratingvalue1 is null) ";
		List<Map<Object,Object>> list=this.getSession().createSQLQuery(sql).setParameter(0, orderNumber).setParameter(1, bossFormNo).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		
		return list;
	}*/
	public MessagesXml queryMessagesXmlByNumAndBossNo(String orderNumber, String bossFormNo) {
		Criteria criteria=this.getSession().createCriteria(MessagesXml.class);
		criteria.add(Restrictions.eq("orderNumber", orderNumber));
		criteria.add(Restrictions.eq("bossFormNo", bossFormNo));
		criteria.add(Restrictions.or(Restrictions.isNull("ratingValue1"), Restrictions.not(Restrictions.eq("ratingValue1", "0"))));
		return  (MessagesXml) criteria.uniqueResult();
	}
	@SuppressWarnings("unchecked")
	public List<MessagesXml> queryMessagesXmlByNumAndBossNo_1(String orderNumber, String bossFormNo){
		Criteria criteria=this.getSession().createCriteria(MessagesXml.class);
		criteria.add(Restrictions.eq("orderNumber", orderNumber));
		criteria.add(Restrictions.eq("bossFormNo", bossFormNo));
		criteria.add(Restrictions.or(Restrictions.isNull("ratingValue1"), Restrictions.not(Restrictions.eq("ratingValue1", "0"))));
		return  criteria.list();
	}
	
	

	/**
	 *根据订单编号和boss编号更新,代办个数：
	 * @param orderNumber
	 * @param bossFormNo
	 * @param count
	 */
	public void updateMessagesXmlByNumAndBossNo(String orderNumber, String bossFormNo,String count) {
		if(!StringUtils.isEmpty(orderNumber)&&!StringUtils.isEmpty(bossFormNo)){
			String sql = "update messagesxml set ratingvalue=?,ratingvalue1=? where ordernumber= ? and bossformno=?";
			this.getSession().createSQLQuery(sql).setParameter(0, "1").setParameter(1, count).setParameter(2, orderNumber).setParameter(3, bossFormNo).executeUpdate();
		}
		
	}

	/**
	 * 更新关联表：
	 * @param orderId2
	 * @param zproductidp
	 */
	public void updateZPCode(String orderId2, String zproductidp) {
		String sql="update bis_orderdetail t set t.orderid=?,t.modifytime=sysdate where t.odetailid=?";
		this.getSession().createSQLQuery(sql).setParameter(0, orderId2).setParameter(1, zproductidp).executeUpdate();
		
	}

	public String isBossBu(String id) {
		String sql ="select * from bis_orderdetail t where t.orderid=?";
		Object od =  this.getSession().createSQLQuery(sql).setParameter(0, id) .uniqueResult();
		if(od!=null){
			return "1";
		}else{
			return "0";
		}
	}
	@SuppressWarnings("unchecked")
	public Integer clerEipById(String wid){
		try{
			List<WaitTask> wts = new ArrayList<WaitTask>();
			String sql ="select  * from waittask t where t.state='0' and t.waitid=? ";
			wts=getSession().createSQLQuery(sql).addEntity(WaitTask.class).setParameter(0, wid).list();
			for(int i=0;i<wts.size();i++){
				WaitTask  wt = wts.get(i);
						String receiver = wt.getHandleLoginName();
					    //以下是同步待办到EIP(删除)
						String xmlres;
							 xmlres = EOMAxisService.getInstance().getEipTaskTodoDelXml(wt.getTaskId(), wt.getName(), receiver);
					    String ssokey = EOMAxisService.getInstance().getSSOKey();
					    if(xmlres!=null && ssokey != null){
					    	Params SSOKeyParams = new Params();
					    	SSOKeyParams.setParameterName("RegSSOKey");
					    	SSOKeyParams.setParameterValue(ssokey);
							Params xmlParams = new Params();
							xmlParams.setParameterName("XMLData");
							xmlParams.setParameterValue(xmlres);
							Object obj =  EOMAxisService.getInstance().EipTaskTodoAdd(SSOKeyParams,xmlParams);
							//将返回结果保存
							wt.setEipDelResult(obj.toString());
							
							if(obj.toString().equals("11")){
								return 1;
							}
					    }
			}
		}catch(Exception e){
			e.printStackTrace();
			return 0;
		}
		return null;
	}

	public OrderForm copy(String orderId2) {
		// TODO Auto-generated method stub
		String sql = "from OrderForm o  where o.orderId=? ";
		return (OrderForm) getSession().createQuery(sql).setString(0, orderId2).uniqueResult();
	}
	
	public OrderForm copyone(String orderId2) {
		// TODO Auto-generated method stub
		String sql = "from OrderForm o  where o.parentOrderNumber=? ";
		return (OrderForm) getSession().createQuery(sql).setString(0, orderId2).uniqueResult();
	}

	public List<SingleAndAttachment> getSingleAndAttachment(String orderId2) {
		// TODO Auto-generated method stub
		String sql ="from SingleAndAttachment s where s.orderID=?";
		List<SingleAndAttachment> or = getSession().createQuery(sql).setString(0, orderId2).list();
		return or;
	}

	public OrderDetail getOrderDetail(String orderId2) {
		// TODO Auto-generated method stub
		String sql = "from OrderDetail o  where o.orderId=? ";
		return (OrderDetail) getSession().createQuery(sql).setString(0, orderId2).uniqueResult();
	}
	/**
	 * 复制产品信息
	 * @param orderDetail
	 */
	public void addOrderDetail(OrderDetail orderDetail){
		this.getSession().save(orderDetail);
	}
	
	/**
	 * 复制产品信息
	 * @param orderDetail
	 */
	public OrderDetail addOrderDetailtwo(OrderDetail orderDetail){
		try {
			Session session = this.getSession();
			session.save(orderDetail);
			return orderDetail;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	
	public OrderForm saveOrderform(OrderForm oi) {
		try {
			Session session = this.getSession();
			session.save(oi);
			return oi;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}

	public void saveSandB(String orderId2, String attachmentId,
			String demandApplication) {
		// TODO Auto-generated method stub
		String sql2 = "UPDATE ORDERSTAGES	o SET o.STAGESTATE = ? WHERE o.ORDERID = ? "
				+ "   " + "and o.uuid= ? ";
		this.getSession().createSQLQuery(sql2).setString(0, "").setString(1, "").setString(2, "").executeUpdate();
		
	}

	public void deleteCopy(String orid) {
		// TODO Auto-generated method stub
		String sql = "delete from singorderform where uuid=?";
		String sql2  ="delete from singorderform where parentOrderNumber=?";
		String sql3 ="delete from SingleAndAttachment where orderID=?";
		this.getSession().createSQLQuery(sql).setString(0, orid).executeUpdate();
		this.getSession().createSQLQuery(sql2).setString(0, orid).executeUpdate();
		this.getSession().createSQLQuery(sql3).setString(0, orid).executeUpdate();
		
	} 
	public Boolean queryByStateDate() {
		Boolean isR = true;
		try{
			String sql =" select sf.ordernumber,op.identification,op.creationtime,op.startime from singorderform sf  right join opinion op on op.orderid=sf.uuid "+
					    " where  sf.type='DI' and sf.state in ('1','2','3','0','-2')  and sf.ordertypeident='1' "
					    + " and  sf.pcode not in (select pcode from producttype where isauditstate ='1')   group by sf.ordernumber,sf.state,op.identification,op.creationtime,op.startime order by op.creationtime asc";
		@SuppressWarnings("unchecked")
		List<Object[]> list = this.getSession().createSQLQuery(sql).
				addScalar("ordernumber", Hibernate.STRING)    
	            .addScalar("identification", Hibernate.STRING)    
	            .addScalar("creationtime", Hibernate.TIMESTAMP)    
	            .addScalar("startime", Hibernate.TIMESTAMP).list();
		String sql_ot = " select sof.ordernumber,ot.opertime,lt.linkname, "+
						 " case  "+
						 "     when ot.twostageid like '%JTZXXQSQ%' then 'Demand_application' "+
						 "      when ot.twostageid like '%JTZXZYCK%' then '' "+
						 "    when  ot.twostageid like '%JTZXZFSQ%'then 'Tariff_application' "+
						 "    when ot.twostageid like '%JTZXYQD%' then '' "+
						 "    when ot.twostageid like '%JTZXZXBJ%' then '' "+
						 "    when ot.twostageid like '%JTZXZXYS%' then '' "+
						 "    when ot.twostageid like '%JTZXKHJF%' then '' "+
						 "    when  ot.twostageid like '%JTZXKHYS%'then 'acceptance' "+
						 "    "+
						 "    when  ot.twostageid like '%WLWYXHD%'then 'itoMarketingActivitiesProcess' "+
						 "    when  ot.twostageid like '%WLWZFSQ%'then 'itoExpenseApplyProcess' "+
						 "    when  ot.twostageid like '%WLWYWBL%'then 'businsssessHandle' "+
						 "    when  ot.twostageid like '%WLWKHYS%'then 'itoBusinessacceptance' "+
						 "     "+
						 "      when  ot.twostageid like '%MASXQSQ%'then 'Demand_application' "+
						 "      when  ot.twostageid like '%MASJTDJTSQWSJ%'then 'upGroupLevel' "+
						 "      when  ot.twostageid like '%MASSXYQD%'then '' "+
						 "      when  ot.twostageid like '%MASYWDG%'then '' "+
						 "      when  ot.twostageid like '%MASYWKT%'then '' "+
						 "      when  ot.twostageid like '%MASYS%'then ''   "+
						 "      when  ot.twostageid like '%MASKHYSQR%'then 'acceptance'    "+
						 "      "+
						 "    when ot.twostageid like '%IMS_XQSQ%' then 'Demand_application' "+
						 "    when  ot.twostageid like '%IMSBKSQ%'then 'serviceReportProcess' "+
						 "    when  ot.twostageid like '%IMSHMDSQ%'then 'redNameListProcess' "+
						 "    when  ot.twostageid like '%IMSTHSQ%'then 'specialNumberProcess' "+
						 "    when  ot.twostageid like '%IMSYXHDSQ%'then 'marketingActivitiesProcesss' "+
						 "    when  ot.twostageid like '%IMSTSZFSQ%'then 'specialExpensesApplyProcess' "+
						 "    when  ot.twostageid like '%IMSYWPGSQ%'then 'businessAssessmentProcess' "+
						 "    when  ot.twostageid like '%IMSYWKT%'then 'businessopening' "+
						 "    when  ot.twostageid like '%IMSYWYS%'then 'iBusinessacceptance' "+
						 "    when  ot.twostageid like '%IMSKHYS%'then 'itoBusinessacceptance'  "+
						 "   else ot.twostageid  end as identification "+
						 "  from singorderform sof  right join orderstages ot on ot.orderid=sof.uuid "+
						 " left join linktemplate lt on lt.linktempcode=ot.twostageid "+
						 "  where  sof.type='DI' "
						 + " and sof.state in ('1','2','3','0','-2') and sof.ordertypeident='1' and  sof.pcode not in (select pcode from producttype where isauditstate ='1')  and ot.opertime is not null ";
		@SuppressWarnings("unchecked")
		List<Object[]> list_ot = this.getSession().createSQLQuery(sql_ot).
				addScalar("ordernumber", Hibernate.STRING)    
	            .addScalar("opertime", Hibernate.TIMESTAMP)    
	            .addScalar("linkname", Hibernate.STRING)    
	            .addScalar("identification", Hibernate.STRING).list();
		List<Object[]> list_old=list;
		
		
			for(Object[] obj:list){///1 2 ,2  3 4 5,2
				 String setP="";
				 Boolean isU =false;  //第一次
				for(Object[] obj_o:list_old){///3 4 5
					if(obj_o[0]!=null && obj[1]!=null && obj[2]!=null){
					
					if(obj_o[0].equals(obj[0]) && obj[1].equals(obj_o[1]) && obj[2].equals(obj_o[2]) && !isU){
						for(Object[] obj_ot:list_ot){
							 if(obj_o[0].equals(obj_ot[0])&&obj_o[1].equals(obj_ot[3])){
								 String sql_op =" update opinion op set op.startime=to_date(?,'yyyy-mm-dd hh24:mi:ss') "
									  		+ "where op.orderid=( select s.uuid from singorderform s where s.type='DI' "
									  		+ "and s.state in ('1','2','3','0','-2') and s.ordertypeident='1' and "
									  		+ " s.pcode not in (select pcode from producttype where isauditstate ='1')"
									  		+ " and s.ordernumber=?) and op.identification=? and to_char(op.creationtime,'yyyy-mm-dd hh24:mi:ss.ff') like ?";
										this.getSession().createSQLQuery(sql_op)
										.setParameter(0, obj_ot[1].toString().substring(0, obj_ot[1].toString().lastIndexOf(".")))
										.setParameter(1, obj_o[0])
										.setParameter(2, obj_o[1])
										.setParameter(3, obj_o[2]+"%").executeUpdate();
							 }
						}
						
						 DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");   
					        try {   
					        	if(obj_o[2]!=null){
					        		
					        		setP = sdf.format((Timestamp) obj_o[2]);   
					        	}
					        } catch (Exception e) {   
					            e.printStackTrace();   
					        }  
					        isU=true;
					}else if(obj_o[0].equals(obj[0]) && obj[1].equals(obj_o[1]) && !obj[2].equals(obj_o[2])){
						if("".equals(setP)){
							for(Object[] obj_ot:list_ot){
								 if(obj_o[0].equals(obj_ot[0])&&obj_o[1].equals(obj_ot[3])){
									 DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");   
								        try {   
								        	setP = sdf.format((Timestamp) obj_ot[1]);   
								        } catch (Exception e) {   
								            e.printStackTrace();   
								        }  
									
								 }
							}
						}
						
						 String sql_op =" update opinion op set op.startime=to_date(?,'yyyy-mm-dd hh24:mi:ss') where op.orderid=( select s.uuid from singorderform s where s.type='DI' and s.state in ('1','2','3','0','-2') and s.ordertypeident='1' and  s.pcode not in (select pcode from producttype where isauditstate ='1') and s.ordernumber=?) and op.identification=? and"
							  		+ " to_char(op.creationtime,'yyyy-mm-dd hh24:mi:ss.ff') like ?";
							 this.getSession().createSQLQuery(sql_op)
								.setParameter(0,setP)
								.setParameter(1, obj_o[0])
								.setParameter(2, obj_o[1])
								.setParameter(3, obj_o[2] +"%").executeUpdate();
						DateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");   
				        try {   
				        	if( obj_o[2]!=null){
				        		
				        		setP = sdf.format((Timestamp) obj_o[2]);   
				        	}
				        } catch (Exception e) {   
				            e.printStackTrace();   
				        }
					}
					}
				}
			}
		}catch(Exception e){
			e.printStackTrace();
			isR=false;
			throw new RuntimeException("事务回滚，aop");
		}
		
		return isR;
	} 
	public OrderDetail getOrderDetailOdetailId(String zproductidShow,String zpcode) {
		// TODO Auto-generated method stub
		String sql = "from OrderDetail o  where o.odetailId=? and o.zpcode=?";
		return (OrderDetail) getSession().createQuery(sql).setString(0, zproductidShow).setString(1,zpcode).uniqueResult();
	}

	public OrderDetail getOrderDetail(String id, String zpcode) {
		// TODO Auto-generated method stub
		String sql = "from OrderDetail o  where o.orderId=? and o.zpcode=?";
		return (OrderDetail) getSession().createQuery(sql).setString(0, id).setString(1, zpcode).uniqueResult();
	}
	
	public List<Object[]> getSystemDept(int userID) {
		String sql ="select "
				+ "case when dn1.DEPARTMENT_LEVEL=1 "
				+ "THEN as2.COMPANY_CODE "
				+ "else TO_CHAR(dn2.DEPARTMENT_NO) end,"
				+ "as2.COMPANY_CODE,"
				+ "TO_CHAR(dn1.DEPARTMENT_NO) "
				+ "from  AFR_SYSTEMDEPT dn1 "
				+ "left join AFR_SYSTEMDEPT dn2 on dn1.DEPARTMENT_PARENT_NO=dn2.DEPARTMENT_NO "
				+ "left join AFR_SYSTEMDEPT as1 on as1.DEPARTMENT_NO=dn2.DEPARTMENT_NO "
				+ "left join AFR_SYSTEM_DEPT_USER asdu on asdu.DEPARTMENT_NO=dn1.DEPARTMENT_NO "
				+ "left join AFR_SYSTEMCOMPANY as2 on as2.COMPANY_CODE=dn2.COMPANY_CODE "
				+ "where asdu.ROWNO=?";
		Query query=getSession().createSQLQuery(sql);
        query.setInteger(0, userID);  
        List<Object[]> s=query.list();  
		return s;
	}

	public void updateordertype(String orderId2) {
		//TODO Auto-generated method stub
		String sql ="UPDATE SINGORDERFORM SET STATE=-1 WHERE UUID=?";
		this.getSession().createSQLQuery(sql).setString(0, orderId2).executeUpdate();
		
	}


	public OrderTask queryOrderTask(String ord_id) {
		// TODO Auto-generated method stub
		String sql = "select * from OrderTask where id=?";
		return (OrderTask)this.getSession().createSQLQuery(sql).addEntity(OrderTask.class).setString(0, ord_id).uniqueResult();
		
	}


	public void setOrderFormOrOrdertask(String busCode, String orderNumber) {
		// TODO Auto-generated method stub
		String sql = "insert into BUS_ORDER(ID,BUSCODE,ORDERCODE) VALUES (sys_guid(),?,?)";
		getSession().createSQLQuery(sql).setString(0, busCode).setString(1, orderNumber).executeUpdate();
		
	}

	/**
	 * \
	* @Title：queryRunDates 
	* @Description： 节假日跑时间：
	* @param ：@return 
	* @return ：Boolean 
	* @throws
	*/
	@SuppressWarnings("unchecked")
	public List<OrderForm> queryRunDates() {
		List<OrderForm> orders =null;
		try{
			
		String hql="select * from singorderform  where state='1' and ordertypeident='1' ";
		
		 orders= this.getSession().createSQLQuery(hql).addEntity(OrderForm.class).list();
	
		}catch(Exception e){
			orders=null;
			throw new RuntimeException("事务回滚，aop");
		}
		return orders;
		 
	}
	/**
	 *查询方法
	* @Title：queryCount 
	* @Description：TODO
	* @param ：@param lundate
	* @param ：@param enddate
	* @param ：@return 
	* @return ：long 
	* @throws
	 */
	public  long queryCount(String lundate,String enddate){
		String sql ="select count(0) from gholiday where datev between to_date(?,'yyyy-mm-dd') and to_date(?,'yyyy-mm-dd') ";
		Object day =   this.getSession().createSQLQuery(sql).setParameter(0, lundate).setParameter(1, enddate).uniqueResult();
		return Long.parseLong(day.toString());
	}
	/**
	 * 添加和更新方法
	* @Title：updateTime_consume 
	* @Description：TODO
	* @param ：@param order
	* @param ：@param coun
	* @param ：@return 
	* @return ：Integer 
	* @throws
	 */
	public  Integer updateTime_consume(OrderForm order,long coun){
		String sql1 ="select count(0) from Time_consume where order_num=? and order_id=? and state=? ";
		Object day =   this.getSession().createSQLQuery(sql1)
				.setParameter(0, order.getOrderNumber())
				 .setParameter(1, order.getOrderId())
		       .setParameter(2, order.getState()).uniqueResult();
		Long cou=  Long.parseLong(day.toString());
		if(cou==0){
			
			String sql =" insert into Time_consume (ID, count, order_num, order_id, state) values (sys_guid(), ?,?,?,?)";
			Integer count= this.getSession().createSQLQuery(sql)
			 .setParameter(0, coun)
			 .setParameter(1, order.getOrderNumber())
			 .setParameter(2, order.getOrderId())
			 .setParameter(3, order.getState()).executeUpdate();
			return count;
		}else{
			String sql ="update Time_consume set count=? where order_num=? and  order_id=? and  state=?";
			Integer count= this.getSession().createSQLQuery(sql)
			 .setParameter(0, coun)
			 .setParameter(1, order.getOrderNumber())
			 .setParameter(2, order.getOrderId())
			 .setParameter(3, order.getState()).executeUpdate();
			return count;
			 
		}
	}
	
 
    public static void main(String[] args) {  
        
        String s = "<pre class=\"brush: java;\">";  
        System.out.println(StringEscapeUtils.escapeHtml(s));  
          
        String u = "<pre class=\"brush: java;\">";  
        System.out.println(StringEscapeUtils.unescapeHtml(u));  
        System.out.println(StringEscapeUtils.unescapeJava("\u4E2D\u56FD")); 
        String sql="1' or '1'='1";  
        System.out.println( StringEscapeUtils.escapeSql(sql)); // 
          
    }  
	public OrderDetail getOrderDetailtwo(String id){
		String sql = "from OrderDetail o where o.odetailId=? ";
		return (OrderDetail) getSession().createQuery(sql).setString(0, id).uniqueResult();
	}
 
	public void deleteOdetail(String id) {
		// TODO Auto-generated method stub
		String sql ="DELETE FROM BIS_ORDERDETAIL WHERE ODETAILID ='"+id+"'";
		this.getSession().createSQLQuery(sql).executeUpdate();
	}

	public OrderStages getOrderStages(String id, String linkId) {
		// TODO Auto-generated method stub
		String sql = "select * from ORDERSTAGES where ORDERID=? and TWOSTAGEID=?";
				getSession().createSQLQuery(sql).addEntity(OrderStages.class).setString(0, id).setString(0, linkId).uniqueResult();
		return null;
	}

	public void updateOrderStages(OrderStages orderstages) {
		// TODO Auto-generated method stub
		try {
			Session session = this.getSession();
			session.update(orderstages);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public OrderTask getOrderTask(String id){
		String sql = "from OrderTask where id=?";
		return (OrderTask)getSession().createQuery(sql).setString(0, id).uniqueResult();
	}

	public void saveBus_order(String busCode, String orderNumber) {
		// TODO Auto-generated method stub
		String sql = "INSERT INTO BUS_ORDER (ID, BUSCODE,ORDERCODE) VALUES (sys_guid(),'"+busCode+"','"+orderNumber+"')";
		getSession().createSQLQuery(sql).executeUpdate();
	}

	/**
	 * 根据订单编号查询商机编号和商机名称：
	 * @param orderNumber   订单编号
	 * @return  List<Map<String, String>>
	 */
	public List<Map<String, String>> queryBusCodeByOrdernum(String orderNumber) { 
		String sql = "select distinct t.BUSCODE,ot.ordertitle  from bus_order t inner join ordertask ot on ot.buscode=t.buscode  where t.ordercode=? "; 
		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setString(0, orderNumber).list();
	}
	
	public String updateOrderform() {
		Connection conn = null;
		String sql = "{call updateorderform()}";
		try {
			conn = SessionFactoryUtils.getDataSource(getSessionFactory()).getConnection();
			PreparedStatement pst = conn.prepareStatement(sql);
			conn.setAutoCommit(false);
			boolean s = pst.execute();
			conn.commit();
			pst.close();
			conn.close();
			return s+"";
		} catch (Exception e) {
			e.printStackTrace();
			return "NO";
		}
		
	}
	
	public String updateOrderStages() {
		Connection conn = null;
		String sql = "{call updateorderStages()}";
		try {
			conn = SessionFactoryUtils.getDataSource(getSessionFactory()).getConnection();
			PreparedStatement pst = conn.prepareStatement(sql);
			conn.setAutoCommit(false);
			boolean s = pst.execute();
			conn.commit();
			pst.close();
			conn.close();
			return s+"";
		} catch (Exception e) {
			e.printStackTrace();
			return "NO";
		}
		
	}
}

