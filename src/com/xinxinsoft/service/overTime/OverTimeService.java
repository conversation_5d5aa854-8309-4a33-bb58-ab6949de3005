package com.xinxinsoft.service.overTime;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

import javax.servlet.http.HttpServletRequest;


import org.apache.commons.lang.StringUtils;
import org.hibernate.transform.Transformers;

import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

/**
 * 超时：SERVICE
 * <AUTHOR>
 *
 */
public class OverTimeService extends BaseService{

	public String queryOverTimeOrder() {
		String sql = "select s.UUID as \"orderId\",s.ORDERNUMBER as \"orderNumber\",s.ORDERTITLE as \"orderTitle\",s.DRAFTMANID as \"draftmanid\", s.DRAFTMAN as \"draftman\",s.DRAFTTIME as \"draftTime\",s.TYPE as \"type\",s.PARENTORDERNUMBER as \"parentOrderNumber\"   "
				+ " from ( select sss.uuid,sss.ordernumber,sss.ordertitle,sss.orderreqtimelimit,sss.draftmanid,sss.draftman,sss.drafttime,sss.transmitstate,sss.ordercompletiontime,sss.type,sss.parentordernumber from (select  t.uuid,t.ordernumber,t.ordertitle,t.orderreqtimelimit,t.draftmanid,t.draftman,t.drafttime,t.transmitstate,t.ordercompletiontime,t.type,t.parentordernumber, "+
					 "case when t.ordercompletiontime is not null then "+
					  "    case when t.ordercompletiontime > t.orderreqtimelimit then '1' else '0' end  "+
					" else  case when  sysdate > t.orderreqtimelimit then '1' else '0'  end   "+   
					 "end case from singorderform t  where t.state <> '-1' and t.transmitstate= '1' and t.ordertypeident='1' order by t.orderreqtimelimit asc,t.drafttime desc) sss where sss.case='1' ) s where ROWNUM<=10 ";
		 @SuppressWarnings("unchecked")
		List<Map<Object, Object>> lists = this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		String json = JSONHelper.SerializeWithNeedAnnotationDateFormat(lists);
		return json;
	}

	/**
	 * 获取当前时间：格式转换：默认格式：yyyy-MM-dd HH:mm:ss
	 * @param str 格式参数：null 
	 * @return  date 时间：
	 */
	public String getDateToString(String str){
		Date d = new Date();
		SimpleDateFormat sdf = null;
		if(str==null){
			 sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		}else{
			sdf= new SimpleDateFormat(str);
		}
		try {
			return sdf.format(d);
		} catch (Exception e) {
			throw new Error(" date Exception throw ");
		}
	}
	
	/**
	 * 根据用户Id查询公司首拼字母：
	 * @param userId
	 * @return
	 */
	public String queryCompanyCode(Integer userId){
		String sql="select  usr.company_code from (select  ausr.rowno,ausr.employee_no,sc.company_name,sc.company_ibm,sc.company_code from  afr_systemuser ausr inner join afr_system_dept_user asdu on asdu.rowno=ausr.rowno inner join afr_systemdept sd on sd.department_no = asdu.department_no inner join afr_systemcompany sc on sc.company_code=sd.company_code  ) usr  where usr.rowno=?";
		@SuppressWarnings("unchecked")
		List<Object[]> lisobj=  getSession().createSQLQuery(sql).setInteger(0, userId).list();
		String retStr="";
		for(Object obj : lisobj){
			retStr= obj.toString();
		}
		return  retStr;
	
	}

	private  ResourceBundle s = ResourceBundle .getBundle("WebService-config");
	private String audit = s.getString("AUDIT_INTERS_ORDER_SWITCH");
	
	public PageResponse getOrderlist(Map<String, String> map, PageRequest page,
			SystemUser user, HttpServletRequest httpServletRequest) {
		List<SystemDept> deptList = user.getSystemDept();
		  String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();//公司首字母缩写简称
			 String sql ="";
			 String pars="";
			 String auditStr="";
			 if(!StringUtils.isEmpty(map.get("companyCode"))){
					companyCode=map.get("companyCode");
					if(!StringUtils.isEmpty(map.get("likename"))){
						pars="and t.company_code='"+companyCode+"' and t.\"orderName\" like '%"+map.get("likename").trim()+"%'";
					}else{
						pars="and t.company_code='"+companyCode+"' ";
					}
					
				}else{
					 if(StringUtils.isEmpty(companyCode)){
						  companyCode=queryCompanyCode(user.getRowNo());
					 }
					 if("00".equals(companyCode)){
						 if(!StringUtils.isEmpty(map.get("likename"))){
							 pars=" and  t.\"orderName\" like '%"+map.get("likename").trim()+"%' ";
							 	auditStr+="订单名称："+map.get("likename").trim()+" ";
							 }else{
								 pars=" ";
							 }
						 }else{
							 if(!StringUtils.isEmpty(map.get("likename"))){
								 pars="and t.company_code='"+companyCode+"' and t.\"orderName\" like '%"+map.get("likename").trim()+"%'";
								 auditStr+="订单名称："+map.get("likename").trim()+" ";
							 }else{
								 pars="and t.company_code='"+companyCode+"' ";
								 }
						 }
				}
			 
			 if(!"".equals(map.get("starDate"))&&map.get("starDate")!=null && !StringUtils.isEmpty(map.get("starDate"))){
				  if(!"".equals(map.get("endDate"))&&map.get("endDate")!=null && !StringUtils.isEmpty(map.get("endDate"))){
					  pars+=" and t.drafttime >= '"+String.valueOf(map.get("starDate"))+"' "
					  		+ " and t.drafttime <= '"+String.valueOf(map.get("endDate"))+"' ";
					  auditStr+="开始时间："+String.valueOf(map.get("starDate"))+" 结束时间："+String.valueOf(map.get("endDate"))+" ";
				  }else{
					  pars+=" and t.drafttime >= '"+String.valueOf(map.get("starDate"))+"' ";
					  auditStr+="开始时间："+String.valueOf(map.get("starDate"))+" ";
				  }
			  }else{
				  if(!"".equals(map.get("endDate"))&&map.get("endDate")!=null && !StringUtils.isEmpty(map.get("endDate"))){
					  pars+= " and t.drafttime <= '"+String.valueOf(map.get("endDate"))+"' ";
					  auditStr+="结束时间："+String.valueOf(map.get("starDate"))+" ";
				  }
			  }
			  
			  if("1".equals(map.get("isFiledSel"))){
				  pars +=" and t.isfeild='是' ";
				  auditStr+="订单是否归档：是 ";
			  }else if("0".equals(map.get("isFiledSel"))){
				  pars +=" and t.isfeild='否' ";
				  auditStr+="订单是否归档：否 ";
			  }
			  if(!StringUtils.isEmpty(map.get("keywordp"))){
				  if(!StringUtils.isEmpty(map.get("likename"))){
					  pars+=" or t.orderreqdescription like '%"+map.get("keywordp").trim()+"%' ";
				  }else {
					  pars="  and  (t.\"orderName\" like '%"+map.get("keywordp").trim()+"%' or t.orderreqdescription like '%"+map.get("keywordp").trim()+"%')";
				  }
				  
			  }
			 sql ="select t.\"orderNumber\",t.\"orderName\" ,t.drafttime,t.\"FullPath\",t.employee_name,t.\"orderHandler\",t.\"completionLimitTime\",t.\"completionTime\",t.\"businessType\",t.\"productType\",t.isfeild from overtimeorder_mv t  where 1=1 "
			 				+ pars + " order by t.drafttime desc,\"completionLimitTime\" asc";
			 if("start".equals(audit)){
				 if("1".equals(map.get("auditP"))){
					 if(!"".equals(auditStr)){
					 String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
						String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
				    	CommLogs.requOrderquery(unl, cun, "0",  auditStr, "", "", "", "", "", "7", DateUtil.getIpAddr(httpServletRequest));
					 }
				 }
			 }
		return getMapNoBy(sql, page);
	}
	public LayuiPage getOrderlist(Map<String, String> map, LayuiPage page,
			SystemUser user, HttpServletRequest httpServletRequest) {
		List<SystemDept> deptList = user.getSystemDept();
		String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();//公司首字母缩写简称
		String sql ="";
		String pars="";
		String auditStr="";
		if(!StringUtils.isEmpty(map.get("companyCode"))){
			companyCode=map.get("companyCode");
			if(!StringUtils.isEmpty(map.get("likename"))){
				pars="and t.company_code='"+companyCode+"' and t.\"orderName\" like '%"+map.get("likename").trim()+"%'";
			}else{
				pars="and t.company_code='"+companyCode+"' ";
			}
			
		}else{
			if(StringUtils.isEmpty(companyCode)){
				companyCode=queryCompanyCode(user.getRowNo());
			}
			if("00".equals(companyCode)){
				if(!StringUtils.isEmpty(map.get("likename"))){
					pars=" and  t.\"orderName\" like '%"+map.get("likename").trim()+"%' ";
					auditStr+="订单名称："+map.get("likename").trim()+" ";
				}else{
					pars=" ";
				}
			}else{
				if(!StringUtils.isEmpty(map.get("likename"))){
					pars="and t.company_code='"+companyCode+"' and t.\"orderName\" like '%"+map.get("likename").trim()+"%'";
					auditStr+="订单名称："+map.get("likename").trim()+" ";
				}else{
					pars="and t.company_code='"+companyCode+"' ";
				}
			}
		}
		
		if(!"".equals(map.get("starDate"))&&map.get("starDate")!=null && !StringUtils.isEmpty(map.get("starDate"))){
			if(!"".equals(map.get("endDate"))&&map.get("endDate")!=null && !StringUtils.isEmpty(map.get("endDate"))){
				pars+=" and t.drafttime >= '"+String.valueOf(map.get("starDate"))+"' "
						+ " and t.drafttime <= '"+String.valueOf(map.get("endDate"))+"' ";
				auditStr+="开始时间："+String.valueOf(map.get("starDate"))+" 结束时间："+String.valueOf(map.get("endDate"))+" ";
			}else{
				pars+=" and t.drafttime >= '"+String.valueOf(map.get("starDate"))+"' ";
				auditStr+="开始时间："+String.valueOf(map.get("starDate"))+" ";
			}
		}else{
			if(!"".equals(map.get("endDate"))&&map.get("endDate")!=null && !StringUtils.isEmpty(map.get("endDate"))){
				pars+= " and t.drafttime <= '"+String.valueOf(map.get("endDate"))+"' ";
				auditStr+="结束时间："+String.valueOf(map.get("starDate"))+" ";
			}
		}
		
		if("1".equals(map.get("isFiledSel"))){
			pars +=" and t.isfeild='是' ";
			auditStr+="订单是否归档：是 ";
		}else if("0".equals(map.get("isFiledSel"))){
			pars +=" and t.isfeild='否' ";
			auditStr+="订单是否归档：否 ";
		}
		if(!StringUtils.isEmpty(map.get("keywordp"))){
			if(!StringUtils.isEmpty(map.get("likename"))){
				pars+=" or t.orderreqdescription like '%"+map.get("keywordp").trim()+"%' ";
			}else {
				pars="  and  (t.\"orderName\" like '%"+map.get("keywordp").trim()+"%' or t.orderreqdescription like '%"+map.get("keywordp").trim()+"%')";
			}
			
		}
		sql ="select t.\"orderNumber\",t.\"orderName\" ,t.drafttime,t.\"FullPath\",t.employee_name,t.\"orderHandler\",t.\"completionLimitTime\",t.\"completionTime\",t.\"businessType\",t.\"productType\",t.isfeild from overtimeorder_mv t  where 1=1 "
				+ pars + " order by t.drafttime desc,\"completionLimitTime\" asc";
		if("start".equals(audit)){
			if("1".equals(map.get("auditP"))){
				if(!"".equals(auditStr)){
					String unl=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
					String cun=String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
					CommLogs.requOrderquery(unl, cun, "0",  auditStr, "", "", "", "", "", "7", DateUtil.getIpAddr(httpServletRequest));
				}
			}
		}
		page.setData(getPageList(sql, null, page));
		page.setCount(getCount("select count(0) from("+sql+")"));
		page.setCode(0);
		return page;
	}

	/**
	 * 获取所以公司：
	 * @return
	 */
	public Object getCompanyCodeAndName() {
		String sql = "select company_code,company_name from afr_systemcompany";
		return this.getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
	}

	public String queryRoot(SystemUser user) {
		 List<SystemDept> deptList = user.getSystemDept();
		String deptStr = deptList.get(0).getSystemCompany().getCompanyCode();
		
		 if(StringUtils.isEmpty(deptStr)){
			 deptStr=queryCompanyCode(user.getRowNo());
		 }
		 String result="";
		 if("00".equals(deptStr)){
			 result="1";
		 }else{
			 result="0";
		 }
		 String str ="{result:'"+result+"'}";
		return str;
	}
	
}
