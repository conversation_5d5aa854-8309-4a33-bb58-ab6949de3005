package com.xinxinsoft.service.knowledge;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.ftp.FTP;
import org.hibernate.transform.Transformers;

import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.knowledge.Knowledge;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.FtpUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.common.DefaultDao;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.easyh.PagePO;
import com.xinxinsoft.utils.easyh.PageRequest;
import com.xinxinsoft.utils.easyh.PageRequest.Like;
import com.xinxinsoft.utils.easyh.PageResponse;

/**
 * 知识库 案例 Service
 * <AUTHOR>
 * @date 2016-9-9
 */
public class KnowledgeService extends DefaultDao<Knowledge>{
	
	/**
	 * 新增
	 */
	public void addEntity(Knowledge entity){
		entity.setCreateDate(new Date());
		entity.setStatus(0);
		
		this.save(entity);
	}
	
	/**
	 * 设置知识库权限
	 * @param kelid
	 * @param roleid
	 */
	public void insertEntity(String kelid,String roleid){
		String sql = " insert into knowled_role (knowledgeid,roleid) values (?, ?) ";
		getSession().createSQLQuery(sql).setParameter(0, kelid).setParameter(1, roleid).executeUpdate();
	
	}
	
	/**
	 * 更新
	 * 
	 * @param entity
	 */
	public void updates(Knowledge entity) {
		getSession().update(entity);
	}
	/**
	 * 分页
	 * @throws Exception 
	 */
	public String getPageList(PageRequest req,SystemUser user,String knowledgeName,String knowledgeType ) throws Exception{
		req.setRows(req.getInteger("pagesize"));
		String hql = "from Knowledge k ";
		 hql= "  select * from( select knowledgeid,knowledgeName,describe,createDate,createUserNo,updateDate,status,knowledgeType,area from ( "+
				 " select  knowledg1_86_ as knowledgeId,knowledg2_86_ as  knowledgeName,describe86_ as describe,describe4_86_ as describeHtml,createDate86_ as createDate,createUs6_86_  as createUserNo,updateDate86_ as "+
				 " updateDate,status86_ as status,knowledg9_86_  as knowledgeType,area86_ as area from (   "+
				 " select * from ( select knowledge0_.knowledgeId as knowledg1_86_, knowledge0_.knowledgeName as knowledg2_86_,  "+
				 "  knowledge0_.describe as describe86_, knowledge0_.describeHtml as describe4_86_, knowledge0_.createDate as createDate86_, "+
				 "  knowledge0_.createUserNo as createUs6_86_, knowledge0_.updateDate as updateDate86_, knowledge0_.status as status86_, "+
				 "   knowledge0_.knowledgeType as knowledg9_86_, knowledge0_.area as area86_ from knowledge knowledge0_ where 1=1 and knowledge0_.status<>'-1' "+
				 "  and createuserno='"+user.getRowNo()+"'  )"+
				 "  union all  "+
         "   select * from ( select knowledge0_.knowledgeId as knowledg1_86_, knowledge0_.knowledgeName as knowledg2_86_, "+
         " knowledge0_.describe as describe86_, knowledge0_.describeHtml as describe4_86_, knowledge0_.createDate as createDate86_, "+
         "  knowledge0_.createUserNo as createUs6_86_, knowledge0_.updateDate as updateDate86_, knowledge0_.status as status86_,  "+
         "  knowledge0_.knowledgeType as knowledg9_86_, knowledge0_.area as area86_ from knowledge knowledge0_ where 1=1 and knowledge0_.status ='1' "+
         "   )  "+
         "  union all  "+
         "  select * from ( select knowledge0_.knowledgeId as knowledg1_86_, knowledge0_.knowledgeName as knowledg2_86_,  "+
         "   knowledge0_.describe as describe86_, knowledge0_.describeHtml as describe4_86_, knowledge0_.createDate as createDate86_ ,"+
         "  knowledge0_.createUserNo as createUs6_86_, knowledge0_.updateDate as updateDate86_, knowledge0_.status as status86_,  "+
         "  knowledge0_.knowledgeType as knowledg9_86_, knowledge0_.area as area86_ from knowledge knowledge0_ inner join "+
         "  knowledgesystemuser ku on ku.knowledgeid=knowledge0_.knowledgeid where 1=1 and knowledge0_.status ='0'  and ku.systemuserid='"+user.getRowNo()+"'"+
         "  ) "+
         "  )  "+
         "  )group by knowledgeid,knowledgeName,describe,createDate,createUserNo,updateDate,status,knowledgeType,area  HAVING knowledgeid not in (select r.knowledgeid from knowled_role r where   r.roleid in (   select sr.name from system_user_role ur inner join system_role sr on sr.id=ur.role_id where ur.row_no='"+user.getRowNo()+"'))) where 1=1 ";
		String countHql = "select count(*) from Knowledge k";
		countHql="select count(0) from (select knowledgeid,knowledgeName,describe,createDate,createUserNo,updateDate,status,knowledgeType,area from  (select  knowledg1_86_ as knowledgeId,knowledg2_86_ as  knowledgeName,describe86_ as describe,describe4_86_ as describeHtml,createDate86_ as createDate,createUs6_86_  as createUserNo,updateDate86_ as updateDate,status86_ as status,knowledg9_86_ as knowledgeType,area86_ as area from ( select * from ( select knowledge0_.knowledgeId as knowledg1_86_, knowledge0_.knowledgeName as knowledg2_86_, "+
				 " knowledge0_.describe as describe86_, knowledge0_.describeHtml as describe4_86_, knowledge0_.createDate as createDate86_, "+
				 "  knowledge0_.createUserNo as createUs6_86_, knowledge0_.updateDate as updateDate86_, knowledge0_.status as status86_,  "+
				 " knowledge0_.knowledgeType as knowledg9_86_, knowledge0_.area as area86_ from knowledge knowledge0_ where 1=1 and knowledge0_.status<>'-1' "+
				 " and createuserno='"+user.getRowNo()+"' )  "+
				 " union all  "+
				 " select * from ( select knowledge0_.knowledgeId as knowledg1_86_, knowledge0_.knowledgeName as knowledg2_86_, "+
				 " knowledge0_.describe as describe86_, knowledge0_.describeHtml as describe4_86_, knowledge0_.createDate as createDate86_, "+
				 "  knowledge0_.createUserNo as createUs6_86_, knowledge0_.updateDate as updateDate86_, knowledge0_.status as status86_,  "+
				 "  knowledge0_.knowledgeType as knowledg9_86_, knowledge0_.area as area86_ from knowledge knowledge0_ where 1=1 and knowledge0_.status ='1' "+
				 "   )  "+
				 " union all  "+
				 " select * from ( select knowledge0_.knowledgeId as knowledg1_86_, knowledge0_.knowledgeName as knowledg2_86_, "+
				 "  knowledge0_.describe as describe86_, knowledge0_.describeHtml as describe4_86_, knowledge0_.createDate as createDate86_, "+
				 "  knowledge0_.createUserNo as createUs6_86_, knowledge0_.updateDate as updateDate86_, knowledge0_.status as status86_,  "+
				 "  knowledge0_.knowledgeType as knowledg9_86_, knowledge0_.area as area86_ from knowledge knowledge0_ inner join "+
				 " knowledgesystemuser ku on ku.knowledgeid=knowledge0_.knowledgeid where 1=1 and knowledge0_.status ='0' and ku.systemuserid='"+user.getRowNo()+"' "+
				 " ))  ) group by knowledgeid,knowledgeName,describe,createDate,createUserNo,updateDate,status,knowledgeType,area "
				 + " HAVING knowledgeid not in (select r.knowledgeid from knowled_role r where   r.roleid in (   select sr.name from system_user_role ur inner join system_role sr on sr.id=ur.role_id where ur.row_no='"+user.getRowNo()+"')))  where 1 = 1  ";
	//	req.setQuery(hql); 
		//req.setQueryCount(countHql);
		/*req.setTargetClass(Map.class);
		req.appendQueryWhere(" where 1 = 1  ");  
		 if(req.addParamter("knowledgeName", String.class,true,Like.full)){
			req.appendQueryWhere(" and  knowledgeName like ?");
		}
		if(req.addParamter("knowledgeType",String.class,true,Like.full)){
			
			req.appendQueryWhere(" and  knowledgeType like ?");
		} */
		PagePO page = req.getPagePo();
		 
		if(knowledgeName!=null&&!"".equals(knowledgeName)){
			hql +=" and  knowledgeName like ? ";
			countHql+=" and  knowledgeName like ? ";
				if(knowledgeType!=null&&!"".equals(knowledgeType)){
					hql +=" and knowledgeType like ? ";
					countHql+=" and knowledgeType like ? "; 
				    page.setData(getSession().createSQLQuery(hql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setParameter(0, "%"+knowledgeName+"%").setParameter(1, knowledgeType+"%").list());
				    page.setTotNumber(Integer.parseInt(getSession().createSQLQuery(countHql).setParameter(0, "%"+knowledgeName+"%").setParameter(1, knowledgeType+"%").uniqueResult().toString()));
				} else{
					 page.setData(getSession().createSQLQuery(hql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setParameter(0, "%"+knowledgeName+"%").list());
					    page.setTotNumber(Integer.parseInt(getSession().createSQLQuery(countHql).setParameter(0, "%"+knowledgeName+"%").uniqueResult().toString()));
				}
		} else{
			if(knowledgeType!=null&&!"".equals(knowledgeType)){
				hql +=" and knowledgeType like ? ";
				countHql+=" and knowledgeType like ? "; 
			    page.setData(getSession().createSQLQuery(hql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setParameter(0, "%"+knowledgeType+"%").list());
			    page.setTotNumber(Integer.parseInt(getSession().createSQLQuery(countHql).setParameter(0, "%"+knowledgeType+"%").uniqueResult().toString()));
			} else{
				 	page.setData(getSession().createSQLQuery(hql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list());
				    page.setTotNumber(Integer.parseInt(getSession().createSQLQuery(countHql).uniqueResult().toString()));
			}
		} 
	    PageResponse res = new PageResponse();

		res.setPagePo(page);
		String json =JSONHelper.SerializeWithNeedAnnotationDateFormat(res); 
		return json;
		
	}
	
	
	/**
	 * 当前登陆人分页
	 * @throws Exception 
	 */
	public String getPageUserList(PageRequest req,SystemUser user,String knowledgeName,String knowledgeType) throws Exception{
	   
		 String sql ="select * from KNOWLEDGE k  "
		 	//	+ " left join "
		 	//	+ " KNOWLEDGESYSTEMUSER ks on KS.KNOWLEDGEID=k.KNOWLEDGEID  "
		 		//+ " where KS.SYSTEMUSERID="+user.getRowNo()+" ";
		 		 + " where k.createuserno="+user.getRowNo()+" ";
	
		 String count="select count(*) from KNOWLEDGE k "
		 	//	+ "   left join KNOWLEDGESYSTEMUSER ks on KS.KNOWLEDGEID=k.KNOWLEDGEID "
		 	//	+ " where KS.SYSTEMUSERID="+user.getRowNo()+" ";
		 + " where k.createuserno="+user.getRowNo()+" ";
		 
		 
        if(knowledgeName!=null&&!"".equals(knowledgeName)){
        	sql+=" and k.knowledgeName like '%"+knowledgeName+"%' ";
        } 
        if(knowledgeType!=null&&!"".equals(knowledgeType)){
        	sql+=" and k.knowledgeType like '%"+knowledgeType+"%' ";
        } 
	    PagePO page = req.getPagePo();
	    
	    
	    page.setData(getSession().createSQLQuery(sql).addEntity(Knowledge.class).list());
	    page.setTotNumber(Integer.parseInt(getSession().createSQLQuery(count).uniqueResult().toString()));
		
	    PageResponse res = new PageResponse();

		res.setPagePo(page);
		String json =JSONHelper.SerializeWithNeedAnnotationDateFormat(res);
		System.out.println(json);
		return json;
	}
	
	
	
	/**
	 * 根据id查询实体
	 */
	public Knowledge getInfoById(String id){
		
		return this.findById(Knowledge.class, id);
	}
	
	/**
	 * 修改状态
	 */
	public void updateStatus(String id,HttpServletRequest httpServletRequest){
		Knowledge entity = this.getInfoById(id);
		entity.setStatus(1);
		this.update(entity);
		
		if("start".equals(audit)){
			String cun = String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
			String un= String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
			if(entity.getStatus()==1){
				CommLogs.requZSKquery(un, "0", entity.getKnowledgeName(), cun, "3",DateUtil.getIpAddr(httpServletRequest));
			}else{
				CommLogs.requZSKquery(un, "0", entity.getKnowledgeName(), cun, "1",DateUtil.getIpAddr(httpServletRequest));
			}
		}
		
	}
	
	/**
	 * 根据案例编号获取附件
	 */
	public List<Map<String, String>> getAttachmentByKnowledgeId(String id){
		/*Set<Attachment> attachmentSet = this.getInfoById(id).getAttachmentList();
		
		List<Attachment> attachmentList = new ArrayList<Attachment>(attachmentSet);*/
		List<Map<String, String>> map = this.getSession().createSQLQuery("select t.attachmentid as \"id\",a.realname as \"name\" from knowledgeattachmentrelation t inner join attachment a on a.attachmentid=t.attachmentid where t.knowledgeid=? ").setString(0, id).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		return  map;
		//return attachmentList;
	}
	private  ResourceBundle s = ResourceBundle .getBundle("WebService-config");
	
	private String audit = s.getString("AUDIT_INTERS_ZSK_SWITCH");
	/**
	 * 删除 (同时删除本地文件)
	 * @param httpServletRequest 
	 * @throws Exception 
	 */
	public void deleteById(String id, HttpServletRequest httpServletRequest) throws Exception{
		try{
			
		Knowledge entity = this.getInfoById(id);
		getSession().createSQLQuery("DELETE  from knowledgeattachmentrelation t where t.knowledgeid=?").setString(0, entity.getKnowledgeId()) .executeUpdate(); 
		getSession().refresh(entity);
		List<Attachment> attachmentList = new ArrayList<Attachment>(entity.getAttachmentList());
		for (Attachment attachment : attachmentList) {
			FileUpload.deleteFile(FileUpload.getFtpURL()+attachment.getAttachmentUrl());
			getSession().createSQLQuery("DELETE  from  attachment t where t.attachmentid=?").setString(0, attachment.getAttachmentId()) .executeUpdate(); 
			getSession().refresh(entity);
		}
		
		if("start".equals(audit)){
			String cun = String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
			String un= String.valueOf(httpServletRequest.getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
			CommLogs.requZSKquery(un, "0", entity.getKnowledgeName(), cun, "2", DateUtil.getIpAddr(httpServletRequest));
		}
		entity.setCreateUser(null);
		getSession().createSQLQuery("DELETE  from knowledgeattachmentrelation t where t.knowledgeid=?").setString(0, entity.getKnowledgeId()) .executeUpdate(); 
		getSession().refresh(entity);
		getSession().createSQLQuery("DELETE  from knowledgesystemuser t where t.knowledgeid=?").setString(0, entity.getKnowledgeId()) .executeUpdate(); 
		getSession().refresh(entity);
		getSession().createSQLQuery("DELETE  from  knowled_role t where t.knowledgeid=?").setString(0, entity.getKnowledgeId()) .executeUpdate(); 
		getSession().refresh(entity);
		getSession().createSQLQuery("DELETE  from knowledge ks where ks.knowledgeid=?").setString(0, entity.getKnowledgeId()) .executeUpdate(); 
		}catch (Exception e) {
			e.printStackTrace();
			throw new RuntimeException("给事务回滚，自定义aop");
			
		}
	}
	//根据用户id 知识库id 添加中间表数据
	public void addSystemUserknowledge(int rowNo, String id) {
		String sql="insert into KNOWLEDGESYSTEMUSER VALUES(?,?)";
		getSession().createSQLQuery(sql).setString(0, id).setInteger(1, rowNo).executeUpdate();
	}
	//根据用户id 知识库id 删除中间表数据
	public void delectSystemUserknowledge(int rowNo, String id) {
		String sql ="DELETE  from KNOWLEDGESYSTEMUSER ks where ks.knowledgeid=? and ks.systemuserid=? ";
		getSession().createSQLQuery(sql).setString(0, id).setInteger(1, rowNo).executeUpdate();

	}
	/**
	 * 根据用户ID 判断该用户是否拥有‘省公司-订单管理员角色’
	 * @param rowno
	 * @return  true 表示拥有，false  则相反
	 */
	public Boolean isJurisdiction(String rowno){
		List<Map<String, String>>  list_ = getROLE_REPMList(rowno);
		 if( list_.size()==0){
			 return false;
		 }else{
			 return true;
		 }
	}
	
	/**
	 * 获取‘省公司-订单管理员角色‘用户信息
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public  List<Map<String, String>> getROLE_REPMList(String rowno){
		String sql =" "+
				" select auser.rowno,auser.employee_name,auser.login_name,auser.mobile,afc.company_name,afc.company_code,sr.cname,sr.name from  AFR_SYSTEMUSER auser  "+
				" inner join AFR_SYSTEM_DEPT_USER asdu on asdu.rowno = auser.rowno "+
				" inner join( "+
				" select LEVEL,t.department_no,t.department_name, "+
				" (case when "+
				" substr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),instr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),'\\',1,2)+1,(instr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),'\\',1,3)-instr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),'\\',1,2))-1) "+
				" is null "+
				" then t.department_name "+
				" else "+
				" substr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),instr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),'\\',1,2)+1,(instr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),'\\',1,3)-instr(SYS_CONNECT_BY_PATH(t.department_name, '\\'),'\\',1,2))-1) "+
				" end) twoDName, "+
				" (case when "+
				" substr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),instr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),'\\',1,2)+1,(instr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),'\\',1,3)-instr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),'\\',1,2))-1) "+
				" is null "+
				" then to_char(t.department_no) "+
				" else "+
				" substr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),instr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),'\\',1,2)+1,(instr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),'\\',1,3)-instr(SYS_CONNECT_BY_PATH(t.department_no, '\\'),'\\',1,2))-1) "+
				" end) twoDNameno,t.company_code "+
				" from afr_systemdept t "+
				" where t.department_no<>1 "+
				" START WITH t.department_parent_no =0   "+
				" CONNECT BY PRIOR t.department_no = t.department_parent_no "+
				" ORDER SIBLINGS BY t.department_name "+
				" ) abc on abc.department_no=asdu.department_no left join AFR_SYSTEMCOMPANY afc on afc.company_code = abc.company_code "+
				" inner join system_user_role sur on sur.row_no=auser.rowno inner join  system_role sr on sr.id=sur.role_id "+
				" where afc.company_code='00' and sr.name='ROLE_REPM'   ";
		  	if(!StringUtils.isEmpty(rowno)){
		  		  sql +="  and auser.rowno=? ";
		  		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).setParameter(0, rowno).list();
		  	}else{
		  		return getSession().createSQLQuery(sql).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		  	}
	}
	
	
}
