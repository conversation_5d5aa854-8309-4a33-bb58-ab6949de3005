package com.xinxinsoft.service.RecommendedManagement;

import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.hibernate.Hibernate;
import org.hibernate.Query;
import org.hibernate.Session;
import org.hibernate.transform.Transformers;

import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.core.SystemCompany;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.dedicatedFlow.Opinion;
import com.xinxinsoft.entity.recommendedManagement.RecommendedManagement;
import com.xinxinsoft.service.core.BaseService;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

public class RecommendedManagementService extends BaseService{

	public PageResponse getRecommendedManagement(PageRequest page,SystemUser user,String title,String birthDate,String type) {
		// TODO Auto-generated method stub
		String sql ="select * from RecommendedManagement where startuserid='"+user.getRowNo()+"'";
		if(!"".equals(type) && type!=null){
			sql+=" and state='"+type+"'";
		}else{
			sql+=" and state in('1','0','3')";
		}
		
		if(!"".equals(title) && title!=null){
			sql+=" and title like '%"+title+"%'";
		}
		
		if(!"".equals(birthDate) && birthDate!=null){
			sql+=" and startTime='"+birthDate+"'";
		}
		return getMap(sql, page);
	}
	
	public List<Map<String, String>> SelectZtreeByUId(String id,SystemUser user,String state) {
		if(!StringUtils.isEmpty(id)){
			List<SystemDept> sd=user.getSystemDept();//查询部门
			String code="";
			code= sd.get(0).getSystemCompany().getCompanyCode();
			if(state.equals("2")){
				code="00";
			}
			String sql="";
			sql+="select afs.ROWNO as \"id\",afs.employee_name||'('||sd.department_name||')' as \"name\",'false' as \"isParent\" ,'false' as \"nocheck\" from AFR_systemuser afs  inner join system_user_role sur on sur.row_no=afs.rowno inner join system_role sr on sr.id=sur.role_id"
					+ "  inner join afr_system_dept_user asdu on asdu.rowno=afs.rowno "
					+ "  inner join afr_systemdept sd on sd.department_no = asdu.department_no "
					+ "  inner join afr_systemcompany sc on sc.company_code=sd.company_code  "
					+ "  where sr.id=? and afs.employee_status='0' "
					+ "  and sc.company_code in "+code+"";
			return this.getSession().createSQLQuery(sql).addScalar("isParent", Hibernate.STRING)
					.addScalar("id", Hibernate.STRING).addScalar("name", Hibernate.STRING).setInteger(0, Integer.valueOf(id))
					.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		}else{
			return null;
		}
	}
	
	public List<Map<String, String>> selectZtreeByUname(String ROLE) {
		if(StringUtils.isEmpty(ROLE)){
			return this.getSession().createSQLQuery("select id as \"id\",cname as \"name\",name as \"ename\",descn as \"descn\",'true' as \"isParent\" ,'true' as \"nocheck\",'0' as \"pid\" from system_role where issystemrole='0' ")
					.addScalar("isParent",Hibernate.STRING).addScalar("nocheck",Hibernate.STRING)
					.addScalar("id",Hibernate.STRING).addScalar("name",Hibernate.STRING).addScalar("pid", Hibernate.STRING)
					.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		}else{
			return this.getSession().createSQLQuery("select id as \"id\",cname as \"name\",name as \"ename\",descn as \"descn\",'true' as \"isParent\" ,'true' as \"nocheck\",'0' as \"pid\" from system_role where issystemrole='0' and NAME = ? ")
					.addScalar("isParent",Hibernate.STRING).addScalar("nocheck",Hibernate.STRING).addScalar("pid", Hibernate.STRING)
					.addScalar("id",Hibernate.STRING).addScalar("name",Hibernate.STRING).setString(0, ROLE).setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
		}
		
	}

	public RecommendedManagement setRecommendedManagement(RecommendedManagement recommendedManagement) {
		// TODO Auto-generated method stub
		try {
			Session session = this.getSession();
			session.saveOrUpdate(recommendedManagement);
			return recommendedManagement;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	//附件上传
	public SingleAndAttachment saveSandA(SingleAndAttachment sa) {
		Session session = this.getSession();
		session.saveOrUpdate(sa);
		return sa;
	}
	
	public Opinion getOpinion(String id, String type) {
		// TODO Auto-generated method stub
		String sql = "select * from OPINION where ROLE='【系统人员】' and "
				+ "IDENTIFICATION='"+type+"' and WHETHERTHROUGH='进行中' and ORDERID='"+id+"'";
		return (Opinion) getSession().createSQLQuery(sql).addEntity(Opinion.class).uniqueResult();
	}

	public RecommendedManagement getRecommendedManagement(String id) {
		// TODO Auto-generated method stub
		String sql = "select * from RecommendedManagement where uuid=?";
		return (RecommendedManagement) getSession().createSQLQuery(sql).addEntity(RecommendedManagement.class).setString(0, id).uniqueResult();
	}
	
	public PageResponse getRecommendedManagementtwwo(PageRequest page,SystemUser user,String title,String birthDate,String type) {
		// TODO Auto-generated method stub
		String sql = "select DISTINCT r.UUID,r.TITLE,r.CONTENT,r.STARTUSERID,"
				+ "r.STARTUSERNAME,r.CITY,r.STARTTIME,r.UPDATETIME,r.STATE from RecommendedManagement r LEFT JOIN OPINION o on o.ORDERID=r.UUID where o.PERSONNELNO='"+user.getRowNo()+"'";
		if(!"".equals(type) && type!=null){
			sql+=" and r.state='"+type+"'";
		}else{
			sql+=" and r.state in('1','0','3')";
		}
		
		if(!"".equals(title) && title!=null){
			sql+=" and r.title like '%"+title+"%'";
		}
		
		if(!"".equals(birthDate) && birthDate!=null){
			sql+=" and r.startTime='"+birthDate+"'";
		}
		
		return getMap(sql, page);
	}
	
	public PageResponse getRecommendedManagementthree(PageRequest page,SystemUser user,String title,String birthDate,String type) {
		// TODO Auto-generated method stub
		String sql = "select DISTINCT r.UUID,r.TITLE,r.CONTENT,r.STARTUSERID,"
				+ "r.STARTUSERNAME,r.CITY,r.STARTTIME,r.UPDATETIME,r.STATE from RecommendedManagement "
				+ "r LEFT JOIN OPINION o on o.ORDERID=r.UUID where o.PERSONNELNO='"+user.getRowNo()+"'";
		if(!"".equals(type) && type!=null){
			sql+=" and r.state='"+type+"'";
		}else{
			sql+=" and r.state='1'";
		}
		
		if(!"".equals(title) && title!=null){
			sql+=" and r.title like '%"+title+"%'";
		}
		
		if(!"".equals(birthDate) && birthDate!=null){
			sql+=" and r.startTime='"+birthDate+"'";
		}
		return getMap(sql, page);
	}
	
	public void saveObjtwo(String opinionRole, String date, String opinion,
			String orderId, String employeeName, int rowNo,
			String linkNameCode, String zhuangtai) {
		// TODO Auto-generated method stub
		String sql = "UPDATE OPINION O SET O.role='"+opinionRole+"',o.creationTime=to_date('"+date+"','yyyy-mm-dd HH24:MI:SS'),o.opinion='"+opinion+"'"
				+ ",o.whetherThrough='"+zhuangtai+"' where o.orderId='"+orderId+"' "
						+ "and o.identification='"+linkNameCode+"' and whetherThrough='进行中' and role='"+opinionRole+"'";
		this.getSession().createSQLQuery(sql).executeUpdate();
	}
	
	/**
	 * 保存 意见信息
	 * 
	 * @param opinion2
	 */
	public Opinion saveOpinion(Opinion opinion2) {
		try {
			Session session = this.getSession();
			session.saveOrUpdate(opinion2);
			session.flush();
			return opinion2;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
	}
	
	public void saveObjone(String opinionRole, String date,
			String orderId, String employeeName, int rowNo,
			String linkNameCode, String zhuangtai, String imsFormInfoId) {
		// TODO Auto-generated method stub
		String zhuangtaione="进行中";
		String sql = "UPDATE OPINION O SET O.role='"+opinionRole+"',o.creationTime=to_date('"+date+"','yyyy-mm-dd HH24:MI:SS')"
				+ ",o.whetherThrough='"+zhuangtai+"',o.formInfoId='"+imsFormInfoId+"' where o.orderId=? and o.personnel=? and o.personnelNo=? and o.identification=? and whetherThrough=?";
		this.getSession().createSQLQuery(sql).setString(0, orderId).setString(1, employeeName).setInteger(2, rowNo).setString(3, linkNameCode).setString(4, zhuangtaione).executeUpdate();
	}
	
	public void saveObjoneone(String opinionRole, String date,
			String orderId, String employeeName, int rowNo,
			String linkNameCode, String zhuangtai, String imsFormInfoId,String opinion) {
		// TODO Auto-generated method stub
		String zhuangtaione="进行中";
		String sql = "UPDATE OPINION O SET O.role='"+opinionRole+"',o.creationTime=to_date('"+date+"','yyyy-mm-dd HH24:MI:SS')"
				+ ",o.whetherThrough='"+zhuangtai+"',o.formInfoId='"+imsFormInfoId+"',o.opinion='"+opinion+"' where o.orderId=? and o.personnel=? and o.personnelNo=? and o.identification=? and whetherThrough=?";
		this.getSession().createSQLQuery(sql).setString(0, orderId).setString(1, employeeName).setInteger(2, rowNo).setString(3, linkNameCode).setString(4, zhuangtaione).executeUpdate();
	}
	
	/**
	 * 获取附件消息
	 */
	public List<Map<String, String>> fuJian(String id,String biaoshi) {
		String sql = "select ah.ATTACHMENTID as \"id\",ah.UPLOADUSER as \"userid\" ,ah.realName as \"name\",ah.uploadDate as \"uploadDate\" from RecommendedManagement  o  "
				+ " left join  SingleAndAttachment oa  on o.uuid=OA.orderID "
				+ " LEFT JOIN ATTACHMENT ah  on oa.attachmentId=ah.ATTACHMENTID where o.uuid=? and oa.link=?";

		return getSession().createSQLQuery(sql)
				.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP)
				.setString(0, id).setString(1, biaoshi).list();
	}

	public PageResponse getRecommendedManagementAll(PageRequest page,
			SystemUser user, String titletwo, String birthDate, String type,
			String staruserid, String company) {
		// TODO Auto-generated method stub
		
		String sql ="select * from RecommendedManagement where 1=1";
		
		if(!"".equals(type) && type!=null){
			sql+=" and state='"+type+"'";
		}else{
			sql+=" and state in('1','0','3')";
		}
		
		if(!"".equals(titletwo) && titletwo!=null){
			sql+=" and title like '%"+titletwo+"%'";
		}
		
		if(!"".equals(birthDate) && birthDate!=null){
			sql+=" and startTime='"+birthDate+"'";
		}
		
		if(!"".equals(company) && company!=null){
			sql+=" and city='"+company+"'";
		}
		if(!"".equals(staruserid) && staruserid!=null){
			sql+=" and startusername like '%"+staruserid+"%'";
		}
		return getMap(sql, page);
	}

	public Object getcompanyAll() {
		// TODO Auto-generated method stub
		String json = null;
 		try {
 			String sql = "select * from AFR_SYSTEMCOMPANY";
 			Query query = this.getSession().createSQLQuery(sql);
 			List<Map<String,String>> list = query.setResultTransformer(Transformers.ALIAS_TO_ENTITY_MAP).list();
 			json = JSONHelper.SerializeWithNeedAnnotationDateFormats(list);
 		} catch (Exception e) {
 			e.printStackTrace();
 		}
 		return json;
	}
}
