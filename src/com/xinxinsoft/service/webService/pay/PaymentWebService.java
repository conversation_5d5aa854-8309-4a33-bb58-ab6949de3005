package com.xinxinsoft.service.webService.pay;
import com.xinxinsoft.entity.pay.Refunds.RefundInfos;
import com.xinxinsoft.entity.pay.Refunds.RefundResultInfo;
import com.xinxinsoft.entity.pay.Refunds.Refunds;
import com.xinxinsoft.entity.pay.payment.*;
import com.xinxinsoft.entity.pay.queryPayment.*;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.service.pay.PayRefundService;
import com.xinxinsoft.service.pay.PaymentService;
import com.xinxinsoft.service.pay.QueryPaymentService;
import com.xinxinsoft.service.webService.BaseWebService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.common.FileUpload;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.jws.WebMethod;
import javax.jws.WebParam;
import javax.jws.WebService;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

/**
 * 支付4.0.0 统一支付webService
 */
@WebService
public class PaymentWebService  extends BaseWebService {

    private static String			PAYURL				= FileUpload.getPaymentUrl();				// 统一支付平台接口地址
    private static String			MERCHANT_NO			= FileUpload.getMerchantNo();							// 统一支付平台商户号
    private static String			PAYKEY				= FileUpload.getPaymentKey();							// 加密KEY
    private static String			PAY_NOTIFY_URL		= FileUpload.getPayNotifyUrl();					// 支付后台通知地址
    private static String			REFUND_NOTIFY_URL		= FileUpload.getRefundnotifyUrl();					// 退款后台通知地址


    private PaymentService paymentService;
    private QueryPaymentService queryPaymentService;
    private PayRefundService payRefundService;

    public PaymentWebService(PaymentService paymentService,QueryPaymentService queryPaymentService,PayRefundService payRefundService){

        this.paymentService = paymentService;
        this.queryPaymentService = queryPaymentService;
        this.payRefundService = payRefundService;
    }

    public PaymentWebService(){ }

    /**
     * 支付发起(纯网关)
     * 渠道发起支付请求 savePaymentInfo
     * @param jsonInfo
     * @return
     */
    @WebMethod
    public String savePaymentInfo(@WebParam(name = "jsonInfo") String jsonInfo){
        JSONObject json = new JSONObject(); // 返回值
        String orderid = MERCHANT_NO + DateUtil.getorderid();
        String url = PAYURL.trim() + "payment-api/pay/payoff";
        String notify_url = PAY_NOTIFY_URL;// 后台支付通知地址

        try{
           // jsonInfo= "{\"groupId\":\"23\",\"orderInfo\":[{\"groupCode\":\"**********\",\"groupName\":\"TESTdingdanxit\",\"pay_userno\":\"***********\",\"pay_note\":\"ceshihuanj\"}],\"cityCode\":\"11\",\"cityName\":\"SGS\",\"operator\":\"aagh38\",\"currency\":\"RMB\",\"totalFee\":\"1\",\"clientIp\":\"**************\",\"goodsInfo\":[{\"id\":\"***********\",\"name\":\"cesshibuanj\",\"quantity\":\"1\",\"rule\":\"1\",\"price\":\"1\"}],\"orderDesc\":\"cheshihuangj\",\"paymentInfos\":[{\"payModeId\":\"311004\",\"accountType\":\"311\",\"tradeFee\":\"1\",\"realFee\":\"1\"}]}";
            logger.info(jsonInfo);
            net.sf.json.JSONObject jsonObj = net.sf.json.JSONObject.fromObject(jsonInfo);
            PayOrderInfo pinfo = (PayOrderInfo)net.sf.json.JSONObject.toBean(net.sf.json.JSONObject.fromObject(new com.xinxinsoft.service.core.json.JSONArray(jsonObj.getString("orderInfo")).get(0).toString()), PayOrderInfo.class);
            pinfo.setCreateDate(new Date());
            pinfo.setOrderId(orderid);
            jsonObj.remove("orderInfo");
            GoodsInfo ginfo = (GoodsInfo)net.sf.json.JSONObject.toBean(net.sf.json.JSONObject.fromObject(new com.xinxinsoft.service.core.json.JSONArray(jsonObj.getString("goodsInfo")).get(0).toString()), GoodsInfo.class);
            ginfo.setOrderId(orderid);
            jsonObj.remove("goodsInfo");
            PaymentInfo pminfo = (PaymentInfo) net.sf.json.JSONObject.toBean(net.sf.json.JSONObject.fromObject(new com.xinxinsoft.service.core.json.JSONArray(jsonObj.getString("paymentInfos")).get(0).toString()), PaymentInfo.class);
            pminfo.setCreateDate(new Date());
            if (CMCCOpenService.having(jsonObj, "op_code")) {
               	String	op_code=jsonObj.getString("op_code");//	1	String	4	操作代码	非空，传入8000、
                if(!StringUtils.isEmpty(op_code) && "8000".equals(op_code)){
                    String login_no = jsonObj.getString("operator");//	1	String	20	工号	非空
                    String login_password = jsonObj.getString("login_password");//	1	String	32	工号密码	非空
                    String group_id = jsonObj.getString("groupId");//	1	String	10	机构代码	可不传入该节点，如果传入必须非空
                    Long contract_no = null;//	?	Long		帐户ID	可空费用为分, ，可以传多个，中间用逗号分隔。
                    String pay_path = jsonObj.getString("pay_path");//	1	String	5	缴费渠道	非空： 01:营业厅 02:自助终端 03:网上营业厅 04:充值平台 05:空中充值代理商 06:一级BOSS 07:银行 08:待办渠道 09:短信营业厅 10:wap营业厅 11:集团网上营业厅 12:IVR 13:经分	 14:工作队收取 15:快信营业厅 16:系统后台 17:网上商城 18:下月账务收取等 28:系统-签约用户-自动充值 42:天猫 43:移动商城
                    String pay_method = jsonObj.getString("pay_method");//	1	String	5	缴费方式	非空： 0:现金 y:银行卡 2:信用卡 3:交费卡 c:充值卡 8:赠费 9:支票 B:空中充值转入 H:年度积分清零 j:积分换预存   J:集团帐号划拨 U:新业务卡充值 s:商务卡 A44:易充值 A22:深圳统一支付平台(签约用户 A33:深圳统一支付平台(非签约用户) 11:手机支付缴费 12:外省缴费 13:店员快充
                    String pay_type = jsonObj.getString("pay_type");//	1	String	50	帐本类型	非空，可以传多个账本，中间用逗号分隔
                    Double delay_rate = jsonObj.getDouble("delay_rate");//	?	Double		滞纳金优惠率	可空，取值【0，1】之间，0：不优惠，1:全部优惠。
                    Double remonth_rate = jsonObj.getDouble("remonth_rate");//	?	Double		补收月租优惠率	可空，取值【0，1】之间，0：不优惠，1:全部优惠。
                    String bank_code = jsonObj.getString("bank_code");//	?	String	5	银行代码[对支票交费]	可为空
                    String check_no = jsonObj.getString("check_no");//	?	String	20	支票号码[对支票交费]	可为空
                    String foreign_sn = "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "";//this.getString("foreign_sn");//	1	String	40	外部流水号	非空，唯一
                    String ctrl_flag = jsonObj.getString("ctrl_flag");//	?	String	10	控制标志	可为空 第1位：发送短信标志 0发送短信，1不发送； 第2位：缴费查询标志,规划字段，暂未使用 0调用缴费查询 1不调用； 第3位：是否根据foreign_sn校验唯一性,规划字段，暂未使用  0 不校验   1 校验
                    String pay_rate = jsonObj.getString("pay_rate");//	?	String	10	折扣率	用于根据传入值取缴费发送短信模板，目前只配置一级BOSS手机支付交话费时候传入0.99进行短信下发
                    String user_phone = pinfo.getCreatePhone();
                    String pay_userno = jsonObj.getString("pay_userno");
                    pinfo.setPay_eight_note(CMCCOpenService.getInstance().setParamPres8000CfmCardSvc(login_no, login_password, group_id, op_code, contract_no, pay_userno,pminfo.getTradeFee(), StringUtils.isBlank(pay_path) ? "01" : pay_path, StringUtils.isBlank(pay_method) ? "0" : pay_method, StringUtils.isBlank(pay_type) ? "01" : pay_type, delay_rate, remonth_rate, bank_code, check_no, pinfo.getPay_note(), foreign_sn, "", ctrl_flag, pay_rate,user_phone));
                    pinfo.setPay_eight_status("2");
                }

            }

            pminfo.setAccountType("311");// 服务端提供类型
            pminfo.setOrderId(orderid);
            jsonObj.remove("paymentInfos");
            if(CMCCOpenService.having(jsonObj, "unfreezzeAndDeductRules")) {
                UnfreezzeAndDeductRules uadrinfo = (UnfreezzeAndDeductRules) net.sf.json.JSONObject.toBean(net.sf.json.JSONObject.fromObject(new com.xinxinsoft.service.core.json.JSONArray(jsonObj.getString("unfreezzeAndDeductRules")).get(0).toString()), UnfreezzeAndDeductRules.class);
                uadrinfo.setCreateDate(new Date());
                jsonObj.remove("unfreezzeAndDeductRules");
            }
//            uadrinfo.setOrderNum(orderid);
            PayProviderInfo ppinfo = (PayProviderInfo)net.sf.json.JSONObject.toBean(jsonObj, PayProviderInfo.class);
            if(StringUtils.isEmpty(ppinfo.getCurrency())){
                ppinfo.setCurrency("RMB");
            }
            if(StringUtils.isEmpty(ppinfo.getAccessMode())) {
                ppinfo.setAccessMode("H5");
            }
            ppinfo.setTotalFee(pminfo.getTradeFee());
            ppinfo.setPartnerPayId(orderid);
            ppinfo.setNotifyUrl(notify_url);
            ppinfo.setCreateDate(new Date());
            ppinfo.setUpdateDate(new Date());
            ppinfo.setPartnerId(MERCHANT_NO);
            net.sf.json.JSONObject jsonBeanEnc = net.sf.json.JSONObject.fromObject(ppinfo.toString());
            jsonBeanEnc.put("goodsInfo", JSONArray.fromObject(net.sf.json.JSONObject.fromObject(ginfo.toString())));
            jsonBeanEnc.put("paymentInfos",JSONArray.fromObject(net.sf.json.JSONObject.fromObject(pminfo.toString())));
            //jsonBeanEnc.put("unfreezzeAndDeductRules",JSONArray.fromObject(net.sf.json.JSONObject.fromObject(uadrinfo.toString())));

            net.sf.json.JSONObject jsonObject = postRequest(jsonBeanEnc.toString(),url);

            ginfo = paymentService.saveOrUpdate(ginfo);
            pinfo = paymentService.save(pinfo);
            pminfo  = paymentService.save(pminfo);

           // uadrinfo= paymentService.save(uadrinfo);

            ppinfo.setPayResult(jsonObject.toString());
            if(CMCCOpenService.having(jsonObject, "retCode")) {
                if ("000000".equals(jsonObject.getString("retCode")) && "true".equals(jsonObject.getString("success"))) {
                    net.sf.json.JSONObject jsonData = net.sf.json.JSONObject.fromObject(jsonObject.get("data"));
                    if (CMCCOpenService.having(jsonData, "paymentInfos")) {
                        JSONArray jsonArray =  JSONArray.fromObject(jsonData.getString("paymentInfos"));
                        for (Object str :jsonArray){
                            PaymentResultInfo paymentResultInfo = (PaymentResultInfo)JSONObject.toBean(JSONObject.fromObject(str), PaymentResultInfo.class);
                            paymentResultInfo.setCreateDate(new Date());
                            paymentResultInfo.setOrderId(ppinfo.getPartnerPayId());
                            paymentResultInfo.setUpdateDate(new Date());
                            paymentResultInfo = paymentService.save(paymentResultInfo);
                        }
                    }
                    jsonData.remove("paymentInfos");
                    PayResults payResults = (PayResults) net.sf.json.JSONObject.toBean(jsonData, PayResults.class);
                    payResults.setOrderid(ppinfo.getPartnerPayId());
                    payResults.setCreateDate(new Date());
                    payResults.setUpdateDate(new Date());
                    if (CMCCOpenService.having(jsonData, "status")) {
                        ppinfo.setStatus(jsonData.getInt("status"));
                    }else{
                        ppinfo.setStatus(0);//待支付
                    }
                    payResults = paymentService.save(payResults);
                    ppinfo = paymentService.save(ppinfo);
                    json.put("retCode", "0");
                    json.put("orderNum", orderid);
                    json.put("retUrl", jsonData.get("redirectUrl"));
                    json.put("error", "success");
                } else {
                    json.put("retCode", "-1");
                    json.put("orderNum", orderid);
                    json.put("retUrl", "");
                    json.put("error", jsonObject.get("retMsg"));
                    throw new Error(jsonObject.get("retMsg")+"");
                }
            }else{
                json.put("retCode", "-1");
                json.put("orderNum", orderid);
                json.put("retUrl", "");
                json.put("error", "异常：" + jsonObject.toString());
                throw new Error(jsonObject.toString());
            }
        }catch (Error e){
            logger.error("异常："+e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
        }catch (Exception e) {
            // TODO: handle exception
            String error = e.getMessage();
            json.put("retCode", "-1");
            json.put("orderNum", orderid);
            json.put("retUrl", "");
            json.put("error", error);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
        }
            return json.toString();
    }

    /***
     * 支付查询 PayQueryHandle
     * @param jsonInfo
     * @return
     */
    @WebMethod
    public String payQueryHandle(@WebParam(name = "jsonInfo") String jsonInfo){
        JSONObject json = new JSONObject(); // 返回值
        String  url  = PAYURL.trim() + "payment-api/pay/paymentInfo";
        try{
            JSONObject jsonObj = JSONObject.fromObject(jsonInfo);
            if(!CMCCOpenService.having(jsonObj, "orderId")) {
                json.put("retCode", "-1");
                json.put("retUrl", "");
                json.put("error", "参数格式中未找到OrderID,无法查询数据");
                return json.toString();
            }
            String  order_id = jsonObj.getString("orderId");
            PayProviderInfo payProviderInfo = paymentService.queryByOrderId(order_id);
            PayResults payResults = paymentService.queryPayResultsByOrderId(order_id);
            PaymentInfo paymentInfo = paymentService.queryPaymentInfoById(order_id);
            List<PaymentResultInfo> paymentResultInfos = paymentService.queryPaymentInfosByOrderId(order_id);
            JSONObject jsonReq =new JSONObject();
                if(payResults != null && payResults.getPayId() != null){
                    jsonReq.put("payId",payResults.getPayId());
                }
                    jsonReq.put("partnerPayId",payProviderInfo.getPartnerPayId());
                    jsonReq.put("partnerId",MERCHANT_NO.trim());
            if(CMCCOpenService.having(jsonObj, "payUserId")) {
                jsonReq.put("payUserId", "");
            }
            if(CMCCOpenService.having(jsonObj, "bondingRules")) {
                BondingRules bondingRules = (BondingRules)JSONObject.toBean(JSONObject.fromObject(new com.xinxinsoft.service.core.json.JSONArray(jsonObj.getString("bondingRules")).get(0).toString()), BondingRules.class);
                jsonReq.put("bondingRules", JSONArray.fromObject(JSONObject.fromObject(bondingRules.toString())));
            }

            //请求：
            net.sf.json.JSONObject jsonObject =postRequest(jsonReq.toString(),url); //JSONObject.fromObject("{\"retCode\":\"000000\",\"retMsg\":\"success\",\"success\":true,\"data\":{\"payId\":\"xxx\",\"partnerPayId\":\"xxx\",\"customerId\":\"xxx\",\"payUserId\":\"xxx\",\"accessMode\":\"xxx\",\"partnerId\":\"xxx\",\"goodsInfo\":[{\"id\":\"XXX\",\"name\":\"XXX\",\"quantity\":\"XXX\",\"rule\":\"XXX\",\"price\":\"XXX\",\"copyright\":\"XXX\"}],\"orderDesc\":\"xxx\",\"currency\":\"xxx\",\"totalFee\":\"xxx\",\"status\":\"xxx\",\"statusInfo\":\"xxx\",\"statusChangeTime\":\"xxx\",\"createTime\":\"xxx\",\"timeout\":\"xxx\",\"paymentItemInfo\":[{\"payItemId\":\"xxx\",\"payId\":\"xxx\",\"orgPayId\":\"xxx\",\"payUserId\":\"xxx\",\"customerId\":\"xxx\",\"accountType\":\"xxx\",\"accountId\":\"xxx\",\"tradeFee\":\"xxx\",\"realFee\":\"xxx\",\"discountId\":\"xxx\",\"exchangeRuleId\":\"xxx\",\"payMode\":\"xxx\",\"status\":\"xxx\",\"statusInfo\":\"xxx\",\"createTime\":\"xxx\",\"payTime\":\"xxx\",\"busiFrom\":\"xxx\",\"busiTo\":\"xxx\"}],\"bondingRules\":[{\"orderNum\":\"xxx\",\"bondingNum\":\"xxx\",\"customerName\":\"xxx\",\"idNum\":\"xxx\",\"frozenFee\":\"xxx\",\"frozenStartTime\":\"xxx\",\"frozenEndTime\":\"xxx\",\"frozenStatus\":\"xxx\",\"bankNameFull\":\"xxx\",\"bankNameShort\":\"xxx\",\"bankAccountNum\":\"xxx\",\"openingBank\":\"xxx\",\"timestamp\":\"xxx\"}]}}\n") ;// postRequest(jsonReq.toString(),url);
            payProviderInfo.setQueryResult(jsonObject.toString());
            payProviderInfo.setUpdateDate(new Date());
            if(CMCCOpenService.having(jsonObject, "retCode")) {
                if ("000000".equals(jsonObject.getString("retCode")) && "true".equals(jsonObject.getString("success"))) {
                    if(CMCCOpenService.having(jsonObject, "data")) {
                        JSONObject dataJson = JSONObject.fromObject(jsonObject.get("data"));
                        payProviderInfo.setStatus(dataJson.getInt("status"));
                        payProviderInfo.setUpdateDate(new Date());
                        if(CMCCOpenService.having(dataJson, "paymentItemInfo")) {
                            JSONArray jsonArray =  JSONArray.fromObject(dataJson.getString("paymentItemInfo"));
                            for (Object str :jsonArray){
                                PaymentItemInfo paymentItemInfo = (PaymentItemInfo)JSONObject.toBean(JSONObject.fromObject(str),PaymentItemInfo.class);
                                json.put("retCode", paymentItemInfo.getStatus());
                                json.put("success", paymentItemInfo.getStatusInfo());
                                for(PaymentResultInfo prInfo:paymentResultInfos){
                                    PaymentResultInfo prsinfo = prInfo;
                                    if(prInfo.getPayItemId().equals(paymentItemInfo.getPayItemId()) || prInfo.getOrgPayId().equals(paymentItemInfo.getOrgPayId())){
                                        prsinfo.setStatus(paymentItemInfo.getStatus());
                                        prsinfo.setStatusInfo(paymentItemInfo.getStatusInfo());
                                        prsinfo.setUpdateDate(new Date());
                                        paymentService.save(prsinfo);///更新
                                    }
                                }
                            }
                        }

                        if(CMCCOpenService.having(dataJson, "status")) {
                            payProviderInfo.setStatus(Integer.parseInt(dataJson.getString("status")));
                            payResults.setStatus(dataJson.getString("status"));
                            json.put("retCode", dataJson.getString("status"));
                        }else{
                            json.put("retCode", "-1");
                        }
                        if(CMCCOpenService.having(dataJson, "statusInfo")) {
                            payProviderInfo.setStatusInfo(dataJson.getString("statusInfo"));
                        }
                        if(CMCCOpenService.having(dataJson, "statusChangeTime")) {
                            payProviderInfo.setStatusChangeTime(dataJson.getString("statusChangeTime"));
                        }
                        if("2".equals(payProviderInfo.getStatus()) || 2== payProviderInfo.getStatus() ) {
                            //8000支付情况
                            PayOrderInfo payOrderInfo = paymentService.queryPayOrderInfoByOrderId(payProviderInfo.getPartnerPayId());
                            if ( payOrderInfo != null && "0000010".equals(payOrderInfo.getPay_type()) && !"0".equals(payOrderInfo.getPay_eight_status())) {
                                Map<String, Object> mapcfm = CMCCOpenService.getInstance().pres8000CfmCardSvc(payOrderInfo.getPay_eight_noteDes());
                                logger.info("8000 支付情况：" + mapcfm);
                                if (mapcfm.containsKey("return_code")) {
                                    if ("0".equals(mapcfm.get("return_code"))) {
                                        payOrderInfo.setPay_eight_status("0");
                                        payOrderInfo.setPay_accept(mapcfm.get("pay_accept")+"");
                                    } else {
                                        payOrderInfo.setPay_eight_result(mapcfm.get("return_msg") + "");
                                        payOrderInfo.setPay_eight_status("1");
                                    }
                                } else {
                                    payOrderInfo.setPay_eight_result(mapcfm.get("res") + "");
                                    payOrderInfo.setPay_eight_status("1");
                                }
                                payOrderInfo.setUpdateDate(new Date());
                                paymentService.save(payOrderInfo);
                            }
                        }

                        payResults.setUpdateDate(new Date());
                        paymentService.save(payResults);
                        paymentService.save(payProviderInfo); ///修改支付订单状态
                        json.put("orderNum", "");
                        json.put("retUrl", "");
                        json.put("error", "");
                    }else{
                        json.put("retCode", "-1");
                        json.put("retUrl", "");
                        json.put("error", "异常：服务器返回数据中未找到DATA ，错误信息："+jsonObject.get("retMsg"));
                        throw new Error("服务器返回数据中未找到DATA ，错误信息："+jsonObject.get("retMsg"));
                    }
                }else {
                    json.put("retCode", "-1");
                    json.put("retUrl", "");
                    json.put("error", jsonObject.get("retMsg"));
                    throw new Error( jsonObject.get("retMsg")+"");
                }
            }else{
                json.put("retCode", "-1");
                json.put("retUrl", "");
                json.put("error", "异常：" + jsonObject.toString());
                throw new Error( jsonObject.toString());
            }

        }catch (Error e){
            logger.error("异常："+e.getMessage());
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
        } catch (Exception e) {
            // TODO: handle exception
            String error = e.getMessage();
            json.put("retCode", "-1");
            json.put("retUrl", "");
            json.put("error", error);
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
        }
            return json.toString();

    }


    /**
     *3.4.2.1	支付退款交易
     * 向支付商发起退款请求（如果支持的话）然后返回退款受理结果。如果一笔退款失败后重新提交，要采用原来的refundId。
     * @param jsonInfo
     * @return
     */
    @WebMethod
    public String refundHandle(@WebParam(name="jsonInfo")String jsonInfo){
        JSONObject json = new JSONObject(); // 返回值

        String  refundId = MERCHANT_NO + DateUtil.getorderid();
        String  url  = PAYURL.trim() + "payment-api/pay/refund";
        try {
            JSONObject jsonObj = JSONObject.fromObject(jsonInfo);
            if(!CMCCOpenService.having(jsonObj, "orderId")) {
                json.put("retCode", "-1");
                json.put("retUrl", "");
                json.put("error", "参数格式中未找到OrderId,无法查询数据");
                return json.toString();
            }
            String  order_id = jsonObj.getString("orderId");
            PayProviderInfo payProviderInfo = paymentService.queryByOrderId(order_id);
            if(payProviderInfo !=null && payProviderInfo.getPartnerPayId() != null) {
                JSONArray refundInfos = new JSONArray();
                JSONObject jsonReqBean = new JSONObject();
                /// 查询支付退款记录
                Refunds rs = payRefundService.queryRefundsByOrderId(order_id);
                    if( rs!=null && rs.getRefundId()!=null && 1 ==(rs.getStatus())){
                        json.put("retCode", "-1");
                        json.put("retUrl", "");
                        json.put("error", "该订单已经退款成功，不能再重复退款");
                        return json.toString();
                    }
                    PayOrderInfo payOrderInfo = paymentService.queryPayOrderInfoByOrderId(order_id);
                    if (payOrderInfo !=null && "0".equals(payOrderInfo.getPay_eight_status())){
                        json.put("retCode", "-1");
                        json.put("retUrl", "");
                        json.put("error", "异常：8000 已经支付成功，不支持退款。");
                        return  json.toString();
                    }
                     //判断是否退款失败状态才能重新发起退款
                        if( rs!=null && 2 == (rs.getStatus())){
                            List<RefundInfos> rInfos = payRefundService.queryRefundInfosByPayId(rs.getRefundId());
                            for (RefundInfos rinfo : rInfos) {
                                refundInfos.add(JSONObject.fromObject(rinfo.toString()));
                            }
                            jsonReqBean =  JSONObject.fromObject(rs.toString());
                        }else {
                            rs = new Refunds();
                                PayResults payResults = paymentService.queryPayResultsByOrderId(order_id);
                            PaymentInfo paymentInfo = paymentService.queryPaymentInfoById(order_id);
                            List<PaymentResultInfo> paymentResultInfo = paymentService.queryPaymentInfosByOrderId(order_id);

                            for(PaymentResultInfo prinfo : paymentResultInfo){
                                RefundInfos rinfos = new RefundInfos();
                                if (paymentInfo != null && paymentInfo.getAccountType() != null) {
                                    rinfos.setAccountType(paymentInfo.getAccountType());
                                    rinfos.setTradeFee(paymentInfo.getTradeFee());
                                }

                                if (payResults != null && payResults.getPayId() != null) {
                                    rs.setPayId(payResults.getPayId());
                                    rinfos.setPayId(payResults.getPayId());
                                }
                                rinfos.setPayItemId(prinfo.getPayItemId());
                                rinfos.setOrgPayId(prinfo.getOrgPayId());
                                refundInfos.add(JSONObject.fromObject(rinfos.toString()));
                                rinfos.setOrderId(refundId);
                                 payRefundService.save(rinfos);
                            }
                            rs.setPartnerId(payProviderInfo.getPartnerId());
                            rs.setPartnerPayId(payProviderInfo.getPartnerPayId());
                            rs.setGroupId(payProviderInfo.getGroupId());
                            rs.setRefundTotalFee(payProviderInfo.getTotalFee());
                            if (CMCCOpenService.having(jsonObj, "remark")) {
                                rs.setRemark(jsonObj.getString("remark"));
                            } else {
                                rs.setRemark("退款");
                            }
                            rs.setNotifyUrl(REFUND_NOTIFY_URL.trim());//退款通知
                            rs.setRefundType("RF");
                            rs.setRefundId(refundId);
                            jsonReqBean =  JSONObject.fromObject(rs.toString());
                        }
                jsonReqBean.put("refundInfos", refundInfos);
                JSONObject jsonObject = postRequest(jsonReqBean.toString(), url);
                rs.setRefundResult(jsonObject.toString());//保存返回参数结果：
                if (CMCCOpenService.having(jsonObject, "retCode")) {
                    if ("000000".equals(jsonObject.getString("retCode")) && "true".equals(jsonObject.getString("success"))) {
                        if (CMCCOpenService.having(jsonObject, "data")) {
                            JSONObject dataJson = JSONObject.fromObject(jsonObject.get("data"));
                            rs.setRefundId(dataJson.getString("refundId"));
                            //如果是异步退款时，不返回，则以退款通知的状态为准。
                            if (CMCCOpenService.having(dataJson, "status")) {
                                rs.setStatus(Integer.parseInt(dataJson.getString("status")));
                                json.put("retCode", rs.getStatus());
                                json.put("success", jsonObject.getString("success"));
                            }
                            RefundResultInfo refundResultInfo = (RefundResultInfo)JSONObject.toBean(dataJson , RefundResultInfo.class);
                                    try{
                                        //退款查询
                                        json = refundqueryHandle(rs,json);
                                        json.put("retCode", rs.getStatus());
                                        json.put("orderNum", "");
                                        json.put("success", rs.getStatusInfo());
                                        json.put("retUrl", "");
                                        json.put("error", rs.getStatusInfo());
                                    }catch (Exception e){
                                        logger.error("退款查询异常:"+e.getMessage());
                                        json.put("retCode", "0");
                                        json.put("orderNum", "");
                                        json.put("success", rs.getStatusInfo());
                                        json.put("retUrl", "");
                                        json.put("error", "退款查询异常:"+e.getMessage());
                                    }
                              refundResultInfo.setRefundId(refundId);
                              refundResultInfo.setStatus(rs.getStatus()+"");
                              refundResultInfo.setCreateDate(new Date());
                              refundResultInfo.setUpdateDate(new Date());
                            payRefundService.save(refundResultInfo);
                            //更新支付表状态
                            PayProviderInfo  ppifon = paymentService.queryByOrderId(rs.getPartnerPayId());
                                           ppifon.setStatus("1".equals(refundResultInfo.getStatus()) ? -2 : -3);
                                            ppifon.setStatusInfo(rs.getStatusInfo());
                                            ppifon.setUpdateDate(new Date());
                                            paymentService.save(ppifon);

                        } else {
                            json.put("retCode", "-1");
                            json.put("retUrl", "");
                            json.put("error", "异常：服务器返回数据中未找到DATA ，错误信息：" + jsonObject.get("retMsg"));
                            rs.setStatus(2);
                        }
                    } else {
                        json.put("retCode", "-1");
                        json.put("retUrl", "");
                        json.put("error", jsonObject.get("retMsg"));
                        rs.setStatus(2);
                    }
                } else {
                    json.put("retCode", "-1");
                    json.put("retUrl", "");
                    json.put("error", "异常：" + jsonObject.toString());
                    rs.setStatus(2);
                }
                payRefundService.save(rs);
            }else{
                json.put("retCode", "-1");
                json.put("retUrl", "");
                json.put("error", "无法根据订单编号（"+order_id+"）查询数据，请核实该支付订单的状态！");
            }
        }catch (Exception e) {
                String error = e.getMessage();
                json.put("retCode", "-1");
                json.put("retUrl", "");
                json.put("error", error);
                e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
        }
        return json.toString();

    }


    /**
     * 退款查询
     * @param rs
     */
    private  JSONObject refundqueryHandle(Refunds rs ,JSONObject json){
        JSONObject jsonReqBean = new JSONObject();
        String  url  = PAYURL.trim() + "payment-api/pay/refundquery";
        if( rs!=null && rs.getRefundId()!=null) {
            if (!StringUtils.isEmpty(rs.getPayId())) {
                jsonReqBean.put("payId", rs.getPayId());
            }
            if (!StringUtils.isEmpty(rs.getPartnerPayId())) {
                jsonReqBean.put("partnerPayId", rs.getPartnerPayId());
            }
            jsonReqBean.put("partnerId", rs.getPartnerId());
            jsonReqBean.put("refundId", rs.getRefundId());
            //请求：
            JSONObject jsonObject = postRequest(jsonReqBean.toString(), url);
            rs.setQueryResult(jsonObject.toString());
            if (CMCCOpenService.having(jsonObject, "retCode")) {
                if ("000000".equals(jsonObject.getString("retCode")) && "true".equals(jsonObject.getString("success"))) {
                    if (CMCCOpenService.having(jsonObject, "data")) {
                        JSONObject dataJson = new JSONObject();

                        if(jsonObject.get("data") instanceof JSONArray){
                            dataJson = JSONObject.fromObject(JSONArray.fromObject(jsonObject.get("data")).get(0));
                        }else{
                            dataJson = JSONObject.fromObject(jsonObject.get("data"));
                        }

                        if(CMCCOpenService.having(dataJson, "refundInfos")) {
                            List<RefundInfos> rInfos = payRefundService.queryRefundInfosByPayId(dataJson.getString("refundId"));
                            JSONArray jsonArray =  JSONArray.fromObject(dataJson.getString("refundInfos"));
                            for (Object str :jsonArray){
                                JSONObject queryDateJson =   JSONObject.fromObject(str);
                                if(rInfos.size()!=0) {
                                    for (RefundInfos r : rInfos) {
                                        if (r.getOrderId().equals(queryDateJson.getString("refundId"))) {
                                            r.setStatus(queryDateJson.getString("status"));
                                            r.setStatusInfo(queryDateJson.getString("statusInfo"));
                                            rs.setStatusInfo(queryDateJson.getString("statusInfo"));
                                            r.setCreateTime(queryDateJson.getString("createTime"));
                                            r.setStatusChageTime(queryDateJson.getString("statusChageTime"));
                                            if(CMCCOpenService.having(queryDateJson, "refundTime")){
                                                r.setRefundTime(queryDateJson.getString("refundTime"));
                                            }else{
                                                r.setRefundTime(dataJson.getString("refundTime"));
                                            }
                                            r.setBatchNo(queryDateJson.getString("batchNo"));
                                            payRefundService.save(r);
                                        }else{
                                            RefundInfos rss = (RefundInfos)JSONObject.toBean(queryDateJson , RefundInfos.class);
                                            payRefundService.save(rss);
                                        }
                                    }
                                }else{
                                    RefundInfos r = (RefundInfos)JSONObject.toBean(queryDateJson , RefundInfos.class);
                                    payRefundService.save(r);
                                }
                            }
                        }

                        if (CMCCOpenService.having(dataJson , "statusInfo")) {
                            rs.setStatusInfo(dataJson.getString("statusInfo"));
                        }
                        if (CMCCOpenService.having(dataJson , "statusChangeTime")) {
                            rs.setStatusChangeTime(dataJson.getString("statusChangeTime"));
                        }
                        if (CMCCOpenService.having(dataJson , "batchNo")) {
                            rs.setBatchNo(dataJson.getString("batchNo"));
                        }
                        if (CMCCOpenService.having(dataJson , "status")) {
                            rs.setStatus(dataJson.getInt("status") == 2 ? 1 : 2);
                        }
                        if(!StringUtils.isBlank(rs.getStatusInfo())){
                            rs.setStatusInfo(dataJson.getInt("status") == 2 ?"退款成功" : "退款失败");
                        }

                    }
                }
            }
            rs.setUpdataDate(new Date());
        }
        return  json;
    }

    /**
     * 3.4.2.3	支付退款查询
     * 提交退款申请后， 通过调用该接口查询退款状态。 退款有一定延时， 请在 3 个工作日后
     * @param jsonInfo
     * @return
     */
    @WebMethod
    public String refundqueryHandle(@WebParam(name="jsonInfo")String jsonInfo){
        JSONObject json = new JSONObject(); // 返回值
        String  url  = PAYURL.trim() + "payment-api/pay/refundquery";
        try {
            JSONObject jsonObj = JSONObject.fromObject(jsonInfo);
            if(!CMCCOpenService.having(jsonObj, "orderId")) {
                json.put("retCode", "-1");
                json.put("retUrl", "");
                json.put("error", "参数格式中未找到OrderId,无法查询数据");
                return json.toString();
            }
            String  order_id = jsonObj.getString("orderId");
            JSONObject jsonReqBean = new JSONObject();
            /// 查询支付退款记录
            Refunds rs = payRefundService.queryRefundsByOrderId(order_id);
            if( rs!=null && rs.getRefundId()!=null){
                if(!StringUtils.isEmpty(rs.getPayId())){
                    jsonReqBean.put("payId",rs.getPayId());
                }
                if(!StringUtils.isEmpty(rs.getPartnerPayId())) {
                    jsonReqBean.put("partnerPayId",rs.getPartnerPayId());
                }
                jsonReqBean.put("partnerId",rs.getPartnerId());
                jsonReqBean.put("refundId",rs.getRefundId());
                //请求：
                JSONObject jsonObject = postRequest(jsonReqBean.toString(), url);
                rs.setQueryResult(jsonObject.toString());
                if (CMCCOpenService.having(jsonObject, "retCode")) {
                    if ("000000".equals(jsonObject.getString("retCode")) && "true".equals(jsonObject.getString("success"))) {
                        if (CMCCOpenService.having(jsonObject, "data")) {
                            JSONObject dataJson = new JSONObject();

                            if(jsonObject.get("data") instanceof JSONArray){
                                dataJson = JSONObject.fromObject(JSONArray.fromObject(jsonObject.get("data")).get(0));
                            }else{
                                dataJson = JSONObject.fromObject(jsonObject.get("data"));
                            }

                            if(CMCCOpenService.having(dataJson, "refundInfos")) {
                                List<RefundInfos> rInfos = payRefundService.queryRefundInfosByPayId(dataJson.getString("refundId"));
                                JSONArray jsonArray =  JSONArray.fromObject(dataJson.getString("refundInfos"));
                                for (Object str :jsonArray){
                                    JSONObject queryDateJson =   JSONObject.fromObject(str);
                                        if(rInfos.size()!=0) {
                                            for (RefundInfos r : rInfos) {
                                                if (r.getOrderId().equals(queryDateJson.getString("refundId"))) {
                                                    r.setStatus(queryDateJson.getString("status"));
                                                    r.setStatusInfo(queryDateJson.getString("statusInfo"));
                                                    rs.setStatusInfo(queryDateJson.getString("statusInfo"));
                                                    r.setCreateTime(queryDateJson.getString("createTime"));
                                                    r.setStatusChageTime(queryDateJson.getString("statusChageTime"));
                                                    if(CMCCOpenService.having(queryDateJson, "refundTime")){
                                                        r.setRefundTime(queryDateJson.getString("refundTime"));
                                                    }else{
                                                        r.setRefundTime(dataJson.getString("refundTime"));
                                                    }
                                                    r.setBatchNo(queryDateJson.getString("batchNo"));
                                                    payRefundService.save(r);
                                                }else{
                                                    RefundInfos rss = (RefundInfos)JSONObject.toBean(queryDateJson , RefundInfos.class);
                                                    payRefundService.save(rss);
                                                }
                                            }
                                        }else{
                                            RefundInfos r = (RefundInfos)JSONObject.toBean(queryDateJson , RefundInfos.class);
                                            payRefundService.save(r);
                                        }
                                }
                            }

                            if (CMCCOpenService.having(dataJson , "statusInfo")) {
                                rs.setStatusInfo(dataJson.getString("statusInfo"));
                            }
                            if (CMCCOpenService.having(dataJson , "statusChangeTime")) {
                                rs.setStatusChangeTime(dataJson.getString("statusChangeTime"));
                            }
                            if (CMCCOpenService.having(dataJson , "batchNo")) {
                                rs.setBatchNo(dataJson.getString("batchNo"));
                            }
                            if (CMCCOpenService.having(dataJson , "status")) {
                                rs.setStatus(dataJson.getInt("status"));
                            }

                            //更新支付表状态
                            PayProviderInfo  ppifon = paymentService.queryByOrderId(rs.getPartnerPayId());
                            ppifon.setStatus("1".equals(rs.getStatus()) ? -2 : -3);
                            ppifon.setStatusInfo(rs.getStatusInfo());
                            ppifon.setUpdateDate(new Date());
                            paymentService.save(ppifon);

                            json.put("retCode", rs.getStatus());
                            json.put("orderNum", "");
                            json.put("success", rs.getStatusInfo());
                            json.put("retUrl", "");
                            json.put("error", "");
                        } else {
                            json.put("retCode", "-1");
                            json.put("retUrl", "");
                            json.put("error", "异常：服务器返回数据中未找到DATA ，错误信息：" + jsonObject.get("retMsg"));
                            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
                        }
                    }else{
                        json.put("retCode", "-1");
                        json.put("retUrl", "");
                        json.put("error", jsonObject.get("retMsg"));
                        TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
                    }
                }else{
                    json.put("retCode", "-1");
                    json.put("retUrl", "");
                    json.put("error", "异常：" + jsonObject.toString());
                    TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
                }
                rs.setUpdataDate(new Date());
                payRefundService.save(rs);//更新退款表
            }else{
                json.put("retCode", "-1");
                json.put("retUrl", "");
                json.put("error", "无法查询该订单编号（"+order_id+"）对应的数据");
            }
        } catch (Exception e) {
            String error = e.getMessage();
            json.put("retCode", "-1");
            json.put("retUrl", "");
            json.put("error", error);
            e.printStackTrace();
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
        }
            return json.toString();
    }


    /***
     * 请求参数
     * @param reqParam
     * @param url
     * @return
     */

    private net.sf.json.JSONObject postRequest(String  reqParam,String url){
        JSONObject params = new JSONObject();
        String signstr = reqParam +"&key=" + PAYKEY;
        String sign = AppMD5Util.getMD5UTF(signstr);
        params.put("data",reqParam);
        logger.info("加密sige："+sign);
        params.put("signType","MD5");
        params.put("sign",sign);
        logger.info("请求数据："  + params);

        // 本地测试测试服请求方法
       // String result= "{\"data\":{\"payFee\":1,\"payId\":\"300200120191111000002027\",\"payTime\":\"2019-11-11 10:54:02\",\"paymentInfos\":[{\"payFee\":1,\"payItemId\":\"30020012019111100000202700\",\"payModeId\":\"311004\"}],\"redirectUrl\":\"weixin://wxpay/bizpayurl?pr=cvo2NJu\",\"status\":\"0\"},\"retCode\":\"000000\",\"retMsg\":\"success\",\"sign\":\"49702e75042857678179a51b66864e39\",\"success\":true}";
      //String result =  CMCC1000OpenService.getInstance().bdcesPatams(url, params.toString());
        //正式服
         String result = UrlConnection.responseUTF8(url,params.toString());
        //废物方法已经放弃
      // String result = PayHttpChttpURLConnectionPOSTonection.(url, params.toString());
     //  String result ="{\"res\":\"{\\\"data\\\":{\\\"payFee\\\":1,\\\"payId\\\":\\\"300200120191217000508990\\\",\\\"payTime\\\":\\\"2019-12-17 16:47:34\\\",\\\"paymentInfos\\\":[{\\\"payFee\\\":1,\\\"payItemId\\\":\\\"30020012019121700050899000\\\",\\\"payModeId\\\":\\\"311051\\\"}],\\\"redirectUrl\\\":\\\"https://qr.alipay.com/bax068990fh03rfc0xn080d2\\\",\\\"status\\\":\\\"0\\\"},\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"0997420deadc77e143ffa1e820572da3\\\",\\\"success\\\":true}\",\"Status\":1}";
        //支付查询
      // String result = "{\"res\":\"{\\\"data\\\":{\\\"accessMode\\\":\\\"H5\\\",\\\"createTime\\\":\\\"2019-12-24 17:38:35\\\",\\\"currency\\\":\\\"RMB\\\",\\\"goodsInfo\\\":[{\\\"id\\\":\\\"***********\\\",\\\"price\\\":\\\"100\\\",\\\"name\\\":\\\"政企缴费\\\",\\\"rule\\\":\\\"1\\\",\\\"quantity\\\":\\\"1\\\"}],\\\"groupId\\\":\\\"1239619\\\",\\\"needLoginFlag\\\":\\\"N\\\",\\\"orderDesc\\\":\\\"测试\\\",\\\"partnerId\\\":\\\"3002001\\\",\\\"partnerPayId\\\":\\\"300200120191225657102438\\\",\\\"payId\\\":\\\"300200120191224000557080\\\",\\\"paymentItemInfo\\\":[{\\\"accountType\\\":\\\"311\\\",\\\"createTime\\\":\\\"2019-12-24 17:38:35\\\",\\\"orgPayId\\\":\\\"401520298420201912244104566671\\\",\\\"payId\\\":\\\"300200120191224000557080\\\",\\\"payItemId\\\":\\\"30020012019122400055708000\\\",\\\"payMode\\\":\\\"311004\\\",\\\"payTime\\\":\\\"2019-12-24 17:38:56\\\",\\\"realFee\\\":\\\"100\\\",\\\"status\\\":\\\"2\\\",\\\"statusInfo\\\":\\\"支付成功\\\",\\\"tradeFee\\\":\\\"100\\\"}],\\\"status\\\":\\\"2\\\",\\\"statusChangeTime\\\":\\\"2019-12-24 17:38:57\\\",\\\"statusInfo\\\":\\\"\\\",\\\"timeout\\\":\\\"30M\\\",\\\"totalFee\\\":\\\"100\\\"},\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"9d2d72f20d4bfa740c8afbed0f7cff4b\\\",\\\"success\\\":true}\",\"Status\":1}\n";
       // String result ="{\"res\":\"{\\\"data\\\":{\\\"payFee\\\":1,\\\"payId\\\":\\\"300200120191217000508790\\\",\\\"payTime\\\":\\\"2019-12-17 16:31:25\\\",\\\"paymentInfos\\\":[{\\\"payFee\\\":1,\\\"payItemId\\\":\\\"30020012019121700050879000\\\",\\\"payModeId\\\":\\\"311004\\\"}],\\\"redirectUrl\\\":\\\"https://statecheck.swiftpass.cn/pay/q/2SpMa4-d5\\\",\\\"status\\\":\\\"0\\\"},\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"f96eec96f80dc024b0f2c7c0ff83eec0\\\",\\\"success\\\":true}\",\"Status\":1}";

        // 退款
       // String result ="{\"res\":\"{\\\"data\\\":{\\\"refundId\\\":\\\"300200120191218810325764\\\",\\\"refundTotalFee\\\":\\\"1\\\"},\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"8ae54801ca97fa4bb37caa870fe36a61\\\",\\\"success\\\":true}\",\"Status\":1}";
        //String result ="{\"res\":\"{\\\"data\\\":{\\\"refundId\\\":\\\"300200120191218467802513\\\",\\\"refundTotalFee\\\":\\\"1\\\"},\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"75f0593c59c1a0162be959ea17647b63\\\",\\\"success\\\":true}\",\"Status\":1}";
      // String result = "{\"res\":\"{\\\"data\\\":[{\\\"batchNo\\\":\\\"201912183002001810325764\\\",\\\"groupId\\\":\\\"23\\\",\\\"partnerId\\\":\\\"3002001\\\",\\\"partnerPayId\\\":\\\"300200120191217023687415\\\",\\\"payId\\\":\\\"300200120191217000508990\\\",\\\"refundId\\\":\\\"300200120191218810325764\\\",\\\"refundInfos\\\":[{\\\"accountType\\\":\\\"311\\\",\\\"batchNo\\\":\\\"20191218300200181032576400\\\",\\\"createTime\\\":\\\"2019-12-18 09:53:03\\\",\\\"orgPayId\\\":\\\"401520298420201912177247284146\\\",\\\"partnerPayId\\\":\\\"300200120191217281364507\\\",\\\"payId\\\":\\\"300200120191217000508990\\\",\\\"refundFee\\\":\\\"1\\\",\\\"refundId\\\":\\\"300200120191218810325764\\\",\\\"refundItemId\\\":\\\"30020012019121881032576400\\\",\\\"refundType\\\":\\\"RF\\\",\\\"status\\\":\\\"0\\\",\\\"statusChageTime\\\":\\\"2019-12-18 09:53:03\\\",\\\"statusInfo\\\":\\\"待退款\\\"}],\\\"refundTime\\\":\\\"2019-12-18 09:53:03\\\",\\\"refundTotalFee\\\":1,\\\"refundType\\\":\\\"RF\\\",\\\"remark\\\":\\\"default reason...\\\",\\\"status\\\":\\\"0\\\",\\\"statusChangeTime\\\":\\\"2019-12-18 09:53:03\\\"}],\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"f1ac116ffe4e5786bf15c36624fdc16c\\\",\\\"success\\\":true}\",\"Status\":1}\n";
//        String result = "{\"res\":\"{\\\"data\\\":{\\\"refundId\\\":\\\"300200120191218614082735\\\",\\\"refundTotalFee\\\":\\\"1\\\"},\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"53703bf5f67bbbdbef3325132246a653\\\",\\\"success\\\":true}\",\"Status\":1}";
//        if(url.indexOf("refundquery")>-1){
//            result = "{\"res\":\"{\\\"data\\\":[{\\\"batchNo\\\":\\\"201912183002001810325764\\\",\\\"groupId\\\":\\\"23\\\",\\\"partnerId\\\":\\\"3002001\\\",\\\"partnerPayId\\\":\\\"300200120191217023687415\\\",\\\"payId\\\":\\\"300200120191217000508990\\\",\\\"refundId\\\":\\\"300200120191218810325764\\\",\\\"refundInfos\\\":[{\\\"accountType\\\":\\\"311\\\",\\\"batchNo\\\":\\\"20191218300200181032576400\\\",\\\"createTime\\\":\\\"2019-12-18 09:53:03\\\",\\\"orgPayId\\\":\\\"401520298420201912177247284146\\\",\\\"partnerPayId\\\":\\\"300200120191217281364507\\\",\\\"payId\\\":\\\"300200120191217000508990\\\",\\\"refundFee\\\":\\\"1\\\",\\\"refundId\\\":\\\"300200120191218810325764\\\",\\\"refundItemId\\\":\\\"30020012019121881032576400\\\",\\\"refundType\\\":\\\"RF\\\",\\\"status\\\":\\\"0\\\",\\\"statusChageTime\\\":\\\"2019-12-18 09:53:03\\\",\\\"statusInfo\\\":\\\"待退款\\\"}],\\\"refundTime\\\":\\\"2019-12-18 09:53:03\\\",\\\"refundTotalFee\\\":1,\\\"refundType\\\":\\\"RF\\\",\\\"remark\\\":\\\"default reason...\\\",\\\"status\\\":\\\"0\\\",\\\"statusChangeTime\\\":\\\"2019-12-18 09:53:03\\\"}],\\\"retCode\\\":\\\"000000\\\",\\\"retMsg\\\":\\\"success\\\",\\\"sign\\\":\\\"f1ac116ffe4e5786bf15c36624fdc16c\\\",\\\"success\\\":true}\",\"Status\":1}";
//        }
     logger.info("返回数据："  + result);
        net.sf.json.JSONObject jsonObject = net.sf.json.JSONObject.fromObject(result);
        return net.sf.json.JSONObject.fromObject(jsonObject.get("res"));
    }

}
