package com.xinxinsoft.utils.easyh;


import java.io.Serializable;
import java.util.Iterator;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.xinxinsoft.utils.StringUtil;

public class PageResponse implements Serializable{
	private static final long serialVersionUID = 1L;
	//页
	@Expose private int page;
	//总页数
	@Expose private int total;
	//总条数
	@Expose private int records;
	//结果集
	@Expose private Object rows;
	
	public int getPage() {
		return page;
	}
	public void setPage(int page) {
		this.page = page;
	}
	public int getTotal() {
		return total;
	}
	public void setTotal(int total) {
		this.total = total;
	}
	public int getRecords() {
		return records;
	}
	public void setRecords(int records) {
		this.records = records;
	}
	public Object getRows() {
		return rows;
	}
	public void setRows(Object rows) {
		this.rows = rows;
	}
	public void setPagePo(PagePO page){
		setPage(page.getCpage());
		setRecords(page.getTotNumber());
		setTotal(page.getTotPage());
		setRows(page.getData());
	}
	
	public String toJson(GsonBuilder builder){
		Gson gson = builder.create();
		return gson.toJson(this);
	}
	
	public String toJson(GsonBuilder builder, ConvertValue convertValue){
		String json = toJson(builder);
		if(convertValue != null){
			JSONObject jsonobj = JSONObject.fromObject(json);
			JSONArray array = jsonobj.getJSONArray("rows");
			for (int i=0; i<array.size(); i++) {
				JSONObject obj = array.getJSONObject(i);
				Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
				while(iterator.hasNext()){
					Map.Entry<String, Object> entry = iterator.next();
					obj.put(entry.getKey(), convertValue.convert(entry.getKey(), entry.getValue()));
				}
			}
			json = jsonobj.toString();
		}
		return json;
	}
	/**
	 * json数组显示方式
	 * @param builder
	 * @param key
	 * @param cells
	 * @return
	 */
	public String toOldJson(GsonBuilder builder, String key,Object... cells) {
		String json = toJson(builder);
		JSONObject jsonobj = JSONObject.fromObject(json);
		System.out.println(jsonobj);
		JSONArray array = jsonobj.getJSONArray("rows");
				
		StringBuffer sb=new StringBuffer();
		JSONArray newarray=new JSONArray();
		for (int i = 0; i < array.size(); i++) {
			JSONObject newobj=new JSONObject();
			sb.append("[").append("\"");
			JSONObject obj = array.getJSONObject(i);
			Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
			while (iterator.hasNext()) {
				Map.Entry<String, Object> entry = iterator.next();
				if (key.equals(entry.getKey())) {
					//obj.put("id", entry.getValue());
					newobj.put("id", entry.getValue());
				}
				for (int j = 0; j < cells.length; j++) {
					/*if(cells[j].toString().split(".").length > 0){
						JSONObject cellobj=(JSONObject) entry.getValue();
					}*/
					if(cells[j].equals(entry.getKey())){
						sb.append(StringUtil.AllNullToString(entry.getValue())).append("\"").append(",\"");
					}
				}
			}
			sb.append(" _").append("\"").append("]");
			newobj.put("cell", sb.toString());
			newarray.add(newobj);
		}
		jsonobj.remove("rows");
		jsonobj.put("rows", newarray);
		json = jsonobj.toString();
		return json;
	}
	
	public static class ConvertValue{
		public Object convert(String field, Object value){
			return value;
		}

		
	}
}
