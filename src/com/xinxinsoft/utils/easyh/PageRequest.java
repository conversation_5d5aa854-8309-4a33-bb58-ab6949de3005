package com.xinxinsoft.utils.easyh;

import java.io.Serializable;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import javax.servlet.http.HttpServletRequest;

@SuppressWarnings("unchecked")
public class PageRequest implements Serializable {
	private static final long serialVersionUID = 1L;
	// 页
	private int page;
	// 显示行数
	private int rows;
	// 排序字段
	private String sidx; // order name
	// 排序模式
	private String sord; // order(asc:desc)
	// 查询语句
	private String query;
	// 查询总数
	private String queryCount;
	// 是否是hql
	private boolean hql = true;
	// 查询参数
	private List<Object> parameters = new ArrayList<Object>(0);
	// 请求对象
	private HttpServletRequest request;
	
	//使用addEntity
	private boolean autofill;
	public boolean isAutofill() {
			return autofill;
		}

		public void setAutofill(boolean autofill) {
			this.autofill = autofill;
		}

	private Class targetClass;

	public Class getTargetClass() {
		return targetClass;
	}

	public Object[] getParameters() {
		return parameters == null ? new Object[0] : parameters.toArray();
	}

	public void setParameters(Object... parameters) {
		this.parameters = new ArrayList<Object>(Arrays.asList(parameters));
	}
	
	/**
	 * 添加查询参数
	 * @param param
	 */
	public void addParamterValue(Object param){
		this.parameters.add(param);
	}
	
	/**
	 * 添加查询参数，且自动验证空值
	 * @param key
	 * @param c
	 */
	public boolean addParamter(String key, Class<?> c){
		if(validateParam(key)){
			addParamterValue(getParam(key, c));
			return true;
		}
		return false;
	}
	
	public static enum Like{
		befor, after, none, full
	}
	public boolean addParamter(String key, Class<?> c, boolean like, Like type){
		if(validateParam(key)){
			Object v = getParam(key, c);
			if(like)
			{
				switch(type)
				{
					case none:addParamterValue(v);break;
					case befor:addParamterValue("%" + v);break;
					case after:addParamterValue(v + "%");break;
					case full:addParamterValue("%" + v + "%");break;
					default:;
				}
			}
			else
			{
				addParamterValue(v);
			}
			return true;
		}
		return false;
	}
	
	public String getQuery() {
		if (getSidx() != null && !"".equals(getSidx().trim())) {
			//System.out.println(query);
			if (query.toUpperCase().contains("ORDER BY")) {
				return query + " " + getSidx() + " " + getSord();
			} else {
				return query + " ORDER BY " + getSidx() + " " + getSord();
			}
		}
		return query;
	}

	public void setQuery(String query) {
		this.query = query;
	}

	public String getQueryCount() {
		return queryCount;
	}

	public void setQueryCount(String queryCount) {
		this.queryCount = queryCount;
	}

	public boolean isHql() {
		return hql;
	}

	public void useHql(boolean hql, Class targetClass) {
		this.hql = hql;
		this.targetClass = targetClass;
	}

	public void useHql(boolean hql) {
		this.hql = hql;
	}

	public int getPage() {
		return page;
	}

	public void setPage(int page) {
		this.page = page;
	}

	public int getRows() {
		return rows;
	}

	public void setRows(int rows) {
		this.rows = rows;
	}

	public String getSidx() {
		return sidx;
	}

	public void setSidx(String sidx) {
		this.sidx = sidx;
	}

	public String getSord() {
		if(sord==null) return "";
		return sord;
	}

	public void setSord(String sord) {
		this.sord = sord;
	}

	public PagePO getPagePo() {
		PagePO page = new PagePO();
		page.setCpage(this.page);
		page.setShowNumber(this.rows);
		return page;
	}

	public String getString(String name) {
		return request.getParameter(name);
	}

	public Integer getInteger(String name) {
		return new Integer(getString(name));
	}

	public Object getObject(String name) {
		return getString(name);
	}

	public HttpServletRequest getRequest() {
		return request;
	}

	public void setRequest(HttpServletRequest request) {
		this.request = request;
	}

	public void setTargetClass(Class<?> targetClass) {
		this.targetClass = targetClass;
	}
	
	/**
	 * 验证参数
	 * @param request
	 * @param key
	 * @return
	 */
	public boolean validateParam(String key) {
		if (request == null)
			return false;
		String valaue = request.getParameter(key);
		if (valaue == null || valaue.trim().equals("")) {
			return false;
		}
		return true;
	}
	
	/**
	 * 获取转换后的参数
	 * @param <T>
	 * @param request
	 * @param key
	 * @param c
	 * @return
	 */
	@SuppressWarnings("unchecked")
	public <T> T getParam(String key, Class<T> c) {
		if (c == String.class) {
			return (T) request.getParameter(key);
		} else if (c == Date.class) {
			SimpleDateFormat format = new SimpleDateFormat(
					"yyyy-MM-dd HH:mm:ss");
			try {
				return (T) format.parse(request.getParameter(key));
			} catch (ParseException e) {
				e.printStackTrace();
				return null;
			}
		} 
		else if(c == Float.class){
			return (T) new Float(Float.parseFloat(request.getParameter(key)));
		}
		else if(c == Double.class){
			return (T) new Double(Double.parseDouble(request.getParameter(key)));
		}
		else if (c == Integer.class) {
			return (T) new Integer(Integer.parseInt(request.getParameter(key)));
		}
		return null;
	}
	
	public void appendQueryWhere(String where){
		this.query += where;
		this.queryCount += where;
	}
	
	public void appendUnionQuery(String str){
		this.query += " UNION ALL ";
		this.query+= str;
		this.queryCount += " UNION ALL ";
		this.queryCount +=  str;
		
	}
	
	public void buildQueryWhere(BuildQueryWhere builder){
		builder.execute(this);
	}
	
	public static abstract class BuildQueryWhere{
		public abstract void execute(PageRequest request);
	}
	
	public void BuildUnionQuery(BuildUnionQuery builder){
		builder.build(this);
	}
	
	public static abstract class BuildUnionQuery{
		public abstract void build(PageRequest request);
	}
}
