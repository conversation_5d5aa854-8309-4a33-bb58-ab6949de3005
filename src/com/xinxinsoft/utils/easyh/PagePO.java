package com.xinxinsoft.utils.easyh;

public class PagePO {
	private int cpage = 1; // 当前页码
	private int showNumber = 10; // 显示条数
	private Object data; // 返回的数据
	private int totNumber = 0; // 总条数
	
	public int getTotPage() {
		//取大于一个数的最小整数
		return (int)Math.ceil(totNumber/1.0/showNumber);
	}
	
	public PagePO(){
		
	}
	
	public PagePO(int cpage,int totNumber,Object data,int showNumber){
		this.cpage = cpage;
		this.totNumber = totNumber;
		this.data = data;
		this.showNumber = showNumber;
	}
	
	/**
	 * 得到开始下标
	 * @return
	 */
	public Integer getStartNumber() {
		if(0 == this.cpage){
			return 0;
		}
		return (this.cpage - 1) * this.showNumber;
	
	}
	
	public void setTotPage(int totPage) {
	}
	

	
	public int getCpage() {
		return cpage;
	}
	public void setCpage(int cpage) {
		this.cpage = cpage;
	}
	public int getShowNumber() {
		return showNumber;
	}
	public void setShowNumber(int showNumber) {
		this.showNumber = showNumber;
	}
	public Object getData() {
		return data;
	}
	public void setData(Object data) {
		this.data = data;
	}
	public int getTotNumber() {
		return totNumber;
	}
	public void setTotNumber(int totNumber) {
		this.totNumber = totNumber;
	}
}
