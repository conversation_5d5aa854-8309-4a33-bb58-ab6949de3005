package com.xinxinsoft.utils.easyh;


import java.io.Serializable;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;

public class NonPageResponse extends ArrayList<Object> implements Serializable, List<Object>{
	private static final long serialVersionUID = 1L;
	
	public String toJson(GsonBuilder builder){
		Gson gson = builder.create();
		return gson.toJson(this);
	}
	
	@SuppressWarnings("unchecked")
	public String toJson(GsonBuilder builder, ConvertValue convertValue){
		String json = toJson(builder);
		System.out.println(json);
		if(convertValue != null){
			JSONObject jsonobj = JSONObject.fromObject(json);
			JSONArray array = jsonobj.getJSONArray("rows");
			for (int i=0; i<array.size(); i++) {
				JSONObject obj = array.getJSONObject(i);
				Iterator<Map.Entry<String, Object>> iterator = obj.entrySet().iterator();
				while(iterator.hasNext()){
					Map.Entry<String, Object> entry = iterator.next();
					obj.put(entry.getKey(), convertValue.convert(entry.getKey(), entry.getValue()));
				}
			}
			json = jsonobj.toString();
		}
		return json;
	}
	
	public static class ConvertValue{
		public Object convert(String field, Object value){
			return value;
		}
	}
	
	public Object uniqueResult(){
		if(size() == 0) return null;
		return get(0);
	}
}
