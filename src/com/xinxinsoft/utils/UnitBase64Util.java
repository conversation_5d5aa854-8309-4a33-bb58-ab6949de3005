package com.xinxinsoft.utils;


import org.apache.commons.codec.binary.Base64;

import java.io.UnsupportedEncodingException;

public abstract class UnitBase64Util {
    /**
     * base64加密
     * @param str
     * @return
     */
    public static String encode(String str) throws UnsupportedEncodingException {
        byte[] bytes = str.getBytes();
        //Base64 加密
        byte[] byteArr = Base64.encodeBase64(str.getBytes("UTF-8"));
        return new String(byteArr);
    }

    /**
     * base64解密
     * @param str
     * @return
     */
    public static String decrypt(String str) throws UnsupportedEncodingException {
        byte[] bytes = str.getBytes();
        //Base64 加密
        byte[] byteArr = Base64.decodeBase64(str.getBytes());
        return new String(byteArr, "UTF-8");
    }

    public static void main(String[] args) {
        try {
            //91511681MA2B28653  //华蓥市易诚电子商务有限公司 //洪鑫晶  华蓥市格林枫情整体家居店 92511681MA6AC0N84C
           // String name = decrypt("5rmW5YyX55yB5a6c5Z+O5biC5LiJ5Y6C55KQM+WPtw==");
           String name=encode("");
            System.out.println(name);
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }
}
