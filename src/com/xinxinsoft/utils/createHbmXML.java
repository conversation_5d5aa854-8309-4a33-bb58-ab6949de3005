package com.xinxinsoft.utils;

import java.io.File;
import java.io.FileWriter;
import java.io.IOException;
import java.io.Writer;
import java.lang.reflect.Field;
import java.lang.reflect.Method;

/**
 * 
     * @ClassName: createHbmXML
     * @Description: TODO(用于通过实体类生成hbm.xml)
     * <AUTHOR>
     * @date 2020-4-13
     *
 */
public class createHbmXML {

	private final static String UNDERLINE = "_";

	/**
	 * 输入类的包名及类名，在其目录下创建xxx.hbm.xml文件
	 * 
	 * @param packageName
	 *            类的包名
	 * @param className
	 *            类名
	 */
	public static void createHbmXML(String packageName, String className) {
		try {
			Class<?> clazz = Class.forName(packageName + "." + className);
			Field[] fields = clazz.getDeclaredFields();
			StringBuilder sBuilder = new StringBuilder();
			sBuilder.append("<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n"
					+ "<!DOCTYPE hibernate-mapping PUBLIC "
					+ "\"-//Hibernate/Hibernate Mapping DTD 3.0//EN\" "
					+ " \"http://www.hibernate.org/dtd/hibernate-mapping-3.0.dtd\" > \n");
			sBuilder.append("<hibernate-mapping package=\"" + packageName + /*"."+ className +*/ "\">\n");
			sBuilder.append("<class name=\"" + className + "\" table=\""
					+ humpToUnderline(firstToLowCase(className)) + "\">\n");
			for (Field field : fields) {
				String param = field.getName();
				String paramType=field.getGenericType().toString().replace("class ", "");
				System.out.println("param====>"+param+"     paramType====>"+paramType);
				
				if(paramType.equals("int")){
					paramType="java.lang.Integer";
				}
				
				if ("id".equals(param)) {
					String keyType="native";
					if(paramType.equals("java.lang.String")){
						keyType="uuid";
					}
					sBuilder.append("\t<id name=\"id\" column=\"id\" type=\""+paramType+"\"><generator class=\""+keyType+"\"></generator></id>\n");
				} else {
					sBuilder.append("\t<property name=\"" + param + "\" column=\""+humpToUnderline(param)+"\" type=\""+paramType+"\"/>\n");
				}
			}
			sBuilder.append(" </class>\n</hibernate-mapping>");
			File directory = new File(".");
			String path = directory.getCanonicalPath();
			packageName = packageName.replace('.', File.separatorChar);
			String fileName = className + ".hbm.xml";
			path = path + File.separator + "src" + File.separator + packageName
					+ File.separator + fileName;
			createFile(sBuilder, className, path);
		} catch (ClassNotFoundException e) {
			e.printStackTrace();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/**
	 * 首字母小写。ClientInfo-->clientInfo
	 * 
	 * @param className
	 *            类名
	 * @return 属性名
	 */
	public static String firstToLowCase(String className) {
		char temp[] = className.toCharArray();
		temp[0] += 32;
		String string = String.valueOf(temp);
		return string;
	}

	/**
	 * 创建xx.hbm.xml文件
	 * 
	 * @param sBuilder
	 * @param className
	 */
	public static void createFile(StringBuilder sBuilder, String className,
			String path) {
		File file = new File(path);
		try {
			if (file.exists()) {
				file.delete();
				file.createNewFile();
				System.out.println("文件重新创建");
			}
			Writer out = new FileWriter(file);
			out.write(sBuilder.toString());
			out.close();
			System.out.println("创建成功");
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	/***
	 * 下划线命名转为驼峰命名
	 * 
	 * @param para
	 *            下划线命名的字符串
	 */

	public static String underlineToHump(String para) {
		StringBuilder result = new StringBuilder();
		String a[] = para.split(UNDERLINE);
		for (String s : a) {
			if (!para.contains(UNDERLINE)) {
				result.append(s);
				continue;
			}
			if (result.length() == 0) {
				result.append(s.toLowerCase());
			} else {
				result.append(s.substring(0, 1).toUpperCase());
				result.append(s.substring(1).toLowerCase());
			}
		}
		return result.toString();
	}

	/***
	 * 驼峰命名转为下划线命名
	 * 
	 * @param para
	 *            驼峰命名的字符串
	 */

	public static String humpToUnderline(String para) {
		StringBuilder sb = new StringBuilder(para);
		int temp = 0;// 定位
		if (!para.contains(UNDERLINE)) {
			for (int i = 0; i < para.length(); i++) {
				if (Character.isUpperCase(para.charAt(i))) {
					sb.insert(i + temp, UNDERLINE);
					temp += 1;
				}
			}
		}
		return sb.toString().toUpperCase();
	}

	
	
	public static void main(String[] args) {
		//createHbmXML.createHbmXML("com.xinxinsoft.entity.pms", "PmsAttrValue");
		
	}
}
