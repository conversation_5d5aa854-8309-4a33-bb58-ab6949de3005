package com.xinxinsoft.utils;

import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.AcroFields;
import com.itextpdf.text.pdf.PdfReader;
import com.itextpdf.text.pdf.security.CertificateInfo;
import com.itextpdf.text.pdf.security.PdfPKCS7;
import org.bouncycastle.jce.provider.BouncyCastleProvider;

import java.lang.reflect.Field;
import java.security.Security;
import java.util.ArrayList;
import java.util.List;

/**
 * @ClassName: PDFSignUtils
 * @Title: PDFSignUtils
 * @Package: com.xinxinsoft.utils
 * @author: liyang
 * @date: 2022/5/6 15:12
 * @Version: 1.0
 * @Description: TODO
 */
public class PDFSignUtils {
    public static void setUp() throws Exception {
        BouncyCastleProvider bcp = new BouncyCastleProvider();
        Security.insertProviderAt(bcp, 1);
    }

    public static Boolean isVerify(List<PDFSign> list){
        Boolean result = false;
        look:
        for (int i = 0; i < list.size(); i++) {
            PDFSign pdfSign = list.get(i);

            for (int j = 0; j < list.size(); j++) {
                PDFSign pdfSign2 = list.get(j);
                if(pdfSign.getPage() == pdfSign2.getPage()){
                    if(pdfSign.getName()!=null && pdfSign2.getName()!=null && !pdfSign.getName().equals(pdfSign2.getName())){
                        result = true;
                        break look;
                    }
                }
            }
        }
        return result;
    }

    public static boolean combination(String path)throws Exception{
        setUp();
        Boolean result = null;
        Field rsaDataField = PdfPKCS7.class.getDeclaredField("RSAdata");
        rsaDataField.setAccessible(true);
        PdfReader reader = new PdfReader(path);

        AcroFields acroFields = reader.getAcroFields();
        List<String> names = acroFields.getSignatureNames();

        List<PDFSign> list = new ArrayList<PDFSign>();

        for (String name : names) {
            PDFSign pdfSign = new PDFSign();
            pdfSign.setName(name);
            PdfPKCS7 pk = acroFields.verifySignature(name);
            List<AcroFields.FieldPosition> positions = acroFields.getFieldPositions(name);

            int page = positions.get(0).page;
            pdfSign.setPage(page);

            CertificateInfo.X500Name issuerFields = CertificateInfo.getIssuerFields(pk.getSigningCertificate());
            if(issuerFields!=null){
                pdfSign.setIssuerFields(issuerFields.toString());
            }

            CertificateInfo.X500Name subjectFields = CertificateInfo.getSubjectFields(pk.getSigningCertificate());
            if(subjectFields!=null){
                pdfSign.setSubjectFields(subjectFields.toString());
            }

            list.add(pdfSign);
        }
        reader.close();
        boolean bl = isVerify(list);
        return bl;
    }

    public static Boolean testVerifyTestMGomez(String path) throws Exception {
        setUp();
        Boolean result = null;
        Field rsaDataField = PdfPKCS7.class.getDeclaredField("RSAdata");
        rsaDataField.setAccessible(true);
        PdfReader reader = new PdfReader(path);

        AcroFields acroFields = reader.getAcroFields();
        List<String> names = acroFields.getSignatureNames();
        for (String name : names) {
            System.out.println("-----------------------------");
            System.out.println("签名域名称: " + name);
            System.out.println("是否覆盖整个文档: " + acroFields.signatureCoversWholeDocument(name));
            PdfPKCS7 pk = acroFields.verifySignature(name);
            List<AcroFields.FieldPosition> positions = acroFields.getFieldPositions(name);
            Rectangle rect = positions.get(0).position; // In points:
            float left = rect.getLeft();
            float bTop = rect.getTop();
            float width = rect.getWidth();
            float height = rect.getHeight();

            int page = positions.get(0).page;
            Rectangle pageSize = reader.getPageSize(page);
            float pageHeight = pageSize.getTop();
            float top = pageHeight - bTop;
            System.out.println("页码:"+page);
            System.out.println("宽高度(w,h): ( "+width+","+height+" )");
            System.out.println("坐标(x,y): ( "+left+","+top+" )");
            System.out.println("主题: " + CertificateInfo.getSubjectFields(pk.getSigningCertificate()));
            System.out.println("发证机关: " + CertificateInfo.getIssuerFields(pk.getSigningCertificate()));
            // System.out.println("签署时间:"+ DateHelper.getDate(pk.getSignDate().getTime()));
            // System.out.println("证书有效期起始时间: " + DateHelper.getDate(pk.getSigningCertificate().getNotBefore()));
            // System.out.println("证书有效期结束时间: " + DateHelper.getDate(pk.getSigningCertificate().getNotAfter()));
            System.out.println("发证机关秘钥标识符: " +pk.getSigningCertificate().getPublicKey());
            Object rsaDataFieldContent = rsaDataField.get(pk);
            if (rsaDataFieldContent != null && ((byte[]) rsaDataFieldContent).length == 0) {
                System.out.println("Found zero-length encapsulated content: ignoring");
                rsaDataField.set(pk, null);
            }
            System.out.println("签名是否有效: " + pk.verify());
        }
        reader.close();

        return result;
    }
}
