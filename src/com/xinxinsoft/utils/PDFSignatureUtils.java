package com.xinxinsoft.utils;

import java.awt.image.BufferedImage;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import net.coobird.thumbnailator.Thumbnails;
import Decoder.BASE64Decoder;

import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.utils.common.FileUpload;

public class PDFSignatureUtils {

	private static final String basePath = "F:/FTP/"; // 模版存储路径，项目里我就放在resource下

	public static void main(String[] args) throws Exception {

		// makeWord();
		// makePdfByXcode();
		//addWater("F:/FTP/test.pdf", "F:/FTP/test_w.pdf","【省公司】2018011212312312312");
		addSign("F:/FTP/contracts/danyichanpin.pdf","盖章处","G:/12.jpg","G:/互联网专线业务协议100.pdf");
		//xuanzhaun();
		 /*File file = new File("E:/new - 副本.png");
		 File outFile = new File("E:/new - 副本1111.png");
         //逆时针旋转90度测试  
         BufferedImage image2 = ImageIO.read(file);    
         image2=rotateCounterclockwise90(image2);  
         ImageIO.write(image2, "png", outFile); */
	}

	/**
	 * 签章功能
	 * @param sourcePDF 原PDF路径
	 * @param splitParentkeyValues  关键字
	 * @param imagePath  图片地址
	 * @param targetPath  签章后的PDF地址
	 * @throws Exception  错误代码
	 */
	public static void addSign(String sourcePDF,String splitParentkeyValues,String imagePath,String targetPath) throws Exception{
		  //sourcePDF: pdf的文档路径
		  //splitParentkeyValues[i]:关键字，其中对关键字进行特殊符号的过滤，不然会导致后面的匹配结果有误。
		List<MatchItem> matches =null;
		matches = matchPage(sourcePDF, splitParentkeyValues);
		//找出关键字后，将要盖章的图片准确定位到关键字周围，也可以采用坐标的方
 		MatchItem matchItem  = new MatchItem();
		if(matches.size()>0){
			// 读取模板文件
			InputStream input = new FileInputStream(new File(sourcePDF));
			com.itextpdf.text.pdf.PdfReader reader = new com.itextpdf.text.pdf.PdfReader(input);
			com.itextpdf.text.pdf.PdfStamper stamper = new com.itextpdf.text.pdf.PdfStamper(reader, new FileOutputStream(targetPath));
			com.itextpdf.text.pdf.PdfGState gs = new com.itextpdf.text.pdf.PdfGState();
			System.out.println(matches.size());
			for (int i = 0;i<matches.size();i++){
				if(i == 0 || i == 2){
					int pageNum = matches.get(i).getPageNum();
					gs.setFillOpacity(9.0f);
					float pageWidth = reader.getPageSize(pageNum).getWidth();
					float pageHeight = reader.getPageSize(pageNum).getHeight();

					//matchItem.setX(matches.get(0).getX()-splitParentkeyValues.length() * 20);
					//matchItem.setY(matches.get(0).getY() - 150 / 1.527731f);
					matchItem.setX(matches.get(i).getX());
					matchItem.setY(matches.get(i).getY());
					com.itextpdf.text.Image img=com.itextpdf.text.Image.getInstance(imagePath);

					// 根据域的大小缩放图片
					System.out.println("宽："+pageWidth+"高"+pageHeight);

					//指定签章图片大小尺寸
					float imgWidth=102;
					float imgHeight=102;
					img.scaleToFit(imgWidth, imgHeight);
					//设置印章加入位置。。
					System.out.println(matchItem.getY());
					System.out.println(imgHeight/2);
					System.out.println(matchItem.getY()-imgHeight/2);
					//System.out.println();
					//img.setAbsolutePosition(matchItem.getX()+40, matchItem.getY()-40);// 位置
					img.setAbsolutePosition(matchItem.getX()+imgWidth/1, matchItem.getY()-imgHeight/3);// 位置
					com.itextpdf.text.pdf.PdfContentByte over = stamper.getOverContent(pageNum);
					over.setGState(gs);
					over.addImage(img);
				}
			}
			gs.clear();
			stamper.close();
		    reader.close();
			input.close();
		    System.out.println("签章成功");
		}
	}

	/**
	 * 查找所有
	 * 
	 * @param fileName
	 *            文件路径
	 * @param keyword
	 *            关键词
	 * @return
	 * @throws Exception
	 */
	public static List<MatchItem> matchPage(String fileName, String keyword)
			throws Exception {
		List<MatchItem> items = new ArrayList();
		com.itextpdf.text.pdf.PdfReader reader = new com.itextpdf.text.pdf.PdfReader(fileName);
		int pageSize = reader.getNumberOfPages();
		for (int page = 1; page <= pageSize; page++) {
			items.addAll(matchPage(reader, page, keyword));
		}
		return items;
	}

	/**
	 * 在文件中寻找特定的文字内容
	 * 
	 * @param reader
	 * @param pageNumber
	 * @param keyword
	 * @return
	 * @throws Exception
	 */
	public static List<MatchItem> matchPage(com.itextpdf.text.pdf.PdfReader reader,
			Integer pageNumber, String keyword) throws Exception {
		KeyWordPositionListener renderListener = new KeyWordPositionListener();
		renderListener.setKeyword(keyword);
		PdfReaderContentParser parse = new PdfReaderContentParser(reader);
		com.itextpdf.text.Rectangle rectangle = reader.getPageSize(pageNumber);
		renderListener.setPageNumber(pageNumber);
		renderListener.setCurPageSize(rectangle);
		parse.processContent(pageNumber, renderListener);
		return findKeywordItems(renderListener, keyword);
	}

	/**
	 * 找到匹配的关键词块
	 * 
	 * @param renderListener
	 * @param keyword
	 * @return
	 */
	public static List<MatchItem> findKeywordItems(KeyWordPositionListener renderListener,
			String keyword) {
		// 先判断本页中是否存在关键词
		List<MatchItem> allItems = renderListener.getAllItems();// 所有块LIST
		StringBuffer sbtemp = new StringBuffer("");
		for (MatchItem item : allItems) {// 将一页中所有的块内容连接起来组成一个字符串。
			sbtemp.append(item.getContent());
		}
		if (sbtemp.toString().indexOf(keyword) == -1) {// 一页组成的字符串没有关键词，直接return
			return renderListener.getMatches();
		}
		// 第一种情况：关键词与块内容完全匹配的项
		List matches = renderListener.getMatches();
		// 第二种情况：多个块内容拼成一个关键词，则一个一个来匹配，组装成一个关键词
		sbtemp = new StringBuffer("");
		List tempItems = new ArrayList();
		for (MatchItem item : allItems) {
			// 1，关键词中存在某块 2，拼装的连续的块=关键词 3，避开某个块完全匹配关键词
			// 关键词 中国移动 而块为 中 ，国，移动
			// 关键词 中华人民 而块为中，华人民共和国 这种情况解决不了，也不允许存在
			if (keyword.indexOf(item.getContent()) != -1
					&& !keyword.equals(item.getContent())) {
				tempItems.add(item);
				sbtemp.append(item.getContent());
				if (keyword.indexOf(sbtemp.toString()) == -1) {// 如果暂存的字符串和关键词
																// 不再匹配时
					sbtemp = new StringBuffer(item.getContent());
					tempItems.clear();
					tempItems.add(item);
				}
				if (sbtemp.toString().equalsIgnoreCase(keyword)) {// 暂存的字符串正好匹配到关键词时
					MatchItem tmpitem = getRightItem(tempItems, keyword);
					if (tmpitem != null) {
						matches.add(tmpitem);// 得到匹配的项
					}
					sbtemp = new StringBuffer("");// 清空暂存的字符串
					tempItems.clear();// 清空暂存的LIST
					continue;// 继续查找
				}
			} else {// 如果找不到则清空
				sbtemp = new StringBuffer("");
				tempItems.clear();
			}
		}
		// 第三种情况：关键词存在块中
		for (MatchItem item : allItems) {
			if (item.getContent().indexOf(keyword) != -1
					&& !keyword.equals(item.getContent())) {
				matches.add(item);
			}
		}
		return matches;
	}

	public static MatchItem getRightItem(List<MatchItem> tempItems,
			String keyword) {
		for (MatchItem item : tempItems) {
			if (keyword.indexOf(item.getContent()) != -1
					&& !keyword.equals(item.getContent())) {
				return item;
			}
		}

		return null;
	}
	
	public static void xuanzhaun(){
		try {
			Thumbnails.of("E:/new.png").size(1280,1024).rotate(-90).toFile("E:/new12.png");
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	//逆时针旋转90度（通过交换图像的整数像素RGB 值）  
    public static BufferedImage rotateCounterclockwise90(BufferedImage bi) {  
        int width = bi.getWidth();  
        int height = bi.getHeight();  
        BufferedImage bufferedImage = new BufferedImage(height, width, bi.getType());  
        for (int i = 0; i < width; i++)  
            for (int j = 0; j < height; j++)  
                bufferedImage.setRGB(j, i, bi.getRGB(i, j));  
        return bufferedImage;  
    }  
    
    public static String getimgPathPng(String number,String username,String filename,String companyGroupId){
    	SimpleDateFormat sdFormattwo = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
        String myTimetwo = sdFormattwo.format(new Date());
		try{ 
			String xmlStr=CMCCOpenService.getInstance().paperlessSystem(number,username,myTimetwo,filename,companyGroupId);
			xmlStr = xmlStr.replaceAll(" ","");
				Map<String,Object> map = new HashMap<String, Object>();
				Document document = DocumentHelper.parseText(xmlStr);
				Element  element = XmlUtil.getRoot(document);
				List<Element> elements = XmlUtil.getElements(element);
				for(Element elemen:elements ){
					map.put(elemen.getName(), elemen.getText()); 
				}
			if("000000".equals(map.get("RETNCD"))){ 
				byte[] imgStr = new BASE64Decoder().decodeBuffer(map.get("PDBS64").toString());   
		        if (imgStr == null ){ //图像数据为空
		            return "ON";  
		        }
		        System.out.println("加密："+imgStr);
		        byte[] b = imgStr;
	            for(int i=0;i<b.length;++i){  
	                if(b[i]<0){
	                    b[i]+=256;  
	                }  
	            }
	            File file = new File(FileUpload.getContractFTPURL()+"pdfsignature");
	            if (!file.exists()) {
	        		file.mkdir();
	        	}
	            //Calendar cal = Calendar.getInstance(); 
	            SimpleDateFormat sdFormat = new SimpleDateFormat("yyyyMMddhhmmssSSS"); 
	            String myTime = sdFormat.format(new Date());
	            String imgFilePath = FileUpload.getContractFTPURL()+"pdfsignature"+"/"+myTime+".png";//新生成的图片  
	            OutputStream out = new FileOutputStream(imgFilePath);    
	            out.write(b);  
	            out.flush();  
	            out.close(); 
	            return imgFilePath; 
			}else{
				return "ON"; 	
			}
        }catch (Exception e){  
            return "ON";  
        }  
	}
	
}
