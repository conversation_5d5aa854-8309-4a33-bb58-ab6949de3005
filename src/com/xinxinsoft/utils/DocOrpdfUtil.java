package com.xinxinsoft.utils;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.MultiFormatWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.decoder.ErrorCorrectionLevel;
import com.spire.doc.*;
import com.spire.doc.Document;
import com.spire.doc.documents.*;
import com.spire.doc.fields.DocPicture;
import com.spire.doc.fields.TextRange;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.openxml4j.opc.OPCPackage;
import org.apache.poi.util.Units;
import org.apache.poi.xwpf.model.XWPFHeaderFooterPolicy;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.xwpf.usermodel.Borders;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTBookmark;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTP;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.CTSectPr;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.imageio.ImageIO;
import javax.swing.filechooser.FileSystemView;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: DocOrpdfUtil
 * @Title: DocOrpdfUtil
 * @Package: com.xinxinsoft.utils
 * @author: liyang
 * @date: 2021/10/19 10:20
 * @Version: 1.0
 * @Description: TODO
 */
public class DocOrpdfUtil {
    private static Logger logger = LoggerFactory.getLogger(DocOrpdfUtil.class);
    /**
     * CODE_WIDTH：二维码宽度，单位像素
     * CODE_HEIGHT：二维码高度，单位像素
     * FRONT_COLOR：二维码前景色，0x000000 表示黑色
     * BACKGROUND_COLOR：二维码背景色，0xFFFFFF 表示白色
     * 演示用 16 进制表示，和前端页面 CSS 的取色是一样的，注意前后景颜色应该对比明显，如常见的黑白
     */
    private static final int CODE_WIDTH = 400;
    private static final int CODE_HEIGHT = 400;
    private static final int FRONT_COLOR = 0x000000;
    private static final int BACKGROUND_COLOR = 0xFFFFFF;
    /**
     * @author: liyang
     * @date: 2021/10/19 11:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO word文档表格规正
     */
    public static void docTableToRegulate(String inPath, String outPath) {
        try{
            //加载Word文档
            Document doc = new Document();
            doc.loadFromFile(inPath);
            //获取section
            Section section = doc.getSections().get(0);
            //获取表格
            Table table = section.getTables().get(0);
            //设置表格列宽适应内容
            //table.autoFit(AutoFitBehaviorType.Auto_Fit_To_Contents);
            //设置表格列宽适应窗体
            table.autoFit(AutoFitBehaviorType.Auto_Fit_To_Window);
            //设置表格固定列宽
            //table.autoFit(AutoFitBehaviorType.Fixed_Column_Widths);
            //保存文档
            doc.saveToFile(outPath,FileFormat.Docx_2013);
            doc.dispose();
            logger.error("word文档表格规正成功");
        }catch (Exception e){
            logger.error("word文档表格规正错误信息："+e.getMessage(),e);
        }
    }


    public static XWPFDocument createHeaderLEFT(XWPFDocument doc,String imgFile ) throws Exception {
        CTSectPr sectPr = doc.getDocument().getBody().addNewSectPr();
        XWPFHeaderFooterPolicy headerFooterPolicy = new XWPFHeaderFooterPolicy( doc, sectPr );
        XWPFHeader header = headerFooterPolicy.createHeader( headerFooterPolicy.DEFAULT );
        XWPFParagraph paragraph = header.createParagraph();
        paragraph.setAlignment( ParagraphAlignment.LEFT );
        paragraph.setBorderBottom( Borders.THICK );
        XWPFRun run = paragraph.createRun();
        File file = new File(imgFile);
        InputStream is = new FileInputStream(file);
        XWPFPicture picture = run.addPicture( is, XWPFDocument.PICTURE_TYPE_JPEG, imgFile, Units.toEMU( 25 ), Units.toEMU( 25 ) );
        String blipID = "";
        for( XWPFPictureData picturedata : header.getAllPackagePictures() ) { // 这段必须有，不然打开的logo图片不显示
            blipID = header.getRelationId( picturedata );
            picture.getCTPicture().getBlipFill().getBlip().setEmbed( blipID );
        }
        run.addTab();
        is.close();
        return doc;
    }


    public static XWPFDocument createHeaderRIGHT(XWPFDocument doc,String imgFile ) throws Exception {
        CTSectPr sectPr = doc.getDocument().getBody().addNewSectPr();
        XWPFHeaderFooterPolicy headerFooterPolicy = new XWPFHeaderFooterPolicy( doc, sectPr );
        XWPFHeader header = headerFooterPolicy.createHeader( headerFooterPolicy.DEFAULT );
        XWPFParagraph paragraph = header.createParagraph();
        paragraph.setAlignment( ParagraphAlignment.RIGHT );
        paragraph.setBorderBottom( Borders.THICK );
        XWPFRun run = paragraph.createRun();
        File file = new File( imgFile );
        InputStream is = new FileInputStream( file );
        XWPFPicture picture = run.addPicture( is, XWPFDocument.PICTURE_TYPE_JPEG, imgFile, Units.toEMU( 25 ), Units.toEMU( 25 ) );
        String blipID = "";
        for( XWPFPictureData picturedata : header.getAllPackagePictures() ) { // 这段必须有，不然打开的logo图片不显示
            blipID = header.getRelationId( picturedata );
            picture.getCTPicture().getBlipFill().getBlip().setEmbed( blipID );
        }
        run.addTab();
        is.close();
        return doc;
    }

    public static void refreshBooks(XWPFDocument doc, Map<String, InputStream> dataMap) throws IOException, InvalidFormatException {
        List<XWPFParagraph> paragraphs = doc.getParagraphs();
        for (XWPFParagraph xwpfParagraph : paragraphs) {
            CTP ctp = xwpfParagraph.getCTP();
            for (int dwI = 0; dwI < ctp.sizeOfBookmarkStartArray(); dwI++) {
                CTBookmark bookmark = ctp.getBookmarkStartArray(dwI);
                InputStream picIs = dataMap.get(bookmark.getName());
                if(picIs != null){
                    XWPFRun run = xwpfParagraph.createRun();
                    //bus.png为鼠标在word里选择图片时，图片显示的名字，400，400则为像素单元，根据实际需要的大小进行调整即可。
                    run.addPicture(picIs,XWPFDocument.PICTURE_TYPE_PNG,"bus.png,", Units.toEMU(25), Units.toEMU(25));
                }
            }
        }
    }


    //自定义方法来添加图片、文字页眉及页码
    public static void AddHeaderFooter(Section sec,String filePath1,String filePath2){
        //加载图片添加到页眉，并设置图片在段落中的对齐方式
        HeaderFooter header = sec.getHeadersFooters().getHeader();
        Paragraph hpara= header.addParagraph();
        DocPicture pico =hpara.appendPicture(filePath1);
        pico.setHorizontalAlignment(ShapeHorizontalAlignment.Left);
        pico.setVerticalOrigin(VerticalOrigin.Top_Margin_Area);
        pico.setVerticalAlignment(ShapeVerticalAlignment.Center);
        pico.setWidth(80);
        pico.setHeight(20);
        TextRange txt = hpara.appendText("                                                                  ");
        txt.getCharacterFormat().setUnderlineStyle(UnderlineStyle.None);
        txt.getCharacterFormat().setTextColor(Color.GRAY);
        txt.getCharacterFormat().setFontName("仿宋");
        txt.getCharacterFormat().setFontSize(12f);
        txt.getCharacterFormat().setBold(true);
        DocPicture pic =hpara.appendPicture(filePath2);
        pic.setHorizontalAlignment(ShapeHorizontalAlignment.Right);
        pic.setVerticalOrigin(VerticalOrigin.Top_Margin_Area);
        pic.setVerticalAlignment(ShapeVerticalAlignment.Center);
        pic.setWidth(40);
        pic.setHeight(40);
        hpara.getFormat().setHorizontalAlignment(HorizontalAlignment.Right);
        //添加文字到页眉，并设置字体、字号、字体加粗、对齐方式
        /*TextRange txt = hpara.appendText("青年时报");
        txt.getCharacterFormat().setUnderlineStyle(UnderlineStyle.None);
        txt.getCharacterFormat().setTextColor(Color.GRAY);
        txt.getCharacterFormat().setFontName("仿宋");
        txt.getCharacterFormat().setFontSize(12f);
        txt.getCharacterFormat().setBold(true);*/
        //hpara.getFormat().setHorizontalAlignment(HorizontalAlignment.Right);
        //设置图片的文本环绕方式、页眉底部边线（粗细、间距）
        /*pic.setTextWrappingStyle(TextWrappingStyle.Behind);
        hpara.getFormat().getBorders().getBottom().setBorderType(BorderStyle.Single);
        hpara.getFormat().getBorders().getBottom().setLineWidth(0.5f);
        hpara.getFormat().getBorders().setSpace(2f);*/

        //添加页码到页脚，并设置页脚对齐方式，顶部边线粗细、间距
       /* HeaderFooter footer = sec.getHeadersFooters().getFooter();
        Paragraph fpara= footer.addParagraph();
        fpara.appendField("页码",FieldType.Field_Page);
        fpara.appendText("/");
        fpara.appendField("总页数",FieldType.Field_Num_Pages);
        fpara.getFormat().setHorizontalAlignment(HorizontalAlignment.Center);
        fpara.getFormat().getBorders().getTop().setBorderType(BorderStyle.Single);
        fpara.getFormat().getBorders().getTop().setLineWidth(1f);
        fpara.getFormat().getBorders().getTop().setSpace(2f);*/
    }



    /**
     * 生成二维码 并 保存为图片
     */
    /**
     * @param codeContent        :二维码参数内容，如果是一个网页地址，如 https://www.baidu.com/ 则 微信扫一扫会直接进入此地址
     *                           如果是一些参数，如 1541656080837，则微信扫一扫会直接回显这些参数值
     * @param codeImgFileSaveDir :二维码图片保存的目录,如 D:/codes
     * @param fileName           :二维码图片文件名称，带格式,如 123.png
     */
    public static void createCodeToFile(String codeContent, File codeImgFileSaveDir, String fileName) {
        try {
            /** 参数检验*/
            if (codeContent == null || "".equals(codeContent)) {
                System.out.println("二维码内容为空，不进行操作...");
                return;
            }
            codeContent = codeContent.trim();
            if (codeImgFileSaveDir == null || codeImgFileSaveDir.isFile()) {
                codeImgFileSaveDir = FileSystemView.getFileSystemView().getHomeDirectory();
                System.out.println("二维码图片存在目录为空，默认放在桌面...");
            }
            if (!codeImgFileSaveDir.exists()) {
                codeImgFileSaveDir.mkdirs();
                System.out.println("二维码图片存在目录不存在，开始创建...");
            }
            if (fileName == null || "".equals(fileName)) {
                fileName = new Date().getTime() + ".png";
                System.out.println("二维码图片文件名为空，随机生成 png 格式图片...");
            }
            /**com.google.zxing.EncodeHintType：编码提示类型,枚举类型
             * EncodeHintType.CHARACTER_SET：设置字符编码类型
             * EncodeHintType.ERROR_CORRECTION：设置误差校正
             *      ErrorCorrectionLevel：误差校正等级，L = ~7% correction、M = ~15% correction、Q = ~25% correction、H = ~30% correction
             *      不设置时，默认为 L 等级，等级不一样，生成的图案不同，但扫描的结果是一样的
             * EncodeHintType.MARGIN：设置二维码边距，单位像素，值越小，二维码距离四周越近
             * */
            Map<EncodeHintType, Object> hints = new HashMap();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.ERROR_CORRECTION, ErrorCorrectionLevel.M);
            hints.put(EncodeHintType.MARGIN, 1);

            /**
             * MultiFormatWriter:多格式写入，这是一个工厂类，里面重载了两个 encode 方法，用于写入条形码或二维码
             *      encode(String contents,BarcodeFormat format,int width, int height,Map<EncodeHintType,?> hints)
             *      contents:条形码/二维码内容
             *      format：编码类型，如 条形码，二维码 等
             *      width：码的宽度
             *      height：码的高度
             *      hints：码内容的编码类型
             * BarcodeFormat：枚举该程序包已知的条形码格式，即创建何种码，如 1 维的条形码，2 维的二维码 等
             * BitMatrix：位(比特)矩阵或叫2D矩阵，也就是需要的二维码
             */
            MultiFormatWriter multiFormatWriter = new MultiFormatWriter();
            BitMatrix bitMatrix = multiFormatWriter.encode(codeContent, BarcodeFormat.QR_CODE, CODE_WIDTH, CODE_HEIGHT, hints);

            /**java.awt.image.BufferedImage：具有图像数据的可访问缓冲图像，实现了 RenderedImage 接口
             * BitMatrix 的 get(int x, int y) 获取比特矩阵内容，指定位置有值，则返回true，将其设置为前景色，否则设置为背景色
             * BufferedImage 的 setRGB(int x, int y, int rgb) 方法设置图像像素
             *      x：像素位置的横坐标，即列
             *      y：像素位置的纵坐标，即行
             *      rgb：像素的值，采用 16 进制,如 0xFFFFFF 白色
             */
            BufferedImage bufferedImage = new BufferedImage(CODE_WIDTH, CODE_HEIGHT, BufferedImage.TYPE_INT_BGR);
            for (int x = 0; x < CODE_WIDTH; x++) {
                for (int y = 0; y < CODE_HEIGHT; y++) {
                    bufferedImage.setRGB(x, y, bitMatrix.get(x, y) ? FRONT_COLOR : BACKGROUND_COLOR);
                }
            }

            /**javax.imageio.ImageIO java 扩展的图像IO
             * write(RenderedImage im,String formatName,File output)
             *      im：待写入的图像
             *      formatName：图像写入的格式
             *      output：写入的图像文件，文件不存在时会自动创建
             *
             * 即将保存的二维码图片文件*/
            File codeImgFile = new File(codeImgFileSaveDir, fileName);
            ImageIO.write(bufferedImage, "png", codeImgFile);

            System.out.println("二维码图片生成成功：" + codeImgFile.getPath());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static void main(String[] args) {
        /*Document doc = new Document();
        doc.loadFromFile("F:\\FTPTWO\\NEWCONTR\\ECSEE\\1634611700368\\1634611700369.docx");
        //保存为PDF格式的文件
        doc.saveToFile("F:\\FTPTWO\\NEWCONTR\\ECSEE\\1634611700368\\Word转PDF.pdf", FileFormat.PDF);
        doc.close();*/

        /*String codeContent1 = "电子合同编码：【雅安分公司-宝兴分公司】2022031500001";
        createCodeToFile(codeContent1, null, null);*/
        /*Document doc = new Document();
        doc.loadFromFile("F:\\hfsywb.docx");
        Float width=doc.getSections().get(0).getPageSetup().getClientWidth();*/
        FileInputStream in = null;
        try {
            in = new FileInputStream( "F:/hfsywb2.docx" );
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        }
        OPCPackage open = null;
        try {
            open = OPCPackage.open( in );
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        XWPFDocument doc = null;
        try {
            doc = new XWPFDocument(open);
        } catch (IOException e) {
            e.printStackTrace();
        }
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
            String dateString = formatter.format(new Date());
            /*Map<String,InputStream> dataMap = new HashMap<>();
            dataMap.put("imge",new FileInputStream("F:/1648016297775.png"));
            refreshBooks(doc,dataMap);
            doc.write(new FileOutputStream("F:/"+dateString+".docx"));*/

            //加载需要添加页眉页脚的文档
            Document document= new Document("F:/hfsywb.docx");
            Section sec = document.getSections().get(0);
            //调用方法添加页眉页脚
            AddHeaderFooter(sec,"/images/mobileSign.png","");
            //AddHeaderFooter(sec,"F:/4231432143214.png",70,25);
            //保存文档
            document.saveToFile("F:/"+dateString+".docx");
            document.close();
        } catch (Exception e) {
            e.printStackTrace();
        }

        //加载Word文档
        /*Document doc = new Document();
        doc.loadFromFile("F:\\FTPTWO\\NEWCONTR\\ECSEE\\1634611700368\\1634611700368.docx");
        //获取section
        Section section = doc.getSections().get(0);
        //获取表格
        Table table = section.getTables().get(0);
        //设置表格列宽适应内容
        //table.autoFit(AutoFitBehaviorType.Auto_Fit_To_Contents);
        //设置表格列宽适应窗体
        table.autoFit(AutoFitBehaviorType.Auto_Fit_To_Window);
        //设置表格固定列宽
        //table.autoFit(AutoFitBehaviorType.Fixed_Column_Widths);
        //保存文档
        doc.saveToFile("F:\\FTPTWO\\NEWCONTR\\ECSEE\\1634611700368\\1634611700369.docx",FileFormat.Docx_2013);
        doc.dispose();*/
    }

}
