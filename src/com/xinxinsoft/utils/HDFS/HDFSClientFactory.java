package com.xinxinsoft.utils.HDFS;

import com.xinxinsoft.utils.HDFS.utils.ProertiesUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.util.ResourceBundle;
import java.util.concurrent.TimeUnit;

/**
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.HDFS.HDFSClientFactory
 * @description:  读取HDFS 配置文件
 * @author: WF
 * @date: 2020-04-11 11:27
 **/
public class HDFSClientFactory {

    private static Logger logger = LoggerFactory.getLogger(HDFSClientFactory.class);

//    public final static String classPathFileName = "com.xinxinsoft.config.hadoopConfig.";
    public final static String classPathFileName = "";
    public final static String fileName = "hdfs_config";//properties

    private static ResourceBundle resource = null;

    public static HDFSClient getHDFSClient() throws IOException, InterruptedException {
        resource = getResource();
        if(resource==null){
            throw new NullPointerException("无法读取配置文件：hdfs_config.properties!");
        }
        HDFSClient client = null;
        HDFSClientPool pool = HDFSClientPool.getInstance();
        client = pool.get("HDFSClient");
        if (client != null) {
            return client;
        }else{
            client = new HDFSClient(resource);
            pool.put("HDFSClient", client);
        }
        return client;
    }


    public static ResourceBundle getResource(){

        return Singleton.INSTANCE.getInstance();
    }

    //获取缓存配置文件
    private enum  Singleton{
        INSTANCE;
        private ResourceBundle resource;
        //jvm保证方法只被调用一次
        Singleton(){
            try {
                TimeUnit.SECONDS.sleep(1);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
            resource =  ProertiesUtil.getBundle(ProertiesUtil.VM_CONFIG_PATH, classPathFileName, fileName);
        }

        public ResourceBundle getInstance(){
            return  resource;
        }
    }

}
