package com.xinxinsoft.utils.HDFS.utils;

import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.HDFS.HDFSClientFactory;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.fs.*;
import org.apache.hadoop.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.util.*;

/**
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.HDFS.utils.HDFSUtils
 * @description: HDFS操作类
 * @author: WF
 * @date: 2020-04-11 14:09
 **/
public class HDFSUtils {

   private static Logger logger = LoggerFactory.getLogger(HDFSUtils.class);
    /**
     * 存放在HDFS文件的目录名称
     * hdfs://cluster1/euicc/file/文件名
     */
    private final static String HDFS_FILE_PATH = "dfs.hdfs_file_path";
    private final static String DEFAULT_FS = "fs.defaultFS";
    public static final String CHARSET_STRING = "UTF-8";
    private final  static String BACK_SEPARATOR = "/";


    /**
     * 判断HDFS上是否存在某文件夹或文件
     * @param path
     * @return  Boolean
     * @throws IOException
     */
    public static Boolean isExistsFile(String path) throws IOException, InterruptedException {
        FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
        return  fileSys.exists(new Path(path));
    }


    /**
     *  创建HDFS文件夹
     * @param path  目录路径，如/eom/test
     * @return  Boolean
     * @throws IOException
     */
    public static Boolean mkdirsFile(String path ) throws IOException, InterruptedException {
        FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
        Path pathIn = new Path(path);
        if(!fileSys.exists(pathIn)){
            return fileSys.mkdirs(pathIn);
        }else{
            return false;
        }
    }
    /**
     * 将字符串写入HDFS文件中
     * @param pathString
     * @param content
     */
    public static void createFile(String pathString,String content) throws IOException, InterruptedException {
        FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
        //创建一个指向HDFS目标文件的输出流
        InputStream is = new ByteArrayInputStream(content.getBytes(CHARSET_STRING));
        OutputStream out = fileSys.create(new Path(pathString),false);//不覆盖同名文件，如果已存在同名文件，会抛出异常
        //用IOUtils工具将文件从本地文件系统复制到HDFS目标文件中
        IOUtils.copyBytes(is, out, 4096,true);
    }

    /**
     * 将字符串追加到HDFS文件中
     * @param pathString
     * @param content
     * @throws IOException
     */
    public static void appendFile(String pathString,String content) throws IOException{
        try {
            FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
            //创建一个指向HDFS目标文件的输出流
            InputStream is = new ByteArrayInputStream(content.getBytes(CHARSET_STRING));
            OutputStream out = null;
            if(fileSys.exists(new Path(pathString))){//判断是否存在该文件
                out = fileSys.append(new Path(pathString));//追加到改文件
            }else{
                out = fileSys.create(new Path(pathString));//新增一个文件
            }
            //用IOUtils工具将文件从本地文件系统复制到HDFS目标文件中
            IOUtils.copyBytes(is, out, 4096,true);
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
        }
    }

    /**
     * 读取文件大小
     * @param pathString
     * @return
     * @throws IOException
     */
    public static long readFileSize(String pathString) throws IOException, InterruptedException {
        FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
        FileStatus stat = fileSys.getFileStatus(new Path(pathString));
        return stat.getLen();
    }

    /**
     * 读取HDFS文件
     * @param pathString
     * @return String
     */
    public static byte[] readFileToByte(String pathString) throws IOException, InterruptedException {
        byte[] fileByte = null;
        FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
        FSDataInputStream in = fileSys.open(new Path(pathString));
        int size = (int)readFileSize(pathString);
        ByteArrayOutputStream bis = null;
        try {
            bis = new ByteArrayOutputStream(size);
            byte[] buff = new byte[size];
            int count = 0;
            while((count = in.read(buff)) >0){
                bis.write(buff, 0, count);
            }
            fileByte = bis.toByteArray();
        } finally{
            if(bis!=null){
                bis.close();
            }
            IOUtils.closeStream(in);
        }
        return fileByte;
    }

    /**
     * 读取HDFS文件
     * @param pathString
     * @return String
     */
    public static String readFileToString(String pathString) throws IOException, InterruptedException {
        Long start = System.currentTimeMillis();
        StringBuilder sb = new StringBuilder();
        FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
        FSDataInputStream in = fileSys.open(new Path(pathString));
        BufferedReader reader = new BufferedReader(new InputStreamReader(in,CHARSET_STRING));
        String line = null;
        try {
            while ((line = reader.readLine()) != null) {
                sb.append(line + "\n");
            }
        } finally {
            if(reader !=null){
                reader.close();
            }
            IOUtils.closeStream(in);
        }
        logger.info("读取文件耗时:"+(System.currentTimeMillis()-start));
        return sb.toString();
    }

    /**
     * 模糊查询某目录下包含某个字符串的文件
     * @param pathString 目录路径
     * @param targetStr  目标字符串
     * @return
     * @throws IOException
     */
    public static List<Map<String,Object>> searchFile(String pathString, String targetStr) throws IOException, InterruptedException {
        List<Map<String,Object>> fileList = new ArrayList<Map<String,Object>>();
        if(!isExistsFile(pathString)){
            return fileList;
        }
        FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
        FileStatus[] list = fileSys.listStatus(new Path(pathString));//获取某目录下的所有文件
        for (FileStatus file : list) {
            Path path = file.getPath();
            //读取文件内容
            FSDataInputStream in = fileSys.open(path);
            BufferedReader reader = new BufferedReader(new InputStreamReader(in,CHARSET_STRING));
            String line = null;
            try {
                while ((line = reader.readLine()) != null) {
                    if(line.contains(targetStr)){//循环读取文件内容，判断文件是否存在targerStr
                        Map<String,Object> fileMap = new HashMap<String,Object>();
                        fileMap.put("file_name", path.getName());
                        fileMap.put("file_path", path.toUri().getPath());
                        FileStatus stat = fileSys.getFileStatus(path);//获取文件大小
                        fileMap.put("file_size", stat.getLen());//文件大小单位  b
                        fileList.add(fileMap);
                        break;
                    }
                }
            } finally {
                if(reader !=null){
                    reader.close();
                }
                IOUtils.closeStream(in);
            }
        }
        return fileList;
    }

    /**
     * 列出某目录下所有文件
     * @param pathString 文件路径
     * @return
     * @throws IOException
     */
    public static Map<String,Object> searchFile(String pathString) throws IOException, InterruptedException {
        if(isExistsFile(pathString)){
            Path path = new Path(pathString);
            Map<String,Object> fileMap = new HashMap<String,Object>();
            fileMap.put("file_name", path.getName());
            fileMap.put("file_path", path.toUri().getPath());
            FileSystem fileSys = HDFSClientFactory.getHDFSClient().getFileSys();
            FileStatus stat = fileSys.getFileStatus(path);//获取文件大小
            fileMap.put("file_size", stat.getLen());//文件大小单位  b
            return fileMap;
        }else{
            return null;
        }
    }

    /**
     * 上传本地文件到服务器
     * @param filePath
     * @param file_name
     * @param file_buff
     * @throws IOException
     */
    public static Map<String ,String> uploadFile(byte[] file_buff, String file_name, String filePath) throws IOException, InterruptedException {
        String hdfs_file_path = HDFSClientFactory.getResource().getString(HDFS_FILE_PATH);
        filePath = StringUtils.isEmpty(filePath) ? hdfs_file_path+ "userFile" : hdfs_file_path+filePath;
        Long start = System.currentTimeMillis();
        filePath = filePath +  BACK_SEPARATOR + DateUtil.convertDateToString(new Date(),"yyyyMM") + BACK_SEPARATOR;
        String suffix = "";
        if (file_name.length() > 2 && file_name.indexOf(".") > -1) {
            suffix = file_name.substring(file_name.indexOf("."), file_name.length());
        }
        FileSystem fs = HDFSClientFactory.getHDFSClient().getFileSys();
        Path catalogPath = new Path(filePath);
        //HDFS服务器路径为 /euicc/upload/年月/，判断是否有此目录
        if(!fs.exists(catalogPath)){
            //创建目录
            fs.mkdirs(catalogPath);
        }
        //生成文件名
        String fileName = System.currentTimeMillis() + suffix;
        String pathName = filePath + fileName;
        logger.info("文件准备上传："+pathName);
        Path path = new Path(pathName);
        //创建一个指向HDFS目标文件的输出流
        InputStream is = new ByteArrayInputStream(file_buff);
        //不覆盖同名文件，如果已存在同名文件，会抛出异常
        OutputStream out = null;
        if(!fs.exists(path)){
            out = fs.create(path, false);
        }
        else{
            out = fs.append(path);
        }
        //用IOUtils工具将文件从本地文件系统复制到HDFS目标文件中
        IOUtils.copyBytes(is, out, 4096, true);
        long end = System.currentTimeMillis() - start;
        logger.info("文件上传成功，用时： " + end);
        Map<String ,String> resultMap = new HashMap<>();
        resultMap.put("file_path",HDFSClientFactory.getResource().getString(DEFAULT_FS) + filePath + fileName);
        resultMap.put("file_name",fileName);
        return resultMap;
    }

    /**
     * 上传本地文件到服务器
     * @param file_buff
     * @param file_name
     * @throws IOException
     */
    public static Map<String ,String> uploadFile(byte[] file_buff, String file_name) throws IOException, InterruptedException {
        return uploadFile(file_buff, file_name, null);
    }
    /**
     * 上传本地文件到服务器
     * @param is)
     * @throws IOException
     */
    public static Map<String ,String> uploadFile(InputStream is) throws IOException, InterruptedException {

        String hdfs_file_path = HDFSClientFactory.getResource().getString(HDFS_FILE_PATH);
        Long start = System.currentTimeMillis();
        String filePath= hdfs_file_path  + DateUtil.convertDateToString(new Date(),"yyyyMM") + BACK_SEPARATOR;
        String suffix = "";
        FileSystem fs = HDFSClientFactory.getHDFSClient().getFileSys();

        Path catalogPath = new Path(filePath);
        //HDFS服务器路径为 //年月/，判断是否有此目录
        if(!fs.exists(catalogPath)){
            //创建目录
            fs.mkdirs(catalogPath);
        }
        //生成文件名
        String fileName = System.currentTimeMillis()+ suffix;
        String pathName = filePath + fileName;
        logger.info("文件准备上传："+pathName);
        Path path = new Path(pathName);
        //不覆盖同名文件，如果已存在同名文件，会抛出异常
        OutputStream out = null;
        if(!fs.exists(path)){
            out = fs.create(path, false);
        }
        else{
            out = fs.append(path);
        }
        //用IOUtils工具将文件从本地文件系统复制到HDFS目标文件中
        IOUtils.copyBytes(is, out, 4096, true);
        long end = System.currentTimeMillis() - start;
        logger.info("文件上传成功，用时： " + end);
        Map<String ,String> resultMap = new HashMap<>();
        resultMap.put("file_path",pathName);
        resultMap.put("file_name",fileName);
        return resultMap;
    }

    /**
     * 以流形式上传本地文件到分布式文件系统中
     * @param inputDir 本地文件夹
     * @param hadoopFileName 文件上传到hadoop 上的文件名字
     * @throws IOException
     */
    public static Map<String ,String> readLocalFile2Hadoop(String inputDir,String hadoopFileName) throws IOException, InterruptedException {

        FileSystem fileSystem = HDFSClientFactory.getHDFSClient().getFileSys();

        String hdfs_file_path = HDFSClientFactory.getResource().getString(HDFS_FILE_PATH);
        Long start = System.currentTimeMillis();
        String filePath= hdfs_file_path  + DateUtil.convertDateToString(new Date(),"yyyyMM") + BACK_SEPARATOR;
        String suffix = "";
        if (hadoopFileName.length() > 2 && hadoopFileName.indexOf(".") > -1) {
            suffix = hadoopFileName.substring(hadoopFileName.indexOf("."), hadoopFileName.length());
        }
        Path path = new Path(HDFSClientFactory.getResource().getString(HDFS_FILE_PATH));

        LocalFileSystem localFS = FileSystem.getLocal(fileSystem.getConf());

        //路径在hadoop 上不存在就创建
        if(fileSystem.exists(path)) {
            fileSystem.mkdirs(path);
        }
        //生成 系统文件名
        String fileName = start+ suffix;
        String pathName = filePath + fileName;
        logger.info("文件准备上传："+pathName);
        FileStatus[] inputFiles =  localFS.listStatus(new Path(inputDir));

        FSDataOutputStream out ;
        FSDataInputStream in;
        for (int i = 0 ; i < inputFiles.length ; i++) {
            in = localFS.open(inputFiles[i].getPath());
            out = fileSystem.create(new Path(pathName));//HDFSClientFactory.getResource().getString(HDFS_FILE_PATH)+
            byte[]  buffer = new byte[256];
            int byteRead = 0 ;
            while ((byteRead = in.read(buffer)) > 0) {
                out.write(buffer, 0, byteRead);
            }
            out.close();
            in.close();
        }
        Map<String ,String> resultMap = new HashMap<>();
        resultMap.put("file_path",pathName);
        resultMap.put("file_name",fileName);
        return resultMap;
    }
    /**
     * 删除文件或文件夹
     * @param filePath
     * @throws Exception
     */
    public static void delFileToHDFS(String filePath) throws Exception {
        if(StringUtils.isEmpty(filePath)){
            throw new Exception("file path can not be null");
        }
        FileSystem fs = null;
        try {
            fs = HDFSClientFactory.getHDFSClient().getFileSys();
            fs.deleteOnExit(new Path(filePath));
            logger.info("文件删除成功：" + filePath);
        } catch (Exception e) {
            e.printStackTrace();
        } finally{
//            if(fs != null){
//                fs.close();
//            }
        }
    }


    /**
     *<AUTHOR>
     *@Description:
     *@Date 2:58 PM 2/21/19
     *@Param src:本地文件路径
     *@Param dst:hdfs路径
     *@return void
     */
    public static Map<String ,String> Local2Hdfs(String src) throws IOException, InterruptedException {
        Long start = System.currentTimeMillis();
            FileSystem fs = HDFSClientFactory.getHDFSClient().getFileSys();
        String   dst = HDFSClientFactory.getResource().getString(HDFS_FILE_PATH)+DateUtil.convertDateToString(new Date(),"yyyyMM") + BACK_SEPARATOR;
        String suffix = null;
        if (src.length() > 2 && src.indexOf(".") > -1) {
            suffix = src.substring(src.indexOf("."), src.length());
        }

        String fileSystemName = start+suffix;
        dst = dst + fileSystemName;
            Path srcPath = new Path(src);
            Path dstPath = new Path(dst);
            logger.info("本地文件路径src ->"+src);
            logger.info("上传到hdfs的文件路径dst->"+dst);
            fs.copyFromLocalFile(true, true, srcPath, dstPath);
        logger.info("上传耗时：" + (System.currentTimeMillis() - start));
        Map<String ,String> resultMap = new HashMap<>();
        resultMap.put("file_path",dst);
        resultMap.put("file_name",fileSystemName);
        return resultMap;
    }
    //下载
    public static void uploadHDFS(String dest,String local) throws IOException, InterruptedException {
        FileSystem fs = HDFSClientFactory.getHDFSClient().getFileSys();
        FSDataInputStream fsdi = fs.open(new Path(dest));
        OutputStream output = new FileOutputStream(local);
        IOUtils.copyBytes(fsdi,output,4096,false);
    }

    public static void main(String[] args) throws Exception {
//        FileSystem fs = HDFSClientFactory.getHDFSClient().getFileSys();
/*        System.out.println(File.separator);
        System.out.println(File.separatorChar);
        System.out.println(File.pathSeparatorChar);*/

       //delFileToHDFS("/data/eomFile/202004");
///WEB-INF/hadoopConfig/hdfs_config
            HDFSUtils.isExistsFile("/data/eomFile/");
        //delFileToHDFS("hdfs://cluster1/euicc/upload/201608/e082c61de0464465975a0df2f60d91e4.log");

//        byte[] file = readFileToByte("/euicc/upload/201608/e082c61de0464465975a0df2f60d91e4.log");

//        String content = readFileToString("hdfs://cluster1/euicc/upload/201608/51af6e4c4deb43feac36a0f1663364f8.log");
//        String FILE_PATH = ServletActionContext.getServletContext().getRealPath("/WEB-INF/hadoopConfig") + File.separator;
//
//        System.out.println(Thread.currentThread().getContextClassLoader().getResource(""));
//        System.out.println(System.getProperty("user.dir"));  //这个是去工程的绝对路径的
//        Properties prop = new Properties();
//        try {
//            prop.load(new FileInputStream(System.getProperty("user.dir")+"/EOM/WebRoot/WEB-INF/hadoopConfig/hdfs_config.properties"));
//        } catch (Exception e) {
//            System.out.println( "file " + "jwpay_system_version.properties" + " not found!\n" + e);
//        }
//        System.out.println(prop.getProperty("fs.defaultFS"));
//        ResourceBundle hdfs_config = ResourceBundle.getBundle("hdfs_config");
//        System.out.println(hdfs_config.getString("fs.defaultFS"));
//        //System.out.println(hdfs_config.getString("org.hibernate.level"));
//        System.out.println("************"+hdfs_config);
    }

}
