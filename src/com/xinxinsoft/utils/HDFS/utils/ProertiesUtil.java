package com.xinxinsoft.utils.HDFS.utils;

import org.apache.commons.lang.StringUtils;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.Properties;
import java.util.ResourceBundle;

/**
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.HDFS.utils.HDFSProertiesUtil
 * @description: 获取 后缀名为.properties 的配置文件
 * @author: WF
 * @date: 2020-04-11 11:25
 **/
public class ProertiesUtil {

    public static  final  String VM_CONFIG_PATH ="";

    public static String getString(ResourceBundle resource, String s) {
        return resource.getString(s);
    }

    public static ResourceBundle getBundle(String s, String classPathFileName, String fileName) {
        if(!StringUtils.isBlank(classPathFileName)){
            return ResourceBundle.getBundle(classPathFileName + "." +fileName);
        }
        return ResourceBundle.getBundle(fileName);
    }

    /**
     * 动态获取配置
     * @param fileName
     * @return
     */
    public static Properties dynamicBundle(String fileName){
        Properties p = new Properties();
        try {
            //非实时动态获取  I:\word\src\web\EOM\src\test.properties
            //p.load(new InputStreamReader(this.class.getClassLoader().getResourceAsStream(filePath), "UTF-8"));
            //下面为动态获取
            String path = Thread.currentThread().getContextClassLoader().getResource(fileName).getPath();
            InputStream is = new FileInputStream(path);//+ File.separator+ "test.properties"
            p.load(is);
            return p;
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }
}
