package com.xinxinsoft.utils.HDFS;

import com.xinxinsoft.utils.HDFS.utils.ProertiesUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.hadoop.conf.Configuration;
import org.apache.hadoop.fs.FileSystem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.ResourceBundle;

/**
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.HDFS.HDFSClient
 *          ///////////////////////////////////////////////////////////////////////////////////
 *         ///////用get不能close,否则多线程报错(所以我用static)，而用newInstance必须每次close.//////
 *         //////////////////////////////////////////////////////////////////////////////////
 * @description: JAVA客户端连接HDFS服务器
 * @author: WF
 * @date: 2020-04-11 11:20
 **/
public class HDFSClient {

    private static Logger logger = LoggerFactory.getLogger(HDFSClient.class);

    private FileSystem fileSys ;

    public FileSystem getFileSys() {
        return fileSys;
    }

    public void setFileSys(FileSystem fileSys) {
        this.fileSys = fileSys;
    }

    public HDFSClient(ResourceBundle resource) throws IOException, InterruptedException {
        Configuration conf = new Configuration();
        // 客户端连接HDFS时，默认的路径前缀，对应core-site.xml中的fs.defaultFS属性值
        String defaultFS =  ProertiesUtil.getString(resource, "fs.defaultFS");
        conf.set("fs.defaultFS", defaultFS);

        // hadoop集群名称，对应hdfs-site.xml中的dfs.nameservices属性值
        String nameservices =  ProertiesUtil.getString(resource,"dfs.nameservices");
        conf.set("dfs.nameservices", nameservices);
        // namenodes名称，对应hdfs-site.xml中的dfs.ha.namenodes.[nameservice ID]属性值
        String namenodes =  ProertiesUtil.getString(resource, "dfs.namenodes");
        if (StringUtils.isBlank(namenodes)) {
            throw new NullPointerException("the 'dfs.namenodes' value is empty in hdfs_config.properties!");
        }
        conf.set("dfs.ha.namenodes." + nameservices, namenodes);
        String[] nns = namenodes.split(",");
        // namenode地址，地址与namenodes一一对应，有两个namenode就要配置两个address
        // 分别对应hdfs-site.xml中的dfs.namenode.rpc-address.[nameservice ID].[namenode ID]
        String rpcAddress = ProertiesUtil.getString(resource,"dfs.namenode.rpc-address");
        if (StringUtils.isBlank(namenodes)) {
            throw new NullPointerException("the 'dfs.namenode.rpc-address' value is empty in hdfs_config.properties!");
        }
        String[] ras = rpcAddress.split(",");
        if (ras.length != nns.length) {//配置dfs.namenodes的数量要与dfs.namenode.rpc-address的数量要一致
            throw new RuntimeException("'dfs.namenodes' 与 'dfs.namenode.rpc-address' 配置的数量不一致!");
        }
        int i = 0;
        for (String namenode : nns) {
            conf.set("dfs.namenode.rpc-address." + nameservices + "." + namenode, ras[i]);
            i++;
        }

        // HDFS客户端连接到Active
        // NameNode的一个java类，对应hdfs-site.xml中的dfs.client.failover.proxy.provider.[nameservice ID]
        String providerClass = ProertiesUtil.getString(resource,"dfs.client.failover.proxy.provider");
        if (StringUtils.isBlank(providerClass)) {
            providerClass = "org.apache.hadoop.hdfs.server.namenode.ha.ConfiguredFailoverProxyProvider";
        }
        conf.set("dfs.client.failover.proxy.provider." + nameservices,providerClass);
        conf.set("dfs.client.socket-timeout", "300000");
//        conf.setBoolean("fs.hdfs.impl.disable.cache", true);
        Long start = System.currentTimeMillis();

         fileSys = FileSystem.get(URI.create("/"), conf, ProertiesUtil.getString(resource, "user.name"));
//        fileSys = FileSystem.newInstance(URI.create("/"), conf,ProertiesUtil.getString(resource, "user.name"));
         logger.info("客户端连接HDFS耗时：" + (System.currentTimeMillis() - start));
    }

}
