package com.xinxinsoft.utils.HDFS;

import java.util.concurrent.ConcurrentHashMap;

/**
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.HDFS.HDFSClientPool
 * @description:  HDFS 连接线程池
 * @author: WF
 * @date: 2020-04-11 14:01
 **/
public class HDFSClientPool {

    private ConcurrentHashMap<String,HDFSClient> poolMap = null;
    //volatile + 双重检测机制 -> 禁止指令重排序
    private static  HDFSClientPool hdfsPool = null;

    private HDFSClientPool(){
        this.poolMap = new ConcurrentHashMap<>();
    }
    /**
     * synchronized导致性能开销增加
     */
    public static HDFSClientPool getInstance(){
        if( null == hdfsPool){//双重检测机制
            synchronized(HDFSClientPool.class){ //同步锁
                if(null == hdfsPool){
                    hdfsPool = new HDFSClientPool();
                }
            }
        }
        return hdfsPool;
    }

    public void put(String key, HDFSClient client) {
        this.poolMap.put(key, client);
    }

    public HDFSClient get(String key) {
        return this.poolMap.get(key);
    }
}
