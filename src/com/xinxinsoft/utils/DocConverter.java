package com.xinxinsoft.utils;  
import java.awt.Color;
import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;

import com.artofsolving.jodconverter.DocumentConverter;
import com.artofsolving.jodconverter.openoffice.connection.OpenOfficeConnection;
import com.artofsolving.jodconverter.openoffice.connection.SocketOpenOfficeConnection;
import com.artofsolving.jodconverter.openoffice.converter.OpenOfficeDocumentConverter;
import com.lowagie.text.Document;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfCopy;
import com.lowagie.text.pdf.PdfCopy.PageStamp;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfReader;
  
/** 
 * doc docx格式转换 
 */  
public class DocConverter {  
    private static final int environment = 2;// 环境 1：windows 2:linux
    private String fileString;// (只涉及pdf2swf路径问题)  
    private String outputPath = "";// 输入路径 ，如果不设置就输出在默认的位置  
    private String fileName;  
    private File pdfFile;  
    private File swfFile;  
    private File docFile;
    private File newFile;
    private String waterString;
    public DocConverter(String fileString,String waterString) {  
        init(fileString,waterString);  
    }
    public DocConverter(String fileString,String waterString,int type) {
        init(fileString,waterString,type);
    }

    /** 
     * 重新设置file 
     *  
     * @param fileString 
     */  
    public void setFile(String fileString,String waterString) {  
        init(fileString,waterString);  
    }  
  
    /** 
     * 初始化 
     *  
     * @param fileString 
     */  
    private void init(String fileString,String waterString) {  
        this.fileString = fileString;  
        this.waterString = waterString;
        if("".equals(fileString) || fileString == null){
        	return;
        }
        fileName = fileString.substring(0, fileString.lastIndexOf("."));  
        docFile = new File(fileString);  
        pdfFile = new File(fileName + ".pdf");  
        newFile = new File(fileName + "(1)" + ".pdf");
        swfFile = new File(fileName + ".swf");
    }

    /**
     * 初始化
     *
     * @param fileString
     */
    private void init(String fileString,String waterString,int type) {
        this.fileString = fileString;
        this.waterString = waterString;
        if("".equals(fileString) || fileString == null){
            return;
        }
        fileName = fileString.substring(0, fileString.lastIndexOf("."));
        docFile = new File(fileString);
        pdfFile = new File(fileName + ".pdf");
        newFile = new File(fileName + "(1)" + ".pdf");
        swfFile = new File(fileName + ".swf");
    }
    /** 
     * 转为PDF 
     *  
     * @param file 
     */  
    private void doc2pdf() throws Exception {  
        if (docFile.exists()) {  
            if (!pdfFile.exists()) {  
                OpenOfficeConnection connection = new SocketOpenOfficeConnection(8100);  
                try {  
                    connection.connect();  
                    DocumentConverter converter = new OpenOfficeDocumentConverter(connection);  
                    converter.convert(docFile, pdfFile);  
                     //close the connection  
                    connection.disconnect();  
                    System.out.println("****pdf转换成功，PDF输出：" + pdfFile.getPath()+ "****");  
                } catch (java.net.ConnectException e) {  
                    e.printStackTrace();  
                    System.out.println("****swf转换器异常，openoffice服务未启动！****");  
                    throw e;  
                } catch (com.artofsolving.jodconverter.openoffice.connection.OpenOfficeException e) {  
                    e.printStackTrace();  
                    System.out.println("****swf转换器异常，读取转换文件失败****");  
                    throw e;  
                } catch (Exception e) {  
                    e.printStackTrace();  
                    throw e;  
                }  
            } else {  
                System.out.println("****已经转换为pdf，不需要再进行转化****");  
            }  
        } else {  
            //System.out.println("****swf转换器异常，需要转换的文档不存在，无法转换****");
        }  
    }  
      
    /** 
     * 转换成 swf 
     */  
    @SuppressWarnings("unused")  
    private void pdf2swf() throws Exception {  
        Runtime r = Runtime.getRuntime();  
        if (!swfFile.exists()) {  
            if (newFile.exists()) {
                if (environment == 1) {// windows环境处理  
                    try {
                        //Process p = r.exec("G:/Program Files (x86)/SWFTools/pdf2swf.exe "+ newFile.getPath() + " -o "+ swfFile.getPath() + " -T 9");
                    	Process p = r.exec("D:/OpenOffice 4/SWFTools/pdf2swf.exe "+ newFile.getPath() + " -o "+ swfFile.getPath() + " -T 9");
                        System.out.print(loadStream(p.getInputStream()));  
                        System.err.print(loadStream(p.getErrorStream()));  
                        System.out.print(loadStream(p.getInputStream()));  
                        System.err.println("****swf转换成功，文件输出："  
                                + swfFile.getPath() + "****");  
                        /*if (pdfFile.exists()) {  
                            pdfFile.delete();  
                        } */ 
  
                    } catch (IOException e) {  
                        e.printStackTrace();  
                        throw e;  
                    }  
                } else if (environment == 2) {// linux环境处理  
                    try {  
                        Process p = r.exec("/usr/local/swftools/bin/pdf2swf " + newFile.getPath()
                                + " -o " + swfFile.getPath() + " -T 9");  
                        System.out.print(loadStream(p.getInputStream()));  
                        System.err.print(loadStream(p.getErrorStream()));  
                        System.err.println("****swf转换成功，文件输出："  
                                + swfFile.getPath() + "****");  
                        /*if (pdfFile.exists()) {  
                            pdfFile.delete();  
                        } */ 
                    } catch (Exception e) {  
                        e.printStackTrace();  
                        throw e;  
                    }  
                }  
            } else {
                System.out.println("****pdf不存在,无法转换****");  
            }
        } else {  
            System.out.println("****swf已经存在不需要转换****");  
        }  
    }  
  
    static String loadStream(InputStream in) throws IOException {  
  
        int ptr = 0;  
        in = new BufferedInputStream(in);  
        StringBuffer buffer = new StringBuffer();  
  
        while ((ptr = in.read()) != -1) {  
            buffer.append((char) ptr);  
        }  
        return buffer.toString();  
    }  
    /** 
     * 转换主方法 
     */  
    @SuppressWarnings("unused")  
    public boolean conver() {  
  
        if (swfFile.exists()) {  
            System.out.println("****swf转换器开始工作，该文件已经转换为swf****");  
            return true;  
        }  
  
        if (environment == 1) {  
            System.out.println("****swf转换器开始工作，当前设置运行环境windows****");  
        } else {  
            System.out.println("****swf转换器开始工作，当前设置运行环境linux****");  
        }  
        try{  
            doc2pdf();  
            addWater(pdfFile.getPath(), newFile.getPath(), waterString);
            //pdf2swf();
        } catch (Exception e) {  
            e.printStackTrace();  
            return false;  
        }  
  
        if (swfFile.exists()) {  
            return true;  
        } else {  
            return false;  
        }  
    }

    /**
     * 转换主方法2
     *
     * 利用com.aspose.words.Document 转pdf 检查 linux 中文 是否乱码
     * 避开 openoffice 转pdf
     */
    @SuppressWarnings("unused")
    public boolean converdTp() {

        /*if (swfFile.exists()) {
            System.out.println("****swf转换器开始工作，该文件已经转换为swf****");
            return true;
        }*/

        if (environment == 1) {
            System.out.println("****swf转换器开始工作，当前设置运行环境windows****");
        } else {
            System.out.println("****swf转换器开始工作，当前设置运行环境linux****");
        }
        try {
            ///利用com.aspose.words.Document 转pdf 检查 linux 是否乱码
            XMlToDoc.doc2pdf(docFile.getPath(),pdfFile.getPath());
            addWater(pdfFile.getPath(), newFile.getPath(), waterString);
            //pdf2swf();
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }

        if (swfFile.exists()) {
            return true;
        } else {
            return false;
        }
    }

    /** 
     * 返回文件路径 
     *  
     * @param s 
     */  
    public String getswfPath() {  
        if (swfFile.exists()) {  
            String tempString = swfFile.getPath();  
            /*tempString = tempString.replaceAll("\\\\", "/"); */ 
            return tempString;  
        } else {  
            return "";  
        }  
    }  
    /** 
     * 返回文件路径 
     *  
     * @param s 
     */  
    public String getpdfPath() {  
        if (newFile.exists()) {
            String tempString = newFile.getPath();
            /*tempString = tempString.replaceAll("\\\\", "/"); */ 
            return tempString;  
        } else {  
            return "";  
        }  
    }  
    /** 
     * 设置输出路径 
     */  
    public void setOutputPath(String outputPath) {  
        this.outputPath = outputPath;  
        if (!outputPath.equals("")) {  
        	String sep = File.separator;
            String realName = fileName.substring(fileName.lastIndexOf(sep),  
                    fileName.lastIndexOf("."));  
            if (outputPath.charAt(outputPath.length()) == sep.charAt(0)) {  
                swfFile = new File(outputPath + realName + ".swf");  
            } else {  
                swfFile = new File(outputPath + realName + ".swf");  
            }  
        }  
    }  
    
    public static int addWater(String fileName, String savepath,  
            String waterMarkString)  {  
    	if("".equals(waterMarkString) || waterMarkString == null){
    		return 0;
    	}
        // 文档总页数  
        int num = 0;  
        Document document = new Document();  
        try  
        {  
            PdfReader reader = new PdfReader(fileName);  
            BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",  
                    BaseFont.EMBEDDED);  
  
            num = reader.getNumberOfPages();  
            PdfCopy copy = new PdfCopy(document, new FileOutputStream(savepath));  
            PdfGState gs = new PdfGState();  
            document.open();  
            for (int i = 0; i < num;)  
            {  
                PdfImportedPage page = copy.getImportedPage(reader, ++i);  
                PageStamp stamp = copy.createPageStamp(page);  
                Font f = new Font(base);  
                // 添加水印  
                PdfContentByte under = stamp.getOverContent(); 
                gs.setFillOpacity(0.3f);
                under.setGState(gs);
                under.beginText();  
                under.setColorFill(Color.LIGHT_GRAY);  
                  
                // 字符越长，字体越小，设置字体  
                int fontSize = getFontSize(waterMarkString);  
                under.setFontAndSize(base, fontSize);  
  
                // 设置水印文字字体倾斜 开始  
                float pageWidth = reader.getPageSize(i).getWidth();  
                float pageHeight = reader.getPageSize(i).getHeight();  
  
                under.showTextAligned(Element.ALIGN_CENTER, waterMarkString,  
                        pageWidth / 2, pageHeight / 2, 60);// 水印文字成60度角倾斜,且页面居中展示  
  
                // 字体设置结束  
                under.endText();  
                stamp.alterContents();  
                copy.addPage(page);  
            }  
        }  
        catch (Exception e)  
        {  
            e.printStackTrace();  
            return -1;  
        }  
        finally  
        {  
            if (null != document)  
            {  
                document.close();  
            }  
        }  
        System.out.println("pdf totalpages:" + num);  
        return num;  
  
    }  
    /** 
     * 根据水印文字长度计算获取字体大小 
     * @param waterMarkName 
     * @return 
     */  
    private static int getFontSize(String waterMarkName){  
        int fontSize = 80;  
        if(null != waterMarkName && !"".equals(waterMarkName)){  
            int length = waterMarkName.length();  
            if(length <=26 && length >= 18){  
                fontSize = 26;  
            }else if(length <18 && length >= 8){  
                fontSize = 40;  
            }else if(length <8 && length >= 1){  
                fontSize = 80;  
            }else {  
                fontSize = 40;  
            }  
        }         
        return fontSize;  
    }  
}  