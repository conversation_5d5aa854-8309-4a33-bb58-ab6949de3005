package com.xinxinsoft.utils;


import java.io.File;
import java.io.InputStream;
import java.io.StringReader;
import java.util.Iterator;
import java.util.List;

import javax.xml.ws.WebServiceException;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.dom4j.*;
import org.dom4j.io.SAXReader;

public class XmlUtil {
	/**
	 * 根据Xml文件生成Document对象
	 * 
	 * @param file
	 *            xml文件路径
	 * @return Document对象
	 * @throws DocumentException
	 */
	public static Document getDocument(File file) throws DocumentException {
		SAXReader xmlReader = new SAXReader();
		return xmlReader.read(file);
	}

	/**
	 * 根据输入流生成Document对象
	 * 
	 * @param is
	 *            输入流
	 * @return Document对象
	 * @throws DocumentException
	 */
	public static Document getDocument(InputStream is) throws DocumentException {
		SAXReader xmlReader = new SAXReader();
		return xmlReader.read(is);
	}

	/**
	 * 根据Document得到根结点
	 * 
	 * @param doc
	 *            Document目录
	 * @return 根结点
	 */
	public static Element getRoot(Document doc) {
		return doc.getRootElement();
	}

	/**
	 * 取出当前结点下的所有子结点
	 * 
	 * @param root
	 *            当前结点
	 * @return 一组Element
	 */
	@SuppressWarnings("unchecked")
	public static List<Element> getElements(Element root) {
		return root.elements();
	}

	/**
	 * 根据元素名称返回一组Element
	 * 
	 * @param root
	 *            当前结点
	 * @param name
	 *            要返回的元素名称
	 * @return 一组Element
	 */
	@SuppressWarnings("unchecked")
	public static List<Element> getElementsByName(Element root, String name) {
		return root.elements(name);
	}

	/**
	 * 根据元素名称返回一个元素(如果有多个元素的话，只返回第一个)
	 * 
	 * @param root
	 *            当前结点
	 * @param name
	 *            要返回的元素名称
	 * @return 一个Element元素
	 */
	public static Element getElementByName(Element root, String name) {
		return root.element(name);
	}

	/**
	 * 根据当前元素,返回该元素的所有属性
	 * 
	 * @param root
	 *            当前结点
	 * @return 当前结点的所有属性
	 */
	@SuppressWarnings("unchecked")
	public static List<Attribute> getAttributes(Element root) {
		return root.attributes();
	}

	/**
	 * 根据属性名称,返回当前元素的某个属性
	 * 
	 * @param root
	 *            当前结点
	 * @return 当前结点的一个属性
	 */
	public static Attribute getAttributeByName(Element root, String name) {
		return root.attribute(name);
	}
	
	/**
	 * 根据元素名称返回一组Element
	 * @param root当前结点
	 * @param name要返回的元素名称
	 * @return name元素的值
	 */
	public static String getElementTextByName(Element root, String name){
		return root.element(name).getText();
	}
	/**
     * 解析返回报文
     * @param node 标记所在节点
     * @param attr 标记所在属性
     * @param soap 报文
     * @return 标记值
     * @throws WebServiceModuleRuntimeException
     */
    public static String parseResponseSoap(String node, String attr, String soap)  {
        //然后用SOAPMessage 和 SOAPBody
        Document personDoc;
        try {
            personDoc = new SAXReader().read(new StringReader(soap));
            Element rootElt = personDoc.getRootElement(); // 获取根节点
            @SuppressWarnings("rawtypes")
			Iterator body = rootElt.elementIterator("Body");
            while (body.hasNext()) {
                Element recordEless = (Element) body.next();
                return nextSubElement(node,attr,recordEless);
            }
        } catch (DocumentException e) {
            throw new WebServiceException("解析返回报文失败", e);
        }
        return "";
    }
    
    /**
     * 递归方法，查找本节点是否有标记信息，如果没有就查找下一层，
     * 在下一层里同样查找本层节点，只要找到值，就层层返回。
     * @param node 节点标签名
     * @param attr 节点属性值
     * @param el 当前节点对象
     * @return 目标值
     */
    public static String nextSubElement(String node, String attr, Element el) {
        if (el.getName().equals(node)) {
            //说明 找到了目标节点
            //属性值为空说明取标签内容
            if (attr.equals("")) {
                @SuppressWarnings("rawtypes")
				Iterator sub2 = el.elementIterator();
                //有子节点说明标签内容不是单一值，需要拿到查询结果
                if (sub2.hasNext()) {
                    while (sub2.hasNext()) {
                        @SuppressWarnings("unused")
						Element s2 = (Element) sub2.next();
                        //如果返回的不是单一的标记值，而是查询结果，有些麻烦，
                        //查询结果应当是list<map>格式，但是map的key值不好确定，是标签名作为key还是属性值作为key
                        //todo
                    }
                } else {
                    return  el.getText();
                }

            } else {
                Attribute attrbute = el.attribute(attr);
                return attrbute.getText();
            }
        } else {
            @SuppressWarnings("rawtypes")
			Iterator sub2 = el.elementIterator();
            while (sub2.hasNext()) {
                Element sub = (Element) sub2.next();
                return nextSubElement(node, attr, sub);
            }
        }
        return "";
    }

	/**
	 * xml转json
	 *
	 * @param xmlStr
	 * @return
	 * @throws DocumentException
	 */
	public static JSONObject xml2Json(String xmlStr) throws DocumentException {
		Document doc = DocumentHelper.parseText(xmlStr);
		JSONObject json = new JSONObject();
		dom4j2Json(doc.getRootElement(), json);
		System.out.println(json);
		return json;
	}

	/**
	 * xml转json
	 *
	 * @param element
	 * @param json
	 */
	public static void dom4j2Json(Element element, JSONObject json) {
		List<Element> chdEl = element.elements();
		for(Element e : chdEl){
			if (!e.elements().isEmpty()) {
				JSONObject chdjson = new JSONObject();
				dom4j2Json(e, chdjson);
				Object o = json.get(e.getName());
				if (o != null) {
					JSONArray jsona = null;
					if (o instanceof JSONObject) {
						JSONObject jsono = (JSONObject) o;
						json.remove(e.getName());
						jsona = new JSONArray();
						jsona.add(jsono);
						jsona.add(chdjson);
					}
					if (o instanceof JSONArray) {
						jsona = (JSONArray) o;
						jsona.add(chdjson);
					}
					json.put(e.getName(), jsona);
				} else {
					if (!chdjson.isEmpty()) {
						json.put(e.getName(), chdjson);
					}
				}
			} else {
				if (!e.getText().isEmpty()) {
					json.put(e.getName(), e.getText());
				}
			}
		}
	}
}
