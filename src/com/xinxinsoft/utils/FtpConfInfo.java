package com.xinxinsoft.utils;

public class FtpConfInfo {

	private String User;
	private String Password;
	private String Server;
	private String Location;
	private String FileName;
	private Long MaxWorkTime;
	private int Port;
	private String Encoding;

	public String getUser() {
		return User;
	}

	public void setUser(String user) {
		User = user;
	}

	public String getPassword() {
		return Password;
	}

	public void setPassword(String password) {
		Password = password;
	}

	public String getServer() {
		return Server;
	}

	public void setServer(String server) {
		Server = server;
	}

	public String getLocation() {
		return Location;
	}

	public void setLocation(String location) {
		Location = location;
	}

	public String getFileName() {
		return FileName;
	}

	public void setFileName(String fileName) {
		FileName = fileName;
	}

	public Long getMaxWorkTime() {
		return MaxWorkTime;
	}

	public void setMaxWorkTime(Long maxWorkTime) {
		MaxWorkTime = maxWorkTime;
	}

	public int getPort() {
		return Port;
	}

	public void setPort(int port) {
		Port = port;
	}

	public String getEncoding() {
		return Encoding;
	}

	public void setEncoding(String encoding) {
		Encoding = encoding;
	}

}
