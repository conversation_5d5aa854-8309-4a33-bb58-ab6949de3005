package com.xinxinsoft.utils;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.text.ParseException;
import java.util.HashMap;
import java.util.Map;

/**
 * @ClassName: ProcessUtils
 * @Title: ProcessUtils
 * @Package: com.xinxinsoft.utils
 * @author: liyang
 * @date: 2022/3/22 14:32
 * @Version: 1.0
 * @Description: TODO
 */
public class ProcessUtils {
    private static Logger logger= LoggerFactory.getLogger(ProcessUtils.class);

    public static boolean processJudge(Map<String, Object> variables,String conditionText) throws Exception{
        for(Map.Entry<String, Object> entry : variables.entrySet()){
            String mapKey = entry.getKey();
            Object mapValue = entry.getValue();
            conditionText = conditionText.replace(mapKey, mapValue.toString());
            conditionText = conditionText.replaceAll("[#${}']", "");
        }
        Object strReuslt = FormulaUtils.eval(conditionText);
        boolean b=Boolean.parseBoolean(strReuslt.toString());
        return b;
    }

    public static void main(String[] args) throws Exception{
        Map<String, Object> variables = new HashMap<String, Object>();
        variables.put("preferentialRatio","61");
        String conditionText = "preferentialRatio>60";
        for(Map.Entry<String, Object> entry : variables.entrySet()){
            String mapKey = entry.getKey();
            Object mapValue = entry.getValue();
            conditionText = conditionText.replace(mapKey, mapValue.toString());
            conditionText = conditionText.replaceAll("[#${}']", "");
        }
        Object strReuslt = FormulaUtils.eval(conditionText);
        boolean b=Boolean.parseBoolean(strReuslt.toString());
        System.out.println(b);
    }
}
