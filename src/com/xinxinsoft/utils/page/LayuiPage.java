package com.xinxinsoft.utils.page;
import java.util.Collection;
import java.util.List;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * Layui分页
 *
 */
public class LayuiPage {

    //数据
	@Expose
	@SerializedName("data")
    private Collection data;
    
    //总条数
	@Expose
	@SerializedName("count")
    private long count;
    
    //索引开始值
	@Expose
	@SerializedName("pageNo")
    private int pageNo;
    
    //每页条数
	@Expose
	@SerializedName("pageSize")
    private int pageSize;
	
	//状态码 0-成功（table需要）
	@Expose
	@SerializedName("code")
	private int code;
     
	public int getCode() {
		return code;
	}

	public void setCode(int code) {
		this.code = code;
	}

	public Collection getData() {
		return data;
	}

	public void setData(Collection data) {
		this.data = data;
	}

	public long getCount() {
		return count;
	}

	public void setCount(long count) {
		this.count = count;
	}

	public int getPageNo() {
		return pageNo;
	}

	public void setPageNo(int pageNo) {
		this.pageNo = (pageNo - 1) * pageSize;
	}

	public int getPageSize() {
		return pageSize;
	}

	public void setPageSize(int pageSize) {
		this.pageSize = pageSize;
	}
	/**
	 * 构造方法
	 * @param pageNo 索引起始位置
	 * @param pageSize 数据条数
	 */
	public  LayuiPage(Integer pageNo,Integer pageSize) {
		this.pageNo = (pageNo - 1) * pageSize;
		this.pageSize = pageSize;
	}
}
