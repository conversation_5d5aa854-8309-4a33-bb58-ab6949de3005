package com.xinxinsoft.utils;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.zip.ZipInputStream;

import org.jbpm.api.*;
import org.jbpm.api.task.Participation;
import org.jbpm.api.task.Task;

public class JbpmUtil {
	private ProcessEngine processEngine;
	private RepositoryService repositoryService = null;  
    private ExecutionService executionService = null;  
    private TaskService taskService = null;  
    private HistoryService historyService = null;  
    private ManagementService managementService = null; 
    public JbpmUtil(){
    	
    }
    /** 
     * 部署新流程定义 
     * @param resourceName 
     * @return 返回流程定义id
     */  
    public String deployNew(String resourceName) {  
        return repositoryService.createDeployment().addResourceFromClasspath(  
                resourceName).deploy();  
    }
    /**
     * 解压打包流程
     * @param zipUrl
     */
    public void deployZIP(String zipUrl){
    	InputStream in=this.getClass().getClassLoader().getResourceAsStream(zipUrl);
    	ZipInputStream zipInputStream = new ZipInputStream(in);  
    	// 部署  
        String deploymentId = processEngine.getRepositoryService()//  
                .createDeployment()//  
                .addResourcesFromZipInputStream(zipInputStream)//  
                .deploy();  
        System.out.println("deploymentId = " + deploymentId);  
    	//return repositoryService.createDeployment().addResourcesFromZipInputStream(zipInputStream).deploy(); 
    }
    /** 
     * 部署新流程定义(zip) 
     * @param resourceName 
     * @return 返回流程定义id 
     */  
    public String deployZipNew(String resourceZipName){  
    	ZipInputStream zis = new ZipInputStream(this.getClass().getResourceAsStream(resourceZipName));  

        return repositoryService.createDeployment().addResourcesFromZipInputStream(zis).deploy();     

    }
    /** 
     * 开始一个流程实例 
     * @param id 
     * @param map 
     * @return 
     */  
    public ProcessInstance startPIById(String id,Map<String,?> map){    
    	return executionService.startProcessInstanceById(id, map);        
    }
    /**
     * 开始一个流程实例 
     * @param processKey
     * @return
     */
    public ProcessInstance startPIByKey(String processKey){    
    	return executionService.startProcessInstanceByKey(processKey);        
    }
    /**
     * 开始一个流程实例 
     * @param processKey
     * @param userId
     * @return
     */
    public ProcessInstance startPIByKey(String processKey,String userId){    
    	return executionService.startProcessInstanceByKey(processKey, userId);        
    }
    /**
     * 开始一个流程实例 
     * @param processKey
     * @param map
     * @return
     */
    public ProcessInstance startPIByKey(String processKey,Map<String,?> map){    
    	return executionService.startProcessInstanceByKey(processKey, map);        
    }
    /**
     * 根据任务id获取流程实列
     * @param taskId
     * @return
     */
    public ProcessInstance findProcessInstanceById(String taskId){
    	return processEngine.getExecutionService().findProcessInstanceById(taskId);
    }
    /** 
     * 完成任务 
     * @param taskId 
     * @param map 不允许值中存在对象，会导致JBPM提交失败
     */  
    public void completeTask(String taskId,Map<String,?> map){ 
    	taskService.setVariables(taskId, map);
    	taskService.completeTask(taskId);    
    }
    /** 
     * 完成任务 
     * @param taskId 
     * @param map 不允许值中存在对象，会导致JBPM提交失败
     */  
    public boolean completeTaskBool(String taskId,Map<String,?> map){ 
    	try{
    		taskService.setVariables(taskId, map);
        	taskService.completeTask(taskId);
        	return true;
    	}catch (Exception e) {
			e.printStackTrace();
    		return false;
		}
    	    
    }
    /** 
     * 完成任务 
     * @param taskId 
     */  
    public void completeTask(String taskId){  
    	System.out.println("=============1");
    	taskService.completeTask(taskId);  
    	System.out.println("=============2");
    }
    /** 
     * 将任务流转到指定名字的流程outcome（流转线路）中去 
     * @param taskId 
     * @param outcome 
     */  
    public void completeTask(String taskId,String outcome){  
    	taskService.completeTask(taskId, outcome);
    }
    public void completeTask(String taskId,Map<String,?> map,String outcome){  
    	taskService.setVariables(taskId, map);
    	taskService.completeTask(taskId, outcome);
    }
    
    public boolean completeTaskBool(String taskId,Map<String,?> map,String outcome){  
    	try{
    		taskService.setVariables(taskId, map);
        	taskService.completeTask(taskId, outcome);
    		return true;
    	}catch (Exception e) {
			e.printStackTrace();
			return false;
		}
    	  
    }
    
    /** 
     * 获得所有发布了的流程 
     * @return 
     */  
    public List<ProcessDefinition> getAllPdList(){  
    	return repositoryService.createProcessDefinitionQuery().list();  
    }
    /** 
     * 获得所有流程实例 
     * @return 
     */  
    public List<ProcessInstance> getAllPiList(){  
    	return executionService.createProcessInstanceQuery().list();  
    }
    
    /**
     * 根据流程id 获取所有变量
     */
	public Set<String>  getPid(String pid){
		 return executionService.getVariableNames(pid);

	}
    
	/**
	 * 
	 * @param pid
	 * @param set
	 * @return
	 */
	public Map<String, Object> getVariables(String pid,Set<String> set){
		return executionService.getVariables(pid, set);
	}
    
    /**
     * 根据流程实列id获取流程实列对象
     * @param piId
     * @return
     */
    public ProcessInstance getPiByPiId(String piId){
    	List<ProcessInstance> listPi = getAllPiList();
    	ProcessInstance processInstance = null;
    	for(ProcessInstance pi : listPi){
    		if(piId.equals(pi.getId())){
    			processInstance = pi;
    			break;
    		}
    	}
    	return processInstance;
    }
    /** 
     * 根据流程实例Id，即executionId获取指定的变量值 
     * @param executionId 
     * @param variableName 
     * @return 
     */  
    public Object getVariableByexecutionId(String executionId,String variableName){  
    	return executionService.getVariable(executionId, variableName);  
    }
    /**
     * 根据流程实列id获取当前应该执行的任务节点名
     * @param piId
     * @return
     */
    public String getCurrentActivityName(String piId){
    	String name = executionService.createProcessInstanceQuery().processInstanceId(piId).uniqueResult().findActiveActivityNames().toString();  
    	return name;
    }
    /** 
     * 根据任务id，即taskId获取指定变量值 
     * @param taskId 
     * @param variableName 
     * @return 
     */  
    public Object getVariableByTaskId(String taskId,String variableName){  

    	System.out.println(taskId+"=======ccc=================");
    	System.out.println(taskService.getVariable(taskId, variableName)+"========================");
    	return taskService.getVariable(taskId, variableName);  
    }
    
    
    //获取任务变量  
    public Object getVariable(String taskId,String variableName) {  
      
    	return  taskService.getVariable(taskId, variableName);  
    }  
    /** 
     * 获取指定用户名字的任务 
     * @param userName 
     * @return 
     */  
    public List<Task> findPersonalTasks(String userName){  
    	return taskService.findPersonalTasks(userName);
    }
    /** 
     * 根据任务id获取任务 
     * @param taskId 
     * @return 
     */  
    public Task getTask(String taskId) {  
    	return taskService.getTask(taskId);  
    }
    /**
     * 根据任务id获取流程实列id
     * @param taskId
     * @return
     */
    public String findProcessInstanceByTaskId(String taskId){
    	return getTask(taskId).getExecutionId();
    }
    /** 
     * 级联删除流程定义，直接删除该流程定义下的所有实例 
     *  
     * @param deploymentId  流程定义id 
     */  
    public void deleteDeploymentCascade(String deploymentId) {  
    	                  
    	repositoryService.deleteDeploymentCascade(deploymentId);  
    }
    /** 
     * 删除流程实列
     */  
    public void deleteProcessInstance(String pid) {  
    	executionService.deleteProcessInstanceCascade(pid);  
    }  
    /**
     * 根据流程名查找流程id列表
     * @param processDefinitionName
     * @return
     */
    public List<String> findProcessDefinitionIdsByName(String processDefinitionName){
    	List<String> listStr = new ArrayList<String>();
    	List<ProcessDefinition> listProcessDefinition  = getAllPdList();
    	for(ProcessDefinition pd : listProcessDefinition){
    		if(pd.getName().equals(processDefinitionName)){
    			listStr.add(pd.getDeploymentId());
    		}
    	}
    	System.out.println(listStr.size());
    	return listStr;
    }
    /**
     * 根据实列id查找本实列的流程id
     * @param piId
     * @return
     */
    public String findProcessDefinitionIdBypiId(String piId){
    	List<ProcessInstance> listProcessInstance = getAllPiList();
    	String pdId = "";
    	for(ProcessInstance pi : listProcessInstance){
    		if(pi.getId().equals(piId)){
    			pdId = pi.getProcessDefinitionId();
    			break;
    		}
    	}
    	return pdId;
    }
    /**
     * 根据登录用户的id和流程名查询登录用户的待办列表
     * @param pdName
     * @param userNo
     * @return
     */
    public List<Task> findTaskByPdNameAndUserName(String pdName,String userNo){
    	List<String> listPdId = findProcessDefinitionIdsByName(pdName);
    	List<Task> tastList = findPersonalTasks(userNo+"");
    	List<Task> list = new ArrayList<Task>();
    	for(Task task : tastList){
			if(task.getExecutionId().contains(pdName)){
				
				list.add(task);
			}
//    		if(listPdId.contains(findProcessDefinitionIdBypiId(task.getExecutionId()))){//使用fork和join时不能用此来判断
//				list.add(task);
//			}
		}
    	return list;
    }
    /**
     * 根据任务id获取下一步的流向路线名的集合（transition，流向路线的名称要和指向的任务节点名称相同，这样就少写一个判断）
     * @param taskId
     * @return
     */
    public Set<String> findOutComesByTaskId(String taskId){
    	Set<String> listStr = taskService.getOutcomes(taskId);
    	for(String outcome : listStr){
    		System.out.println("outcome Name----------"+outcome);
    	}
    	return taskService.getOutcomes(taskId);
    }
    //processEngine.getTaskService().getOutcomes(task.getId())
    
    public JbpmUtil(ProcessEngine processEngine) {  
        this.processEngine = processEngine;  
        repositoryService = processEngine.getRepositoryService();  
        executionService = processEngine.getExecutionService();  
        taskService = processEngine.getTaskService();  
        historyService = processEngine.getHistoryService();  
        managementService = processEngine.getManagementService();  
    }
    public ProcessEngine getProcessEngine() {  
        return processEngine;  
    }  
  
    public void setProcessEngine(ProcessEngine processEngine) {  
        this.processEngine = processEngine;  
        System.out.println("processEngine="+processEngine);  
        repositoryService = processEngine.getRepositoryService();  
        executionService = processEngine.getExecutionService();  
        taskService = processEngine.getTaskService();  
        historyService = processEngine.getHistoryService();  
        managementService = processEngine.getManagementService();  
    }
	public RepositoryService getRepositoryService() {
		return repositoryService;
	}
	public void setRepositoryService(RepositoryService repositoryService) {
		this.repositoryService = repositoryService;
	}
	public ExecutionService getExecutionService() {
		return executionService;
	}
	public void setExecutionService(ExecutionService executionService) {
		this.executionService = executionService;
	}
	public TaskService getTaskService() {
		return taskService;
	}
	public void setTaskService(TaskService taskService) {
		this.taskService = taskService;
	}
	public HistoryService getHistoryService() {
		return historyService;
	}
	public void setHistoryService(HistoryService historyService) {
		this.historyService = historyService;
	}
	public ManagementService getManagementService() {
		return managementService;
	}
	public void setManagementService(ManagementService managementService) {
		this.managementService = managementService;
	}
    

}
