package com.xinxinsoft.utils;


import org.apache.commons.lang.StringUtils;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.hssf.util.HSSFColor;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.ss.util.CellRangeAddressList;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.*;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR> @date 2018/6/27 11:47
 * @desc
 */
public class ExcelToHTML {

    /**
     * @param filePath excel源文件文件的路径
     * @param htmlPositon 生成的html文件的路径
     * @param isWithStyle 是否需要表格样式 包含 字体 颜色 边框 对齐方式
     */
    public static String readExcelToHtml(String filePath ,String htmlPositon, boolean isWithStyle,int sheetnum){

        InputStream is = null;
        String htmlExcel = null;
        try {
            File sourcefile = new File(filePath);
            is = new FileInputStream(sourcefile);
            Workbook wb = WorkbookFactory.create(is);
            if (wb instanceof XSSFWorkbook) {   //03版excel处理方法
                XSSFWorkbook xWb = (XSSFWorkbook) wb;
                htmlExcel = ExcelToHTML.getExcelInfo(xWb,isWithStyle,sheetnum);
            }else if(wb instanceof HSSFWorkbook){  //07及10版以后的excel处理方法
                HSSFWorkbook hWb = (HSSFWorkbook) wb;
                htmlExcel = ExcelToHTML.getExcelInfo(hWb,isWithStyle,sheetnum);
            }
            //writeFile(htmlExcel,htmlPositon);
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            try {
                is.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return htmlExcel;
    }



    private static String getExcelInfo(Workbook wb,boolean isWithStyle,int sheetnum){

        StringBuffer sb = new StringBuffer();
        Sheet sheet = wb.getSheetAt(sheetnum);//获取第一个Sheet的内容
       // int lastRowNum = sheet.getLastRowNum(); //获取最后一行
        int lastRowNum = sheet.getPhysicalNumberOfRows(); //获取最后一行
        Map<String, String> map[] = getRowSpanColSpanMap(sheet);
        sb.append("<table style='border-collapse:collapse;' id='tariff_ht' width='100%'>");
        Row row = null;        //兼容
        Cell cell = null;    //兼容
        Map<Integer,String > strMap = new HashMap<>();
        for (int rowNum = sheet.getFirstRowNum(); rowNum <= lastRowNum; rowNum++) {
            row = sheet.getRow(rowNum);
            if (row == null) {
                sb.append("<tr><td ><nobr> </nobr></td></tr>");
                continue;
            }
            StringBuffer  sb_tr = new StringBuffer();
            Boolean isNull = false;
            int  several = 0;
            int lastColNum = row.getLastCellNum();
            for (int colNum = 0; colNum < lastColNum; colNum++) {
                cell = row.getCell(colNum);
                if (cell == null) {    //特殊情况 空白的单元格会返回null
                    sb_tr.append("<td> </td>");isNull = true;
                    continue;
                }
                String stringValue = getCellValue(cell);
                int colSpan = 0;
                if (map[0].containsKey(rowNum + "," + colNum)) {
                    String pointString = map[0].get(rowNum + "," + colNum);
                    map[0].remove(rowNum + "," + colNum);
                    int bottomeRow = Integer.valueOf(pointString.split(",")[0]);
                    int bottomeCol = Integer.valueOf(pointString.split(",")[1]);
                    int rowSpan = bottomeRow - rowNum + 1;
                    colSpan = bottomeCol - colNum + 1;
                    sb_tr.append("<td rowspan= '" + rowSpan + "' colspan= '"+ colSpan + "' ");
                } else if (map[1].containsKey(rowNum + "," + colNum)) {
                    map[1].remove(rowNum + "," + colNum);
                    continue;
                } else {
                    sb_tr.append("<td ");
                }
                //判断是否需要样式
                if(isWithStyle){
                    dealExcelStyle(wb, sheet, cell, sb_tr);//处理单元格样式
                }
                sb_tr.append("><nobr val='");
                for (Map.Entry<Integer,String> entry :strMap.entrySet()){
                    if(colNum==Integer.valueOf(entry.getKey())){
                        sb_tr.append(entry.getValue());
                    }
                }
                sb_tr.append("' >");
                if (stringValue == null || "".equals(stringValue.trim())) {
                    sb_tr.append("   ");
                    isNull = true;
                } else {
                    Boolean isR = false;
                    //获取该Sheet下面所有数据验证项
                    for(DataValidation validation :   sheet.getDataValidations()){
                        CellRangeAddressList addressList = validation.getRegions();
                        //空值判断
                        if(null == addressList || addressList.getSize() == 0){
                            continue;
                        }
                        //获取单元格行位置
                        int rows_fa = several=addressList.getCellRangeAddress(0).getFirstRow();
                        int rows = addressList.getCellRangeAddress(0).getLastRow();
                        //获取单元格列位置
                        int column = addressList.getCellRangeAddress(0).getFirstColumn();
                        //int column_fa = addressList.getCellRangeAddress(0).getFirstColumn();
                        Boolean  rs = false;
                        if(rows_fa <= rowNum && rows >= rowNum)rs=true;
                        //根据位置信息判断是不是自己想要获取的单元格位置
                        if(rs && colNum==column) {
                            DataValidationConstraint constraint = validation.getValidationConstraint();
                            //获取单元格数组
                            String[] strs = constraint.getExplicitListValues();
                            //输出数组
                            // System.out.println(StringUtils.join(strs, "-"));
                            sb_tr.append("<select style=\"width: 100%;height: 100%;border: 0px;padding:5px;\" onchange=\"GettextAndValue(this);\">");
                            for (String str:strs ) {
                                sb_tr.append("<option>"+str+"</option>");
                            }
                            sb_tr.append( "</select>");
                            isR = true;
                        }
                    }
                    if(!isR){
                        stringValue = stringValue.replace(String.valueOf((char) 160)," ");
                        // 将ascii码为160的空格转换为html下的空格（ ）
                        sb_tr.append(stringValue);

                        if(rowNum!=0 ) {
                            if (strMap.containsKey(colNum) && several!=0 && rowNum< several) {
                                strMap.put(colNum, StringUtils.trim(strMap.get(colNum) + "_" + stringValue));
                            } else {
                                if(colSpan>1){
                                    int wcole = 0;
                                    int wconcole = colNum;
                                    while(wcole < colSpan){
                                        strMap.put(wconcole, StringUtils.trim(stringValue));
                                        wcole ++;
                                        wconcole =colNum +wcole;
                                    }
                                }else{
                                    if(!strMap.containsKey(colNum))
                                        strMap.put(colNum, StringUtils.trim(stringValue));
                                }
                            }
                        }
                    }
                    isNull = false;
                }
                sb_tr.append("</nobr></td>");
            }
            if(!isNull) {
                if(rowNum >= several) {
                    sb.append("<tr class=\"obtain_bue\">");
                }else{
                    sb.append("<tr>");
                }
                sb.append(sb_tr);
                sb.append("</tr>");
            }
        }
//        StringBuffer resb = new StringBuffer();
        ///String  sp = StringUtils.remove(sb.toString(),StringUtils.substring(sb.toString(),StringUtils.lastIndexOf(sb.toString(),"<tr>"),StringUtils.lastIndexOf(sb.toString(),"</tr>")));
      //  sp = StringUtils.replace(sp,"</tr></tr>","");
//        resb.append(sb);
        sb.append("</table> <script>function  GettextAndValue(obj){  obj.options[obj.selectedIndex].setAttribute('selected','selected');  }</script>");
        return sb.toString();
    }


    private static Map<String, String>[] getRowSpanColSpanMap(Sheet sheet) {

        Map<String, String> map0 = new HashMap<String, String>();
        Map<String, String> map1 = new HashMap<String, String>();
        int mergedNum = sheet.getNumMergedRegions();
        CellRangeAddress range = null;
        for (int i = 0; i < mergedNum; i++) {
            range = sheet.getMergedRegion(i);
            int topRow = range.getFirstRow();
            int topCol = range.getFirstColumn();
            int bottomRow = range.getLastRow();
            int bottomCol = range.getLastColumn();
            map0.put(topRow + "," + topCol, bottomRow + "," + bottomCol);
            // System.out.println(topRow + "," + topCol + "," + bottomRow + "," + bottomCol);
            int tempRow = topRow;
            while (tempRow <= bottomRow) {
                int tempCol = topCol;
                while (tempCol <= bottomCol) {
                    map1.put(tempRow + "," + tempCol, "");
                    tempCol++;
                }
                tempRow++;
            }
            map1.remove(topRow + "," + topCol);
        }
        Map[] map = { map0, map1 };
        return map;
    }


    /**
     * 获取表格单元格Cell内容
     * @param cell
     * @return
     */
    private static String getCellValue(Cell cell) {

        String result = new String();
        switch (cell.getCellType()) {
            case Cell.CELL_TYPE_NUMERIC:// 数字类型
                if (HSSFDateUtil.isCellDateFormatted(cell)) {// 处理日期格式、时间格式
                    SimpleDateFormat sdf = null;
                    if (cell.getCellStyle().getDataFormat() == HSSFDataFormat.getBuiltinFormat("h:mm")) {
                        sdf = new SimpleDateFormat("HH:mm");
                    } else {// 日期
                        sdf = new SimpleDateFormat("yyyy-MM-dd");
                    }
                    Date date = cell.getDateCellValue();
                    result = sdf.format(date);
                } else if (cell.getCellStyle().getDataFormat() == 58) {
                    // 处理自定义日期格式：m月d日(通过判断单元格的格式id解决，id的值是58)
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                    double value = cell.getNumericCellValue();
                    Date date = org.apache.poi.ss.usermodel.DateUtil
                            .getJavaDate(value);
                    result = sdf.format(date);
                } else {
                    double value = cell.getNumericCellValue();
                    CellStyle style = cell.getCellStyle();
                    DecimalFormat format = new DecimalFormat();
                    String temp = style.getDataFormatString();
                    // 单元格设置成常规
                    if (temp.equals("General")) {
                        format.applyPattern("#.####");
                    }
                    result = format.format(value);
                }
                break;
            case Cell.CELL_TYPE_STRING:// String类型
                result = cell.getRichStringCellValue().toString();
                break;
            case Cell.CELL_TYPE_BLANK:
                result = "";
                break;
            case HSSFCell.CELL_TYPE_FORMULA:
                result = String.valueOf(cell.getCellFormula());
                break;
            case HSSFCell.CELL_TYPE_ERROR: // 故障
                result ="" ;
                break;
            default:
                result = "";
                break;
        }
        return result;
    }

    /**
     * 处理表格样式
     * @param wb
     * @param sheet
     * @param sb
     */
    private static void dealExcelStyle(Workbook wb,Sheet sheet,Cell cell,StringBuffer sb){

        CellStyle cellStyle = cell.getCellStyle();
        if (cellStyle != null) {
            short alignment = cellStyle.getAlignment();
            //    sb.append("align='" + convertAlignToHtml(alignment) + "' ");//单元格内容的水平对齐方式
            short verticalAlignment = cellStyle.getVerticalAlignment();
            sb.append("valign='"+ convertVerticalAlignToHtml(verticalAlignment)+ "' ");//单元格中内容的垂直排列方式

            if (wb instanceof XSSFWorkbook) {

                XSSFFont xf = ((XSSFCellStyle) cellStyle).getFont();
                short boldWeight = xf.getBoldweight();
                String  align = convertAlignToHtml(alignment);
                sb.append("style='");
                sb.append("font-weight:" + boldWeight + ";"); // 字体加粗
                sb.append("font-size: " + xf.getFontHeight() / 2 + "%;"); // 字体大小
                int columnWidth = sheet.getColumnWidth(cell.getColumnIndex()) ;
                sb.append("width:" + columnWidth + "px;");
                sb.append("text-align:" + align + ";");//表头排版样式
                XSSFColor xc = xf.getXSSFColor();
                if (xc != null && !"".equals(xc)) {
                    sb.append("color:#" + xc.getARGBHex().substring(2) + ";"); // 字体颜色
                }

                XSSFColor bgColor = (XSSFColor) cellStyle.getFillForegroundColorColor();
                if (bgColor != null && !"".equals(bgColor)) {
                    sb.append("background-color:#" + bgColor.getARGBHex().substring(2) + ";"); // 背景颜色
                }
                sb.append(getBorderStyle(0,cellStyle.getBorderTop(), ((XSSFCellStyle) cellStyle).getTopBorderXSSFColor()));
                sb.append(getBorderStyle(1,cellStyle.getBorderRight(), ((XSSFCellStyle) cellStyle).getRightBorderXSSFColor()));
                sb.append(getBorderStyle(2,cellStyle.getBorderBottom(), ((XSSFCellStyle) cellStyle).getBottomBorderXSSFColor()));
                sb.append(getBorderStyle(3,cellStyle.getBorderLeft(), ((XSSFCellStyle) cellStyle).getLeftBorderXSSFColor()));

            }else if(wb instanceof HSSFWorkbook){

                HSSFFont hf = ((HSSFCellStyle) cellStyle).getFont(wb);
                short boldWeight = hf.getBoldweight();
                short fontColor = hf.getColor();
                sb.append("style='");
                HSSFPalette palette = ((HSSFWorkbook) wb).getCustomPalette(); // 类HSSFPalette用于求的颜色的国际标准形式
                HSSFColor hc = palette.getColor(fontColor);
                sb.append("font-weight:" + boldWeight + ";"); // 字体加粗
                sb.append("font-size: " + hf.getFontHeight() / 2 + "%;"); // 字体大小
                String  align = convertAlignToHtml(alignment);
                sb.append("text-align:" + align + ";");//表头排版样式
                String fontColorStr = convertToStardColor(hc);
                if (fontColorStr != null && !"".equals(fontColorStr.trim())) {
                    sb.append("color:" + fontColorStr + ";"); // 字体颜色
                }
                int columnWidth = sheet.getColumnWidth(cell.getColumnIndex()) ;
                sb.append("width:" + columnWidth + "px;");
                short bgColor = cellStyle.getFillForegroundColor();
                hc = palette.getColor(bgColor);
                String bgColorStr = convertToStardColor(hc);
                if (bgColorStr != null && !"".equals(bgColorStr.trim())) {
                    sb.append("background-color:" + bgColorStr + ";"); // 背景颜色
                }
                sb.append( getBorderStyle(palette,0,cellStyle.getBorderTop(),cellStyle.getTopBorderColor()));
                sb.append( getBorderStyle(palette,1,cellStyle.getBorderRight(),cellStyle.getRightBorderColor()));
                sb.append( getBorderStyle(palette,3,cellStyle.getBorderLeft(),cellStyle.getLeftBorderColor()));
                sb.append( getBorderStyle(palette,2,cellStyle.getBorderBottom(),cellStyle.getBottomBorderColor()));
            }

            sb.append("' ");
        }
    }

    /**
     * 单元格内容的水平对齐方式
     * @param alignment
     * @return
     */
    private static String convertAlignToHtml(short alignment) {

        String align = "center";
        switch (alignment) {
            case CellStyle.ALIGN_LEFT:
                align = "left";
                break;
            case CellStyle.ALIGN_CENTER:
                align = "center";
                break;
            case CellStyle.ALIGN_RIGHT:
                align = "right";
                break;
            default:
                break;
        }
        return align;
    }

    /**
     * 单元格中内容的垂直排列方式
     * @param verticalAlignment
     * @return
     */
    private static String convertVerticalAlignToHtml(short verticalAlignment) {

        String valign = "middle";
        switch (verticalAlignment) {
            case CellStyle.VERTICAL_BOTTOM:
                valign = "bottom";
                break;
            case CellStyle.VERTICAL_CENTER:
                valign = "center";
                break;
            case CellStyle.VERTICAL_TOP:
                valign = "top";
                break;
            default:
                break;
        }
        return valign;
    }

    private static String convertToStardColor(HSSFColor hc) {

        StringBuffer sb = new StringBuffer("");
        if (hc != null) {
            if (HSSFColor.AUTOMATIC.index == hc.getIndex()) {
                return null;
            }
            sb.append("#");
            for (int i = 0; i < hc.getTriplet().length; i++) {
                sb.append(fillWithZero(Integer.toHexString(hc.getTriplet()[i])));
            }
        }

        return sb.toString();
    }

    private static String fillWithZero(String str) {
        if (str != null && str.length() < 2) {
            return "0" + str;
        }
        return str;
    }

    static String[] bordesr={"border-top:","border-right:","border-bottom:","border-left:"};
    static String[] borderStyles={"solid ","solid ","solid ","solid ","solid ","solid ","solid ","solid ","solid ","solid","solid","solid","solid","solid"};

    private static  String getBorderStyle(  HSSFPalette palette ,int b,short s, short t){

        if(s==0)return  bordesr[b]+borderStyles[s]+"#d0d7e5 1px;";;
        String borderColorStr = convertToStardColor( palette.getColor(t));
        borderColorStr=borderColorStr==null|| borderColorStr.length()<1?"#000000":borderColorStr;
        return bordesr[b]+borderStyles[s]+borderColorStr+" 1px;";

    }

    private static  String getBorderStyle(int b,short s, XSSFColor xc){

        if(s==0)return  bordesr[b]+borderStyles[s]+"#d0d7e5 1px;";;
        if (xc != null && !"".equals(xc)) {
            String borderColorStr = xc.getARGBHex();//t.getARGBHex();
            borderColorStr=borderColorStr==null|| borderColorStr.length()<1?"#000000":borderColorStr.substring(2);
            return bordesr[b]+borderStyles[s]+borderColorStr+" 1px;";
        }

        return "";
    }
    /*
     * @param content 生成的excel表格标签
     * @param htmlPath 生成的html文件地址
     */
    private static void writeFile(String content,String htmlPath){
        File file2 = new File(htmlPath);
        StringBuilder sb = new StringBuilder();
        try {
            if(!file2.exists()){
                file2.getParentFile().mkdir();
              try {
             //创建文件
                  file2.createNewFile();
             } catch (IOException e) {
                e.printStackTrace();
             }
            }

            sb.append("<html><head><meta http-equiv=\"Content-Type\" content=\"text/html; charset=utf-8\"><title>Html Test</title></head><body>");
            sb.append("<div>");
            sb.append(content);
            sb.append("</div>");
            sb.append("<div style=\"text-align: center;padding:5px;\"><button>添加</button><div>");
            sb.append("</body></html>");

            PrintStream printStream = new PrintStream(new FileOutputStream(file2));

            printStream.println(sb.toString());//将字符串写入文件

        } catch (IOException e) {

            e.printStackTrace();

        }

    }

}
