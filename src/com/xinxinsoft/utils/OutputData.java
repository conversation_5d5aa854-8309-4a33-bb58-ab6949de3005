package com.xinxinsoft.utils;

import java.util.Collection;

import com.google.gson.annotations.Expose;
import com.google.gson.annotations.SerializedName;

/**
 * 输出数据头信息
 * <AUTHOR>
 * @Date 2016-10-28 下午5:50:44
 * @version 1.00
 */
public class OutputData {
	@Expose
	@SerializedName("errorFlag")
	private String  errorFlag;//错误标志
	@Expose
	@SerializedName("errorMessage")
	private String errorMessage;//错误信息
	@Expose
	@SerializedName("result")
	private Collection list;//返回数据行
	
	
	public String getErrorFlag() {
		return errorFlag;
	}
	public void setErrorFlag(String errorFlag) {
		this.errorFlag = errorFlag;
	}
	public String getErrorMessage() {
		return errorMessage;
	}
	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}
	public Collection getList() {
		return list;
	}
	public void setList(Collection list) {
		this.list = list;
	}
	
	
//	
}
