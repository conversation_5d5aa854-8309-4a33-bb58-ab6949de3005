package com.xinxinsoft.utils.common;


import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.Rectangle;
import com.itextpdf.text.pdf.PdfWriter;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.List;

/**
 * 将多张图片合并转为PDF；需要用到iTextpdf包，
 * <AUTHOR>
 */
public class PrintToPdfUtil {

    /**
     *
     * @param imageFolderPath
     *            图片文件夹地址
     * @param pdfPath
     *            PDF文件保存地址
     *
     */
    public static void toPdf(List<File> imageFolderPath, String pdfPath) {
        Document document = new Document();
        //设置文档页边距
        document.setMargins(0,0,0,0);
        FileOutputStream fos = null;
        try {
            // 图片地址
            String imagePath = null;
            // 输入流
            fos = new FileOutputStream(pdfPath);
            // 创建一个PdfWriter实例，
            PdfWriter.getInstance(document, fos);
            //打开文档
            document.open();
            for(File file :imageFolderPath){
                String fileName=file.getName();
                if (fileName.toLowerCase().endsWith(".bmp")
                        || fileName.toLowerCase().endsWith(".jpg")
                        || fileName.toLowerCase().endsWith(".jpeg")
                        || fileName.toLowerCase().endsWith(".gif")
                        || fileName.toLowerCase().endsWith(".png")) {
                    String temp=file.getPath();
                    Image img=Image.getInstance(temp);
                    img.setAlignment(Image.ALIGN_CENTER);
                    // 根据图片大小设置页面，一定要先设置页面，再newPage（），否则无效
                    document.setPageSize(new Rectangle(img.getWidth(), img.getHeight()));
                    document.newPage();
                    document.add(img);
                }
            }

        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        } finally {
            // 关闭文档
            if(document.isOpen())
                document.close();
            try {
                fos.flush();
                fos.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

}