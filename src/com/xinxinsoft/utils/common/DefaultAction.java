package com.xinxinsoft.utils.common;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.Writer;
import java.util.Hashtable;

import javax.servlet.ServletException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpUtils;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.exception.ExceptionUtils;
import org.apache.struts2.ServletActionContext;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.opensymphony.xwork2.ActionSupport;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.easyh.NonPageRequest;
import com.xinxinsoft.utils.easyh.PageRequest;
import com.xinxinsoft.utils.easyh.PageResponse;


public class DefaultAction extends ActionSupport {
	private static final long serialVersionUID = 1L;
	public static String EDIT = "edit";
	public static String ADD = "add";
	public static String LIST = "list";
	public DefaultAction() {
		super();
	}
	private String format(String name) {
		return StringUtils.replaceEach(name,//
				new String[] {  "＜", "＞" },	
				new String[] {  "<", ">"});
		// return StringEscapeUtils.escapeHtml4(name);
	}
	
	public void writeError(String errMessage) {
		getResponse().setContentType("text/html;charset=UTF-8");
		JSONObject err = new JSONObject();
		err.put("error", errMessage);
		outString(err.toString());
		outEnd();
	}
	
	public void writeText(String str) {
		getResponse().setContentType("text/html;charset=UTF-8");
		outString(format(str));
		outEnd();
	}
	public void writeError(Exception e) {
		getResponse().setContentType("text/html;charset=UTF-8");
		JSONObject err = new JSONObject();
		err.put("error", ExceptionUtils.getFullStackTrace(e));
		outString(err.toString());
		outEnd();
	}
	
	public void writeResult(String str) {
		getResponse().setContentType("text/html;charset=UTF-8");
		outString(String.format("{\"res\":\"%s\"}", str));
		outEnd();
	}
	
	public void outString(String str)
    {
        try
        {
            PrintWriter out = getResponse().getWriter();
            out.print(str);
        }
        catch (IOException e)
        {
            e.printStackTrace();
        }
    }
	public void outEnd(){
    	try {
			getResponse().getWriter().close();
		} catch (IOException e) {
			e.printStackTrace();
		}
    }
	public HttpServletRequest getRequest() {
		return ServletActionContext.getRequest();
	}

	public HttpServletResponse getResponse() {
		return ServletActionContext.getResponse();
	}

	public void Write(HttpServletResponse res, String json) {
		res.setCharacterEncoding("UTF-8");
		res.setHeader("Cache-Control", "no-cache");
		res.setContentType("text/html");
		Writer writer;
		try {
			writer = res.getWriter();
			writer.write(format(json));
			writer.flush();
			writer.close();
		} catch (IOException e) {
			e.printStackTrace();
		}
	}

	public void Write(String json) {
		Write(getResponse(), json);
	}

	protected String getRealPath(String p) {
		return ServletActionContext.getServletContext().getRealPath(p);
	}

	protected Integer getInteger(String name) {
		String p = getString(name);
		if (p != null && !"".equals(p)) {
			try {
				return Integer.parseInt(p);
			} catch (Exception e) {
				return new Integer(0);
			}
		}
		return new Integer(0);
	}

	protected String getString(String name) {
		return getRequest().getParameter(name);
	}

	protected Double getDouble(String name) {
		try {
			return Double.parseDouble(getString(name));
		} catch (Exception e) {
			return new Double(0l);
		}
	}

	protected Integer getId() {
		return getInteger("id");
	}

	/**
	 * 附件下载
	 * 
	 * @return
	 */
	public String attdownload() {
		HttpServletRequest request = ServletActionContext.getRequest();
		String downPath = "c:/att/file.txt";
		String fileName = "file.txt";
		request.setAttribute("downPath", downPath);
		request.setAttribute("fileName", fileName);
		return "download";
	}



	/*
	 * 获取分页的请求对象
	 * 
	 * @return
	 */
	public PageRequest getPageRequest() {
		try {
			PageRequest req = new PageRequest();
			HttpServletRequest request = getRequest();
			
			req.setPage(new Integer(request.getParameter("page") == null ? "1"
					: request.getParameter("page")));
			req.setRows(new Integer(request.getParameter("rows") == null ? "10"
					: request.getParameter("rows")));
			req.setSidx(request.getParameter("sidx"));
			req.setSord(request.getParameter("sord"));
			req.setRequest(request);
			return req;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}

	}
	public NonPageRequest getNonPageRequest() {
		try {
			NonPageRequest req = new NonPageRequest();
			HttpServletRequest request = getRequest();
			req.setSidx(request.getParameter("sidx"));
			req.setSord(request.getParameter("sord"));
			req.setRequest(request);
			return req;
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		
	}
	public String forward(String path) throws ServletException, IOException {
		getRequest().getRequestDispatcher(path).forward(getRequest(), getResponse());
		return NONE;
	}

	public String sendRedirect(String path) throws IOException {
		getResponse().sendRedirect(path);
		return NONE;
	}
	public String formartJson(Object obj){
		Gson gs = new GsonBuilder().serializeNulls().setDateFormat(
		"yyyy-MM-dd").excludeFieldsWithoutExposeAnnotation()
		.create();
		return gs.toJson(obj);
	}
	
	public Hashtable<String, String[]> getQueryParam(String param, char d, char g){
		param = param.replace(d, '=');
		param = param.replace(g, '&');
		Hashtable<String, String[]> queryparam = HttpUtils.parseQueryString(param);
		return queryparam;
	}
	
	public void showRequestLogInfo() {
		System.out.println("===================================================================");
		System.out.println("==================== HEAD INFO ====================================");
		java.util.Enumeration names = getRequest().getHeaderNames();
		while (names.hasMoreElements()) {
			String name = (String) names.nextElement();
			System.out.println(name + ":" + getRequest().getHeader(name));
		}
		System.out.println("==================== PARAM INFO ===================================");
		java.util.Enumeration pnames = getRequest().getParameterNames();
		while (pnames.hasMoreElements()) {
			String name = (String) pnames.nextElement();
			System.out.println(name + ":" + getRequest().getParameter(name));
		}
		System.out.println("now service sessionid :" + getRequest().getSession().getId());
		System.out.println("===================================================================");
	}
	
	
	
}
