package com.xinxinsoft.utils.common;

import java.io.Serializable;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.hibernate.Query;
import org.hibernate.transform.ResultTransformer;
import org.hibernate.transform.Transformers;
import org.springframework.orm.hibernate3.support.HibernateDaoSupport;
import org.springframework.util.Assert;

import com.xinxinsoft.utils.easyh.NonPageRequest;
import com.xinxinsoft.utils.easyh.NonPageResponse;
import com.xinxinsoft.utils.easyh.PagePO;
import com.xinxinsoft.utils.easyh.PageRequest;
import com.xinxinsoft.utils.easyh.PageResponse;


/**
 * 数据持久对象父类
 * 
 * <AUTHOR>
 * 
 * @param <T>
 */
@SuppressWarnings("all")
public abstract class DefaultDao<T> extends HibernateDaoSupport {

	/**
	 * 按主键查询对象
	 * 
	 * @param entityClass
	 * @param id
	 * @return 调用对象
	 */
	protected <CLASS> CLASS findById(Class<CLASS> entityClass, Serializable id) {
		return (CLASS) getHibernateTemplate().get(entityClass, id);
	}
	
	
	
	/**
	 * 保存或更新
	 * 
	 * @param entity
	 */
	protected void saveOrUpdate(T entity) {
		getHibernateTemplate().saveOrUpdate(entity);
	}

	/**
	 * 保存
	 * 
	 * @param entity
	 */
	public T save(T entity) {
			return (T) getHibernateTemplate().save(entity);
	}
	/**
	 * 保存
	 */
	public Integer saveReId(T entity){
		return  (Integer) getHibernateTemplate().save(entity);
	}


	/**
	 * 更新
	 * 
	 * @param entity
	 */
	protected void update(T entity) {
		getHibernateTemplate().update(entity);
	}

	
	/**
	 * 删除
	 * 
	 * @param entity
	 */
	protected void delete(T entity) {
		getHibernateTemplate().delete(entity);
	}

	protected T merge(T entity) {
		T result = (T) getHibernateTemplate().merge(entity);
		return result;
	}
	
	/**
	 * 查询返回单个值的
	 * @param hql
	 * @param params
	 * @return
	 */
	protected Object uniqueResult(String hql, Object... params)
	{
		Query query = getSession().createQuery(hql);
		setParams(query, params);
		return query.uniqueResult();
	}

	/**
	 * 获取总数
	 * 
	 * @param hql
	 * @param params
	 * @return
	 */
	protected int getTotalCount(String hql, Object... params) {
//		Query query = getSession().createQuery(hql);
//		setParams(query, params);
//		return Integer.parseInt(query.uniqueResult().toString());
		return Integer.parseInt(uniqueResult(hql, params)+"");
	}

	/**
	 * 根据条件查询所有
	 * 
	 * @param hql
	 * @param params
	 * @return
	 */
	protected List<T> findAll(String hql, Object... params) {
		Query query = getSession().createQuery(hql);
		setParams(query, params);
		return query.list();
	}

	/**
	 * 分页查询
	 * 
	 * @param pageIndex
	 * @param pageSize
	 * @param hql
	 * @param params
	 * @return
	 */
	protected List<T> findByPage(int pageIndex, int pageSize, String hql,
			Object... params) {
		Query query = getSession().createQuery(hql);// .setParameters(params,
		// getTypes(params));
		setParams(query, params);
		query.setFirstResult((pageIndex - 1) * pageSize);
		query.setMaxResults(pageSize);
		return query.list();
	}
	/**
	 * 分页查询
	 * 
	 * @param pageIndex
	 * @param pageSize
	 * @param hql
	 * @param params
	 * @return
	 */
	@SuppressWarnings("hiding")
	protected <CLASS> List<CLASS> findByPage(Class<CLASS> entity, int pageIndex, int pageSize, String hql,
			Object... params) {
		Query query = getSession().createQuery(hql);
		setParams(query, params);
		query.setFirstResult((pageIndex - 1) * pageSize);
		query.setMaxResults(pageSize);
		return query.list();
	}

	/**
	 * 获取对应Hibernate的数据类型
	 * 
	 * @param params
	 * @return
	 */
	protected void setParams(Query query, Object... params) {
		
		for (int i = 0; i < params.length; i++) {
			query.setParameter(i, params[i]);
		}
	}
	
	protected List<T> findBySqlPage(Class<? extends T> target, int pageIndex, int pageSize, String sql,
			Object... params) {
		Query query = getSession().createSQLQuery(sql);
		try {
		setParams(query, params);
		query.setFirstResult((pageIndex - 1) * pageSize);
		query.setMaxResults(pageSize);
		ResultTransformer transformer = null;
		if(target.equals(Map.class)){
			transformer = Transformers.ALIAS_TO_ENTITY_MAP;
		}
		else if(target.equals(List.class)){
			transformer = Transformers.TO_LIST;
		}
		else{
			transformer = Transformers.aliasToBean(target);
		}
		query.setResultTransformer(transformer);
		
		} catch (Exception e) {
			// TODO: handle exception
		}
		return query.list();
	}
	protected int getSqlTotalCount(String sql, Object... params) {
		Query query = getSession().createSQLQuery(sql);
		setParams(query, params);
		
		return Integer.parseInt(query.uniqueResult().toString());
	}
	
	protected List<T> findBySqlPage_autofill(Class<? extends T> target, int pageIndex, int pageSize, String sql,
			Object... params) {
		Query query = getSession().createSQLQuery(sql).addEntity(target);
		setParams(query, params);
		query.setFirstResult((pageIndex - 1) * pageSize);
		query.setMaxResults(pageSize);
		return query.list();
	}
	/**
	 * 不需要 参数
	 * @param target
	 * @param pageIndex
	 * @param pageSize
	 * @param sql
	 * @return
	 */
	protected List<T> findBySqlPage_autofill(Class<? extends T> target, int pageIndex, int pageSize, String sql) {
		Query query = getSession().createSQLQuery(sql).addEntity(target);
		query.setFirstResult((pageIndex - 1) * pageSize);
		query.setMaxResults(pageSize);
		return query.list();
	}
	
	/**
	 * 
	 * @param req
	 */
	protected PageResponse grid(PageRequest req) throws Exception{
		PageResponse res = new PageResponse();
		PagePO page = req.getPagePo();
		if(req.isHql()){
			page.setData(findByPage(page.getCpage(), page.getShowNumber(), req.getQuery(), req.getParameters()));
			page.setTotNumber(getTotalCount(req.getQueryCount(), req.getParameters()));
		}
		else if(req.isAutofill()){
			page.setData(findBySqlPage_autofill(req.getTargetClass(), page.getCpage(), page.getShowNumber(), req.getQuery(), req.getParameters()));
			page.setTotNumber(getSqlTotalCount(req.getQueryCount(), req.getParameters()));
		}
		else{
			page.setData(findBySqlPage(req.getTargetClass(), page.getCpage(), page.getShowNumber(), req.getQuery(), req.getParameters()));
			page.setTotNumber(getSqlTotalCount(req.getQueryCount(), req.getParameters()));
		}
		
		res.setPagePo(page);
		return res;
	}

	public NonPageResponse grid(NonPageRequest req) throws Exception {
		NonPageResponse res = new NonPageResponse();
		if (req.isHql()) {
			res.addAll(findAll(req.getQuery(), req.getParameters()));
		} else if(req.isAutofill()){
			res.addAll(findBySql_autofill(req.getTargetClass(), req.getQuery(), req.getParameters()));
		}
		else{
			res.addAll(findBySql(req.getTargetClass(), req.getQuery(), req.getParameters()));
		}
		return res;
	}
	protected List<T> findBySql_autofill(Class<? extends T> target, String sql, Object... params) {
		Query query = getSession().createSQLQuery(sql).addEntity(target);
		setParams(query, params);
		return query.list();
	}
	
	protected List<T> findBySql(Class<? extends T> target, String sql, Object... params) {
		Query query = getSession().createSQLQuery(sql);
		setParams(query, params);
		ResultTransformer transformer = null;
		if (target.equals(Map.class)) {
			transformer = Transformers.ALIAS_TO_ENTITY_MAP;
		} else {
			transformer = Transformers.aliasToBean(target);
		}
		query.setResultTransformer(transformer);
		return query.list();
	}
	
}
