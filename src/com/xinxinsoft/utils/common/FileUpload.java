package com.xinxinsoft.utils.common;

import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Map;

import javax.imageio.ImageIO;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import com.xinxinsoft.service.config.Config;

public class FileUpload {
	
	//private static HttpSession session=ServletActionContext.getRequest().getSession();
	private static final Logger log = LoggerFactory.getLogger(FileUpload.class);
	/**
	 * 上载文件到远程FTP路径中
	 * 
	 * @param ftpUrl
	 *            eg: ************************************/poster/
	 * @param file
	 * @return [boolean] false=失败;true=成功.
	 */
	public static boolean uploadToApp(String ftpUrl, File file, String newName,Map<String,String> map,String userPhone){
	    Date   start_time = new Date();  
		  try {  
	            FileInputStream in = new FileInputStream(file);  
	            FileOutputStream out = new FileOutputStream(ftpUrl+newName);  
	            FileChannel inChan = in.getChannel();  
	            FileChannel outChan = out.getChannel();  
	            //开辟缓冲区  
	            ByteBuffer buf = ByteBuffer.allocate(1024);  
	            
	            long upFileSize=file.length();  
	            double persent=0;
				int len = 0;//表示成功读取的字节数的个数搜索	
				
	            while ((len=inChan.read(buf)) != -1){  
	                //重设缓冲区  
	                buf.flip();  
	                //输出缓冲区  
	                outChan.write(buf);  
	                //清空缓冲区  
	                buf.clear();  
	              //计算文件进度
				       persent+=len/(double)upFileSize*100D;
						map.put(userPhone, Math.round(persent)+"");
				       Thread.sleep(10);
	            }  
	            outChan.close();  
	            inChan.close();
	            out.flush();
	            out.close(); 
	            in.close();  
	            return true;
	        } catch (Exception e) {  
	            e.printStackTrace();
	            return false;
	        }finally{  
	        	Date  end_time = new Date();

			  log.info("======================文件复制方式总耗时："+(end_time.getTime() - start_time.getTime())+"毫秒");
	        }  
	}
	
	/**
	 * 上载文件到远程FTP路径中
	 * 
	 * @param ftpUrl
	 *            eg: ************************************/poster/
	 * @param file
	 * @return [boolean] false=失败;true=成功.
	 */
	public static boolean upload(String ftpUrl, File file, String newName){
	    Date   start_time = new Date();  
		  try {  
	            FileInputStream in = new FileInputStream(file);  
	            FileOutputStream out = new FileOutputStream(ftpUrl+newName);  
	            FileChannel inChan = in.getChannel();  
	            FileChannel outChan = out.getChannel();  
	            //开辟缓冲区  
	            ByteBuffer buf = ByteBuffer.allocate(1024);  
	            while ((inChan.read(buf)) != -1){  
	                //重设缓冲区  
	                buf.flip();  
	                //输出缓冲区  
	                outChan.write(buf);  
	                //清空缓冲区  
	                buf.clear();  
	            }  
	            outChan.close();  
	            inChan.close();
	            out.flush();
	            out.close(); 
	            in.close();  
	            return true;
	        } catch (Exception e) {  
	            e.printStackTrace();
	            return false;
	        }finally{  
	        	Date  end_time = new Date();

			  log.info("======================文件复制方式总耗时："+(end_time.getTime() - start_time.getTime())+"毫秒");
	        }  
	}
	
	/**
	 * 添加打叉水印
	 * 
	 *            打叉图片路径
	 *            源图片路径
	 * @param targerPath
	 *            目标图片路径
	 *            旋转角度
	 */
	@SuppressWarnings("resource")
	public static boolean upLoadZipImage(File srcImageFile,
			String targerPath,int needWidth,String type,Map<String,String> map ,String userPhone) {
		boolean flag=false;
		OutputStream os = null;
		try {
			
			 Image srcImg = ImageIO.read(srcImageFile);
		     int imgsize=needWidth;
		     double wideth = (double) srcImg.getWidth(null); // 得到源图宽
		     double height = (double) srcImg.getHeight(null); // 得到源图长
		     double imgbit=imgsize/wideth;
		     //缩放处理
		     int iWideth = imgsize;  
		     int iHeight = (int) (height*imgbit);
			
			
			BufferedImage buffImg=null;
			if(srcImg.getWidth(null)>iWideth || srcImg.getHeight(null)>iHeight)
			{
				buffImg = new BufferedImage(iWideth,
						iHeight, BufferedImage.TYPE_INT_RGB);				
			}
			else
			{
				buffImg = new BufferedImage(srcImg.getWidth(null),
					srcImg.getHeight(null), BufferedImage.TYPE_INT_RGB);
			}
			// 得到画笔对象
			Graphics2D g = buffImg.createGraphics();

			// 设置对线段的锯齿状边缘处理
//			g.setRenderingHint(RenderingHints.KEY_INTERPOLATION,
//					RenderingHints.VALUE_INTERPOLATION_BILINEAR);

			if(srcImg.getWidth(null)>iWideth || srcImg.getHeight(null)>iHeight)
			{
				g.drawImage(srcImg.getScaledInstance(iWideth, iHeight, Image.SCALE_SMOOTH), 0, 0, null);	
			}else
			{
				g.drawImage(srcImg.getScaledInstance(srcImg.getWidth(null), srcImg
					.getHeight(null), Image.SCALE_SMOOTH), 0, 0, null);
			}
			g.dispose();
			os = new FileOutputStream(targerPath);
			String type1=type.replace(".", "");
			
			InputStream is = new FileInputStream(srcImageFile);
			long upFileSize=srcImageFile.length();   
			byte[] b = new byte[1024];
			double persent=0;
			int len = 0;//表示成功读取的字节数的个数搜索			
			
			while((len=is.read(b))!=-1){
			       //计算文件进度
			       persent+=len/(double)upFileSize*100D;
			       //session.setAttribute("prog", Math.round(persent)+""); 
					map.put(userPhone, Math.round(persent)+"");
			       Thread.sleep(10);
			      }	
			// 生成图片
			ImageIO.write(buffImg, type1.toLowerCase(), os);
						
			flag=true;

		} catch (Exception e) {
			e.printStackTrace();
		} 
		finally {
			try {
				if (null != os)
					os.close();
			} catch (Exception e) {
				e.printStackTrace();
			}
		}
		return flag;
	}

	
	
	/**
	 * FtpUrl 配置文件中获取URL默认地址
	 * @return
	 */
	public static String getFtpURL(){
		return Config.getString("FTP_URL");
	}

	public static String getDocumentURL(){
		return Config.getString("DOCUMENT_URL");
	}
	/**
	 * FtpUrl 配置文件中获取URL默认地址
	 * @return
	 */
	public static String getFtpURL_auditWorksheet(){
		return Config.getString("FTP_AUDITWORKSHEETURL");
	}

    /**
     * UNIT_FILE_DIR 用户存储集团基础资料的文件地址路径
     *
     * @Param configKey: 配置文件配置的key
     * @Return: java.lang.String 返回key对应的值
     * @Author: Leo
     * @Date: 2021/7/15 14:49
     * @Description: 根据配置文件的KEY获取对应的值
     */
    public static String getFtpUrlTool(String configKey) {
        return Config.getString(configKey);
    }

    /**
	 * 合同文件
	 * @return
	 */
	public static String getContractFTPURL(){
		return Config.getString("CONTRACTFTP_URL");
	}
	
	/**
	 * 合同CSV文件
	 * @return
	 */
	public static String getContractCsv(){
		return Config.getString("CONTRACTCSV_URL");
	}

	/**
	 * 手机app
	 * @return
	 */
	public static String getCont_PdfURL(){
		return Config.getString("CONT_PDF_URL");
	}
	
	public static String getUserPhotoDir(){
		return Config.getString("USER_PHOTO_DIR");
	}
	/**
	 * 配置临时文件的存放地址
	 * @return
	 */
	public static String getFtpDownloadURL(){
		
		return Config.getString("FTP_TEMP_FILEPATH");
	}
	
	/**
	 * 获取当前时间：格式转换：默认格式：yyyy-MM-dd HH:mm:ss
	 * @param str 格式参数：null 
	 * @return  date 时间：
	 */
	public static String getDateToString(String str){
		Date d = new Date();
		SimpleDateFormat sdf = null;
		if(str==null){
			 sdf= new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		}else{
			sdf= new SimpleDateFormat(str);
		}
		try {
			return sdf.format(d);
		} catch (Exception e) {
			throw new Error(" date Exception throw ");
		}
	}
	/**
	 * 获取文件名后缀: .xxx
	 * @param filename
	 * @return
	 */
	public static String getFilePix(String filename){
		String[] n = filename.split("\\.");
		if(n!=null && n.length>0){
			String pix =n[n.length-1];
			return "."+pix;
		}
		return "";
	}
    /**
     * 获取文件名后缀: xxx
     * @param filename
     * @return
     */
    public static String getLastFilePix(String filename){
        String[] n = filename.split("\\.");
        if(n!=null && n.length>0){
            String pix =n[n.length-1];
            return pix;
        }
        return "";
    }
	
	/**
	 * 获取文件名前缀: 
	 * @return
	 */
	public static String getFileChname(String filename){
		return filename.substring(0,filename.lastIndexOf("."));
	}
	
	
	
	/** 
	  * 删除单个文件 
	  *  
	  * @param fileName 
	  *            要删除的文件的文件名 
	  * @return 单个文件删除成功返回true，否则返回false 
	  */  
	 public static boolean deleteFile(String fileName) {  
		 System.gc();
	  File file = new File(fileName);  
	  // 如果文件路径所对应的文件存在，并且是一个文件，则直接删除  
	  if (file.exists() && file.isFile()) {  
	   if (file.delete()) {
		   log.info("删除单个文件" + fileName + "成功！");
	    return true;  
	   } else {
		   log.info("删除单个文件" + fileName + "失败！");
	    return false;  
	   }  
	  } else {
		  log.info("删除单个文件失败：" + fileName + "不存在！");
	   return false;  
	  }  
	 }
	 
	 /**
	  * 将文件转成base64 字符串
	  * @return  *
	  * @throws Exception
	  */
	 public static String encodeBase64File(String path) throws Exception {
	  File file = new File(path);;
	  FileInputStream inputFile = new FileInputStream(file);
	  byte[] buffer = new byte[(int) file.length()];
	  inputFile.read(buffer);
	  inputFile.close();
	  return new BASE64Encoder().encode(buffer);

	 }

	 /**
	  * 将base64字符解码保存文件
	  * @param base64Code
	  * @throws Exception
	  */
	 public static boolean decoderBase64File(String ftpUrl,String base64Code,String newName,Map<String,String> map,String userPhone) {
		 Date   start_time = new Date();  
		 try {  
			  byte[] buffer = new BASE64Decoder().decodeBuffer(base64Code);
			  ByteArrayInputStream in = new ByteArrayInputStream(buffer);
			  FileOutputStream out = new FileOutputStream(ftpUrl+newName);
			  double persent=0;
				int len = 0;//表示成功读取的字节数的个数搜索	
				byte[] b = new byte[1024];
				long upFileSize=buffer.length; 
				log.info("upFileSize====="+upFileSize); 
				///防止第一次提交。第二次在提交；
				if(map.get(userPhone)!=null){
					while(Integer.parseInt(map.get(userPhone))>0)
					{
						if(Integer.parseInt(map.get(userPhone))<=0){
							break ;
						}
					}
				}
		      while ((len=in.read(b)) != -1){  
			       persent+=len/(double)upFileSize*100D;
			       if(Math.round(persent)<99){
					 map.put(userPhone, Math.round(persent)+"");
			       }
					//log.info("persent====="+persent+"==========userPhone========"+userPhone);
			       Thread.sleep(10);		          
		      }  
		      out.write(buffer);
		      
		      in.close();  
		      out.close();
		      map.put(userPhone,"99");
		      return true;
		 }
	      catch (Exception e) {         
	    	  e.printStackTrace();
	    	  return false;
	        }finally{  
	        	Date  end_time = new Date();
			  log.info("======================BASE64:文件复制方式总耗时："+(end_time.getTime() - start_time.getTime())+"毫秒");
	        }  
	 }
    /**
     * 将base64字符解码保存文件
     *
     * @param base64Code
     * @throws Exception
     */
    public static boolean decoderBase64FileTool(String ftpUrl, String base64Code, String newName) {
        Date start_time = new Date();
        try {
			//解密
            byte[] buffer = new BASE64Decoder().decodeBuffer(base64Code);
           // ByteArrayInputStream in = new ByteArrayInputStream(buffer);
			//处理数据
			for (int i = 0; i < buffer.length; ++i) {
				if (buffer[i] < 0) {
					buffer[i] += 256;
				}
			}
			FileOutputStream out = new FileOutputStream(ftpUrl + newName);
			out.write(buffer);
			out.flush();
            //in.close();
            out.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        } finally {
            Date end_time = new Date();
            log.info("==>BASE64:文件复制方式总耗时：" + (end_time.getTime() - start_time.getTime()) / 1000 + "S");
        }
    }
//	 /**
//	  * 将base64字符解码保存文件
//	  * @param base64Code
//	  * @throws Exception
//	  */
//	 public static boolean decoderBase64FileLimit(String ftpUrl,String base64Code,String newName,Map<String,String> map,String userPhone) {
//		 Date   start_time = new Date();
//		 try {
//			  byte[] buffer = new BASE64Decoder().decodeBuffer(base64Code);
//			  ByteArrayInputStream in = new ByteArrayInputStream(buffer);
//			  FileOutputStream out = new FileOutputStream(ftpUrl+newName);
//			  double persent=0;
//				int len = 0;//表示成功读取的字节数的个数搜索
//				byte[] b = new byte[1024];
//				long upFileSize=buffer.length;
////				if(upFileSize>2*1024*1024){
////					//限制 文件大于2M上传
////					return false;
////				}
//				///防止第一次提交。第二次在提交；
//				if(map.get(userPhone)!=null){
//					while(Integer.parseInt(map.get(userPhone))>0)
//					{
//						if(Integer.parseInt(map.get(userPhone))<=0){
//							break ;
//						}
//					}
//				}
//		      while ((len=in.read(b)) != -1){
//			       persent+=len/(double)upFileSize*100D;
//			       if(Math.round(persent)<99){
//					 map.put(userPhone, Math.round(persent)+"");
//			       }
//			       Thread.sleep(10);
//		      }
//		      out.write(buffer);
//
//		      in.close();
//		      out.close();
//		      map.put(userPhone,"99");
//		      return true;
//		 }
//	      catch (Exception e) {
//	    	  e.printStackTrace();
//	    	  return false;
//	      }finally{
//	        	Date  end_time = new Date();
//	        	log.info("======================BASE64:文件复制方式总耗时："+(end_time.getTime() - start_time.getTime())/1000+"S");
//	      }
//	 }

    /**
     * 图片转成二进制
     * @param f
     * @return
     */
    public static String getImageBinary(File f,String prfix){
        BufferedImage bi;
        try {
			BASE64Encoder encoder = new BASE64Encoder();
            bi = ImageIO.read(f);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, prfix, baos);
            byte[] bytes = baos.toByteArray();
            return encoder.encodeBuffer(bytes).trim();
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    public static boolean base64StringToImage(String savePath,String base64String,String pixstr){

    	if(base64String==null)
			return false;
		BASE64Decoder decoder = new BASE64Decoder();
        ByteArrayInputStream bais= null;
        byte[] bytes1 =null;
        try {
			// Base64解码
            bytes1 = decoder.decodeBuffer(base64String);

			bais = new ByteArrayInputStream(bytes1);

            BufferedImage bi1 = ImageIO.read(bais);

            File w2 = new File(savePath);//可以是jpg,png,gif格式

            ImageIO.write(bi1, pixstr, w2);//不管输出什么格式图片，此处不需改动
			return true;
        } catch (IOException e) {
            e.printStackTrace();
			return false;
        }finally {
            try {
                bais.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
            bytes1.clone();
        }
    }
	 /**
	  * 将base64字符保存文本文件
	  * @param base64Code
	  * @param targetPath
	  * @throws Exception
	  */
	 public static void toFile(String base64Code, String targetPath)
	   throws Exception {

	  byte[] buffer = base64Code.getBytes();
	  FileOutputStream out = new FileOutputStream(targetPath);
	  out.write(buffer);
	  out.close();
	 }		 
	 
	 /**
		 * App版本上传
		 * @return
		 */
		public static String getEditionURL(){
			
			return Config.getString("APP_EDITION_DIR");
		}
	/**
	 * ESBFTP文件本地存储地址
	 * @return
	 */
	public static String getEsbFtpUrl(){
		return Config.getString("ESBFTP_URL");
	}
	
	/**
	 * ESBFTP文件下载地址
	 * @return
	 */
	public static String getEsbDowlowndFtpUrl(){
		return Config.getString("ESBDOWLOWNDFTP_URL");
	}
	
	/**
	 * 财务系统文件本地存储地址
	 * @return
	 */
	public static String getFinancialSystemUrl(){
		return Config.getString("FINANCIALSYSTEMFTP_URL");
	}
	/**
	 * 上传文件地址
	 * @return
	 */

	public static String getPaperConURL() { 
		return Config.getString("CONT_PAPER_PDF_URL");
	}


	/**
	 * 同步boss 环节的 txt文件路径
	 * @return
	 */
	public static String getBossSyncTxtUrl(){return Config.getString("BOSS_SYNC_TXT_URL");};


	/**
	 * 获取新建小微集团地址
	 */
	public static String getS3851AppCfmUrl(){
		return Config.getString("S3851APPCFM_URL");
	}

	/**
	 * 请求无纸化系统 URL
	 * @return
	 */
	public static String getEndpointUrl(){
		return Config.getString("ENDPOINT_URL");
	}

	/**
	 *
	 * @return
	 */
	public static String getRequer_Url(){
		return Config.getString("REQUER_URL");
	}

	/**
	 * 正式环境ESB 请求地址
	 * @return
	 */
	public static String getESBWS_Url(){
		return Config.getString("ESBWS_URL");
	}

	/**
	 *测试环境ESB 请求地址
	 * @return
	 */
	public static String getTest_ESBWS_Url(){
		return Config.getString("TEST_ESBWS_URL");
	}
	public static String getQryResourceResult4PreOrderWsdl_Url(){
		return Config.getString("QRYRESOURCERESULT4PREORDERWSDL_URL");
	}
	public static String getStartPreOrderWsdl_Url(){
		return Config.getString("STARTPREORDERWSDL_URL");
	}
	public static String getQryPreDealWsdl_Url(){
		return Config.getString("QRYPREDEALWSDL_URL");
	}

	/**
	 * 合同一致性
	 * @return
	 */
	public static String getContractinfoURL() { 
		return Config.getString("CONTRACTINFO_URL");
	}

	public static String getAuditWorksheet(){
		return Config.getString("AUDITWORKFILE_URL");
	}
	/**
	 * 支付请求地址接口地址：
	 * @return
	 */
	public static  String getPaymentUrl(){
		return Config.getString("PAYMENT_URL");
	}

	/**
	 * 商户号
	 * @return
	 */
	public static String getMerchantNo() {
		return Config.getString("MERCHANT_NO");
	}

	/**
	 *加密KEY
	 * @return
	 */
	public static String getPaymentKey() {
		return Config.getString("KEY");
	}

	/**
	 *支付后台通知地址
	 * @return
	 */
	public static String getPayNotifyUrl() {
		return Config.getString("PAYNOTIFY_URL");
	}

	/**
	 *退款后台通知地址
	 * @return
	 */
	public static String getRefundnotifyUrl() {
		return Config.getString("REFUNDNOTIFY_URL");
	}

	public static Integer getOverTimeDay() {
		return Config.getInt("OVERTIME_DAY");
	}

    /**
     *支付前台通知地址
     * @return
     */
    public static String getPayRetyUrl() {
        return Config.getString("PAYRET_URL");
    }

    /**
     *缴费结果通知地址
     * @return
     */
    public static String getPaybusiNotifyUrl() {
        return Config.getString("BUSINOTIFY_URL");
    }
    /**
     *冲正时退款成功的结果通知地址，通知退款结果
     * @return
     */
    public static String getCancelNotifyUrl() {
        return Config.getString("CANCEL_NOTIFY_URL");
    }
}
