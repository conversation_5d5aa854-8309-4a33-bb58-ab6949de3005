package com.xinxinsoft.utils.common;

import net.coobird.thumbnailator.Thumbnails;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigDecimal;
import java.math.MathContext;
import java.util.ArrayList;

/**
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 * 图片压缩操作工具类
 * @path: com.xinxinsoft.Threads.dmo.ImageHelper
 * @description:
 * @author: WF
 * @date: 2020-04-10 14:19
 **/
public class ImageHelper {
    private static Logger log = Logger.getLogger(ImageHelper.class);

    public static void main(String[] args) {
        //jpg  10M以内 以 scale =0.45
        //jpg 大于10M 以 scale = 0.22

        String path = "C:\\Users\\<USER>\\Desktop\\CS_MAS\\微信图片_20200902100423.jpg";
        ImageHelper.scaleImage(path , "F:\\FTP/4444.jpg", 0.65, "jpg");

        //String pathSize = pathSize(path );
       // System.out.println("获取到图片的大小: " + pathSize);
     //   ImageHelper.scaleImageWithParams("F:\\FTP\\1.jpg", "F:\\FTP\\2222.jpg", 200, 150, true, "jpg");
    }


    /***
     * 按指定的比例缩放图片
     *
     * @param sourceImage 源图片，如：C:/test.jpg
     * @param destImage 目标图片，即压缩后的图片，如：C:/new_test.jpg
     * @param scale 缩放比例，如1.2
     * @param format 图片后缀名
     */
    public static boolean scaleImage(String sourceImage, String destImage, double scale, String format) {

        File file = new File(sourceImage);
        if(file.length()<=0)
             return false;
        BufferedImage bufferedImage=null;
        BufferedImage outputImage=null;
        try {
            bufferedImage = ImageIO.read(file);
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();
            if(file.length()/1024.0 > 2048){  //判断如果文件大于2MB则进行比例压缩
                width = parseDoubleToInt(width * scale);
                height = parseDoubleToInt(height * scale);
            }
            Image image = bufferedImage.getScaledInstance(width, height, Image.SCALE_SMOOTH);
            outputImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
            Graphics graphics = outputImage.getGraphics();
            graphics.drawImage(image, 0, 0, null);
            graphics.dispose();

            ImageIO.write(outputImage, format, new File(destImage));
            return true;

        } catch (IOException e) {
            log.error("按指定的比例缩放图片出错了", e);
            e.printStackTrace();
            return false;
        }finally {
            if(bufferedImage !=null){
                bufferedImage.flush();
            }
            if(outputImage!=null){
                outputImage.flush();
            }

        }

    }


    /**
     * 将double类型的数据转换为int，四舍五入原则
     *
     * @param sourceDouble
     * @return
     */
    private static int parseDoubleToInt(double sourceDouble) {
        int result = 0;
        result = (int) sourceDouble;
        return result;
    }

    /***
     *
     * @param bufferedImage
     *            要缩放的图片对象
     * @param width_scale
     *            要缩放到的宽度
     * @param height_scale
     *            要缩放到的高度
     * @return 一个集合，第一个元素为宽度，第二个元素为高度
     */
    private static ArrayList<Integer> getAutoWidthAndHeight(BufferedImage bufferedImage, int width_scale,
                                                            int height_scale) {
        ArrayList<Integer> arrayList = new ArrayList<Integer>();
        int width = bufferedImage.getWidth();
        int height = bufferedImage.getHeight();
        double scale_w = getDot2Decimal(width_scale, width);

        System.out.println("getAutoWidthAndHeight width=" + width + "scale_w=" + scale_w);
        double scale_h = getDot2Decimal(height_scale, height);
        if (scale_w < scale_h) {
            arrayList.add(parseDoubleToInt(scale_w * width));
            arrayList.add(parseDoubleToInt(scale_w * height));
        } else {
            arrayList.add(parseDoubleToInt(scale_h * width));
            arrayList.add(parseDoubleToInt(scale_h * height));
        }
        return arrayList;

    }

    /***
     * 返回两个数a/b的小数点后三位的表示
     *
     * @param a
     * @param b
     * @return
     */
    private static double getDot2Decimal(int a, int b) {

        BigDecimal bigDecimal_1 = new BigDecimal(a);
        BigDecimal bigDecimal_2 = new BigDecimal(b);
        BigDecimal bigDecimal_result = bigDecimal_1.divide(bigDecimal_2, new MathContext(4));
        Double double1 = new Double(bigDecimal_result.toString());
        return double1;
    }

    /**
     * 获取本地图片的字节数
     * @param imgPath
     * @return
     */
    public static String pathSize(String imgPath) {
        File file = new File(imgPath);
        FileInputStream fis;
        int fileLen = 0;
        try {
            fis = new FileInputStream(file);
            fileLen = fis.available();
        }
        catch (FileNotFoundException e)
        {
            e.printStackTrace();
        } catch (IOException e)
        {
            e.printStackTrace();
        }
        return bytes2kb(fileLen);
    }

    /**
     * 将获取到的字节数转换为KB，MB模式
     * @param bytes
     * @return
     */
    public static String bytes2kb(long bytes){
        BigDecimal filesize = new BigDecimal(bytes);
        BigDecimal megabyte = new BigDecimal(1024 * 1024);
        float returnValue = filesize.divide(megabyte, 2, BigDecimal.ROUND_UP).floatValue();
        if(returnValue > 1)
            return (returnValue + "MB");
        BigDecimal kilobyte = new BigDecimal(1024);
        returnValue = filesize.divide(kilobyte, 2, BigDecimal.ROUND_UP).floatValue();
        return (returnValue + "KB");
    }



}
