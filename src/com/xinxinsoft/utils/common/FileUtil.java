package com.xinxinsoft.utils.common;

import com.aspose.words.Section;
import com.lowagie.text.Table;
import com.xinxinsoft.service.core.json.JSONException;
import com.xinxinsoft.utils.WordUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.poi.xwpf.usermodel.XWPFTableCell.XWPFVertAlign;
import org.apache.xmlbeans.XmlCursor;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Element;
import org.jsoup.select.Elements;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import com.google.common.base.Strings;

import java.io.*;
import java.math.BigInteger;
import java.nio.ByteBuffer;
import java.nio.MappedByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.channels.FileChannel.MapMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class FileUtil {


	/**
	 * 替换word变量
	 * @param srcPath 模板文件路径
	 * @param destPath 创建的文件路径
	 * @param map 要替换的指定变量和内容
	 */
	public static void  seekAndReplace(String srcPath, String destPath, Map<String, String> map) {
		try {
			XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(srcPath));
			/*JSONArray objList=JSONArray.fromObject(map.get("${postage}"));
			document = insertTabByArray("${postage}",objList,document);//插入表格*/
			Iterator<XWPFParagraph> itPara = document.getParagraphsIterator();
			List<XWPFTable> tables = document.getTables();
			while (itPara.hasNext()) {
				XWPFParagraph paragraph1 = (XWPFParagraph) itPara.next();
				int length = paragraph1.getRuns().size();
				if (length > 0) {
					StringBuffer text = new StringBuffer(StringUtils.join(paragraph1.getRuns().toArray()));
					//System.out.println("这是："+text);
					if(!match(text.toString())) continue;
					Set<String> set = map.keySet();
					Iterator<String> iterator = set.iterator();
					 while (iterator.hasNext()){
						 String key = iterator.next();
						 //System.out.println("这是："+key);
						 if(!DealTextKey(key,text.toString())) continue;
						 for (int i = (length - 1); i >= 0; i--){
							 paragraph1.removeRun(i);
						 }
						 DealXWPFRun(paragraph1,text,key,map.get(key));
					 }
				}
			}
			replaceInTable(document,map);
			FileOutputStream outStream = null;
			outStream = new FileOutputStream(new File(destPath));
			document.write(outStream);
			outStream.close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 替换表格里面的变量
	 *
	 * @param doc  要替换的文档
	 * @param params 参数
	 */
	public static void replaceInTable(XWPFDocument doc, Map<String, String> params) throws Exception{
		Iterator<XWPFTable> iterator = doc.getTablesIterator();
		XWPFTable table;
		List<XWPFTableRow> rows;
		List<XWPFTableCell> cells;
		List<XWPFParagraph> paras;
		while (iterator.hasNext()) {
			table = iterator.next();
			rows = table.getRows();
			for (XWPFTableRow row : rows) {
				cells = row.getTableCells();
				for (XWPFTableCell cell : cells) {
					paras = cell.getParagraphs();
					for (XWPFParagraph para : paras) {
						replaceInPara(para,params);
					}
				}
			}
		}
	}
	//占位符 {}
	private static final Pattern SymbolPattern = Pattern.compile(".*\\$\\{(.+?)\\}.*", Pattern.CASE_INSENSITIVE);

	/**
	 * 替换段落里面的变量
	 *
	 * @param para  要替换的段落
	 * @param params 参数
	 */
	public static void  replaceInPara(XWPFParagraph para, Map<String, String> params) {
		List<XWPFRun> runs;
		String text = para.getParagraphText();
		/*Matcher matcher3 = SymbolPattern.matcher(text.trim());
		if(matcher3.find()){
			String group = matcher3.group(1);
			String group2 = StringUtils.replace(para.getParagraphText(),"${"+group+"}",params.get("${"+group+"}"));//matcher3.group(1);
			text =group2;
		}
		runs = para.getRuns();
		if(runs.size()>0){
			String fontFamily = runs.get(0).getFontFamily();
			int fontSize = runs.get(0).getFontSize();
			XWPFRun xwpfRun = para.insertNewRun(0);
			xwpfRun.setFontFamily(fontFamily);
			xwpfRun.setText(text);
			if(fontSize > 0) {
				xwpfRun.setFontSize(fontSize);
			}
			int max = runs.size();
			for (int i = 1; i < max; i++) {
				para.removeRun(1);
			}
		}*/
		Iterator<String> iter = params.keySet().iterator();
		while(iter.hasNext()) {
			String key = iter.next();
			if(text.contains(key)){
				String group2 = text.replace(key,params.get(key));//matcher3.group(1);
				text =group2;
			}
		}
		runs = para.getRuns();
		if(runs.size()>0){
			String fontFamily = runs.get(0).getFontFamily();
			int fontSize = runs.get(0).getFontSize();
			XWPFRun xwpfRun = para.insertNewRun(0);
			xwpfRun.setFontFamily(fontFamily);
			xwpfRun.setText(text);
			if(fontSize > 0) {
				xwpfRun.setFontSize(fontSize);
			}
			int max = runs.size();
			for (int i = 1; i < max; i++) {
				para.removeRun(1);
			}
		}
	}

	private static boolean match(String str) {
		String regex =  ".*\\$\\{(.+?)\\}.*";
		return Pattern.matches(regex, str);
	}


	private static boolean DealTextKey(String key ,String text){
			boolean isKey = false;
			if (text.indexOf(key) >= 0) {
				isKey = true;
			}
		return isKey;
	}

	private static void DealXWPFRun(XWPFParagraph paragraph1,StringBuffer text,String mapKey,String val) throws Exception{
		XWPFRun newRun = paragraph1.insertNewRun(0);
		String[] lines = val.split("/n");
		String text_str = text.toString();
		for (int i=0;i<lines.length;i++){
			text.setLength(0);
			text.append(StringUtils.replace(text_str.toString(),mapKey,lines[i]));
			if(i>0) {
				newRun.addCarriageReturn();//硬回车
				newRun.addTab();
				newRun.setText(lines[i]);
			}else{
				newRun.setText(text.toString(), 0);
			}
			newRun.setFontSize(9);
			//newRun.setColor("ff0066");//设置字体颜色
		   // newRun.setUnderline(UnderlinePatterns.SINGLE);//设置下划线样式以及突出显示文本
		}
	}


	public static void main(String[] args) {
		//String dirName = "d:/LEO/";// 创建目录
		//FileUtil.createDir(dirName);
		//readFile("F:\\FTP\\2019-06\\syncLink.txt");
//		String content = "种事俯拾地芥实际地方sdfsdflksdjfd;";
//
//		String pattern = ".*\\$\\{(.+?)\\}.*";
//
//		boolean isMatch = Pattern.matches(pattern, content);
//		System.out.println(isMatch);
//		System.out.println(match("种事俯拾地芥实际地方${gewgwefwefw}sdfsdflksdjfd;"));

		System.out.println(getFilePix("F:/namd/sdfsdfs.pdf"));
	}

	/**
	 * 创建目录
	 * 
	 * @param destDirName
	 *            目标目录名
	 * @return 目录创建成功返回true，否则返回false
	 */
	public static boolean createDir(String destDirName) {
		File dir = new File(destDirName);
		if (dir.exists()) {
			return false;
		}
		if (!destDirName.endsWith(File.separator)) {
			destDirName = destDirName + File.separator;
		}
		// 创建单个目录
		if (dir.mkdirs()) {
			return true;
		} else {
			return false;
		}
	}

	/**
	 * 判断list中是否有重复元素
	 * @param list
	 * @return
	 */
	  public static boolean hasSame(List<? extends Object> list)  
	    {  
	        if(null == list)  
	            return false;  
	        return list.size() == new HashSet<Object>(list).size();  
	    }  
	/**
	 * 删除文件
	 * 
	 * @param filePathAndName
	 *            String 文件路径及名称 如c:/fqf.txt
	 * @param fileContent
	 *            String
	 * @return boolean
	 */
	public static void delFile(String filePathAndName) {
		try {
			String filePath = filePathAndName;
			filePath = filePath.toString();
			java.io.File myDelFile = new java.io.File(filePath);
			myDelFile.delete();

		} catch (Exception e) {
			System.out.println("删除文件操作出错");
			e.printStackTrace();

		}
	}


	/**
	 * 删除文件
	 *
	 * @param files 文件集合
	 *            String
	 * @return boolean
	 */
	public static void delFiles(List<File> files) {
		try {
			for (File file : files) {
				file.delete();
			}
		} catch (Exception e) {
			System.out.println("删除文件操作出错");
			e.printStackTrace();

		}
	}

	/**
	 * 读取到字节数组0
	 * 
	 * @param filePath //路径
	 * @throws IOException
	 */
	public static byte[] getContent(String filePath) throws IOException {
		File file = new File(filePath);
		long fileSize = file.length();
		if (fileSize > Integer.MAX_VALUE) {
			System.out.println("file too big...");
			return null;
		}
		FileInputStream fi = new FileInputStream(file);
		byte[] buffer = new byte[(int) fileSize];
		int offset = 0;
		int numRead = 0;
		while (offset < buffer.length
				&& (numRead = fi.read(buffer, offset, buffer.length - offset)) >= 0) {
			offset += numRead;
		}
		// 确保所有数据均被读取
		if (offset != buffer.length) {
			throw new IOException("Could not completely read file "
					+ file.getName());
		}
		fi.close();
		return buffer;
	}

	/**
	 * 读取到字节数组1
	 * 
	 * @param filePath
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray(String filePath) throws IOException {

		File f = new File(filePath);
		if (!f.exists()) {
			throw new FileNotFoundException(filePath);
		}
		ByteArrayOutputStream bos = new ByteArrayOutputStream((int) f.length());
		BufferedInputStream in = null;
		try {
			in = new BufferedInputStream(new FileInputStream(f));
			int buf_size = 1024;
			byte[] buffer = new byte[buf_size];
			int len = 0;
			while (-1 != (len = in.read(buffer, 0, buf_size))) {
				bos.write(buffer, 0, len);
			}
			return bos.toByteArray();
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				in.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			bos.close();
		}
	}

	/**
	 * 读取到字节数组2
	 * 
	 * @param filePath
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray2(String filePath) throws IOException {

		File f = new File(filePath);
		if (!f.exists()) {
			throw new FileNotFoundException(filePath);
		}

		FileChannel channel = null;
		FileInputStream fs = null;
		try {
			fs = new FileInputStream(f);
			channel = fs.getChannel();
			ByteBuffer byteBuffer = ByteBuffer.allocate((int) channel.size());
			while ((channel.read(byteBuffer)) > 0) {
				// do nothing
				// System.out.println("reading");
			}
			return byteBuffer.array();
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				channel.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
			try {
				fs.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}

	/**
	 * Mapped File way MappedByteBuffer 可以在处理大文件时，提升性能
	 * 
	 * @param filename
	 * @return
	 * @throws IOException
	 */
	public static byte[] toByteArray3(String filePath) throws IOException {

		FileChannel fc = null;
		RandomAccessFile rf = null;
		try {
			rf = new RandomAccessFile(filePath, "r");
			fc = rf.getChannel();
			MappedByteBuffer byteBuffer = fc.map(MapMode.READ_ONLY, 0,
					fc.size()).load();
			//System.out.println(byteBuffer.isLoaded());
			byte[] result = new byte[(int) fc.size()];
			if (byteBuffer.remaining() > 0) {
				// System.out.println("remain");
				byteBuffer.get(result, 0, byteBuffer.remaining());
			}
			return result;
		} catch (IOException e) {
			e.printStackTrace();
			throw e;
		} finally {
			try {
				rf.close();
				fc.close();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
	//删除指定文件夹下所有文件
	//param path 文件夹完整绝对路径
	   public static boolean delAllFile(String path) {
	       boolean flag = false;
	       File file = new File(path);
	       if (!file.exists()) {
	         return flag;
	       }
	       if (!file.isDirectory()) {
	         return flag;
	       }
	       String[] tempList = file.list();
	       File temp = null;
	       for (int i = 0; i < tempList.length; i++) {
	          if (path.endsWith(File.separator)) {
	             temp = new File(path + tempList[i]);
	          } else {
	              temp = new File(path + File.separator + tempList[i]);
	          }
	          if (temp.isFile()) {
	        	  System.gc();
	             temp.delete();
	          }
	          if (temp.isDirectory()) {
	             delAllFile(path + "/" + tempList[i]);//先删除文件夹里面的文件
	             delFolder(path + "/" + tempList[i]);//再删除空文件夹
	             flag = true;
	          }
	       }
	       return flag;
	     } 
//删除文件夹
//param folderPath 文件夹完整绝对路径

   public static void delFolder(String folderPath) {
	   try {
	      delAllFile(folderPath); //删除完里面所有内容
	      String filePath = folderPath;
	      filePath = filePath.toString();
	      java.io.File myFilePath = new java.io.File(filePath);
	      System.gc();
	      myFilePath.delete(); //删除空文件夹
	   } catch (Exception e) {
	     e.printStackTrace(); 
	   }
	}
	/**
	 * 获取文件名后缀: .xxx
	 * @param name
	 * @return
	 */
	public static String getFilePix(String filename){
		String[] n = filename.split("\\.");
		if(n!=null && n.length>0){
			String pix =n[n.length-1];
			return pix;
		}
		return "";
	}

	/***
	 * 写入文件 txt
	 * @return
	 */
	public static boolean writeTxtFile(String filePath,String content){
		boolean writeIsEnd = true;
		File file  = new File(filePath);
		FileWriter fw = null;
		try{
			if(!file.exists()){
				file.createNewFile();
			}
			fw = new FileWriter(file.getAbsoluteFile());
			BufferedWriter bw = new BufferedWriter(fw);
			bw.write(content);
			bw.close();
		}catch (IOException e){
			e.printStackTrace();
			writeIsEnd = false;
		}finally {
			try{
				fw.close();
			}catch (IOException ioe){
				ioe.printStackTrace();
			}
		}
		return writeIsEnd;

	}

	/**
	 * 读取大文件
	 *
	 * @param filePath
	 */
	public static void readFile(String filePath) {
		File file = new File(filePath);
		BufferedReader reader = null;
		try {
			reader = new BufferedReader(new FileReader(file), 2 * 1024 * 1024);   //如果是读大文件，设置缓存
			String tempString = null;
			while ((tempString = reader.readLine()) != null) {
				System.out.println(tempString);
			}
			reader.close();
		} catch (IOException e) {
			e.printStackTrace();
		} finally {
			if (reader != null) {
				try {
					reader.close();
				} catch (IOException e) {
					e.printStackTrace();
				}
			}
		}
	}

	/**
	 * 向word中写表格
	 * 
	 * <AUTHOR>
	 * @param element
	 * @return
	 * @throws Exception
	 */
	private static void setTable(Element element,XWPFDocument document) throws Exception {
		Document tableDoc = element.ownerDocument();
		Elements trList = tableDoc.getElementsByTag("tr");
		Elements tdList = trList.first().getElementsByTag("td");
		XWPFTable table = document.createTable(trList.size(), tdList.size());
		boolean[][] colspanFlag = new boolean[trList.size()][tdList.size()];
		boolean[][] rowspanFlag = new boolean[trList.size()][tdList.size()];
		for (int row = 0; row < trList.size(); row++) {
			Element tr = trList.get(row);
			Elements tds = tr.getElementsByTag("td");
			for (int col = 0; col < tds.size(); col++) {
				Element td = tds.get(col);
				String colspan = td.attr("colspan");
				String rowspan = td.attr("rowspan");
				String align = td.attr("align");
				String widthStyle = td.attr("width");
				String style = td.attr("style");
				// 记录需要合并的列
				if (!StringUtils.isEmpty(colspan)) {
					int colCount = Integer.parseInt(colspan);
					for (int i = 0; i < colCount - 1; i++) {
						colspanFlag[row][col + i + 1] = true;
					}
				}
				// 记录需要合并的行
				if (!StringUtils.isEmpty(rowspan)) {
					int rowCount = Integer.parseInt(rowspan);
					for (int i = 0; i < rowCount - 1; i++) {
						rowspanFlag[row + i + 1][col] = true;
					}
				}
				// 处理合并
				XWPFTableCell cell = table.getRow(row).getCell(col);
				if (colspanFlag[row][col]) {
					setColMerge(cell, STMerge.CONTINUE);
					continue;
				} else {
					setColMerge(cell, STMerge.RESTART);
				}
				if (rowspanFlag[row][col]) {
					setRowMerge(cell, STMerge.CONTINUE);
					continue;
				} else {
					setRowMerge(cell, STMerge.RESTART);
				}
				// 设置列宽
				if (!Strings.isNullOrEmpty(widthStyle) && !"0".equals(widthStyle)) {
					int width = Integer.parseInt(widthStyle);

					setWidth(cell, width);
				}

				XWPFParagraph paragraph = null;
				int size = cell.getParagraphs().size();
				if (size == 0) {
					paragraph = cell.addParagraph();
				} else {
					paragraph = cell.getParagraphs().get(size - 1);
				}
				// 设置水平样式
				if ("CENTER".equalsIgnoreCase(align)) {
					paragraph.setAlignment(ParagraphAlignment.CENTER);
				} else if ("LEFT".equalsIgnoreCase(align)) {
					paragraph.setAlignment(ParagraphAlignment.LEFT);
				}
				// 设置垂直居中
				cell.setVerticalAlignment(XWPFVertAlign.CENTER);
				
				// 设置没有边框
				if (!style.contains("border:")) {
					setNotBorder(cell);
				}
				//XWPFRun run = paragraph.insertNewRun(0);
				XWPFRun run = paragraph.createRun();
				run.setText(td.text());
			}
		}
	}

	/**
	 * 设置行合并属性
	 * 
	 * <AUTHOR>
	 * @date 2019-05-31 14:08:02
	 * @param tableCell
	 * @param mergeVlaue
	 */
	private static void setRowMerge(XWPFTableCell tableCell, STMerge.Enum mergeVlaue) {
		CTTc ctTc = tableCell.getCTTc();
		CTTcPr cpr = ctTc.isSetTcPr() ? ctTc.getTcPr() : ctTc.addNewTcPr();
		CTVMerge merge = cpr.isSetVMerge() ? cpr.getVMerge() : cpr.addNewVMerge();
		merge.setVal(mergeVlaue);
	}

	/**
	 * 设置列合并属性
	 * 
	 * <AUTHOR>
	 * @date 2019-05-31 14:07:50
	 * @param tableCell
	 * @param mergeVlaue
	 */
	private static void setColMerge(XWPFTableCell tableCell, STMerge.Enum mergeVlaue) {
		CTTc ctTc = tableCell.getCTTc();
		CTTcPr cpr = ctTc.isSetTcPr() ? ctTc.getTcPr() : ctTc.addNewTcPr();
		CTHMerge merge = cpr.isSetHMerge() ? cpr.getHMerge() : cpr.addNewHMerge();
		merge.setVal(mergeVlaue);
	}

	/**
	 * 补全表格
	 * 
	 * <AUTHOR>
	 * @date 2019-05-31 13:32:52
	 * @param tableHtml
	 * @return
	 */
	private static Element supplementTable(String tableHtml) {
		Document tableDoc = Jsoup.parse(tableHtml);
		Elements trels = tableDoc.getElementsByTag("tr");
		// 补全合并的列
		supplementMergedColumns(trels);
		// 补全合并的行
		supplementMergedRows(trels);
		return tableDoc.getElementsByTag("table").first();
	}

	/**
	 * 补全合并的列
	 * 
	 * <AUTHOR>
	 * @date 2019-05-31 11:57:36
	 * @param trels
	 */
	private static void supplementMergedColumns(Elements trels) {
		// 所有tr
		Iterator<Element> trelIter = trels.iterator();
		while (trelIter.hasNext()) {
			Element trel = trelIter.next();
			// 获取所有td
			Elements tdels = trel.getElementsByTag("td");
			Iterator<Element> tdelIter = tdels.iterator();
			while (tdelIter.hasNext()) {
				Element tdel = tdelIter.next();
				// 删除样式
				tdel.removeAttr("class");
				// 取到合并的列数量
				String colspanIndex = tdel.attr("colspan");
				if (StringUtils.isEmpty(colspanIndex)) {
					continue;
				}
				Integer colspanVal = Integer.parseInt(colspanIndex);
				for (int i = 1; i < colspanVal; i++) {
					trel.appendElement("td");
				}
			}
		}
	}

	/**
	 * 补全合并的行（调用此方法前 需要调用 “补全合并的列”方法）
	 * 
	 * <AUTHOR>
	 * @date 2019-05-31 11:57:47
	 * @param trels
	 */
	private static void supplementMergedRows(Elements trels) {
		// 获取最大的列
		int tdSize = 0;
		Iterator<Element> iterator = trels.iterator();
		while (iterator.hasNext()) {
			Element element = iterator.next();
			int size = element.getElementsByTag("td").size();
			if (size > tdSize) {
				tdSize = size;
			}
		}

		for (int i = 0; i < trels.size(); i++) {
			Element currTr = trels.get(i);
			int currTrTds = currTr.getElementsByTag("td").size();
			if (currTrTds == tdSize) {
				continue;
			}

			int count = tdSize - currTrTds;
			for (int j = 0; j < count; j++) {
				currTr.appendElement("td");
			}
		}
	}

	/**
	 * 设置列宽
	 * 
	 * <AUTHOR>
	 * @date 2019-06-28 11:30:22
	 * @param cell
	 * @param width
	 */
	private static void setWidth(XWPFTableCell cell, int width) {
		CTTblWidth ctTblWidth = cell.getCTTc().addNewTcPr().addNewTcW();
		// 此处乘以20是我以最接近A4上创建表格的宽度手动设置的
		// 目前没有找到将px转换为word里单位的方式
		ctTblWidth.setW(BigInteger.valueOf(width).multiply(BigInteger.valueOf(20)));
		ctTblWidth.setType(STTblWidth.DXA);
	}

	/**
	 * 设置表格为没有边框线
	 * 
	 * <AUTHOR>
	 * @date 2019-06-28 11:33:48
	 * @param cell
	 */
	private static void setNotBorder(XWPFTableCell cell) {
		CTTcBorders cTTcBorders = cell.getCTTc().getTcPr().addNewTcBorders();
		CTBorder clBorder = cTTcBorders.addNewLeft();
		clBorder.setVal(STBorder.Enum.forString("none"));
		clBorder.setShadow(STOnOff.ON);
		CTBorder crBorder = cTTcBorders.addNewRight();
		crBorder.setVal(STBorder.Enum.forString("none"));
		crBorder.setShadow(STOnOff.ON);
		CTBorder cbBorder = cTTcBorders.addNewBottom();
		cbBorder.setVal(STBorder.Enum.forString("none"));
		cbBorder.setShadow(STOnOff.ON);
		CTBorder ctBorder = cTTcBorders.addNewTop();
		ctBorder.setVal(STBorder.Enum.forString("none"));
		ctBorder.setShadow(STOnOff.ON);
	}

	/**
	 * 在定位的位置插入表格；
	 * @param key 定位的变量值
	 * @param doc 需要替换的DOC
	 */
	public static XWPFDocument insertTabByArray(String key, JSONArray tableList,XWPFDocument doc) throws JSONException {
		List<XWPFParagraph> paragraphList = doc.getParagraphs();  //获取doc所有段落
		if (paragraphList != null && paragraphList.size() > 0) {
			for (XWPFParagraph paragraph : paragraphList) {
				List<XWPFRun> runs = paragraph.getRuns();
				for (int i = 0; i < runs.size(); i++) {
					StringBuffer text = new StringBuffer(StringUtils.join(runs.toArray()));
					if (text.toString() != null) {
						if (text.indexOf(key) >= 0) {
							runs.get(i).setText(text.toString().replace(key, ""), 0);
							for (int j = 0; j < tableList.size(); j++) {
								XmlCursor cursor = paragraph.getCTP().newCursor(); //从段落中获取光标
								cursor.toNextSibling();   //将光标移动到下一个段落 ，因为光标生成是在段落的最开始位置 所以需要向下移动一个Xml的光标，也就是将光标移动到下一个段落之前，
								// 在指定游标位置插入表格
								XWPFTable table = doc.insertNewTbl(cursor);
								table.getRow(0).getCell(0).setText(tableList.getJSONObject(j).getString("PROD_NAME"));
								//高度
								/*CTTblPr tblPr = table.getCTTbl().getTblPr();
								tblPr.getTblW().setType(STTblWidth.DXA);
								tblPr.getTblW().setW(BigInteger.valueOf(8500));*/
								//宽度
								/*CTTblPr tablePr = table.getCTTbl().getTblPr();
								CTTblWidth width = tablePr.addNewTblW();
								width.setW(BigInteger.valueOf(8500));*/
								setTableContext(table,tableList.getJSONObject(j));

							}
							break;
						}
					}
				}
			}
		}
		return doc;
	}

	public static void setTableContext(XWPFTable table, JSONObject obj) throws JSONException {
		JSONArray topList=obj.getJSONArray("TITLE");
		for (int i = 0; i < topList.size(); i++) {
			SetTitle(table, i, i+1, topList.getJSONArray(i));
		}
		JSONArray rowItem=obj.getJSONArray("ROWITEM");
		for (int i = 0; i <rowItem.size(); i++) {
			WordUtil.getInstance().insertRow(table,i+1,i+2);
			JSONArray colValue=rowItem.getJSONArray(i);
			for (int j = 0; j <colValue.size() ; j++) {  //获取table行并对table进行赋值
				table.getRow(colValue.getJSONObject(j).getInt("NOWROW")+1).getCell(colValue.getJSONObject(j).getInt("NOWCOL")).setText(colValue.getJSONObject(j).get("VALUE").toString());
			}
		}

		for (int i = 0; i < rowItem.size(); i++) {
			JSONArray colValue=rowItem.getJSONArray(i);
			for (int j = 0; j <colValue.size() ; j++) {
				if(!"0".equals(colValue.getJSONObject(j).get("RSPAN"))){
					WordUtil.getInstance().mergeCellsVertically(table,colValue.getJSONObject(j).getInt("NOWCOL"),colValue.getJSONObject(i).getInt("NOWROW")+1,colValue.getJSONObject(j).getInt("RSPAN")+colValue.getJSONObject(j).getInt("NOWROW"));
				}
				if(!"0".equals(colValue.getJSONObject(i).get("CSPAN"))){
					WordUtil.getInstance().mergeCellsHorizontal(table,colValue.getJSONObject(i).getInt("NOWROW"),colValue.getJSONObject(i).getInt("NOWCOL"),colValue.getJSONObject(i).getInt("CSPAN")+colValue.getJSONObject(i).getInt("NOWCOL"));
				}
			}
		}

	}

	/**
	 * 设置table头部
	 * @param table
	 * @param fromRowIndex 起始行
	 * @param newrowIndex 新增行位置
	 * @param colValue 头部值
	 * @throws JSONException
	 */
	public static void SetTitle(XWPFTable table, int fromRowIndex,int newrowIndex, JSONArray colValue) throws JSONException {
		// 在表格中指定的位置新增一行
		XWPFTableRow targetRow = table.insertNewTableRow(newrowIndex);
		// 获取第一条
		XWPFTableRow topRow = table.getRow(fromRowIndex);
		//当为table的第二行时
		if(fromRowIndex==0){
			//设置第一行合并列
			XWPFTableCell topCell=topRow.getCell(0);
			// int celltop=topRow.getTableCells().size()-1;
			if (topCell.getCTTc().getTcPr() == null)
				topCell.getCTTc().addNewTcPr();
			if (topCell.getCTTc().getTcPr().getGridSpan() == null)
				topCell.getCTTc().getTcPr().addNewGridSpan();
			topCell.getCTTc().getTcPr().getGridSpan().setVal(BigInteger.valueOf(colValue.size()));
		}
		//新增CELL
		for (int i = 0; i < colValue.size(); i++) {
			XWPFTableCell copyCell = targetRow.addNewTableCell();
			copyCell.setText(colValue.getJSONObject(i).get("VALUE").toString());
		}

	}

}