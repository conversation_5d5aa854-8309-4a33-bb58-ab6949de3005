package com.xinxinsoft.utils.common;

import java.text.SimpleDateFormat;
import java.util.Date;

public class DataConvert {


    public static Integer stringConvertInteger(String value) {
        return value != null && !"".equals(value) ? Integer.valueOf(value) : null;
    }

    public static Float stringConvertStrArray(String value) {
        return value != null && !"".equals(value) ? Float.valueOf(value) : null;
    }

    public static Boolean stringConvertBolean(String value) {
        return value != null && !"".equals(value) ? Boolean.valueOf(value) : null;
    }

    public static Date stringConvertUtilDate(String value) throws Exception {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        return format.parse(value);

    }

    public static java.sql.Date stringConvertSqlDate(String value) throws Exception {
        return java.sql.Date.valueOf(value);
    }

    public static Double stringConvertDouble(String value) {
        return value != null && !"".equals(value) ? Double.valueOf(value) : null;
    }
    public static String stringConvertStrArray(String[] value) {
        String message = "";
        if (value != null && value.length > 0) {
            for (int i = 0; i < value.length; i++) {
                String s = value[i];
                message += s;
                if (i < value.length - 1) {
                    message += "";
                }
                return message;
            }
        }

        return message;
    }


}







