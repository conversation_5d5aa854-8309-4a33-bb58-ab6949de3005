package com.xinxinsoft.utils.word;

import org.apache.poi.xwpf.usermodel.XWPFRun;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableCell;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import java.util.*;

/**
 * @TODO：
 * @Author: Leo
 * @Date: 2022/10/4 10:14
 */
public class wordTest {
    public static void main(String[] args) {

        // 模板全的路径
        String templatePaht = "F:\\FTP\\znzw.docx";
        // 输出位置
        String outPath = "F:\\FTP\\outznzw.docx";

        //替换关键字
        Map<String, Object> paramMap = new HashMap<>();
        paramMap.put("contractId","CD2022222154561");
        paramMap.put("name","张三");
        paramMap.put("username","张三");
        paramMap.put("tel","15822222222");
        paramMap.put("stime","2022-10-04");
        paramMap.put("etime","2025-10-04");
        // 表格中的参数示例 参数数据结构 [[str]]
        List<List<String>> tbRow1 = new ArrayList<>();

        //模拟前端以传值：[table:{key:value},{key:value},{key:value}]
        String test="智享套餐VIP0|1、模块一|分类2|分类3|分类4|分类5|分类6";
        String[] list=test.split("\\|");
        List<String> tbRow1_row1 = Arrays.asList(list);

        //List<String> tbRow1_row1 = new ArrayList<>(Arrays.asList("智享套餐VIP0", "1、模块一", "分类2", "分类3", "分类4", "分类5", "分类6"));
        List<String> tbRow1_row2 = new ArrayList<>(Arrays.asList("智享套餐VIP0", "2、模块二", "分类2", "分类3", "分类4", "分类5", "分类6"));
        List<String> tbRow1_row3 = new ArrayList<>(Arrays.asList("智享套餐VIP1", "3、模块三", "分类2", "分类3", "分类4", "分类5", "分类6"));
        List<String> tbRow1_row4 = new ArrayList<>(Arrays.asList("智享套餐VIP2", "2、模块二", "分类2", "分类3", "分类4", "分类5", "分类6"));
        List<String> tbRow1_row5 = new ArrayList<>(Arrays.asList("智享套餐VIP2", "5、模块五", "分类2", "分类3", "分类4", "分类5", "分类6"));
        tbRow1.add(tbRow1_row1);
        tbRow1.add(tbRow1_row2);
        tbRow1.add(tbRow1_row3);
        tbRow1.add(tbRow1_row4);
        tbRow1.add(tbRow1_row5);
        paramMap.put(PoiWordUtils.addRowText + "tb", tbRow1);

        // 图片占位符示例 ${image:imageid} 比如 ${image:image0}, ImageEntity中的值就为image:image0
        // 段落中的图片
        ImageEntity imgEntity1 = new ImageEntity();
        imgEntity1.setHeight(30);
        imgEntity1.setWidth(80);
        imgEntity1.setUrl("F:\\FTP\\logo.jpg");
        imgEntity1.setTypeId(ImageUtils.ImageType.JPG);
        paramMap.put("image:image0", imgEntity1);


        DynWordUtils.process(paramMap,new int[]{0}, templatePaht, outPath);
    }
    public void copy(XWPFTable table, XWPFTableRow sourceRow, int rowIndex){
        //在表格指定位置新增一行
        XWPFTableRow targetRow = table.insertNewTableRow(rowIndex);
        //复制行属性
        targetRow.getCtRow().setTrPr(sourceRow.getCtRow().getTrPr());
        List<XWPFTableCell> cellList = sourceRow.getTableCells();
        if (null == cellList) {
            return;
        }
        //复制列及其属性和内容
        XWPFTableCell targetCell = null;
        for (XWPFTableCell sourceCell : cellList) {
            targetCell = targetRow.addNewTableCell();
            //列属性
            targetCell.getCTTc().setTcPr(sourceCell.getCTTc().getTcPr());
            //段落属性
            if(sourceCell.getParagraphs()!=null&&sourceCell.getParagraphs().size()>0){
                targetCell.getParagraphs().get(0).getCTP().setPPr(sourceCell.getParagraphs().get(0).getCTP().getPPr());
                if(sourceCell.getParagraphs().get(0).getRuns()!=null&&sourceCell.getParagraphs().get(0).getRuns().size()>0){
                    XWPFRun cellR = targetCell.getParagraphs().get(0).createRun();
                    cellR.setText(sourceCell.getText());
                    cellR.setBold(sourceCell.getParagraphs().get(0).getRuns().get(0).isBold());
                }else{
                    targetCell.setText(sourceCell.getText());
                }
            }else{
                targetCell.setText(sourceCell.getText());
            }
        }
    }
}
