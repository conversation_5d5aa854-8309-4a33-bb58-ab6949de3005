package com.xinxinsoft.utils;

import com.xinxinsoft.service.core.json.JSONException;
import net.sf.json.JSONArray;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.math.BigInteger;
import java.util.List;

/**
 * @version v1.0
 * @ProjectName: EOM
 * @ClassName: WordUtil
 * @Description: TODO(利用POI操作word的工具类)
 * @Author: Leo
 * @Date: 2020/8/16 17:22
 */
public class WordUtil {

    private  static  WordUtil wordUtil=null;
    private WordUtil(){}
    public static  WordUtil getInstance(){
        synchronized (WordUtil.class) {
            if(wordUtil==null){
                wordUtil=new WordUtil();
            }
        }
        return wordUtil;
    }



    public void SetRows(XWPFTable table, int fromRowIndex,int newrowIndex, JSONArray colValue) throws JSONException {
        for (int i = 0; i < colValue.size(); i++) {
            // 在表格中指定的位置新增一行
            XWPFTableRow targetRow = table.insertNewTableRow(newrowIndex);
            // 获取第一条
            XWPFTableRow topRow = table.getRow(fromRowIndex);
            for (int j = 0; j <  topRow.getTableCells().size(); j++) {
                XWPFTableCell copyCell = targetRow.addNewTableCell();
            }
        }
        //新增CELL
        for (int i = 0; i < colValue.size(); i++) {
            if(!"0".equals(colValue.getJSONObject(i).get("RSPAN"))){
                System.out.println(colValue.getJSONObject(i).get("NOWCOL")+"____"+colValue.getJSONObject(i).get("NOWROW"));
                WordUtil.getInstance().mergeCellsVertically(table,colValue.getJSONObject(i).getInt("NOWCOL"),colValue.getJSONObject(i).getInt("NOWROW"),colValue.getJSONObject(i).getInt("RSPAN")+colValue.getJSONObject(i).getInt("NOWROW"));
            }
            if(!"0".equals(colValue.getJSONObject(i).get("CSPAN"))){
                WordUtil.getInstance().mergeCellsHorizontal(table,colValue.getJSONObject(i).getInt("NOWROW"),colValue.getJSONObject(i).getInt("NOWCOL"),colValue.getJSONObject(i).getInt("CSPAN")+colValue.getJSONObject(i).getInt("NOWCOL"));
            }
        }

    }
    /**
     * insertRow 在word表格中指定位置插入一行，并将某一行的样式复制到新增行
     * @param fromRowIndex 需要复制的行位置
     * @param newrowIndex 需要新增一行的位置
     * @param listValue 对应的值
     * */
    public void insertRow(XWPFTable table, int fromRowIndex,int newrowIndex, List<String> listValue){
        // 在表格中指定的位置新增一行
        XWPFTableRow targetRow = table.insertNewTableRow(newrowIndex);
        // 获取第一条
        XWPFTableRow topRow = table.getRow(fromRowIndex);
        //当为table的第二行时
        if(fromRowIndex==0){
            //设置第一行合并列
            XWPFTableCell topCell=topRow.getCell(0);
            // int celltop=topRow.getTableCells().size()-1;
            if (topCell.getCTTc().getTcPr() == null)
                topCell.getCTTc().addNewTcPr();
            if (topCell.getCTTc().getTcPr().getGridSpan() == null)
                topCell.getCTTc().getTcPr().addNewGridSpan();
            topCell.getCTTc().getTcPr().getGridSpan().setVal(BigInteger.valueOf(listValue.size()));
        }

        //新增CELL
        for (int i = 0; i < listValue.size(); i++) {
            XWPFTableCell copyCell = targetRow.addNewTableCell();
            copyCell.setText(listValue.get(i));
        }
    }
    /**
     * insertRow 在word表格中指定位置插入一行，并将某一行的样式复制到新增行
     * @param copyrowIndex 需要复制的行位置
     * @param newrowIndex 需要新增一行的位置
     * */
    public void insertRow(XWPFTable table, int copyrowIndex, int newrowIndex) {
        // 在表格中指定的位置新增一行
        XWPFTableRow targetRow = table.insertNewTableRow(newrowIndex);
        // 获取需要复制行对象
        XWPFTableRow copyRow = table.getRow(copyrowIndex);
        //复制行对象
        targetRow.getCtRow().setTrPr(copyRow.getCtRow().getTrPr());
        //或许需要复制的行的列
        List<XWPFTableCell> copyCells = copyRow.getTableCells();
        //复制列对象
        XWPFTableCell targetCell = null;
        for (int i = 0; i < copyCells.size(); i++) {
            XWPFTableCell copyCell = copyCells.get(i);
            targetCell = targetRow.addNewTableCell();
            targetCell.getCTTc().setTcPr(copyCell.getCTTc().getTcPr());
            if (copyCell.getParagraphs() != null && copyCell.getParagraphs().size() > 0) {
                targetCell.getParagraphs().get(0).getCTP().setPPr(copyCell.getParagraphs().get(0).getCTP().getPPr());
                if (copyCell.getParagraphs().get(0).getRuns() != null
                        && copyCell.getParagraphs().get(0).getRuns().size() > 0) {
                    XWPFRun cellR = targetCell.getParagraphs().get(0).createRun();
                    cellR.setBold(copyCell.getParagraphs().get(0).getRuns().get(0).isBold());
                }
            }
        }

    }
    /**
     *  word跨列合并单元格
     * @param table table对象
     * @param row 需要合并的行
     * @param fromCell 从几列开始
     * @param toCell 到第几列结束
     */
    public   void mergeCellsHorizontal(XWPFTable table, int row, int fromCell, int toCell) {
        for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(cellIndex);
            if ( cellIndex == fromCell ) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * word跨行合并单元格
     * @param table table对象
     * @param col 需要合并的列
     * @param fromRow 从第几行开始
     * @param toRow  到第几行结束
     */
    public  void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            if ( rowIndex == fromRow ) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }
    /**
     * 在定位的位置插入表格；
     * @param key 定位的变量值
     * @param doc 需要替换的DOC
     */
    public void insertTab(String key, XWPFDocument doc) {
        List<XWPFParagraph> paragraphList = doc.getParagraphs();
        if (paragraphList != null && paragraphList.size() > 0) {
            for (XWPFParagraph paragraph : paragraphList) {
                List<XWPFRun> runs = paragraph.getRuns();
                for (int i = 0; i < runs.size(); i++) {
                    String text = runs.get(i).getText(0).trim();
                    if (text != null) {
                        if (text.indexOf(key) >= 0) {
                            runs.get(i).setText(text.replace(key, ""), 0);
                            XmlCursor cursor = paragraph.getCTP().newCursor();
                            // 在指定游标位置插入表格
                            XWPFTable table = doc.insertNewTbl(cursor);
                            CTTblPr tablePr = table.getCTTbl().getTblPr();
                            CTTblWidth width = tablePr.addNewTblW();
                            width.setW(BigInteger.valueOf(8500));
                            break;
                        }
                    }
                }
            }
        }
    }
    /**
     * 设置单元格水平位置和垂直位置
     *
     * @param xwpfTable
     * @param verticalLoction    单元格中内容垂直上TOP，下BOTTOM，居中CENTER，BOTH两端对齐
     * @param horizontalLocation 单元格中内容水平居中center,left居左，right居右，both两端对齐
     */
    public void setCellLocation(XWPFTable xwpfTable, String verticalLoction, String horizontalLocation) {
        List<XWPFTableRow> rows = xwpfTable.getRows();
        for (XWPFTableRow row : rows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                CTTc cttc = cell.getCTTc();
                CTP ctp = cttc.getPList().get(0);
                CTPPr ctppr = ctp.getPPr();
                if (ctppr == null) {
                    ctppr = ctp.addNewPPr();
                }
                CTJc ctjc = ctppr.getJc();
                if (ctjc == null) {
                    ctjc = ctppr.addNewJc();
                }
                ctjc.setVal(STJc.Enum.forString(horizontalLocation)); //水平居中
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.valueOf(verticalLoction));//垂直居中
            }
        }
    }

    /**
     * 设置表格位置
     *
     * @param xwpfTable
     * @param location  整个表格居中center,left居左，right居右，both两端对齐
     */
    public  void setTableLocation(XWPFTable xwpfTable, String location) {
        CTTbl cttbl = xwpfTable.getCTTbl();
        CTTblPr tblpr = cttbl.getTblPr() == null ? cttbl.addNewTblPr() : cttbl.getTblPr();
        CTJc cTJc = tblpr.addNewJc();
        cTJc.setVal(STJc.Enum.forString(location));
    }
    /**
     * 在定位的位置插入表格；
     * @param key 定位的变量值
     * @param doc 需要替换的DOC
     */
    public void insertText(String key, String value,String splitKey,XWPFDocument doc) {
        List<XWPFParagraph> paragraphList = doc.getParagraphs();
        if (paragraphList != null && paragraphList.size() > 0) {
            for (XWPFParagraph paragraph : paragraphList) {
                List<XWPFRun> runs = paragraph.getRuns();
                for (int i = 0; i < runs.size(); i++) {
                    String text = runs.get(i).getText(0).trim();
                    if (text != null) {
                        if (text.indexOf(key) >= 0) {
                            runs.get(i).setText(text.replace(key, ""), 0);
                            //分段显示的情况
                            String[] values = value.split(splitKey);
                            if(values.length > 1) {
                                for (int j = 0; j < values.length; j++) {
                                    //存在分段则新建一个run
                                    XWPFRun newrun = paragraph.insertNewRun(j);
                                    //copy样式
                                    newrun.getCTR().setRPr(runs.get(i).getCTR().getRPr());
                                    //换行
                                    newrun.addBreak();
                                    //缩进
                                    newrun.addTab();
                                    newrun.setText(values[j]+"\r\n");
                                }
                                break;
                            }else{
                                runs.get(i).setText(text.replace(key, value), 0);
                            }

                        }
                    }
                }
            }
        }
    }
}
