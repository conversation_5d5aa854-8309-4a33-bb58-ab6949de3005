package com.xinxinsoft.utils;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


import java.security.Key;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;

/**
 * 单点登录DES加密\解密工具类
 */
public class EncryptionUtils {

	private static Logger logger= LoggerFactory.getLogger(EncryptionUtils.class);
	/**
	 * 数据库大字段加密
	 */
	public static final String KEY_MW= "<![CDATA[KEY_DATABASE_LARGE_FIELD_ENCRYPTION]]>";
	
	public static String byteArr2HexStr(byte[] arrB) throws Exception {
		int iLen = arrB.length;
		StringBuffer sb = new StringBuffer(iLen * 2);
		for (int i = 0; i < iLen; ++i) {
			int intTmp = arrB[i];

			while (intTmp < 0) {
				intTmp += 256;
			}

			if (intTmp < 16) {
				sb.append("0");
			}
			sb.append(Integer.toString(intTmp, 16));
		}
		return sb.toString();
	}

	public static byte[] hexStr2ByteArr(String strIn) throws Exception {
		byte[] arrB = strIn.getBytes();
		int iLen = arrB.length;

		byte[] arrOut = new byte[iLen / 2];
		for (int i = 0; i < iLen; i += 2) {
			String strTmp = new String(arrB, i, 2);
			arrOut[(i / 2)] = (byte) Integer.parseInt(strTmp, 16);
		}
		return arrOut;
	}
	
	/**
	 * 解密
	 * @param source
	 * @return
	 */
	public static String decrypt(String source) {
		String key = "fkdslajfkd4353252432sajfkjd54325saljdfjdsl4352kajfls";
		if ((source == null) || (key == null)) {
			return source;
		}
		String rtn = null;
		try {
			Key secKey = getKey(key.getBytes());
			Cipher decryptCipher = Cipher.getInstance("DES");
			decryptCipher.init(2, secKey);
			rtn = new String(decryptCipher.doFinal(hexStr2ByteArr(source)));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rtn;
	}

	/**
	 * 加密
	 * @param source 需要加密的内容
	 * @return
	 */
	public static String encrypt(String source) {
		String key = "fkdslajfkd4353252432sajfkjd54325saljdfjdsl4352kajfls";
		if ((source == null) || (key == null)) {
			return source;
		}
		String rtn = null;
		try {
			Key secKey = getKey(key.getBytes());
			Cipher encryptCipher = Cipher.getInstance("DES");
			encryptCipher.init(1, secKey);
			rtn = byteArr2HexStr(encryptCipher.doFinal(source.getBytes()));
		} catch (Exception e) {
			e.printStackTrace();
		}
		return rtn;
	}

	public static Key getKey(byte[] arrBTmp) throws Exception {
		byte[] arrB = new byte[8];

		for (int i = 0; (i < arrBTmp.length) && (i < arrB.length); ++i) {
			arrB[i] = arrBTmp[i];
		}

		Key key = new SecretKeySpec(arrB, "DES");
		return key;
	}

 
	public static  void main(String[] args) throws Exception { //wangfei_nc


	   // String[] s = "S_0_SVR_SDD".split("S_0_");
       // System.out.println(s[1]);

       // System.out.println(System.currentTimeMillis());
       // System.out.println(UnitBase64Util.decrypt("566A6Ziz5biC5pa95a626ZWH5bmz5oGv5Lmh"));
//        System.out.println("SGS20200506165452344".substring("SGS20200506165452344".length()-17,"SGS20200506165452344".length()));
//        System.out.println("成都市金牛区金房水韵天府小区2栋5单元7、8、9、10、11、12F(扩容)".length());
//		System.out.println(""+MD5.MD5("a=123&b=deng"));
//		System.out.println(""+MD5.MD5("b=deng&a=123"));
//		String encryptString = EncryptionUtils.encrypt("{\"cutomDetailAddress\":\"\",\"accessAdd\":\"\",\"customContact\":\"\",\"bandwidth\":\"\",\"cellName\":\"\",\"cusAppServIPAddNum\":\"\",\"customContactPhone\":\"\",\"customCode\":\"\",\"contract_no\":\"\",\"bill_cycle\":\"\",\"service_no\":\"\",\"taxpayerId\":\"\"}");//,"pay_userno":"18228007600"

        System.out.println("手机号=====>"+EncryptionUtils.encrypt("13890022965"));
		System.out.println("4A账户=====>"+EncryptionUtils.encrypt("lixuemei161"));
//		System.out.println("加密后：" + encryptString);
//		System.out.println("解密后：" + EncryptionUtils.decrypt("a2db02d812affd36e9a76882b58382a1"));
//		 System.out.println(EncryptionUtils.KEY_MW+MD5.MD5(EncryptionUtils.KEY_MW).toUpperCase());


   /*     System.out.println("SGS20200506165452344".substring("SGS20200506165452344".length()-17,"SGS20200506165452344".length()));
        System.out.println("成都市金牛区金房水韵天府小区2栋5单元7、8、9、10、11、12F(扩容)".length());
		System.out.println(""+MD5.MD5("a=123&b=deng"));
		System.out.println(""+MD5.MD5("b=deng&a=123"));*/
//		String encryptString = EncryptionUtils.encrypt("{\"cutomDetailAddress\":\"\",\"accessAdd\":\"\",\"customContact\":\"王雄金\",\"bandwidth\":\"\",\"cellName\":\"\",\"cusAppServIPAddNum\":\"\",\"customContactPhone\":\"15888888888\",\"customCode\":\"2801172543\"}");
//       System.out.println("加密后：" + encryptString);
		//String ed="57528c3f0d61f800d6f9e53eb468ed486d08aafcedcfb3d568dc9d83bfd736f01608b0b508db88aa0e33fc56afece169e3e07b8ae0be49ec1bccf36ac95c1b9ab13a41ed34c776638f92c002c0679e8d41440377e532d4f4f51696cf3998de56b065adbb65f709fe572f63d96a28d128d8c66498308de77132f9749dcc3b8500d336d65ebf61ec91489c0d9acfbe5fbaad123468a4d4609a1081a5c36524f90e655e7ef7010936e4f3ea086a5b5f06c67055d03b0dfc5eb16d221a9b501c54e6407998b49bf08191";
		//System.out.println(EncryptionUtils.decrypt(ed));
      //  System.out.println("=====>"+EncryptionUtils.encrypt("chenshunning"));

		//System.out.println("解密后：" + EncryptionUtils.decrypt(ec));
//		 System.out.println(EncryptionUtils.KEY_MW+MD5.MD5(EncryptionUtils.KEY_MW).toUpperCase());

		//-----用于解密下单的json数据
		//String ec="eJytlcFq20AQhl8l0TmE2VntShtKQZbWxdTYxrEDwRg9SMihDSWltDRtA2kPORRKKe0hRxeH9GmqOHqLrsBRDv13m0KED0Yzn/7R/LOjg2g0HhbTfFL2Bt1htDM7iMZ2d9qflHtZf2qjnUgIoubHOo62muxR2Sua++QuF0xJR4dbf2PrsIk1xjhF2PXLk/roqvr5vD5/v/r1qnq9rL58r94c3ywubj6f1c8+rK7O6tMX1WIhRfV28Xt5Wh+/W/34hDRMHDPSYKNJwqocIR+yKtMVHpkYyUiplDGpB1EI2fAki5AniYIalHACMcbZqYG9Ov9WXS49owL9EDIhQ5zG7oEYw6aEhGB/H9hGJwM9QYWtD5GW/9eBFoMdkJpomwghzBhpJ0SRZ3KgqXfhxIMFl0BisLOJgUtgHW4uhDVgWE151OBUtE+VHjVJQcx4sDTUEsUxNECxhDN1h0kP9g81eOIdFugks9Do3Zr7sCXXHy/w+DIszpsuoU/VyVeYnih8pm5d96wvbcI7UniGSOBztS7GUDOcqEwXCYJuzAQGw4ru9T2grzFtm4lijwF44tuwIPaA8B034IYivs8X7XadNcnzw0ebs7zIJtnsqd0vmz+dbNeW/Wz8xJbdnu0XpR3k4/3RpDcczOePTZJJnekiZ2WLTsxFwda6PduhLNVK538AfLmEoA==";
		//System.out.println(CodecUtils.decompressData(ec, EncryptionUtils.KEY_MW+MD5.MD5(EncryptionUtils.KEY_MW).toUpperCase()));

	}
}

