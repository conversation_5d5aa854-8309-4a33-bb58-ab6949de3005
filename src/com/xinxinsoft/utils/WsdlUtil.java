package com.xinxinsoft.utils;

import org.apache.http.Consts;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.TimeUnit;


/**
 * @version v1.0
 * @ProjectName: EOM
 * @ClassName: WsdlUtil
 * @Description: TODO(调用WSDL接口的公共类)
 * @Author: Leo
 * @Date: 2020/8/12 10:39
 */
public class WsdlUtil {
    private static Logger logger = LoggerFactory.getLogger(WsdlUtil.class);
    private static WsdlUtil wsdlUtil = null;
    private static CloseableHttpClient httpClient=null;

    public static WsdlUtil getInstance() {
        if (wsdlUtil == null) {
            synchronized (WsdlUtil.class) {
                if (wsdlUtil == null) {
                    wsdlUtil = new WsdlUtil();
                }
            }
        }
        return wsdlUtil;
    }

    private WsdlUtil() {

    }

    public static CloseableHttpClient getHttpClient() {
        if (httpClient == null) {
            Registry<ConnectionSocketFactory> registry = RegistryBuilder.<ConnectionSocketFactory>create()
                    .register("http", PlainConnectionSocketFactory.getSocketFactory())
                    .register("https", SSLConnectionSocketFactory.getSocketFactory())
                    .build();
            PoolingHttpClientConnectionManager connectionManager = new PoolingHttpClientConnectionManager(registry);
            //最大连接数3000
            connectionManager.setMaxTotal(300);
            //路由链接数400
            connectionManager.setDefaultMaxPerRoute(100);
            RequestConfig requestConfig = RequestConfig.custom()
                    .setSocketTimeout(60000)
                    .setConnectTimeout(60000)
                    .setConnectionRequestTimeout(10000)
                    .build();
            httpClient =HttpClients.custom().setDefaultRequestConfig(requestConfig)
                    .setConnectionManager(connectionManager)
                    .evictExpiredConnections()
                    .evictIdleConnections(30, TimeUnit.SECONDS)
                    .setConnectionManagerShared(true)
                    .build();;
        }
        return httpClient;
    }


    /**
     * 请求wsdl
     * 2021-11-19 11:13:39  leo 调整设置请求超时时长问题。防止请求流过大系统卡死
     * @param xml 请求xml
     * @param url 请求地址
     * @return
     */
    public String requestWsdl(String xml, String url) {
        String returnMessage = null;
        CloseableHttpClient httpClient=null;
        try {
            // 创建Httpclient对象
            logger.info("===========>调用接口："+url);
            httpClient = getHttpClient();
            HttpPost  post = new HttpPost(url);
            post.setHeader("SOAPAction", "");
            StringEntity stringEntity = new StringEntity(xml, Consts.UTF_8.name());
            stringEntity.setContentType("text/xml; charset=utf-8");//SOAP1.1
           //entity.setContentType("application/soap+xml; charset=utf-8");//SOAP1.2

            post.setEntity(stringEntity);
            // 执行请求
            HttpResponse response = httpClient.execute(post);
            HttpEntity entity = response.getEntity();

            if (entity != null) {
                returnMessage=EntityUtils.toString(entity);
//                // 将源码流保存在一个byte数组当中，因为可能需要两次用到该流，
//                byte[] bytes = EntityUtils.toByteArray(entity);
//                String  resCharSet = EntityUtils.getContentCharSet(entity);
//                if (resCharSet == null) {
//                    returnMessage = new String(bytes, "GBK");
//                } else {
//                    returnMessage = new String(bytes, "UTF-8");
//                }

            }
            logger.info("==========>响应结果：" + response.getStatusLine());
        } catch (Exception ex) {
            //ex.printStackTrace();
            logger.info("==========>接口返回结果："+ex.getMessage());
        }finally {
            if(httpClient != null){
                try {
                    httpClient.close();
                }catch (Exception e){
                    logger.info("==========>关闭连接异常："+e.getMessage());
                }
            }

        }
        //logger.info("==========>接口返回结果："+returnMessage.toString());
        return returnMessage;
    }



    //请求入参
    public String getXMLfor138(String requestXml) {
        StringBuffer sb = new StringBuffer();
        sb.append("<?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:impl='http://impl.services.inf.ztesoft.com'>" +
                "<soapenv:Header/>" +
                "<soapenv:Body>" +
                "<impl:commonService soapenv:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/'>" +
                "<reqXml xsi:type='soapenc:string' xmlns:soapenc='http://schemas.xmlsoap.org/soap/encoding/'>" +
                "<![CDATA[");
        sb.append(requestXml);
        sb.append("]]></reqXml> </impl:commonService></soapenv:Body></soapenv:Envelope>");
        return sb.toString();
    }

    /**
     * @param busi_opp_id        商机ID
     * @param order_number       订单编码
     * @param begin_time         合同生效时间
     * @param end_time           合同失效时间
     * @param order_person_phone 下单人手机号
     * @param product_id         产品ID
     * @param product_name       产品名称
     * @param product_cost       产品资费
     * @param product_type       产品类型
     * @return 反馈请求的xml文件
     */
    public String qryCustInfoByPersonPhone(String busi_opp_id, String order_number, String begin_time, String end_time, String order_person_phone, String product_id, String product_name, String product_cost, String product_type) {
        StringBuilder sb = new StringBuilder();
        sb.append("<ROOT>");
        sb.append("<HEADER>");
        sb.append("<TRANSACTION_ID>" + EOMCODEUtil.MakeOrderNo(10) + "</TRANSACTION_ID>");
        sb.append("<SERVICE_CODE>callBackBusiOpp</SERVICE_CODE>");
        sb.append("<CLIENT_NAME>SCCRM0</CLIENT_NAME>");
        sb.append("<PASSWORD>123456</PASSWORD>");
        sb.append("</HEADER>");
        sb.append("<BODY>");
        sb.append("<BUSI_OPP_ID>" + busi_opp_id + "</BUSI_OPP_ID>");
        sb.append("<ORDER_NUMBER>" + order_number + "</ORDER_NUMBER>");
        sb.append("<BEGIN_TIME>" + begin_time + "</BEGIN_TIME>");
        sb.append("<END_TIME>" + end_time + "</END_TIME>");
        sb.append("<ORDER_PERSON_PHONE>" + order_person_phone + "</ORDER_PERSON_PHONE>");
        sb.append("<PRODUCT_LIST>");
        sb.append("<PRODUCT_ID>" + product_id + "</PRODUCT_ID>");
        sb.append("<PRODUCT_NAME>" + product_name + "</PRODUCT_NAME>");
        sb.append("<PRODUCT_COST>" + product_cost + "</PRODUCT_COST>");
        sb.append("<PRODUCT_TYPE>" + product_type + "</PRODUCT_TYPE>");
        sb.append("</PRODUCT_LIST>");
        sb.append("</BODY>");
        sb.append("</ROOT>");
        //System.out.println(sb);
        return sb.toString();
    }

}


