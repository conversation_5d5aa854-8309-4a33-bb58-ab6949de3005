package com.xinxinsoft.utils;

import com.aspose.words.SaveFormat;
import freemarker.template.Configuration;
import freemarker.template.Template;

import com.itextpdf.text.pdf.parser.PdfReaderContentParser;
import com.lowagie.text.Document; 
import com.lowagie.text.Element; 
import com.lowagie.text.pdf.BaseFont;
import com.lowagie.text.pdf.PdfContentByte;
import com.lowagie.text.pdf.PdfCopy;
import com.lowagie.text.pdf.PdfGState;
import com.lowagie.text.pdf.PdfImportedPage;
import com.lowagie.text.pdf.PdfCopy.PageStamp;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.lowagie.text.pdf.PdfReader;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.awt.Color;
import java.io.*;
import java.net.URLEncoder;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletResponse;
public class XMlToDoc {
        private static Logger logger = LoggerFactory.getLogger(XMlToDoc.class);
    // 模版存储路径，项目里我就放在resource下
	private static final String basePath =FileUpload.getCont_PdfURL();

	public static void main(String[] args) throws Exception {

		// makeWord();
		 // makePdfByXcode();
		//doc2pdf("G:/FTP/test.docx", "G:/FTP/test.pdf");
	//	addWater("G:/FTP/test.pdf", "G:/FTP/test_w.pdf","【省公司】2018011212312312312");
	//	File file_w =new File("G:/FTP/test.pdf");
	//	if(file_w.exists()){
		// System.gc(); 
	//		file_w.delete();
	//	}
//		 addSign("G:/FTP/test_w.pdf","盖章","G:/FTP/test.png","G:/FTP/test_自贡.pdf");
//		File file_w_s =new File("G:/FTP/test_w.pdf");
//		if(file_w_s.exists()){
//			file_w_s.delete();
//		}
		//"C:\Users\<USER>\Desktop\2.docx"
       //
		System.out.println("----------------:"+"F:/FTP/test.docx".substring(0,"F:/FTP/test.docx".indexOf(".")));
	}  

	/**
	 * 生成docx
	 * 
	 * @throws Exception
	 */
	@Deprecated
	static void makeWord() throws Exception {
		/** 初始化配置文件 **/
		Configuration configuration = new Configuration();
		String fileDirectory = basePath;
		/** 加载文件 **/
		configuration.setDirectoryForTemplateLoading(new File(fileDirectory));
		/** 加载模板 **/
		Template template = configuration.getTemplate("document.xml");
		/** 准备数据 **/
		Map<String, String> dataMap = new HashMap<String, String>();
		/** 在ftl文件中有${textDeal}这个标签 **/
		dataMap.put("id", "黄浦江吴彦祖");
		dataMap.put("number", "20");
		dataMap.put("language", "java,php,python,c++.......");
		dataMap.put("example", "Hello World!");
		dataMap.put("qianzhang", "${qianzhang}");

		/** 指定输出word文件的路径 **/
		String outFilePath = basePath + "data.xml";
		File docFile = new File(outFilePath);
		FileOutputStream fos = new FileOutputStream(docFile);
		Writer out = new BufferedWriter(new OutputStreamWriter(fos), 10240);
		template.process(dataMap, out);
		if (out != null) {
			out.close();
		}
		try {
		 /*	ZipInputStream zipInputStream = ZipUtils.wrapZipInputStream(new FileInputStream(new File(basePath+ "test.zip")));
			ZipOutputStream zipOutputStream = ZipUtils.wrapZipOutputStream(new FileOutputStream(new File(basePath+ "test.docx")));
			String itemname = "word/document.xml";
			ZipUtils.replaceItem(zipInputStream, zipOutputStream, itemname,new FileInputStream(new File(basePath + "data.xml")));
			System.out.println("success"); */

		} catch (Exception e) {
			System.out.println(e.toString());
		}
	}
	 /** 
     * 向客户端发送数据 
     * @param  path
     * @param docName 返回客户端的word文件名  
     * @param formatType DOC 或者 DOCX 
     * @param openNewWindow 在线打开或者下载 
     * @param response 
     * @throws Exception 
     */  
    public static void sendToBrowser(String path, String fileName, String formatType,boolean openNewWindow, HttpServletResponse response)
            throws Exception {   
        int BUFFER_SIZE = 4096;
        File file = new File(path);   
   	    InputStream in = null;
   		OutputStream out = null;
		try{ 
			    byte[] data = FileUtil.toByteArray2(path); 
			    fileName = URLEncoder.encode(fileName, "UTF-8");  
			    response.reset();  
			    response.setCharacterEncoding("utf-8"); 
			    response.addHeader("Content-Length", "" + data.length);  
			    
		   if (openNewWindow)  
		            response.setHeader("content-disposition", "attachment; filename=" + fileName);  
		        else  
		            response.addHeader("content-disposition", "inline; filename="+ fileName);  
		     if ("DOC".equals(formatType)) {  
		            response.setContentType("application/msword");  
		        } else if ("DOCX".equals(formatType)) {  
		            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");  
		        } else if ("PDF".equals(formatType)) {  
		            response.setContentType("application/pdf");  //
		        } else{
		        	response.setContentType("application/octet-stream");  
		        }
			    response.setContentLength((int) file.length());
			     response.setHeader("Accept-Ranges", "bytes"); 
			    int readLength = 0;
			            
			    in = new BufferedInputStream(new FileInputStream(file), BUFFER_SIZE);
			    out = new BufferedOutputStream(response.getOutputStream());
			                 
			    byte[] buffer = new byte[BUFFER_SIZE];
			             while ((readLength=in.read(buffer)) > 0) {
			                   byte[] bytes = new byte[readLength];
			                   	System.arraycopy(buffer, 0, bytes, 0, readLength);
			                     out.write(bytes);
			                }
			                 
			                out.flush(); 
			    response.flushBuffer();
		
		}catch(Exception e){
			           e.printStackTrace();
			        }finally {
			             if (in != null) {
			                 try {
			                    in.close();
			                 } catch (Exception e) {
			                }
			             }
			             if (out != null) {
			                 try {
		                     out.close();
			                } catch (Exception e) {
		                 }
			            }
			         }
    } 

	/**
	 * docx转pdf
	 */
	public static void doc2pdf(String inPath, String outPath) {
        File file ;
        FileOutputStream os = null;
        com.aspose.words.Document doc = null;
        try {

            long old = System.currentTimeMillis();
            logger.info("文档转pdf 开始...");
            // 新建一个空白pdf文档
            file = new File(outPath);
             os = new FileOutputStream(file);
            // Address是将要被转化的word文档
            doc = new com.aspose.words.Document(inPath);
            // 全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF,EPUB, XPS, SWF 相互转换
            doc.save(os, SaveFormat.PDF);
            long now = System.currentTimeMillis();
            logger.info("共耗时：" + ((now - old) / 1000.0) + "秒");
        } catch (Exception e) {
            logger.info("文档转pdf异常",e);
        } finally {
                try {
                    if(os!=null){
                        os.close();
                    }
                    if(doc !=null){
                        doc.deepClone();
                    }
                }catch (Exception e){
                    logger.info("文档转pdf 关闭 异常",e);
                }
        }
    }  
//      /**
//	 * 生成pdf
//	 */
//	public static void makePdfByXcode() {
//		long startTime = System.currentTimeMillis();
//		try {
//			XWPFDocument document = new XWPFDocument(new FileInputStream(
//					new File(basePath + "test.docx")));
//			// document.setParagraph(new Pa );
//			File outFile = new File(basePath + "test.pdf");
//			outFile.getParentFile().mkdirs();
//			OutputStream out = new FileOutputStream(outFile);
//			// IFontProvider fontProvider = new AbstractFontRegistry();
//			PdfOptions options = PdfOptions.create(); // gb2312
//			PdfConverter.getInstance().convert(document, out, options);
//		} catch (Exception e) {
//			e.printStackTrace();
//		}
//		System.out.println("Generate ooxml.pdf with "+ (System.currentTimeMillis() - startTime) + " ms.");
//	}

	 /*
	 * 添加合同水印
	 * 
	 * @param fileName
	 *            原文件地址
	 * @param savepath
	 *            添加水印后的文件地址
	 * @param waterMarkString
	 *            水印文字
	 * @return
	 */
	public static int addWater(String fileName, String savepath,
			String waterMarkString) {
		if ("".equals(waterMarkString) || waterMarkString == null) {
			return 0;
		}
		// 文档总页数
		int num = 0;
		Document document = new Document();
		try {
			PdfReader reader = new PdfReader(fileName);
			BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",BaseFont.EMBEDDED);

			num = reader.getNumberOfPages();
			PdfCopy copy = new PdfCopy(document, new FileOutputStream(savepath));
			PdfGState gs = new PdfGState();
			document.open();
			for (int i = 0; i < num;) {
				PdfImportedPage page = copy.getImportedPage(reader, ++i);
				PageStamp stamp = copy.createPageStamp(page);
			//	Font f = new Font(base);
				// 添加水印
				PdfContentByte under = stamp.getOverContent();
				gs.setFillOpacity(0.3f);
				under.setGState(gs);
				under.beginText();
				under.setColorFill(Color.LIGHT_GRAY);

				// 字符越长，字体越小，设置字体
				int fontSize = getFontSize(waterMarkString);
				under.setFontAndSize(base, fontSize);

				// 设置水印文字字体倾斜 开始
				float pageWidth = reader.getPageSize(i).getWidth();
				float pageHeight = reader.getPageSize(i).getHeight();

				under.showTextAligned(Element.ALIGN_CENTER, waterMarkString,
						pageWidth / 2, pageHeight / 2, 60);// 水印文字成60度角倾斜,且页面居中展示

				// 字体设置结束
				under.endText();
				stamp.alterContents();
				copy.addPage(page);
			}
		} catch (Exception e) {
			e.printStackTrace();
			return -1;
		} finally {
			if (null != document) {
				document.close();
			}
		}
		return num;

	}

	/**
	 * 根据水印文字长度计算获取字体大小
	 * 
	 * @param waterMarkName
	 * @return
	 */
	private static int getFontSize(String waterMarkName) {
		int fontSize = 80;
		if (null != waterMarkName && !"".equals(waterMarkName)) {
			int length = waterMarkName.length();
			if (length <= 26 && length >= 18) {
				fontSize = 26;
			} else if (length < 18 && length >= 8) {
				fontSize = 40;
			} else if (length < 8 && length >= 1) {
				fontSize = 80;
			} else {
				fontSize = 40;
			}
		}
		return fontSize;
	}

	/**
	 * 签章功能
	 * @param sourcePDF 原PDF路径
	 * @param splitParentkeyValues  关键字
	 * @param imagePath  图片地址
	 * @param targetPath  签章后的PDF地址
	 * @throws Exception  错误代码
	 */
	public static void addSign(String sourcePDF,String splitParentkeyValues,String imagePath,String targetPath) throws Exception{
		  //sourcePDF: pdf的文档路径
		  //splitParentkeyValues[i]:关键字，其中对关键字进行特殊符号的过滤，不然会导致后面的匹配结果有误。
		List<MatchItem> matches =null;
		matches = matchPage(sourcePDF, splitParentkeyValues);

		//找出关键字后，将要盖章的图片准确定位到关键字周围，也可以采用坐标的方
		MatchItem matchItem  = new MatchItem();
		int pageNum = matches.get(0).getPageNum();
		// 读取模板文件
        InputStream input = new FileInputStream(new File(sourcePDF));
        com.itextpdf.text.pdf.PdfReader reader = new com.itextpdf.text.pdf.PdfReader(input);
        com.itextpdf.text.pdf.PdfStamper stamper = new com.itextpdf.text.pdf.PdfStamper(reader, new FileOutputStream(targetPath));
        com.itextpdf.text.pdf.PdfGState gs = new com.itextpdf.text.pdf.PdfGState();
        gs.setFillOpacity(9.0f);
		float pageWidth = reader.getPageSize(pageNum).getWidth();
		float pageHeight = reader.getPageSize(pageNum).getHeight();
        
		//matchItem.setX(matches.get(0).getX()-splitParentkeyValues.length() * 20);
		//matchItem.setY(matches.get(0).getY() - 150 / 1.527731f);
		matchItem.setX(matches.get(0).getX());
		matchItem.setY(matches.get(0).getY());
		com.itextpdf.text.Image img=com.itextpdf.text.Image.getInstance(imagePath);
		
		// 根据域的大小缩放图片
		System.out.println("宽："+pageWidth+"高"+pageHeight);
		
		//指定签章图片大小尺寸
		float imgWidth=102;
		float imgHeight=102;
		img.scaleToFit(imgWidth, imgHeight);
		//设置印章加入位置。。
		img.setAbsolutePosition(matchItem.getX() +imgWidth/2, matchItem.getY()-imgHeight/2);// 位置
		com.itextpdf.text.pdf.PdfContentByte over = stamper.getOverContent(pageNum);
		over.setGState(gs);
		over.addImage(img);
		
		stamper.close();
	    reader.close();
	    System.out.println("签章成功");
	}

	/**
	 * 查找所有
	 * 
	 * @param fileName
	 *            文件路径
	 * @param keyword
	 *            关键词
	 * @return
	 * @throws Exception
	 */
	public static List<MatchItem> matchPage(String fileName, String keyword)
			throws Exception {
		List<MatchItem> items = new ArrayList<>();
		com.itextpdf.text.pdf.PdfReader reader = new com.itextpdf.text.pdf.PdfReader(fileName);
		int pageSize = reader.getNumberOfPages();
		for (int page = pageSize; page > 0; page--) {
			if(page<0){
				break;
			}
			items.addAll(matchPage(reader, page, keyword));
			
		}
		return items;
	}

	/**
	 * 在文件中寻找特定的文字内容
	 * 
	 * @param reader
	 * @param pageNumber
	 * @param keyword
	 * @return
	 * @throws Exception
	 */
	public static List<MatchItem> matchPage(com.itextpdf.text.pdf.PdfReader reader,
			Integer pageNumber, String keyword) throws Exception {
		KeyWordPositionListener renderListener = new KeyWordPositionListener();
		renderListener.setKeyword(keyword);
		PdfReaderContentParser parse = new PdfReaderContentParser(reader);
		com.itextpdf.text.Rectangle rectangle = reader.getPageSize(pageNumber);
		renderListener.setPageNumber(pageNumber);
		renderListener.setCurPageSize(rectangle);
		parse.processContent(pageNumber, renderListener);
		return findKeywordItems(renderListener, keyword);
	}

	/**
	 * 找到匹配的关键词块
	 * 
	 * @param renderListener
	 * @param keyword
	 * @return
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public static List<MatchItem> findKeywordItems(KeyWordPositionListener renderListener,
			String keyword) {
		// 先判断本页中是否存在关键词
		List<MatchItem> allItems = renderListener.getAllItems();// 所有块LIST
		StringBuffer sbtemp = new StringBuffer("");
		for (MatchItem item : allItems) {// 将一页中所有的块内容连接起来组成一个字符串。
			sbtemp.append(item.getContent());
		}
		if (sbtemp.toString().indexOf(keyword) == -1) {// 一页组成的字符串没有关键词，直接return
			return renderListener.getMatches();
		}
		// 第一种情况：关键词与块内容完全匹配的项
		List matches = renderListener.getMatches();
		// 第二种情况：多个块内容拼成一个关键词，则一个一个来匹配，组装成一个关键词
		sbtemp = new StringBuffer("");
		
		List tempItems = new ArrayList();
		for (MatchItem item : allItems) {
			// 1，关键词中存在某块 2，拼装的连续的块=关键词 3，避开某个块完全匹配关键词
			// 关键词 中国移动 而块为 中 ，国，移动
			// 关键词 中华人民 而块为中，华人民共和国 这种情况解决不了，也不允许存在
			if (keyword.indexOf(item.getContent()) != -1
					&& !keyword.equals(item.getContent())) {
				tempItems.add(item);
				sbtemp.append(item.getContent());
				if (keyword.indexOf(sbtemp.toString()) == -1) {// 如果暂存的字符串和关键词
																// 不再匹配时
					sbtemp = new StringBuffer(item.getContent());
					tempItems.clear();
					tempItems.add(item);
				}
				if (sbtemp.toString().equalsIgnoreCase(keyword)) {// 暂存的字符串正好匹配到关键词时
					MatchItem tmpitem = getRightItem(tempItems, keyword);
					if (tmpitem != null) {
						matches.add(tmpitem);// 得到匹配的项
					}
					sbtemp = new StringBuffer("");// 清空暂存的字符串
					tempItems.clear();// 清空暂存的LIST
					continue;// 继续查找
				}
			} else {// 如果找不到则清空
				sbtemp = new StringBuffer("");
				tempItems.clear();
			}
		}
		// 第三种情况：关键词存在块中
		for (MatchItem item : allItems) {
			if (item.getContent().indexOf(keyword) != -1
					&& !keyword.equals(item.getContent())) {
				matches.add(item);
			}
		}
		return matches;
	}

	public static MatchItem getRightItem(List<MatchItem> tempItems,
			String keyword) {
		for (MatchItem item : tempItems) {
			if (keyword.indexOf(item.getContent()) != -1
					&& !keyword.equals(item.getContent())) {
				return item;
			}
		}

		return null;
	}
}
