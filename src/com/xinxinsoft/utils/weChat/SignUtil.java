package com.xinxinsoft.utils.weChat;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.ArrayUtils;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;
import java.util.TreeMap;

/**
 * ----------------------------------------------------------
 * 　　　　　　　　talk is cheap，show me the code！
 * ----------------------------------------------------------
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.weChat.SignUtil
 * @description: 签名工具类
 * @author: WF
 * @date: 2020-05-12 16:24
 **/
public class SignUtil {

    /**
     *   根据参数判断map中是否存在，然后拼接在一起
     * @param params
     * @param execluedKeys
     * @return
     */
    public static String jointParams(Map<String ,String >  params,String ... execluedKeys){
            if(MapUtils.isEmpty(params)){
                return "";
            }
        StringBuffer sb = new StringBuffer();
            for (Map.Entry<String,String> entry : params.entrySet()){
                String key  = entry.getKey();
                String value = entry.getValue();
                if(ArrayUtils.contains(execluedKeys,key)){
                    continue;
                }
                sb.append("&").append("key").append("=").append(value);
            }

        return sb.toString().replaceFirst("&","");
    }

    /**
     * 字符串加密，并且根据toUpperCase 是否全部转换成大写
     * @param str  加密的字符串
     * @param toUpperCase  是否转换成大写，
     * @return  返回加密的字符串
     */
    public static String md5(String str ,boolean toUpperCase){
        if(str == null){
            return  "";
        }
        String md5 = DigestUtils.md5Hex(str);
        return  toUpperCase ? md5.toUpperCase() : md5;
    }

    /**
     *  加密字符方法
     * @param str
     * @return
     */
    public static String md5(String str){
        return  md5(str,true);
    }

    /**
     * 判断是否存在
     * @param params
     * @return
     */
    public static TreeMap<String ,String > sortParams(Map<String,String> params){
        if(MapUtils.isEmpty(params)){
            return  new TreeMap<>();
        }
        return  new TreeMap<>(params);
    }


    public static Map<String,String> sortMap(Map<String,String> params){
            return  new TreeMap<String,String>(params);
    }

    public static String genSignStr(Map<String,String> sortParams,String...  excluedKeys){
        if(MapUtils.isEmpty(sortParams)){
            return "";
        }
        StringBuffer sb = new StringBuffer();
        for (Map.Entry<String,String> entry : sortParams.entrySet()){
            String key  = entry.getKey();
            String value = entry.getValue();
                if(ArrayUtils.contains(excluedKeys,key)){
                    continue;
                }
                sb.append("&").append(key).append("=").append(value);
        }
        return sb.toString().replaceFirst("&","");
    }

    public static boolean checkSign(String genSignStr, String sign) {
        String resMd5 = DigestUtils.md5Hex(genSignStr).toUpperCase();
        return StringUtils.equals(resMd5, sign);
    }

    public static boolean checkSign(String sign, Map<String, String> params, String... excluedKeys) {
        Map<String, String> sortMap = sortMap(params);
        String signStr = genSignStr(sortMap, excluedKeys);
        return checkSign(signStr, sign);
    }
}
