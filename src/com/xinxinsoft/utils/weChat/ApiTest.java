package com.xinxinsoft.utils.weChat;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.xinxinsoft.utils.ImageUtils;
import net.sf.json.JSONObject;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * ----------------------------------------------------------
 * 　　　　　　　　talk is cheap，show me the code！
 * ----------------------------------------------------------
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.weChat.ApiTest
 * @description:  测试
 * @author: WF
 * @date: 2020-05-12 17:37
 **/
public class ApiTest {
    /// 客户端秘钥
    private static String clientPrivateKey = "MIICdgIBADANBgkqhkiG9w0BAQEFAASCAmAwggJcAgEAAoGBAMtv1OLs6SWX3Bgppzmm/qx3+Ez+N4cOwdVZRfeEgfj8VQHmRQq501xpqjm90erGoEhd8baMucwEnS5VGZ4CMK3WnqsjVKcdLI9gQH0uLXri03VVGdsy7S+8+92iOZOB2hHvlhiCPdFtN0U76Js+kIC0D+GyoeHnwIQCNQIqwFShAgMBAAECgYAk9HamlkSv+IsZtI9VELnYmJ55VgC+PvcFC4eDNAXsNeI3Sx4lmaYEdolendXBIT3Ch5Y+BAhIQFpRLVJO+rTevCKoAIsnFuXh+MkbFfw6XT3kUQ601wIYzUzJRRUlFdgaW8484wIz16VeAq/0FtUhQoWzIhw4s8RBpZE8xiLqwQJBAPZ50EmiF2qTnUQWAub7+whDFeoJwV2OOwsd3/aSUuxzrmKrVAGXL485H6XytWTDcUQJ/rWyU3ON2O3MgpYmlhkCQQDTTESZw5mcFXRHPGSUO+DNooJ920TTQ9KJ4R5AUfGqbSh7a/05QdOms9v/2ErQjL27Q82IE+bKcxsH9cp9qbPJAkAw0tR1oYwt6mu+rvWIN+X0LwXKRIBvPdtoSlSxDS8r84ZZuUJ8xiv/nPst7jQMb8KiDbDM4TQysaiFAnvjrmURAkEApMLg70Gdox77l5yZFRmH1vE0ba0wAsjtAAShUShUmD8cBPmU8DPLNT8kemCPHgXQ4JgjSUuzeAO/fTqE1e4hOQJAGZw1Z9YHlOgccspjQBlEBph2lLToR7RtlNtz/E+ipeWRNlPNNy09GNA3O/g1vkHruc1CLiwUtsRhdfHD3qIXSA==";
    ///客户端公钥
    private static String clientPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDLb9Ti7Okll9wYKac5pv6sd/hM/jeHDsHVWUX3hIH4/FUB5kUKudNcaao5vdHqxqBIXfG2jLnMBJ0uVRmeAjCt1p6rI1SnHSyPYEB9Li164tN1VRnbMu0vvPvdojmTgdoR75YYgj3RbTdFO+ibPpCAtA/hsqHh58CEAjUCKsBUoQIDAQAB";
    //服务端公钥，由服务端提供
    private static String serverPublicKey = "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDtUedPs+Ncjb1MwAi6zMp6/dMWMoAdqweOE58p4BuxPLfW4bCykeeyV/DiLazMaLXHUZtgrBaKiTlEyM5QMZcFeTcJAUsbm1H7nI+geD/zmCUJs8CNQ072td3YhBH7ulL6lz717GU4hsymIF7wdk2QmdUmkeWTwzLO3UeI4sJTRQIDAQAB";

    private static String appkey = "scyddd";


    public static void main(String[] args) {
        try {

            System.out.println(ImageUtils.encodeImgageToBase64("C:\\Users\\<USER>\\Desktop\\电子发票.png"));
            String url = "https://wechattest.datalk.biz/zq/receive/invoice";
//            String url = "http://wxpt.scmcc.com.cn/zq/receive/invoice";
            Map<String, String> params = new HashMap<>();
            params.put("id",String.valueOf(System.currentTimeMillis()));
            params.put("manager_mobile", "***********");
            params.put("customer_mobile", "***********");
            params.put("invoice_account","admin_eom");
            params.put("invoice_month","2020-02-26");
            params.put("invoice_url", ImageUtils.encodeImgageToBase64("C:\\Users\\<USER>\\Desktop\\电子发票.png"));
            params.put("code280", "**********");

            String jsonResult = sendReq(url, params);
//            String jsonResult ="{\"rsCode\":\"0\",\"rsCause\":\"操作成功\",\"data\":{\"enkey\":\"rktq/wFT92EyJ5S9gx45gj1AlI/BGjBi7b/wMX39mlT6ewkNWj7iL0LDuw8h3yEVVHBnZw/vo0+G/TuzHbA9NFFNRNn4ZnZiy8qycttt2wR6w3ailxPwfSPjxy0z2VgB3Ra99mJSMz2w6S3XJFrOGueFYteuNCHw+QiDgxlIYFM=\",\"endata\":\"Ho3mcmeyGDxQrov4Y/rosA==\"}}";
            System.out.println("请求结果：" + jsonResult);
            responseDecrypt(jsonResult);

        } catch (Exception   ex) {
            ex.printStackTrace();
        }
    }

    // 获取请求加密数据
    public static String reqEncryptData(Map<String, String> params)  {
        // 加密
        String ranStr = RandomStringUtils.randomAlphanumeric(16);
        String enkey = RSATool.encrypt(ranStr, serverPublicKey);
        Gson gson = new GsonBuilder().serializeNulls().disableHtmlEscaping().create();
        String paramstr = gson.toJson(params);
        String endata = AESTool.encrypt(paramstr, ranStr);

        // 签名
        long timestamp = System.currentTimeMillis();
        String nonce = UUID.randomUUID().toString().replaceAll("-", "");
        params.put("timestamp", String.valueOf(timestamp));
        params.put("nonce", nonce);
        params.put("appkey", appkey);
        String sign = SignUtil.genSignStr(SignUtil.sortMap(params));
        String md5Sign = DigestUtils.md5Hex(sign).toUpperCase();
        System.out.println("md5Sign => " + md5Sign);
        System.out.println("timestamp => " + timestamp);
        System.out.println("nonce => " + nonce);

        // 组装请求数据
        CryptoDTO dto = new CryptoDTO();

        dto.setEnkey(enkey);
        dto.setEndata(endata);
        dto.setNonce(nonce);
        dto.setTimestamp(String.valueOf(timestamp));
        dto.setSignature(md5Sign);
        dto.setAppkey(appkey);

        System.out.println("请求数据: ==> \n" + gson.toJson(dto));

        return gson.toJson(dto);
    }

    public static String sendReq(String url, Map<String, String> params) throws Exception{
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json;charset=utf-8");
        return HttpClientHelper.create().setRequestURL(url).setHeader(headers).setRequestBody(reqEncryptData(params)).sendPostWithBody();

    }

    public static void responseDecrypt(String jsonResult) {

        // 解密
        JSONObject jobj =JSONObject.fromObject(jsonResult);
        jobj = jobj.getJSONObject("data");
//        String enkeyRes = JsonUtil.getStringFromJsonObj(jobj, "enkey");
//        String endataRes = JsonUtil.getStringFromJsonObj(jobj, "endata");
        String enkeyRes = jobj.getString("enkey");
        String endataRes =jobj.getString("endata");
        System.out.println("请求结果数据 ======================================== S");
        System.out.println(jsonResult);
        System.out.println("请求结果数据 ======================================== E");
        if (StringUtils.isAnyBlank(enkeyRes, endataRes)) {
          return;
        }

        String resAesKey = RSATool.decrypt(enkeyRes, clientPrivateKey);
        String result = AESTool.decrypt(endataRes, resAesKey);
        System.out.println("解密结果数据 ======================================== S");
        System.out.println(result);
        System.out.println("解密结果数据 ======================================== E");

    }

}
