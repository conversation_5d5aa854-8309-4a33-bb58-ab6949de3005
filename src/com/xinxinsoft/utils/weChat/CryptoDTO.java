package com.xinxinsoft.utils.weChat;

import java.io.Serializable;

/**
 * ----------------------------------------------------------
 * 　　　　　　　　talk is cheap，show me the code！
 * ----------------------------------------------------------
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.weChat.CryptoDTO
 * @description: 请求实体
 * @author: WF
 * @date: 2020-05-12 16:21
 **/
public class CryptoDTO implements Serializable {

    /** 加密的 JSON 数据 */
    private String endata;
    /** 加密的 AES Key */
    private String enkey;
    /** 时间戳 10分钟内数据有效 */
    private String timestamp;
    /** 随机数 至少为10位 */
    private String nonce;
    /** 参数签名 */
    private String signature;
    /** 应用标识 */
    private String appkey;

    public String getEndata() {
        return endata;
    }

    public void setEndata(String endata) {
        this.endata = endata;
    }

    public String getEnkey() {
        return enkey;
    }

    public void setEnkey(String enkey) {
        this.enkey = enkey;
    }

    public String getTimestamp() {
        return timestamp;
    }

    public void setTimestamp(String timestamp) {
        this.timestamp = timestamp;
    }

    public String getNonce() {
        return nonce;
    }

    public void setNonce(String nonce) {
        this.nonce = nonce;
    }

    public String getSignature() {
        return signature;
    }

    public void setSignature(String signature) {
        this.signature = signature;
    }

    public String getAppkey() {
        return appkey;
    }

    public void setAppkey(String appkey) {
        this.appkey = appkey;
    }
}
