package com.xinxinsoft.utils.weChat;


import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.HttpStatus;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.entity.mime.HttpMultipartMode;
import org.apache.http.entity.mime.MultipartEntityBuilder;
import org.apache.http.entity.mime.content.FileBody;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.Args;
import org.apache.http.util.EntityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URI;
import java.nio.charset.Charset;
import java.text.MessageFormat;
import java.util.*;

/**
 * ----------------------------------------------------------
 * 　　　　　　　　talk is cheap，show me the code！
 * ----------------------------------------------------------
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.weChat.HttpClientHelper
 * @description:
 * @author: WF
 * @date: 2020-05-12 17:39
 **/
public class HttpClientHelper {


    private static final Logger logger = LoggerFactory.getLogger(HttpClientHelper.class);

    /** jsonResult 中的错误 key */
    public static final String JSON_KEY_ERROR = "error";

    /** 默认编码 */
    private String encode = "UTF-8";
    /** 从connect Manager获取Connection 超时时间，单位秒 */
    private int connectoinRequestTimeout = 10;
    /** 连接超时时间，单位秒 */
    private int connectionTimeout = 60;
    /** 请求获取数据的超时时间，单位秒 */
    private int socketTimeout = 60;

    private static final int METHOD_POST = 1;
    private static final int METHOD_GET = 2;

    private String requestURL = null;
    private HttpPost httpPost = null;
    private Map<String, Object> params = null;
    private List<NameValuePair> getMethodNvps = null;

    private String requestBodyParams = null;
    private Map<String, String> header = null;

    private String proxyHost;
    private int proxyPort;
    private String proxyScheme;
    private boolean doProxy = true;

    private HttpClientHelper(){
        super();
    }

    public static HttpClientHelper create() {
        HttpClientHelper helper = new HttpClientHelper();
        return helper;
    }

    /**
     * <p>
     * Title:        setGetParams
     * <p>
     * Description:  设置请求URL
     * 若多次调用了该方法，以最后一次调用设置的参数才生效，前面几次的已被销毁
     *
     * @param params GET 请求的参数，为 null 或 emptys 时表示不设置参数<br/>
     * @return {@link HttpClientHelper}
     */
    public HttpClientHelper setGetParams(Map<String, String> params) {
        List<NameValuePair> nvps                    = null;
        Iterator<Map.Entry<String, String>> iterator = null;
        Map.Entry<String, String> entry              = null;
        String key                                   = null;
        String val                                   = null;
        try {
            nvps = new ArrayList<NameValuePair>();
            iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                entry = iterator.next();
                key = entry.getKey();
                if(key == null || "".equals(key)){ continue; }else{ key = key.trim(); }
                val = entry.getValue();
                val = val == null ? "" : val;
                nvps.add(new BasicNameValuePair(key, val));
            }
            this.getMethodNvps = nvps;
        } finally {
            val      = null;
            key      = null;
            entry    = null;
            iterator = null;
            nvps     = null;
        }
        return this;
    }
    public String sendGet() throws Exception {
        logger.debug("sendGet in");
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            //get 请求
            HttpGet httpGet = new HttpGet(this.requestURL);
            //设置请求参数
            if (CollectionUtils.isNotEmpty(this.getMethodNvps)) {
                String urlParams = EntityUtils.toString(new UrlEncodedFormEntity(this.getMethodNvps, this.encode));
                httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + urlParams));
            }
            logger.debug("sendGet:[{}]", httpGet.getURI().toString());
            //设置超时时间
            setTimeouts(null, httpGet, METHOD_GET);

            // 设置请求头
            if (this.header != null) {
                for (Map.Entry<String, String> header : this.header.entrySet()) {
                    httpGet.addHeader(header.getKey(), header.getValue());
                }
            }

            if (doProxy && StringUtils.isNotBlank(proxyHost) && proxyPort > 0) {
                HttpHost proxy = new HttpHost(this.proxyHost.trim(), this.proxyPort, StringUtils.trimToNull(this.proxyScheme));
                RequestConfig build = RequestConfig.copy(httpGet.getConfig()).setProxy(proxy).build();
                httpGet.setConfig(build);
            }

            //创建连接
            httpClient = HttpClients.createDefault();
            //发送请求，获取响应对象
            response = httpClient.execute(httpGet);
            //处理响应对象，获取结果
            String result = getResponseResult(response);
            logger.debug("response result:[{}]", result);
            return result;
        } catch (Exception ex){
            logger.error("sendGet occur exception: {}", ex.getMessage(), ex);
            throw ex;
        } finally {
            close(httpClient, response);
            response = null;
            httpClient = null;
            logger.debug("sendGet out");
        }
    }

    private void close(CloseableHttpClient httpClient, CloseableHttpResponse response) throws IOException {
        if (httpClient != null) {
            httpClient.close();
        }
        if (response != null) {
            response.close();
        }
    }


    /**
     * <p>
     * Title:        setRequestURL
     * <p>
     * Description:  设置请求URL
     *
     * @param requestURL 请求的URL
     * @return cn.com.adtis.fall.utils.HttpClientHelper
     */
    public HttpClientHelper setRequestURL(String requestURL) {
        if (requestURL == null || "".equals(requestURL.trim())) {
            throw new IllegalArgumentException("requestURL must not be null or blank.");
        }
        this.requestURL = requestURL;
        return this;
    }

    /**
     * <p>
     * Title:        setPostParams
     * <p>
     * Description:  设置请求URL
     * 若多次调用了该方法，以最后一次调用设置的参数才生效，前面几次的已被销毁
     *
     * @param params Post 请求的参数，为 null 或 emptys 时表示不设置参数<br/>
     *               键值对的值的类型如下：<br/>
     *               java.io.File、java.lang.String、8中基本类型及其封装类（int、short、float、double、long、boolean、byte、char、Integer、Short、Float、Double、Long、Boolean、Byte、Character）
     * @return cn.com.adtis.fall.utils.HttpClientHelper
     * @throws Exception 请求参数键值对的值有不支持的类型时抛出
     */
    public HttpClientHelper setPostParams(Map<String, Object> params) throws Exception {
        this.params = params;
        return this;
    }

    /**
     * <p>
     * Title:        sendPost
     * <p>
     * Description:  发送post请求
     *
     * @return 请求接果
     * @throws Exception
     */
    public String sendPost() throws Exception {
        logger.debug("sendPost in");
        if (this.requestURL == null || "".equals(this.requestURL.trim())) {
            throw new Exception("requestURL must not be null or blank.");
        }
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            //post 请求
            this.httpPost = this.httpPost != null ? this.httpPost : new HttpPost();
            this.httpPost.setURI(URI.create(this.requestURL));
            logger.debug("send POST: {}, params: {}", this.requestURL, parseParams(this.params));
            //设置请求参数
            setHttpPostEntity(this.httpPost, this.params);
            //设置超时时间
            setTimeouts(this.httpPost, null, METHOD_POST);

            // 设置请求头
            if (this.header != null) {
                for (Map.Entry<String, String> header : this.header.entrySet()) {
                    this.httpPost.addHeader(header.getKey(), header.getValue());
                }
            }

            if (doProxy && StringUtils.isNotBlank(proxyHost) && proxyPort > 0) {
                HttpHost proxy = new HttpHost(this.proxyHost.trim(), this.proxyPort, StringUtils.trimToNull(this.proxyScheme));
                RequestConfig build = RequestConfig.copy(this.httpPost.getConfig()).setProxy(proxy).build();
                this.httpPost.setConfig(build);
            }

            //创建连接
            httpClient = HttpClientBuilder.create().build();
            //发送请求，获取响应对象
            response = httpClient.execute(this.httpPost);
            //处理响应对象，获取结果
            String result = getResponseResult(response);
            logger.debug("response result:[{}]", result);
            return result;
        } catch (Exception ex) {
            logger.error("sendPost occur exception: {}", ex.getMessage(), ex);
            throw ex;
        } finally {
            close(httpClient, response);
            response = null;
            httpClient = null;
            logger.debug("sendPost out");
        }
    }///~

    /**
     * <p>
     * Title:        getResponseResult
     * <p>
     * Description:  处理响应对象，获取结果
     *
     * @param response 响应对象
     * @return 响应结果
     */
    private String getResponseResult(CloseableHttpResponse response) {
        int statusCode = response.getStatusLine().getStatusCode();
        logger.debug("response status code: [{}]", statusCode);
        if (statusCode != HttpStatus.SC_OK) {
            logger.debug("get information failed,statusCode is [{}]", statusCode);
            return MessageFormat.format("'{'\"{0}\":\"get information failed,statusCode is [{1}]\"'}'", JSON_KEY_ERROR, String.valueOf(statusCode));//
        }
        HttpEntity responseEntity = response.getEntity();
        if (responseEntity == null) {
            return MessageFormat.format("'{'\"{0}\":\"[HttpClientHelper][getResponseResult] responseEntity is null\"'}'", JSON_KEY_ERROR);//
        }
        String jsonResult = null;
        try {
            jsonResult = EntityUtils.toString(responseEntity, this.encode);
            EntityUtils.consume(responseEntity);
        } catch (IOException ioex) {
            logger.debug("Get response result occur exception:{}", ioex.getMessage(), ioex);
            return MessageFormat.format("'{'\"{0}\":\"[HttpClientHelper][getResponseResult] Get response result occur exception: {1}\"'}'", JSON_KEY_ERROR, ioex.getMessage());//
        }
        return jsonResult;
    }

    /**
     * <p>
     * Title:        setHttpPostEntity
     * <p>
     * Description:  设置post请求参数
     *
     * @param httpPost org.apache.http.client.methods.HttpPost
     * @param params 请求参数
     * @throws Exception 参数中存在不支持的对象时抛出
     */
    private void setHttpPostEntity(HttpPost httpPost, Map<String, Object> params) throws Exception {
        if (params == null || params.isEmpty()) {
            return;
        }
        if (httpPost == null) {
            throw new IllegalArgumentException("httpPort must not be null.");
        }
        //查看是否有文件File
        if (haveFile(params)) {
            setHttpPostEntityWithFile(httpPost, params);
        } else {
            setHttpPostEntityNoFile(httpPost, params);
        }
    }///~

    /**
     * <p>
     * Title:        setHttpPostEntityNoFile
     * <p>
     * Description:  设置post请求参数有上传文件
     *
     * @param httpPost org.apache.http.client.methods.HttpPost
     * @param params 请求参数
     * @throws Exception 参数中存在不支持的对象时抛出
     */
    private void setHttpPostEntityWithFile(HttpPost httpPost, Map<String, Object> params) throws Exception {
        MultipartEntityBuilder builder     = null;
        Iterator<Map.Entry<String, Object>> iterator    = null;
        Map.Entry<String, Object>           entry       = null;
        String key         = null;
        Object val         = null;
        File valFile     = null;
        try {
            builder = MultipartEntityBuilder.create();
            // builder.setCharset(Charset.forName(this.encode));
            builder.setMode(HttpMultipartMode.BROWSER_COMPATIBLE);
            iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                entry = iterator.next();
                key = entry.getKey();
                if(key == null || "".equals(key)){ continue; }else{ key = key.trim(); }
                val = entry.getValue();
                if (val instanceof File) {
                    valFile = (File) val;
                    // builder.addBinaryBody(key, valFile, ContentType.DEFAULT_BINARY, valFile.getName());
                    // builder.addBinaryBody(key, valFile);
                    builder.addPart(key, new FileBody(valFile));
                } else if (val instanceof String) {
                    builder.addTextBody(key, (String) val, ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Short) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Byte) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Integer) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Float) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Double) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Boolean) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Long) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                } else if (val instanceof Character) {
                    builder.addTextBody(key, String.valueOf(val), ContentType.create(ContentType.DEFAULT_TEXT.getMimeType(), this.encode));
                }else {
                    if (null == val) {
                        throw new Exception("params value must not be null.");
                    }
                    throw new Exception("params value no support:" + val.getClass().getName());
                }
            }
            // builder.setCharset(Charset.forName(this.encode));
            httpPost.setEntity(builder.build());
        } finally {
            valFile     = null;
            val         = null;
            key         = null;
            entry       = null;
            iterator    = null;
            builder     = null;
        }
    }///~

    private String parseParams(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return null;
        }
        Iterator<Map.Entry<String, Object>> iterator    = null;
        Map.Entry<String, Object>           entry       = null;
        String key         = null;
        Object val         = null;
        File valFile     = null;
        boolean                             isFirst     = true;
        StringBuffer sb = new StringBuffer();
        sb.append("{");
        iterator = params.entrySet().iterator();
        while (iterator.hasNext()) {
            entry = iterator.next();
            key = entry.getKey();
            val = entry.getValue();
            if (!isFirst) {
                sb.append(",");
            } else {
                isFirst = false;
            }
            if ((val instanceof String)
                    ||(val instanceof Short)
                    ||(val instanceof Byte)
                    ||(val instanceof Integer)
                    ||(val instanceof Float)
                    ||(val instanceof Double)
                    ||(val instanceof Boolean)
                    ||(val instanceof Long)
                    ||(val instanceof Character)) {
                sb.append(key).append("=").append(String.valueOf(val));
            } else if (val instanceof File) {
                valFile = (File) val;
                sb.append(key).append("=").append(valFile.getAbsolutePath());
            } else {
                sb.append(key).append("=").append("this value no support:").append(val == null ? "value is null" : val.getClass().getName());
            }
        }
        sb.append("}");
        return sb.toString();
    }


    /**
     * <p>
     * Title:        setHttpPostEntityNoFile
     * <p>
     * Description:  设置post请求参数无上传文件
     *
     * @param httpPost org.apache.http.client.methods.HttpPost
     * @param params 请求参数
     * @throws Exception 参数中存在不支持的对象时抛出
     */
    private void setHttpPostEntityNoFile(HttpPost httpPost, Map<String, Object> params) throws Exception {
        List<NameValuePair> nvps                    = null;
        Iterator<Map.Entry<String, Object>> iterator = null;
        Map.Entry<String, Object> entry              = null;
        String key                                   = null;
        Object val                                   = null;
        UrlEncodedFormEntity entity                  = null;
        try {
            nvps = new ArrayList<NameValuePair>();
            iterator = params.entrySet().iterator();
            while (iterator.hasNext()) {
                entry = iterator.next();
                key = entry.getKey();
                if(key == null || "".equals(key)){ continue; }else{ key = key.trim(); }
                val = entry.getValue();
                if (val instanceof String) {
                    nvps.add(new BasicNameValuePair(key, ((String) val).trim()));
                } else if (val instanceof Integer) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else if (val instanceof Long) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else if (val instanceof Float) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else if (val instanceof Double) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else if (val instanceof Boolean) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else if (val instanceof Character) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else if (val instanceof Short) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else if (val instanceof Byte) {
                    nvps.add(new BasicNameValuePair(key, String.valueOf(val)));
                } else {
                    if (null == val) {
                        throw new Exception("params value must not be null.");
                    }
                    throw new Exception("params value no support:" + val.getClass().getName());
                }
            }
            entity = new UrlEncodedFormEntity(nvps, this.encode);
            httpPost.setEntity(entity);
        } finally {
            entity   = null;
            val      = null;
            key      = null;
            entry    = null;
            iterator = null;
            nvps     = null;
        }
    }

    /**
     * <p>
     * Title:        haveFile
     * <p>
     * Description:
     *
     * @param params 判断参数中是否有文件
     * @return true-参数中有文件，否则没有文件
     */
    private boolean haveFile(Map<String, Object> params) {
        if (params == null || params.isEmpty()) {
            return false;
        }
        for (Object val : params.values()) {
            if (val instanceof File) {
                return true;
            }
        }
        return false;
    }

    private void setTimeouts(HttpPost httpPost, HttpGet httpGet, int type) {
        if (type == METHOD_POST) {
            httpPost =  Args.notNull(httpPost, "httpPort");
        } else if (type == METHOD_GET) {
            httpGet = Args.notNull(httpGet, "httpGet");
        } else {
            throw new IllegalArgumentException("unknown type[{}]" + type);
        }
        RequestConfig.Builder builder = RequestConfig.custom();

        //从connect Manager获取Connection 超时时间，单位毫秒
        builder.setConnectionRequestTimeout(this.connectoinRequestTimeout * 1000);
        //连接超时时间，单位毫秒
        builder.setConnectTimeout(this.connectionTimeout * 1000);
        //请求获取数据的超时时间，单位毫秒
        builder.setSocketTimeout(this.socketTimeout * 1000);

        if (type == METHOD_POST) {
            httpPost.setConfig(builder.build());
        } else if (type == METHOD_GET) {
            httpGet.setConfig(builder.build());
        }
    }///~

    public HttpClientHelper setRequestBody(String data) {
        // this.httpPost.setEntity(new StringEntity(data, Charset.forName("UTF-8")));
        this.requestBodyParams = data;
        return this;
    }

    public String sendPostWithBody() throws Exception {
        logger.debug("sendPostWithBody in");
        if (this.requestURL == null || "".equals(this.requestURL.trim())) {
            throw new Exception("requestURL must not be null or blank.");
        }
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            //post 请求
            this.httpPost = this.httpPost != null ? this.httpPost : new HttpPost();
            this.httpPost.setURI(URI.create(this.requestURL));
            logger.debug("send POST: {}, params: {}", this.requestURL, this.requestBodyParams);
            //设置请求参数
            this.httpPost.setEntity(new StringEntity(this.requestBodyParams, Charset.forName("UTF-8")));
            // 设置请求头
            if (this.header != null) {
                for (Map.Entry<String, String> header : this.header.entrySet()) {
                    this.httpPost.addHeader(header.getKey(), header.getValue());
                }
            }

            //设置超时时间
            setTimeouts(this.httpPost, null, METHOD_POST);

            if (doProxy && StringUtils.isNotBlank(proxyHost) && proxyPort > 0) {
                HttpHost proxy = new HttpHost(this.proxyHost.trim(), this.proxyPort, StringUtils.trimToNull(this.proxyScheme));
                RequestConfig build = RequestConfig.copy(this.httpPost.getConfig()).setProxy(proxy).build();
                this.httpPost.setConfig(build);
            }

            //创建连接
            httpClient = HttpClients.createDefault();
            //发送请求，获取响应对象
            response = httpClient.execute(this.httpPost);
            //处理响应对象，获取结果
            String result = getResponseResult(response);
            logger.debug("response result:[{}]", result);
            return result;
        } catch (Exception ex) {
            logger.error("sendPost occur exception: {}", ex.getMessage(), ex);
            throw ex;
        } finally {
            close(httpClient, response);
            response = null;
            httpClient = null;
            logger.debug("sendPostWithBody out");
        }
    }

    public HttpClientHelper setHeader(Map<String, String> headers) {
        this.header = headers;
        return this;
    }

    public HttpClientHelper sendWxTempMaterial(String key, File file) {
        MultipartEntityBuilder builder = MultipartEntityBuilder.create();
        builder.addPart(key, new FileBody(file, ContentType.create("image/*", "utf8"), file.getName()));
        httpPost = new HttpPost();
        httpPost.setEntity(builder.build());
        return this;
    }

    public String getEncode() {
        return encode;
    }

    public HttpClientHelper setEncode(String encode) {
        this.encode = encode;
        return this;
    }

    public int getConnectoinRequestTimeout() {
        return connectoinRequestTimeout;
    }

    public HttpClientHelper setConnectoinRequestTimeout(int connectoinRequestTimeout) {
        this.connectoinRequestTimeout = connectoinRequestTimeout;
        return this;
    }

    public int getConnectionTimeout() {
        return connectionTimeout;
    }

    public HttpClientHelper setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
        return this;
    }

    public int getSocketTimeout() {
        return socketTimeout;
    }

    public HttpClientHelper setSocketTimeout(int socketTimeout) {
        this.socketTimeout = socketTimeout;
        return this;
    }

    public String getProxyHost() {
        return proxyHost;
    }

    public void setProxyHost(String proxyHost) {
        this.proxyHost = StringUtils.isBlank(proxyHost) ? null : proxyHost.trim();
    }

    public int getProxyPort() {
        return proxyPort;
    }

    public void setProxyPort(int proxyPort) {
        this.proxyPort = proxyPort;
    }

    public String getProxyScheme() {
        return proxyScheme;
    }

    public void setProxyScheme(String proxyScheme) {
        this.proxyScheme = StringUtils.isBlank(proxyScheme) ? null : proxyScheme.trim();
    }

    public boolean isDoProxy() {
        return doProxy;
    }

    public void setDoProxy(boolean doProxy) {
        this.doProxy = doProxy;
    }

    @Override
    public String toString() {
        return "[" +
                "encode='" + encode + '\'' +
                ", connectoinRequestTimeout=" + connectoinRequestTimeout +
                ", connectionTimeout=" + connectionTimeout +
                ", socketTimeout=" + socketTimeout +
                ", requestURL='" + requestURL + '\'' +
                ']';
    }

    public static void main(String[] args) {
        String result = null;
        try {
            String url = "http://192.168.2.204:8380/saas/saasComm/avatar";
            Map<String, Object> params = new HashMap<String, Object>();
            params.put("phone", "18782424353");
            result = HttpClientHelper.create().setRequestURL(url).setPostParams(params).sendPost();
        } catch (Exception ex) {
            result = ex.getMessage();
        }
        System.out.println(result);
    }

    public InputStream sendGetRStream() throws Exception {
        logger.debug("sendGetRStream in");
        CloseableHttpClient httpClient = null;
        CloseableHttpResponse response = null;
        try {
            //get 请求
            HttpGet httpGet = new HttpGet(this.requestURL);
            //设置请求参数
            if (CollectionUtils.isNotEmpty(this.getMethodNvps)) {
                String urlParams = EntityUtils.toString(new UrlEncodedFormEntity(this.getMethodNvps, this.encode));
                httpGet.setURI(new URI(httpGet.getURI().toString() + "?" + urlParams));
            }
            logger.debug("sendGetRStream:[{}]", httpGet.getURI().toString());
            //设置超时时间
            setTimeouts(null, httpGet, METHOD_GET);

            // 设置请求头
            if (this.header != null) {
                for (Map.Entry<String, String> header : this.header.entrySet()) {
                    httpGet.addHeader(header.getKey(), header.getValue());
                }
            }

            if (doProxy && StringUtils.isNotBlank(proxyHost) && proxyPort > 0) {
                HttpHost proxy = new HttpHost(this.proxyHost.trim(), this.proxyPort, StringUtils.trimToNull(this.proxyScheme));
                RequestConfig build = RequestConfig.copy(httpGet.getConfig()).setProxy(proxy).build();
                httpGet.setConfig(build);
            }

            //创建连接
            httpClient = HttpClients.createDefault();
            //发送请求，获取响应对象
            response = httpClient.execute(httpGet);
            //处理响应对象，获取结果
            int statusCode = response.getStatusLine().getStatusCode();
            if (statusCode != HttpStatus.SC_OK) {
                return null;
            }
            HttpEntity responseEntity = response.getEntity();
            byte[] bytes = EntityUtils.toByteArray(responseEntity);
            InputStream resIs = new ByteArrayInputStream(bytes);
            EntityUtils.consume(responseEntity);
            return resIs;
        } catch (Exception ex){
            //jsonResult = "{\"error\":\"[HttpClientHelper][sendPost] exception: " + ex.getMessage() + "\"}";
            logger.error("sendGetRStream occur exception: {}", ex.getMessage(), ex);
            throw ex;
        } finally {
            close(httpClient, response);
            response = null;
            httpClient = null;
            logger.debug("sendGetRStream out");
        }
    }

}

