package com.xinxinsoft.utils.weChat;

import net.sf.json.JSONObject;
import org.apache.commons.codec.binary.Base64;
import org.apache.hadoop.hdfs.web.JsonUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * ----------------------------------------------------------
 * 　　　　　　　　talk is cheap，show me the code！
 * ----------------------------------------------------------
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.utils.weChat.AESTool
 * @description:
 * <pre>
 * AES加密工具
 *
 * <a href="http://tool.chacuo.net/cryptaes">在线加密工具地址参考</a>
 *
 * AES加密模式：ECB/CBC/CTR/OFB/CFB
 * 填充：pkcs5padding/pkcs7padding/zeropadding/iso10126/ansix923
 * 数据块：128位/192位/256位
 * 密码：【设置加解密的密码，JAVA中有效密码为16位/24位/32位， 其中24位/32位需要JCE（Java 密码扩展无限制权限策略文件， 每个JDK版本对应一个JCE，百度即可找到）】
 * 偏移量：【iv偏移量，ECB不用设置】
 * 输出：base64/hex
 * 字符集：gb2312/gbk/gb18030/utf8
 * </pre>
 *
 * @author: WF
 * @date: 2020-05-12 17:34
 **/
public class AESTool {
    private static Logger logger = LoggerFactory.getLogger(AESTool.class);
    /** 可配置到Constant中，并读取配置文件注入 16位 */
    private static final String KEY = "datalk88datalk88";

    /** 参数分别代表 算法名称/加密模式/数据填充方式 */
    private static final String ALGORITHMSTR = "AES/ECB/PKCS5Padding";
    private static final String ENCODING = StandardCharsets.UTF_8.name();

    /** 加密算法 AES */
    public static final String ALGORITHM = "AES";
    private static final int INITIALIZE_LENGTH = 128;


    public static SecretKey getKey(){
        try {
            KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM);
            keyGenerator.init(INITIALIZE_LENGTH);
            return keyGenerator.generateKey();
        } catch (Exception ex) {
            logger.error("{}", ex.getMessage(), ex);
            return null;
        }
    }

    public static String getkeyStr() {
        SecretKey key = getKey();
        byte[] encoded = key.getEncoded();
        return Base64.encodeBase64String(encoded);
    }

    /**
     * 加密
     *
     * @param content    加密的字符串
     * @param encryptKey key值
     *
     * @return 加密后的密文
     *
     */
    public static String encrypt(String content, String encryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance(ALGORITHM);
            kgen.init(INITIALIZE_LENGTH);
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), ALGORITHM));
            byte[] b = cipher.doFinal(content.getBytes(ENCODING));
            // 采用base64算法进行转码,避免出现中文乱码
            return Base64.encodeBase64String(b);
        } catch (Exception ex) {
            logger.error("{}", ex.getMessage(), ex);
            return null;
        }
    }

    /**
     * 解密
     *
     * @param encryptStr 解密的字符串
     * @param decryptKey 解密的key值
     *
     * @return 解密后的明文
     *
     */
    public static String decrypt(String encryptStr, String decryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance(ALGORITHM);
            kgen.init(INITIALIZE_LENGTH);
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), ALGORITHM));
            // 采用base64算法进行转码,避免出现中文乱码
            byte[] encryptBytes = Base64.decodeBase64(encryptStr);
            byte[] decryptBytes = cipher.doFinal(encryptBytes);
            return new String(decryptBytes);
        } catch (Exception ex) {
            logger.error("{}", ex.getMessage(), ex);
            return null;
        }
    }

    public static String decryptByte(byte[] encryptByte, String decryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance(ALGORITHM);
            kgen.init(INITIALIZE_LENGTH);
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(Cipher.DECRYPT_MODE, new SecretKeySpec(decryptKey.getBytes(), ALGORITHM));
            byte[] decryptBytes = cipher.doFinal(encryptByte);
            return new String(decryptBytes);
        } catch (Exception ex) {
            logger.error("{}", ex.getMessage(), ex);
            return null;
        }
    }

    public static byte[] encryptByte(String content, String encryptKey) {
        try {
            KeyGenerator kgen = KeyGenerator.getInstance(ALGORITHM);
            kgen.init(INITIALIZE_LENGTH);
            Cipher cipher = Cipher.getInstance(ALGORITHMSTR);
            cipher.init(Cipher.ENCRYPT_MODE, new SecretKeySpec(encryptKey.getBytes(), ALGORITHM));
            return cipher.doFinal(content.getBytes(ENCODING));
        } catch (Exception ex) {
            logger.error("{}", ex.getMessage(), ex);
           return null;
        }
    }

    public static String encrypt(String content) {
        return encrypt(content, KEY);
    }

    public static String decrypt(String encryptStr) {
        return decrypt(encryptStr, KEY);
    }


    public static void main(String[] args) throws Exception{
        System.out.println(getkeyStr());
        Map<String, String> map = new HashMap<String, String>(4);
        map.put("key", "value");
        map.put("中文", "汉字");
        String content = JSONObject.fromObject(map).toString();
        System.out.println("加密前：" + content);

        String encrypt = encrypt(content, KEY);
        System.out.println("加密后：" + encrypt);

        String decrypt = decrypt(encrypt, KEY);
        System.out.println("解密后：" + decrypt);
    }

}
