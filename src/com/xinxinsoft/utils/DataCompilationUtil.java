package com.xinxinsoft.utils;

import com.xinxinsoft.sendComms.CMCC1000OpenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.apache.commons.codec.binary.Base64;
import javax.crypto.Cipher;
import javax.crypto.SecretKey;
import javax.crypto.spec.SecretKeySpec;

public class DataCompilationUtil {
    private static Logger log = LoggerFactory.getLogger(DataCompilationUtil.class);
    /*private static final String ALGORITHM1 = "AES";
    private static final String KEY1 = "myKey10114129109";*/
    private static final String ALGORITHM = "DESede";
    private static final String KEY = "l0m8qnucCOw67IaJl0m8qnuc";

    /**
     * 解密
     * @param src
     * @return
     */
    public static String decryptFromBase64(String src){
        if(src==null)
            return "";
        try {
            if(src.contains(" +")){
                String str = src.replace(" +","+");
                return new String(decrypt(KEY.getBytes(), Base64.decodeBase64(str)), "UTF-8");
            }else{
                String decryptStr=new String(decrypt(KEY.getBytes(), Base64.decodeBase64(src)), "UTF-8");
                return decryptStr.length()<1? src : decryptStr;
            }
        } catch (Exception ex) {
            log.error("字段解密错误"+ex.getMessage());
        }
        return src;
    }

    /**
     * 加密
     * @param src
     * @return
     */
    public static String encryptFromBase64(String src){
        try {
            if(src!=null&&!"".equals(src)){
                String encryptedstr = Base64.encodeBase64String(encrypt(KEY.getBytes(),src.getBytes()));
                return encryptedstr ;
            }else{
                return src;
            }
        } catch (Exception ex) {
            log.error("字段加密错误"+ex.getMessage());
        }
        return src;
    }

    // keybyte为加密密钥，长度24字节
    // src为加密后的缓冲区
    public static byte[] decrypt(byte[] keybyte, byte[] src) throws Exception {
        // 生成密钥
        SecretKey deskey = new SecretKeySpec(keybyte, ALGORITHM);
        // 解密
        Cipher c1 = Cipher.getInstance(ALGORITHM);
        c1.init(Cipher.DECRYPT_MODE, deskey);
        return c1.doFinal(src);
    }

    public static byte[] encrypt(byte[] keybyte, byte[] src) throws Exception {
        // 生成密钥
        SecretKey deskey = new SecretKeySpec(keybyte, ALGORITHM);
        // 解密
        Cipher c1 = Cipher.getInstance(ALGORITHM);
        c1.init(Cipher.ENCRYPT_MODE, deskey);
        return c1.doFinal(src);
    }

    public static void main(String[] args) throws Exception {

        String plainText4 = "P+qxT+91fmE9ZVCj86AZGWJtsKR2HmHoWWUTW4VwTPjjciF1DA8UiA==";
        //String str=encryptFromBase64(plainText);
       //System.out.println(encryptFromBase64(plainText));
     /*   System.out.println("--->"+decryptFromBase64(plainText));
        System.out.println("--->"+decryptFromBase64(plainText1));
        System.out.println("--->"+decryptFromBase64(plainText2));
        System.out.println("--->"+decryptFromBase64(plainText3));*/
        System.out.println("--->"+decryptFromBase64(plainText4));
        /*byte[] plainBytes = plainText.getBytes(StandardCharsets.UTF_8);
        // 加密
        byte[] encryptedBytes = encrypt1(KEY1.getBytes(StandardCharsets.UTF_8), plainBytes);
        String encryptedText = Base64.encodeBase64String(encryptedBytes);
        System.out.println("加密结果：" + encryptedText);
        // 解密
        byte[] decryptedBytes = decrypt1(KEY1.getBytes(StandardCharsets.UTF_8), Base64.decodeBase64("mdWdpZJvUlC4QGhjyzeVmv3FOn+dLpIyFMK+f35nr58aWy1hL7/69Vf/71e/CWV0FI3WVyQpvR5m7rdNcB1gQsvvwJg0hnoJMcfQrayMtAkso8J2mthmywnt8niTSgN15xS1v7RNjU8E9v2KiotcVzqkGXHYp5knRF1ZtIjtaN8="));
        String decryptedText = new String(decryptedBytes, StandardCharsets.UTF_8);
        System.out.println("解密结果：" + decryptedText);*/

//        String paras="{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241743640091392\",\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\",\"ROUTING\":{\"ROUTE_KEY\":\"12\",\"ROUTE_VALUE\":\"**************\"}},\"BODY\":{\"APPLY_FEE\":\"81300\",\"ORDER_ID\":\"*******************\",\"UNIT_ID\":\"**********\",\"UNIT_NAME\":\"四川岷江新濠酒店有限公司\",\"CONTRACT_NO\":\"**************\",\"PHONE_NO\":\"***********\",\"INV_TYPE\":\"04\",\"BEGIN_CYCLE\":\"202504\",\"END_CYCLE\":\"202504\",\"OPR_TYPE\":\"0\",\"LOGIN_NO\":\"ajmwM0009\",\"TAX_PAYER\":\"91510181060081930G\",\"TAX_ADDRESS\":\"四川省成都市都江堰市都江堰大道388号\",\"TAX_PHONE\":\"028-********\",\"TAX_BANK_NAME\":\"工行成都都江堰支行\",\"TAX_BANK_ACCOUNT\":\"4402224019100156637\",\"BATCH_NO\":\"*******************\",\"BATCH_FLAG\":\"0\",\"TAX_RATE\":\"6\",\"EXPECT_REPAY_TIME\":\"*****************\"}}}";
//        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams("http://*************:51000/esbWS/rest/sPreInvApply", paras);
//        System.out.println("===>:"+resultStr);


    }


}