package com.xinxinsoft.utils;
import java.io.ByteArrayInputStream;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Set;

import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.dom4j.io.SAXReader;


public class XML2Map {
	private static final String OPTIONAL = "opt_";//选填
	private static final String STRIGULA = "str_";//-分隔
	private static final String HIDDEN = "hid_";//隐藏域
	
	@SuppressWarnings("rawtypes")
	public static Map<String,Object> transform(String xmlStr) throws Exception{
		Map<String, Object> params = new HashMap<String,Object>();
		try {
			SAXReader saxReader = new SAXReader();
			Document document = saxReader.read(new ByteArrayInputStream(xmlStr.getBytes("UTF-8")));
			Element root = document.getRootElement();
			List elementLists = root.elements();
			for(int i=0;i<elementLists.size();i++){
				Element element = (Element) elementLists.get(i);
				//截取
				String elementName = element.getName();
				String elementText = element.getText();
				if(elementText==null || elementText.equals("")){
					elementText= "";
				}
				if(elementName.length() >= 4){
					String start = elementName.substring(0, 4);
					switch(start){
					case OPTIONAL:
						if("".equals(elementText) || elementText == null){
							params.put(elementName, "/");
						}else{
							params.put(elementName, elementText);
						}
						break;
					case STRIGULA:
						if(StringUtils.isEmpty(elementName)){break;}
						String[] textArr = elementName.split("_");
						String tag = textArr[1];
						String choiceNum = textArr[2];
						String count = textArr[3];
						for(int m=0;m<Integer.parseInt(count);m++){
							int id = m + 1;
							if((m+1)==Integer.parseInt(choiceNum)){
								params.put("str_" + tag +"_" + choiceNum + "_" + count, "√");
							}else{
								params.put("str_" + tag +"_" + id + "_" + count, " ");
							}
						}
						
						String[] text = elementText.split("-");
						if(text.length == 3){
							params.put("str_" + tag +"_" + "4", " ");
							params.put("str_" + tag +"_" + "5", " ");
							params.put("str_" + tag +"_" + "6", " ");
						}
						else if(text.length == 4){
							params.put("str_" + tag +"_" + "4", text[3]);
							params.put("str_" + tag +"_" + "5", " ");
							params.put("str_" + tag +"_" + "6", " ");
						}else if(text.length == 5){
							params.put("str_" + tag +"_" + "4", text[3]);
							params.put("str_" + tag +"_" + "5", text[4]);
							params.put("str_" + tag +"_" + "6", " ");
						}else if(text.length == 6){
							params.put("str_" + tag +"_" + "4", text[3]);
							params.put("str_" + tag +"_" + "5", text[4]);
							params.put("str_" + tag +"_" + "6", text[5]);
						}
						break;
					case HIDDEN:
						String[] elementHid = elementName.split("_");
						if("selected".equals(elementHid[1])){
							fillInput(params, elementName, elementText,"selected_pic_tag");
						}else if("unselected".equals(elementHid[1])){
							fillInput(params, elementName, elementText,"unselected_pic_tag");
						}else if("express".equals(elementHid[1])){
							boolean flag = false;
							if(!"".equals(elementText) && elementText != null){
								String[] elements = elementText.split(",");
								int total = Integer.parseInt(elements[0].split("_")[3]);
								for(int j=0;j<total;j++){
									String tmp = "chk_opt_express_" + total + "_" + (j+1);
									for(int k=0;k<elements.length;k++){
										String ele = elements[k];
										if("".equals(ele)){
											break;
										}
										if(tmp.equals(ele)){
											params.put(tmp, "selected_pic_tag");
											flag = true;
											break;
										}
									}
									if(!flag){
										params.put(tmp, "unselected_pic_tag");
									}
									flag = false;
								}
							}
						}else if("input".equals(elementHid[1])){
							fillInput(params, elementName, elementText, "input");
						}
						break;
					default:
						params.put(elementName, elementText);
						break;
					}
				}else{
					params.put(elementName, elementText);
				}
			}
		} catch (DocumentException e) {
			e.printStackTrace();
		}
		return params;
	}

	//添加input参数值
	private static void fillInput(Map<String, Object> params,String elementName, String elementText,String type) {
		if("".equals(elementText)){
			return;
		}
		JSONObject jsonObject = JSONObject.fromObject(elementText);
		Set keySet = jsonObject.keySet();
		Iterator iterator = keySet.iterator();
		while(iterator.hasNext()){
			String next = (String) iterator.next();
			String nextValue = jsonObject.getString(next);
			if(!"".equals(next)){
				if("input".equals(type)){
					params.put(next, jsonObject.get(next));
				}else{
					String[] result = nextValue.split("-");
					if(result.length >= 2){
						String[] ids = result[1].split(",");
						for(int j=0;j<ids.length;j++){
							params.put(result[0] + "_" + ids[j], type);
						}
					}
				}
			}
		}
	}
}
