package com.xinxinsoft.utils.result;

import java.io.Serializable;

import com.google.gson.annotations.Expose;
import com.xinxinsoft.utils.JSONHelper;


/**
 * 统一API响应结果封装
 */
@SuppressWarnings("serial")
public class Result implements Serializable{
	@Expose
	private int code;
	@Expose
    private String message;
	@Expose
    private Object data;

    public Result setCode(ResultCode resultCode) {
        this.code = resultCode.code();
        return this;
    }

    public int getCode() {
        return code;
    }

    public String getMessage() {
        return message;
    }

    public Result setMessage(String message) {
        this.message = message;
        return this;
    }

    public Object getData() {
        return data;
    }

    public Result setData(Object data) {
        this.data = data;
        return this;
    }

    @Override
    public String toString() {
        return JSONHelper.SerializeWithNeedAnnotationDateFormats(this);
    }
}
