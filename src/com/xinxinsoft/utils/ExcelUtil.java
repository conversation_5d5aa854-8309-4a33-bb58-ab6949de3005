package com.xinxinsoft.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;


import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.DateUtil;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xinxinsoft.service.core.json.JSONArray;

/**
 * 读取Excel
 *
 * <AUTHOR>
 */
public class ExcelUtil {
	private Logger logger = LoggerFactory.getLogger(ExcelUtil.class);
	private Workbook wb;
	private Sheet sheet;
	private Row row;

	public ExcelUtil(File filepath) {
		if(filepath==null){
			return;
		}
		System.out.println(filepath.getName());
		String ext = filepath.getPath().substring(filepath.getPath().lastIndexOf("."));
		try {
			InputStream is = new FileInputStream(filepath);
			/*if(".xls".equals(ext)){
				wb = new HSSFWorkbook(is);
			}else if(".xlsx".equals(ext)){*/
				wb = new XSSFWorkbook(is);
			/*}else{
				wb=null;
			}*/
        } catch (FileNotFoundException e) {
            logger.error("FileNotFoundException", e);
        } catch (IOException e) {
            logger.error("IOException", e);
        }
    }

    /**
     * 读取Excel表格表头的内容
     *
     * @param InputStream
     * @return String 表头内容的数组
     * <AUTHOR>
     */
    public String[] readExcelTitle() throws Exception {
        if (wb == null) {
            throw new Exception("Workbook对象为空！");
        }
        sheet = wb.getSheetAt(0);
        row = sheet.getRow(0);
        // 标题总列数
        int colNum = row.getPhysicalNumberOfCells();
        System.out.println("colNum:" + colNum);
        String[] title = new String[colNum];
        for (int i = 0; i < colNum; i++) {
            // title[i] = getStringCellValue(row.getCell((short) i));
            title[i] = row.getCell(i).getCellFormula();
        }
        return title;
    }

    /**
     * 读取Excel数据内容
     *
     * @param InputStream
     * @return Map 包含单元格数据内容的Map对象
     * <AUTHOR>
     */
    public Map<Integer, Map<Integer, Object>> readExcelContent() throws Exception {
        if (wb == null) {
            throw new Exception("Workbook对象为空！");
        }
        Map<Integer, Map<Integer, Object>> content = new HashMap<Integer, Map<Integer, Object>>();

        sheet = wb.getSheetAt(0);
        // 得到总行数
        int rowNum = sheet.getLastRowNum();
        row = sheet.getRow(0);
        int colNum = row.getPhysicalNumberOfCells();
        // 正文内容应该从第二行开始,第一行为表头的标题
        for (int i = 1; i <= rowNum; i++) {
            row = sheet.getRow(i);
            int j = 0;
            Map<Integer, Object> cellValue = new HashMap<Integer, Object>();
            while (j < colNum) {
                Cell cell = row.getCell(j);
                cell.setCellType(cell.CELL_TYPE_STRING);
                Object obj = getCellFormatValue(cell);
                cellValue.put(j, obj);
                j++;
            }
            content.put(i, cellValue);
        }
        return content;
    }

    //读取Excel数据内容,忽略为空的数据
    public Map<Integer, Map<Integer, Object>> readExcelContentTwo() throws Exception {
        if (wb == null) {
            throw new Exception("Workbook对象为空！");
        }
        Map<Integer, Map<Integer, Object>> content = new HashMap<Integer, Map<Integer, Object>>();

        sheet = wb.getSheetAt(0);
        // 得到总行数
        int rowNum = sheet.getLastRowNum();
        row = sheet.getRow(0);
        int colNum = row.getPhysicalNumberOfCells();
        // 正文内容应该从第二行开始,第一行为表头的标题
        for (int i = 1; i <= rowNum; i++) {
            row = sheet.getRow(i);
            int j = 0;
            Map<Integer, Object> cellValue = new HashMap<Integer, Object>();
            while (j < colNum) {
                Cell cell = row.getCell(j);
                if (cell == null) {
                    cellValue.put(j, null);
                } else {
                    cell.setCellType(cell.CELL_TYPE_STRING);
                    Object obj = getCellFormatValue(cell);
                    cellValue.put(j, obj);
                }
                j++;
            }
            content.put(i, cellValue);
        }
        return content;
    }

    /**
     * 根据Cell类型设置数据
     *
     * @param cell
     * @return
     * <AUTHOR>
     */
    private Object getCellFormatValue(Cell cell) {
        Object cellvalue = "";
        if (cell != null) {
            // 判断当前Cell的Type
            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_NUMERIC:// 如果当前Cell的Type为NUMERIC
                case Cell.CELL_TYPE_FORMULA: {
                    // 判断当前的cell是否为Date
                    if (DateUtil.isCellDateFormatted(cell)) {
                        // 如果是Date类型则，转化为Data格式
                        // data格式是带时分秒的：2013-7-10 0:00:00
                        // cellvalue = cell.getDateCellValue().toLocaleString();
                        // data格式是不带带时分秒的：2013-7-10
                        Date date = cell.getDateCellValue();
                        cellvalue = date;
                    } else {// 如果是纯数字

                        // 取得当前Cell的数值
                        cellvalue = String.valueOf(cell.getNumericCellValue());
                    }
                    break;
                }
                case Cell.CELL_TYPE_STRING:// 如果当前Cell的Type为STRING
                    // 取得当前的Cell字符串
                    cellvalue = cell.getRichStringCellValue().getString();
                    break;
                default:// 默认的Cell值
                    cellvalue = "";
            }
        } else {
            cellvalue = "";
        }
        return cellvalue;
    }
 
	/*public static void main(String[] args) {
		try {
			String filepath = "G:test.xlsx";
			ExcelUtil excelReader = new ExcelUtil(filepath);
			// 对读取Excel表格标题测试
//			String[] title = excelReader.readExcelTitle();
//			System.out.println("获得Excel表格的标题:");
//			for (String s : title) {
//				System.out.print(s + " ");
//			}
			
			// 对读取Excel表格内容测试
			Map<Integer, Map<Integer,Object>> map = excelReader.readExcelContent();
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(map);
			System.out.println(json);
			List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
			System.out.println("获得Excel表格的内容:");
			for (int i = 1; i <= map.size(); i++) {
				Map<String, Object> maps = new HashMap<String, Object>();
				maps.put("zhanghu",map.get(i).get(0));
				maps.put("jine",map.get(i).get(1));
				maps.put("leixing",map.get(i).get(2));
				list.add(maps);
				System.out.println(map.get(i).get(1));
				
			}
			String json2 =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(list);
			System.out.println(json2);
		} catch (FileNotFoundException e) {
			System.out.println("未找到指定路径的文件!");
			e.printStackTrace();
		}catch (Exception e) {
			e.printStackTrace();
		}
	}*/
}
