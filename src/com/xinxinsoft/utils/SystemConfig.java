package com.xinxinsoft.utils;

import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.Element;
import org.springframework.core.io.ClassPathResource;


/**
 * 系统相关配置
 * @Title PermissionConfig.java
 * @Package com.xinxinsoft.service.utils
 * <AUTHOR> QQ：361229957 E-mail：<EMAIL>
 * @date 2016-4-15 下午5:02:25
 * @version 1.0 
 */
public class SystemConfig {
	
	private static SystemConfig PERMISSION_CONFIG = new SystemConfig();
	
	/**
	 * 登录页面URL
	 */
	public static String LOGIN_URL = "";
	
	/**
	 * 注销URL
	 */
	public static String LOGOUT_URL = "";
	
	/**
	 * 匿名用户可访问资源
	 */
	public static List<String> ANONYMOUS_RESOURCE_STRING = new ArrayList<String>();
	
	/**
	 * 匿名用户可访问资源
	 */
	public static List<Pattern> ANONYMOUS_RESOURCE_PATTERN = new ArrayList<Pattern>();
	
	/**
	 * 登录界面中表单元素
	 */
	private LoginPageFormItems loginPageFormItems = new LoginPageFormItems();
	
	/**
	 * 保存到session中的属性
	 */
	private SessionItems sessionItems = new SessionItems();

	
	/**
	 * 实例
	 * @return
	 */
	public static SystemConfig instance(){
		return PERMISSION_CONFIG;
	}
	
	/**
	 * 初始化配置
	 * @param permissionConfigFile LoginFilter中配置
	 */
	public void init(String systemConfigFile){
		System.out.println("初始化配置systemConfigFile");
		Document document;
		Element element;
		Element elementChild;
		try {
//			String filterConfigFile = arg0.getInitParameter("permissionConfigFile");
			document = XmlUtil.getDocument(new ClassPathResource(systemConfigFile).getFile());
			Element root = XmlUtil.getRoot(document);
			Element anonymous_resource = root.element("anonymous-resource");
			List<Element> list = XmlUtil.getElementsByName(anonymous_resource, "url-pattern");
			if(list != null){
				for(Element e : list){
//					ANONYMOUS_RESOURCE_STRING.add(e.getTextTrim());
					ANONYMOUS_RESOURCE_PATTERN.add(Pattern.compile(e.getTextTrim()));
				}
			}
			element = root.element("login-url");
			elementChild = XmlUtil.getElementByName(element, "url-pattern");
			LOGIN_URL = elementChild.getTextTrim();
			
			element = root.element("logout-url");
			elementChild = XmlUtil.getElementByName(element, "url-pattern");
			LOGOUT_URL = elementChild.getTextTrim();
			
			element = root.element("login-page-form-items");
			elementChild = XmlUtil.getElementByName(element, "username");
			loginPageFormItems.setUsername(elementChild.getTextTrim());
			elementChild = XmlUtil.getElementByName(element, "password");
			loginPageFormItems.setPassowrd(elementChild.getTextTrim());
			
			element = root.element("session-items");
			elementChild = XmlUtil.getElementByName(element, "currentLoginUser");
			sessionItems.setCurrentLoginUser(elementChild.getTextTrim());
			elementChild = XmlUtil.getElementByName(element, "userLoginName");
			sessionItems.setUserLoginName(elementChild.getTextTrim());
			elementChild = XmlUtil.getElementByName(element, "username");
			sessionItems.setUsername(elementChild.getTextTrim());
			elementChild = XmlUtil.getElementByName(element, "menu");
			sessionItems.setMenu(elementChild.getTextTrim());
			elementChild = XmlUtil.getElementByName(element, "result");
			sessionItems.setResult(elementChild.getTextTrim());
			
			elementChild = XmlUtil.getElementByName(element, "priority");
			sessionItems.setPriority(elementChild.getTextTrim());

//			element = root.element("news-items");
//			elementChild = XmlUtil.getElementByName(element, "ContentHtml");
//			newsItems.setContentHtml(elementChild.getTextTrim());
		} catch (DocumentException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	/**
	 * 是否有权限(正则) - Pattern
	 * @return
	 */
	public boolean hasPermissionPattern(List<Pattern> pList, String url){
		boolean b = false;
		if(pList != null && pList.size() > 0 && url != null && !"".equals(url)){
			for(Pattern p : pList){
				b = p.matcher(url).find();
				if(b){
					break;
				}
			}
		}
		return b;
	}
	
	/**
	 * 是否有权限 - String
	 * @return
	 */
	public boolean hasPermissionString(List<String> pList, String url){
		boolean b = false;
		if(pList != null && pList.size() > 0 && url != null && !"".equals(url)){
			for(String p : pList){
				b = url.contains(p);
				if(b){
					break;
				}
			}
		}
		return b;
	}
	
	/**
	 * 是否是注销
	 * @param url
	 * @return
	 */
	public boolean isLogout(String url){
		if(url != null){
			return url.contains(LOGOUT_URL);
		}
		return false;
	}
	
	/**
	 * 跳转到登录页面
	 * @param req
	 * @param response
	 * @throws IOException
	 */
	public void gotoLoginPage(HttpServletRequest req, HttpServletResponse response) throws IOException{
		response.sendRedirect(req.getContextPath() + LOGIN_URL);
	}
	
	public LoginPageFormItems getLoginPageFormItems() {
		return loginPageFormItems;
	}

	public void setLoginPageFormItems(LoginPageFormItems loginPageFormItems) {
		this.loginPageFormItems = loginPageFormItems;
	}

	public SessionItems getSessionItems() {
		return sessionItems;
	}

	public void setSessionItems(SessionItems sessionItems) {
		this.sessionItems = sessionItems;
	}

	
	/**
	 * 登录界面中表单元素
	 * @Title PermissionConfig.java
	 * @Package com.xinxinsoft.service.utils
	 * <AUTHOR> QQ：361229957 E-mail：<EMAIL>
	 * @date 2016-4-15 下午5:28:48
	 * @version 1.0
	 */
	public class LoginPageFormItems{
		//用户名
		private String username;
		//密码
		private String passowrd;
		/*//验证码
		private String code;*/

		public String getUsername() {
			return username;
		}

		public void setUsername(String username) {
			this.username = username;
		}

		public String getPassowrd() {
			return passowrd;
		}

		public void setPassowrd(String passowrd) {
			this.passowrd = passowrd;
		}
	}
	
	/**
	 * 保存到session中的属性
	 * @Title PermissionConfig.java
	 * @Package com.xinxinsoft.service.utils
	 * <AUTHOR> QQ：361229957 E-mail：<EMAIL>
	 * @date 2016-4-15 下午5:31:01
	 * @version 1.0
	 */
	public class SessionItems{
		private String currentLoginUser;
		private String userLoginName;
		private String username;
		private String menu;
		private String result;
		private String priority;
		
		
		public String getPriority() {
			return priority;
		}
		public void setPriority(String priority) {
			this.priority = priority;
		}
		public String getCurrentLoginUser() {
			return currentLoginUser;
		}
		public void setCurrentLoginUser(String currentLoginUser) {
			this.currentLoginUser = currentLoginUser;
		}
		public String getUserLoginName() {
			return userLoginName;
		}
		public void setUserLoginName(String userLoginName) {
			this.userLoginName = userLoginName;
		}
		public String getUsername() {
			return username;
		}
		public void setUsername(String username) {
			this.username = username;
		}
		public String getMenu() {
			return menu;
		}
		public void setMenu(String menu) {
			this.menu = menu;
		}
		public String getResult() {
			return result;
		}
		public void setResult(String result) {
			this.result = result;
		}
	}

	
}
