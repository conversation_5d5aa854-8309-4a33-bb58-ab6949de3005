package com.xinxinsoft.utils;

import java.text.DecimalFormat;
import java.text.Format;
import java.text.SimpleDateFormat;
import java.util.Date;

public class EOMCODEUtil {
	//private static final int MAX_VALUE=99999;
    private static final String FORMAT = "yyyyMMdd";
    private static final Format DF= new SimpleDateFormat(FORMAT);
    
    private static final String COUNTFORMAT="00000";
    private static final DecimalFormat CF=new DecimalFormat(COUNTFORMAT);
    
    
    private static String CompanyCode="CD";
    
    private static String BusinessCode="0";
    private static String ProductCode="000";
    
    private static String TBusinessCode="9";
    private static String TProductCode="999";
    /**
     * 主订单CODE生成
     * @param companyCode  公司编码
     * @param currentCount 当天订单条数
     * @return
     */
	public static String getTHostCode(String companyCode,int currentCount){
		CompanyCode=companyCode;
		String Code=CompanyCode+TBusinessCode+TProductCode+DF.format(new Date())+CF.format(currentCount+1);
		return Code;
	}
	/**
     * 主订单CODE生成
     * @param companyCode  公司编码
     * @param currentCount 当天订单条数
     * @return
     */
	public static String getHostCode(String companyCode,int currentCount){
		CompanyCode=companyCode;
		String Code=CompanyCode+BusinessCode+ProductCode+DF.format(new Date())+CF.format(currentCount+1);
		return Code;
	}
	
	/**
	 * 子订单CODE生成
	 * @param companyCode  公司编码
	 * @param businessCode 业务编码
	 * @param productCode  产品编码
	 * @param currentCount 当天订单条数
	 * @return
	 */
	public static String getSonCode(String companyCode,String businessCode,String productCode,int currentCount){
		CompanyCode=companyCode;
		BusinessCode=businessCode;
		ProductCode=productCode;
		String Code=CompanyCode+BusinessCode+ProductCode+DF.format(new Date())+CF.format(currentCount+1);
		return Code;
	}
	
	
	public void USESQL(){
		//String="";
	}
    public static void main(String[] args) {
    	String code=EOMCODEUtil.MakeOrderNo(10);
    	System.out.println(code);
    }

	/*
	 * 生成流水号， YYYMMDDHHMMSS+randomCount位随机数
	 */
	public static String MakeOrderNo(int randomCount) {
		String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
		String randomStr = "";
		for (int i = 0; i < randomCount; i++) {
			int x = (int) (Math.random() * 10);
			randomStr = randomStr + x;
		}
		return timeStamp + randomStr;
	}

}
