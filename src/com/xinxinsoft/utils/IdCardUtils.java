package com.xinxinsoft.utils;

public abstract class IdCardUtils {
    /** 中国公民身份证号码最小长度。 */
    public static final int CHINA_ID_MIN_LENGTH = 15;

    /** 中国公民身份证号码最大长度。 */
    public static final int CHINA_ID_MAX_LENGTH = 18;

    public static Boolean verification(String idCard){
        return idCard == null || CHINA_ID_MIN_LENGTH > idCard.length();
    }

    /**
     * 根据身份编号获取生日
     *
     * @param idCard 身份编号
     * @return 生日(yyyyMMdd)
     */
    public static String getBirthByIdCard(String idCard) {
        return verification(idCard) ? null : idCard.substring(6, 14);
    }

}
