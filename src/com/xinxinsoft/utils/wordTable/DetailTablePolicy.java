package com.xinxinsoft.utils.wordTable;

import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.policy.DynamicTableRenderPolicy;
import com.deepoove.poi.policy.MiniTableRenderPolicy;
import org.apache.poi.xwpf.usermodel.XWPFTable;
import org.apache.poi.xwpf.usermodel.XWPFTableRow;

import java.util.List;

/**
 * @ClassName: DetailTablePolicy
 * @Title: DetailTablePolicy
 * @Package: com.xinxinsoft.utils
 * @author: liyang
 * @date: 2022/7/28 9:55
 * @Version: 1.0
 * @Description: TODO
 */
public class DetailTablePolicy extends DynamicTableRenderPolicy {
    //填充数据所在行数
    int goodsStartRow = 11;
    @Override
    public void render(XWPFTable table, Object data) {
        if (null == data) return;
        DetailData detailData = (DetailData) data;
        List<RowRenderData> goods = detailData.getGoods();
        if (null != goods) {
            table.removeRow(goodsStartRow);
            for (int i = 0; i < goods.size(); i++) {
                XWPFTableRow insertNewTableRow = table.insertNewTableRow(goodsStartRow);
                for (int j = 0; j < goods.get(i).size(); j++) insertNewTableRow.createCell();
                MiniTableRenderPolicy.Helper.renderRow(table, goodsStartRow, goods.get(i));
            }
        }
    }
}
