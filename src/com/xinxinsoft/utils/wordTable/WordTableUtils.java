package com.xinxinsoft.utils.wordTable;

import com.deepoove.poi.XWPFTemplate;
import com.deepoove.poi.config.Configure;
import com.deepoove.poi.data.RowRenderData;
import com.deepoove.poi.data.TextRenderData;
import com.deepoove.poi.data.style.Style;
import com.deepoove.poi.data.style.TableStyle;
import org.apache.log4j.Logger;

import java.io.FileOutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: WordTableUtils
 * @Title: WordTableUtils
 * @Package: com.xinxinsoft.utils
 * @author: liyang
 * @date: 2022/7/28 15:04
 * @Version: 1.0
 * @Description: TODO Word填充表格工具
 */
public class WordTableUtils {
    private static final Logger logger = Logger.getLogger(WordTableUtils.class);
    public static void setWordeTableContent(List<Map<String,Object>> listMap,String filePath,String outFilePath,String key){
        try {
            Style style = new Style();
            style.setFontSize(9);
            TableStyle rowStyle = new TableStyle();
            List<RowRenderData> list = new ArrayList<>();
            ContractInfoData datas = new ContractInfoData();
            DetailData detailTable = new DetailData();
            for (int i = 0; i < listMap.size(); i++) {
                Map<String,Object> map = listMap.get(i);
                if(map.size()==4){
                    RowRenderData good = RowRenderData.build(
                            map.get("price").toString(),
                            map.get("unitPrice").toString(),
                            map.get("unitType").toString(),
                            map.get("unitNumber").toString());
                    good.setRowStyle(rowStyle);
                    list.add(good);
                }else if(map.size()==6){
                    RowRenderData good = RowRenderData.build(
                            map.get("businessName").toString(),
                            map.get("specifications").toString(),
                            map.get("quantity").toString(),
                            map.get("operatingSystemType").toString(),
                            map.get("catalogueCatalogue").toString(),
                            map.get("discountPrice").toString());
                    good.setRowStyle(rowStyle);
                    list.add(good);
                }
            }
            detailTable.setGoods(list);
            datas.setDetailTable(detailTable);
            Configure.ConfigureBuilder builder = Configure.newBuilder();
            builder.buildGramer("[[", "]]");
            Configure config = builder.customPolicy(key, new DetailTablePolicy()).build();
            XWPFTemplate template = XWPFTemplate.compile(filePath, config).render(datas);
            FileOutputStream out = new FileOutputStream(outFilePath);
            template.write(out);
            out.flush();
            out.close();
            template.close();
        }catch (Exception e){
            logger.error("Word填充表格内容错误"+e.getMessage(),e);
        }
    }
}
