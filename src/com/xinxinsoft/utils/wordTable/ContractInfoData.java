package com.xinxinsoft.utils.wordTable;

import com.deepoove.poi.config.Name;

/**
 * @ClassName: PaymentData
 * @Title: PaymentData
 * @Package: com.xinxinsoft.utils
 * @author: liyang
 * @date: 2022/7/28 10:34
 * @Version: 1.0
 * @Description: TODO
 */
public class ContractInfoData {
    @Name("detail_table")
    private DetailData detailTable;
    public void setDetailTable(DetailData detailTable) {
        this.detailTable = detailTable;
    }

    public DetailData getDetailTable() {
        return this.detailTable;
    }
}
