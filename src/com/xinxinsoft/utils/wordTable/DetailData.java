package com.xinxinsoft.utils.wordTable;

import com.deepoove.poi.data.RowRenderData;

import java.util.List;

/**
 * @ClassName: DetailData
 * @Title: DetailData
 * @Package: com.xinxinsoft.utils
 * @author: liyang
 * @date: 2022/7/28 10:27
 * @Version: 1.0
 * @Description: TODO
 */
public class DetailData {
    // 货品数据
    private List<RowRenderData> goods;
    public List<RowRenderData> getGoods() {
        return goods;
    }
    public void setGoods(List<RowRenderData> goods) {
        this.goods = goods;
    }
}
