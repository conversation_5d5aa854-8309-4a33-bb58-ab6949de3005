package com.xinxinsoft.utils;

import com.aspose.words.SaveFormat;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.pdf.*;
import com.spire.doc.*;
import com.spire.doc.Document;
import com.spire.doc.documents.Paragraph;
import com.spire.doc.documents.UnderlineStyle;
import com.spire.doc.documents.VerticalOrigin;
import com.spire.doc.fields.DocPicture;
import com.spire.doc.fields.TextRange;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.STMerge;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.*;
import java.util.Map.Entry;

public class BokeWordToPdfUtilsTwo {
    private static Logger logger = LoggerFactory.getLogger(XMlToDoc.class);


    public static boolean changWord(String inputUrl, String outUrl, Map<String, String> textMap, List<String[]> tableList, String companyName, String fileName, String ftpUrl) {
        FileOutputStream stream = null;
        com.aspose.words.Document doc = null;
        boolean changeFlag = true;
        try {
            XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(inputUrl));

            BokeWordToPdfUtilsTwo.changeText(document, textMap);

            BokeWordToPdfUtilsTwo.changeTable(document, textMap, tableList);

            File file = new File(outUrl);
            stream = new FileOutputStream(file);
            document.write(stream);

            String pdfname = outUrl.substring(0, outUrl.indexOf(".")) + "(1)" + ".pdf";//生成的pdf路径(无水印)
            String newname = outUrl.substring(0, outUrl.indexOf(".")) + ".pdf";//生成的pdf路径(有水印)

            //添加二维码
            DocOrpdfUtil.createCodeToFile(textMap.get("orderNo"), new File(ftpUrl), fileName);//生成二维码
            com.spire.doc.Document docu = new Document(outUrl);
            Section sec = docu.getSections().get(0);
            AddHeaderFooter(sec, ftpUrl + fileName, textMap.get("orderNo")); //调用方法添加页眉页脚
            docu.saveToFile(outUrl);//保存文档
            docu.close();


            //docx转pdf
            file = new File(pdfname);
            stream = new FileOutputStream(file);
            doc = new com.aspose.words.Document(outUrl);
            doc.save(stream, SaveFormat.PDF);
            stream.close();

            //水印
            addWater(pdfname, newname, "/EOMAPP/UploadFiles/AndFlySpeed/zgyd.png", "四川移动" + companyName);//正式
//            addWater(pdfname, newname, "F:\\FTPONE\\zgyd.png", "四川移动" + companyName);//本地
            file = new File(outUrl);
            file.delete();//删除生成的word
            file = new File(pdfname);
            file.delete();//删除无水印的pdf
            file = new File(ftpUrl + fileName);
            file.delete();//删除二维码图片
        } catch (Exception e) {
            e.printStackTrace();
            changeFlag = false;
        } finally {
            try {
                if (stream != null) {
                    stream.close();
                }

            } catch (Exception e) {
                logger.info("文档转pdf 关闭 异常", e);
            }
        }
        return changeFlag;
    }

    //自定义方法来添加图片、文字页眉及页码
    public static void AddHeaderFooter(Section sec, String filePath2, String orderNo) {
        //加载图片添加到页眉，并设置图片在段落中的对齐方式
        HeaderFooter header = sec.getHeadersFooters().getHeader();
        Paragraph hpara = header.addParagraph();
        TextRange txt = hpara.appendText("编码:" + orderNo);
        txt.getCharacterFormat().setUnderlineStyle(UnderlineStyle.None);
        txt.getCharacterFormat().setFontName("仿宋");
        txt.getCharacterFormat().setFontSize(15f);

        TextRange txt1 = hpara.appendText("                       ");
        txt1.getCharacterFormat().setUnderlineStyle(UnderlineStyle.None);
        txt1.getCharacterFormat().setTextColor(Color.GRAY);
        txt1.getCharacterFormat().setFontName("仿宋");
        txt1.getCharacterFormat().setFontSize(14f);
        txt1.getCharacterFormat().setBold(true);

        DocPicture pic = hpara.appendPicture(filePath2);
        pic.setWidth(70);
        pic.setHeight(70);
    }

    public static void changeText(XWPFDocument document, Map<String, String> textMap) {
        //修改页眉
        List<XWPFHeader> headerList = document.getHeaderList();
        for (XWPFHeader header : headerList) {
            List<XWPFParagraph> paragraphs = header.getParagraphs();
            for (XWPFParagraph xwpfParagraph : paragraphs) {
                List<XWPFRun> runs = xwpfParagraph.getRuns();
                for (XWPFRun run : runs) {
                    String textValue = changeValue(run.toString(), textMap);
                    run.setText(textValue, 0);
                }
            }
        }
        //修改内容
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            String text = paragraph.getText();
            if (chekText(text)) {
                List<XWPFRun> runs = paragraph.getRuns();
                for (XWPFRun run : runs) {
                    String textValue = changeValue(run.toString(), textMap);
                    run.setText(textValue, 0);
                }
            }
        }
    }

    public static void changeTable(XWPFDocument document, Map<String, String> textMap, List<String[]> tableList) {
        List<XWPFTable> tables = document.getTables();
        for (int i = 0; i < tables.size(); i++) {
            XWPFTable table = tables.get(i);
            if (table.getRows().size() >= 1) {
                if (chekText(table.getText())) {
                    List<XWPFTableRow> tableRows = table.getRows();
                    eachTable(tableRows, textMap);
                } else {
                    insertTable(table, tableList);
                }
            }
        }
    }

    public static void eachTable(List<XWPFTableRow> tableRows, Map<String, String> textMap) {
        for (XWPFTableRow row : tableRows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                if (chekText(cell.getText())) {
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    for (XWPFParagraph paragraph : paragraphs) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        for (XWPFRun run : runs) {
                            run.setText(changeValue(run.toString(), textMap), 0);
                        }
                    }
                }
            }
        }
    }

    /**
     * @param table
     * @param col
     * @param fromRow
     * @param toRow
     */
    public static void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            if (rowIndex == fromRow) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }


    //添加表格填充数据
    public static void insertTable(XWPFTable table, List<String[]> tableList) {
        for (int i = 0; i < tableList.size(); i++) {
            XWPFTableRow row = table.createRow();
        }

        mergeCellsVertically(table, 0, 0, tableList.size());
        List<XWPFTableRow> rows = table.getRows();
        for (int i = 1; i < rows.size(); i++) {
            XWPFTableRow newRow = rows.get(i);
            List<XWPFTableCell> cells = newRow.getTableCells();
            for (int j = 1; j < cells.size(); j++) {
                XWPFTableCell cell = cells.get(j);
                cell.setText(tableList.get(i - 1)[j - 1]);
            }
        }
    }

    public static boolean chekText(String text) {
        Boolean textFalg = false;
        if (text.indexOf("$") != -1) {
            textFalg = true;
        }
        return textFalg;
    }

    public static String changeValue(String value, Map<String, String> textMap) {
        Set<Entry<String, String>> textSets = textMap.entrySet();
        for (Entry<String, String> textSet : textSets) {
            String Key = "${" + textSet.getKey() + "}";
            if (value.indexOf(Key) != -1) {
                value = value.replace(Key, textSet.getValue());
            }
        }
        if (chekText(value)) {
            value = "";
        }
        return value;
    }

    public static void addWater(String fileName, String savepath, String imgFile, String companyWaterMark) {
        // 文档总页数
        int num = 0;
        com.lowagie.text.Document document = new com.lowagie.text.Document();
        try {
            PdfReader reader = new PdfReader(fileName);
            BaseFont base = BaseFont.createFont("STSong-Light", "UniGB-UCS2-H",
                    BaseFont.EMBEDDED);
            num = reader.getNumberOfPages();
            PdfCopy copy = new PdfCopy(document, new FileOutputStream(savepath));
            PdfGState gs = new PdfGState();
            document.open();
            com.lowagie.text.Rectangle pageSizeWithRotation = null;
            for (int i = 0; i < num; ) {
                PdfImportedPage page = copy.getImportedPage(reader, ++i);
                PdfCopy.PageStamp stamp = copy.createPageStamp(page);
                Font f = new Font(base);
                // 添加水印
                PdfContentByte under = stamp.getOverContent();
                gs.setFillOpacity(0.3f);
                under.setGState(gs);
                under.beginText();
                under.setColorFill(Color.LIGHT_GRAY);//字符颜色
                under.setCharacterSpacing(9);//字符间距

                // 设置字体大小
                under.setFontAndSize(base, 20);

                // 获取每一页的高度、宽度
                pageSizeWithRotation = reader.getPageSizeWithRotation(i);
                float pageHeight = pageSizeWithRotation.getHeight();//页面高
                float pageWidth = pageSizeWithRotation.getWidth();//页面宽

                // 间隔
                int interval = 1;
                // 获取水印文字的高度和宽度
                int textH = 0, textW = 0;
                JLabel label = new JLabel();
                label.setText(companyWaterMark);
                FontMetrics metrics = label.getFontMetrics(label.getFont());
                textH = metrics.getHeight();
                textW = metrics.stringWidth(label.getText());

                //循环加水印
                for (int height = interval + textH; height < pageHeight; height = height + textH * 11) {
                    for (int width = interval + textW; width < pageWidth + textW; width = width + textW * 8) {
                        under.showTextAligned(Element.ALIGN_LEFT, companyWaterMark, 160, (height - textH) + 50, 15); //添加水印设置坐标 参数:(位置,水印文本,x轴,y轴,旋转角度)
                        under.showTextAligned(Element.ALIGN_LEFT, getDateToStrTimestamp(new Date()), 180, (height - textH) + 20, 15);//添加水印设置坐标 参数:(位置,水印文本,x轴,y轴,旋转角度)
                        //添加图片
                        com.lowagie.text.Image image = com.lowagie.text.Image.getInstance(imgFile);//获取图片
                        image.setAbsolutePosition(80, (height - textH) - 10);//添加图片设置坐标 参数(x轴,y轴)
                        // 设置图片的显示大小
                        image.scaleToFit(65, 65);
                        image.setRotationDegrees(15);//旋转角度
                        under.addImage(image);
                    }
                }

                // 字体设置结束
                under.endText();
                stamp.alterContents();
                copy.addPage(page);
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (null != document) {
                document.close();
            }
        }
    }

    /**
     * 根据水印文字长度计算获取字体大小
     *
     * @param waterMarkName
     * @return
     */
    private static int getFontSize(String waterMarkName) {
        int fontSize = 80;
        if (null != waterMarkName && !"".equals(waterMarkName)) {
            int length = waterMarkName.length();
            if (length <= 26 && length >= 18) {
                fontSize = 26;
            } else if (length < 18 && length >= 8) {
                fontSize = 40;
            } else if (length < 8 && length >= 1) {
                fontSize = 80;
            } else {
                fontSize = 40;
            }
        }
        return fontSize;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getDateToStrTimestamp(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

}
