package com.xinxinsoft.utils;

/**
 * 公用审计日志enum
 */
public enum LogsEnum {

	/**
	 * 登录审计日志
	 */
    /**
     * 4A和EIP登录
     */
    LOGIN4A("login4A", "4A和EIP登录", "1-SCNGDDXT-10001", "4A和EIP登录订单系统"),

    /**
     * 绿网登录
     */
    LOGIN("login", "绿网登录", "1-SCNGDDXT-10028", "绿网登录"),

    /**
     * 订单系统登录
     */
    LOGINORDER("loginOrder", "订单系统登录", "1-SCNGDDXT-10029", "订单系统登录"),

	/**
	 * 角色管理日志
	 */
	/**
	 * 用户角色权限修改
	 */
	UPDATEUSERINFO("updateUserInfo", "角色管理", "1-SCNGDDXT-10002", "用户角色权限修改"),

	/**
	 * 用户角色新增
	 */
	ADDROLE("addRole", "添加角色", "1-SCNGDDXT-10003", "用户角色新增"),

	/**
	 * 用户角色删除
	 */
	DELETEROLE("deleteRole", "删除角色", "1-SCNGDDXT-10004", "用户角色删除"),

	/**
	 * 用户角色修改
	 */
	UPDATEROLE("updateRole", "修改角色", "1-SCNGDDXT-10005", "用户角色修改"),

	/**
	 * 消息日志
	 */
	/**
	 * 消息通知新增
	 */
	ADDMESSAGESUSER("addMessagesUser", "消息通知", "1-SCNGDDXT-10006", "消息通知新增"),

	/**
	 * 附件操作日志
	 */
	/**
	 * 删除附件
	 */
	DELETEFILE("deleteFile", "删除附件", "1-SCNGDDXT-10028", "删除附件"),

	/**
	 * 附件上传
	 */
	UPLOADFILE("uploadFile", "附件上传", "1-SCNGDDXT-10007", "附件上传"),

	/**
	 * 附件下载
	 */
	ATTACHMENTUPLOAD("attachmentUpload", "附件下载", "1-SCNGDDXT-10008", "附件下载"),

	/**
	 * 知识库日志
	 */
	/**
	 * 知识库案例停用
	 */
	DISABLEKNOWLEDGE("updateKnowledge", "知识库", "1-SCNGDDXT-10010", "知识库案例停用"),

	/**
	 * 知识库案例删除
	 */
	DELETEKNOWLEDGE("deleteById", "知识库", "1-SCNGDDXT-10011", "知识库案例删除"),

	/**
	 * 知识库案例启用
	 */
	ENABLEKNOWLEDGE("updateKnowledge", "知识库", "1-SCNGDDXT-10012", "知识库案例启用"),

	/**
	 * 知识库案例新增
	 */
	ADDKNOWLEDGE("addEntity", "知识库", "1-SCNGDDXT-10009", "知识库案例新增"),

	/**
	 * 订单操作日志
	 */
	/**
	 * 通用订单新增
	 */
	ADDORDERFORM("addOrderForm", "我的通用单", "1-SCNGDDXT-10013", "通用订单新增"),

	/**
	 * 通用订单修改
	 */
	UPDATEORDERFORM("updateOrderForm", "我的通用单", "1-SCNGDDXT-10014", "通用订单修改"),

	/**
	 * 通用订单删除
	 */
	DELETEBYOID("deleteByOid", "我的通用单", "1-SCNGDDXT-10026", "通用订单删除"),

	/**
	 * 专用订单新增
	 */
	SAVEORDER("saveOrder", "我的需求单", "1-SCNGDDXT-10015", "专用订单新增"),

	/**
	 * 订单统计查询
	 */
	ORDERQUERY("orderQuery", "订单统计", "1-SCNGDDXT-10017", "订单统计查询"),

	/**
	 * 订单明细统计查询
	 */
	GETORDERLIST("getOrderList", "订单明细统计", "1-SCNGDDXT-10018", "订单明细统计查询"),

	/**
	 * 超时订单统计查询
	 */
	OVERTIMEORDERUS("overTimeOrderUS", "超时订单统计订单", "1-SCNGDDXT-10019", "超时订单统计查询"),

	/**
	 * 流转量统计查询
	 */
	LZLQUERY("lzlquery", "流转量统计", "1-SCNGDDXT-10020", "流转量统计查询"),

	/**
	 * 专业订单环节耗时统计查询
	 */
	PROFESSIONALORDERTIME("professionalOrderTime", "专业订单环节耗时统计", "1-SCNGDDXT-10021", "专业订单环节耗时统计查询"),

	/**
	 * 订单统计Excel导出
	 */
	EXPORTEXCELTOJXL("exportExcelToJxl", "订单统计", "1-SCNGDDXT-10022", "订单统计Excel导出"),

	/**
	 * 订单明细统计Excel导出
	 */
	EXPORTVECAPP("exportVecApp", "订单明细统计", "1-SCNGDDXT-10023", "订单明细统计Excel导出"),

	/**
	 * 超时订单统计Excel导出
	 */
	EXPORTEXCELOVERTIMETOCSJXL("exportExcelOverTimeToCsJxl", "超时订单统计", "1-SCNGDDXT-10024", "超时订单统计Excel导出"),

	/**
	 * 流转量统计Excel导出
	 */
	EXPORTVECAPPS("exportVecApp", "流转量统计", "1-SCNGDDXT-10025", "流转量统计Excel导出"),

	/**
	 * 专业订单环节耗时统计Excel导出
	 */
	EXPORTEXCELSEPCIALORDER("exportExcelSepcialOrder", "专业订单环节耗时统计", "1-SCNGDDXT-10025", "专业订单环节耗时统计Excel导出"),

	/**
	 * 筛选
	 */
	COMMCXQUERY("query_CommCX", "", "1-SCNGDDXT-10027", "筛选"),

	/**
	 * 订单任务创建
	 */
	WAITTASK("waittask", "待办创建", "1-SCNGDDXT-10016", "订单任务创建"),

	/**
	 * 订单任务处理
	 */
	WAITTASKD("waittask", "待办审批", "1-SCNGDDXT-10016", "订单任务处理");

	/**
	 *
	 * @param s1
	 * @param s2
	 * @param s3
	 * @param s4
	 */

	LogsEnum(String s1, String s2, String s3, String s4) {
        this.s1 = s1;
        this.s2 = s2;
        this.s3 = s3;
        this.s4 = s4;
    }

    private String s1;

    private String s2;

    private String s3;

    private String s4;

    public String getS1() {
		return s1;
	}

	public void setS1(String s1) {
		this.s1 = s1;
	}

	public String getS2() {
		return s2;
	}

	public void setS2(String s2) {
		this.s2 = s2;
	}

	public String getS3() {
		return s3;
	}

	public void setS3(String s3) {
		this.s3 = s3;
	}

	public String getS4() {
		return s4;
	}

	public void setS4(String s4) {
		this.s4 = s4;
	}

	LogsEnum() {
    }
}
