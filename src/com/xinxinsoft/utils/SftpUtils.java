package com.xinxinsoft.utils;

import com.jcraft.jsch.Channel;
import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.JSch;
import com.jcraft.jsch.Session;
import com.xinxinsoft.utils.common.FileUpload;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * SftpUtils
 * gcy
 * 2020年9月23日 10:51:00
 * @version v1.0
 */
public class SftpUtils {
    private static final Logger log = LoggerFactory.getLogger(SftpUtils.class);

    /**
     * 1. 第一步:连接sftp服务器,先获取Session
     * @param host
     *            主机
     * @param port
     *            端口
     * @param username
     *            用户名
     * @param password
     *            密码
     * @return
     */
    public static Session getSession(String host, int port, String username, String password) {
        Session session = null;
        try {
            JSch jsch = new JSch();
            session =jsch.getSession(username, host, port);
            log.info("Session创建");
            session.setPassword(password);
            Properties sshConfig = new Properties();
            sshConfig.put("StrictHostKeyChecking", "no");
            session.setConfig(sshConfig);
            session.connect();
            log.info("Session连接");
        } catch (Exception e) {
            e.printStackTrace();
            if (session!= null && session.isConnected()){
                session.disconnect();
            }
        }
        return session;
    }


    /**
     * 2.第二步: 连接sftp服务器,再获取链接
     * @return
     */
    public static ChannelSftp getConnect(Session session) {
        ChannelSftp sftp = null;
        try {
            if(session == null){
                log.info("Can't Create Connect,Because session is null");
                return sftp;
            }
            Channel channel = session.openChannel("sftp");
            log.info("开启渠道.");
            channel.connect();
            sftp = (ChannelSftp) channel;
            log.info("连接SFTP服务器： " + session.getHost()+":"+session.getPort());
        } catch (Exception e) {
            e.printStackTrace();
            if (sftp!= null && sftp.isConnected()){
                sftp.disconnect();
            }
        }
        return sftp;
    }

    /**
     * 3.第三步:关闭 channel和session
     * @param channel
     */
    public static void disconnect(Channel channel , Session session) {
        try {
            if (channel!= null && channel.isConnected()){
                channel.disconnect();
                log.info("关闭渠道");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (session!= null && session.isConnected()){
                session.disconnect();
                log.info("关闭session");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }



    /**
     * 上传文件到远端服务器,如果在同一目录下,文件名相同会自动替换
     * 如果上传一半,网络原因中断,那服务器上会有一半大小的文件,请重新上传.
     * @param destDirectory
     *            远端服务器要上传的目录  : /data/temp/test/
     * @param srcDirectory
     *            本地要上传的目录 : D:/test/
     * @param srcFileName
     *            本地要上传的文件 : upload.txt
     * @param sftp
     */
    public static void upload(String destDirectory, String srcDirectory, String srcFileName, ChannelSftp sftp) throws Exception{
        try {
            sftp.cd(destDirectory);
            File file = new File(srcDirectory+srcFileName);
            if(!file.exists()){
                throw new Exception(srcDirectory+srcFileName+" is not exists");
            }
            log.info("上传本地文件"+srcDirectory+srcFileName+"到远端服务器"+destDirectory+" 开始");
            sftp.put(new FileInputStream(file), file.getName());
            log.info("上传本地文件"+srcDirectory+srcFileName+"到远端服务器"+destDirectory+" 结束");
            //sftp.put("D:/application/eclipse64ee/workspace/SFTP/src/com/testdemo/www/ftp/SFTPTooL.java","/data/temp/test");//将本地目录的文件直接上传到服务器上
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 上传流到远端服务器,如果在同一目录下,文件名相同会自动替换
     * 如果上传一半,网络原因中断,那服务器上会有一半大小的文件,请重新上传.
     * @param destDirectory
     *            远端服务器要上传的目录  : /data/temp/test/
     * @param srcStream
     *            本地要上传的流 : D:/test/
     * @param srcFileName
     *            本地指定到远端服务器要生成的文件名 : upload.txt
     * @param sftp
     */
    public static void upload(String destDirectory, InputStream srcStream, String srcFileName, ChannelSftp sftp) throws Exception{
        try {
            sftp.cd(destDirectory);
            if(srcStream == null){
                throw new Exception("流为空,"+srcFileName+" is not exists");
            }
            log.info("上传流"+srcFileName+"到远端服务器"+destDirectory+" 开始");
            sftp.put(srcStream, srcFileName);
            log.info("上传流"+srcFileName+"到远端服务器"+destDirectory+" 结束");
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上下载文件
     *
     * @param remoteDirectory
     *            在远端服务器上要下载的目录 :/data/temp/test/
     * @param remoteFile
     *            在远端服务器上要下载的文件名 :　download.txt
     * @param localDirectory
     *            本地所在文件夹 : D:/test/
     * @param localFile
     *            本地将要生成的的文件名 : download.txt
     * @param sftp 链接
     */
    public static void download(String remoteDirectory, String remoteFile,String localDirectory, String localFile, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(remoteDirectory);
            File file = new File(localDirectory);
            if(!file.exists())
                file.mkdirs();
            File saveFile = new File(localDirectory,localFile);
            log.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+localFile+" 开始");
            sftp.get(remoteFile, new FileOutputStream(saveFile));
            log.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+localFile+" 结束");
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上批量下载文件到本地文件夹
     *
     * @param remoteDirectory
     *            在远端服务器上要下载的目录 :/data/temp/test/
     * @param remoteFile
     *            在远端服务器上要下载的文件名 :　*.txt
     * @param localDirectory
     *            本地所在文件夹 : D:/test/
     * @param sftp 链接
     */
    public static void download(String remoteDirectory, String remoteFile,String localDirectory, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(remoteDirectory);
            File file = new File(localDirectory);
            if(!file.exists())
                file.mkdirs();
            log.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+" 开始");
            sftp.get(remoteFile, localDirectory);
            log.info("从远端服务器下载文件"+remoteDirectory+remoteFile+"到本地"+localDirectory+" 结束");
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上删除文件(仅能删除文件,不能删目录)
     *
     * @param directory
     *            在远端服务器上,要删除文件所在目录 : /data/temp/test/
     * @param deleteFile
     *            在远端服务器上,要删除的文件
     * @param sftp 链接
     */
    public static void delete(String directory, String deleteFile, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(directory);
            sftp.rm(deleteFile);
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
    }

    /**
     * 在远端服务器上的指定文件夹下创建新的目录(多层次)
     * @param directory
     *            远端服务器上,要创建文件所在目录 : /data/temp/test/
     * @param folderPath
     *            远端服务器上,要创建的文件夹名 : ( 可以为多层次,形如  good 或  test2/good/ok )
     * @param sftp 链接
     */
    public static void mkdir(String directory, String folderPath, ChannelSftp sftp)  throws Exception{
        try {
            sftp.cd(directory);//切换目录,如果目录不存在就会报错
        } catch (Exception e) {
            e.printStackTrace();
            throw e;
        }
        String[] folders = folderPath.split("/");
        for(String currentFolder :folders){
            try{
                sftp.ls(currentFolder);//展示目录,如果文件夹不存在就会报错
                sftp.cd(currentFolder);
            }catch(Exception e){
                sftp.mkdir(currentFolder);//即然不存在,就创建该文件夹
                sftp.cd(currentFolder);
                log.info(currentFolder+" is no exists, make the dir success");
            }
        }
    }

    /**
     * 日期转换
     * @param currentTime
     * @return
     */
    public static String dateOfThePreviousDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,-1);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }
    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatethree() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(new Date());
        return dateString;
    }

    /**
     * 创建文件
     *
     * @throws IOException
     */
    public static boolean creatTxtFile(String name,String path) throws IOException {
        boolean flag = false;
        File filename = new File(path + name);
        if (!filename.exists()) {
            filename.createNewFile();
            flag = true;
        }
        return flag;
    }
    /**
     * 写文件
     *
     * @param newStr
     *       新内容
     * @param name
     *      文件名称
     * @param name
     *      文件地址
     * @throws IOException
     */
    public static boolean writeTxtFile(String newStr,String name,String path) throws IOException {
        //先读取原有文件内容，然后进行写入操作
        boolean flag = false;
        String filein = newStr + "\r\n";
        String temp = "";
        FileInputStream fis = null;
        InputStreamReader isr = null;
        BufferedReader br = null;
        FileOutputStream fos = null;
        PrintWriter pw = null;
        try {
            // 文件路径
            File file = new File(path + name);
            // 将文件读入输入流
            fis = new FileInputStream(file);
            isr = new InputStreamReader(fis);
            br = new BufferedReader(isr);
            StringBuffer buf = new StringBuffer();
            // 保存该文件原有的内容
            for (int j = 1; (temp = br.readLine()) != null; j++) {
                buf = buf.append(temp);
                // System.getProperty("line.separator")
                // 行与行之间的分隔符 相当于“\n”
                buf = buf.append(System.getProperty("line.separator"));
            }
            buf.append(filein);
            fos = new FileOutputStream(file);
            pw = new PrintWriter(fos);
            pw.write(buf.toString().toCharArray());
            pw.flush();
            flag = true;
        } catch (IOException e1) {
            e1.printStackTrace();
        } finally {
            if (pw != null) {
                pw.close();
            }
            if (fos != null) {
                fos.close();
            }
            if (br != null) {
                br.close();
            }
            if (isr != null) {
                isr.close();
            }
            if (fis != null) {
                fis.close();
            }
        }
        return flag;
    }

    /**
     * 获取文件大小(字节byte)
     * 方式一：file.length()
     */
    public static String  getFileLength(File file){
        long fileLength = 0L;
        if(file.exists() && file.isFile()){
            fileLength = file.length();
        }
        log.info("文件"+file.getName()+"的大小为:"+fileLength+"byte");
        return fileLength+"";
    }

    public String getDate(){
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd");
        Date date = new Date();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, -1);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

    /**
     * 日期转换
     * @param currentTime
     * @return
     */
    public static String dateOfThePreviousDayTwo() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE,0);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

}

