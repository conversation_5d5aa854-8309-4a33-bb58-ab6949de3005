package com.xinxinsoft.utils; 
import java.io.*;
import java.util.Date;
import java.awt.*;
import java.awt.image.*;
import javax.imageio.ImageIO;
import com.sun.image.codec.jpeg.*;
/**
 * 图片压缩处理 
 */
public class ImgCompress { 
	
	@SuppressWarnings("deprecation")
	public static void main(String[] args) throws Exception {
		System.out.println("开始：" + new Date().toLocaleString()); 
		ImgCompress.saveMinPhoto("F:/FTP/1.jpg", "F:/FTP/zs4.jpg");
		System.out.println("结束：" + new Date().toLocaleString());
	}
  
	/**
	 *  压缩   
	 */
	public static void saveMinPhoto(String srcURL, String deskURL) throws Exception {
		File srcFile = new java.io.File(srcURL);
		Image src = ImageIO.read(srcFile);
		int srcHeight = src.getHeight(null);
		int srcWidth = src.getWidth(null);  
		BufferedImage tag = new BufferedImage(srcWidth, srcHeight, BufferedImage.TYPE_3BYTE_BGR);
		tag.getGraphics().drawImage(src, 0, 0, srcWidth, srcHeight, null); 
		FileOutputStream deskImage = new FileOutputStream(deskURL); //输出到文件流
		JPEGImageEncoder encoder = JPEGCodec.createJPEGEncoder(deskImage);
		encoder.encode(tag); //近JPEG编码
		deskImage.close();
	}

}