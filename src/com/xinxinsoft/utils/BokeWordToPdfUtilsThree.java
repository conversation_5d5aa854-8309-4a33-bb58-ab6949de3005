package com.xinxinsoft.utils;

import com.aspose.words.SaveFormat;
import com.lowagie.text.Element;
import com.lowagie.text.Font;
import com.lowagie.text.pdf.*;
import com.spire.doc.Document;
import com.spire.doc.*;
import com.spire.doc.documents.Paragraph;
import com.spire.doc.documents.UnderlineStyle;
import com.spire.doc.documents.VerticalOrigin;
import com.spire.doc.fields.DocPicture;
import com.spire.doc.fields.TextRange;
import org.apache.poi.POIXMLDocument;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlCursor;
import org.apache.xmlbeans.XmlObject;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.swing.*;
import java.awt.*;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

public class BokeWordToPdfUtilsThree {
    private static Logger logger = LoggerFactory.getLogger(XMlToDoc.class);

    /**
     * @param inputUrl 模板地址
     * @param outUrl   生成地址
     * @param list     文本内容
     * @param fileName 二维码文件名
     * @return
     */
    public static boolean changWord(String inputUrl, String outUrl, List<Map<String, String>> list, Map<String,
            String> textMap, String fileName, String ftpUrl, List<String[]> tableList) {
        FileOutputStream stream = null;
        com.aspose.words.Document doc = null;
        boolean changeFlag = true;
        try {

            XWPFDocument document = new XWPFDocument(POIXMLDocument.openPackage(inputUrl));

            BokeWordToPdfUtilsThree.changeText(document, list.get(0));//文本替换

            BokeWordToPdfUtilsThree.changeTable(document, textMap, tableList);//表格文本替换

            File file = new File(outUrl);
            stream = new FileOutputStream(file);
            document.write(stream);

            String pdfname = outUrl.substring(0, outUrl.indexOf(".")) + ".pdf";//生成的pdf路径(无水印)

            //添加二维码
            DocOrpdfUtil.createCodeToFile(list.get(0).get("batchNo"), new File(ftpUrl), fileName);//生成二维码
            Document docu = new Document(outUrl);
            Section sec = docu.getSections().get(0);
            AddHeaderFooter(sec, ftpUrl + fileName); //调用方法添加页眉页脚
            docu.saveToFile(outUrl);//保存文档
            docu.close();


            //docx转pdf
            file = new File(pdfname);
            stream = new FileOutputStream(file);
            doc = new com.aspose.words.Document(outUrl);
            doc.save(stream, SaveFormat.PDF);
            stream.close();


            file = new File(outUrl);
            file.delete();//删除生成的word
            file = new File(ftpUrl + fileName);
            file.delete();//删除二维码图片
        } catch (Exception e) {
            e.printStackTrace();
            changeFlag = false;
        } finally {
            try {
                if (stream != null) {
                    stream.close();
                }

            } catch (Exception e) {
                logger.info("文档转pdf 关闭 异常", e);
            }
        }
        return changeFlag;
    }

    public static void changeText(XWPFDocument document, Map<String, String> textMap) {
        //修改内容
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            String text = paragraph.getText();
            if (chekText(text)) {
                List<XWPFRun> runs = paragraph.getRuns();
                for (XWPFRun run : runs) {
                    String textValue = changeValue(run.toString(), textMap);
                    run.setText(textValue, 0);
                }
            }
        }
    }

    public static void changeTable(XWPFDocument document, Map<String, String> textMap, List<String[]> tableList) {
        List<XWPFTable> tables = document.getTables();
        for (int i = 0; i < tables.size(); i++) {
            XWPFTable table = tables.get(i);
            if (table.getRows().size() >= 1) {
                if (chekText(table.getText())) {
                    List<XWPFTableRow> tableRows = table.getRows();
                    eachTable(tableRows, textMap);
                    insertTable(table, tableList);
                }
            }
        }
    }

    public static void eachTable(List<XWPFTableRow> tableRows, Map<String, String> textMap) {
        for (XWPFTableRow row : tableRows) {
            List<XWPFTableCell> cells = row.getTableCells();
            for (XWPFTableCell cell : cells) {
                if (chekText(cell.getText())) {
                    List<XWPFParagraph> paragraphs = cell.getParagraphs();
                    for (XWPFParagraph paragraph : paragraphs) {
                        List<XWPFRun> runs = paragraph.getRuns();
                        for (XWPFRun run : runs) {
                            run.setText(changeValue(run.toString(), textMap), 0);
                        }
                    }
                }
            }
        }
    }

    /**
     * @param table
     * @param col
     * @param fromRow
     * @param toRow
     */
    public static void mergeCellsVertically(XWPFTable table, int col, int fromRow, int toRow) {
        for (int rowIndex = fromRow; rowIndex <= toRow; rowIndex++) {
            XWPFTableCell cell = table.getRow(rowIndex).getCell(col);
            if (rowIndex == fromRow) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewVMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    public static void insertTable(XWPFTable table, List<String[]> tableList) {
        for (int i = 0; i < tableList.size(); i++) {
            // 在指定位置插入新行
            XWPFTableRow row = table.insertNewTableRow(14 + i);
            List<XWPFTableCell> cells = row.getTableCells();
            for (int j = 0; j < tableList.get(i).length; j++) {
                // 检查单元格是否存在，如果不存在，创建新单元格
                XWPFTableCell cell = null;
                if (j < cells.size()) {
                    cell = cells.get(j);
                } else {
                    cell = row.addNewTableCell();
                    // 获取上一行相应单元格的样式
                    XWPFTableCell prevCell = table.getRow(14 + i - 1).getCell(j);
                    if (prevCell != null) {
                        // 将上一行单元格的样式应用到新单元格
                        cell.getCTTc().setTcPr(prevCell.getCTTc().getTcPr());
                    }
                }
                // 设置单元格的文本
                cell.setText(tableList.get(i)[j]);
            }
        }
    }

//    //添加表格填充数据
//    public static void insertTable(XWPFTable table, List<String[]> tableList) {
//        for (int i = 0; i < tableList.size(); i++) {
//            XWPFTableRow row = table.createRow();
//        }
//
//        mergeCellsVertically(table, 0, 0, tableList.size());
//        List<XWPFTableRow> rows = table.getRows();
//        for (int i = 1; i < rows.size(); i++) {
//            XWPFTableRow newRow = rows.get(i);
//            List<XWPFTableCell> cells = newRow.getTableCells();
//            for (int j = 1; j < cells.size(); j++) {
//                XWPFTableCell cell = cells.get(j);
//                cell.setText(tableList.get(i - 1)[j - 1]);
//            }
//        }
//    }

    public static boolean chekText(String text) {
        Boolean textFalg = false;
        if (text.indexOf("$") != -1) {
            textFalg = true;
        }
        return textFalg;
    }

    public static String changeValue(String value, Map<String, String> textMap) {
        Set<Entry<String, String>> textSets = textMap.entrySet();
        for (Entry<String, String> textSet : textSets) {
            String Key = "${" + textSet.getKey() + "}";
            if (value.indexOf(Key) != -1) {
                value = value.replace(Key, textSet.getValue());
            }
        }
        if (chekText(value)) {
            value = "";
        }
        return value;
    }


    //自定义方法来添加图片、文字页眉及页码
    public static void AddHeaderFooter(Section sec, String filePath2) {
        //加载图片添加到页眉，并设置图片在段落中的对齐方式
        HeaderFooter header = sec.getHeadersFooters().getHeader();
        Paragraph hpara = header.addParagraph();

        TextRange txt1 = hpara.appendText("                                                                    ");
        txt1.getCharacterFormat().setUnderlineStyle(UnderlineStyle.None);
        txt1.getCharacterFormat().setTextColor(Color.GRAY);
        txt1.getCharacterFormat().setFontName("仿宋");
        txt1.getCharacterFormat().setFontSize(14f);
        txt1.getCharacterFormat().setBold(true);

        DocPicture pic = hpara.appendPicture(filePath2);
        pic.setHorizontalAlignment(ShapeHorizontalAlignment.Right);
        pic.setVerticalOrigin(VerticalOrigin.Page);
        pic.setVerticalAlignment(ShapeVerticalAlignment.Top);
        pic.setWidth(40);
        pic.setHeight(40);
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getDateToStrTimestamp(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

}
