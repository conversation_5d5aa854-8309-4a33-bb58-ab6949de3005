package com.xinxinsoft.utils;

import java.util.Random;

public class SSOKeyUtils {
	// 定义随机机密因子整型数组
	private static int[] m_s1 = { 18, 46, 52, 22, 39, 0, 58, 54, 23, 37, 38,
			25, 42, 36, 62, 30, 41, 14, 7, 50, 8, 9, 51, 59, 21, 15, 34, 45,
			56, 3, 55, 28, 49, 32, 35, 20, 24, 53, 33, 40, 11, 17, 26, 31, 48,
			5, 43, 29, 44, 12, 1, 19, 4, 13, 16, 27, 57, 47, 2, 6, 63, 10, 61,
			60 };
	// ASCII码中 0-9 A-F用作加密后映射显示
	private static byte[] m = { 48, 49, 50, 51, 52, 53, 54, 55, 56, 57, 65, 66,
			67, 68, 69, 70 };

	public static String encode(String key, String objValue) {
		// 随机初始化64位的字节数组并根据传入的加密数据进行逐个字节进行赋值
		byte[] abyte0 = new byte[64];
		new Random(System.currentTimeMillis()).nextBytes(abyte0);

		byte[] abyte1 = objValue.getBytes();
		for (int i = 0; (i < abyte1.length) && (i < 64); i++) {
			abyte0[i] = abyte1[i];
		}

		// 如果加密字节小于64位将最后一位设置成0
		if (abyte1.length < 64)
			abyte0[abyte1.length] = 0;
		// 将加密密钥和要加密的值求和保存
		byte[] abyte2 = key.getBytes();
		for (int j = 0; j < abyte0.length; j++) {
			byte[] temp = abyte0;
			temp[j] = ((byte) (temp[j] + abyte2[(j % abyte2.length)]));
		}
		// 根据加密因子和求和后的值交换，并根据映射生成字符串
		return byteToString(switchArray(abyte0, m_s1));
	}

	private static byte[] switchArray(byte[] abyte0, int[] ai) {
		byte[] abyte1 = new byte[abyte0.length];
		for (int i = 0; i < abyte1.length; i++) {
			abyte1[i] = abyte0[ai[i]];
		}
		return abyte1;
	}

	private static String byteToString(byte[] abyte0) {
		if ((abyte0 == null) || (abyte0.length == 0))
			return "";
		byte[] abyte1 = new byte[2 * abyte0.length];
		for (int i = 0; i < abyte0.length; i++) {
			abyte1[(2 * i + 0)] = m[(abyte0[i] & 0xF)];
			abyte1[(2 * i + 1)] = m[((abyte0[i] & 0xF0) >> 4)];
		}

		return new String(abyte1);
	}
	



}
