package com.xinxinsoft.utils;

import javax.servlet.ServletConfig;
import javax.servlet.http.HttpServletRequest;

import com.ckfinder.connector.configuration.Configuration;
import com.ckfinder.connector.configuration.ConfigurationFactory;
import com.xinxinsoft.entity.core.SystemUser;




public class CKfinderConfig extends Configuration {

	/*static {
		try {
			Type t = com.ckfinder.connector.handlers.command.FileUploadCommand.class;
			ClassPool classPool = ClassPool.getDefault();
			CtClass ctClass = classPool.get("com.ckfinder.connector.handlers.command.FileUploadCommand");
			CtMethod ctMethod = ctClass.getDeclaredMethod("getFileItemName");
			System.out.println(ctMethod.getName());
//	    	ctMethod.setModifiers(javassist.Modifier.PUBLIC);
			ctMethod.setBody("{return  String.valueOf(new java.util.Date().getTime()) + \".\" + \"jpg\";}");
			ctClass.toClass();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}*/
	
	public CKfinderConfig(ServletConfig servletConfig) {
		super(servletConfig);
	}
	
	private String userURL;
	
	public String getUserURL() {
		return userURL;
	}

	public void setUserURL(String userURL) {
		this.userURL = userURL;
	}

	@Override
	public void prepareConfigurationForRequest(HttpServletRequest request) {
		CKfinderConfig baseConfig;
		try {
			SystemUser user =  (SystemUser) request.getSession().getAttribute("currentLoginUser");
			baseConfig = (CKfinderConfig)ConfigurationFactory.getInstace().getConfiguration();
			baseConfig.setUserURL(baseConfig.baseURL + user.getRowNo() + "/");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}



	@Override
	protected Configuration createConfigurationInstance() {
		return new CKfinderConfig(this.servletConf);
	}

	@Override
	public String getBaseURL() {
		return getUserURL();
	}
	
}
