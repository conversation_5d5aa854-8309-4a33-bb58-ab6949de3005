package com.xinxinsoft.utils;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;

import com.xinxinsoft.action.appOpenAction.Broadband1000OpeningQueryAction;
import com.xinxinsoft.entity.core.SystemUser;
import org.apache.axis.encoding.Base64;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;
import org.joda.time.LocalDateTime;
import org.joda.time.format.DateTimeFormatter;
import org.json.JSONObject;
import sun.misc.BASE64Encoder;


public class UrlConnection {
    private static final Logger logger = Logger.getLogger(UrlConnection.class);
	
	/**
	 * 
	 * @param url 请求地址
	 * @param content 参数内容
	 * @return
	 */
	@SuppressWarnings("finally")
	public static String responseUTF8(String url,String content) {
        String line         = "";
        String message        = "";
        BufferedReader bufferedReader = null;
        JSONObject outObj=new JSONObject();
        
        try {
            URL urlObject = new URL(url);
            HttpURLConnection urlConn = (HttpURLConnection) urlObject.openConnection();
            urlConn.setDoOutput(true);
            /*设定禁用缓存*/
            urlConn.setRequestProperty("Pragma:", "no-cache");      
            urlConn.setRequestProperty("Cache-Control", "no-cache");
            /*维持长连接*/
            urlConn.setRequestProperty("Connection", "Keep-Alive");  
            /*设置字符集*/
            urlConn.setRequestProperty("Charset", "UFT-8");
            /*设定输出格式为json*/
            urlConn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            /*设置使用POST的方式发送*/
            urlConn.setRequestMethod("POST");             
            /*设置不使用缓存*/
            urlConn.setUseCaches(false);
            /*设置容许输出*/
            urlConn.setDoOutput(true);  
            /*设置容许输入*/
            urlConn.setDoInput(true);              
            urlConn.connect();
            
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(urlConn.getOutputStream(),"UTF-8"); 
            outStreamWriter.write(content);
            outStreamWriter.flush();
            outStreamWriter.close();
            
            /*若post失败*/
            if((urlConn.getResponseCode() != HttpURLConnection.HTTP_OK)){
               // returnData = "{\"jsonStrStatus\":0,\"processResults\":[]}";
                message = "发送POST失败！"+ "code="+urlConn.getResponseCode() + "," + "失败消息："+ urlConn.getResponseMessage();
                // 定义BufferedReader输入流来读取URL的响应
                InputStream errorStream = urlConn.getErrorStream(); 
                
                if(errorStream != null)
                {
                    InputStreamReader inputStreamReader = new InputStreamReader(errorStream,"utf-8");
                    bufferedReader = new BufferedReader(inputStreamReader);  
              
                    while ((line = bufferedReader.readLine()) != null) {  
                        message += line;    
                    }         
                    inputStreamReader.close();
                }
                errorStream.close();
                outObj.put("Status", 0);
                logger.info("res："+message);
                outObj.put("res", message);
            }else{
                /*发送成功返回发送成功状态*/
                //postState = true;
                
                // 定义BufferedReader输入流来读取URL的响应
                InputStream inputStream = urlConn.getInputStream();  
                
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream,"utf-8");
                bufferedReader = new BufferedReader(inputStreamReader);  
          
                while ((line = bufferedReader.readLine()) != null) {  
                    message += line;  
                }        
                //returnData = message;
                inputStream.close();
                inputStreamReader.close();
                
                outObj.put("Status", 1);
                logger.info("res："+message);
                outObj.put("res", message);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("本地错误信息"+e.getMessage(),e);
            outObj.put("Status", 0);
            logger.info("res："+e.getMessage());
            outObj.put("res", e.getMessage());
        }finally{
            try {   
                if (bufferedReader != null) {  
                    bufferedReader.close();  
                }                  
            } catch (IOException ex) {  
                ex.printStackTrace();
                logger.error("本地IO错误信息"+ex.getMessage(),ex);
            }
            logger.info("接口请求地址："+url);
            logger.info("接口请求入参："+content);
            logger.info("接口请求返回数据："+outObj);
            return outObj.toString();
        }
    }
	/**
	 * 
	 * @param url 请求地址
	 * @param content 参数内容
	 * @return
	 */
	@SuppressWarnings("finally")
	public static String responseGBK(String url,String content) {
        String line         = "";
        String message        = "";
        //String returnData   = "";
        //boolean postState     = false;
        BufferedReader bufferedReader = null;
        JSONObject outObj=new JSONObject();
        
        try {
            URL urlObject = new URL(url);
            HttpURLConnection urlConn = (HttpURLConnection) urlObject.openConnection();
            urlConn.setDoOutput(true);
            /*设定禁用缓存*/
            urlConn.setRequestProperty("Pragma:", "no-cache");
            urlConn.setRequestProperty("Cache-Control", "no-cache");
            /*维持长连接*/
            urlConn.setRequestProperty("Connection", "Keep-Alive");  
            /*设置字符集*/
            urlConn.setRequestProperty("Charset", "GBK");
            /*设定输出格式为json*/
            urlConn.setRequestProperty("Content-Type", "application/json;charset=gbk");
            /*设置使用POST的方式发送*/
            urlConn.setRequestMethod("POST");             
            /*设置不使用缓存*/
            urlConn.setUseCaches(false);
            /*设置容许输出*/
            urlConn.setDoOutput(true);  
            /*设置容许输入*/
            urlConn.setDoInput(true);              
            urlConn.connect();
            
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(urlConn.getOutputStream(),"GBK"); 
            outStreamWriter.write(content);
            outStreamWriter.flush();
            outStreamWriter.close();
            
            /*若post失败*/
            if((urlConn.getResponseCode() != HttpURLConnection.HTTP_OK)){
               
                message = "发送POST失败！"+ "code="+urlConn.getResponseCode() + "," + "失败消息："+ urlConn.getResponseMessage();
                // 定义BufferedReader输入流来读取URL的响应
                InputStream errorStream = urlConn.getErrorStream(); 
                
                if(errorStream != null)
                {
                    InputStreamReader inputStreamReader = new InputStreamReader(errorStream,"gbk");
                    bufferedReader = new BufferedReader(inputStreamReader);  
              
                    while ((line = bufferedReader.readLine()) != null) {  
                        message += line;    
                    }         
                    inputStreamReader.close();
                }
                errorStream.close();
               // returnData = "{\"jsonStrStatus\":0,\"processResults\":["+message+"]}";
                outObj.put("Status", 0);
                logger.info("res："+message);
                outObj.put("res", message);
            }else{
                /*发送成功返回发送成功状态*/
                //postState = true;
                
                // 定义BufferedReader输入流来读取URL的响应
                InputStream inputStream = urlConn.getInputStream();  
                
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream,"gbk");
                bufferedReader = new BufferedReader(inputStreamReader);  
          
                while ((line = bufferedReader.readLine()) != null) {  
                    message += line;  
                }        
                //returnData = message;
                inputStream.close();
                inputStreamReader.close();
                outObj.put("Status", 1);
                logger.info("res："+message);
                outObj.put("res", message);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("本地GBK错误信息"+e.getMessage(),e);
            outObj.put("Status", 0);
            logger.info("res："+e.getMessage());
            outObj.put("res", e.getMessage());
        }finally{
            try {   
                if (bufferedReader != null) {  
                    bufferedReader.close();  
                }                  
            } catch (IOException ex) {  
                ex.printStackTrace();
                logger.error("本地GBKIO错误信息"+ex.getMessage(),ex);
            }
            logger.info("接口请求地址："+url);
            logger.info("接口请求入参："+content);
            logger.info("接口请求返回数据："+outObj);
            return outObj.toString();
        }
    }
	/**
	 * 
	 * @param url 请求地址
	 * @param content 参数内容
	 * @return
	 */
	@SuppressWarnings("finally")
	public static String responseCharset(String url,String content,String charset) {
        String line         = "";
        String message        = "";
        //String returnData   = "";
        //boolean postState     = false;
        BufferedReader bufferedReader = null;
        JSONObject outObj=new JSONObject();
        
        try {
            URL urlObject = new URL(url);
            HttpURLConnection urlConn = (HttpURLConnection) urlObject.openConnection();
            urlConn.setDoOutput(true);
            /*设定禁用缓存*/
            urlConn.setRequestProperty("Pragma:", "no-cache");      
            urlConn.setRequestProperty("Cache-Control", "no-cache");
            /*维持长连接*/
            urlConn.setRequestProperty("Connection", "Keep-Alive");  
            /*设置字符集*/
            urlConn.setRequestProperty("Charset",charset);
            /*设定输出格式为json*/
            urlConn.setRequestProperty("Content-Type", "application/json;charset="+charset);
            /*设置使用POST的方式发送*/
            urlConn.setRequestMethod("POST");             
            /*设置不使用缓存*/
            urlConn.setUseCaches(false);
            /*设置容许输出*/
            urlConn.setDoOutput(true);  
            /*设置容许输入*/
            urlConn.setDoInput(true);              
            urlConn.connect();
            
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(urlConn.getOutputStream(),charset); 
            outStreamWriter.write(content);
            outStreamWriter.flush();
            outStreamWriter.close();
            
            /*若post失败*/
            if((urlConn.getResponseCode() != HttpURLConnection.HTTP_OK)){
               
                message = "发送POST失败！"+ "code="+urlConn.getResponseCode() + "," + "失败消息："+ urlConn.getResponseMessage();
                // 定义BufferedReader输入流来读取URL的响应
                InputStream errorStream = urlConn.getErrorStream(); 
                
                if(errorStream != null)
                {
                    InputStreamReader inputStreamReader = new InputStreamReader(errorStream,charset);
                    bufferedReader = new BufferedReader(inputStreamReader);  
              
                    while ((line = bufferedReader.readLine()) != null) {  
                        message += line;    
                    }         
                    inputStreamReader.close();
                }
                errorStream.close();
               // returnData = "{\"jsonStrStatus\":0,\"processResults\":["+message+"]}";
                outObj.put("Status", 0);
                outObj.put("res", message);
            }else{
                /*发送成功返回发送成功状态*/
                //postState = true;
                
                // 定义BufferedReader输入流来读取URL的响应
                InputStream inputStream = urlConn.getInputStream();  
                
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream,charset);
                bufferedReader = new BufferedReader(inputStreamReader);  
          
                while ((line = bufferedReader.readLine()) != null) {  
                    message += line;  
                }        
                //returnData = message;
                inputStream.close();
                inputStreamReader.close();
                outObj.put("Status", 1);
                outObj.put("res", message);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            outObj.put("Status", 0);
            outObj.put("res", e.getMessage());
        }finally{
            try {   
                if (bufferedReader != null) {  
                    bufferedReader.close();  
                }                  
            } catch (IOException ex) {  
                ex.printStackTrace();
                logger.error(ex.getMessage(),ex);
            }
            logger.info("接口请求地址："+url);
            logger.info("接口请求入参："+content);
            logger.info("接口请求返回数据："+outObj);
            return outObj.toString();
        }
    }
	
    /*读取request数据*/
    public static String getRequestData(HttpServletRequest request) throws IOException{
        BufferedReader reader = request.getReader();
        char[] buf = new char[1024*2];
        int len = 0;
        StringBuffer contentBuffer = new StringBuffer();
        while ((len = reader.read(buf)) != -1) {
            contentBuffer.append(buf, 0, len);
        }
        
        String content = contentBuffer.toString();
        
        if(content == null){
            content = "";
        }
        
        return content;
    }

    
    /** 
     * 发送GET请求 
     *  
     * @param url 
     *            目的地址 
     * @param parameters 
     *            请求参数，Map类型。 
     * @return 远程响应结果 
     */  
    public static String sendGet(String url, Map<String, String> parameters) { 
        String result="";
        BufferedReader in = null;// 读取响应输入流  
        StringBuffer sb = new StringBuffer();// 存储参数  
        String params = "";// 编码之后的参数
        try {
            // 编码请求参数  
            if(parameters.size()==1){
                for(String name:parameters.keySet()){
                    sb.append(name).append("=").append(
                            java.net.URLEncoder.encode(parameters.get(name),  
                            "UTF-8"));
                }
                params=sb.toString();
            }else{
                for (String name : parameters.keySet()) {  
                    sb.append(name).append("=").append(  
                            java.net.URLEncoder.encode(parameters.get(name),  
                                    "UTF-8")).append("&");  
                }  
                String temp_params = sb.toString();  
                params = temp_params.substring(0, temp_params.length() - 1);  
            }
            String full_url = url + "?" + params; 
            System.out.println(full_url); 
            // 创建URL对象  
            java.net.URL connURL = new java.net.URL(full_url);  
            // 打开URL连接  
            java.net.HttpURLConnection httpConn = (java.net.HttpURLConnection) connURL  
                    .openConnection();  
            // 设置通用属性  
            httpConn.setRequestProperty("Accept", "*/*");  
            httpConn.setRequestProperty("Connection", "Keep-Alive");  
            httpConn.setRequestProperty("User-Agent",  
                    "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)");  
            // 建立实际的连接  
            httpConn.connect();  
            // 响应头部获取  
            Map<String, List<String>> headers = httpConn.getHeaderFields();  
            // 遍历所有的响应头字段  
            for (String key : headers.keySet()) {  
                System.out.println(key + "\t：\t" + headers.get(key));  
            }  
            // 定义BufferedReader输入流来读取URL的响应,并设置编码方式  
            in = new BufferedReader(new InputStreamReader(httpConn  
                    .getInputStream(), "UTF-8"));  
            String line;  
            // 读取返回的内容  
            while ((line = in.readLine()) != null) {  
                result += line;  
            }  
        } catch (Exception e) {
            e.printStackTrace();
        }finally{
            try {  
                if (in != null) {  
                    in.close();  
                }  
            } catch (IOException ex) {  
                ex.printStackTrace();  
            }  
        }
        return result ;
    }  
  
    /** 
     * 发送POST请求 
     *  
     * @param url 
     *            目的地址 
     * @param parameters 
     *            请求参数，Map类型。 
     * @return 远程响应结果 
     */  
    public static String sendPost(String url, Map<String, String> parameters) {  
        String result = "";// 返回的结果  
        BufferedReader in = null;// 读取响应输入流  
        PrintWriter out = null;  
        StringBuffer sb = new StringBuffer();// 处理请求参数  
        String params = "";// 编码之后的参数  
        try {  
            // 编码请求参数  
            if (parameters.size() == 1) {  
                for (String name : parameters.keySet()) {  
                    sb.append(name).append("=").append(  
                            java.net.URLEncoder.encode(parameters.get(name),  
                                    "UTF-8"));  
                }  
                params = sb.toString();  
            } else {  
                for (String name : parameters.keySet()) {  
                    sb.append(name).append("=").append(  
                            java.net.URLEncoder.encode(parameters.get(name),  
                                    "UTF-8")).append("&");  
                }  
                String temp_params = sb.toString();  
                params = temp_params.substring(0, temp_params.length() - 1);  
            }  
            // 创建URL对象  
            java.net.URL connURL = new java.net.URL(url);  
            // 打开URL连接  
            java.net.HttpURLConnection httpConn = (java.net.HttpURLConnection) connURL  
                    .openConnection();  
            // 设置通用属性  
            httpConn.setRequestProperty("Accept", "*/*");  
            httpConn.setRequestProperty("Connection", "Keep-Alive");  
            httpConn.setRequestProperty("User-Agent",  
                    "Mozilla/4.0 (compatible; MSIE 8.0; Windows NT 6.1)");  
            // 设置POST方式  
            httpConn.setDoInput(true);  
            httpConn.setDoOutput(true);  
            // 获取HttpURLConnection对象对应的输出流  
            out = new PrintWriter(httpConn.getOutputStream());  
            // 发送请求参数  
            out.write(params);  
            // flush输出流的缓冲  
            out.flush();  
            // 定义BufferedReader输入流来读取URL的响应，设置编码方式  
            in = new BufferedReader(new InputStreamReader(httpConn  
                    .getInputStream(), "UTF-8"));  
            String line;  
            // 读取返回的内容  
            while ((line = in.readLine()) != null) {  
                result += line;  
            }  
        } catch (Exception e) {  
            e.printStackTrace();  
        } finally {  
            try {  
                if (out != null) {  
                    out.close();  
                }  
                if (in != null) {  
                    in.close();  
                }  
            } catch (IOException ex) {  
                ex.printStackTrace();  
            }  
        }  
        return result;  
    }

    /**
     *
     * @param url 请求地址
     * @param content 参数内容
     * @return
     */
    @SuppressWarnings("finally")
    public static String responseOSUTF8(String url,String content) {
        String line         = "";
        String message        = "";
        BufferedReader bufferedReader = null;
        JSONObject outObj=new JSONObject();
        net.sf.json.JSONObject jsonObject= net.sf.json.JSONObject.fromObject(content);
        String object = jsonObject.get("obj").toString();
        net.sf.json.JSONObject header= net.sf.json.JSONObject.fromObject(jsonObject.get("header"));
        try {
            URL urlObject = new URL(url);
            HttpURLConnection urlConn = (HttpURLConnection) urlObject.openConnection();
            urlConn.setDoOutput(true);
            /*设定禁用缓存*/
            urlConn.setRequestProperty("Pragma:", "no-cache");
            urlConn.setRequestProperty("Cache-Control", "no-cache");
            /*维持长连接*/
            urlConn.setRequestProperty("Connection", "Keep-Alive");
            /*设置字符集*/
            urlConn.setRequestProperty("Charset", "UFT-8");
            /*设定输出格式为json*/
            urlConn.setRequestProperty("Content-Type", "application/json;charset=utf-8");

            urlConn.setRequestProperty("x-sg-scenario-code","");
            urlConn.setRequestProperty("x-sg-scenario-version","");
            urlConn.setRequestProperty("x-sg-ability-code","");
            urlConn.setRequestProperty("x-sg-api-code",header.get("svcApiCode").toString());
            urlConn.setRequestProperty("x-sg-api-version",header.get("svcApiVersion").toString());
            urlConn.setRequestProperty("x-sg-app-key",header.get("appkey").toString());
            urlConn.setRequestProperty("x-sg-dest-app-key","");
            urlConn.setRequestProperty("x-sg-timestamp", header.get("timestamp").toString());
            urlConn.setRequestProperty("x-sg-scenario-id",header.get("scenarioId").toString());
            urlConn.setRequestProperty("x-sg-message-id",header.get("messageId").toString());
            urlConn.setRequestProperty("x-sg-route-type","");
            urlConn.setRequestProperty("x-sg-route-value","");
            urlConn.setRequestProperty("x-sg-test",header.get("test").toString());
            urlConn.setRequestProperty("x-sg-md5-secret",header.get("md5Secret").toString());
            urlConn.setRequestProperty("x-sg-spanid",header.get("spanid").toString());
            /*设置使用POST的方式发送*/
            urlConn.setRequestMethod("POST");
            /*设置不使用缓存*/
            urlConn.setUseCaches(false);
            /*设置容许输出*/
            urlConn.setDoOutput(true);
            /*设置容许输入*/
            urlConn.setDoInput(true);
            urlConn.connect();

            OutputStreamWriter outStreamWriter = new OutputStreamWriter(urlConn.getOutputStream(),"UTF-8");
            outStreamWriter.write(object);
            outStreamWriter.flush();
            outStreamWriter.close();

            /*若post失败*/
            if((urlConn.getResponseCode() != HttpURLConnection.HTTP_OK)){
                // returnData = "{\"jsonStrStatus\":0,\"processResults\":[]}";
                message = "发送POST失败！"+ "code="+urlConn.getResponseCode() + "," + "失败消息："+ urlConn.getResponseMessage();
                // 定义BufferedReader输入流来读取URL的响应
                InputStream errorStream = urlConn.getErrorStream();

                if(errorStream != null)
                {
                    InputStreamReader inputStreamReader = new InputStreamReader(errorStream,"utf-8");
                    bufferedReader = new BufferedReader(inputStreamReader);

                    while ((line = bufferedReader.readLine()) != null) {
                        message += line;
                    }
                    inputStreamReader.close();
                }
                errorStream.close();
                outObj.put("Status", 0);
                outObj.put("res", message);
            }else{
                /*发送成功返回发送成功状态*/
                //postState = true;

                // 定义BufferedReader输入流来读取URL的响应
                InputStream inputStream = urlConn.getInputStream();

                InputStreamReader inputStreamReader = new InputStreamReader(inputStream,"utf-8");
                bufferedReader = new BufferedReader(inputStreamReader);

                while ((line = bufferedReader.readLine()) != null) {
                    message += line;
                }
                //returnData = message;
                inputStream.close();
                inputStreamReader.close();

                outObj.put("Status", 1);
                outObj.put("res", message);
            }
        } catch (Exception e) {
            e.printStackTrace();

            outObj.put("Status", 0);
            outObj.put("res", e.getMessage());
        }finally{
            try {
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
            return outObj.toString();
        }
    }


    public static String responseGETOS85(String url,String content) {
        String line         = "";
        String message        = "";
        BufferedReader bufferedReader = null;
        JSONObject outObj=new JSONObject();
        try {
            net.sf.json.JSONObject jsonObject= net.sf.json.JSONObject.fromObject(content);
            net.sf.json.JSONObject obj= net.sf.json.JSONObject.fromObject(jsonObject.get("obj"));
            net.sf.json.JSONObject header= net.sf.json.JSONObject.fromObject(jsonObject.get("header"));
            // 正式环境地址
            URL restURL = new URL(url+"?ticket=" + obj.get("ticket").toString());
            HttpURLConnection conn = (HttpURLConnection)
                    restURL.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("Charset", "utf-8");

            conn.setRequestProperty("x-sg-scenario-code","");
            conn.setRequestProperty("x-sg-scenario-version","");
            conn.setRequestProperty("x-sg-ability-code","");
            conn.setRequestProperty("x-sg-api-code",header.get("svcApiCode").toString());
            conn.setRequestProperty("x-sg-api-version",header.get("svcApiVersion").toString());
            conn.setRequestProperty("x-sg-app-key",header.get("appkey").toString());
            conn.setRequestProperty("x-sg-dest-app-key","");
            conn.setRequestProperty("x-sg-timestamp", header.get("timestamp").toString());
            conn.setRequestProperty("x-sg-scenario-id",header.get("scenarioId").toString());
            conn.setRequestProperty("x-sg-message-id",header.get("messageId").toString());
            conn.setRequestProperty("x-sg-route-type","");
            conn.setRequestProperty("x-sg-route-value","");
            conn.setRequestProperty("x-sg-test",header.get("test").toString());
            conn.setRequestProperty("x-sg-md5-secret",header.get("md5Secret").toString());
            conn.setRequestProperty("x-sg-spanid",header.get("spanid").toString());
            /*若get失败*/
            if((conn.getResponseCode() != HttpURLConnection.HTTP_OK)){
                message = "发送GET失败！"+ "code="+conn.getResponseCode() + "," + "失败消息："+ conn.getResponseMessage();
                // 定义BufferedReader输入流来读取URL的响应
                InputStream errorStream = conn.getErrorStream();
                if(errorStream != null) {
                    InputStreamReader inputStreamReader = new InputStreamReader(errorStream,"utf-8");
                    bufferedReader = new BufferedReader(inputStreamReader);
                    while ((line = bufferedReader.readLine()) != null) {
                        message += line;
                    }
                    inputStreamReader.close();
                }
                errorStream.close();
                outObj.put("Status", 0);
                outObj.put("res", message);
            }else{
                // 定义BufferedReader输入流来读取URL的响应
                InputStream inputStream = conn.getInputStream();
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream,"utf-8");
                bufferedReader = new BufferedReader(inputStreamReader);
                while ((line = bufferedReader.readLine()) != null) {
                    message += line;
                }
                logger.info("这是获取的pdf值："+message);
                inputStream.close();
                inputStreamReader.close();
                outObj.put("Status", 1);
                outObj.put("res", message);
            }
        } catch (Exception e) {
            e.printStackTrace();
            outObj.put("Status", 0);
            outObj.put("res", e.getMessage());
        }finally{
            try {
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
            return outObj.toString();
        }
    }


    public static String responseGETOS85Str(String url,String content,String Charset) {
        String line         = "";
        String message        = "";
        BufferedReader bufferedReader = null;
        JSONObject outObj=new JSONObject();
        try {
            String str="";
            String urlStr="";
            if(content!=null&&!"".equals(content)){
                net.sf.json.JSONObject jsonObject= net.sf.json.JSONObject.fromObject(content);
                // 遍历 JSONObject 的所有键值对
                for (Object key : jsonObject.keySet()) {
                    // 获取键名
                    String keyStr = (String) key;
                    // 获取对应的值
                    Object value = jsonObject.get(keyStr);
                    // 打印键和值
                    str+=keyStr+"="+value+"&";
                }
                str=str.substring(0,str.length()-1);
                urlStr=url+"?"+str;
            }else{
                urlStr=url;
            }
            logger.info("这是GET请求地址以及值："+urlStr);
            // 正式环境地址
            URL restURL = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection)
                    restURL.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("Charset", Charset);
            if (conn.getResponseCode() == HttpURLConnection.HTTP_MOVED_PERM || conn.getResponseCode() == HttpURLConnection.HTTP_MOVED_TEMP) {
                String newUrl = conn.getHeaderField("Location");
                if (newUrl != null) {
                    url = newUrl; // 更新URL为重定向后的新URL
                    conn.disconnect(); // 断开旧连接
                    // 继续循环，发起新的请求
                    logger.info("这是获取的重定向的地址："+newUrl);
                    responseGETOS85Str(url,content,Charset);
                }
            }else if((conn.getResponseCode() != HttpURLConnection.HTTP_OK)){
                message = "发送GET失败！"+ "code="+conn.getResponseCode() + "," + "失败消息："+ conn.getResponseMessage();
                // 定义BufferedReader输入流来读取URL的响应
                InputStream errorStream = conn.getErrorStream();
                if(errorStream != null) {
                    InputStreamReader inputStreamReader = new InputStreamReader(errorStream,"utf-8");
                    bufferedReader = new BufferedReader(inputStreamReader);
                    while ((line = bufferedReader.readLine()) != null) {
                        message += line;
                    }
                    inputStreamReader.close();
                }
                errorStream.close();
                outObj.put("Status", 0);
                logger.info("res："+message);
                outObj.put("res", message);
            }else{
                // 定义BufferedReader输入流来读取URL的响应
                InputStream inputStream = conn.getInputStream();
                InputStreamReader inputStreamReader = new InputStreamReader(inputStream,"utf-8");
                bufferedReader = new BufferedReader(inputStreamReader);
                while ((line = bufferedReader.readLine()) != null) {
                    message += line;
                }
                inputStream.close();
                inputStreamReader.close();
                outObj.put("Status", 1);
                logger.info("res："+message);
                outObj.put("res", message);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("本地get错误信息"+e.getMessage(),e);
            outObj.put("Status", 0);
            logger.info("res："+e.getMessage());
            outObj.put("res", e.getMessage());
        }finally{
            try {
                if (bufferedReader != null) {
                    bufferedReader.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
                logger.error("本地IO错误信息"+ex.getMessage(),ex);
            }
            logger.info("接口请求入参："+content);
            logger.info("接口请求返回数据："+outObj.toString());
            return outObj.toString();
        }
    }


    public static String responseGETOS85StrTwo(String url,String content,String charset){
        String message= "";
        JSONObject outObj=new JSONObject();
        try {
            net.sf.json.JSONObject jsonObject= net.sf.json.JSONObject.fromObject(content);
            String str="";
            // 遍历 JSONObject 的所有键值对
            for (Object key : jsonObject.keySet()) {
                // 获取键名
                String keyStr = (String) key;
                // 获取对应的值
                Object value = jsonObject.get(keyStr);
                // 打印键和值
                str+=keyStr+"="+value+"&";
            }
            str=str.substring(0,str.length()-1);
            String urlStr=url+"?"+str;
            logger.info("这是GET请求地址以及值："+urlStr);
            URL restURL = new URL(urlStr);
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type","application/x-www-form-urlencoded");
            conn.setRequestProperty("Charset", charset);
            if((conn.getResponseCode() != HttpURLConnection.HTTP_OK)){
                message = "发送GET失败！"+ "code="+conn.getResponseCode() + "," + "失败消息："+ conn.getResponseMessage();
                outObj.put("Status", 0);
            }else{
                outObj.put("Status", 1);
            }
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line="";
            while (null != (line = bReader.readLine())) {
                message += line;
            }
            bReader.close();
            conn.disconnect();
            outObj.put("res", message);
        }catch (Exception e){
            e.printStackTrace();
        }
        logger.info("这是GET请求返回值："+message);
        return outObj.toString();
    }


    public static byte[] responseGETOS85Byte(String url,String content) {
        byte[] data=null;
        try {
            net.sf.json.JSONObject jsonObject= net.sf.json.JSONObject.fromObject(content);
            net.sf.json.JSONObject header= net.sf.json.JSONObject.fromObject(jsonObject.get("header"));
            if(jsonObject.containsKey("obj")){
                net.sf.json.JSONObject obj= net.sf.json.JSONObject.fromObject(jsonObject.get("obj"));
                if(obj.containsKey("ticket")){
                    if(obj.get("ticket").toString().length()>0&&!"null".equals(obj.get("ticket").toString())&&obj.get("ticket").toString()!=null
                            &&!"undefined".equals(obj.get("ticket").toString())&&!"null".equals(obj.get("ticket").toString())){
                        url=url+"?ticket=" + obj.get("ticket").toString();
                    }
                }
            }
            URL restURL = new URL(url);
            HttpURLConnection conn = (HttpURLConnection)
                    restURL.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "application/x-www-form-urlencoded");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("x-sg-scenario-code","");
            conn.setRequestProperty("x-sg-scenario-version","");
            conn.setRequestProperty("x-sg-ability-code","");
            conn.setRequestProperty("x-sg-api-code",header.get("svcApiCode").toString());
            conn.setRequestProperty("x-sg-api-version",header.get("svcApiVersion").toString());
            conn.setRequestProperty("x-sg-app-key",header.get("appkey").toString());
            conn.setRequestProperty("x-sg-dest-app-key","");
            conn.setRequestProperty("x-sg-timestamp", header.get("timestamp").toString());
            conn.setRequestProperty("x-sg-scenario-id",header.get("scenarioId").toString());
            conn.setRequestProperty("x-sg-message-id",header.get("messageId").toString());
            conn.setRequestProperty("x-sg-route-type","");
            conn.setRequestProperty("x-sg-route-value","");
            conn.setRequestProperty("x-sg-test",header.get("test").toString());
            conn.setRequestProperty("x-sg-md5-secret",header.get("md5Secret").toString());
            conn.setRequestProperty("x-sg-spanid",header.get("spanid").toString());
            /*若get失败*/
            if((conn.getResponseCode() != HttpURLConnection.HTTP_OK)){
                // 定义BufferedReader输入流来读取URL的响应
                InputStream errorStream = conn.getErrorStream();
                data=toByteArray(errorStream);
                errorStream.close();
            }else{
                // 定义BufferedReader输入流来读取URL的响应
                InputStream inputStream = conn.getInputStream();
                data=toByteArray(inputStream);
                inputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return data;
    }

    public static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024*4];
        int n = 0;
        while (-1 != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }

    public static void main(String[] args) {
       String result= UrlConnection.responseUTF8("http://localhost:8080/EOM/jsp/iboss/iBossByNoUpOrder_iQueTakeEffectContractSvr.do","{\"ROOT\":{\"REQUEST_INFO\":{\"LOGIN_NO\":\"\",\"UNIT_ID\":\"2804209780\",\"PAGE_NUM\":\"1\",\"PAGE_SZIE\":\"5\"},\"REQUEST_DATE\":\"2020-05-13 15:54:36\",\"TOKEN\":\"aefba047b8aee05fe279ce509a8d30\",\"ENTITY_NAME\":\"qryContractFromOrderSysEntity\"}}");
        System.out.printf(result);
    }
}
