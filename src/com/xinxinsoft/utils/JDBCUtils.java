package com.xinxinsoft.utils;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.Vector;

/**
 * @ClassName: JDBCUtils
 * @Description: TODO
 * @Author: 33228
 * @Date: 2022/4/5 12:00
 * @Version: 1.0
 */
public class JDBCUtils {
    static String driver ="oracle.jdbc.OracleDriver";
    static String url ="jdbc:oracle:thin:@//*************:1521/EOMDB";
    static String user ="ZQDDXT";
    static String pwd ="zE=e7h%H";

    static Vector<Connection> pools = new Vector<Connection>();

    public static Connection getDBConnection(){
        try {
            //1.加载驱动
            Class.forName(driver);
            //2.取得数据库连接
            Connection conn =  DriverManager.getConnection(url, user, pwd);
            return conn;
        } catch (SQLException e) {
            e.printStackTrace();
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        return null;
    }

    static {
        int i = 0;
        while(i<10){
            pools.add(getDBConnection());
            i++;
        }
    }

    public static synchronized Connection getPool(){
        if(pools != null && pools.size() > 0){
            int last_ind = pools.size() -1;
            return pools.remove(last_ind);
        }else{
            return getDBConnection();
        }
    }

    public static int insert(String str){
        Connection conn = getPool();
        PreparedStatement pstmt = null;
        try {
            String sql = "INSERT INTO BPMS_GROUP_ASSOCIATION (\"PHONE\", \"GROUPCODE\", \"BUMBER\") VALUES (?, ?, ?)";
            pstmt = conn.prepareStatement(sql);
            String[] resultArray = str.split("\\|");
            pstmt.setString(1, resultArray[0]);
            pstmt.setString(2, resultArray[1]);
            pstmt.setString(3, resultArray[2]);
            return pstmt.executeUpdate();
        } catch (SQLException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }finally{
            if(pstmt != null){
                try {
                    pstmt.close();
                } catch (SQLException e) {
                    e.printStackTrace();
                }
            }

            if(conn != null){
//					conn.close();
                pools.add(conn);
            }
        }
        return 0;
    }
}
