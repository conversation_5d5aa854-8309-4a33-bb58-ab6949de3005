package com.xinxinsoft.utils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.UnknownHostException;
import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.Enumeration;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.servlet.http.HttpServletRequest;

import org.apache.commons.lang.StringUtils;

public class DateUtil {

	public  final static String DATE_FORMAT_YYYY_MM_DD = "yyyy-MM-dd";
	public  final static String DATE_FORMAT_YYYYMMDD = "yyyyMMdd";
	public  final static String DATE_FORMAT_YYYY_MM_DD_HH_MM_SS = "yyyy-MM-dd HH:mm:ss";
	public  final static String DATE_FORMAT_YYYYMMDDHHMMSS = "yyyyMMddHHmmss";

	/**
	 * 获取当前网络ip
	 *
	 * @param request
	 * @return
	 */
	public static String getIpAddr(HttpServletRequest request) {
		String ipAddress = request.getHeader("x-forwarded-for");

		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			ipAddress = request.getHeader("WL-Proxy-Client-IP");
		}
		if (ipAddress == null || ipAddress.length() == 0 || "unknown".equalsIgnoreCase(ipAddress)) {
			try {
				ipAddress = request.getRemoteAddr();
			} catch (Exception e) {
				e.printStackTrace();
				//logger.info("getRemoteAddr 方法获取IP失败");
				try {
					ipAddress = InetAddress.getLocalHost().getHostAddress();
				} catch (UnknownHostException e1) {
					e1.printStackTrace();
				}
			}
			if (!StringUtils.isEmpty(ipAddress)) {
				if (ipAddress.equals("127.0.0.1") || ipAddress.equals("0:0:0:0:0:0:0:1")) {
					//根据网卡取本机配置的IP
					InetAddress inet = null;
					try {
						inet = InetAddress.getLocalHost();
					} catch (UnknownHostException e) {
						e.printStackTrace();
					}
					ipAddress = inet.getHostAddress();
				}
			} else {
				// logger.info("IP获取失败");
			}

		}
		//对于通过多个代理的情况，第一个IP为客户端真实IP,多个IP按照','分割
		if (ipAddress != null && ipAddress.length() > 15) { //"***.***.***.***".length() = 15
			if (ipAddress.indexOf(",") > 0) {
				ipAddress = ipAddress.substring(0, ipAddress.indexOf(","));
			}
		}
		return ipAddress;
	}

	/**
	 * 获取本机ip地址  非127.0.0.1
	 *
	 * @return
	 */
	public static String getLocalIp() {
		try {
			Enumeration<NetworkInterface> allNetInterfaces = NetworkInterface.getNetworkInterfaces();
			while (allNetInterfaces.hasMoreElements()) {
				NetworkInterface netInterface = (NetworkInterface) allNetInterfaces.nextElement();
				Enumeration<InetAddress> addresses = netInterface.getInetAddresses();
				while (addresses.hasMoreElements()) {
					InetAddress ip = (InetAddress) addresses.nextElement();
					if (ip != null && ip instanceof Inet4Address & !ip.isLoopbackAddress() && ip.getHostAddress().indexOf(":") == -1) {
						//System.out.println("本机的IP = " + ip.getHostAddress());
						return ip.getHostAddress();
					}
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
		}

		return null;
	}

	/**
	 * 将字符串转行成时间
	 *
	 * @param str 字符串的格式必须为 YYYY-MM,否则转换失败
	 * @return
	 */
	public static Date convertStringToDateMin(String str) {
		Calendar calendar = Calendar.getInstance();
		try {
			String[] temp = str.split("-");
			calendar.set(Integer.valueOf(temp[0]),
					Integer.valueOf(temp[1]) - 1, Integer.valueOf(temp[2]));
			calendar.set(Calendar.HOUR_OF_DAY, 0);
			calendar.clear(Calendar.MINUTE);
			calendar.clear(Calendar.SECOND);
		} catch (Exception e) {
			//e.printStackTrace();
			return null;
		}
		return calendar.getTime();
	}

	/**
	 * 将字符串转行成日期
	 *
	 * @param str 字符串的格式必须为 YYYY-MM,否则转换失败
	 * @return
	 */
	public static Date convertStringToDateMinWithoutTime(String str) {
		Calendar calendar = Calendar.getInstance();
		try {
			String[] temp = str.split("-");
			calendar.set(Integer.valueOf(temp[0]),
					Integer.valueOf(temp[1]) - 1, Integer.valueOf(temp[2]));
			calendar.add(Calendar.DAY_OF_YEAR, -1);
			calendar.set(Calendar.HOUR_OF_DAY, 23);
			calendar.set(Calendar.MINUTE, 59);
			calendar.set(Calendar.SECOND, 59);
		} catch (Exception e) {
			return null;
		}
		return calendar.getTime();
	}

	public static Date convertStringToDateMax(String str) {
		Calendar calendar = Calendar.getInstance();
		try {
			String[] temp = str.split("-");
			calendar.set(Integer.valueOf(temp[0]),
					Integer.valueOf(temp[1]) - 1, Integer.valueOf(temp[2]));
			calendar.set(Calendar.HOUR_OF_DAY, 23);
			calendar.set(Calendar.MINUTE, 59);
			calendar.set(Calendar.SECOND, 59);
		} catch (Exception e) {
			return null;
		}
		return calendar.getTime();
	}


	/**
	 * 获取输入时间的月份的最后一天
	 *
	 * @param date
	 * @return 返回的时间为输入的年月的第一天的时间
	 */
	public static Date getLastDateOfMonth(Date date) {
		Calendar calendar = Calendar.getInstance();
		try {
			calendar.setTime(date);
			calendar.add(Calendar.MONTH, 1);
			calendar.set(Calendar.DAY_OF_MONTH, 1);
			calendar.add(Calendar.DAY_OF_MONTH, -1);
			calendar.set(Calendar.HOUR_OF_DAY, 23);
			calendar.set(Calendar.MINUTE, 59);
			calendar.set(Calendar.SECOND, 59);
		} catch (Exception e) {
			return null;
		}
		return calendar.getTime();
	}

	/**
	 * 获取输入时间的月份的最后一天
	 *
	 * @param str 字符串的格式必须为 YYYY-MM,否则转行失败
	 * @return 返回的时间为输入的年月的第一天的时间
	 */
	public static Date getLastDateOfMonth(String str) {
		Calendar calendar = Calendar.getInstance();
		try {
			calendar.setTime(convertStringToDateMin(str));
			calendar.add(Calendar.MONTH, 1);
			calendar.set(Calendar.DAY_OF_MONTH, 1);
			calendar.add(Calendar.DAY_OF_MONTH, -1);
			calendar.set(Calendar.HOUR_OF_DAY, 23);
			calendar.set(Calendar.MINUTE, 59);
			calendar.set(Calendar.SECOND, 59);
		} catch (Exception e) {
			return null;
		}
		return calendar.getTime();
	}

	/**
	 * 获取输入时间的月份的最后一天
	 *
	 * @param str 字符串的格式必须为 YYYY-MM,否则转行失败
	 * @return 返回的时间为输入的年月的第一天的时间
	 */
	public static Date getFirstDateOfPreviousMonth(String str) {
		Calendar calendar = Calendar.getInstance();
		try {
			calendar.setTime(convertStringToDateMin(str));
			calendar.add(Calendar.MONTH, -1);
		} catch (Exception e) {
			return null;
		}
		return calendar.getTime();
	}

	/**
	 * 将日期转换为字符串
	 *
	 * @param date：日期
	 * @param pattern：格式
	 * @return
	 */
	public static String convertDateToString(Date date, String pattern) {
		SimpleDateFormat sdf = null;
		String dateString = null;
		if (date != null && pattern != null) {
			sdf = new SimpleDateFormat(pattern);
			dateString = sdf.format(date);
		}
		return dateString;
	}

	/**
	 * 将字符串转行成时间
	 *
	 * @param str 字符串的格式必须为 yyyy-MM-dd HH:mm,否则转换失败
	 * @return
	 */
	public static Date convertStringToDate(String str) {
		Calendar calendar = Calendar.getInstance();
		try {
			String[] tempDateAndTime = str.split(" ");
			String[] tempDate = tempDateAndTime[0].split("-");
			String[] tempTime = tempDateAndTime[1].split(":");
			calendar.set(Integer.valueOf(tempDate[0]),
					Integer.valueOf(tempDate[1]) - 1,
					Integer.valueOf(tempDate[2]),
					Integer.valueOf(tempTime[0]),
					Integer.valueOf(tempTime[1]));
			//calendar.set(Calendar.HOUR_OF_DAY, 0);
			//calendar.clear(Calendar.MINUTE);
			calendar.clear(Calendar.SECOND);
		} catch (Exception e) {
			e.printStackTrace();
			return null;
		}
		return calendar.getTime();
	}

	/**
	 * 加N小时
	 *
	 * @param hour 加N小时
	 */
	public static Date getDateAddHour(int hour) {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar c = Calendar.getInstance();
		c.add(Calendar.HOUR, hour);//要加的小时
		try {
			return format.parse(format.format(c.getTime()).toString());
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 减去或者加上N分钟
	 *
	 * @param minute 减去或者加上N分钟
	 */
	public static Date getDateAddOrMimute(int minute) {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar c = Calendar.getInstance();
		c.add(Calendar.MINUTE, minute);
		try {
			return format.parse(format.format(c.getTime()).toString());
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}

	/**
	 * 加N小时
	 *
	 * @param hour 加N小时
	 */
	public static Date getDateAddHours(int hour, Date d) {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar c = Calendar.getInstance();
		c.setTime(d);
		c.add(Calendar.HOUR, hour);//要加的小时
		try {
			return format.parse(format.format(c.getTime()).toString());
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}

	//获取当前时间
	public static String getDate() {

		Date nowTime = new Date();
		SimpleDateFormat time = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
		return (time.format(nowTime));

	}

	public static Date getDateone() {
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		Calendar c = Calendar.getInstance();
		try {
			return format.parse(format.format(c.getTime()).toString());
		} catch (ParseException e) {
			e.printStackTrace();
			return null;
		}
	}

	public static void main(String[] args) {
		//System.err.println(getLastDateOfMonth("2009-1"));
		//System.out.println(convertStringToDate("2010-09-12 14:00:32"));
		//System.out.println(getDate());
		//System.out.println(getDateAddHours(-2,new Date()));
		//System.out.println(convertDateToString(new Date(),"YYMMDD"));
	}

	/**
	 * 判断是否中文乱码
	 *
	 * @param strName
	 * @return
	 */
	public static boolean isMessyCode(String strName) {
		try {
			Pattern p = Pattern.compile("\\s*|\t*|\r*|\n*");
			Matcher m = p.matcher(strName);
			String after = m.replaceAll("");
			String temp = after.replaceAll("\\p{P}", "");
			char[] ch = temp.trim().toCharArray();

			int length = (ch != null) ? ch.length : 0;
			for (int i = 0; i < length; i++) {
				char c = ch[i];
				if (!Character.isLetterOrDigit(c)) {
					String str = "" + ch[i];
					if (!str.matches("[\u4e00-\u9fa5]+")) {
						return true;
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

		return false;
	}

	/*时间加减得到天数
	 * @param beginDateStr
	 * @param endDateStr
	 * @return
	 * long
	 * <AUTHOR>
	 */
	public static long getDaySub(Date beginDate, Date endDate) {
		long day = 0;
		day = (endDate.getTime() - beginDate.getTime()) / (24 * 60 * 60 * 1000);
		return day;
	}

	/**
	 * 获取当前时间：格式转换：默认格式：yyyy-MM-dd HH:mm:ss
	 *
	 * @param str 格式参数：null
	 * @return date 时间：
	 */
	public static Date toDate(String str) {
		Date d = new Date();
		SimpleDateFormat sdf = null;
		if (str == null) {
			sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		} else {
			sdf = new SimpleDateFormat(str);
		}
		try {
			return sdf.parse(sdf.format(d));
		} catch (ParseException e) {
			throw new Error(" date Exception throw ");
		}
	}

	/**
	 * 根据字符串转换成指定的时间格式 默认格式：yyyy-MM-dd HH:mm:ss
	 *
	 * @param orderReqTimeLimit
	 * @return
	 */
	public static Date stringToDate(String orderReqTimeLimit, String str) {
		SimpleDateFormat sdf = null;
		if (str == null) {
			sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		} else {
			sdf = new SimpleDateFormat(str);
		}
		try {
			return sdf.parse(orderReqTimeLimit);
		} catch (ParseException e) {
			throw new Error("时间格式和时间字符不一致");
		}

	}


	/**
	 * 时间格式
	 *
	 * @param date
	 * @return
	 */
	public static String dateToString(Date date) {
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		try {
			String time = sdf.format(date);
			return time;
		} catch (Exception e) {
			e.printStackTrace();
		}
		return null;
	}


	public static String getorderid() {
		// 获取时间戳
		Date date = new Date();
		SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd");
		String time = format.format(date.getTime());
		// 获取9位随机数
		String randomStr = getRandomCharAndNumr();
		String orderid = time + randomStr;
		return orderid;
	}

	public static String getRandomCharAndNumr() {
		String randomStr = "";
		for (int i = 0; i < 9; i++) {
			int random = (int) (Math.random() * 9);
			if (randomStr.indexOf(random + "") != -1) {
				i = i - 1;
			} else {
				randomStr += random;
			}
		}
		return randomStr;
	}
}
