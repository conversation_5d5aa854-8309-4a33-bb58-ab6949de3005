package com.xinxinsoft.utils;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.dom4j.Document;
import org.dom4j.Element;


public class XMLToVeiw {

	private static String xmlString = "<CONTENT><NAME VALUE='李四' TYPE='TXET' CN='用户名'>李四</NAME><PASS VALUE='pass' CN='密码' TYPE='PASSWORD'>pass</PASS></CONTENT>";
    //select xmltype(t.xmlcontent).extract('//CONTENT//NAME/text()').getstringval() from afr_xml t
	//select * from afr_xml t where xmltype(t.xmlcontent).extract('//CONTENT//NAME/text()').getstringval()='李四'
	
	/**
	 * XML格式转换HTML(table)
	 * @param xmlString XML内容
	 * @param tdCountLine TD的展示个数
	 * @return  返回HTML信息
	 * @throws Exception 错误
	 */
	public static String XmlToHtml(String xmlString,int tdCountLine) throws Exception {
		InputStream  in= new ByteArrayInputStream(
				xmlString.getBytes("UTF-8"));
		Document doc = XmlUtil.getDocument(in);

		List<Element> list = XmlUtil.getElements(XmlUtil.getRoot(doc));
		int i=0;
		StringBuilder html=new StringBuilder();
		for (Element element : list) {
			
			if(tdCountLine==1){
				html.append("<tr>");
				html.append(generateHTML(element.getName(),XmlUtil.getAttributeByName(element,"CN").getText(),XmlUtil.getAttributeByName(element,"VALUE").getText(),element.getText()));
				html.append("</tr>");
			}else{
				//结束标签
				if(i%tdCountLine ==0 && i!=0){
					html.append("</tr>");
				}
				//判断添加开始标签
				if(i%tdCountLine ==0){
					html.append("<tr>");
				}
				html.append(generateHTML(element.getName(),XmlUtil.getAttributeByName(element,"CN").getText(),XmlUtil.getAttributeByName(element,"VALUE").getText(),element.getText()));
				
				//判断总数量不能除尽时，添加结束标签
				if(list.size()==(i+1)){
					html.append("</tr>");
				}
			}
			i++;
		}
		return html.toString();
	}
	
	/**
	 * 根据XMLnode元素反馈HTML
	 * @param T 生成HTML类型
	 * @param title 生成标题
	 * @param content 生成内容
	 */
	public static String generateHTML(String CODE,String CN,String VALUE,String TEXT){
		StringBuilder html=new StringBuilder();
		html.append("<td style=\"text-align: right;\"><label>"+CN+"：</label></td>");
		html.append("<td style=\"text-align: left;\"><input type=\"hidden\" id=\""+CODE+"\" value=\""+VALUE+"\"><label>"+TEXT+"</label></td>");
		return html.toString();
	}

	/**
	 * JSON(制定格式)转换XML
	 * JSON格式" [{\"CN\":\"用户名\",\"VALUE\":\"李四\",\"TEXT\":\"李四\",\"CODE\":\"NAME\",\"TYPE\":\"TEXT\"},{\"CN\":\"密码\",\"VALUE\":\"pass\",\"TEXT\":\"pass\",\"CODE\":\"PASS\",\"TYPE\":\"PASSWORD\"}]"
	 * @param json JSON(制定格式)
	 * @return 返回转化后的XML
	 */
	public static String VeiwToXML(String json){
		StringBuilder xmlBuilder=new StringBuilder();
		//将String转换成jsonArray
		JSONArray jsonArray=JSONArray.fromObject(json);
		
		xmlBuilder.append("<CONTENT>");
		for (int i = 0; i < jsonArray.size(); i++) {
			JSONObject jsonObj=(JSONObject) jsonArray.get(i);
			xmlBuilder.append("<"+jsonObj.getString("CODE")+" VALUE='"+jsonObj.getString("VALUE")+"' CN='"+jsonObj.getString("CN")+"' TYPE='"+jsonObj.getString("TYPE")+"'>");
			xmlBuilder.append(jsonObj.get("TEXT"));
			xmlBuilder.append("</"+jsonObj.getString("CODE")+">");
		}
		xmlBuilder.append("</CONTENT>");
		return xmlBuilder.toString();
	}
	/**
	 * XML文件转换为MAP数据
	 * @param xmlString
	 * @return
	 * @throws Exception 
	 */
	public static List<Map<String,String>> ViewToMap(String xmlString) throws Exception{
		InputStream  in= new ByteArrayInputStream(xmlString.getBytes("UTF-8"));
		Document doc = XmlUtil.getDocument(in);

		List<Element> list = XmlUtil.getElements(XmlUtil.getRoot(doc));
		List<Map<String,String>> outListMap=new ArrayList<Map<String,String>>();
		for (Element element : list) {
			Map<String, String> outMap=new HashMap<String, String>();
			//HTML对应的ID
			outMap.put("ID", element.getName());
			//放入键值对
			outMap.put("VALUE", XmlUtil.getAttributeByName(element,"VALUE").getText()); 
			//获取类型
			outMap.put("TYPE", XmlUtil.getAttributeByName(element,"TYPE").getText());
			//放入LIST
			outListMap.add(outMap);
			
		}
		
		return outListMap;
	}
	
	
	public static void main(String[] args) {
		try {  
		/*	List<Map<String,String>> outListMap=ViewToMap(xmlString);
			System.out.println(outListMap.size());
			System.out.println(JSONHelper.SerializeWithNeedAnnotation(outListMap));
			String json=" [{\"CN\":\"用户名\",\"VALUE\":\"李四\",\"TEXT\":\"李四\",\"CODE\":\"NAME\",\"TYPE\":\"TEXT\"}," +
					"{\"CN\":\"密码\",\"VALUE\":\"pass\",\"TEXT\":\"pass\",\"CODE\":\"PASS\",\"TYPE\":\"PASSWORD\"}]";
			System.out.println(VeiwToXML(json));
			System.out.println("-----------------------------------------------------");
			System.out.println(XmlToHtml(VeiwToXML(json),2));*/
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
}
