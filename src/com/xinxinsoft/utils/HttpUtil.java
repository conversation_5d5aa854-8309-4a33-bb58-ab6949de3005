package com.xinxinsoft.utils;

import java.io.*;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Properties;

import com.xinxinsoft.sendComms.CMCCOpenService;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpHost;
import org.apache.http.HttpResponse;
import org.apache.http.auth.AuthScope;
import org.apache.http.auth.UsernamePasswordCredentials;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.params.HttpClientParams;
import org.apache.http.conn.params.ConnRoutePNames;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.HttpConnectionParams;
import org.apache.http.params.HttpParams;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import com.xinxinsoft.service.config.Config;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import sun.misc.BASE64Decoder;

/**
 * 模拟http请求调用接口
 * <AUTHOR>
 *
 */
@SuppressWarnings("deprecation")
public class HttpUtil {
	
	/**
	 * 资产App接口地址
	 */
	private String eamHttpUrl = null;
	
	/**
	 * 是否使用代理，true-使用代理，false-不使用代理
	 */
	private static boolean proxyUse = false;
	/**
	 * 代理url
	 */
	private static String proxyUrl = null;
	/**
	 * 代理端口
	 */
	private static int proxyPort = 0;
	
	/**
	 * 资产接口中转系统地址
	 */
	private String EAMManagementHttpUrl = null;

	private static HttpUtil instance = new HttpUtil();
	
	private static final org.apache.log4j.Logger log = Logger.getLogger(HttpUtil.class);
	
	public static HttpUtil getInstance() {
		return instance;
	}
	
	public HttpUtil() {
		loadConfig();
	}
	
	/**
	 * 功能：加载数据库配置文件
	 */
	public void loadConfig() {
		InputStream in = this.getClass().getClassLoader().getResourceAsStream("WebService-config.properties");
		Properties properties = new Properties();
		try {
			properties.load(in);
			eamHttpUrl = properties.getProperty("eamHttpUrl");
			proxyUse = "true".equals(properties.getProperty("proxyUse"));
			proxyUrl = properties.getProperty("proxyUrl");
			proxyPort = Integer.parseInt(StringUtils.isBlank(properties.getProperty("proxyPort"))?"0":properties.getProperty("proxyPort"));
			
			EAMManagementHttpUrl = properties.getProperty("EAMManagementHttpUrl");
		} catch (IOException e) {
			log.error(e.getStackTrace());
			e.printStackTrace();
		} finally {
			try {
				in.close();
				properties.clear();
			} catch (IOException e) {
				log.error(e.getMessage(), e);
				e.printStackTrace();
			}
		}
	}
	
	/**
	 * 功能:通过HttpClient发起Post请求 ---- 使用此方法调用EAM系统后台服务
	 * @param jsonParams
	 * @param actionPath
	 * @return
	 */
	public Object invoke(String jsonParams,String actionPath) {
		try {
			//System.out.println(jsonParams);
			HttpPost httpRequest = new HttpPost(eamHttpUrl.replace("{actionPath}",actionPath));
			jsonParams = URLEncoder.encode(jsonParams, HTTP.UTF_8);
			httpRequest.setEntity(new ByteArrayEntity(jsonParams.getBytes()));
			HttpClient httpClient = getHttpClient(proxyUse);
			HttpResponse response = httpClient.execute(httpRequest);
			if (response.getStatusLine().getStatusCode() == 200) {
				return EntityUtils.toString(response.getEntity());
			} else {
				return new Exception("通信出错。");
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
			return e;
		}
	}
	
	/**
	 * 功能:通过HttpClient发起Post请求 ---- 使用此方法调用EMAManagement系统后台服务
	 * @param jsonParams
	 * @param actionPath
	 * @return
	 */
	public Object invokeEMAManagement(String jsonParams,String actionPath) {
		try {
			//System.out.println(jsonParams);
			HttpPost httpRequest = new HttpPost(EAMManagementHttpUrl.replace("{actionPath}",actionPath));
			jsonParams = URLEncoder.encode(jsonParams,HTTP.UTF_8);
			System.out.println(jsonParams);
			httpRequest.setEntity(new ByteArrayEntity(jsonParams.getBytes()));
			HttpClient httpClient = getHttpClient(false);
			HttpResponse response = httpClient.execute(httpRequest);
			if (response.getStatusLine().getStatusCode() == 200) {
				return EntityUtils.toString(response.getEntity());
			} else {
				return new Exception("通信出错。");
			}
		} catch (Exception e) {
			log.error(e.getMessage(), e);
			e.printStackTrace();
			return e;
		}finally{
			
		}
	}
	
	
	private HttpClient getHttpClient(boolean proxyUse) throws Exception {
		// 创建 HttpParams 以用来设置 HTTP 参数（这一部分不是必需的）
		HttpParams httpParams = new BasicHttpParams();
		// 设置连接超时和 Socket 超时，以及 Socket 缓存大小
		HttpConnectionParams.setConnectionTimeout(httpParams, 100 * 1000);
		HttpConnectionParams.setSoTimeout(httpParams, 100 * 1000);
		HttpConnectionParams.setSocketBufferSize(httpParams, 8192);
		// 设置重定向，缺省为 true
		HttpClientParams.setRedirecting(httpParams, true);
		// 创建一个 HttpClient 实例
		// 注意 HttpClient httpClient = new HttpClient(); 是Commons HttpClient
		// 中的用法，在 Android 1.5 中我们需要使用 Apache 的缺省实现 DefaultHttpClient
		
		
		DefaultHttpClient client = new DefaultHttpClient(httpParams);  
		//enableSSL(client);
//		CloseableHttpClient client = null;
//		SSLContext sslContext = new SSLContextBuilder().loadTrustMaterial(null, new TrustStrategy() { 
//			//信任所有
//			public boolean isTrusted(X509Certificate[] chain, String authType) throws CertificateException { 
//				return true;
//			}
//		}).build();
//		SSLConnectionSocketFactory sslsf = new SSLConnectionSocketFactory(sslContext);

		
        /******************** 设置代理 - 开始 *****************************/
        
        if(proxyUse){
			client.getCredentialsProvider().setCredentials(  
	                new AuthScope(proxyUrl, proxyPort),  
	                new UsernamePasswordCredentials("", ""));  
			HttpHost proxy = new HttpHost(proxyUrl, proxyPort);  
			client.getParams().setParameter(ConnRoutePNames.DEFAULT_PROXY, proxy);  
			
			// 依次是代理地址，代理端口号，协议类型    
//		    HttpHost proxy = new HttpHost(proxyUrl, proxyPort, "http");    
//		    DefaultProxyRoutePlanner routePlanner = new DefaultProxyRoutePlanner(proxy);  
//		    HttpClients.custom().setRoutePlanner(routePlanner);
        }
		
		/******************** 设置代理 - 结束 *****************************/
//        client = HttpClients.custom().setSSLSocketFactory(sslsf).build();
		return client;
	}

	
//	public static void main(String[] args){
//		Object obj = test();
//		System.out.println(obj);
//	}
//	
//	public static Object test()
//	{
//		String loginAccount = "********";
//		String passWord = "1234";
//		
//		JSONObject jsonParams = new JSONObject();
//		JSONObject items = new JSONObject();
//		try {
//			items.put("account", loginAccount);
//			items.put("password", MD5Utils.dencryption(passWord));
//			jsonParams.put("provinceCode", CacheConst.PROVINCE_CODE);
//			jsonParams.put("items",items);
//		} catch (JSONException e) {
//			e.printStackTrace();
//			return e;
//		}
//
//		return HttpUtil.getInstance().invokeEMAManagement(jsonParams.toString(),"eamAppOrgUserService_login.app");
//
//	}
	/**
	 * 请求无纸化系统拿到盖章图
	 * @param ContractNO  合同编号  例: 【雅安分公司-宝兴分公司-集团专线】*************
	 * @param OperName  合同创建人
	 * @param operDate  合同创建时间
	 * @param ContractType  合同类型
	 * @param Region  所属地区
	 * @param path  路径
	 * @return
	 */
	public static String  getPaperless(String contractNum,String createName ,String createDate,String ContractType,String Region,String path){

		try {
			String xmlStr = CMCCOpenService.getInstance().paperlessSystem(contractNum, createName, createDate
					, ContractType, Region);
			// 字符串转XML
			Map<String, Object> map = new HashMap<String, Object>();
			Document document = DocumentHelper.parseText(xmlStr);
			Element element = XmlUtil.getRoot(document);
			List<Element> elements = XmlUtil.getElements(element);
			for (Element elemen : elements) {
				map.put(elemen.getName(), elemen.getText());
			}
			if ("000000".equals(map.get("RETNCD"))) { //表示请求成功
				Long time = System.currentTimeMillis();
				String url_path = path + "GZ_" + time + ".png";
				byte[] buffer = new BASE64Decoder().decodeBuffer(map.get("PDBS64").toString());
				ByteArrayInputStream in = new ByteArrayInputStream(buffer);
				FileOutputStream out = new FileOutputStream(url_path);
				out.write(buffer);
				in.close();
				out.close();
				return url_path;
			} else {
				log.error("请求无纸化服务失败！"+map.get("RETNCD"));
				return null;
			}
		}catch (Exception e){
			//e.printStackTrace();
			log.error("异常===>"+e.getMessage());
			return null;
		}
	}


	/**
	 * 获取用户头像存放地址
	 * @return
	 */
	public static String getUserPhotoDir(){
		
		return Config.getString("USER_PHOTO_DIR");
	}
	
	public static String appGetOrderNumber(){
		
		return Config.getString("APP_GETORDERNUMBER");
	}
	
	/**
	 * 获取用户头像HTTP访问地址
	 * @return
	 */
	public static String getUserPhotoURL(){
		return Config.getString("USER_PHOTO_URL");
	}
	
	
	/**
	 * 获取附件下载地址
	 * @return
	 */
	public static String getFileURL(){
		
		return Config.getString("FILE_URL");
	}
	
	/**
	 * 获取专线web端请求地址jpm
	 * @return
	 */
	public static String getAppJbpmDi(){
		
		return Config.getString("APP_JBPM_DI");
	}
	/**
	 * 附件上传：请求web 端地址
	 * @return
	 */
	public static String getAppFlie(){
		return Config.getString("APP_FILE_UPLOAD_URL");
	}
	
	/**
	 * 获取通用首页代办
	 * @return
	 */
	public static String getAppWaitTaskTy(){
		
		return Config.getString("APP_WAIT_TASK_TY");
	}
	
	/**
	 * 获取WEB端4A认证中转接口
	 * @return
	 */
	public static String getAppCheck4A(){
		
		return Config.getString("APP_CHECK_4A_URL");
	}
	
	/**
	 * 获取WEB端短信认证接口
	 * @return
	 */
	public static String getAppCheckMsg(){
		
		return Config.getString("APP_CHECK_MSG_URL");
	}	
	
	/**
	 * 获取WEB端获取集团客户信息
	 * @return
	 */
	public static String getGroupCustomer(){
		
		return Config.getString("APP_GET_GROUPCUSTOMER_URL");
	}		
	
	/**
	 * FtpUrl 配置文件中获取URL默认地址
	 * @return
	 */
	public static String getFtpURL(){
		
		return Config.getString("FTP_URL");
	}
	/**
	 * 配置临时文件的存放地址
	 * @return
	 */
	public static String getFtpDownloadURL(){
		
		return Config.getString("FTP_TEMP_FILEPATH");
	}
	public static String getEditionURL(){
		
		return Config.getString("APP_EDITION_DIR");
	}
	/**
	 * 下载
	 * @return
	 */
	public static String getFtpDownloadURLS(){
		
		return Config.getString("APP_DOWNLOAD");
	}
	
	
	
	/**
	 * 下载
	 * @return
	 */
	public static String getFtpDOWNLOADPHOTO(){
		
		return Config.getString("APP_DOWNLOADPHOTO");
	}
	
	
	/**
	 * 删除附件及附件相关表
	 * @return
	 */
	public static String getFtpDeleteURL(){
		
		return Config.getString("APP_DELETE_ATT");
	}
	
	/**
	 * 只删除附件文件
	 * @return
	 */
	public static String getFtpDeleteFileURL(){
		
		return Config.getString("APP_DELETE_FILE");
	}	
	
	/**
	 * 获取上传进度
	 * @return
	 */
	public static String getProgressSvrUrl(){
		
		return Config.getString("APP_GET_PROGRESS");
	}		
	
	/**
	 * 重置上传进度
	 * @return
	 */
	public static String resetProgressSvrUrl(){
		
		return Config.getString("APP_RESET_PROGRESS");
	}
	/**
	 * 新建小微集团
	 * @return
	 */
	public static String s3851AppCfmSvrUrl(){ 
		return Config.getString("APP_S851_CFM_PROGRESS");
	}	
	/**
	 * 小微集团查询
	 * @return
	 */
	public static String s3851AppQrySvrUrl(){ 
		return Config.getString("APP_S851_QRY_PROGRESS");
	}	
}
