package com.xinxinsoft.utils;

import java.awt.Color;
import java.awt.Graphics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.geom.AffineTransform;
import java.awt.image.AffineTransformOp;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

import javax.imageio.ImageIO;


/**
 * 图像处理
 * <AUTHOR>
 *
 */
public class ImageUtils {
	
	public static String[] typeStrings = {"jpg", "jpeg", "png", "gif", "bmp"};

	/**
	 * @param args
	 */
	public static void main(String[] args) {
		//原始图片
    	String  imageString = "F:\\servers\\apache-tomcat-6.0.14\\webapps\\newsSrv\\File\\ImageFiles\\2012-09-20\\27\\Koala.jpg";
    	//处理后的图片保存地址
    	String pathString = "d:/testImage";
        // 1-缩放图像：
        // 方法一：按比例缩放
//    	ImageUtils.scale(imageString, pathString + "/abc_scale.jpg", 240, "jpg");//测试OK
        // 方法二：按高度和宽度缩放
    	ImageUtils.scale2(imageString, pathString + "/abc_scale2.png", 89, 70, "png", false);//测试OK
	}
	
	/**
     * 缩放图像（按图片宽度等比例缩放）
     * @param srcImageFile 源图像文件地址
     * @param result 缩放后的图像地址
     * @param needWidth 需要的图片的宽度
     * @param type 类型（jpg，png等）
     */
    public final static BufferedImage scale(String srcImageFile, String result, int needWidth, String type) {
    	BufferedImage tag = null;
    	if( needWidth > 0 && needWidth < 5000){
	        try {
	        	File file = new File(srcImageFile);
	        	if(file.exists()){
		            BufferedImage src = ImageIO.read(new File(srcImageFile)); // 读入文件
		            int width = src.getWidth(); // 得到源图宽
		            int height = src.getHeight(); // 得到源图长
		            double scale = Double.valueOf(needWidth) / Double.valueOf(width);
		            System.out.println(scale);
	//	            if (flag) {// 放大
		                width = (int)(width * scale);
		                height = (int)(height * scale);
	//	            } else {// 缩小
	//	                width = (int)(width / scale);
	//	                height = (int)(height / scale);
	//	            }
		            System.out.println(width);
		            System.out.println(height);
		            Image image = src.getScaledInstance(width, height, Image.SCALE_DEFAULT);
		            tag = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
		            Graphics g = tag.getGraphics();
		            g.drawImage(image, 0, 0, null); // 绘制缩小后的图
		            g.dispose();
		            ImageIO.write(tag, checkType(type), new File(result));// 输出到文件流
	        	}
	        } catch (IOException e) {
	            e.printStackTrace();
	        }
    	}
        return tag;
    }
    
    /**
     * 缩放图像（按高度和宽度缩放）
     * @param srcImageFile 源图像文件地址
     * @param result 缩放后的图像地址
     * @param height 缩放后的高度
     * @param width 缩放后的宽度
     * @param bb 比例不对时是否需要补白：true为补白; false为不补白;
     */
    public final static Image scale2(String srcImageFile, String result, int width, int height, String type, boolean bb) {
    	Image itemp = null;
        try {
            double ratiow = 0.0; // 缩放比例
            double ratioh = 0.0; // 缩放比例
            File f = new File(srcImageFile);
            if(f.exists()){
	            BufferedImage bi = ImageIO.read(f);
	            itemp = bi.getScaledInstance(width, height, Image.SCALE_SMOOTH);
	            // 计算比例
	            if ((bi.getHeight() > height) || (bi.getWidth() > width)) {
//	                if (bi.getHeight() > bi.getWidth()) {
//	                    ratio = (new Integer(height)).doubleValue() / bi.getHeight();
//	                } else {
//	                    ratio = (new Integer(width)).doubleValue() / bi.getWidth();
//	                }
	            	ratiow = (new Integer(width)).doubleValue() / bi.getWidth();
	            	ratioh = (new Integer(height)).doubleValue() / bi.getHeight();
	                AffineTransformOp op = new AffineTransformOp(AffineTransform.getScaleInstance(ratiow, ratioh), null);
	                itemp = op.filter(bi, null);
	            }
	            if (bb) {//补白
	                BufferedImage image = new BufferedImage(width, height, BufferedImage.TYPE_INT_RGB);
	                Graphics2D g = image.createGraphics();
	                g.setColor(Color.white);
	                g.fillRect(0, 0, width, height);
	                if (width == itemp.getWidth(null))
	                    g.drawImage(itemp, 0, (height - itemp.getHeight(null)) / 2,
	                            itemp.getWidth(null), itemp.getHeight(null),
	                            Color.white, null);
	                else
	                    g.drawImage(itemp, (width - itemp.getWidth(null)) / 2, 0,
	                            itemp.getWidth(null), itemp.getHeight(null),
	                            Color.white, null);
	                g.dispose();
	                itemp = image;
	            }
	            ImageIO.write((BufferedImage) itemp, checkType(type), new File(result));
//	            readImageStream(itemp);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return itemp;
    }
    
    /**
     * 检查图片类型
     * @param type
     * @return
     */
    private static String checkType(String type){
    	String t = "png";
    	if(type != null){
    		for(String s: typeStrings){
    			if(type.toLowerCase().equals(s)){
    				t = type;
    				break;
    			}
    		}
    	}
    	return t;
    }
    
    public static byte[] readImageStream(Image img){
//    	ImageOutputStream iiStream = null;
    	ByteArrayOutputStream baos = null;
    	byte[] bal = null;
    	try {
//    		iiStream = ImageIO.createImageOutputStream(new ByteArrayOutputStream());
    		baos = new ByteArrayOutputStream();
    		ImageIO.write((BufferedImage)img, "png", baos);
			System.out.println("以字节为单位读取文件内容，一次读多个字节：");
			if(baos != null){
				bal = baos.toByteArray();
//				for(int i = 0, n = bal.length; i< n; i++){
//					System.out.println(bal[i]);
//				}
			}
//			//一次读多个字节  
//			byte[] tempbytes = new byte[100];
//			int byteread = 0;
//			//读入多个字节到字节数组中，byteread为一次读入的字节数  
//			while ((byteread = iiStream.read(tempbytes)) != -1) {
//				System.out.write(tempbytes, 0, byteread);
//				System.out.println(byteread);
//			}
		} catch (Exception e1) {
			e1.printStackTrace();
		} finally {
			if (baos != null) {
				try {
					baos.close();
				} catch (IOException e1) {
					e1.printStackTrace();
				}
			}
		}
		return bal;
    }
    
    /** 
	 * 以字节为单位读取文件，常用于读二进制文件，如图片、声音、影像等文件。 
	 * @param fileName 文件的名 
	 */
	public static void readFileByBytes(String fileName) {
		File file = new File(fileName);
		InputStream in = null;
		try {
		//	System.out.println("以字节为单位读取文件内容，一次读一个字节：");
			// 一次读一个字节  
			in = new FileInputStream(file);
			int tempbyte;
			while ((tempbyte = in.read()) != -1) {
				System.out.write(tempbyte);
			}
			in.close();
		} catch (IOException e) {
			e.printStackTrace();
			return;
		}
		try {
			System.out.println("以字节为单位读取文件内容，一次读多个字节：");
			//一次读多个字节  
			byte[] tempbytes = new byte[100];
			int byteread = 0;
			in = new FileInputStream(fileName);
			//读入多个字节到字节数组中，byteread为一次读入的字节数  
			while ((byteread = in.read(tempbytes)) != -1) {
				System.out.write(tempbytes, 0, byteread);
			}
		} catch (Exception e1) {
			e1.printStackTrace();
		} finally {
			if (in != null) {
				try {
					in.close();
				} catch (IOException e1) {
				}
			}
		}
	} 

	
	 /**
     * 图片缩放.
     * 
     * @param width
     *            需要的宽度
     * @param height
     *            需要的高度
     * @param openUrl 文件路径
     * @param saveUrl 保存路径
     * @param saveName 保存文件名称
     * @param suffix  文件后缀名
     * @throws Exception
     */
    public static void zoom(int width, int height,String openUrl,String saveUrl,String saveName,String suffix) throws Exception {
        double sx = 0.0;
        double sy = 0.0;

        File file = new File(openUrl);
        if (!file.isFile()) {
            throw new Exception("ImageDeal>>>" + file + " 不是一个图片文件!");
        }
        BufferedImage bi = ImageIO.read(file); // 读取该图片
        // 计算x轴y轴缩放比例--如需等比例缩放，在调用之前确保参数width和height是等比例变化的
        sx = (double) width / bi.getWidth();
        sy = (double) height / bi.getHeight();

        AffineTransformOp op = new AffineTransformOp(
                AffineTransform.getScaleInstance(sx, sy), null);
        File sf = new File(saveUrl, saveName + "." + suffix);
        Image zoomImage = op.filter(bi, null);
        try {
            ImageIO.write((BufferedImage) zoomImage, suffix, sf); // 保存图片
        } catch (Exception e) {
            e.printStackTrace();
        }

    }
    
    /**
     * 
     * 旋转
     * 
     * @param degree 旋转角度
     * @param openUrl 文件路径
     * @param saveUrl 保存路径
     * @param saveName 保存文件名称
     * @param suffix  文件后缀名
     * @throws Exception
     */
    public static void spin(int degree,String openUrl,String saveUrl,String saveName,String suffix) throws Exception {
        int swidth = 0; // 旋转后的宽度
        int sheight = 0; // 旋转后的高度
        int x; // 原点横坐标
        int y; // 原点纵坐标

        File file = new File(openUrl);
        if (!file.isFile()) {
            throw new Exception("ImageDeal>>>" + file + " 不是一个图片文件!");
        }
        BufferedImage bi = ImageIO.read(file); // 读取该图片
        // 处理角度--确定旋转弧度
        degree = degree % 360;
        if (degree < 0)
            degree = 360 + degree;// 将角度转换到0-360度之间
        double theta = Math.toRadians(degree);// 将角度转为弧度

        // 确定旋转后的宽和高
        if (degree == 180 || degree == 0 || degree == 360) {
            swidth = bi.getWidth();
            sheight = bi.getHeight();
        } else if (degree == 90 || degree == 270) {
            sheight = bi.getWidth();
            swidth = bi.getHeight();
        } else {
            swidth = (int) (Math.sqrt(bi.getWidth() * bi.getWidth()
                    + bi.getHeight() * bi.getHeight()));
            sheight = (int) (Math.sqrt(bi.getWidth() * bi.getWidth()
                    + bi.getHeight() * bi.getHeight()));
        }

        x = (swidth / 2) - (bi.getWidth() / 2);// 确定原点坐标
        y = (sheight / 2) - (bi.getHeight() / 2);

        BufferedImage spinImage = new BufferedImage(swidth, sheight,
                bi.getType());
        // 设置图片背景颜色
        Graphics2D gs = (Graphics2D) spinImage.getGraphics();
        gs.setColor(Color.white);
        gs.fillRect(0, 0, swidth, sheight);// 以给定颜色绘制旋转后图片的背景

        AffineTransform at = new AffineTransform();
        at.rotate(theta, swidth / 2, sheight / 2);// 旋转图象
        at.translate(x, y);
        AffineTransformOp op = new AffineTransformOp(at,
                AffineTransformOp.TYPE_BICUBIC);
        spinImage = op.filter(bi, spinImage);
        File sf = new File(saveUrl, saveName + "." + suffix);
        ImageIO.write(spinImage, suffix, sf); // 保存图片
    }
    /**
     * 马赛克化.
     * @param size  马赛克尺寸，即每个矩形的长宽
     * @param openUrl 文件路径
     * @param saveUrl 保存路径
     * @param saveName 保存文件名称
     * @param suffix  文件后缀名
     * @return
     * @throws Exception
     */
    public static boolean mosaic(int size,String openUrl,String saveUrl,String saveName,String suffix) throws Exception {
        File file = new File(openUrl);
        if (!file.isFile()) {
            throw new Exception("ImageDeal>>>" + file + " 不是一个图片文件!");
        }
        BufferedImage bi = ImageIO.read(file); // 读取该图片
        BufferedImage spinImage = new BufferedImage(bi.getWidth(),
                bi.getHeight(), bi.TYPE_INT_RGB);
        if (bi.getWidth() < size || bi.getHeight() < size || size <= 0) { // 马赛克格尺寸太大或太小
            return false;
        }

        int xcount = 0; // 方向绘制个数
        int ycount = 0; // y方向绘制个数
        if (bi.getWidth() % size == 0) {
            xcount = bi.getWidth() / size;
        } else {
            xcount = bi.getWidth() / size + 1;
        }
        if (bi.getHeight() % size == 0) {
            ycount = bi.getHeight() / size;
        } else {
            ycount = bi.getHeight() / size + 1;
        }
        int x = 0;   //坐标
        int y = 0;
        // 绘制马赛克(绘制矩形并填充颜色)
        Graphics gs = spinImage.getGraphics();
        for (int i = 0; i < xcount; i++) {
            for (int j = 0; j < ycount; j++) {
                //马赛克矩形格大小
                 int mwidth = size;
                 int mheight = size;
                 if(i==xcount-1){   //横向最后一个比较特殊，可能不够一个size
                     mwidth = bi.getWidth()-x;
                 }
                 if(j == ycount-1){  //同理
                     mheight =bi.getHeight()-y;
                 }
              // 矩形颜色取中心像素点RGB值
                int centerX = x;
                int centerY = y;
                if (mwidth % 2 == 0) {
                    centerX += mwidth / 2;
                } else {
                    centerX += (mwidth - 1) / 2;
                }
                if (mheight % 2 == 0) {
                    centerY += mheight / 2;
                } else {
                    centerY += (mheight - 1) / 2;
                }
                Color color = new Color(bi.getRGB(centerX, centerY));
                gs.setColor(color);
                gs.fillRect(x, y, mwidth, mheight);
                y = y + size;// 计算下一个矩形的y坐标
            }
            y = 0;// 还原y坐标
            x = x + size;// 计算x坐标
        }
        gs.dispose();
        File sf = new File(saveUrl, saveName + "." + suffix);
        ImageIO.write(spinImage, suffix, sf); // 保存图片
        return true;
    }
    /**
     * 将本地图片进行Base64位编码
     * @param imageFile
     * @return
     */
    public static String encodeImgageToBase64(String imageFile) {
        // 其进行Base64编码处理
        byte[] data = null;
        // 读取图片字节数组
        try {
            InputStream in = new FileInputStream(imageFile);
            data = new byte[in.available()];
            in.read(data);
            in.close();
        } catch (IOException e) {
            e.printStackTrace();
        }

        // 对字节数组Base64编码
        return Base64Util.encode(data);
    }
}
