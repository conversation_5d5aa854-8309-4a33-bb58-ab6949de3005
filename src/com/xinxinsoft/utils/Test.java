package com.xinxinsoft.utils;


import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.ESBReqMsgUtil;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.utils.result.Result;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.UnsupportedEncodingException;
import java.util.ArrayList;
import java.util.List;

public class Test {
    private static final Logger logger= LoggerFactory.getLogger(Test.class);


    public static void main(String[] args) throws InterruptedException {
        String pushUrl = "http://**************:52000/esbWS/rest/";


        List<postInfo> postInfoList = new ArrayList<postInfo>();
        postInfoList.add(new postInfo("com_sitech_ordersvc_person_comp_inter_s4000_IP4000BusiRouterCoSvc_pCfmRouter","{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241730690787984\",\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\",\"ROUTING\":{\"ROUTE_KEY\":\"14\",\"ROUTE_VALUE\":\"aT01e7C05\"}},\"WORN_SERV_CODE\":\"sGrpWhiteListSvc\",\"COMMON_INFO\":{\"PROVINCE_GROUP\":\"10008\"},\"UNIT_ID\":\"**********\",\"LOGIN_NO\":\"aT01e7C05\",\"EXP_DATE\":\"**************\",\"OP_DESC\":\"湖南建工集团有限公司新280白名单申请\"}}"));
        postInfoList.add(new postInfo("com_sitech_acctmgr_deal_inter_IInvoiceTaxRate_queryTaxRate","{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241722328465400\",\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\",\"ROUTING\":{\"ROUTE_KEY\":\"12\",\"ROUTE_VALUE\":\"**************\"}},\"BODY\":{\"PHONE_NO\":\"***********\",\"CONTRACT_NO\":\"**************\"}}}"));
        postInfoList.add(new postInfo("com_sitech_acctmgr_inter_invoice_IConsumeInvoiceQuery_qry","{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"CHANNEL_ID\":\"155\",\"PASSWORD\":\"123456\",\"ROUTING\":{\"ROUTE_KEY\":\"12\",\"ROUTE_VALUE\":\"**************\"},\"ENDUSRIP\":\"\",\"USERNAME\":\"zqddxt\",\"CONTACT_ID\":\"21292426241744967857626\",\"ENDUSRLOGINID\":\"\",\"ENV_ID\":\"1\"},\"BODY\":{\"CONTRACT_NO\":\"**************\",\"ID_NO\":\"\",\"PHONE_NO\":\"***********\",\"BEGIN_DATE\":\"202411\",\"END_DATE\":\"202412\",\"DEC_FLAG\":\"1\",\"USER_FLAG\":\"0\",\"PRINT_TYPE\":\"2\",\"LOGIN_NO\":\"aBiS76008\",\"LOGIN_GROUP_ID\":\"1774916\",\"BUSI_GROUP_ID\":\"1774916\",\"REGION_ID\":11,\"OP_CODE\":\"8224\"}}}"));
        postInfoList.add(new postInfo("s2400BackFeeCfm","{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241747269482015\",\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\",\"ROUTING\":{\"ROUTE_KEY\":\"14\",\"ROUTE_VALUE\":\"fde1q0323\"}},\"BODY\":{\"LOGIN_NO\":\"fde1q0323\",\"PHONE_NO\":\"***********\",\"OP_CODE\":\"8037\",\"OP_NOTE\":\"订单补收申请\",\"OP_SN\":\"DZ202505141030418290\",\"PAY_TYPE\":\"w\",\"CONTRACT_NO\":**************,\"ID_NO\":**************,\"ADJ_FEE\":-39800,\"ADJ_INFO\":\"0q1q|-39800|#\",\"PBIZ_TYPE\":\"1\",\"CBIZ_TYPE\":\"集团业务其他错收\",\"BILL_MONTH\":\"202505\",\"FILE_NAME\":\"*************.jpg\",\"FILE_PATH\":\"/ftpsale/billingfile/*************.jpg|\",\"UNIT_ID\":\"**********\"}}}"));

        for (postInfo postInfo : postInfoList){
            String resultStr = CMCC1000OpenService.getInstance().bdcesPatamss(pushUrl + postInfo.getUrl(), postInfo.getParam());
            logger.info("调用测试环境 ——》输出参数：{}", resultStr);
            Thread.sleep(3000);
        }

    }


}

class postInfo{
    private String url;
    private String param;

    public String getUrl() {
        return url;
    }
    public String getParam() {
        return param;
    }
    public postInfo(String url, String param) {
        super();
        this.url = url;
        this.param = param;
    }
}

