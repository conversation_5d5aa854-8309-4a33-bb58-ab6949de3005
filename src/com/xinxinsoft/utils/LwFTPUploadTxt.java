package com.xinxinsoft.utils;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPFile;

import sun.net.TelnetInputStream;

/**
 * 
 * <AUTHOR>
 *
 */
public class LwFTPUploadTxt {
	
	private String linkState = "";//0 主路径连接成功 1 备用路径连接成功

	/**
	 * 绿网ftp登录
	 * @return
	 */
	public FTPClient getFtp() {
		FTPClient ftpClient = new FTPClient();

		FTPClientConfig config = new FTPClientConfig(FTPClientConfig.SYST_NT);
		ftpClient.configure(config);

		String host = FtpUtil.getFTPHost();
		String user = FtpUtil.getFTPUser();
		String psd = FtpUtil.getFTPPsd();
		int port = FtpUtil.getFTPPort();
		try {
			ftpClient.connect(host, port);
			ftpClient.login(user, psd);// 登录ftp
			//System.out.println(ftpClient.getReplyString());
			ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
			ftpClient.setControlEncoding("GBK");
			ftpClient.enterLocalPassiveMode();
			linkState = "0";
			System.out.println(ftpClient.getReplyString());
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}// 连接ftp
		return ftpClient;
	}
	
	/**
	 *  登录到ftp
	 * @param host 主服务器
	 * @param user	用户名
	 * @param psd	密码
	 * @param host1 备用服务
	 * @param port 端口号
	 * @return
	 */
	public FTPClient getFtp(String host,String user,String psd,String host1,int port) {
		FTPClient ftpClient = new FTPClient();

		FTPClientConfig config = new FTPClientConfig(FTPClientConfig.SYST_NT);
		ftpClient.configure(config);

		try {
			ftpClient.connect(host, port);
			ftpClient.login(user, psd);// 登录ftp
			//System.out.println(ftpClient.getReplyString());
			ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
			ftpClient.setControlEncoding("GBK");
			ftpClient.enterLocalPassiveMode();
			linkState = "0";
			System.out.println("------------");
		} catch (Exception e) {
			try {
				ftpClient.connect(host1, port);
				ftpClient.login(user, psd);// 登录ftp
				ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
				ftpClient.setControlEncoding("GBK");
				ftpClient.enterLocalPassiveMode();
				linkState = "1";
				System.out.println("============");
			} catch (Exception e2) {
				e2.printStackTrace();
			}
			e.printStackTrace();
		}// 连接ftp
		return ftpClient;
	}
	
	public String getLinkState(){
		return linkState;
	}
	
	public static void main(String[] args) {
		LwFTPUploadTxt lft = new LwFTPUploadTxt();
		/*String host = "**************";
		String host1 = "127.0.0.1";
		String user = "root";
		String psd = "";
		int port = 21;*/
		String host = FtpUtil.getMainHost();
		String host1 = FtpUtil.getBackupHost();
		String user = FtpUtil.getDSJUser();
		String psd = FtpUtil.getDSJPsd();
		int port = FtpUtil.getDSJPort();
		FTPClient ftpClient = lft.getFtp(host, user, psd, host1, port);//登录
		File f = new File("C:/Users/<USER>/Desktop/1234.doc");
		String path = "";
		if(lft.getLinkState().equals("1")) {//备用主机
			path = FtpUtil.getBackupPath();
		} else {//主机
			path = FtpUtil.getMainPath();
		}
		lft.uploadTxt(f, path, ftpClient);//上传
	}

	/**
	 * 上传txt文件
	 * 
	 * @param file
	 *            上传的文件
	 * @param path
	 *            ftp目录
	 * @param ftpClient
	 */
	public boolean uploadTxt(File file, String path, FTPClient ftpClient) {
		boolean bl  = false;
		if(!path.endsWith("/")){//判读路径是否以“/”结尾
			path += "/";
		}
		try {
			if (file != null) {
				ftpClient.makeDirectory(path);
				ftpClient.changeWorkingDirectory(path);
				FileInputStream input = new FileInputStream(file);
				ftpClient.storeFile(file.getName(), input);
				input.close();
				bl=true;
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			bl=false;
		}
		return bl;
	}
	
	/**
	 * 上传txt文件
	 * 
	 * @param file
	 *            上传的文件
	 * @param path
	 *            ftp目录
	 * @param ftpClient
	 */
	public boolean uploadTxtTwo(File file, String path, FTPClient ftpClient,String datatime) {
		boolean bl  = false;
		if(!path.endsWith("/")){//判读路径是否以“/”结尾
			path += "/";
		}
		path+=datatime+"/";
		try {
			if (file != null) {
				ftpClient.makeDirectory(path);
				ftpClient.changeWorkingDirectory(path);
				FileInputStream input = new FileInputStream(file);
				ftpClient.storeFile(file.getName(), input);
				input.close();
				bl=true;
			}
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			bl=false;
		}
		return bl;
	}

	/**
	 * 从ftp下载文件文件
	 * @param ftpPath 
	 * @param ftpClient
	 * @param path
	 * @param fileName
	 * @return
	 */
	public String Down(String ftpPath, FTPClient ftpClient, String path,String fileName) {
		//TelnetInputStream is = null;
		String txtName = "";
		if(!ftpPath.endsWith("/")){//判读路径是否以“/”结尾
			ftpPath += "/";
		}
		try {
			ftpClient.makeDirectory(ftpPath);
			ftpClient.changeWorkingDirectory(ftpPath);//改变ftp工作目录
			txtName = lastTimeFile(ftpClient, fileName, ftpPath);//获取最新文件名称
			File file2 = null;
			if(path.endsWith("/")){//判读路径是否以“/”结尾
				file2 = new File(path+txtName);
			} else {
				file2 = new File(path+"/"+txtName);
			}
			if(file2 != null && !file2.exists()){
				file2.createNewFile();
			}
			OutputStream out = new FileOutputStream(file2);
			OutputStream os = new FileOutputStream(file2);
			ftpClient.retrieveFile(txtName, os);
			os.close();
//			byte[] b = new byte[1024];
//			int byteread = -1;
//			while ((byteread = (input.read(b))) != -1) {
//				out.write(b, 0, byteread);
//			}
//			out.flush();
//			input.close();
//			out.close();
		} catch (IOException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			txtName = "";
		}
		return txtName;
	}
	
	/**
	 * 获取修改时间最新的文件名
	 * @param list
	 * @return 
	 */
	public String lastTimeFile(FTPClient ftpClient,String fileName,String ftpPath){
		List<FTPFile> list = new ArrayList<FTPFile>();
		if(!ftpPath.endsWith("/")){//判读路径是否以“/”结尾
			ftpPath += "/";
		}
		//获取相关文件名的文件组
		try {
			ftpClient.makeDirectory(ftpPath);
			ftpClient.changeWorkingDirectory(ftpPath);//改变ftp工作目录
			ftpClient.configure(new FTPClientConfig()); 
			FTPFile[] files = ftpClient.listFiles();//遍历

			for (FTPFile file : files) {
				if (file.isFile()) {
					if (file.getName().indexOf(fileName) >= 0) {//匹配文件
						list.add(file);
					}
				}
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
		
		String name = "";
		if(list.size() > 0) {
			int[] a = new int[list.size()];
			List<Date> l = new ArrayList<Date>();
			for(int i = 0;i<list.size();i++){
				a[i] = i;
				Date date1 = list.get(i).getTimestamp().getTime();
				l.add(date1);
			}
			for(int i = 0;i<l.size()-1;i++){
				for(int j=i+1;j<l.size();j++){
			 		if(l.get(i).before(l.get(j))){
						int temp = a[i];
						a[i] = a[j];
						a[j] = temp;
						Collections.swap(l,i,j);
					}
				}
			}
			name = list.get(a[0]).getName();
		}
		
		return name;
	}

	/**
	 * 关闭ftp链接
	 * 
	 * @param ftpClient
	 */
	public void closeFtp(FTPClient ftpClient) {
		if (ftpClient != null && ftpClient.isConnected()) {
			try {
				ftpClient.logout();
				ftpClient.disconnect();
			} catch (IOException e) {
				e.printStackTrace();
			}
		}
	}
}
