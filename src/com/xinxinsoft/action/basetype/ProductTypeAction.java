package com.xinxinsoft.action.basetype;

import java.util.Date;
import java.util.List;
import java.util.Map;



import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.basetype.BusinessType;
import com.xinxinsoft.entity.basetype.ProductType;
import com.xinxinsoft.service.basetype.ProductTypeService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;


public class ProductTypeAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;

	private ProductType pd;

	private ProductTypeService service;

	public ProductTypeService getService() {
		return service;
	}

	public void setService(ProductTypeService service) {
		this.service = service;
	}

	/**
	 * 列表展示
	 */
	
	public PageResponse doList(PageRequest page) {

		return service.dolist(page, pd);
	}

	public ProductType getPd() {
		return pd;
	}

	public void setPd(ProductType pd) {
		this.pd = pd;
	}

	/**
	 * 查询出业务类型 产品类型数据并且跳转到新增页面 或者查询页面
	 * 
	 * @return
	 */
	public String tiaoAdd() {
		String id = getString("id");
		Integer state = getInteger("state");
		if (state == 1) {
			pd = service.queryProduectId(id);
			getRequest().setAttribute("pd", pd);
			return "query";

		} else {
			if (id != null && !"".equals(id)) {
				pd = service.queryProduectId(id);
				getRequest().setAttribute("pd", pd);
			}
			List<BusinessType> bu = service.queryList();
			getRequest().setAttribute("tbu", bu);

			return "Jump";
		}
	}

	/**
	 * 新增 或者修改实体
	 */
	public void addProductType() {
		try {

			pd.setCreationTime(new Date());
			if("".equals(pd.getProductId())){
				pd.setProductId(null);
			}
			
			service.save(pd);
			Write("1");
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 查询出所用已经提交的 产品类型 返回 json
	 */
	public void queryProduectjson() {
		List<Map<String, String>> bu = service.queryProduectjson();
		Write(JSONHelper.SerializeWithNeedAnnotation(bu));

	}
	/**
	 * 根据业务id查询产品类型集合
	 */
	public void queryProduectBid(){
		String id=getString("id");
		List<Map<String, String>> bu=service.queryProduectBid(id);
		Write(JSONHelper.SerializeWithNeedAnnotation(bu));

	}
	/**
	 * 根据业务id查询产品类型集合: 通用：
	 */
	public void queryProduectBidTY(){
		String id=getString("id");
		List<Map<String, String>> bu=service.queryProduectBidTY(id);
		Write(JSONHelper.SerializeWithNeedAnnotation(bu));

	}
	/**
	 * 根据业务名字查询产品类型集合
	 */
	public void queryProduectBName(){
		String name=getString("name");
		List<Map<String, String>> bu=service.queryProduectBName(name);
		Write(JSONHelper.SerializeWithNeedAnnotation(bu));
	}
	/**
	 * 假删除
	 */
	public void delete(){
		String id =getString("id");
		 pd=service.queryProduectId(id);
		if(pd!=null){
		 if(pd.getState()==0){
			 service.delete(id);
			 writeText("删除成功");

		 }else{
			 //如果是提交判断是否被引用
			 Integer i=service.quote(id);
			 if(i==0){
				 service.delete(id);
				 writeText("删除成功");
			 }else{
				 writeText("该业务被引用");
			 }
			 	
		  }
		}else{
		 	writeText("数据错误");

		}
	}
	
	/**
	 * 获取子产品:
	 */
	public void queryZProduectByPcode(){
		String pcode=getString("pcode");
		String otype=getString("otype");
		String zotype=getString("zotype");
		List<Map<String, String>> bu=service.queryZProduectByPcode(pcode,otype,zotype);
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(bu));
	}
	
	
}
