package com.xinxinsoft.action.basetype;

import com.xinxinsoft.utils.UrlConnection;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.dedicatedFlow.BossTache;
import com.xinxinsoft.service.basetype.BossTacheService;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import org.apache.commons.lang.StringUtils;

import java.net.URLDecoder;

/**
 * 
 * <AUTHOR>
 *
 */
public class BossTacheAction  extends BaseAction{
	private BossTache tache;
	private BossTacheService service;
	
	

	public BossTacheService getService() {
		return service;
	}

	public void setService(BossTacheService service) {
		this.service = service;
	}

	public BossTache getTache() {
		return tache;
	}

	public void setTache(BossTache tache) {
		this.tache = tache;
	}
	@Override
	public PageResponse doList(PageRequest page) {
		String actName=getString("actName");
		String productName=getString("productName");
		
		return service.doList(page,actName,productName);
	}
	/*
	 * 保存
	 */
	public void addBossTache(){
		try {
			String productId=getString("productId");
			
			//如果之前有关联中间表 先删除中间表管理
			//删除关联
			if(tache.getId()!=null){
			  service.delectlinkTache(tache.getId());
			}
			//保存实体
			service.save(tache);
			//新增中间表
			service.saveLinkTache(productId,tache.getId());
			writeText("1");
		} catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	//删除
	public void dele(){
		try {
			Integer id=getInteger("id");
			//删除关联
			service.delectlinkTache(id);
			//删除环节
			service.delect(id);
			writeText("删除成功");
		} catch (Exception e) {
			e.printStackTrace();
		}
		
		
	}
	//查询
	public void queryBossTache(){
		Integer id=getInteger("id");
		//System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormat(service.queryBossTache(id)));
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(service.queryBossTache(id)));
	}


	/**
	 * 根据产品查询元素
	 */
	public void queryElementCfg(){
		String pcode = this.getString("pcode");
		String ctype = this.getString("ctype");
		if(!StringUtils.isBlank(pcode) && !StringUtils.isBlank(ctype)){
			String  strjson = "{\"title\":\""+service.queryProcByName(pcode)+"\",\"list\":"+JSONHelper.SerializeWithNeedAnnotationDateFormat(service.queryElementCfg(pcode+"_"+ctype))+"}";
			Write(strjson);
		}else{
			Write("");
		}
	}

	/**
	 * 新增
	 */
	public void addElementCfg(){
	    try {
            String request_info = UrlConnection.getRequestData(getRequest());
			this.Write(service.addElementCfg(request_info));
        }catch (Exception e){
	    	e.printStackTrace();
			this.Write("-1");
        }
    }

    /**
     * 修改
     */
    public void updateElementCfg(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
            this.Write(service.updateElementCfg(request_info));
        }catch (Exception e){
            e.printStackTrace();
            this.Write("-1");
        }
    }

	/**
	 * 根据订单编号，和产品类型，查询相关信息
	 */
	public void querySeeElement(){
		String orderno= this.getString("order_no");
		String pcode = this.getString("pcode");
		if(!StringUtils.isBlank(pcode) && !StringUtils.isBlank(orderno) ){
			String  strjson = "{\"title\":\""+service.queryProcByName(pcode)+"\",\"list\":"+JSONHelper.SerializeWithNeedAnnotationDateFormat(service.querySeeElement(orderno))+"}";
			Write(strjson);
		}else{
			Write("");
		}
	}
}
