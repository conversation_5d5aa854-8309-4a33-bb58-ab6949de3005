package com.xinxinsoft.action.basetype;

import java.util.Date;
import java.util.List;
import java.util.Map;

import com.xinxinsoft.action.BaseAction;

import com.xinxinsoft.entity.basetype.BusinessType;
import com.xinxinsoft.service.basetype.BusinessTypeService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.common.DefaultAction;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

/**
*   业务实例 action
* <AUTHOR>
* @Date    2016-8-30
* @version 1.00
*/

public class BusinessTypeAction extends BaseAction {
	private BusinessType bu; 
	
	
	
	
	
	
	
	
	public BusinessType getBu() {
		return bu;
	}

	public void setBu(BusinessType bu) {
		this.bu = bu;
	}

	private BusinessTypeService businessTypeService;

	public BusinessTypeService getBusinessTypeService() {
		return businessTypeService;
	}

	public void setBusinessTypeService(BusinessTypeService businessTypeService) {
		this.businessTypeService = businessTypeService;
	}
	/**
	*  业务添加或者保存
	* @param 
	* @return 返回 1 保存成功   2 保存失败
	* @throws 
	*/

	public void  addbusiness(){
		Integer state=getInteger("state");
		if(bu.getBusinessName()!=null&&!"".equals(bu.getBusinessName())){
			if(bu.getBusinessId()==null||"".equals(bu.getBusinessId())){
				bu.setCreationTime(new Date());				
			}
			bu.setState(state);
			if("".equals(bu.getBusinessId())){
				bu.setBusinessId(null);
			}
			
			
			businessTypeService.add(bu);
			Write("1");
		}else{
			writeText("2");
		}
	}
	//列表展示
	@Override
	public PageResponse doList(PageRequest page) {
		String name=getString("name");
		
		return businessTypeService.dolist(name,page);
	}
	/**
	 * 跳转到编辑 或者查看页面
	 * @return
	 */
	public String saveorquery(){
		String id=getString("id");
		Integer state=getInteger("state");
		BusinessType bu=businessTypeService.query(id);
		getRequest().setAttribute("bu", bu);
		if(state==1){
			return "query";
		}else if(state==0){
			return "add";

		}else {
			return "error";
		}
		
	}

	
	
	
	/**
	 * 查询所有业务类型的名称 和id
	 */
	public void  querybu(){
		List<Map<String, String>> bu=businessTypeService.queryList();
		Write(JSONHelper.SerializeWithNeedAnnotation(bu));
	}
	
	
	/**
	 * 查询专用的业务类型的名称 和id
	 */
	public void  querybuDI(){
		List<Map<String, String>> bu=businessTypeService.querybuDI();
		Write(JSONHelper.SerializeWithNeedAnnotation(bu));
	}
	/**
	 * 假删除
	 */
	public void delete(){
		String id =getString("id");
		BusinessType bu=businessTypeService.query(id);
		if(bu!=null){
		 if(bu.getState()==0){
			 businessTypeService.delete(id);
			 writeText("删除成功");

		 }else{
			 //如果是提交判断是否被引用
			 Integer i=businessTypeService.quote(id);
			 if(i==0){
				 businessTypeService.delete(id);
				 writeText("删除成功");
			 }else{
				 writeText("该业务被引用");
			 }
			 	
		  }
		}else{
		 	writeText("数据错误");

		}
		
	}
	
}
