package com.xinxinsoft.action.transfer;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLEncoder;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.ResourceBundle;
import java.util.Set;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.xinxinsoft.action.test.JbpmTest;
import com.xinxinsoft.entity.PreinvApply.PreinvApply;
import com.xinxinsoft.entity.claimForFunds.LateFeeMoneyData;
import com.xinxinsoft.entity.transfer.*;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.sendComms.transFerinterface.TransFerInformationSrv;
import com.xinxinsoft.sendComms.unitService.GroupInfoSrv;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.LateFeeMoneyDataService;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.http.client.methods.HttpPost;
import org.apache.log4j.Logger;
import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.OrderForm;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.ordertask.OrderTask;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.transfer.TransferInformationTwoService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

/**
 * 转账管理action
 *
 * <AUTHOR>
 */
public class TransferInformationTwoAction extends BaseAction {
    private static final Logger logger = Logger.getLogger(TransferInformationTwoAction.class);
    private ResourceBundle s = ResourceBundle.getBundle("WebService-config");
    private String audit = s.getString("AUDIT_INTERS_FJ_SWITCH");
    private TransferInformationTwoService tInformationService;
    private WaitTaskService service;                // 代办
    private SystemUserService systemUserService;
    private TransferJBPMUtils transferJBPMUtils;
    private LateFeeMoneyDataService lateFeeMoneyDataService;
    private JbpmUtil jbpmUtil;
    private File file1;
    private File branchFile1;
    private String branchFile1FileName;

    private File customerLetterFile;
    private String customerLetterFileFileName;


    private File systemFileNameFile;
    private String systemFileNameFileFileName;


    private File productsFile1;
    private String productsFile1FileName;


    private AttachmentService attachmentService;
    private Bpms_riskoff_service taskService;

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public TransferInformationTwoService gettInformationService() {
        return tInformationService;
    }

    public void settInformationService(
            TransferInformationTwoService tInformationService) {
        this.tInformationService = tInformationService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public TransferJBPMUtils getTransferJBPMUtils() {
        return transferJBPMUtils;
    }

    public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
        this.transferJBPMUtils = transferJBPMUtils;
    }

    public JbpmUtil getJbpmUtil() {
        return jbpmUtil;
    }

    public void setJbpmUtil(JbpmUtil jbpmUtil) {
        this.jbpmUtil = jbpmUtil;
    }

    public File getBranchFile1() {
        return branchFile1;
    }

    public void setBranchFile1(File branchFile1) {
        this.branchFile1 = branchFile1;
    }

    public String getBranchFile1FileName() {
        return branchFile1FileName;
    }

    public void setBranchFile1FileName(String branchFile1FileName) {
        this.branchFile1FileName = branchFile1FileName;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public File getCustomerLetterFile() {
        return customerLetterFile;
    }

    public String getCustomerLetterFileFileName() {
        return customerLetterFileFileName;
    }

    public File getSystemFileNameFile() {
        return systemFileNameFile;
    }

    public String getSystemFileNameFileFileName() {
        return systemFileNameFileFileName;
    }

    public File getProductsFile1() {
        return productsFile1;
    }

    public String getProductsFile1FileName() {
        return productsFile1FileName;
    }

    public void setCustomerLetterFile(File customerLetterFile) {
        this.customerLetterFile = customerLetterFile;
    }

    public void setCustomerLetterFileFileName(String customerLetterFileFileName) {
        this.customerLetterFileFileName = customerLetterFileFileName;
    }

    public void setSystemFileNameFile(File systemFileNameFile) {
        this.systemFileNameFile = systemFileNameFile;
    }

    public void setSystemFileNameFileFileName(String systemFileNameFileFileName) {
        this.systemFileNameFileFileName = systemFileNameFileFileName;
    }

    public void setProductsFile1(File productsFile1) {
        this.productsFile1 = productsFile1;
    }

    public void setProductsFile1FileName(String productsFile1FileName) {
        this.productsFile1FileName = productsFile1FileName;
    }

    public LateFeeMoneyDataService getLateFeeMoneyDataService() {
        return lateFeeMoneyDataService;
    }

    public void setLateFeeMoneyDataService(LateFeeMoneyDataService lateFeeMoneyDataService) {
        this.lateFeeMoneyDataService = lateFeeMoneyDataService;
    }

    public void uploadFile() {
        try {
            //根据当天日期生成文件夹：名称：
            String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
            String ftpUrl = FileUpload.getFtpURL() + urlDate;
            File headPath = new File(ftpUrl);//获取文件夹路径
            if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                headPath.mkdirs();
            }
            Map<String, String> map = new HashMap<String, String>();
            if (branchFile1 != null) {
                Long time = System.currentTimeMillis();
                String pixstr = FileUpload.getFilePix(branchFile1FileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }
                if (FileUpload.upload(ftpUrl, branchFile1, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(branchFile1FileName);
                    attachmentEntity.setUploadUser((SystemUser) this.getRequest().getSession()
                            .getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser()));
                    String attachmentId = this.attachmentService.addEntity(attachmentEntity);
                    ///审计接口调用
                    if ("start".equals(audit)) {
                        final String request = DateUtil.getIpAddr(this.getRequest());
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                ///审计接口调用
                                CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                            }
                        }).start();
                    }
                    map.put("branchFile1", attachmentId);
                } else {
                    writeText("0");
                }
            } else {
                map.put("branchFile1", "");
            }
            if (customerLetterFile != null) {
                Long time = System.currentTimeMillis();
                String pixstr = FileUpload.getFilePix(customerLetterFileFileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }
                if (FileUpload.upload(ftpUrl, customerLetterFile, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(customerLetterFileFileName);
                    attachmentEntity.setUploadUser((SystemUser) this.getRequest().getSession()
                            .getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser()));
                    String attachmentId = this.attachmentService.addEntity(attachmentEntity);
                    ///审计接口调用
                    if ("start".equals(audit)) {
                        final String request = DateUtil.getIpAddr(this.getRequest());
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                ///审计接口调用
                                CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                            }
                        }).start();
                    }
                    map.put("customerLetterFile", attachmentId);
                } else {
                    writeText("0");
                }
            } else {
                map.put("customerLetterFile", "");
            }
            if (systemFileNameFile != null) {
                Long time = System.currentTimeMillis();
                String pixstr = FileUpload.getFilePix(systemFileNameFileFileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }
                if (FileUpload.upload(ftpUrl, systemFileNameFile, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(systemFileNameFileFileName);
                    attachmentEntity.setUploadUser((SystemUser) this.getRequest().getSession()
                            .getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser()));
                    String attachmentId = this.attachmentService.addEntity(attachmentEntity);
                    ///审计接口调用
                    if ("start".equals(audit)) {
                        final String request = DateUtil.getIpAddr(this.getRequest());
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                ///审计接口调用
                                CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                            }
                        }).start();
                    }
                    map.put("systemFileNameFile", attachmentId);
                } else {
                    writeText("0");
                }
            } else {
                map.put("systemFileNameFile", "");
            }
            if (productsFile1 != null) {
                Long time = System.currentTimeMillis();
                String pixstr = FileUpload.getFilePix(productsFile1FileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }
                if (FileUpload.upload(ftpUrl, productsFile1, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(productsFile1FileName);
                    attachmentEntity.setUploadUser((SystemUser) this.getRequest().getSession()
                            .getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser()));
                    String attachmentId = this.attachmentService.addEntity(attachmentEntity);
                    ///审计接口调用
                    if ("start".equals(audit)) {
                        final String request = DateUtil.getIpAddr(this.getRequest());
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                ///审计接口调用
                                CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                            }
                        }).start();
                    }
                    map.put("productsFile1", attachmentId);
                } else {
                    writeText("0");
                }
            } else {
                map.put("productsFile1", "");
            }
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        } catch (Exception e) {
            e.printStackTrace();
            writeText("0");
        }
    }

    /**
     * 查询四个附件
     */
    public void queryAttachment1() {
        String json = this.getString("json");
        JSONObject data = JSONObject.fromObject(json);
        Map<String, String> map = new HashMap<String, String>();
        if (!"".equals(data.getString("branchFile1"))) {
            Attachment attachment = tInformationService.getAttachment(data.getString("branchFile1"));
            map.put("branchFile1", attachment.getRealName());
        } else {
            map.put("branchFile1", "");
        }
        if (!"".equals(data.getString("customerLetterFile"))) {
            Attachment attachment = tInformationService.getAttachment(data.getString("customerLetterFile"));
            map.put("customerLetterFile", attachment.getRealName());
        } else {
            map.put("branchFile1", "");
        }
        if (!"".equals(data.getString("systemFileNameFile"))) {
            Attachment attachment = tInformationService.getAttachment(data.getString("systemFileNameFile"));
            map.put("systemFileNameFile", attachment.getRealName());
        } else {
            map.put("branchFile1", "");
        }
        if (!"".equals(data.getString("productsFile1"))) {
            Attachment attachment = tInformationService.getAttachment(data.getString("productsFile1"));
            map.put("productsFile1", attachment.getRealName());
        } else {
            map.put("branchFile1", "");
        }
        Write(JSONHelper.SerializeWithNeedAnnotation(map));
    }

    public void getContracNo() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String turnAccountNumber = getString("turnAccountNumber");//转出账户
            String transferAccountNumber = getString("transferAccountNumber");//转入账户
            String json = tInformationService.getContracNo(page, turnAccountNumber, transferAccountNumber);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 转账首页列表
     */
    public void findByPage() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String intoGroup = getString("intoGroup");
            String intoGroupName = getString("intoGroupName");
            String outGroup = getString("outGroup");
            String outGroupName = getString("outGroupName");
            String TtranferNumber = getString("TtranferNumber");
            Integer state = getInteger("state"); // 状态
            String selectcon = getString("selectcon"); // 状态
            Integer statetwo = getInteger("statetwo");
            String startTime = getString("startTime");// 申请开始时间
            String endTime = getString("endTime");// 申请结束时间
            //logger.info("运行帆帆发" + state);
            String phone = getString("phone");
            if (phone == null || phone.equals("null") || phone.equals("")) {
                user = this.user;
                page = tInformationService.getTranferPageTwo(page, intoGroup, intoGroupName, outGroup, outGroupName, state, user, TtranferNumber, selectcon, startTime, endTime, statetwo);

            } else {
                user = systemUserService.getUserByPhone(phone);
                page = tInformationService.getTranferPageApp(page, intoGroup, intoGroupName, outGroup, outGroupName, state, user, TtranferNumber, selectcon, startTime, endTime, statetwo);
            }
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }


    public File getFile1() {
        return file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }

    /**
     * 查询已审批转账信息
     */
    public void queryTransferListByPage() {
        try {
            PageRequest page = new PageRequest(getRequest());
            String intoGroup = getString("intoGroup");
            String outGroup = getString("outGroup");
            String TtranferNumber = getString("TtranferNumber");
            Integer state = getInteger("state");
            PageResponse response = tInformationService.getQueryTransferListByPage(intoGroup, outGroup, state, page, user, TtranferNumber);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }


    /**
     * 导入账户模板生成账本
     */
    public void importtransferInformation() {
        try {
            ExcelUtilHSSFWorkbook excelReader = new ExcelUtilHSSFWorkbook(file1);
            InputStream is = new FileInputStream(file1);
            Workbook wb = new HSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            System.out.println("这是列数：" + column);
            //对读取Excel表格内容测试
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContentTwo();
            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();

            if (column == 8) {
                for (int i = 1; i <= map.size(); i++) {
                    Map<String, Object> maps = new HashMap<String, Object>();
                    maps.put("turnAccountNumber", map.get(i).get(0));//转出账户号码
                    maps.put("turnAccountType", map.get(i).get(1));//转出账户类型
                    maps.put("transferAccountNumber", map.get(i).get(2));//转入账户号码
                    maps.put("transferAccounType", map.get(i).get(3));//转入账户类型
                    if (map.get(i).get(4).toString().matches("^[0-9]{11}$")) {
                        maps.put("transferAccountPhone", map.get(i).get(4));//转入手机号码
                    } else {
                        maps.put("transferAccountPhone", "");//转入手机号码
                    }
                    if (map.get(i).get(5).toString().matches("^[0-9]{11}$")) {
                        maps.put("tranAccountPhone", map.get(i).get(5));//转出手机号码
                    } else {
                        maps.put("tranAccountPhone", "");//转出手机号码
                    }
                    maps.put("transferAccounName", map.get(i).get(6));//转入用户名称
                    maps.put("transferAccountsType", map.get(i).get(7));//转账类型
                    String zxFlag = "";
                    if (map.get(i).get(7).toString().equals("普通转账")) {
                        zxFlag = "0";
                    } else if (map.get(i).get(7).toString().equals("专线转账")) {
                        zxFlag = "1";
                    }
                    //获取账本
                    String json = sQConPreInfo(map.get(i).get(0).toString(), zxFlag);
                    if (!json.equals("NO")) {
                        JSONObject jb = JSONObject.fromObject(json);
                        maps.put("outMsg", jb.getString("OUT_DATA"));//账本信息
                        JSONObject outData = JSONObject.fromObject(jb.getString("OUT_DATA"));
                        maps.put("CUR_BALANCE", outData.getString("CUR_BALANCE"));//账户总余额
                    }
                    list.add(maps);
                }
                tInformationService.TranferPageCsvTwo(list);
            } else {
                for (int i = 1; i <= map.size(); i++) {
                    Map<String, Object> maps = new HashMap<String, Object>();
                    maps.put("turnAccountNumber", map.get(i).get(0));//转出账户号码
                    maps.put("turnAccountType", map.get(i).get(1));//转出账户类型
                    maps.put("transferAccountNumber", map.get(i).get(2));//转入账户号码
                    maps.put("transferAccounType", map.get(i).get(3));//转入账户类型
                    if (map.get(i).get(4).toString().matches("^[0-9]{11}$")) {
                        maps.put("transferAccountPhone", map.get(i).get(4));//转入手机号码
                    } else {
                        maps.put("transferAccountPhone", "");//转入手机号码
                    }
                    if (map.get(i).get(5).toString().matches("^[0-9]{11}$")) {
                        maps.put("tranAccountPhone", map.get(i).get(5));//转出手机号码
                    } else {
                        maps.put("tranAccountPhone", "");//转出手机号码
                    }
                    maps.put("transferAccountsType", map.get(i).get(6));//转账类型

                    String zxFlag = "";
                    if (map.get(i).get(6).toString().equals("普通转账")) {
                        zxFlag = "0";
                    } else if (map.get(i).get(6).toString().equals("专线转账")) {
                        zxFlag = "1";
                    }
                    //获取账本
                    String json = sQConPreInfo(map.get(i).get(0).toString(), zxFlag);
                    if (!json.equals("NO")) {
                        JSONObject jb = JSONObject.fromObject(json);
                        maps.put("outMsg", jb.getString("OUT_DATA"));//账本信息
                        JSONObject outData = JSONObject.fromObject(jb.getString("OUT_DATA"));
                        maps.put("CUR_BALANCE", outData.getString("CUR_BALANCE"));//账户总余额
                    }
                    list.add(maps);
                }
                tInformationService.TranferPageCsv(list);
            }
        } catch (Exception e) {
            logger.info("导入账户模板生成账本错误:" + e);
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 导入账本
     */
    public void importtransferInformationThree() {
        try {
            ExcelUtilHSSFWorkbook excelReader = new ExcelUtilHSSFWorkbook(file1);
            InputStream is = new FileInputStream(file1);
            Workbook wb = new HSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            System.out.println("这是列数：" + column);
            //对读取Excel表格内容测试
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContentTwo();
            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
            if (column == 13) {
                Map<String, Object> maps = null;
                JSONObject OUTMSG = null;
                JSONArray jsonArray = null;
                String CUR_BALANCE = "";
                for (int i = 1; i <= map.size(); i++) {
                    if (!map.get(i).get(0).toString().equals("") && i != 1) {
                        OUTMSG.put("OUTMSG", jsonArray);
                        maps.put("outData", OUTMSG);
                        list.add(maps);
                    }
                    if (!map.get(i).get(0).toString().equals("")) {
                        OUTMSG = new JSONObject();
                        maps = new HashMap<String, Object>();
                        jsonArray = new JSONArray();
                        maps.put("turnAccountNumber", map.get(i).get(0));//转出账户号码
                        maps.put("turnAccountType", map.get(i).get(1));//转出账户类型
                        maps.put("transferAccountNumber", map.get(i).get(2));//转入账户号码
                        maps.put("transferAccounType", map.get(i).get(3));//转入账户类型
                        if (map.get(i).get(4).toString().matches("^[0-9]{11}$")) {
                            maps.put("transferAccountPhone", map.get(i).get(4));//转入手机号码
                        } else {
                            maps.put("transferAccountPhone", "");//转入手机号码
                        }
                        if (map.get(i).get(5).toString().matches("^[0-9]{11}$")) {
                            maps.put("tranAccountPhone", map.get(i).get(5));//转出手机号码
                        } else {
                            maps.put("tranAccountPhone", "");//转出手机号码
                        }
                        maps.put("transferAccounName", map.get(i).get(6));//转入用户名
                        maps.put("transferAccountsType", map.get(i).get(7));//转账类型
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("PAY_TYPE", map.get(i).get(8)); //预存类型
                        jsonObject.put("PAY_NAME", map.get(i).get(9));//预存类型名称
                        jsonObject.put("PREPAY_FEE", map.get(i).get(10));//可转金额
                        jsonObject.put("AMOUNT", map.get(i).get(11));//申请金额
                        CUR_BALANCE = map.get(i).get(12).toString();
                        jsonObject.put("CONTRACTNO_BALANCE", CUR_BALANCE);//申请金额
                        //OUTMSG.put("CUR_BALANCE", map.get(i).get(11));//账户总余额
                        jsonArray.add(jsonObject);
                    }
                    if (map.get(i).get(0).toString().equals("")) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("PAY_TYPE", map.get(i).get(8)); //预存类型
                        jsonObject.put("PAY_NAME", map.get(i).get(9));//预存类型名称
                        jsonObject.put("PREPAY_FEE", map.get(i).get(10));//可转金额
                        jsonObject.put("AMOUNT", map.get(i).get(11));//申请金额
                        jsonObject.put("CONTRACTNO_BALANCE", CUR_BALANCE);//申请金额
                        jsonArray.add(jsonObject);
                    }
                    if (i == map.size()) {
                        OUTMSG.put("OUTMSG", jsonArray);
                        maps.put("outData", OUTMSG);
                        list.add(maps);
                    }
                }
            } else {
                Map<String, Object> maps = null;
                JSONObject OUTMSG = null;
                JSONArray jsonArray = null;
                String CUR_BALANCE = "";
                for (int i = 1; i <= map.size(); i++) {
                    if (!map.get(i).get(0).toString().equals("") && i != 1) {
                        OUTMSG.put("OUTMSG", jsonArray);
                        maps.put("outData", OUTMSG);
                        list.add(maps);
                    }
                    if (!map.get(i).get(0).toString().equals("")) {
                        OUTMSG = new JSONObject();
                        maps = new HashMap<String, Object>();
                        jsonArray = new JSONArray();
                        maps.put("turnAccountNumber", map.get(i).get(0));//转出账户号码
                        maps.put("turnAccountType", map.get(i).get(1));//转出账户类型
                        maps.put("transferAccountNumber", map.get(i).get(2));//转入账户号码
                        maps.put("transferAccounType", map.get(i).get(3));//转入账户类型
                        if (map.get(i).get(4).toString().matches("^[0-9]{11}$")) {
                            maps.put("transferAccountPhone", map.get(i).get(4));//转入手机号码
                        } else {
                            maps.put("transferAccountPhone", "");//转入手机号码
                        }
                        if (map.get(i).get(5).toString().matches("^[0-9]{11}$")) {
                            maps.put("tranAccountPhone", map.get(i).get(5));//转出手机号码
                        } else {
                            maps.put("tranAccountPhone", "");//转出手机号码
                        }
                        maps.put("transferAccountsType", map.get(i).get(6));//转账类型
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("PAY_TYPE", map.get(i).get(7)); //预存类型
                        jsonObject.put("PAY_NAME", map.get(i).get(8));//预存类型名称
                        jsonObject.put("PREPAY_FEE", map.get(i).get(9));//可转金额
                        jsonObject.put("AMOUNT", map.get(i).get(10));//申请金额
                        CUR_BALANCE = map.get(i).get(11).toString();
                        jsonObject.put("CONTRACTNO_BALANCE", CUR_BALANCE);//申请金额
                        //OUTMSG.put("CUR_BALANCE", map.get(i).get(11));//账户总余额
                        jsonArray.add(jsonObject);
                    }
                    if (map.get(i).get(0).toString().equals("")) {
                        JSONObject jsonObject = new JSONObject();
                        jsonObject.put("PAY_TYPE", map.get(i).get(7)); //预存类型
                        jsonObject.put("PAY_NAME", map.get(i).get(8));//预存类型名称
                        jsonObject.put("PREPAY_FEE", map.get(i).get(9));//可转金额
                        jsonObject.put("AMOUNT", map.get(i).get(10));//申请金额
                        jsonObject.put("CONTRACTNO_BALANCE", CUR_BALANCE);//申请金额
                        jsonArray.add(jsonObject);
                    }
                    if (i == map.size()) {
                        OUTMSG.put("OUTMSG", jsonArray);
                        maps.put("outData", OUTMSG);
                        list.add(maps);
                    }
                }
            }
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(list);
            if (map.size() > 0) {
                Write(json);
            } else {
                Write("NULL");
            }
        } catch (Exception e) {
            logger.info("导入账本错误:" + e);
            e.printStackTrace();
            Write("NO");
        }
    }

    public void jurisdictionfindByPage() {
        try {
            PageRequest page = new PageRequest(getRequest());
            String intoGroup = getString("intoGroup");
            String intoGroupName = getString("intoGroupName");
            String outGroup = getString("outGroup");
            String outGroupName = getString("outGroupName");
            String rolename = getString("rolename");
            String TtranferNumber = getString("TtranferNumber");
            String deptStr = getString("deptStr");
            PageResponse response = tInformationService.jurisdictionfindByPage(intoGroup, intoGroupName, outGroup, outGroupName, rolename, deptStr, page, user, TtranferNumber);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 根据id获取相关转账信息
     */
    public void findById() {
        try {
            String id = getString("id");
            Map<String, Object> map = tInformationService.findByRowNo(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(map));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 根据登录人信息返回数据
     */
    public void findByUser() {
        try {
            String tableName = "";
            Map<String, Object> map = new HashMap<String, Object>();
            // 获取当前用户
            SystemUser user = this.user;
            // 获取用户权限
            List list = tInformationService.findByRowNo(user.getRowNo());
            String flag = "N";
            // 获取公司编码
            List companyCode = tInformationService.findCodeByRowNo(user.getRowNo());
            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("4076")) {
                    flag = "Y";
                    break;
                }
            }
            if (flag.equals("Y")) {
                if ((companyCode.get(0).toString()).equals("00")) {
                    // 查询所有分公司以及所属区县公司信息
                    List<Map<String, String>> listBranchOffice = tInformationService.findAllToBranchOffice();
                    Set<String> set = new HashSet<>();
                    // 获取所有分公司
                    for (int i = 0; i < listBranchOffice.size(); i++) {
                        String branchoffice = listBranchOffice.get(i).get("BRANCHOFFICE");
                        set.add(branchoffice);
                    }
                    // 获取分公司所对应的区县公司
                    List<Map<String, Object>> listBranchOfficeAndCountry = new ArrayList<>();
                    for (String string : set) {
                        Map<String, Object> mapAll = new HashMap<>();
                        mapAll.put("branchoffice", string);
                        List<String> countrys = new ArrayList<>();
                        for (int i = 0; i < listBranchOffice.size(); i++) {
                            String branchoffice = listBranchOffice.get(i).get("BRANCHOFFICE");
                            if (branchoffice.equals(string)) {
                                countrys.add(listBranchOffice.get(i).get("COUNTRY"));
                            }
                        }
                        mapAll.put("country", countrys);
                        listBranchOfficeAndCountry.add(mapAll);
                    }
                    map.put("level", "0");
                    map.put("flag", flag);
                    map.put("all", listBranchOfficeAndCountry);
                } else {
                    // 获取公司名称
                    String companyName = tInformationService.findCompanyName((companyCode.get(0).toString()));
                    // 根据公司编号查询部门名称
                    List<SystemDept> listCountry = tInformationService.findAllByBranchOffice(companyName);
                    map.put("level", "1");
                    map.put("flag", flag);
                    map.put("country", listCountry);
                }
            } else {
                map.put("flag", flag);
            }
            Write(JSONHelper.Serialize(map));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 导出转账信息
     */
    public void exportExcel() {
        try {
            List<Map<String, Object>> listMap = tInformationService.findAll();
            tInformationService.exportExcelToJxl(listMap);// List<Map<String,//
            // Object>> mapList

        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 新增转账信息
     */
    public void add() {
        try {
            String role = getString("role");
            String id = getString("id");
            String userid = getString("userID");
            String waitId = getString("waitId");
            String attachmentId = getString("attachmentId");
            String copyo = getString("copyo");
            int retrieve = getInteger("retrieve");
            int competitiveDemand = getInteger("competitiveDemand");
            int transFerType = getInteger("transFerType");
            String expTime = getString("expTime");
            String jsonone = getString("json");
            String lateFeeMoney = getString("lateFeeMoney");

            String fileTypeOne = getString("fileTypeOne");
            String fileTypeTwo = getString("fileTypeTwo");

            String attachmentOne = getString("attachmentOne");
            String fileNameOne = getString("fileNameOne");

            String attachmentTwo = getString("attachmentTwo");
            String fileNameTwo = getString("fileNameTwo");

            String attachmentThree = getString("attachmentThree");

            String attachmentFour = getString("attachmentFour");

            String attachmentFive = getString("attachmentFive");

            String type = getString("type");

            if ("TJCG".equals(type)) {//作废草稿单
                TransferInformation trInformation = tInformationService.getTransferInformation(id);
                trInformation.setState(-1);
                trInformation.setUpdateTime(new Date());
                tInformationService.update(trInformation);
            } else {
                WaitTask wait = service.queryWaitByTaskId(waitId);
                if (!"copy".equals(copyo)) {
                    if (wait != null) {
                        System.out.println("================开始代办================");
                        service.updateWait(wait, this.getRequest());
                        System.out.println("================结束代办================");
                    }
                }
            }

            String shiqu = "";
            String quxian = "";
            String IBM = "";
            List<Object[]> sone = tInformationService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                quxian = (String) sone.get(i)[0];
                shiqu = (String) sone.get(i)[1];
                IBM = (String) sone.get(i)[2];
            }
            TransferInformation transferInformation = new TransferInformation();
            transferInformation.setBranchOffice(shiqu);// 分公司
            transferInformation.setCountry(quxian);// 区县
            transferInformation.setCustomerManager(user.getRowNo() + "");
            String sateTime = getStringDatetwo(new Date());
            transferInformation.setTransferNumber(IBM + "" + sateTime);
            transferInformation.setCompetitiveDemand(competitiveDemand);
            transferInformation.setExpTime(expTime);
            transferInformation.setLateFeeMoney(lateFeeMoney);
            if (getString("transferTitle") != null && !"".equals("transferTitle")) {
                transferInformation.setTransferTitle(getString("transferTitle"));// 转账标题
            }
            if (getString("outMoney") != null && !"".equals("outMoney")) {
                transferInformation.setOutMoney(getString("outMoney"));// 转账金额
            }
            if (getString("transferInstructions") != null && !"".equals("transferInstructions")) {
                transferInformation.setTransferInstructions(getString("transferInstructions"));// 转账说明
            }
            transferInformation.setProposer(user.getEmployeeName());// 申请人
            transferInformation.setApplyTime(new Date());// 申请时间
            String GroupCode = "";//集团编码
            if (retrieve == 0) {
                if (getString("intoGroup") != null && !"".equals(getString("intoGroup"))) {
                    transferInformation.setIntoGroup(getString("intoGroup"));// 转入集团280
                    GroupCode = getString("intoGroup");
                }
                if (getString("intoGroupName") != null && !"".equals(getString("intoGroupName"))) {
                    transferInformation.setIntoGroupName(getString("intoGroupName"));// 转入集团名称
                }
                if (getString("outGroup") != null && !"".equals(getString("outGroup"))) {
                    transferInformation.setOutGroup(getString("outGroup"));// 转出集团280
                }
                if (getString("outGroupName") != null && !"".equals(getString("outGroupName"))) {
                    transferInformation.setOutGroupName(getString("outGroupName"));// 转出集团名称
                }
            } else if (retrieve == 2) {
                if (getString("group") != null && !"".equals(getString("group"))) {
                    transferInformation.setOutGroup(getString("group"));// 转出集团280
                    GroupCode = getString("group");
                }
                if (getString("groupName") != null && !"".equals(getString("groupName"))) {
                    transferInformation.setOutGroupName(getString("groupName"));// 转出集团名称
                }
            } else {
                if (getString("group") != null && !"".equals(getString("group"))) {
                    transferInformation.setIntoGroup(getString("group"));// 转入集团280
                    transferInformation.setOutGroup(getString("group"));// 转出集团280
                    GroupCode = getString("group");
                }
                if (getString("groupName") != null && !"".equals(getString("groupName"))) {
                    transferInformation.setIntoGroupName(getString("groupName"));// 转入集团名称
                    transferInformation.setOutGroupName(getString("groupName"));// 转出集团名称
                }
            }
            if (retrieve == 2) {
                transferInformation.setAcrossGroup(0);// 是否跨集团
            } else if (retrieve == 1) {
                transferInformation.setAcrossGroup(1);// 是否跨集团
            } else {
                transferInformation.setAcrossGroup(0);// 是否跨集团
            }

            if ("CG".equals(type)) {
                transferInformation.setState(-2);// 草稿
            } else {
                transferInformation.setState(2);// 修改状态为审批中
            }
            transferInformation.setVersionNumber("1");
            transferInformation.setUpdateTime(new Date());// 更新时间
            transferInformation.setTransferType(transFerType + "");
            transferInformation.setHANDLER_ID(userid);
            TransferInformation transferInformationtwo = tInformationService.add(transferInformation);
            JSONArray jsonObject1 = JSONArray.fromObject(jsonone);
            if (transFerType == 2) {
                for (int i = 0; i < jsonObject1.size(); i++) {
                    String s = jsonObject1.getString(i);
                    JSONObject data2 = JSONObject.fromObject(s);
                    TransferAccountInformation transferAccountInformation = new TransferAccountInformation();
                    transferAccountInformation.setTransferNumber(transferInformationtwo.getTransferNumber());
                    transferAccountInformation.setTransferAccountNumber(data2.getString("transferAccountNumber"));//转入账户号码
                    transferAccountInformation.setTransferAccounType(data2.getString("transferAccounType"));
                    transferAccountInformation.setTurnAccountNumber(data2.getString("turnAccountNumber"));//转出账户号码
                    transferAccountInformation.setTurnAccountType(data2.getString("turnAccountType"));
                    transferAccountInformation.setTransferAccountPhone(data2.getString("transferAccountPhone"));

                    //测试用假数据
//                    transferAccountInformation.setB_library("0");//否
//                    transferAccountInformation.setGroupCheck("0");//否

                    //B库验真
                    List<Map<String, String>> list = tInformationService.getAllPayDesignLimit(getString("intoGroup"), data2.getString("transferAccountPhone"));
                    if (list.size() <= 0) {
                        transferAccountInformation.setB_library("1");//否
                    } else {
                        transferAccountInformation.setB_library("0");//是
                    }

                    //校验是否本集团通讯录成员
                    if (GroupInfoSrv.getInstance().QryCustInfoByPersonPhone(data2.getString("transferAccountPhone"), getString("intoGroup"))) {
                        transferAccountInformation.setGroupCheck("0");//是
                    } else {
                        transferAccountInformation.setGroupCheck("1");//否
                    }

                    transferAccountInformation.setTransferAccounName(data2.getString("transferAccounName"));
                    transferAccountInformation.setTranAccountPhone(data2.getString("tranAccountPhone"));
                    transferAccountInformation.setAmountOfMoney(data2.getString("amountOfMoney"));
                    if (!"".equals(data2.getString("lateFee")) && !"undefined".equals(data2.getString("lateFee"))
                            && !"null".equals(data2.getString("lateFee")) && data2.getString("lateFee") != null) {
                        transferAccountInformation.setLateFee(data2.getString("lateFee"));
                        if (!"".equals(data2.getString("lateFeeMoney")) && !"undefined".equals(data2.getString("lateFeeMoney"))
                                && !"null".equals(data2.getString("lateFeeMoney")) && data2.getString("lateFeeMoney") != null) {
                            transferAccountInformation.setLateFeeMoney(data2.getString("lateFeeMoney"));
                        }
                    }
                    transferAccountInformation.setAccountNumber(transferInformationtwo.getTransferNumber() + i);
                    if (data2.getString("transferAccountsType").equals("普通转账")) {
                        transferAccountInformation.setTransferAccountsType("0");
                    } else {
                        transferAccountInformation.setTransferAccountsType("1");
                    }
                    TransferAccountInformation trans = tInformationService.addTransferAccountInformation(transferAccountInformation);
                    JSONArray jsonArray = JSONArray.fromObject(data2.getString("accountBookJson"));
                    for (int j = 0; j < jsonArray.size(); j++) {
                        String obj = jsonArray.getString(j);
                        JSONObject jsonObject = JSONObject.fromObject(obj);
                        if (!jsonObject.getString("AMOUNT").equals("") && jsonObject.getString("AMOUNT") != null) {
                            TransferBook book = new TransferBook();
                            book.setTransferDetailedNumber(trans.getUuid());
                            book.setPayType(jsonObject.getString("PAY_TYPE"));
                            book.setPayName(jsonObject.getString("PAY_NAME"));
                            book.setPrepayFee(jsonObject.getString("PREPAY_FEE"));
                            book.setAmount(jsonObject.getString("AMOUNT"));
                            book.setContractnoBalance(jsonObject.getString("CONTRACTNO_BALANCE"));
                            tInformationService.addTransferBook(book);
                        }
                    }
                }
            } else {
                for (int i = 0; i < jsonObject1.size(); i++) {
                    String s = jsonObject1.getString(i);
                    JSONObject data2 = JSONObject.fromObject(s);
                    TransferAccountInformation transferAccountInformation = new TransferAccountInformation();
                    transferAccountInformation.setTransferNumber(transferInformationtwo.getTransferNumber());
                    transferAccountInformation.setTransferAccountNumber(data2.getString("transferAccountNumber"));
                    transferAccountInformation.setTransferAccounType(data2.getString("transferAccounType"));
                    transferAccountInformation.setTurnAccountNumber(data2.getString("turnAccountNumber"));
                    transferAccountInformation.setTurnAccountType(data2.getString("turnAccountType"));
                    transferAccountInformation.setTransferAccountPhone(data2.getString("transferAccountPhone"));
                    //transferAccountInformation.setTransferAccounName(data2.getString("transferAccounName"));
                    transferAccountInformation.setTranAccountPhone(data2.getString("tranAccountPhone"));
                    transferAccountInformation.setAmountOfMoney(data2.getString("amountOfMoney"));
                    if (!"".equals(data2.getString("lateFee")) && !"undefined".equals(data2.getString("lateFee"))
                            && !"null".equals(data2.getString("lateFee")) && data2.getString("lateFee") != null) {
                        transferAccountInformation.setLateFee(data2.getString("lateFee"));
                        if (!"".equals(data2.getString("lateFeeMoney")) && !"undefined".equals(data2.getString("lateFeeMoney"))
                                && !"null".equals(data2.getString("lateFeeMoney")) && data2.getString("lateFeeMoney") != null) {
                            transferAccountInformation.setLateFeeMoney(data2.getString("lateFeeMoney"));
                        }
                    }
                    transferAccountInformation.setAccountNumber(transferInformationtwo.getTransferNumber() + i);
                    if (data2.getString("transferAccountsType").equals("普通转账")) {
                        transferAccountInformation.setTransferAccountsType("0");
                    } else {
                        transferAccountInformation.setTransferAccountsType("1");
                    }
                    TransferAccountInformation trans = tInformationService.addTransferAccountInformation(transferAccountInformation);
                    JSONArray jsonArray = JSONArray.fromObject(data2.getString("accountBookJson"));
                    for (int j = 0; j < jsonArray.size(); j++) {
                        String obj = jsonArray.getString(j);
                        JSONObject jsonObject = JSONObject.fromObject(obj);
                        if (!jsonObject.getString("AMOUNT").equals("") && jsonObject.getString("AMOUNT") != null) {
                            TransferBook book = new TransferBook();
                            book.setTransferDetailedNumber(trans.getUuid());
                            book.setPayType(jsonObject.getString("PAY_TYPE"));
                            book.setPayName(jsonObject.getString("PAY_NAME"));
                            book.setPrepayFee(jsonObject.getString("PREPAY_FEE"));
                            book.setAmount(jsonObject.getString("AMOUNT"));
                            book.setContractnoBalance(jsonObject.getString("CONTRACTNO_BALANCE"));
                            tInformationService.addTransferBook(book);
                        }
                    }
                }
            }

            List<SingleAndAttachment> ss = tInformationService.getSingleAndAttachment(id);
            //遍历获取的附件中间表数据
            if (ss != null) {
                for (int i = 0; i < ss.size(); i++) {
                    if (i == 0) {
                        if (!"".equals(attachmentId)) {
                            attachmentId += "," + ss.get(i).getAttachmentId() + ",";
                        } else {
                            attachmentId += ss.get(i).getAttachmentId() + ",";
                        }
                    } else {
                        attachmentId += ss.get(i).getAttachmentId() + ",";
                    }
                }
            }
            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            Attachment att = tInformationService.getAttachment(json[i]);
                            att.setType(tInformationService.getAttachmentType("QT"));//所属类型
                            att.setGroupCode(GroupCode);//集团编码
                            att.setSystemSource("EOM");//系统源(EOM/138)
                            tInformationService.saveOrUpdateAttachment(att);

                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(transferInformationtwo.getId());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(TransferInformation.Transfer);
                            tInformationService.saveSandA(sa);
                        }
                    }
                }
            }
            //社会统一信用代码 或 营业执照 附件
            if (!StringUtils.isEmpty(attachmentOne) && attachmentOne != null) {
                Attachment att = new Attachment();
                if (fileTypeOne.equals("15")) {
                    att.setType(tInformationService.getAttachmentType("SHTYXYDM"));//所属类型
                } else if (fileTypeOne.equals("11")) {
                    att.setType(tInformationService.getAttachmentType("YYZZ"));//所属类型
                }
                att.setRealName(fileNameOne);//附件真实名称
                att.setAttachmentUrl(attachmentOne);//附件地址(138附件 存138附件id)
                att.setUploadUser(user);//上传人
                att.setUploadDate(new Date());//上传时间
                att.setGroupCode(GroupCode);//集团编码
                att.setSystemSource("138");//系统源(EOM/138)
                Attachment attachment = tInformationService.saveOrUpdateAttachment(att);

                //附件中间表
                SingleAndAttachment sa = new SingleAndAttachment();
                sa.setOrderID(transferInformationtwo.getId());
                sa.setAttachmentId(attachment.getAttachmentId());
                sa.setLink(TransferInformation.Transfer);
                tInformationService.saveSandA(sa);
            }
            //法人身份证 或 授权函 附件
            if (!StringUtils.isEmpty(attachmentTwo) && attachmentTwo != null) {
                Attachment att = new Attachment();
                if (fileTypeTwo.equals("12")) {
                    att.setType(tInformationService.getAttachmentType("FRSFZ"));//所属类型
                } else if (fileTypeTwo.equals("16")) {
                    att.setType(tInformationService.getAttachmentType("SQH"));//所属类型
                }
                att.setRealName(fileNameTwo);//附件真实名称
                att.setAttachmentUrl(attachmentTwo);//附件地址(138附件 存138附件id)
                att.setUploadUser(user);//上传人
                att.setUploadDate(new Date());//上传时间
                att.setGroupCode(GroupCode);//集团编码
                att.setSystemSource("138");//系统源(EOM/138)
                Attachment attachment = tInformationService.saveOrUpdateAttachment(att);

                //附件中间表
                SingleAndAttachment sa = new SingleAndAttachment();
                sa.setOrderID(transferInformationtwo.getId());
                sa.setAttachmentId(attachment.getAttachmentId());
                sa.setLink(TransferInformation.Transfer);
                tInformationService.saveSandA(sa);
            }
            //协议选传 附件
            if (!StringUtils.isEmpty(attachmentThree) && attachmentThree != null) {
                Attachment att = tInformationService.getAttachment(attachmentThree);
                att.setType(tInformationService.getAttachmentType("XYXC"));//所属类型
                att.setGroupCode(GroupCode);//集团编码
                att.setSystemSource("EOM");//系统源(EOM/138)
                tInformationService.saveOrUpdateAttachment(att);

                //附件中间表
                SingleAndAttachment sa = new SingleAndAttachment();
                sa.setOrderID(transferInformationtwo.getId());
                sa.setAttachmentId(att.getAttachmentId());
                sa.setLink(TransferInformation.Transfer);
                tInformationService.saveSandA(sa);
            }
            //转账函 附件
            if (!StringUtils.isEmpty(attachmentFour) && attachmentFour != null) {
                Attachment att = tInformationService.getAttachment(attachmentFour);
                att.setType(tInformationService.getAttachmentType("ZZH"));//所属类型
                att.setGroupCode(GroupCode);//集团编码
                att.setSystemSource("EOM");//系统源(EOM/138)
                tInformationService.saveOrUpdateAttachment(att);
                //附件中间表
                SingleAndAttachment sa = new SingleAndAttachment();
                sa.setOrderID(transferInformationtwo.getId());
                sa.setAttachmentId(att.getAttachmentId());
                sa.setLink(TransferInformation.Transfer);
                tInformationService.saveSandA(sa);
            }
            //营销活动备案记录
            if (!StringUtils.isEmpty(attachmentFive) && attachmentFive != null) {
                Attachment att = tInformationService.getAttachment(attachmentFive);
                att.setType(tInformationService.getAttachmentType("YXHDBAJL"));//所属类型
                att.setGroupCode(GroupCode);//集团编码
                att.setSystemSource("EOM");//系统源(EOM/138)
                tInformationService.saveOrUpdateAttachment(att);
                //附件中间表
                SingleAndAttachment sa = new SingleAndAttachment();
                sa.setOrderID(transferInformationtwo.getId());
                sa.setAttachmentId(att.getAttachmentId());
                sa.setLink(TransferInformation.Transfer);
                tInformationService.saveSandA(sa);
            }

            if (!"CG".equals(type)) {
                Map<String, String> map = new HashMap<String, String>();
                map.put("decisionKey", "APPLY");
                map.put("decisionVal", role); // 所属地区（区县：QX、市公司：SGS、省重客：SZK）
                String processId = transferJBPMUtils.startTransfer("TransferProcessFinal", map);
                // 流程启动完成
                // 保存信息到流程表
                TransferProcess ProcessList = new TransferProcess();
                ProcessList.setProcessId(processId);// 流程id
                ProcessList.setProcessName(transferInformation.getTransferTitle());// 流程名称
                ProcessList.setUserId(user.getEmployeeName());
                ProcessList.setTransferId(transferInformation.getId());
                ProcessList.setCreatorNo(user.getRowNo() + "");
                ProcessList.setStartTime(getStringDate(new Date()));
                ProcessList.setState(1);
                TransferProcess ProcessBean = tInformationService.saveProcessList(ProcessList); // 保存任务表信息
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                // 先保存自己本身的任务
                TransferTask TaskList = new TransferTask();
                TaskList.setRole("客户经理");
                TaskList.setProcess(transferInformationtwo.getId());
                TaskList.setCreator(user.getEmployeeName());
                TaskList.setCreatorNo(user.getRowNo() + "");
                TaskList.setCreatDate(getStringDate(new Date()));
                TaskList.setPlanDate(getStringDate(new Date()));
                TaskList.setOper(user.getEmployeeName());
                TaskList.setOperNo(user.getRowNo() + "");
                TaskList.setOperDate(getStringDate(new Date()));
                TaskList.setSpendTime("0");
                TaskList.setStatus("2");
                TaskList.setType("SH");
                TaskList.setExpectedCompletionTime(getStringDate(new Date()));
                TransferTask TaskBean = tInformationService.saveTaskList(TaskList);// 保存流程表信息
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                //保存下一步任务信息
                TransferTask TaskListtwo = new TransferTask();
                TaskListtwo.setProcess(transferInformationtwo.getId());
                TaskListtwo.setRole(task.getActivityName());
                TaskListtwo.setCreator(user.getEmployeeName());
                TaskListtwo.setCreatorNo(user.getRowNo() + "");
                TaskListtwo.setCreatDate(getStringDatethree());
                TaskListtwo.setPlanDate(getStringDate(new Date()));
                TaskListtwo.setOper(USER.getEmployeeName());
                TaskListtwo.setOperNo(USER.getRowNo() + "");
                //TaskListtwo.setOperDate(getStringDate(new Date()));
                TaskListtwo.setSpendTime("0");
                TaskListtwo.setStatus("1");
                TaskListtwo.setType("SH");
                TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                TransferTask TaskBeantwo = tInformationService.saveTaskList(TaskListtwo);// 保存流程表信息
                daibantwo(transferInformationtwo, userid, processId, user, TaskBeantwo);// 代办
            }

            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDate(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatetwo(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换2
     *
     * @param strDate
     * @return
     * @throws ParseException
     */
    public Date formatForDate(String strDate) throws ParseException {
        Date date = null;
        if (strDate != null && !"".equals(strDate)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
            date = format.parse(strDate);
        }
        return date;
    }

    // 退回方法
    public void returnTransition() {
        try {
            String id = getString("id");// 转账信息id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String transferTaskID = getString("transferTaskID");
            if (waitId == null || waitId.equals("") || transferTaskID == null || transferTaskID.equals("")) {
                WaitTask waitTask = tInformationService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                transferTaskID = str.substring(str.lastIndexOf("=") + 1);
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                //WaitTask wait = service.queryWaitByTaskId(waitId);
                System.out.println("================退回开始代办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================退回结束代办================");
            } else {
                Write("NO");
                return;
            }
            TransferTask transferTask = tInformationService.getTaskList(transferTaskID);// 根据流程id查询任务表信息
            transferTask.setOperDate(getStringDate(new Date()));//操作时间
            transferTask.setReplyContent(opinion);//审批意见
            transferTask.setStatus("0");
            TransferTask transfer = tInformationService.updateTask(transferTask);// 修改任务表
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);// 根据转账信息id查询转账信息
            transferInformation.setState(3);
            transferInformation.setHANDLER_ID(null);
            tInformationService.update(transferInformation);
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.valueOf(transferInformation.getCustomerManager()));
            daibanthree(transferInformation, transferInformation.getCustomerManager(), waitId, user, transfer);// 调用service层方法生成待办
            Write("OK");
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 流程进行
     */
    public void handleTransfer() {
        try {
            String pid = getString("processId");// 流程id
            String t = getString("zhuanshentwo");// 下一步可执行流程线条值
            String userid = getString("userId");// 用户id
            String id = getString("id");// 转账信息id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String phone = getString("phone");
            String transferTaskID = getString("transferTaskID");
            if (waitId == null || waitId.equals("") || transferTaskID == null || transferTaskID.equals("")) {
                WaitTask waitTask = tInformationService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                transferTaskID = waitTask.getTaskId();
            }
            TransferTask transferTask = tInformationService.getTaskList(transferTaskID);// 根据流程id查询任务表信息
            if (transferTask != null) {
                transferTask.setOperDate(getStringDate(new Date()));//操作时间
                transferTask.setReplyContent(opinion);//审批意见
                transferTask.setStatus("2");
                tInformationService.updateTask(transferTask);//修改任务表
            }
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            List<Map<String, Object>> list = tInformationService.getCountyByUserID(transferInformation.getCustomerManager());
            String code = "";
            for (int i = 0; i < list.size(); i++) {
                if ("true".equals(list.get(i).get("ISMAINDPT").toString())) {
                    code = list.get(i).get("COMPANY_CODE").toString();
                    break;
                }
            }

            // 执行流程
            //查询流程跟踪信息并改变
            System.out.println("待办ID==========：" + waitId);
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                System.out.println("================处理中开始代办================");
                //WaitTask wait = service.queryWaitByTaskId(waitId);
                service.updateWait(wait, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            logger.info("task.getActivityName==" + task.getActivityName());
            Map<String, String> map = new HashMap<String, String>();
            String str = pid.substring(0, pid.indexOf("."));
            if ("TransferFour".equals(str)) {
                if ("省重客分管经理".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "DSSMDO");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}*//*else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}*//*else {
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("市公司领导".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "DSPMDO");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}*//*else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}*//*else {
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("市公司政企部经理".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "DSDMDO");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}else{
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("市公司政企部经理副".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, "市公司政企部经理");
                    map.put("decisionKey", "DSDMDO");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}else{
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("省公司管理员".equals(task.getActivityName())) {
                    map.put("decisionKey", "SGSGLY");
                    map.put("decisionVal", "YES");
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("区县分管经理".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "QXFGJL");
                    map.put("decisionVal", "YES");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "YES");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "YES");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "YES");
						}else{
							map.put("decisionVal", "NO");
						}
					}else{
						map.put("decisionVal", "YES");
					}*/
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("省重客业务管理室经理".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SZK3DM");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}else{
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("省公司政企业务管理室2次".equals(task.getActivityName())) {
                    map.put("decisionKey", "SGSMR");
                    map.put("decisionVal", "YES");
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else {
                    jbpmUtil.completeTask(task.getId(), t);
                }
            } else {
                //新流程
                //TransferProcessFinal
                if ("省重客分管经理".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "DSSMDO");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}*//*else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}*//*else {
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("市公司领导".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "DSPMDO");
                    map.put("decisionVal", "NO");
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("区县业务管理员".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "ROLE_QXDM");
                    map.put("decisionVal", "YES");
                    jbpmUtil.completeTask(task.getId(), map, t);
                } else if ("区县政企部主任".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "ROLE_QXSM");
                    map.put("decisionVal", "YES");
                    jbpmUtil.completeTask(task.getId(), map, t);
                } else if ("市公司政企部经理".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "DSDMDO");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}else{
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("市公司政企部经理副".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, "市公司政企部经理");
                    map.put("decisionKey", "DSDMDO");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}else{
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("省公司管理员".equals(task.getActivityName())) {
                    map.put("decisionKey", "SGSGLY");
                    map.put("decisionVal", "YES");
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("区县分管经理".equals(task.getActivityName())) {
//                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "QXFGJL");
                    map.put("decisionVal", "YES");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "YES");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "YES");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "YES");
						}else{
							map.put("decisionVal", "NO");
						}
					}else{
						map.put("decisionVal", "YES");
					}*/
                    jbpmUtil.completeTask(task.getId(), map, t);
                } else if ("省重客业务管理室经理".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SZK3DM");
                    map.put("decisionVal", "NO");
					/*if(transferInformation.getCompetitiveDemand()==0){
						if (transferInformation.getAcrossGroup()==0) {
							map.put("decisionVal", "NO");
						}else if(Double.parseDouble(transferCitiesData.getAmount())>0){
							if(Double.parseDouble(transferInformation.getOutMoney())>=Double.parseDouble(transferCitiesData.getAmount())){
								map.put("decisionVal", "NO");
							}
						}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
							map.put("decisionVal", "NO");
						}else{
							map.put("decisionVal", "YES");
						}
					}else{
						map.put("decisionVal", "NO");
					}*/
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("省公司政企业务管理室2次".equals(task.getActivityName())) {
                    map.put("decisionKey", "SGSMR");
                    map.put("decisionVal", "YES");
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else {
                    jbpmUtil.completeTask(task.getId(), t);
                }
            }
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            //保存下一步任务信息
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            TransferTask TaskListtwo = new TransferTask();
            TaskListtwo.setProcess(id);
            TaskListtwo.setRole(tasktwo.getActivityName());
            TaskListtwo.setCreator(user.getEmployeeName());
            TaskListtwo.setCreatorNo(user.getRowNo() + "");
            TaskListtwo.setCreatDate(getStringDate(new Date()));
            TaskListtwo.setPlanDate(getStringDate(new Date()));
            TaskListtwo.setOper(USER.getEmployeeName());
            TaskListtwo.setOperNo(USER.getRowNo() + "");
            //TaskListtwo.setOperDate(getStringDate(new Date()));
            TaskListtwo.setSpendTime("0");
            TaskListtwo.setStatus("1");
            TaskListtwo.setType("SH");
            TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
            TransferTask TaskBeantwo = tInformationService.saveTaskList(TaskListtwo);// 保存流程表信息
            if ("省重客分管经理".equals(task.getActivityName())) {
                TransferNode transferNode = tInformationService.getTransferNode("省公司政企业务管理室");
                if (Integer.parseInt(transferNode.getWhetherToOpen()) == 1) {
                    TransferTask transferTaskt = tInformationService.getTaskList(TaskBeantwo.getUuid());// 根据流程id查询任务表信息
                    if (transferTaskt != null) {
                        transferTaskt.setOperDate(getStringDate(new Date()));// 操作时间
                        transferTaskt.setReplyContent(transferNode.getOpinion());//审批意见
                        transferTaskt.setStatus("2");
                        tInformationService.updateTask(transferTaskt);// 修改任务表
                    }
                    Task tasktwot = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                    jbpmUtil.completeTask(tasktwot.getId(), "ROLE_SGSBM");
                    TransferNode transferNodetwo = tInformationService.getTransferNodeUserid("省公司管理员");
                    SystemUser systemUser = systemUserService.getUserInfoRowNo(Integer.parseInt(transferNodetwo.getCountry()));
                    Task tasktwott = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                    TransferTask TaskListtwot = new TransferTask();
                    TaskListtwot.setProcess(id);
                    TaskListtwot.setRole(tasktwott.getActivityName());
                    TaskListtwot.setCreator(USER.getEmployeeName());
                    TaskListtwot.setCreatorNo(USER.getRowNo() + "");
                    TaskListtwot.setCreatDate(getStringDatethree());
                    TaskListtwot.setPlanDate(getStringDatethree());
                    TaskListtwot.setOper(systemUser.getEmployeeName());
                    TaskListtwot.setOperNo(systemUser.getRowNo() + "");
                    TaskListtwot.setSpendTime("0");
                    TaskListtwot.setStatus("1");
                    TaskListtwot.setType("SH");
                    TaskListtwot.setExpectedCompletionTime(getStringDatethree());
                    TransferTask TaskBeantwot = tInformationService.saveTaskList(TaskListtwot);// 保存流程表信息
                    daibantwo(transferInformation, transferNodetwo.getCountry(), pid, systemUser, TaskBeantwot);// 调用service层方法生成代办
                } else {
                    daibantwo(transferInformation, userid, pid, user, TaskListtwo);// 调用service层方法生成代办
                }
            } else if ("市公司领导".equals(task.getActivityName())) {
                TransferNode transferNode = tInformationService.getTransferNode("省公司政企业务管理室");
                if (Integer.parseInt(transferNode.getWhetherToOpen()) == 1) {
                    TransferTask transferTaskt = tInformationService.getTaskList(TaskBeantwo.getUuid());// 根据流程id查询任务表信息
                    if (transferTaskt != null) {
                        transferTaskt.setOperDate(getStringDate(new Date()));// 操作时间
                        transferTaskt.setReplyContent(transferNode.getOpinion());//审批意见
                        transferTaskt.setStatus("2");
                        tInformationService.updateTask(transferTaskt);// 修改任务表
                    }
                    Task tasktwot = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                    jbpmUtil.completeTask(tasktwot.getId(), "ROLE_SGSBM");
                    TransferNode transferNodetwo = tInformationService.getTransferNodeUserid("省公司管理员");
                    SystemUser systemUser = systemUserService.getUserInfoRowNo(Integer.parseInt(transferNodetwo.getCountry()));
                    Task tasktwott = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                    TransferTask TaskListtwot = new TransferTask();
                    TaskListtwot.setProcess(id);
                    TaskListtwot.setRole(tasktwott.getActivityName());
                    TaskListtwot.setCreator(USER.getEmployeeName());
                    TaskListtwot.setCreatorNo(USER.getRowNo() + "");
                    TaskListtwot.setCreatDate(getStringDatethree());
                    TaskListtwot.setPlanDate(getStringDatethree());
                    TaskListtwot.setOper(systemUser.getEmployeeName());
                    TaskListtwot.setOperNo(systemUser.getRowNo() + "");
                    TaskListtwot.setSpendTime("0");
                    TaskListtwot.setStatus("1");
                    TaskListtwot.setType("SH");
                    TaskListtwot.setExpectedCompletionTime(getStringDatethree());
                    TransferTask TaskBeantwot = tInformationService.saveTaskList(TaskListtwot);// 保存流程表信息
                    daibantwo(transferInformation, transferNodetwo.getCountry(), pid, systemUser, TaskBeantwot);// 调用service层方法生成代办
                } else {
                    daibantwo(transferInformation, userid, pid, user, TaskListtwo);// 调用service层方法生成代办
                }
            } else {
                daibantwo(transferInformation, userid, pid, user, TaskListtwo);// 调用service层方法生成代办
            }
            transferInformation.setHANDLER_ID(userid);
            tInformationService.update(transferInformation);
            Write("OK");
        } catch (Error ee) {
            Write("NO");
            logger.error("转账流程进行错误1:" + ee.getMessage(), ee);
            throw new RuntimeException(" 给事务回滚，自定义1");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            logger.error("转账流程进行错误2:" + e.getMessage(), e);
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    // 完成方法
    public void completeTransition() {
        try {
            String processId = getString("processId");// 流程id
            String id = getString("id");// 账户信息id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 审批意见
            String transferTaskID = getString("transferTaskID");
            String phone = getString("phone");
            if (waitId == null || waitId.equals("") || transferTaskID == null || transferTaskID.equals("")) {
                WaitTask waitTask = tInformationService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                transferTaskID = str.substring(str.lastIndexOf("=") + 1);
            }
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            List<TransferAccountInformation> transferAccountInformation = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            System.out.println("失效时间：" + transferInformation.getExpTime());
            System.out.println("判断失效时间：" + transferInformation.getExpTime() == null);
            if (transferInformation.getExpTime() == null) {
                Write("NON");
            } else {
				/*SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
				String json="";
				String code="";
				if("0".equals(transferInformation.getVersionNumber())){//旧版本
					json = setTransfer(transferInformation,USER);
					JSONObject jsthree = JSONObject.fromObject(json);
					String datatwo = jsthree.getString("res");
					System.out.println("这是返回数据解析结果"+datatwo);
					JSONObject jsone = JSONObject.fromObject(datatwo);
					JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
					if("0".equals(jstwo.getString("RETURN_CODE"))){
						code="0";
					}else{
						code = jstwo.getString("DETAIL_MSG");
					}
				}else{//新版本
					json = sSplitPrepayTran(transferInformation,USER,"0");
					if("1".equals(json)){
						code="1";
					}else if("0".equals(json)){
						code="0";
					}else{
						code=json;
					}
				}*/
                //if("0".equals(code)){

                //添加审批完成提醒对接指南针数据
                for (int i = 0; i < transferAccountInformation.size(); i++) {
                    JSONObject obj = taskService.getInvoice_message(transferAccountInformation.get(i).getTransferNumber(), transferAccountInformation.get(i).getTurnAccountNumber(), transferAccountInformation.get(i).getTurnAccountType(), transferAccountInformation.get(i).getAmountOfMoney(), tasktwo.getActivityName(), user.getRowNo());
                    taskService.setReminder_information_tbl(transferInformation.getOutGroup(), "1", "14", "转账", obj.toString(), "转账");
                }
                TransferTask transferTask = tInformationService.getTaskList(transferTaskID);// 根据流程id查询任务表信息
                if (transferTask != null) {
                    transferTask.setOperDate(getStringDate(new Date()));// 操作时间
                    transferTask.setReplyContent(opinion);//审批意见
                    transferTask.setStatus("2");
                    tInformationService.updateTask(transferTask);// 修改任务表
                }
                transferInformation.setState(1);// 状态修改为已完成
                transferInformation.setUpdateTime(new Date());// 更新时间
                transferInformation.setHANDLER_ID(transferInformation.getCustomerManager());
                tInformationService.update(transferInformation);
                jbpmUtil.deleteProcessInstance(processId);//删除流程
                //保存下一步任务信息
                TransferTask TaskListtwo = new TransferTask();
                TaskListtwo.setProcess(id);
                TaskListtwo.setRole("客户经理");
                TaskListtwo.setCreator(user.getEmployeeName());
                TaskListtwo.setCreatorNo(user.getRowNo() + "");
                TaskListtwo.setCreatDate(getStringDate(new Date()));
                TaskListtwo.setPlanDate(getStringDate(new Date()));
                TaskListtwo.setOper(transferInformation.getProposer());
                TaskListtwo.setOperNo(transferInformation.getCustomerManager() + "");
                //TaskListtwo.setOperDate(getStringDate(new Date()));
                TaskListtwo.setSpendTime("0");
                TaskListtwo.setStatus("1");
                TaskListtwo.setType("SH");
                TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                TransferTask TaskBeantwo = tInformationService.saveTaskList(TaskListtwo);// 保存流程表信息
                daiban(transferInformation, transferInformation.getCustomerManager(), processId, user, TaskBeantwo, "FQZF");
                WaitTask wait = service.queryWaitByTaskId(waitId);
                if (wait != null) {
                    service.updateWait(wait, this.getRequest());
                } else {
                    Write("NO");
                    throw new RuntimeException(" 给事务回滚，自定义");
                }
                Write("OK");
				/*}else if("1".equals(code)){
					Write("系统错误，请联系系统管理员");
				}else{
					Write(code);
				}*/
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    //作废方法
    public void Invalid() {
        try {
            String id = getString("id");// 转账信息id
            String opinion = getString("opinion");// 作废原因
            String waitId = getString("waitId");
            String processId = getString("processId");//流程id
            if (waitId == null || waitId.equals("")) {
                WaitTask waitTask = tInformationService.getWaitTask(id);
                waitId = waitTask.getWaitId();
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                System.out.println("================作废开始代办================");
                //WaitTask wait = service.queryWaitByTaskId(waitId);
                service.updateWait(wait, this.getRequest());
                System.out.println("================作废结束代办================");
            } else {
                Write("NO");
                return;
            }
            TransferInformation trInformation = tInformationService.getTransferInformation(id);
            trInformation.setState(-1);//状态修改为退回
            trInformation.setUpdateTime(new Date());//更新时间
            trInformation.setInvalidReason(opinion);//作废原因
            tInformationService.update(trInformation);//修改转账信息表
            jbpmUtil.deleteProcessInstance(processId);//删除流程实例
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    // 判断角色
    public void judgeRole() {
        try {
            // 获取当前用户
            SystemUser user = this.user;
            String role = "";
            // 获取公司编码
            //List companyCode = tInformationService.findCodeByRowNo(user.getRowNo());
            //获取部门信息
            List<Map<String, Object>> map = tInformationService.findDept(user.getRowNo());
			/*System.out.println((companyCode.get(0).toString())+"=================");
			if ((companyCode.get(0).toString()).equals("00")) {
				role = "SZK";
			}
			else {
				if ((map.get("TWODNAME").toString()).indexOf("分公司")==-1) {
					role = "SGS";
				}
				else{
					role = "QX";
				}
			}*/
            String json = JSONHelper.SerializeWithNeedAnnotation(map);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }

    }

    // 提交待办生成
    public void daibantwo(TransferInformation transferInformation, String userid, String processId, SystemUser user, TransferTask transferTasktwo) {
        WaitTask wt = new WaitTask();
        wt.setName("[转账]" + transferInformation.getTransferTitle());
        wt.setCreationTime(new Date());
//        wt.setUrl("jsp/transfer/handleTransferTwo.jsp?id=" + transferInformation.getId() + "&processId="// 流程ID
//                + processId
//                + "&transferTaskID="
//                + transferTasktwo.getUuid());
        wt.setUrl("jsp/transfer/handleTransferTwo.jsp");
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
        wt.setState(WaitTask.HANDLE);
        wt.setHandleUserId(USER.getRowNo());
        wt.setHandleUserName(USER.getEmployeeName());
        wt.setHandleLoginName(USER.getLoginName());
        wt.setCreateUserId(user.getRowNo());
        wt.setCreateUserName(user.getEmployeeName());
        wt.setCreateLoginName(user.getLoginName());
        wt.setCode("ZZ");
        wt.setOrderNo(transferInformation.getTransferNumber());//transferNumber
        wt.setTaskId(transferTasktwo.getUuid());
        service.saveWaitPushMOA(wt, this.getRequest());
    }

    // 退回待办生成
    public void daibanthree(TransferInformation transferInformation, String userid, String waitId, SystemUser user, TransferTask transfer) {
        WaitTask wt = new WaitTask();
        wt.setName("[转账]" + transferInformation.getTransferTitle());
        wt.setCreationTime(new Date());
//        wt.setUrl("jsp/transfer/returnTransferTwo.jsp?id=" + transferInformation.getId());// 这是需要改动的退回以后的页面地址
        wt.setUrl("jsp/transfer/returnTransferTwo.jsp");
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
        wt.setState(WaitTask.HANDLE);
        wt.setHandleUserId(USER.getRowNo());
        wt.setHandleUserName(USER.getEmployeeName());
        wt.setHandleLoginName(USER.getLoginName());
        wt.setCreateUserId(user.getRowNo());
        wt.setCreateUserName(user.getEmployeeName());
        wt.setCreateLoginName(user.getLoginName());
        wt.setCode("ZZ");
        wt.setTaskId(transfer.getUuid());
        wt.setOrderNo(transferInformation.getTransferNumber());
        service.saveWaitPushMOA(wt, this.getRequest());
    }

    //完成代办生成
    public void daiban(TransferInformation transferInformation, String userid, String processId, SystemUser user, TransferTask transferTasktwo, String type) {
        WaitTask wt = new WaitTask();
        wt.setName("[转账]" + transferInformation.getTransferTitle());
        wt.setCreationTime(new Date());
        wt.setUrl("jsp/transfer/completeTransferTwo.jsp?type=" + type);//这是需要改动的退回以后的页面地址
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
        wt.setState(WaitTask.HANDLE);
        wt.setHandleUserId(USER.getRowNo());
        wt.setHandleUserName(USER.getEmployeeName());
        wt.setHandleLoginName(USER.getLoginName());
        wt.setCreateUserId(user.getRowNo());
        wt.setCreateUserName(user.getEmployeeName());
        wt.setCreateLoginName(user.getLoginName());
        wt.setCode("ZZ");
        wt.setTaskId(transferTasktwo.getUuid());
        wt.setOrderNo(transferInformation.getTransferNumber());
        service.saveWaitPushMOA(wt, this.getRequest());

    }

    //阅读结束方法
    public void readEnd() {
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            String transferTaskID = getString("transferTaskID");
            if (waitId == null || waitId.equals("") || transferTaskID == null || transferTaskID.equals("")) {
                WaitTask waitTask = tInformationService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                String[] sub = str.substring(str.lastIndexOf("transferTaskID")).split("&");
                transferTaskID = sub[0].substring(sub[0].lastIndexOf("=") + 1);
            }
            TransferTask transferTask = tInformationService.getTaskList(transferTaskID);// 根据流程id查询任务表信息
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
            String code = "";
            List<TransferAccountInformation> transferAccountInformationList = tInformationService.gettransferAccountInformationlistTwo(transferInformation.getTransferNumber());
            if (transferAccountInformationList.size() > 0) {
                if ("0".equals(transferInformation.getVersionNumber())) {//旧版本
                    String json = setTransferTwo(transferInformation, USER);//第二次推送
                    JSONObject jsthree = JSONObject.fromObject(json);
                    String datatwo = jsthree.getString("res");
                    logger.info("这是老转账接口返回数据解析结果" + datatwo);
                    JSONObject jsone = JSONObject.fromObject(datatwo);
                    JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                    if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                        for (int i = 0; i < transferAccountInformationList.size(); i++) {
                            transferAccountInformationList.get(i).setBossState("0");
                            tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                        }
                        code = "0";
                    } else {
                        for (int i = 0; i < transferAccountInformationList.size(); i++) {
                            transferAccountInformationList.get(i).setBossState("1");
                            transferAccountInformationList.get(i).setBossMsg(jstwo.getString("DETAIL_MSG"));
                            tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                        }
                        code = jstwo.getString("DETAIL_MSG");
                    }
                } else {//新版本
                    String json = sSplitPrepayTran(transferInformation, USER, "0");
                    if ("1".equals(json)) {
                        code = "1";
                    } else if ("0".equals(json)) {
                        code = "0";
                    } else {
                        code = json;
                    }
                }
            } else {
                code = "0";
            }
            //String code=sSplitPrepayTran(transferInformation,USER,"0");
            if ("0".equals(code)) {
                if (transferTask != null) {
                    transferTask.setOperDate(getStringDate(new Date()));//操作时间
                    transferTask.setReplyContent("");//审批意见
                    transferTask.setStatus("2");
                    tInformationService.updateTask(transferTask);//修改任务表
                }
                WaitTask wait = service.queryWaitByTaskId(waitId);
                if (wait != null) {
                    logger.info("================转账阅读开始代办================");
                    service.updateWait(wait, this.getRequest());
                    logger.info("================转账阅读结束代办================");
                } else {
                    Write("NO");
                    return;
                }
                transferInformation.setHANDLER_ID(null);
                tInformationService.update(transferInformation);
                Write("OK");
            } else if ("1".equals(code)) {
                Write("NO");
            } else {
                Write(code);
            }
        } catch (Exception e) {
            logger.error("转账推送出错:" + e.getMessage(), e);
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 根据ID查询跟踪处理
     *
     * @return
     */
    public void processtracking() {
        String id = getString("id");
        List<TransferTask> p = tInformationService.processtracking(id);
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
    }

    /**
     * 获取附件消息
     */
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = tInformationService.fuJian(id, biaoshi);
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
    }

    public void submitTransfer() {
        try {
            String waitId = getString("waitId");
            String id = getString("id");// 账户信息id
            String transferTaskID = getString("transferTaskID");
            String userid = this.getString("userid");//接收人ID
            String processId = getString("processId");
            String opinion = getString("opinion");// 审批意见
            String type = getString("type");
            if (waitId == null || waitId.equals("") || transferTaskID == null || transferTaskID.equals("")) {
                WaitTask waitTask = tInformationService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                transferTaskID = str.substring(str.lastIndexOf("=") + 1);
            }
            TransferTask transferTask = tInformationService.getTaskList(transferTaskID);// 根据流程id查询任务表信息
            if (transferTask != null) {
                transferTask.setOperDate(getStringDate(new Date()));//操作时间
                transferTask.setReplyContent(opinion);//审批意见
                transferTask.setStatus("2");
                tInformationService.updateTask(transferTask);//修改任务表
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                System.out.println("================转发开始代办================");
                //WaitTask wait = service.queryWaitByTaskId(waitId);
                service.updateWait(wait, this.getRequest());
                System.out.println("================转发结束代办================");
            } else {
                Write("NO");
                return;
            }
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            if ("ZF".equals(type)) {
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
                TransferTask TaskListtwo = new TransferTask();
                TaskListtwo.setProcess(id);
                TaskListtwo.setRole("客户经理");
                TaskListtwo.setCreator(user.getEmployeeName());
                TaskListtwo.setCreatorNo(user.getRowNo() + "");
                TaskListtwo.setCreatDate(getStringDate(new Date()));
                TaskListtwo.setPlanDate(getStringDate(new Date()));
                TaskListtwo.setOper(USER.getEmployeeName());
                TaskListtwo.setOperNo(USER.getRowNo() + "");
                TaskListtwo.setSpendTime("0");
                TaskListtwo.setStatus("1");
                TaskListtwo.setType("SH");
                TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                TransferTask TaskBeantwo = tInformationService.saveTaskList(TaskListtwo);// 保存流程表信息
                daibanfor(transferInformation, transferInformation.getCustomerManager(), user, "FQZF", processId, TaskBeantwo);
            } else {
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                TransferTask TaskListtwo = new TransferTask();
                TaskListtwo.setProcess(id);
                TaskListtwo.setRole("转发审核");
                TaskListtwo.setCreator(user.getEmployeeName());
                TaskListtwo.setCreatorNo(user.getRowNo() + "");
                TaskListtwo.setCreatDate(getStringDate(new Date()));
                TaskListtwo.setPlanDate(getStringDate(new Date()));
                TaskListtwo.setOper(USER.getEmployeeName());
                TaskListtwo.setOperNo(USER.getRowNo() + "");
                TaskListtwo.setSpendTime("0");
                TaskListtwo.setStatus("1");
                TaskListtwo.setType("SH");
                TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                TransferTask TaskBeantwo = tInformationService.saveTaskList(TaskListtwo);// 保存流程表信息
                daibanfor(transferInformation, userid, user, "ZF", processId, TaskBeantwo);
            }
            transferInformation.setHANDLER_ID(userid);
            tInformationService.update(transferInformation);
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            Write("No");
        }
    }

    //完成代办生成
    public void daibanfor(TransferInformation transferInformation, String userid, SystemUser user, String type, String processId, TransferTask transferTask) {
        WaitTask wt = new WaitTask();
        wt.setName("[转账]" + transferInformation.getTransferTitle());
        wt.setCreationTime(new Date());
//        wt.setUrl("jsp/transfer/completeTransferTwo.jsp?id=" + transferInformation.getId() + "&type=" + type + "&processId=" + processId + "&transferTaskID="
//                + transferTask.getUuid());// 这是需要改动的退回以后的页面地址
        wt.setUrl("jsp/transfer/completeTransferTwo.jsp?type=" + type);
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
        wt.setState(WaitTask.HANDLE);
        wt.setHandleUserId(USER.getRowNo());
        wt.setHandleUserName(USER.getEmployeeName());
        wt.setHandleLoginName(USER.getLoginName());
        wt.setCreateUserId(user.getRowNo());
        wt.setCreateUserName(user.getEmployeeName());
        wt.setCreateLoginName(user.getLoginName());
        wt.setCode("ZZ");
        wt.setTaskId(transferTask.getUuid());
        wt.setOrderNo(transferInformation.getTransferNumber());
        service.saveWaitPushMOA(wt, this.getRequest());

    }

    public void updateBus_order() {
        List<WaitTask> waitask = tInformationService.getWaitTask();
        for (int i = 0; i < waitask.size(); i++) {
            String waitname = waitask.get(i).getName();
            String[] name = waitname.split("]");
            OrderTask ordertask = tInformationService.getOrderTask(name[1]);
            OrderForm orderForm = tInformationService.getOrderForm(name[1]);
            if (ordertask != null) {
                if (orderForm != null) {
                    tInformationService.saveBus_order(ordertask.getBusCode(), orderForm.getOrderNumber());
                }
            }
        }
    }

    /**
     * 根据ID查询跟踪处理的省公司管理员以及
     * 省公司政企业务管理室2次
     *
     * @return
     */
    public void getTransferTask() {
        try {
            String id = getString("id");
            Integer rolename = getInteger("rolename");
            String name = "";
            if (rolename == 1) {
                name = "省公司管理员";
            } else {
                name = "省公司政企业务管理室2次";
            }
            TransferTask p = tInformationService.getTransferTask(id, name);
            Write(JSONHelper.SerializeWithNeedAnnotation(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }

    public void transferInformationList() {
        try {
            String id = getString("id");
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            List<TransferAccountInformation> transferAccountInformation = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(transferAccountInformation));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }

    public void downLoadtransferInformation() {
        try {
            Integer type = getInteger("type");
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest req = ServletActionContext.getRequest();
            if (type != 3) {
                String name = "账户信息模板";
                String filepath = req.getRealPath("/template/transferInformation.xlsx");
                byte[] data = FileUtil.toByteArray(filepath);
                String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
                response.reset();
                response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream;charset=UTF-8");
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();
            } else {
                String name = "账户信息模板";
                String filepath = req.getRealPath("/template/transferInformationtwo.xlsx");
                byte[] data = FileUtil.toByteArray(filepath);
                String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
                response.reset();
                response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream;charset=UTF-8");
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("转账模板下载错误信息：" + e.getMessage(), e);
        }
    }

    /**
     * 日期转换
     *
     * @param
     * @return
     */
    public static String getStringDatethree() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, 2);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

    public void getTransferCitiesData() {
        try {
            String dangqianrenwu = getString("dangqianrenwu");
            List<SystemDept> deptList = user.getSystemDept();
            String code = deptList.get(0).getSystemCompany().getCompanyCode();
            TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, dangqianrenwu);
            Write(JSONHelper.SerializeWithNeedAnnotation(transferCitiesData));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }

    public void getTransferCitiesDataByOrderCode() {
        try {
            String dangqianrenwu = getString("dangqianrenwu");
            String customerManager = getString("customerManager");
            List<Map<String, Object>> list = tInformationService.getCountyByUserID(customerManager);
            String code = list.get(0).get("COMPANY_CODE").toString();
            TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, dangqianrenwu);
            Write(JSONHelper.SerializeWithNeedAnnotation(transferCitiesData));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }

    /**
     * 调用boss接口查询账户信息
     */
    public void setTransferw() {
        try {
            String id = getString("id");
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            String s = setTransfer(transferInformation, user);
            Write(s);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 调用boss接口查询账户信息
     */
    public String setTransfer(TransferInformation transferInformation, SystemUser user) {
        HttpPost post = new HttpPost("http://10.113.183.13:8080/EOM/AuditWorksheetHttpAction_aaaaa.action");
        String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
        //String ESB_URL="http://*************:52000/esbWS/rest/";//测试
        try {
            String url = ESB_URL + "s3103Cfm";
            JSONObject object = new JSONObject();
            object.put("LOGIN_NO", user.getBossUserName());
            object.put("OP_CODE", "I");
            object.put("IN_MSG", getTransferAccountInformation(transferInformation));
            object.put("REMARK", transferInformation.getTransferNumber());
			/*if(transferInformation.getTransferInstructions()==null){
				object.put("REMARK","无");
			}else{
				object.put("REMARK",transferInformation.getTransferInstructions());
			}*/
            String json = setParamObj(object);
            System.out.println("转账调用接口输入参数：" + json);
            String jsonString = UrlConnection.responseGBK(url, json.toString());
            System.out.println("转账调用接口返回参数：" + jsonString);
            return jsonString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            post.releaseConnection();
        }
    }

    /**
     * 根据转账编号查询账户信息
     *
     * @throws ParseException
     */
    public Object getTransferAccountInformation(TransferInformation transferInformation) throws ParseException {
        List<TransferAccountInformation> transferAccountInformationList = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
        JSONArray jsonList = new JSONArray();
        if (Integer.parseInt(transferInformation.getTransferType()) == 2) {
            for (int i = 0; i < transferAccountInformationList.size(); i++) {
                Map<String, Object> object = new HashMap<String, Object>();
                object.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                object.put("OUT_CONTRACT_NO", transferAccountInformationList.get(i).getTurnAccountNumber());//转出账户号码
                object.put("IN_GROUP_NAME", transferAccountInformationList.get(i).getTransferAccounName());//转入集团名称
                object.put("IN_UNIT_ID", transferAccountInformationList.get(i).getTransferAccountPhone());//转入集团编码(手机号码)
                object.put("IN_CONTRACT_NO", transferAccountInformationList.get(i).getTransferAccountNumber());//转入账户号码
                object.put("EFF_DATE", getStringDateThree(transferInformation.getApplyTime()));//生效时间
                object.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
				/*Double price=Double.parseDouble(transferAccountInformationList.get(i).getAmountOfMoney());
				DecimalFormat df = new DecimalFormat("#.00");
		        price = Double.valueOf(df.format(price));
		        int money = (int)(price * 100);*/
                NumberFormat format = NumberFormat.getInstance();
                try {
                    Number number = format.parse(transferAccountInformationList.get(i).getAmountOfMoney());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    object.put("LIMIT_FEE", amount);//转账金额
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                if (transferAccountInformationList.get(i).getAccountNumber() == null) {
                    String accountNumber = transferInformation.getTransferNumber() + i;
                    transferAccountInformationList.get(i).setAccountNumber(accountNumber);
                    TransferAccountInformation transferAccount = tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                    object.put("LOGIN_ACCEPT", transferAccount.getAccountNumber());//规则流水
                } else {
                    object.put("LOGIN_ACCEPT", transferAccountInformationList.get(i).getAccountNumber());//规则流水
                }
                jsonList.add(object);
            }
        } else {
            for (int i = 0; i < transferAccountInformationList.size(); i++) {
                Map<String, Object> object = new HashMap<String, Object>();
                object.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                object.put("OUT_CONTRACT_NO", transferAccountInformationList.get(i).getTurnAccountNumber());//转出账户号码
                object.put("IN_GROUP_NAME", transferInformation.getIntoGroupName());//转入集团名称
                object.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                object.put("IN_CONTRACT_NO", transferAccountInformationList.get(i).getTransferAccountNumber());//转入账户号码
                object.put("EFF_DATE", getStringDateThree(transferInformation.getApplyTime()));//生效时间
                object.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
				/*Double price=Double.parseDouble(transferAccountInformationList.get(i).getAmountOfMoney());
				DecimalFormat df = new DecimalFormat("#.00");
		        price = Double.valueOf(df.format(price));
		        int money = (int)(price * 100);
				object.put("LIMIT_FEE",money);//转账金额*/
                NumberFormat format = NumberFormat.getInstance();
                try {
                    Number number = format.parse(transferAccountInformationList.get(i).getAmountOfMoney());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    object.put("LIMIT_FEE", amount);//转账金额
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                if (transferAccountInformationList.get(i).getAccountNumber() == null) {
                    String accountNumber = transferInformation.getTransferNumber() + i;
                    transferAccountInformationList.get(i).setAccountNumber(accountNumber);
                    TransferAccountInformation transferAccount = tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                    object.put("LOGIN_ACCEPT", transferAccount.getAccountNumber());//规则流水
                } else {
                    object.put("LOGIN_ACCEPT", transferAccountInformationList.get(i).getAccountNumber());//规则流水
                }
                jsonList.add(object);
            }
        }
        JSONObject objectjson = new JSONObject();
        objectjson.put("TRANS_INFO", jsonList);
        return objectjson;
    }


    public String setTransferTwo(TransferInformation transferInformation, SystemUser user) {
        HttpPost post = new HttpPost("http://10.113.183.13:8080/EOM/AuditWorksheetHttpAction_aaaaa.action");
        String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
        //String ESB_URL = "http://*************:52000/esbWS/rest/";//测试
        try {
            String url = ESB_URL + "s3103Cfm";
            JSONObject object = new JSONObject();
            object.put("LOGIN_NO", user.getBossUserName());
            object.put("OP_CODE", "I");
            object.put("IN_MSG", getTransferAccountInformationTwo(transferInformation));
            object.put("REMARK", transferInformation.getTransferNumber());
			/*if(transferInformation.getTransferInstructions()==null){
				object.put("REMARK","无");
			}else{
				object.put("REMARK",transferInformation.getTransferInstructions());
			}*/
            String json = setParamObj(object);
            System.out.println("转账调用接口输入参数：" + json);
            String jsonString = UrlConnection.responseGBK(url, json.toString());
            System.out.println("转账调用接口返回参数：" + jsonString);
            return jsonString.toString();
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        } finally {
            post.releaseConnection();
        }
    }

    /**
     * 根据转账编号查询账户信息
     *
     * @throws ParseException
     */
    public Object getTransferAccountInformationTwo(TransferInformation transferInformation) throws ParseException {
        List<TransferAccountInformation> transferAccountInformationList = tInformationService.gettransferAccountInformationlistTwo(transferInformation.getTransferNumber());
        JSONArray jsonList = new JSONArray();
        if (Integer.parseInt(transferInformation.getTransferType()) == 2) {
            for (int i = 0; i < transferAccountInformationList.size(); i++) {
                Map<String, Object> object = new HashMap<String, Object>();
                object.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                object.put("OUT_CONTRACT_NO", transferAccountInformationList.get(i).getTurnAccountNumber());//转出账户号码
                object.put("IN_GROUP_NAME", transferAccountInformationList.get(i).getTransferAccounName());//转入集团名称
                object.put("IN_UNIT_ID", transferAccountInformationList.get(i).getTransferAccountPhone());//转入集团编码(手机号码)
                object.put("IN_CONTRACT_NO", transferAccountInformationList.get(i).getTransferAccountNumber());//转入账户号码
                object.put("EFF_DATE", getStringDateThree(transferInformation.getApplyTime()));//生效时间
                object.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
				/*Double price=Double.parseDouble(transferAccountInformationList.get(i).getAmountOfMoney());
				DecimalFormat df = new DecimalFormat("#.00");
		        price = Double.valueOf(df.format(price));
		        int money = (int)(price * 100);*/
                NumberFormat format = NumberFormat.getInstance();
                try {
                    Number number = format.parse(transferAccountInformationList.get(i).getAmountOfMoney());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    object.put("LIMIT_FEE", amount);//转账金额
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                if (transferAccountInformationList.get(i).getAccountNumber() == null) {
                    String accountNumber = transferInformation.getTransferNumber() + i;
                    transferAccountInformationList.get(i).setAccountNumber(accountNumber);
                    TransferAccountInformation transferAccount = tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                    object.put("LOGIN_ACCEPT", transferAccount.getAccountNumber());//规则流水
                } else {
                    object.put("LOGIN_ACCEPT", transferAccountInformationList.get(i).getAccountNumber());//规则流水
                }
                jsonList.add(object);
            }
        } else {
            for (int i = 0; i < transferAccountInformationList.size(); i++) {
                Map<String, Object> object = new HashMap<String, Object>();
                object.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                object.put("OUT_CONTRACT_NO", transferAccountInformationList.get(i).getTurnAccountNumber());//转出账户号码
                object.put("IN_GROUP_NAME", transferInformation.getIntoGroupName());//转入集团名称
                object.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                object.put("IN_CONTRACT_NO", transferAccountInformationList.get(i).getTransferAccountNumber());//转入账户号码
                object.put("EFF_DATE", getStringDateThree(transferInformation.getApplyTime()));//生效时间
                object.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
				/*Double price=Double.parseDouble(transferAccountInformationList.get(i).getAmountOfMoney());
				DecimalFormat df = new DecimalFormat("#.00");
		        price = Double.valueOf(df.format(price));
		        int money = (int)(price * 100);
				object.put("LIMIT_FEE",money);//转账金额*/
                NumberFormat format = NumberFormat.getInstance();
                try {
                    Number number = format.parse(transferAccountInformationList.get(i).getAmountOfMoney());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    object.put("LIMIT_FEE", amount);//转账金额
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                if (transferAccountInformationList.get(i).getAccountNumber() == null) {
                    String accountNumber = transferInformation.getTransferNumber() + i;
                    transferAccountInformationList.get(i).setAccountNumber(accountNumber);
                    TransferAccountInformation transferAccount = tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                    object.put("LOGIN_ACCEPT", transferAccount.getAccountNumber());//规则流水
                } else {
                    object.put("LOGIN_ACCEPT", transferAccountInformationList.get(i).getAccountNumber());//规则流水
                }
                jsonList.add(object);
            }
        }
        JSONObject objectjson = new JSONObject();
        objectjson.put("TRANS_INFO", jsonList);
        return objectjson;
    }

    /**
     * json 数据格式化：
     *
     * @param body
     * @return
     */
    protected String setParamObj(JSONObject body) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "15");
        routing.put("ROUTE_VALUE", "11");
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        System.out.println(root.toString());
        return root.toString();
    }

    protected String setParamObjBossNo(JSONObject body, String bossNo) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "14");
        routing.put("ROUTE_VALUE", bossNo);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        System.out.println(root.toString());
        return root.toString();
    }


    /**
     * 查询账户信息
     * "outGroup":outGroup,"inGroup":inGroup,"turnAccountNumber":turnAccountNumber,"transferAccountNumber":transferAccountNumber
     */
    public void findPreinvApplyDet() {
        try {
            String outGroup = getString("outGroup");//转出
            String inGroup = getString("inGroup");//转入
            String turnAccountNumber = getString("turnAccountNumber");//转出
            String transferAccountNumber = getString("transferAccountNumber");//转入
            String bossNo = user.getBossUserName();// boss工号
            String phone = user.getMobile();// 手机号
            String busiType = getString("busiType");// 查询类型
            Integer retrieve = getInteger("retrieve");//转账类型
            Integer tabLenCode = getInteger("tabLenCode");
            int count = 0;
            String resultMsg = "";
            if (retrieve == 3) {
                String json = findPreinvApplyDet(outGroup, bossNo, phone, turnAccountNumber, busiType);
                JSONObject jsthree = JSONObject.fromObject(json);
                String datatwo = jsthree.getString("res");
                System.out.println("这是返回数据解析结果" + datatwo);
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    ++count;
                }
                if (count == 1) {
                    resultMsg = "YES";
                } else {
                    resultMsg = "第" + tabLenCode + "行转出集团" + outGroup + "和转出账户" + turnAccountNumber + "不匹配，请确认";
                }
            } else {
                for (int i = 0; i < 2; i++) {
                    if (i == 0) {
                        String json = findPreinvApplyDet(outGroup, bossNo, phone, turnAccountNumber, busiType);
                        JSONObject jsthree = JSONObject.fromObject(json);
                        String datatwo = jsthree.getString("res");
                        System.out.println("这是返回数据解析结果" + datatwo);
                        JSONObject jsone = JSONObject.fromObject(datatwo);
                        JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                        if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                            ++count;
                        } else {
                            break;
                        }
                    } else {
                        String json = findPreinvApplyDet(inGroup, bossNo, phone, transferAccountNumber, busiType);
                        JSONObject jsthree = JSONObject.fromObject(json);
                        String datatwo = jsthree.getString("res");
                        System.out.println("这是返回数据解析结果" + datatwo);
                        JSONObject jsone = JSONObject.fromObject(datatwo);
                        JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                        if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                            ++count;
                        } else {
                            break;
                        }
                    }
                }
                if (count == 2) {
                    resultMsg = "YES";
                } else if (count == 0) {
                    resultMsg = "第" + tabLenCode + "行转出集团" + outGroup + "和转出账户" + turnAccountNumber + "不匹配，请确认";
                } else {
                    resultMsg = "第" + tabLenCode + "行转入集团" + inGroup + "和转入账户" + transferAccountNumber + "不匹配，请确认";
                }
            }
			/*String json = "";
			if (busiType.equals("A")) {
				json = "{ \"ROOT\": { \"RETURN_CODE\": 0, \"RETURN_MSG\": \"ok!\", \"USER_MSG\": \"处理成功!\", \"DETAIL_MSG\": \"OK!\", \"PROMPT_MSG\": \"\", \"OUT_DATA\": { \"CONTRACT_INFO\": [ { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"定额超出归个人\" }, { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费\" }, { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费\" }, { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费(不欠费)\", \"PRODUCT_NAME\": \"集团ADC业务\" }, { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费\" }, { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费(不欠费)\" }, { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费(不欠费)\" }, { \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费(不欠费)\" } ], \"UNIT_NAME\": \"脱敏\", \"TAXPAYER_INFO\": [ { \"TAXPAYER_NUM\": \"************\", \"TAXPAYER_ADDRESS\": \"成都市青羊区人民南路一段86号7楼\", \"TAXPAYER_PHONE\": \"028-********\", \"TAXPAYER_BANK_NAME\": \"工行盐市口支行营业室\", \"TAXPAYER_BANK_ACCOUNT\": \"4402902029100150717\" }, { \"TAXPAYER_NUM\": \"************\", \"TAXPAYER_ADDRESS\": \"绵阳市经济技术开发区洪恩东路68号\", \"TAXPAYER_PHONE\": \"1\", \"TAXPAYER_BANK_NAME\": \"上海浦东发展银行绵阳支行\", \"TAXPAYER_BANK_ACCOUNT\": \"*****************\" }, { \"TAXPAYER_NUM\": \"************\", \"TAXPAYER_ADDRESS\": \"成都市青羊区人民南路一段86号7楼\", \"TAXPAYER_PHONE\": \"028-********\", \"TAXPAYER_BANK_NAME\": \"工行盐市口支行营业室\", \"TAXPAYER_BANK_ACCOUNT\": \"4402902029100150717\" } ] } } }";
			} else if (busiType.equals("B")) {
				json = "{ \"ROOT\": { \"RETURN_CODE\": 0, \"RETURN_MSG\": \"ok!\", \"USER_MSG\": \"处理成功!\", \"DETAIL_MSG\": \"OK!\", \"PROMPT_MSG\": \"\", \"OUT_DATA\": { \"FLAG\": \"Y\", \"CONTRACT_NO\": **************, \"CONTRACTATT_TYPE\": \"集团统一付费\", \"TAXPAYER_INFO\": [ { \"TAXPAYER_NUM\": \"************\", \"TAXPAYER_ADDRESS\": \"成都市青羊区人民南路一段86号7楼\", \"TAXPAYER_PHONE\": \"028-********\", \"TAXPAYER_BANK_NAME\": \"工行盐市口支行营业室\", \"TAXPAYER_BANK_ACCOUNT\": \"4402902029100150717\" }, { \"TAXPAYER_NUM\": \"************\", \"TAXPAYER_ADDRESS\": \"绵阳市经济技术开发区洪恩东路68号\", \"TAXPAYER_PHONE\": \"1\", \"TAXPAYER_BANK_NAME\": \"上海浦东发展银行绵阳支行\", \"TAXPAYER_BANK_ACCOUNT\": \"*****************\" }, { \"TAXPAYER_NUM\": \"************\", \"TAXPAYER_ADDRESS\": \"成都市青羊区人民南路一段86号7楼\", \"TAXPAYER_PHONE\": \"028-********\", \"TAXPAYER_BANK_NAME\": \"工行盐市口支行营业室\", \"TAXPAYER_BANK_ACCOUNT\": \"4402902029100150717\" } ] } } }";
			}*/
            Write(resultMsg);
            //Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 调用boss接口查询账户信息
     */
    public String findPreinvApplyDet(String groupCode, String bossNo, String phone, String contractNo, String busiType) {
        String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
        //String ESB_URL="http://*************:52000/esbWS/rest/";//测试
        String url = ESB_URL + "sTaxpayerNumberQry";
        JSONObject object = new JSONObject();
        System.out.println("账户号码：" + contractNo);
        System.out.println("集团280：" + groupCode);
        if (contractNo == null || "".equals(contractNo)) {
            object.put("LOGIN_NO", bossNo);
        } else {
            object.put("CONTRACT_NO", contractNo);
        }
        object.put("UNIT_ID", Long.parseLong(groupCode));
        object.put("BUSI_TYPE", "B");
        String json = setParamObj1(object, phone);
        String jsonString = UrlConnection.responseGBK(url, json);
        System.out.println(jsonString);
        return jsonString.toString();
    }

    protected String setParamObj1(JSONObject body, String phone) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "10");
        routing.put("ROUTE_VALUE", phone);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        System.out.println(root.toString());
        return root.toString();
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDateThree(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    public static String getStringDateFive(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     * @throws ParseException
     */
    public static Date getStringDateFour(String currentTime) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        Date dateString = formatter.parse(currentTime);
        return dateString;
    }

    public void getWaiTask() {
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            if (waitId == null || waitId.equals("")) {
                WaitTask waitTask = tInformationService.getWaitTask(id);
                waitId = waitTask.getWaitId();
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                Write("OK");
            } else {
                Write("NO");
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NON");
        }
    }


    /**
     * 调用boss接口
     */
    public void setTransferdelete() {
        try {
            String transferNumber = getString("transferNumber");// 账户信息id
            TransferInformation transferInformation = tInformationService.getTransferInformationtransferNumber(transferNumber);
            System.out.println("失效时间：" + transferInformation.getExpTime());
            System.out.println("判断失效时间：" + transferInformation.getExpTime() == null);
            if (transferInformation.getExpTime() == null) {
                Write("NON");
            } else {
                SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
                String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
                //String ESB_URL="http://*************:52000/esbWS/rest/";//测试
                String url = ESB_URL + "s3103Cfm";
                JSONObject object = new JSONObject();
                object.put("LOGIN_NO", user.getBossUserName());
                object.put("OP_CODE", "D");
                object.put("IN_MSG", getTransferAccountInformation(transferInformation));
                object.put("REMARK", transferInformation.getTransferNumber());
                String json = setParamObj(object);
                System.out.println("转账调用接口输入参数：" + json);
                String jsonString = UrlConnection.responseGBK(url, json.toString());
                System.out.println("转账调用接口返回参数：" + jsonString);
                JSONObject jsthree = JSONObject.fromObject(jsonString);
                String datatwo = jsthree.getString("res");
                System.out.println("这是返回数据解析结果" + datatwo);
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    transferInformation.setState(4);
                    tInformationService.update(transferInformation);
                    Write("OK");
                } else {
                    Write(jstwo.getString("DETAIL_MSG"));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 调用boss接口
     */
    public void setTransferdeleteone() {
        try {
            String transferNumber = getString("transferNumber");// 账户信息id
            TransferInformation transferInformation = tInformationService.getTransferInformationtransferNumber(transferNumber);
            System.out.println("失效时间：" + transferInformation.getExpTime());
            System.out.println("判断失效时间：" + transferInformation.getExpTime() == null);
            if (transferInformation.getExpTime() == null) {
                Write("NON");
            } else {
                SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
                String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
                //String ESB_URL="http://*************:52000/esbWS/rest/";//测试
                String url = ESB_URL + "s3103Cfm";
                JSONObject object = new JSONObject();
                object.put("LOGIN_NO", user.getBossUserName());
                object.put("OP_CODE", "D");
                object.put("IN_MSG", getTransferAccountInformation(transferInformation));
                object.put("REMARK", transferInformation.getTransferNumber());
                String json = setParamObj(object);
                System.out.println("转账调用接口输入参数：" + json);
                String jsonString = UrlConnection.responseGBK(url, json.toString());
                System.out.println("转账调用接口返回参数：" + jsonString);
                JSONObject jsthree = JSONObject.fromObject(jsonString);
                String datatwo = jsthree.getString("res");
                System.out.println("这是返回数据解析结果" + datatwo);
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    transferInformation.setState(4);
                    tInformationService.update(transferInformation);
                    Write("OK");
                }
                Write(jsonString);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 调用boss接口
     */
    public void setTransferinsert() {
        try {
            String transferNumber = getString("transferNumber");// 账户信息id
            TransferInformation transferInformation = tInformationService.getTransferInformationtransferNumber(transferNumber);
            System.out.println("失效时间：" + transferInformation.getExpTime());
            System.out.println("判断失效时间：" + transferInformation.getExpTime() == null);
            if (transferInformation.getExpTime() == null) {
                Write("NON");
            } else {
                SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
                String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
                //String ESB_URL="http://*************:52000/esbWS/rest/";//测试
                String url = ESB_URL + "s3103Cfm";
                JSONObject object = new JSONObject();
                object.put("LOGIN_NO", user.getBossUserName());
                object.put("OP_CODE", "I");
                object.put("IN_MSG", getTransferAccountInformation(transferInformation));
                object.put("REMARK", transferInformation.getTransferNumber());
                String json = setParamObj(object);
                System.out.println("转账调用接口输入参数：" + json);
                String jsonString = UrlConnection.responseGBK(url, json.toString());
                System.out.println("转账调用接口返回参数：" + jsonString);
                Write(jsonString);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 调用boss接口查询
     * <p>
     * String sss = "{ \"ROOT\": { \"RETURN_CODE\": 0, \"RETURN_MSG\": \"ok!\", \"USER_MSG\": \"ok!\", \"DETAIL_MSG\": \"OK!\", \"PROMPT_MSG\": \"\", \"OUT_DATA\": { \"SUM_DATA_CNT\": 1, \"SUM_PAGE_CNT\": 1,"
     * + " \"PAGE_DATA_CNT\": 1, \"PAGE_NOW\": 1, \"TRANS_LIST\": { \"TRANS_INFO\": { \"LOGIN_ACCEPT\": **************, \"OUT_GROUP_NAME\": \"罗江县新盛镇人民政府\", "
     * + "\"OUT_UNIT_ID\": \"2800003337\", \"OUT_CONTRACT_NO\": 27704000720947, \"IN_GROUP_NAME\": \"龙斌\", \"IN_UNIT_ID\": \"13990212383\","
     * + " \"IN_CONTRACT_NO\": 27702001479727, \"EFF_DATE\": \"******** 09:16:51\", \"EXP_DATE\": \"******** 23:59:59\", \"LIMIT_FEE\": 7059, "
     * + "\"STATU\": \"0\", \"LASTOP_TIME\": \"******** 15:00:31\", \"REMARK\": \"DY********091651112\" } } } } }";
     */
    public void setTransferQuery() {
        try {
            String transferNumber = getString("transferNumber");// 账户信息id
            TransferInformation transferInformation = tInformationService.getTransferInformationtransferNumber(transferNumber);
            List<TransferAccountInformation> transferAccountInformationList = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
            Date s = formatForDate(transferInformation.getExpTime());
            String ss = getStringDatethree(s);
            String ESB_URL = "http://*************:51000/esbWS/rest/";
            String url = ESB_URL + "s3103Cfm";
            OK:
            if (transferAccountInformationList.size() > 1) {
                if (Integer.parseInt(transferInformation.getTransferType()) == 2) {
                    for (int i = 0; i < transferAccountInformationList.size(); i++) {
                        JSONObject object = new JSONObject();
                        object.put("LOGIN_NO", user.getBossUserName());//操作工号
                        object.put("OP_CODE", "Q");//操作代码
                        object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                        object.put("IN_UNIT_ID", transferAccountInformationList.get(i).getTransferAccountPhone());//转入集团编码（手机号码）
                        object.put("YEAR_MONTH", ss);//查询年月yyyymm
                        object.put("STATU", "0");//状态 0未使用 1已使用 9删除
                        object.put("PAGE_DATA_CNT", "1");//每页数据量
                        object.put("PAGE_NOW", "1");//查询页
                        String json = setParamObj(object);
                        String jsonString = UrlConnection.responseGBK(url, json.toString());
                        System.out.println("查询转账接口返回数据：" + jsonString);
                        JSONObject jsthree = JSONObject.fromObject(jsonString);
                        String datatwo = jsthree.getString("res");
                        JSONObject jsone = JSONObject.fromObject(datatwo);
                        JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                        if ("0".equals(jstwo.getString("RETURN_CODE"))) {//OUT_CONTRACT_NO
                            JSONObject jsonlisttwo = JSONObject.fromObject(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("TRANS_LIST"));
                            JSONObject data2 = JSONObject.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                            String login_accept = data2.getString("LOGIN_ACCEPT");
                            String out_contract_no = data2.getString("OUT_CONTRACT_NO");
                            String in_contract_no = data2.getString("IN_CONTRACT_NO");
                            String remark = data2.getString("REMARK");
                            if (out_contract_no.equals(transferAccountInformationList.get(i).getTurnAccountNumber().trim())
                                    && in_contract_no.equals(transferAccountInformationList.get(i).getTransferAccountNumber().trim())) {
                                JSONObject object1 = new JSONObject();
                                object1.put("LOGIN_NO", user.getBossUserName());
                                object1.put("OP_CODE", "D");
                                object1.put("IN_MSG", getTransferAccountInformationTwo(transferInformation, login_accept));
                                object1.put("REMARK", transferInformation.getTransferNumber());
                                String json1 = setParamObj(object1);
                                String jsonString1 = UrlConnection.responseGBK(url, json1.toString());
                                System.out.println("转账调用接口输入参数：" + json);
                                System.out.println("转账调用接口返回参数：" + jsonString1);
                                JSONObject jsthree1 = JSONObject.fromObject(jsonString1);
                                String datatwo1 = jsthree1.getString("res");
                                JSONObject jsone1 = JSONObject.fromObject(datatwo1);
                                JSONObject jstwo1 = JSONObject.fromObject(jsone1.getString("ROOT"));
                                if (!"0".equals(jstwo1.getString("RETURN_CODE"))) {
                                    Write(jstwo1.toString());
                                    break OK;
                                }
                            } else {
                                Write("转出账号为[" + transferAccountInformationList.get(i).getTurnAccountNumber() + "]的号码冲正失败");
                                break OK;
                            }
                        }
                        System.out.println("跨集团转个人转账查询接口第" + i + "条返回数据，手机号码：" + transferAccountInformationList.get(i).getTransferAccountPhone() + ":" + jsonString);
                    }
                    transferInformation.setState(4);
                    tInformationService.update(transferInformation);
                    Write("冲正成功");
                } else {
                    JSONObject object = new JSONObject();
                    object.put("LOGIN_NO", user.getBossUserName());//操作工号
                    object.put("OP_CODE", "Q");//操作代码
                    object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                    object.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                    object.put("YEAR_MONTH", ss);//查询年月 yyyymm
                    object.put("STATU", "0");//状态 0未使用 1已使用 9删除
                    object.put("PAGE_DATA_CNT", transferAccountInformationList.size() + "");//每页数据量
                    object.put("PAGE_NOW", "1");//查询页
                    String json = setParamObj(object);
                    String jsonString = UrlConnection.responseGBK(url, json.toString());
                    JSONObject jsthree = JSONObject.fromObject(jsonString);
                    System.out.println("查询转账接口返回数据：" + jsonString);
                    String datatwo = jsthree.getString("res");
                    JSONObject jsone = JSONObject.fromObject(datatwo);
                    JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                    if ("0".equals(jstwo.getString("RETURN_CODE"))) {//OUT_CONTRACT_NO
                        JSONObject jsonlisttwo = JSONObject.fromObject(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("TRANS_LIST"));
                        JSONObject data2 = JSONObject.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                        for (int i = 0; i < transferAccountInformationList.size(); i++) {
                            String login_accept = data2.getString("LOGIN_ACCEPT");
                            String out_contract_no = data2.getString("OUT_CONTRACT_NO");
                            String in_contract_no = data2.getString("IN_CONTRACT_NO");
                            String remark = data2.getString("REMARK");
                            if (out_contract_no.equals(transferAccountInformationList.get(i).getTurnAccountNumber().trim())
                                    && in_contract_no.equals(transferAccountInformationList.get(i).getTransferAccountNumber().trim())) {
                                JSONObject object1 = new JSONObject();
                                object1.put("LOGIN_NO", user.getBossUserName());
                                object1.put("OP_CODE", "D");
                                object1.put("IN_MSG", getTransferAccountInformationTwo(transferInformation, login_accept));
                                object1.put("REMARK", transferInformation.getTransferNumber());
                                String json1 = setParamObj(object1);
                                String jsonString1 = UrlConnection.responseGBK(url, json1.toString());
                                System.out.println("转账调用接口输入参数：" + json);
                                System.out.println("转账调用接口返回参数：" + jsonString1);
                                JSONObject jsthree1 = JSONObject.fromObject(jsonString1);
                                String datatwo1 = jsthree1.getString("res");
                                JSONObject jsone1 = JSONObject.fromObject(datatwo1);
                                JSONObject jstwo1 = JSONObject.fromObject(jsone1.getString("ROOT"));
                                if (!"0".equals(jstwo1.getString("RETURN_CODE"))) {
                                    Write(jstwo1.toString());
                                    break OK;
                                }
                            } else {
                                Write("转出账号为[" + transferAccountInformationList.get(i).getTurnAccountNumber() + "]的号码冲正失败");
                                break OK;
                            }
                        }
                    }
                    transferInformation.setState(4);
                    tInformationService.update(transferInformation);
                    System.out.println("跨集团;不跨集团转账查询接口返回数据:" + jsonString);
                    Write("冲正成功");
                }
            } else {
                if (Integer.parseInt(transferInformation.getTransferType()) == 2) {
                    for (int i = 0; i < transferAccountInformationList.size(); i++) {
                        JSONObject object = new JSONObject();
                        object.put("LOGIN_NO", user.getBossUserName());//操作工号
                        object.put("OP_CODE", "Q");//操作代码
                        object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                        object.put("IN_UNIT_ID", transferAccountInformationList.get(i).getTransferAccountPhone());//转入集团编码（手机号码）
                        object.put("YEAR_MONTH", ss);//查询年月 yyyymm
                        object.put("STATU", "0");//状态 0未使用 1已使用 9删除
                        object.put("PAGE_DATA_CNT", "1");//每页数据量
                        object.put("PAGE_NOW", "1");//查询页
                        String json = setParamObj(object);
                        String jsonString = UrlConnection.responseGBK(url, json.toString());
                        JSONObject jsthree = JSONObject.fromObject(jsonString);
                        System.out.println("查询转账接口返回数据：" + jsonString);
                        String datatwo = jsthree.getString("res");
                        JSONObject jsone = JSONObject.fromObject(datatwo);
                        JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                        if ("0".equals(jstwo.getString("RETURN_CODE"))) {//OUT_CONTRACT_NO
                            JSONObject jsonlisttwo = JSONObject.fromObject(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("TRANS_LIST"));
                            JSONObject data2 = JSONObject.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                            String login_accept = data2.getString("LOGIN_ACCEPT");
                            String out_contract_no = data2.getString("OUT_CONTRACT_NO");
                            String in_contract_no = data2.getString("IN_CONTRACT_NO");
                            String remark = data2.getString("REMARK");
                            if (out_contract_no.equals(transferAccountInformationList.get(i).getTurnAccountNumber().trim())
                                    && in_contract_no.equals(transferAccountInformationList.get(i).getTransferAccountNumber().trim())) {
                                JSONObject object1 = new JSONObject();
                                object1.put("LOGIN_NO", user.getBossUserName());
                                object1.put("OP_CODE", "D");
                                object1.put("IN_MSG", getTransferAccountInformationTwo(transferInformation, login_accept));
                                object1.put("REMARK", transferInformation.getTransferNumber());
                                String json1 = setParamObj(object1);
                                String jsonString1 = UrlConnection.responseGBK(url, json1.toString());
                                System.out.println("转账调用接口输入参数：" + json);
                                System.out.println("转账调用接口返回参数：" + jsonString1);
                                JSONObject jsthree1 = JSONObject.fromObject(jsonString1);
                                String datatwo1 = jsthree1.getString("res");
                                JSONObject jsone1 = JSONObject.fromObject(datatwo1);
                                JSONObject jstwo1 = JSONObject.fromObject(jsone1.getString("ROOT"));
                                if (!"0".equals(jstwo1.getString("RETURN_CODE"))) {
                                    Write(jstwo1.toString());
                                    break OK;
                                }
                            } else {
                                Write("转出账号为[" + transferAccountInformationList.get(i).getTurnAccountNumber() + "]的号码冲正失败");
                                break OK;
                            }
                        }
                        System.out.println("跨集团转个人转账查询接口第" + i + "条返回数据，手机号码：" + transferAccountInformationList.get(i).getTransferAccountPhone() + ":" + jsonString);
                    }
                    transferInformation.setState(4);
                    tInformationService.update(transferInformation);
                    Write("冲正成功");
                } else {
                    JSONObject object = new JSONObject();
                    object.put("LOGIN_NO", user.getBossUserName());//操作工号
                    object.put("OP_CODE", "Q");//操作代码
                    object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                    object.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                    object.put("YEAR_MONTH", ss);//查询年月 yyyymm
                    object.put("STATU", "0");//状态 0未使用 1已使用 9删除
                    object.put("PAGE_DATA_CNT", transferAccountInformationList.size() + "");//每页数据量
                    object.put("PAGE_NOW", "1");//查询页
                    String json = setParamObj(object);

                    String sssss = "{\"res\":\"{ \"ROOT\": { \"RETURN_CODE\": 0, \"RETURN_MSG\": \"ok!\", \"USER_MSG\": \"ok!\", \"DETAIL_MSG\": \"OK!\", \"PROMPT_MSG\": \"\", \"OUT_DATA\": { \"SUM_DATA_CNT\": 1, \"SUM_PAGE_CNT\": 1, \"PAGE_DATA_CNT\": 1, \"PAGE_NOW\": 1, \"TRANS_LIST\": { \"TRANS_INFO\": { \"LOGIN_ACCEPT\": **************, \"OUT_GROUP_NAME\": \"四川西子电气有限公司\", \"OUT_UNIT_ID\": \"**********\", \"OUT_CONTRACT_NO\": **************, \"IN_GROUP_NAME\": \"四川西子电气有限公司\", \"IN_UNIT_ID\": \"**********\", \"IN_CONTRACT_NO\": **************, \"EFF_DATE\": \"******** 17:41:37\", \"EXP_DATE\": \"******** 23:59:59\", \"LIMIT_FEE\": 2400000, \"STATU\": \"0\", \"LASTOP_TIME\": \"******** 17:13:12\", \"REMARK\": \"TFXQ********174137267\" } } } } }\",\"Status\":1}";


                    String jsonString = UrlConnection.responseGBK(url, json.toString());
                    System.out.println("查询转账接口返回数据：" + jsonString);
                    JSONObject jsthree = JSONObject.fromObject(jsonString);
                    String datatwo = jsthree.getString("res");
                    JSONObject jsone = JSONObject.fromObject(datatwo);
                    JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                    if ("0".equals(jstwo.getString("RETURN_CODE"))) {//OUT_CONTRACT_NO
                        JSONObject jsonlisttwo = JSONObject.fromObject(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("TRANS_LIST"));
                        JSONObject data2 = JSONObject.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                        String login_accept = data2.getString("LOGIN_ACCEPT");
                        String out_contract_no = data2.getString("OUT_CONTRACT_NO");
                        String in_contract_no = data2.getString("IN_CONTRACT_NO");
                        String remark = data2.getString("REMARK");
						/*System.out.println("OUT_CONTRACT_NO:"+out_contract_no);
						System.out.println("本地转出："+transferAccountInformationList.get(0).getTurnAccountNumber());
						System.out.println("IN_CONTRACT_NO："+in_contract_no);
						System.out.println("本地转入："+transferAccountInformationList.get(0).getTransferAccountNumber());
						System.out.println(out_contract_no.equals(transferAccountInformationList.get(0).getTurnAccountNumber().trim())
								&& in_contract_no.equals(transferAccountInformationList.get(0).getTransferAccountNumber().trim()));*/

                        if (out_contract_no.equals(transferAccountInformationList.get(0).getTurnAccountNumber().trim())
                                && in_contract_no.equals(transferAccountInformationList.get(0).getTransferAccountNumber().trim())) {
                            JSONObject object1 = new JSONObject();
                            object1.put("LOGIN_NO", user.getBossUserName());
                            object1.put("OP_CODE", "D");
                            object1.put("IN_MSG", getTransferAccountInformationTwo(transferInformation, login_accept));
                            object1.put("REMARK", transferInformation.getTransferNumber());
                            String json1 = setParamObj(object1);
                            String jsonString1 = UrlConnection.responseGBK(url, json1.toString());
                            System.out.println("转账调用接口输入参数：" + json);
                            System.out.println("转账调用接口返回参数：" + jsonString1);
                            JSONObject jsthree1 = JSONObject.fromObject(jsonString1);
                            String datatwo1 = jsthree1.getString("res");
                            JSONObject jsone1 = JSONObject.fromObject(datatwo1);
                            JSONObject jstwo1 = JSONObject.fromObject(jsone1.getString("ROOT"));
                            if (!"0".equals(jstwo1.getString("RETURN_CODE"))) {
                                Write(jstwo1.toString());
                                break OK;
                            }
                        } else {
                            Write("转出账号为[" + transferAccountInformationList.get(0).getTurnAccountNumber() + "]的号码冲正失败");
                            break OK;
                        }
                    }
                    transferInformation.setState(4);
                    tInformationService.update(transferInformation);
                    System.out.println("跨集团;不跨集团转账查询接口返回数据:" + jsonString);
                    Write("冲正成功");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("冲正失败");
        }
    }

    /**
     * 根据转账编号查询账户信息
     *
     * @throws ParseException
     */
    public Object getTransferAccountInformationTwo(TransferInformation transferInformation, String login_accept) throws ParseException {
        List<TransferAccountInformation> transferAccountInformationList = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
        JSONArray jsonList = new JSONArray();
        if (Integer.parseInt(transferInformation.getTransferType()) == 2) {
            for (int i = 0; i < transferAccountInformationList.size(); i++) {
                Map<String, Object> object = new HashMap<String, Object>();
                object.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                object.put("OUT_CONTRACT_NO", transferAccountInformationList.get(i).getTurnAccountNumber());//转出账户号码
                object.put("IN_GROUP_NAME", transferAccountInformationList.get(i).getTransferAccounName());//转入集团名称
                object.put("IN_UNIT_ID", transferAccountInformationList.get(i).getTransferAccountPhone());//转入集团编码(手机号码)
                object.put("IN_CONTRACT_NO", transferAccountInformationList.get(i).getTransferAccountNumber());//转入账户号码
                object.put("EFF_DATE", getStringDateThree(transferInformation.getUpdateTime()));//生效时间
                object.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
                NumberFormat format = NumberFormat.getInstance();
                try {
                    Number number = format.parse(transferAccountInformationList.get(i).getAmountOfMoney());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    object.put("LIMIT_FEE", amount);//转账金额
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                if (transferAccountInformationList.get(i).getAccountNumber() == null) {
                    String accountNumber = transferInformation.getTransferNumber() + i;
                    transferAccountInformationList.get(i).setAccountNumber(accountNumber);
                    TransferAccountInformation transferAccount = tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                    object.put("LOGIN_ACCEPT", login_accept);//规则流水
                } else {
                    object.put("LOGIN_ACCEPT", login_accept);//规则流水
                }
                jsonList.add(object);
            }
        } else {
            for (int i = 0; i < transferAccountInformationList.size(); i++) {
                Map<String, Object> object = new HashMap<String, Object>();
                object.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                object.put("OUT_CONTRACT_NO", transferAccountInformationList.get(i).getTurnAccountNumber());//转出账户号码
                object.put("IN_GROUP_NAME", transferInformation.getIntoGroupName());//转入集团名称
                object.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                object.put("IN_CONTRACT_NO", transferAccountInformationList.get(i).getTransferAccountNumber());//转入账户号码
                object.put("EFF_DATE", getStringDateThree(transferInformation.getUpdateTime()));//生效时间
                object.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
                NumberFormat format = NumberFormat.getInstance();
                try {
                    Number number = format.parse(transferAccountInformationList.get(i).getAmountOfMoney());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    object.put("LIMIT_FEE", amount);//转账金额
                } catch (ParseException e) {
                    e.printStackTrace();
                }
                if (transferAccountInformationList.get(i).getAccountNumber() == null) {
                    String accountNumber = transferInformation.getTransferNumber() + i;
                    transferAccountInformationList.get(i).setAccountNumber(accountNumber);
                    TransferAccountInformation transferAccount = tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                    object.put("LOGIN_ACCEPT", login_accept);//规则流水
                } else {
                    object.put("LOGIN_ACCEPT", login_accept);//规则流水
                }
                jsonList.add(object);
            }
        }
        JSONObject objectjson = new JSONObject();
        objectjson.put("TRANS_INFO", jsonList);
        return objectjson;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatethree(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    public void selectTransThree() {
        String recId = getString("recId");
        String threeDate = getString("threeDate");
        Integer pageNo = getInteger("pageNo");
        Integer pageSize = getInteger("pageSize");
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        String pageStr = tInformationService.selectTransThree(page, recId, threeDate);
        Write(pageStr);
    }

    public void setTransferQueryTwo() {
        try {
            String transferNumber = getString("transferNumber");// 账户信息id
            TransferInformation transferInformation = tInformationService.getTransferInformationtransferNumber(transferNumber);
            List<TransferAccountInformation> transferAccountInformationList = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
            String sss = getStringDatethree(transferInformation.getApplyTime());
            String ESB_URL = "http://*************:51000/esbWS/rest/";
            String url = ESB_URL + "s3103Cfm";
            if (Integer.parseInt(transferInformation.getTransferType()) == 2) {
                for (int i = 0; i < transferAccountInformationList.size(); i++) {
                    int count = 0;
                    int page = 0;
                    JSONObject object = new JSONObject();
                    object.put("LOGIN_NO", user.getBossUserName());//操作工号
                    object.put("OP_CODE", "Q");//操作代码
                    object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                    object.put("IN_UNIT_ID", transferAccountInformationList.get(i).getTransferAccountPhone());//转入集团编码（手机号码）
                    object.put("YEAR_MONTH", sss);//查询年月yyyymm
                    object.put("STATU", "0");//状态 0未使用 1已使用 9删除
                    object.put("PAGE_DATA_CNT", "20");//每页数据量
                    object.put("PAGE_NOW", "1");//查询页
                    String json = setParamObj(object);
                    String jsonString = UrlConnection.responseGBK(url, json.toString());
                    System.out.println("查询转账接口返回数据：" + jsonString);
                    JSONObject jsthree = JSONObject.fromObject(jsonString);
                    String datatwo = jsthree.getString("res");
                    JSONObject jsone = JSONObject.fromObject(datatwo);
                    JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                    if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                        count = Integer.parseInt(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("SUM_DATA_CNT"));//数据总条数
                        page = Integer.parseInt(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("SUM_PAGE_CNT"));//数据总页数
                    } else {
                        Write("查询数据失败！请联系管理员");
                        return;
                    }
                    if (count > 0) {
                        if (page > 0) {
                            for (int j = 1; j <= page; j++) {
                                JSONObject object2 = new JSONObject();
                                object2.put("LOGIN_NO", user.getBossUserName());//操作工号
                                object2.put("OP_CODE", "Q");//操作代码
                                object2.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                                object2.put("IN_UNIT_ID", transferAccountInformationList.get(i).getTransferAccountPhone());//转入集团编码（手机号码）
                                object2.put("YEAR_MONTH", sss);//查询年月 yyyymm
                                object2.put("STATU", "0");//状态 0未使用 1已使用 9删除
                                object2.put("PAGE_DATA_CNT", "20");//每页数据量
                                object2.put("PAGE_NOW", j + "");//查询页
                                String json2 = setParamObj(object2);
                                String jsonString2 = UrlConnection.responseGBK(url, json2.toString());
                                JSONObject jsthree2 = JSONObject.fromObject(jsonString2);
                                System.out.println("查询转账接口返回数据2：" + jsonString2);
                                String datatwo2 = jsthree2.getString("res");
                                JSONObject jsone2 = JSONObject.fromObject(datatwo2);
                                JSONObject jstwo2 = JSONObject.fromObject(jsone2.getString("ROOT"));
                                if ("0".equals(jstwo2.getString("RETURN_CODE"))) {
                                    JSONObject out_data = JSONObject.fromObject(jstwo.getString("OUT_DATA"));
                                    System.out.println("======" + out_data);
                                    JSONObject jsonlisttwo = JSONObject.fromObject(out_data.getString("TRANS_LIST"));
                                    if (transferAccountInformationList.size() == 1) {
                                        JSONObject obj = JSONObject.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                                        String out_contract_no = obj.getString("OUT_CONTRACT_NO");//转出账号
                                        String out_unit_id = obj.getString("OUT_UNIT_ID");//转出集团编码
                                        String in_contract_no = obj.getString("IN_CONTRACT_NO");//转入账号
                                        String in_unit_id = obj.getString("IN_UNIT_ID");//转入集团编码
                                        String eff_date = obj.getString("EFF_DATE");//转入集团编码
                                        String login_accept = obj.getString("LOGIN_ACCEPT");//规则流水
                                        String eff_datesub = eff_date.substring(0, 8);
                                        if (out_contract_no.equals(transferAccountInformationList.get(0).getTurnAccountNumber().trim()) &&
                                                out_unit_id.equals(transferInformation.getOutGroup().trim()) &&
                                                in_contract_no.equals(transferAccountInformationList.get(0).getTransferAccountNumber().trim()) &&
                                                in_unit_id.equals(transferAccountInformationList.get(0).getTransferAccountPhone().trim()) &&
                                                eff_datesub.equals(getStringDateFive(transferInformation.getApplyTime()))) {
                                            transferAccountInformationList.get(0).setBossStatus("1");
                                            transferAccountInformationList.get(0).setLogin_accept(login_accept);
                                            tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(0));
                                        }
                                    } else {
                                        JSONArray data2 = JSONArray.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                                        for (int l = 0; l < data2.size(); l++) {
                                            JSONObject obj = JSONObject.fromObject(data2.get(l));
                                            String out_contract_no = obj.getString("OUT_CONTRACT_NO");//转出账号
                                            String out_unit_id = obj.getString("OUT_UNIT_ID");//转出集团编码
                                            String in_contract_no = obj.getString("IN_CONTRACT_NO");//转入账号
                                            String in_unit_id = obj.getString("IN_UNIT_ID");//转入集团编码
                                            String eff_date = obj.getString("EFF_DATE");//转入集团编码
                                            String login_accept = obj.getString("LOGIN_ACCEPT");//规则流水
                                            String eff_datesub = eff_date.substring(0, 8);
                                            if (out_contract_no.equals(transferAccountInformationList.get(i).getTurnAccountNumber().trim()) &&
                                                    out_unit_id.equals(transferInformation.getOutGroup().trim()) &&
                                                    in_contract_no.equals(transferAccountInformationList.get(i).getTransferAccountNumber().trim()) &&
                                                    in_unit_id.equals(transferAccountInformationList.get(i).getTransferAccountPhone().trim()) &&
                                                    eff_datesub.equals(getStringDateFive(transferInformation.getApplyTime()))) {
                                                transferAccountInformationList.get(i).setBossStatus("1");
                                                transferAccountInformationList.get(i).setLogin_accept(login_accept);
                                                tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(i));
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        Write("未查询到需要冲正的数据！");
                        return;
                    }
                }
                List<TransferAccountInformation> list = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
                List<TransferAccountInformation> isbossStatus = tInformationService.getBossStatus(transferInformation.getTransferNumber());
                if (isbossStatus.size() == 0) {
                    JSONArray jsonList = new JSONArray();
                    for (int i = 0; i < list.size(); i++) {
                        Map<String, Object> object = new HashMap<String, Object>();
                        object.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                        object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                        object.put("OUT_CONTRACT_NO", list.get(i).getTurnAccountNumber());//转出账户号码
                        object.put("IN_GROUP_NAME", list.get(i).getTransferAccounName());//转入集团名称
                        object.put("IN_UNIT_ID", list.get(i).getTransferAccountPhone());//转入集团编码(手机号码)
                        object.put("IN_CONTRACT_NO", list.get(i).getTransferAccountNumber());//转入账户号码
                        object.put("EFF_DATE", getStringDateThree(transferInformation.getApplyTime()));//生效时间
                        object.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
                        NumberFormat format = NumberFormat.getInstance();
                        try {
                            Number number = format.parse(list.get(i).getAmountOfMoney());
                            double temp = number.doubleValue() * 100.0;
                            format.setGroupingUsed(false);
                            //设置返回数的小数部分所允许的最大位数
                            format.setMaximumFractionDigits(0);
                            String amount = format.format(temp);
                            object.put("LIMIT_FEE", amount);//转账金额
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        object.put("LOGIN_ACCEPT", list.get(i).getLogin_accept());//规则流水
                        jsonList.add(object);
                    }
                    JSONObject objectjson = new JSONObject();
                    objectjson.put("TRANS_INFO", jsonList);
                    JSONObject object1 = new JSONObject();
                    object1.put("LOGIN_NO", user.getBossUserName());
                    object1.put("OP_CODE", "D");
                    object1.put("IN_MSG", objectjson);
                    object1.put("REMARK", transferInformation.getTransferNumber());
                    String json1 = setParamObj(object1);
                    String jsonString1 = UrlConnection.responseGBK(url, json1.toString());
                    System.out.println("转账调用冲正接口输入参数：" + json1);
                    System.out.println("转账调用冲正接口返回参数：" + jsonString1);
                    JSONObject jsthree1 = JSONObject.fromObject(jsonString1);
                    String datatwo1 = jsthree1.getString("res");
                    JSONObject jsone1 = JSONObject.fromObject(datatwo1);
                    JSONObject jstwo1 = JSONObject.fromObject(jsone1.getString("ROOT"));
                    if (!"0".equals(jstwo1.getString("RETURN_CODE"))) {
                        Write(jstwo1.toString());
                    } else {
                        transferInformation.setState(4);
                        tInformationService.update(transferInformation);
                        Write("冲正成功");
                    }
                } else {
                    transferInformation.setState(5);
                    tInformationService.update(transferInformation);
                    Write("null");
                }
            } else {
                JSONObject object = new JSONObject();
                object.put("LOGIN_NO", user.getBossUserName());//操作工号
                object.put("OP_CODE", "Q");//操作代码
                object.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                object.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                object.put("YEAR_MONTH", sss);//查询年月 yyyymm
                object.put("STATU", "0");//状态 0未使用 1已使用 9删除
                object.put("PAGE_DATA_CNT", "20");//每页数据量
                object.put("PAGE_NOW", "1");//查询页
                String json = setParamObj(object);
                String jsonString = UrlConnection.responseGBK(url, json.toString());
                JSONObject jsthree = JSONObject.fromObject(jsonString);
                System.out.println("查询转账接口返回数据1：" + jsonString);
                String datatwo = jsthree.getString("res");
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {//OUT_CONTRACT_NO
                    int count = Integer.parseInt(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("SUM_DATA_CNT"));
                    if (count > 0) {
                        int page = Integer.parseInt(JSONObject.fromObject(jstwo.getString("OUT_DATA")).getString("SUM_PAGE_CNT"));
                        if (page > 0) {
                            for (int j = 1; j <= page; j++) {
                                JSONObject object2 = new JSONObject();
                                object2.put("LOGIN_NO", user.getBossUserName());//操作工号
                                object2.put("OP_CODE", "Q");//操作代码
                                object2.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                                object2.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                                object2.put("YEAR_MONTH", sss);//查询年月 yyyymm
                                object2.put("STATU", "0");//状态 0未使用 1已使用 9删除
                                object2.put("PAGE_DATA_CNT", "20");//每页数据量
                                object2.put("PAGE_NOW", j + "");//查询页
                                String json2 = setParamObj(object2);
                                String jsonString2 = UrlConnection.responseGBK(url, json2.toString());
                                JSONObject jsthree2 = JSONObject.fromObject(jsonString2);
                                System.out.println("查询转账接口返回数据2：" + jsonString2);
                                String datatwo2 = jsthree2.getString("res");
                                JSONObject jsone2 = JSONObject.fromObject(datatwo2);
                                JSONObject jstwo2 = JSONObject.fromObject(jsone2.getString("ROOT"));
                                if ("0".equals(jstwo2.getString("RETURN_CODE"))) {//OUT_CONTRACT_NO
                                    JSONObject out_data = JSONObject.fromObject(jstwo.getString("OUT_DATA"));
                                    System.out.println("======" + out_data);
                                    JSONObject jsonlisttwo = JSONObject.fromObject(out_data.getString("TRANS_LIST"));
                                    if (transferAccountInformationList.size() == 1) {
                                        JSONObject obj = JSONObject.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                                        String out_contract_no = obj.getString("OUT_CONTRACT_NO");//转出账号
                                        String out_unit_id = obj.getString("OUT_UNIT_ID");//转出集团编码
                                        String in_contract_no = obj.getString("IN_CONTRACT_NO");//转入账号
                                        String in_unit_id = obj.getString("IN_UNIT_ID");//转入集团编码
                                        String eff_date = obj.getString("EFF_DATE");//转入集团编码
                                        String eff_datesub = eff_date.substring(0, 8);
                                        String login_accept = obj.getString("LOGIN_ACCEPT");//规则流水
                                        for (int k = 0; k < transferAccountInformationList.size(); k++) {
                                            System.out.println(out_contract_no.equals(transferAccountInformationList.get(k).getTurnAccountNumber().trim()));
                                            System.out.println(out_unit_id.equals(transferInformation.getOutGroup().trim()));
                                            System.out.println(in_contract_no.equals(transferAccountInformationList.get(k).getTransferAccountNumber().trim()));
                                            System.out.println(eff_datesub.equals(getStringDateFive(transferInformation.getApplyTime())));
                                            if (out_contract_no.equals(transferAccountInformationList.get(k).getTurnAccountNumber().trim()) &&
                                                    out_unit_id.equals(transferInformation.getOutGroup().trim()) &&
                                                    in_contract_no.equals(transferAccountInformationList.get(k).getTransferAccountNumber().trim()) &&
                                                    in_unit_id.equals(transferInformation.getIntoGroup().trim()) &&
                                                    eff_datesub.equals(getStringDateFive(transferInformation.getApplyTime()))) {
                                                System.out.println("=========" + login_accept + "===========");
                                                transferAccountInformationList.get(k).setBossStatus("1");
                                                transferAccountInformationList.get(k).setLogin_accept(login_accept);
                                                tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(k));
                                            }
                                        }
                                    } else {
                                        JSONArray data2 = JSONArray.fromObject(jsonlisttwo.getString("TRANS_INFO"));
                                        for (int i = 0; i < data2.size(); i++) {
                                            JSONObject obj = JSONObject.fromObject(data2.get(i));
                                            String out_contract_no = obj.getString("OUT_CONTRACT_NO");//转出账号
                                            String out_unit_id = obj.getString("OUT_UNIT_ID");//转出集团编码
                                            String in_contract_no = obj.getString("IN_CONTRACT_NO");//转入账号
                                            String in_unit_id = obj.getString("IN_UNIT_ID");//转入集团编码
                                            String eff_date = obj.getString("EFF_DATE");//转入集团编码
                                            String login_accept = obj.getString("LOGIN_ACCEPT");//规则流水
                                            String eff_datesub = eff_date.substring(0, 8);
                                            for (int k = 0; k < transferAccountInformationList.size(); k++) {
                                                if (out_contract_no.equals(transferAccountInformationList.get(k).getTurnAccountNumber().trim()) &&
                                                        out_unit_id.equals(transferInformation.getOutGroup().trim()) &&
                                                        in_contract_no.equals(transferAccountInformationList.get(k).getTransferAccountNumber().trim()) &&
                                                        in_unit_id.equals(transferInformation.getIntoGroup().trim()) &&
                                                        eff_datesub.equals(getStringDateFive(transferInformation.getApplyTime()))) {
                                                    transferAccountInformationList.get(k).setBossStatus("1");
                                                    transferAccountInformationList.get(k).setLogin_accept(login_accept);
                                                    tInformationService.updateTransferAccountInformation(transferAccountInformationList.get(k));
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    } else {
                        Write("未查询到需要冲正的数据！");
                        return;
                    }
                } else {
                    Write("查询数据失败！请联系管理员");
                    return;
                }
                List<TransferAccountInformation> list = tInformationService.gettransferAccountInformationlist(transferInformation.getTransferNumber());
                List<TransferAccountInformation> isbossStatus = tInformationService.getBossStatus(transferInformation.getTransferNumber());
                if (isbossStatus.size() == 0) {
                    JSONArray jsonList = new JSONArray();
                    for (int i = 0; i < list.size(); i++) {
                        Map<String, Object> objectone = new HashMap<String, Object>();
                        objectone.put("OUT_GROUP_NAME", transferInformation.getOutGroupName());//转出集团名称
                        objectone.put("OUT_UNIT_ID", transferInformation.getOutGroup());//转出集团编码
                        objectone.put("OUT_CONTRACT_NO", list.get(i).getTurnAccountNumber());//转出账户号码
                        objectone.put("IN_GROUP_NAME", transferInformation.getIntoGroupName());//转入集团名称
                        objectone.put("IN_UNIT_ID", transferInformation.getIntoGroup());//转入集团编码（手机号码）
                        objectone.put("IN_CONTRACT_NO", list.get(i).getTransferAccountNumber());//转入账户号码
                        objectone.put("EFF_DATE", getStringDateThree(transferInformation.getApplyTime()));//生效时间
                        objectone.put("EXP_DATE", getStringDateThree(getStringDateFour(transferInformation.getExpTime())));//失效时间
                        NumberFormat format = NumberFormat.getInstance();
                        try {
                            Number number = format.parse(list.get(i).getAmountOfMoney());
                            double temp = number.doubleValue() * 100.0;
                            format.setGroupingUsed(false);
                            //设置返回数的小数部分所允许的最大位数
                            format.setMaximumFractionDigits(0);
                            String amount = format.format(temp);
                            object.put("LIMIT_FEE", amount);//转账金额
                        } catch (ParseException e) {
                            e.printStackTrace();
                        }
                        objectone.put("LOGIN_ACCEPT", list.get(i).getLogin_accept());//规则流水
                        jsonList.add(objectone);
                    }
                    JSONObject objectjson = new JSONObject();
                    objectjson.put("TRANS_INFO", jsonList);
                    JSONObject object1 = new JSONObject();
                    object1.put("LOGIN_NO", user.getBossUserName());
                    object1.put("OP_CODE", "D");
                    object1.put("IN_MSG", objectjson);
                    object1.put("REMARK", transferInformation.getTransferNumber());
                    String json1 = setParamObj(object1);
                    String jsonString1 = UrlConnection.responseGBK(url, json1.toString());
                    System.out.println("转账调用冲正接口输入参数：" + json1);
                    System.out.println("转账调用冲正接口返回参数：" + jsonString1);
                    JSONObject jsthree1 = JSONObject.fromObject(jsonString1);
                    String datatwo1 = jsthree1.getString("res");
                    JSONObject jsone1 = JSONObject.fromObject(datatwo1);
                    JSONObject jstwo1 = JSONObject.fromObject(jsone1.getString("ROOT"));
                    if (!"0".equals(jstwo1.getString("RETURN_CODE"))) {
                        Write(jstwo1.toString());
                    } else {
                        transferInformation.setState(4);
                        tInformationService.update(transferInformation);
                        Write("冲正成功");
                    }
                } else {
                    transferInformation.setState(5);
                    tInformationService.update(transferInformation);
                    Write("null");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("冲正失败！" + e.getMessage());
        }
    }

    /**
     * @author: liyang
     * @date: 2021/2/24 9:46
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 账户转账BOSS落地接口  现金类预存转账
     */
    public String s8014Cfm(TransferInformation transferInformation, SystemUser user) {
        try {
            String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
            String url = ESB_URL + "s8014Cfm";
            List<TransferAccountInformation> list = tInformationService.gettransferAccountInformationlistTwo(transferInformation.getTransferNumber());
            int count = 0;
            StringBuffer str = new StringBuffer();
            for (TransferAccountInformation TransferAccountInformation : list) {
                JSONObject object1 = new JSONObject();
                object1.put("LOGIN_NO", user.getBossUserName());//findPreinvApplyDet
                object1.put("OP_CODE", "8014");//操作代码 传入8014
                object1.put("PHONE_NO_OUT", TransferAccountInformation.getTranAccountPhone());//转出电话号码
                object1.put("CONTRACT_OUT", TransferAccountInformation.getTurnAccountNumber());//转出账户
                object1.put("PHONE_NO_IN", TransferAccountInformation.getTransferAccountPhone());//转入电话号码
                object1.put("CONTRACT_IN", TransferAccountInformation.getTransferAccountNumber());//转入账户
                object1.put("PAY_TYPE", "0");//转账账本 8014默认0,8022选择
                NumberFormat format = NumberFormat.getInstance();
                Number number = format.parse(TransferAccountInformation.getAmountOfMoney());
                double temp = number.doubleValue() * 100.0;
                format.setGroupingUsed(false);
                //设置返回数的小数部分所允许的最大位数
                format.setMaximumFractionDigits(0);
                String amount = format.format(temp);
                object1.put("TRAN_FEE", amount);//转账金额
                object1.put("TRAN_PATH", "01");//转账渠道
                object1.put("TRAN_METHOD", "0");//转账方式
                object1.put("OP_TYPE", "ZHZZ");//操作类型
                object1.put("OP_NOTE", "订单系统：" + transferInformation.getTransferInstructions());//转账备注
                object1.put("FOREIGN_SN", "");//外部转账流水
                object1.put("FOREIGN_TIME", "");//外部转账时间
                object1.put("SELD_FLAG", "");//
                object1.put("ISNET_FLAG", "");//在离网标识
                object1.put("CONTACT_ID", "");//统一接触流水
                object1.put("TRAN_SN", TransferAccountInformation.getAccountNumber());//转账流水
                object1.put("KDZJ_FLAG", "");//
                object1.put("IF_FLAG", "1");//新增逻辑判断标识 默认传“1”
                String json = setParamObj(object1);
                logger.info("转账调用调整接口输入参数：" + json);
                String returnJson = UrlConnection.responseGBK(url, json);
                logger.info("转账调用调整接口返回参数：" + returnJson);
                JSONObject jsthree = JSONObject.fromObject(returnJson);
                String datatwo = jsthree.getString("res");
                System.out.println("这是返回数据解析结果" + datatwo);
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    ++count;
                    TransferAccountInformation.setBossState("0");
                } else {
                    TransferAccountInformation.setBossState("1");
                    TransferAccountInformation.setBossMsg(jstwo.getString("DETAIL _MSG"));
                    tInformationService.updateTransferAccountInformation(TransferAccountInformation);
                    str.append(TransferAccountInformation.getAccountNumber() + ",");
                }
            }
            if (count == list.size()) {
                return "0";
            } else {
                return str.toString();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "1";
        }
    }

    /**
     * @author: liyang
     * @date: 2021/2/24 9:46
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 账户转账BOSS落地接口  账本类型转账
     */
    public String sSplitPrepayTran(TransferInformation transferInformation, SystemUser user, String op_type) {
        try {
            String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
            String url = ESB_URL + "sSplitPrepayTran";
            List<TransferAccountInformation> list = tInformationService.gettransferAccountInformationlistTwo(transferInformation.getTransferNumber());
            SystemUser userBoss = systemUserService.getByUserInfoRowNo(Integer.parseInt(transferInformation.getCustomerManager()));
            if (list.size() > 0) {
                int count = 0;
                StringBuffer str = new StringBuffer();
                for (TransferAccountInformation TransferAccountInformation : list) {
                    JSONObject object1 = new JSONObject();
                    //object1.put("LOGIN_NO", "aa0010052");//findPreinvApplyDet
                    object1.put("LOGIN_NO", user.getBossUserName());//findPreinvApplyDet
                    object1.put("OP_CODE", "8014");//操作代码 传入8014
                    object1.put("PHONE_NO_OUT", TransferAccountInformation.getTranAccountPhone());//转出电话号码
                    object1.put("CONTRACT_NO_OUT", Long.parseLong(TransferAccountInformation.getTurnAccountNumber()));//转出账户
                    object1.put("PHONE_NO_IN", TransferAccountInformation.getTransferAccountPhone());//转入电话号码
                    object1.put("CONTRACT_NO_IN", Long.parseLong(TransferAccountInformation.getTransferAccountNumber()));//转入账户
                    //object1.put("PAY_TYPE", "0");//转账账本
                    NumberFormat format = NumberFormat.getInstance();
                    Number number = format.parse(TransferAccountInformation.getAmountOfMoney());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    //String ss="订单号[*******************],[**************]转入[**************],金额1000";//订单号[xxx]，[xxx]转入[xxx]，金额XX
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    object1.put("TRANOUT_FEE", Long.parseLong(amount));//转账金额
                    object1.put("PAY_PATH", "01");//转账渠道
                    object1.put("PAY_METHOD", "0");//转账方式
                    //object1.put("OP_TYPE", "ZHZZ");//操作类型
                    object1.put("OP_NOTE", "订单号[" + TransferAccountInformation.getAccountNumber() + "]");//转账备注
                    object1.put("FOREIGN_SN", TransferAccountInformation.getAccountNumber());//外部转账流水
                    object1.put("FOREIGN_TIME", getDateToString(new Date()));//外部转账时间
                    object1.put("ZX_FLAG", TransferAccountInformation.getTransferAccountsType());//转账类型 专线转账1、普通转账0
                    object1.put("OP_TYPE", op_type);//
                    JSONArray jsonArray = new JSONArray();
                    JSONObject tranList = new JSONObject();
                    List<TransferBook> TransferBook = tInformationService.getTransferBook(TransferAccountInformation.getUuid());
                    for (TransferBook book : TransferBook) {
                        JSONObject tranInfo = new JSONObject();
                        NumberFormat formatBook = NumberFormat.getInstance();
                        Number numberBook = formatBook.parse(book.getAmount());
                        double tempBook = numberBook.doubleValue() * 100.0;
                        formatBook.setGroupingUsed(false);
                        //设置返回数的小数部分所允许的最大位数
                        formatBook.setMaximumFractionDigits(0);
                        String amountBook = formatBook.format(tempBook);
                        tranInfo.put("PAY_TYPE", book.getPayType());//转账账本类型
                        tranInfo.put("TRAN_FEE", Long.parseLong(amountBook));//单个账本转账金额
                        jsonArray.add(tranInfo);
                    }
                    tranList.put("TRAN_INFO", jsonArray);
                    object1.put("TRAN_LIST", tranList);
                    if (TransferAccountInformation.getLateFee() == null) {
                        object1.put("DELAY_RATE", Double.parseDouble("0"));
                    } else {
                        object1.put("DELAY_RATE", Double.parseDouble(TransferAccountInformation.getLateFee()));
                    }
                    String json = setParamObjBossNo(object1, userBoss.getBossUserName());
                    logger.info("转账账本类型转账调用调整接口输入参数：" + json);
                    String returnJson = UrlConnection.responseGBK(url, json);
                    //String returnJson = CMCC1000OpenService.getInstance().bdcesPatamss("http://**************:51000/esbWS/rest/sSplitPrepayTran", json);
                    logger.info("转账账本类型转账调用调整接口返回参数：" + returnJson);
                    JSONObject jsthree = JSONObject.fromObject(returnJson);
                    String datatwo = jsthree.getString("res");
                    JSONObject jsone = JSONObject.fromObject(datatwo);
                    JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                    if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                        ++count;
                        TransferAccountInformation.setBossState("0");
                    } else {
                        TransferAccountInformation.setBossState("1");
                        TransferAccountInformation.setBossMsg(jstwo.getString("DETAIL_MSG"));
                        tInformationService.updateTransferAccountInformation(TransferAccountInformation);
                        str.append("转出账户：" + TransferAccountInformation.getTurnAccountNumber() + jstwo.getString("DETAIL_MSG") + ",");
                    }
                }
                if (count == list.size()) {
                    return "0";
                } else {
                    String returnStr = str.toString();
                    returnStr = returnStr.substring(0, returnStr.length() - 1).toString();
                    return returnStr.toString();
                }
            } else {
                return "0";
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return "1";
        }
    }

    /**
     * @author: liyang
     * @date: 2021/4/7 16:30
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询账户可转账金额
     */
    public void sQConPreInfo() {
        try {
            String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
            String url = ESB_URL + "sQConPreInfo";
            String contractNo = getString("contractNo");//11411148970334
            String zxFlag = getString("zxFlag");//11411148970334
            JSONObject object1 = new JSONObject();
            object1.put("CONTRACT_NO", contractNo);
            object1.put("ZX_FLAG", zxFlag);
            String json = setParamObjBossNo(object1, user.getBossUserName());
            logger.info("转账账本类型转账调用调整接口输入参数：" + json);
            String returnJson = UrlConnection.responseGBK(url, json);
            //String returnJson = CMCC1000OpenService.getInstance().bdcesPatamss("http://**************:51000/esbWS/rest/sQConPreInfo", json);//"{\"ROOT\":{\"RETURN_CODE\":0,\"RETURN_MSG\":\"ok!\",\"USER_MSG\":\"ok!\",\"DETAIL_MSG\":\"OK!\",\"PROMPT_MSG\":\"\",\"OUT_DATA\":{\"CUR_BALANCE\":1988,\"OUTMSG\":[{\"PAY_TYPE\":\"v\",\"PAY_TYPE_NAME\":\"营销活动送费\",\"PREPAY_FEE\":1000},{\"PAY_TYPE\":\"0\",\"PAY_TYPE_NAME\":\"现金\",\"PREPAY_FEE\":1888}]}}}";//CMCC1000OpenService.getInstance().bdcesPatamss("http://**************:51000/esbWS/rest/sQConPreInfo", json);
            logger.info("转账账本类型转账调用调整接口返回参数：" + returnJson);
            JSONObject jsthree = JSONObject.fromObject(returnJson);
            String datatwo = jsthree.getString("res");
            JSONObject jsone = JSONObject.fromObject(datatwo);
            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                Write(returnPars(1, jstwo.getString("OUT_DATA"), ""));
            } else {
                Write(returnPars(-1, "", jstwo.getString("DETAIL_MSG")));
            }

            //假数据
//            String jsons = "{ \"ROOT\": { \"RETURN_CODE\": 0, \"RETURN_MSG\": \"ok!\", \"USER_MSG\": \"ok!\", \"DETAIL_MSG\": \"OK!\", \"PROMPT_MSG\": \"\", \"OUT_DATA\": { \"OUTMSG\": [ { \"PAY_TYPE\": \"e\", \"PAY_TYPE_NAME\": \"二卡合一\", \"PREPAY_FEE\": 50 }, { \"PAY_TYPE\": \"0\", \"PAY_TYPE_NAME\": \"现金\", \"PREPAY_FEE\": 641300000000 } ], \"CUR_BALANCE\": 5663 } } }";
//            JSONObject jsone = JSONObject.fromObject(jsons);
//            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
//            Write(returnPars(1, jstwo.getString("OUT_DATA"), ""));


        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            Write(returnPars(-1, "", "查询错误" + e.getMessage()));
        }
    }

//    public String sQConPreInfo(String contractNo, String zxFlag) {
//        try {
//            logger.info("运行假数据");
//            String returnJson = "";
//            if (contractNo.equals("123")) {
//                returnJson = "{\"res\":\"{ \\\"ROOT\\\": { \\\"RETURN_CODE\\\": 0, \\\"RETURN_MSG\\\": \\\"ok!\\\", \\\"USER_MSG\\\": \\\"ok!\\\", \\\"DETAIL_MSG\\\": \\\"OK!\\\", \\\"PROMPT_MSG\\\": \\\"\\\", \\\"OUT_DATA\\\": { \\\"OUTMSG\\\": [ { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 }, { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 },{ \\\"PAY_TYPE\\\": \\\"0\\\", \\\"PAY_TYPE_NAME\\\": \\\"现金\\\", \\\"PREPAY_FEE\\\": 6413 },{ \\\"PAY_TYPE\\\": \\\"0\\\", \\\"PAY_TYPE_NAME\\\": \\\"现金\\\", \\\"PREPAY_FEE\\\": 6413 } ], \\\"CUR_BALANCE\\\": 5663 } } }\",\"Status\":1}";
//            } else if (contractNo.equals("456")) {
//                returnJson = "{\"res\":\"{ \\\"ROOT\\\": { \\\"RETURN_CODE\\\": 0, \\\"RETURN_MSG\\\": \\\"ok!\\\", \\\"USER_MSG\\\": \\\"ok!\\\", \\\"DETAIL_MSG\\\": \\\"OK!\\\", \\\"PROMPT_MSG\\\": \\\"\\\", \\\"OUT_DATA\\\": { \\\"OUTMSG\\\": [ { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 }, { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 },{ \\\"PAY_TYPE\\\": \\\"0\\\", \\\"PAY_TYPE_NAME\\\": \\\"现金\\\", \\\"PREPAY_FEE\\\": 6413 } ], \\\"CUR_BALANCE\\\": 5663 } } }\",\"Status\":1}";
//            } else if (contractNo.equals("789")) {
//                returnJson = "{\"res\":\"{ \\\"ROOT\\\": { \\\"RETURN_CODE\\\": 0, \\\"RETURN_MSG\\\": \\\"ok!\\\", \\\"USER_MSG\\\": \\\"ok!\\\", \\\"DETAIL_MSG\\\": \\\"OK!\\\", \\\"PROMPT_MSG\\\": \\\"\\\", \\\"OUT_DATA\\\": { \\\"OUTMSG\\\": [ { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 }, { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 } ], \\\"CUR_BALANCE\\\": 5663 } } }\",\"Status\":1}";
//            }
//            JSONObject jsthree = JSONObject.fromObject(returnJson);
//            String datatwo = jsthree.getString("res");
//            JSONObject jsone = JSONObject.fromObject(datatwo);
//            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
//            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
//                return jstwo.toString();
//            } else {
//                return ("NO");
//            }
//
//        } catch (Exception e) {
//            logger.error(e.getMessage(), e);
//            Write(returnPars(-1, "", "查询错误" + e.getMessage()));
//            return ("NO");
//        }
//    }

    public String sQConPreInfo(String contractNo, String zxFlag) {
        try {
            String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
            String url = ESB_URL + "sQConPreInfo";
            //String contractNo = getString("contractNo");//11411148970334
            //String zxFlag = getString("zxFlag");//11411148970334
            JSONObject object1 = new JSONObject();
            object1.put("CONTRACT_NO", contractNo);
            object1.put("ZX_FLAG", zxFlag);
            String json = setParamObjBossNo(object1, user.getBossUserName());
            logger.info("转账账本类型转账调用调整接口输入参数：" + json);
            String returnJson = UrlConnection.responseGBK(url, json);
            //String returnJson = CMCC1000OpenService.getInstance().bdcesPatamss("http://**************:51000/esbWS/rest/sQConPreInfo", json);//"{\"ROOT\":{\"RETURN_CODE\":0,\"RETURN_MSG\":\"ok!\",\"USER_MSG\":\"ok!\",\"DETAIL_MSG\":\"OK!\",\"PROMPT_MSG\":\"\",\"OUT_DATA\":{\"CUR_BALANCE\":1988,\"OUTMSG\":[{\"PAY_TYPE\":\"v\",\"PAY_TYPE_NAME\":\"营销活动送费\",\"PREPAY_FEE\":1000},{\"PAY_TYPE\":\"0\",\"PAY_TYPE_NAME\":\"现金\",\"PREPAY_FEE\":1888}]}}}";//CMCC1000OpenService.getInstance().bdcesPatamss("http://**************:51000/esbWS/rest/sQConPreInfo", json);
            logger.info("转账账本类型转账调用调整接口返回参数：" + returnJson);

//           String returnJson = "";
//            if (contractNo.equals("123")) {
//                returnJson = "{\"res\":\"{ \\\"ROOT\\\": { \\\"RETURN_CODE\\\": 0, \\\"RETURN_MSG\\\": \\\"ok!\\\", \\\"USER_MSG\\\": \\\"ok!\\\", \\\"DETAIL_MSG\\\": \\\"OK!\\\", \\\"PROMPT_MSG\\\": \\\"\\\", \\\"OUT_DATA\\\": { \\\"OUTMSG\\\": [ { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 }, { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 },{ \\\"PAY_TYPE\\\": \\\"0\\\", \\\"PAY_TYPE_NAME\\\": \\\"现金\\\", \\\"PREPAY_FEE\\\": 6413 },{ \\\"PAY_TYPE\\\": \\\"0\\\", \\\"PAY_TYPE_NAME\\\": \\\"现金\\\", \\\"PREPAY_FEE\\\": 6413 } ], \\\"CUR_BALANCE\\\": 5663 } } }\",\"Status\":1}";
//            } else if (contractNo.equals("456")) {
//                returnJson = "{\"res\":\"{ \\\"ROOT\\\": { \\\"RETURN_CODE\\\": 0, \\\"RETURN_MSG\\\": \\\"ok!\\\", \\\"USER_MSG\\\": \\\"ok!\\\", \\\"DETAIL_MSG\\\": \\\"OK!\\\", \\\"PROMPT_MSG\\\": \\\"\\\", \\\"OUT_DATA\\\": { \\\"OUTMSG\\\": [ { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 }, { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 },{ \\\"PAY_TYPE\\\": \\\"0\\\", \\\"PAY_TYPE_NAME\\\": \\\"现金\\\", \\\"PREPAY_FEE\\\": 6413 } ], \\\"CUR_BALANCE\\\": 5663 } } }\",\"Status\":1}";
//            } else if (contractNo.equals("789")) {
//                returnJson = "{\"res\":\"{ \\\"ROOT\\\": { \\\"RETURN_CODE\\\": 0, \\\"RETURN_MSG\\\": \\\"ok!\\\", \\\"USER_MSG\\\": \\\"ok!\\\", \\\"DETAIL_MSG\\\": \\\"OK!\\\", \\\"PROMPT_MSG\\\": \\\"\\\", \\\"OUT_DATA\\\": { \\\"OUTMSG\\\": [ { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 }, { \\\"PAY_TYPE\\\": \\\"e\\\", \\\"PAY_TYPE_NAME\\\": \\\"二卡合一\\\", \\\"PREPAY_FEE\\\": 50 } ], \\\"CUR_BALANCE\\\": 5663 } } }\",\"Status\":1}";
//            }

            JSONObject jsthree = JSONObject.fromObject(returnJson);
            String datatwo = jsthree.getString("res");
            JSONObject jsone = JSONObject.fromObject(datatwo);
            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                return jstwo.toString();
            } else {
                return ("NO");
            }
        } catch (Exception e) {
            logger.error("查询账户可转账金额错误==" + e.getMessage(), e);
            return ("NO");
        }
    }

    public void qryPayMent() {
		/*String ESB_URL="http://*************:51000/esbWS/rest/";//正式
		String ESB_URL_172 = "http://**************:51000/esbWS/rest/";
		String url = ESB_URL + "com_sitech_custsvc_atom_inter_IPaymentPlanAoSvc_qryPayMent";
		String PHONE_NO=getString("PHONE_NO");
		JSONObject object = new JSONObject();
		object.put("PHONE_NO", PHONE_NO);
		String json = setParamObj1(object,PHONE_NO);
		logger.info("转账申请号码验证入参："+json);
		String returnJson = UrlConnection.responseGBK(url, json);
		//String returnJson = CMCC1000OpenService.getInstance().bdcesPatamss(url, json);
		logger.info("转账申请号码验证接口返回参数："+returnJson);*/
        String PHONE_NO = getString("PHONE_NO");
        String EXPIRE_FLAG = getString("EXPIRE_FLAG");
        String CONTRACT_NO = getString("CONTRACT_NO");
        String DEAD_FLAG = getString("DEAD_FLAG");

        Result result = TransFerInformationSrv.getInstance().qryPayMent(PHONE_NO, EXPIRE_FLAG, CONTRACT_NO, DEAD_FLAG);
        Write(result.getData().toString());
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getDateToString(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description: TODO 返回参数生成
     */
    private static String returnPars(int state, String data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    /**
     * @author: liyang
     * @date: 2021/4/7 16:30
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询账户可转账金额
     */
    public void getTransferBook() {
        try {
            String id = getString("id");// 账户信息id
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            List<TransferAccountInformation> list = tInformationService.gettransferAccountInformationlistTwo(transferInformation.getTransferNumber());
            int count = 0;
            for (TransferAccountInformation TransferAccountInformation : list) {
                List<TransferBook> TransferBook = tInformationService.getTransferBook(TransferAccountInformation.getUuid());
                for (TransferBook book : TransferBook) {
                    //新增 统一支付微信（W）,招行支付宝（Z），专线预存费率6（22），专线预存费率9（23）
                    if (!"0".equals(book.getPayType()) && !"W".equals(book.getPayType()) && !"Z".equals(book.getPayType())
                            && !"22".equals(book.getPayType()) && !"23".equals(book.getPayType())) {
                        ++count;
                    }
                }
            }
            if (count == 0) {//全部是现金转账
                Write(returnPars(1, "0", ""));
            } else {//不全部是现金转账
                Write(returnPars(1, "1", ""));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            e.printStackTrace();
            Write(returnPars(-1, "", "未知错误" + e.getMessage()));
        }
    }

    /**
     * 新增转账信息
     */
    public void verificationTransfer() {
        try {
            String ESB_URL = "http://*************:51000/esbWS/rest/";//正式
            String url = ESB_URL + "sSplitPrepayTran";
            String transferInstructions = getString("transferInstructions");//转账说明
            String json = getString("json");
            JSONArray jsonObject1 = JSONArray.fromObject(json);
            String str = "";
            int count = 0;
            String IBM = "";
            List<Object[]> sone = tInformationService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            for (int i = 0; i < jsonObject1.size(); i++) {
                String sateTime = getStringDatetwo(new Date());
                String s = jsonObject1.getString(i);
                JSONObject data2 = JSONObject.fromObject(s);
                JSONObject object1 = new JSONObject();
                object1.put("LOGIN_NO", user.getBossUserName());
                object1.put("OP_CODE", "8014");//操作代码 传入8014
                object1.put("PHONE_NO_OUT", data2.getString("tranAccountPhone"));//转出电话号码
                object1.put("CONTRACT_NO_OUT", Long.parseLong(data2.getString("turnAccountNumber")));//转出账户
                object1.put("PHONE_NO_IN", data2.getString("transferAccountPhone"));//转入电话号码
                object1.put("CONTRACT_NO_IN", Long.parseLong(data2.getString("transferAccountNumber")));//转入账户
                //object1.put("PAY_TYPE", "0");//转账账本
                NumberFormat format = NumberFormat.getInstance();
                Number number = format.parse(data2.getString("amountOfMoney"));
                double temp = number.doubleValue() * 100.0;
                format.setGroupingUsed(false);
                //设置返回数的小数部分所允许的最大位数
                format.setMaximumFractionDigits(0);
                String amount = format.format(temp);
                object1.put("TRANOUT_FEE", Long.parseLong(amount));//转账金额
                object1.put("PAY_PATH", "01");//转账渠道
                object1.put("PAY_METHOD", "0");//转账方式
                //object1.put("OP_TYPE", "ZHZZ");//操作类型
                object1.put("OP_NOTE", "订单号[" + IBM + sateTime + "]");//转账备注
                object1.put("FOREIGN_SN", IBM + sateTime);//外部转账流水
                object1.put("FOREIGN_TIME", getDateToString(new Date()));//外部转账时间
                object1.put("OP_TYPE", "1");//
                JSONArray jsonArray = new JSONArray();
                JSONObject tranList = new JSONObject();
                JSONArray accountBookJson = JSONArray.fromObject(data2.getString("accountBookJson"));
                for (int j = 0; j < accountBookJson.size(); j++) {
                    String obj = accountBookJson.getString(j);
                    JSONObject jsonObject = JSONObject.fromObject(obj);
                    if (!jsonObject.getString("AMOUNT").equals("")) {
                        JSONObject tranInfo = new JSONObject();
                        NumberFormat formatBook = NumberFormat.getInstance();
                        Number numberBook = formatBook.parse(jsonObject.getString("AMOUNT"));
                        double tempBook = numberBook.doubleValue() * 100.0;
                        formatBook.setGroupingUsed(false);
                        //设置返回数的小数部分所允许的最大位数s
                        formatBook.setMaximumFractionDigits(0);
                        String amountBook = formatBook.format(tempBook);
                        tranInfo.put("PAY_TYPE", jsonObject.getString("PAY_TYPE"));//转账账本类型
                        tranInfo.put("TRAN_FEE", Long.parseLong(amountBook));//单个账本转账金额
                        jsonArray.add(tranInfo);
                    }
                }
                tranList.put("TRAN_INFO", jsonArray);
                object1.put("TRAN_LIST", tranList);
                if ("".equals(data2.getString("lateFee")) || "null".equals(data2.getString("lateFee"))
                        || "undefined".equals(data2.getString("lateFee")) || data2.getString("lateFee") == null
                ) {
                    object1.put("DELAY_RATE", Double.parseDouble("0"));
                } else {
                    object1.put("DELAY_RATE", Double.parseDouble(data2.getString("lateFee")));
                }

                if (data2.getString("transferAccountsType").equals("普通转账")) {
                    object1.put("ZX_FLAG", "0");
                } else {
                    object1.put("ZX_FLAG", "1");
                }
                String bossJson = setParamObjBossNo(object1, user.getBossUserName());
                logger.info("转账账本类型转账前置校验是否可转账接口输入参数：" + bossJson);
                String returnJson = UrlConnection.responseGBK(url, bossJson);
                //String returnJson = CMCC1000OpenService.getInstance().bdcesPatamss("http://**************:51000/esbWS/rest/sSplitPrepayTran", bossJson);
                logger.info("转账账本类型转账前置校验是否可转账接口返回参数：" + returnJson);
                JSONObject jsthree = JSONObject.fromObject(returnJson);
                String datatwo = jsthree.getString("res");
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    JSONObject outData = JSONObject.fromObject(jstwo.getString("OUT_DATA"));
                    if (outData.getInt("TRAN_FLAG") == 1) {
                        str += "转出账户：" + data2.getString("turnAccountNumber") + "或转入账户：" + data2.getString("transferAccountNumber") + "(" + outData.getString("TRAN_MSG") + "),";
                    } else {
                        ++count;
                    }
                } else {
                    str += "转出账户：" + data2.getString("turnAccountNumber") + "或转入账户：" + data2.getString("transferAccountNumber") + "(" + jstwo.getString("DETAIL_MSG") + "),";
                }
            }
            if (!"".equals(str)) {
                str = str.substring(0, str.length() - 1);
            }
            if (jsonObject1.size() == count && (str.length() == 0 || "".equals(str))) {
                Write(returnPars(1, "0", ""));
            } else {
                if (str.length() > 0) {
                    Write(returnPars(-1, "", str));
                } else {
                    Write(returnPars(1, "", "校验成功"));
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            e.printStackTrace();
            Write(returnPars(-1, "", "未知错误" + e.getMessage()));
        }
    }


    /**
     * 查询已审批转账信息
     */
    public void importtransferInformationTwo() {
        try {
            ExcelUtil excelReader = new ExcelUtil(file1);
            InputStream is = new FileInputStream(file1);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            System.out.println("这是列数" + column);
            //对读取Excel表格内容测试
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            System.out.println("map==" + map);
            for (int i = 1; i <= map.size(); i++) {
                map.get(i).get(0);//URL
                map.get(i).get(1);//地市名称
                map.get(i).get(2);//合同编号
                String url = "http://10.113.156.85:8080/EOM/jsp/pms/pmsSrv_downHdfsByURL.do";
                String line = "";
                String message = "";
                BufferedReader bufferedReader = null;
                org.json.JSONObject outObj = new org.json.JSONObject();
                try {
                    URL urlObject = new URL(url);
                    HttpURLConnection urlConn = (HttpURLConnection) urlObject.openConnection();
					/*urlConn.setDoOutput(true);
					//设定禁用缓存
					urlConn.setRequestProperty("Pragma","no-cache");
					urlConn.setRequestProperty("Cache-Control", "no-cache");
					//维持长连接
					urlConn.setRequestProperty("Connection", "Keep-Alive");
					//设置字符集
					urlConn.setRequestProperty("Charset", "UFT-8");
					//设定输出格式为json
					urlConn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
					//设置使用POST的方式发送
					urlConn.setRequestMethod("POST");
					//设置不使用缓存
					urlConn.setUseCaches(false);
					//设置容许输出
					urlConn.setDoOutput(true);
					//设置容许输入
					urlConn.setDoInput(true);
					urlConn.connect();*/
                    urlConn.setDoOutput(true);
                    urlConn.setDoInput(true);
                    urlConn.setUseCaches(false);
                    // 设置请求方式（GET/POST）
                    urlConn.setRequestMethod("GET");
                    urlConn.setRequestProperty("content-type", "application/x-www-form-urlencoded");
                    OutputStreamWriter outStreamWriter = new OutputStreamWriter(urlConn.getOutputStream(), "UTF-8");
                    //outStreamWriter.write(content);
                    outStreamWriter.write("url=" + "");//参数形式跟在地址栏的一样
                    outStreamWriter.flush();
                    outStreamWriter.close();
                    /*若post失败*/
                    if ((urlConn.getResponseCode() != HttpURLConnection.HTTP_OK)) {
                        InputStream errorStream = urlConn.getErrorStream();
                        if (errorStream != null) {
                            InputStreamReader inputStreamReader = new InputStreamReader(errorStream, "utf-8");
                            bufferedReader = new BufferedReader(inputStreamReader);
                            while ((line = bufferedReader.readLine()) != null) {
                                message += line;
                            }
                            inputStreamReader.close();
                        }
                        errorStream.close();
                        outObj.put("Status", 0);
                        outObj.put("res", message);
                    } else {
                        /*发送成功返回发送成功状态*/
                        InputStream inputStream = urlConn.getInputStream();
                        System.out.println("网络文件大小" + urlConn.getContentLength());
                        if (urlConn.getContentLength() > -1) {
                            byte[] data = toByteArray(inputStream);
                            String fileName = String.valueOf(map.get(i).get(1)) + String.valueOf(map.get(i).get(2));//合同编号;
                            String filePix = "." + FileUpload.getLastFilePix(String.valueOf(map.get(i).get(0)));
                            //String filePix = ".pdf";
                            byteToFile(data, "F:/File/" + fileName + filePix);
                        } else {
                            /**
                             * 文本流
                             */
                            InputStreamReader inputStreamReader = new InputStreamReader(inputStream, "utf-8");
                            bufferedReader = new BufferedReader(inputStreamReader);
                            while ((line = bufferedReader.readLine()) != null) {//读取每一行
                                message += line;
                            }
                            inputStreamReader.close();
                        }
                        inputStream.close();
                        outObj.put("Status", 1);
                        if (!"".equals(message)) {
                            outObj.put("res", message);
                        } else {
                            outObj.put("res", "下载数据成功");
                        }
                    }
                    Write("YES");
                } catch (Exception e) {
                    e.printStackTrace();
                    outObj.put("Status", 0);
                    outObj.put("res", e.getMessage());
                    Write("NO");
                } finally {
                    try {
                        if (bufferedReader != null) {
                            bufferedReader.close();
                        }
                    } catch (IOException ex) {
                        ex.printStackTrace();
                    }
                }
            }
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * InputStream 转换成byte[]
     *
     * @param input
     * @return
     * @throws IOException
     */
    private static byte[] toByteArray(InputStream input) throws IOException {
        ByteArrayOutputStream output = new ByteArrayOutputStream();
        byte[] buffer = new byte[1024 * 4];
        int n = 0;
        while (-1 != (n = input.read(buffer))) {
            output.write(buffer, 0, n);
        }
        return output.toByteArray();
    }

    /**
     * @param bytes    byte字节
     * @param filePath 附件保存地址
     * @return
     */
    public static boolean byteToFile(byte[] bytes, String filePath) {
        BufferedInputStream bis = null;
        FileOutputStream fos = null;
        BufferedOutputStream bos = null;
        try {
            ByteArrayInputStream byteInputStream = new ByteArrayInputStream(bytes);
            bis = new BufferedInputStream(byteInputStream);
            File file = new File(filePath);
            File path = file.getParentFile();
            if (!path.exists()) {
                path.mkdirs();
            }
            fos = new FileOutputStream(file);
            bos = new BufferedOutputStream(fos);

            byte[] buffer = new byte[1024];
            int length = bis.read(buffer);
            while (length != -1) {
                bos.write(buffer, 0, length);
                length = bis.read(buffer);
            }
            bos.flush();
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            return false;
        } finally {
            try {
                if (null != bis) {
                    bis.close();
                }
                if (null != fos) {
                    fos.close();
                }
                if (null != bos) {
                    bos.close();
                }
            } catch (IOException e) {
                logger.error(e.getMessage(), e);
                return false;
            }
        }
        return true;
    }

    public void getTransferBookList() {
        try {
            String id = getString("id");
            List<TransferBook> TransferBook = tInformationService.getTransferBook(id);
            if (TransferBook.size() > 0) {
                Write(returnPars(1, JSONHelper.SerializeWithNeedAnnotation(TransferBook), ""));
            } else {
                Write(returnPars(-1, "", "未查询到到账本信息"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("getTransferBook错误：" + e.getMessage(), e);
            Write(returnPars(-1, "", "查询账本信息失败：") + e.getMessage());
        }
    }

    public void getLateFeeMoneyData() {
        try {
            String dangqianrenwu = getString("dangqianrenwu");
            String id = getString("id");
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            List<Map<String, Object>> list = tInformationService.getCountyByUserID(transferInformation.getCustomerManager());
            String code = "";
            for (int i = 0; i < list.size(); i++) {
                if ("true".equals(list.get(i).get("ISMAINDPT").toString())) {
                    code = list.get(i).get("COMPANY_CODE").toString();
                    break;
                }
            }
            LateFeeMoneyData lateFeeMoneyData = tInformationService.getLateFeeMoneyData(code, dangqianrenwu);
            //logger.info("getLateFeeMoneyData=="+JSONHelper.SerializeWithNeedAnnotation(lateFeeMoneyData));
            Write(JSONHelper.SerializeWithNeedAnnotation(lateFeeMoneyData));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }

    public void innitLateFeeMoneyData() {
        try {
            //lateFeeMoneyDataService.InitializeTransferCitiesData();
            //lateFeeMoneyDataService.InitializeTransferFourCitiesData();
            //lateFeeMoneyDataService.InitializeLateFeeMoneyData();
            lateFeeMoneyDataService.InitiaTransferLizeLateFeeMoneyData();
            Write("初始化成功");
        } catch (Exception e) {
            logger.error("资金认领初始化金额和滞纳金金额错误：" + e.getMessage(), e);
            Write("初始化失败");
        }

    }

    //要解除注释
    public void getLateFeeCount() {
        try {
            String contractNo = getString("contractNo");
            String phoneNo = getString("phoneNo");
            Map<String, Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
            //String json1 = "{\"REGION_ID\":\"11\",\"POWER_RIGHT\":\"18\",\"GROUP_ID\":\"23\",\"RETURN_MSG\":\"ok!\",\"PROMPT_MSG\":\"\",\"RETURN_CODE\":\"0\",\"LOGIN_NO\":\"aa0002107\",\"USER_MSG\":\"处理成功!\",\"DETAIL_MSG\":\"OK!\"}";
            JSONObject obj = JSONObject.fromObject(json);
            Result result = ClaimFundsOpenSrv.getInstance().qryFeeInfo(phoneNo, contractNo, obj.getString("GROUP_ID"), user.getBossUserName());
            //Result result =ClaimFundsOpenSrv.getInstance().qryFeeInfo("","13011002083800","111211","aagh38");
            logger.info("资金认领查询滞纳金返回数据" + result.toString());
            String money = "0";
            if (ResultCode.SUCCESS.code() == result.getCode()) {
                JSONObject data = JSONObject.fromObject(result.getData());
                JSONObject root = JSONObject.fromObject(data.get("ROOT"));
                JSONObject outData = JSONObject.fromObject(root.get("OUT_DATA"));
                Object outMsg = outData.get("OUTMSG");
                if (outMsg instanceof JSONArray) {//判断OUTMSG节点是不是数组
                    JSONArray array = JSONArray.fromObject(outData.get("OUTMSG"));
                    for (int i = 0; i < array.size(); i++) {
                        JSONObject fobj = JSONObject.fromObject(array.get(i));
                        if (fobj != null && !fobj.isEmpty() && !fobj.isNullObject() && !"null".equals(fobj) && !"{}".equals(fobj) && fobj.size() > 0) {
                            JSONArray owefeeinfo = JSONArray.fromObject(fobj.get("OWEFEEINFO"));
                            for (int j = 0; j < owefeeinfo.size(); j++) {
                                JSONObject owefeeinfoObj = JSONObject.fromObject(owefeeinfo.get(j));
                                money = (Long.parseLong(money) + owefeeinfoObj.getInt("DELAY_FEE")) + "";
                            }
                        }
                    }
                } else if (outMsg instanceof JSONObject) {//判断OUTMSG节点是不是对象
                    JSONObject object = JSONObject.fromObject(outData.get("OUTMSG"));
                    if (object != null && !object.isEmpty() && !object.isNullObject() && !"null".equals(object) && !"{}".equals(object) && object.size() > 0) {
                        JSONArray owefeeinfo = JSONArray.fromObject(object.get("OWEFEEINFO"));
                        for (int j = 0; j < owefeeinfo.size(); j++) {
                            JSONObject owefeeinfoObj = JSONObject.fromObject(owefeeinfo.get(j));
                            money = (Long.parseLong(money) + owefeeinfoObj.getInt("DELAY_FEE")) + "";
                        }
                    }
                }
            }
            Write(money);
            //Write("1000");
        } catch (Exception e) {
            logger.error("资金认领查询滞纳金金额错误信息：" + e.getMessage(), e);
            Write("0");
        }
    }

    public void getType() {
        try {
            String id = getString("id");// 账户信息id
            WaitTask waitTask = tInformationService.getWaitTask(id);
            String str = waitTask.getUrl();
            String[] sub = str.substring(str.lastIndexOf("type")).split("&");
            String type = sub[0].substring(sub[0].lastIndexOf("=") + 1);
            Write(type);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    public void findTransition() {
        String id = getString("id");
        System.out.println("获取的id===" + id);
        TransferInformation transferInformation = tInformationService.getTransferInformation(id);// 根据id查询开票信息

        TransferProcess rap = tInformationService.getPid(transferInformation.getId());
        String pid = rap.getProcessId();
        JbpmTest jt = new JbpmTest();
        if (!"".equals(pid) && pid != null) {
            // 获取任务对象
            try {
                logger.info("所查询的流程ID：=======》" + pid);
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                //JSONArray jArray = new JSONArray();
                logger.info("当前流程节点====》" + task.getActivityName());
                JSONObject obj = new JSONObject();
                for (String outcome : setlist) {
                    if (!outcome.equals("ALL")) {
                        obj.put("transitionName", outcome);//选人
                        logger.info("当前可执行的流程走向====》" + outcome);
                    }
                    //jArray.add(obj);
                }
                obj.put("dangqianrenwu", task.getActivityName());//当前角色名字
                obj.put("pid", pid);//流程id
                //System.out.println("=="+obj.toString());
                writeText(obj.toString());
            } catch (Exception e) {
                // TODO: handle exception
                logger.info("流程信息获取异常====》" + e);
                writeText("流程信息获取异常");
            }
        } else {
            writeText("流程ID为空");
        }
    }

    public void findComprehensiveById() {
        Result r = new Result();
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            String orderNo = getString("orderNo");
            Map<String, Object> transMap = new HashMap<>();
            if (id != null && !"".equals(id) && !"null".equals(id)) {
                transMap = tInformationService.findByRowNo(id);
            } else {
                transMap = tInformationService.getTransferNumber(orderNo);
            }
            List<TransferAccountInformation> transferAccountInformation = tInformationService.gettransferAccountInformationlist(transMap.get("TRANSFERNUMBER").toString());
            List<TransferTask> p = tInformationService.processtracking(String.valueOf(transMap.get("ID")));
            map.put("transferInformation", transMap);
            map.put("transferAccountInformation", transferAccountInformation);
            map.put("processtracking", p);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(map);
            //System.out.println("==" + r);
            System.out.println("==" + JSONHelper.SerializeWithNeedAnnotationDateFormat(r));
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(r));
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("查询详情失败");
            Write(r.toString());
        }
    }

    //转账按钮
    public void findTransitionApp() {
        try {
            Result r = new Result();
            String id = getString("id");
            String phone = getString("phone");
            List outcomes = new ArrayList<>();
            //SystemUser user = new SystemUser();
            TransferInformation transferInformation = tInformationService.getTransferInformation(id);// 根据id查询开票信息
            List<TransferAccountInformation> list = tInformationService.gettransferAccountInformationlistTwo(transferInformation.getTransferNumber());
            int count = 0;
            for (TransferAccountInformation TransferAccountInformation : list) {
                List<TransferBook> TransferBook = tInformationService.getTransferBook(TransferAccountInformation.getUuid());
                for (TransferBook book : TransferBook) {
                    if (!"0".equals(book.getPayType()) && !"W".equals(book.getPayType()) && !"Z".equals(book.getPayType())
                            && !"22".equals(book.getPayType()) && !"23".equals(book.getPayType())) {
                        ++count;
                    }
                }
            }
            try {
                user = systemUserService.getUserByPhone(phone);
                //System.out.println("获取的loginName为=="+user.getLoginName());
                user = systemUserService.querUsers(user.getLoginName());
                if (user == null) {
                    r.setCode(ResultCode.FAIL);
                    r.setMessage("失败");
                    r.setData("查询人员失败，号码有误");
                    Write(r.toString());
                    return;
                }
            } catch (Exception e) {
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("根据号码查询人员异常");
                Write(r.toString());
                return;
            }
            TransferProcess rap = tInformationService.getPid(transferInformation.getId());
            String pid = rap.getProcessId();
            JbpmTest jt = new JbpmTest();
            if (!"".equals(pid) && pid != null) {
                // 获取任务对象
                try {
                    logger.info("所查询的流程ID：=======》" + pid);
                    Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                    Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                    //JSONArray jArray = new JSONArray();
                    logger.info("当前流程节点====》" + task.getActivityName());
                    List buttons = new ArrayList<>();
                    JSONObject obj = new JSONObject();
                    for (String outcome : setlist) {
                        if (!outcome.equals("结束")) {
                            outcomes.add(outcome);
                        }
                        //jArray.add(obj);
                    }
                    //判断竞争性需求
                    //Integer competitiveDemand = transferInformation.getCompetitiveDemand();
                    //System.out.println("判断==" + transferInformation.getCompetitiveDemand());
                    if (transferInformation.getCompetitiveDemand() == 1) {
                        if (task.getActivityName().equals("区县业务管理员") || task.getActivityName().equals("区县政企部主任") || task.getActivityName().equals("区县分管经理") ||
                                task.getActivityName().equals("市公司客户经理室经理") || task.getActivityName().equals("市公司政企部经理") || task.getActivityName().equals("省重客客户经理室经理") ||
                                task.getActivityName().equals("省重客业务管理员") || task.getActivityName().equals("省重客业务管理室经理") || task.getActivityName().equals("省重客政企业务管理室") ||
                                task.getActivityName().equals("市公司业务管理室经理") || task.getActivityName().equals("省公司政企业务管理室")) {
                            buttons.add("提交");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("市公司业务管理员") || task.getActivityName().equals("其他审批人") ||
                                task.getActivityName().equals("其他审批人2") || task.getActivityName().equals("其他审批人3") || task.getActivityName().equals("其他审批人4") ||
                                task.getActivityName().equals("其他审批人5") || task.getActivityName().equals("其他审批人6") || task.getActivityName().equals("其他审批人7") ||
                                task.getActivityName().equals("其他审批人8") || task.getActivityName().equals("其他审批人9") || task.getActivityName().equals("其他审批人10")
                            //省公司政企客户部经理副 省公司政企客户部经理正
                        ) {
                            buttons.add("提交");
                            buttons.add("转审");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("市公司政企部经理副")) {
                            //                        if (transferInformation.getBranchOffice().equals("成都分公司")) {
                            //                            buttons.add("提交");
                            //                            buttons.add("提交市公司领导");
                            //                            obj.put("pid", pid);
                            //                            obj.put("buttons", buttons);
                            //                            obj.put("outcomes", outcomes);
                            //                            r.setCode(ResultCode.SUCCESS);
                            //                            r.setMessage("成功");
                            //                            r.setData(obj);
                            //                        } else {
                            buttons.add("提交市公司领导");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                            //                        }
                        } else if (task.getActivityName().equals("市公司领导") || task.getActivityName().equals("省重客分管经理")) {
                            if (count == 0 && transferInformation.getAcrossGroup() == 1) {
                                //不跨集团
                                buttons.add("同意");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            } else {
                                //跨集团
                                buttons.add("提交");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            }
                        } else if (task.getActivityName().equals("省公司管理员") || task.getActivityName().equals("省公司政企业务管理室2次")) {
                            List<SystemDept> deptList = user.getSystemDept();
                            String code = deptList.get(0).getSystemCompany().getCompanyCode();
                            TransferCitiesData transferCitiesData = new TransferCitiesData();
                            if (task.getActivityName().equals("省公司政企业务管理室2次")) {
                                transferCitiesData = tInformationService.getTransferCitiesData(code, "省公司政企业务管理室");
                            } else {
                                transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                            }
                            if (Double.valueOf(transferCitiesData.getAmount()) > 0) { //OUTMONEY
                                if (Double.valueOf(transferInformation.getOutMoney()) < Double.valueOf(transferCitiesData.getAmount())) {
                                    buttons.add("同意");
                                    obj.put("pid", pid);
                                    obj.put("buttons", buttons);
                                    obj.put("outcomes", outcomes);
                                    r.setCode(ResultCode.SUCCESS);
                                    r.setMessage("成功");
                                    r.setData(obj);
                                } else {
                                    buttons.add("提交");
                                    obj.put("pid", pid);
                                    obj.put("buttons", buttons);
                                    obj.put("outcomes", outcomes);
                                    r.setCode(ResultCode.SUCCESS);
                                    r.setMessage("成功");
                                    r.setData(obj);
                                }
                            } else {
                                buttons.add("提交");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            }
                        } else if (task.getActivityName().equals("省公司政企客户部经理副") || task.getActivityName().equals("省公司政企客户部经理正")) {
                            buttons.add("同意");
                            //                        buttons.add("转审");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        }
                    } else {
                        //System.out.println("findTransitionApp非竞争");
                        if (task.getActivityName().equals("市公司客户经理室经理") || task.getActivityName().equals("省重客客户经理室经理") || task.getActivityName().equals("省重客业务管理员") || task.getActivityName().equals("省公司政企业务管理室") || task.getActivityName().equals("市公司业务管理室经理")) {
                            buttons.add("提交");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("区县业务管理员") || task.getActivityName().equals("区县政企部主任")) {
                            String str = pid.substring(0, pid.indexOf("."));
                            if ("TransferFour".equals(str)) {
                                buttons.add("提交");
                            } else {
                                List<SystemDept> deptList = user.getSystemDept();
                                String code = deptList.get(0).getSystemCompany().getCompanyCode();
                                TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                                //判断是否跨集团
                                //System.out.println("是否跨集团转账:"+JSONObject.fromObject(transferInformation));
                                if (transferInformation.getAcrossGroup() == 1) { //不跨集团
                                    if (Double.valueOf(transferCitiesData.getAmount()) > 0) { //OUTMONEY
                                        if (Double.valueOf(transferInformation.getOutMoney()) < Double.valueOf(transferCitiesData.getAmount())) {
                                            buttons.add("同意");
                                        } else {
                                            buttons.add("提交");
                                        }
                                    } else {
                                        buttons.add("提交");
                                    }
                                } else {
                                    buttons.add("提交");
                                }
                            }
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("区县分管经理") || task.getActivityName().equals("省重客业务管理室经理") ||
                                task.getActivityName().equals("市公司政企部经理")) {
                            List<SystemDept> deptList = user.getSystemDept();
                            String code = deptList.get(0).getSystemCompany().getCompanyCode();
                            TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                            //判断是否跨集团
                            //System.out.println("是否跨集团转账:"+JSONObject.fromObject(transferInformation));
                            if (task.getActivityName().equals("市公司政企部经理")) {
                                buttons.add("转审");
                            }
                            if (transferInformation.getAcrossGroup() == 1) { //不跨集团
                                if (Double.valueOf(transferCitiesData.getAmount()) > 0) { //OUTMONEY
                                    if (Double.valueOf(transferInformation.getOutMoney()) < Double.valueOf(transferCitiesData.getAmount())) {
                                        buttons.add("同意");
                                    } else {
                                        buttons.add("提交");
                                    }
                                } else {
                                    buttons.add("提交");
                                }
                            } else {
                                buttons.add("提交");
                            }
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("市公司领导")) {
                            if (transferInformation.getAcrossGroup() == 1) { //不跨集团
                                buttons.add("同意");
                            } else {
                                buttons.add("提交");
                            }
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("省重客分管经理")) {
                            if (transferInformation.getAcrossGroup() == 1) { //不跨集团
                                buttons.add("同意");
                            } else {
                                buttons.add("提交");
                            }
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("市公司业务管理员") || task.getActivityName().equals("其他审批人") ||
                                task.getActivityName().equals("其他审批人2") || task.getActivityName().equals("其他审批人3") || task.getActivityName().equals("其他审批人4") ||
                                task.getActivityName().equals("其他审批人5") || task.getActivityName().equals("其他审批人6") || task.getActivityName().equals("其他审批人7") ||
                                task.getActivityName().equals("其他审批人8") || task.getActivityName().equals("其他审批人9") || task.getActivityName().equals("其他审批人10")) {
                            buttons.add("提交");
                            buttons.add("转审");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("市公司政企部经理副")) {
                            List<SystemDept> deptList = user.getSystemDept();
                            String code = deptList.get(0).getSystemCompany().getCompanyCode();
                            LateFeeMoneyData lateFeeMoneyData = tInformationService.getLateFeeMoneyData(code, "市公司政企部经理");
                            //lateFeeMoneyData.getAmount();//资金认领滞纳金配置 金额
                            //transferInformation.getLateFeeMoney();////滞纳金金额
                            TransferCitiesData transferCitiesData = tInformationService.getTransferCitiesData(code, "市公司政企部经理");
                            //判断是否跨集团
                            if (transferInformation.getAcrossGroup() == 1) { //不跨集团
                                if (Double.valueOf(transferCitiesData.getAmount()) > 0) {
                                    if (Double.valueOf(transferInformation.getOutMoney()) < Double.valueOf(transferCitiesData.getAmount()) &&
                                            Double.valueOf(lateFeeMoneyData.getAmount()) <= Double.valueOf(transferInformation.getLateFeeMoney())
                                    ) {
                                        buttons.add("同意");
                                    } else if (Double.valueOf(transferInformation.getOutMoney()) >= Double.valueOf(transferCitiesData.getAmount())) {
                                        buttons.add("提交市公司领导");
                                    }
                                } else {
                                    buttons.add("同意");
                                }
                            } else {
                                buttons.add("提交");
                                buttons.add("转审");
                            }
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("省公司政企业务管理室2次") || task.getActivityName().equals("省公司管理员")) {
                            List<SystemDept> deptList = user.getSystemDept();
                            String code = deptList.get(0).getSystemCompany().getCompanyCode();
                            TransferCitiesData transferCitiesData = new TransferCitiesData();
                            if (task.getActivityName().equals("省公司政企业务管理室2次")) {
                                transferCitiesData = tInformationService.getTransferCitiesData(code, "省公司政企业务管理室");
                            } else {
                                transferCitiesData = tInformationService.getTransferCitiesData(code, task.getActivityName());
                            }
                            buttons.add("转审");
                            //判断是否跨集团
                            //if(transferInformation.getAcrossGroup()==1){ //不跨集团
                            if (Double.valueOf(transferCitiesData.getAmount()) > 0) { //OUTMONEY
                                if (Double.valueOf(transferInformation.getOutMoney()) < Double.valueOf(transferCitiesData.getAmount())) {
                                    buttons.add("同意");
                                } else {
                                    buttons.add("提交");
                                }
                            } else {
                                buttons.add("提交");
                            }
                            //                        }else {
                            //                            buttons.add("提交");
                            //                        }
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("省公司政企客户部经理副") || task.getActivityName().equals("省公司政企客户部经理正")) {
                            buttons.add("同意");
                            buttons.add("转审");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else if (task.getActivityName().equals("市公司领导")) {
                            if (transferInformation.getAcrossGroup() == 1) { //不跨集团
                                buttons.add("同意");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            } else {
                                buttons.add("提交");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            }
                        }
                    }
                    logger.info("转账手机端获取的角色为==" + r.toString());
                    Write(r.toString());
                } catch (Exception e) {
                    // TODO: handle exception
                    logger.error("转账手机端按钮流程信息获取异常====》" + e.getMessage(), e);
                    r.setCode(ResultCode.FAIL);
                    r.setMessage("失败");
                    r.setData("流程信息获取异常");
                    Write(r.toString());
                }
            } else {
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("流程ID为空");
                Write(r.toString());
            }
        } catch (Exception e) {
            logger.error("转账手机端按钮流程信息获取异常2====》" + e.getMessage(), e);
            Write("转账手机端按钮流程信息获取异常2");
        }
    }

    //根据主工单id查询关联的附件信息
    public void getFileByOrderId() {
        try {
            String id = getString("id");
            String biaoshi = getString("biaoshi");
            List<Map<String, String>> s = tInformationService.fuJianTwo(id, biaoshi);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
        } catch (Exception e) {
            logger.error("根据主工单id查询关联的附件信息失败" + e.getMessage(), e);
            Write("NO");
        }
    }

    public void getTransferNumber() {
        try {
            String orderNo = getString("orderNo");
            Map<String, Object> map = tInformationService.getTransferNumber(orderNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(map));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /*
     * <AUTHOR>
     * @Date 2024/1/4 15:12
     * @Description 提交退回人
     **/
    public void submitTheReturn() {
        try {
            String id = getString("id");// 转账信息id
            String pid = getString("processId");// 流程id
            String userid = getString("userId");// 用户id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String transferTaskID = getString("transferTaskID");
            String attachmentId = getString("attachmentId");// 补充附件id

            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            //修改上一步任务信息
            TransferTask transferTask = tInformationService.getTaskList(transferTaskID);// 根据流程id查询任务表信息
            if (transferTask != null) {
                transferTask.setOperDate(getStringDate(new Date()));//操作时间
                transferTask.setStatus("2");
                tInformationService.updateTask(transferTask);//修改任务表
            }

            TransferInformation transferInformation = tInformationService.getTransferInformation(id);
            WaitTask wait = service.queryWaitByTaskId(waitId);
            // 结束当前待办
            if (wait != null) {
                System.out.println("================处理中开始代办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            //保存下一步任务信息
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();

            //新增本次操作任务表信息
            TransferTask tt = new TransferTask();
            tt.setProcess(id);
            tt.setRole("客户经理");
            tt.setCreator(USER.getEmployeeName());
            tt.setCreatorNo(USER.getRowNo() + "");
            tt.setCreatDate(getStringDate(new Date()));
            tt.setPlanDate(getStringDate(new Date()));
            tt.setOper(user.getEmployeeName());
            tt.setOperNo(user.getRowNo() + "");
            tt.setOperDate(getStringDate(new Date()));
            tt.setSpendTime("0");
            tt.setStatus("2");
            tt.setType("SH");
            tt.setExpectedCompletionTime(getStringDate(new Date()));
            tt.setReplyContent(opinion);//审批意见
            tInformationService.saveTaskList(tt);// 保存流程表信息

            try {
                // 等待1秒再设置第二个任务的创建时间
                Thread.sleep(1000);
            } catch (InterruptedException e) {
                e.printStackTrace();
            }

            //新增下一步任务表信息
            TransferTask TaskListtwo = new TransferTask();
            TaskListtwo.setProcess(id);
            TaskListtwo.setRole(tasktwo.getActivityName());
            TaskListtwo.setCreator(user.getEmployeeName());
            TaskListtwo.setCreatorNo(user.getRowNo() + "");
            TaskListtwo.setCreatDate(getStringDate(new Date()));
            TaskListtwo.setPlanDate(getStringDate(new Date()));
            TaskListtwo.setOper(USER.getEmployeeName());
            TaskListtwo.setOperNo(USER.getRowNo() + "");
            TaskListtwo.setSpendTime("0");
            TaskListtwo.setStatus("1");
            TaskListtwo.setType("SH");
            TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
            TransferTask TaskBeantwo = tInformationService.saveTaskList(TaskListtwo);// 保存流程表信息
            daibantwo(transferInformation, userid, pid, user, TaskBeantwo);

            if (!StringUtils.isEmpty(attachmentId)) {
                // 判断是否上传了附件，获取前台提交的附件Id；
                String[] json = attachmentId.split(",");
                if (json.length > 0) {
                    Date date = new Date();
                    for (String value : json) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(transferInformation.getId());
                        sa.setAttachmentId(value);
                        sa.setLink(TransferInformation.Transfer);
                        sa.setCreateDate(date);
                        tInformationService.saveSandA(sa);
                    }
                }
            }
            transferInformation.setHANDLER_ID(userid);
            transferInformation.setState(2);
            tInformationService.update(transferInformation);
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            logger.error("转账提交退回人操作失败错误信息:" + e.getMessage(), e);
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }


}
