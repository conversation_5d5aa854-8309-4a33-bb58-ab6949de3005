package com.xinxinsoft.action.order;

import java.util.Date;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.order.OrderDetail;
import com.xinxinsoft.service.order.OrderDetailService;
import com.xinxinsoft.utils.StringUtil;
import com.xinxinsoft.utils.UUIDUtil;
import com.xinxinsoft.utils.XMLToVeiw;
import com.xinxinsoft.utils.easyh.JSONHelper;

public class OrderDetailAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = 3613369139718706791L;

	OrderDetailService orderDetailService;

	/**
	 * 添加信息
	 */
	public void addOrderDetail() {
		try {
			String uuid=null;
			OrderDetail orderDetail =null;
			String xmlJson = getString("json");
			String id = getString("id");
			String zpcode = getString("zpcode");
			OrderDetail orderDetailtwo=null;
			if(id != null && !id.equals("")){
				orderDetail =orderDetailService.getOrderDetailByID(id);
			}
			if(orderDetail==null){
				OrderDetail orderDetailone = new OrderDetail();
				uuid = UUIDUtil.getInstance().generatUUID(false);
				orderDetailone.setCreateTime(new Date());
				orderDetailone.setOdetailId(uuid);
				orderDetailone.setXmlContent(XMLToVeiw.VeiwToXML(xmlJson));
				orderDetailone.setZpcode(zpcode);
				orderDetailtwo=orderDetailService.addOrderDetail(orderDetailone);
			}else{
				uuid = orderDetail.getOdetailId();
				orderDetail.setCreateTime(new Date());
				orderDetail.setZpcode(zpcode);
				orderDetail.setXmlContent(XMLToVeiw.VeiwToXML(xmlJson));
				orderDetailtwo=orderDetailService.addOrderDetail(orderDetail);
			}
			if(orderDetailtwo.getXmlContent()==null){
				writeText("NO");
			}else{
				writeText(uuid);
			}
			
		} catch (Exception e) {
			e.printStackTrace();
			writeText("NO");
		}
	}

	/**
	 * 获取XML信息
	 */
	public void getXMLString(){
		String xmlJson = getString("json");
		
		writeText(StringUtil.AllNullToString(XMLToVeiw.VeiwToXML(xmlJson)));
	}
	/**
	 * 根据订单ID查询产品信息
	 */
	public void getOrderDetailByOID(){
		try {
			String orderId=getString("oid");
			int tdCountLine=getInteger("tdCountLine");
			OrderDetail orderDetail=orderDetailService.getOrderDetailByID(orderId);
			if(orderDetail!=null){
			 String html=XMLToVeiw.XmlToHtml(orderDetail.getXmlContent(),tdCountLine);
			 Write(html);
			}else{
				Write("");
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	public OrderDetailService getOrderDetailService() {
		return orderDetailService;
	}

	public void setOrderDetailService(OrderDetailService orderDetailService) {
		this.orderDetailService = orderDetailService;
	}

}
