package com.xinxinsoft.action.ums;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.customer.GroupContactInfo;
import com.xinxinsoft.entity.customer.GuideDocumentation;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.ums.UnitInfo;
import com.xinxinsoft.entity.ums.UnitInfoDTO;
import com.xinxinsoft.entity.ums.UnitInfoRecord;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.sendComms.accountService.UnitAccountInfoSrv;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.sendComms.omsService.common.PackOprInfoVO;
import com.xinxinsoft.sendComms.unitService.CMCCUnitOpenService;
import com.xinxinsoft.sendComms.unitService.UnitInfoSrv;
import com.xinxinsoft.sendComms.unitService.UnitInfoVO;
import com.xinxinsoft.service.IBossService.IBossByNoService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.groupcustomer.GroupCustomerService;
import com.xinxinsoft.service.ums.UnitInfoService;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;

import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.http.HttpServletResponse;
import javax.swing.plaf.PanelUI;

public class UnitInfoAction extends BaseAction {
    private UnitInfoService unitInfoService;
    private AttachmentService attachmentService;
    private GroupCustomerService groupCustomerService;
    private IBossByNoService iBossByNoService;
    private static final Logger logger = LoggerFactory.getLogger(UnitInfoAction.class);

    public static Map<String, Object> keyMap = new ConcurrentHashMap<>();

    private final String FTP_URL = FileUpload.getFtpUrlTool("UNIT_FILE_DIR");
    private final String REQUER_URL = FileUpload.getRequer_Url();//"http://10.113.222.58:9999/pois/";

    /**
     * J610通过证件号码查询集团建档信息接口
     */
    public void certificateQueryGroup() {
        try {
            String idIccid = getString("idIccid");
            String idType = getString("idType");
            Result certificateQueryGroup = unitInfoService.certificateQueryGroup(idIccid, idType);
            Write(certificateQueryGroup.toString());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("证件号码查询集团建档信息错误===>" + e.toString());
        }
    }

    /**
     * J610经办人变更接口
     */
    public void    agentChange() {
        try {
            String phone = getString("phone");//操作人电话
            //System.out.println("操作人电话"+phone);
            String enclosureId = getString("enclosureId");//附件id
            //String phone = "18000527723";//操作人电话
            SystemUser su = groupCustomerService.getBossNo(phone);
            String portraitId = getString("portraitId");//人像比对流水号
            //System.out.println("人像比对流水号"+portraitId);
            String agentAuthorization = getString("agentAuthorization");//经办人委托授权有效期
            //System.out.println("经办人委托授权有效期"+agentAuthorization);
            String agentBirthday = getString("agentBirthday");//经办人生日
            //System.out.println("经办人生日"+agentBirthday);
            String agentCustName = getString("agentCustName");//经办人姓名，base64加密
            //System.out.println("经办人姓名"+agentCustName);
            String agentIdAddress = getString("agentIdAddress");//经办人证件地址，base64加密
            //System.out.println("经办人证件地址"+agentIdAddress);
            String agentIdIccid = getString("agentIdIccid");//经办人证件号码，base64加密
            //System.out.println("经办人证件号码"+agentIdIccid);
            String agentIdType = getString("agentIdType");//经办人证件类型
            //System.out.println("经办人证件类型"+agentIdType);
            String agentIdValiddate = getString("agentIdValiddate");//经办人证件有效期
            //System.out.println("经办人证件有效期"+agentIdValiddate);
            String agentNation = getString("agentNation");//经办人国籍（1,中国）
            //System.out.println("经办人国籍"+agentNation);
            String agentNationId = getString("agentNationId");//经办人民族（11，汉族）
            //System.out.println("经办人民族"+agentNationId);
            String agentRegisterOffice = getString("agentRegisterOffice");//经办人签证机关
            //System.out.println("经办人签证机关"+agentRegisterOffice);
            String agentSexCode = getString("agentSexCode");//经办人性别 11:男 12:女
            //System.out.println("经办人性别"+agentSexCode);
            String custName = getString("custName");//集团名称，base64加密
            String idAddress = getString("idAddress");//集团证件地址，base64加密
            String idIccid = getString("idIccid");//集团证件号码，base64加密
            String idType = getString("idType");//集团证件类型
            String idValiddate = getString("idValiddate");//集团证件有效期
            String respBirthday = getString("respBirthday");//责任人生日
            //System.out.println("责任人生日"+respBirthday);
            String respCustName = getString("respCustName");//责任人姓名，base64加密
            //System.out.println("责任人姓名"+respCustName);
            String respIdAddress = getString("respIdAddress");//责任人证件地址，base64加密
            //System.out.println("责任人证件地址"+respIdAddress);
            String respIdIccid = getString("respIdIccid");//责任人证件号码，base64加密
            //System.out.println("责任人证件号码"+respIdIccid);
            String respIdType = getString("respIdType");//责任人证件类型
            //System.out.println("责任人证件类型"+respIdType);
            String respIdValiddate = getString("respIdValiddate");//责任人证件有效期
            //System.out.println("责任人证件有效期"+respIdValiddate);
            String respNation = getString("respNation");//责任人国籍
            //System.out.println("责任人国籍"+respNation);
            String respNationId = getString("respNationId");//责任人民族
            //System.out.println("责任人民族"+respNationId);
            String respRegisterOffice = getString("respRegisterOffice");//责任人签证机关
            String respSexCode = getString("respSexCode");//责任人性别 11:男 12:女
            //System.out.println("责任人性别"+respSexCode);
            String status = getString("status");//审批状态（完成审批）
            String agentPersonPhone = getString("agentPersonPhone"); //经办人手机号 2021/11/16

            Result result = unitInfoService.agentChange(agentAuthorization, agentBirthday, agentCustName, agentIdAddress, agentIdIccid, agentIdType, agentIdValiddate, agentNation,
                    agentNationId, agentRegisterOffice, agentSexCode, custName, idAddress, idIccid, idType, idValiddate, respBirthday, respCustName, respIdAddress,
                    respIdIccid, respIdType, respIdValiddate, respNation, respNationId, respRegisterOffice, respSexCode, status, String.valueOf(su.getBossUserName()), portraitId,agentPersonPhone);
            if (result.getCode() == 200) {
                //数据入表
                /**
                 * id、操作人、操作人boss工号、操作时间、集团证件号码(这个证件号码就是查询接口的入参号码；此号码也会在提交接口的入参里面存在)、提交接口成功时的入参json字符串(字段长度整大点)
                 * 附件id
                 */
                UnitInfoRecord unitInfoRecord = new UnitInfoRecord();
                unitInfoRecord.setOperNo(String.valueOf(su.getRowNo()));//操作人boss工号
                unitInfoRecord.setOperName(su.getEmployeeName());//操作人
                unitInfoRecord.setOperDate(new Date());//操作时
                unitInfoRecord.setIdIccid(idIccid);//集团证件号码
                if(enclosureId != null && !"".equals(enclosureId)){
                    logger.info("经办人变更附件id:" + enclosureId);
                    //分割fileIds
                    String[] fileIdArray = enclosureId.split("_");
                    Attachment attachment = unitInfoFileUpdate(fileIdArray, su,"集团变更证件资料.zip");
                    unitInfoRecord.setEnclosureId(attachment.getAttachmentId());//附件id
                }else {
                    unitInfoRecord.setEnclosureId(enclosureId);//附件id
                }
                unitInfoRecord.setPortraitId(portraitId);//人像比对流水号
                unitInfoRecord.setJsonString(unitInfoService.agentChangeTwo(agentAuthorization, agentBirthday, agentCustName, agentIdAddress, agentIdIccid, agentIdType, agentIdValiddate, agentNation,
                        agentNationId, agentRegisterOffice, agentSexCode, custName, idAddress, idIccid, idType, idValiddate, respBirthday, respCustName, respIdAddress,
                        respIdIccid, respIdType, respIdValiddate, respNation, respNationId, respRegisterOffice, respSexCode, status, String.valueOf(su.getBossUserName()), portraitId,agentPersonPhone));
                unitInfoService.addUnitInfo(unitInfoRecord);
                Write(result.toString());
                return;
            } else {
                Write(result.toString());
                //this.Write("{\"RETURN_CODE\":\"999999\",\"RETURN_MSG\":\"调用经办人变更接口出错\",\"DETAIL_MSG\":\"调用经办人变更接口出错\"}");
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("J610经办人变更错误===>" + e.toString());
        }
    }

    /**
     * 添加集团
     * zy
     */
    public void addUnitInfo() {

        try {
            String userPhoneNum = getString("userPhoneNum");
            SystemUser su = groupCustomerService.getBossNo(userPhoneNum);
            UnitInfo unitInfo = getUnitInfo();
            Attachment attachment = null;
            String fileId = getString("fileId");
            if (fileId != null && !"".equals(fileId)) {
                logger.info("集团建档fileId:" + fileId);
                attachment = attachmentService.getAttachmentById(fileId);
            } else {
                String fileIds = getString("fileIds");
                logger.info("集团建档fileIds:" + fileIds);
                //分割fileIds
                String[] fileIdArray = fileIds.split("_");
                attachment = unitInfoFileUpdate(fileIdArray, su,"新建集团证件资料.zip");
            }
            //当无法获取到附件信息
            if(attachment==null){
                Write(ResultGenerator.genFailResult("亲爱的同事，系统获取附件异常，请重新尝试!").toString());
                return;
            }

            Result result = unitInfoService.addUnitsInfo(unitInfo, attachment, getReqPatarms());
            //解析返回数据
            result= HttpURLConnectClientFactory.analyticParamsByResult(result.getData().toString());
            logger.info(result.toString());
            if ("SUCCESS".equals(result.getMessage())) {
                JSONObject map = JSONObject.fromObject(result.getData());
                JSONObject outData = JSONObject.fromObject(map.get("OUT_DATA"));
                GroupContactInfo groupContactInfo = addGroupCustomer(unitInfo, attachment.getAttachmentId(), outData, su);
                if (("1").equals(unitInfo.getPortrait()) || unitInfo.getPortrait() == "1") {
                    //调用人像比对信息落地接口
                    Result result2 = unitInfoService.addPortraitInfo(unitInfo, String.valueOf(su.getRowNo()));
                    if ("SUCCESS".equals(result2.getMessage())) {
                        groupContactInfo.setPortrait("1");
                        groupCustomerService.updateGroupContactInfo(groupContactInfo);
                        outData.put("CUST_NAME", UnitBase64Util.decrypt(outData.get("CUST_NAME").toString()));//集团名称
                        outData.put("PORTRAIT_RESULT", "人像比对流水号保存成功:" + unitInfo.getPortraitId());//人像比对落地结果
                        result.setData(outData);
                        logger.info("新建集团成功结果（含人像比对）--》" + result);
                        Write(result.toString());
                        return;
                    } else {
                        outData.put("CUST_NAME", UnitBase64Util.decrypt(outData.get("CUST_NAME").toString()));//集团名称
                        outData.put("PORTRAIT_RESULT", "人像比对流水号保存失败:" + unitInfo.getPortraitId());//人像比对落地结果
                        result.setData(outData);
                        logger.info("新建集团成功结果（含人像比对）--》" + result);
                        Write(result.toString());
                        return;
                    }
                } else {
                    outData.put("CUST_NAME", UnitBase64Util.decrypt(outData.get("CUST_NAME").toString()));//集团名称
                    result.setData(outData);
                    logger.info("新建集团成功结果（无人像比对）--》" + result);
                    Write(result.toString());
                    return;
                }
            }
            //附件上传成功，推送BOSS失败时，反馈附件信息到前端允许用户二次推送
            JSONObject o = JSONObject.fromObject(result.getData());
            o.put("fileId", attachment.getAttachmentId());
            result.setData(o);
            logger.info("第一次返回数据：" + result.toString());
            Write(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("集团建档错误===>" + e.toString(), e);
            Write(ResultGenerator.genFailResult("亲爱的同事,新建集团操作失败,具体问题请联系系统管理员!").toString());
        }
    }


    /**
     * @Description: //TODO 营业执照信息验真（根据chkType调整验真渠道）
     * @Author: Leo
     * @Date: 2021/10/13 10:18
     */
    public  void enterpriseChk(){
        String entName=getString("entName"); //集团名称
        String uniscId=getString("uniscId"); //统一信用代码
        String legalPersonName=getString("legalPersonName"); //法人名称
        String chkType=getString("chkType"); //达拓：DT  集团企业验真：JT

        String bossno=getString("bossno"); //BOSS工号
        String groupId=getString("groupId"); //BOSS归属组织

        Result result=ResultGenerator.genFailResult("亲爱的同事，集团验真失败，请核实验真信息是否完整");
        if("DT".equals(chkType)){
            result = UnitInfoSrv.getInstance().enterpriseChkByDT(entName,uniscId,legalPersonName);
        }
        if ("JT".equals(chkType)){
            result = UnitInfoSrv.getInstance().enterpriseChk(bossno,groupId,entName,uniscId,legalPersonName);
        }
        Write(result.toString());
    }
    /**
     * 验证工号是否为全网工号
     */
    public void VerifyJob() {
        Result result = ResultGenerator.genSuccessResult();
        System.out.println("请求的参数："+result.getData());
        Map<String, String> maps = new HashMap<>();
        maps.put("BUSI_INFO", this.getString("busiInfo"));
        maps.put("OPR_INFO", this.getString("oprInfo"));

        if (MapUtils.isNotEmpty(maps)) {
            result = unitInfoService.verifyJobNumber(this.getString("busiInfo")
                    , this.getString("oprInfo")
                    , this.getString("boosNo"));//getReqPatarms()
        } else {
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }
        Write(result.toString());
    }


    /**
     * 收集集团信息
     * zy
     */
    public void collectUnitInfo() {
        Attachment attachment=null;
        try {
            String msg = "";
            String userPhoneNum = getString("userPhoneNum");
            SystemUser su = groupCustomerService.getBossNo(userPhoneNum);
            String uLoginNo = getString("u_login_no");
            //判断数据库中是否已经存在，如存在则提醒用户该资料已收集
            UnitInfo unitInfo = getUnitInfo();
            List<UnitInfo> list = unitInfoService.getUnitInfoByLicenseNo(unitInfo.getRegisterNo());
            if (list.size() > 0) {
                Result result=ResultGenerator.genFailResult("亲爱的同事，营业执照信息已存在,请勿重复收集!");
                Write(result.toString());
                return;
            }
            String fileId = getString("fileId");
            if (fileId != null && !"".equals(fileId)) {
                logger.info("集团建档fileId:" + fileId);
                attachment = attachmentService.getAttachmentById(fileId);
            } else {
                String fileIds = getString("fileIds");
                logger.info("集团建档fileIds:" + fileIds);
                //分割fileIds
                String[] fileIdArray = fileIds.split("_");
                //压缩附件为ZIP(修改文件集合为删除状态，并将文件设置为压缩包保存)
                attachment = unitInfoFileUpdate(fileIdArray, su,"集团资料收集.zip");
            }
            //当无法获取到附件信息
            if(attachment==null){
                //附件上传成功，允许用户二次推送
                Write(ResultGenerator.genFailResult("亲爱的同事，系统获取附件异常，请重新尝试!").toString());
                return;
            }
            /**
             * 接口请求问题（暂时先屏蔽。）2020-08-19 17:32:55
             */
            String checkFalg = unitInfoService.unitVerifyTruth(unitInfo);
            if ("一致".equals(checkFalg)) {
                unitInfo.setCheckFlag("1");
            } else {
                unitInfo.setCheckFlag("0");
            }
            //String checkFalg = "校验接口异常,正在努力修复";
            msg = "验真结果: " + checkFalg;
            unitInfo.setRelatedFile(attachment.getAttachmentId()); //相关附件ID
            unitInfo.setCreateLoginNo(uLoginNo);
            unitInfo.setLicenseNo(unitInfo.getRegisterNo());
            unitInfoService.collectUnitInfo(unitInfo);
            Write(ResultGenerator.genSuccessResult(msg).toString());
        } catch (Exception e) {
            e.printStackTrace();
            Result result=ResultGenerator.genFailResult("亲爱的同事,程序异常操作失败，请重新尝试");
            if(attachment!=null){
                //附件上传成功，允许用户二次推送
                JSONObject o = JSONObject.fromObject(result.getData());
                o.put("fileId", attachment.getAttachmentId());
                result.setData(o);
            }
            Write(result.toString());
        }
    }

    /**
     * 查询集团
     * zy
     */
    public void qryGrpCredential() {
        Map<String, Object> map = new HashMap<String, Object>();
        Map<String, Object> rootMap;
        try {
            String token = "";
            String request_info = UrlConnection.getRequestData(getRequest());
            logger.info("请求参数(qryGrpCredential)==>" + request_info);
            if ("".equals(request_info)) {
                map.put("RETURN_CODE", "-1");
                map.put("RETURN_MSG", "请求参数异常");
                map.put("DETAIL_MSG", "请求参数异常");

                rootMap = new HashMap<String, Object>();
                rootMap.put("ROOT", map);
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(rootMap).toString());
                return;
            }
            JSONObject request_info_object = JSONObject.fromObject(request_info);
            JSONObject root = JSONObject.fromObject(request_info_object.get("ROOT"));
            if (CMCCOpenService.having(root, "TOKEN")) {
                token = root.getString("TOKEN");
            } else {
                map.put("RETURN_CODE", "-1");
                map.put("RETURN_MSG", "请求参数异常");
                map.put("DETAIL_MSG", "请求参数异常");

                rootMap = new HashMap<String, Object>();
                rootMap.put("ROOT", map);
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(rootMap).toString());
                return;
            }

            if (StringUtils.isEmpty(token)) {
                map.put("RETURN_CODE", "-1");
                map.put("RETURN_MSG", "请求参数异常");
                map.put("DETAIL_MSG", "请求参数异常");

                rootMap = new HashMap<String, Object>();
                rootMap.put("ROOT", map);
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(rootMap).toString());
                return;
            } else {
                String name = EncryptionUtils.decrypt(token);
                if (StringUtils.isEmpty(iBossByNoService.querySysName(name))) {
                    logger.info("请求参数(qryGrpCredential),参数异常==>name=" + name + "；token=" + token);
                    map.put("RETURN_CODE", "-1");
                    map.put("RETURN_MSG", "请求参数异常");
                    map.put("DETAIL_MSG", "无效TOKEN");

                    rootMap = new HashMap<String, Object>();
                    rootMap.put("ROOT", map);
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(rootMap).toString());
                    return;
                }
            }
            JSONObject body = JSONObject.fromObject(root.get("BODY"));

            PageRequest pageRequest = new PageRequest();


            PageResponse pageResponse = unitInfoService.getUnitInfoAll(createUnitInfoDTO(body), pageRequest);

            List<Map<String, Object>> data = (ArrayList) pageResponse.getList();


            List<Object> resultList = new ArrayList<Object>();


            if (data != null) {
                for (Map<String, Object> item : data) {
                    Map<String, Object> RESULT_LIST = new HashMap<String, Object>();

                    Map<String, Object> idcardInfo = new HashMap<String, Object>();
                    idcardInfo.put("IDCARD_NO", item.get("IDCARD_NO"));
                    idcardInfo.put("ID_CUST_NAME", item.get("ID_CUST_NAME"));
                    idcardInfo.put("ID_ADDRESS", item.get("ID_ADDRESS"));
                    idcardInfo.put("ID_SEX_CODE", item.get("ID_SEX_CODE"));
                    idcardInfo.put("ID_BIRTHDAY", item.get("ID_BIRTHDAY"));
                    idcardInfo.put("ID_COUNTRY", item.get("ID_COUNTRY"));
                    idcardInfo.put("ID_NATION", item.get("ID_NATION"));
                    idcardInfo.put("ID_REGISTER_OFFICE", item.get("ID_REGISTER_OFFICE"));
                    idcardInfo.put("ID_REGISTER_DATE", item.get("ID_REGISTER_DATE"));
                    idcardInfo.put("ID_VALID_DATE", item.get("ID_VALID_DATE"));
                    idcardInfo.put("IDCARD_FILE", "");
                    idcardInfo.put("BANKCARD_FILE", "");

                    RESULT_LIST.put("IDCARD_INFO", idcardInfo);

                    Map<String, Object> businessLicense = new HashMap<String, Object>();
                    businessLicense.put("LICENSE_NO", item.get("LICENSE_NO"));
                    businessLicense.put("REGISTER_NO", item.get("REGISTER_NO"));
                    businessLicense.put("CUST_NAME", item.get("CUST_NAME"));
                    businessLicense.put("CUST_TYPE", item.get("CUST_TYPE"));
                    businessLicense.put("ID_ADDRESS", item.get("UNIT_ADDRESS"));
                    businessLicense.put("LEGAL_PERSON", item.get("LEGAL_PERSON"));
                    businessLicense.put("FORMED", item.get("FORMED"));
                    businessLicense.put("BUSI_TERM", item.get("BUSI_TERM"));
                    businessLicense.put("LICENSE_File", "");

                    businessLicense.put("EXPIRATION_TIME", item.get("EXPIRATION_TIME"));
                    RESULT_LIST.put("BUSINESS_LICENSE", businessLicense);

                    RESULT_LIST.put("RELATED_FILE",item.get("RELATED_FILE"));
                    resultList.add(RESULT_LIST);
                }
            }

            map.put("RETURN_CODE", "0");
            map.put("RETURN_MSG", "OK");
            map.put("DETAIL_MSG", "OK");
            map.put("PAGE_COUNT", pageResponse.getTotalCount());

            Map<String, Object> outData = new HashMap<String, Object>();
            outData.put("RESULT_LIST", resultList);
            map.put("OUT_DATA", outData);
        } catch (Exception e) {
            e.printStackTrace();
            map.put("RETURN_CODE", "-1");
            map.put("RETURN_MSG", "查询失败");
            map.put("DETAIL_MSG", "查询失败");
        }

        rootMap = new HashMap<String, Object>();
        rootMap.put("ROOT", map);
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(rootMap).toString());
    }

    private String getFileUrl(String aid) {
        Attachment attachment = attachmentService.getAttachmentById(aid);

        return attachment != null ? FTP_URL + attachment.getAttachmentUrl() : "";
    }


    private GroupContactInfo addGroupCustomer(UnitInfo unitInfo, String attrId, JSONObject map, SystemUser su) throws UnsupportedEncodingException {
        GroupContactInfo groupContactInfo = new GroupContactInfo();

        groupContactInfo.setGroupName(UnitBase64Util.encode(unitInfo.getCustName()));       //集团名称
        groupContactInfo.setGroupCode(map.get("UNIT_ID").toString());                      //集团编号
        groupContactInfo.setCustomerEmail(map.get("CUST_ID").toString());                  //集团唯一标识
        groupContactInfo.setIdentNumber(unitInfo.getRegisterNo());      //集团证件号  证件编号
        groupContactInfo.setIdentAddress(UnitBase64Util.encode(unitInfo.getUnitAddress())); //集团地址 证件地址
        groupContactInfo.setContactFixedTelephone(unitInfo.getFixedPhone()); //集团联系人固定电话
        groupContactInfo.setContactMobilePhone(unitInfo.getPhone());  //集团联系人手机号
        groupContactInfo.setGroupAttId(attrId); //相关附件

        groupContactInfo.setContactsName(UnitBase64Util.encode(unitInfo.getIdCustName()));  //法人名称
        groupContactInfo.setContactsAddr(UnitBase64Util.encode(unitInfo.getIdAddress()));    //法人地址

        groupContactInfo.setPortrait("0");       //是否需要人像比对     1:是    0：否
        groupContactInfo.setPortraitId(unitInfo.getPortraitId());   //人像比对流水号

        //责任人信息
        groupContactInfo.setState(unitInfo.getUnitType());
        groupContactInfo.setSex("11".equals(unitInfo.getIdSexCode()) ? "11" : "12");
        groupContactInfo.setResp_nation("中国");
        groupContactInfo.setResp_nation_id("1");
        groupContactInfo.setIdentValidDate(unitInfo.getIdValidDate());                        //证件有效期
        groupContactInfo.setVisaOffice(unitInfo.getIdRegisterOffice());


        groupContactInfo.setCreateDate(new Date());
        groupContactInfo.setUser_name(su.getBossUserName());
        groupContactInfo.setChinese_name(su.getEmployeeName());
        groupContactInfo.setMobile_phone(su.getMobile());
        groupContactInfo.setUnitType(unitInfo.getCustType());  //集团类型（小微、正式）

        //2021.10.8新增

        groupContactInfo.setFormed(unitInfo.getFormed());     //证件成立时间
        groupContactInfo.setExpirationTime(unitInfo.getExpirationTime());     //证件失效日期
        groupContactInfo.setRegisteredCapital(unitInfo.getRegisteredCapital());             //注册资金
        groupContactInfo.setOrganizationType(unitInfo.getOrganizationType());           //组织机构类型

        //2021.11.12新增
        groupContactInfo.setAgentPersonPhone(unitInfo.getAgentPersonPhone());
        

        GroupContactInfo g = groupCustomerService.saveGroupContactInfo(groupContactInfo);

//        System.out.println(groupContactInfo.toString());
        return g;
    }

    /**
     * @Return: com.xinxinsoft.entity.enclosure.Attachment
     * @Author: Leo
     * @Date: 2021/7/27 18:32
     * @Description: 获取文件集合并根据文件地址路径下将多个文件压缩成压缩包，并删除文件集合和对应的文件
     */
    private Attachment unitInfoFileUpdate(String[] fileIdArray, SystemUser su,String fileTypeName) throws FileNotFoundException {
        List<Attachment> attachments = getAttid(fileIdArray);
        //查询出三个文件对象
        if (attachments.size() == 0) {
            return null;
        }
        List<File> files = new ArrayList<File>();
        String ftpUrl = "";
        ftpUrl = attachments.get(0).getAttachmentUrl();
        //根据附件地址获取文件存储地址
        ftpUrl=ftpUrl.substring(0,ftpUrl.lastIndexOf(File.separator)+1);

        for (Attachment attachment : attachments) {
            files.add(new File(FTP_URL + attachment.getAttachmentUrl()));
        }

        ftpUrl = createRAR(files, ftpUrl);
        //添加压缩文件到数据库
        Attachment attachment = new Attachment();
        attachment.setStatus("1");
        attachment.setAttachmentUrl(ftpUrl);
        attachment.setRealName(fileTypeName);
        attachment.setAttachmentName(ftpUrl.substring(ftpUrl.lastIndexOf(File.separator)+1));
        attachment.setDescribe(fileTypeName);
        attachment.setUploadUser(su);
        attachment.setUploadDate(new Date());
        attachmentService.addEntity(attachment);

        /*if(attachment!=null){
            //删除源文件
            FileUtil.delFiles(files);
        }*/
        return attachment;
    }

    private String createRAR(List<File> files, String ftpUrl) throws FileNotFoundException {
        StringBuilder newFileName = new StringBuilder();
        StringBuilder returnStr = new StringBuilder();

        Long time = System.currentTimeMillis();
        returnStr.append(ftpUrl).append("RAR_").append(time).append(".zip");
        newFileName.append(FTP_URL).append(returnStr);

        File zipFile=new File(newFileName.toString());
       // FileOutputStream fos2 = new FileOutputStream(new File(newFileName.toString()));
        ZipUtilsComm.zipFiles(files, zipFile);

        return returnStr.toString();
    }


    private List<Attachment> getAttid(String[] fileIdArray) {
        List<Attachment> list = new ArrayList<Attachment>();
        for (String s : fileIdArray) {
            Attachment attachment = attachmentService.getAttachmentById(s);
            if (attachment != null) {
                //修改为删除状态
                attachment.setStatus("1");
                attachment.setUploadDate(new Date());

                list.add(attachment);
            }
        }

        return list;
    }

    /**
     * 公共处理请求参数
     */
    private PackOprInfoVO getReqPatarms() {
        HttpServletResponse resp = getResponse();
        resp.setHeader("Access-Control-Allow-Origin", "*");
        resp.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
        resp.setHeader("Access-Control-Max-Age", "7200");
        resp.setHeader("Access-Control-Allow-Headers", "x-requested-with, Content-Type");
        resp.setHeader("Access-Control-Allow-Credentials", "true");

        PackOprInfoVO oprInfoVO = new PackOprInfoVO();
        oprInfoVO.setGroup_id(getString("u_group_id"));
        oprInfoVO.setLogin_no(getString("u_login_no"));
        oprInfoVO.setOp_code(getString("u_op_code"));
        oprInfoVO.setPower_right(getString("u_power_right"));
        oprInfoVO.setRegion_id(getString("u_region_id"));

        return oprInfoVO;
    }


    private UnitInfoDTO createUnitInfoDTO(JSONObject body) {
        UnitInfoDTO unitInfoDTO = new UnitInfoDTO();
        unitInfoDTO.setLoginNo(body.getString("LOGIN_NO"));
        if (body.containsKey("END_TIME")) {
            unitInfoDTO.setEndTime(body.getString("END_TIME"));
        }
        if (body.containsKey("START_TIME")) {
            unitInfoDTO.setStartTime(body.getString("START_TIME"));
        }
        if (body.containsKey("ID_ICCID")) {
            unitInfoDTO.setIdIccid(body.getString("ID_ICCID"));
        }
        if (body.containsKey("ID_TYPE")) {
            unitInfoDTO.setIdType(body.getString("ID_TYPE"));
        }
        if (body.containsKey("CUST_NAME")) {
            unitInfoDTO.setCustName(body.getString("CUST_NAME"));
        }
        unitInfoDTO.setPage(body.getInt("PAGE_NUM"));
        unitInfoDTO.setPageSize(body.getInt("PAGE_SIZE"));

        return unitInfoDTO;
    }


    private UnitInfo getUnitInfo() {
        UnitInfo unitInfo = new UnitInfo();
        //责任人信息
        //String idType  = getString("idType");       //集团证件类型
        String idCustName = getString("respPersonName"); //责任人名称
        String idcardNo = getString("respPersonIdCardNo");  //责任人身份证号码
        String idAddress = getString("respPersonIdAddress"); //责任人身份证地址
        Integer idSexCode = getInteger("respPersonSex"); //责任人性别
        String idBirthday = getString("respPersonBirthday");  //生日
        String idCountry = getString("respPersonCountry"); //国家国籍
        String idNation = getString("respPersonNation"); //民族
        String idRegisterOffice = getString("respPersonIdRegisterOffice"); //责任人签证机关
        String idRegisterDate = getString("respPersonIdValidDateStart");  //责任人签发时间
        String idValidDate = getString("respPersonIdValidDateEnd");  //责任人证件有效期 8位日期字符，格式为：YYYYMMDD
        //经办人信息
        String agentIdCustName = getString("agentPersonName"); //经办人名称
        String agentIdcardNo = getString("agentPersonIdCardNo"); //经办人身份证
        String agentIdAddress = getString("agentPersonIdAddress"); //身份证地址
        Integer agentIdSexCode = getInteger("agentPersonSex"); //经办人性别
        String agentIdBirthday = getString("agentPersonBirthday"); //生日
        String agentIdCountry = getString("agentPersonCountry");  //国家国籍
        String agentIdNation = getString("agentPersonNation"); //民族
        String agentIdRegisterOffice = getString("agentPersonIdRegisterOffice"); //经办人签证机关
        String agentIdRegisterDate = getString("agentPersonIdValidDateStart"); //经办人签发时间
        String agentIdValidDate = getString("agentPersonIdValidDateEnd");  //经办人证件有效期 8位日期字符，格式为：YYYYMMDD
        String agentPersonPhone = getString("agentPersonPhone"); //经办人手机号
        //	经办人委托授权有效期（字符格式：YYYY-MM-DD）
        String authorizationValidate=getString("agentPersonAuthorizationValidate");
        //集团相关信息
        String licenseNo = getString("licenseNo"); //社会信用代码 base64加密
        String registerNo = getString("registerNo"); // 注册号 base64加密
        String custName = getString("custName"); //集团名称
        String custType = getString("custType"); //集团类型
        String unitAddress = getString("unitAddress"); //集团地址
        String legalPerson = getString("legalPerson"); //法定代表人
        String formed = getString("formed"); // 成立时间
        String busiTerm = getString("busiTerm"); //营业期限
        String expirationTime = getString("expirationTime"); //失效时间
        String fixedPhone = getString("fixedPhone"); //固定电话
        String phone = getString("phone"); //联系电话
        String unitType = getString("unitType"); //集团状态 未知

        String portrait = getString("portrait"); //是否需要人像比对1:是    0：否
        String portraitId = getString("portraitId"); //人像比对流水号
        String industryId = getString("INDUSTRY_ID");//行业标签


        String registeredCapital = getString("registeredCapital");     //注册资金   2021.10.12新增
        String organizationType = getString("organizationType");       //组织机构类型 2021.10.12新增



        unitInfo.setRegisteredCapital(registeredCapital);
        unitInfo.setOrganizationType(organizationType);

        //unitInfo.setIdType(idType);
        unitInfo.setIdCustName(idCustName);
        unitInfo.setIdcardNo(idcardNo);
        unitInfo.setIdAddress(idAddress);
        unitInfo.setIdSexCode(idSexCode);
        unitInfo.setIdBirthday(idBirthday);
        unitInfo.setIdCountry(idCountry);
        unitInfo.setIdNation(idNation);
        unitInfo.setIdRegisterOffice(idRegisterOffice);
        unitInfo.setIdRegisterDate(idRegisterDate);
        unitInfo.setIdValidDate(idValidDate);

        unitInfo.setAgentIdcardNo(agentIdcardNo);
        unitInfo.setAgentIdAddress(agentIdAddress);
        unitInfo.setAgentIdValidDate(agentIdValidDate);
        unitInfo.setAgentIdRegisterDate(agentIdRegisterDate);
        unitInfo.setAgentIdCustName(agentIdCustName);
        unitInfo.setAgentIdRegisterOffice(agentIdRegisterOffice);
        unitInfo.setAgentIdSexCode(agentIdSexCode);
        unitInfo.setAgentIdBirthday(agentIdBirthday);
        unitInfo.setAgentIdNation(agentIdNation);
        unitInfo.setAgentIdCountry(agentIdCountry);
        unitInfo.setAuthorizationValidate(authorizationValidate);
        unitInfo.setAgentPersonPhone(agentPersonPhone);

        unitInfo.setLicenseNo(licenseNo);
        unitInfo.setRegisterNo(registerNo);
        unitInfo.setCustName(custName);
        unitInfo.setCustType(custType);
        unitInfo.setUnitAddress(unitAddress);
        unitInfo.setLegalPerson(legalPerson);
        unitInfo.setFormed(formed);
        unitInfo.setBusiTerm(busiTerm);
        unitInfo.setExpirationTime(expirationTime);
        unitInfo.setFixedPhone(fixedPhone);
        unitInfo.setPhone(phone);
        unitInfo.setUnitType(unitType);
        unitInfo.setPortrait(portrait);
        unitInfo.setPortraitId(portraitId);
        unitInfo.setIndustryId(industryId);

        return unitInfo;
    }


    /**
     * 经办人手号机验证
     */
    public void getLoginInfoByPhone(){
        try {
            String phoneNo = getString("phoneNo");
            String bossNo = getString("bossNo");
            Result result = unitInfoService.getLoginInfoByPhone(phoneNo, bossNo);
            Write(result.toString());
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
    }

    /**
     * 经办人手机号验证是否为移动号码
      */
    public void sZqQryProInfo(){
        try {
            String phoneNo = getString("phoneNo");
            String bossNo = getString("bossNo");
            Result result = UnitAccountInfoSrv.getInstance().sZqQryProInfo("", "",phoneNo, bossNo);
            Write(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @Description TODO 查询集团行业标签
     * <AUTHOR>
     * @Date 2022/5/20 15:20
     **/
    public void queryEnumerationIndustry(){
        Map<String, Object> mapJson = new HashMap<>();
       try {
            String classificationCode = getString("classificationCode");
            String industryName = getString("industryName");
            List<Map<String, String>> maps = unitInfoService.queryEnumerationIndustry(classificationCode,industryName);
            if (maps.size()>0){
                mapJson.put("code",200);
                mapJson.put("data",maps);
                mapJson.put("msg","查询成功！");
            }else {
                mapJson.put("code",400);
                mapJson.put("data",maps);
                mapJson.put("msg","查询失败，未查询到对应行业信息！");
            }
       }catch (Exception e){
           mapJson.put("code",500);
           mapJson.put("data","");
           mapJson.put("msg","数据处理异常，请联系管理员处理！");
       }
        Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
    }

    public void recordThirdPartyInfo(){
        Map<String, Object> mapJson = new HashMap<>();
        Map<String,String> map = getSecretKey();
        String companyId= getString("companyId");
        String groupCode= getString("groupCode");//集团280
        if("200".equals(map.get("code"))){
            Result result = unitInfoService.recordThirdPartyInfo(map.get("data").toString(),companyId,1,groupCode);
            if (result.getCode() == 200) {
                JSONObject obj = JSONObject.fromObject(result.getData());
                String rsCode = obj.getString("rsCode");
                if("0".equals(rsCode)){
                    mapJson.put("code",200);
                    mapJson.put("data","");
                    mapJson.put("msg","推送成功");
                }else{
                    mapJson.put("code",500);
                    mapJson.put("data","");
                    mapJson.put("msg","推送集客查查失败！"+obj.getString("rsCause"));
                }
            }else{
                mapJson.put("code",500);
                mapJson.put("data","");
                mapJson.put("msg","推送集客查查失败！"+result.getMessage());
            }
        }else{
            mapJson.put("code",500);
            mapJson.put("data","");
            mapJson.put("msg","获取key失败"+map.get("msg"));
        }
        Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
    }

    public Map<String,String> getSecretKey(){
        Map<String,String> map= new HashMap<>();
        Result result = unitInfoService.getSecretKey();
        if (result.getCode() == 200) {
            JSONObject obj = JSONObject.fromObject(result.getData());
            String rsCode = obj.getString("rsCode");
            if ("0".equals(rsCode)) {
                JSONObject dataObj = obj.getJSONObject("data");
                Integer error = dataObj.getInt("error");
                if (error == 0) {
                    String secretKey = dataObj.getString("secretKey");
                    map.put("code","200");
                    map.put("data",secretKey);
                    map.put("msg","获取KEY成功");
                }else{
                    map.put("code","500");
                    map.put("data","");
                    map.put("msg","获取身份验证key失败！data参数有误");
                }
            }else{
                map.put("code","500");
                map.put("data","");
                map.put("msg","获取身份验证key失败！"+obj.getString("rsCause"));
            }
        }else{
            map.put("code","500");
            map.put("data","");
            map.put("msg","获取身份验证key失败！"+result.getMessage());
        }
        return map;
    }

    /**
     * @描述 当前方法获取集团建档 集客查查第三方接入身份验证 每日身份验证key
     * @参数
     * @返回值
     * @创建人 李阳
     * @创建时间 2023/3/6 15:28
     * @修改人和其它信息 http://localhost:8080/EOM/unitInfo_getCompanyInfoByCompanyId.action
     */
    public void getCompanyInfoByCompanyId(){
        Map<String, Object> mapJson = new HashMap<>();
        try{
            String companyId = getString("companyId");
            String date = String.valueOf(Integer.parseInt(new SimpleDateFormat("yyyyMMdd").format(new Date())));
            if(keyMap.size()>0){
                Integer time = Integer.parseInt(keyMap.get("time").toString());
                if(Integer.parseInt(date)>time){
                    Result result = unitInfoService.getSecretKey();
                    if (result.getCode() == 200) {
                        JSONObject obj = JSONObject.fromObject(result.getData());
                        String rsCode = obj.getString("rsCode");
                        if("0".equals(rsCode)){
                            JSONObject dataObj = obj.getJSONObject("data");
                            Integer error = dataObj.getInt("error");
                            if(error==0){
                                String secretKey = dataObj.getString("secretKey");
                                keyMap.put("key",secretKey);
                                keyMap.put("time",date);
                                Result companyResult = unitInfoService.getCompanyInfoByCompanyId(secretKey,companyId);
                                if (companyResult.getCode() == 200) {
                                    JSONObject companyobj = JSONObject.fromObject(companyResult.getData());
                                    String companyRsCode = companyobj.getString("rsCode");
                                    if ("0".equals(companyRsCode)) {
                                        mapJson.put("code",200);
                                        mapJson.put("data",companyResult.getData());
                                        mapJson.put("msg","查询企业信息成功！"+companyobj.getString("rsCause"));
                                    }else{
                                        mapJson.put("code",400);
                                        mapJson.put("data","");
                                        mapJson.put("msg","查询企业信息失败！"+companyobj.getString("rsCause"));
                                    }
                                }else{
                                    mapJson.put("code",400);
                                    mapJson.put("data","");
                                    mapJson.put("msg","查询企业信息失败！"+companyResult.getMessage());
                                }
                            }else{
                                mapJson.put("code",400);
                                mapJson.put("data","");
                                mapJson.put("msg","获取身份验证key失败！data参数有误");
                            }
                        }else{
                            mapJson.put("code",400);
                            mapJson.put("data","");
                            mapJson.put("msg","获取身份验证key失败！"+obj.getString("rsCause"));
                        }
                    }else{
                        mapJson.put("code",400);
                        mapJson.put("data","");
                        mapJson.put("msg","获取身份验证key失败！"+result.getMessage());
                    }
                }else{
                    Result companyResult = unitInfoService.getCompanyInfoByCompanyId(keyMap.get("key").toString(),companyId);
                    if (companyResult.getCode() == 200) {
                        JSONObject companyobj = JSONObject.fromObject(companyResult.getData());
                        String companyRsCode = companyobj.getString("rsCode");
                        if ("0".equals(companyRsCode)) {
                            mapJson.put("code",200);
                            mapJson.put("data",companyResult.getData());
                            mapJson.put("msg","查询企业信息成功！"+companyobj.getString("rsCause"));
                        }else{
                            mapJson.put("code",400);
                            mapJson.put("data","");
                            mapJson.put("msg","查询企业信息失败！"+companyobj.getString("rsCause"));
                        }
                    }else{
                        mapJson.put("code",400);
                        mapJson.put("data","");
                        mapJson.put("msg","查询企业信息失败！"+companyResult.getMessage());
                    }
                }
            }else{
                Result result = unitInfoService.getSecretKey();
                if (result.getCode() == 200) {
                    JSONObject obj = JSONObject.fromObject(result.getData());
                    String rsCode = obj.getString("rsCode");
                    if("0".equals(rsCode)){
                        JSONObject dataObj = obj.getJSONObject("data");
                        Integer error = dataObj.getInt("error");
                        if(error==0){
                            String secretKey = dataObj.getString("secretKey");
                            keyMap.put("key",secretKey);
                            keyMap.put("time",date);
                            Result companyResult = unitInfoService.getCompanyInfoByCompanyId(secretKey,companyId);
                            if (companyResult.getCode() == 200) {
                                JSONObject companyobj = JSONObject.fromObject(companyResult.getData());
                                String companyRsCode = companyobj.getString("rsCode");
                                if ("0".equals(companyRsCode)) {
                                    mapJson.put("code",200);
                                    mapJson.put("data",companyResult.getData());
                                    mapJson.put("msg","查询企业信息成功！"+companyobj.getString("rsCause"));
                                }else{
                                    mapJson.put("code",400);
                                    mapJson.put("data","");
                                    mapJson.put("msg","查询企业信息失败！"+companyobj.getString("rsCause"));
                                }
                            }else{
                                mapJson.put("code",400);
                                mapJson.put("data","");
                                mapJson.put("msg","查询企业信息失败！"+companyResult.getMessage());
                            }
                        }else{
                            mapJson.put("code",400);
                            mapJson.put("data","");
                            mapJson.put("msg","获取身份验证key失败！data参数有误");
                        }
                    }else{
                        mapJson.put("code",400);
                        mapJson.put("data","");
                        mapJson.put("msg","获取身份验证key失败！"+obj.getString("rsCause"));
                    }
                }else{
                    mapJson.put("code",400);
                    mapJson.put("data","");
                    mapJson.put("msg","获取身份验证key失败！"+result.getMessage());
                }
            }
            Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
        }catch (Exception e){
            logger.error("集客查查第三方接入身份验证错误："+e.getMessage(),e);
            mapJson.put("code",500);
            mapJson.put("data","");
            mapJson.put("msg","查询企业信息失败！");
            Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
        }
    }

    public void getGuideDocumentation(){
        Map<String, Object> mapJson = new HashMap<>();
        try{
            String companyId = getString("companyId");
            GuideDocumentation gd= unitInfoService.getGuideDocumentation(companyId);
            if(gd!=null){
                mapJson.put("data",gd.getContent());
            }else{
                mapJson.put("data","");
            }
            mapJson.put("code",200);
            mapJson.put("msg","查询成功！");
            Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
        }catch (Exception e){
            logger.error("集客查查对接指南针建档本地获取参数失败："+e.getMessage(),e);
            mapJson.put("code",500);
            mapJson.put("data","");
            mapJson.put("msg","查询失败！");
            Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
        }
    }

    public void removeKeyMap(){
        keyMap.clear();
    }


    public UnitInfoService getUnitInfoService() {
        return unitInfoService;
    }

    public void setUnitInfoService(UnitInfoService unitInfoService) {
        this.unitInfoService = unitInfoService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public GroupCustomerService getGroupCustomerService() {
        return groupCustomerService;
    }

    public void setGroupCustomerService(GroupCustomerService groupCustomerService) {
        this.groupCustomerService = groupCustomerService;
    }

    public IBossByNoService getiBossByNoService() {
        return iBossByNoService;
    }

    public void setiBossByNoService(IBossByNoService iBossByNoService) {
        this.iBossByNoService = iBossByNoService;
    }
}
