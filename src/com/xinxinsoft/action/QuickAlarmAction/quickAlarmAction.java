package com.xinxinsoft.action.QuickAlarmAction;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.sendComms.QuickAlarmSrv;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.EncryptionUtils;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.*;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * @Description: TODO 系统保障管理类
 * @Author: TX
 * @Date: 2022/9/21 10:15
 * @Version: 1.0
 */
public class quickAlarmAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(quickAlarmAction.class);
    private static Boolean isES = false;
    //测试环境
    private static final String ESB_URL_236= "http://**************:8080/operator";
    //正式环境
    private static final String ESB_URL_86 = "http://*************:8080/operator";
    static {
        if("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())){
            isES = true;
        }
    }

    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;

    private File file;

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }


    /**
     * @Description 一键报障-查询故障单列表
     * <AUTHOR>
     * @Date 2022/10/28 15:36
     **/
    public void oneClickReportFaultList(){
        try{
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数
            LayuiPage page = new LayuiPage(pageNo, pagesize);

            String groupStr = getString("groupStr");
            Integer status = getInteger("status");
            Integer createWay = getInteger("create_way");
            String provinceOrderCode = getString("province_order_code");
            String serialNumber = getString("serial_number");
            Result result = QuickAlarmSrv.getInstance().queryOneClickReportFaultList(cookies,(pageNo/10+1),pagesize,groupStr,status,createWay,provinceOrderCode,serialNumber);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONObject resultObj=obj.getJSONObject("result");
                    Long count = (long)resultObj.getInt("total");
                    JSONArray rowsArray = resultObj.getJSONArray("rows");
                    page.setCount(count);
                    page.setData(rowsArray);
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，故障单列表查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，故障单列表查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }

        }catch (Exception e){
            logger.error("查询故障单列表异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，查询故障单列表异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 快捷报障-查询故障单列表
     * <AUTHOR>
     * @Date 2022/10/28 15:36
     **/
    public void queryQuickReportFault(){
        try{
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数
            LayuiPage page = new LayuiPage(pageNo, pagesize);

            String faultTitle = getString("faultTitle");
            Integer status = getInteger("status");
            Result result = QuickAlarmSrv.getInstance().queryQuickReportFault(cookies,(pageNo/10+1),pagesize,faultTitle,status);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONObject resultObj=obj.getJSONObject("result");
                    Long count = (long)resultObj.getInt("total");
                    JSONArray rowsArray = resultObj.getJSONArray("rows");
                    page.setCount(count);
                    page.setData(rowsArray);
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，故障单列表查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，故障单列表查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }

        }catch (Exception e){
            logger.error("查询故障单列表异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，查询故障单列表异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }



    /**
     * @Description 新增一键报障
     * <AUTHOR>  
     * @Date 2022/9/23 16:09 
     **/
    public void addOneClickReportFault(){
        try{
            String faultTypeId = getString("fault_type_id");
            String custCode = getString("cust_code");
            String productPrcid = getString("product_prcid");
            String poductName = getString("poduct_name");
            String productInstance = getString("product_instance");
            String provinceOrderCode = getString("province_order_code");
            String serialNumber = getString("serial_number");
            String isFirstLevel = getString("is_first_level");
            String wholeNetProductCode = getString("whole_net_product_code");
            String wholeNetCustomerCode = getString("whole_net_customer_code");
            String faultDesc = getString("fault_desc");
            String faultTime = getString("fault_time");
            String attachList = getString("attach_list");

            String createTime = getDateString(new Date());
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().addOneClickReportFault(faultTypeId,custCode,poductName,productPrcid,productInstance,provinceOrderCode
                    ,serialNumber,isFirstLevel,wholeNetProductCode, wholeNetCustomerCode,faultDesc,faultTime,createTime,attachList,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，故障单已生成请在待办界面查询进度!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单推送失败【"+obj.getString("res_message")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单推送失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单推送异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单推送异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 一键报障-pc端发起、不发起智能报障
     * <AUTHOR>
     * @Date 2022/9/23 16:28
     **/
    public void launchIntelligentReportFault(){
        try {
            String operateType = getString("operate_type");
            Integer faultOrderId = getInteger("fault_order_id");

            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().launchIntelligentReportFault(operateType,faultOrderId,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单处理成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单处理失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单处理失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单处理异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单处理异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 一键报障-重派报障单
     * <AUTHOR>
     * @Date 2023/11/2 10:32
     **/
    public void reLaunchOneClickReportFault(){
        try {
            String relaunchReason = getString("relaunch_reason");
            Integer faultOrderId = getInteger("fault_order_id");

            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().reLaunchOneClickReportFault(relaunchReason,faultOrderId,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单重派成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单重派失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单重派失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单重派异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单重派异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 一键报障-故障单归档
     * <AUTHOR>
     * @Date 2023/11/2 10:57
     **/
    public void fileOneClickReportFault(){
        try {
            String feedback = getString("feedback");
            Integer satisfaction = getInteger("satisfaction");
            Integer faultOrderId = getInteger("fault_order_id");

            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().fileOneClickReportFault(feedback,satisfaction,faultOrderId,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单归档成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单归档失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单归档失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单归档异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单归档异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 一键报障-故障单催单
     * <AUTHOR>
     * @Date 2023/11/2 10:59
     **/
    public void reminderOneClickReportFault(){
        try {
            String reason = getString("reason");
            Integer faultOrderId = getInteger("fault_order_id");

            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().reminderOneClickReportFault(reason,faultOrderId,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单催单成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单催单失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单催单失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单催单异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单催单异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 一键报障-提交工单中途意见
     * <AUTHOR>
     * @Date 2023/11/2 11:03
     **/
    public void addOneClickReportFaultOpinion(){
        try {
            String opinion = getString("opinion");
            Integer faultOrderId = getInteger("fault_order_id");

            String attachList = getString("attach_list");
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().addOneClickReportFaultOpinion(faultOrderId,opinion,attachList,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单中途意见提交成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单中途意见提交失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单中途意见提交失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单中途意见提交异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单中途意见提交异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 新增快捷报障
     * <AUTHOR>
     * @Date 2022/9/23 16:28 
     **/
    public void saveQuickReportFault(){
        try {
            String faultType = getString("fault_type");
            String faultDesc = getString("fault_desc");

            String attachList = getString("attach_list");
            String faultTime = getDateString(new Date());
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().saveQuickReportFault(faultType,faultDesc,faultTime,attachList,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，故障单已生成请在待办界面查询进度!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，快捷报障工单推送失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，快捷报障工单推送失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("快捷报障工单推送异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，快捷报障工单推送异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }



    /**
     * @Description 系统报障附件上传
     * <AUTHOR>
     * @Date 2022/9/23 14:08
     **/
    public void uploadReportFaultFile(){
        try {
            String fileName = getString("fileName");
            if (file != null) {
                String loginNo;
                if (user.getMobile()==null || "".equals(user.getMobile())){
                    Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                    return;
                }else {
                    loginNo = EncryptionUtils.encrypt(user.getMobile());
                }
                Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
                String[] cookies;
                if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                    cookies = (String[])cookieResilt.getData();
                }else {
                    Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                    return;
                }
                // 调用自定义的post数据方法，提交表单数据及上传文件
                String pathUrl = ESB_URL_86;
                if(isES) {
                    //正式服务器
                    pathUrl = ESB_URL_236;
                }
                Result result = QuickAlarmSrv.getInstance().ResPonseByFile(pathUrl+"/quickReportFault/uploadReportFaultFile.do",  file,fileName, "utf-8",cookies);
                if (result.getCode()== ResultCode.SUCCESS.code()){
                    JSONObject obj=JSONObject.fromObject(result.getData());
                    if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                        JSONArray faultFileList = obj.getJSONArray("result");
                        Write(returnPars(1,faultFileList,"上传成功！"));
                    }else {
                        Write(returnPars(-1,"","亲爱的同事，文件上传失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                    }
                }else {
                    Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，文件信息异常，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("文件信息上传异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，文件信息上传异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 一键报障/快捷报障-查询工单全量轨迹
     * <AUTHOR>
     * @Date 2023/6/6 16:07
     **/
    public void quickReportFaultOrderTrack(){
        try{
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            String bomcOrderId = getString("bomc_order_id");
            Result result = QuickAlarmSrv.getInstance().quickReportFaultOrderTrack(cookies,bomcOrderId);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONObject resultObj=obj.getJSONObject("result");
                    Write(returnPars(1,resultObj,"查询成功！"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，故障工单全量轨迹查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，故障工单全量轨迹查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("故障工单全量轨迹查询异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，故障工单全量轨迹查询异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 一键报障-查询故障单详情
     * <AUTHOR>
     * @Date 2022/10/28 17:33
     **/
    public void queryOneClickReportFaultDetail(){
        try{
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Integer faultOrderId = getInteger("fault_order_id");
            Result result = QuickAlarmSrv.getInstance().queryOneClickReportFaultDetail(cookies,faultOrderId);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONObject resultObj=obj.getJSONObject("result");
                    Write(returnPars(1,resultObj,"查询成功！"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，故障单详情查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，故障单详情查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("查询故障单详情异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，查询故障单详情异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }


    /**
     * @Description 一键报障-查询故障类型列表
     * <AUTHOR>
     * @Date 2022/9/21 10:51
     **/
    public void queryFaultTypeList() {
        try{
            String loginNo;
            Map<String,Object> retMap = new HashMap<>();
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().queryFaultTypeList(cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONArray quickReportFaultList = obj.getJSONArray("result");
                    if (quickReportFaultList.size()>0){
                        retMap.put("QuickReportFaultList",quickReportFaultList);
                    }else {
                        Write(returnPars(2,"","亲爱的同事，未查询到故障类型信息，请确认!"));
                    }
                }else {
                    Write(returnPars(-1,"","亲爱的同事，故障类型信息查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，故障类型信息查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
            Write(returnPars(1,retMap,"查询成功！"));
        }catch (Exception e){
            logger.error("故障类型信息查询异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，故障类型信息查询异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }


    /**
     * @Description 快捷报障-查询故障类型列表
     * <AUTHOR>
     * @Date 2023/11/1 15:25
     **/
    public void queryQuickReportFaultType(){
        try{
            String loginNo;
            Map<String,Object> retMap = new HashMap<>();
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().queryQuickReportFaultType(cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONArray quickReportFaultTypeList = obj.getJSONArray("result");
                    if (quickReportFaultTypeList.size()>0){
                        retMap.put("QuickReportFaultTypeList",quickReportFaultTypeList);
                    }else {
                        Write(returnPars(2,"","亲爱的同事，未查询到故障类型信息，请确认!"));
                    }
                }else {
                    Write(returnPars(-1,"","亲爱的同事，故障类型信息查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，故障类型信息查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
            Write(returnPars(1,retMap,"查询成功！"));
        }catch (Exception e){
            logger.error("故障类型信息查询异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，故障类型信息查询异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 快捷报障-查询工单详细信息
     * <AUTHOR>
     * @Date 2023/11/1 15:25
     **/
    public void queryQuickReportFaultInfo(){
        try{
            String loginNo;
            Map<String,Object> retMap = new HashMap<>();
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Integer faultOrderId = getInteger("fault_order_id");

            Result result = QuickAlarmSrv.getInstance().queryQuickReportFaultInfo(faultOrderId,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONObject resultObj=obj.getJSONObject("result");
                    Write(returnPars(1,resultObj,"查询成功！"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，工单详细信息查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，工单详细信息查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("工单详细信息查询异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，工单详细信息查询异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 快捷报障-工单重新派发
     * <AUTHOR>
     * @Date 2023/11/15 9:47
     **/
    public void quickReportFaultRemake(){
        try {
            Integer faultOrderId = getInteger("fault_order_id");
            String relaunchReason = getString("relaunch_reason");

            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().quickReportFaultRemake(faultOrderId,relaunchReason,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单重新派发成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单重新派发失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单重新派发失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单重新派发异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单重新派发异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 快捷报障-工单归档
     * <AUTHOR>
     * @Date 2023/11/15 9:49
     **/
    public void quickReportFaultArchived(){
        try {
            Integer faultOrderId = getInteger("fault_order_id");
            Integer satisfaction = getInteger("satisfaction");
            String feedback = getString("feedback");

            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().quickReportFaultArchived(faultOrderId,satisfaction,feedback,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单归档成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单归档失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单归档失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单归档异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单归档异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 快捷报障-工单催单
     * <AUTHOR>
     * @Date 2023/11/15 9:51
     **/
    public void quickReportFaultReminder(){
        try {
            Integer faultOrderId = getInteger("fault_order_id");
            String reason = getString("reason");

            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().quickReportFaultReminder(faultOrderId,reason,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单催单成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单催单失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单催单失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单催单异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单催单异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 快捷报障-工单添加中途意见
     * <AUTHOR>
     * @Date 2023/11/15 9:52
     **/
    public void quickReportFaultOpinion(){
        try {
            String opinion = getString("opinion");
            Integer faultOrderId = getInteger("fault_order_id");

            String attachList = getString("attach_list");
            String loginNo;
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().quickReportFaultOpinion(faultOrderId,opinion,attachList,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    Write(returnPars(1,"","亲爱的同事，报障工单中途意见提交成功!"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，报障工单中途意见提交失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，报障工单中途意见提交失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("报障工单中途意见提交异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，报障工单中途意见提交异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description 系统报障集团查询
     * <AUTHOR>
     * @Date 2022/9/21 10:51
     **/
    public void queryGroupByCode() {
        try{
            String groupCode = getString("groupCode");
            String loginNo;
            if (groupCode==null || "".equals(groupCode)){
                Write(returnPars(-1,"","亲爱的同事，集团编号参数异常，请刷新页面重试或联系管理员处理！"));
                return;
            }
            if (user.getMobile()==null || "".equals(user.getMobile())){
                Write(returnPars(-1,"","亲爱的同事，当前订单用户信息异常，用户号码获取失败，请确认！"));
                return;
            }else {
                loginNo = EncryptionUtils.encrypt(user.getMobile());
            }
            Result cookieResilt = QuickAlarmSrv.getInstance().otherSystemLogin("utf-8",loginNo);
            String[] cookies;
            if (cookieResilt.getCode()== ResultCode.SUCCESS.code() && cookieResilt.getData()!=null && cookieResilt.getData().getClass().isArray()){
                cookies = (String[])cookieResilt.getData();
            }else {
                Write(returnPars(-1,"","亲爱的同事，用户单点登录失败："+cookieResilt.getMessage()+"，请刷新页面重试或联系管理员处理!"));
                return;
            }
            Result result = QuickAlarmSrv.getInstance().queryGroupByCode(groupCode,cookies);
            if (result.getCode()== ResultCode.SUCCESS.code()){
                JSONObject obj=JSONObject.fromObject(result.getData());
                if (obj.has("res_code") && "00000".equals(obj.getString("res_code"))){
                    JSONObject resultObj=obj.getJSONObject("result");
                    JSONArray rowsArray = resultObj.getJSONArray("rows");
                    if (rowsArray.size()>0){
                        Write(returnPars(1,rowsArray.get(0),"查询成功！"));
                    }else {
                        Write(returnPars(2,"","亲爱的同事，当前用户未查询到【"+groupCode+"】对应集团信息，请确认!"));
                    }
                }else {
                    Write(returnPars(-1,"","亲爱的同事，集团查询失败【"+obj.getString("desc")+"】，请刷新页面重试或联系管理员处理!"));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事，集团查询失败："+result.getMessage()+"，请刷新页面重试或联系管理员处理!"));
            }
        }catch (Exception e){
            logger.error("系统保障集团查询异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，集团信息查询异常："+e.getMessage()+"，请刷新页面重试或联系管理员处理!"));
        }
    }

    /**
     * @Description TODO 返回参数生成
     * <AUTHOR>
     * @param state code响应码
     * @param data  响应对象
     * @param msg   响应信息
     * @return java.lang.String
     * @Date 2022/7/13 14:45
     **/
    private static String returnPars(int state,Object data,String msg){
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code",state);
        mapJson.put("data",data);
        mapJson.put("msg",msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    /**
     * @Description TODO 日期格式转换
     * <AUTHOR>
     * @param currentTime   日期
     * @return java.lang.String
     * @Date 2022/8/10 16:12
     **/
    public static String getDateString(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        return formatter.format(currentTime);
    }
}
