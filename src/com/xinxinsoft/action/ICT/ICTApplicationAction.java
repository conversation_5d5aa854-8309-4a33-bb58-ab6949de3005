package com.xinxinsoft.action.ICT;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import com.xinxinsoft.sendComms.accountService.UnitAccountInfoSrv;
import com.xinxinsoft.sendComms.unitService.UnitInfoSrv;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;
import org.apache.commons.lang.StringUtils;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.google.gson.GsonBuilder;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.ICT.ICTApplication;
import com.xinxinsoft.entity.ICT.ICTApplicationDet;
import com.xinxinsoft.entity.ICT.ICTConfiguration;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.service.ICT.ICTApplicationService;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;

/**
 * @Author: Leo
 * @Date: 2021/8/24 18:15
 * @Description:IDC管理action
 */
public class ICTApplicationAction extends BaseAction{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/*private static ResourceBundle s = ResourceBundle.getBundle("WebService-config");
	private static String ESB_URL = s.getString("ESB_URL");*/
	private static final Logger logger = LoggerFactory.getLogger(ICTApplicationAction.class);
	private ICTApplicationService ictApplicationService;
	private WaitTaskService service;
	private SystemUserService systemUserService;
	private TransferJBPMUtils transferJBPMUtils;
	private JbpmUtil jbpmUtil;
	private Bpms_riskoff_service taskService;
	public WaitTaskService getService() {
		return service;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public TransferJBPMUtils getTransferJBPMUtils() {
		return transferJBPMUtils;
	}

	public JbpmUtil getJbpmUtil() {
		return jbpmUtil;
	}

	public void setService(WaitTaskService service) {
		this.service = service;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
		this.transferJBPMUtils = transferJBPMUtils;
	}

	public void setJbpmUtil(JbpmUtil jbpmUtil) {
		this.jbpmUtil = jbpmUtil;
	}

	public ICTApplicationService getIctApplicationService() {
		return ictApplicationService;
	}

	public void setIctApplicationService(ICTApplicationService ictApplicationService) {
		this.ictApplicationService = ictApplicationService;
	}

	public Bpms_riskoff_service getTaskService() {
		return taskService;
	}

	public void setTaskService(Bpms_riskoff_service taskService) {
		this.taskService = taskService;
	}

	/**
	 * @Author: Leo
	 * @Date: 2021/8/24 18:24
	 * @Description:获取纳税人信息
	 */
	public void getUnitTaxPlayerInfo(){
		String unitId=getString("unitId");
		Result result= ResultGenerator.genFailResult("");
		if(unitId==null || "".equals(unitId)){
			result.setMessage("亲爱的同事，系统获取集团280异常,请核实是否选择集团");
			Write(result.toString());
			return;
		}
		result=UnitInfoSrv.getInstance().getUnitTaxPlayerInfo(unitId,user.getBossUserName());
//		result.setCode(ResultCode.SUCCESS);
//		result.setData("{ \"ROOT\": { \"RETURN_CODE\": 0, \"RETURN_MSG\": \"ok!\", \"USER_MSG\": \"处理成功!\", \"DETAIL_MSG\": \"OK!\", \"PROMPT_MSG\": \"\", \"OUT_DATA\": { \"DATA\": { \"TAXPAYER_ID\": \"*************\", \"UNIT_NAME\": \"UnKnown\", \"ADDRESS\": \"UnKnown\", \"PHONE_NO\": \"MTg4Mjg2ODAyMjA\\u003d\", \"BANK_NAME\": \"系统测试\", \"BANK_ACCOUNT\": \"UnKnown\", \"BILL_TYPE\": \"1\" } } } }");
		Write(result.toString());
	}
	/**
	 * @Author: Leo
	 * @Date: 2021/8/24 19:20
	 * @Description:根据集团编码获取集团下的产品信息
	 */
	public void getZXProdInfo(){
		String unitId=getString("unitId");
		String phoneNo=getString("phoneNo");  //用户号码
		String groupNo=getString("groupNo");  //产品号码
		if(phoneNo==null){
			phoneNo="";
		}
		if(groupNo==null){
			groupNo="";
		}
		if(unitId==null){
			unitId="";
		}
		Result result= UnitAccountInfoSrv.getInstance().sZqQryProInfo(unitId,groupNo,phoneNo,user.getBossUserName());
		Write(result.toString());

	}
	/**
	 * 新增ICT申请
	 */
	public void addICTApplication() {
		try {
			String role = getString("role");
			String id = getString("id");
			Integer userid = getInteger("userid");
			String title = getString("title");
			long amount = getLong("amount");
			String groupCoding = getString("groupCoding");
			String groupName = getString("groupName");
			String phone_no = getString("phone_no");
			String explanation = getString("explanation");
			String waitId = getString("waitId");
			String attachmentId = getString("attachmentId");
			String json = getString("json");
			String copyo=getString("copyo");
			Integer invoiceType = getInteger("invoiceType");
			String contractNo = getString("contractNo");//账户号码
			String bustId = getString("bustId");//客户ID
			String auditId = getString("auditId");//审批工号
			String taxPayer = getString("taxPayer");//纳税人识别号
			String taxAddress = getString("taxAddress");//纳税人地址
			String taxPhone = getString("taxPhone");//纳税人电话
			String taxBankName = getString("taxBankName");//纳税人银行名称
			String taxBankAccount = getString("taxBankAccount");//纳税人银行账号
			String taxName = getString("taxName");//纳税人名称
			String whether = getString("whether");//是否合并发票
			String billingItems = getString("billingItems");//开票项目
			String billingChannel = getString("billingChannel");//开票渠道
			String numberProjects = getString("numberProjects");//项目数量
			String msgrecvPhoneNumber = getString("msgrecvPhoneNumber");

			//Integer salesType = getInteger("salesType");
			/*String branchFileUrl =getString("branchFileUrl");
			String customerLetterFileUrl =getString("customerLetterFileUrl");
			String systemFileUrl =getString("systemFileUrl");
			String productsFileUrl =getString("productsFileUrl");*/
			List<Object[]> sone = ictApplicationService.getbumen(user.getRowNo());
			String district = (String) sone.get(0)[0];
			String companyName = (String) sone.get(0)[1];
			String IBM = (String) sone.get(0)[2];
			String companyCode=(String) sone.get(0)[3];
			String sateTime = getTimestamp(new Date());
			ICTApplication ict = new ICTApplication();
			if(invoiceType==0){
				ict.setNumbering(IBM + sateTime);
			}else{
				Random random = new Random();
				int ends = random.nextInt(99);
				String number = String.format("%02d",ends);//如果不足两位，前面补0
				ict.setNumbering("SDXT"+getTimestampTwo(new Date())+number);
			}
			ict.setTitle(title);
			ict.setExplanation(explanation);
			ict.setCompanyName(companyName);
			ict.setCompanyCode(companyCode);
			ict.setDistrict(district);
			ict.setCreator(user.getRowNo() + "");
			ict.setCreatorName(user.getEmployeeName());
			ict.setCreatorPhone(user.getMobile());
			ict.setCreateDate(new Date());
			ict.setGroupCode(groupCoding);
			ict.setGroupName(groupName);
			ict.setProductNumber(phone_no);
			ict.setInvoiceType(invoiceType);
			ict.setOperationType(1);
			ict.setState(1);
			ict.setAmount(amount);
			ict.setContractNo(contractNo);
			ict.setBustId(bustId);
			ict.setAuditId(auditId);
			ict.setTaxPayer(taxPayer);
			ict.setTaxAddress(taxAddress);
			ict.setTaxPhone(taxPhone);
			ict.setTaxBankName(taxBankName);
			ict.setTaxBankAccount(taxBankAccount);
			ict.setWhether(whether);
			ict.setBillingItems(billingItems);
			ict.setBillingChannel(billingChannel);
			ict.setNumberProjects(numberProjects);
			ict.setTaxName(taxName);
			ict.setMsgRecvPhone(msgrecvPhoneNumber);
			/*ict.setBranchFileUrl(branchFileUrl);
			ict.setCustomerLetterFileUrl(customerLetterFileUrl);
			ict.setSystemFileUrl(systemFileUrl);
			ict.setProductsFileUrl(productsFileUrl);*/
			ICTApplication ictl = ictApplicationService.saveICTApplication(ict);
			JSONArray jsonObject1 = JSONArray.fromObject(json);
			if(invoiceType==0){
				for (int i=0;i<jsonObject1.size();i++){
					String s = jsonObject1.getString(i);
					JSONObject data = JSONObject.fromObject(s);
					ICTApplicationDet ictd = new ICTApplicationDet();
					ictd.setMasterWorkOrderID(ictl.getId());
					Random random = new Random();
					int ends = random.nextInt(9);
					ictd.setDetailedCoding("SDXT"+getTimestampTwo(new Date())+ends+i);
					ictd.setSalesAmount(data.getLong("salesAmount"));
					ictd.setInvoiceAmount(data.getLong("invoiceAmount"));
					ictd.setSalesType(data.getInt("salesType"));
					ictd.setProductAbbreviation(data.getString("productAbbreviation"));
					ictd.setProductName(data.getString("productName"));
					ictd.setWhetherToInvoice("N");
					ictd.setReductionBossStatus("0");
					ictd.setCount(data.getString("count"));
					ictd.setTaxIncluded(data.getLong("taxIncluded"));
					ictd.setTaxRate(data.getString("taxRate"));
					ictd.setTaxMoney(data.getLong("taxMoney"));
					ictd.setPrice(data.getLong("price"));
					ictd.setSizeModel(data.getString("sizeModel"));
					ictApplicationService.saveICTApplicationDet(ictd);
				}
			}else{
				for (int i=0;i<jsonObject1.size();i++){
					String s = jsonObject1.getString(i);
					JSONObject data = JSONObject.fromObject(s);
					ICTApplicationDet ictd = new ICTApplicationDet();
					ictd.setMasterWorkOrderID(ictl.getId());
					Random random = new Random();
					int ends = random.nextInt(9);
					ictd.setDetailedCoding("SDXT"+getTimestampTwo(new Date())+ends+i);
					ictd.setSalesAmount(data.getLong("salesAmount"));
					ictd.setSalesVolumes(data.getString("salesVolumes"));
					ictd.setNatureInvoiceLine(data.getString("natureInvoiceLine"));
					ictd.setTaxMark(data.getString("taxMark"));
					ictd.setInvoiceAmount(data.getLong("invoiceAmount"));
					ictd.setSalesType(data.getInt("salesType"));
					ictd.setProductAbbreviation(data.getString("productAbbreviation"));
					ictd.setProductName(data.getString("productName"));
					ictd.setWhetherToInvoice("N");
					ictd.setReductionBossStatus("0");
					ictApplicationService.saveICTApplicationDet(ictd);
				}
			}
			/*
			 * Map<String, String> map = new HashMap<String, String>();
			 * map.put("decisionKey", "APPLY"); map.put("decisionVal", role);
			 * 所属地区（区县：QX、市公司：SGS、省重客：SZK）
			 */
			Map<String, String> map = new HashMap<String, String>();
			map.put("decisionKey", "APPLY");
			map.put("decisionVal", role);
			String processId = jbpmUtil.startPIByKey("ICTApplication", map).getId();
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			taskService.setBpms_riskoff_process(ictl.getId(), processId, 1, user);
			taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
			String taskid= taskService.setBpms_riskoff_task(processId,null, 1, "SH", task.getActivityName(),userid, user);//预存下一步任务
			List<SingleAndAttachment> ss = ictApplicationService.getSingleAndAttachment(id);//查询已存在附件
			// 遍历获取的附件中间表数据
			if (ss != null) {
				for (int i = 0; i < ss.size(); i++) {
					if(i==0){
						if(!"".equals(attachmentId)){
							attachmentId += ","+ss.get(i).getAttachmentId()+",";
						}else{
							attachmentId += ss.get(i).getAttachmentId()+",";
						}
					}else{
						attachmentId += ss.get(i).getAttachmentId() + ",";
					}
				}
			}
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] jsonAttachment = attachmentId.split(",");
					if (jsonAttachment.length > 0) {
						for (int i = 0; i < jsonAttachment.length; i++) {
							SingleAndAttachment sah = new SingleAndAttachment();
							sah.setOrderID(ictl.getId());
							sah.setAttachmentId(jsonAttachment[i]);
							sah.setLink(ICTApplication.ICTApplication);
							ictApplicationService.saveSandA(sah);
						}
					}
				}
			}
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(!"copy".equals(copyo)){
				if (wait != null) {
					service.updateWait(wait, this.getRequest());
				} else {
					if (!"".equals(id) && id != null) {
						Write("NO");
						return;
					}
				}
			}
			commitUpcoming(ictl,userid,processId,user,taskid);// 代办
			Write("OK");
		} catch (Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 流程进行
	 */
	public void handleICTApplication() {
		try {
			String processId = getString("processId");// 流程id
			String t = getString("nextTask");// 下一步可执行流程线条值
			Integer userid = getInteger("userId");// 用户id
			String id = getString("id");// 主信息id
			String opinion = getString("opinion");//审批意见
			String waitId = getString("waitId");//待办id
			String taskId = getString("taskid");//任务id
			List<SystemDept> deptList = user.getSystemDept();
			String code = deptList.get(0).getSystemCompany().getCompanyCode();
			ICTApplication ict = ictApplicationService.getICTApplication(id);
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			Map<String, String> map = new HashMap<String, String>();
			TransferCitiesData transferCitiesData= taskService.getTransferCitiesData(code,task.getActivityName(),"ICTApplication");
			if ("市公司政企部经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SGSZQBJL");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				if(t.equals("ALL")){
					jbpmUtil.completeTask(task.getId(), t);
				}else{
					jbpmUtil.completeTask(task.getId(), map,t);
				}
			}else if ("市公司政企部经理副".equals(task.getActivityName())){
				TransferCitiesData transferCitiesDataf= ictApplicationService.getTransferCitiesData(code,"市公司政企部经理");
				map.put("decisionKey", "SGSZQBJL");
				if(Double.parseDouble(transferCitiesDataf.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesDataf.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesDataf.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				if(t.equals("ALL")){
					jbpmUtil.completeTask(task.getId(), t);
				}else{
					jbpmUtil.completeTask(task.getId(), map,t);
				}
			}else if ("区县分管经理".equals(task.getActivityName())) {
				map.put("decisionKey", "QXFGJL");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if ("省重客客户经理室经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SZKKHJLSJL");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else {
				jbpmUtil.completeTask(task.getId(), t);
			}
			// 保存下一步任务信息
			Task taskt = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			taskService.updateBpms_riskoff_task(opinion, 2, taskId);
			String rtaskid =taskService.setBpms_riskoff_task(processId,"",1,"SH",taskt.getActivityName(),userid, user);
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if (wait != null) {
				service.updateWait(wait, this.getRequest());
			} else {
				Write("NO");
				throw new Error("待办ID==========：" + waitId);
			}
			commitUpcoming(ict, userid, processId, user, rtaskid);
			Write("OK");
		} catch (Error ee) {
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义");
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义");
		}
	}
	
	/**
	 * 完成方法
	 */
	public void completeICTApplication(){
		try {
			String pid = getString("processId");// 流程id
			String id = getString("id");// 账户信息id
			String waitId = getString("waitId");// 待办id
			String opinion = getString("opinion");// 审批意见
			String taskId = getString("taskid");//任务id
			ICTApplication ict = ictApplicationService.getICTApplication(id);
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			Map<String, String> map = new HashMap<String, String>();
			if ("市公司政企部经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SGSZQBJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
			}else if ("市公司政企部经理副".equals(task.getActivityName())){
				map.put("decisionKey", "SGSZQBJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
			}else if ("区县分管经理".equals(task.getActivityName())) {
				map.put("decisionKey", "QXFGJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_DSBM");
			}else if ("省重客客户经理室经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SZKKHJLSJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_SZKSM");
			}else {
				jbpmUtil.completeTask(task.getId(), "结束");
			}
			taskService.updateBpms_riskoff_task(opinion, 2, taskId);
			String rtaskid =taskService.setBpms_riskoff_task(pid,"",1,"SH","客户经理",Integer.valueOf(ict.getCreator()),user);
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(wait!=null){
				service.updateWait(wait,this.getRequest());
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			completeUpcoming(ict,Integer.valueOf(ict.getCreator()),pid, user,rtaskid,"FQZF");
			Write("OK");
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 *退回方法
	 */
	public void returnICTApplication(){
		try {
			String id = getString("id");// 转账信息id
			String processId = getString("processId");// 流程id
			String waitId = getString("waitId");// 待办id
			String opinion = getString("opinion");// 退回意见
			String taskId = getString("taskid");//任务id
			ICTApplication ict = ictApplicationService.getICTApplication(id);
			ict.setState(2);
			ictApplicationService.updateICTApplication(ict);
			jbpmUtil.deleteProcessInstance(processId);
			taskService.updateBpms_riskoff_task(opinion, 0, taskId);
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(wait!=null){
				System.out.println("================退回开始代办================");
				service.updateWait(wait,this.getRequest());
				System.out.println("================退回结束代办================");
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			returnUpcoming(ict, ict.getCreator(),user,processId);// 调用service层方法生成待办
			Write("OK");
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 * 作废方法
	 */
	public void InvalidICTApplication() {
		try {
			String id = getString("id");// 转账信息id
			String opinion = getString("opinion");//作废原因
			String waitId = getString("waitId");
			String processId = getString("processId");// 流程id
			WaitTask wait = service.queryWaitByTaskId(waitId);
			ICTApplication ict = ictApplicationService.getICTApplication(id);
			ict.setInvalidReason(opinion);
			ict.setState(-1);
			ictApplicationService.updateICTApplication(ict);
			if(wait!=null){
				logger.info("================作废开始代办================");
				taskService.setBpms_riskoff_task(processId,"",-1,"SH","客户经理",Integer.valueOf(ict.getCreator()),user);
				service.updateWait(wait,this.getRequest());
				logger.info("================作废结束代办================");
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			Write("OK");
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 * 转发阅读结束方法
	 */
	public void readEndICTApplication(){
		try {
			String id = getString("id");
			String waitId=getString("waitId");
			String taskId = getString("taskid");//任务id
			ICTApplication ict = ictApplicationService.getICTApplication(id);
			//if(ict.getBossStatus()==null){
			if(ict.getInvoiceType()==1){
				String text2=insertICTGeneralInvoiceBossService(ict,user);
				if("OK".equals(text2)){
					String text=insertICTBossService(ict,user);
					if("OK".equals(text)){
						String type=updateICTBossService(ict,user);
						if("OK".equals(type)){
							System.out.println("这是推送的普票");
							ict.setBossDate(getStringDatetime(new Date()));
							ict.setState(0);
							ict.setBossStatus("1");
							ictApplicationService.updateICTApplication(ict);
							taskService.updateBpms_riskoff_task("", 2, taskId);
							WaitTask wait = service.queryWaitByTaskId(waitId);
							if(wait!=null){
								service.updateWait(wait,this.getRequest());
							}else{
								Write("NO");
								throw new RuntimeException(" 给事务回滚，自定义"); 
							}
							Write("OK");
						}else{
							if("NON".equals(type)){
								Write("系统出错请重试或联系管理员");	
							}else{
								Write(type);
							}
						}
					}else{
						if("NON".equals(text)){
							Write("系统出错请重试或联系管理员");	
						}else{
							Write(text);
						}
					}
				}else{
					if("NON".equals(text2)){
						Write("系统出错请重试或联系管理员");	
					}else{
						Write(text2);
					}
				}
			}else{
				String text1=insertICTBossService(ict,user);
				if("OK".equals(text1)){
					String text=insertICTBossServicetwo(ict,user);
					if("OK".equals(text)){
						String type=updateICTBossService(ict,user);
						if("OK".equals(type)){
							System.out.println("这是推送的专票");
							ict.setBossDate(getStringDatetime(new Date()));
							ict.setState(0);
							ict.setBossStatus("1");
							ictApplicationService.updateICTApplication(ict);
							taskService.updateBpms_riskoff_task("", 2, taskId);
							WaitTask wait = service.queryWaitByTaskId(waitId);
							if(wait!=null){
								service.updateWait(wait,this.getRequest());
							}else{
								Write("NO");
								throw new RuntimeException(" 给事务回滚，自定义"); 
							}
							Write("OK");
						}else {
							if("NON".equals(type)){
								Write("系统出错请重试或联系管理员");
							}else{
								Write(text);
							}
						}
					}else{
						if("NON".equals(text)){
							Write("系统出错请重试或联系管理员");	
						}else{
							Write(text);
						}
					}
				}else{
					if("NON".equals(text1)){
						Write("系统出错请重试或联系管理员");	
					}else{
						Write(text1);
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 * 获取附件消息
	 */
	public void dowloadFile() {
		String id = getString("id");
		List<Map<String, String>> s = ictApplicationService.dowloadFile(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}
	
	/**
	 * 流程结束客户经理和转发人员循环转发
	 */
	public void loopForwarding(){
		try{
			String waitId=getString("waitId");
			String id = getString("id");// 账户信息id
			String taskId = getString("taskid");//任务id
			Integer userid = getInteger("userid");//接收人ID
			String processId = getString("processId");
			String opinion=getString("opinion");
			String type  = getString("type");
			taskService.updateBpms_riskoff_task(opinion, 2, taskId);
			ICTApplication ict = ictApplicationService.getICTApplication(id);
			if("ZF".equals(type)){
				String rtaskid =taskService.setBpms_riskoff_task(processId,"",1,"SH","客户经理",Integer.valueOf(ict.getCreator()),user);
				completeUpcoming(ict,Integer.valueOf(ict.getCreator()),processId,user,rtaskid,"FQZF");
			}else{
				//if(ict.getBossStatus()==null){
					//String text ="NO";//setBossService(ict,user,"1");
					//if("OK".equals(text)){
						ict.setState(0);
						ict.setBossStatus("1");
						ictApplicationService.updateICTApplication(ict);
						String rtaskid =taskService.setBpms_riskoff_task(processId,"",1,"SH","转发审核",userid,user);
						completeUpcoming(ict,userid,processId,user,rtaskid,"ZF");
					/*}else{
						if("NO".equals(text)){
							Write("BOSS推送失败,请重试或联系管理员");	
						}else if("NON".equals(text)){
							Write("系统出错请重试或联系管理员");	
						}else{
							WaitTask wait = service.queryWaitByTaskId(waitId);
							if(wait!=null){
								service.updateWait(wait,this.getRequest());
							}else{
								Write("NO");
								throw new RuntimeException(" 给事务回滚，自定义"); 
							}
							Write(text);
						}
					}*/
				//}else{
					//String rtaskid =taskService.setBpms_riskoff_task(processId,"",1,"SH","转发审核",userid,user);
					//completeUpcoming(ict,userid,processId,user,rtaskid,"ZF");
				//}
			}
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(wait!=null){
				service.updateWait(wait,this.getRequest());
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("No");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	public void getICTApplicationType(){
		try {
			String numbering = getString("numbering");//工单号
			String title = getString("title");//工单标题
			String id = getString("id");//id
			String phone_no = getString("phone_no");//产品号码
			ICTApplication ict=null;
			if("".equals(id)&&id==null){
				ict = ictApplicationService.getICTApplication(id);
			}else{
				ict = ictApplicationService.getICTApplicationType(numbering,title,phone_no);
			}
			if(ict==null){
				Write("null");
			}else{
				Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(ict));
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 查询应收未收工单列表
	 */
	public void getICTApplicationPage() {
		try {
			Integer pageNo = getInteger("pageNo");// 当前页码数
			Integer pagesize = getInteger("pageSize");// 每页显示件数
			String numbering = getString("numbering");//工单号
			String title = getString("title");// 工单标题
			Integer state = getInteger("state"); // 状态
			String selectcon = getString("selectcon"); // 状态
			LayuiPage page = new LayuiPage(pageNo, pagesize);
			if(state==2){
				page = ictApplicationService.getICTApplicationPageo(page, title, numbering, state,selectcon, user);
			}else{
				page = ictApplicationService.getICTApplicationPaget(page, title, numbering, state,selectcon, user);
			}
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 根据主表编号查询所有关联的信息
	 */
	public void getICTApplicationDetList() {
		try {
			String id = getString("id");
			List<ICTApplicationDet> receiveApplyDet = ictApplicationService.getICTApplicationDetList(id);
			Write(new GsonBuilder().serializeNulls().setDateFormat("yyyy-MM-dd").excludeFieldsWithoutExposeAnnotation().create()
					.toJson(receiveApplyDet));
		} catch (Exception e) {
			e.printStackTrace();
			Write("ON");
		}
	}
	
	public void processtracking() {
		String id = getString("id");
		List<Bpms_riskoff_task> list=taskService.getPublicEntityTaskList(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(list));
	}
	
	/**
	 * 根据id获取工单信息
	 */
	public void getICTApplication() {
		try {
			String id = getString("id");
			ICTApplication sal = ictApplicationService.getICTApplication(id);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(sal));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getICTApplicationDetPage(){
		try {
			Integer pageNo = getInteger("pageNo");
			Integer pageSize = getInteger("pageSize");
			LayuiPage page = new LayuiPage(pageNo, pageSize);
			String id = getString("id");//id
			String json = ictApplicationService.getICTApplicationDetPage(page,id);
			Write(json);
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getServiceNumber(){
		try {
			Integer pageNo = getInteger("pageNo");
			Integer pageSize = getInteger("pageSize");
			LayuiPage page = new LayuiPage(pageNo, pageSize);
			String serviceNumber = getString("serviceNumber");//账户
			String json = ictApplicationService.getServiceNumber(page,serviceNumber);
			Write(json);
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 查询配置金额
	 */
	public void getTransferCitiesData(){
		try{
			String dangqianrenwu = getString("dangqianrenwu");
			List<SystemDept> deptList = user.getSystemDept();
		   	String code =deptList.get(0).getSystemCompany().getCompanyCode();
		   	TransferCitiesData transferCitiesData= ictApplicationService.getTransferCitiesData(code,dangqianrenwu);
		   	Write(JSONHelper.SerializeWithNeedAnnotation(transferCitiesData));
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getTimestamp(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getTimestampTwo(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getTimestampThree(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatethree() {
		Calendar calendar = Calendar.getInstance();
        calendar.add (Calendar.SECOND, 2);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(calendar.getTime());
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatetime(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	 /**
     * 分转元，转换为bigDecimal在toString
     * @return
     */
    public static String yuanToCents(int price) {
        return BigDecimal.valueOf(Long.valueOf(price)).divide(new BigDecimal(100)).toString();
    }
	
	//提交待办生成
	public void commitUpcoming(ICTApplication ict, Integer userid, String processId, SystemUser user, String taskid) {
		WaitTask wt = new WaitTask();
		wt.setName("[ICT审批工单]" + ict.getTitle());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/ICT/handleICTApplication.jsp?id="+ict.getId()+"&processId="// 流程ID
				+ processId+"&taskid="+taskid);
		SystemUser USER = systemUserService.getUserInfoRowNo(userid);
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(ICTApplication.ICTApplication);
		wt.setTaskId(ict.getId());
		service.saveWait(wt, this.getRequest());
	}

	// 完成代办生成
	public void completeUpcoming(ICTApplication ict, Integer userid, String processId, SystemUser user,String taskid,
			String type) {
		String ktype="K";
		WaitTask wt = new WaitTask();
		wt.setName("[ICT]" + ict.getTitle());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/ICT/handleICTApplication.jsp?id=" + ict.getId() 
				+ "&taskid=" + taskid
				+ "&processId="+processId+"&type="+type+"&ktype="+ktype);
		SystemUser USER = systemUserService.getUserInfoRowNo(userid);
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(ICTApplication.ICTApplication);
		wt.setTaskId(ict.getId());
		service.saveWait(wt, this.getRequest());
	}

	// 退回待办生成
	public void returnUpcoming(ICTApplication ict, String userid, SystemUser user,String processId) {
		WaitTask wt = new WaitTask();
		wt.setName("[ICT驳回工单]" + ict.getTitle());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/ICT/handleICTApplication.jsp?id=" + ict.getId()
				+ "&processId="+processId+"&ktype=Ret");
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(ICTApplication.ICTApplication);
		wt.setTaskId(ict.getId());
		service.saveWait(wt, this.getRequest());
	}
	//===============================================================================================================================================
	
	/**
	 * 调用boss接口查询账户信息
	 * 
	 * @param groupCode
	 *            集团280
	 * @param bossNo
	 *            boss工号
	 * @param phone
	 *            联系电话
	 * @param contractNo
	 *            账户号码
	 * @return
	 */
	public void sQryZXProdInfo(){
		String ESB_URL = "http://*************:51000/esbWS/rest/";// 正式
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL = "http://************:51000/esbWS/rest/";// 正式
		String url = ESB_URL + "sUserOrdQry";
		String bossNo = user.getBossUserName();//BOSS工号
		String groupCode = getString("groupCode");
		JSONObject object = new JSONObject();
		JSONObject obj1 = new JSONObject();
		object.put("WORN_SERV_CODE","sZqQryProInfo");
		object.put("UNIT_ID", Long.parseLong(groupCode)+"");
		obj1.put("PROVINCE_GROUP", "10008");
		object.put("COMMON_INFO",obj1);
		object.put("PHONE_NO",user.getMobile());
		object.put("GROUP_NO","");
		object.put("LOGIN_NO",bossNo);
		String json = setParamObj(object,bossNo);
		String jsonString = UrlConnection.responseGBK(url, json);
		logger.info("查询账户输入数据:"+json);
		logger.info("查询账户返回数据:"+jsonString);
		Write(jsonString.toString());
	}

	// 设置参数
	protected String setParamObj(JSONObject body, String bossNo) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "14");
		routing.put("ROUTE_VALUE", bossNo);
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		return root.toString();
	}
	
	/**
	 * json 数据格式化：
	 * @param body
	 * @return
	 */
	protected String setParamObjEsb(JSONObject body,String productNo) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "10");
		routing.put("ROUTE_VALUE",productNo);
		header.put("ROUTING", routing);
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}
	
	/**
	 * 获取产品简称
	 */
	public void getICTConfigurationSimpleName(){
		try {
			List<Map<String, String>> s = ictApplicationService.getICTConfigurationSimpleName();
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");
		}
	}
	
	/**
	 * 获取产品名称
	 */
	public void getICTConfigurationName(){
		try {
			String name = getString("name");//账户
			List<Map<String, String>> s = ictApplicationService.getICTConfigurationName(name);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");
		}
	}
	
	/**
	 * ICT设备销售甩单接口
	 */
	public String insertICTBossService(ICTApplication ict,SystemUser user){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://************:51000/esbWS/rest/";//测试
		try{
			String type="";
			String url = ESB_URL+"com_sitech_custsvc_atom_inter_IP4752AoSvc_throwOrderForICT";
			System.out.println("请求地址url000=======："+url);
			List<ICTApplicationDet> list =ictApplicationService.getICTApplicationDetList(ict.getId()); 
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			JSONObject outObjfour = new JSONObject();
			JSONObject object = new JSONObject();
			JSONArray list1 = new JSONArray();
			object.put("PHONE_NO",ict.getProductNumber()+"");//产品号码
			object.put("ALL_REJ_ORDER_ID",ict.getNumbering()+"");//甩单流水
			object.put("GROUP_CODE",ict.getGroupCode()+"");//集团编码
			object.put("GROUP_NAME",ict.getGroupName()+"");//集团名称
			object.put("DRAFT_TYPE",ict.getInvoiceType()+"");//开票类型(0:专票,1:普票,2:不开票)
			object.put("OP_TYPE","1");//操作类型(1:收入确认;2:收入核减)
			object.put("IF_DRAFT","N");//是否出票(Y:是;N:否;默认值:N)
			object.put("OP_CODE","4752");//操作编码(默认4752)
			object.put("GROUP_ID",groupId+"");//机构号
			object.put("OP_TIME",getTimestampTwo(new Date()));//操作时间YYYYMMDDHH24MISS
			object.put("LOGIN_NO",user.getBossUserName());//操作工号
			object.put("REMAKE",ict.getExplanation()+"");//备注
			for(int i=0;i<list.size();i++){
				JSONObject outObjthree = new JSONObject();
				List<ICTConfiguration> listDetail =ictApplicationService.getICTConfigurationExplanation(list.get(i).getProductName());
				outObjthree.put("REJ_ORDER_ID",list.get(i).getDetailedCoding()+"");//设备流水
				outObjthree.put("SALE_AMOUNT",list.get(i).getSalesAmount()+"");//销售金额(单位是分)
				outObjthree.put("DRAFT_AMOUNT",list.get(i).getInvoiceAmount()+"");//开票金额(单位是分)
				outObjthree.put("DEC_AMOUNT","");//核减金额(核减时必传)
				outObjthree.put("ICT_NAME",list.get(i).getProductName()+"");//ICT设备名称
				outObjthree.put("ICT_SIMP_NAME",list.get(i).getProductAbbreviation()+"");//ICT设备简称
				outObjthree.put("MAC_DETAIL",listDetail.get(0).getExplanation()+"");//设备描述
				outObjthree.put("OLD_REJ_ORDER_ID","");//原甩单订单号（核减时必传）
				outObjthree.put("SALE_TYPE",list.get(i).getSalesType()+"");//销售类型(0:ICT设备;1:终端)
				outObjthree.put("REMAKE",ict.getExplanation()+"");//备注
				list1.add(outObjthree);
			}
			object.put("MAC_LIST", list1);
			outObjfour.put("BUSI_INFO", object);
			String json1 = setParamObjEsb(outObjfour,ict.getProductNumber());
			logger.info("ICT申请调用接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			/*String ds="{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"DB_ID\":\"\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241590132608657\","
					+ "\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\","
					+ "\"ROUTING\":{\"ROUTE_KEY\":\"10\",\"ROUTE_VALUE\":\"***********\"}},"
					+ "\"BODY\":{\"BUSI_INFO\":{\"PHONE_NO\":\"***********\",\"ALL_REJ_ORDER_ID\":\"CD20200521014952276\","
					+ "\"GROUP_CODE\":\"**********\",\"GROUP_NAME\":\"四川移动集团业务测试新\",\"SALE_TYPE\":0,\"DRAFT_TYPE\":1,"
					+ "\"OP_TYPE\":\"1\",\"IF_DRAFT\":\"N\",\"OP_CODE\":\"4752\",\"GROUP_ID\":\"23\",\"OP_TIME\":\"**************\","
					+ "\"LOGIN_NO\":\"aa1000310\",\"REMAKE\":\"这是一个测试数据\","
					+ "\"MAC_LIST\":[{\"REJ_ORDER_ID\":\"CD202005210149522760\",\"SALE_AMOUNT\":200,\"DRAFT_AMOUNT\":200,"
					+ "\"DEC_AMOUNT\":\"\",\"ICT_NAME\":\"其他商业、饮食、服务专用设备\",\"ICT_SIMP_NAME\":\"商用设备\","
					+ "\"MAC_DETAIL\":\"包括自动售货机零件、钱币兑换机零件、洗碗机零件、洗衣机零件、干衣机零件\",\"OLD_REJ_ORDER_ID\":\"\",\"REMAKE\":\"2\"}]}}}}";
			
			JSONObject data = JSONObject.fromObject(ds);
			String jsonString = CMCC1000OpenService.getInstance().bdcesPatams("http://************:51000/esbWS/rest/com_sitech_custsvc_atom_inter_IP4752AoSvc_throwOrderForICT",data.toString());
			System.out.println(jsonString);
			Result result =HttpURLConnectClientFactory.analyticParamsByResult(jsonString);
			Object objT = result.getData();*/
			logger.info("ICT申请调用接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}
	
	
	/**
	 * ICT专票推送接口
	 */
	public String insertICTBossServicetwo(ICTApplication ict,SystemUser user){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://10.113.166.109:58000/esbWS/rest/";//测试
		//String ESB_URL="http://************:51000/esbWS/rest/";//测试
		try{
			String type="";
			String url = ESB_URL + "s8248Cfm";
			System.out.println("请求地址=======："+url);
			List<ICTApplicationDet> list =ictApplicationService.getICTApplicationDetList(ict.getId()); 
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			JSONObject object = new JSONObject();
			JSONArray list1 = new JSONArray();
			object.put("SERVICE_NO",ict.getProductNumber());//服务号码
			object.put("DATA_SOURCE","ict");//发票来源类型
			String s="";
			if("1".equals(ict.getWhether())){
				s="0";
			}else{
				s="3";
			}
			SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(ict.getAuditId()));
			object.put("STATE",ict.getWhether());//开票状态
			object.put("INV_TYPE",s);//开票类型
			object.put("LOGIN_NO",user.getBossUserName());//申请工号
			object.put("REPORT_TO",USER.getBossUserName());//审批工号
			object.put("CUST_ID",ict.getBustId());//客户ID
			object.put("CONTRACT_NO",ict.getContractNo());//账户号码
			object.put("TAXPAYER_ID",ict.getTaxPayer());//纳税人识别号
			object.put("UNIT_NAME",ict.getTaxName());//纳税人名称
			object.put("INV_OPR_NOTE",ict.getExplanation());//申请开票备注
			object.put("GROUP_ID",groupId);//归属机构代码
			object.put("BUSI_GROUP_ID",groupId);//办理渠道
			object.put("INVAREA_TYPE",ict.getWhether()+"");//是否申请合并发票
			for(int i=0;i<list.size();i++){
				JSONObject outObjthree = new JSONObject();
				outObjthree.put("ORDER_ID",list.get(i).getDetailedCoding());//订单流水号
				outObjthree.put("BILL_CYCLE",getTimestampTwo(ict.getCreateDate()));//非空，年月
				outObjthree.put("RE_NAME",list.get(i).getProductName());//必填，开票科目（费用/项目名称）
				outObjthree.put("RE_NUM",list.get(i).getCount()+"");//必填，开票数量
				outObjthree.put("TEX_SHOULD",list.get(i).getTaxIncluded());//必填，含税价=税费+不含税金额，单位：分
				outObjthree.put("TEX_RATE",list.get(i).getTaxRate());//必填，税率。取百分比之后的数据（例如0.09）
				outObjthree.put("TEX_FEE",list.get(i).getTaxMoney());//必填，税费，单位：分
				outObjthree.put("UNIT_PRICE",list.get(i).getPrice());//单价，不含税金额，单位：分
				outObjthree.put("FEE_ACCEPT","");//必填，默认传空值
				outObjthree.put("FEE_CODE","");//必填，默认传空值
				outObjthree.put("FEE_CODE_SEQ","0");//必填，默认传0
				outObjthree.put("INV_MODEL","");//必填，不填时为空即可
				list1.add(outObjthree);
			}
			object.put("INMSG", list1);
			String json1 = setParamObj(object,user.getBossUserName());
			logger.info("ICT申请调用接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			logger.info("ICT申请调用接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}
	
	/**
	 * ICT是否出票更新接口
	 */
	public String updateICTBossService(ICTApplication ict,SystemUser user){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://************:51000/esbWS/rest/";//正式
		try{
			String type="";
			String url = ESB_URL + "com_sitech_custsvc_atom_inter_IP4752AoSvc_updateIfDraft";
			System.out.println("请求地址=======："+url);
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			JSONObject outObjfour = new JSONObject();
			JSONObject object = new JSONObject();
			object.put("PHONE_NO",ict.getProductNumber());//产品号码
			object.put("ALL_REJ_ORDER_ID",ict.getNumbering());//甩单流水
			object.put("IF_DRAFT","Y");//是否出票(Y:是;N:否;默认值:N)
			object.put("OP_CODE","4752");//操作编码(不传默认4752)
			object.put("GROUP_ID",groupId);//机构号
			object.put("OP_TIME",getTimestampTwo(new Date()));//操作时间YYYYMMDDHH24MISS
			object.put("LOGIN_NO",user.getBossUserName());//操作工号
			object.put("REMAKE",ict.getExplanation());//备注
			outObjfour.put("BUSI_INFO", object);
			String json1 = setParamObjEsb(outObjfour,ict.getProductNumber());
			logger.info("ICT是否出票更新接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			logger.info("ICT是否出票更新接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}
	
	/*public String sEinvApply(ICTApplication ict,SystemUser user){
		String ESB_URL="http://************:51000/esbWS/rest/";//正式
		try{
			String type="";
			String url = ESB_URL + "sEinvApply";
			logger.info("ICT普票开票请求地址=======："+url);
			List<ICTApplicationDet> list =ictApplicationService.getICTApplicationDetList(ict.getId()); 
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			String region_id = obj.getString("REGION_ID");
			JSONObject object = new JSONObject();
			JSONArray list1 = new JSONArray();
			JSONObject objData = new JSONObject();
			SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(ict.getAuditId()));
			object.put("OP_CODE",ict.getWhether());//模块代码
			object.put("CONTRACT_NO",ict.getContractNo());//账户
			object.put("PHONE_NO",user.getBossUserName());//发票须展示的号码
			object.put("INSYS_PHONE_NO",USER.getBossUserName());//系统记录的手机号码（虚拟号码）
			object.put("LOGIN_NO",user.getBossUserName());//工号
			object.put("REGION_ID",region_id);//工号 region_id
			object.put("GROUP_ID",groupId);//工号 group_id
			object.put("BUSI_GROUP_ID","10008");//工号 BUSI_GROUP_ID
			object.put("KPXM",ict.getExplanation());//开票项目
			object.put("INV_KPLX",groupId);//开票类型  1正票 、 2红票(I，必传)
			object.put("INV_GHFMC",groupId);//购货方名称
			object.put("INV_GHFQYLX",ict.getWhether()+"");//01：企业02：机关事业单位03：个人04：其它
			object.put("INV_CZDM",ict.getWhether()+"");//操作代码 10 正常开具 20 冲红
			object.put("INV_KPHJJE",ict.getWhether()+"");//价税合计金额(I，必传；红字发票传负值)
			object.put("INV_FPQQLSH",ict.getWhether()+"");//发票请求唯一流水
			object.put("DATA_SOURCE",ict.getWhether()+"");//开票渠道（账务 billing ，CRM crm）其他渠道商议后传入
			object.put("INV_YFP_DM",ict.getWhether()+"");//冲红的原发票代码（INV_KPLX =2 时传入）
			object.put("INV_YFP_HM",ict.getWhether()+"");//冲红的原发票号码（INV_KPLX =2 时传入）
			object.put("INV_TSCHBZ",ict.getWhether()+"");//冲红特殊标志 0 电子发票，1 其他 （INV_KPLX =2 时传入）
			object.put("INV_CHYY",ict.getWhether()+"");//冲红原因（INV_KPLX =2 时传入）
			object.put("INV_ALLCREDIT",ict.getWhether()+"");//是否部分冲红 p部分 a全部冲红（目前只支持全部冲红）（INV_KPLX =2 时传入）
			object.put("INV_LFPQQLSH",ict.getWhether()+"");//原蓝字发票请求流水（INV_KPLX =2 时传入）
			object.put("INV_KP_YM",ict.getWhether()+"");//原蓝字发票的开具时间（年月）
			object.put("MSG_RECV_PHONE",ict.getWhether()+"");//电子发票短信接收号码
			object.put("RECV_EMAIL",ict.getWhether()+"");//电子发票邮件接收短信
			object.put("INV_BZ",ict.getWhether()+"");//发票备注，注意，冲红时该字段必
			object.put("GHF_NSRSBH",ict.getWhether()+"");//购货方识别号 非必传
			object.put("GHF_DZ",ict.getWhether()+"");//购货方地址，电话  非必传
			object.put("GHF_YHZH",ict.getWhether()+"");//购货方银行账号 非必传
			for(int i=0;i<list.size();i++){
				JSONObject outObjthree = new JSONObject();
				outObjthree.put("INV_XMSL",list.get(i).getDetailedCoding());//订单流水号
				outObjthree.put("INV_XMDJ",getTimestampTwo(ict.getCreateDate()));//非空，年月
				outObjthree.put("INV_XMJE",list.get(i).getProductName());//必填，开票科目（费用/项目名称）
				outObjthree.put("INV_FPHXZ",list.get(i).getCount()+"");//必填，开票数量
				outObjthree.put("INV_XMMC",list.get(i).getTaxIncluded());//必填，含税价=税费+不含税金额，单位：分
				outObjthree.put("INV_HSBZ",list.get(i).getTaxRate());//必填，税率。取百分比之后的数据（例如0.09）
				outObjthree.put("SPBM",list.get(i).getTaxMoney());//必填，税费，单位：分
				list1.add(outObjthree);
			}
			objData.put("DATA", list1);
			object.put("INV_DATA_NODE", objData);
			String json1 = setParamObj(object,user.getBossUserName());
			logger.info("ICT申请调用接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			logger.info("ICT申请调用接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}*/
	
	/**
	 * ICT普票推送接口
	 */
	public String insertICTGeneralInvoiceBossService(ICTApplication ict,SystemUser user){
		//String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://10.113.166.109:58000/esbWS/rest/";//测试
		String ESB_URL="http://*************:51000/esbWS/rest/";//测试
		try{
			String type="";
			String url = ESB_URL + "sEinvApply";
			System.out.println("请求地址=======："+url);
			List<ICTApplicationDet> list =ictApplicationService.getICTApplicationDetList(ict.getId()); 
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			String regionid = obj.getString("REGION_ID");
			JSONObject object = new JSONObject();
			JSONObject objectData = new JSONObject();
			JSONArray list1 = new JSONArray();
			object.put("OP_CODE","SDXT");//服务号码
			object.put("CONTRACT_NO",ict.getContractNo()==null?"":Long.parseLong(ict.getContractNo()));// 账户
			object.put("PHONE_NO",ict.getProductNumber());//发票须展示的号码
			object.put("INSYS_PHONE_NO",ict.getProductNumber());//系统记录的手机号码（虚拟号码）
			object.put("LOGIN_NO",user.getBossUserName());//工号 group_id
			object.put("REGION_ID",regionid);//工号 region_id
			object.put("GROUP_ID",groupId);//客户ID
			object.put("BUSI_GROUP_ID",groupId);//账户号码
			object.put("KPXM",ict.getBillingItems());//开票项目
			object.put("INV_KPLX","1");//开票类型  1正票 、 2红票(I，必传)
			object.put("INV_GHFMC",ict.getGroupName());//购货方名称
			object.put("INV_GHFQYLX",ict.getBillingChannel());//01：企业02：机关事业单位03：个人04：其它
			object.put("INV_CZDM","10");//操作代码 10 正常开具 20 冲红
			String amount = BigDecimal.valueOf(ict.getAmount()).divide(new BigDecimal(100)).toString();
			object.put("MSG_RECV_PHONE",ict.getMsgRecvPhone());//电子发票短信接收号码
			object.put("INV_KPHJJE",amount);//总金额
			object.put("INV_FPQQLSH",ict.getNumbering());//流水
			object.put("DATA_SOURCE","ict");//开票渠道  crm
			object.put("INV_YFP_DM","");//冲红的原发票代码（INV_KPLX =2 时传入）
			object.put("INV_YFP_HM","");//冲红的原发票号码（INV_KPLX =2 时传入）
			object.put("INV_TSCHBZ","");//冲红特殊标志 0 电子发票，1 其他 （INV_KPLX =2 时传入）
			object.put("INV_CHYY","");//冲红原因（INV_KPLX =2 时传入）
			object.put("INV_ALLCREDIT","");//是否部分冲红 p部分 a全部冲红（目前只支持全部冲红）（INV_KPLX =2 时传入）
			object.put("INV_LFPQQLSH","");//原蓝字发票请求流水（INV_KPLX =2 时传入）
			object.put("INV_KP_YM","");//原蓝字发票的开具时间（年月）
			object.put("RECV_EMAIL","");//电子发票邮件接收短信
			for(int i=0;i<list.size();i++){
				JSONObject outObjthree = new JSONObject();
				outObjthree.put("INV_XMSL",list.get(i).getSalesVolumes());//项目数量
				String salesAmount = BigDecimal.valueOf(list.get(i).getSalesAmount()).divide(new BigDecimal(100)).toString();
				outObjthree.put("INV_XMDJ",salesAmount);//项目单价
				String invXmje = BigDecimal.valueOf(list.get(i).getInvoiceAmount()).divide(new BigDecimal(100)).toString();
				outObjthree.put("INV_XMJE",invXmje);//项目金额
				outObjthree.put("INV_FPHXZ",list.get(i).getNatureInvoiceLine());//发票行性质: 0正常行、1折扣行、2被折扣行
				outObjthree.put("INV_XMMC","*"+list.get(i).getProductAbbreviation()+"*"+list.get(i).getProductName());//项目名称
				outObjthree.put("INV_HSBZ",list.get(i).getTaxMark());//含税标志，0表示都不含税，1表示都含税。
				List<ICTConfiguration> ictp= ictApplicationService.getICTConfigurationExplanation(list.get(i).getProductName());
				String code = ictp.get(0).getCode();
				if(code.length()<19){
		    	   int length =19-code.length();
		    	   for(int j=0;j<length;j++){
		    		   code+="0";
		    	   }
			    }
				outObjthree.put("SPBM",code);//商品编码（税局下发的19位商品编码，固定19 位）
				list1.add(outObjthree);
			}
			objectData.put("DATA", list1);
			object.put("INV_DATA_NODE", objectData);
			object.put("PJX_MEM",list.size());
			object.put("INV_BZ",ict.getExplanation());//发票备注，注意，冲红时该字段必填
			object.put("GHF_NSRSBH", ict.getTaxPayer());
			object.put("GHF_DZ", ict.getTaxAddress());
			object.put("GHF_YHZH", ict.getTaxBankAccount());
			String json1 = setParamObj(object,user.getBossUserName());
			logger.info("ICT普票申请调用接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			logger.info("ICT普票申请调用接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}
	
	public void getUserBossNo(){
		try{
			Integer userid = getInteger("userid");
			SystemUser USER = systemUserService.getUserInfoRowNo(userid);
			if(!"".equals(USER.getBossUserName())&&USER.getBossUserName()!=null){
				System.out.println(USER.getBossUserName());
				Write(USER.getBossUserName());
			}else{
				Write("null");
			}
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
}
