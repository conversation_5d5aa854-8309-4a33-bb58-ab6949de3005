package com.xinxinsoft.action.ICT;

import java.io.File;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.ResourceBundle;

import org.apache.commons.lang.StringUtils;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.ICT.ICTApplication;
import com.xinxinsoft.entity.ICT.ICTApplicationDet;
import com.xinxinsoft.entity.ICT.ICTConfiguration;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.service.ICT.ICTApplicationService;
import com.xinxinsoft.service.ICT.ReductionICTApplicationService;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;

public class ReductionICTApplicationAction extends BaseAction{
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private static ResourceBundle s = ResourceBundle.getBundle("WebService-config");
	private String audit = s.getString("AUDIT_INTERS_FJ_SWITCH");
	/*private static String ESB_URL = s.getString("ESB_URL");*/
	private static final Logger logger = LoggerFactory.getLogger(ReductionICTApplicationAction.class);
	private ReductionICTApplicationService reductionIctApplicationService;
	private ICTApplicationService ictApplicationService;
	private AttachmentService attachmentService;
	private WaitTaskService service;
	private SystemUserService systemUserService;
	private TransferJBPMUtils transferJBPMUtils;
	private Bpms_riskoff_service taskService;
	private JbpmUtil jbpmUtil;
	private File branchFile1;
	private String branchFile1FileName;
	
	private File customerLetterFile;
	private String customerLetterFileFileName;
	public WaitTaskService getService() {
		return service;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public TransferJBPMUtils getTransferJBPMUtils() {
		return transferJBPMUtils;
	}

	public JbpmUtil getJbpmUtil() {
		return jbpmUtil;
	}

	public void setService(WaitTaskService service) {
		this.service = service;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
		this.transferJBPMUtils = transferJBPMUtils;
	}

	public void setJbpmUtil(JbpmUtil jbpmUtil) {
		this.jbpmUtil = jbpmUtil;
	}

	public File getBranchFile1() {
		return branchFile1;
	}

	public String getBranchFile1FileName() {
		return branchFile1FileName;
	}

	public File getCustomerLetterFile() {
		return customerLetterFile;
	}

	public String getCustomerLetterFileFileName() {
		return customerLetterFileFileName;
	}

	public void setBranchFile1(File branchFile1) {
		this.branchFile1 = branchFile1;
	}

	public void setBranchFile1FileName(String branchFile1FileName) {
		this.branchFile1FileName = branchFile1FileName;
	}

	public void setCustomerLetterFile(File customerLetterFile) {
		this.customerLetterFile = customerLetterFile;
	}

	public void setCustomerLetterFileFileName(String customerLetterFileFileName) {
		this.customerLetterFileFileName = customerLetterFileFileName;
	}

	public AttachmentService getAttachmentService() {
		return attachmentService;
	}

	public void setAttachmentService(AttachmentService attachmentService) {
		this.attachmentService = attachmentService;
	}

	public ReductionICTApplicationService getReductionIctApplicationService() {
		return reductionIctApplicationService;
	}

	public void setReductionIctApplicationService(
			ReductionICTApplicationService reductionIctApplicationService) {
		this.reductionIctApplicationService = reductionIctApplicationService;
	}

	public Bpms_riskoff_service getTaskService() {
		return taskService;
	}

	public void setTaskService(Bpms_riskoff_service taskService) {
		this.taskService = taskService;
	}

	public ICTApplicationService getIctApplicationService() {
		return ictApplicationService;
	}

	public void setIctApplicationService(ICTApplicationService ictApplicationService) {
		this.ictApplicationService = ictApplicationService;
	}

	public void uploadFile() {
		try {
			//根据当天日期生成文件夹：名称：
			String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
			String  ftpUrl=FileUpload.getFtpURL()+urlDate;
			File headPath = new File(ftpUrl);//获取文件夹路径
	        if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
	        	headPath.mkdirs();
	        }
	        Map<String, String> map = new HashMap<String, String>();
			if(branchFile1!=null){
				Long time = System.currentTimeMillis();
		        String pixstr =FileUpload.getFilePix(branchFile1FileName);
		        if(StringUtils.isEmpty(pixstr)){
		        	writeText("0");
		        }
				if(FileUpload.upload(ftpUrl, branchFile1, time + pixstr)) {
					final Attachment attachmentEntity = new Attachment();
					attachmentEntity.setAttachmentName(time + pixstr);// 防重名
					attachmentEntity.setAttachmentUrl(urlDate+time + pixstr);
					attachmentEntity.setUploadDate(new Date());
					attachmentEntity.setRealName(branchFile1FileName);
					attachmentEntity.setUploadUser((SystemUser) this.getRequest().getSession()
							.getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser()));
					String attachmentId = this.attachmentService.addEntity(attachmentEntity);
					 ///审计接口调用
					if("start".equals(audit)){
						final String request= DateUtil.getIpAddr(this.getRequest());
						new Thread(new Runnable() {
							@Override
							public void run() {
								///审计接口调用
								CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
							}
						}).start();
					}
					map.put("branchFile1",attachmentId);
				}else{
					writeText("0");
				}
			}else{
				map.put("branchFile1","");
			}
			if(customerLetterFile!=null){
				Long time = System.currentTimeMillis();
				String pixstr =FileUpload.getFilePix(customerLetterFileFileName);
		        if(StringUtils.isEmpty(pixstr)){
		        	writeText("0");
		        }
				if(FileUpload.upload(ftpUrl, customerLetterFile, time + pixstr)) {
					final Attachment attachmentEntity = new Attachment();
					attachmentEntity.setAttachmentName(time + pixstr);// 防重名
					attachmentEntity.setAttachmentUrl(urlDate+time + pixstr);
					attachmentEntity.setUploadDate(new Date());
					attachmentEntity.setRealName(customerLetterFileFileName);
					attachmentEntity.setUploadUser((SystemUser) this.getRequest().getSession()
							.getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser()));
					String attachmentId = this.attachmentService.addEntity(attachmentEntity);
					 ///审计接口调用
					 if("start".equals(audit)){
						 final String request= DateUtil.getIpAddr(this.getRequest());
							new Thread(new Runnable() {
								@Override
								public void run() {
									///审计接口调用
									CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
								}
							}).start();
					 }
					 map.put("customerLetterFile",attachmentId);
				}else{
					writeText("0");
				}
			}else{
				map.put("customerLetterFile","");
			}
			Write(JSONHelper.SerializeWithNeedAnnotation(map));
		} catch (Exception e) {
			e.printStackTrace();
			writeText("0");
		}
	}
	
	/**
	 *  查询四个附件      
	 */
	public void queryAttachment1(){
		String json = this.getString("json"); 
		JSONObject data = JSONObject.fromObject(json);
		Map<String, String> map = new HashMap<String, String>();
		if(!"".equals(data.getString("sDecisionMinutes"))){
			Attachment attachment= reductionIctApplicationService.getAttachment(data.getString("sDecisionMinutes"));
			map.put("sDecisionMinutes", attachment.getRealName());
		}else{
			map.put("sDecisionMinutes", "");
		}
		if(!"".equals(data.getString("szDecisionMinutes"))){
			Attachment attachment= reductionIctApplicationService.getAttachment(data.getString("szDecisionMinutes"));
			map.put("szDecisionMinutes", attachment.getRealName());	
		}else{
			map.put("szDecisionMinutes","");
		}
		Write(JSONHelper.SerializeWithNeedAnnotation(map));
	}
	/**
	 * 新增ICT核减申请
	 */
	public void addICTApplication() {
		try {
			String role = getString("role");
			String id = getString("id");
			String returnId = getString("returnId");
			Integer userid = getInteger("userid");
			String title = getString("title");
			long amount = getLong("amount");
			String explanation = getString("explanation");
			String waitId = getString("waitId");
			String attachmentId = getString("attachmentId");
			String json = getString("json");
			String copyo=getString("copyo");
			String sDecisionMinutes =getString("sDecisionMinutes");
			String szDecisionMinutes =getString("szDecisionMinutes");
			List<Object[]> sone = reductionIctApplicationService.getbumen(user.getRowNo());
			String district = (String) sone.get(0)[0];
			String companyName = (String) sone.get(0)[1];
			String IBM = (String) sone.get(0)[2];
			String companyCode=(String) sone.get(0)[3];
			String sateTime = getTimestamp(new Date());
			ICTApplication ictQuery = reductionIctApplicationService.getICTApplication(id);
			ICTApplication ict = new ICTApplication();
			ict.setNumbering(IBM + sateTime);
			ict.setTitle(title);
			ict.setExplanation(explanation);
			ict.setCompanyName(companyName);
			ict.setCompanyCode(companyCode);
			ict.setDistrict(district);
			ict.setCreator(user.getRowNo() + "");
			ict.setCreatorName(user.getEmployeeName());
			ict.setCreatorPhone(user.getMobile());
			ict.setCreateDate(new Date());
			ict.setGroupCode(ictQuery.getGroupCode());
			ict.setGroupName(ictQuery.getGroupName());
			ict.setProductNumber(ictQuery.getProductNumber());
			ict.setInvoiceType(ictQuery.getInvoiceType());
			ict.setSalesType(ictQuery.getSalesType());
			ict.setOperationType(2);
			ict.setState(1);
			ict.setAmount(amount);
			ict.setRevenueRecognitionId(id);
			ict.setsDecisionMinutes(sDecisionMinutes);
			ict.setSzDecisionMinutes(szDecisionMinutes);
			ict.setContractNo(ictQuery.getContractNo());
			ict.setBustId(ictQuery.getBustId());
			ict.setAuditId(ictQuery.getAuditId());
			ict.setTaxPayer(ictQuery.getTaxPayer());
			ict.setTaxAddress(ictQuery.getTaxAddress());
			ict.setTaxPhone(ictQuery.getTaxPhone());
			ict.setTaxBankName(ictQuery.getTaxBankName());
			ict.setTaxBankAccount(ictQuery.getTaxBankAccount());
			ict.setWhether(ictQuery.getWhether());
			ict.setMsgRecvPhone(ictQuery.getMsgRecvPhone());
			/*ict.setBranchFileUrl(branchFileUrl);
			ict.setCustomerLetterFileUrl(customerLetterFileUrl);
			ict.setSystemFileUrl(systemFileUrl);
			ict.setProductsFileUrl(productsFileUrl);*/
			ictQuery.setState(-3);
			reductionIctApplicationService.updateICTApplication(ictQuery);
			ICTApplication ictl = reductionIctApplicationService.saveICTApplication(ict);
			JSONArray jsonObject1 = JSONArray.fromObject(json);
			for (int i=0;i<jsonObject1.size();i++) {
				String s = jsonObject1.getString(i);
				JSONObject data = JSONObject.fromObject(s);
				ICTApplicationDet ictd = new ICTApplicationDet();
				ICTApplicationDet getictd= reductionIctApplicationService.getICTApplicationDetById(data.getString("detid"));
				ictd.setMasterWorkOrderID(ictl.getId());
				ictd.setDetailedCoding(ictl.getNumbering()+i);
				ictd.setSalesAmount(getictd.getSalesAmount());
				ictd.setInvoiceAmount(getictd.getInvoiceAmount());
				ictd.setSalesType(getictd.getSalesType());
				ictd.setProductAbbreviation(getictd.getProductAbbreviation());
				ictd.setProductName(getictd.getProductName());
				ictd.setWhetherToInvoice(getictd.getWhetherToInvoice());
				ictd.setCount(getictd.getCount());
				ictd.setTaxIncluded(getictd.getTaxIncluded());
				ictd.setTaxRate(getictd.getTaxRate());
				ictd.setTaxMoney(getictd.getTaxMoney());
				ictd.setPrice(getictd.getPrice());
				ictd.setSizeModel(getictd.getSizeModel());
				ictd.setRevenueRecognitionId(data.getString("detid"));
				ictd.setDeductionAmount(data.getLong("deductionAmount"));
				ictd.setSalesVolumes(getictd.getSalesVolumes());
				ictd.setNatureInvoiceLine(getictd.getNatureInvoiceLine());
				ictd.setTaxMark(getictd.getTaxMark());
				getictd.setReductionBossStatus("1");//核减预占
				getictd.setDeductionAmount(getictd.getDeductionAmount()+data.getLong("deductionAmount"));
				reductionIctApplicationService.updateICTApplicationDet(getictd);
				reductionIctApplicationService.saveICTApplicationDet(ictd);
				
			}
			Map<String, String> map = new HashMap<String, String>();
			map.put("decisionKey", "APPLY");
			map.put("decisionVal", role);
			String processId = jbpmUtil.startPIByKey("reductionICTApplication", map).getId();
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			taskService.setBpms_riskoff_process(ictl.getId(), processId, 1, user);
			taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
			String taskid= taskService.setBpms_riskoff_task(processId,null, 1, "SH", task.getActivityName(),userid, user);//预存下一步任务
			List<SingleAndAttachment> ss = reductionIctApplicationService.getSingleAndAttachment(id);//查询已存在附件
			// 遍历获取的附件中间表数据
			if (ss != null) {
				for (int i = 0; i < ss.size(); i++) {
					if(i==0){
						if(!"".equals(attachmentId)){
							attachmentId += ","+ss.get(i).getAttachmentId()+",";
						}else{
							attachmentId += ss.get(i).getAttachmentId()+",";
						}
					}else{
						attachmentId += ss.get(i).getAttachmentId() + ",";
					}
				}
			}
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] jsonAttachment = attachmentId.split(",");
					if (jsonAttachment.length > 0) {
						for (int i = 0; i < jsonAttachment.length; i++) {
							SingleAndAttachment sah = new SingleAndAttachment();
							sah.setOrderID(ictl.getId());
							sah.setAttachmentId(jsonAttachment[i]);
							sah.setLink(ICTApplication.ICTApplication);
							reductionIctApplicationService.saveSandA(sah);
						}
					}
				}
			}
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(!"copy".equals(copyo)){
				if (wait != null) {
					service.updateWait(wait, this.getRequest());
				} else {
					if (!"".equals(returnId) && returnId != null) {
						Write("NO");
						return;
					}
				}
			}
			commitUpcoming(ictl,userid,processId,user,taskid);// 代办
			Write("OK");
		} catch (Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 流程进行
	 */
	public void handleICTApplication() {
		try {
			String processId = getString("processId");// 流程id
			String t = getString("nextTask");// 下一步可执行流程线条值
			Integer userid = getInteger("userId");// 用户id
			String id = getString("id");// 主信息id
			String opinion = getString("opinion");//审批意见
			String waitId = getString("waitId");//待办id
			String taskId = getString("taskid");//任务id
			List<SystemDept> deptList = user.getSystemDept();
			String code = deptList.get(0).getSystemCompany().getCompanyCode();
			ICTApplication ict = reductionIctApplicationService.getICTApplication(id);
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			Map<String, String> map = new HashMap<String, String>();
			TransferCitiesData transferCitiesData= taskService.getTransferCitiesData(code,task.getActivityName(),"ICTApplication");
			if ("市公司领导".equals(task.getActivityName())) {
				map.put("decisionKey", "SGSLD");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if ("市公司政企部经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SGSZQBJL");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				if(t.equals("ALL")){
					jbpmUtil.completeTask(task.getId(), t);
				}else{
					jbpmUtil.completeTask(task.getId(), map,t);
				}
			}else if ("市公司政企部经理副".equals(task.getActivityName())){
				TransferCitiesData transferCitiesDataT= taskService.getTransferCitiesData(code,"市公司政企部经理","reductionICTApplication");
				map.put("decisionKey", "SGSZQBJL");
				if(Double.parseDouble(transferCitiesDataT.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesDataT.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesDataT.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				if(t.equals("ALL")){
					jbpmUtil.completeTask(task.getId(), t);
				}else{
					jbpmUtil.completeTask(task.getId(), map,t);
				}
			}else if ("省公司管理员".equals(task.getActivityName())){
				map.put("decisionKey", "SGSGLY");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				if(t.equals("ALL")){
					jbpmUtil.completeTask(task.getId(), t);
				}else{
					jbpmUtil.completeTask(task.getId(), map,t);
				}
			}else if ("区县分管经理".equals(task.getActivityName())) {
				map.put("decisionKey", "QXFGJL");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if ("省重客分管经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SZKFGJL");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if ("省重客业务管理室经理".equals(task.getActivityName())){
				map.put("decisionKey", "SZKYWGLSJL");
				if(Double.parseDouble(transferCitiesData.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesData.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesData.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if ("省公司政企客户部经理副".equals(task.getActivityName())){
				TransferCitiesData transferCitiesDataT= taskService.getTransferCitiesData(code,"省公司政企客户部经理","reductionICTApplication");
				map.put("decisionKey", "SGSZQKHBJL");
				if(Double.parseDouble(transferCitiesDataT.getAmount())>0){
					if(ict.getAmount()>=Double.parseDouble(transferCitiesDataT.getAmount())*100){
						map.put("decisionVal", "YES");
					}else{
						map.put("decisionVal", "NO");
					}
				}else if(Double.parseDouble(transferCitiesDataT.getAmount())==0){
					map.put("decisionVal", "YES");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else {
				jbpmUtil.completeTask(task.getId(), t);
			}
			
			// 保存下一步任务信息
			Task taskt = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			taskService.updateBpms_riskoff_task(opinion, 2, taskId);
			String rtaskid =taskService.setBpms_riskoff_task(processId,"",1,"SH",taskt.getActivityName(),userid, user);
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if (wait != null) {
				service.updateWait(wait, this.getRequest());
			} else {
				Write("NO");
				throw new Error("待办ID==========：" + waitId);
			}
			commitUpcoming(ict, userid, processId, user, rtaskid);
			Write("OK");
		} catch (Error ee) {
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义");
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义");
		}
	}
	
	/**
	 * 完成方法
	 */
	public void completeICTApplication() {
		try {
			String pid = getString("processId");// 流程id
			String id = getString("id");// 账户信息id
			String waitId = getString("waitId");// 待办id
			String opinion = getString("opinion");// 审批意见
			String taskId = getString("taskid");//任务id
			ICTApplication ict = reductionIctApplicationService.getICTApplication(id);
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			Map<String, String> map = new HashMap<String, String>();
			if ("市公司领导".equals(task.getActivityName())) {
				map.put("decisionKey", "SGSLD");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSBM");
			}else if ("市公司政企部经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SGSZQBJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
			}else if ("市公司政企部经理副".equals(task.getActivityName())){
				map.put("decisionKey", "SGSZQBJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
			}else if ("区县分管经理".equals(task.getActivityName())) {
				map.put("decisionKey", "QXFGJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_DSBM");
			}else if ("省重客业务管理室经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SZKYWGLSJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_SZKSM");
			}else if ("省重客分管经理".equals(task.getActivityName())) {
				map.put("decisionKey", "SZKFGJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSBM");
			}else if ("省公司政企客户部经理副".equals(task.getActivityName())) {
				map.put("decisionKey", "SGSZQKHBJL");
				map.put("decisionVal", "NO");
				jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
			}else {
				jbpmUtil.completeTask(task.getId(), "结束");
			}
			taskService.updateBpms_riskoff_task(opinion, 2, taskId);
			String rtaskid =taskService.setBpms_riskoff_task(pid,"",1,"SH","客户经理",Integer.valueOf(ict.getCreator()), user);
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(wait!=null){
				service.updateWait(wait,this.getRequest());
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			completeUpcoming(ict,Integer.valueOf(ict.getCreator()),pid, user,rtaskid,"FQZF");
			Write("OK");
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 *退回方法
	 */
	public void returnICTApplication(){
		try {
			String id = getString("id");// 转账信息id
			String processId = getString("processId");// 流程id
			String waitId = getString("waitId");// 待办id
			String opinion = getString("opinion");// 退回意见
			String taskId = getString("taskid");//任务id
			ICTApplication ict = reductionIctApplicationService.getICTApplication(id);
			ICTApplication ictTwo = reductionIctApplicationService.getICTApplication(ict.getRevenueRecognitionId());
			List<ICTApplicationDet> detList = reductionIctApplicationService.getICTApplicationDetList(id);
			for(int i=0;i<detList.size();i++){
				ICTApplicationDet det = reductionIctApplicationService.getICTApplicationDetById(detList.get(i).getRevenueRecognitionId());
				det.setDeductionAmount(det.getDeductionAmount()-detList.get(i).getDeductionAmount());
				if((det.getDeductionAmount()-detList.get(i).getDeductionAmount())>0){
					det.setReductionBossStatus("4");
				}else{
					det.setReductionBossStatus("0");
				}
				reductionIctApplicationService.updateICTApplicationDet(det);
			}
			taskService.updateBpms_riskoff_task(opinion, 0, taskId);
			ict.setState(2);
			ictTwo.setState(0);
			reductionIctApplicationService.updateICTApplication(ict);
			reductionIctApplicationService.updateICTApplication(ictTwo);
			jbpmUtil.deleteProcessInstance(processId);
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(wait!=null){
				System.out.println("================退回开始代办================");
				service.updateWait(wait,this.getRequest());
				System.out.println("================退回结束代办================");
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			returnUpcoming(ict, ict.getCreator(),user,processId);// 调用service层方法生成待办
			Write("OK");
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 * 作废方法
	 */
	public void InvalidICTApplication(){
		try {
			String id = getString("id");// 转账信息id
			String opinion = getString("opinion");//作废原因
			String waitId = getString("waitId");
			String processId = getString("processId");// 流程id
			WaitTask wait = service.queryWaitByTaskId(waitId);
			ICTApplication ict = reductionIctApplicationService.getICTApplication(id);
			ict.setInvalidReason(opinion);
			ict.setState(-1);
			reductionIctApplicationService.updateICTApplication(ict);
			if(wait!=null){
				String rtaskid =taskService.setBpms_riskoff_task(processId,opinion,-1,"SH","客户经理",Integer.valueOf(ict.getCreator()), user);
				service.updateWait(wait,this.getRequest());
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			Write("OK");
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 * 转发阅读结束方法
	 */
	public void readEndICTApplication(){
		try {
			String id = getString("id");
			String waitId=getString("waitId");
			String taskId = getString("taskid");//任务id
			ICTApplication ict = reductionIctApplicationService.getICTApplication(id);
			//if(ict.getBossStatus()==null){
			if(ict.getInvoiceType()==1){
				String text2 =insertICTGeneralInvoiceBossService(ict,user);
				if("OK".equals(text2)){
					String text =insertICTBossService(ict,user);
					if("OK".equals(text)){
						ict.setState(0);
						ict.setBossStatus("1");
						reductionIctApplicationService.updateICTApplication(ict);
						taskService.updateBpms_riskoff_task("", 2, taskId);
						List<ICTApplicationDet> detList = reductionIctApplicationService.getICTApplicationDetList(id);
						for(int i=0;i<detList.size();i++){
							ICTApplicationDet det = reductionIctApplicationService.getICTApplicationDetById(detList.get(i).getRevenueRecognitionId());
							Long deductionAmount=det.getDeductionAmount();
							Long salesAmount=det.getSalesAmount();
							if(deductionAmount.equals(salesAmount)){
								det.setReductionBossStatus("3");
							}else{
								if(deductionAmount>0&&deductionAmount<salesAmount){
									det.setReductionBossStatus("4");
								}
							}
						}
						taskService.updateBpms_riskoff_task("已完成", 2, taskId);
						WaitTask wait = service.queryWaitByTaskId(waitId);
						if(wait!=null){
							service.updateWait(wait,this.getRequest());
						}else{
							Write("NO");
							throw new RuntimeException(" 给事务回滚，自定义"); 
						}
						ict.setState(0);
						reductionIctApplicationService.updateICTApplication(ict);
						Write("OK");
					}else{
						if("NON".equals(text)){
							Write("系统出错请重试或联系管理员");	
						}else{
							Write(text);
						}
					}
				}else{
					if("NON".equals(text2)){
						Write("系统出错请重试或联系管理员");	
					}else{
						Write(text2);
					}
				}
			}else{
				String text1 =insertICTBossService(ict,user);
				if("OK".equals(text1)){
					String text =insertICTBossServicethree(ict,user);
					if("OK".equals(text)){
						ict.setState(0);
						ict.setBossStatus("1");
						reductionIctApplicationService.updateICTApplication(ict);
						taskService.updateBpms_riskoff_task("", 2, taskId);
						List<ICTApplicationDet> detList = reductionIctApplicationService.getICTApplicationDetList(id);
						for(int i=0;i<detList.size();i++){
							ICTApplicationDet det = reductionIctApplicationService.getICTApplicationDetById(detList.get(i).getRevenueRecognitionId());
							Long deductionAmount=det.getDeductionAmount();
							Long salesAmount=det.getSalesAmount();
							if(deductionAmount.equals(salesAmount)){
								det.setReductionBossStatus("3");
							}else{
								if(deductionAmount>0&&deductionAmount<salesAmount){
									det.setReductionBossStatus("4");
								}
							}
						}
						taskService.updateBpms_riskoff_task("已完成", 2, taskId);
						WaitTask wait = service.queryWaitByTaskId(waitId);
						if(wait!=null){
							service.updateWait(wait,this.getRequest());
						}else{
							Write("NO");
							throw new RuntimeException(" 给事务回滚，自定义"); 
						}
						ict.setState(0);
						reductionIctApplicationService.updateICTApplication(ict);
						Write("OK");
					}else{
						if("NON".equals(text)){
							Write("系统出错请重试或联系管理员");	
						}else{
							Write(text);
						}
					}
				}else{
					if("NON".equals(text1)){
						Write("系统出错请重试或联系管理员");	
					}else{
						Write(text1);
					}
				}
				
			}
				
			/*}else{
				taskService.updateBpms_riskoff_task("", 2, taskId);
				ict.setState(0);
				reductionIctApplicationService.updateICTApplication(ict);
				ictT.setState(0);
				reductionIctApplicationService.updateICTApplication(ictT);
				WaitTask wait = service.queryWaitByTaskId(waitId);
				if(wait!=null){
					service.updateWait(wait,this.getRequest());
				}else{
					Write("NO");
					throw new RuntimeException(" 给事务回滚，自定义"); 
				}
				Write("OK");
			}*/
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	/**
	 * 获取附件消息
	 */
	public void dowloadFile() {
		String id = getString("id");
		List<Map<String, String>> s = reductionIctApplicationService.dowloadFile(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}
	
	/**
	 * 流程结束客户经理和转发人员循环转发
	 */
	public void loopForwarding(){
		try{
			String waitId=getString("waitId");
			String id = getString("id");//账户信息id
			String taskId = getString("taskid");//任务id
			Integer userid = this.getInteger("userid");//接收人ID
			String processId = getString("processId");
			String opinion=getString("opinion");
			String type  = getString("type");
			taskService.updateBpms_riskoff_task(opinion, 2, taskId);
			ICTApplication ict = reductionIctApplicationService.getICTApplication(id);
			if("ZF".equals(type)){
				String rtaskid =taskService.setBpms_riskoff_task(processId,"",1,"SH","客户经理",Integer.valueOf(ict.getCreator()),user);
				completeUpcoming(ict,Integer.valueOf(ict.getCreator()),processId,user,rtaskid,"FQZF");
			}else{
				String rtaskid =taskService.setBpms_riskoff_task(processId,"",1,"SH","转发审核",userid,user);
				completeUpcoming(ict,userid,processId,user,rtaskid,"ZF");
			}
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if(wait!=null){
				service.updateWait(wait,this.getRequest());
			}else{
				Write("NO");
				throw new RuntimeException(" 给事务回滚，自定义"); 
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("No");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}
	}
	
	public void getICTApplicationType(){
		try {
			String numbering = getString("numbering");//工单号
			String title = getString("title");//工单标题
			String id = getString("id");//id
			String phone_no = getString("phone_no");//产品号码
			ICTApplication ict=null;
			if(!"".equals(id)&&id!=null){
				ict = reductionIctApplicationService.getICTApplication(id);
			}else{
				ict = reductionIctApplicationService.getICTApplicationType(numbering,title,phone_no);
			}
			if(ict==null){
				Write("null");
			}else{
				Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(ict));
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getICTApplicationTypet(){
		try {
			String numbering = getString("numbering");//工单号
			String id = getString("id");//id
			String title = getString("title");//id
			String phone_no = getString("phone_no");//id
			ICTApplication ict=null;
			if(!"".equals(id)&&id!=null){
				ict = reductionIctApplicationService.getICTApplication(id);
			}else{
				ict = reductionIctApplicationService.getICTApplicationType(numbering,title,phone_no);
			}
			if(ict==null){
				Write("null");
			}else{
				String whether =selectICTBossServicet(ict.getNumbering());
				if("Y".equals(whether)){
					Write(whether);
				}else if("NO".equals(whether)){
					Write(whether);
				}else if("N".equals(whether)){
					Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(ict));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 查询应收未收工单列表
	 */
	public void getICTApplicationPage() {
		try {
			Integer pageNo = getInteger("pageNo");// 当前页码数
			Integer pagesize = getInteger("pageSize");// 每页显示件数
			String numbering = getString("numbering");//工单号
			String title = getString("title");// 工单标题
			Integer state = getInteger("state"); // 状态
			String selectcon = getString("selectcon"); // 状态
			LayuiPage page = new LayuiPage(pageNo, pagesize);
			if(state==2){
				page = reductionIctApplicationService.getICTApplicationPageo(page, title, numbering, state,selectcon, user);
			}else{
				page = reductionIctApplicationService.getICTApplicationPaget(page, title, numbering, state,selectcon, user);
			}
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 根据主表编号查询所有关联的信息
	 */
	public void getICTApplicationDetList() {
		try {
			String id = getString("id");
			List<ICTApplicationDet> receiveApplyDet = reductionIctApplicationService.getICTApplicationDetList(id);
			Write(new GsonBuilder().serializeNulls().setDateFormat("yyyy-MM-dd").excludeFieldsWithoutExposeAnnotation().create()
					.toJson(receiveApplyDet));
		} catch (Exception e) {
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 根据id获取工单信息
	 */
	public void getICTApplication() {
		try {
			String id = getString("id");
			ICTApplication sal = reductionIctApplicationService.getICTApplication(id);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(sal));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getICTApplicationDetPreempt(){
		try {
			String id = getString("id");
			List<ICTApplicationDet> sal = reductionIctApplicationService.getICTApplicationDetPreempt(id);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(sal));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getICTApplicationDetPage(){
		try {
			Integer pageNo = getInteger("pageNo");
			Integer pageSize = getInteger("pageSize");
			LayuiPage page = new LayuiPage(pageNo, pageSize);
			String id = getString("id");//id
			String json = reductionIctApplicationService.getICTApplicationDetPage(page,id);
			Write(json);
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getServiceNumber(){
		try {
			Integer pageNo = getInteger("pageNo");
			Integer pageSize = getInteger("pageSize");
			LayuiPage page = new LayuiPage(pageNo, pageSize);
			String serviceNumber = getString("serviceNumber");//账户
			String json = reductionIctApplicationService.getServiceNumber(page,serviceNumber);
			Write(json);
		}catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 查询配置金额
	 */
	public void getTransferCitiesData(){
		try{
			String dangqianrenwu = getString("dangqianrenwu");
			List<SystemDept> deptList = user.getSystemDept();
		   	String code =deptList.get(0).getSystemCompany().getCompanyCode();
		   	TransferCitiesData transferCitiesData= reductionIctApplicationService.getTransferCitiesData(code,dangqianrenwu);
		   	Write(JSONHelper.SerializeWithNeedAnnotation(transferCitiesData));
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	public void getReductionICTApplicationDet(){
		try{
			String id = getString("id");
			List<ICTApplicationDet> sal = reductionIctApplicationService.getICTApplicationDetList(id);
			List<ICTApplicationDet> list = new ArrayList<ICTApplicationDet>();
			for(int i=0;i<sal.size();i++){
				ICTApplicationDet cdsa= reductionIctApplicationService.getICTApplicationDetById(sal.get(i).getRevenueRecognitionId());
				list.add(cdsa);
			}
		   	Write(JSONHelper.SerializeWithNeedAnnotation(list));
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	public void getReductionICTApplicationDetTwo(){
		try{
			String number = getString("number");
			ICTApplication ict = reductionIctApplicationService.getICTApplicationType(number,null,null);
			if(ict.getState()==-3){
				Write("-3");
			}else{
				List<ICTApplicationDet> sal = reductionIctApplicationService.getICTApplicationDetList(ict.getId());
			   	Write(JSONHelper.SerializeWithNeedAnnotation(sal));
			}
			
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getTimestamp(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatethree() {
		Calendar calendar = Calendar.getInstance();
        calendar.add (Calendar.SECOND, 2);
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(calendar.getTime());
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatetime(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	 /**
     * 分转元，转换为bigDecimal在toString
     * @return
     */
    public static String yuanToCents(int price) {
        return BigDecimal.valueOf(Long.valueOf(price)).divide(new BigDecimal(100)).toString();
    }
	
	//提交待办生成
	public void commitUpcoming(ICTApplication ict, Integer userid, String processId, SystemUser user, String taskid) {
		WaitTask wt = new WaitTask();
		wt.setName("[ICT]" + ict.getTitle());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/ICT/reductionHandleICTApplication.jsp?id="+ict.getId()+"&processId="// 流程ID
				+ processId+"&taskid="+taskid);
		SystemUser USER = systemUserService.getUserInfoRowNo(userid);
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(ICTApplication.ICTApplication);
		wt.setTaskId(ict.getId());
		service.saveWait(wt, this.getRequest());
	}

	// 完成代办生成
	public void completeUpcoming(ICTApplication ict, Integer userid, String processId, SystemUser user,String taskid,
			String type) {
		String ktype="K";
		WaitTask wt = new WaitTask();
		wt.setName("[ICT]" + ict.getTitle());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/ICT/reductionHandleICTApplication.jsp?id=" + ict.getId() 
				+ "&taskid=" + taskid
				+ "&processId="+processId+"&type="+type+"&ktype="+ktype);
		SystemUser USER = systemUserService.getUserInfoRowNo(userid);
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(ICTApplication.ICTApplication);
		wt.setTaskId(ict.getId());
		service.saveWait(wt, this.getRequest());
	}

	// 退回待办生成
	public void returnUpcoming(ICTApplication ict, String userid, SystemUser user,String processId) {
		WaitTask wt = new WaitTask();
		wt.setName("[ICT]" + ict.getTitle());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/ICT/returnReductionICTApplication.jsp?id=" + ict.getId()
				+ "&processId="+processId);
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(ICTApplication.ICTApplication);
		wt.setTaskId(ict.getId());
		service.saveWait(wt, this.getRequest());
	}
	
	//===============================================================================================================================================
	// 设置参数
	protected String setParamObj(JSONObject body, String bossNo) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "14");
		routing.put("ROUTE_VALUE", bossNo);
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		return root.toString();
	}
	
	/**
	 * json 数据格式化：
	 * @param body
	 * @return
	 */
	protected String setParamObjEsb(JSONObject body,String productNo) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "10");
		routing.put("ROUTE_VALUE",productNo);
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}
	
	/**
	 * 获取产品简称
	 */
	public void getICTConfigurationSimpleName(){
		try {
			List<Map<String, String>> s = reductionIctApplicationService.getICTConfigurationSimpleName();
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");
		}
	}
	
	/**
	 * 获取产品名称
	 */
	public void getICTConfigurationName(){
		try {
			String name = getString("name");//账户
			List<Map<String, String>> s = reductionIctApplicationService.getICTConfigurationName(name);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");
		}
	}
	
	/**
	 * ICT设备销售甩单冲正接口
	 * 
	 * ict 收入核减工单
	 */
	public String insertICTBossService(ICTApplication ict,SystemUser user){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://************:51000/esbWS/rest/";//测试
		try{
			String type="";
			String url = ESB_URL + "com_sitech_custsvc_atom_inter_IP4752AoSvc_throwOrderForICT";
			System.out.println("请求地址=======："+url);
			ICTApplication ictT = reductionIctApplicationService.getICTApplication(ict.getRevenueRecognitionId());
			List<ICTApplicationDet> list =ictApplicationService.getICTApplicationDetList(ict.getId()); 
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			JSONObject outObjfour = new JSONObject();
			JSONObject object = new JSONObject();
			JSONArray list1 = new JSONArray();
			object.put("PHONE_NO",ictT.getProductNumber()+"");//产品号码
			object.put("ALL_REJ_ORDER_ID",ictT.getNumbering()+"");//甩单流水
			object.put("GROUP_CODE",ictT.getGroupCode()+"");//集团编码
			object.put("GROUP_NAME",ictT.getGroupName()+"");//集团名称
			object.put("DRAFT_TYPE",ictT.getInvoiceType()+"");//开票类型(0:专票,1:普票,2:不开票)
			object.put("OP_TYPE","2");//操作类型(1:收入确认;2:收入核减)
			object.put("IF_DRAFT","N");//是否出票(Y:是;N:否;默认值:N)
			object.put("OP_CODE","4752");//操作编码(默认4752)
			object.put("GROUP_ID",groupId+"");//机构号
			object.put("OP_TIME",getTimestampTwo(new Date()));//操作时间YYYYMMDDHH24MISS
			object.put("LOGIN_NO",user.getBossUserName()+"");//操作工号
			object.put("REMAKE",ict.getExplanation()+"");//备注
			for(int i=0;i<list.size();i++){
				JSONObject outObjthree = new JSONObject();
				ICTApplicationDet det =ictApplicationService.getICTApplicationDetById(list.get(i).getRevenueRecognitionId()); 
				List<ICTConfiguration> listDetail =ictApplicationService.getICTConfigurationExplanation(det.getProductName());
				outObjthree.put("REJ_ORDER_ID",det.getDetailedCoding()+"");//设备流水
				outObjthree.put("SALE_AMOUNT",det.getSalesAmount()+"");//销售金额(单位是分)
				outObjthree.put("DRAFT_AMOUNT",det.getInvoiceAmount()+"");//开票金额(单位是分)
				outObjthree.put("DEC_AMOUNT",list.get(i).getDeductionAmount()+"");//核减金额(核减时必传)
				outObjthree.put("ICT_NAME",det.getProductName()+"");//ICT设备名称
				outObjthree.put("ICT_SIMP_NAME",det.getProductAbbreviation()+"");//ICT设备简称
				outObjthree.put("MAC_DETAIL",listDetail.get(0).getExplanation()+"");//设备描述
				ICTApplicationDet ictdet = ictApplicationService.getByRevenueRecognitionId(list.get(i).getRevenueRecognitionId());
				outObjthree.put("OLD_REJ_ORDER_ID",ictdet.getDetailedCoding());//原甩单订单号（核减时必传）
				outObjthree.put("SALE_TYPE",list.get(i).getSalesType()+"");//销售类型(0:ICT设备;1:终端)
				outObjthree.put("REMAKE",ict.getExplanation()+"");//备注
				list1.add(outObjthree);
			}
			object.put("MAC_LIST", list1);
			outObjfour.put("BUSI_INFO", object);
			String json1 = setParamObj(outObjfour,user.getBossUserName());
			logger.info("ICT申请调用接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			logger.info("ICT申请调用接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}
	
	
	/**
	 * ICT专票冲正接口
	 * ict 收入核减工单
	 */
	public String insertICTBossServicethree(ICTApplication ict,SystemUser user){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://************:51000/esbWS/rest/";//测试
		try{
			String type="";
			String url = ESB_URL + "s8248PrintedApp";
			System.out.println("请求地址=======："+url);
			ICTApplication ictT = reductionIctApplicationService.getICTApplication(ict.getRevenueRecognitionId());
			List<ICTApplicationDet> list =ictApplicationService.getICTApplicationDetList(ict.getId()); 
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			JSONObject object = new JSONObject();
			JSONArray list1 = new JSONArray();
			ICTApplication ictTwo = ictApplicationService.getICTApplication(ict.getRevenueRecognitionId());
			SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(ict.getAuditId()));
			object.put("GROUP_ID",groupId);//归属机构代码
			object.put("LOGIN_NO",USER.getBossUserName());//必填，申请开具工号
			object.put("OPR_FLAG","1");//必填，申请操作类型，1冲红 0作废
			object.put("QRY_MONTH",getTimestampTwo(ictTwo.getCreateDate()));//查询月份
			object.put("REPORT_TO",ictT.getContractNo()+"");//必填。该工号用于申请成功后登陆8247审批发票开具用
			object.put("REASON",ictT.getExplanation()+"");//
			object.put("DATA_SOURCE","ict");//必填。默认填写ict(全小写)表示ICT设备类专票。用来区分发票来源类型
			for(int i=0;i<list.size();i++){
				ICTApplicationDet det =ictApplicationService.getICTApplicationDetById(list.get(i).getRevenueRecognitionId()); 
				JSONObject outObjthree = new JSONObject();
				outObjthree.put("ORDER_SN",det.getDetailedCoding());//订单流水号
				outObjthree.put("REPORT_TIME",getTimestampTwo(new Date()));//申请日期非空，精确到秒，纯数字
				list1.add(outObjthree);
			}
			object.put("INMSG", list1);
			String json1 = setParamObj(object,user.getBossUserName());
			logger.info("ICT申请调用接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			logger.info("ICT申请调用接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}
	
	
	/**
	 * ICT查询剩余未回款接口
	 */
	public void selectICTBossService(){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://************:51000/esbWS/rest/";//测试
		try{
			String url = ESB_URL + "com_sitech_custsvc_atom_inter_IP4752AoSvc_qrySurplusAmount";
			System.out.println("请求地址=======："+url);
			String number = getString("number");
			ICTApplication ictT = reductionIctApplicationService.getICTApplicationType(number,null,null);
			JSONObject outObjfour = new JSONObject();
			JSONObject object = new JSONObject();
			object.put("PHONE_NO",ictT.getProductNumber()+"");//产品号码
			object.put("ALL_REJ_ORDER_ID",ictT.getNumbering()+"");//甩单流水
			outObjfour.put("BUSI_INFO", object);
			String json = setParamObj(outObjfour,user.getBossUserName());
			logger.info("ICT查询剩余未回款接口输入参数："+json);
			String jsonString = UrlConnection.responseGBK(url, json.toString());
			logger.info("ICT查询剩余未回款接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			//String dsa ="{\"ROOT\": {\"HEADER\": {},\"BODY\": {\"OUT_DATA\": {\"LIST\": [{\"SURPLUS_AMOUNT\": 1000000,\"TOL_DEC_AMOUNT\": *********,\"REJ_ORDER_ID\": \"SDXTYA202007311521550910\",\"SALE_AMOUNT\": *********,\"TOL_REBACK_AMOUNT\": 0},{\"SURPLUS_AMOUNT\": 100,\"TOL_DEC_AMOUNT\": *********,\"REJ_ORDER_ID\": \"SDXTYA20200731152155091\",\"SALE_AMOUNT\": *********,\"TOL_REBACK_AMOUNT\": 0},{\"SURPLUS_AMOUNT\": 50000000,\"TOL_DEC_AMOUNT\": 50000000,\"REJ_ORDER_ID\":\"CD202005081806061712\",\"SALE_AMOUNT\": *********,\"TOL_REBACK_AMOUNT\": 0}]},\"RETURN_CODE\": \"0\",\"RETURN_MSG\": \"OK\",\"USER_MSG\": \"OK\",\"DETAIL_MSG\": \"OK\",\"RUN_IP\":\"*************\",\"PROMPT_MSG\": \"\"}}}";
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			JSONObject jsThree = JSONObject.fromObject(jstwo.getString("BODY"));
			if("0".equals(jsThree.getString("RETURN_CODE"))){
				JSONObject outdata=JSONObject.fromObject(jsThree.getString("OUT_DATA"));
				JSONArray list = JSONArray.fromObject(outdata.getString("LIST"));
				Write(list.toString());
			}else{
				Write("NON_"+jsThree.getString("DETAIL_MSG"));
			}
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * ICT是否出票查询接口
	 */
	public String selectICTBossServicet(String number){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://**************:58000/esbWS/rest/";//测试
		try{
			String url = ESB_URL + "com_sitech_custsvc_atom_inter_IP4752AoSvc_qryIfDraft";
			System.out.println("请求地址=======："+url);
			ICTApplication ictT = reductionIctApplicationService.getICTApplicationType(number,null,null);
			JSONObject outObjfour = new JSONObject();
			JSONObject object = new JSONObject();
			object.put("ALL_REJ_ORDER_ID",ictT.getNumbering()+"");//甩单流水
			outObjfour.put("BUSI_INFO", object);
			String json = setParamObj(outObjfour,user.getBossUserName());
			logger.info("ICT是否出票查询接口输入参数："+json);
			String jsonString = UrlConnection.responseGBK(url, json.toString());
			logger.info("ICT是否出票查询接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			//String dsa ="{\"ROOT\": {\"HEADER\": {},\"BODY\": {\"OUT_DATA\": {\"IF_DRAFT\":\"N\"},\"RETURN_CODE\": \"0\",\"RETURN_MSG\": \"OK\",\"USER_MSG\": \"OK\",\"DETAIL_MSG\": \"OK\",\"RUN_IP\":\"*************\",\"PROMPT_MSG\": \"\"}}}";
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			JSONObject jsThree = JSONObject.fromObject(jstwo.getString("BODY"));
			if("0".equals(jsThree.getString("RETURN_CODE"))){
				JSONObject outdata=JSONObject.fromObject(jsThree.getString("OUT_DATA"));
				String whether = outdata.getString("IF_DRAFT");
				return whether;
			}else{
				return "NO";
			}
		}catch(Exception e){
			e.printStackTrace();
			return "NO";
		}
	}
	
	/**
	 * ICT专票推送接口
	 */
	public String insertICTGeneralInvoiceBossService(ICTApplication ict,SystemUser user){
		String ESB_URL="http://*************:51000/esbWS/rest/";
		//String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String ESB_URL="http://**************:58000/esbWS/rest/";//测试
		//String ESB_URL="http://************:51000/esbWS/rest/";//测试
		try{
			String type="";
			String url = ESB_URL + "s8248Cfm";
			System.out.println("请求地址=======："+url);
			List<ICTApplicationDet> list =ictApplicationService.getICTApplicationDetList(ict.getId()); 
			Map<String,Object> mapcfm =CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json =com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			ICTApplication ictTwo = reductionIctApplicationService.getICTApplication(ict.getRevenueRecognitionId());
			JSONObject obj = JSONObject.fromObject(json);
			String groupId = obj.getString("GROUP_ID");
			String regionid = obj.getString("REGION_ID");
			JSONObject object = new JSONObject();
			JSONObject objectData = new JSONObject();
			JSONArray list1 = new JSONArray();
			object.put("OP_CODE","SDXT");//服务号码
			object.put("CONTRACT_NO",ict.getContractNo()==null?"":Long.parseLong(ict.getContractNo()));// 账户
			object.put("PHONE_NO",ict.getProductNumber());//发票须展示的号码
			object.put("INSYS_PHONE_NO",ict.getProductNumber());//系统记录的手机号码（虚拟号码）
			object.put("LOGIN_NO",user.getBossUserName());//工号 group_id
			object.put("REGION_ID",regionid);//工号 region_id
			object.put("GROUP_ID",groupId);//客户ID
			object.put("BUSI_GROUP_ID",groupId);//账户号码
			object.put("KPXM",ict.getBillingItems());//开票项目
			object.put("INV_KPLX","2");//开票类型  1正票 、 2红票(I，必传)
			object.put("INV_GHFMC",ict.getGroupName());//购货方名称
			object.put("INV_GHFQYLX",ict.getBillingChannel());//01：企业02：机关事业单位03：个人04：其它
			object.put("INV_CZDM","10");//操作代码 10 正常开具 20 冲红
			String amount = BigDecimal.valueOf(ict.getAmount()).divide(new BigDecimal(100)).toString();
			object.put("INV_KPHJJE",amount);//总金额
			object.put("INV_FPQQLSH",ict.getNumbering());//流水
			object.put("DATA_SOURCE","ict");//开票渠道 crm
			object.put("INV_YFP_DM","");//冲红的原发票代码（INV_KPLX =2 时传入）
			object.put("INV_YFP_HM","");//冲红的原发票号码（INV_KPLX =2 时传入）
			object.put("INV_TSCHBZ","0");//冲红特殊标志 0 电子发票，1 其他 （INV_KPLX =2 时传入）
			object.put("INV_CHYY",ict.getExplanation());//冲红原因（INV_KPLX =2 时传入）
			object.put("INV_ALLCREDIT","a");//是否部分冲红 p部分 a全部冲红（目前只支持全部冲红）（INV_KPLX =2 时传入）
			object.put("INV_LFPQQLSH",ictTwo.getNumbering());//原蓝字发票请求流水（INV_KPLX =2 时传入）
			object.put("INV_KP_YM",getTimestampFour(ict.getBossDate()));//原蓝字发票的开具时间（年月）
			object.put("MSG_RECV_PHONE",ictTwo.getMsgRecvPhone());//电子发票短信接收号码
			object.put("RECV_EMAIL","");//电子发票邮件接收短信
			for(int i=0;i<list.size();i++){
				JSONObject outObjthree = new JSONObject();
				outObjthree.put("INV_XMSL",list.get(i).getSalesVolumes());//项目数量
				String salesAmount = BigDecimal.valueOf(list.get(i).getSalesAmount()).divide(new BigDecimal(100)).toString();
				outObjthree.put("INV_XMDJ",salesAmount);//项目单价
				String invXmje = BigDecimal.valueOf(list.get(i).getInvoiceAmount()).divide(new BigDecimal(100)).toString();
				outObjthree.put("INV_XMJE",invXmje);//项目金额
				outObjthree.put("INV_FPHXZ",list.get(i).getNatureInvoiceLine());//发票行性质: 0正常行、1折扣行、2被折扣行
				outObjthree.put("INV_XMMC","*"+list.get(i).getProductAbbreviation()+"*"+list.get(i).getProductName());//项目名称
				outObjthree.put("INV_HSBZ",list.get(i).getTaxMark());//含税标志，0表示都不含税，1表示都含税。
				List<ICTConfiguration> ictp= ictApplicationService.getICTConfigurationExplanation(list.get(i).getProductName());
				String code = ictp.get(0).getCode();
				if(code.length()<19){
		    	   int length =19-code.length();
		    	   for(int j=0;j<length;j++){
		    		   code+="0";
		    	   }
			    }
				outObjthree.put("SPBM",code);//商品编码（税局下发的19位商品编码，固定19 位）
				list1.add(outObjthree);
			}
			objectData.put("DATA", list1);
			object.put("INV_DATA_NODE", objectData);
			object.put("PJX_MEM",list.size());
			object.put("INV_BZ",ict.getExplanation());//发票备注，注意，冲红时该字段必填
			object.put("GHF_NSRSBH", ict.getTaxPayer());
			object.put("GHF_DZ", ict.getTaxAddress());
			object.put("GHF_YHZH", ict.getTaxBankAccount());
			String json1 = setParamObj(object,user.getBossUserName());
			logger.info("ICT普票申请调用接口输入参数："+json1);
			String jsonString = UrlConnection.responseGBK(url, json1.toString());
			logger.info("ICT普票申请调用接口返回参数："+jsonString);
			JSONObject jsthree = JSONObject.fromObject(jsonString);
			String datatwo = jsthree.getString("res");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if("0".equals(jstwo.getString("RETURN_CODE"))){
				type="OK";
			}else{
				type=jstwo.getString("RETURN_MSG");
			}
			return type;
		}catch(Exception e){
			e.printStackTrace();
			return "NON";
		}
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getTimestampTwo(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getTimestampThree(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMM");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getTimestampFour(String currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
}
