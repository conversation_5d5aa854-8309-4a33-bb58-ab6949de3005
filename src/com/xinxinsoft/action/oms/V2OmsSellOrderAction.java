package com.xinxinsoft.action.oms;

import com.itextpdf.text.Document;
import com.itextpdf.text.DocumentException;
import com.itextpdf.text.Image;
import com.itextpdf.text.PageSize;
import com.itextpdf.text.pdf.PdfWriter;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.contractUniformity.Con_OrderForm;
import com.xinxinsoft.entity.contractUniformity.ContractInfo;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.executeJobLog.JobLog;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.oms.*;
import com.xinxinsoft.entity.pms.PmsPriceinfo;
import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.entity.pms.PmsProductInfo;
import com.xinxinsoft.entity.pms.PmsProductLabel;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.OmsSellOrderService.OmsSellOrderSrv;
import com.xinxinsoft.sendComms.groupOrderService.GrpOrderIdAcceptSrv;
import com.xinxinsoft.sendComms.omsService.BusiOppService;
import com.xinxinsoft.sendComms.omsService.GoodsPrcInfoSrv;
import com.xinxinsoft.sendComms.omsService.OrderEsbService;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.appOpenService.OMSService;
import com.xinxinsoft.service.contract.CustomClauseContractService;
import com.xinxinsoft.service.core.user.StructureOfPersonnelService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.oms.OmsOrderProductService;
import com.xinxinsoft.service.oms.OmsOrderWorkbenchService;
import com.xinxinsoft.service.oms.V2OmsSellOrderService;
import com.xinxinsoft.service.smsPush.SmsPushService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.ProcessUtils;
import com.xinxinsoft.utils.StringHelper;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.activation.MimetypesFileTypeMap;
import javax.annotation.Resource;
import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 销售工单（客户经理需求单）
 */
public class V2OmsSellOrderAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(V2OmsSellOrderAction.class);
    @Resource(name = "V2OmsSellOrderService")
    private V2OmsSellOrderService omsSellOrderService;
    private OmsOrderWorkbenchService omsOrderWorkbenchService;
    private OmsOrderProductService omsOrderProductService;
    private AttachmentService attachmentService;
    private Bpms_riskoff_service taskService;
    private WaitTaskService service;//待办
    private SystemUserService systemUserService;//系统人员工具
    @Resource(name = "StructureOfPersonnelService")
    private StructureOfPersonnelService structureOfPersonnelService;
    @Resource(name = "OMSService")
    private OMSService omsService;
    @Resource(name="smsPushService")
    private SmsPushService smsPushService;
    @Resource(name="CustomClauseContractService")
    private CustomClauseContractService customClauseContractService;
    private File file1;
    private String file1FileName;

    private File fileBack;
    private String fileBackFileName;
    private File fileLicense;
    private String fileLicenseFileName;

    public OmsOrderWorkbenchService getOmsOrderWorkbenchService() {
        return omsOrderWorkbenchService;
    }

    public void setOmsOrderWorkbenchService(OmsOrderWorkbenchService omsOrderWorkbenchService) {
        this.omsOrderWorkbenchService = omsOrderWorkbenchService;
    }

    public OmsOrderProductService getOmsOrderProductService() {
        return omsOrderProductService;
    }

    public void setOmsOrderProductService(OmsOrderProductService omsOrderProductService) {
        this.omsOrderProductService = omsOrderProductService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public File getFile1() {
        return file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }

    public String getFile1FileName() {
        return file1FileName;
    }

    public void setFile1FileName(String file1FileName) {
        this.file1FileName = file1FileName;
    }

    public File getFileBack() {
        return fileBack;
    }

    public void setFileBack(File fileBack) {
        this.fileBack = fileBack;
    }

    public String getFileBackFileName() {
        return fileBackFileName;
    }

    public void setFileBackFileName(String fileBackFileName) {
        this.fileBackFileName = fileBackFileName;
    }

    public File getFileLicense() {
        return fileLicense;
    }

    public void setFileLicense(File fileLicense) {
        this.fileLicense = fileLicense;
    }

    public String getFileLicenseFileName() {
        return fileLicenseFileName;
    }

    public void setFileLicenseFileName(String fileLicenseFileName) {
        this.fileLicenseFileName = fileLicenseFileName;
    }
    /**
     * @Description: 工单手动推送超时预警
     * @return: void
     * @Author: TX
     * @Date: 2021/10/14 11:32
     */
    public void CountOmsTimeoutTemind(){
        Map<String,Object> map =new HashMap<String,Object>();
        try{
            String linkOrderNo = getString("linkOrderNo");
            List<Map<String,Object>> list = omsSellOrderService.QueryOrderByLinkStatus(linkOrderNo);
            Map<String,Object> obj;
            if (list.size()==1){
                obj = list.get(0);
            }else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","获取工单信息失败,工单信息异常,请联系管理员处理!");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            SystemUser usertwo = systemUserService.getByUserInfoRowNo(Integer.parseInt(obj.get("OPER_NO").toString()));
            if (usertwo.getMobile()==null||usertwo.getMobile().equals("")){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","环节处理人的电话为空,推送提醒信息失败");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            omsSellOrderService.saveddyjPush_0_0001(obj.get("CREATOR_NAME").toString(),obj.get("CREATOR_DATE").toString(),obj.get("TITLE").toString(),usertwo.getMobile());
            map.put("code",1);
            map.put("data","");
            map.put("msg","信息推送成功!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            map.put("code",-1);
            map.put("data","");
            map.put("msg","推送提醒信息失败,程序异常,请联系管理员处理!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }
    /**
     * @Description: 获取指定小时后的日期
     * @Param: [date:起始时间, hours:小时数]
     * @return: java.util.Date
     * @Author: TX
     * @Date: 2021/10/8 16:42
     */
    public Date getTargetDate(Date date,Integer hours){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //判断当前日期是否为周末,是的话获取后一天0点
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.set(Calendar. HOUR_OF_DAY , 0);
            calendar.set(Calendar. MINUTE , 0);
            calendar.set(Calendar. SECOND , 0);
            calendar.set(Calendar. MILLISECOND , 0);
            calendar.add(Calendar. DAY_OF_MONTH , 1);
        }
        //判断配置时间是否超过一天
        while (hours>=24){
            //当前时间加一天,并判断加一天后是否未周末,如果是继续加一天
            calendar.add(Calendar.DAY_OF_MONTH, +1);
            while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
                calendar.add(Calendar.DAY_OF_MONTH, +1);
            }
            hours-=24;
        }
        //未超过一天(24小时)直接加到当前时间的上,并判断加上后时间是否为周末,如果是再加一天
        calendar.add(Calendar.HOUR_OF_DAY, +hours);
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.add(Calendar.DAY_OF_MONTH, +1);
        }
        return calendar.getTime();
    }

    /**
     * 查询合同集合
     */
    public void queryContractInfoList(){
        try {
            Integer pageNo = getInteger("pageNo");//第几页
            Integer pageSize = getInteger("pageSize");//每页显示多少数据
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String contractName = getString("contractName");
            String groupCode = getString("groupCode");
            String contractid = getString("contractid");
            String pageStr = omsSellOrderService.queryBossFormInfo(page,user.getRowNo(),contractName,groupCode,contractid);
            this.Write(pageStr);
        } catch (Exception e) {
            logger.error("查询合同集合异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/6 14:49
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询列表
     */
    public void getOmsSellOrderList(){
        try {
            Integer type=getInteger("type");
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数
            String orderNo=getString("orderNo");//工单编码
            String title=getString("title");//需求名称
            String unitId=getString("unitId");//集团280
            String unitName=getString("unitName");//集团名称
            String startTime=getString("startTime");
            String endTime=getString("endTime");
            String state=getString("state");
            String prcNo=getString("prcNo");
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            LayuiPage json=null;
            if(type==4){
                json= omsSellOrderService.getOderMyApprovalComplete(type,page,user,orderNo,title,unitId,unitName,startTime,endTime,state,prcNo);
            }else{
                json= omsSellOrderService.getOmsSellOrderList(type,page,user,orderNo,title,unitId,unitName,startTime,endTime,state,prcNo);
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        }catch (Exception e) {
            logger.error("需求单列表异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("1");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/6 14:49
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询待处理的环节待办列表
     */
    public void getOderSubLink(){
        try {
            Integer type=getInteger("type");
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数
            String orderNo=getString("orderNo");//工单编码
            String title=getString("title");//需求名称
            String unitId=getString("unitId");//集团280
            String unitName=getString("unitName");//集团名称
            String startTime=getString("startTime");
            String endTime=getString("endTime");
            String prcNo=getString("prcNo");
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            LayuiPage json= omsSellOrderService.getOmsSellOrderPageBylinkPC(page,user,orderNo,title,unitId,unitName,startTime,endTime,prcNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        }catch (Exception e) {
            logger.error("需求单列表异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("1");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 10:40
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 客户经理关联合同
     */
    public void queryContractInfo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String orderId = getString("orderId");
            String waitId=getString("waitId");
            String taskId=getString("taskId");
            String workbenchId=getString("workbenchId");
            String userId = this.getString("userId");//订单经理ID
            String userName = this.getString("userName");//订单经理名称
            String omsReturnType = getString("omsReturnType");//是否需求驳回客户经理重新关联合同（0是）
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(orderId);
            oms.setModifyDate(new Date());
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(Integer.parseInt(oms.getCreateNo()));
            for (int j = 0; j < sone.size(); j++) {
                IBM = (String) sone.get(j)[2];
            }
            ContractInfo contractInfo= omsSellOrderService.queryContractInfo(id);
            if("0".equals(omsReturnType)){
                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(oms.getOperateName());
                dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(oms.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),oms.getTitle(),"1",rdig.getId());
                if(contractInfo!=null){
                    oms.setContractId(contractInfo.getId());
                }else{
                    throw  new Exception("合同查询为空，请确认");
                }
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                map.put("code",1);
                map.put("data","");
                map.put("msg","操作成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }else{
                OmsOrderLink link =omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                boolean bl=true;
                if("5".equals(link.getLinkCode())){
                    if(contractInfo!=null){
                        oms.setContractId(contractInfo.getId());
                    }else{
                        throw  new Exception("合同查询为空，请确认");
                    }
                    omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                    List<OmsOrderProduct> list = omsSellOrderService.getOmsOrderProductList(oms.getOrderNo());
                    for(OmsOrderProduct pro:list){
                        if(pro.getGrpOrdId()!=null){
                            if(oms.getDemandType()==0){
                                if(!"1".equals(pro.getGrpOrdId())){
                                    if(pro.getIsPushBoss()==1){
                                        if(pro.getBossCallback()==null){
                                            bl=false;
                                        }else{
                                            if(pro.getBossCallback()==1){
                                                bl=false;
                                            }
                                        }
                                    }else{
                                        bl=false;
                                    }
                                }else{
                                    bl=false;
                                }
                            }
                        }else{
                            if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                bl=false;
                            }
                        }
                    }
                    if(bl){
                        link.setOper_date(new Date());
                        link.setStatus(1);
                        omsSellOrderService.saveOrupdateOmsOrderLink(link);
                        OmsPretreatmentDate nextOmsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"6");
                        OmsOrderLink nextLink = new OmsOrderLink();
                        nextLink.setCreator_name(user.getEmployeeName());//发起人
                        nextLink.setCreator_no(user.getRowNo());//发起人工号
                        nextLink.setCreator_date(new Date());//发起人时间(当前时间)
                        nextLink.setOper_name(user.getEmployeeName());//操作人
                        nextLink.setOper_no(user.getRowNo());//操作人工号
                        nextLink.setOper_date(new Date());//操作时间(当前时间)
                        nextLink.setStatus(1);//状态(状态根据环节确定)
                        nextLink.setLinkCode("6");//环节编码或者固定的环节编码
                        nextLink.setLinkName("需求归档");//环节名称
                        nextLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                        nextLink.setLinkOrderNo(IBM + taskService.getNumber());
                        nextLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(nextOmsPretreatmentDate.getPretreatment_date())));
                        omsSellOrderService.saveOrupdateOmsOrderLink(nextLink);
                        map.put("code",1);
                        map.put("data","");
                        map.put("msg","关联合同成功");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    }else{
                        map.put("code",1);
                        map.put("data","");
                        map.put("msg","关联合同成功");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    }
                }else{
                    link.setOper_date(new Date());
                    link.setStatus(1);
                    omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    if(oms.getFreeTrial()!=0) {
                        OmsPretreatmentDate beforeOmsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(), "3");
                        OmsOrderLink beforeLink = new OmsOrderLink();
                        beforeLink.setCreator_name(user.getEmployeeName());//发起人
                        beforeLink.setCreator_no(user.getRowNo());//发起人工号
                        beforeLink.setCreator_date(new Date());//发起人时间(当前时间)
                        beforeLink.setOper_name(user.getEmployeeName());//操作人
                        beforeLink.setOper_no(user.getRowNo());//操作人工号
                        beforeLink.setOper_date(new Date());//操作时间(当前时间)
                        beforeLink.setStatus(1);//状态(状态根据环节确定)
                        beforeLink.setLinkCode("3");//环节编码或者固定的环节编码
                        beforeLink.setLinkName("协议签订");//环节名称
                        beforeLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                        beforeLink.setLinkOrderNo(IBM + taskService.getNumber());
                        beforeLink.setPretreatment_date(this.getTargetDate(new Date(), Integer.parseInt(beforeOmsPretreatmentDate.getPretreatment_date())));
                        omsSellOrderService.saveOrupdateOmsOrderLink(beforeLink);
                    }
                    Thread.sleep(500);
                    OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"4");
                    OmsOrderLink nextStepLink = new OmsOrderLink();
                    nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                    nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                    nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                    nextStepLink.setOper_name(userName);//操作人
                    nextStepLink.setOper_no(Integer.parseInt(userId));//操作人工号
                    nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                    nextStepLink.setStatus(0);//状态(状态根据环节确定)
                    nextStepLink.setLinkCode("4");//环节编码或者固定的环节编码
                    nextStepLink.setLinkName("需求确认");//环节名称
                    nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                    nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                    nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                    omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);

                    OmsLinkDialogue dig = new OmsLinkDialogue();
                    dig.setCreator_name(user.getEmployeeName());
                    dig.setCreator_no(user.getRowNo());
                    dig.setCreator_date(new Date());
                    dig.setOper_name(userName);
                    dig.setOper_no(Integer.parseInt(userId));
                    dig.setOper_date(new Date());
                    dig.setStatus(0);//1已处理，0未处理
                    dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                    dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                    dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                    oms.setState("0");
                    if(contractInfo!=null){
                        oms.setContractId(contractInfo.getId());
                    }else{
                        throw  new Exception("合同查询为空，请确认");
                    }
                    if(!"undefined".equals(userId)&&userId!=null&&userId.length()>0&&!"null".equals(userId)){
                        oms.setOperateNo(userId);
                        oms.setOperateName(userName);
                        oms.setOperateDate(new Date());
                    }else{
                        throw  new Exception("订单经理为空，请确认");
                    }
                    if(!"0".equals(oms.getNoTreaty())) {
                        oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                        omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                    }
                    if(oms.getFreeTrial()==1){//不免审
                        if(!"undefined".equals(taskId)&&taskId!=null&&taskId.length()>0&&!"null".equals(taskId)) {
                            Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task("已归档", 2, taskId);//修改本条数据
                            if(null == bpms_riskoff_task){
                                throw new Exception("未查询到当前任务信息，请确认当前任务是否存在");
                            }
                        }
                        if(!"undefined".equals(workbenchId)&&workbenchId!=null&&workbenchId.length()>0&&!"null".equals(workbenchId)) {
                            omsOrderWorkbenchService.updateOmsOrderWorkbench(workbenchId,1);
                            taskService.updatebpmsRiskoffProcess(workbenchId,2);
                        }else{
                            throw new Exception("未查询到当前审批工单ID信息，请确认当前审批是否正常");
                        }
                        WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                        if (wt != null) {
                            service.updateWait(wt, this.getRequest());
                        }else{
                            throw new Exception("未查询到待办信息,待办信息为空");
                        }
                    }
                    OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                    commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),oms.getTitle(),"1",rdig.getId());
                    map.put("code",1);
                    map.put("data","");
                    map.put("msg","关联合同成功，等待订单经理"+userName+"确认!");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("查询合同对象异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","关联合同失败"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/26 10:11
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 更改合同
     */
    public void undateOrderContract(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String omsOrderId = getString("omsOrderId");
            String waitId=getString("waitId");
            ContractInfo contractInfo= omsSellOrderService.queryContractInfo(id);
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(omsOrderId);
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            OmsLinkDialogue rdig=null;
            if(contractInfo!=null && omsOrderId!=null && !"".equals(omsOrderId)&& !"undefined".equals(omsOrderId)){
                OmsLinkDialogue odg=null;
                if(wt!=null){
                    odg = omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
                String linkOrderNo="";
                if(odg!=null){
                    linkOrderNo=odg.getLinkOrderNo();
                }else{
                    linkOrderNo=oms.getLinkOrderNo();
                }
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(linkOrderNo);
                oms.setContractId(contractInfo.getId());
                oms.setModifyDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                if(omsDig!=null){
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                }

                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(oms.getOperateName());
                dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(link.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            }
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),oms.getTitle(),"1",rdig.getId());
            map.put("code",1);
            map.put("data","");
            map.put("msg","更改合同成功，等待订单经理"+oms.getOperateName()+"确认!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("更改合同对象异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","更改合同失败"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            Write("1");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 10:40
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 补录资费BOSS信息
     */
    public void supplementary(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");
            String bossNo=getString("bossNo");
            String bossPhoneNo=getString("bossPhoneNo");
            OmsOrderProduct oms = omsSellOrderService.getOmsOrderProduct(id);
            oms.setBossNo(bossNo);
            oms.setBossPhoneNo(bossPhoneNo);
            omsSellOrderService.saveOrupdateOmsOrderProduct(oms);
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderByOrderNo(oms.getOrderNo());
            omsOrder.setModifyDate(new Date());
            omsOrder.setOperateHandleDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);
            map.put("code",1);
            map.put("data","");
            map.put("msg","BOSS信息补录成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("补录资费BOSS信息异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","BOSS信息补录失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 10:49
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 保存需求单
     */
    public void addOmsSellOrder(){
        Map<String,Object> mapJson =new HashMap<>();
        try {
            String json = getString("json");//资费json
            String attachmentId = getString("attachmentId");//附件id
            String unitId = getString("unitId");
            String title = getString("title");
            Integer demandType = getInteger("demandType");
            boolean whetherManager = Boolean.valueOf(getString("whetherManager"));//是否自行审批（0是；1不是）
            boolean electronicContract=Boolean.valueOf(getString("electronicContract"));//是否电子协议
            boolean freeTrial=Boolean.valueOf(getString("freeTrial"));//是否免审

            //String subProductJson=getString("subProductJson");
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            List<Map<String,String>> userMap=omsSellOrderService.getVwUserinfoByRowno(String.valueOf(user.getRowNo()));
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String pmsinfoName="";
            Integer score=0;
            String orderNo = IBM + taskService.getNumber();
            String unitName = getString("unitName");
            if (!"".equals(json) && json != null && json.length() > 0) {
                String priceNo="";
                JSONArray jsonArray = JSONArray.fromObject(json);
                JSONObject obj = JSONObject.fromObject(jsonArray.get(0));
                JSONArray prodArry = JSONArray.fromObject(obj.get("data").toString());
                for(int j=0;j<prodArry.size();j++){
                    JSONObject prodObj = JSONObject.fromObject(prodArry.get(j).toString());
                    if("资费代码".equals(prodObj.get("templateName"))){
                        priceNo=prodObj.get("templateValue").toString();
                    }
                }
                PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(priceNo);
                if(pmsinfo!=null){
                    pmsinfoName=pmsinfo.getPrcName()+"...";
                }
            }
            //String title = ""+unitName+"关于（"+pmsinfoName+"）需求单";
            StringBuilder prcName= new StringBuilder();
            StringBuilder prodName= new StringBuilder();
            StringBuilder labelName= new StringBuilder();
            PmsProdPriceInfo pmsinfo=null;
            PmsProductInfo pmsproduct=null;
            PmsProductLabel pmslabel=null;
            if (!"".equals(json) && json != null && json.length() > 0) {
                JSONArray jsonArray = JSONArray.fromObject(json);
                for (int i = 0; i < jsonArray.size(); i++) {
                    OmsOrderProduct omsProduct = new OmsOrderProduct();
                    String jsonArrayStr = jsonArray.getString(i);
                    JSONObject obj = JSONObject.fromObject(jsonArrayStr);
                    JSONArray prodArry = JSONArray.fromObject(obj.get("data").toString());
                    Map<String,String> objMap = new HashMap();
                    //for(int j=0;j<prodArry.size();j++){//这是正常从左到右删除数据的方法
                    for (int j = prodArry.size() - 1;j >= 0;j--){//这是从右到左删除数据的方法
                        JSONObject prodObj = JSONObject.fromObject(prodArry.get(j).toString());
                        if("折扣折让需求".equals(prodObj.get("templateName"))){
                            objMap.put("discount",prodObj.get("templateValue").toString());
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                        if("客户需求".equals(prodObj.get("templateName"))){
                            objMap.put("description",prodObj.get("templateValue").toString());
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                        if("资费代码".equals(prodObj.get("templateName"))){
                            objMap.put("prodPriceNo",prodObj.get("templateValue").toString());
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                        if("资费名称".equals(prodObj.get("templateName"))){
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                    }
                    String moneyNo = IBM + taskService.getNumber();
                    pmsinfo= omsSellOrderService.queryPmsProdPriceInfo(objMap.get("prodPriceNo"));
                    pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    pmslabel = omsSellOrderService.queryPmsProductLabel(pmsproduct.getLabelId());
                    score += pmslabel.getScore();
                    omsProduct.setPrcName(pmsinfo.getPrcName());
                    omsProduct.setSerialNumber(moneyNo);
                    omsProduct.setOrderNo(orderNo);
                    omsProduct.setLabelNo(pmslabel.getLabelId());
                    omsProduct.setProdNo(pmsproduct.getProdId());
                    omsProduct.setPrcNo(pmsinfo.getPrcId());
                    omsProduct.setIsCont(pmsproduct.getConType());
                    omsProduct.setScore(pmslabel.getScore());
                    omsProduct.setTemlates(prodArry.toString());
                    omsProduct.setDiscount(objMap.get("discount"));
                    omsProduct.setDescription(objMap.get("description"));
                    omsProduct.setHandleNo(String.valueOf(user.getRowNo()));
                    omsProduct.setHandleName(user.getEmployeeName());
                    omsProduct.setState(OmsOrderProduct.STATE_WAITING);
                    omsProduct.setHandleDate(new Date());
                    if(demandType==0){
                        /*if("1".equals(pmsinfo.getIsPushBoss())){
                            Result result= GrpOrderIdAcceptSrv.getInstance().createGrpOrder(unitId,moneyNo,"D001",
                                    pmsinfo.getPrcName(),
                                    user.getBossUserName(),
                                    String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", new Date()),pmslabel.getSubLabelType());
                            if(ResultCode.SUCCESS.code()==result.getCode()){
                                JSONObject root=JSONObject.fromObject(JSONObject.fromObject(result.getData()).get("ROOT"));
                                JSONObject body=JSONObject.fromObject(root.get("BODY"));
                                if("0".equals(body.get("RETURN_CODE"))) {
                                    JSONObject outData = JSONObject.fromObject(JSONObject.fromObject(root.get("BODY")).get("OUT_DATA"));
                                    omsProduct.setGrpOrdId(outData.get("grpOrdId").toString());
                                    omsProduct.setIsPushBoss(0);
                                }else{
                                    mapJson.put("code", -1);
                                    mapJson.put("data", "");
                                    mapJson.put("msg", "资费："+pmsinfo.getPrcName()+"注册统一ID失败" + body.get("RETURN_MSG"));
                                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                                    return;
                                }
                            }else {
                                mapJson.put("code", -1);
                                mapJson.put("data", "");
                                mapJson.put("msg", "资费："+pmsinfo.getPrcName()+"注册统一ID失败" + result.getMessage());
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                                return;
                            }
                        }*/
                        omsProduct.setGrpOrdId(DateUtil.getRandomCharAndNumr());
                        omsProduct.setIsPushBoss(0);
                    }
                    omsSellOrderService.saveOrupdateOmsOrderProduct(omsProduct);
                    prcName.append(pmsinfo.getPrcName()).append("|");
                    prodName.append(pmsproduct.getProdName()).append("|");
                    labelName.append(pmslabel.getLabelName()).append("|");
                }
            }
            OmsSellOrder oms = new OmsSellOrder();
            if (unitId != null && !"".equals(unitId) && !"undefined".equals(unitId)) {
                oms.setUnitId(unitId);//集团280
                oms.setUnitName(unitName);//集团名称
            }
            if (getString("contractId") != null && !"".equals(getString("contractId")) && !"undefined".equals(getString("contractId"))) {
                oms.setContractId(getString("contractId"));//合同ID
                //关联中间表
                ContractInfo contractInfo= omsSellOrderService.queryContractInfo(getString("contractId"));
                Con_OrderForm cof = new Con_OrderForm();
                cof.setContractId(contractInfo.getContractId());
                cof.setFormNo(orderNo);
                cof.setFormTableName(OmsSellOrder.OMSSELLORDER);
                cof.setState(0);
                omsSellOrderService.saveOrUpdateCon_OrderForm(cof);
            }

            if (getString("unitInfoId") != null && !"".equals(getString("unitInfoId")) && !"undefined".equals(getString("unitInfoId"))) {
                oms.setUnitInfoId(getString("unitInfoId"));//集团ID
            }
            oms.setDemandType(demandType);//需求类型
            oms.setOrderNo(orderNo);
            oms.setIsUnit(getString("isUnit"));//是否为建档集团
            oms.setTitle(title);//需求标题
            oms.setCompanyNo(userMap.get(0).get("COMPANY_CODE"));
            oms.setPrcName(prcName.toString());//资费名称
            oms.setProdName(prodName.toString());//产品名称
            oms.setLabelName(labelName.toString());//产品大类名称
            oms.setCreateDate(new Date());
            oms.setExpectDate(strToDate(plusDay2(score)));
            oms.setDeleteState(OmsSellOrder.DELETE_FALSE);
            oms.setState(OmsSellOrder.TATE_CONSTRUCTION);
            oms.setMemo(getString("memo"));
            oms.setCustomerName(getString("customerName"));
            oms.setCustomerPhone(getString("customerPhone"));
            oms.setScore(score);
            oms.setCreateName(user.getEmployeeName());
            oms.setCreateNo(String.valueOf(user.getRowNo()));
            oms.setVersionNumber(3);
            oms.setCompanyName(userMap.get(0).get("COMPANY_NAME"));
            oms.setCountyName(userMap.get(0).get("COUNTY_NAME"));
            oms.setCountyNo(userMap.get(0).get("COUNTY_NO"));
            oms.setElectronicContract(electronicContract==true ?0:1);
            oms.setFreeTrial(freeTrial==true ?0:1);
            oms.setModifyDate(new Date());
            oms.setWhetherManager(whetherManager==true ?0:1);
            if(freeTrial){
                oms.setOrderPool(0);
            }else if(!whetherManager){
                oms.setOrderPool(0);
            }else{
                oms.setOrderPool(1);
            }
            OmsPretreatmentDate PretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"1");
            OmsOrderLink link = new OmsOrderLink();
            link.setCreator_name(user.getEmployeeName());//发起人
            link.setCreator_no(user.getRowNo());//发起人工号
            link.setCreator_date(new Date());//发起人时间(当前时间)
            link.setOper_name(user.getEmployeeName());//操作人
            link.setOper_no(user.getRowNo());//操作人工号
            link.setOper_date(new Date());//操作时间(当前时间)
            link.setStatus(1);//状态(状态根据环节确定)
            link.setLinkCode("1");//环节编码或者固定的环节编码
            link.setLinkName("需求发起");//环节名称
            link.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
            link.setLinkOrderNo(IBM + taskService.getNumber());
            link.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(PretreatmentDate.getPretreatment_date())));
            omsSellOrderService.saveOrupdateOmsOrderLink(link);

            /*if (!"".equals(subProductJson) && subProductJson != null && subProductJson.length() > 0) {
                JSONArray subArry = JSONArray.fromObject(subProductJson);
                for (int i = 0; i < subArry.size(); i++) {
                    JSONObject obj = JSONObject.fromObject(subArry.get(i));
                    SubPriceKeyInformation sub = customClauseContractService.getSubPriceKeyInformationById(obj.getString("PRC_CODE"));
                    if(sub!=null){
                        obj.put("PRC_NAME",sub.getSubPriceName());
                        SubOmsProductTariff tariff = new SubOmsProductTariff();
                        tariff.setSubPriceCode(sub.getSubPriceCode());
                        tariff.setSubPriceName(sub.getSubPriceName());
                        tariff.setSubLabelId(sub.getSubLabelId());
                        tariff.setSubProductCode(obj.getString("PROD_CODE"));
                        tariff.setSubOrderContent(obj.toString());
                        tariff.setOrderNo(oms.getOrderNo());
                        tariff.setPriceCode(pmsinfo.getPrcId());
                        tariff.setLabelId(pmslabel.getLabelId());
                        tariff.setProductCode(pmsproduct.getProdId());
                        tariff.setCreationTime(new Date());
                        tariff.setState(0);
                        omsSellOrderService.saveOrupdateSubOmsProductTariff(tariff);
                    }
                }
            }*/

            if(electronicContract){//判断是否是电子合同
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name(user.getEmployeeName());//操作人
                nextStepLink.setOper_no(user.getRowNo());//操作人工号
                nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                nextStepLink.setStatus(0);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("2");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("需求审批");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(PretreatmentDate.getPretreatment_date())));
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            }else{
                if(freeTrial){
                    OmsOrderLink nextStepLink = new OmsOrderLink();
                    nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                    nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                    nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                    nextStepLink.setOper_name(user.getEmployeeName());//操作人
                    nextStepLink.setOper_no(user.getRowNo());//操作人工号
                    nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                    nextStepLink.setStatus(0);//状态(状态根据环节确定)
                    nextStepLink.setLinkCode("3");//环节编码或者固定的环节编码
                    nextStepLink.setLinkName("协议签订");//环节名称
                    nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                    nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                    nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(PretreatmentDate.getPretreatment_date())));
                    omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                    oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                }else{
                    OmsOrderLink nextStepLink = new OmsOrderLink();
                    nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                    nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                    nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                    nextStepLink.setOper_name(user.getEmployeeName());//操作人
                    nextStepLink.setOper_no(user.getRowNo());//操作人工号
                    nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                    nextStepLink.setStatus(0);//状态(状态根据环节确定)
                    nextStepLink.setLinkCode("2");//环节编码或者固定的环节编码
                    nextStepLink.setLinkName("需求审批");//环节名称
                    nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                    nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                    nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(PretreatmentDate.getPretreatment_date())));
                    omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                    oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                }
            }
            OmsSellOrder omsr=omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            if(omsr==null){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","需求单创建失败！写入参数失败，请联系管理员");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }else{
                if (!StringUtils.isEmpty(attachmentId)) {
                    //判断是否上传了附件,获取前台提交的附件Id；
                    String[] fileId = attachmentId.split(",");
                    if (fileId.length > 0) {
                        for (String s : fileId) {
                            if(s.length()>0){
                                SingleAndAttachment sa = new SingleAndAttachment();
                                sa.setOrderID(omsr.getId());
                                sa.setAttachmentId(s);
                                sa.setLink(OmsSellOrder.OMSSELLORDER);
                                omsSellOrderService.saveSandA(sa);
                            }
                        }
                    }
                }
                mapJson.put("code",1);
                mapJson.put("data",omsr.getId());
                mapJson.put("msg","需求单创建成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("需求保存异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 11:16
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询需求数据生成工作台
     */
    public void queryOmsSellOrder(){
        try {
            String id = getString("id");//需求单ID
            Map<String, Object> map = new HashMap<>();
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            List<OmsOrderProduct> omsProduct = omsSellOrderService.getOmsOrderProductList(oms.getOrderNo());
            List<OmsOrderLink> links = omsSellOrderService.getOmsOrderLinkByOrderNumberList(oms.getOrderNo());
            List<Map<String,String>> linkDiaLoguelist =omsSellOrderService.getOmsLinkDiaLogue(oms.getOrderNo());
            OmsOrderLink link=null;
            if(oms.getLinkOrderNo()!=null){
                link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            }
            if (oms.getCreateNo()!=null&&!"".equals(oms.getCreateNo())){
                map.put("CreateUser",systemUserService.getByUserInfoRowNo(Integer.parseInt(oms.getCreateNo())));
            }else {
                map.put("CreateUser","");
            }
            if (oms.getOperateNo()!=null&&!"".equals(oms.getOperateNo())){
                map.put("OrderUser",systemUserService.getByUserInfoRowNo(Integer.parseInt(oms.getOperateNo())));
            }else {
                map.put("OrderUser","");
            }
            if("2".equals(oms.getVersionNumber())){
                Map<String, Object> linkmap = new HashMap<>();
                linkmap.put("orderId",oms.getOrderNo());
                linkmap.put("boosName",user.getBossUserName());
                OrderEsbService.getInstance().orderProcessQuery(linkmap);
                OrderEsbService.getInstance().orderInfoQuery(linkmap);
            }

            if(link!=null){
                OmsLinkDialogue dig = omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
                if(dig!=null){
                    map.put("OmsLinkDialogue",dig);
                }else{
                    map.put("OmsLinkDialogue","");
                }
            }else{
                map.put("OmsLinkDialogue","");
            }
            if(links!=null){
                map.put("OmsOrderLink", links);
            }else{
                map.put("OmsOrderLink", "");
            }

            if(linkDiaLoguelist!=null&&linkDiaLoguelist.size()>0){
                map.put("linkDiaLoguelist", linkDiaLoguelist);
            }else{
                map.put("linkDiaLoguelist", "");
            }
            ContractInfo contract = omsSellOrderService.queryContractInfo(oms.getContractId());
            if (contract != null) {
                map.put("ContractInfo", contract);
            } else {
                map.put("ContractInfo", "");
            }
            if (oms.getUnitId() != null) {
                GroupCustomer group = omsSellOrderService.getGroupCustomer(oms.getUnitId());
                map.put("GroupCustomer", group);
            } else {
                map.put("GroupCustomer", "");
            }
            List<OmsOrderWorkbench> omsWorkbench = omsSellOrderService.getOmsOrderWorkbenchList(oms.getOrderNo());
            if (omsWorkbench.size() > 0) {
                List<Object> list = new ArrayList();
                for(int i=0;i<omsWorkbench.size();i++){
                    Map<String,Object> omsMap = new HashMap<>();
                    Map<String,String> mapUserName=omsSellOrderService.getApprovalUserList(omsWorkbench.get(i).getId(),"SH");
                    if(mapUserName.get("SPUSER")==null){
                        List<Bpms_riskoff_task> taskList= taskService.getPublicEntityTaskList(omsWorkbench.get(i).getId());
                        omsMap.put("approvalUser",taskList.get(0).getCreator_name());
                    }else{
                        omsMap.put("approvalUser",mapUserName.get("SPUSER"));
                    }
                    omsMap.put("omsOrderWorkbench",omsWorkbench.get(i));
                    list.add(omsMap);
                }
                map.put("omsWorkbenchList", list);
            } else {
                map.put("omsWorkbenchList", "");
            }
            List<FollowUpOrder> followUpOrderList = omsSellOrderService.getFollowUpOrderByOrderNo(oms.getOrderNo());
            if(null != followUpOrderList && !followUpOrderList.isEmpty()){
                map.put("followUpOrderList", followUpOrderList);
            }else{
                map.put("followUpOrderList", "");
            }
            /*List<SubProductKeyInformation> subProduct= customClauseContractService.getSubProductKeyInformationList(omsProduct.get(0).getProdNo()
                    ,omsProduct.get(0).getLabelNo());//查询产品配置的关键信息
            if(subProduct!=null&&subProduct.size()>0){
                map.put("SubProductKeyInformation",subProduct);
            }else{
                map.put("SubProductKeyInformation",new JSONArray());
            }*/
            map.put("OmsSellOrder", oms);
            map.put("OmsOrderProductList", omsProduct);
            map.put("code", "0");
            System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询需求数据生成工作台异常"+e.getMessage(),e);
            Map<String, Object> map = new HashMap<>();
            map.put("OmsLinkDialogue","");
            map.put("OmsOrderLink", "");//环节信息
            map.put("ContractInfo", "");//合同信息
            map.put("GroupCustomer", "");//集团信息
            map.put("omsWorkbenchList", "");//工作台审批信息
            map.put("OmsSellOrder", "");//需求单信息
            map.put("OmsOrderProductList", "");//资费信息
            map.put("code", "1");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }

    }


    public static Date strToDate(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = format.parse(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:14
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批查询详情信息
     */
    public void getOmsSellOrderById(){
        String workbenchId = getString("workbenchId");
        String status = getString("status");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(workbenchId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Map<String,Object> resultMap=omsSellOrderService.getOmsOrderWorkbenchAndBpms_riskoff_taskByOmsSellOrderId(workbenchId);
            if(null == resultMap){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到数据");
            }else{
                String processSign = (String)resultMap.get("PROCESS_SIGN");
                if(null != processSign && !"".equals(processSign) && !"null".equals(processSign)){
                    List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(workbenchId);//任务信息
                    resultMap.put("list",taskList);
                }
                String contractId = (String)resultMap.get("ORER_CONTRACT_ID");
                ContractInfo contractInfo= omsSellOrderService.queryContractInfo(contractId);//合同信息
                String orderNo = (String)resultMap.get("ORDER_NO");
                OmsSellOrder order =omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
                resultMap.put("OmsSellOrder",order);
                if (order.getCreateNo()!=null&&!"".equals(order.getCreateNo())){
                    resultMap.put("CreateUser",systemUserService.getByUserInfoRowNo(Integer.parseInt(order.getCreateNo())));
                }else {
                    resultMap.put("CreateUser","");
                }
                if (order.getOperateNo()!=null&&!"".equals(order.getOperateNo())){
                    resultMap.put("OrderUser",systemUserService.getByUserInfoRowNo(Integer.parseInt(order.getOperateNo())));
                }else {
                    resultMap.put("OrderUser","");
                }
                if (order.getUnitId() != null) {
                    GroupCustomer group = omsSellOrderService.getGroupCustomer(order.getUnitId());
                    resultMap.put("GroupCustomer", group);
                } else {
                    resultMap.put("GroupCustomer", "");
                }
                List<OmsOrderProduct> omsProduct = omsSellOrderService.getOmsOrderProductList(orderNo);//资费信息
                List<FollowUpOrder> followUpOrderList = omsSellOrderService.getFollowUpOrderByOrderNo(orderNo);
                if(null != followUpOrderList && !followUpOrderList.isEmpty()){
                    resultMap.put("followUpOrderList", followUpOrderList);
                }else{
                    resultMap.put("followUpOrderList", "");
                }
                String memoHtmlStr = ClobToString((Clob) resultMap.get("MEMO_HTML"));
                String memoStr = ClobToString((Clob) resultMap.get("MEMO"));

                resultMap.remove("MEMO_HTML");
                resultMap.remove("MEMO");

                resultMap.put("MEMO_HTML",memoHtmlStr);
                resultMap.put("MEMO",memoStr);

                resultMap.put("contractInfo",contractInfo);
                resultMap.put("omsProductList",omsProduct);
                Object objId = resultMap.get("PRC_ID");
                String prcName= "";
                /*if(null != objId && !objId.equals("")){
                    JSONArray products = JSONArray.fromObject(objId);
                    if(products.size() > 0){//判断是否有抄送人或者审核人
                        for (int i = 0; i < products.size(); i++) {//循环增加抄送或审核任务数据
                            Object obj = products.get(i);
                            String proId = obj.toString();
                            PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(proId);
                            PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId());
                            prcName += pmsproduct.getProdName()+"("+pmsinfo.getPrcName()+")，";
                        }
                        prcName = prcName.substring(0, prcName.length() - 1);
                    }
                }*/
                resultMap.put("appendProduct",prcName);
                map.put("code",1);
                map.put("data",resultMap);
                map.put("msg","");
            }

        } catch (Exception e) {
            logger.error("根据ID查询数据异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    public String ClobToString(Clob clob) throws SQLException, IOException {
        String reString = "";
        Reader is = clob.getCharacterStream();
        BufferedReader br = new BufferedReader(is);
        String s = br.readLine();
        StringBuffer sb = new StringBuffer();
        while (s != null) {
            sb.append(s);
            s = br.readLine();
        }
        reString = sb.toString();
        if(br!=null){
            br.close();
        }
        if(is!=null){
            is.close();
        }
        return reString;
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:17
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批通过保存数据 转审他人保存数据
     */
    public void saveBpms_riskoff_task(){
        String workbenchId = getString("workbenchId");
        String taskId = getString("taskId");
        String desc = getString("desc");
        Integer status = getInteger("status");//页面传值为2
        String type = getString("type");
        String waitId = getString("waitId");
        String jsonUser = getString("jsonUser");
        Map<String,Object> map =new HashMap<>();
        try {
            OmsOrderWorkbench work =omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            logger.info("审批单状态："+work.getState());
            //审批单状态
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(work.getOrderNo());
            if(!StringHelper.isBlank(workbenchId) || !StringHelper.isBlank(taskId)){
                throw new Exception("参数错误");
            }
            //修改当前人的任务数据状态
            Bpms_riskoff_task bpms_riskoff_task =taskService.updateBpms_riskoff_task(desc, status, taskId);
            if(null == bpms_riskoff_task){
                throw new Exception("任务错误，请联系管理员核对");
            }
            //联表查询工作台流程表数据
            Map<String,Object> resultMap=omsSellOrderService.getOmsOrderWorkbenchAndBpms_riskoff_taskByOmsSellOrderId(workbenchId);
            if(null == resultMap){
                throw new Exception("未查询到工作台或工单数据或流程数据，请联系管理员核对");
            }
            String processId = (String)resultMap.get("PROCESS_SIGN");
            String title = (String)resultMap.get("TITLE");
            JSONArray jsonArray = JSONArray.fromObject(jsonUser);
            String typeName = "";
            if(type.equals("CS")){
                typeName = "抄送";
            }else{
                typeName = "审核";
            }
            if(jsonArray.size() > 0){//判断是否有审核人
                for (int i = 0; i < jsonArray.size(); i++) {//循环增加审核任务数据
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String id = taskService.setBpms_riskoff_taskSaveAll(processId,"",1,type,"预受理发起"+typeName,userId,user,
                            (Integer.parseInt(bpms_riskoff_task.getBak1())+1)+"",bpms_riskoff_task.getId(),null);
                    String url = "jsp/demandOrderTwo/approvalShow.jsp?workbenchId=" + workbenchId+ "&taskId=" +id ;
                    commitBackLogData(id,"["+oms.getUnitName()+"]"+title,userId,user,url);//给转审人发起代办
                }
            }
            if(type.equals("CS")){//如果是抄送，判断是否还有审核数据
                List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskListByStatus(processId, "1","SH");//查询是否还有审批数据
                if(taskList.size() == 0){//无审核数据修改状态
                    String operateNo = (String)resultMap.get("OPERATE_NO");//工作台发起人ID
                    String id = taskService.setBpms_riskoff_taskSaveAll(processId,"",1,"SH","预受理发起(工单审核完成待办任务)",Integer.valueOf(operateNo),user,
                            (Integer.parseInt(bpms_riskoff_task.getBak1())+1)+"",bpms_riskoff_task.getId(),"2");
                    String url = "jsp/demandOrderTwo/approvalShow.jsp?workbenchId=" + workbenchId+ "&taskId=" +
                            id +"&orderType=0";
                    commitBackLogData(id,"["+oms.getUnitName()+"]"+title,Integer.valueOf(work.getOperateNo()),user,url);//工单审核完成发给发起人代办+"（工单审核完成）"
                    if("0".equals(work.getBak1())){
                        oms.setNoTreaty("0");
                        omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                    }
                }
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                throw new Exception("未查询到待办信息,待办信息为空");
            }
            if(type.equals("CS")){
                map.put("code",1);
                map.put("data","");
                map.put("msg","审批成功");
            }else{
                map.put("code",1);
                map.put("data","");
                map.put("msg","转审成功");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("审批/转审异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常"+e.getMessage());
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批工单被驳回
     */
    public void saveOverrule(){
        String workbenchId = getString("workbenchId");
        String desc = getString("desc");
        Integer status = getInteger("status");//页面传值为0
        String taskId = getString("taskId");
        String waitId = getString("waitId");
        String userId = getString("userId");//驳回人员ID
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(workbenchId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Bpms_riskoff_task bpms_riskoff_task =taskService.updateBpms_riskoff_task(desc, status, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            Bpms_riskoff_process bpms_riskoff_process = taskService.getbpms_riskoff_processBizid(workbenchId);
            OmsOrderWorkbench omsOrderWorkbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            String bak="1";//判断为1的话就是审批的人，为0的时候就是发起人
            if(userId.equals(omsOrderWorkbench.getOperateNo())){
                bak="0";
            }
            String id = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"SH","预受理发起(工单审核驳回待办任务)",Integer.valueOf(userId),user,
                    String.valueOf(Integer.parseInt(bpms_riskoff_task.getBak1())+1),bpms_riskoff_task.getId(),bak);
            String url = "jsp/demandOrderTwo/approvalShow.jsp?workbenchId=" + workbenchId+"&taskId=" +
                    id +"&orderNo="+omsOrderWorkbench.getOrderNo();
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(omsOrderWorkbench.getOrderNo());
            commitBackLogData(id,"["+omsSellOrder.getUnitName()+"]"+omsOrderWorkbench.getTitle(),Integer.valueOf(userId),user,url);//审核驳回发起代办+"（审核驳回）"
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息--修改自己的代办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","驳回成功");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("审批否决异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 回复审批
     */
    public void replyOrder(){
        String workbenchId = getString("workbenchId");
        String desc = getString("desc");
        String taskId = getString("taskId");
        String waitId = getString("waitId");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(workbenchId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Bpms_riskoff_task bpms_riskoff_task =taskService.updateBpms_riskoff_task(desc, 2, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            Bpms_riskoff_process bpms_riskoff_process = taskService.getbpms_riskoff_processBizid(workbenchId);
            OmsOrderWorkbench omsOrderWorkbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            String id = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"SH","预受理发起",Integer.valueOf(bpms_riskoff_task.getCreator_no()),user,
                    String.valueOf(Integer.parseInt(bpms_riskoff_task.getBak1())+1),bpms_riskoff_task.getId(),null);
            String url = "jsp/demandOrderTwo/approvalShow.jsp?workbenchId=" + workbenchId+"&taskId=" +id;
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(omsOrderWorkbench.getOrderNo());
            commitBackLogData(id,"["+omsSellOrder.getUnitName()+"]"+omsOrderWorkbench.getTitle(),Integer.valueOf(bpms_riskoff_task.getCreator_no()),user,url);//审核驳回发起代办+"（审核驳回）"
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息--修改自己的代办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","回复成功");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("审批否决异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","回复异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }


    /**
     * @author: liyang
     * @date: 2021/8/31 14:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批完成客户经理结束和抄送审阅结束
     */
    public void reviewBpms_riskoff_task(){
        Integer status = getInteger("status");
        String taskId = getString("taskId");
        String waitId = getString("waitId");//待办id
        String orderType = getString("orderType");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(taskId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task("已阅", status, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","已审阅");
        } catch (Exception e) {
            logger.error("审阅异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    public void uploadFile() {
        try {
            Map<String,Object> map =new HashMap<>();
            //判断上传文件的名称是否为空
            if (file1 != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                StorageCfg storageCfg = attachmentService.queryStorageCfg();
                String ftpUrl = storageCfg.getFileName() + urlDate;
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                String pixstr = FileUpload.getFilePix(file1FileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, file1, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(file1FileName);
                    attachmentEntity.setVersion(storageCfg.getId());
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    map.put("attachmentId",attachmentId);
                    map.put("code",1);
                    map.put("data","");
                    map.put("msg","");
                    writeText(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));

                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }

        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            e.printStackTrace();
            writeText("0");
        }
    }

    /**
     * 身份证背面照上传
     */
    public void uploadFileBack() {
        try {
            Map<String,Object> map =new HashMap<>();
            //判断上传文件的名称是否为空
            if (fileBack != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                StorageCfg storageCfg = attachmentService.queryStorageCfg();
                String ftpUrl = storageCfg.getFileName() + urlDate;
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                String pixstr = FileUpload.getFilePix(fileBackFileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, fileBack, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(fileBackFileName);
                    attachmentEntity.setVersion(storageCfg.getId());
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    map.put("attachmentId",attachmentId);
                    map.put("code",1);
                    map.put("data","");
                    map.put("msg","");
                    writeText(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }

        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            e.printStackTrace();
            writeText("0");
        }
    }

    /**
     * 营业执照上传
     */
    public void uploadFileLicense() {
        try {
            Map<String,Object> map =new HashMap<>();
            //判断上传文件的名称是否为空
            if (fileLicense != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                StorageCfg storageCfg = attachmentService.queryStorageCfg();
                String ftpUrl = storageCfg.getFileName() + urlDate;
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                String pixstr = FileUpload.getFilePix(fileLicenseFileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, fileLicense, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(fileLicenseFileName);
                    attachmentEntity.setVersion(storageCfg.getId());
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    map.put("attachmentId",attachmentId);
                    map.put("code",1);
                    map.put("data","");
                    map.put("msg","");
                    writeText(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            e.printStackTrace();
            writeText("0");
        }
    }

    /**
     * 识别
     */
    public void idCardFrontIdentify(){
        String attId = getString("attId");
        String ocrType = getString("ocrType");
        String url = "http://171.217.92.191:1191/OcrClient/servlet/OcrServlet";
        Map<String,Object> map =new HashMap<>();
        try {
            Attachment attachment = attachmentService.getAttachmentById(attId);
            StorageCfg storageCfg = attachmentService.queryStorageCfgById(attachment.getVersion());
            Map<String,String> textMap=new HashMap<>();
            textMap.put("ocrType",ocrType);
            Map<String,String> fileMap=new HashMap<>();

            fileMap.put("file1",storageCfg.getFileName()+attachment.getAttachmentUrl());
            String result = formUpload(url, textMap, fileMap);
            map.put("code",1);
            map.put("data",result);
            map.put("msg","");
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }
    public static String formUpload(String urlStr, Map<String, String> textMap,
                                    Map<String, String> fileMap) {
        String res = "";
        HttpURLConnection conn = null;
        // boundary就是request头和上传文件内容的分隔符
        String BOUNDARY = "---------------------------123821742118716";
        try {
            URL url = new URL(urlStr);
            conn = (HttpURLConnection) url.openConnection();
            conn.setConnectTimeout(5000);
            conn.setReadTimeout(30000);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            conn.setUseCaches(false);
            conn.setRequestMethod("POST");
            conn.setRequestProperty("Connection", "Keep-Alive");
            conn.setRequestProperty("User-Agent",
                    "Mozilla/5.0 (Windows; U; Windows NT 6.1; zh-CN; rv:*******)");
            conn.setRequestProperty("Content-Type",
                    "multipart/form-data; boundary=" + BOUNDARY);
            OutputStream out = new DataOutputStream(conn.getOutputStream());
            // text
            if (textMap != null) {
                StringBuffer strBuf = new StringBuffer();
                Iterator iter = textMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    strBuf.append("\r\n").append("--").append(BOUNDARY)
                            .append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\""
                            + inputName + "\"\r\n\r\n");
                    strBuf.append(inputValue);
                }
                out.write(strBuf.toString().getBytes("UTF-8"));
            }
            if (fileMap != null) {
                Iterator iter = fileMap.entrySet().iterator();
                while (iter.hasNext()) {
                    Map.Entry entry = (Map.Entry) iter.next();
                    String inputName = (String) entry.getKey();
                    String inputValue = (String) entry.getValue();
                    if (inputValue == null) {
                        continue;
                    }
                    File file = new File(inputValue);
                    String filename = file.getName();
                    //没有传入文件类型，同时根据文件获取不到类型，默认采用application/octet-stream
                    String contentType = new MimetypesFileTypeMap().getContentType(file);
                    //contentType非空采用filename匹配默认的图片类型
                    if(!"".equals(contentType)){
                        if (filename.endsWith(".png")) {
                            contentType = "image/png";
                        }else if (filename.endsWith(".jpg") || filename.endsWith(".jpeg") || filename.endsWith(".jpe")) {
                            contentType = "image/jpeg";
                        }else if (filename.endsWith(".gif")) {
                            contentType = "image/gif";
                        }else if (filename.endsWith(".ico")) {
                            contentType = "image/image/x-icon";
                        }
                    }
                    if (contentType == null || "".equals(contentType)) {
                        contentType = "application/octet-stream";
                    }
                    StringBuffer strBuf = new StringBuffer();
                    strBuf.append("\r\n").append("--").append(BOUNDARY)
                            .append("\r\n");
                    strBuf.append("Content-Disposition: form-data; name=\""
                            + inputName + "\"; filename=\"" + filename
                            + "\"\r\n");
                    strBuf.append("Content-Type:" + contentType + "\r\n\r\n");
                    out.write(strBuf.toString().getBytes());
                    DataInputStream in = new DataInputStream(
                            new FileInputStream(file));
                    int bytes = 0;
                    byte[] bufferOut = new byte[1024];
                    while ((bytes = in.read(bufferOut)) != -1) {
                        out.write(bufferOut, 0, bytes);
                    }
                    in.close();
                }
            }
            byte[] endData = ("\r\n--" + BOUNDARY + "--\r\n").getBytes();
            out.write(endData);
            out.flush();
            out.close();
            // 读取返回数据
            StringBuffer strBuf = new StringBuffer();
            BufferedReader reader = new BufferedReader(new InputStreamReader(
                    conn.getInputStream(),"UTF-8"));
            String line = null;
            while ((line = reader.readLine()) != null) {
                strBuf.append(line).append("\n");
            }
            res = strBuf.toString();
            reader.close();
            reader = null;
        } catch (Exception e) {
            System.out.println("发送POST请求出错。" + urlStr);
            logger.error(e.getMessage(),e);
            e.printStackTrace();
        } finally {
            if (conn != null) {
                conn.disconnect();
                conn = null;
            }
        }
        return res;
    }



    /**
     * @author: liyang
     * @date: 2021/8/19 17:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 工单确认（第三版）
     */
    public void processingToDo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(order.getLinkOrderNo());
            link.setOper_date(new Date());
            link.setStatus(1);
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(order.getCompanyNo(),"4");
            OmsOrderLink nextStepLink = new OmsOrderLink();
            nextStepLink.setCreator_name(user.getEmployeeName());//发起人
            nextStepLink.setCreator_no(user.getRowNo());//发起人工号
            nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
            nextStepLink.setOper_name(user.getEmployeeName());//操作人
            nextStepLink.setOper_no(user.getRowNo());//操作人工号
            nextStepLink.setOper_date(new Date());//操作时间(当前时间)
            nextStepLink.setStatus(0);//状态(状态根据环节确定)
            nextStepLink.setLinkCode("5");//环节编码或者固定的环节编码
            nextStepLink.setLinkName("业务办理");//环节名称
            nextStepLink.setOrderNumber(order.getOrderNo());//需求单ID或者编码
            nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
            nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            order.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            order.setModifyDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(order);
            omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);

            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(user.getEmployeeName());
            dig.setOper_no(user.getRowNo());
            dig.setOper_date(new Date());
            dig.setStatus(0);//1已处理，0未处理
            dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            dig.setOper_role("ROLE_ODMR");//处理角色客户经理
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
            OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(order, user.getRowNo(), order.getTitle(), "", rdig.getId());

            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            if(omsDigList.size()>0) {
                for(OmsLinkDialogue omsDig:omsDigList) {
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                    WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                    //结束当前待办
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        throw new Exception("查询待办失败，未查询待待办信息");
                    }
                }
            }
            map.put("code",1);
            map.put("data","操作成功");
            map.put("msg","需求确认成功,请至工作台进行操作");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error(e.getMessage(),e);
            map.put("code",-1);
            map.put("data","操作失败！"+e.getMessage());
            map.put("msg","操作失败！"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * 提交待办生成
     * 所有跳转approvalShow.jsp页面都传taskId
     *用taskId的原因是-->APP端获取不到代办ID，传taskId好去查询代办任务
     */
    public void commitBackLogData(String taskId, String title,Integer userid,SystemUser user, String url) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + title);//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl(url);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     *  需求单生成待办给订单经理
     * @param order 需求单
     * @param userid 分配人员ID
     */
    public Integer commitOmsSellOrderData(OmsSellOrder order,Integer userid,String title,String isContract,String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + title);//待办名称
        waitTask.setCreationTime(new Date());//代办生成时间
        waitTask.setUrl("jsp/demandOrderTwo/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        return service.saveWait(waitTask, this.getRequest());
    }

    /**
     * @author: liyang
     * @date: 2021/8/5 17:24
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 工单驳回  第三版
     */
    public void orderManagerRejected(){
        Map<String,Object> map = new HashMap<>();
        try {
            String orderNo = getString("orderNo");
            String waitId = getString("waitId");
            String mome = getString("desc");
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(omsSellOrder.getLinkOrderNo());
            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            if(omsDigList.size()>0) {
                for(OmsLinkDialogue omsDig:omsDigList) {
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                    WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                    //结束当前待办
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        throw new Exception("查询待办失败，未查询待待办信息");
                    }
                }
            }
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(omsSellOrder.getCreateName());
            dig.setOper_no(Integer.parseInt(omsSellOrder.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setMessage(mome);
            dig.setStatus(0);//1已处理，0未处理
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
            dig.setLinkOrderNo(link.getLinkOrderNo());
            OmsLinkDialogue rdig= omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            String url = "jsp/demandOrderTwo/returnDemand.jsp?id="+omsSellOrder.getId();
            commitBackLogData(rdig.getId(),omsSellOrder.getTitle(),Integer.valueOf(omsSellOrder.getCreateNo()),user,url);//订单经理确认驳回
            /*OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            if(omsDig!=null) {
                omsDig.setOper_date(new Date());
                omsDig.setStatus(1);
                omsSellOrderService.updateOmsLinkDialogue(omsDig);
            }
            WaitTask wt = service.queryWaitById();//获取待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                throw new Exception("待办查询失败");
            }*/
            map.put("code",1);
            map.put("data","");
            map.put("msg","驳回成功,请等待客户经理"+omsSellOrder.getCreateName()+"操作驳回工单!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("订单经理驳回异常",e.getMessage());
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常！"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * 获取附件消息
     */
    public void fuJian() {
        try {
            String id = getString("id");//工单ID
            String biaoshi = getString("biaoshi");
            List<Map<String, String>> s = omsSellOrderService.fuJian(id, biaoshi);
            for(int i=0;i<s.size();i++){
                Object object = s.get(i).get("userid");
                if(object==null||object=="null"){
                    s.get(i).put("userName","--");
                }else{
                    SystemUser uploadUser = systemUserService.getUserInfoRowNo(Integer.parseInt(String.valueOf(object)));// 获取下一步处理人信息
                    if(uploadUser!=null){
                        s.get(i).put("userName",uploadUser.getEmployeeName());
                    }else{
                        s.get(i).put("userName","--");
                    }
                }
            }
            Write(JSONHelper.Serialize(s));
        } catch (Exception e) {
            logger.error("工单获取附件出错",e);
            e.printStackTrace();
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/6 16:17
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 及时分配订单经理
     */
    public void randomlyAssignOrderManagers(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");
            String userid = getString("userId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            OmsOrderLink link =omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            Map<String,String> map = new HashMap<>();
            if(userid==null || "".equals(userid) || "undefined".equals(userid)){
                List<Map<String, String>> mapList = queryFuzzyUser("ROLE","ROLE_ODMR","",oms.getIsSupport(),oms.getIsUrgent());
                if(mapList.size() == 0 || mapList.isEmpty()){
                    omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                    link.setOper_date(new Date());
                    link.setStatus(0);
                    omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    mapJson.put("code",1);
                    mapJson.put("data",oms);
                    mapJson.put("msg","需求单已提交到系统");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return;
                }
                int[] arry = new int[mapList.size()];
                for (int i = 0; i < mapList.size(); i++) {
                    Object rowno = mapList.get(i).get("ROWNO");
                    int count = omsSellOrderService.getOmsSellOrderScoreByUserId(Integer.valueOf(rowno.toString()));
                    arry[i] = count;
                    map.put(String.valueOf(Integer.valueOf(rowno.toString())), String.valueOf(count));
                }
                if(arry.length==0){
                    userid = mapList.get(new Random().nextInt(mapList.size())).get("ROWNO")+"";
                }else{
                    int min = arry[0];
                    for (int i = 0; i < arry.length; i++) {
                        if (arry[i] < min) {
                            min = arry[i];
                        }
                    }
                    for (Map.Entry entry : map.entrySet()) {
                        if (String.valueOf(min).equals(entry.getValue())) {
                            userid = String.valueOf(entry.getKey());
                        }
                    }
                }
            }
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
            oms.setOperateName(USER.getEmployeeName());
            oms.setOperateNo(String.valueOf(USER.getRowNo()));
            oms.setOperateDate(new Date());
            oms.setOperateHandleDate(new Date());
            oms.setModifyDate(new Date());
            if(oms.getWhetherManager()==0){
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            }else{
                link.setOper_date(new Date());
                link.setOper_name(USER.getEmployeeName());
                link.setOper_no(USER.getRowNo());
                link.setStatus(1);
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                String IBM = "";
                List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("3","工单确认",oms.getOrderNo());
                if(existence.size()>0){
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return ;
                }
                OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"3");
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name(USER.getEmployeeName());//操作人
                nextStepLink.setOper_no(USER.getRowNo());//操作人工号
                nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                nextStepLink.setStatus(0);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("3");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("工单确认");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                oms.setLinkOrderNo(oms.getLinkOrderNo());
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(USER.getEmployeeName());
                dig.setOper_no(USER.getRowNo());
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig= omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                commitOmsSellOrderData(oms,Integer.parseInt(userid),oms.getTitle(),"",rdig.getId());
            }
            mapJson.put("code",1);
            mapJson.put("data",oms);
            mapJson.put("msg","需求单已分配至订单经理"+USER.getEmployeeName()+" 处");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error("订单经理分配异常"+e.getMessage(),e);
            e.printStackTrace();
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","订单经理分配异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:51
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否有订单经理
     * ALL(查询全省);
     * ROLE(查询角色);
     * CITY(查询地市);
     * COUNTY(查询区县);
     * COLLECTION(收藏)--type
     * 角色CODE--roleName
     * 模糊查询名字--name
     * isAllot：是否是市公司渠道 是：2
     * isUrgent：是否加急，加急是2
     */
    public List<Map<String, String>> queryFuzzyUser(String type,String roleName,String name,String isAllot,String isUrgent){
        List<Map<String, String>> listMap= new ArrayList<>();
        try {
            if ("ALL".equals(type)) {//查询全省
                if (!"".equals(name) && name != null) {
                    //根据页面输入名称模糊查询人员
                    listMap = structureOfPersonnelService.queryVwUserinfo(name);
                }
            } else if ("ROLE".equals(type)) {//查询角色
                if (!"".equals(roleName) && roleName != null) {
                    //根据角色code查询角色下面的人员（区分地市和区县）
                    List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
                    if(userlistMap.size() > 0){
                        if(isAllot.equals("2")){
                            listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName,user,userlistMap,name,"D");
                        }else {
                            /*if (isUrgent.equals("2")) {
                                logger.info("这是市公司+这是区县");
                                listMap = structureOfPersonnelService.queryRoleUserInfo(roleName, user, userlistMap, name, "A");
                            } else {*/
                            if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {
                                logger.info("这是区县" + "是否需要市综调中心值：" + isAllot);
                                    /*if (isAllot.equals("2")) {
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "D");
                                    } else {
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "A");
                                    }*/
                                listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "Q");
                            } else {
                                if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                                    logger.info("这是省公司");
                                    listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "S");
                                } else {
                                    logger.info("这是市公司");
                                    listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "D");
                                }
                            }
                            //}
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return listMap;
    }

    /**
     * 作废工单
     * 可删除
     */
    public void orderCancellation(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");
            String mome = getString("mome");
            String waitId = getString("waitId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            List<OmsOrderWorkbench> omsOrderWorkbenchByOrderNoAndStateList = omsSellOrderService.getOmsOrderWorkbenchList(orderNo);
            for (OmsOrderWorkbench workbench : omsOrderWorkbenchByOrderNoAndStateList) {
                List<Bpms_riskoff_task> publicEntityTaskList = taskService.getPublicEntityTaskList(workbench.getId());
                for (Bpms_riskoff_task bpmsRiskoffTask : publicEntityTaskList) {
                    if(bpmsRiskoffTask.getStatus() == 1){
                        taskService.updateBpms_riskoff_task(mome,-1,bpmsRiskoffTask.getId());
                    }
                    List<WaitTask> wtList = service.queryListWaitByTaskId(bpmsRiskoffTask.getId());//获取待办信息
                    for (WaitTask wt : wtList) {
                        if(!WaitTask.HAS_BEEN_COMPLETED.equals(wt.getState())){
                            service.updateWait(wt,this.getRequest());
                        }
                    }
                }
            }
            oms.setModifyDate(new Date());
            oms.setMemo(mome);
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            Result result= GrpOrderIdAcceptSrv.getInstance().uptRegisterInfo(oms.getGrpOrdId(),oms.getOrderNo(),"D001","R",user.getBossUserName()
                    ,String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", new Date()));
            logger.info("一键甩单注册统一接收参数注册信息变更（作废）:"+result.getData());
            mapJson.put("code",1);
            mapJson.put("data",oms);
            mapJson.put("msg","需求单已被作废");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("工单作废异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","需求单作废异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/12/23 14:37
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 作废审批工单(这里要发送短信到还未审批完成的单子)
     */
    public void approvalRevoked(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String workbenchId = getString("workbenchId");
            String desc = getString("bak1");
            String waitId = getString("waitId");
            String taskId = getString("taskId");
            Bpms_riskoff_process bpms_riskoff_process = taskService.updatebpmsRiskoffProcess(workbenchId, -1);//修改审批流程表
            Bpms_riskoff_task bpms_riskoff_task =taskService.updateBpms_riskoff_task(desc, -1, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return ;
            }
            OmsOrderWorkbench workbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            workbench.setBak1(null);
            workbench.setState(OmsOrderWorkbench.STATE_INVALID);
            omsOrderWorkbenchService.saveOmsOrderWorkbench(workbench);
            List<Bpms_riskoff_task> publicEntityTaskList = taskService.getPublicEntityTaskList(workbenchId);
            if(null != publicEntityTaskList && publicEntityTaskList.size() > 0){
                for (Bpms_riskoff_task riskoffTask : publicEntityTaskList) {
                    if(riskoffTask.getStatus() == 1||riskoffTask.getStatus() == -1){
                        //这里调用短信接口
                        taskService.updateBpms_riskoff_task("审批发起人已作废工单", -1, riskoffTask.getId());
                        WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,riskoffTask.getId());
                        SystemUser systemUser=systemUserService.getUserInfoRowNo(wt.getHandleUserId());
                        //结束当前待办
                        if (wt != null) {
                            service.updateWait(wt, this.getRequest());
                            JSONObject obj = new JSONObject();
                            obj.put("title",workbench.getTitle());//工单标题
                            obj.put("rejectUser",bpms_riskoff_task.getCreator_name());//驳回人员
                            obj.put("creator",workbench.getOperateName());//发起人员
                            smsPushService.sendOmsSellOrder(obj.toString(),systemUser.getMobile(),"20421152");
                        }
                    }
                }
            }

            WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(workbench.getOrderNo());
            oms.setNoTreaty(null);
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            mapJson.put("code",1);
            mapJson.put("data","");
            mapJson.put("msg","审批已被作废");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("审批作废异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","审批作废异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 根据名字状态查询区县地市订单经理
     */
    public void sonSelectByUId() {
        String name = getString("name");
        String isAllot = getString("isAllot");//分配标识 2
        String isUrgent = getString("isUrgent");//加急标识 2
        List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
        List<Map<String, String>> listMap = new ArrayList<>();
        if(userlistMap.size() > 0){
            if(isAllot.equals("2")){//市公司支撑
                listMap = structureOfPersonnelService.queryRoleUserInfoWeb("ROLE_ODMR",user,userlistMap,name,"D");
            }else{//不是市公司支撑
                /*if(isUrgent.equals("2")){//加急查询当前市公司所有
                    logger.info("这是市公司+这是区县");
                    listMap = structureOfPersonnelService.queryRoleUserInfoWeb("ROLE_ODMR",user,userlistMap,name,"A");
                }else{*///不加急
                    if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {
                        logger.info("这是区县");
                            /*if(isAllot.equals("2")){
                                listMap = structureOfPersonnelService.queryRoleUserInfoWebAndSignIn("ROLE_ODMR",user,userlistMap,name,"D");
                            }else{
                                listMap = structureOfPersonnelService.queryRoleUserInfoWebAndSignIn("ROLE_ODMR",user,userlistMap,name,"Q");
                            }*/
                        listMap = structureOfPersonnelService.queryRoleUserInfoWeb("ROLE_ODMR",user,userlistMap,name,"Q");
                    } else {
                        if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                            logger.info("这是省公司");
                            listMap = structureOfPersonnelService.queryRoleUserInfoWeb("ROLE_ODMR",user,userlistMap,name,"S");
                        } else {
                            logger.info("这是市公司");
                            listMap = structureOfPersonnelService.queryRoleUserInfoWeb("ROLE_ODMR",user,userlistMap,name,"D");
                        }
                    }
                //}
            }
        }
        for (int i = 0; i < listMap.size(); i++) {
            Map<String, String> map = listMap.get(i);
            int count = omsSellOrderService.getCountNowByUserIdDay(Integer.valueOf(String.valueOf(map.get("id"))));
            List<OmsOrderManagerSignIn> list= omsSellOrderService.getOmsOrderManagerSignIn(String.valueOf(map.get("id")));
            if(list.size()>0){
                map.put("name", map.get("name") + "[今日在途单量(" + count + ")]-在线");
            }else{
                map.put("name", map.get("name") + "[今日在途单量(" + count + ")]-离线");
            }
        }
        putIcon(listMap);
        writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(listMap));
    }

    public void putIcon(List<Map<String, String>> data) {
        for (Map<String, String> map : data) {
            if ("true".equals(map.get("isParent"))) {
                map.put("icon", "images/zuzhijigou.png");
            } else {
                map.put("icon", "images/renyuan.png");
            }
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 获取资费明细信息 获取模板数据
     */
    public void getCustomTemplate(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String productId = getString("productId");
            String labelid = getString("labelid");
            OmsCustomDetails templateList = omsSellOrderService.getCustomTemplateByProductId(productId,"0");
            if(null != templateList ){
                mapJson.put("OmsCustomDetails",templateList);
            }else{
                mapJson.put("OmsCustomDetails","");
            }
            /*if(productId.length()>0&&labelid.length()>0){
                List<SubProductKeyInformation> subProduct= customClauseContractService.getSubProductKeyInformationList(productId,labelid);//查询产品配置的关键信息
                mapJson.put("SubProductKeyInformation",subProduct);
            }else{
                mapJson.put("SubProductKeyInformation","");
            }*/
            mapJson.put("code",1);
            mapJson.put("data","");
            mapJson.put("msg","查询无数据");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取模板数据异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:33
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 增加模板数据
     */
    public void addTemplate(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String productId = getString("productId");
            String temlates = getString("temlates");
            String title = getString("title");
            OmsCustomDetails templateList = omsSellOrderService.getCustomTemplateByProductId(productId,"0");
            if(null != templateList ){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","该资费已经存在模板");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            OmsCustomDetails temlate = new OmsCustomDetails();
            temlate.setTitle(title);
            temlate.setContent(temlates);
            temlate.setProductId(productId);
            temlate.setCreateDate(new Date());
            temlate.setDeleteState("0");
            temlate.setState("0");
            temlate.setUpdateDate(new Date());
            OmsCustomDetails omsCustomDetails = omsSellOrderService.saveOrupdateOmsCustomDetails(temlate);
            mapJson.put("code",1);
            mapJson.put("data",omsCustomDetails);
            mapJson.put("msg","增加成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("增加模板数据异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","增加异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:34
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否有订单经理签到
     */
    public void orderManagerSignIn(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            List<OmsOrderManagerSignIn> signIn = omsSellOrderService.orderManagerSignIn(user.getRowNo());
            if(null == signIn || signIn.isEmpty()){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","无订单经理签到");
            }else{
                mapJson.put("code",1);
                mapJson.put("data",signIn);
                mapJson.put("msg","有订单经理签到");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询是否有订单经理签到异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:35
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 增加签到数据
     */
    public void addOrderManagerSignIn(){
        Map<String,Object> mapJson = new HashMap<>();
        try {

            OmsOrderManagerSignIn signIn = new OmsOrderManagerSignIn();
            signIn.setRowNo(user.getRowNo()+"");
            signIn.setUserName(user.getEmployeeName());
            signIn.setBeforeSignInDate(new Date());
            signIn.setStatus("0");
            signIn.setIsDelete("0");
            OmsOrderManagerSignIn managerSignIn = omsSellOrderService.saveOrUpdateOrderManagerSignIn(signIn);
            if(null == managerSignIn){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","接单开启失败");
            }else{
                mapJson.put("code",1);
                mapJson.put("data",managerSignIn);
                mapJson.put("msg","接单开启成功");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error("增加签到数据异常"+e.getMessage(),e);
            e.printStackTrace();
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","接单异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:37
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 订单经理签退
     */
    public void deleteOrderManagerSignIn(){
        Map<String,Object> mapJson = new HashMap<>();
        try {

            List<OmsOrderManagerSignIn> signIn = omsSellOrderService.orderManagerSignIn(user.getRowNo());
            if(null == signIn || signIn.isEmpty()){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","未查询到接单数据");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            for (OmsOrderManagerSignIn omsOrderManagerSignIn : signIn) {
                omsOrderManagerSignIn.setAfterSignInDate(new Date());
                omsOrderManagerSignIn.setStatus("1");
                omsSellOrderService.saveOrUpdateOrderManagerSignIn(omsOrderManagerSignIn);
            }

            mapJson.put("code",1);
            mapJson.put("data","");
            mapJson.put("msg","关闭接单成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("订单经理签退异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","关闭接单异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:37
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 统计分析明细列表
     */
    public void getOmsSellOrderListtwo(){
        try{
            String UserRow = getString("UserRow");              //用户工号
            String startTime = getString("startTime");      //开始时间
            String endTime = getString("endTime");          //结束时间
            String CompanyName = getString("CompanyName");          //地市
            List<Map<String,Object>> pagelist = omsSellOrderService.getgetOrderMangerSigninExcel(startTime,endTime,UserRow,user,CompanyName);
            omsSellOrderService.OrderMangerSigninExcel(pagelist);
        }catch (Exception e){
            logger.error("订单经理签到列表异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:38
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 统计分析明细列表
     */
    public void getOmsSellOrderListThree(){
        try {
            // 获取当前用户
            SystemUser user = this.user;
            // 获取用户权限
            //System.out.println(user.getRowNo());
            List list = omsSellOrderService.findByRowNo(user.getRowNo());
            boolean flag = false;
            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            }
            String ORDER_NO = getString("ORDER_NO");        //工单编号
            String TITLE = getString("TITLE");               //工单名称
            String UNIT_ID = getString("UNIT_ID");          //集团编号
            String starTime = getString("starTime");        //开始时间
            String endTime = getString("endTime");          //结束时间
            String state = getString("state");              //工单状态
            String COMPANY_NAME = getString("COMPANY_NAME");//地市
            Integer pageNo = getInteger("pageNo");          // 当前页码数
            Integer pagesize = getInteger("pageSize");      // 每页显示件数
            String prcNo=getString("prcNo");
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            LayuiPage json= omsSellOrderService.getOmsSellOrderStatisticeList(page,user,state,endTime,starTime,UNIT_ID,TITLE,ORDER_NO,COMPANY_NAME,flag,prcNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        }catch (Exception e) {
            logger.error("统计分析明细列表异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("1");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:38
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 统计分析明细列表
     */
    public void getOmsSellOrderListVersionNumber(){
        try {
            // 获取当前用户
            SystemUser user = this.user;
            // 获取用户权限
            //System.out.println(user.getRowNo());
            List list = omsSellOrderService.findByRowNo(user.getRowNo());
            boolean flag = false;
            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            }
            String ORDER_NO = getString("ORDER_NO");        //工单编号
            String TITLE = getString("TITLE");               //工单名称
            String UNIT_ID = getString("UNIT_ID");          //集团编号
            String starTime = getString("starTime");        //开始时间
            String endTime = getString("endTime");          //结束时间
            String state = getString("state");              //工单状态
            String COMPANY_NAME = getString("COMPANY_NAME");//地市
            Integer pageNo = getInteger("pageNo");          // 当前页码数
            Integer pagesize = getInteger("pageSize");      // 每页显示件数
            String prcNo=getString("prcNo");
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            LayuiPage json= omsSellOrderService.getOmsSellOrderListVersionNumber(page,user,state,endTime,starTime,UNIT_ID,TITLE,ORDER_NO,COMPANY_NAME,flag,prcNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        }catch (Exception e) {
            logger.error("统计分析明细列表异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("1");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:38
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询列表-导出Excel
     */
    public void getOmsSellOrderExcel(){
        try {
            // 获取当前用户
            SystemUser user = this.user;
            // 获取用户权限
            //System.out.println(user.getRowNo());
            List list = omsSellOrderService.findByRowNo(user.getRowNo());
            boolean flag = false;
            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            }
            String ORDER_NO = getString("ORDER_NO");        //工单编号
            String TITLE = getString("TITLE");               //工单名称
            String UNIT_ID = getString("UNIT_ID");          //集团编号
            String starTime = getString("starTime");        //开始时间
            String endTime = getString("endTime");          //结束时间
            String state = getString("state");              //工单状态
            String COMPANY_NAME = getString("COMPANY_NAME");//地市
            String type=getString("type");
            if("0".equals(type)){
                List<Map<String,Object>> pagelist = omsSellOrderService.getOmsSellOrderStatisticeExcel(user,state,endTime,starTime,UNIT_ID,TITLE,ORDER_NO,COMPANY_NAME,flag);
                omsSellOrderService.OmsSellOrderStatisticeExcel(pagelist,0);
            }else{
                List<Map<String,Object>> pagelist = omsSellOrderService.getOmsSellOrderStatisticeExcelTwo(user,state,endTime,starTime,UNIT_ID,TITLE,ORDER_NO,COMPANY_NAME,flag);
                omsSellOrderService.OmsSellOrderStatisticeExcel(pagelist,1);
            }

        }catch (Exception e) {
            logger.error("查询列表-导出Excel异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("1");
        }
    }

    /**
     *当前日期加上天数后的日期
     * @param num 为增加的天数
     * @return
     */
    public static String plusDay2(int num){
        Date d = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currdate = format.format(d);
        System.out.println("现在的日期是：" + currdate);
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, num);// num为增加的天数，可以改变的
        d = ca.getTime();
        String enddate = format.format(d);
        System.out.println("增加天数以后的日期：" + enddate);
        return enddate;
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:38
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 订单经理签到列表
     */
    public void getOrderMangerSignin(){
        try{
            String UserRow = getString("UserRow");              //用户工号
            String startTime = getString("startTime");      //开始时间
            String endTime = getString("endTime");          //结束时间
            String CompanyName = getString("CompanyName");          //地市
            Integer pageNo = getInteger("pageNo");          // 当前页码数
            Integer pagesize = getInteger("pageSize");      // 每页显示件数
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            LayuiPage json = omsSellOrderService.getOrderMangerSignin(page,startTime,endTime,UserRow,user,CompanyName);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        }catch (Exception e){
            logger.error("订单经理签到列表异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    public void getOmsOrder(){
        try{
            String id = getString("id");//用户工号
            List<Map<String,Object>> list = omsSellOrderService.getOmsOrder(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(list));
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            e.printStackTrace();
        }
    }

    public void getMangerSigninCompany(){
        Result result = ResultGenerator.genSuccessResult();
        try{
            if (!"00".equals(user.getSystemDept().get(0).getCompanyCode())){
                List<Map<String,Object>> list = omsSellOrderService.getMangerSigninCompany();
                if (list.size()>0){
                    result.setCode(ResultCode.SUCCESS);
                    result.setData(list);
                }else {
                    result.setCode(ResultCode.FAIL);
                    result.setMessage("亲爱的的同事，地市列表查询错误！");
                }
            }
            Write(result.toString());
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            result.setCode(ResultCode.FAIL);
            result.setMessage("亲爱的的同事，地市列表查询错误！");
            Write(result.toString());
        }
    }

    /**
     * 获取分配列表
     */
    public void getAllotOmsSellOrderList(){
        try {
            String ORDER_NO = getString("ORDER_NO");        //工单编号
            String TITLE = getString("TITLE");               //工单名称
            String UNIT_ID = getString("UNIT_ID");          //集团编号
            String starTime = getString("starTime");        //开始时间
            String endTime = getString("endTime");          //结束时间
            String COMPANY_NAME = getString("COMPANY_NAME");//地市
            Integer pageNo = getInteger("pageNo");          // 当前页码数
            Integer pagesize = getInteger("pageSize");      // 每页显示件数
            String type = getString("type");
            String time=getString("time");
            String prcNo=getString("prcNo");
            String stateTime="";
            if(!"".equals(time)&&time!=null&&time.length()>0){
                stateTime=addDateMinut(Integer.valueOf(time));
            }
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            if("0".equals(type)){
                LayuiPage json= omsSellOrderService.getAllotOmsSellOrderList(page,user,stateTime,endTime,starTime,UNIT_ID,TITLE,ORDER_NO,COMPANY_NAME,prcNo);
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
            }else{
                LayuiPage json= omsSellOrderService.getAllotOmsSellOrderTwentyFourList(page,user,stateTime,endTime,starTime,UNIT_ID,TITLE,ORDER_NO,COMPANY_NAME,prcNo);
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
            }
        }catch (Exception e) {
            logger.error("获取分配列表异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("1");
        }
    }

    /**
     * 给时间加上几个小时
     * @param day 当前时间 格式：yyyy-MM-dd HH:mm:ss
     * @param hour 需要加的时间
     * @return
     */
    public static String addDateMinut(int hour){
        Calendar calendar = Calendar.getInstance();//此时打印它获取的是系统当前时间
        //calendar.add(Calendar.DATE, -1);    //得到前一天
        calendar.add(Calendar.HOUR, -hour);    //得到前一天
        //System.out.println(String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", calendar.getTime()));
        return String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", calendar.getTime());

    }

    /**
     * @author: liyang
     * @date: 2021/8/26 10:30
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 地市管理员分配订单经理 (有订单经理的情况下再次分配订单经理)
     */
    public void allotOrderManagers(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");
            String userid = getString("userId");
            String description = getString("description");
            String type = getString("type");//0是未分配订单经理 1是超过24小时
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            oms.setModifyDate(new Date());
            if(oms.getWhetherManager()==0){//是自行审批的情况下
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
                oms.setOperateName(USER.getEmployeeName());
                oms.setOperateNo(String.valueOf(USER.getRowNo()));
                oms.setOperateDate(new Date());
                oms.setOperateHandleDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                mapJson.put("code",1);
                mapJson.put("data",oms);
                mapJson.put("msg","需求单已分配至订单经理"+USER.getEmployeeName()+" 处");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }else{//不是自行审批的情况下
                if("0".equals(type)){//未分配订单经理
                    if(oms.getOperateNo()==null){
                        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
                        AllotOrderManager manager = new AllotOrderManager();
                        manager.setCreateDate(new Date());
                        manager.setCreateName(user.getEmployeeName());
                        manager.setCreateNo(user.getRowNo()+"");
                        manager.setDescription(description);
                        manager.setNewManaher(USER.getEmployeeName());
                        manager.setNewManaherNo(String.valueOf(USER.getRowNo()));
                        manager.setOldManager(oms.getOperateName());
                        manager.setOldManagerNo(oms.getOperateNo());
                        manager.setOrderNo(orderNo);
                        omsSellOrderService.saveOrupdateAllotOrderManager(manager);
                        oms.setOperateName(USER.getEmployeeName());
                        oms.setOperateNo(String.valueOf(USER.getRowNo()));
                        oms.setOperateDate(new Date());
                        oms.setOperateHandleDate(new Date());
                        omsSellOrderService.saveOrupdateOmsSellOrder(oms);

                        OmsOrderLink link =omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                        link.setOper_date(new Date());
                        link.setOper_name(USER.getEmployeeName());
                        link.setOper_no(USER.getRowNo());
                        link.setStatus(1);
                        omsSellOrderService.saveOrupdateOmsOrderLink(link);
                        String IBM = "";
                        List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
                        for (int i = 0; i < sone.size(); i++) {
                            IBM = (String) sone.get(i)[2];
                        }
                        List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("3","工单确认",oms.getOrderNo());
                        if(existence.size()>0){
                            mapJson.put("code",-1);
                            mapJson.put("data","");
                            mapJson.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                            return ;
                        }
                        OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"3");
                        OmsOrderLink nextStepLink = new OmsOrderLink();
                        nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                        nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                        nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                        nextStepLink.setOper_name(USER.getEmployeeName());//操作人
                        nextStepLink.setOper_no(USER.getRowNo());//操作人工号
                        nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                        nextStepLink.setStatus(0);//状态(状态根据环节确定)
                        nextStepLink.setLinkCode("3");//环节编码或者固定的环节编码
                        nextStepLink.setLinkName("工单确认");//环节名称
                        nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                        nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                        nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                        oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                        omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                        oms.setLinkOrderNo(oms.getLinkOrderNo());
                        omsSellOrderService.saveOrupdateOmsSellOrder(oms);

                        OmsLinkDialogue dig = new OmsLinkDialogue();
                        dig.setCreator_name(user.getEmployeeName());
                        dig.setCreator_no(user.getRowNo());
                        dig.setCreator_date(new Date());
                        dig.setOper_name(USER.getEmployeeName());
                        dig.setOper_no(USER.getRowNo());
                        dig.setOper_date(new Date());
                        dig.setStatus(0);//1已处理，0未处理
                        dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                        dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                        dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                        OmsLinkDialogue rdig= omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                        commitOmsSellOrderData(oms,Integer.parseInt(userid),oms.getTitle(),"",rdig.getId());//提交代办
                        mapJson.put("code",1);
                        mapJson.put("data",oms);
                        mapJson.put("msg","需求单已分配至订单经理"+USER.getEmployeeName()+" 处");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    }else{
                        mapJson.put("code",-1);
                        mapJson.put("data",oms);
                        mapJson.put("msg","当前需求单已被分配订单经理");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    }
                }else{//超过24小时未确认
                    OmsOrderLink link =omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                    OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
                    if(oms!=null&&link!=null&&omsDig!=null){
                        if("3".equals(link.getLinkCode())&&link.getStatus()==0&&omsDig.getStatus()==0){//工单确认环节
                            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
                            AllotOrderManager manager = new AllotOrderManager();
                            manager.setCreateDate(new Date());
                            manager.setCreateName(user.getEmployeeName());
                            manager.setCreateNo(user.getRowNo()+"");
                            manager.setDescription(description);
                            manager.setNewManaher(USER.getEmployeeName());
                            manager.setNewManaherNo(String.valueOf(USER.getRowNo()));
                            manager.setOldManager(oms.getOperateName());
                            manager.setOldManagerNo(oms.getOperateNo());
                            manager.setOrderNo(orderNo);
                            omsSellOrderService.saveOrupdateAllotOrderManager(manager);
                            oms.setOperateName(USER.getEmployeeName());
                            oms.setOperateNo(String.valueOf(USER.getRowNo()));
                            oms.setOperateDate(new Date());
                            oms.setOperateHandleDate(new Date());
                            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                            link.setOper_date(new Date());
                            link.setOper_name(USER.getEmployeeName());
                            link.setOper_no(USER.getRowNo());
                            omsSellOrderService.saveOrupdateOmsOrderLink(link);
                            if(omsDig!=null) {
                                omsDig.setOper_date(new Date());
                                omsDig.setOper_name(USER.getEmployeeName());
                                omsDig.setOper_no(USER.getRowNo());
                                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                            }
                            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//根据待办id查询待办信息
                            if (wt != null) {
                                service.updateWait(wt, this.getRequest());
                            } else {
                                mapJson.put("code",-1);
                                mapJson.put("data","");
                                mapJson.put("msg","未查询到待办信息");
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                                return;
                            }
                            commitOmsSellOrderData(oms,Integer.parseInt(userid),oms.getTitle(),"",omsDig.getId());//提交代办
                            mapJson.put("code",1);
                            mapJson.put("data",oms);
                            mapJson.put("msg","需求单已分配至订单经理"+USER.getEmployeeName()+" 处");
                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                            return;
                        }else{
                            mapJson.put("code",-1);
                            mapJson.put("data",oms);
                            mapJson.put("msg","当前工单环节发生改变不能分配,请刷新页面,或者联系系统管理员");
                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                            return;
                        }
                    }else{
                        mapJson.put("code",-1);
                        mapJson.put("data","");
                        if(oms==null){
                            mapJson.put("msg","未查询到工单信息");
                        }else if(link==null){
                            mapJson.put("msg","未查询到环节信息");
                        }else if(omsDig==null){
                            mapJson.put("msg","未查询到子环节信息");
                        }
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                        return;
                    }
                }
            }
        } catch (Exception e) {
            logger.error("订单管理员分配订单经理异常"+e.getMessage(),e);
            e.printStackTrace();
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","订单经理分配异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/26 15:09
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 跟进
     */
    public void addFollowUp(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");
            String description = getString("description");
            String descriptionHtml = getString("descriptionHtml");
            String attachmentId = getString("attachmentId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            FollowUpOrder followUpOrder = new FollowUpOrder();
            followUpOrder.setAttmentIds(attachmentId);
            followUpOrder.setCreateDate(new Date());
            followUpOrder.setCreateName(user.getEmployeeName());
            followUpOrder.setCreateNo(user.getRowNo()+"");
            followUpOrder.setDescription(description);
            followUpOrder.setDescriptionHtml(descriptionHtml);
            followUpOrder.setOrderId(oms.getId());
            followUpOrder.setOrderNo(orderNo);
            /*if(oms.getState().equals(OmsSellOrder.TATE_RETURN)){//判断是否时订单经理驳回
                List<OmsOrderWorkbench> omsOrderWorkbenchByOrderNoAndStateList = omsSellOrderService.getOmsOrderWorkbenchList(orderNo);
                String workbenchId ="";
                for (OmsOrderWorkbench workbench : omsOrderWorkbenchByOrderNoAndStateList) {
                    if(null != workbench.getBak1() && workbench.getBak1().equals("DDBH") && !workbench.getState().equals("-1")){//判断是否时订单经理驳回
                        workbenchId = workbench.getId();
                        List<Bpms_riskoff_task> publicEntityTaskList = taskService.getPublicEntityTaskList(workbench.getId());
                        for (Bpms_riskoff_task bpmsRiskoffTask : publicEntityTaskList) {
                            if(null != bpmsRiskoffTask.getMemo() && bpmsRiskoffTask.getMemo().equals("预受理订单经理驳回发起流程")){//修改任务表数据
                                taskService.updateBpms_riskoff_task("添加跟进数据时修改流程",-1,bpmsRiskoffTask.getId());
                                List<WaitTask> waitTaskList = service.queryListWaitByTaskId(bpmsRiskoffTask.getId());//获取待办信息
                                for (WaitTask waitTask : waitTaskList) {
                                    if(!WaitTask.HAS_BEEN_COMPLETED.equals(waitTask.getState())){
                                        service.updateWait(waitTask, this.getRequest());
                                    }
                                }
                            }
                        }
                        taskService.updatebpmsRiskoffProcess(workbench.getId(),-1);//修改流程表数据
                        omsOrderWorkbenchService.updateOmsOrderWorkbench(workbench.getId(),-1);//修改工作台数据
                        commitOmsSellOrderData(oms,Integer.valueOf(oms.getOperateNo()),oms.getTitle(),"");
                    }
                }
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                followUpOrder.setType("1");
                followUpOrder.setWorkbenchId(workbenchId);
            }else{*/
            followUpOrder.setType("0");
            //}
            omsSellOrderService.saveFollowUpOrder(followUpOrder);
            if (!StringUtils.isEmpty(attachmentId)) {
                //判断是否上传了附件,获取前台提交的附件Id；
                String[] fileId = attachmentId.split(",");
                if (fileId.length > 0) {
                    for (String s : fileId) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(oms.getId());
                        sa.setAttachmentId(s);
                        sa.setLink(OmsSellOrder.OMSSELLORDER);
                        omsSellOrderService.saveSandA(sa);
                    }
                }
            }
            mapJson.put("code",1);
            mapJson.put("data","");
            mapJson.put("msg","跟进成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error("增加跟进内容异常"+e.getMessage(),e);
            e.printStackTrace();
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","跟进异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * gcy
     * 图片转pdf
     */
    public void addpDf(){
        //前端接受附件id集合
        String attachmentId = getString("attachmentId");
        //判断是否上传了附件,获取前台提交的附件Id；
        String[] fileId = attachmentId.split(",");
        //创建个存放图片地址的集合
        List<String> imageUrlList = new ArrayList();
        //根据附件id循环获取
        String path="";
        for (int i = 0; i < fileId.length; i++){
            Attachment attachment = omsSellOrderService.getAttachment(fileId[i]);//根据附件id查询路径
            StorageCfg storageCfg = attachmentService.queryStorageCfgById(attachment.getVersion());//查询实时路径
            path = storageCfg.getFileName() + attachment.getAttachmentUrl();//拼接路径
            imageUrlList.add(path); //添加图片地址到集合
        }
        try {
            //存放pdf文件的路径
            //获取毫秒数
            Long time = System.currentTimeMillis();
            //根据当天日期生成文件夹：名称：
            String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
            String ftpUrl="";
            String url="";
            StorageCfg storageCfg= attachmentService.queryStorageCfg();
            ftpUrl=storageCfg.getFileName()+urlDate+time+".pdf";//pdf文件路径
            url=storageCfg.getFileName()+urlDate;//pdf文件夹路径
            File headPath = new File(url);//获取文件夹路径
            if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                headPath.mkdirs();
            }
            String pdfUrl = ftpUrl;//存放路径
            File file = pdf(imageUrlList, pdfUrl);//生成pdf
            file.createNewFile();
            //把pdf文件路径存入合同表
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * gcy
     * 图片转pdf2
     * @param h
     * @param w
     * @return
     */
    public static int getPercent2(float h, float w) {
        int p = 0;
        float p2 = 0.0f;
        p2 = 530 / w * 100;
        p = Math.round(p2);
        return p;
    }

    public static File pdf(List<String> imageUrllist, String pdfUrl) {
        //new一个pdf文档
        Document doc = new Document(PageSize.A4, 20, 20, 20, 20);
        try {
            //pdf写入
            PdfWriter.getInstance(doc, new FileOutputStream(pdfUrl));
            //打开文档
            doc.open();
            //遍历集合，将图片放在pdf文件
            for (int i = 0; i < imageUrllist.size(); i++) {
                //在pdf创建一页：此处为每一张图片是pdf文件的一页
                doc.newPage();
                //通过文件路径获取image
                Image png1 = Image.getInstance(imageUrllist.get(i));
                float heigth = png1.getHeight();
                float width = png1.getWidth();
                int percent = getPercent2(heigth, width);
                png1.setAlignment(Image.MIDDLE);
                // 表示是原来图像的比例;
                png1.scalePercent(percent+3);
                doc.add(png1);
            }
            doc.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        //输出流
        File mOutputPdfFile = new File(pdfUrl);
        if (!mOutputPdfFile.exists()) {
            mOutputPdfFile.deleteOnExit();
            return null;
        }
        //反回文件输出流
        return mOutputPdfFile;
    }

    /**
     * 获取时间配置
     */
    public void getTimeConfig(){
        Map<String,Object> mapJson = new HashMap<>();
        try {

            TimeConfig timeConfig = omsSellOrderService.getTimeConfig("ORDER_SIGN");
            if(null == timeConfig ){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","未查询到签到时间数据");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            mapJson.put("code",1);
            mapJson.put("data",timeConfig);
            mapJson.put("msg","关闭接单成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询签到时间异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:52
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 获取工单中的产品列表
     */
    public void getProductsByOrderNo(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");
            List<Map<String, Object>> list = omsSellOrderService.getOmsOrderProductListByOrderNo(orderNo);
            if(null == list || list.isEmpty()){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","未查询到产品数据");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            Map<String, Object> map = new HashMap<>();
            for (Map<String, Object> product : list) {
                PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(product.get("PROD_NO").toString(),product.get("LABEL_NO").toString());
                product.put("PROD_NAME",pmsproduct.getProdName());
            }
            mapJson.put("code",1);
            mapJson.put("data",list);
            mapJson.put("msg","产品查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","产品查询异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * 跟进工作台ID获取工作台数据
     */
    public void getWorkbenchById(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String workbenchId = getString("workbenchId");
            OmsOrderWorkbench workbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            if(null == workbench){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","未查询到工作台数据");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            mapJson.put("code",1);
            mapJson.put("data",workbench);
            mapJson.put("msg","工作台查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error(e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","工作台查询异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/9 14:55
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 更改需求单
     */
    public void updateOmsSellOrder(){
        Map<String,Object> mapJson=new HashMap<>();
        try{
            String id = getString("id");//需求单ID
            //String title = getString("title");
            String customerName = getString("customerName");//客户联系人
            String customerPhone = getString("customerPhone");//客户电话
            Integer demandType = getInteger("demandType");//需求类型
            String json = getString("json");//资费json
            String attachmentId = getString("attachmentId");//附件id
            String waitId = getString("waitId");
            String contractId = getString("contractId");
            //String subProductJson=getString("subProductJson");
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(order.getLinkOrderNo());
            OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            List<SingleAndAttachment> singleAndAttachmentList = omsSellOrderService.getSingleAndAttachmentList(order.getId());
            if(null != singleAndAttachmentList && singleAndAttachmentList.size()>0 && !singleAndAttachmentList.isEmpty()){
                for (SingleAndAttachment singleAndAttachment : singleAndAttachmentList) {
                    attachmentId += ","+singleAndAttachment.getAttachmentId();
                }
            }
            PmsProdPriceInfo pmsinfo=null;
            PmsProductInfo pmsproduct=null;
            PmsProductLabel pmslabel=null;
            if (!"".equals(json) && json != null && json.length() > 0) {
                JSONArray jsonArray = JSONArray.fromObject(json);
                for (int i = 0; i < jsonArray.size(); i++) {
                    String jsonArrayStr = jsonArray.getString(i);
                    JSONObject obj = JSONObject.fromObject(jsonArrayStr);
                    JSONArray prodArry = JSONArray.fromObject(obj.get("data").toString());
                    Map<String,String> objMap = new HashMap();
                    objMap.put("id",obj.getString("id"));
                    //for(int j=0;j<prodArry.size();j++){//这是正常从左到右删除数据的方法
                    for (int j = prodArry.size() - 1;j >= 0;j--){//这是从右到左删除数据的方法
                        JSONObject prodObj = JSONObject.fromObject(prodArry.get(j).toString());
                        if("折扣折让需求".equals(prodObj.get("templateName"))){
                            objMap.put("discount",prodObj.get("templateValue").toString());
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                        if("客户需求".equals(prodObj.get("templateName"))){
                            objMap.put("description",prodObj.get("templateValue").toString());
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                        if("资费代码".equals(prodObj.get("templateName"))){
                            objMap.put("prodPriceNo",prodObj.get("templateValue").toString());
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                        if("资费名称".equals(prodObj.get("templateName"))){
                            prodArry.remove(j);
                            //j--;//这是正常从左到右删除数据的方法
                        }
                    }
                    OmsOrderProduct omsProduct = omsSellOrderService.getOmsOrderProduct(objMap.get("id"));
                    pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(objMap.get("prodPriceNo"));
                    pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    pmslabel = omsSellOrderService.queryPmsProductLabel(pmsproduct.getLabelId());
                    omsProduct.setPrcName(pmsinfo.getPrcName());
                    omsProduct.setLabelNo(pmslabel.getLabelId());
                    omsProduct.setProdNo(pmsproduct.getProdId());
                    omsProduct.setPrcNo(pmsinfo.getPrcId());
                    omsProduct.setIsCont(pmsproduct.getConType());
                    omsProduct.setScore(pmslabel.getScore());
                    omsProduct.setTemlates(prodArry.toString());
                    omsProduct.setDiscount(objMap.get("discount"));
                    omsProduct.setDescription(objMap.get("description"));
                    omsProduct.setHandleNo(String.valueOf(user.getRowNo()));
                    omsProduct.setHandleName(user.getEmployeeName());
                    omsProduct.setState(OmsOrderProduct.STATE_WAITING);
                    omsProduct.setHandleDate(new Date());
                    omsSellOrderService.saveOrupdateOmsOrderProduct(omsProduct);
                }
            }
            //order.setTitle(title);
            order.setCustomerName(customerName);
            order.setCustomerPhone(customerPhone);
            order.setDemandType(demandType);
            if(!"undefined".equals(contractId)&&contractId!=null&&contractId.length()>0&&!"null".equals(contractId)){
                order.setContractId(contractId);
            }
            if (!StringUtils.isEmpty(attachmentId)) {
                //判断是否上传了附件,获取前台提交的附件Id；
                String[] fileId = attachmentId.split(",");
                if (fileId.length > 0) {
                    for (String s : fileId) {
                        if(!s.equals("")){
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(order.getId());
                            sa.setAttachmentId(s);
                            sa.setLink(OmsSellOrder.OMSSELLORDER);
                            omsSellOrderService.saveSandA(sa);
                        }
                    }
                }
            }
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(order.getOperateName());
            dig.setOper_no(Integer.parseInt(order.getOperateNo()));
            dig.setOper_date(new Date());
            dig.setMessage("");
            dig.setStatus(0);//1已处理，0未处理
            dig.setOper_role("ROLE_ODMR");//处理角色订单经理ROLE_ODMR
            dig.setCreator_role("ROLE_CUMR");//发起角色客户经理ROLE_CUMR
            dig.setLinkOrderNo(link.getLinkOrderNo());
            OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            omsSellOrderService.saveOrupdateOmsSellOrder(order);
            /*Integer count = omsSellOrderService.deleteSubOmsProductTariff(order.getOrderNo());
            if (!"".equals(subProductJson) && subProductJson != null && subProductJson.length() > 0) {
                JSONArray subArry = JSONArray.fromObject(subProductJson);
                for (int i = 0; i < subArry.size(); i++) {
                    JSONObject obj = JSONObject.fromObject(subArry.get(i));
                    SubPriceKeyInformation sub = customClauseContractService.getSubPriceKeyInformationById(obj.getString("PRC_CODE"));
                    if(sub!=null){
                        obj.put("PRC_NAME",sub.getSubPriceName());
                        SubOmsProductTariff tariff = new SubOmsProductTariff();
                        tariff.setSubPriceCode(sub.getSubPriceCode());
                        tariff.setSubPriceName(sub.getSubPriceName());
                        tariff.setSubLabelId(sub.getSubLabelId());
                        tariff.setSubProductCode(obj.getString("PROD_CODE"));
                        tariff.setSubOrderContent(obj.toString());
                        tariff.setOrderNo(order.getOrderNo());
                        tariff.setPriceCode(pmsinfo.getPrcId());
                        tariff.setLabelId(pmslabel.getLabelId());
                        tariff.setProductCode(pmsproduct.getProdId());
                        tariff.setCreationTime(new Date());
                        tariff.setState(0);
                        omsSellOrderService.saveOrupdateSubOmsProductTariff(tariff);
                    }
                }
            }*/
            if(rdig!=null) {
                int type= commitOmsSellOrderData(order, Integer.parseInt(order.getOperateNo()), order.getTitle(), "", rdig.getId());
                WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
                if (wt != null) {
                    if(omsDig!=null) {
                        omsDig.setStatus(1);
                        omsDig.setOper_date(new Date());
                        omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                    }
                    service.updateWait(wt, this.getRequest());
                } else {
                    throw new Exception("未查询到待办信息");
                }
            }
            mapJson.put("code",1);
            mapJson.put("data",order.getOrderNo());
            mapJson.put("msg","需求单更改成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error("需求单更改错误信息："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","需求单更改失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/19 16:51
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否关联合同
     */
    public boolean getProductIsContract(OmsSellOrder order){
        try {
            List<OmsOrderProduct> listProduct = omsSellOrderService.getOmsOrderProductList(order.getOrderNo());
            if(listProduct.isEmpty()){
                return false;
            }else{
                int count=0;
                for(OmsOrderProduct oms : listProduct){
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(oms.getProdNo(),oms.getLabelNo());
                    if(!"0".equals(pmsproduct.getConType())){//如果为0的时候就不关联
                        ++count;
                    }
                }
                if(count>0){
                    return true;
                }else{
                    return false;
                }
            }
        }catch (Exception e){
            logger.error("查询是否订单经理发起审批工单被驳回错误信息："+e.getMessage(),e);
            return false;
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 16:05
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 推送BOSS资费数据
     */
    public void pushBoss(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");
            OmsOrderProduct oms = omsSellOrderService.getOmsOrderProduct(id);
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderByOrderNo(oms.getOrderNo());
            omsOrder.setModifyDate(new Date());
            omsOrder.setOperateHandleDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);
            PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(oms.getPrcNo());
            PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
            PmsProductLabel pmslabel = omsSellOrderService.queryPmsProductLabel(pmsproduct.getLabelId());
            ContractInfo contract=null;
            if(omsOrder.getContractId()!=null){
                contract = omsSellOrderService.getContractInfoId(omsOrder.getContractId());
            }
            if("1".equals(pmsinfo.getIsPushBoss())){
                SystemUser accountOpenUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getCreateNo()));
                Map<String,Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(accountOpenUser.getBossUserName());
                Result resultOne= BusiOppService.getInstance().AccountOpenArrears(accountOpenUser.getBossUserName(),
                        omsOrder.getUnitId(),
                        "6622",
                        mapcfm.get("REGION_ID").toString(),
                        accountOpenUser.getBossUserName(),
                        "6633");
                if(ResultCode.SUCCESS.code()==resultOne.getCode()){
                    JSONObject jsonObject = JSONObject.fromObject(resultOne);
                    JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                    String chk_flag = data.get("CHK_FLAG").toString();
                    String chk_msg = data.get("CHK_MSG").toString();
                    if("Y".equals(chk_flag)){
                        if("1".equals(oms.getGrpOrdId())){
                            String grpOrdId ="";
                            Result result= GrpOrderIdAcceptSrv.getInstance().createGrpOrder(omsOrder.getUnitId(),oms.getSerialNumber(),"D001",
                                    pmsinfo.getPrcName(),
                                    //"ahhgoa",
                                    user.getBossUserName(),
                                    String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", new Date()),pmslabel.getSubLabelType());
                            logger.info("一键甩单注册统一接收参数:"+result.getData());
                            if(ResultCode.SUCCESS.code()==result.getCode()){
                                JSONObject root=JSONObject.fromObject(JSONObject.fromObject(result.getData()).get("ROOT"));
                                JSONObject body=JSONObject.fromObject(root.get("BODY"));
                                if("0".equals(body.get("RETURN_CODE"))){
                                    JSONObject outData=JSONObject.fromObject(JSONObject.fromObject(root.get("BODY")).get("OUT_DATA"));
                                    grpOrdId = outData.get("grpOrdId").toString();
                                    //grpOrdId=taskService.getNumber();
                                    logger.info("一键甩单注册统一ID:"+grpOrdId);
                                    if("".equals(grpOrdId)){
                                        map.put("code",-1);
                                        map.put("data","");
                                        map.put("msg","统一ID注册失败,grpOrdId字段为空");
                                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                        return;
                                    }else{
                                        SystemUser createUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getCreateNo()));
                                        Result returnResult =OmsSellOrderSrv.getInstance().addPreOrderInfoEntity(grpOrdId,omsOrder.getUnitId(),omsOrder.getTitle(),
                                                oms.getSerialNumber(),pmsproduct.getProdId(),pmsinfo.getPrcId(),
                                                pmslabel.getSubLabelType(),contract==null?"":contract.getContractId(),pmsproduct.getProdName(),pmsinfo.getPrcName(),
                                                //"ahhgoa",
                                                user.getBossUserName(),
                                                createUser.getBossUserName(),
                                                oms.getDescription(),
                                                oms.getTemlates(),pmslabel.getCategoryId(),pmslabel.getLabelId());
                                        if(ResultCode.SUCCESS.code()==returnResult.getCode()){
                                            JSONObject returnRoot=JSONObject.fromObject(JSONObject.fromObject(returnResult.getData()).get("ROOT"));
                                            JSONObject returnBody=JSONObject.fromObject(returnRoot.get("BODY"));
                                            if("0".equals(returnBody.getString("RETURN_CODE"))){
                                                oms.setGrpOrdId(grpOrdId);
                                                oms.setIsPushBoss(1);
                                                omsSellOrderService.saveOrupdateOmsOrderProduct(oms);
                                            }else{
                                                map.put("code",-1);
                                                map.put("data","");
                                                map.put("msg","推送BOSS失败："+returnBody.get("RETURN_MSG"));
                                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                                return;
                                            }
                                        }else{
                                            map.put("code",-1);
                                            map.put("data","");
                                            map.put("msg","推送BOSS失败："+returnResult.getMessage());
                                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                            return;
                                        }
                                    }
                                }else{
                                    map.put("code",-1);
                                    map.put("data","");
                                    map.put("msg","统一ID注册失败"+body.get("RETURN_MSG"));
                                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                    return;
                                }
                            }else{
                                map.put("code",-1);
                                map.put("data","");
                                map.put("msg","统一ID注册失败"+result.getMessage());
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                return;
                            }
                        }else{
                            SystemUser createUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getCreateNo()));
                            Result returnResult = OmsSellOrderSrv.getInstance().addPreOrderInfoEntity(oms.getGrpOrdId(),omsOrder.getUnitId(),omsOrder.getTitle(),
                                    oms.getSerialNumber(),pmsproduct.getProdId(),pmsinfo.getPrcId(),
                                    pmslabel.getSubLabelType(),contract==null?"":contract.getContractId(),pmsproduct.getProdName(),pmsinfo.getPrcName(),
                                    //"ahhgoa",
                                    user.getBossUserName(),
                                    createUser.getBossUserName(),
                                    oms.getDescription(),
                                    oms.getTemlates(),pmslabel.getCategoryId(),pmslabel.getLabelId());
                            if(ResultCode.SUCCESS.code()==returnResult.getCode()){
                                JSONObject returnRoot=JSONObject.fromObject(JSONObject.fromObject(returnResult.getData()).get("ROOT"));
                                JSONObject returnBody=JSONObject.fromObject(returnRoot.get("BODY"));
                                if("0".equals(returnBody.getString("RETURN_CODE"))){
                                    oms.setIsPushBoss(1);
                                    omsSellOrderService.saveOrupdateOmsOrderProduct(oms);
                                }else{
                                    map.put("code",-1);
                                    map.put("data","");
                                    map.put("msg","推送BOSS失败："+returnBody.get("RETURN_MSG"));
                                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                    return;
                                }
                            }else{
                                map.put("code",-1);
                                map.put("data","");
                                map.put("msg","推送BOSS失败："+returnResult.getMessage());
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                return;
                            }
                        }
                    }else{
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","当前集团已欠费，请联系客户经理处理欠费以后再推送："+chk_msg);
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return;
                    }
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg",resultOne.getMessage());
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
                /*oms.setIsPushBoss(1);
                omsSellOrderService.saveOrupdateOmsOrderProduct(oms);*/
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","推送BOSS成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("推送资费BOSS信息异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","推送BOSS失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }


    /**
     * @author: liyang
     * @date: 2021/8/30 14:50
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 订单经理关闭工单
     */
    public void closeWorkOrder(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String mome = getString("desc");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            oms.setState("-1");
            oms.setModifyDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("9","工单关闭",oms.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }

            List<OmsLinkDialogue> odgList = omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            for(OmsLinkDialogue odg: odgList){
                odg.setStatus(1);
                omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,odg.getId());//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }

            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"9");
            OmsOrderLink nextStepLink = new OmsOrderLink();
            nextStepLink.setCreator_name(user.getEmployeeName());//发起人
            nextStepLink.setCreator_no(user.getRowNo());//发起人工号
            nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
            nextStepLink.setOper_name(user.getEmployeeName());//操作人
            nextStepLink.setOper_no(user.getRowNo());//操作人工号
            nextStepLink.setOper_date(new Date());//操作时间(当前时间)
            nextStepLink.setStatus(0);//状态(状态根据环节确定)
            nextStepLink.setLinkCode("7");//环节编码或者固定的环节编码
            nextStepLink.setLinkName("工单关闭");//环节名称
            nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
            nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
            nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);

            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(oms.getCreateName());
            dig.setOper_no(Integer.parseInt(oms.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setStatus(0);//1已处理，0未处理
            dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            dig.setMessage(mome);
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理ROLE_ODMR
            OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(oms,Integer.parseInt(oms.getCreateNo()),oms.getTitle(),"",rdig.getId());//提交代办
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("订单经理关闭工单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 15:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 客户经理作废需求单 (工单确认环节)（草稿）
     */
    public void orderToVoid(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String mome = getString("desc");
            String waitId = getString("waitId");
            String taskId = getString("taskId");
            String workbenchId = getString("workbenchId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("7","工单关闭",oms.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"9");
            OmsOrderLink nextStepLink = new OmsOrderLink();
            nextStepLink.setCreator_name(user.getEmployeeName());//发起人
            nextStepLink.setCreator_no(user.getRowNo());//发起人工号
            nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
            nextStepLink.setOper_name(user.getEmployeeName());//操作人
            nextStepLink.setOper_no(user.getRowNo());//操作人工号
            nextStepLink.setOper_date(new Date());//操作时间(当前时间)
            nextStepLink.setStatus(1);//状态(状态根据环节确定)
            nextStepLink.setLinkCode("7");//环节编码或者固定的环节编码
            nextStepLink.setLinkName("工单关闭");//环节名称
            nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
            nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
            nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            oms.setState("-1");
            oms.setModifyDate(new Date());
            oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(user.getEmployeeName());
            dig.setOper_no(user.getRowNo());
            dig.setOper_date(new Date());
            dig.setStatus(1);//1已处理，0未处理
            dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            dig.setMessage(mome);
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            dig.setCreator_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            if(!"undefined".equals(workbenchId)&&workbenchId!=null&&workbenchId.length()>0&&!"null".equals(workbenchId)&&
                    !"undefined".equals(taskId)&&taskId!=null&&taskId.length()>0&&!"null".equals(taskId)){
                Bpms_riskoff_process process = taskService.updatebpmsRiskoffProcess(workbenchId, -1);//修改审批流程表
                Bpms_riskoff_task bpms_riskoff_task =taskService.updateBpms_riskoff_task("作废", -1, taskId);//修改本条数据
                if(null == bpms_riskoff_task){
                    throw new Exception("任务错误，请联系管理员核对");
                }
                OmsOrderWorkbench invalidWorkbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
                invalidWorkbench.setBak1(null);
                invalidWorkbench.setState(OmsOrderWorkbench.STATE_INVALID);
                omsOrderWorkbenchService.saveOmsOrderWorkbench(invalidWorkbench);
                List<Bpms_riskoff_task> publicEntityTaskList = taskService.getPublicEntityTaskList(process.getBiz_id());
                if(null != publicEntityTaskList && publicEntityTaskList.size() > 0){
                    for (Bpms_riskoff_task riskoffTask : publicEntityTaskList) {
                        if(riskoffTask.getStatus() == 1||riskoffTask.getStatus() == -1){
                            //这里调用短信接口
                            taskService.updateBpms_riskoff_task("审批发起人已作废需求单", -1, riskoffTask.getId());
                            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,riskoffTask.getId());
                            SystemUser systemUser=systemUserService.getUserInfoRowNo(wt.getHandleUserId());
                            //结束当前待办
                            if (wt != null) {
                                service.updateWait(wt, this.getRequest());
                                JSONObject obj = new JSONObject();
                                obj.put("title",invalidWorkbench.getTitle());//工单标题
                                obj.put("rejectUser",bpms_riskoff_task.getCreator_name());//驳回人员
                                obj.put("creator",invalidWorkbench.getOperateName());//发起人员
                                smsPushService.sendOmsSellOrder(obj.toString(),systemUser.getMobile(),"20421152");
                            }else{
                                throw new Exception("未查询到待办信息");
                            }
                        }
                    }
                }
                oms.setNoTreaty(null);
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            }else{
                List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
                if(omsDigList.size()>0) {
                    for(OmsLinkDialogue omsDig:omsDigList) {
                        if(omsDig.getStatus()==0){
                            omsDig.setStatus(1);
                            omsDig.setOper_date(new Date());
                            omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                            //结束当前待办
                            if (wt != null) {
                                service.updateWait(wt, this.getRequest());
                            } else {
                                throw new Exception("查询待办失败，为null");
                            }
                        }
                    }
                }
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("客户经理作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 15:56
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 客户经理阅读作废需求单(订单经理工单关闭)
     */
    public void readOmsOrderSell(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            String workbenchId = getString("workbenchId");
            OmsSellOrder oms=null;
            if(id==null||"".equals(id)){
                OmsOrderWorkbench work=omsSellOrderService.getOmsOrderWorkbench(workbenchId);
                oms = omsSellOrderService.getOmsSellOrderByOrderNo(work.getOrderNo());
            }else{
                oms = omsSellOrderService.getOmsSellOrderById(id);
            }
            oms.setState("-1");
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            if(oms.getVersionNumber()==2){
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                link.setStatus(1);
                link.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
                if(omsDig!=null) {
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                }
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("客户经理阅读作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/3 17:16
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询配置的产品审批名称集合
     */
    public void getOmsProductApproveList(){
        Map<String,Object> map =new HashMap<>();
        try {
            String prcId = getString("prcId");
            String ste="";
            if(prcId.indexOf(",")!=-1){//包含
                String[] prArray = prcId.split(",");
                for(int i=0;i<prArray.length;i++){
                    PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(prArray[i]);
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    ste+=pmsproduct.getProdId()+",";
                }
            }else{//不包含
                if(prcId.length()>1){
                    PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(prcId);
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    ste=pmsproduct.getProdId();
                }
            }
            String[] str=ste.split(",");
            List<Map<String,Object>> list= omsSellOrderService.getOmsProductApproveList(str);
            map.put("code",1);
            map.put("data",JSONHelper.SerializeWithNeedAnnotationDateFormat(list));
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("客户经理阅读作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/6 14:34
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询环节名称，列表展示
     */
    public void getOrderLink() {
        Map<String, Object> map = new HashMap<>();
        try {
            String linkOrderNo = getString("linkOrderNo");
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(linkOrderNo);
            map.put("code", 1);
            map.put("data", link.getLinkName());
            map.put("msg", "查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("客户经理阅读作废需求单错误信息：" + e.getMessage(), e);
            map.put("code", -1);
            map.put("data", "");
            map.put("msg", "查询失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void fileRead(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            if(omsDig!=null) {
                omsDig.setStatus(1);
                omsDig.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功!工单已完成归档");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("客户经理阅读作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败!工单归档未完成");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/14 16:37
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 根据子环节ID查询代办ID
     */
    public void getOmsSellOrderOperationInfo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String DIALOGUELINKID = getString("DIALOGUELINKID");
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,DIALOGUELINKID);//根据待办id查询待办信息
            if (wt == null) {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询待当前待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }else{
                map.put("code",1);
                map.put("data",wt.getWaitId());
                map.put("msg","查询成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }
        }catch (Exception e){
            logger.error("从列表进入处理待办"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/14 16:38
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 更改合同名称
     */
    public void updateContractInfo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String contractName = getString("contractName");
            ContractInfo contractInfo= omsSellOrderService.queryContractInfo(id);
            contractInfo.setContractName(contractName);
            omsSellOrderService.updateContractInfo(contractInfo);
            map.put("code",1);
            map.put("data","");
            map.put("msg","更改合同名称成功！");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("从列表进入处理待办"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","更改合同名称失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void getOmsLinkDiaLogueIf(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            OmsLinkDialogue oms=omsSellOrderService.getOmsLinkDialogueRole(id);
            map.put("code",1);
            map.put("data",oms);
            map.put("msg","查询环节代办信息成功！");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询环节代办信息失败信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询环节代办信息失败！");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:59
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 添加收藏人
     */
    public void addCollectionUser(){
        String id = getString("collectionUserId");//收藏人ID
        Map<String,Object> map = new HashMap<>();
        try {
            List<Object> list = structureOfPersonnelService.getList();
            for(int j = 0 ; j < list.size() ; j++){
                Object[] jj=(Object[]) list.get(j);
                boolean b = structureOfPersonnelService.getSystemUser_etp(id,Integer.parseInt(String.valueOf(jj[0])),user);
                if(b==false){
                    structureOfPersonnelService.setDept(id,Integer.parseInt(String.valueOf(jj[0])),user.getRowNo());
                }
            }
            map.put("code",1);
            map.put("data","收藏成功");
            map.put("msg","收藏成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("收藏异常："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","收藏失败");
            map.put("msg","收藏失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/11/1 14:39
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 删除收藏人
     */
    public void deleteCollectionUser(){
        String id = getString("collectionUserId");
        Map<String,Object> map = new HashMap<>();
        try {
            List<Object> list = structureOfPersonnelService.getList();
            for(int j = 0 ; j < list.size() ; j++){
                Object[] jj=(Object[]) list.get(j);
                boolean b = structureOfPersonnelService.getSystemUser_etp(id,Integer.parseInt(String.valueOf(jj[0])),user);
                if(b){
                    structureOfPersonnelService.deleteCollectionUser(id,Integer.parseInt(String.valueOf(jj[0])),user.getRowNo());
                }
            }
            map.put("code",1);
            map.put("data","移除成功");
            map.put("msg","移除成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("删除收藏异常："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","移除失败");
            map.put("msg","移除失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/14 16:40
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批中查询驳回人员
     */
    public void getRejectUserList(){
        Map<String,Object> map = new HashMap<>();
        try {
            String taskId = getString("taskId");
            String id = getString("id");
            //查询审批人
            Bpms_riskoff_task task = taskService.getBpms_riskoff_task(taskId);//查询本身任务
            JSONArray arry = new JSONArray();
            if(task.getBak2()==null){
                JSONObject obj = new JSONObject();
                obj.put("userName",task.getOper_name());
                obj.put("userId",task.getOper_no());
                arry.add(obj);
            }else{
                boolean falg =true;
                while (falg){
                    if(task!=null&&user!=null) {
                        if (task.getBak2() == null && task.getOper_no() != user.getRowNo()) {
                            JSONObject obj = new JSONObject();
                            obj.put("userName", task.getOper_name());
                            obj.put("userId", task.getOper_no());
                            arry.add(obj);
                            falg = false;
                        } else {
                            if (!taskId.equals(task.getId()) && task.getOper_no() != user.getRowNo()) {
                                JSONObject obj = new JSONObject();
                                obj.put("userName", task.getOper_name());
                                obj.put("userId", task.getOper_no());
                                task = taskService.getBpms_riskoff_task(task.getBak2());
                                arry.add(obj);
                                falg = true;
                            } else {
                                task = taskService.getBpms_riskoff_task(task.getBak2());
                                falg = true;
                            }
                        }
                    }else{
                        OmsOrderWorkbench oms =omsSellOrderService.getOmsOrderWorkbench(id);
                        JSONObject obj = new JSONObject();
                        obj.put("userName",oms.getOperateName());
                        obj.put("userId",oms.getOperateNo());
                        arry.add(obj);
                        falg=false;
                    }
                }
            }
            map.put("code",1);
            map.put("data",removeRepeatData(arry));
            map.put("msg","查询驳回人员列表成功");
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        } catch (Exception e) {
            logger.error("查询驳回人员列表异常："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询驳回人员列表失败");
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }

    public static JSONArray removeRepeatData(JSONArray array) {
        JSONArray arrayTemp = new JSONArray();
        for(int i = 0;i < array.size();i++){
            if(i==0){
                arrayTemp.add(array.get(i));
            }else{
                int numJ = 0;
                for(int j = 0;j < arrayTemp.size(); j++){
                    JSONObject newJsonObjectI = (JSONObject)array.get(i);
                    JSONObject newJsonObjectJ = (JSONObject)arrayTemp.get(j);
                    String index_idI = newJsonObjectI.get("userId").toString();
                    String valueI = newJsonObjectI.get("userName").toString();
                    String index_idJ = newJsonObjectJ.get("userId").toString();
                    if(index_idI.equals(index_idJ)){
                        arrayTemp.remove(j);
                        JSONObject newObject = new JSONObject();
                        newObject.put("userId", index_idI);
                        newObject.put("userName", valueI);
                        arrayTemp.add(newObject);
                        break;
                    }
                    numJ++;
                }
                if(numJ-1 == arrayTemp.size()-1){
                    arrayTemp.add(array.get(i));
                }
            }
        }
        return arrayTemp;
    }


    /**
     * 集团产品分类服务
     */
    public void qryGroupLabelInfoSvrTwo() {
        Map<String,Object> map =new HashMap<>();
        logger.info("集团产品更新服务开始");
        Date start_time=new Date();
        try {
            List<PmsProductInfo> listPmsProductInfo = new ArrayList<>();
            List<PmsProductLabel> listPmsProductLabel = new ArrayList<>();
            List<PmsProdPriceInfo> listPmsProdPriceInfo = new ArrayList<>();
            Result result = ResultGenerator.genSuccessResult();
            result = GoodsPrcInfoSrv.getInstance().QryAllGoodsInfo();
            if (result.getCode() == 200) {
                JSONObject obj = JSONObject.fromObject(result.getData());
                JSONObject root = JSONObject.fromObject(obj.get("ROOT"));
                if(root.getInt("RETURN_CODE")==0){
                    JSONObject outData = JSONObject.fromObject(root.get("OUT_DATA"));
                    if (outData.containsKey("LABEL_LIST")) {
                        JSONArray labelList =JSONArray.fromObject(outData.get("LABEL_LIST"));
                        if(labelList.size()>0){
                            for(int i=0;i<labelList.size();i++){
                                JSONObject labelObj = JSONObject.fromObject(labelList.get(i).toString());
                                JSONArray subLabelList =JSONArray.fromObject(labelObj.get("SUB_LABEL_LIST"));
                                if(subLabelList.size()>0){
                                    for(int j=0;j<subLabelList.size();j++){
                                        PmsProductLabel pmsLabel = new PmsProductLabel();
                                        JSONObject json = JSONObject.fromObject(subLabelList.get(j).toString());
                                        pmsLabel.setCategoryId(labelObj.get("LABEL_ID").toString());
                                        pmsLabel.setLabelId(json.get("SUB_LABEL_ID").toString());
                                        pmsLabel.setLabelName(json.get("SUB_LABEL_NAME").toString());
                                        pmsLabel.setSubLabelType(json.get("SUB_LABEL_TYPE").toString());//1:长流程业务，2:短流程业务
                                        pmsLabel.setStatus(1);
                                        pmsLabel.setIsDelete("1");
                                        pmsLabel.setType("TB");
                                        pmsLabel.setScore(0);
                                        listPmsProductLabel.add(pmsLabel);
                                        JSONArray subLabelPrc =JSONArray.fromObject(json.get("SUB_LABEL_PRC"));
                                        if(subLabelPrc.size()>0){
                                            String product="";
                                            for(int k=0;k<subLabelPrc.size();k++){
                                                JSONObject subLabelPrcObj = JSONObject.fromObject(subLabelPrc.get(k).toString());
                                                String index=json.get("SUB_LABEL_ID").toString()+subLabelPrcObj.get("GOODS_ID").toString()+"%";
                                                if(product.indexOf(index)==-1){
                                                    product+=json.get("SUB_LABEL_ID").toString()+subLabelPrcObj.get("GOODS_ID").toString()+"%";
                                                    PmsProductInfo ppd = new PmsProductInfo();
                                                    ppd.setProdId(subLabelPrcObj.get("GOODS_ID").toString());
                                                    ppd.setProdName(subLabelPrcObj.get("GOODS_NAME").toString());
                                                    ppd.setLabelId(json.get("SUB_LABEL_ID").toString());
                                                    ppd.setUpdateDate(new Date());
                                                    ppd.setStatus(1);
                                                    ppd.setIsDelete("1");
                                                    ppd.setType("TB");
                                                    listPmsProductInfo.add(ppd);
                                                }
                                                PmsProdPriceInfo pppi = new PmsProdPriceInfo();
                                                pppi.setPrcId(subLabelPrcObj.get("PRC_ID").toString());
                                                pppi.setPrcName(subLabelPrcObj.get("GOODS_PRC_DESC").toString());
                                                pppi.setProdId(subLabelPrcObj.get("GOODS_ID").toString());
                                                pppi.setIsPushBoss(subLabelPrcObj.get("STATE").toString());
                                                pppi.setUpdateDate(new Date());
                                                pppi.setStatus(1);
                                                pppi.setIsDelete("1");
                                                pppi.setType("TB");
                                                pppi.setOpenTree("1");
                                                pppi.setLabelId(json.get("SUB_LABEL_ID").toString());
                                                listPmsProdPriceInfo.add(pppi);
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            //更新分类表数据以及临时表
            if(listPmsProductLabel.size() > 0 && !listPmsProductLabel.isEmpty()){
                try {
                    omsService.deleteTempTable("PMS_PRODUCT_LABEL_TEMP");
                    omsService.insertTempTable("PMS_PRODUCT_LABEL_TEMP","PMS_PRODUCT_LABEL");
                    omsService.deleteTempTable("PMS_PRODUCT_LABEL");
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("删除/查询增加临时表出错",e.getMessage());
                    throw new RuntimeException("删除/查询增加临时表出错,未去同步数据");
                }
                for (PmsProductLabel label : listPmsProductLabel) {
                    omsService.addPmsProductLabel(label);
                }
            }
            //更新产品表数据以及产品临时表
            if(listPmsProductInfo.size() > 0 && !listPmsProductInfo.isEmpty()){
                try {
                    omsService.deleteTempTable("PMS_PRODUCT_INFO_TEMP");
                    omsService.insertTempTable("PMS_PRODUCT_INFO_TEMP","PMS_PRODUCT_INFO");
                    omsService.deleteTempTable("PMS_PRODUCT_INFO");
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("删除/查询增加临时表出错",e.getMessage());
                    throw new RuntimeException("删除/查询增加临时表出错,未去同步数据");
                }
                for (PmsProductInfo info : listPmsProductInfo) {
                    omsService.addPmsProductInfo(info);
                }
            }

            //更新资费表数据以及资费临时表
            if(listPmsProdPriceInfo.size() > 0 && !listPmsProdPriceInfo.isEmpty()){
                try {
                    omsService.deleteTempTable("PMS_PROD_PRICE_INFO_TEMP");
                    omsService.insertTempTable("PMS_PROD_PRICE_INFO_TEMP","PMS_PROD_PRICE_INFO");
                    omsService.deleteTempTable("PMS_PROD_PRICE_INFO");
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("删除/查询增加临时表出错",e.getMessage());
                    throw new RuntimeException("删除/查询增加临时表出错,未去同步数据");
                }
                for (PmsProdPriceInfo proPrice : listPmsProdPriceInfo) {
                    omsService.addPmsProdPriceInfo(proPrice);
                }
            }
            omsService.updatePmsProductLabel();
            omsService.updatePmsProductInfo();
            omsService.updatePmsProdPriceInfo();
            Date end_time = new Date();
            logger.info("集团产品更新数据正常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            e.printStackTrace();
            Date end_time = new Date();
            logger.info("集团产品更新数据异常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
            logger.error("集团产品更新数据异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败!"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void qryIDynamicSqlQryAoSvc(){
        Map<String,Object> map =new HashMap<>();
        logger.info("集团产品二次筛选更新服务开始");
        Date start_time=new Date();
        try {
            Result result = ResultGenerator.genSuccessResult();
            result = GoodsPrcInfoSrv.getInstance().IDynamicSqlQryAoSvc();
            if (result.getCode() == 200) {
                JSONObject obj = JSONObject.fromObject(result.getData());
                JSONObject root = JSONObject.fromObject(obj.get("ROOT"));
                if(root.getInt("RETURN_CODE")==0) {
                    JSONObject outData = JSONObject.fromObject(root.get("OUT_DATA"));
                    JSONArray arry = JSONArray.fromObject(outData.get("LIST"));
                    omsService.deletePmsPriceinfo();
                    for(int i=0;i<arry.size();i++){
                        JSONObject arryObj = JSONObject.fromObject(arry.get(i));
                        PmsProdPriceInfo pms = omsService.queryPmsProdPriceInfo(arryObj.getString("CODE_VALUE"));
                        if(pms!=null){
                            PmsProductInfo pmsProd = omsService.queryPmsProductInfo(pms.getProdId(),pms.getLabelId());
                            PmsProductLabel pmsLabel = omsService.queryPmsProductLabel(pms.getLabelId());
                            PmsPriceinfo priceinfo = new PmsPriceinfo();
                            priceinfo.setPrcId(arryObj.getString("CODE_VALUE"));
                            priceinfo.setPrcName(pms.getPrcName());
                            priceinfo.setProdId(pmsProd.getProdId());
                            priceinfo.setProdName(pmsProd.getProdName());
                            priceinfo.setName(arryObj.getString("CODE_NAME"));
                            priceinfo.setLabelId(pmsLabel.getLabelId());
                            priceinfo.setLabelName(pmsLabel.getLabelName());
                            omsService.addPmsPriceinfo(priceinfo);
                        }
                    }
                }
            }
            Date end_time = new Date();
            logger.info("集团产品二次筛选更新数据正常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            Date end_time = new Date();
            logger.info("集团产品二次筛选更新数据异常结束用时:"+(end_time.getTime() - start_time.getTime())+"毫秒");
            logger.error("集团产品二次筛选更新数据异常"+e.getMessage(),e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败!"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void innitGroup(){
        Map<String,Object> map = new HashMap<>();
        try {
            String groupCode = getString("groupCode");
            GroupCustomer group = omsSellOrderService.getGroupCustomer(groupCode);
            map.put("code",1);
            map.put("data",group);
            map.put("msg","查询集团信息成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("查询集团信息异常："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询集团信息失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/14 19:59
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 根据代办查询当前的任务属于哪个环节
     */
    public void queryOmsOrderLink(){
        Map<String,Object> map =new HashMap<>();
        try {
            String waitId=getString("waitId");
            String diaLogueLinkId=getString("diaLogueLinkId");
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            if (wt != null) {
                OmsLinkDialogue dig=omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                OmsOrderLink link=omsSellOrderService.getOmsOrderLinkByLinkOrderNo(dig.getLinkOrderNo());
                if(dig!=null){
                    map.put("OmsLinkDialogue",dig);
                    map.put("OmsOrderLink",link);
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","查询当前子环节失败");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
            }else{
                if(diaLogueLinkId!=null&&!"".equals(diaLogueLinkId)&&diaLogueLinkId!="undefined"){
                    OmsLinkDialogue dig=omsSellOrderService.getOmsLinkDialogueById(diaLogueLinkId);
                    OmsOrderLink link=omsSellOrderService.getOmsOrderLinkByLinkOrderNo(dig.getLinkOrderNo());
                    if(dig!=null){
                        map.put("OmsLinkDialogue",dig);
                        map.put("OmsOrderLink",link);
                    }else{
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","查询当前子环节失败");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return;
                    }
                }
            }
            map.put("code",1);
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("查询合同对象异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询失败"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void businessUser(){
        Map<String,Object> map =new HashMap<>();
        try {
            String waitId=getString("waitId");
            String userId=getString("userId");
            String userName=getString("userName");
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            if (wt != null) {
                OmsLinkDialogue dig=omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                OmsOrderLink link=omsSellOrderService.getOmsOrderLinkByLinkOrderNo(dig.getLinkOrderNo());
                OmsSellOrder order = omsSellOrderService.getOmsSellOrderByOrderNo(link.getOrderNumber());
                if(dig!=null){
                    dig.setOper_no(Integer.parseInt(userId));
                    dig.setOper_name(userName);
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                    link.setOper_no(Integer.parseInt(userId));
                    link.setOper_name(userName);
                    omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    order.setOperateNo(userId);
                    order.setOperateName(userName);
                    omsSellOrderService.saveOrupdateOmsSellOrder(order);
                    service.updateWait(wt, this.getRequest());
                    commitOmsSellOrderData(order,Integer.parseInt(userId),order.getTitle(),"",dig.getId());//提交代办
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","查询当前子环节失败");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","更改业务办理人员成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("更改业务办理人员失败异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","更改业务办理人员失败"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/11 16:28
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否能归档（第三版改动）
     */
    public void queryWhetherItCanBeArchived(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");
            String waitId=getString("waitId");
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderById(id);
            List<OmsOrderProduct> list = omsSellOrderService.getOmsOrderProductList(omsOrder.getOrderNo());
            List<OmsOrderLink> links = omsSellOrderService.getOmsOrderLinkByOrderNumberList(omsOrder.getOrderNo());
            int type=0;
            if(list.size()>0){
                for(OmsOrderProduct pro:list){
                    if(pro.getGrpOrdId()!=null){
                        if(omsOrder.getDemandType()==0){
                            if("1".equals(pro.getGrpOrdId())){
                                type=1;
                                break;
                            }else{
                                if(pro.getIsPushBoss()==0){
                                    type=1;
                                    break;
                                }
                            }
                        }else{
                            if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                type=1;
                                break;
                            }
                        }
                    }else{
                        if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                            type=1;
                            break;
                        }
                    }
                }
            }
            if(type==0){
                String data="0";
                //这里可能有两种情况
                //1：全部推送和补录以后 推送的数据BOSS没有回调，那么我们就只能结束代办不能归档
                //2：全部推送和补录以后 推送的数据BOSS全部回调，那么我们就可以结束代办和归档
                for(OmsOrderProduct pro:list){
                    if(pro.getGrpOrdId()!=null){
                        if(omsOrder.getDemandType()==0){
                            if(!"1".equals(pro.getGrpOrdId())){
                                if(pro.getIsPushBoss()==1){
                                    if(pro.getBossCallback()==null){
                                        data="1";
                                    }else{
                                        if(pro.getBossCallback()!=0){
                                            data="1";
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if(!"1".equals(data)){
                    if(omsOrder.getContractId()==null){
                        data="1";
                    }
                }
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                    OmsLinkDialogue odg=omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                    OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(odg.getLinkOrderNo());
                    if(odg!=null) {
                        odg.setStatus(1);
                        odg.setOper_date(new Date());
                        omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                        link.setStatus(1);
                        link.setOper_date(new Date());
                        omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    }else{
                        throw  new Exception("未查询到子环节信息,归档失败");
                    }
                } else {
                    throw  new Exception("未查询到待办信息,归档失败");
                }
                map.put("code",1);
                map.put("data",data);
                map.put("msg","操作成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }else{
                map.put("code",1);
                map.put("data","1");
                map.put("msg","操作成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("查询是否能归档信息异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","1");
            map.put("msg","查询是否能归档信息异常"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }


    /**
     * @author: liyang
     * @date: 2022/1/11 16:25
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO  自动归档方法 （第三版改动）
     */
    public void orderComplete() {
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            String waitId=getString("waitId");
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderById(id);
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence = omsSellOrderService.getOmsOrderLinkByCode("6", "需求归档", omsOrder.getOrderNo());
            if (existence != null) {
                if (existence.size() > 0) {
                    throw new Exception("当前环节已完毕,环节异常,请联系系统管理员");
                }
            }
            OmsOrderLink endLink = new OmsOrderLink();
            endLink.setCreator_name(user.getEmployeeName());//发起人
            endLink.setCreator_no(user.getRowNo());//发起人工号
            endLink.setCreator_date(new Date());//发起人时间(当前时间)
            endLink.setOper_name(user.getEmployeeName());//操作人
            endLink.setOper_no(user.getRowNo());//操作人工号
            endLink.setOper_date(new Date());//操作时间(当前时间)
            endLink.setStatus(1);//状态(状态根据环节确定)
            endLink.setLinkCode("6");//环节编码或者固定的环节编码
            endLink.setLinkName("需求归档");//环节名称
            endLink.setOrderNumber(omsOrder.getOrderNo());//需求单ID或者编码
            endLink.setLinkOrderNo(IBM + taskService.getNumber());
            omsSellOrderService.saveOrupdateOmsOrderLink(endLink);
            omsOrder.setState("1");
            omsOrder.setLinkOrderNo(endLink.getLinkOrderNo());
            omsOrder.setCompleteDate(new Date());
            omsOrder.setModifyDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);//结束当前待办
            /*//发待办
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(omsOrder.getCreateName());
            dig.setOper_no(Integer.parseInt(omsOrder.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setStatus(0);//1已处理，0未处理
            dig.setLinkOrderNo(endLink.getLinkOrderNo());
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理ROLE_ODMR
            OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(omsOrder,Integer.parseInt(omsOrder.getCreateNo()),omsOrder.getTitle(),"",rdig.getId());//提交代办*/

            map.put("code", 1);
            map.put("data", "");
            map.put("msg", "需求单已归档");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("自动归档异常" + e.getMessage(), e);
            map.put("code", -1);
            map.put("data", "");
            map.put("msg", "异常" + e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/13 14:36
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否是最后一条推送或者补录
     */
    public void getOmsOrderProductCount(){
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderById(id);
            List<OmsOrderProduct> prcList = omsSellOrderService.getOmsOrderProductList(omsOrder.getOrderNo());
            int type=0;
            for(OmsOrderProduct pro:prcList){
                if(pro.getGrpOrdId()!=null){
                    if(omsOrder.getDemandType()==0){
                        if("1".equals(pro.getGrpOrdId())){//未推送的老需求单
                            ++type;
                        }else{
                            if(pro.getIsPushBoss()==0){//未推送的新需求单
                                ++type;
                            }
                        }
                    }else {
                        if (pro.getBossNo() == null && pro.getBossPhoneNo() == null) {//未补录的
                            ++type;
                        }
                    }
                }else{
                    if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){//未补录的
                        ++type;
                    }
                }
            }
            map.put("code", 1);
            map.put("data", type);
            map.put("msg", "查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("查询是否是最后一条推送或者补录失败信息：" + e.getMessage(), e);
            map.put("code", -1);
            map.put("data", "1");
            map.put("msg", "查询是否是最后一条推送或者补录失败信息：" + e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void approveLinkPick(){
        Map<String,Object> map =new HashMap<>();
        try {
            String userId=getString("userId");
            String id = getString("id");
            String isSupport = getString("isSupport");
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            SystemUser user=systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
            if(order!=null){
                order.setOperateNo(String.valueOf(user.getRowNo()));
                order.setOperateName(user.getEmployeeName());
                order.setIsSupport(isSupport);
                order.setOperateDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(order);
            }else{
                map.put("code",-1);
                map.put("data","");
                map.put("msg","查询当前工单失败");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","提交成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("提交失败"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","提交失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public Integer getUserJurisdiction(SystemUser user){
        Map<String,Object> map =new HashMap<>();
        int type=0;
        try {
            List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
            if(userlistMap.size() > 0){
                if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {
                    logger.info("这是区县");
                    type=1;
                } else {
                    if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                        logger.info("这是省公司");
                        type= 3;
                    } else {
                        logger.info("这是市公司");
                        type= 2;
                    }
                }
            }
        }catch (Exception e){
            logger.error("查询人员权限错误信息："+e.getMessage(),e);
            type= 0;
        }
        return type;
    }

    public void getContractApprovalRole(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            boolean contractApprovalRole = false;
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            if(oms.getContractId()!=null){
                ContractInfo contract = omsSellOrderService.queryContractInfo(oms.getContractId());
                List<OmsOrderWorkbench> omsWorkbench = omsSellOrderService.getOmsOrderWorkbenchList(oms.getOrderNo());
                if (omsWorkbench.size() > 0) {
                    List<Integer> listStr = new ArrayList();
                    for (int j = 0; j < omsWorkbench.size(); j++) {
                        List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(omsWorkbench.get(j).getId());
                        for (int k = 0; k < taskList.size(); k++) {
                            if (taskList.get(k).getStatus() == 2) {
                                listStr.add(taskList.get(k).getOper_no());
                            }
                        }
                    }
                    SystemUser conUser = systemUserService.getUserInfoRowNo(Integer.parseInt(contract.getCreator()));
                    if (oms.getElectronicContract() == 0) {
                        for (int l = 0; l < listStr.size(); l++) {
                            List<Map<String, String>> listRole = systemUserService.getPriorityTwo(String.valueOf(listStr.get(l)));
                            for (int i = 0; i < listRole.size(); i++) {
                                List<OmsOrderProduct> omsOrderProduct = omsSellOrderService.getOmsOrderProductList(oms.getOrderNo());
                                int companyLevel = getUserJurisdiction(conUser);
                                List<Map<String, String>> approvalCondition = customClauseContractService.getApprovalCondition(
                                        omsOrderProduct.get(0).getProdNo(), omsOrderProduct.get(0).getLabelNo(), companyLevel);
                                JSONArray array = JSONArray.fromObject(contract.getApprovalElements());
                                JSONObject jsonObject = JSONObject.fromObject(array.get(0));
                                Iterator it = jsonObject.keys();
                                Map<String, Object> variables = new HashMap<>();
                                while (it.hasNext()) {
                                    String key = (String) it.next();// 获得key
                                    String value = jsonObject.getString(key);//获得value
                                    variables.put(key, value);
                                }
                                boolean bl = false;
                                for (int z = 0; z < approvalCondition.size(); z++) {
                                    bl = ProcessUtils.processJudge(variables, approvalCondition.get(z).get("CONTENT"));
                                    if (bl) {
                                        if (approvalCondition.get(z).get("ROLE").equals(listRole.get(i).get("NAME"))) {
                                            contractApprovalRole = true;
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            map.put("code",1);
            map.put("data",contractApprovalRole);
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询一键甩单电子合同审批情况错误信息"+e.getMessage(),e);
            map.put("code",1);
            map.put("data",false);
            map.put("msg","查询一键甩单电子合同审批情况错误信息"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void getContractInfo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String orderId = getString("orderId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(orderId);
            ContractInfo contract = omsSellOrderService.queryContractInfo(id);
            if(oms!=null&&contract!=null){

            }else{
                map.put("code",1);
                map.put("data",contract);
                map.put("msg","查询成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }
        }catch (Exception e){
            logger.error("查询一键甩单电子合同审批情况错误信息"+e.getMessage(),e);
            map.put("code",1);
            map.put("data",false);
            map.put("msg","查询一键甩单电子合同审批情况错误信息"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    //无协议报建提交办理服务
    public void orderBusinessHanled(){
        Map<String,Object> map =new HashMap<>();
        try {
            String orderId = getString("orderId");
            String waitId = getString("waitId");
            String taskId = getString("taskId");
            String workbenchId = getString("workbenchId");
            String userId = this.getString("userId");//订单经理ID
            String userName = this.getString("userName");//订单经理名称
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(orderId);
            if(!"undefined".equals(taskId)&&taskId!=null&&taskId.length()>0&&!"null".equals(taskId)) {
                Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task("已归档", 2, taskId);//修改本条数据
                if(null == bpms_riskoff_task){
                    throw new Exception("未查询到当前任务信息，请确认当前任务是否存在");
                }
            }
            if(!"undefined".equals(workbenchId)&&workbenchId!=null&&workbenchId.length()>0&&!"null".equals(workbenchId)) {
                omsOrderWorkbenchService.updateOmsOrderWorkbench(workbenchId,1);
                taskService.updatebpmsRiskoffProcess(workbenchId,2);
            }else{
                throw new Exception("未查询到当前审批工单ID信息，请确认当前审批是否正常");
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            }else{
                throw new Exception("未查询到待办信息,待办信息为空");
            }
            if(!"undefined".equals(userId)&&userId!=null&&userId.length()>0&&!"null".equals(userId)){
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                link.setStatus(1);
                link.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                String IBM = "";
                List<Object[]> sone = taskService.getCompayIBM(Integer.parseInt(oms.getCreateNo()));
                for (int j = 0; j < sone.size(); j++) {
                    IBM = (String) sone.get(j)[2];
                }
                OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"4");
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name(userName);//操作人
                nextStepLink.setOper_no(Integer.parseInt(userId));//操作人工号
                nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                nextStepLink.setStatus(0);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("4");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("需求确认");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(userName);
                dig.setOper_no(Integer.parseInt(userId));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                oms.setOperateNo(userId);
                oms.setOperateName(userName);
                oms.setOperateDate(new Date());
                oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                oms.setModifyDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),oms.getTitle(),"1",rdig.getId());
                map.put("code",1);
                map.put("data","");
                map.put("msg","无协议报建提交办理成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }else{
                throw  new Exception("订单经理为空，请确认");
            }
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("无协议报建提交办理服务错误："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","无协议报建提交办理服务错误:"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void getSubOmsProductTariffList(){
        Map<String,Object> map =new HashMap<>();
        try{
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }

            //第一版解决代办
            List<OmsSellOrder> oms=omsSellOrderService.queryOmsSellOrderListOrLink();
            for(OmsSellOrder obj : oms){
                List<OmsOrderProduct> list = omsSellOrderService.getOmsOrderProductList(obj.getOrderNo());
                int type=0;
                if(list.size()>0){
                    for(OmsOrderProduct pro:list){
                        if(pro.getGrpOrdId()!=null){
                            if(obj.getDemandType()==0){
                                if("1".equals(pro.getGrpOrdId())){
                                    type=1;
                                }else{
                                    if(pro.getIsPushBoss()==0){
                                        type=1;
                                    }
                                }
                            }else{
                                if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                    type=1;
                                }
                            }
                        }else{
                            if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                type=1;
                            }
                        }
                    }
                }

                if(type==0){
                    String data="0";
                    for(OmsOrderProduct pro:list){
                        if(pro.getGrpOrdId()!=null){
                            if(obj.getDemandType()==0){
                                if(!"1".equals(pro.getGrpOrdId())){
                                    if(pro.getIsPushBoss()==1){
                                        if(pro.getBossCallback()==null){
                                            data="1";
                                        }else{
                                            if(pro.getBossCallback()!=0){
                                                data="1";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if(!"1".equals(data)){
                        if(obj.getContractId()==null){
                            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(obj.getLinkOrderNo());
                            if(omsDigList.size()>0) {
                                for(OmsLinkDialogue dig :omsDigList){
                                    if(dig.getOper_no().equals(obj.getOperateNo())){
                                        dig.setStatus(1);
                                        dig.setOper_date(new Date());
                                        omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                                        WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,dig.getId());//获取待办信息
                                        //结束当前待办
                                        if (wt != null) {
                                            service.updateWait(wt, this.getRequest());
                                        } else {
                                            throw new Exception("查询待办失败，未查询待待办信息");
                                        }
                                    }
                                }
                            }
                        }else{
                            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(obj.getLinkOrderNo());
                            if(omsDigList.size()>0) {
                                for(OmsLinkDialogue omsDig:omsDigList) {
                                    omsDig.setStatus(1);
                                    omsDig.setOper_date(new Date());
                                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                                    WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                                    //结束当前待办
                                    if (wt != null) {
                                        service.updateWait(wt, this.getRequest());
                                    } else {
                                        throw new Exception("查询待办失败，未查询待待办信息");
                                    }
                                }
                            }
                            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(obj.getLinkOrderNo());
                            link.setStatus(1);
                            link.setOper_date(new Date());
                            omsSellOrderService.saveOrupdateOmsOrderLink(link);
                            OmsOrderLink endLink = new OmsOrderLink();
                            endLink.setCreator_name(user.getEmployeeName());//发起人
                            endLink.setCreator_no(user.getRowNo());//发起人工号
                            endLink.setCreator_date(new Date());//发起人时间(当前时间)
                            endLink.setOper_name(user.getEmployeeName());//操作人
                            endLink.setOper_no(user.getRowNo());//操作人工号
                            endLink.setOper_date(new Date());//操作时间(当前时间)
                            endLink.setStatus(1);//状态(状态根据环节确定)
                            endLink.setLinkCode("6");//环节编码或者固定的环节编码
                            endLink.setLinkName("工单归档");//环节名称
                            endLink.setOrderNumber(obj.getOrderNo());//需求单ID或者编码
                            endLink.setLinkOrderNo(IBM + taskService.getNumber());
                            omsSellOrderService.saveOrupdateOmsOrderLink(endLink);
                            obj.setState("1");
                            obj.setLinkOrderNo(endLink.getLinkOrderNo());
                            obj.setCompleteDate(new Date());
                            omsSellOrderService.saveOrupdateOmsSellOrder(obj);
                        }
                    }else{
                        List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(obj.getLinkOrderNo());
                        if(omsDigList.size()>0) {
                            for(OmsLinkDialogue omsDig:omsDigList) {
                                omsDig.setStatus(1);
                                omsDig.setOper_date(new Date());
                                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                                //结束当前待办
                                if (wt != null) {
                                    service.updateWait(wt, this.getRequest());
                                } else {
                                    throw new Exception("查询待办失败，未查询待待办信息");
                                }
                            }
                        }
                    }
                }
            }

            //第二版解决代办
            List<OmsSellOrder> omsTwo=omsSellOrderService.queryOmsSellOrderListOrLinkTwo();
            for(OmsSellOrder objTwo : oms){
                List<OmsOrderProduct> list = omsSellOrderService.getOmsOrderProductList(objTwo.getOrderNo());
                int type=0;
                if(list.size()>0){
                    for(OmsOrderProduct pro:list){
                        if(pro.getGrpOrdId()!=null){
                            if(objTwo.getDemandType()==0){
                                if("1".equals(pro.getGrpOrdId())){
                                    type=1;
                                }else{
                                    if(pro.getIsPushBoss()==0){
                                        type=1;
                                    }
                                }
                            }else{
                                if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                    type=1;
                                }
                            }
                        }else{
                            if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                type=1;
                            }
                        }
                    }
                }

                if(type==0){
                    String data="0";
                    for(OmsOrderProduct pro:list){
                        if(pro.getGrpOrdId()!=null){
                            if(objTwo.getDemandType()==0){
                                if(!"1".equals(pro.getGrpOrdId())){
                                    if(pro.getIsPushBoss()==1){
                                        if(pro.getBossCallback()==null){
                                            data="1";
                                        }else{
                                            if(pro.getBossCallback()!=0){
                                                data="1";
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                    if(!"1".equals(data)){
                        if(objTwo.getContractId()==null){
                            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(objTwo.getLinkOrderNo());
                            if(omsDigList.size()>0) {
                                for(OmsLinkDialogue dig :omsDigList){
                                    if(dig.getOper_no().equals(objTwo.getOperateNo())){
                                        dig.setStatus(1);
                                        dig.setOper_date(new Date());
                                        omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                                        WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,dig.getId());//获取待办信息
                                        //结束当前待办
                                        if (wt != null) {
                                            service.updateWait(wt, this.getRequest());
                                        } else {
                                            throw new Exception("查询待办失败，未查询待待办信息");
                                        }
                                    }
                                }
                            }
                        }else{
                            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(objTwo.getLinkOrderNo());
                            if(omsDigList.size()>0) {
                                for(OmsLinkDialogue omsDig:omsDigList) {
                                    omsDig.setStatus(1);
                                    omsDig.setOper_date(new Date());
                                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                                    WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                                    //结束当前待办
                                    if (wt != null) {
                                        service.updateWait(wt, this.getRequest());
                                    } else {
                                        throw new Exception("查询待办失败，未查询待待办信息");
                                    }
                                }
                            }
                            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(objTwo.getLinkOrderNo());
                            link.setStatus(1);
                            link.setOper_date(new Date());
                            omsSellOrderService.saveOrupdateOmsOrderLink(link);
                            OmsOrderLink endLink = new OmsOrderLink();
                            endLink.setCreator_name(user.getEmployeeName());//发起人
                            endLink.setCreator_no(user.getRowNo());//发起人工号
                            endLink.setCreator_date(new Date());//发起人时间(当前时间)
                            endLink.setOper_name(user.getEmployeeName());//操作人
                            endLink.setOper_no(user.getRowNo());//操作人工号
                            endLink.setOper_date(new Date());//操作时间(当前时间)
                            endLink.setStatus(0);//状态(状态根据环节确定)
                            endLink.setLinkCode("8");//环节编码或者固定的环节编码
                            endLink.setLinkName("工单归档");//环节名称
                            endLink.setOrderNumber(objTwo.getOrderNo());//需求单ID或者编码
                            endLink.setLinkOrderNo(IBM + taskService.getNumber());
                            omsSellOrderService.saveOrupdateOmsOrderLink(endLink);
                            objTwo.setState("1");
                            objTwo.setLinkOrderNo(endLink.getLinkOrderNo());
                            objTwo.setCompleteDate(new Date());
                            omsSellOrderService.saveOrupdateOmsSellOrder(objTwo);
                        }
                    }else{
                        List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(objTwo.getLinkOrderNo());
                        if(omsDigList.size()>0) {
                            for(OmsLinkDialogue omsDig:omsDigList) {
                                omsDig.setStatus(1);
                                omsDig.setOper_date(new Date());
                                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                                //结束当前待办
                                if (wt != null) {
                                    service.updateWait(wt, this.getRequest());
                                } else {
                                    throw new Exception("查询待办失败，未查询待待办信息");
                                }
                            }
                        }
                    }
                }
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功总计条数：");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询附件资费错误："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败:"+e);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /*public void getSubOmsProductTariffList(){
        Map<String,Object> map =new HashMap<>();
        try{
            String omsProductId = getString("id");
            OmsOrderProduct omsOrderProduct= omsSellOrderService.getOmsOrderProduct(omsProductId);
            List<SubOmsProductTariff> list= omsSellOrderService.getSubOmsProductTariffList(omsOrderProduct.getPrcNo(),omsOrderProduct.getProdNo(),
                    omsOrderProduct.getLabelNo(),omsOrderProduct.getOrderNo());
            map.put("code",1);
            map.put("data","");
            map.put("SubOmsProductTariff",list);
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询附件资费错误："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询附件资费错误:"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }*/

    /**
     * @author: liyang
     * @date: 2022/7/13 11:08
     * @Version: 1.0
     * @return:
     * @Description: TODO
     */
    /*public void getSubOmsProductTariff(){
        Map<String,Object> map =new HashMap<>();
        try{
            String subPriceCode = getString("subPriceCode");
            String subProductCode = getString("subProductCode");
            String subLabelId = getString("subLabelId");
            String orderNo = getString("orderNo");
            SubOmsProductTariff obj= omsSellOrderService.getSubOmsProductTariff(subPriceCode,subProductCode,
                    subLabelId,orderNo);
            if(obj==null){
                map.put("code",-1);
                map.put("data","");
                map.put("SubOmsProductTariff",null);
                map.put("msg","查询为NULL");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }else{
                map.put("code",1);
                map.put("data","");
                map.put("SubOmsProductTariff",obj);
                map.put("msg","查询成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }
        }catch (Exception e){
            logger.error("查询附件资费错误："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","无协议报建提交办理服务错误:"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }*/
}
