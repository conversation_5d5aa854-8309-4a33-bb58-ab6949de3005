package com.xinxinsoft.action.oms;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.oms.OmsOrderProductService;
import com.xinxinsoft.service.oms.OmsOrderWorkbenchService;
import com.xinxinsoft.service.oms.OmsSellOrderService;

/**
 * 销售工单产品受理详细
 */
public class OmsOrderProductAction extends BaseAction {

    private OmsSellOrderService omsSellOrderService;
    private OmsOrderWorkbenchService omsOrderWorkbenchService;
    private OmsOrderProductService omsOrderProductService;
    private AttachmentService attachmentService;

    public OmsSellOrderService getOmsSellOrderService() {
        return omsSellOrderService;
    }

    public void setOmsSellOrderService(OmsSellOrderService omsSellOrderService) {
        this.omsSellOrderService = omsSellOrderService;
    }

    public OmsOrderWorkbenchService getOmsOrderWorkbenchService() {
        return omsOrderWorkbenchService;
    }

    public void setOmsOrderWorkbenchService(OmsOrderWorkbenchService omsOrderWorkbenchService) {
        this.omsOrderWorkbenchService = omsOrderWorkbenchService;
    }

    public OmsOrderProductService getOmsOrderProductService() {
        return omsOrderProductService;
    }

    public void setOmsOrderProductService(OmsOrderProductService omsOrderProductService) {
        this.omsOrderProductService = omsOrderProductService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }
}
