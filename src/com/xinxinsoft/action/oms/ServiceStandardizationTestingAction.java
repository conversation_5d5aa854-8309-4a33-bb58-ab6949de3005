package com.xinxinsoft.action.oms;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.contract.MobileCorporation;
import com.xinxinsoft.entity.contractUniformity.ContractInfo;
import com.xinxinsoft.entity.contractUniformity.ContractRenewal;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.oms.*;
import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.entity.pms.PmsProductInfo;
import com.xinxinsoft.entity.pms.PmsProductLabel;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.oms.OmsOrderProductService;
import com.xinxinsoft.service.oms.OmsOrderWorkbenchService;
import com.xinxinsoft.service.oms.OmsSellOrderService;
import com.xinxinsoft.service.oms.ServiceStandardizationTestingService;
import com.xinxinsoft.utils.StringHelper;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;

public class ServiceStandardizationTestingAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(ServiceStandardizationTestingAction.class);
    private ServiceStandardizationTestingService testingService;
    private OmsSellOrderService omsSellOrderService;
    private OmsOrderWorkbenchService omsOrderWorkbenchService;
    private OmsOrderProductService omsOrderProductService;
    private Bpms_riskoff_service taskService;
    private SystemUserService systemUserService;//系统人员工具
    public ServiceStandardizationTestingService getTestingService() {
        return testingService;
    }

    public void setTestingService(ServiceStandardizationTestingService testingService) {
        this.testingService = testingService;
    }

    public OmsSellOrderService getOmsSellOrderService() {
        return omsSellOrderService;
    }

    public void setOmsSellOrderService(OmsSellOrderService omsSellOrderService) {
        this.omsSellOrderService = omsSellOrderService;
    }

    public OmsOrderWorkbenchService getOmsOrderWorkbenchService() {
        return omsOrderWorkbenchService;
    }

    public void setOmsOrderWorkbenchService(OmsOrderWorkbenchService omsOrderWorkbenchService) {
        this.omsOrderWorkbenchService = omsOrderWorkbenchService;
    }

    public OmsOrderProductService getOmsOrderProductService() {
        return omsOrderProductService;
    }

    public void setOmsOrderProductService(OmsOrderProductService omsOrderProductService) {
        this.omsOrderProductService = omsOrderProductService;
    }

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    /**
     * @author: liyang
     * @date: 2021/9/8 15:42
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 订单生成服务
     */
    public void orderCreate(){
        Map<String,Object> mapJson =new HashMap<>();
        try {
            String content = UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("订单生成服务接口参数异常");
                mapJson.put("bizCode",-1);
                mapJson.put("customOrderId","");
                mapJson.put("msg","接收参数为空");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            String paramsJson = URLDecoder.decode(content.toString(), "UTF-8");
            logger.info("订单生成服务："+paramsJson);
            JSONObject datajson = JSONObject.fromObject(paramsJson);
            JSONObject rootjson = JSONObject.fromObject(datajson.get("ROOT"));
            JSONObject data = JSONObject.fromObject(rootjson.get("BODY"));
            String serviceNum = data.getString("serviceNum");
            mapJson.put("bizCode","0000");
            mapJson.put("customOrderId",serviceNum);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            mapJson.put("bizCode",-1);
            mapJson.put("customOrderId","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/8 14:39
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 订单基本信息查询
     */
    public void orderInfoQuery(){
        Map<String,Object> map =new HashMap<>();
        try {
            String content = UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("订单基本信息查询接口参数异常");
                map.put("orderNumber","");//订单编号
                map.put("orderType","");//订单类型
                map.put("acceptNumber","");//业务受理号码
                map.put("customerCode","");//客户编码
                map.put("customName","");//客户名称
                map.put("acceptChannel","");//受理渠道
                map.put("acceptStaffNO","");//受理操作员
                map.put("acceptTime","");//受理时间
                map.put("dueAmount","");//应收费用
                map.put("offsetAmount","");//减免费用
                map.put("actualAmount","");//实收费用
                map.put("orderChargeCode","");//收费状态
                map.put("orderStatus","");//订单状态
                map.put("businessNumber","");//受理单流水号
                map.put("relatedPayNo","");//关联支付单号
                map.put("relatedContractNo","");//关联合同单号
                map.put("orderItem",new ArrayList<String>());//订单行列表
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            String json = URLDecoder.decode(content.toString(), "UTF-8");
            logger.info("订单基本信息查询接口接收参数："+json);
            JSONObject datajson = JSONObject.fromObject(json);
            JSONObject rootjson = JSONObject.fromObject(datajson.get("ROOT"));
            JSONObject data = JSONObject.fromObject(rootjson.get("BODY"));
            String orderId = data.getString("orderId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderId);
            if (oms.getContractId() != null) {
                ContractInfo contract = omsSellOrderService.queryContractInfo(oms.getContractId());
                map.put("relatedContractNo",contract.getContractId());//关联合同单号
            } else {
                map.put("relatedContractNo","");//关联合同单号
            }
            map.put("orderNumber",oms.getOrderNo());//订单编号
            map.put("orderType","2");//订单类型
            map.put("acceptNumber",oms.getOrderNo());//业务受理号码
            map.put("customerCode",oms.getUnitId());//客户编码
            map.put("customName",oms.getUnitName());//客户名称
            map.put("acceptChannel",oms.getCompanyNo());//受理渠道
            map.put("acceptStaffNO",oms.getOperateName());//受理操作员
            map.put("acceptTime",new SimpleDateFormat("yyyyMMddHHmmss").format(oms.getCreateDate()).toString());//受理时间
            map.put("dueAmount","");//应收费用
            map.put("offsetAmount","");//减免费用
            map.put("actualAmount","");//实收费用
            map.put("orderChargeCode","");//收费状态
            map.put("orderStatus","0");//订单状态
            map.put("businessNumber",oms.getOrderNo());//受理单流水号
            map.put("relatedPayNo","0");//关联支付单号
            map.put("orderItem",new ArrayList<String>());//订单行列表
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            map.put("orderNumber","");//订单编号
            map.put("orderType","");//订单类型
            map.put("acceptNumber","");//业务受理号码
            map.put("customerCode","");//客户编码
            map.put("customName","");//客户名称
            map.put("acceptChannel","");//受理渠道
            map.put("acceptStaffNO","");//受理操作员
            map.put("acceptTime","");//受理时间
            map.put("dueAmount","");//应收费用
            map.put("offsetAmount","");//减免费用
            map.put("actualAmount","");//实收费用
            map.put("orderChargeCode","");//收费状态
            map.put("orderStatus","");//订单状态
            map.put("businessNumber","");//受理单流水号
            map.put("relatedPayNo","");//关联支付单号
            map.put("relatedContractNo","");//关联合同单号
            map.put("orderItem",new ArrayList<String>());//订单行列表
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * 订单流程环节查询服务
     */
    /**
     * @author: liyang
     * @date: 2021/9/8 10:23
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 订单流程环节查询服务
     */
    public void orderProcessQuery(){
        Map<String,Object> result = new HashMap<>();
        try {
            String content = UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("订单流程环节查询服务接口参数异常");
                result.put("orderProcessInfo","");
                result.put("orderId","");
                result.put("orderStatus","");
                result.put("orderOwner","");
                result.put("orderDealInfo","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
                return;
            }
            String json = URLDecoder.decode(content.toString(), "UTF-8");
            logger.info("订单流程环节查询服务："+json);
            JSONObject datajson = JSONObject.fromObject(json);
            JSONObject rootjson = JSONObject.fromObject(datajson.get("ROOT"));
            JSONObject data = JSONObject.fromObject(rootjson.get("BODY"));
            String orderId = data.getString("orderId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderId);
            if(oms!=null){
                List<OmsOrderLink> links = omsSellOrderService.getOmsOrderLinkByOrderNumberList(oms.getOrderNo());
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                if(null != links && links.size() > 0){
                    result.put("orderProcessInfo",links);
                }else{
                    result.put("orderProcessInfo","");
                }
                result.put("orderId",oms.getOrderNo());
                if("4".equals(link.getLinkCode())){
                    result.put("orderStatus","04");
                }else {
                    result.put("orderStatus","03");
                }
                result.put("orderOwner",oms.getCreateName());
                result.put("orderDealInfo",link.getOper_name());
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
            }else{
                result.put("orderProcessInfo","");
                result.put("orderId","");
                result.put("orderStatus","");
                result.put("orderOwner","");
                result.put("orderDealInfo","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            result.put("orderProcessInfo","");
            result.put("orderId","");
            result.put("orderStatus","");
            result.put("orderOwner","");
            result.put("orderDealInfo","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
        }
    }

    /**
     * 查询商品基本信息服务
     */
    public void offerInfoQuery(){
        Map<String,Object> map =new HashMap<>();
        try {
            String content = UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("查询商品基本信息服务接口参数异常");
                map.put("bizCode",-1);
                map.put("data","");
                map.put("msg","接收参数为空");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            String json = URLDecoder.decode(content.toString(), "UTF-8");
            logger.info("查询商品基本信息服务："+json);
            JSONObject datajson = JSONObject.fromObject(json);
            JSONObject rootjson = JSONObject.fromObject(datajson.get("ROOT"));
            JSONObject data = JSONObject.fromObject(rootjson.get("BODY"));

            String offerID = data.getString("offerID");
            Map<String,Object> product=  testingService.queryOmsOrderProductById(offerID);
            if(null == product || product.size() == 0){
                map.put("bizCode",-1);
                map.put("data","");
                map.put("msg","未查询到数据");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            product.put("offerID",isString(product.get("ID")));
            product.put("offerName",isString(product.get("PRC_NAME")));
            product.put("offerType","");
            product.put("offerStatus",3);
            product.put("goodsInfo",isString(product.get("DESCRIPTION")));
            map.put("bizCode","0000");
            map.put("data",product);
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            map.put("bizCode",-1);
            map.put("data","");
            map.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/8 14:40
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同基本信息查询服务
     */
    public void contractInfoQuery(){
        Map<String,Object> map =new HashMap<>();
        try {
            String content = UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("合同基本信息查询服务参数异常");
                map.put("contractID","");//合同编码
                map.put("contractNumber","");//合同号
                map.put("offerList",new ArrayList<String>());
                map.put("firstParty","");//甲方名称
                map.put("secondParty","");//乙方名称
                map.put("customerID","");//客户编码
                map.put("contractDate","");//合同签约时间
                map.put("validDate","");//合同生效时间
                map.put("expireDate","");//合同失效时间
                map.put("contractFileList",new ArrayList<String>());//合同附件标识
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            String json = URLDecoder.decode(content.toString(), "UTF-8");
            logger.info("合同基本信息查询服务接口接收参数："+json);
            JSONObject datajson = JSONObject.fromObject(json);
            JSONObject rootjson = JSONObject.fromObject(datajson.get("ROOT"));
            JSONObject data = JSONObject.fromObject(rootjson.get("BODY"));
            String contractID = data.getString("contractID");
            ContractInfo contract = omsSellOrderService.getContractUnifor(contractID);
            MobileCorporation o = omsSellOrderService.getMobileCorporationObj(contract.getCompanyCode());
            if(null == contract){
                map.put("contractID","");
                map.put("contractNumber","");
                map.put("offerList",new ArrayList<String>());
                map.put("firstParty","");//甲方名称
                map.put("secondParty","");//乙方名称
                map.put("customerID","");
                map.put("contractDate","");
                map.put("validDate","");
                map.put("expireDate","");
                map.put("contractFileList",new ArrayList<String>());
                return;
            }
            map.put("contractID",contract);//合同编码
            map.put("contractNumber",contract.getContractNumber());//合同号
            map.put("offerList",new ArrayList<String>());
            map.put("firstParty",contract.getUNIT_NAME());//甲方名称
            map.put("secondParty",o.getMobileName());//乙方名称
            map.put("customerID",contract.getUNIT_ID());//客户编码
            map.put("contractDate",new SimpleDateFormat("yyyyMMddHHmmss").format(contract.getCreateDate()).toString());//合同签约时间
            map.put("validDate",new SimpleDateFormat("yyyyMMddHHmmss").format(contract.getEffectDate()).toString());//合同生效时间
            map.put("expireDate",new SimpleDateFormat("yyyyMMddHHmmss").format(contract.getLostEffectDate()).toString());//合同失效时间
            map.put("contractFileList",new ArrayList<String>());//合同附件标识
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            map.put("contractID","");
            map.put("contractNumber","");
            map.put("offerList",new ArrayList<String>());
            map.put("firstParty","");//甲方名称
            map.put("secondParty","");//乙方名称
            map.put("customerID","");
            map.put("contractDate","");
            map.put("validDate","");
            map.put("expireDate","");
            map.put("contractFileList",new ArrayList<String>());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/8 15:42
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同明细信息查询服务
     */
    public void contractItemInfoQuery(){
        Map<String,Object> map =new HashMap<>();
        try {
            String content = UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("合同明细信息查询服务参数异常");
                map.put("contractID","");
                map.put("contractNumber","");
                map.put("contractName","");
                map.put("firstParty","");
                map.put("secondParty","");
                map.put("offerList",new ArrayList<String>());
                map.put("customerID","");
                map.put("contractDate","");//合同签约时间
                map.put("validDate","");//合同生效时间
                map.put("expireDate","");//合同失效时间
                map.put("textOfClause","");//条款正文
                map.put("contractAmount","");
                map.put("contractFileList",new ArrayList<String>());//合同附件列表
                map.put("contractCheckHistory",new ArrayList<String>());//合同审批历史
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            String json = URLDecoder.decode(content.toString(), "UTF-8");
            logger.info("合同明细信息查询服务接口接收参数："+json);
            JSONObject datajson = JSONObject.fromObject(json);
            JSONObject rootjson = JSONObject.fromObject(datajson.get("ROOT"));
            JSONObject data = JSONObject.fromObject(rootjson.get("BODY"));
            String contractID = data.getString("contractID");
            ContractInfo contract = omsSellOrderService.getContractUnifor(contractID);
            MobileCorporation o = omsSellOrderService.getMobileCorporationObj(contract.getCompanyCode());
            ContractRenewal renewal= omsSellOrderService.getContractRenewal(contract.getContractId());
            if(null == contract){
                map.put("contractID","");
                map.put("contractNumber","");
                map.put("contractName","");
                map.put("firstParty","");
                map.put("secondParty","");
                map.put("offerList",new ArrayList<String>());
                map.put("customerID","");
                map.put("contractDate","");//合同签约时间
                map.put("validDate","");//合同生效时间
                map.put("expireDate","");//合同失效时间
                map.put("textOfClause","");//条款正文
                map.put("contractAmount","");
                map.put("contractFileList",new ArrayList<String>());//合同附件列表
                map.put("contractCheckHistory",new ArrayList<String>());//合同审批历史
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("contractID",contractID);
            map.put("contractNumber",contract.getContractNumber());
            map.put("contractName",contract.getContractName());
            map.put("firstParty",contract.getUNIT_NAME());
            map.put("secondParty",o.getMobileName());
            map.put("offerList",new ArrayList<String>());
            map.put("customerID","");
            map.put("contractDate",new SimpleDateFormat("yyyyMMddHHmmss").format(contract.getCreateDate()).toString());//合同签约时间
            map.put("validDate",new SimpleDateFormat("yyyyMMddHHmmss").format(contract.getEffectDate()).toString());//合同生效时间
            map.put("expireDate",new SimpleDateFormat("yyyyMMddHHmmss").format(contract.getLostEffectDate()).toString());//合同失效时间
            map.put("textOfClause",String.valueOf(renewal.getContent()));//条款正文
            map.put("contractAmount","");
            List<Map<String,String>> contractFileList = new ArrayList();
            Map<String,String> contractFile = new HashMap<>();
            contractFile.put("contractFileID",contract.getContractNumber());//合同附件编号
            contractFile.put("fileType","pdf");//附件类型
            contractFile.put("fileName",contract.getContractName());//附件名称
            contractFile.put("filePath",contract.getpDFAttUrl()==null?contract.getPdfUrl():"");//附件文件路径
            contractFileList.add(contractFile);
            map.put("contractFileList",contractFileList);//合同附件列表
            map.put("contractCheckHistory",new ArrayList<String>());//合同审批历史
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            map.put("contractID","");
            map.put("contractNumber","");
            map.put("contractName","");
            map.put("firstParty","");
            map.put("secondParty","");
            map.put("offerList",new ArrayList<String>());
            map.put("customerID","");
            map.put("contractDate","");//合同签约时间
            map.put("validDate","");//合同生效时间
            map.put("expireDate","");//合同失效时间
            map.put("textOfClause","");//条款正文
            map.put("contractAmount","");
            map.put("contractFileList",new ArrayList<String>());//合同附件列表
            map.put("contractCheckHistory",new ArrayList<String>());//合同审批历史
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    private String isString(Object object){
        if(object == null || "".equals(object) || "null".equals(object)){
            return "";
        }else{
            return object.toString();
        }
    }
}
