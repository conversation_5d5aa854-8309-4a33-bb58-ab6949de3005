package com.xinxinsoft.action.oms;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.fourtOpening.forcedOrder;
import com.xinxinsoft.sendComms.EIPoOrganization;
import com.xinxinsoft.service.oms.OmsStatisticalService;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import org.apache.htrace.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import net.sf.json.JSONObject;


import javax.crypto.spec.PSource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class OmsStatisticalAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(OmsStatisticalAction.class);
    private OmsStatisticalService omsStatisticalService;

    public OmsStatisticalService getOmsStatisticalService() {
        return omsStatisticalService;
    }

    public void setOmsStatisticalService(OmsStatisticalService omsStatisticalService) {
        this.omsStatisticalService = omsStatisticalService;
    }


    /**
     * 一键甩单
     * 显示省公司下的市23个
     * 显示市级下的区县XX个
     */
    public void queryRegion(){
        Result r = new Result();
        try {
            //String company_code = getString("company_code");
//            if (company_code.equals("00")){
                //查询省
                List<Map<String, String>> province = omsStatisticalService.getProvince();
                //Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(province));
                r.setCode(ResultCode.SUCCESS);
                r.setMessage("成功");
                r.setData(province);
                Write(r.toString());
//            }else {
//                //查询市
//                List<Map<String, String>> city = omsStatisticalService.getCity(company_code);
//                //Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(city));
//                r.setCode(ResultCode.SUCCESS);
//                r.setMessage("成功");
//                r.setData(city);
//                Write(r.toString());
//            }
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }

    /**
     * 综合查询
     */
    public void integratedQuery(){
        Result r = new Result();
        JSONObject json = new JSONObject();
        //不传查省
        String company_no = getString("company_no");//省
        String company_code = getString("company_code");//市
        if(!("null").equals(company_code) && company_code != null && !company_code.equals("")){
            //查市
            float provinceTotal = (float)omsStatisticalService.getProvinceTotal(company_code);//查询地市总量（排除作废工单）
            //市下面的区县详细
            List<Map<String, String>> cityStatistical = omsStatisticalService.getCityStatistical(company_code);//查询所有数据（没有区分新旧工单）
            //System.out.println("市详细=="+JSONHelper.SerializeWithNeedAnnotationDateFormats(cityStatistical));
            //市产品top5
            List<Map<String, String>> product = omsStatisticalService.getProduct(company_code);//查询产品前五条数据
            for (int i=0;i<product.size();i++){
                //System.out.println(product.get(i));
                JSONObject jsonObject1 = JSONObject.fromObject(product.get(i));
                float number = (float)jsonObject1.getInt("NUMBER");
                float result = number / provinceTotal * 100;
                product.get(i).put("PROPORTION",String.format("%10.2f%%", result).trim());
            }
            //查询老版本已完成的工单数量
            Integer OldVersion = omsStatisticalService.QueryOldVersion(company_code);//查询甩单老版本中 已完成和已归档的工单总量
            List<Map<String, String>> stateProduct = omsStatisticalService.getStateProduct(company_code);//查询新版本环节各个环节上的甩单总量
            List<Map<String,String>> OrderAllNumber = new ArrayList<Map<String,String>>();
            Map<String,String> map;
            Integer number = 0;
            for (int i=0;i<stateProduct.size();i++){
                //System.out.println(product.get(i));
                JSONObject jsonObject1 = JSONObject.fromObject(stateProduct.get(i));
                switch (jsonObject1.getInt("LINK_CODE")){
                    case 8:
                        map = new HashMap<String,String>();
                        map.put("TheArchiveNumber",String.valueOf(OldVersion += jsonObject1.getInt("NUMBER")));
                        OrderAllNumber.add(map);
                        break;
                    case 3:
                        map = new HashMap<String,String>();
                        map.put("ToAcceptNumber",String.valueOf(jsonObject1.getInt("NUMBER")));
                        OrderAllNumber.add(map);
                        break;
                    case 2:
                        map = new HashMap<String,String>();
                        map.put("ToDispatchNumber",String.valueOf(jsonObject1.getInt("NUMBER")));
                        OrderAllNumber.add(map);
                        break;
                    default:
                        if (jsonObject1.getInt("LINK_CODE")!=1){
                            number+=jsonObject1.getInt("NUMBER");
                        }
                        break;
                }
            }
            map = new HashMap<String,String>();
            map.put("ConstructionNumber",String.valueOf(number));
            OrderAllNumber.add(map);
            //System.out.println("产品top5=="+JSONHelper.SerializeWithNeedAnnotationDateFormats(product));
            //昨天，今天增长率
            Integer today1 = omsStatisticalService.getToday(company_code);
            Integer yesterday1 = omsStatisticalService.getYesterday(company_code);
            if(yesterday1==0){
                json.put("todayGrowth",today1 - yesterday1);
            }else {
                float today = (float)omsStatisticalService.getToday(company_code);
                float yesterday = (float)omsStatisticalService.getYesterday(company_code);
                logger.info("今天甩单数量"+today);
                logger.info("昨天甩单数量"+yesterday);
                if (yesterday == today) {
                    json.put("todayGrowth","0.00%");
                } else {
                    float result = (today - yesterday) / yesterday * 100;
                    json.put("todayGrowth",String.format("%10.2f%%", result).trim());
                }
            }
            Integer weeks1 = omsStatisticalService.getWeeks(company_code);
            Integer lastWeek1 = omsStatisticalService.getLastWeek(company_code);
            if(lastWeek1==0){
                json.put("weekGrowth",weeks1 - lastWeek1);
            }else {
                float weeks = (float)omsStatisticalService.getWeeks(company_code);//这周
                float lastWeek = (float)omsStatisticalService.getLastWeek(company_code);//上周
                logger.info("这周甩单数量"+weeks);
                logger.info("上周甩单数量"+lastWeek);
                if(weeks==lastWeek){
                    json.put("weekGrowth","0.00%");
                }else {
                    float result = (weeks - lastWeek) / lastWeek * 100;
                    json.put("weekGrowth",String.format("%10.2f%%", result).trim());
                }
            }
            //这月，上月增长率
            Integer month1 = omsStatisticalService.getMonth(company_code);
            Integer lastMonth1 = omsStatisticalService.getLastMonth(company_code);
            if(lastMonth1==0){
                json.put("monthGrowth",month1 - lastMonth1);
            }else {
                float month = (float)omsStatisticalService.getMonth(company_code);//这月
                float lastMonth = (float)omsStatisticalService.getLastMonth(company_code);//上月
                logger.info("这月甩单数量"+month);
                logger.info("上月甩单数量"+lastMonth);
                if(month==lastMonth){
                    json.put("monthGrowth","0.00%");
                }else {
                    float result = (month - lastMonth) / lastMonth * 100;
                    json.put("monthGrowth",String.format("%10.2f%%", result).trim());
                }
            }
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            json.put("provinceTotal",provinceTotal);//全市的数量
            json.put("today",today1);//今天的甩单量
            json.put("stateProduct",OrderAllNumber);
            json.put("statisticalAnalysis",cityStatistical);
            json.put("product",product);
            r.setData(json);
            Write(r.toString());
        }else {
            //查省
            float provinceTotal = (float)omsStatisticalService.getProvinceTotal(company_no);
//            System.out.println("全省数量=="+provinceTotal);
            //省下面的市详细
            List<Map<String, String>> provinceStatistical = omsStatisticalService.getProvinceStatistical();
            //System.out.println("市详细=="+JSONHelper.SerializeWithNeedAnnotationDateFormats(provinceStatistical));
            //省产品top5
            List<Map<String, String>> product = omsStatisticalService.getProduct(company_no);
            //System.out.println("长度为"+product.size());
            //JSONObject jsonObject = JSONObject.fromObject(product);
            for (int i=0;i<product.size();i++){
                //System.out.println(product.get(i));
                JSONObject jsonObject1 = JSONObject.fromObject(product.get(i));
                float number = (float)jsonObject1.getInt("NUMBER");
                //System.out.println("number===="+number);
                //System.out.println("provinceTotal===="+provinceTotal);
                //System.out.println((float)number/provinceTotal*100+"%");
                float result = number / provinceTotal * 100;
                product.get(i).put("PROPORTION",String.format("%10.2f%%", result).trim());
            }
            //查询老版本已完成的工单数量
            Integer OldVersion = omsStatisticalService.QueryOldVersion(company_no);
            List<Map<String, String>> stateProduct = omsStatisticalService.getStateProduct(company_no);
            List<Map<String,String>> OrderAllNumber = new ArrayList<Map<String,String>>();
            Map<String,String> map;
            Integer number = 0;
            for (int i=0;i<stateProduct.size();i++){
                //System.out.println(product.get(i));
                JSONObject jsonObject1 = JSONObject.fromObject(stateProduct.get(i));
                switch (jsonObject1.getInt("LINK_CODE")){
                    case 8:
                        map = new HashMap<String,String>();
                        map.put("TheArchiveNumber",String.valueOf(OldVersion += jsonObject1.getInt("NUMBER")));
                        OrderAllNumber.add(map);
                        break;
                    case 3:
                        map = new HashMap<String,String>();
                        map.put("ToAcceptNumber",String.valueOf(jsonObject1.getInt("NUMBER")));
                        OrderAllNumber.add(map);
                        break;
                    case 2:
                        map = new HashMap<String,String>();
                        map.put("ToDispatchNumber",String.valueOf(jsonObject1.getInt("NUMBER")));
                        OrderAllNumber.add(map);
                        break;
                    default:
                        if (jsonObject1.getInt("LINK_CODE")!=1){
                            number+=jsonObject1.getInt("NUMBER");
                        }
                        break;
                }
            }
            map = new HashMap<String,String>();
            map.put("ConstructionNumber",String.valueOf(number));
            OrderAllNumber.add(map);
            //昨天，今天增长率
            Integer today1 = omsStatisticalService.getToday(company_no);//今日
            Integer yesterday1 = omsStatisticalService.getYesterday(company_no);//昨日
            if(yesterday1==0){
                json.put("todayGrowth",today1 - yesterday1);
            }else {
//                float today = (float)today1;
//                float yesterday = (float)yesterday1;
                logger.info("今天甩单数量"+today1);
                logger.info("昨天甩单数量"+yesterday1);
                if (yesterday1 == today1) {
                    json.put("todayGrowth","0.00%");
                } else {
                    float result = ((today1 - yesterday1) / yesterday1)* 100;
                    logger.info("对比数量"+result+"&==="+String.format("%10.2f%%", result).trim());
                    json.put("todayGrowth",String.format("%10.2f%%", result).trim());
                }
            }
            //这周，上周增长率
            Integer weeks1 = omsStatisticalService.getWeeks(company_no);//这周
            Integer lastWeek1 = omsStatisticalService.getLastWeek(company_no);//上周
            if(lastWeek1==0){
                json.put("weekGrowth",weeks1 - lastWeek1);
            }else {
                float weeks = (float)weeks1;//这周
                float lastWeek = (float)lastWeek1;//上周
                logger.info("这周甩单数量"+weeks);
                logger.info("上周甩单数量"+lastWeek);
                if(weeks==lastWeek){
                    json.put("weekGrowth","0.00%");
                }else {
                    float result = ((weeks - lastWeek) / lastWeek) * 100;
                    json.put("weekGrowth",String.format("%10.2f%%", result).trim());
                }
            }
            //这月，上月增长率
            Integer month1 = omsStatisticalService.getMonth(company_no);//这月
            Integer lastMonth1 = omsStatisticalService.getLastMonth(company_no);//上月
            if(lastMonth1==0){
                json.put("monthGrowth",month1 - lastMonth1);
            }else {
                float month = (float)month1;//这月
                float lastMonth = (float)lastMonth1;//上月
                logger.info("这月甩单数量"+month);
                logger.info("上月甩单数量"+lastMonth);
                if(month==lastMonth){
                    json.put("monthGrowth","0.00%");
                }else {
                    float result = ((month - lastMonth) / lastMonth) * 100;
                    json.put("monthGrowth",String.format("%10.2f%%", result).trim());
                }
            }
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            json.put("provinceTotal",provinceTotal);
            json.put("today",today1);
            json.put("stateProduct",OrderAllNumber);
            json.put("statisticalAnalysis",provinceStatistical);
            json.put("product",product);
            r.setData(json);
            Write(r.toString());
        }
    }

    /**
     * 甩单总量(省，市)
     */
    public void getProvinceTotal(){
        Result r = new Result();
        try {
            String company_no = getString("company_no");
            Integer provinceTotal = omsStatisticalService.getProvinceTotal(company_no);
            //System.out.println("数量为"+provinceTotal);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(provinceTotal);
            Write(r.toString());
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }

    /**
     * 显示省公司和市公司的总量
     */
//    public void getProvinceStatistical(){
//        List<Map<String, String>> provinceStatistical = omsStatisticalService.getProvinceStatistical();
//        Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(provinceStatistical));
//        JSONObject obj = JSONObject.fromObject(systemDept);
//        org.json.JSONObject
//        EIPoOrganization.getEIPDepartmentData(null);
//        JSONObject
//        System.out.println(obj);
//        Write(obj.toString());
//
//    }

    /**
     * 显示市公司下面区县的总量
     */
//    public void getCityStatistical() {
//        String company_code = getString("company_code");
//        List<Map<String, String>> cityStatistical = omsStatisticalService.getCityStatistical(company_code);
//        Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(cityStatistical));
//    }


    /**
     * 一键甩单统计分析数据
     * 省下面的市的统计
     * 市下面的区县统计
     */
    public void statisticalAnalysis(){
        Result r = new Result();
        try {
            String company_code = getString("company_code");
            if(company_code == "" || ("").equals(company_code) || ("null").equals(company_code)){   //显示省下面的市
                List<Map<String, String>> provinceStatistical = omsStatisticalService.getProvinceStatistical();
                //Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(provinceStatistical));
                r.setCode(ResultCode.SUCCESS);
                r.setMessage("成功");
                r.setData(provinceStatistical);
                Write(r.toString());
            }else { //显示市下面的区县
                List<Map<String, String>> cityStatistical = omsStatisticalService.getCityStatistical(company_code);
                //Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(cityStatistical));
                r.setCode(ResultCode.SUCCESS);
                r.setMessage("成功");
                r.setData(cityStatistical);
                Write(r.toString());
            }
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }

    }



    /**
     * 一键甩单产品Top5
     */
    public void getProduct(){
        Result r = new Result();
        try{
            String company_no = getString("company_no");
            List<Map<String, String>> product = omsStatisticalService.getProduct(company_no);
            //Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(product));
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(product);
            Write(r.toString());
        }catch (Exception e){
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }

    /**
     * 比较今日和昨日增量
     */
    public void getCompareDayIncremental(){
        Result r = new Result();
        try{
            //不传查所有
            String company_code = getString("company_code");//市
            String county_no = getString("county_no");//区县
//            System.out.println("company_code=="+company_code);
//            System.out.println("county_no=="+county_no);
            if(county_no!=null&&!("").equals(county_no)&&!("null").equals(county_no)){ //查询区县
                if (company_code!=null&&!("").equals(company_code)&&!("null").equals(company_code)){
                    Map<String, String> countyToday = omsStatisticalService.getCountyToday(company_code, county_no);//今日
                    Map<String, String> yesterdayCounty = omsStatisticalService.getYesterdayCounty(company_code, county_no);//昨日
                    String s = JSONHelper.SerializeWithNeedAnnotationDateFormats(countyToday);
                    String s1 = JSONHelper.SerializeWithNeedAnnotationDateFormats(yesterdayCounty);
                    JSONObject jsonObject = JSONObject.fromObject(s);
                    //String number = jsonObject.getString("NUMBER");
                    int number = jsonObject.getInt("NUMBER");
                    JSONObject jsonObject1 = JSONObject.fromObject(s1);
                    //String number1 = jsonObject1.getString("NUMBER");
                    int number1 = jsonObject1.getInt("NUMBER");
//                    System.out.println("运行查询区县");
//                    System.out.println("今日"+number);
//                    System.out.println("昨日"+number1);
                    r.setCode(ResultCode.SUCCESS);
                    r.setMessage("成功");
                    if(number==0&&number1==0){
                        r.setData("0%");
                    }else {
                        r.setData((number-number1)/number1*100+"%");
                    }
//                System.out.println((today-yesterday)/yesterday*100+"%");
                    Write(r.toString());
                }else {
                    r.setCode(ResultCode.FAIL);
                    r.setMessage("失败");
                    r.setData("参数不能为空");
                    Write(r.toString());
                }
            }else { //查询市或省
                Integer today = omsStatisticalService.getToday(company_code);//今日
                Integer yesterday = omsStatisticalService.getYesterday(company_code);//昨日
//                System.out.println("运行查询省或市");
//                System.out.println("今日"+today);
//                System.out.println("昨日"+yesterday);
                r.setCode(ResultCode.SUCCESS);
                r.setMessage("成功");
                if(today==0&&yesterday==0){
                    r.setData("0%");
                }else {
                    r.setData((today-yesterday)/yesterday*100+"%");
                }
//                System.out.println((today-yesterday)/yesterday*100+"%");
                Write(r.toString());
            }
        }catch (Exception e){
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }


    /**
     * 比较这周和上周的增量
     */
    public void getCompareWeeksIncremental(){
        Result r = new Result();
        try{
            String company_code = getString("company_code");//市
            String county_no = getString("county_no");//区县
//            System.out.println("company_code=="+company_code);
//            System.out.println("county_no=="+county_no);
            if(county_no!=null&&!("").equals(county_no)&&!("null").equals(county_no)){ //查询区县
                if (company_code!=null&&!("").equals(company_code)&&!("null").equals(company_code)){
                    Map<String, String> weeksToday = omsStatisticalService.getWeeksToday(company_code, county_no);//这周
                    Map<String, String> lastWeekToday = omsStatisticalService.getLastWeekToday(company_code, county_no);//上周
                    String s = JSONHelper.SerializeWithNeedAnnotationDateFormats(weeksToday);
                    String s1 = JSONHelper.SerializeWithNeedAnnotationDateFormats(lastWeekToday);
                    JSONObject jsonObject = JSONObject.fromObject(s);
                    int number = jsonObject.getInt("NUMBER");
                    JSONObject jsonObject1 = JSONObject.fromObject(s1);
                    int number1 = jsonObject1.getInt("NUMBER");
//                System.out.println("运行查询区县");
//                System.out.println("这周"+number);
//                System.out.println("上周"+number1);
                    r.setCode(ResultCode.SUCCESS);
                    r.setMessage("成功");
                    if(number==0&&number1==0){
                        r.setData("0%");
                    }else {
                        r.setData((number-number1)/number1*100+"%");
                    }
//                System.out.println((today-yesterday)/yesterday*100+"%");
                    Write(r.toString());
                }else {
                    r.setCode(ResultCode.FAIL);
                    r.setMessage("失败");
                    r.setData("参数不能为空");
                    Write(r.toString());
                }
            }else { //查询市或省
                Integer today = omsStatisticalService.getWeeks(company_code);//这周
                Integer yesterday = omsStatisticalService.getLastWeek(company_code);//上周
//                System.out.println("运行查询省或市");
//                System.out.println("这周"+today);
//                System.out.println("上周"+yesterday);
                r.setCode(ResultCode.SUCCESS);
                r.setMessage("成功");
                if(today==0&&yesterday==0){
                    r.setData("0%");
                }else {
                    r.setData((today-yesterday)/yesterday*100+"%");
                }
//                System.out.println((today-yesterday)/yesterday*100+"%");
                Write(r.toString());
            }
        }catch (Exception e){
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }

    /**
     * 比较这月和上月增量
     */
    public void getCompareMonthIncremental(){
        Result r = new Result();
        try{
            String company_code = getString("company_code");//市
            String county_no = getString("county_no");//区县
            //System.out.println(county_no != null && !county_no.equals(""));
            //System.out.println(company_code != null && !company_code.equals(""));
//            System.out.println("company_code=="+company_code);
//            System.out.println("county_no=="+county_no);
            if(county_no!=null&&!("").equals(county_no)&&!("null").equals(county_no)){ //查询区县
                if (company_code!=null&&!("").equals(company_code)&&!("null").equals(company_code)){
                    Map<String, String> monthToday = omsStatisticalService.getMonthToday(company_code, county_no);//这月
                    Map<String, String> lastMonthToday = omsStatisticalService.getLastMonthToday(company_code, county_no);//上月
                    String s = JSONHelper.SerializeWithNeedAnnotationDateFormats(monthToday);
                    String s1 = JSONHelper.SerializeWithNeedAnnotationDateFormats(lastMonthToday);
                    JSONObject jsonObject = JSONObject.fromObject(s);
                    int number = jsonObject.getInt("NUMBER");
                    JSONObject jsonObject1 = JSONObject.fromObject(s1);
                    int number1 = jsonObject1.getInt("NUMBER");
//                    System.out.println("运行查询区县");
//                    System.out.println("这月"+number);
//                    System.out.println("上月"+number1);
                    r.setCode(ResultCode.SUCCESS);
                    r.setMessage("成功");
                    if(number==0&&number1==0){
                        r.setData("0%");
                    }else {
                        r.setData((number-number1)/number1*100+"%");
                    }
//                System.out.println((today-yesterday)/yesterday*100+"%");
                    Write(r.toString());
                }else {
                    r.setCode(ResultCode.FAIL);
                    r.setMessage("失败");
                    r.setData("参数不能为空");
                    Write(r.toString());
                }
            }else { //查询市或省
                Integer today = omsStatisticalService.getMonth(company_code);//这月
                Integer yesterday = omsStatisticalService.getLastMonth(company_code);//上月
                System.out.println("运行查询省或市");
                System.out.println("这月"+today);
                System.out.println("上月"+yesterday);
//                System.out.println((today-yesterday)/yesterday*100+"%");
                r.setCode(ResultCode.SUCCESS);
                r.setMessage("成功");
                if(today==0&&yesterday==0){
                    r.setData("0%");
                }else {
                    r.setData((today-yesterday)/yesterday*100+"%");
                }
//                System.out.println((today-yesterday)/yesterday*100+"%");
                Write(r.toString());
            }
        }catch (Exception e){
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }

    }
    //compare day incremental


}
