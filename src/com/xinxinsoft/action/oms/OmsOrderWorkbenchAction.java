package com.xinxinsoft.action.oms;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.claimForFunds.MoneyApply;
import com.xinxinsoft.entity.claimForFunds.MoneyTotal;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.oms.*;
import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.entity.pms.PmsProductInfo;
import com.xinxinsoft.entity.pms.PmsProductLabel;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.ums.UnitInfo;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.StructureOfPersonnelService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.groupcustomer.GroupCustomerService;
import com.xinxinsoft.service.oms.OmsOrderProductService;
import com.xinxinsoft.service.oms.OmsOrderWorkbenchService;
import com.xinxinsoft.service.oms.OmsSellOrderService;
import com.xinxinsoft.service.ums.UnitInfoService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.StringHelper;
import com.xinxinsoft.utils.StringUtil;
import com.xinxinsoft.utils.UnitBase64Util;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.result.ResultGenerator;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.io.UnsupportedEncodingException;
import java.util.*;

/**
 * 工单受理工作台
 */
public class OmsOrderWorkbenchAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(OmsOrderWorkbenchAction.class);
    private OmsSellOrderService omsSellOrderService;
    private OmsOrderWorkbenchService omsOrderWorkbenchService;
    private OmsOrderProductService omsOrderProductService;
    private AttachmentService attachmentService;
    private Bpms_riskoff_service taskService;//任务
    private WaitTaskService service;//待办
    private SystemUserService systemUserService;//系统人员工具
    @Resource(name = "StructureOfPersonnelService")
    private StructureOfPersonnelService structureOfPersonnelService;
    private UnitInfoService unitInfoService;
    private GroupCustomerService groupCustomerService;

    private final String FTP_URL =  FileUpload.getFtpUrlTool("UNIT_FILE_DIRs");
    public OmsSellOrderService getOmsSellOrderService() {
        return omsSellOrderService;
    }

    public void setOmsSellOrderService(OmsSellOrderService omsSellOrderService) {
        this.omsSellOrderService = omsSellOrderService;
    }

    public OmsOrderWorkbenchService getOmsOrderWorkbenchService() {
        return omsOrderWorkbenchService;
    }

    public void setOmsOrderWorkbenchService(OmsOrderWorkbenchService omsOrderWorkbenchService) {
        this.omsOrderWorkbenchService = omsOrderWorkbenchService;
    }

    public OmsOrderProductService getOmsOrderProductService() {
        return omsOrderProductService;
    }

    public void setOmsOrderProductService(OmsOrderProductService omsOrderProductService) {
        this.omsOrderProductService = omsOrderProductService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public UnitInfoService getUnitInfoService() {
        return unitInfoService;
    }

    public void setUnitInfoService(UnitInfoService unitInfoService) {
        this.unitInfoService = unitInfoService;
    }

    public GroupCustomerService getGroupCustomerService() {
        return groupCustomerService;
    }

    public void setGroupCustomerService(GroupCustomerService groupCustomerService) {
        this.groupCustomerService = groupCustomerService;
    }

    public void saveOmsOrderWorkbench(){
        Map<String,Object> map =new HashMap<>();
        try {
            String type = getString("type");//判断是否是代办
            String orderId = getString("orderId");
            String title = getString("title");
            String mome = getString("mome");
            String momeHtml = getString("momeHtml");
            String submitJson = getString("submitJson");//审批人
            String CCUserJson = getString("CCUserJson");//抄送人
            String custom = getString("custom");
            String productIdArray = getString("productIdArray");
            String noTreaty = getString("noTreaty");//是否无协议报建(0)
            String attachmentId = getString("attachmentId");
            String waitId = getString("waitId");
            String workbenchId = getString("workbenchId");
            String onATaskId = getString("taskId");
            if(!StringHelper.isBlank(orderId)){
                map.put("code",-1);
                map.put("msg","参数错误");
                map.put("data","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            StringBuilder prcName= new StringBuilder();
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(orderId);
            if( null != productIdArray && !"".equals(productIdArray) && !"null".equals(productIdArray) && !"undefined".equals(productIdArray)){
                JSONArray products = JSONArray.fromObject(productIdArray);
                if(products.size() > 0){//判断是否有资费ID
                    for (int i = 0; i < products.size(); i++) {//循环获取资费
                        Object obj = products.get(i);
                        String proId = obj.toString();
                        PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(proId);
                        prcName.append(pmsinfo.getPrcName()).append("|");
                    }
                }
            }
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String orderNo = IBM + Bpms_riskoff_service.getUnlockedNumber();
            if (noTreaty == null || "".equals(noTreaty.trim()) || "null".equals(noTreaty.trim())||"undefined".equals(noTreaty.trim())||"1".equals(noTreaty.trim())) {
                noTreaty=null;
            }
            OmsOrderWorkbench workbench = omsOrderWorkbenchService.saveOmsOrderWorkbench(orderId,title,mome,momeHtml,custom,orderNo,prcName.toString(),productIdArray,OmsOrderWorkbench.TATE_WAITING,user,noTreaty);
            if(null == workbench){
                map.put("code",-1);
                map.put("msg","审批数据创建失败");
                map.put("data","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                throw new RuntimeException("工作台数据创建失败");
            }
            String process_sign="OmsOrderWorkbench."+Bpms_riskoff_service.getLockUpNumber();
            Bpms_riskoff_process bpms_riskoff_process = taskService.setBpms_riskoff_process(workbench.getId(),process_sign,1,user);
            omsSellOrderService.saveOrupdateOmsSellOrder(omsSellOrder);
            JSONArray array = JSONArray.fromObject(submitJson);
            String launchTask = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",2,"SH","预受理发起",user.getRowNo(),user,"0",null,null);
            if(array.size() > 0){//判断是否审核人
                for (int i = 0; i < array.size(); i++) {//循环增加审核任务数据
                    JSONObject jsonObject = array.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String taskId = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"SH","预受理发起",userId,user,"1",launchTask,null);
                    commitBackLogData(workbench,userId,user,taskId,omsSellOrder);
                }
            }

            JSONArray ccarray = JSONArray.fromObject(CCUserJson);
            if(ccarray.size() > 0){//判断是否有抄送人
                for (int i = 0; i < ccarray.size(); i++) {//循环增加抄送任务数据
                    JSONObject jsonObject = ccarray.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String taskId = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"CS","预受理发起",userId,user,"1",launchTask,null);
                    commitBackLogData(workbench,userId,user,taskId,omsSellOrder);
                }
            }

            if (!StringUtils.isEmpty(attachmentId)) {
                //判断是否上传了附件,获取前台提交的附件Id；
                String[] fileId = attachmentId.split(",");
                if (fileId.length > 0) {
                    for (String s : fileId) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(omsSellOrder.getId());
                        sa.setAttachmentId(s);
                        sa.setLink(OmsSellOrder.OMSSELLORDER);
                        omsSellOrderService.saveSandA(sa);
                    }
                }
            }
            if(!"undefined".equals(waitId)&&waitId!=null&&waitId.length()>0&&!"null".equals(waitId)) {
                if(!"undefined".equals(onATaskId)&&onATaskId!=null&&onATaskId.length()>0&&!"null".equals(onATaskId)) {
                    Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task("已归档", 2, onATaskId);//修改本条数据
                    if(null == bpms_riskoff_task){
                        throw new Exception("未查询到当前任务信息，请确认当前任务是否存在");
                    }
                }
                if(!"undefined".equals(workbenchId)&&workbenchId!=null&&workbenchId.length()>0&&!"null".equals(workbenchId)) {
                    omsOrderWorkbenchService.updateOmsOrderWorkbench(workbenchId,1);
                    taskService.updatebpmsRiskoffProcess(workbenchId,2);
                }else{
                    throw new Exception("未查询到当前审批工单ID信息，请确认当前审批是否正常");
                }
                WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    throw new Exception("未查询到待办信息,待办信息为空");
                }
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","审批发起成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("保存工作台异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     *  需求单生成待办给订单经理
     * @param order 需求单
     * @param userid 分配人员ID
     */
    public void commitOmsSellOrderData(OmsSellOrder order,Integer userid,String title,String isContract,String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + title);//待办名称
        waitTask.setCreationTime(new Date());//代办生成时间
        waitTask.setUrl("jsp/demandOrder/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 查询是否有订单经理
     *
     * ALL(查询全省);ROLE(查询角色);CITY(查询地市);COUNTY(查询区县),COLLECTION(收藏)--type
     * 角色CODE--roleName
     * 模糊查询名字--name
     */
    public List<Map<String, String>> queryFuzzyUser(String type,String roleName,String name){
        List<Map<String, String>> listMap= new ArrayList<>();
        try {
            if ("ALL".equals(type)) {//查询全省
                if (!"".equals(name) && name != null) {
                    //根据页面输入名称模糊查询人员
                    listMap = structureOfPersonnelService.queryVwUserinfo(name);
                }
            } else if ("ROLE".equals(type)) {//查询角色
                if (!"".equals(roleName) && roleName != null) {
                    //根据角色code查询角色下面的人员（区分地市和区县）
                    List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
                    if(userlistMap.size() > 0){
                        if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {
                            logger.info("这是区县");
                            listMap = structureOfPersonnelService.queryRoleUserInfo(roleName, user, userlistMap, name, "Q");
                        } else {
                            if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                                logger.info("这是省公司");
                                listMap = structureOfPersonnelService.queryRoleUserInfo(roleName, user, userlistMap, name, "S");
                            } else {
                                logger.info("这是市公司");
                                listMap = structureOfPersonnelService.queryRoleUserInfo(roleName, user, userlistMap, name, "D");
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return listMap;
    }
    /**
     * 提交待办生成
     *
     */
    public void commitBackLogData(OmsOrderWorkbench workbench, Integer userid,SystemUser user, String taskid,OmsSellOrder oms) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理][" +oms.getUnitName()+"]"+workbench.getTitle());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/demandOrder/approvalShow.jsp?workbenchId=" + workbench.getId()+ "&taskId=" +taskid);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskid);
        service.saveWait(waitTask, this.getRequest());
    }



    private String getFileUrl(String aid){
        Attachment attachment = attachmentService.getAttachmentById(aid);

        return attachment != null ? FTP_URL + attachment.getAttachmentUrl() : "";
    }


    public void readOmsOrderWorkbench(){
        Map<String, Object> map = new HashMap<>();
        try {
            String waitId = getString("waitId");
            String taskId = getString("taskId");
            Integer status = getInteger("status");
            Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task("已阅", status, taskId);//修改本条数据
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code", -1);
                map.put("data", "");
                map.put("msg", "未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code", 1);
            map.put("msg", "操作成功");
            map.put("data", "");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("发起审批人已阅方法，解除待办错误信息："+e.getMessage(),e);
            map.put("code", -1);
            map.put("msg", "操作失败"+e.getMessage());
            map.put("data", "");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void getIsNotice(){
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            List<OmsOrderWorkbench> benchList = omsOrderWorkbenchService.getOmsOrderWorkbenchByOrderNoAndStateList(order.getOrderNo(),OmsOrderWorkbench.STATE_INVALID);
            if(benchList.isEmpty()){
                map.put("code", 1);
                map.put("msg", "");
                map.put("data", "0");
            }else{
                if(Integer.parseInt(order.getOperateNo())==user.getRowNo()){
                    map.put("code", 1);
                    map.put("msg", "");
                    map.put("data", "1");
                }else{
                    if(Integer.parseInt(order.getCreateNo())==user.getRowNo()){
                        map.put("code", 1);
                        map.put("msg", "");
                        map.put("data", "1");
                    }else{
                        map.put("code", 1);
                        map.put("msg", "");
                        map.put("data", "0");
                    }
                }
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询是否订单经理发起审批工单被驳回错误信息："+e.getMessage(),e);
            map.put("code", -1);
            map.put("msg", "查询是否订单经理发起审批工单被驳回错误信息："+e.getMessage());
            map.put("data", "");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }
}
