package com.xinxinsoft.action.oms;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.contractUniformity.ContractInfo;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.oms.OmsLinkDialogue;
import com.xinxinsoft.entity.oms.OmsOrderLink;
import com.xinxinsoft.entity.oms.OmsOrderWorkbench;
import com.xinxinsoft.entity.oms.OmsSellOrder;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.omsService.OrderEsbService;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.oms.OmsOrderProductService;
import com.xinxinsoft.service.oms.OmsOrderWorkbenchService;
import com.xinxinsoft.service.oms.OmsSellOrderService;
import com.xinxinsoft.service.oms.ServiceStandardizationTestingService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.StringHelper;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultGenerator;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;

public class OrderTesting extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(OrderTesting.class);
    private ServiceStandardizationTestingService testingService;
    private OmsSellOrderService omsSellOrderService;
    private OmsOrderWorkbenchService omsOrderWorkbenchService;
    private OmsOrderProductService omsOrderProductService;
    private Bpms_riskoff_service taskService;
    private SystemUserService systemUserService;//系统人员工具
    private WaitTaskService service;//待办
    public ServiceStandardizationTestingService getTestingService() {
        return testingService;
    }

    public void setTestingService(ServiceStandardizationTestingService testingService) {
        this.testingService = testingService;
    }

    public OmsSellOrderService getOmsSellOrderService() {
        return omsSellOrderService;
    }

    public void setOmsSellOrderService(OmsSellOrderService omsSellOrderService) {
        this.omsSellOrderService = omsSellOrderService;
    }

    public OmsOrderWorkbenchService getOmsOrderWorkbenchService() {
        return omsOrderWorkbenchService;
    }

    public void setOmsOrderWorkbenchService(OmsOrderWorkbenchService omsOrderWorkbenchService) {
        this.omsOrderWorkbenchService = omsOrderWorkbenchService;
    }

    public OmsOrderProductService getOmsOrderProductService() {
        return omsOrderProductService;
    }

    public void setOmsOrderProductService(OmsOrderProductService omsOrderProductService) {
        this.omsOrderProductService = omsOrderProductService;
    }

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    /**
     * 列表查询
     */
    public void getOrderList(){
        try {
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数

            String serviceNum=getString("serviceNum");//工单编码
            String planName=getString("planName");//需求名称
            String customID=getString("customID");//集团280
            String startTime=getString("startTime");
            String endTime=getString("endTime");
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            LayuiPage json= testingService.getOrderList(page,user,serviceNum,planName,customID,startTime,endTime);
            System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        }catch (Exception e) {
            logger.error(e.getMessage(),e);
            e.printStackTrace();
            Write("1");
        }
    }
    /**
     * 订单生成服务
     */
    public void orderCreate(){
        Map<String,Object> mapJson =new HashMap<>();
        Result result = ResultGenerator.genSuccessResult();
        try {
            Map<String,Object> map =new HashMap<>();
            String json = getString("json");//资费json
            String userid = getString("userid");
            String goodsId = getString("goodsId");
            String serviceNum = getString("serviceNum");
            String paymentNo = getString("paymentNo");
            String channelID = getString("channelID");
            String productList = getString("productList");
            String paymentList = getString("paymentList");
            String accountNo = getString("accountNo");
            String orderOperationType = getString("orderOperationType");
            String protocolId = getString("protocolId");
            String feeList = getString("feeList");
            String protocolList = getString("protocolList");
            String planId = getString("planId");
            String servicePropertyList = getString("servicePropertyList");
            String planName = getString("planName");
            String operatType = getString("operatType");
            String orderType = getString("orderType");
            String orderDescription = getString("orderDescription");
            String customID = getString("customID");
            String contractNo = getString("contractNo");
            String customName = getString("customName");
            String dueAmount = getString("dueAmount");
            String offsetAmount = getString("offsetAmount");
            String actualAmount = getString("actualAmount");
            String relatedContractNo = getString("relatedContractNo");
            String cityId = getString("cityId");
            String orderOwner = getString("orderOwner");

            map.put("json",json);
            map.put("userid",userid);
            map.put("goodsId",goodsId);
            map.put("serviceNum",serviceNum);
            map.put("paymentNo",paymentNo);
            map.put("channelID",channelID);
            map.put("productList",productList);
            map.put("paymentList",paymentList);
            map.put("accountNo",accountNo);
            map.put("orderOperationType",orderOperationType);
            map.put("protocolId",protocolId);
            map.put("feeList",feeList);
            map.put("protocolList",protocolList);
            map.put("planId",planId);
            map.put("servicePropertyList",servicePropertyList);
            map.put("planName",planName);
            map.put("operatType",operatType);
            map.put("orderType",orderType);
            map.put("orderDescription",orderDescription);
            map.put("customID",customID);
            map.put("contractNo",contractNo);
            map.put("customName",customName);
            map.put("dueAmount",dueAmount);
            map.put("offsetAmount",offsetAmount);
            map.put("actualAmount",actualAmount);
            map.put("relatedContractNo",relatedContractNo);
            map.put("cityId","");
            map.put("orderOwner",orderOwner);
            map.put("rowNo",user.getRowNo());
            map.put("boosName",user.getBossUserName());
            result = OrderEsbService.getInstance().orderCreate(map);
            JSONObject jsonObject = JSONObject.fromObject(result);
            if("200".equals(jsonObject.get("code").toString())){
                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                mapJson.put("bizCode",data.get("bizCode"));
                mapJson.put("customOrderId",data.get("customOrderId"));
                mapJson.put("msg",data.get("msg"));
            }else{
                mapJson.put("bizCode",-1);
                mapJson.put("customOrderId","");
                mapJson.put("msg","操作失败");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            mapJson.put("bizCode",-1);
            mapJson.put("customOrderId","");
            mapJson.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * 工作台保存
     */
    public void saveOmsOrderWorkbench(){
        Map<String,Object> map =new HashMap<>();
        try {
            String orderId = getString("orderId");
            String title = getString("title");
            String mome = getString("mome");
            String momeHtml = getString("momeHtml");
            String submitJson = getString("submitJson");
            String CCUserJson = getString("CCUserJson");
            String custom = getString("custom");
            String noTreaty = getString("noTreaty");//是否无协议报建
            if(!StringHelper.isBlank(orderId)){
                map.put("bizCode",-1);
                map.put("msg","参数错误");
                map.put("data","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String orderNo = IBM + Bpms_riskoff_service.getUnlockedNumber();
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(orderId);
            if (noTreaty == null || "".equals(noTreaty.trim()) || "null".equals(noTreaty.trim())||"undefined".equals(noTreaty.trim())||"1".equals(noTreaty.trim())) {
                noTreaty=null;
            }else{
                if("0".equals(noTreaty)){
                    List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("5","工单合同关联",orderId);
                    if(existence!=null){
                        if(existence.size()>0){
                            for(int i=0;i<existence.size();i++){
                                omsSellOrderService.deleteOmsOrderLink(existence.get(i).getId());
                            }
                        }
                    }
                    List<OmsOrderLink> existenceT =omsSellOrderService.getOmsOrderLinkByCode("4","工单审批",orderId);
                    if(existenceT!=null){
                        if(existenceT.size()==1){
                            OmsOrderLink link = existenceT.get(0);
                            link.setStatus(0);
                            omsSellOrderService.saveOrupdateOmsOrderLink(link);
                            omsSellOrder.setLinkOrderNo(link.getLinkOrderNo());
                            if(omsSellOrder.getWhetherManager()!=0) {
                                OmsLinkDialogue dig = new OmsLinkDialogue();
                                dig.setCreator_name(user.getEmployeeName());
                                dig.setCreator_no(user.getRowNo());
                                dig.setCreator_date(new Date());
                                dig.setOper_name(omsSellOrder.getCreateName());
                                dig.setOper_no(Integer.parseInt(omsSellOrder.getCreateNo()));
                                dig.setOper_date(new Date());
                                dig.setStatus(0);//1已处理，0未处理
                                dig.setLinkOrderNo(link.getLinkOrderNo());
                                dig.setOper_role("ROLE_CUMR");//处理角色客户经理
                                dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
                                OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                                commitOmsSellOrderData(omsSellOrder, Integer.parseInt(omsSellOrder.getCreateNo()), omsSellOrder.getTitle(), "", rdig.getId());

                                OmsLinkDialogue digTwo = new OmsLinkDialogue();
                                digTwo.setCreator_name(user.getEmployeeName());
                                digTwo.setCreator_no(user.getRowNo());
                                digTwo.setCreator_date(new Date());
                                digTwo.setOper_name(omsSellOrder.getOperateName());
                                digTwo.setOper_no(Integer.parseInt(omsSellOrder.getOperateNo()));
                                digTwo.setOper_date(new Date());
                                digTwo.setStatus(0);//1已处理，0未处理
                                digTwo.setLinkOrderNo(link.getLinkOrderNo());
                                digTwo.setOper_role("ROLE_ODMR");//处理角色订单经理
                                digTwo.setCreator_role("ROLE_ODMR");//发起角色订单经理
                                OmsLinkDialogue rdigTwo = omsSellOrderService.saveOrupdateOmsLinkDialogue(digTwo);
                                commitOmsSellOrderData(omsSellOrder, Integer.parseInt(omsSellOrder.getOperateNo()), omsSellOrder.getTitle(), "", rdigTwo.getId());
                            }

                        }
                    }
                }
            }
            OmsOrderWorkbench workbench = omsOrderWorkbenchService.saveOmsOrderWorkbench(orderId,title,mome,momeHtml,custom,orderNo,"","",OmsOrderWorkbench.TATE_WAITING,user,noTreaty);
            String process_sign="ServiceStandardizationTesting."+Bpms_riskoff_service.getLockUpNumber();
            Bpms_riskoff_process bpms_riskoff_process = taskService.setBpms_riskoff_process(workbench.getId(),process_sign,1,user);
            omsSellOrder.setNoTreaty(noTreaty);
            omsSellOrderService.saveOrupdateOmsSellOrder(omsSellOrder);
            JSONArray array = JSONArray.fromObject(submitJson);
            if(array.size() > 0){//判断是否有抄送人或者审核人
                for (int i = 0; i < array.size(); i++) {//循环增加抄送或审核任务数据
                    JSONObject jsonObject = array.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String taskId = taskService.setBpms_riskoff_task(bpms_riskoff_process.getProcess_sign(),"",1,"SH","订单任务发起",userId,user);
                }
            }

            JSONArray ccarray = JSONArray.fromObject(CCUserJson);
            if(ccarray.size() > 0){//判断是否有抄送人或者审核人
                for (int i = 0; i < ccarray.size(); i++) {//循环增加抄送或审核任务数据
                    JSONObject jsonObject = ccarray.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String taskId = taskService.setBpms_riskoff_task(bpms_riskoff_process.getProcess_sign(),"",1,"CS","订单任务发起",userId,user);
                }
            }
            map.put("bizCode","0000");
            map.put("data","");
            map.put("msg","任务发起成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            map.put("bizCode",-1);
            map.put("data","");
            map.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     *  需求单生成待办给订单经理
     * @param order 需求单
     * @param userid 分配人员ID
     */
    public void commitOmsSellOrderData(OmsSellOrder order,Integer userid,String title,String isContract,String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + title);//待办名称
        waitTask.setCreationTime(new Date());//代办生成时间
        waitTask.setUrl("jsp/demandOrder/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 合同查询
     */
    public void queryContractInfo(){
        try {
            String id = getString("id");
            String omsOrderId = getString("omsOrderId");
            ContractInfo contractInfo= omsSellOrderService.queryContractInfo(id);
            if(contractInfo!=null && omsOrderId!=null && !"".equals(omsOrderId)&& !"undefined".equals(omsOrderId)){
                OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(omsOrderId);
                oms.setContractId(contractInfo.getId());
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(contractInfo));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            Write("1");
        }
    }

    /**
     * 订单基本信息查询
     */
    public void orderInfoQuery(){

        Map<String,Object> mapJson =new HashMap<>();
        Result result = ResultGenerator.genSuccessResult();
        try {
            Map<String,Object> map =new HashMap<>();
            String orderId = getString("orderId");
            map.put("orderId",orderId);
            map.put("boosName",user.getBossUserName());
            result = OrderEsbService.getInstance().orderInfoQuery(map);
            JSONObject jsonObject = JSONObject.fromObject(result);
            if("200".equals(jsonObject.get("code").toString())){
                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                mapJson.put("bizCode",data.get("bizCode"));
                mapJson.put("data",data.get("data"));
                mapJson.put("msg",data.get("msg"));
            }else{
                mapJson.put("bizCode",-1);
                mapJson.put("data","");
                mapJson.put("msg","操作失败");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            mapJson.put("bizCode",-1);
            mapJson.put("data","");
            mapJson.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * 订单流程环节查询服务
     */
    public void orderProcessQuery(){
        Map<String,Object> mapJson =new HashMap<>();
        Result results = ResultGenerator.genSuccessResult();
        try {
            Map<String,Object> map =new HashMap<>();
            String orderId = getString("orderId");
            map.put("orderId",orderId);
            map.put("boosName",user.getBossUserName());
            results = OrderEsbService.getInstance().orderProcessQuery(map);
            JSONObject jsonObject = JSONObject.fromObject(results);
            if("200".equals(jsonObject.get("code").toString())){
                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                mapJson.put("bizCode",data.get("bizCode"));
                mapJson.put("data",data.get("data"));
                mapJson.put("msg",data.get("msg"));
            }else{
                mapJson.put("bizCode",-1);
                mapJson.put("data","");
                mapJson.put("msg","操作失败");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));

        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            mapJson.put("bizCode",-1);
            mapJson.put("data","");
            mapJson.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * 查询商品基本信息服务
     */
    public void offerInfoQuery(){
        Map<String,Object> mapJson =new HashMap<>();
        Result results = ResultGenerator.genSuccessResult();
        try {
            Map<String,Object> map =new HashMap<>();
            String offerID = getString("offerID");
            map.put("offerID",offerID);
            map.put("boosName",user.getBossUserName());
            results = OrderEsbService.getInstance().offerInfoQuery(map);
            JSONObject jsonObject = JSONObject.fromObject(results);
            if("200".equals(jsonObject.get("code").toString())){
                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                mapJson.put("bizCode",data.get("bizCode"));
                mapJson.put("data",data.get("data"));
                mapJson.put("msg",data.get("msg"));
            }else{
                mapJson.put("bizCode",-1);
                mapJson.put("data","");
                mapJson.put("msg","操作失败");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            mapJson.put("bizCode",-1);
            mapJson.put("data","");
            mapJson.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * 合同基本信息查询服务
     */
    public void contractInfoQuery(){
        Map<String,Object> mapJson =new HashMap<>();
        Result results = ResultGenerator.genSuccessResult();
        try {
            Map<String,Object> map =new HashMap<>();
            String contractID = getString("contractID");
            map.put("contractID",contractID);
            map.put("boosName",user.getBossUserName());
            results = OrderEsbService.getInstance().contractInfoQuery(map);
            JSONObject jsonObject = JSONObject.fromObject(results);
            if("200".equals(jsonObject.get("code").toString())){
                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                mapJson.put("bizCode",data.get("bizCode"));
                mapJson.put("data",data.get("data"));
                mapJson.put("msg",data.get("msg"));
            }else{
                mapJson.put("bizCode",-1);
                mapJson.put("data","");
                mapJson.put("msg","操作失败");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            mapJson.put("bizCode",-1);
            mapJson.put("data","");
            mapJson.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * 合同明细信息查询服务
     */
    public void contractItemInfoQuery(){
        Map<String,Object> mapJson =new HashMap<>();
        Result results = ResultGenerator.genSuccessResult();
        try {
            Map<String,Object> map =new HashMap<>();
            String contractID = getString("contractID");
            map.put("contractID",contractID);
            map.put("boosName",user.getBossUserName());
            results = OrderEsbService.getInstance().contractItemInfoQuery(map);
            JSONObject jsonObject = JSONObject.fromObject(results);
            if("200".equals(jsonObject.get("code").toString())){
                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                mapJson.put("bizCode",data.get("bizCode"));
                mapJson.put("data",data.get("data"));
                mapJson.put("msg",data.get("msg"));
            }else{
                mapJson.put("bizCode",-1);
                mapJson.put("data","");
                mapJson.put("msg","操作失败");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            mapJson.put("bizCode",-1);
            mapJson.put("data","");
            mapJson.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }
}
