package com.xinxinsoft.action.arrearsModule;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.http.HttpServletResponse;

import jxl.CellView;
import jxl.Workbook;
import jxl.format.Alignment;
import jxl.format.UnderlineStyle;
import jxl.format.VerticalAlignment;
import jxl.write.Label;
import jxl.write.WritableCellFormat;
import jxl.write.WritableFont;
import jxl.write.WritableSheet;
import jxl.write.WritableWorkbook;
import jxl.write.WriteException;

import org.apache.struts2.ServletActionContext;

import com.oreilly.servlet.multipart.ExceededSizeException;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.arrearsModule.ArrearsService;
import com.xinxinsoft.service.arrearsModule.ArrearsStatisticalService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.page.LayuiPage;

public class ArrearsStatisticalAction extends BaseAction {


	private ArrearsStatisticalService arrearsStatisticalService;
	private ArrearsService arrearsService;

	public void setArrearsStatisticalService(ArrearsStatisticalService arrearsStatisticalService) {
		this.arrearsStatisticalService = arrearsStatisticalService;
	}
	public void setArrearsService(ArrearsService arrearsService) {
		this.arrearsService = arrearsService;
	}

	public void getCompanyCode() {
		try {
			String companyCode = arrearsService.findCodeByRowNo(user.getRowNo());
			Write(companyCode);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			Write("ERROR");
		}
		
		
	}
	
	
	public void getArrearsStatisticalByPage() {
		LayuiPage layuiPage = new LayuiPage(1, 0);
		try {
			int defIndex = 1;
			int defSize = 10;
			String city = getString("branchOffice");
			String country = getString("country");
			String tableName = getString("tableName");
			if (tableName==null||tableName.equals("")) {
				throw new ExceededSizeException("表名为空");
			}

			// 获取当前用户
			SystemUser user = this.user;
			// 获取用户权限
			List list = arrearsService.findByRowNo(user.getRowNo());
			boolean flag = false;
			String json = null;

			for (int i = 0; i < list.size(); i++) {
				if ((list.get(i).toString()).equals("ROLE_REPM")||(list.get(i).toString()).equals("ROLE_QJMR")) {//??????????????
					flag = true;
					break;
				}
			}
			// 获取公司编码
			String companyCode = arrearsService.findCodeByRowNo(user.getRowNo());
			// 获取公司名称
			String companyName = arrearsService.findCompanyName(companyCode);
			if (getInteger("pageNo")!=null&&getInteger("pageNo")!=0) {
				defIndex = getInteger("pageNo");
			}
			if (getInteger("pageSize")!=null&&getInteger("pageSize")!=0) {
				defSize = getInteger("pageSize");
			}
			layuiPage  =  new LayuiPage(defIndex,defSize);
			if (companyCode.equals("00")==false) {
				city = companyName;
			}
				layuiPage.setCount(Long.parseLong(arrearsStatisticalService.getArrearsStatisticalsByTableNameCount(tableName, city,country)));
				layuiPage.setData(arrearsStatisticalService.getArrearsStatisticalsByTableName(layuiPage, tableName, city,country,true));
				layuiPage.setCode(0);
			

		} catch (Exception e) {
			// TODO: handle exception
			layuiPage.setCode(1);
			e.printStackTrace();
		}
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(layuiPage));
	}
	/**
	 * 获取超期账户信息
	 */
	public void sendAccount() {
		LayuiPage layuiPage = new LayuiPage(1, 0);
		try {
			int defIndex = 1;
			int defSize = 10;
			String city = getString("branchOffice");
			String country = getString("country");
			String accountNumber = getString("accountNumber");
			String groupCode = getString("groupCompanyCode");
			String tableName = getString("tableName");
			String dataState = getString("DataState");
			if (tableName==null||tableName.equals("")) {
				throw new ExceededSizeException("表名为空");
			}
			String spilicMon = tableName.substring(12);//取当前表的日期
			String spilicYear = tableName.substring(8,12);//取当前表的日期
			String calculationsAccountTableName = "CalculationsAccount_"+spilicYear+spilicMon;
			// 获取当前用户
			SystemUser user = this.user;
			// 获取用户权限
			List list = arrearsService.findByRowNo(user.getRowNo());
			boolean flag = false;
			String json = null;

			for (int i = 0; i < list.size(); i++) {
				if ((list.get(i).toString()).equals("ROLE_REPM")||(list.get(i).toString()).equals("ROLE_QJMR")) { //??????????????
					flag = true;
					break;
				}
			}
			// 获取公司编码
			String companyCode = arrearsService.findCodeByRowNo(user.getRowNo());
			// 获取公司名称
			String companyName = arrearsService.findCompanyName(companyCode);
			if (getInteger("pageNo")!=null&&getInteger("pageNo")!=0) {
				defIndex = getInteger("pageNo");
			}
			if (getInteger("pageSize")!=null&&getInteger("pageSize")!=0) {
				defSize = getInteger("pageSize");
			}
			layuiPage  =  new LayuiPage(defIndex,defSize);
			if (flag) {
				if (companyCode.equals("00")==false) {
					city = companyName;
				}
				
					layuiPage.setCount(Long.parseLong(arrearsStatisticalService.getCalculationListCount(calculationsAccountTableName, city, country, accountNumber, groupCode,dataState,null,null,false)));
					layuiPage.setData(arrearsStatisticalService.getCalculationList(calculationsAccountTableName, city, country, accountNumber, groupCode, layuiPage, true,dataState,null,null,false));
					layuiPage.setCode(0);
			}else {
				Object[] jobNumber = arrearsService.getBossUserName(this.user.getLoginName());
				String jobNumber2 = "";
				String mobile = "";
				if (jobNumber[0] != null) {
					jobNumber2 = jobNumber[0].toString();
				}
				if (jobNumber[1] != null) {
					mobile = jobNumber[1].toString();
				}
				layuiPage.setCount(Long.parseLong(arrearsStatisticalService.getCalculationListCount(calculationsAccountTableName, city, country, accountNumber, groupCode,dataState,jobNumber2,mobile,true)));
				layuiPage.setData(arrearsStatisticalService.getCalculationList(calculationsAccountTableName, city, country, accountNumber, groupCode, layuiPage, true,dataState,jobNumber2,mobile,true));
				layuiPage.setCode(0);
			}

		} catch (Exception e) {
			// TODO: handle exception
			layuiPage.setCode(1);
			e.printStackTrace();
		}
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(layuiPage));
	}
	
	public void uploadExcel() {
		try {
			String city = getString("branchOffice");
			String country = getString("country");
			String accountNumber = getString("accountNumber");
			String groupCode = getString("groupCompanyCode");
			String tableName = getString("tableName");
			String dataState = getString("DataState");
			String AccountMsg = getString("AccountMsg");
			// 获取公司编码
			String companyCode = arrearsService.findCodeByRowNo(user.getRowNo());
			// 获取公司名称
			String companyName = arrearsService.findCompanyName(companyCode);
			// 获取当前用户
			SystemUser user = this.user;
			// 获取用户权限
			List list = arrearsService.findByRowNo(user.getRowNo());
			boolean flag = false;
			String json = null;

			for (int i = 0; i < list.size(); i++) {
				if ((list.get(i).toString()).equals("ROLE_REPM")||(list.get(i).toString()).equals("ROLE_QJMR")) { //??????????????
					flag = true;
					break;
				}
			}
			List<Map<String, Object>> lists = new ArrayList<>();
			

				
				if (AccountMsg.equals("ArrearsStatistical")) {
					lists = arrearsStatisticalService.getArrearsStatisticalsByTableName(null, tableName, city,country,false);
				}else {
					String spilicMon = tableName.substring(12);//取当前表的日期
					String spilicYear = tableName.substring(8,12);//取当前表的日期
					String calculationsAccountTableName = "CalculationsAccount_"+spilicYear+spilicMon;
					if (flag) {
						if (companyCode.equals("00")==false) {
							city = companyName;
						}
						lists = arrearsStatisticalService.getCalculationList(calculationsAccountTableName, city, country, accountNumber, groupCode, null, false,dataState,null,null,false);
					}else {
						Object[] jobNumber = arrearsService.getBossUserName(this.user.getLoginName());
						String jobNumber2 = "";
						String mobile = "";
						if (jobNumber[0] != null) {
							jobNumber2 = jobNumber[0].toString();
						}
						if (jobNumber[1] != null) {
							mobile = jobNumber[1].toString();
						}
						lists = arrearsStatisticalService.getCalculationList(calculationsAccountTableName, city, country, accountNumber, groupCode, null, false,dataState,jobNumber2,mobile,true);
						System.out.println(lists.size());
					}
			}
				
			
			exportExcelToJxl(lists, AccountMsg);
			
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
	
	
	} 

	
	
	
	/**
	 * 根据登录人信息返回数据
	 */
	public void findByUser() {
		try {
			String tableName = "";
			if (getString("tableName") != null && !"".equals(getString("tableName"))) {
				tableName = getString("tableName");// 表名
			} else {
				String time = new SimpleDateFormat("yyyyMM").format(new Date());
				tableName = "Arrears_" + time;
			}
			Integer index = arrearsService.getTableIsExistence(tableName);
			Map<String, Object> map = new HashMap<String, Object>();
			if (index == 0) {
				map.put("flag", "N");
			} else {
				// 获取当前用户
				SystemUser user = this.user;
				// 获取用户权限
				List list = arrearsService.findByRowNo(user.getRowNo());
				String flag = "N";
				
				for (int i = 0; i < list.size(); i++) {
					if ((list.get(i).toString()).equals("ROLE_REPM")||(list.get(i).toString()).equals("ROLE_QJMR")) {  
						flag = "Y";
						break;
					}
				}
				// 查询所有产品信息
				List listProduct = arrearsService.findAllToProduct(tableName);
				
			//	flag = "Y";//临
				
				
				String companyCode = arrearsService.findCodeByRowNo(user.getRowNo());
				if (flag.equals("Y")) {
					// 获取公司编码
				
				//	companyCode = "00";//临
					if (companyCode.equals("00")) {
						// 查询所有分公司以及所属区县公司信息
						List<Map<String, String>> listBranchOffice = arrearsService.findAllToBranchOffice(tableName);
						Set<String> set = new HashSet<>();
						// 获取所有分公司
						for (int i = 0; i < listBranchOffice.size(); i++) {
							String branchoffice = listBranchOffice.get(i).get("BRANCHOFFICE");
							set.add(branchoffice);
						}
						// 获取分公司所对应的区县公司
						List<Map<String, Object>> listBranchOfficeAndCountry = new ArrayList<>();
						for (String string : set) {
							Map<String, Object> mapAll = new HashMap<>();
							mapAll.put("branchoffice", string);
							List<String> countrys = new ArrayList<>();
							for (int i = 0; i < listBranchOffice.size(); i++) {
								String branchoffice = listBranchOffice.get(i).get("BRANCHOFFICE");
								if (branchoffice.equals(string)) {
									countrys.add(listBranchOffice.get(i).get("COUNTRY"));
								}
							}
							mapAll.put("country", countrys);
							listBranchOfficeAndCountry.add(mapAll);
						}
						map.put("level", "0");
						map.put("flag", flag);
						map.put("all", listBranchOfficeAndCountry);
						map.put("productType", listProduct);

					} else {
						// 获取公司名称
						String companyName = arrearsService.findCompanyName(companyCode);
						// 根据公司编号查询部门名称
						List listCountry = arrearsService.findAllByBranchOffice(tableName, companyName);
						map.put("level", "1");
						map.put("flag", flag);
						map.put("country", listCountry);
						map.put("productType", listProduct);
					}
				} else {
					/*Object[] jobNumber = arrearsService.getBossUserName(this.user.getLoginName());
					String jobNumber2 = "";
					String mobile = "";
					if (jobNumber[0] != null) {
						jobNumber2 = jobNumber[0].toString();
					}
					if (jobNumber[1] != null) {
						mobile = jobNumber[1].toString();
					}*/
					// 根据公司编号查询部门名称
					//List listCountry =  arrearsStatisticalService.getCountrysByBoosMsg(jobNumber2,mobile);
					// 获取公司名称
					String companyName = arrearsService.findCompanyName(companyCode);
					// 根据公司编号查询部门名称
					List listCountry = arrearsService.findAllByBranchOffice(tableName, companyName);
					map.put("flag", flag);
					map.put("country", listCountry);
					map.put("productType", listProduct);
				}
			}
			Write(JSONHelper.Serialize(map));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	
	
	
	
	
	
	
	
	
	
	
	
	/**
	 * 生产excel并导出到本地
	 * 
	 * @param list
	 * @param type
	 * @param httpServletRequest
	 * @throws SQLException
	 * @throws IOException
	 * @throws WriteException
	 */

	public void exportExcelToJxl(List<Map<String, Object>> list,String AccountMsg) throws IOException, WriteException {
		HttpServletResponse response = ServletActionContext.getResponse();
		String excelFile = FileUpload.getFtpURL() + "exportExcelToJxl.xls";
		File file = new File(FileUpload.getFtpURL());
		if (!file.exists() && !file.isDirectory()) {
			file.mkdir();
		}
		String exportName = "ExportData_" + FileUpload.getDateToString("yyyyMMdd");
		//String year = tableName.substring(tableName.length() - 6, tableName.length() - 2);
		int y = 7;
	//	String month = tableName.substring(tableName.length() - 2, tableName.length());
	//	int m = Integer.parseInt(month);
		try {
			// 1、创建工作簿(WritableWorkbook)对象，打开excel文件，若文件不存在，则创建文件
			WritableWorkbook writeBook = Workbook.createWorkbook(new File(excelFile));
			// 2、新建工作表(sheet)对象，并声明其属于第几页
			WritableSheet firstSheet = writeBook.createSheet("欠费信息", 1);// 第一个参数为工作簿的名称，第二个参数为页数
			//String[] headers = new String[] { "分公司", "区县", "客户经理", "工号"};// this.createColumnName(tableName);
			// String[] headers = new String[] { "分公司", "区县", "客户经理", "工号",
			// "集团编码", "集团名称", "账户号码", "产品类型", "催缴时间", "催缴结果", "欠费金额", "是否能回收",
			// "回收时间" };
			String[] headers = null;
			if (AccountMsg.equals("ArrearsStatistical")) {
				
				headers = new String[] {"地点","欠费账户数","欠费金额","已处理账户数","未处理账户数","超期账户数","超期金额"};
			}else {
				headers = new String[] {"账户","分公司","区县分公司","集团名称","集团编码","产品号码","欠费金额","处理状态"};
			}
			
			
			for (int i = 0; i < headers.length; i++) {
				// 3、创建单元格(Label)对象
				Label label0 = new Label(i, 0, headers[i]);// 第一个参数指定单元格的列数、第二个参数指定单元格的行数，第三个指定写的字符串内容
				WritableFont wf2 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
				WritableFont wf3 = new WritableFont(WritableFont.ARIAL, 10, WritableFont.NO_BOLD, false, UnderlineStyle.NO_UNDERLINE, jxl.format.Colour.BLACK); // 定义格式
				// 标题栏 // 颜色
				WritableCellFormat wcfTitle = new WritableCellFormat(wf2);
				wcfTitle.setBackground(jxl.format.Colour.IVORY); // 象牙白
				wcfTitle.setBorder(jxl.format.Border.ALL, jxl.format.BorderLineStyle.THIN, jxl.format.Colour.BLACK); // BorderLineStyle边框
				wcfTitle.setVerticalAlignment(VerticalAlignment.CENTRE); // 设置垂直对齐
				wcfTitle.setAlignment(Alignment.CENTRE); // 设置垂直对齐
				// 内容栏
				WritableCellFormat wcfContent = new WritableCellFormat(wf3);
				wcfContent.setVerticalAlignment(VerticalAlignment.CENTRE); // 设置垂直对齐
				wcfContent.setAlignment(Alignment.CENTRE); // 设置垂直对齐

				CellView navCellView = new CellView();
				navCellView.setSize(100 * 50);

				label0 = new Label(i, 0, headers[i], wcfTitle); // Label(col,row,str);
				firstSheet.setColumnView(i, navCellView); // 设置col显示样式
				firstSheet.setRowView(i, 400, false); // 设置行高
				firstSheet.addCell(label0);
				if (list.size() > 0) {
					for (int i1 = 0; i1 < list.size(); i1++) {
						if (AccountMsg.equals("ArrearsStatistical")) {
							if (list.get(i1).get("SITE") != null) {// 地市
								Label label = new Label(0, i1 + 1, String.valueOf(list.get(i1).get("SITE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("ACCOUNT") != null) {
								Label label = new Label(1, i1 + 1, String.valueOf(list.get(i1).get("ACCOUNT")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("MONEY") != null) {
								Label label = new Label(2, i1 + 1, String.valueOf(list.get(i1).get("MONEY")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("PROCESSED") != null) {
								Label label = new Label(3, i1 + 1, String.valueOf(list.get(i1).get("PROCESSED")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("UNTREATED") != null) {
								Label label = new Label(4, i1 + 1, String.valueOf(list.get(i1).get("UNTREATED")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("EXCEEDDATE") != null) {
								Label label = new Label(5, i1 + 1, String.valueOf(list.get(i1).get("EXCEEDDATE")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("EXCEEDMONEY") != null) {
								Label label = new Label(6, i1 + 1, String.valueOf(list.get(i1).get("EXCEEDMONEY")), wcfContent);
								firstSheet.addCell(label);
							}
						}else {
							if (list.get(i1).get("account") != null) {// 地市
								Label label = new Label(0, i1 + 1, String.valueOf(list.get(i1).get("account")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("city") != null) {
								Label label = new Label(1, i1 + 1, String.valueOf(list.get(i1).get("city")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("country") != null) {
								Label label = new Label(2, i1 + 1, String.valueOf(list.get(i1).get("country")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("groupName") != null) {
								Label label = new Label(3, i1 + 1, String.valueOf(list.get(i1).get("groupName")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("groupCompanyCode") != null) {
								Label label = new Label(4, i1 + 1, String.valueOf(list.get(i1).get("groupCompanyCode")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("productNo") != null) {
								Label label = new Label(5, i1 + 1, String.valueOf(list.get(i1).get("productNo")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("summoney") != null) {
								Label label = new Label(6, i1 + 1, String.valueOf(list.get(i1).get("summoney")), wcfContent);
								firstSheet.addCell(label);
							}
							if (list.get(i1).get("state") != null) {
								if (String.valueOf(list.get(i1).get("state")).equals("0")) {
									Label label = new Label(7, i1 + 1, "未处理", wcfContent);
									firstSheet.addCell(label);
								}else {
									Label label = new Label(7, i1 + 1, "已处理", wcfContent);
									firstSheet.addCell(label);
								}
								
							}
						}
					}
				}
			} // 4、打开流，开始写文件
			writeBook.write();
			// 5、关闭流
			writeBook.close();

			byte[] data = FileUtil.toByteArray2(excelFile);
			String fileName = URLEncoder.encode(exportName, "UTF-8");
			response.reset();
			response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + ".xls" + "\"");
			response.addHeader("Content-Length", "" + data.length);
			response.setContentType("application/octet-stream;charset=UTF-8");
			OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
			outputStream.write(data);
			outputStream.flush();
			outputStream.close();
			response.flushBuffer();
			File fe = new File(excelFile);
			fe.delete();
		} finally {
			File fe = new File(excelFile);
			if (file.exists() && file.isDirectory()) {
				fe.delete();
			}

		}
	}
	
	
	
	
	
	
}
