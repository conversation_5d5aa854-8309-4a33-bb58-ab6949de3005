package com.xinxinsoft.action.SaveSendAction;

import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletResponse;

import net.sf.json.JSONArray;

import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.checkEntity.CheckOptiion;
import com.xinxinsoft.entity.checkEntity.SaveSend;
import com.xinxinsoft.entity.checkEntity.SupCollect;
import com.xinxinsoft.entity.checkEntity.Terminal;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.dedicatedFlow.Opinion;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.SaveSendService.SaveSendCheckService;
import com.xinxinsoft.service.SaveSendService.SaveSendCheckTimeService;
import com.xinxinsoft.service.SaveSendService.SaveSendService;
import com.xinxinsoft.service.SaveSendService.TerminalCheckTimeService;
import com.xinxinsoft.service.SaveSendService.TerminalService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

public class TerminalAction extends BaseAction{
	
	private WaitTaskService taskService;
	private SystemUserService systemUserService;
	private TerminalService terminalService;
	private TerminalCheckTimeService terminalCheckTimeService;
	
	public WaitTaskService getTaskService() {
		return taskService;
	}

	public void setTaskService(WaitTaskService taskService) {
		this.taskService = taskService;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public TerminalService getTerminalService() {
		return terminalService;
	}

	public void setTerminalService(TerminalService terminalService) {
		this.terminalService = terminalService;
	}

	public TerminalCheckTimeService getTerminalCheckTimeService() {
		return terminalCheckTimeService;
	}

	public void setTerminalCheckTimeService(
			TerminalCheckTimeService terminalCheckTimeService) {
		this.terminalCheckTimeService = terminalCheckTimeService;
	}

	//提交方法
			public void SaveSend(){
				try{
					String id = getString("id");
					String rolestate = getString("rolestate");
					//String userId = getString("userid");
					String waitId = getString("waitid");
					String attachmentId = getString("attachmentId");
					String rolewait = getString("rolewait");
					if(rolestate.equals("0")){
						String rolename = "";
						int state =0;
						if(rolewait.equals("1")){
							rolename ="【一级稽核】";
							state=1;
						}else if(rolewait.equals("2")){
							rolename ="【二级稽核】";
							state=2;
						}else if(rolewait.equals("3")){
							rolename ="【三级稽核】";
							state=3;
						}
						//改变客户经理的处理状态
						terminalService.saveObjone("【客户经理】",getStringDate(),id,user.getEmployeeName(),user.getRowNo(),Terminal.TerminalProcess,"通过",id);//改变上一步预处理信息的状态
						//保存流程跟踪信息
						CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, state+"");//查询退回人员
						SystemUser USER = null;
						USER= terminalService.getUserInfoRowNo(Integer.parseInt(checkOptiion.getStarUserID()));//这里要变
						Opinion opinion2 = new Opinion();
						opinion2.setCreationTime(new Date());
						opinion2.setStarTime(new Date());
						opinion2.setOrderId(id);
						opinion2.setPersonnel(USER.getEmployeeName());//这里要变
						opinion2.setPersonnelNo(USER.getRowNo());//这里要变
						opinion2.setIdentification(Terminal.TerminalProcess); 
						opinion2.setRole(rolename);//这里要变
						opinion2.setWhetherThrough("进行中");
						terminalService.saveOpinion(opinion2);
						Terminal saveSend = terminalService.getSaveSend(id);
						daibanonethree(checkOptiion.getStarUserID()+"",saveSend,state);//这里要变
					}else if(rolestate.equals("1")){
						terminalService.saveObjone("【一级稽核】",getStringDate(),id,user.getEmployeeName(),user.getRowNo(),Terminal.TerminalProcess,"通过",id);
						Terminal saveSend = terminalService.getCheckCall(id);
						saveSend.setState("2");
						saveSend.setDatetime(yestedayDate());
						saveSend.setRolestate(rolestate);
						saveSend.setUserid(null);
						saveSend.setErsanwheth(null);
						saveSend.setReason(null);
						if(saveSend.getWhetherReturn()!=null){
							CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "2");//查询第二步的提交人
							if(checkOptiion==null){
								saveSend.setWhetherReturn(null);
							}
						}
						terminalService.saveOrUpdateSaveSend(saveSend);
						//记录流程转向信息
						CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "1");
						if(checkOptiion==null){
							CheckOptiion optiionCheck=new CheckOptiion();
							optiionCheck.setCheckID(id);
							optiionCheck.setStarUserID(user.getRowNo()+"");
							optiionCheck.setBuzhou("1");
							terminalService.saveOpinionone(optiionCheck);
							Opinion opinion2 = new Opinion();
							opinion2.setCreationTime(new Date());
							opinion2.setStarTime(new Date());
							opinion2.setOrderId(id);
							opinion2.setPersonnel(user.getEmployeeName());
							opinion2.setPersonnelNo(user.getRowNo());
							opinion2.setIdentification(Terminal.TerminalProcess);
							opinion2.setRole("【一级稽核】");
							opinion2.setWhetherThrough("通过");
							terminalService.saveOpinion(opinion2);
						}
					}else if(rolestate.equals("2")){
						terminalService.saveObjone("【二级稽核】",getStringDate(),id,user.getEmployeeName(),user.getRowNo(),Terminal.TerminalProcess,"通过",id);
						Terminal saveSend = terminalService.getCheckCall(id);
						saveSend.setState("3");
						saveSend.setDatetime(yestedayDate());
						saveSend.setRolestate(rolestate);//步骤处理状态
						saveSend.setUserid(null);
						saveSend.setErsanwheth(null);
						saveSend.setReason(null);
						if(saveSend.getWhetherReturn()!=null){
							CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "3");//查询第三步的提交人
							if(checkOptiion==null){
								saveSend.setWhetherReturn(null);
							}
						}
						terminalService.saveOrUpdateSaveSend(saveSend);
						//记录流程转向信息
						CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "2");
						if(checkOptiion==null){
							CheckOptiion optiionCheck=new CheckOptiion();
							optiionCheck.setCheckID(id);
							optiionCheck.setStarUserID(user.getRowNo()+"");
							optiionCheck.setBuzhou("2");
							terminalService.saveOpinionone(optiionCheck);
							//保存流程跟踪信息
							Opinion opinion2 = new Opinion();
							opinion2.setCreationTime(new Date());
							opinion2.setStarTime(new Date());
							opinion2.setOrderId(id);
							opinion2.setPersonnel(user.getEmployeeName());
							opinion2.setPersonnelNo(user.getRowNo());
							opinion2.setIdentification(Terminal.TerminalProcess);
							opinion2.setRole("【二级稽核】");
							opinion2.setWhetherThrough("通过");
							terminalService.saveOpinion(opinion2);
						}
					}else if(rolestate.equals("3")){
						terminalService.saveObjone("【三级稽核】",getStringDate(),id,user.getEmployeeName(),user.getRowNo(),Terminal.TerminalProcess,"通过",id);
						Terminal saveSend = terminalService.getCheckCall(id);
						saveSend.setState("1");
						saveSend.setDatetime(yestedayDate());
						saveSend.setRolestate("0");//步骤处理状态
						saveSend.setUserid(null);
						saveSend.setWhetherReturn(null);
						saveSend.setErsanwheth(null);
						saveSend.setReason(null);
						terminalService.saveOrUpdateSaveSend(saveSend);
						//记录流程转向信息
						CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "3");
						if(checkOptiion==null){
							CheckOptiion optiionCheck=new CheckOptiion();
							optiionCheck.setCheckID(id);
							optiionCheck.setStarUserID(user.getRowNo()+"");
							optiionCheck.setBuzhou("3");
							terminalService.saveOpinionone(optiionCheck);
							//保存流程跟踪信息
							Opinion opinion2 = new Opinion();
							opinion2.setCreationTime(new Date());
							opinion2.setStarTime(new Date());
							opinion2.setOrderId(id);
							opinion2.setPersonnel(user.getEmployeeName());
							opinion2.setPersonnelNo(user.getRowNo());
							opinion2.setIdentification(Terminal.TerminalProcess);
							opinion2.setRole("【三级稽核】");
							opinion2.setWhetherThrough("通过");
							terminalService.saveOpinion(opinion2);
						}
					}
					
					if(!waitId.equals("")){
						WaitTask wait = taskService.queryWaitByTaskId(waitId);
						if(wait != null){
							taskService.updateWait(wait,this.getRequest());
						}
					}
					
					//添加附件
					if (!StringUtils.isEmpty(attachmentId)) {
						if (attachmentId != null) {
							String[] json = attachmentId.split(",");
							if (json.length > 0) {
								for (int i = 0; i < json.length; i++) {
									SingleAndAttachment sa = new SingleAndAttachment();
									sa.setOrderID(id);
									sa.setAttachmentId(json[i]);
									sa.setLink(Terminal.TerminalProcess);
									terminalService.saveSandA(sa);
								}
							}
						}
					}
					this.Write("OK");
				}catch(Exception e){
					e.printStackTrace();
					this.Write("NO");
				}
			}
			
			//退回方法
			public void TSaveSend(){
				try{
					String id = getString("id");
					String rolestate = getString("rolestate");
					String waitId = getString("waitid"); 
					String attachmentId = getString("attachmentId");
					String opiniontext = getString("opinion");//处理意见
					String reason = getString("reason");//理由，
					if(rolestate.equals("1")){
						Terminal saveSend = terminalService.getSaveSend(id);
						saveSend.setState("2");
						saveSend.setDatetime(yestedayDate());
						saveSend.setRolestate(rolestate);
						saveSend.setUserid(null);
						saveSend.setWhetherReturn("0");
						saveSend.setReason(reason);
						Terminal i=terminalService.saveOrUpdateSaveSend(saveSend);
						//保存流程跟踪信息
						Opinion opinionf= terminalService.getOpinion(id, user.getEmployeeName(), user.getRowNo(), Terminal.TerminalProcess);
						if(opinionf!=null){
							terminalService.saveObjone("【一级稽核】",getStringDate(),id,user.getEmployeeName(),user.getRowNo(),Terminal.TerminalProcess,"退回",id);//改变上一步预处理信息的状态
						}else{
							Opinion opinion2 = new Opinion();
							opinion2.setCreationTime(new Date());
							opinion2.setStarTime(new Date());
							opinion2.setOrderId(id);
							opinion2.setPersonnel(user.getEmployeeName());
							opinion2.setPersonnelNo(user.getRowNo());
							opinion2.setOpinion(opiniontext);
							opinion2.setIdentification(Terminal.TerminalProcess);
							opinion2.setRole("【一级稽核】");
							opinion2.setWhetherThrough("退回");
							opinion2.setReason(reason);
							terminalService.saveOpinion(opinion2);
						}
						//客户经理待处理信息
						SystemUser USER = null;
						USER= terminalService.getUserInfoRowNo(Integer.parseInt(saveSend.getStartUserid()));
						Opinion opinion = new Opinion();
						opinion.setCreationTime(new Date());
						opinion.setStarTime(new Date());
						opinion.setOrderId(id);
						opinion.setPersonnel(USER.getEmployeeName());
						opinion.setPersonnelNo(USER.getRowNo());
						opinion.setIdentification(Terminal.TerminalProcess);
						opinion.setRole("【客户经理】");
						opinion.setWhetherThrough("进行中");
						terminalService.saveOpinion(opinion);
						CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "1");
						if(checkOptiion==null){
							CheckOptiion optiionCheck=new CheckOptiion();
							optiionCheck.setCheckID(id);
							optiionCheck.setStarUserID(user.getRowNo()+"");
							optiionCheck.setBuzhou("1");
							terminalService.saveOpinionone(optiionCheck);
						}
						daibanonetwo(user.getRowNo()+"",saveSend,0,1);
						if(!waitId.equals("")){
							WaitTask wait = taskService.queryWaitByTaskId(waitId);
							if(wait != null){
								taskService.updateWait(wait,this.getRequest());
							}
						}
					}else if(rolestate.equals("2")){
						//二级稽核退回，根据id以及步骤状态找到一级稽核的稽核人，写入流程跟踪信息，并发送代办给一级稽核，改变数据的角色状态为1
						Terminal saveSend = terminalService.getSaveSend(id);
						saveSend.setWhetherReturn("0");
						saveSend.setErsanwheth("2");
						saveSend.setReason(reason);
						terminalService.saveOrUpdateSaveSend(saveSend);
						//记录流程转向信息
						CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "2");
						if(checkOptiion==null){
							CheckOptiion optiionCheck=new CheckOptiion();
							optiionCheck.setCheckID(id);
							optiionCheck.setStarUserID(user.getRowNo()+"");
							optiionCheck.setBuzhou("2");
							terminalService.saveOpinionone(optiionCheck);
						}
						//保存流程跟踪信息
						Opinion opinionf= terminalService.getOpinion(id, user.getEmployeeName(), user.getRowNo(), Terminal.TerminalProcess);
						if(opinionf!=null){
							terminalService.saveObjone("【二级稽核】",getStringDate(),id,user.getEmployeeName(),user.getRowNo(),Terminal.TerminalProcess,"退回",id);//改变上一步预处理信息的状态
						}else{
							//保存流程跟踪信息
							Opinion opinion2 = new Opinion();
							opinion2.setCreationTime(new Date());
							opinion2.setStarTime(new Date());
							opinion2.setOrderId(id);
							opinion2.setPersonnel(user.getEmployeeName());
							opinion2.setPersonnelNo(user.getRowNo());
							opinion2.setIdentification(Terminal.TerminalProcess);
							opinion2.setRole("【二级稽核】");
							opinion2.setWhetherThrough("退回");
							opinion2.setOpinion(opiniontext);
							opinion2.setReason(reason);
							terminalService.saveOpinion(opinion2);
						}
						//客户经理待处理信息
						SystemUser USER = null;
						USER= terminalService.getUserInfoRowNo(Integer.parseInt(saveSend.getStartUserid()));
						Opinion opinion = new Opinion();
						//opinion.setCreationTime(new Date());
						opinion.setStarTime(new Date());
						opinion.setOrderId(id);
						opinion.setPersonnel(USER.getEmployeeName());
						opinion.setPersonnelNo(USER.getRowNo());
						opinion.setIdentification(Terminal.TerminalProcess);
						opinion.setRole("【客户经理】");
						opinion.setWhetherThrough("进行中");
						terminalService.saveOpinion(opinion);
						daibanone(saveSend.getStartUserid(),saveSend,0,2);
						if(!waitId.equals("")){
							WaitTask wait = taskService.queryWaitByTaskId(waitId);
							if(wait != null){
								taskService.updateWait(wait,this.getRequest());
							}
						}
					}else if(rolestate.equals("3")){
						Terminal saveSendw = terminalService.getSaveSend(id);
						saveSendw.setWhetherReturn("0");
						saveSendw.setErsanwheth("3");
						saveSendw.setReason(reason);
						terminalService.saveOrUpdateSaveSend(saveSendw);
						CheckOptiion checkOptiion = terminalService.getcheckBuzhou(id, "3");
						if(checkOptiion==null){
							CheckOptiion optiionCheck=new CheckOptiion();
							optiionCheck.setCheckID(id);
							optiionCheck.setStarUserID(user.getRowNo()+"");
							optiionCheck.setBuzhou("3");
							terminalService.saveOpinionone(optiionCheck);
						}
						Opinion opinionf= terminalService.getOpinion(id, user.getEmployeeName(), user.getRowNo(), Terminal.TerminalProcess);
						if(opinionf!=null){
							terminalService.saveObjone("【三级稽核】",getStringDate(),id,user.getEmployeeName(),user.getRowNo(),Terminal.TerminalProcess,"退回",id);//改变上一步预处理信息的状态
						}else{
							//保存流程跟踪信息
							Opinion opinion2 = new Opinion();
							opinion2.setCreationTime(new Date());
							opinion2.setStarTime(new Date());
							opinion2.setOrderId(id);
							opinion2.setPersonnel(user.getEmployeeName());
							opinion2.setPersonnelNo(user.getRowNo());
							opinion2.setIdentification(Terminal.TerminalProcess);
							opinion2.setRole("【三级稽核】");
							opinion2.setWhetherThrough("退回");
							opinion2.setOpinion(opiniontext);
							opinion2.setReason(reason);
							terminalService.saveOpinion(opinion2);
						}
						//客户经理待处理信息
						SystemUser USER = null;
						USER= terminalService.getUserInfoRowNo(Integer.parseInt(saveSendw.getStartUserid()));
						Opinion opinion = new Opinion();
						opinion.setStarTime(new Date());
						opinion.setOrderId(id);
						opinion.setPersonnel(USER.getEmployeeName());
						opinion.setPersonnelNo(USER.getRowNo());
						opinion.setIdentification(Terminal.TerminalProcess);
						opinion.setRole("【客户经理】");
						opinion.setWhetherThrough("进行中");
						terminalService.saveOpinion(opinion);
						daibanone(saveSendw.getStartUserid(),saveSendw,0,3);
						if(!waitId.equals("")){
							WaitTask wait = taskService.queryWaitByTaskId(waitId);
							if(wait != null){
								taskService.updateWait(wait,this.getRequest());
							}
						}
					}
					//添加附件
					if (!StringUtils.isEmpty(attachmentId)) {
						if (attachmentId != null) {
							//判断是否上传了附件，获取前台提交的附件Id；
							String[] json = attachmentId.split(",");
							if (json.length > 0) {
								for (int i = 0; i < json.length; i++) {
									SingleAndAttachment sa = new SingleAndAttachment();
									sa.setOrderID(id);
									sa.setAttachmentId(json[i]);
									sa.setLink(Terminal.TerminalProcess);
									terminalService.saveSandA(sa);
									// 得到 每个对象中的属性值
								}
							}
						}
					}
					
					this.Write("OK");
				}catch(Exception e){
					e.printStackTrace();
					this.Write("NO");
				}
			}

			//待办生成
			/**
			 * 
			 * @param userId发起代办的人员
			 * @param check存送单对象
			 * @param i处理角色
			 */
			public void daibanone(String userId,Terminal check,int i,int j) {
				
				WaitTask wt = new WaitTask();
				wt.setName("[稽核]"+"[终端]"+check.getRemarks());
				wt.setCreationTime(new Date());
				wt.setUrl("jsp/checkjsp/terminal.jsp?id="
						+ check.getUuid()
						+ "&rolestate="
						+ i
						+ "&userid="
						+ user.getRowNo()
						+ "&rolewait="
						+ j);
				SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
				wt.setState(WaitTask.HANDLE);
				wt.setHandleUserId(USER.getRowNo());
				wt.setHandleUserName(USER.getEmployeeName());
				wt.setHandleLoginName(USER.getLoginName());
				wt.setCreateUserId(user.getRowNo());
				wt.setCreateUserName(user.getEmployeeName());
				wt.setCreateLoginName(user.getLoginName());
				wt.setCode(Terminal.TerminalProcess);
				wt.setEndTime(new Date());
				wt.setTaskId(check.getUuid());
				taskService.saveWait(wt,this.getRequest());
			}
			
			/**
			 * 
			 * @param userId发起代办的人员
			 * @param check存送单对象
			 * @param i处理角色
			 */
			public void daibanonethree(String userId,Terminal check,int i) {
				
				WaitTask wt = new WaitTask();
				wt.setName("[稽核]"+"[终端]"+check.getRemarks());
				wt.setCreationTime(new Date());
				wt.setUrl("jsp/checkjsp/terminal.jsp?id="
						+ check.getUuid()
						+ "&rolestate="
						+ i
						+ "&userid="
						+ user.getRowNo());
				SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
				wt.setState(WaitTask.HANDLE);
				wt.setHandleUserId(USER.getRowNo());
				wt.setHandleUserName(USER.getEmployeeName());
				wt.setHandleLoginName(USER.getLoginName());
				wt.setCreateUserId(user.getRowNo());
				wt.setCreateUserName(user.getEmployeeName());
				wt.setCreateLoginName(user.getLoginName());
				wt.setCode(Terminal.TerminalProcess);
				wt.setEndTime(new Date());
				wt.setTaskId(check.getUuid());
				taskService.saveWait(wt,this.getRequest());
			}
			
			
			
			/**
			 * 
			 * @param userId发起代办的人员
			 * @param check存送单对象
			 * @param i处理角色
			 */
			public void daibanonetwo(String userId,Terminal check,int i,int j) {
				WaitTask wt = new WaitTask();
				wt.setName("[稽核]"+"[终端]"+check.getRemarks());
				wt.setCreationTime(new Date());
				wt.setUrl("jsp/checkjsp/terminal.jsp?id="
							+ check.getUuid()
							+ "&rolestate="
							+ i
							+ "&userid="
							+ userId
							+ "&rolewait="
							+ j);
				SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(check.getStartUserid()));
				wt.setState(WaitTask.HANDLE);
				wt.setHandleUserId(USER.getRowNo());
				wt.setHandleUserName(USER.getEmployeeName());
				wt.setHandleLoginName(USER.getLoginName());
				wt.setCreateUserId(user.getRowNo());
				wt.setCreateUserName(user.getEmployeeName());
				wt.setCreateLoginName(user.getLoginName());
				wt.setCode(Terminal.TerminalProcess);
				wt.setEndTime(new Date());
				wt.setTaskId(check.getUuid());
				taskService.saveWait(wt,this.getRequest());
			}
			
			//得到前一天的时间
			public String yestedayDate(){
				Calendar calendar = Calendar.getInstance();//此时打印它获取的是系统当前时间
		        calendar.add(Calendar.DATE, -1);//得到前一天      
				String yestedayDate= new SimpleDateFormat("yyyy-MM-dd").format(new Date());
		        System.out.println(yestedayDate);
		        return yestedayDate;
			}
			
			//查询未处理页面数据
			public void getSaveSend(){
				Role role = (Role)getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getPriority());
				String number = getString("demandNumber");
				String rolestate="0";
				if(role.getName().equals("ROLE_FCMR")){//一级稽核
					rolestate="1";
				}else if(role.getName().equals("ROLE_TMLR")){//二级稽核
					rolestate="2";
				}else if(role.getName().equals("ROLE_TALR")){//三级稽核
					rolestate="3";
				}
				List<SystemDept> deptList = user.getSystemDept();
				String deptCode =deptList.get(0).getSystemCompany().getCompanyCode();//获取公司编号
				try {
					PageRequest page = new PageRequest(getRequest());
					PageResponse response = terminalService.getSaveSend(page,rolestate,deptCode,user,number);
					String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
					Write(json);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			//查询处理中页面数据
			public void getSaveSendwc(){
				Role role = (Role)getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getPriority());
				String rolestate="";
				String number = getString("demandNumber");
				String type = getString("type");
				if(role.getName().equals("ROLE_FCMR")){//一级稽核
					rolestate="【一级稽核】";
				}else if(role.getName().equals("ROLE_TMLR")){//二级稽核
					rolestate="【二级稽核】";
				}else if(role.getName().equals("ROLE_TALR")){//三级稽核
					rolestate="【三级稽核】";
				}
				try {
					PageRequest page = new PageRequest(getRequest());
					PageResponse response = terminalService.getSaveSendwc(page,rolestate,user.getRowNo(),number,type);
					String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
					Write(json);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			//查询已归档页面数据
			public void getSaveSendlc(){
				Role role = (Role)getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getPriority());
				String rolestate="0";
				String number = getString("demandNumber");
				if(role.getName().equals("ROLE_FCMR")){//一级稽核
					rolestate="1";
				}else if(role.getName().equals("ROLE_TMLR")){//二级稽核
					rolestate="2";
				}else if(role.getName().equals("ROLE_TALR")){//三级稽核
					rolestate="3";
				}
				List<SystemDept> deptList = user.getSystemDept();
				String deptCode =deptList.get(0).getSystemCompany().getCompanyCode();//获取公司编号
				try {
					PageRequest page = new PageRequest(getRequest());
					PageResponse response = terminalService.getSaveSendlc(page,rolestate,deptCode,number);
					String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
					Write(json);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
			
			//锁定稽核单
			public void lockingSaveSend(){
				try{
					String id = getString("id");
					Terminal saveSend= terminalService.getSaveSend(id);
					saveSend.setUserid(user.getRowNo()+"");
					terminalService.saveOrUpdateSaveSend(saveSend);
					this.Write("OK");
				}catch (Exception e) {
					e.printStackTrace();
					this.Write("NO");
				}
			}
			
			public void query2(){
				try{
					terminalCheckTimeService.SavesendTime();
					this.Write("OK");
				}catch (Exception e) {
					e.printStackTrace();
					this.Write("NO");
				}
			}
			
			public void query3(){
				try{
					terminalCheckTimeService.SavesendTimeB();
					this.Write("OK");
				}catch (Exception e) {
					e.printStackTrace();
					this.Write("NO");
				}
			}
			
			public static String getStringDate() {
				   Date currentTime = new Date();
				   SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				   String dateString = formatter.format(currentTime);
				   return dateString;
			}
			
			public void getReason(){
				Object list = terminalService.getReason();
				ServletResponse response = ServletActionContext.getResponse(); 
				response.setContentType("text/html;charset=utf-8");
				try {
					response.getWriter().write(list.toString());
					response.getWriter().flush();
					response.getWriter().close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			public void getSaveSendReason(){
				String id = getString("id");
				Terminal saveSend= terminalService.getSaveSend(id);
				JSONArray json = JSONArray.fromObject(saveSend);
				ServletResponse response = ServletActionContext.getResponse(); 
				response.setContentType("text/html;charset=utf-8");
				try {
					response.getWriter().write(json.toString());
					response.getWriter().flush();
					response.getWriter().close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			/**
			 * 获取附件消息
			 */
			public void fuJian() {
				String id = getString("id");
				String biaoshi = getString("biaoshi");
				List<Map<String, String>> s = terminalService.fuJian(id,biaoshi);
				Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
			}
			
			public void getAuditResult(){
				Object list = terminalService.getAuditResult();
				ServletResponse response = ServletActionContext.getResponse(); 
				response.setContentType("text/html;charset=utf-8");
				try {
					response.getWriter().write(list.toString());
					response.getWriter().flush();
					response.getWriter().close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
			
			public void getUnqualifyAndReason(){
				Object list = terminalService.getUnqualifyAndReason();
				ServletResponse response = ServletActionContext.getResponse(); 
				response.setContentType("text/html;charset=utf-8");
				try {
					response.getWriter().write(list.toString());
					response.getWriter().flush();
					response.getWriter().close();
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
			}
	
}
