package com.xinxinsoft.action.upTest;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.DecimalFormat;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import com.xinxinsoft.utils.common.DefaultAction;

public class upTestAction extends DefaultAction {

	// 收上传文件
	private File file;

	public File getFile() {
		return file;
	}

	public void setFile(File file) {
		this.file = file;
	}

	public void uptest() {
		HttpServletRequest req = getRequest();
		HttpServletResponse resp = getResponse();
		// 获取session，保存进度和上传结果，上传结果初始值为NOK，当为Ok表示上传完成
		//HttpSession session = req.getSession();
		//session.setAttribute("per", "0");
		String name = getString("name");
		System.out.println(name);
		long maxSize = file.length();
		long length = 0;
		//String per = "";
		//int load = (int) (maxSize/10);
		try {
			InputStream in = new BufferedInputStream(new FileInputStream(file));
			OutputStream out = new BufferedOutputStream(new FileOutputStream(
					req.getRealPath("") + "/upload/" + name));
			byte[] buffer = new byte[(int) maxSize];
			while ( in.read(buffer) > 0) {
				out.write(buffer);
			}
			writeText("ok");
			if (null != in) {
				in.close();
			}
			if (null != out) {
				out.close();
			}
		} catch (Exception e) {
			writeText("no");
			e.printStackTrace();
		} 
	}
	
	public void getProgress(){
		HttpServletRequest req = getRequest();
		HttpSession session = req.getSession();
		String per = (String) session.getAttribute("per");
		System.out.println(per);
	}

	public void upload(File file, HttpServletRequest req) {
		String name = file.getName();
		try {
			InputStream in = null;
			OutputStream out = null;
			try {
				in = new BufferedInputStream(new FileInputStream(file));
				out = new BufferedOutputStream(new FileOutputStream(
						req.getRealPath("") + "/upload/" + name));
				byte[] buffer = new byte[16 * 1024];
				while (in.read(buffer) > 0) {
					out.write(buffer);
					Thread.sleep(10);
				}
			} finally {
				if (null != in) {
					in.close();
				}
				if (null != out) {
					out.close();
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
		}

	}
	
}
