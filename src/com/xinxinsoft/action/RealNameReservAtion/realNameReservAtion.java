package com.xinxinsoft.action.RealNameReservAtion;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.realNameReserv.RealNameReserv;
import com.xinxinsoft.entity.realNameReserv.RealNameReservDet;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.RealNameReservService.realNameReservService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.DataCompilationUtil;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * @Description:  业务管理类
 * @Author: TX
 * @Date: 2022/4/22 10:03
 * @Version: 1.0
 */
public class realNameReservAtion extends BaseAction {

    private static final Logger logger = LoggerFactory.getLogger(realNameReservAtion.class);

    @Resource(name = "WaitTaskService")
    private WaitTaskService service;
    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "Bpms_riskoff_service")
    private Bpms_riskoff_service taskService;
    @Resource(name = "TransferJBPMUtils")
    private TransferJBPMUtils transferJBPMUtils;
    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;
    @Resource(name = "realNameReservService")
    private realNameReservService reservService;


    private File file1;

    public File getFile1() {
        return file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }

    /*
     * <AUTHOR>
     * @Date 2023/6/9 17:03
     * @Description  EXCEL模板信息导入
     **/
    public void importExcel() {
        try {
            ExcelUtil excelReader = new ExcelUtil(this.file1);
            InputStream is = new FileInputStream(this.file1);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList<>();
            HashMap<String, Object> maps;
            if (column == 3) {
                for (int i = 1; i <= map.size(); ++i) {
                    maps = new HashMap<>();
                    maps.put("phone", (map.get(i)).get(0));
                    maps.put("name", (map.get(i)).get(1));
                    maps.put("IDNumber", (map.get(i)).get(2));
                    list.add(maps);
                }
            } else if (column == 5) {
                for (int i = 1; i <= map.size(); ++i) {
                    maps = new HashMap<>();
                    maps.put("phone", (map.get(i)).get(0));
                    maps.put("name", (map.get(i)).get(1));
                    maps.put("IDNumber", (map.get(i)).get(2));
                    maps.put("currentUser", (map.get(i)).get(3));
                    maps.put("currentCode", (map.get(i)).get(4));
                    list.add(maps);
                }
            } else {
                this.Write(returnPars(-1, null, "上传文件格式错误,请下载模板文件上传!"));
            }
            if (map.size() > 0) {
                this.Write(returnPars(1, list, "上传文件成功,共导入" + list.size() + "条数据!"));
            } else {
                this.Write(returnPars(-1, null, "上传的表格数据为空,请确认后重新上传!"));
            }
        } catch (Exception var11) {
            var11.printStackTrace();
            logger.info("导入错误==>" + var11.getMessage());
            this.Write(returnPars(-1, null, "解析文件异常,请联系管理员核查!"));
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/6/9 17:03
     * @Description   模版下载
     **/
    public void excelDownload() {
        String type = getString("type");
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name;
            String filepath;
            if (type.equals("GH")) {
                name = "预约实名过户模板";
                filepath = request.getSession().getServletContext().getRealPath("/template/realNameReserv_01.xlsx");
            } else {
                name = "预约实名新增模板";
                filepath = request.getSession().getServletContext().getRealPath("/template/realNameReserv.xlsx");
            }
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
            logger.info("下载模板错误==>" + var9);
        }
    }

    /**
     * @Description  添加集团实名预约（新增）
     * <AUTHOR>
     * @Date 2022/4/22 17:00
     **/
    public void addRealNameReservation() {
        try {
            String group = getString("group");//集团编码
            String groupName = getString("groupName");//集团名称
            String groupLevel = getString("groupLevel");//集团等级

            String title = getString("title");//工单标题
            String customerMome = getString("customerMome");//描述

            String userID = getString("userID");
            String attachmentId = getString("attachmentId");

            String role = getString("role");
            String jsons = getString("jsons");
            String types = getString("types");//过户方式
            String values = getString("values");//变更渠道
            //获取下一步审批人信息
            SystemUser systemUser = systemUserService.getUserInfoRowNo(Integer.parseInt(userID));
            if (systemUser == null) {
                Write(returnPars(-1, "", "亲爱的同事,您选择的下一步处理人信息异常,请重新选择或联系管理员处理!"));
                return;
            }

            String sateTime = taskService.getNumber();
            String IBM = "";
            List<Object[]> sone = reservService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            RealNameReserv reserv = new RealNameReserv();
            reserv.setOrderNo(IBM + sateTime);//工单编码
            reserv.setOrderTitle(title);//标题
            reserv.setOrderMome(customerMome);//描述

            reserv.setGroupCode(group);
            reserv.setGroupName(groupName);
            reserv.setGroupLevel(groupLevel);

            reserv.setCreationUserRow(user.getRowNo());//创建人
            reserv.setCreationDate(new Date());//创建时间
            reserv.setModifyDate(new Date());//修改时间
            reserv.setOrderState(0);//工单状态 ：0：审批中   1：审批完成   2：被驳回   -1：作废   -2：已失效
            if (types != null && values != null) {
                reserv.setOrderType("GH"); //工单类型  XZ：新增    GH：过户
                reserv.setTransferOwnership(types);//过户方式 1：个人过户至单位   2：单位过户至个人  3：单位过户至单位
                reserv.setChangeChannel(values);////变更渠道  GZH：公众号     ZNZ：指南针
            } else {
                reserv.setOrderType("XZ"); //工单类型  XZ：新增    GH：过户
            }
            reserv = reservService.saveRealNameReserv(reserv);

            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(reserv.getId());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(RealNameReserv.REALNAMERESERV);
                            reservService.saveSandA(sa);
                        }
                    }
                }
            }

            JSONArray jsonArray = JSONArray.fromObject(jsons);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                RealNameReservDet det = new RealNameReservDet();
                det.setOrderNo(reserv.getOrderNo());//工单编号  记录主工单编号,与主工单关联
                det.setCustomerPhone(jsonObject.getString("phone"));//电话号码
                det.setCustomerName(DataCompilationUtil.encryptFromBase64(jsonObject.getString("name")));//实际使用人名称
                det.setCustomerCode(DataCompilationUtil.encryptFromBase64(jsonObject.getString("IDNumber")));//证件号码
                if (types != null && values != null) {
                    det.setCurrentUser(DataCompilationUtil.encryptFromBase64(jsonObject.getString("currentUser"))); //现使用人名称
                    det.setCurrentCode(DataCompilationUtil.encryptFromBase64(jsonObject.getString("currentCode")));//现证件号码
                    det.setChangeChannel(values);////变更渠道  GZH：公众号     ZNZ：指南针
                }
                det.setCreationUserRow(user.getRowNo());//创建人编号
                det.setCreationDate(new Date());//创建时间
                det.setModifyDate(new Date());//修改时间
                reservService.addDet(det);
            }


            //生成任务信息
            String node = "";
            if ("SZK".equals(role)) {
                node = "ROLE_SZKYWGLY";
            } else if ("SGS".equals(role)) {
                if ("ZNZ".equals(values) || "GZH".equals(values)) {
                    node = "ROLE_SSSSMZ";
                } else {
                    node = "ROLE_DSBM";
                }
            } else {
                node = "ROLE_QXYW";
            }
            Map<String, String> map = new HashMap<>();
            map.put("node", node);
            String processId;
            if ("GZH".equals(values)) {
                processId = this.transferJBPMUtils.startTransfer("RealNameReserv_03", map);//公众号流程
            } else if ("ZNZ".equals(values)) {
                processId = this.transferJBPMUtils.startTransfer("RealNameReserv_02", map);//指南针流程
            } else {
                processId = this.transferJBPMUtils.startTransfer("RealNameReserv_01", map);//实际使用人变更
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();//任务
            taskService.setBpms_riskoff_process(reserv.getId(), processId, 1, user);
            taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
            String taskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), systemUser.getRowNo(), user);//预存下一步任务
            CreatWait(reserv, taskid, systemUser, null, values, user);
            Write(returnPars(1, "", "亲爱的同事，您的工单已提交至：" + systemUser.getEmployeeName() + " 处,请等待审批！"));
        } catch (Exception e) {
            logger.info("实名预约创建异常：" + e.getMessage(), e);
            e.printStackTrace();
            Write(returnPars(-1, "", "亲爱的同事，预约工单创建异常【" + e.getMessage() + "】，请联系管理员处理！"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * @Description  工单审批提交方法
     * <AUTHOR>
     * @Date 2022/7/13 15:20
     **/
    public void handRealNameReserv() {
        try {
            String id = getString("id");//id
            String opinion = getString("opinion");//审批意见
            Integer userid = getInteger("userId");//下一步处理用户id
            String phone = getString("phone");//手机端当前审批人
            String juese = getString("juese");//下一步处理人职务

            SystemUser systemUser = systemUserService.getByUserInfoRowNo(userid);

            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                try {
                    user = systemUserService.querUsers(user.getLoginName());
                } catch (Exception e) {
                    e.printStackTrace();
                    Write(returnPars(-1, "", "亲爱的同事，app提交工单失败[" + e.getMessage() + "],请联系管理员处理！"));
                    throw new RuntimeException("app参数信息异常");
                }
            }

            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
            WaitTask wt = taskService.queryWaitByTaskId(riskoff_task.getId(), user.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            } else if (!riskoff_task.getOper_no().equals(user.getRowNo())) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已提交至:" + riskoff_task.getOper_name() + " 处,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            }
            if (wt == null) {
                Write(returnPars(-1, "", "亲爱的同事,待办信息异常,联系系统管理员"));
                throw new RuntimeException("待办信息异常");
            }
            RealNameReserv reserv = reservService.getRealNameReservById(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();  //获取流程任务表信息
            if ("ZNZ".equals(reserv.getChangeChannel())) {
                if ("ROLE_SSSSMZ".equals(juese)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("node", "大于");
                    jbpmUtil.completeTask(task.getId(), map, juese);//结束流程环节
                } else if ("ROLE_DSSM".equals(juese)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("node", "大于");
                    jbpmUtil.completeTask(task.getId(), map, juese);//结束流程环节
                } else if ("ROLE_SZKSM".equals(juese)) {
                    Map<String, Object> map = new HashMap<>();
                    map.put("node", "大于");
                    jbpmUtil.completeTask(task.getId(), map, juese);//结束流程环节
                } else {
                    jbpmUtil.completeTask(task.getId());//结束流程环节
                }
            } else {
                jbpmUtil.completeTask(task.getId());//结束流程环节
            }

            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            taskService.updateBpms_riskoff_task(opinion, 2, riskoff_task.getId());

            String taskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", tasktwo.getActivityName(), userid, user);

            service.updateWait(wt, this.getRequest());
            CreatWait(reserv, taskid, systemUser, null, reserv.getChangeChannel(), user);
            Write(returnPars(1, "", "亲爱的同事，工单处理成功已提交至:" + systemUser.getEmployeeName() + " 处！"));
        } catch (Exception e) {
            logger.error("集团实名预约提交异常：" + e.getMessage(), e);
            e.printStackTrace();
            Write(returnPars(-1, "", "亲爱的同事，工单提交失败[" + e.getMessage() + "],请联系管理员处理！"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * @Description  集团实名预约审批方法
     * <AUTHOR>
     * @Date 2022/4/24 14:01
     **/
    public void completeRealNameReserv() {
        try {
            String id = getString("id");
            String type = getString("type");
            String opinion = getString("opinion");
            String phone = getString("phone");

            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                try {
                    user = systemUserService.querUsers(user.getLoginName());
                } catch (Exception e) {
                    e.printStackTrace();
                    Write(returnPars(-1, "", "亲爱的同事，app提交工单失败[" + e.getMessage() + "],请联系管理员处理！"));
                    throw new RuntimeException("app参数信息异常");
                }
            }

            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
            WaitTask wait = taskService.queryWaitByTaskId(riskoff_task.getId(), user.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            } else if (!riskoff_task.getOper_no().equals(user.getRowNo())) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已提交至:" + riskoff_task.getOper_name() + " 处,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            }
            if (wait == null) {
                Write(returnPars(-1, "", "亲爱的同事,待办信息异常,联系系统管理员"));
                throw new RuntimeException("待办信息异常");
            }

            RealNameReserv reserv = reservService.getRealNameReservById(id);
            if (type.equals("WANCHENG")) {
                reserv.setOrderState(3);//审批完成待确认
                SystemUser systemUser = systemUserService.getByUserInfoRowNo(reserv.getCreationUserRow());
                String taskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), null, 1, "SH", systemUser.getEmployeeName(), systemUser.getRowNo(), user);//预存下一步任务
                CreatWait(reserv, taskid, systemUser, type, reserv.getChangeChannel(), user);
            } else if (type.equals("TUIHUI")) {
                reserv.setOrderState(2);
                SystemUser systemUser = systemUserService.getByUserInfoRowNo(reserv.getCreationUserRow());
                String taskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), null, 1, "SH", systemUser.getEmployeeName(), systemUser.getRowNo(), user);//预存下一步任务
                CreatWait(reserv, taskid, systemUser, type, reserv.getChangeChannel(), user);
            } else {
                throw new Exception("提交信息异常：" + type);
            }
            reservService.updateRealNameReserv(reserv);

            if (process.getProcess_sign().indexOf("RealNameReserv_01") != -1) {
                jbpmUtil.deleteProcessInstance(process.getProcess_sign());
            }
            service.updateWait(wait, this.getRequest());

            taskService.updateBpms_riskoff_task(opinion, 2, riskoff_task.getId());
            Write(returnPars(1, "", "亲爱的同事,工单处理完成!"));
        } catch (Exception e) {
            logger.error("集团实名预约处理异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事,工单处理异常[" + e.getMessage() + "],请联系管理员处理!"));
            throw new RuntimeException("事务回滚");
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/6/14 10:48
     * @Description  确认方法
     **/
    public void confirmRealNameReserv() {
        try {
            String id = getString("id");
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                try {
                    user = systemUserService.querUsers(user.getLoginName());
                } catch (Exception e) {
                    e.printStackTrace();
                    Write(returnPars(-1, "", "亲爱的同事，app提交工单失败[" + e.getMessage() + "],请联系管理员处理！"));
                    throw new RuntimeException("app参数信息异常");
                }
            }
            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            WaitTask wait = taskService.queryWaitByTaskId(riskoff_task.getId(), user.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            } else if (!riskoff_task.getOper_no().equals(user.getRowNo())) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已提交至:" + riskoff_task.getOper_name() + " 处,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            }
            if (wait == null) {
                Write(returnPars(-1, "", "亲爱的同事,待办信息异常,联系系统管理员"));
                throw new RuntimeException("待办信息异常");
            }

            RealNameReserv reserv = reservService.getRealNameReservById(id);
            reserv.setOrderState(1);//1：审批完成
            reservService.updateRealNameReserv(reserv);


            // 获取当前时间
            Date date = new Date();
            Calendar calendar = new GregorianCalendar();
            calendar.setTime(date);
            // 把日期往后增加2天,整数  往后推,负数往前移动
            calendar.add(Calendar.DATE, 2);
            Date time = calendar.getTime();

            List<RealNameReservDet> list = reservService.getDetList(reserv.getOrderNo());//明细数据

            Result result = null;
            if (reserv.getOrderType().equals("GH")) {//过户才推送
                result = reservService.pushBoss(list, user.getMobile(), time);//推送接口
                logger.info("实名制身份数据推送接口返回参数：" + result);
            }

            for (RealNameReservDet det : list) {
                if (reserv.getOrderType().equals("GH")) {//过户才推送
                    if (result != null && result.getCode() == 200) {
                        JSONObject data = JSONObject.fromObject(result.getData());
                        if (data.getString("rsCode").equals("0")) {
                            det.setPushStatus("Y");//推送成功
                        } else {
                            det.setPushStatus("N");//推送失败
                            det.setPushMsg(data.toString());//推送失败错误信息
                        }
                    }
                }
                det.setEffectiveDate(time);//失效时间 审批完成后48小时
                reservService.updateDet(det);
            }

            taskService.updatebpmsRiskoffProcess(reserv.getId(), 2);

            service.updateWait(wait, this.getRequest());

            taskService.updateBpms_riskoff_task("完成", 2, riskoff_task.getId());
            Write(returnPars(1, null, "亲爱的同事，工单处理完成！失效时间为：" + getDateToString(calendar.getTime()) + "，请及时办理业务!"));
        } catch (Exception e) {
            logger.error("集团实名预约处理异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事,工单处理异常[" + e.getMessage() + "],请联系管理员处理!"));
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * @Description  集团实名预约作废方法
     * <AUTHOR>
     * @Date 2022/4/24 14:34
     **/
    public void giveUpToVoid() {
        try {
            String id = getString("id");
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                try {
                    user = systemUserService.querUsers(user.getLoginName());
                } catch (Exception e) {
                    e.printStackTrace();
                    Write(returnPars(-1, "", "亲爱的同事，app提交工单失败[" + e.getMessage() + "],请联系管理员处理！"));
                    throw new RuntimeException("app参数信息异常");
                }
            }
            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            WaitTask wait = taskService.queryWaitByTaskId(riskoff_task.getId(), user.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            } else if (!riskoff_task.getOper_no().equals(user.getRowNo())) {
                Write(returnPars(-1, "", "亲爱的同事,当前待办已提交至:" + riskoff_task.getOper_name() + " 处,请关闭页面不要重复提交！"));
                throw new RuntimeException("任务信息异常");
            }
            if (wait == null) {
                Write(returnPars(-1, "", "亲爱的同事,待办信息异常,联系系统管理员"));
                throw new RuntimeException("待办信息异常");
            }

            RealNameReserv reserv = reservService.getRealNameReservById(id);
            reserv.setOrderState(-1);
            reservService.updateRealNameReserv(reserv);

            service.updateWait(wait, this.getRequest());
            taskService.updatebpmsRiskoffProcess(reserv.getId(), 2);
            taskService.updateBpms_riskoff_task("工单作废", 2, riskoff_task.getId());
            Write(returnPars(1, "", "亲爱的同事,工单处理完成!"));
        } catch (Exception e) {
            logger.error("集团实名预约作废异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事,工单处理异常[" + e.getMessage() + "],请联系管理员处理!"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * @Description  根据工单id查询工单信息
     * <AUTHOR>
     * @Date 2022/7/28 17:10
     **/
    public void findRealNameReservOrder() {
        try {
            String orderNo = getString("orderNo");
            String id = getString("id");
            String phone = getString("phone");
            RealNameReserv reserv;
            if (orderNo != null && !orderNo.equals("")) {
                reserv = reservService.getRealNameReservByOrderNo(orderNo);//新单
            } else {
                reserv = reservService.getRealNameReservById(id);//老单 或 列表
            }
            List<RealNameReservDet> list = reservService.getDetList(reserv.getOrderNo());//明细数据  数量
            Map<String, Object> USER = reservService.getCountyByUserID(reserv.getCreationUserRow());
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(reserv.getId());//根据ID查询流程
            String role = "";
            List<String> buttenList = new ArrayList<>();
            String str = process.getProcess_sign().substring(0, process.getProcess_sign().indexOf("."));
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                try {
                    user = systemUserService.querUsers(user.getLoginName());
                } catch (Exception e) {
                    e.printStackTrace();
                    Write(returnPars(-1, "", "亲爱的同事，app提交工单失败[" + e.getMessage() + "],请联系管理员处理！"));
                    throw new RuntimeException("app参数信息异常");
                }
            }
            if ("RealNameReserv_01".equals(str) || "RealNameReserv_03".equals(str)) {//新增流程
                switch (reserv.getOrderState()) {
                    case 0:  //审批中
                        Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();  //获取流程任务表信息
                        if (task != null) {
                            Set<String> nextLine = jbpmUtil.findOutComesByTaskId(task.getId());
                            if (nextLine.toArray()[0].toString().equals("END")) {
                                buttenList.add("AGREE"); //同意
                            } else {
                                role = nextLine.toArray()[0].toString();
                                buttenList.add("SUBMIT");//提交
                            }
                        } else {
                            buttenList.add("AGREE");    //同意
                        }
                        buttenList.add("RETURN");       //退回
                        break;
                    case 2:
                        buttenList.add("GIVE");         //作废
                        break;
                    case 3:
                        buttenList.add("CONFIRM");      //确认
                        break;
                    default:
                        buttenList.add("CLOSE");        //关闭
                        break;
                }
            } else if ("RealNameReserv_02".equals(str)) {
                //角色
                switch (reserv.getOrderState()) {
                    case 0:  //审批中
                        List<SystemDept> deptList = user.getSystemDept();
                        String code = deptList.get(0).getSystemCompany().getCompanyCode();
                        Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();  //获取流程任务表信息
                        TransferCitiesData transferCitiesData = reservService.getTransferCitiesData(code, task.getActivityName());
                        if (task != null) {
                            Set<String> nextLine = jbpmUtil.findOutComesByTaskId(task.getId());
                            if (nextLine.toArray()[0].toString().equals("END")) {
                                buttenList.add("AGREE"); //同意
                            } else {
                                if (task.getActivityName().equals("三级经理(区县公司)") && list.size() < Integer.valueOf(transferCitiesData.getAmount())) {
                                    buttenList.add("AGREE");    //同意
                                } else if (task.getActivityName().equals("三级经理(市公司)") && list.size() < Integer.valueOf(transferCitiesData.getAmount())) {
                                    buttenList.add("AGREE");    //同意
                                } else if (task.getActivityName().equals("省重客业务管理室经理") && list.size() < Integer.valueOf(transferCitiesData.getAmount())) {
                                    buttenList.add("AGREE");    //同意
                                } else {
                                    role = nextLine.toArray()[0].toString();
                                    buttenList.add("SUBMIT");//提交
                                }
                            }
                        } else {
                            buttenList.add("AGREE");    //同意
                        }
                        buttenList.add("RETURN");       //退回
                        break;
                    case 2:
                        buttenList.add("GIVE");         //作废
                        break;
                    case 3:
                        buttenList.add("CONFIRM");      //确认
                        break;
                    default:
                        buttenList.add("CLOSE");        //关闭
                        break;
                }
            }
            Map<String, Object> retMap = new HashMap<>();
            retMap.put("RealNameReserv", reserv);
            for (RealNameReservDet det : list) {
                det.setCustomerName(DataCompilationUtil.decryptFromBase64(det.getCustomerName()));
                det.setCustomerCode(DataCompilationUtil.decryptFromBase64(det.getCustomerCode()));
                if (reserv.getOrderType().equals("GH")) {
                    det.setCurrentUser(DataCompilationUtil.decryptFromBase64(det.getCurrentUser()));
                    det.setCurrentCode(DataCompilationUtil.decryptFromBase64(det.getCurrentCode()));
                }
            }
            retMap.put("RealNameReservDet", list);
            retMap.put("USER", USER);
            retMap.put("buttenList", buttenList);
            retMap.put("role", role);
            Write(returnPars(1, retMap, "查询成功！"));
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("集团账户关系工单查询异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，工单查询异常：【" + e.getMessage() + "】，请联系管理员处理"));
        }
    }

    public void CreatWait(RealNameReserv reserv, String taskid, SystemUser systemUser, String type, String values, SystemUser user) {
        WaitTask waitTask = new WaitTask();

        waitTask.setCreationTime(new Date());// 代办生成时间
        if ("GZH".equals(values) || "ZNZ".equals(values)) {
            if (type == null) {
                waitTask.setName("[实名预约过户申请]" + reserv.getOrderTitle());//待办名称
            } else if (type.equals("WANCHENG")) {
                waitTask.setName("[实名预约过户申请完成]" + reserv.getOrderTitle());//待办名称
            } else if (type.equals("TUIHUI")) {
                waitTask.setName("[实名预约过户申请退回]" + reserv.getOrderTitle());//待办名称
            }
            waitTask.setUrl("jsp/realNameReservation/handleTransferOwnership.jsp");//过户审批页面
        } else {
            if (type == null) {
                waitTask.setName("[实名预约新增申请]" + reserv.getOrderTitle());//待办名称
            } else if (type.equals("WANCHENG")) {
                waitTask.setName("[实名预约新增申请完成]" + reserv.getOrderTitle());//待办名称
            } else if (type.equals("TUIHUI")) {
                waitTask.setName("[实名预约新增申请退回]" + reserv.getOrderTitle());//待办名称
            }
            waitTask.setUrl("jsp/realNameReservation/handleRealNameReserv.jsp");//新增审批页面
        }
        waitTask.setState(WaitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(systemUser.getRowNo());// 处理人id
        waitTask.setHandleUserName(systemUser.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(systemUser.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode("SMYY");//标识
        waitTask.setTaskId(taskid);//待办ID
        waitTask.setOrderNo(reserv.getOrderNo());//工单编码
        this.service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * @Description  查询列表信息
     * <AUTHOR>
     * @Date 2022/4/24 16:58
     **/
    public void findAllByPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            String orderNo = getString("orderNo");        //工单编号
            String gruopCode = getString("gruopCode");        //集团编号
            String orderTitle = getString("orderTitle");        //工单名称
            String orderState = getString("orderState");    //工单状态
            String stateCreatorDate = getString("stateCreatorDate");    //工单创建时间   开始时间
            String endCreatorDate = getString("endCreatorDate");        //工单创建时间   结束时间

            String tableType = getString("tableType");        //列表状态
            page = reservService.findAllByPage(page, orderNo, gruopCode, orderTitle, orderState, stateCreatorDate, endCreatorDate, tableType, user);
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            this.Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }

    }

    /**
     * @Description  根据工单编号查询详细信息
     * <AUTHOR>
     * @Date 2022/4/22 17:10
     **/
    public void findRealNameReserv() {
        try {
            String id = getString("id");
            RealNameReserv reserv = reservService.getRealNameReservById(id);
            if (reserv == null) {
                Write(returnPars(-1, "", "亲爱的同事，查询工单详细信息失败【单号异常】，请联系管理员处理！"));
                return;
            }
            List<RealNameReservDet> list = reservService.getDetList(reserv.getOrderNo());//明细数据
            if (reserv == null) {
                Write(returnPars(-1, "", "亲爱的同事，查询工单明细信息失败【单号异常】，请联系管理员处理！"));
                return;
            }
            SystemUser systemUser = systemUserService.getByUserInfoRowNo(reserv.getCreationUserRow());
            if (systemUser == null) {
                Write(returnPars(-1, "", "亲爱的同事，查询工单详细信息失败【创建工号异常】，请联系管理员处理！"));
                return;
            }

            Map<String, Object> returnMap = new HashMap<>();
            returnMap.put("reserv", reserv);
            for (RealNameReservDet det : list) {
                det.setCustomerName(DataCompilationUtil.decryptFromBase64(det.getCustomerName()));
                det.setCustomerCode(DataCompilationUtil.decryptFromBase64(det.getCustomerCode()));
                if (reserv.getOrderType() != null && reserv.getOrderType().equals("GH")) {
                    det.setCurrentUser(DataCompilationUtil.decryptFromBase64(det.getCurrentUser()));
                    det.setCurrentCode(DataCompilationUtil.decryptFromBase64(det.getCurrentCode()));
                }
            }
            returnMap.put("RealNameReservDet", list);
            returnMap.put("systemUser", systemUser);
            Write(returnPars(1, returnMap, "查询成功！"));
        } catch (Exception e) {
            logger.info("查询详细信息异常：" + e.getMessage(), e);
            e.printStackTrace();
            Write(returnPars(-1, "", "亲爱的同事，查询详细信息异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }

    /**
     * @Description  根据实际使用人名称和电话查询预约信息
     * <AUTHOR>
     * @Date 2022/4/24 17:28
     **/
    public void qryRealNameCertify() {
        JSONObject outmsg = new JSONObject();
        JSONObject detailmsg = new JSONObject();
        try {
//            String content = "{\n" +
//                    "    \"ROOT\": {\n" +
//                    "    \"BODY\": {\n" +
//                    "        \"CUSTOMER_PHONE\": \"18388300670\",\n" +
//                    "        \"CUSTOMER_CODE\": \"530302199803040334\",\n" +
//                    "        \"TRANSACTION_ID\": \"Certify_20220825103013718\"\n" +
//                    "    }\n" +
//                    "    }\n" +
//                    "}";

            String content = UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("集团实名预约查询传递参数异常");
                detailmsg.put("RETURN_CODE", "400");
                detailmsg.put("RETURN_MSG", "集团实名预约查询传递参数异常");
                detailmsg.put("RETURN_TIME", taskService.getNumber());
                Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
            } else {
                logger.info("集团实名预约查询接口调用数据信息:" + content);
                String json = URLDecoder.decode(content, "UTF-8");
                JSONObject obj = JSONObject.fromObject(json);
                JSONObject root = obj.getJSONObject("ROOT");
                JSONObject data = root.getJSONObject("BODY");
                outmsg.put("HEADER", root.getJSONObject("HEADER"));
                if (data.has("TRANSACTION_ID")) {
                    detailmsg.put("TRANSACTION_ID", data.getString("TRANSACTION_ID"));
                } else {
                    detailmsg.put("RETURN_CODE", "400");
                    detailmsg.put("RETURN_MSG", "集团实名预约查询传递参数异常,TRANSACTION_ID为空");
                    detailmsg.put("RETURN_TIME", taskService.getNumber());
                    outmsg.put("BODY", detailmsg);
                    Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
                    return;
                }

                if (data.has("CUSTOMER_PHONE")) {
                    String customerPhone = data.getString("CUSTOMER_PHONE");
                    List<RealNameReservDet> reservs = reservService.queryReservAtion(customerPhone);
                    if (reservs.size() > 0) {
                        Map<String, Object> map = new HashMap<>();
                        map.put("ORDER_NUMBER", reservs.get(0).getOrderNo());
                        map.put("RESERVATION_STATUS", true);

                        detailmsg.put("RETURN_CODE", "200");
                        detailmsg.put("RETURN_MSG", "处理成功");
                        detailmsg.put("RETURN_TIME", taskService.getNumber());
                        detailmsg.put("OUT_DATA", map);
                        outmsg.put("BODY", detailmsg);
                        Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
                    } else {
                        Map<String, Object> map = new HashMap<>();
                        map.put("RESERVATION_STATUS", false);

                        detailmsg.put("RETURN_CODE", "200");
                        detailmsg.put("RETURN_MSG", "处理成功");
                        detailmsg.put("RETURN_TIME", taskService.getNumber());
                        detailmsg.put("OUT_DATA", map);
                        outmsg.put("BODY", detailmsg);
                        Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
                    }
                } else {
                    detailmsg.put("RETURN_CODE", "400");
                    detailmsg.put("RETURN_MSG", "集团实名预约查询传递参数异常,缺少关键信息");
                    detailmsg.put("RETURN_TIME", taskService.getNumber());
                    outmsg.put("BODY", detailmsg);
                    Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
                }
            }
        } catch (Exception e) {
            logger.info("集团实名预约查询接口异常：" + e.getMessage(), e);
            e.printStackTrace();
            detailmsg.put("RETURN_CODE", "400");
            detailmsg.put("RETURN_MSG", "集团实名预约查询接口异常：" + e);
            detailmsg.put("RETURN_TIME", taskService.getNumber());
            outmsg.put("BODY", detailmsg);
            Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
        }
    }

    /**
     * @Description  根据工单编号和模块标识查询附件信息
     * <AUTHOR>
     * @Date 2022/4/22 17:27
     **/
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = reservService.fuJian(id, biaoshi);
        Write(JSONHelper.Serialize(s));
    }

    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description:  返回参数生成
     */
    private static String returnPars(int state, Object data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    /**
     * @param currentTime 时间字符串
     * @return java.util.Date
     * @Description  根据时间字符串获取时间
     * <AUTHOR>
     * @Date 2022/4/22 14:38
     **/
    public static Date getStringToDate(String currentTime) {
        Date dateString = null;
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            dateString = formatter.parse(currentTime);
        } catch (ParseException e) {
            //  Auto-generated catch block
            e.printStackTrace();
        }
        return dateString;
    }

    /**
     * @param currentTime 时间
     * @return java.lang.String
     * @Description  根据时间获取时间字符串
     * <AUTHOR>
     * @Date 2022/4/22 14:37
     **/
    public static String getDateToString(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @Description  根据当前用户查询用户角色信息
     * <AUTHOR>
     * @Date 2022/8/29 9:55
     **/
    public void getJudgeRole() {
        try {
            List<Map<String, Object>> maps = reservService.getCountyByUserID(String.valueOf(user.getRowNo()));
            if (maps.size() > 0) {
                Write(returnPars(1, maps, "查询成功！"));
            } else {
                Write(returnPars(-1, "", "亲爱的同事，用户信息异常【" + user.getRowNo() + "】,请联系管理员处理！ "));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("根据用户编号查询用户角色信息异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，查询用户角色信息异常：" + e.getMessage() + " ,请联系管理员处理！"));
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/6/13 14:50
     * @Description  获取实名预约号码信息
     **/
    public void getRealNameReservInfo() {
        try {
//            String request_info = "{\n" +
//                    "    \"ROOT\": {\n" +
//                    "        \"PHONE\": \"18388300670\",\n" +
//                    "        \"ID_NUMBER\": \"530302199803040334\",\n" +
//                    "        \"CHANGE_CHANNEL\": \"GZH\"\n" +
//                    "    }\n" +
//                    "}";
            String request_info = UrlConnection.getRequestData(getRequest());
            logger.info("getRealNameReservInfo：接收数据：==>" + request_info);
            if ("".equals(request_info)) {
                this.Write("{\"RETURN_CODE\":\"-1\",\"RETURN_MSG\":\"请求参数异常\",\"DETAIL_MSG\":\"请求参数异常\"}");
                return;
            }
            request_info = URLDecoder.decode(request_info, "UTF-8");
            JSONObject request_info_objec = JSONObject.fromObject(request_info);
            JSONObject root = JSONObject.fromObject(request_info_objec.getString("ROOT"));
            List<Map<String, Object>> info = reservService.getRealNameReservInfo(root.getString("PHONE"), DataCompilationUtil.encryptFromBase64(root.getString("ID_NUMBER")), root.getString("CHANGE_CHANNEL"));
            if (info.size() > 0) {
                for (Map<String, Object> map : info) {
                    map.put("CUSTOMER_CODE", DataCompilationUtil.decryptFromBase64(map.get("CUSTOMER_CODE").toString()));
                    map.put("CUSTOMER_NAME", DataCompilationUtil.decryptFromBase64(map.get("CUSTOMER_NAME").toString()));
                }
                Write(returnPars(1, info, "查询成功！"));
            } else {
                Write(returnPars(-1, "", "亲爱的同事，未获查询到实名预约号码信息"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("获取实名预约号码信息异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，查询实名预约号码信息异常：" + e.getMessage()));
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/6/14 15:41
     * @Description  校验是否已申请
     **/
    public void verifyRealNameReserv() {
        try {
            String phone = getString("phone");//手机号
            String idNumber = getString("idNumber");//身份证
            String changeChannel = getString("changeChannel");//变更渠道
            List<Map<String, Object>> info = reservService.getRealNameReservInfo(phone, idNumber, changeChannel);
            if (info.size() > 0) {
                RealNameReserv order = reservService.getRealNameReservByOrderNo(info.get(0).get("ORDER_NO").toString());
                if (order.getOrderState() != 1) {
                    Write(returnPars(1, null, null));
                } else {
                    Write(returnPars(-1, phone, null));
                }

            } else {
                Write(returnPars(1, null, null));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("校验是否已申请错误：" + e.getMessage(), e);
            Write(returnPars(-1, "", "校验是否已申请异常：" + e.getMessage()));
        }
    }


    public void findAllByPageApp() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            String orderNo = getString("orderNo");        //工单编号
            String gruopCode = getString("gruopCode");        //集团编号
            String orderTitle = getString("orderTitle");        //工单名称
            String orderState = getString("orderState");    //工单状态
            String stateCreatorDate = getString("stateCreatorDate");    //工单创建时间   开始时间
            String endCreatorDate = getString("endCreatorDate");        //工单创建时间   结束时间
            String tableType = getString("tableType");        //列表状态

            String phone = getString("phone");//电话号码
            if ("".equals(phone) || phone == null) {
                this.Write("参数有误！无电话号码");
                return;
            }


            SystemUser user = new SystemUser();
            user = systemUserService.getUserByPhone(phone);
            user = systemUserService.querUsers(user.getLoginName());

            if ("2".equals(tableType)) {
                page = reservService.getRealNameReservPageApp(page, user);
            } else {
                page = reservService.findAllByPage(page, orderNo, gruopCode, orderTitle, orderState, stateCreatorDate, endCreatorDate, tableType, user);
            }
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            this.Write(json);
        } catch (Exception e) {
            logger.error("");
            e.printStackTrace();
            this.Write("列表加载失败！");
        }

    }

    public void processtracking() {
        try {
            String id = this.getString("id");
            List<Bpms_riskoff_task> RedList = this.taskService.getPublicEntityTaskList(id);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(RedList));
        } catch (Exception e) {
            logger.info("查询任务信息错误==>" + e);
            e.printStackTrace();
        }
    }

    public void highRiskCheckQueries() {
        try {
            if (StringUtils.isEmpty(user.getBossUserName())){
                Write(returnPars(-1,"","亲爱的同事，由于当前用户未配置BOSS工号无法完成校验，请联系管理员配置后再进行操作"));
                return;
            }

            String idIccId = this.getString("idIccId");
            String idType = this.getString("idType");
            String custName = this.getString("custName");
            Result result = reservService.highRiskChecks(idIccId,idType,custName,user.getBossUserName());
            if (result.getCode()==ResultCode.SUCCESS.code()){
                JSONObject json = JSONObject.fromObject(result.getData());
                JSONObject root = JSONObject.fromObject(json.get("ROOT"));
                if ("0".equals(root.getString("RETURN_CODE"))){
                    JSONObject outData = root.getJSONObject("OUT_DATA");
                    if (outData==null){
                        Write(returnPars(-1, "", "亲爱的同事, 调用账户信息查询接口返回数据异常, 请联系管理员处理!"));
                    }else {
                        if (outData.getJSONArray("DATA").isEmpty()){
                            Write(returnPars(-1, "", "亲爱的同事, 调用账户信息查询接口返回数据异常, 请联系管理员处理!"));
                            return;
                        }
                        JSONObject data = outData.getJSONArray("DATA").getJSONObject(0);
                        Write(returnPars(1,data,"校验完成"));
                    }
                }else {
                    Write(returnPars(-1,"",root.getString("RETURN_MSG")));
                }
            }else {
                Write(returnPars(-1,"","亲爱的同事,调用账户信息查询接口异常["+result.getMessage()+"],请联系管理员处理!"));
            }

        } catch (Exception e) {
            e.printStackTrace();
            logger.info("高风险校验查询接口异常:"+e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事,高风险校验查询接口查询异常["+e.getMessage()+"],请联系管理员处理!"));
        }
    }
}
