package com.xinxinsoft.action.preinvIoTCardAction;

import com.jcraft.jsch.ChannelSftp;
import com.jcraft.jsch.Session;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.preinvIoTCard.PreinvIoTCardOrder;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.preinvIoTCardService.PreinvIoTCardService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.SftpUtils;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.*;

public class PreinvIoTCardAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(PreinvIoTCardAction.class);

    @Resource(name = "PreinvIoTCardService")
    private PreinvIoTCardService ioTService;
    @Resource(name = "Bpms_riskoff_service")
    private Bpms_riskoff_service taskService;
    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;
    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "WaitTaskService")
    private WaitTaskService service;
    @Resource(name = "TransferJBPMUtils")
    private TransferJBPMUtils transferJBPMUtils;

    private File file;
    private String fileName;

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /*
     * <AUTHOR>
     * @Date 2023/10/31 16:09
     * @Description 发起流程
     **/
    public void add() {
        try {
            String attachmentId = getString("attachmentId");//附件id
            String role = getString("role");//角色权限
            Integer userId = getInteger("userId");//下一步任务人id
            String orderName = getString("orderName");//工单名称
            String orderMemo = getString("orderMemo");//工单描述
            String groupCode = getString("groupCode");//归属集团编号
            String groupName = getString("groupName");//归属集团名称
            String customerType = getString("customerType");//客户类型
            String customerNumber = getString("customerNumber");//客户编码
            String acctCode = getString("acctCode");//账户编码
            String amount = getString("amount");//预开发票额度
            String fileName = getString("fileName");//附件文件名

            if (pushFTPS(fileName)) {
                Write(returnPars(-1, "", "附件上传到sftp服务器失败,请联系管理员处理!"));
                throw new RuntimeException("事务回滚");
            }

            // 获取下一步处理人信息
            SystemUser USER = systemUserService.getUserInfoRowNo(userId);
            if (USER == null) {
                throw new Exception("处理人信息异常：" + userId);
            }

            List<Map<String, String>> companyList = ioTService.getVwUserinfoByRowno(user.getRowNo());
            String county = "";
            if (companyList.size() > 0) {
                for (int a = 0; a < companyList.size(); a++) {
                    if (companyList.get(a).get("ISMAINDPT").equals("true")) {
                        county = companyList.get(a).get("COMPANY_NAME");
                    }
                }
            } else {
                Write(returnPars(-1, "", "用户视图未查询到对应地市信息，请与管理员联系完整信息后重试！"));
                throw new RuntimeException("事务回滚");
            }

            //获取申请编码前面的字母
            String IBM = "";
            List<Object[]> sone = ioTService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            PreinvIoTCardOrder order = new PreinvIoTCardOrder();
            order.setOrderNo(IBM + getUnlockedNumber());
            order.setOrderName(orderName);
            order.setOrderState("1");
            order.setOrderMemo(orderMemo);
            order.setCreatorId(String.valueOf(user.getRowNo()));
            order.setCreatorName(user.getEmployeeName());
            order.setCreatorDate(new Date());
            order.setPushState("0");
            order.setCompanyName(county);
            order.setGroupCode(groupCode);
            order.setGroupName(groupName);
            order.setCustomerType(customerType);
            order.setCustomerNumber(customerNumber);
            order.setAcctCode(acctCode);
            order.setFileName(fileName);
            order.setAmount(yuanToFen(amount));
            order.setCzState("0");
            PreinvIoTCardOrder cardOrder = ioTService.saveOrUpdateOrder(order);

            //关联附件
            if (!StringUtils.isEmpty(attachmentId)) {
                // 判断是否上传了附件，获取前台提交的附件Id；
                String[] json = attachmentId.split(",");
                if (json.length > 0) {
                    for (int i = 0; i < json.length; i++) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(cardOrder.getId());
                        sa.setAttachmentId(json[i]);
                        sa.setLink("WLWKYUKP");
                        taskService.saveSingleAndAttachment(sa);
                    }
                }
            }


            Map<String, String> map = new HashMap<>();
            map.put("decisionKey", "APPLY");
            map.put("decisionValue", role);
            String processId = transferJBPMUtils.startTransfer("PreinvApplyDetermineProcessFinal", map);

            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            taskService.setBpms_riskoff_process(cardOrder.getId(), processId, 1, user);//流程保存
            taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
            String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), userId, user);//预存下一步任务
            Integer type = commitBackLogData(cardOrder, USER, user, taskid, "");
            if (type != 1) {
                throw new Exception("生成EIP待办失败");
            }
            Write(returnPars(1, "", "亲爱的同事，审批工单已提交至：" + USER.getEmployeeName() + " 处，请等待审批！"));

        } catch (Exception e) {
            logger.error("预开票物联网配额申请发起流程错误:" + e.getMessage(), e);
            Write(returnPars(-1, "", "操作失败!" + e));
            throw new RuntimeException("事务回滚");
        }
    }

    public void getPageList() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String tableType = getString("tableType");    //菜单类型 0:审批中  1:我创建  2:我经手
            String orderNo = getString("orderNo");        //工单编号
            String orderName = getString("orderName");    //工单名称
            String batchUnitId = getString("batchUnitId");//集团编号
            String orderType = getString("orderType");  //工单状态
            String stateCreatorDate = getString("stateCreatorDate");
            String endCreatorDate = getString("endCreatorDate");
            page = ioTService.findAllByPage(page, orderNo, batchUnitId, orderName, orderType, stateCreatorDate, endCreatorDate, tableType, user);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(page));
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/10/31 16:19
     * @Description 获取工单信息
     **/
    public void getInformation() {
        Map<String, Object> map = new HashMap<>();
        String orderNo = getString("orderNo");
        String type = getString("type");
        try {
            PreinvIoTCardOrder order = ioTService.queryByOrderNo(orderNo);
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(order.getId());
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(order.getId());//任务信息
            List<String> buttons = new ArrayList<>();
            if (task != null) {
                String activityName = task.getActivityName();
                List<SystemDept> deptList = systemUserService.getDeptListByRowNo(Integer.parseInt(order.getCreatorId()));
                String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();
                TransferCitiesData transferCitiesData = ioTService.getTransferCitiesData(companyCode, activityName);
                buttons.add("TH");
                buttons.add("TJ");
                switch (activityName) {
                    case "区县业务管理员":
                        if (checkMoney(order, transferCitiesData)) {
                            buttons.remove("TJ");
                            buttons.add("TY");
                        }
                        break;
                    case "区县政企部主任":
                        if (checkMoney(order, transferCitiesData)) {
                            buttons.remove("TJ");
                            buttons.add("TY");
                        }
                        break;
                    case "区县分管经理":
                        buttons.add("ZS");
                        if (checkMoney(order, transferCitiesData)) {
                            buttons.remove("TJ");
                            buttons.add("TY");
                        }
                        break;
                    case "市公司客户经理室经理":
                        if (checkMoney(order, transferCitiesData)) {
                            buttons.remove("TJ");
                            buttons.add("TY");
                        }
                        break;
                    case "市公司业务管理员":
                        buttons.add("ZS");
                        break;
                    case "市公司业务管理室经理":
                        buttons.add("ZS");
                        break;
                    case "市公司政企部经理":
                        buttons.add("ZS");
                        if (checkMoney(order, transferCitiesData)) {
                            buttons.remove("TJ");
                            buttons.add("TY");
                        }
                        break;
                    case "省重客客户经理室经理":
                        if (checkMoney(order, transferCitiesData)) {
                            buttons.remove("TJ");
                            buttons.add("TY");
                        }
                        break;
                    case "市公司领导":
                    case "省重客分管经理":
                        buttons.remove("TJ");
                        buttons.add("TY");
                        break;
                    default:
                        break;
                }
                //给其他审批人添加转审按钮
                if (activityName.contains("其他")) {
                    buttons.add("ZS");
                }
            } else {
                if (order.getOrderState().equals("2")) {
                    buttons.add("toVoid");//作废
                } else {
                    buttons.add("TS");//推送
                    buttons.add("BD");//闭单
                }
            }


            //获取线条值
            String taskname = findTransitionSrv(process.getProcess_sign()); //根据流程id查询任务名称
            map.put("ROLE", taskname);
            map.put("ORDER", order);//工单详情
            map.put("TASKLIST", taskList);//任务详情
            if (!"CK".equals(type)) {
                map.put("BUTTONS", buttons); //按钮
            }
            Write(returnPars(1, JSONHelper.SerializeWithNeedAnnotationDateFormat(map), "操作成功"));
        } catch (Exception e) {
            logger.error("物联网卡预开票额度申请数据查询异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请数据查询异常【" + e.getMessage() + "】，请联系管理员处理！"));
            throw new RuntimeException("事务回滚");
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/1 18:55
     * @Description 提交  转审
     **/
    public void handleWorkOrder() {
        try {
            String role = getString("role");//下一步流程任务
            Integer userId = getInteger("userId");//用户id
            String id = getString("id");//工单id
            String opinion = getString("opinion");//审批意见
            String waitId = getString("waitId");//待办id
            String taskId = getString("taskId");//任务id
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            PreinvIoTCardOrder order = ioTService.queryById(id);

            SystemUser USER = systemUserService.getUserInfoRowNo(userId);// 获取下一步处理人信息
            if (USER == null) {
                throw new Exception("处理人信息异常：" + userId);
            }

            Bpms_riskoff_task bpms_riskoff_task = taskService.getBpms_riskoff_task(taskId);

            /**
             * gcy 处理重复提交问题
             * 状态 0退回 1处理中 2处理完成 -1作废
             */
            if (bpms_riskoff_task.getStatus() == 2) {
                WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                service.updateWait(wt, this.getRequest());
                Write(returnPars(-1, "", "亲爱的同事，你操作的待办任务已处理过了，请勿重复操作，如待办还存在（EIP）请尝试刷新网页或联系系统管理员。"));
                return;
            }

            Map<String, String> map = new HashMap<String, String>();

            if ("区县分管经理".equals(task.getActivityName())) {
                map.put("decisionKey", "ROLE_DSBM");
                map.put("decisionValue", "NO");
                jbpmUtil.completeTask(task.getId(), map);// 流程流转
            } else if ("区县业务管理员".equals(task.getActivityName())) {
                map.put("decisionKey", "ROLE_QXDM");
                map.put("decisionValue", "YES");
                jbpmUtil.completeTask(task.getId(), map);// 流程流转
            } else if ("区县政企部主任".equals(task.getActivityName())) {
                map.put("decisionKey", "ROLE_QXSM");
                map.put("decisionValue", "YES");
                jbpmUtil.completeTask(task.getId(), map);// 流程流转
            } else if ("省重客客户经理室经理".equals(task.getActivityName())) {
                map.put("decisionKey", "ROLE_SZKSM");
                map.put("decisionValue", "NO");
                jbpmUtil.completeTask(task.getId(), map);// 流程流转
            } else if ("市公司政企部经理".equals(task.getActivityName())) {
                map.put("decisionKey", "ROLE_DSSM");
                map.put("decisionValue", "NO");
                if (role.equals("ALL")) {
                    jbpmUtil.completeTask(task.getId(), role);
                } else {
                    jbpmUtil.completeTask(task.getId(), map, role);
                }
            } else if ("市公司客户经理室经理".equals(task.getActivityName())) {
                map.put("node", "大于");
                jbpmUtil.completeTask(task.getId(), map, role);
            } else {
                jbpmUtil.completeTask(task.getId(), role);
            }

            //获取下一步任务
            Task nextTask = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            String taskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), null,
                    1, "SH", nextTask.getActivityName(), userId, user);//预存下一步任务
            if (taskid != null && !"".equals(taskid)) {
                Integer type = commitBackLogData(order, USER, user, taskid, "");
                if (type != 1) {//不等于1的时候待办生成异常
                    Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请流程处理异常【代办生成失败】，请联系管理员处理！"));
                } else {
                    if (waitId != null && !"".equals(waitId) && !"undefined".equals(waitId)) {
                        WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                        if (wt != null) {
                            service.updateWait(wt, this.getRequest());
                        } else {
                            Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请流程处理异常【未查询到待办】，请联系管理员处理！"));
                            return;
                        }
                    }
                    Bpms_riskoff_task bpmTask = taskService.getBpms_riskoff_task(taskId);
                    if (bpmTask != null) {
                        taskService.updateBpms_riskoff_task(opinion, 2, taskId);
                    } else {
                        Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请流程处理异常【工单提交任务查询为空】，请联系管理员处理！"));
                        return;
                    }
                    Write(returnPars(1, "", "亲爱的同事，工单处理完成，已提交至：" + USER.getEmployeeName() + " 处。"));
                }
            } else {
                Write(returnPars(1, "", "亲爱的同事，工单处理完成。"));
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.info("物联网卡预开票额度申请流程处理异常：" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请流程处理异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/1 19:25
     * @Description 同意
     **/
    public void completeWorkOrder() {
        try {
            String id = getString("id");// 账户信息id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 审批意见
            String taskId = getString("taskId");
            PreinvIoTCardOrder order = ioTService.queryById(id);
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
            jbpmUtil.deleteProcessInstance(process.getProcess_sign());// 删除流程

            if (waitId != null && !"".equals(waitId) && !"undefined".equals(waitId)) {
                WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请完成工单异常【未查询到待办】，请联系系统管理员！"));
                    return;
                }
            }
            Bpms_riskoff_task bpmTask = taskService.getBpms_riskoff_task(taskId);
            if (bpmTask != null) {
                taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            } else {
                Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请完成工单异常【工单提交任务查询为空】，请联系系统管理员！"));
                return;
            }
            String taskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), null, 1, "SH", "客户经理", Integer.parseInt(order.getCreatorId()), user);//预存下一步任务
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(order.getCreatorId()));// 获取下一步处理人信息
            if (USER == null) {
                Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请完成工单异常【处理人信息异常】，请联系系统管理员！"));
            }
            Integer type = commitBackLogData(order, USER, user, taskid, "");
            if (type != 1) {//不等于1的时候待办生成异常
                Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请完成工单异常【代办生成失败】，请联系系统管理员！"));
            } else {
                Write(returnPars(1, "", "亲爱的同事，物联网卡预开票额度申请申请审批完成。"));
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.info("物联网卡预开票额度申请工单完成处理异常:" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，物联网卡预开票额度申请工单完成处理异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/1 19:37
     * @Description 闭单
     **/
    public void readEndWorkOrder() {
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            String taskId = getString("taskId");
            Bpms_riskoff_task bpmTask = taskService.getBpms_riskoff_task(taskId);
            PreinvIoTCardOrder order = ioTService.queryById(id);
            order.setOrderState("0");
            ioTService.saveOrUpdateOrder(order);
            if (bpmTask != null) {
                taskService.updateBpms_riskoff_task("", 2, taskId);
            } else {
                throw new Exception("任务查询为空");
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                service.updateWait(wait, this.getRequest());
            } else {
                throw new Exception("未查询到待办");
            }
            Write(returnPars(1, "", "亲爱的同事，待办闭单完成！"));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.info(e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，闭单处理异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/1 20:03
     * @Description 退回工单
     **/
    public void returnWorkOrder() {
        try {
            String id = getString("id");//开票id
            String opinion = getString("opinion");//退回意见
            String waitId = getString("waitId");
            String taskId = getString("taskId");
            PreinvIoTCardOrder order = ioTService.queryById(id);
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
            Bpms_riskoff_task bpmTask = taskService.getBpms_riskoff_task(taskId);
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(order.getCreatorId()));// 获取下一步处理人信息
            if (USER == null) {
                throw new Exception("处理人信息异常");
            }

            Integer waitType = commitBackLogData(order, USER, user, taskId, "return");
            if (waitType != 1) {//不等于1的时候待办生成异常
                throw new Exception("代办生成失败");
            } else {
                if (bpmTask != null) {
                    taskService.updateBpms_riskoff_task(opinion, 0, taskId);
                } else {
                    throw new Exception("工单提交任务查询为空");
                }
                WaitTask wait = service.queryWaitByTaskId(waitId);
                if (wait != null) {
                    service.updateWait(wait, this.getRequest());
                } else {
                    throw new Exception("未查询到待办");
                }
                jbpmUtil.deleteProcessInstance(process.getProcess_sign());//删除流程
                order.setOrderState("2");
                ioTService.saveOrUpdateOrder(order);
            }
            Write(returnPars(1, "", "亲爱的同事，工单已退回发起人：" + USER.getEmployeeName() + " 处！"));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.info(e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，有价卡工单退回异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/1 20:05
     * @Description 作废
     **/
    public void toVoidWorkOrder() {
        try {
            String id = getString("id");//开票id
            String waitId = getString("waitId");
            PreinvIoTCardOrder order = ioTService.queryById(id);
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                service.updateWait(wait, this.getRequest());
            } else {
                Write(returnPars(-1, "", "未查询到待办"));
                return;
            }
            order.setOrderState("-1");
            ioTService.saveOrUpdateOrder(order);
            Write(returnPars(1, "", "亲爱的同事，工单已作废，请确认！"));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.info("有价卡工单作废异常:" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，有价卡工单作废异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/11/2 10:18
     * @Description 推送按钮
     **/
    public void pushWorkOrder() {
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            String taskId = getString("taskId");
            Bpms_riskoff_task bpmTask = taskService.getBpms_riskoff_task(taskId);
            PreinvIoTCardOrder order = ioTService.queryById(id);


            Map<String, Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
            JSONObject obj = JSONObject.fromObject(json);
            //推送接口
            Result result = ioTService.com_sitech_tsn_service_ISTSNPubSndSvc_pubCall(user.getBossUserName(), obj.getString("GROUP_ID"), order);
            if (result.getCode() == 200) {
                JSONObject data = JSONObject.fromObject(result.getData());
                JSONObject root = data.getJSONObject("ROOT");
                if (root.getString("RETURN_CODE").equals("0")) {//推送成功
                    order.setOrderState("0");
                    order.setPushState("1");
                    order.setPushTime(new Date());
                    ioTService.saveOrUpdateOrder(order);

                    if (bpmTask != null) {
                        taskService.updateBpms_riskoff_task("", 2, taskId);
                    } else {
                        throw new Exception("任务查询为空");
                    }
                    WaitTask wait = service.queryWaitByTaskId(waitId);
                    if (wait != null) {
                        service.updateWait(wait, this.getRequest());
                    } else {
                        throw new Exception("未查询到待办");
                    }
                    Write(returnPars(1, "", "亲爱的同事，工单推送成功！"));
                } else {//失败
                    order.setPushState("-1");
                    order.setPushMsg(root.getString("RETURN_MSG"));
                    order.setPushTime(new Date());
                    ioTService.saveOrUpdateOrder(order);
                    Write(returnPars(-1, "", "推送失败:" + root.getString("RETURN_MSG")));
                }
            } else {
                order.setPushState("-1");
                order.setPushMsg(result.getMessage());
                order.setPushTime(new Date());
                ioTService.saveOrUpdateOrder(order);
                Write(returnPars(-1, "", "推送失败:" + result.getMessage()));
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.error("物联网卡预开票额度申请推送失败:" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，推送处理异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/11/2 14:43
     * @Description 冲正
     **/
    public void chongZhengWorkOrder() {
        try {
            String orderNo = getString("orderNo");
            String description = getString("description");
            PreinvIoTCardOrder order = ioTService.queryByOrderNo(orderNo);
            Map<String, Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
            JSONObject obj = JSONObject.fromObject(json);
            //冲正接口
            Result result = ioTService.com_sitech_tsn_service_ISTSNPubSndSvc_pubCall(user.getBossUserName(), obj.getString("GROUP_ID"), order, description);
            if (result.getCode() == 200) {
                JSONObject data = JSONObject.fromObject(result.getData());
                JSONObject root = data.getJSONObject("ROOT");
                if (root.getString("RETURN_CODE").equals("0")) {//推送成功
                    order.setCzState("1");
                    order.setCzTime(new Date());
                    ioTService.saveOrUpdateOrder(order);
                    Write(returnPars(1, "", "亲爱的同事，工单冲正成功！"));
                } else {//失败
                    order.setCzState("-1");
                    order.setCzMsg(root.getString("RETURN_MSG"));
                    order.setCzTime(new Date());
                    ioTService.saveOrUpdateOrder(order);
                    Write(returnPars(-1, "", "冲正失败:" + root.getString("RETURN_MSG")));
                }
            } else {
                order.setCzState("-1");
                order.setCzMsg(result.getMessage());
                order.setCzTime(new Date());
                ioTService.saveOrUpdateOrder(order);
                Write(returnPars(-1, "", "冲正失败:" + result.getMessage()));
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.error("物联网卡预开票额度申请冲正失败:" + e.getMessage(), e);
            Write(returnPars(-1, "", "亲爱的同事，冲正处理异常【" + e.getMessage() + "】，请联系管理员处理！"));
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/11/2 17:16
     * @Description 附件上传保存本地
     **/
    public void uploadFiles() {
        try {
//            String ftpUrl = "F:/FTPTWO/";//本地
            String ftpUrl = "/eomapp_new0_LC/UploadFile/IoTCard/";//正式
            if (FileUpload.upload(ftpUrl, file, fileName)) {
                Write(returnPars(1, "", "亲爱的同事，附件上传成功!"));
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "亲爱的同事，附件上传处理异常【" + e.getMessage() + "】，请联系管理员处理！"));
            logger.error("附件上传保存本地错误:" + e.getMessage(), e);
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/11/2 16:04
     * @Description 附件上传到sftp服务器
     **/
    public boolean pushFTPS(String fileName) {
        /*连接SFTP服务器*/
        Session session = SftpUtils.getSession("************", 22, "oneboss", "xNf7@Ycy");
        ChannelSftp channel = SftpUtils.getConnect(session);
        try {
            //本地附件路径
            String ftpUrl = "/eomapp_new0_LC/UploadFile/IoTCard/";
            //上传
            InputStream inti = new FileInputStream(ftpUrl + fileName);
            SftpUtils.upload("/onebosslogbak/ftpfile/upload/CTBS/PrepInvoice", inti, fileName, channel);
            return false;
        } catch (Exception e) {
            logger.error("附件上传到sftp服务器错误:" + e.getMessage(), e);
            return true;
        } finally {
            SftpUtils.disconnect(channel, session);
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/11/1 14:30
     * @Description 根据集团查询集团下的账户信息
     **/
    public void getAccountQuery() {
        String groupCode = getString("groupCode");
        Result result = ClaimFundsOpenSrv.getInstance().getUnitInfo(user.getBossUserName(), groupCode);
//        Result result = ClaimFundsOpenSrv.getInstance().getUnitInfo("njbO11", "**********");
        logger.info("请求getUnitInfo接口结果===>" + result.toString());
        if (ResultCode.SUCCESS.code() == result.getCode()) {
            JSONObject resObj = JSONObject.fromObject(result.getData());
            Write(resObj.getString("ROOT"));
        } else {
            Write("NO");
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/10/31 17:17
     * @Description 金额计算
     **/
    private boolean checkMoney(PreinvIoTCardOrder vcd, TransferCitiesData transferCitiesData) {
        String amout = BigDecimal.valueOf(Long.parseLong(vcd.getAmount())).divide(new BigDecimal(100), 2, BigDecimal.ROUND_HALF_UP).toString();
        return transferCitiesData != null && (Double.parseDouble(amout) <= Double.parseDouble(transferCitiesData.getAmount()));
    }


    /**
     * @author: liyang
     * @date: 2021/1/20 14:46
     * @Version: 1.0
     * @param: 流程id
     * @return: 返回下一步任务集合信息
     * @Description: 获取线条任务值
     */
    public String findTransitionSrv(String pid) {
        Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
        if (task != null) {
            Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
            JSONArray jArray = new JSONArray();
            //获取多条任务参数
            for (String outcome : setlist) {
                JSONObject obj = new JSONObject();
                obj.put("transitionName", outcome);
                jArray.add(obj);
            }
            JSONObject objtwo = new JSONObject();
            objtwo.put("processNode", task.getName());
            objtwo.put("nextStep", jArray.toString());
            return com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(objtwo);
        } else {
            return "";
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/10/31 16:08
     * @Description 生成退回代办
     **/
    public Integer returnBackLogData(PreinvIoTCardOrder order, SystemUser USER, SystemUser user, String taskid) {
        try {
            WaitTask waitTask = new WaitTask();
            waitTask.setName("[物联网预开票额度申请退回]" + order.getOrderName());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/preinvIoTCard/handlePreinvIoTCard.jsp");
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode("WLWKYKP");
            waitTask.setTaskId(taskid);
            waitTask.setOrderNo(order.getOrderNo());
            this.service.saveWait(waitTask, this.getRequest());
            return 1;
        } catch (Exception e) {
            return -1;
        }
    }

    public Integer commitBackLogData(PreinvIoTCardOrder order, SystemUser USER, SystemUser user, String taskid, String type) {
        try {
            WaitTask waitTask = new WaitTask();
            if ("return".equals(type)) {
                waitTask.setName("[物联网预开票额度申请退回]" + order.getOrderName());
            } else {
                waitTask.setName("[物联网预开票额度申请]" + order.getOrderName());
            }
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/preinvIoTCard/handlePreinvIoTCard.jsp");
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode("WLWKYKP");
            waitTask.setTaskId(taskid);
            waitTask.setOrderNo(order.getOrderNo());
            this.service.saveWait(waitTask, this.getRequest());
            return 1;
        } catch (Exception e) {
            return -1;
        }
    }

    private static String returnPars(int state, String data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    /**
     * 获取年月日 + 10位随机数
     *
     * @return
     */
    public static String getUnlockedNumber() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(new Date());
        Random r = new Random();
        for (int i = 0; i < 9; i++) {
            dateString += r.nextInt(10);
        }
        return dateString;
    }

    /*
     * <AUTHOR>
     * @Date 2023/11/1 18:33
     * @Description 元转分
     **/
    public String yuanToFen(String amount) {
        // 去除金额中的逗号和空格
        amount = amount.replace(",", "").replace(" ", "");

        // 将金额转换为 BigDecimal 类型
        BigDecimal yuan = new BigDecimal(amount);

        // 将金额乘以 100，然后转换为整数类型的值
        BigDecimal fen = yuan.multiply(new BigDecimal(100));
        int fenInt = fen.intValue();

        return String.valueOf(fenInt);
    }

    /*
     * <AUTHOR>
     * @Date 2023/11/1 18:33
     * @Description 获取附件
     **/
    public void getFiles() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = ioTService.getFiles(id, biaoshi);
        Write(com.xinxinsoft.utils.easyh.JSONHelper.Serialize(s));
    }

}
