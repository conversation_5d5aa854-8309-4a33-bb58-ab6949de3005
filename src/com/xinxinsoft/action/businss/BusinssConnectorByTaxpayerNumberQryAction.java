package com.xinxinsoft.action.businss;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import net.sf.json.JSONObject;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.utils.UrlConnection;

public class BusinssConnectorByTaxpayerNumberQryAction extends BaseAction {
	/**
	 * 调用boss接口查询账户信息 根据账户
	 */
	public void findPreinvApplyDetByContractNo() {
		/*Map<String, String> tempRes = new HashMap<>();
		tempRes.put("res", "{\"RETURN_CODE\":0,\"RETURN_MSG\":\"ok!\",\"USER_MSG\":\"处理成功!\",\"DETAIL_MSG\":\"OK!\",\"PROMPT_MSG\":\"\",\"OUT_DATA\":{\"FLAG\":\"N\",\"CONTRACT_NO\":11604106441316,\"CONTRACTATT_TYPE\":\"集团成员帐户\"}}");
		tempRes.put("flag", "Y");
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(tempRes));
		if (true) {
			return;
		}*/
		//String ESB_URL="http://10.113.171.38:51000/esbWS/rest/";//正式
		String ESB_URL="http://10.113.183.51:52000/esbWS/rest/";//测试
		String groupCode = getString("groupCode");
		String additional = getString("additional");
		String contractNo = getString("contractNo");
		String url = ESB_URL + "sTaxpayerNumberQry";
		JSONObject object = new JSONObject();
		System.out.println("集团280：" + groupCode);
		object.put("UNIT_ID", Long.parseLong(groupCode));
		object.put("BUSI_TYPE", "B");
		object.put("CONTRACT_NO",contractNo);//账户
		if (additional!=null&&additional.equals("")==false) {
			JSONObject json = JSONObject.fromObject(additional);
			for (Object str:json.keySet()) {
			     String key = (String)str;
			     object.put(key, json.getString(key));
			}
		}
		String netJson = setParamObj1(object, user.getMobile());
		String jsonString = UrlConnection.responseGBK(url, netJson);
		JSONObject jsthree = JSONObject.fromObject(jsonString);
		String datatwo = jsthree.getString("res");
		System.out.println("这是返回数据解析结果"+datatwo);
		JSONObject jsone = JSONObject.fromObject(datatwo);
		System.out.println(1);
		JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
		System.out.println(2);
		Map<String, String> result = new HashMap<>();
		if ("0".equals(jstwo.getString("RETURN_CODE"))) {
		   result.put("flag", "Y");
		   result.put("res", jstwo.toString());
		}else {
			result.put("flag", "N");
			if ("40639020001".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "集团编码不存在");
			}else if ("40639020002".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "申请工号与集团客户经理不一致");
			}else if ("40639020003".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "集团账户数量超过查询限制，请去BOSS查询");
			}else if ("40639020004".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "该集团没有客户信息");
			}else if ("40639020005".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "对应多个纳税人识别号，请先清理数据");
			}
			result.put("more", jstwo.getString("RETURN_MSG"));
			result.put("ALL", jsonString);
		}
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(result));
	}

	/**
	 * 调用boss接口查询账户信息 根据集团
	 */
	public void findPreinvApplyDetByGroupCode() {
		//String ESB_URL="http://10.113.171.38:51000/esbWS/rest/";//正式
		String ESB_URL="http://10.113.183.51:52000/esbWS/rest/";//测试
		String groupCode = getString("groupCode");
		String additional = getString("additional");
		String url = ESB_URL + "sTaxpayerNumberQry";
		JSONObject object = new JSONObject();
		System.out.println("集团280：" + groupCode);
		object.put("UNIT_ID", Long.parseLong(groupCode));
		object.put("BUSI_TYPE", "A");
		object.put("LOGIN_NO",this.user.getBossUserName());//工号
		if (additional!=null&&additional.equals("")==false) {
			JSONObject json = JSONObject.fromObject(additional);
			for (Object str:json.keySet()) {
			     String key = (String)str;
			     object.put(key, json.getString(key));
			}
		}
		String netJson = setParamObj1(object, user.getMobile());
		String jsonString = UrlConnection.responseGBK(url, netJson);
		JSONObject jsthree = JSONObject.fromObject(jsonString);
		String datatwo = jsthree.getString("res");
		System.out.println("这是返回数据解析结果"+datatwo);
		JSONObject jsone = JSONObject.fromObject(datatwo);
		JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
		Map<String, String> result = new HashMap<>();
		if ("0".equals(jstwo.getString("RETURN_CODE"))) {
		   result.put("flag", "Y");
		   result.put("res", jstwo.toString());
		}else {
			result.put("flag", "N");
			if ("40639020001".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "集团编码不存在");
			}else if ("40639020002".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "申请工号与集团客户经理不一致");
			}else if ("40639020003".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "集团账户数量超过查询限制，请去BOSS查询");
			}else if ("40639020004".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "该集团没有客户信息");
			}else if ("40639020005".equals(jstwo.getString("RETURN_CODE"))) {
				result.put("errorMsg", "对应多个纳税人识别号，请先清理数据");
			}
			result.put("more", jstwo.getString("RETURN_MSG"));
			result.put("ALL", jsonString);
		}
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(result));
	}
	
	protected String setParamObj1(JSONObject body, String phone) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "10");
		routing.put("ROUTE_VALUE", phone);
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}

}
