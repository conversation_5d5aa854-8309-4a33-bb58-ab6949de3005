package com.xinxinsoft.action.businss;

import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.Set;

import org.apache.commons.lang.StringUtils;
import org.jbpm.api.Execution;
import org.jbpm.api.ProcessInstance;
import org.jbpm.api.task.Task;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import bsh.This;

import com.sun.org.apache.xpath.internal.operations.And;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.PreinvApply.PreinvApply;
import com.xinxinsoft.entity.businss.BusinessCycleProcess;
import com.xinxinsoft.entity.businss.BusinessCycleTask;
import com.xinxinsoft.entity.businss.Businsscycle;
import com.xinxinsoft.entity.businss.DelayAccount;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.transfer.TransferAccountInformation;
import com.xinxinsoft.entity.transfer.TransferInformation;
import com.xinxinsoft.entity.transfer.TransferTask;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.businss.BusinssService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

import org.jbpm.pvm.internal.model.ActivityImpl;
import org.jbpm.pvm.internal.model.ProcessDefinitionImpl;
import org.jbpm.pvm.internal.model.TransitionImpl;

public class BusinssAction extends BaseAction {
	private BusinssService businssService; //业务周期
	private WaitTaskService	waitTaskService; //代办管理
	private SystemUserService systemUserService; //用户管理
	private JbpmUtil jbpmUtil;//流程管理
	
	public WaitTaskService getWaitTaskService() {
		return waitTaskService;
	}

	public void setWaitTaskService(WaitTaskService waitTaskService) {
		this.waitTaskService = waitTaskService;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public JbpmUtil getJbpmUtil() {
		return jbpmUtil;
	}

	public void setJbpmUtil(JbpmUtil jbpmUtil) {
		this.jbpmUtil = jbpmUtil;
	}

	public BusinssService getBusinssService() {
		return businssService;
	}

	public void setBusinssService(BusinssService businssService) {
		this.businssService = businssService;
	}
	/**
	 * 获取附件消息
	 */
	public void fuJian() {
		String id = getString("id");
		String biaoshi = getString("biaoshi");
		List<Map<String, String>> s = businssService.fuJian(id,biaoshi);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}
	/**
	 * 根据state分页查询      state:-1(作废) 1(已审批) 2(审批中) 3(已退回)
	 */
	public void findByPage() {
		try {
			String goupCoding = getString("goupCoding");
			String goupName = getString("goupName");
			String state = getString("state");
			int defIndex = 1;
			int defSize = 10;
			if (getInteger("pageNo")!=null&&getInteger("pageNo")!=0) {
				defIndex = getInteger("pageNo");
			}
			if (getInteger("pageSize")!=null&&getInteger("pageSize")!=0) {
				defSize = getInteger("pageSize");
			}
			LayuiPage layuiPage  =  new LayuiPage(defIndex,defSize);
			 layuiPage  = businssService.getBusinsscyleByPage(layuiPage, goupCoding, goupName, state);
			String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(layuiPage);
			Write(json);
		}
		catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	
	/**
	 * 获取申请账户信息   刚刚修改过
	 */
	public void DelayAccountList(){
		try{
			String id = getString("id");
			if (getString("id")!=null&&getString("id")!="") {
				//Businsscycle businsscycle = businssService.getBusinsscyle(id);
				List<DelayAccount> delayAccounts =businssService.getDelayAccountList(id); 
				Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(delayAccounts));
			}else {
				Write("id 不可为空");
			}
			
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	/**
	 * 根据id获取业务周期对象
	 */
	public void findById() {
		try {
			String id = getString("id");
			Map<String, Object> map = businssService.findByRowNo(id);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(map));
		}
		catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 根据ID查询跟踪处理
	 * @return
	 */
	public void processtracking() {
		String id = getString("id"); //获取业务周期id,在根据id获取相关的流程，在用流程id去获取相关的节点信息
		BusinessCycleProcess businessCycleProcess = businssService.getBusinessCycleProcessByBusinssId(id);
	//	List<TransferTask> p = tInformationService.processtracking(id);
		List<BusinessCycleTask> p = businssService.processtask(businessCycleProcess.getId());
		
		
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
	}
	/**
	 * 发起流程
	 */
	public void add() {
		try {
			String role = getString("role");
			String goupCoding = getString("goupCoding"); //集团280
			String groupName = getString("groupName");//集团名称
			String userid = getString("userid");//下一个处理人id
			String attachmentId = getString("attachmentId");// 附件id,多个之间用,隔开
			String orderID = getString("orderId");
			String proposer =String.valueOf(user.getRowNo());// 申请人id
			String taxpayer=  getString("taxpayer");//纳税人识别号
			String applyTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());//申请时间
			String updateTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());//更新时间
			String appMemo = getString("appMemo");//原因
		//	String state = getString("state");//状态 是否审批:-1(作废) 1(已审批) 2(审批中) 3(已退回)
			String businessCycleName = getString("businessCycleName");//业务周期管理标题
		//	String city  = user.getSystemDept().get(0).getCompanyCode();//获取地市code好拼装业务周期单号
			String IBM = "";
			List<Object[]> sone = businssService.getbumen(user.getRowNo());
			for (int i = 0; i < sone.size(); i++) {
				IBM = (String) sone.get(i)[2];
			}
			String sateTime = getStringDatetwo(new Date());
			String batchNO = IBM + "" + sateTime;
			//String waitId=  getString("waitId");

			WaitTask wait = businssService.queryWaitByTaskId(orderID);
			if(wait!=null){
				//WaitTask wait = service.queryWaitByTaskId(waitId);
				System.out.println("================重新提交代办================");
				waitTaskService.updateWait(wait,this.getRequest());
				System.out.println("================重新提交代办结束================");
			}else{
				if(!"".equals(orderID) && orderID!=null){
					Write("NO");
					return;
				}
			}
			if(!"".equals(orderID) && orderID!=null){
				Businsscycle oldBusinsscycle = businssService.getBusinsscyle(orderID);
				if (oldBusinsscycle!=null) {
						oldBusinsscycle.setState("-1");//状态修改为作废
						businssService.update(oldBusinsscycle);
					}
			}
			
		//	String bysinessNo = getString("bysinessNo");//业务周期管理单号
			Businsscycle businsscycle = new Businsscycle();
			businsscycle.setProposer(proposer);
			businsscycle.setBysinessNo(batchNO);//业务周期管理单号
			businsscycle.setCustomerManager(user.getEmployeeName());//客户经理（因为前台会验证，只有集团对应的客户经理才能发起申请所以通过了验证的当前用户就是客户经理）
			businsscycle.setGoupCoding(goupCoding);//集团编码
			businsscycle.setGroupName(groupName);//集团名称
			businsscycle.setTaxpayer(taxpayer);//纳税人识别号
			businsscycle.setApplyTime(getStringDate(new Date()));//申请时间
			businsscycle.setUpdateTime(getStringDate(new Date()));//更新时间
			businsscycle.setState("2");//状态审批中
			businsscycle.setBusinessCycleName(businessCycleName);//标题
			businsscycle.setAppMemo(appMemo);//申请原因
			businsscycle = businssService.saveBusinsscycle(businsscycle);//保存业务周期对象
			String jsonone =getString("json");//申请延期的账户信息
			String shiqu="";
			shiqu = businssService.getBranch(String.valueOf(this.user.getRowNo()));
			JSONArray jsonObject1 = JSONArray.fromObject(jsonone); 
			for(int i=0;i<jsonObject1.size();i++){
				String s = jsonObject1.getString(i);
				JSONObject data2 = JSONObject.fromObject(s); 
				DelayAccount delayAccount= new DelayAccount();
				delayAccount.setBranchOffice(shiqu);//分公司
				delayAccount.setBusiness(data2.getString("business"));//业务类型
				delayAccount.setContractName(data2.getString("contractName"));//合同名称
				//delayAccount.setAccountNo(data2.getString("accountNo"));//申请延期单号(与业务周期单号做区分) 不知道是从哪里来的，等下问。 业务流程单号同样      
				delayAccount.setAccountId(data2.getString("accountId"));//账户id
				delayAccount.setAccountType(data2.getString("accountType"));//账户类型
			//	delayAccount.setCumulativeOwe(datea2.getString("cumulativeOwe"));//申请日累计欠款
			//	delayAccount.setCustomerRevenue(data2.getString("customerRevenue"));//申请日客户收入
			//	delayAccount.setWorthLv(data2.getString("worthLv"));//客户价值等级
				delayAccount.setProposer(String.valueOf(this.user.getRowNo()));//申请人
				delayAccount.setValidityStart((data2.getString("validityStart")));//有效期开始时间  格式：YYYYMMDDHH24MISS
				delayAccount.setValidityEnd(data2.getString("validityEnd"));//有效期结束时间  格式：YYYYMMDDHH24MISS
				delayAccount.setOweQuota(data2.getString("oweQuota"));//欠费限额 
			//	delayAccount.setAppMemo(data2.getString("appMemo"));//申请原因    ！！！！！！！ 等下加上
				delayAccount.setBusinessCycleId(businsscycle.getId());//业务周期管理ID
			//	delayAccount.setState(data2.getString("state"));               ！！！！ 之后添加
				delayAccount.setAccountNo(i+"DelayAccount"+batchNO);//账号单号
				businssService.saveDeayAccount(delayAccount);
			}
			Map<String, String> map = new HashMap<String, String>();
			map.put("decisionVal", role); // 所属地区（区县：QX、市公司：SGS、省重客：SZK）
			String processId = null;
			try {
				processId	= jbpmUtil.startPIByKey("Businss", map).getId();
			} catch (Exception e) {
				processId = null;
			}
			SystemUser wuser = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
			BusinessCycleProcess businessCycleProcess = new BusinessCycleProcess();//业务周期流程类
			businessCycleProcess.setBusinssCycleId(businsscycle.getId());//业务周期id
			businessCycleProcess.setCreaorNo(user.getRowNo()+"");//发起人工号
			businessCycleProcess.setProcessId(processId);//流程id
			businessCycleProcess.setProcessName(businsscycle.getBusinessCycleName());//流程名称
			businessCycleProcess.setStartTime(getStringDate(new Date()));//开始时间
			businessCycleProcess = businssService.saveBusinessCycleProcess(businessCycleProcess);
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			BusinessCycleTask businessCycleTask = new BusinessCycleTask();
			businessCycleTask.setRole("客户经理");
			businessCycleTask.setProcess(businessCycleProcess.getId());
			businessCycleTask.setCreator(user.getEmployeeName());
			businessCycleTask.setCreatorNo(user.getRowNo() + "");
			businessCycleTask.setCreatDate(getStringDate(new Date()));
			businessCycleTask.setPlanDate(getStringDate(new Date()));
			businessCycleTask.setOper(user.getEmployeeName());
			businessCycleTask.setOperNo(user.getRowNo() + "");
			businessCycleTask.setSpendTime("0");
			businessCycleTask.setStatus("2");
			businessCycleTask.setType("SH");
			businessCycleTask.setOperDate(getStringDate(new Date()));
			businessCycleTask.setExpectedCompletionTime(getStringDate(new Date()));
			businessCycleTask = businssService.saveBusinessCycleTask(businessCycleTask);
			//第二个
			BusinessCycleTask businessCycleTaskNext = new BusinessCycleTask();
			businessCycleTaskNext.setProcess(businessCycleProcess.getId());
			businessCycleTaskNext.setCreator(user.getEmployeeName());
			businessCycleTaskNext.setCreatorNo(user.getRowNo() + "");
			businessCycleTaskNext.setCreatDate(getStringDate(new Date()));
			businessCycleTaskNext.setType("SH");
			businessCycleTaskNext.setOper(wuser.getEmployeeName());
			businessCycleTaskNext.setOperNo(String.valueOf(wuser.getRowNo()));
			businessCycleTaskNext = businssService.saveBusinessCycleTask(businessCycleTaskNext);
			String titString = "";
			daibantwo(businsscycle, userid, processId, user,businessCycleTaskNext,titString);// 代办
			List<SingleAndAttachment> ss = businssService.getSingleAndAttachment(orderID);
			//遍历获取的附件中间表数据
			if(ss!=null){
				for (int i=0; i<ss.size();i++){
					/*SingleAndAttachment s = new SingleAndAttachment();
					s.setOrderID(order.getParentOrderNumber());
					s.setAttachmentId(ss.get(i).getAttachmentId());
					s.setLink(Opinion.DEMAND_APPLICATION);
					dedicatedFlowService.saveSandA(s);*/
					attachmentId += ss.get(i).getAttachmentId()+",";
				}
			}
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] json = attachmentId.split(",");
					if (json.length > 0) {
						for (int i = 0; i < json.length; i++) {
							SingleAndAttachment sa = new SingleAndAttachment();
							sa.setOrderID(businsscycle.getId());
							sa.setAttachmentId(json[i]);
							sa.setLink(Businsscycle.Business);
							businssService.saveSandA(sa);
						}
					}
				}
			}
			Write("YES");
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			Write("NO");
		}
	}
	/**
	 * 日期转换
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDate(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(currentTime);
		return dateString;
	}
	
	/**
	 *  完成方法
	 */
	public void completeBusinss() {
		try {
			String pid = getString("processId");// 流程id
			String id = getString("id");// 业务流程id
			String opinion = getString("opinion");// 审批意见
			String taskId = getString("TaskID");//流转任务id

			BusinessCycleTask businessCycleTask = businssService.getTaskById(taskId);// 根据流程id查询任务表信息
			if(businessCycleTask!=null){
				businessCycleTask.setOperDate(getStringDate(new Date()));// 操作时间
				businessCycleTask.setReplyContent(opinion);//审批意见
				businessCycleTask.setStatus("2");
				businssService.updateTask(businessCycleTask);// 修改任务表
			}
			Businsscycle businsscycle = businssService.getBusinsscyle(id);
			String isTs = getString("isT");
			if (isTs!=null&&isTs.equals("")==false) {
				BusinssConnectorByGrpJttfConRedInAction businssConnectorByGrpJttfConRedInAction = new BusinssConnectorByGrpJttfConRedInAction();
				List<DelayAccount> delayAccounts = businssService.getDelayAccountList(businsscycle.getId());
				for (DelayAccount delayAccount : delayAccounts) {
					Map<String, String> queryDResult = businssConnectorByGrpJttfConRedInAction.executeAccountTimeByE(delayAccount.getAccountId(),"", "");
					if (queryDResult.get("flag").equals("N")) {
						throw new Exception("查询该账户出错");
					}else {
						if (queryDResult.get("more").contains("无录入账户[") ){
							if (businssConnectorByGrpJttfConRedInAction.executeAccountTimeByAdd(delayAccount.getAccountId(), delayAccount.getValidityStart(), delayAccount.getValidityEnd())==false) {
								throw new Exception("插入该账户出错:账户"+delayAccount.getAccountId());
							}
						}else {
							if (businssConnectorByGrpJttfConRedInAction.executeAccountTimeByUpdate(delayAccount.getAccountId(), delayAccount.getValidityStart(), delayAccount.getValidityEnd())==false) {
								throw new Exception("更新该账户出错:账户"+delayAccount.getAccountId());
							}
						}
					}
				}
			}
			WaitTask wait = businssService.queryWaitByTaskId(id);
			if(wait!=null){
				System.out.println("================完成开始代办================");
				//WaitTask wait = service.queryWaitByTaskId(waitId);
				waitTaskService.updateWait(wait,this.getRequest());
				System.out.println("================完成结束代办================");
			}else{
				Write("NO");
				return;
			}
			businsscycle.setState("1");// 状态修改为已完成
			businsscycle.setUpdateTime(getStringDate(new Date()));// 更新时间
			businssService.update(businsscycle);
			BusinessCycleProcess businessCycleProcess = businssService.getBusinessCycleProcessByBusinssId(businsscycle.getId());
			businessCycleProcess.setState("2");//修改状态为完成
			businessCycleProcess.setUpdateTime(getStringDate(new Date()));
			businssService.updateBusinessProcess(businessCycleProcess);

			//tInformationService.deleteProcess(pid);// 删除流程
			//jbpmUtil.deleteProcessInstance(pid);//删除流程
			ProcessInstance processInstance = jbpmUtil.getProcessEngine().getExecutionService().createProcessInstanceQuery().processInstanceId(pid).uniqueResult();
			Task task = jbpmUtil.getProcessEngine().getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			jbpmUtil.completeTask(task.getId(), "结束");//完成流程
			//保存下一步任务信息
			
			BusinessCycleTask businessCycleTasktwo = new BusinessCycleTask();
			businessCycleTasktwo.setProcess(businessCycleProcess.getId());
		//	Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			businessCycleTasktwo.setRole("客户经理");
			businessCycleTasktwo.setCreator(user.getEmployeeName());
			businessCycleTasktwo.setCreatorNo(user.getRowNo() + "");
			businessCycleTasktwo.setCreatDate(getStringDate(new Date()));
			businessCycleTasktwo.setPlanDate(getStringDate(new Date()));
			businessCycleTasktwo.setOper(businsscycle.getCustomerManager());
			businessCycleTasktwo.setOperNo(businsscycle.getProposer());
			//TaskListtwo.setOperDate(getStringDate(new Date()));
			businessCycleTasktwo.setSpendTime("0");
			businessCycleTasktwo.setStatus("0");
			businessCycleTasktwo.setType("SH");
			businessCycleTasktwo.setExpectedCompletionTime(getStringDate(new Date()));
			businessCycleTasktwo= businssService.saveBusinessCycleTask(businessCycleTasktwo);// 保存流程表信息
			daiban(businsscycle, businsscycle.getProposer(), user,businessCycleTasktwo,"");
			Write("OK");
		}
		catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 转发方法
	 */
	public void forwarding() {
		try {
			String id = getString("id");// 业务流程id
			String opinion = getString("opinion");// 审批意见
			String taskId = getString("TaskID");//流转任务id
			Integer userId = getInteger("userid");//下一个处理人id
			SystemUser User = systemUserService.getUserInfoRowNo(userId);

			BusinessCycleTask businessCycleTask = businssService.getTaskById(taskId);// 根据流程id查询任务表信息
			if(businessCycleTask!=null){
				businessCycleTask.setOperDate(getStringDate(new Date()));// 操作时间
				businessCycleTask.setReplyContent(opinion);//审批意见
				businessCycleTask.setStatus("2");
				businssService.updateTask(businessCycleTask);// 修改任务表
			}
			WaitTask wait = businssService.queryWaitByTaskId(id);
			if(wait!=null){
				System.out.println("================转发开始代办================");
				//WaitTask wait = service.queryWaitByTaskId(waitId);
				waitTaskService.updateWait(wait,this.getRequest());
				System.out.println("================转发结束代办================");
			}else{
				Write("NO");
				return;
			}
			Businsscycle businsscycle = businssService.getBusinsscyle(id);
			BusinessCycleProcess businessCycleProcess = businssService.getBusinessCycleProcessByBusinssId(businsscycle.getId());
		//tInformationService.deleteProcess(pid);// 删除流程
			//jbpmUtil.deleteProcessInstance(pid);//删除流程
			//保存下一步任务信息
			
			BusinessCycleTask businessCycleTasktwo = new BusinessCycleTask();
			businessCycleTasktwo.setProcess(businessCycleProcess.getId());
		//	Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			businessCycleTasktwo.setRole(businssService.getRoleDescn(User.getRowNo()));
			businessCycleTasktwo.setCreator(user.getEmployeeName());
			businessCycleTasktwo.setCreatorNo(user.getRowNo() + "");
			businessCycleTasktwo.setCreatDate(getStringDate(new Date()));
			businessCycleTasktwo.setPlanDate(getStringDate(new Date()));
			businessCycleTasktwo.setOper(User.getEmployeeName());
			//businessCycleTasktwo.setOperNo(businsscycle.getCustomerManager());
			businessCycleTasktwo.setOperNo(String.valueOf(User.getRowNo()));
			//TaskListtwo.setOperDate(getStringDate(new Date()));
			businessCycleTasktwo.setSpendTime("0");
			businessCycleTasktwo.setStatus("0");
			businessCycleTasktwo.setType("SH");
			businessCycleTasktwo.setExpectedCompletionTime(getStringDate(new Date()));
			businessCycleTasktwo= businssService.saveBusinessCycleTask(businessCycleTasktwo);// 保存流程表信息
			daiban(businsscycle, businsscycle.getProposer(), user,businessCycleTasktwo,"转发");
			Write("OK");
		}
		catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void testAdd(){
		try {
			Businsscycle businsscycle = new Businsscycle();
			businsscycle.setApplyTime("sdfsdfds");
			businsscycle.setBysinessNo("sdf");
			businssService.saveBusinsscycle(businsscycle);
			Write("ok");
		} catch (Exception e) {
			// TODO: handle exception
			Write(e.getClass().toString().replaceAll("class ", "")+":"+e.getLocalizedMessage());
		}
		
	}
	
	
	
	/**
	 * 流程进行
	 */
	public void handleBusinss() {
		try {
			String pid = getString("processId");// 流程id
			String t = getString("zhuanshentwo");// 下一步可执行流程线条值
			String userid = getString("userId");// 用户id
			String id = getString("id");// 业务周期id
			String opinion = getString("opinion");// 审批意见
			String waitId = getString("waitId");// 待办id
			String taskID = getString("TaskID");//任务节点id
			BusinessCycleTask businessCycleTask = businssService.getTaskById(taskID);// 根据流程id查询任务表信息
			SystemUser wuser = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
			if(businessCycleTask!=null){
				businessCycleTask.setOperDate(getStringDate(new Date()));//操作时间
				businessCycleTask.setReplyContent(opinion);//审批意见
				businessCycleTask.setStatus("2");/*
				businessCycleTask.setOper(user.getEmployeeName());
				businessCycleTask.setOperNo(user.getRowNo() + "");*/
				businssService.updateTask(businessCycleTask);//修改任务表
			}
			//boolean isTurn = false;
			//if (t.equals("ALL")) {
			//	isTurn = true;
			//}
			Businsscycle businsscycle = businssService.getBusinsscyle(id);
			// 执行流程
			//查询流程跟踪信息并改变
			System.out.println("待办ID==========："+waitId);
			WaitTask wait = businssService.queryWaitByTaskId(waitId);
			if(wait!=null){
				System.out.println("================处理中开始代办================");
				//WaitTask wait = service.queryWaitByTaskId(waitId);
				waitTaskService.updateWait(wait,this.getRequest());
				System.out.println("================处理中结束代办================");
			}else{
				throw new Error("待办ID==========："+waitId);
			}
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			if (t.equals("ROLE_DSSM")) {
				List<DelayAccount> delayAccounts = businssService.getDelayAccountList(id);
				for (DelayAccount delayAccount : delayAccounts) {
					delayAccount.setCityApprover(String.valueOf(user.getRowNo()));
					businssService.updateDelayAccount(delayAccount);
				}
			}
			if (t.equals("ROLE_SGSMR")) {
				List<DelayAccount> delayAccounts = businssService.getDelayAccountList(id);
				for (DelayAccount delayAccount : delayAccounts) {
					delayAccount.setCityLeaderApprover(String.valueOf(user.getRowNo()));
					businssService.updateDelayAccount(delayAccount);
				}
			}
			Map<String, String> map = new HashMap<String, String>();
			String str = pid.substring(0, pid.indexOf("."));
		//	if("Businss".equals(str)&&isTurn==false){
				 jbpmUtil.completeTask(task.getId(), t);
			//}
			//保存下一步任务信息
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			BusinessCycleProcess businessCycleProcess = businssService.getBusinessCycleProcessByBusinssId(businsscycle.getId());
			businessCycleProcess.setState("1");//修改状态为完成
			businessCycleProcess.setUpdateTime(getStringDate(new Date()));
			businssService.updateBusinessProcess(businessCycleProcess);
			
			BusinessCycleTask businessCycleTasktwo = new BusinessCycleTask();
			businessCycleTasktwo.setProcess(businessCycleProcess.getId());
			businessCycleTasktwo.setRole(tasktwo.getActivityName());
			businessCycleTasktwo.setCreator(user.getEmployeeName());
			businessCycleTasktwo.setCreatorNo(user.getRowNo() + "");
			businessCycleTasktwo.setCreatDate(getStringDate(new Date()));
			businessCycleTasktwo.setPlanDate(getStringDate(new Date()));
			//TaskListtwo.setOperDate(getStringDate(new Date()));
			businessCycleTasktwo.setSpendTime("0");
			businessCycleTasktwo.setStatus("1");
			businessCycleTasktwo.setType("SH");
			businessCycleTasktwo.setOper(wuser.getEmployeeName());
			businessCycleTasktwo.setOperNo(wuser.getRowNo() + "");
			businessCycleTasktwo.setExpectedCompletionTime(getStringDate(new Date()));
			businessCycleTasktwo = businssService.saveBusinessCycleTask(businessCycleTasktwo);// 保存流程表信息
			String tit = "";
			if (t.equals("ALL")) {
				tit = "-转审";
			}
			daibantwo(businsscycle, userid, pid, user,businessCycleTasktwo,tit);// 调用service层方法生成代办
			Write("OK");
		}catch(Error ee){
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}

	}

	// 退回方法
	public void returnTransition() {
		try {
			String id = getString("id");// 业务周期id
			String processId = getString("processId");// 流程id
			String waitId = getString("waitId");// 待办id
			String opinion = getString("opinion");// 退回意见
			String taskId = getString("taskId");//taskId
			WaitTask wait = businssService.queryWaitByTaskId(waitId);
			if(wait!=null){
				//WaitTask wait = service.queryWaitByTaskId(waitId);
				System.out.println("================退回开始代办================");
				waitTaskService.updateWait(wait,this.getRequest());
				System.out.println("================退回结束代办================");
			}else{
				Write("NO");
				return;
			}
			//SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
			BusinessCycleTask businessCycleTask = businssService.getTaskById(taskId);// 根据流程id查询任务表信息
			if(businessCycleTask!=null){
				businessCycleTask.setOperDate(getStringDate(new Date()));//操作时间
				businessCycleTask.setReplyContent(opinion);//审批意见
				businessCycleTask.setStatus("2");/*
				businessCycleTask.setOper(user.getEmployeeName());
				businessCycleTask.setOperNo(user.getRowNo() + "");*/
				businssService.updateTask(businessCycleTask);//修改任务表
			}
			Businsscycle businsscycle = businssService.getBusinsscyle(id);// 根据转账信息id查询转账信息
			
			
			businsscycle.setState("3");//退回
			businssService.updateTask(businessCycleTask);// 修改任务表
			businssService.update(businsscycle);
			//jbpmUtil.deleteProcessInstance(processId);
/*			Task task =  jbpmUtil.getProcessEngine().getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			String sourceName = task.getActivityName();//退回操作开始的节点名
			Execution execution = jbpmUtil.getExecutionService().findExecutionById(task.getExecutionId());
			ProcessDefinitionImpl processDefinitionImpl = (ProcessDefinitionImpl)jbpmUtil.getRepositoryService().createProcessDefinitionQuery().processDefinitionId(execution.getProcessDefinitionId()).uniqueResult();
			ActivityImpl sourceActivityImpl = processDefinitionImpl.getActivity(sourceName);//获得现在所在节点的对象
			ActivityImpl destActivityImpl = processDefinitionImpl.getActivity("区县业务管理员");//开始的节点对象
			TransitionImpl transitionImpl = sourceActivityImpl.createOutgoingTransition();
			transitionImpl.setName("reduction");
			transitionImpl.setDestination(destActivityImpl);	*/		
			
			//jbpmUtil.getProcessEngine().getExecutionService().createProcessInstanceQuery().processInstanceId(processId).uniqueResult();
			//删除流程
			jbpmUtil.deleteProcessInstance(processId);
			//生成下一步任务信息
			BusinessCycleTask businessCycleTasktwo = new BusinessCycleTask();
			businessCycleTasktwo.setProcess(businessCycleTask.getProcess());
			businessCycleTasktwo.setRole("客户经理");
			businessCycleTasktwo.setCreator(user.getEmployeeName());
			businessCycleTasktwo.setCreatorNo(user.getRowNo() + "");
			businessCycleTasktwo.setCreatDate(getStringDate(new Date()));
			businessCycleTasktwo.setPlanDate(getStringDate(new Date()));
			businessCycleTasktwo.setOper(businsscycle.getCustomerManager());
			businessCycleTasktwo.setOperNo(businsscycle.getProposer());
			//TaskListtwo.setOperDate(getStringDate(new Date()));
			businessCycleTasktwo.setSpendTime("0");
			businessCycleTasktwo.setStatus("0");
			businessCycleTasktwo.setType("SH");
			businessCycleTasktwo.setExpectedCompletionTime(getStringDate(new Date()));
			businessCycleTasktwo = businssService.saveBusinessCycleTask(businessCycleTasktwo);// 保存流程表信息
			
			
/*			jbpmUtil.completeTask(task.getId(), transitionImpl.getName());
			removeOutTransition(processDefinitionImpl,sourceName,"reduction");*/
			BusinessCycleProcess businessCycleProcess = businssService.getBusinessCycleProcessByBusinssId(businsscycle.getId());
			businessCycleProcess.setState("0");
			businessCycleProcess.setUpdateTime(getStringDate(new Date()));
			businssService.updateBusinessProcess(businessCycleProcess);
			
			daibanthree(businsscycle, businsscycle.getProposer(), waitId, user,businessCycleTasktwo);// 调用service层方法生成待办
			Write("OK");
		}catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			Write("NO");
		}
	} 
	
	//作废方法
		public void Invalid() {
			try {
				String id = getString("id");// 业务周期id
			//	String opinion = getString("opinion");// 作废原因

				Businsscycle businsscycle = businssService.getBusinsscyle(id);
				businsscycle.setState("-1");//状态修改为作废
				businsscycle.setUpdateTime(getStringDate(new Date()));//更新时间
			//	businsscycle.setInvalidReason(opinion);//作废原因
				businssService.update(businsscycle);//修改转账信息表
				BusinessCycleProcess businessCycleProcess = businssService.getBusinessCycleProcessByBusinssId(businsscycle.getId());
				businessCycleProcess.setState("3");
				businessCycleProcess.setUpdateTime(getStringDate(new Date()));
				businssService.updateBusinessProcess(businessCycleProcess);
				WaitTask wait = businssService.queryWaitByTaskId(businsscycle.getId());
				if(wait!=null){
					System.out.println("================作废开始代办================");
					//WaitTask wait = service.queryWaitByTaskId(waitId);
					waitTaskService.updateWait(wait,this.getRequest());
					System.out.println("================作废结束代办================");
				}else{
					Write("NO");
					return;
				}
				Write("OK");
			}
			catch (Exception e) {
				e.printStackTrace();
				Write("NO");
			}
		}
	
	//完成代办生成
	public void daiban(Businsscycle businsscycle, String userid, SystemUser user,BusinessCycleTask businessCycleTask,String tit){
		WaitTask wt = new WaitTask();
		wt.setName("[业务延期"+tit+"]" + businsscycle.getBusinessCycleName());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/businss/seeBusinsscyle.jsp?id=" + businsscycle.getId() +"&businessCycleTaskId="
				+businessCycleTask.getId());// 这是需要改动的退回以后的页面地址
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(TransferInformation.Transfer);
		wt.setTaskId(businsscycle.getId());
		waitTaskService.saveWait(wt,this.getRequest());
		
	}
	
	// 提交待办生成   发起流程,流程进行时使用
	public void daibantwo(Businsscycle businsscycle, String userid, String processId, SystemUser user,BusinessCycleTask businessCycleTask, String tit) {
		WaitTask wt = new WaitTask();
		wt.setName("[业务延期"+tit+"]" + businsscycle.getBusinessCycleName());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/businss/seeBusinsscyle.jsp?id=" + businsscycle.getId() + "&processId="// 流程ID
				+ processId
				+"&businessCycleTaskId="
				+businessCycleTask.getId());
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(Businsscycle.Business);
		
		wt.setTaskId(businsscycle.getId());
		waitTaskService.saveWait(wt,this.getRequest());
	}
	
	// 退回待办生成
	public void daibanthree(Businsscycle businsscycle, String userid, String waitId, SystemUser user,BusinessCycleTask businessCycleTask) {
		WaitTask wt = new WaitTask();
		wt.setName("[业务延期]" + businsscycle.getBusinessCycleName());
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/businss/addReceiveApply.jsp?id=" + businsscycle.getId()+"&businessCycleTaskId="+businessCycleTask.getId());// 这是需要改动的退回以后的页面地址
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(TransferInformation.Transfer);
		wt.setTaskId(businsscycle.getId());
		
		
		waitTaskService.saveWait(wt,this.getRequest());
	}
	
	/**
	 * 调用boss接口查询账户信息
	 */
	public String findPreinvApplyDet() {
		//String ESB_URL="http://10.113.171.38:51000/esbWS/rest/";//正式
		String ESB_URL="http://10.113.183.51:52000/esbWS/rest/";//测试
		String contractNo = getString("contractNo");
		String groupCode = getString("groupCode");
		String additional = getString("additional");
		String url = ESB_URL + "sTaxpayerNumberQry";
		JSONObject object = new JSONObject();
	    System.out.println("账户号码：" + contractNo);
		System.out.println("集团280：" + groupCode);
		object.put("CONTRACT_NO", contractNo);
		object.put("UNIT_ID", Long.parseLong(groupCode));
		object.put("BUSI_TYPE", "B");
		object.put("LOGIN_NO", this.user.getRowNo());//账户
		if (additional!=null&&additional.equals("")==false) {
			JSONObject json = JSONObject.fromObject(additional);
			for (Object str:json.keySet()) {
			     String key = (String)str;
			     object.put(key, json.get(key));
			}
		}
		String netJson = setParamObj1(object);
	//	String jsonString = UrlConnection.responseGBK(url, json);
		String jsonString = UrlConnection.responseGBK(url, netJson);
		System.out.println(jsonString);
		return jsonString.toString();
	}
	
	protected String setParamObj1(JSONObject body) {
		JSONObject root = new JSONObject();
		JSONObject root_ = new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "10");
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		root_.put("BODY", body);
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}
	
	
	
	
	/**
	 * 日期转换2
	 * 
	 * @param currentTime
	 * @return
	 */
	public static String getStringDatetwo(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
		String dateString = formatter.format(currentTime);
		return dateString;
	}

	/**
	 * 删除自由流
	 * @param processDefinitionImpl 解析jdl.xml文件 之后产生的流程定义对象
	 * @param sourceName 开始节点名称
	 * @param destName 要移除的transition名称
	 * 本方法删除从某个节点开始的路径
	 */
	private void removeOutTransition(ProcessDefinitionImpl processDefinitionImpl,String sourceName,String destName) {
		ActivityImpl sourceaActivityImpl = processDefinitionImpl.getActivity(sourceName);
/*		List<TransitionImpl> transitionImpls =(List<TransitionImpl>) sourceaActivityImpl.getOutgoingTransitions();
		for (TransitionImpl transitionImpl : transitionImpls) {
			if (destName.equals(transitionImpl.getDestination().getName())) {
				transitionImpls.remove(transitionImpl);
			}
		}*/
		sourceaActivityImpl.removeOutgoingTransition(sourceaActivityImpl.getOutgoingTransition(destName));
	}
	
	
	
	
	
	
	
	
	/**
	 * 重新提交
	 */
	public void resubmit() {
		try {
			
			String role = getString("role");//用户职位
			String goupCoding = getString("goupCoding"); //集团280
			String groupName = getString("groupName");//集团名称
			Integer userid = getInteger("userid");//处理人id
			String attachmentId = getString("attachmentId");// 附件id,多个之间用,隔开
			String orderID = getString("orderId");
			String proposer = getString("proposer");// 申请人id
			String taxpayer=  getString("taxpayer");//纳税人识别号
			String applyTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());//申请时间
			String updateTime = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());//更新时间
			String businessCycleName = getString("businessCycleName");//业务周期管理标题
			String appMemo = getString("appMemo");//申请原因
			String IBM = "";
			String jsonone= getString("jsonone");
			List<Object[]> sone = businssService.getbumen(user.getRowNo());
			for (int i = 0; i < sone.size(); i++) {
				IBM = (String) sone.get(i)[2];
			}
			String sateTime = getStringDatetwo(new Date());
		    String batchNO = IBM + "" + sateTime;
			String pid = getString("processId");// 流程id
			String t = getString("target");// 下一步执行的目标节点
			String id = getString("id");// 业务周期id
			String opinion = getString("opinion");// 审批意见
			String waitId = getString("waitId");// 待办id
			String taskID = getString("TaskID");//任务节点id
			WaitTask wait = businssService.queryWaitByTaskId(waitId);
			if(wait!=null){
				//WaitTask wait = service.queryWaitByTaskId(waitId);
				System.out.println("================重新提交代办================");
				waitTaskService.updateWait(wait,this.getRequest());
				System.out.println("================重新提交代办结束================");
			}else {
				Write("NO");
				return;
			}
			Businsscycle businsscycle = businssService.getBusinsscyle(id);
		    businsscycle.setBysinessNo(batchNO);//业务周期管理单号
		    businsscycle.setCustomerManager(user.getEmployeeName());//客户经理（因为前台会验证，只有集团对应的客户经理才能发起申请所以通过了验证的当前用户就是客户经理）
			businsscycle.setGoupCoding(goupCoding);//集团编码
			businsscycle.setGroupName(groupName);//集团名称
			businsscycle.setTaxpayer(taxpayer);//纳税人识别号
			businsscycle.setUpdateTime(getStringDate(new Date()));//更新时间
			businsscycle.setState("2");//状态审批中
			businsscycle.setBusinessCycleName(businessCycleName);//标题
		//	businssService.update(businsscycle);//保存
			Businsscycle newUpBusinsscycle = (Businsscycle)businsscycle.clone();
		//	businssService.delDelayAccountByBusinessCycleId(businsscycle.getId());
			newUpBusinsscycle.setId(null);//id为空
			newUpBusinsscycle = businssService.saveBusinsscycle(newUpBusinsscycle);
			String shiqu="";
			shiqu = businssService.getBranch(String.valueOf(this.user.getRowNo()));
			JSONArray jsonObject1 = JSONArray.fromObject(jsonone); 
			for(int i=0;i<jsonObject1.size();i++){
				String s = jsonObject1.getString(i);
				JSONObject data2 = JSONObject.fromObject(s); 
				DelayAccount delayAccount= new DelayAccount();
				delayAccount.setBranchOffice(shiqu);//分公司
				delayAccount.setBusiness(data2.getString("business"));//业务类型
				delayAccount.setContractName(data2.getString("contractName"));//合同名称
				//delayAccount.setAccountNo(data2.getString("accountNo"));//申请延期单号(与业务周期单号做区分) 不知道是从哪里来的，等下问。 业务流程单号同样      
				delayAccount.setAccountId(data2.getString("accountId"));//账户id
				delayAccount.setAccountType(data2.getString("accountType"));//账户类型
			//	delayAccount.setCumulativeOwe(datea2.getString("cumulativeOwe"));//申请日累计欠款
			//	delayAccount.setCustomerRevenue(data2.getString("customerRevenue"));//申请日客户收入
			//	delayAccount.setWorthLv(data2.getString("worthLv"));//客户价值等级
				delayAccount.setProposer(String.valueOf(this.user.getRowNo()));//申请人
				delayAccount.setValidityStart((data2.getString("validityStart")));//有效期开始时间  格式：YYYYMMDDHH24MISS
				delayAccount.setValidityEnd(data2.getString("validityEnd"));//有效期结束时间  格式：YYYYMMDDHH24MISS
				delayAccount.setOweQuota(data2.getString("oweQuota"));//欠费限额 
			//	delayAccount.setAppMemo(data2.getString("appMemo"));//申请原因    ！！！！！！！ 等下加上
				delayAccount.setBusinessCycleId(newUpBusinsscycle.getId());//业务周期管理ID
			//	delayAccount.setState(data2.getString("state"));               ！！！！ 之后添加
				delayAccount.setAccountNo(i+"DelayAccount"+batchNO);//账号单号
				businssService.saveDeayAccount(delayAccount);
			}
		
			
			BusinessCycleTask businessCycleTask = businssService.getTaskById(taskID);// 根据流程id查询任务表信息
			if(businessCycleTask!=null){
				businessCycleTask.setOperDate(getStringDate(new Date()));//操作时间
				businessCycleTask.setReplyContent(opinion);//审批意见
				businessCycleTask.setStatus("2");
				businssService.updateTask(businessCycleTask);//修改任务表
			}
		//	Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			//Task task = jbpmUtil.getTaskService().getTask(taskID);
/*			 String sourceName =task.getActivityName().toString();
			Execution execution = jbpmUtil.getExecutionService().findExecutionById(task.getExecutionId());
			ProcessDefinitionImpl pd = (ProcessDefinitionImpl) jbpmUtil.getRepositoryService().createProcessDefinitionQuery().processDefinitionId(execution.getProcessDefinitionId()).uniqueResult();
			ActivityImpl sourceActivity =  pd.findActivity(sourceName);
			ActivityImpl destActivityImpl = pd.findActivity(t);
			TransitionImpl transitionImpl = sourceActivity.createOutgoingTransition();
			transitionImpl.setName("go to"+destActivityImpl.getName());
			transitionImpl.setDestination(destActivityImpl);
			sourceActivity.addOutgoingTransition(transitionImpl);*/
			// 执行流程
			//查询流程跟踪信息并改变
		//	System.out.println("待办ID==========："+waitId);
			// jbpmUtil.completeTask(task.getId(), transitionImpl.getName());
		//	 removeOutTransition(pd,task.getActivityName().toString() , transitionImpl.getName());
			//重新开始流程
			Map<String, String> map = new HashMap<String, String>();
			map.put("decisionVal", role); // 所属地区（区县：QX、市公司：SGS、省重客：SZK）
			String processId = null;//流程id
			try {
				processId	= jbpmUtil.startPIByKey("Businss", map).getId();
			} catch (Exception e) {
				processId = null;
			}
			//保存下一步任务信息
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
			BusinessCycleTask businessCycleTasktwo = new BusinessCycleTask();
			
			//BusinessCycleProcess businessCycleProcess = businssService.getBusinessCycleProcessByBusinssId(businsscycle.getId());
			
			BusinessCycleProcess businessCycleProcess = new BusinessCycleProcess();
			//businessCycleProcess
			
			
			
			
			
			
			
			businessCycleTasktwo.setProcess(businessCycleProcess.getId());
			businessCycleTasktwo.setRole(tasktwo.getActivityName());
			businessCycleTasktwo.setCreator(user.getEmployeeName());
			businessCycleTasktwo.setCreatorNo(user.getRowNo() + "");
			businessCycleTasktwo.setCreatDate(getStringDate(new Date()));
			businessCycleTasktwo.setPlanDate(getStringDate(new Date()));
			SystemUser User =  systemUserService.getUserInfoRowNo(userid);
			businessCycleTasktwo.setOper(businsscycle.getCustomerManager());
			businessCycleTasktwo.setOperNo(businsscycle.getProposer());
			//TaskListtwo.setOperDate(getStringDate(new Date()));
			businessCycleTasktwo.setSpendTime("0");
			businessCycleTasktwo.setStatus("1");
			businessCycleTasktwo.setType("SH");
			businessCycleTasktwo.setOper(User.getEmployeeName());
			businessCycleTasktwo.setOperNo(User.getRowNo() + "");
			businessCycleTasktwo.setExpectedCompletionTime(getStringDate(new Date()));
			businessCycleTasktwo = businssService.saveBusinessCycleTask(businessCycleTasktwo);// 保存流程表信息
			String titString = "";
			daibantwo(businsscycle, String.valueOf(userid), pid, user,businessCycleTasktwo,titString);// 调用service层方法生成代办
			List<SingleAndAttachment> ss = businssService.getSingleAndAttachment(id);
			//遍历获取的附件中间表数据
			if(ss!=null){
				for (int i=0; i<ss.size();i++){
					/*SingleAndAttachment s = new SingleAndAttachment();
					s.setOrderID(order.getParentOrderNumber());
					s.setAttachmentId(ss.get(i).getAttachmentId());
					s.setLink(Opinion.DEMAND_APPLICATION);
					dedicatedFlowService.saveSandA(s);*/
					attachmentId += ss.get(i).getAttachmentId()+",";
				}
			}
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] json = attachmentId.split(",");
					if (json.length > 0) {
						for (int i = 0; i < json.length; i++) {
							SingleAndAttachment sa = new SingleAndAttachment();
							sa.setOrderID(businsscycle.getId());
							sa.setAttachmentId(json[i]);
							sa.setLink(Businsscycle.Business);
							businssService.saveSandA(sa);
						}
					}
				}
			}
			Write("OK");
		}catch(Error ee){
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}

		
		
	}
	/**
	 * 根据用户当前所在流程位置，生成下一步按钮
	 */
    public void buttonFactory() {
    	try {
    		List<String > buttons = new ArrayList<>();
        	String pid = getString("processId");
        	String businssId = getString("id");
        	Businsscycle businsscycle = businssService.getBusinsscyle(businssId);
    	//	ProcessInstance processInstance = jbpmUtil.getProcessEngine().getExecutionService().createProcessInstanceQuery().processInstanceId(pid).uniqueResult();//findProcessInstanceById(pid);
    		Task task = jbpmUtil.getProcessEngine().getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult(); 
    		if(task==null){
    			buttons.add("<button class=\"layui-btn layui-btn-normal\" id=\"forwarding\" onclick=\"tonyon()\">转发</button>");
    			if (businsscycle.getProposer().equals(String.valueOf(user.getRowNo()))) {
    				buttons.add("<button class=\"layui-btn layui-btn-normal\" id=\"prExit\" onclick=\"yuedu()\">结束</button>");
				}
    		}else {
    			String roleGoal = "";//选择权限角色
        		for (String nodeTr :jbpmUtil.findOutComesByTaskId(task.getId())) {
        			if(nodeTr.equals("ROLE_SZKSM")){
        				nodeTr = "ROLE_SZKFGJL";
        			}
        			/*if ((task.getActivityName().equals("市公司业务管理员")||task.getActivityName().equals("市公司政企部经理副")||task.getActivityName().equals("市公司政企部经理")
        					||task.getActivityName().equals("省公司管理员")	||task.getActivityName().equals("省公司政企业务管理室2次"))&&nodeTr.equals("ALL")) {
        				if (task.getActivityName().equals("市公司业务管理室经理")) {
        					roleGoal = "ROLE_SGSYWGLSJL";
        				}else if (task.getActivityName().equals("市公司政企部经理副")||task.getActivityName().equals("市公司政企部经理")) {
        					roleGoal = "ROLE_DSDM";
        				}else if (task.equals("省公司管理员")) {
        					roleGoal = "ROLE_SGSBM";
        				}else{
        					roleGoal = "ROLE_SGSMR";
        				}
        				buttons.add("<button Goal=\""+roleGoal+"\" class=\"layui-btn layui-btn-normal\"  id=\"zhuanshen\" onclick=\"zhuanshen1(this)\">转 审</button>");
        			}else */
        			if (task.getActivityName().equals("市公司业务管理室经理")==false||(task.getActivityName().equals("市公司业务管理室经理")&&nodeTr.equals("ALL")==false)) {
        				if(nodeTr.equals("结束")){
        					buttons.add("<button Goal=\""+nodeTr+"\" class=\"layui-btn layui-btn-normal\" id=\"tijiao\" onclick=\"endt()\">完成</button>");
        					continue;
        				}
        				if(task.getActivityName().equals("市公司政企部经理副")&&nodeTr.equals("ROLE_DSSM")){
        					continue;
        				}
        				if (nodeTr.equals("ALL")) {
        					buttons.add("<button Goal=\""+roleGoal+"\" class=\"layui-btn layui-btn-normal\"  id=\"zhuanshen\" onclick=\"zhuanshen1(this)\">转 审</button>");
        					continue;
						}
        				buttons.add("<button Goal=\""+nodeTr+"\" class=\"layui-btn layui-btn-normal\" id=\"tijiao\" onclick=\"commit(this)\">提 交</button>");
        			}
        		} 
            	if (task.getActivityName().equals("市公司政企部经理副")) {
        				buttons.add("<button  Goal=\"ROLE_DSSM\"  class=\"layui-btn layui-btn-normal\" id=\"wancheng\" onclick=\"dacda()\">同 意</button>");
        			}
            	if (task.getActivityName().contains("其他人审批")==false&&task.getActivityName().contains("其他审批人")==false){
            		buttons.add("<button class=\"layui-btn layui-btn-normal\" id=\"tuihui\"  onclick=\"tuihui1()\">退 回</button>");
            	}
                	
			}
			    buttons.add("<button type=\"reset\" class=\"layui-btn layui-btn-primary\"  onclick=\"window.close();\">取 消</button>");
    			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(buttons));
    	} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			Write("NO");
		}
    }
	
	
	

	// 判断角色
	public void judgeRole() {
		try {
			// 获取当前用户
			SystemUser user = this.user;
			String role = "";
			List<Map<String, Object>> map = businssService.findDept(user.getRowNo());

			String json = JSONHelper.SerializeWithNeedAnnotation(map);
			Write(json);
		}
		catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}

	}

	/**
	 * 通过业务周期id获取流程id
	 */
	public void getPidByBusId(){
		try{
			String busId = getString("bid");
			Write(businssService.getBusinessCycleProcessByBusinssId(busId).getId());
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	
	//阅读结束方法
	public void readEnd(){
		try {
			String businId=getString("id");//id是业务周期的id，也是待办的查找taskid
			String businessCycleTaskId = getString("businessCycleTaskId");

			BusinessCycleTask businessCycleTask = businssService.getTaskById(businessCycleTaskId);// 根据流程id查询任务表信息
			if(businessCycleTask!=null){
				businessCycleTask.setOperDate(getStringDate(new Date()));//操作时间
				businessCycleTask.setReplyContent("");//审批意见
				businessCycleTask.setStatus("2");
				businssService.updateTask(businessCycleTask);//修改任务表
			}
			

			
			WaitTask wait = businssService.queryWaitByTaskId(businId);
			if(wait!=null){
				System.out.println("================阅读开始代办================");
				//WaitTask wait = service.queryWaitByTaskId(waitId);
				waitTaskService.updateWait(wait,this.getRequest());
				System.out.println("================阅读结束代办================");
			}else{
				Write("NO");
				return;
			}
			Write("OK");
		} catch (Exception e) {
			e.printStackTrace();
			Write("No");
		}
	}

}
