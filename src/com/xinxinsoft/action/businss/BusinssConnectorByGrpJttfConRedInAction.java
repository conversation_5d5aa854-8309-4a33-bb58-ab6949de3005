package com.xinxinsoft.action.businss;

import java.util.HashMap;
import java.util.Map;
import java.util.Random;

import net.sf.json.JSONObject;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.utils.UrlConnection;

public class BusinssConnectorByGrpJttfConRedInAction extends BaseAction {
	/**
	 * 调用boss接口查询账户信息 根据账户
	 */
	public void executeAccountTime () {
		/*Map<String, String> tempRes = new HashMap<>();
		tempRes.put("res", "{\"RETURN_CODE\":0,\"RETURN_MSG\":\"ok!\",\"USER_MSG\":\"ok!\",\"DETAIL_MSG\":\"OK!\",\"PROMPT_MSG\":\"\",\"OUT_DATA\":{\"OUT_MSG\":{\"CONTRACT_NO\":**************,\"CODE_VALUE\":\"\",\"CODE_DESC\":\"\",\"STATUS\":\"0\",\"LIMIT_TYPE\":\"01\",\"EXP_DATE\":\"**************\",\"EFF_DATE\":\"**************\",\"LOGIN_NO\":\"aagh5P\",\"OP_TIME\":\"**************\"}}}");
		tempRes.put("resIn", "{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"DB_ID\":\"\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241561709614773\",\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\",\"ROUTING\":{\"ROUTE_KEY\":\"10\",\"ROUTE_VALUE\":\"***********\"}},\"BODY\":{\"CONTRACT_NO\":\"**************\",\"OP_TYPE\":\"Q\",\"LOGIN_NO\":\"aagh5P\",\"EXP_DATE\":\"\",\"EFF_DATE\":\"\"}}}");
		tempRes.put("flag", "Y");
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(tempRes));
		if (true) {
			return;
		}*/
		
		
		
		
		//String ESB_URL="http://*************:51000/esbWS/rest/";//正式
		String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		String account = getString("account");//账户
		String additional = getString("additional");//可能之后自定义添加的
		String executeType = getString("executeType");//Q 查询 A 插入 D 删除 U 更新
		String startTime = getString("startTime");//开始时间
		String endTime = getString("endTime");//开始时间
		String needTime = getString("needTime");//是否需要时间
		String isRoot = getString("isRoot");
		String url = ESB_URL + "sGrpJttfConRedIn";
		JSONObject object = new JSONObject();
		Map<String, String> result = new HashMap<>();
		object.put("CONTRACT_NO", account);
		object.put("OP_TYPE", executeType);
		object.put("LOGIN_NO",this.user.getBossUserName());//操作人工号
		if (needTime!=null&&needTime.equals("")==false&&needTime.equals("Y")) {
			object.put("EXP_DATE", startTime);
			object.put("EFF_DATE", endTime);
		}
		if (additional!=null&&additional.equals("")==false) {
			JSONObject json = JSONObject.fromObject(additional);
			for (Object str:json.keySet()) {
			     String key = (String)str;
			     object.put(key, json.getString(key));
			}
		}
		String netJson = "";
		if (isRoot!=null&&isRoot.equals("Y")) {
			netJson = setParamObj1(object, user.getMobile(),true);
		}else {
			netJson = setParamObj1(object, user.getMobile(),false);
		}
		
		result.put("resIn",netJson);
		System.out.println(netJson);
		String jsonString = UrlConnection.responseGBK(url, netJson);
		JSONObject jsthree = JSONObject.fromObject(jsonString);
		String datatwo = jsthree.getString("res");
		System.out.println("这是返回数据解析结果"+datatwo);
		JSONObject jsone = JSONObject.fromObject(datatwo);
		JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
		if ("0".equals(jstwo.getString("RETURN_CODE"))) {
		   result.put("flag", "Y");
		   result.put("res", jstwo.toString());
		}else {
			result.put("flag", "N");
			System.out.println(jstwo.getString("DETAIL_MSG"));
		    result.put("errorMsg", "操作失败");
		    result.put("more", jstwo.getString("DETAIL_MSG"));
		    result.put("ALL", jsonString);
		}
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(result));
	}
	
	/**
	 * 调用boss接口查询账户信息 根据账户
	 */
	public Map<String, String> executeAccountTimeByE(String account,String startTime,String endTime) {
		//String ESB_URL="http://*************:51000/esbWS/rest/";//正式
		String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String account = getString("account");//账户
		//String additional = getString("additional");//可能之后自定义添加的
		String executeType ="U";//Q 查询 A 插入 D 删除 U 更新
		//String startTime = getString("startTime");//开始时间
		//String endTime = getString("endTime");//开始时间
		//String needTime = getString("needTime");//是否需要时间
		String url = ESB_URL + "sGrpJttfConRedIn";
		JSONObject object = new JSONObject();
	
		object.put("CONTRACT_NO", account);
		object.put("OP_TYPE", executeType);
		object.put("LOGIN_NO",this.user.getBossUserName());//操作人工号
		//if (needTime.equals("Y")) {
		object.put("EXP_DATE", startTime);
		object.put("EFF_DATE", endTime);
		//}
/*		if (additional!=null&&additional.equals("")==false) {
			JSONObject json = JSONObject.fromObject(additional);
			for (Object str:json.keySet()) {
			     String key = (String)str;
			     object.put(key, json.getString(key));
			}
		}*/
		String netJson = setParamObj1(object, user.getMobile(),false);
		String jsonString = UrlConnection.responseGBK(url, netJson);
		JSONObject jsthree = JSONObject.fromObject(jsonString);
		String datatwo = jsthree.getString("res");
		System.out.println("这是返回数据解析结果"+datatwo);
		JSONObject jsone = JSONObject.fromObject(datatwo);
		JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
		Map<String, String> result = new HashMap<>();
		if ("0".equals(jstwo.getString("RETURN_CODE"))) {
		   result.put("flag", "Y");
		   result.put("res", jstwo.toString());
		}else {
			result.put("flag", "N");
			System.out.println(jstwo.getString("DETAIL_MSG"));
		    result.put("errorMsg", "操作失败");
		    result.put("more", jstwo.getString("DETAIL_MSG"));
		    result.put("ALL", jsonString);
		    
		}
		try {
			if (result.get("flag").equals("N")) {
				throw new Exception(result.get("more"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		return result;
	//	Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(result));
	}
	
	/**
	 * 访问boos接口添加相关欠费信息
	 * @param account
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public boolean executeAccountTimeByAdd(String account,String startTime,String endTime) {
		//String ESB_URL="http://*************:51000/esbWS/rest/";//正式
		String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String account = getString("account");//账户
		//String additional = getString("additional");//可能之后自定义添加的
		String executeType ="A";//Q 查询 A 插入 D 删除 U 更新
		//String startTime = getString("startTime");//开始时间
		//String endTime = getString("endTime");//开始时间
		//String needTime = getString("needTime");//是否需要时间
		String url = ESB_URL + "sGrpJttfConRedIn";
		JSONObject object = new JSONObject();
	
		object.put("CONTRACT_NO", account);
		object.put("OP_TYPE", executeType);
		object.put("LOGIN_NO",this.user.getBossUserName());//操作人工号
		//if (needTime.equals("Y")) {
		object.put("EXP_DATE", startTime);
		object.put("EFF_DATE", endTime);
		//}
/*		if (additional!=null&&additional.equals("")==false) {
			JSONObject json = JSONObject.fromObject(additional);
			for (Object str:json.keySet()) {
			     String key = (String)str;
			     object.put(key, json.getString(key));
			}
		}*/
		String netJson = setParamObj1(object, user.getMobile(),false);
		String jsonString = UrlConnection.responseGBK(url, netJson);
		JSONObject jsthree = JSONObject.fromObject(jsonString);
		String datatwo = jsthree.getString("res");
		System.out.println("这是返回数据解析结果"+datatwo);
		JSONObject jsone = JSONObject.fromObject(datatwo);
		JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
		Map<String, String> result = new HashMap<>();
		if ("0".equals(jstwo.getString("RETURN_CODE"))) {
		   result.put("flag", "Y");
		   result.put("res", jstwo.toString());
		}else {
			result.put("flag", "N");
			System.out.println(jstwo.getString("DETAIL_MSG"));
		    result.put("errorMsg", "操作失败");
		    result.put("more", jstwo.getString("DETAIL_MSG"));
		    result.put("ALL", jsonString);
		    
		}
		try {
			if (result.get("flag").equals("N")) {
				throw new Exception(result.get("more"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		if (result.get("flag").equals("N")) {
			return false;
		}else {
			return true;
		}
		//return result;
	//	Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(result));
	}
	
	/**
	 * 访问boos接口更新相关欠费信息
	 * @param account
	 * @param startTime
	 * @param endTime
	 * @return
	 */
	public boolean executeAccountTimeByUpdate(String account,String startTime,String endTime) {
		//String ESB_URL="http://*************:51000/esbWS/rest/";//正式
		String ESB_URL="http://*************:52000/esbWS/rest/";//测试
		//String account = getString("account");//账户
		//String additional = getString("additional");//可能之后自定义添加的
		String executeType ="U";//Q 查询 A 插入 D 删除 U 更新
		//String startTime = getString("startTime");//开始时间
		//String endTime = getString("endTime");//开始时间
		//String needTime = getString("needTime");//是否需要时间
		String url = ESB_URL + "sGrpJttfConRedIn";
		JSONObject object = new JSONObject();
	
		object.put("CONTRACT_NO", account);
		object.put("OP_TYPE", executeType);
		object.put("LOGIN_NO",this.user.getBossUserName());//操作人工号
		//if (needTime.equals("Y")) {
		object.put("EXP_DATE", startTime);
		object.put("EFF_DATE", endTime);
		//}
/*		if (additional!=null&&additional.equals("")==false) {
			JSONObject json = JSONObject.fromObject(additional);
			for (Object str:json.keySet()) {
			     String key = (String)str;
			     object.put(key, json.getString(key));
			}
		}*/
		String netJson = setParamObj1(object, user.getMobile(),false);
		String jsonString = UrlConnection.responseGBK(url, netJson);
		JSONObject jsthree = JSONObject.fromObject(jsonString);
		String datatwo = jsthree.getString("res");
		System.out.println("这是返回数据解析结果"+datatwo);
		JSONObject jsone = JSONObject.fromObject(datatwo);
		JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
		Map<String, String> result = new HashMap<>();
		if ("0".equals(jstwo.getString("RETURN_CODE"))) {
		   result.put("flag", "Y");
		   result.put("res", jstwo.toString());
		}else {
			result.put("flag", "N");
			System.out.println(jstwo.getString("DETAIL_MSG"));
		    result.put("errorMsg", "操作失败");
		    result.put("more", jstwo.getString("DETAIL_MSG"));
		    result.put("ALL", jsonString);
		    
		}
		try {
			if (result.get("flag").equals("N")) {
				throw new Exception(result.get("more"));
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
		}
		if (result.get("flag").equals("N")) {
			return false;
		}else {
			return true;
		}
		//return result;
	//	Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(result));
	}
	
	
	
	protected String setParamObj1(JSONObject body, String phone,boolean isRoot) {
		JSONObject root =new JSONObject();
		JSONObject root_ =  new JSONObject();
		JSONObject header = new JSONObject();
		JSONObject routing = new JSONObject();
		routing.put("ROUTE_KEY", "10");
		routing.put("ROUTE_VALUE", phone);
		header.put("POOL_ID", "31");
		header.put("DB_ID", "");
		header.put("ENV_ID", "1");
		header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
		header.put("CHANNEL_ID", "155");
		header.put("USERNAME", "zqddxt");
		header.put("PASSWORD", "123456");
		header.put("ENDUSRLOGINID", "");
		header.put("ENDUSRIP", "");
		header.put("ROUTING", routing);
		root_.put("HEADER", header);
		if (isRoot) {
			for (Object key : body.keySet()) {
			root_.put(key, body.get(key));
		   }
		}else {
			root_.put("BODY", body);
		}
		root.put("ROOT", root_);
		System.out.println(root.toString());
		return root.toString();
	}
	

}
