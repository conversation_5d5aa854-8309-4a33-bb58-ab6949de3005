package com.xinxinsoft.action.arrearsWriteOffAction;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.ArrearsWriteOff.ArrearsCall;
import com.xinxinsoft.entity.ArrearsWriteOff.ArrearsRecord;
import com.xinxinsoft.entity.ArrearsWriteOff.CallRecord;
import com.xinxinsoft.entity.arrearsModule.ArrearsOperating;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.sendComms.unitService.GroupInfoSrv;
import com.xinxinsoft.service.arrearsWriteOffService.ArrearsWriteOffService;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.executejoblog.JobLogServicer;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.io.*;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 欠费和销账
 * action
 */
public class ArrearsWriteOffAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(ArrearsWriteOffAction.class);

    @Resource(name = "ArrearsWriteOffService")
    ArrearsWriteOffService arrearsWriteOffService;
    @Resource(name = "SystemUserService")
    SystemUserService systemUserService;
    @Resource(name = "JobLogServicer")
    JobLogServicer jobLogServicer;
    @Resource(name = "ClaimForFundsService")
    private ClaimForFundsService claimForFundsService;
    /**
     * gcy 查找文件夹
     *
     * @param file
     * @param isRecursive 是否递归子文件夹
     */
    public List<String> filterFileType(File file, boolean isRecursive) {
        List<String> list = new ArrayList<>();
        if (file.isDirectory()) {
            File[] files = file.listFiles();
            for (File f : files) {
                if (f.isFile()) {
                    if (f.getName().endsWith(".txt")) {
                        list.add(f.getName());
                    }
                } else if (isRecursive && f.isDirectory()) {
                    filterFileType(f, true);
                }
            }
        }
        return list;
    }

    //获取指定文件夹下的所有文件名
    public void getAllFileName(String path, List<String> listFileName) {
        File file = new File(path);
        String[] names = file.list();
        if (names != null) {
            String[] completNames = new String[names.length];
            for (int i = 0; i < names.length; i++) {
                completNames[i] = names[i];
                listFileName.add(completNames[i]);
            }
        }
    }

    /**
     * 获取当前时间前一天字符串
     */
    public String getTime() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMdd");// 格式化日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        date = (Date) calendar.getTime();
        int month = calendar.get(Calendar.MONTH) + 1;
        return sdf.format(date);

    }

    /**
     * 获取当前时间前一个月字符串
     *
     * @return
     */
    public String getMonth() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMM");// 格式化日期
        date.setMonth(date.getMonth() - 1);
        return sdf.format(date);
    }


    /**
     * 日期转换2
     *
     * @param strDate
     * @return
     * @throws ParseException
     */
    public Date formatDate(String strDate) throws ParseException {
        Date date = null;

        if (strDate != null && !"".equals(strDate)) {
            StringBuilder sb = new StringBuilder(strDate);
            sb.insert(4, "-");
            sb.insert(7, "-");
            sb.insert(10, " ");
            sb.insert(13, ":");
            sb.insert(16, ":");
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = format.parse(sb.toString());
        }
        return date;
    }

    /**
     * 导入欠费测试方法
     */
    public void arrearsRecord() throws IOException {
        long start = System.currentTimeMillis();
        logger.info("导入欠费文件方法开始执行");
        List<Map<String, String>> arrears = arrearsWriteOffService.getArrears();
        if (arrears.size() != 0) {
            arrearsWriteOffService.deleteAll();// 删除数据
        }
        List<String> list1 = new ArrayList<>();
        //getAllFileName(PREINVAPPLY_UPLOAD,list1);
        getAllFileName("D:\\20210716\\", list1);
        //File file = new File("D:\\20210121");
        System.out.println("路径为" + list1);
        if (!list1.isEmpty()) {
            for (String s : list1) { //循环多个路径
                System.out.println(s);
                //if (s.contains("PREINV_DATA_")) {
                if (s.contains("dw_act_grp_owe_") && s.contains(getMonth())) {
                    //File fileObj = new File(PREINVAPPLY_UPLOAD+s);
                    //logger.info("地址为"+PREINVAPPLY_UPLOAD+s);
                    File fileObj = new File("D:\\20210716\\" + s);
                    logger.info("地址为" + "D:\\20210716\\" + s);
                    // 如果文件不存在，直接返回
                    if (!fileObj.exists()) {
                        logger.info("arrearsRecord方法---文件不存在！");
                        return;
                    }
                    FileInputStream fis = new FileInputStream(fileObj);
                    BufferedReader bf = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
                    String line = null;
                    List<ArrearsRecord> list = new ArrayList<ArrearsRecord>();
                    while ((line = bf.readLine()) != null) {
                        String[] item = line.split("\\|");
                        try {
                            ArrearsRecord arrearsRecord = new ArrearsRecord();
                            arrearsRecord.setArea_lvl1_name(item[0]);
                            arrearsRecord.setArea_lvl2_name(item[1]);
                            arrearsRecord.setService_name(item[2]);
                            arrearsRecord.setUnit_id(item[3]);
                            arrearsRecord.setGrp_cust_name(item[4]);
                            arrearsRecord.setContract_no(item[5]);
                            arrearsRecord.setId_no(item[6]);
                            arrearsRecord.setPhone_no(item[7]);
                            arrearsRecord.setOwe_month(item[8]);
                            arrearsRecord.setStatus("1");
                            //DecimalFormat df = new DecimalFormat(".00");
                            arrearsRecord.setOwe_fee(Double.valueOf(item[9]));
                            list.add(arrearsRecord);
                        } catch (Exception e) {
                            //File f = new File(PREINVAPPLY_UPLOAD+getTime()+"log.txt");
//                            File f = new File("D:\\20210716\\" + getTime() + "log.txt");
//                            //用FileOutputSteam包装文件，并设置文件可追加
//                            OutputStream out = new FileOutputStream(f, true);
//                            String a = "";
//                            for (int i = 0; i < item.length; i++) {
//                                a += item[i] + "|";
//                            }
//                            out.write(a.getBytes()); //向文件中写入数据
//                            out.write('\r'); // \r\n表示换行
//                            out.write('\n');
//                            out.close(); //关闭输出流
//                            System.out.println("写入成功！");
                        }
                    }
                    arrearsWriteOffService.addArrearsRecord(list);// 新增导入欠费信息
                    long end = System.currentTimeMillis();
                    logger.info("arrearsRecord方法执行耗时" + (end - start) / 1000 + "s");
                }
            }
        } else {
            logger.info("没有文件！");
        }
    }

    /**
     * 导入销账数据
     */
    public void callRecord() throws IOException {
        long start = System.currentTimeMillis();
        logger.info("导入销账文件方法开始执行");
        List<String> list1 = new ArrayList<>();
        //getAllFileName(PREINVAPPLY_UPLOAD,list1);
        getAllFileName("D:\\20210716\\", list1);
        //File file = new File("D:\\20210121");
        System.out.println("路径为" + list1);
        if (!list1.isEmpty()) {
            for (String s : list1) { //循环多个路径
                System.out.println(s);
                //if (s.contains("PREINV_DATA_")) {
                if (s.contains("dw_act_grp_laters_t_") && s.contains(getTime())) {
                    //File fileObj = new File(PREINVAPPLY_UPLOAD+s);
                    //logger.info("地址为"+PREINVAPPLY_UPLOAD+s);
                    File fileObj = new File("D:\\20210716\\" + s);
                    logger.info("地址为" + "D:\\20210716\\" + s);
                    // 如果文件不存在，直接返回
                    if (!fileObj.exists()) {
                        logger.info("callRecord方法---文件不存在！");
                        return;
                    }
                    FileInputStream fis = new FileInputStream(fileObj);
                    BufferedReader bf = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
                    String line = null;
                    List<CallRecord> list = new ArrayList<CallRecord>();
                    while ((line = bf.readLine()) != null) {
                        String[] item = line.split("\\|");
//                        System.out.println(item[0]);
//                        System.out.println(item[1]);
//                        System.out.println(item[2]);
//                        System.out.println(item[3]);
//                        System.out.println(item[4]);
//                        System.out.println(item[5]);
//                        System.out.println(item[6]);
//                        System.out.println(item[7]);
//                        System.out.println(item[8]);
//                        System.out.println(Double.valueOf(item[9]));
                        try {
                            CallRecord callRecord = new CallRecord();
                            callRecord.setId_no(item[0]);
                            callRecord.setPhone_no(item[1]);
                            callRecord.setLater_month(item[2]);
                            callRecord.setIi_prod_id(item[3]);
                            callRecord.setLater_fee(Double.valueOf(item[4]));
                            list.add(callRecord);
                        } catch (Exception e) {
                            //File f = new File(PREINVAPPLY_UPLOAD+getTime()+"log.txt");
                            File f = new File("D:\\20210716\\" + getTime() + "log.txt");
                            //用FileOutputSteam包装文件，并设置文件可追加
                            OutputStream out = new FileOutputStream(f, true);
                            String a = "";
                            for (int i = 0; i < item.length; i++) {
                                a += item[i] + "|";
                            }
                            out.write(a.getBytes()); //向文件中写入数据
                            out.write('\r'); // \r\n表示换行
                            out.write('\n');
                            out.close(); //关闭输出流
                            System.out.println("写入成功！");
                        }
                    }
                    arrearsWriteOffService.addCallRecord(list);// 新增导入销账数据
                    long end = System.currentTimeMillis();
                    logger.info("callRecord方法执行耗时" + (end - start) / 1000 + "s");
                }
            }
        } else {
            logger.info("没有文件！");
        }
    }

    /**
     * 欠费销账管理
     * 查询欠费信息
     * 销账信息
     */
    public void getArrearsRecord() throws Exception {
        logger.info("运行欠费销账管理查询欠费信息");
        Integer pageNo = this.getInteger("pageNo");
        Integer pageSize = this.getInteger("pageSize");
//        System.out.println("pageNo"+pageNo);
//        System.out.println("pageSize"+pageSize);
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        String unit_id = getString("unit_id");
        String grp_cust_name = getString("grp_cust_name");
        String contract_no = getString("contract_no");
        String id_no = getString("id_no");
        String phone_no = getString("phone_no");
        String recv_utility = getString("recv_utility");
        List list = arrearsWriteOffService.findByRowNo(user.getRowNo());
        boolean flag = false;
        for (int i = 0; i < list.size(); i++) {
            if ((list.get(i).toString()).equals("ROLE_QJMR")) { //欠费管理员
                flag = true;
                break;
            }
        }
        page = arrearsWriteOffService.getArrearsDet(page, unit_id, grp_cust_name, contract_no, id_no, phone_no, flag, this.user, recv_utility);
        //System.out.println("欠费数据==="+JSONHelper.SerializeWithOutInnerClass(page));
        //logger.info("欠费数据===" + JSONHelper.SerializeWithOutInnerClass(page));
        this.Write(JSONHelper.SerializeWithOutInnerClass(page));
    }

    /**
     * 根据手机号查询集团
     */
    public void GroupCustomerByPhone() throws Exception {
        Result r = new Result();
        try {
            String userPhone = getString("userPhone");
            SystemUser user = systemUserService.getUserByPhone(userPhone);
            List<GroupCustomer> customer = com.xinxinsoft.sendComms.GroupJobNumberService.getInstance().getCustInfoQueryTwo("", "", user.getBossUserName());
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(customer);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("根据手机号查询集团失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }


    /**
     * 根据手机号和集团编号查询集团
     */
    public void getCustInfoQuery(){
        Result r = new Result();
        try {
            logger.info("运行根据boss工号查询集团");
            String userPhone = getString("userPhone");
            String unit_id = getString("unit_id");
            String pageSize = getString("pageSize");//多少条
            String pageNo = getString("pageNo");//第几页
            SystemUser user = systemUserService.getUserByPhone(userPhone);
            if("".equals(unit_id)||"null".equals(unit_id)||unit_id==null||"undefined".equals(unit_id)){
                pageSize="1";
                pageNo="15";
            }
            List<GroupCustomer> custInfoQuery = GroupInfoSrv.getInstance().getCustInfoQuery(unit_id, "", user.getBossUserName(),pageSize,pageNo);
            //List<GroupCustomer> custInfoQuery = com.xinxinsoft.sendComms.GroupJobNumberService.getInstance().getCustInfoQueryTwo("", "", user.getBossUserName());
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(custInfoQuery);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("根据手机号查询集团失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }


    /**
     * 根据boss工号查询集团
     */
    public void GroupCustomerByBossNo() throws Exception {
        logger.info("运行根据boss工号查询集团");
        Result r = new Result();
        try {
            String bossNo = getString("bossNo");
//            String pageSize = getString("pageSize");//多少条
//            String pageNo = getString("pageNo");//第几页
            //SystemUser user = systemUserService.getUserByPhone(userPhone);
            logger.info("查询集团获取的boss工号"+ user.getBossUserName());
            //List<GroupCustomer> customer = com.xinxinsoft.sendComms.GroupJobNumberService.getInstance().getCustInfoQueryTwo("", "", user.getBossUserName());
            //logger.info("查询集团获取的集团=="+ customer);
//            if("".equals(unit_id)||"null".equals(unit_id)||unit_id==null||"undefined".equals(unit_id)){
//                pageSize="1";
//                pageNo="15";
//            }
            List<GroupCustomer> customer = GroupInfoSrv.getInstance().getCustInfoQuery("", "", user.getBossUserName(),"1","15");
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(customer);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("根据boss工号查询集团失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }

    }

    public void getArrearsRecordTwo(){
        String unit_id = getString("unit_id");
        Integer pageNo = this.getInteger("pageNo");
        Integer pageSize = this.getInteger("pageSize");
//        System.out.println("pageNo"+pageNo);
//        System.out.println("pageSize"+pageSize);
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        page = arrearsWriteOffService.getArrearsDetTwo(page, unit_id);
        this.Write(JSONHelper.SerializeWithOutInnerClass(page));
    }


    /**
     * 根据280查询
     * 查询欠费信息销账信息
     * 手机端
     */
    public void getArrearsRecordApp() throws Exception {
        Result r = new Result();
        try {
//            String userPhone = getString("userPhone");
//            SystemUser user = systemUserService.getUserByPhone(userPhone);
            String unit_id = getString("unit_id");
//            if(unit_id==""||unit_id==null){
//                r.setCode(ResultCode.FAIL);
//                r.setMessage("失败");
//                r.setData("集团280");
//                Write(r.toString());
//            }
//            String grp_cust_name = getString("grp_cust_name");
//            String contract_no = getString("contract_no");
//            String id_no = getString("id_no");
//            String phone_no = getString("phone_no");
//            String recv_utility = getString("recv_utility");

            List<Map<String, String>> arrearsDetNew = arrearsWriteOffService.getArrearsDetNew(unit_id);
            //System.out.println("手机端欠费数据===" + JSONHelper.SerializeWithOutInnerClass(arrearsDetNew));
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(arrearsDetNew);
            Write(r.toString());
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData(e.getMessage());
            Write(r.toString());
        }
    }

    /**
     * 催缴单
     * 获取欠费详情
     */
    public void getArrears() {
        String id_no = getString("id_no");
        String owe_month = getString("owe_month");
        String phone_no = getString("phone_no");
        //System.out.println("id_no"+id_no);
        ArrearsRecord arrearsRecord = arrearsWriteOffService.getArrearsRecord(id_no, owe_month, phone_no);
        Write(JSONHelper.Serialize(arrearsRecord));
    }

    /**
     * 催缴单
     * 获取欠费详情
     * 手机
     */
    public void getArrearsApp() {
        Result r = new Result();
        try {
            String id_no = getString("id_no");
            String owe_month = getString("owe_month");
            String phone_no = getString("phone_no");
            //System.out.println("id_no"+id_no);
            ArrearsRecord arrearsRecord = arrearsWriteOffService.getArrearsRecord(id_no, owe_month, phone_no);
            //Write(JSONHelper.Serialize(arrearsRecord));
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(arrearsRecord);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("获取欠费详情失败" + e.getMessage());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
        }
    }

    /**
     * 催缴单
     * 获取销账详情
     */
    public void getCall() {
        String id_no = getString("id_no");
        String phone_no = getString("phone_no");
        String owe_month = getString("owe_month");
        List<Map<String, String>> call = arrearsWriteOffService.getCall(id_no, phone_no, owe_month);
        Write(JSONHelper.Serialize(call));
    }

    /**
     * 催缴单
     * 获取销账详情
     * 手机
     */
    public void getCallApp() {
        Result r = new Result();
        try {
            String id_no = getString("id_no");
            String phone_no = getString("phone_no");
            String owe_month = getString("owe_month");
            List<Map<String, String>> call = arrearsWriteOffService.getCall(id_no, phone_no, owe_month);
            //Write(JSONHelper.Serialize(call));
            //Write(JSONHelper.Serialize(arrearsRecord));
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(call);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("获取销账详情失败" + e.getMessage());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
        }
    }

    /**
     * 添加催缴记录
     */
    public void addArrearsOperating() {
        try {
            logger.info("运行添加催缴记录");
            String plan_date = getString("plan_date");//时间
            String recv_memo = getString("recv_memo");//备注
            //DecimalFormat decimalFormat = new DecimalFormat("0");
            String unit_no = getString("unit_no");
            String unit_name = getString("unit_name");
            String area_lvl1_name = getString("area_lvl1_name");
            String area_lvl2_name = getString("area_lvl2_name");
            String qianfei = getString("qianfei");
            String xiaozhang = getString("xiaozhang");
            //logger.info("qianfei=="+qianfei);
            //logger.info("xiaozhang=="+xiaozhang);
            //System.out.println("欠费=="+qianfei);
            //System.out.println("销账=="+xiaozhang);
            Double arrears = 0.0;
            Double calls = 0.0;
            String[] split = qianfei.split(",");
            String[] split1 = xiaozhang.split(",");
            for (int i=1;i<split.length;i++){
                if(split[i].equals("null")||split[i]==null||split[i]=="null"||split[i]==""){
                    arrears+=Double.parseDouble("0.0");
                }else {
                    arrears+=Double.parseDouble(split[i]);
                }
                if(split1[i].equals("null")||split1[i]==null||split1[i]=="null"||split1[i]==""){
                    calls+=Double.parseDouble("0.0");
                }else {
                    calls+=Double.parseDouble(split1[i]);
                }
            }
            String preivType = getString("preivType");
            String recv_utility = getString("recv_utility");
            String jsonone = getString("jsonone");
            //System.out.println(qianfei);
            //System.out.println(xiaozhang);
            String attachmentId = getString("attachmentId");

            ArrearsOperating arrearsOperating = new ArrearsOperating();
            arrearsOperating.setUnit_no(unit_no);
            arrearsOperating.setUnit_name(unit_name);
            //arrearsOperating.setRecv_mon(recv_mon);//欠费月份
            arrearsOperating.setRecv_date(new Date());
            arrearsOperating.setState(2);
            arrearsOperating.setCity(area_lvl1_name);//地市
            arrearsOperating.setCountry(area_lvl2_name);//区县
            if (preivType.equals("1")) {
                arrearsOperating.setRecv_state(1);//1.可回收 2.不可回收
                arrearsOperating.setPlan_date(plan_date);//计划回收时间
            } else {
                arrearsOperating.setRecv_state(2);//1.可回收 2.不可回收
                arrearsOperating.setRecv_utility(recv_utility);//不可回收月 recv_utility
            }
            arrearsOperating.setRecv_billfee(String.valueOf(arrears - calls));//金额
            arrearsOperating.setRecv_uno(String.valueOf(user.getRowNo()));
            arrearsOperating.setRecv_memo(recv_memo);
            arrearsOperating.setRecv_uname(user.getEmployeeName());
            arrearsOperating.setRecv_status("1");
            String arrearsId = arrearsWriteOffService.saveArrearsOperating(arrearsOperating);
            if (!StringUtils.isEmpty(attachmentId) && attachmentId != null) {
                String[] jsontwo = attachmentId.split(",");
                if (jsontwo.length > 0) {
                    for (int i = 0; i < jsontwo.length; ++i) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(arrearsId);
                        sa.setAttachmentId(jsontwo[i]);
                        sa.setLink(ArrearsOperating.ArrearsOperating);
                        this.claimForFundsService.saveSandA(sa);
                    }
                }
            }
            if (!"".equals(jsonone) && jsonone != null && jsonone.length() > 0) {
                JSONArray jsonArray = JSONArray.fromObject(jsonone);
                for (int i = 0; i < jsonArray.size(); i++) {
                    String fourt = jsonArray.getString(i);
                    JSONObject json = JSONObject.fromObject(fourt);
                    //System.out.println("json=="+json);
                    ArrearsCall arrearsCall = new ArrearsCall();
                    //System.out.println(decimalFormat.format(Double.parseDouble(json.getString("arrears")) - Double.parseDouble(json.getString("calls"))));
                    arrearsCall.setArrearsOperaId(arrearsId);//关联催缴单id
                    arrearsCall.setContract_no(json.getString("contract_no"));//帐号号码
//                    if(json.getString("calls").equals("null")||json.getString("calls")==null||json.getString("calls")=="null"||json.getString("calls")==""){
//                        arrearsCall.setOwe_fee(decimalFormat.format(Double.parseDouble(json.getString("arrears")) - Double.parseDouble("0")));
//                    }else {
//                        arrearsCall.setOwe_fee(decimalFormat.format(Double.parseDouble(json.getString("arrears")) - Double.parseDouble(json.getString("calls"))));
//                    }
                    //arrearsCall.setOwe_fee(decimalFormat.format(Double.parseDouble(json.getString("arrears")) - Double.parseDouble(json.getString("calls"))));
                    arrearsCall.setOwe_month(json.getString("owe_month"));//欠费月份
                    arrearsCall.setOwe_fee(json.getString("arrears"));//欠费金额
                    arrearsCall.setLater_fee(json.getString("calls"));//销账金额
                    //销账金额
                    //添加催缴记录详情
                    arrearsWriteOffService.addArrearsCall(arrearsCall);
                }
            }else {
                Write("No");
                return;
            }
            Write("YES");
        } catch (Exception e) {
            logger.info("添加记录失败" + e);
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }

    }


    /**
     * 获取附件消息
     */
    public void fuJian() {
        String id = getString("id");
        //System.out.println(id);
        String biaoshi = getString("biaoshi");
        //System.out.println(biaoshi);
        List<Map<String, String>> s = arrearsWriteOffService.fuJian(id, biaoshi);
        //System.out.println("附件==>"+JSONHelper.Serialize(s));
        Write(JSONHelper.Serialize(s));
    }


    /**
     * 添加催缴记录
     * 手机
     */
    public void addArrearsOperaApp() {
        Result r = new Result();
        try {
            String plan_date = getString("plan_date");//计划回收时间
            String recv_billfee = getString("recv_billfee");//金额
            String recv_memo = getString("recv_memo");//备注
//            String contract_no = getString("contract_no");//账号
            String unit_no = getString("unit_no");//集团280
            String unit_name = getString("unit_name");//集团名称
            String area_lvl1_name = getString("area_lvl1_name");//获取地市
            String area_lvl2_name = getString("area_lvl2_name");//获取区县
            String preivType = getString("preivType");//获取 1.可回收 2.不可回收
            String recv_utility = getString("recv_utility");//不可回收月
            String userPhone = getString("userPhone");
            SystemUser user = systemUserService.getUserByPhone(userPhone);
            //获取json
            String object = getString("aggregateData");//多个数据
            //String code = arrearsWriteOffService.findCodeByRowNo(user.getRowNo());//区县
            ArrearsOperating arrearsOperating = new ArrearsOperating();
//            arrearsOperating.setContract_no(contract_no);
            arrearsOperating.setUnit_no(unit_no);
            arrearsOperating.setUnit_name(unit_name);
            //arrearsOperating.setProduct_no(null);//产品编码
            arrearsOperating.setRecv_date(new Date());
            arrearsOperating.setState(2);
            arrearsOperating.setCity(area_lvl1_name);//地市
            arrearsOperating.setCountry(area_lvl2_name);//区县
            if (preivType.equals("1")) {
                arrearsOperating.setRecv_state(1);//1.可回收 2.不可回收
                arrearsOperating.setPlan_date(plan_date);//计划回收时间
            } else {
                arrearsOperating.setRecv_state(2);//1.可回收 2.不可回收
                arrearsOperating.setRecv_utility(recv_utility);//不可回收月 recv_utility
            }
            arrearsOperating.setRecv_billfee(recv_billfee);//金额
            arrearsOperating.setRecv_uno(String.valueOf(user.getRowNo()));
            arrearsOperating.setRecv_memo(recv_memo);
            arrearsOperating.setRecv_uname(user.getEmployeeName());
            arrearsOperating.setRecv_status("1");
            String operaId = arrearsWriteOffService.saveArrearsOperating(arrearsOperating);
//            if (!"".equals(jsonString) && jsonString != null && jsonString.length() > 0) {
//                JSONArray jsonArray = JSONArray.fromObject(jsonString);
//                for (int i = 0; i < jsonArray.size(); i++) {
//                    String fourt = jsonArray.getString(i);
//                    JSONObject json = JSONObject.fromObject(fourt);
//                    //String datatime = Bpms_riskoff_service.getUnlockedNumber(); 获取唯一时间戳
//                    ArrearsCall arrearsCall = new ArrearsCall();
//                    arrearsCall.setArrearsOperaId(operaId);
//                    arrearsCall.setContract_no(json.getString("contract_no"));
//                    arrearsCall.setOwe_fee(json.getString("recv_billfee"));
//                    //添加催缴记录详情
//                    arrearsWriteOffService.addArrearsCall(arrearsCall);
//                }
//            }
            System.out.println("object"+object);
            String[] split = object.split("B");
            for (int i = 0; i < split.length; i++) {
                String[] split1 = split[i].split("A");
                ArrearsCall arrearsCall = new ArrearsCall();
                arrearsCall.setArrearsOperaId(operaId);
                arrearsCall.setContract_no(split1[0]);
                arrearsCall.setOwe_fee(split1[1]);
                arrearsCall.setLater_fee(split1[2]);
                arrearsCall.setOwe_month(split1[3]);
                //添加催缴记录详情
                arrearsWriteOffService.addArrearsCall(arrearsCall);
            }
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData("YES");
            Write(r.toString());
        } catch (Exception e) {
            logger.info("添加记录失败" + e.getMessage());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
            throw new RuntimeException(" 给事务回滚，自定义");
        }

    }

    /**
     * 查询催缴记录
     */
    public void findArrearsByPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String recv_uname = getString("recv_uname");
            String unit_name = getString("unit_name");
            String unit_no = getString("unit_no");
            String state = getString("state");
            // 获取用户权限
            List list = arrearsWriteOffService.findByRowNo(user.getRowNo());
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("ROLE_QJMR")) { //欠费管理员
                    flag = true;
                    break;
                }
            }
            page = arrearsWriteOffService.findArrearsByPage(page, recv_uname, unit_name, unit_no, this.user, flag, state);
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            //System.out.println("查询催缴记录"+json);
            this.Write(json);
        } catch (Exception e) {
            logger.info("查询催缴记录失败" + e.toString());
            this.Write("列表加载失败！");
        }
    }

    /**
     * 根据id查询催缴记录
     */
    public void findArrearsByDetails() {
        String id = getString("id");
        //System.out.println("id==="+id);
        ArrearsOperating arrearsByDetails = arrearsWriteOffService.findArrearsByDetails(id);
        //System.out.println("根据id查询催缴记录==="+JSONHelper.Serialize(arrearsByDetails));
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(arrearsByDetails));
    }

    /**
     * 根据id删除催缴记录
     */
    public void upDateArrearsById() {
        Result r = new Result();
        try{
            String id = getString("id");
            Integer integer = arrearsWriteOffService.upDateArrearsById(id);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(integer);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("根据id删除催缴记录" + e);
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
        //System.out.println("id==="+id);
        //System.out.println("根据id查询催缴记录==="+JSONHelper.Serialize(arrearsByDetails));
//        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(arrearsByDetails));
    }

    /**
     * 根据id查询催缴记录详情
     */
    public void findArrearsByCall() {
        String id = getString("id");
        List<Map<String, Object>> arrearsByCall = arrearsWriteOffService.findArrearsByCall(id);
        Write(JSONHelper.Serialize(arrearsByCall));
    }


    /**
     * 查询催缴记录
     * 手机
     */
    public void findArrearsApp() {
        Result r = new Result();
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            String userPhone = getString("userPhone");
            String unit_no = getString("unit_no");
//            String country = getString("country");
//            String city = getString("city");
            String state = getString("state");//工单类型
            String status = getString("status");//工单有效状态

            SystemUser user = systemUserService.getUserByPhone(userPhone);

            // 获取用户权限
            List list = arrearsWriteOffService.findByRowNo(user.getRowNo());
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("ROLE_QJMR")) { //欠费管理员
                    flag = true;
                    break;
                }
            }
            List<Map<String, String>> arrearsByApp = arrearsWriteOffService.findArrearsByApp((pageNo - 1) * pageSize, pageNo * pageSize + 1, unit_no, user, flag, state,status);
            //String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            //System.out.println(json);
            //this.Write(json);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(arrearsByApp);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("查询催缴记录失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
        }
    }

    /**
     * 统计分析
     * 地市
     */
    public void getStatisticalCity() {
        String area_lvl1_name = getString("area_lvl1_name");
        //System.out.println(area_lvl1_name);
        //统计分析 地市的欠费和回收数据
        //统计分析 可回收催缴单
        //统计分析 不可回收催缴单
        //拼接返回r
        //logger.info("运行统计分析 地市的欠费和回收数据");
        List<Map<String, String>> strings = new ArrayList<>();
        List<Map<String, String>> collectionCity = arrearsWriteOffService.getCollectionCity(area_lvl1_name);//地市
        List<Map<String, String>> collectionLetter = arrearsWriteOffService.getCollectionLetter(area_lvl1_name);//可回收催缴单
        for (int i = 0; i < collectionCity.size(); i++) {
            Map<String, String> maps = new HashMap<String, String>();
            maps.put("AREA_LVL1_NAME", collectionCity.get(i).get("AREA_LVL1_NAME"));//地市
            maps.put("OWE_FEE", collectionCity.get(i).get("OWE_FEE"));//欠费金额
            maps.put("LATER_FEE", collectionCity.get(i).get("LATER_FEE"));//销账金额
            for (int j = 0; j < collectionLetter.size(); j++) {
                //System.out.println(collectionCity.get(i).get("AREA_LVL1_NAME"));
                //System.out.println(collectionLetter.get(j).get("CITY"));
                if (collectionCity.get(i).get("AREA_LVL1_NAME").equals(collectionLetter.get(j).get("CITY"))) {
                    List<Map<String, String>> recoverableCall = arrearsWriteOffService.getRecoverableCall(collectionCity.get(i).get("AREA_LVL1_NAME")); //不可回收催缴单
                    maps.put("COUNTS", collectionLetter.get(j).get("COUNTS"));//可回收催缴单
                    if (recoverableCall.size() > 0) {
                        maps.put("COUNTS_NO", recoverableCall.get(0).get("COUNTS_NO"));//不可回收催缴单
                    } else {
                        maps.put("COUNTS_NO", "0");//不可回收催缴单
                    }
                }
            }
            strings.add(maps);
        }
        String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(strings);
        this.Write(json);
    }

    /**
     * 统计分析
     * 地市
     * 手机
     */
    public void getStatisticalCityApp() {
        Result r = new Result();
        try {
            String area_lvl1_name = getString("area_lvl1_name");
            List<Map<String, String>> strings = new ArrayList<>();
            List<Map<String, String>> collectionCity = arrearsWriteOffService.getCollectionCity(area_lvl1_name);//地市
            List<Map<String, String>> collectionLetter = arrearsWriteOffService.getCollectionLetter(area_lvl1_name);//可回收催缴单
            for (int i = 0; i < collectionCity.size(); i++) {
                Map<String, String> maps = new HashMap<String, String>();
                maps.put("AREA_LVL1_NAME", collectionCity.get(i).get("AREA_LVL1_NAME"));//地市
                maps.put("OWE_FEE", collectionCity.get(i).get("OWE_FEE"));//欠费金额
                maps.put("LATER_FEE", collectionCity.get(i).get("LATER_FEE"));//销账金额
                for (int j = 0; j < collectionLetter.size(); j++) {
                    if (collectionCity.get(i).get("AREA_LVL1_NAME").equals(collectionLetter.get(j).get("CITY"))) {
                        List<Map<String, String>> recoverableCall = arrearsWriteOffService.getRecoverableCall(collectionCity.get(i).get("AREA_LVL1_NAME")); //不可回收催缴单
                        maps.put("COUNTS", collectionLetter.get(j).get("COUNTS"));//可回收催缴单
                        if (recoverableCall.size() > 0) {
                            maps.put("COUNTS_NO", recoverableCall.get(0).get("COUNTS_NO"));//不可回收催缴单
                        } else {
                            maps.put("COUNTS_NO", "0");//不可回收催缴单
                        }
                    }
                }
                strings.add(maps);
            }
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(strings);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("查询统计分析失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
        }
    }

    /**
     * 统计分析
     * 区县
     */
    public void getStatisticalCounty() {
        //没有参数查询所有区县信息
        //传参查询某个区县信息
        String area_lvl1_name = getString("area_lvl1_name");
        //System.out.println("area_lvl1_name===>"+area_lvl1_name);
        List<Map<String, String>> strings = new ArrayList<>();
        List<Map<String, String>> statisticalCounty = arrearsWriteOffService.getStatisticalCounty(area_lvl1_name);//AREA_LVL2_NAME
        //System.out.println("区县统计"+JSONHelper.SerializeWithNeedAnnotationDateFormats(statisticalCounty));
        List<Map<String, String>> collectionLetterTwo = arrearsWriteOffService.getCollectionLetterTwo(area_lvl1_name);
        //System.out.println("可回收不可回收"+JSONHelper.SerializeWithNeedAnnotationDateFormats(collectionLetterTwo));
        for (int i = 0; i < statisticalCounty.size(); i++) {
            Map<String, String> maps = new HashMap<String, String>();
            maps.put("AREA_LVL2_NAME", statisticalCounty.get(i).get("AREA_LVL2_NAME"));//地市
            maps.put("OWE_FEE", statisticalCounty.get(i).get("OWE_FEE"));//欠费金额
            maps.put("LATER_FEE", statisticalCounty.get(i).get("LATER_FEE"));//销账金额
            for (int j = 0; j < collectionLetterTwo.size(); j++) {
                if (statisticalCounty.get(i).get("AREA_LVL2_NAME").equals(collectionLetterTwo.get(j).get("COUNTRY"))) {
                    //System.out.println("===>"+statisticalCounty.get(i).get("AREA_LVL2_NAME"));
                    List<Map<String, String>> area_lvl2_name = arrearsWriteOffService.getRecoverableCallTwo(statisticalCounty.get(i).get("AREA_LVL2_NAME"));//不可回收催缴单
                    //String s = JSONHelper.SerializeWithNeedAnnotationDateFormats(area_lvl2_name);
                    //System.out.println(s);
                    maps.put("COUNTS", collectionLetterTwo.get(j).get("COUNTS"));//可回收催缴单
                    if (area_lvl2_name.size() > 0) {
                        //System.out.println("数组大于"+area_lvl2_name.get(0).get("COUNTS_NO"));
                        maps.put("COUNTS_NO", area_lvl2_name.get(0).get("COUNTS_NO"));//不可回收催缴单
                    } else {
                        //System.out.println("数组小于");
                        maps.put("COUNTS_NO", "0");//不可回收催缴单
                    }
                }
            }
            strings.add(maps);
        }
        String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(strings);
        //System.out.println("区县"+json);
        this.Write(json);
    }

    /**
     * 统计分析
     * 区县
     */
    public void getStatisticalCountyApp() {
        Result r = new Result();
        try {
            //没有参数查询所有区县信息
            //传参查询某个区县信息
            String area_lvl1_name = getString("area_lvl1_name");
            //System.out.println("area_lvl1_name===>"+area_lvl1_name);
            List<Map<String, String>> strings = new ArrayList<>();
            List<Map<String, String>> statisticalCounty = arrearsWriteOffService.getStatisticalCounty(area_lvl1_name);//AREA_LVL2_NAME
            List<Map<String, String>> collectionLetterTwo = arrearsWriteOffService.getCollectionLetterTwo(area_lvl1_name);
            for (int i = 0; i < statisticalCounty.size(); i++) {
                Map<String, String> maps = new HashMap<String, String>();
                maps.put("AREA_LVL2_NAME", statisticalCounty.get(i).get("AREA_LVL2_NAME"));//地市
                maps.put("OWE_FEE", statisticalCounty.get(i).get("OWE_FEE"));//欠费金额
                maps.put("LATER_FEE", statisticalCounty.get(i).get("LATER_FEE"));//销账金额
                for (int j = 0; j < collectionLetterTwo.size(); j++) {
                    if (statisticalCounty.get(i).get("AREA_LVL2_NAME").equals(collectionLetterTwo.get(j).get("COUNTRY"))) {
                        //System.out.println("===>"+statisticalCounty.get(i).get("AREA_LVL2_NAME"));
                        List<Map<String, String>> area_lvl2_name = arrearsWriteOffService.getRecoverableCallTwo(statisticalCounty.get(i).get("AREA_LVL2_NAME"));//不可回收催缴单
                        //String s = JSONHelper.SerializeWithNeedAnnotationDateFormats(area_lvl2_name);
                        //System.out.println(s);
                        maps.put("COUNTS", collectionLetterTwo.get(j).get("COUNTS"));//可回收催缴单
                        if (area_lvl2_name.size() > 0) {
                            //System.out.println("数组大于"+area_lvl2_name.get(0).get("COUNTS_NO"));
                            maps.put("COUNTS_NO", area_lvl2_name.get(0).get("COUNTS_NO"));//不可回收催缴单
                        } else {
                            //System.out.println("数组小于");
                            maps.put("COUNTS_NO", "0");//不可回收催缴单
                        }
                    }
                }
                strings.add(maps);
            }
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(strings);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("查询统计分析区县失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
        }

    }

    /**
     * 手动导入欠费数据
     */
    public void downloadArrearsFtpFile(){
        Result r = new Result();
        logger.info("启动导入欠费线程");
        String pathName = getString("pathName");
        ArrearsThread arrearsThread = new ArrearsThread(pathName,arrearsWriteOffService);
        arrearsThread.start();
        logger.info("欠费导入线程结束");
        r.setCode(ResultCode.SUCCESS);
        r.setMessage("成功");
        r.setData("OK");
        Write(r.toString());
    }

    /**
     * 手动导入销账数据
     */
    public void downloadCallsFtpFile(){
        Result r = new Result();
        logger.info("启动导入销账线程");
        String pathName = getString("pathName");
        ArrearsCallThread arrearsCallThread = new ArrearsCallThread(pathName, arrearsWriteOffService);
        arrearsCallThread.start();
        logger.info("销账导入线程结束");
        r.setCode(ResultCode.SUCCESS);
        r.setMessage("成功");
        r.setData("OK");
        Write(r.toString());
    }



    //手机端根据280查询最新一次催缴金额
    public void callAmountApp(){
        Result r = new Result();
        try{
            String unit_no = getString("unit_no");
            //System.out.println("unit_no=="+unit_no);
            String s = arrearsWriteOffService.callAmountApp(unit_no);
            //System.out.println("s=="+s);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(s);
            Write(r.toString());
        }catch (Exception e){
            logger.info("手机端根据280查询最新一次催缴金额失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
        }
    }

    //手机端根据280查询总条数数据
    public void totalNumberApp(){
        Result r = new Result();
        try{
            String unit_no = getString("unit_no");
            String s = arrearsWriteOffService.totalNumberApp(unit_no);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(s);
            Write(r.toString());
        }catch (Exception e){
            logger.info("手机端根据280查询总条数数据失败" + e.toString());
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("NO");
            Write(r.toString());
        }
    }
    //select recv_billfee from (select * from ArrearsOperating where unit_no='2803746362' order by recv_date desc) where rownum=1

    //select COUNT(0) from ArrearsOperating where unit_no='2803746362'
}
