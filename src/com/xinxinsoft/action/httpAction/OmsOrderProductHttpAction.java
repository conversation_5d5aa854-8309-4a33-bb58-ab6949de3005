package com.xinxinsoft.action.httpAction;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.oms.OmsLinkDialogue;
import com.xinxinsoft.entity.oms.OmsOrderLink;
import com.xinxinsoft.entity.oms.OmsOrderProduct;
import com.xinxinsoft.entity.oms.OmsSellOrder;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.oms.OmsSellOrderService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.easyh.JSONHelper;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.net.URLDecoder;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @ClassName: OmsSellOrderHttpAction
 * @Title: OmsSellOrderHttpAction
 * @Package: com.xinxinsoft.action.httpAction
 * @author: liyang
 * @date: 2021/12/31 9:52
 * @Version: 1.0
 * @Description: TODO
 */
public class OmsOrderProductHttpAction extends BaseAction {
    private static final Logger logger = Logger.getLogger(OmsOrderProductHttpAction.class);
    private OmsSellOrderService omsSellOrderService;
    private Bpms_riskoff_service taskService;
    private WaitTaskService service;//待办
    private SystemUserService systemUserService;//系统人员工具
    public OmsSellOrderService getOmsSellOrderService() {
        return omsSellOrderService;
    }

    public void setOmsSellOrderService(OmsSellOrderService omsSellOrderService) {
        this.omsSellOrderService = omsSellOrderService;
    }
    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    /**
     * @author: liyang
     * @date: 2021/12/31 10:02
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 一键甩单
     */
    public void updateOmsOrderProductState(){
        String resultMsg = "";
        try{
            String content= UrlConnection.getRequestData(getRequest());
            if ("".equals(content)) {
                logger.info("一键甩单接收参数为空提示");
                Write(returnParameter("1","一键甩单接口接收参数为空").toString());
                return;
            }else{
                //入参
                String json = URLDecoder.decode(content.toString(), "UTF-8");
                JSONObject datajson = JSONObject.fromObject(json);
                JSONObject rootjson = JSONObject.fromObject(datajson.get("ROOT"));
                String grpOrdId = rootjson.getString("grpOrdId");//统一流水
                String state = rootjson.getString("state");//状态标识(0已完成，1:未完成，2:已作废);
                String msg = rootjson.getString("msg");
                OmsOrderProduct prc =omsSellOrderService.getOmsOrderProductByGrpOrdId(grpOrdId);
                if(prc!=null){
                    logger.info("统一ID："+grpOrdId+"==状态说明："+msg);
                    prc.setBossCallback(Integer.parseInt(state));
                    prc.setBossMsg(msg);
                    omsSellOrderService.saveOrupdateOmsOrderProduct(prc);
                    OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(prc.getOrderNo());
                    boolean bl = queryWhetherItCanBeArchived(oms);
                    if(bl){//能归档
                        Map<String, String> map = orderComplete(prc,oms);
                        if("0".equals(map.get("code"))){
                            Write(returnParameter("0","操作成功").toString());
                        }else{
                            Write(returnParameter("1",map.get("msg")).toString());
                        }
                    }else{//不能归档
                        Write(returnParameter("1","当前需求单暂时不能归档").toString());
                    }
                }
            }
        }catch(Exception e){
            logger.error(e.getMessage()+"未知错误",e);
            e.printStackTrace();
            Write(returnParameter("1","操作失败"+e.getMessage()).toString());
            return;
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/11 16:28
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否能归档
     */
    public boolean queryWhetherItCanBeArchived(OmsSellOrder omsOrder){
        boolean bl=true;
        try {
            List<OmsOrderProduct> list = omsSellOrderService.getOmsOrderProductList(omsOrder.getOrderNo());
            List<OmsOrderLink> links = omsSellOrderService.getOmsOrderLinkByOrderNumberList(omsOrder.getOrderNo());
            int type=0;
            if(list.size()>0){
                for(OmsOrderProduct pro:list){
                    if(pro.getGrpOrdId()!=null){
                        if(omsOrder.getDemandType()==0){
                            if("1".equals(pro.getGrpOrdId())){
                                type=1;
                            }else{
                                if(pro.getIsPushBoss()==0){
                                    type=1;
                                }
                            }
                        }else{
                            if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                type=1;
                            }
                        }
                    }else{
                        if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                            type=1;
                        }
                    }
                }
            }
            if(type==0){
                int linkCode=0;
                int linkStatus=0;
                if(omsOrder.getContractId()==null){
                    bl=false;
                }else{
                    /*for(OmsOrderLink l:links){
                        if("6".equals(l.getLinkCode())){
                            linkCode=1;
                            if(l.getStatus()==0){
                                linkStatus=1;
                            }
                        }
                    }
                    if(linkCode!=1&&linkStatus!=1){
                        bl=false;
                    }else{*/
                        for(OmsOrderProduct pro:list){
                            if(pro.getGrpOrdId()!=null){
                                if(omsOrder.getDemandType()==0){
                                    if(!"1".equals(pro.getGrpOrdId())){
                                        if(pro.getIsPushBoss()==1){
                                            if(pro.getBossCallback()==null){
                                                bl=false;
                                            }else{
                                                if(pro.getBossCallback()==1){
                                                    bl=false;
                                                }
                                            }
                                        }else{
                                            bl=false;
                                        }
                                    }else{
                                        bl=false;
                                    }
                                }
                            }
                        }
                    //}
                }
            }else if(type==2){//不能归档也不能能结束代办
                bl=false;
            }else{//不能归档也不能能结束代办
                bl=false;
            }
        }catch (Exception e){
            bl=false;
        }
        return bl;
    }


    /**
     * @author: liyang
     * @date: 2022/1/11 16:25
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO  自动归档方法
     */
    public Map<String, String> orderComplete(OmsOrderProduct oms,OmsSellOrder omsOrder) {
        Map<String, String> map = new HashMap<>();
        try {
            if(omsOrder.getVersionNumber()==3){
                String IBM = "";
                List<Object[]> sone = taskService.getCompayIBM(Integer.parseInt(omsOrder.getCreateNo()));
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                List<OmsOrderLink> existence = omsSellOrderService.getOmsOrderLinkByCode("6", "需求归档", oms.getOrderNo());
                if (existence != null) {
                    if (existence.size() > 0) {
                        map.put("code", "1");
                        map.put("msg", "当前环节已完毕,环节异常,请联系系统管理员");
                        return map;
                    }
                }
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(omsOrder.getLinkOrderNo());
                link.setStatus(1);
                link.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                OmsOrderLink endLink = new OmsOrderLink();
                endLink.setCreator_name(omsOrder.getOperateName());//发起人
                endLink.setCreator_no(Integer.parseInt(omsOrder.getOperateNo()));//发起人工号
                endLink.setCreator_date(new Date());//发起人时间(当前时间)
                endLink.setOper_name(omsOrder.getCreateName());//操作人
                endLink.setOper_no(Integer.parseInt(omsOrder.getCreateNo()));//操作人工号
                endLink.setOper_date(new Date());//操作时间(当前时间)
                endLink.setStatus(1);//状态(状态根据环节确定)
                endLink.setLinkCode("6");//环节编码或者固定的环节编码
                endLink.setLinkName("需求归档");//环节名称
                endLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                endLink.setLinkOrderNo(IBM + taskService.getNumber());
                omsSellOrderService.saveOrupdateOmsOrderLink(endLink);
                omsOrder.setState("1");
                omsOrder.setLinkOrderNo(endLink.getLinkOrderNo());
                omsOrder.setCompleteDate(new Date());
                omsOrder.setModifyDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);
            }else{
                String IBM = "";
                List<Object[]> sone = taskService.getCompayIBM(Integer.parseInt(omsOrder.getCreateNo()));
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                List<OmsOrderLink> existence = omsSellOrderService.getOmsOrderLinkByCode("8", "工单归档", oms.getOrderNo());
                if (existence != null) {
                    if (existence.size() > 0) {
                        map.put("code", "1");
                        map.put("msg", "当前环节已完毕,环节异常,请联系系统管理员");
                        return map;
                    }
                }
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(omsOrder.getLinkOrderNo());
                link.setStatus(1);
                link.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                OmsOrderLink endLink = new OmsOrderLink();
                endLink.setCreator_name(omsOrder.getOperateName());//发起人
                endLink.setCreator_no(Integer.parseInt(omsOrder.getOperateNo()));//发起人工号
                endLink.setCreator_date(new Date());//发起人时间(当前时间)
                endLink.setOper_name(omsOrder.getCreateName());//操作人
                endLink.setOper_no(Integer.parseInt(omsOrder.getCreateNo()));//操作人工号
                endLink.setOper_date(new Date());//操作时间(当前时间)
                endLink.setStatus(1);//状态(状态根据环节确定)
                endLink.setLinkCode("8");//环节编码或者固定的环节编码
                endLink.setLinkName("工单归档");//环节名称
                endLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                endLink.setLinkOrderNo(IBM + taskService.getNumber());
                omsSellOrderService.saveOrupdateOmsOrderLink(endLink);
                omsOrder.setState("1");
                omsOrder.setLinkOrderNo(endLink.getLinkOrderNo());
                omsOrder.setCompleteDate(new Date());
                omsOrder.setModifyDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);
            }
            map.put("code", "0");
            map.put("msg", "需求单归档成功");
        } catch (Exception e) {
            logger.error("接口自动归档异常：" + e.getMessage(), e);
            map.put("code", "1");
            map.put("msg", "接口自动归档异常："+e.getMessage());
        }
        return map;
    }

    /**
     *  需求单生成待办给订单经理
     * @param order 需求单
     * @param userid 分配人员ID
     */
    public void commitOmsSellOrderData(OmsSellOrder order,Integer userid,String title,String isContract,String taskId,SystemUser user) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + title);//待办名称
        waitTask.setCreationTime(new Date());//代办生成时间
        if(order.getVersionNumber()==3){
            waitTask.setUrl("jsp/demandOrderTwo/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        }else{
            waitTask.setUrl("jsp/demandOrder/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        }
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 反馈信息
     * @param fileName 返回电子协议文件名称
     * @param tradeId 受理单编号（唯一标识）
     * @param bizCode 成功：0000不成功：其他
     * @param bizDesc 错误信息描述
     * @param base64Str 返回的pdf文件base64编码
     * @return
     */
    private static JSONObject returnParameter(String rootCode, String rootMsg) {
        JSONObject rootObj = new JSONObject();
        JSONObject root = new JSONObject();
        rootObj.put("RETURN_CODE",rootCode);
        rootObj.put("RETURN_MSG",rootMsg);
        root.put("ROOT",rootObj);
        return root;
    }

}
