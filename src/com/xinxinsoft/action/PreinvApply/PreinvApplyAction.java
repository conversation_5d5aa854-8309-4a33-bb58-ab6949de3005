package com.xinxinsoft.action.PreinvApply;

import java.io.*;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.xinxinsoft.entity.PreinvApply.*;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.manualInvApply.ManualInvApply;
import com.xinxinsoft.entity.manualInvApply.ManualInvApplyTask;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.PreinvApply.PreinvApplyService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.webService.accountAndTypeMatch;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 预开票申请
 *
 * <AUTHOR>
 */
public class PreinvApplyAction extends BaseAction {
    private TransferJBPMUtils transferJBPMUtils;
    private WaitTaskService service;
    private SystemUserService systemUserService;
    private PreinvApplyService preinvApplyService;
    private JbpmUtil jbpmUtil;
    private static String PREINVAPPLY_UPLOAD = Config.getString("PREINVAPPLY_UPLOAD");
    private Logger logger = LoggerFactory.getLogger(PreinvApplyAction.class);

    public void setJbpmUtil(JbpmUtil jbpmUtil) {
        this.jbpmUtil = jbpmUtil;
    }

    public void setPreinvApplyService(PreinvApplyService preinvApplyService) {
        this.preinvApplyService = preinvApplyService;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
        this.transferJBPMUtils = transferJBPMUtils;
    }

    /**
     * 查询申请开票信息并分页显示
     */
    public void findByPage() {
        try {
            Map<String, Object> map = new HashMap<String, Object>();
            // 获取当前用户
            SystemUser user = this.user;
            // 获取用户权限
            List list = preinvApplyService.findByRowNo(user.getRowNo());
            boolean flag = false;
            String json = null;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            }
            // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo());
            // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            String page = getString("page");// 当前页码数
            String pagesize = getString("pagesize");// 每页显示件数
            String batchNo = getString("batchNo");// 工单号
            String appTitle = getString("appTitle");// 工单标题
            String groupCode = getString("groupCode");// 280编码
            String startTime = getString("startTime");// 申请开始时间
            String endTime = getString("endTime");// 申请结束时间
            String startState = getString("state");// 工单状态
            String state = getString("status");// 状态
            PageVO pageVO = new PageVO<>();
            pageVO.setPage(Integer.parseInt(page));
            pageVO.setPageSize(Integer.parseInt(pagesize));
            if (flag) {
                if (companyCode.get(0).toString().equals("00")) {
                    json = preinvApplyService.findByPage(pageVO, batchNo, appTitle, groupCode, startTime, endTime, startState, state);
                } else {
                    json = preinvApplyService.findByPageForBatchoffice(pageVO, companyName, batchNo, appTitle, groupCode, startTime, endTime,
                            startState, state);
                }
            } else {
                json = preinvApplyService.findByPageForUser(pageVO, String.valueOf(user.getRowNo()), batchNo, appTitle, groupCode, startTime,
                        endTime, startState, state);
            }
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }


    /**
     * 查询申请开票信息并分页显示(2)
     */
    public void findByPageTow() {
        try {
            String phone = getString("phone");
            Map<String, Object> map = new HashMap<String, Object>();
            SystemUser user = new SystemUser();
            if (phone == null || phone.equals("null") || phone.equals("")) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
            }
            // 获取用户权限
            List list = preinvApplyService.findByRowNo(user.getRowNo());
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            }
            // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo());
            // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数
            String batchNo = getString("batchNo");// 工单号
            String appTitle = getString("appTitle");// 工单标题
            String groupCode = getString("groupCode");// 280编码
            String startTime = getString("startTime");// 申请开始时间
            String endTime = getString("endTime");// 申请结束时间
            String startState = getString("state");// 工单状态
            String state = getString("status");// 状态
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            /*if (flag) {
				if (companyCode.get(0).toString().equals("00")) {
					page = preinvApplyService.findByPage(page, batchNo, appTitle, groupCode, startTime, endTime, startState, state);
				} else {
					page = preinvApplyService.findByPageForBatchoffice(page, companyName, batchNo, appTitle, groupCode, startTime, endTime,
							startState, state);
				}
			} else {
				page = preinvApplyService.findByPageForUser(page, String.valueOf(user.getRowNo()), batchNo, appTitle, groupCode, startTime, endTime,
						startState, state);
			}*/
            page = preinvApplyService.findByPage(page, batchNo, appTitle, groupCode, startTime, endTime, startState, state, getRoleLevel(), user);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }

    /**
     * 查询作废开票信息并分页显示
     */
    public void findToPage() {
        try {
            // 获取当前用户
            SystemUser user = this.user;
            // 获取用户权限
            List list = preinvApplyService.findByRowNo(user.getRowNo());
            boolean flag = false;
            String json = null;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            }
            // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo());
            // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            String page = getString("page");// 当前页码数
            String pagesize = getString("pagesize");// 每页显示件数
            String batchNo = getString("batchNo");// 工单号
            String appTitle = getString("appTitle");// 工单标题
            String groupCode = getString("groupCode");// 280编码
            String startTime = getString("startTime");// 申请开始时间
            String endTime = getString("endTime");// 申请结束时间
            String reverseSatate = getString("state");// 开票状态
            String status = getString("status");// 状态
            PageVO pageVO = new PageVO<>();
            pageVO.setPage(Integer.parseInt(page));
            pageVO.setPageSize(Integer.parseInt(pagesize));
            if (flag) {
                if (companyCode.equals("00")) {
                    json = preinvApplyService.findToPage(pageVO, batchNo, appTitle, groupCode, startTime, endTime, reverseSatate, status);
                } else {
                    json = preinvApplyService.findByPageToBatchoffice(pageVO, companyName, batchNo, appTitle, groupCode, startTime, endTime,
                            reverseSatate, status);
                }
            } else {
                json = preinvApplyService.findByPageToUser(pageVO, String.valueOf(user.getRowNo()), batchNo, appTitle, groupCode, startTime, endTime,
                        reverseSatate, status);
            }
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }

    /**
     * 查询冲正开票信息并分页显示
     */
    public void findToPageTow() {
        try {
            // 获取当前用户
            SystemUser user = this.user;
            // 获取用户权限
            List list = preinvApplyService.findByRowNo(user.getRowNo());
            boolean flag = false;
            String json = null;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            }
            // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo());
            // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pageSize = getInteger("pageSize");// 每页显示件数
            String batchNo = getString("batchNo");// 工单号
            String appTitle = getString("appTitle");// 工单标题
            String groupCode = getString("groupCode");// 280编码
            String startTime = getString("startTime");// 申请开始时间
            String endTime = getString("endTime");// 申请结束时间
            String reverseSatate = getString("state");// 开票状态
            String status = getString("status");// 状态
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            if (flag) {
                if (companyCode.equals("00")) {
                    page = preinvApplyService.findToPage(page, batchNo, appTitle, groupCode, startTime, endTime, reverseSatate, status);
                } else {
                    page = preinvApplyService.findByPageToBatchoffice(page, companyName, batchNo, appTitle, groupCode, startTime, endTime,
                            reverseSatate, status);
                }
            } else {
                page = preinvApplyService.findByPageToUser(page, String.valueOf(user.getRowNo()), batchNo, appTitle, groupCode, startTime, endTime,
                        reverseSatate, status);
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }


    /**
     * 查询预开票申请和预开票冲正工单
     */
    public void findToPreInvoicingList() {
        try {
            // 获取当前用户
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null || phone.equals("null") || phone.equals("")) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                if (user == null) {
                    Write("{\"code\":\"9999\",\"data\":\"没有该用户\"}");
                    return;
                }
            }
            // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo());
            // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pageSize = getInteger("pageSize");// 每页显示件数
            String batchNo = getString("batchNo");// 工单号
            String appTitle = getString("appTitle");// 工单标题
            String groupCode = getString("groupCode");// 280编码
            String startTime = getString("startTime");// 申请开始时间
            String endTime = getString("endTime");// 申请结束时间
            String startState = getString("startState");// 申请工单状态
            String reverseSatate = getString("reverseSatate");// 冲正工单状态
            String status = getString("status");// 状态
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            if (phone == null || phone.equals("null") || phone.equals("")) {
                page = preinvApplyService.findToPreInvoicingList(page, String.valueOf(user.getRowNo()), batchNo, appTitle, groupCode, startTime, endTime, startState,
                        reverseSatate, status);
            } else {
                if ("1".equals(status) || "2".equals(status)) {
                    page = preinvApplyService.findToPreInvoicingList(page, String.valueOf(user.getRowNo()), batchNo, appTitle, groupCode, startTime, endTime, startState,
                            reverseSatate, status);
                } else {
                    page = preinvApplyService.findToPreInvoicingPhone(page, String.valueOf(user.getRowNo()));
                }
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }

    /**
     * 新增开票信息
     */
    public void add() {
        try {
            DecimalFormat decimalFormat = new DecimalFormat("0");
            Map<String, String> msg = new HashMap<>();
            String id = getString("id");// 开票id
            String role = getString("role");// 角色权限
            String userId = getString("userId");// 下一步任务人id
            String waitId = getString("waitId");// 代办id
            String attachmentId = getString("attachmentId");// 附件id
            String pId = getString("processId");// 流程id
            String appAmout = getString("AppAmount");// 总金额
            String appTitle = getString("AppTitle");// 工单标题
            String groupCode = getString("GroupCode");// 集团编码
            String groupName = getString("GroupName");// 集团名称 加密
            String appType = getString("AppType");// 业务类型
            String appMemo = getString("AppMemo");// 申请原因
            String recDate = getString("RecDate");// 计划回收时间
            String taxPayer = getString("TaxPayer");// 纳税人识别号
            String taxAddress = getString("TaxAddress");
            String taxPhone = getString("TaxPhone");// 纳税人电话
            String taxBankName = getString("TaxBankName");// 纳税人银行名称 加密
            String taxBankAccount = getString("TaxBankAccount");// 纳税人银行账号
            String TaxName = getString("TaxName");
            String jsonString = getString("json");
            String lateFeeHandle = getString("lateFeeHandle"); // 滞纳金处理方案
            String preivType = getString("preivType"); //发票类型
            String remarks = getString("remarks");//物联网开票备注
            String processJudge = getString("processJudge");//是否突破限制
            String type = getString("type");//是否草稿

            if ("TJCG".equals(type)) {//作废草稿单
                PreinvApply preinvApply = preinvApplyService.findById(id);
                preinvApply.setStartState("-1");
                preinvApply.setUpdateDate(new Date());
                preinvApplyService.updatePreinvApply(preinvApply);
            }

            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (pId != null) {
                jbpmUtil.deleteProcessInstance(pId);// 删除流程
            }
            if (wait != null) {
                System.out.println("================退回开始待办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================退回结束待办================");
            }
            PreinvApply preinvApply = new PreinvApply();
            preinvApply.setLateFeeHandle(lateFeeHandle);// 滞纳金处理方案
            preinvApply.setAppAmout(appAmout);// 总金额
            preinvApply.setAppMemo(appMemo);// 申请原因
            preinvApply.setAppTitle(appTitle);// 工单标题

            preinvApply.setGroupCode(groupCode);// 集团编码
            preinvApply.setGroupName(groupName);// 集团名称
            preinvApply.setOprType("0");// 开具状态为开票
            preinvApply.setHandleState("0");// 发票处理状态为初始状态

            if ("CG".equals(type)) {
                preinvApply.setStartState("-2");// 草稿
            } else {
                preinvApply.setStartState("0");// 状态为开票审批中
            }
            preinvApply.setReverseSatate("3");// 作废状态初始值为3
            preinvApply.setErrorMessage("无");// 错误信息初始值
            preinvApply.setPreivType(preivType);
            preinvApply.setRemarks(remarks);
            if (processJudge.equals("true") && !preivType.equals("2")) {
                preinvApply.setInvoice_limit("1");
            } else {
                preinvApply.setInvoice_limit("0");
            }

            String IBM = "";
            String branchOffice = "";
            String country = "";
            List<Object[]> sone = preinvApplyService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                country = (String) sone.get(i)[0];
                branchOffice = (String) sone.get(i)[1];
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = preinvApplyService.getNumber();
            String batchNO = IBM + "" + sateTime;
            preinvApply.setBatchNo(batchNO);// 工单编码
            preinvApply.setCreateDate(new Date());// 创建日期
            preinvApply.setBranchOffice(branchOffice);// 地市
            preinvApply.setCountry(country);// 区县
            preinvApply.setCreatorId(user.getRowNo() + "");// 创建人编号
            preinvApply.setCreatorName(user.getEmployeeName());// 创建人名称
            if (appType != null && !"".equals(appType)) {
                preinvApply.setAppType(appType);//业务类型
            }
            if (recDate != null && !"".equals(recDate)) {
                preinvApply.setRecDate(formatForDate(recDate));// 计划回收时间
            }
            if (taxAddress != null && !"".equals(taxAddress)) {
                preinvApply.setTaxAddress(DataCompilationUtil.encryptFromBase64(taxAddress));// 纳税人地址
            }
            if (taxBankAccount != null && !"".equals(taxBankAccount)) {
                preinvApply.setTaxBankAccount(taxBankAccount);// 纳税人银行账号
            }
            if (taxBankName != null && !"".equals(taxBankName)) {
                preinvApply.setTaxBankName(taxBankName);// 纳税人银行名称
            }
            if (taxPayer != null && !"".equals(taxPayer)) {
                preinvApply.setTaxPayer(taxPayer);// 纳税人识别号
            }
            if (taxPhone != null && !"".equals(taxPhone)) {
                preinvApply.setTaxPhone(taxPhone);// 纳税人电话
            }
            if (TaxName != null && !"".equals(TaxName) && !"undefined".equals(TaxName)) {
                preinvApply.setTaxName(DataCompilationUtil.encryptFromBase64(TaxName));// 纳税人名称
            }
            Date date = new Date();
            String invNo;
            if (preivType.equals("1") || preivType.equals("4") || preivType.equals("5")) {
                JSONArray jsonArray = JSONArray.fromObject(jsonString);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject object = jsonArray.getJSONObject(i);
                    JSONObject object1;
                    ApplyCycleDet applyCycleDet;
                    if (object.get("InvState").toString().equals("02")) { //增值税专用发票
                        JSONArray jsonArray2 = JSONArray.fromObject(object.get("ApplyCycleDet"));
                        for (int j = 0; j < jsonArray2.size(); j++) {
                            String stateTime = preinvApplyService.getNumber();
                            invNo = "KP" + "" + stateTime;
                            PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
                            preinvApplyDet.setBatchNo(batchNO);// 批次号
                            preinvApplyDet.setBossNo(user.getBossUserName());// boss编号
                            preinvApplyDet.setContrctNo(object.get("ContrctNo").toString());// 账户号码
                            preinvApplyDet.setContrctType(object.get("ContrctType").toString());// 账户类型
                            preinvApplyDet.setInvNo(invNo);// 发票编码
                            preinvApplyDet.setPhone(object.get("phone").toString());// 服务号码
                            preinvApplyDet.setInvState("0");// 发票状态
                            preinvApplyDet.setPactName(object.get("PactName").toString());// 合同名称
                            preinvApplyDet.setProductName(object.get("ProductName").toString());// 产品名称
                            preinvApplyDet.setRecDate(formatForDate(object.get("RecDate").toString()));// 计划回收日期
                            preinvApplyDet.setInvType(object.get("InvState").toString());// 发票类型
                            preinvApplyDet.setOpInvDate(new Date());// 开票时间
                            preinvApplyDet.setIsOver("0");// 未逾期
                            preinvApplyDet.setOpInvName(user.getEmployeeName());// 开票人名称
                            preinvApplyDet.setOpInvNo(String.valueOf(user.getRowNo()));// 开票人工号
                            preinvApplyDet.setJson(jsonArray2.get(j).toString());// 账期json字段
                            preinvApplyDet.setInvoice_limit(object.get("InvoiceLimit").toString());//是否申请突破限制(新)
                            preinvApplyDet.setTaxRate(object.get("taxRate").toString());

                            object1 = jsonArray2.getJSONObject(j);
                            logger.info("账期==" + object1.toString());
                            applyCycleDet = new ApplyCycleDet();

                            applyCycleDet.setAmout(decimalFormat.format(object1.getDouble("Amount") * 100));// 金额元转分
                            applyCycleDet.setCycleYM(object1.get("CycleYM").toString());// 账期
                            preinvApplyDet.setInvAmout(decimalFormat.format(object1.getDouble("Amount") * 100));// 开票金额
                            preinvApplyDet.setBeginCycle(object1.get("CycleYM").toString());// 开始账期
                            preinvApplyDet.setEndCycle(object1.get("CycleYM").toString());// 结束账期
                            applyCycleDet.setInvNo(invNo);// 发票编码
                            applyCycleDet.setInvoiceType(object1.getString("invoiceType").toString());// 发票状态
                            preinvApplyService.addappApplyCycleDet(applyCycleDet);// 新增账期
                            preinvApplyService.addPreinvApplyDet(preinvApplyDet);// 新增开票明细
                        }
                    } else {
                        String stateTime;
                        if (!object.get("InvState").toString().equals("01")) {//增值税电子发票
                            date.setTime(date.getTime() + 20000L);
                            stateTime = preinvApplyService.getNumber();
                            invNo = "KP" + stateTime;
                            PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
                            preinvApplyDet.setBatchNo(batchNO);
                            preinvApplyDet.setBossNo(this.user.getBossUserName());
                            preinvApplyDet.setContrctNo(object.get("ContrctNo").toString());
                            preinvApplyDet.setContrctType(object.get("ContrctType").toString());
                            preinvApplyDet.setInvNo(invNo);
                            preinvApplyDet.setPhone(object.get("phone").toString());
                            preinvApplyDet.setInvState("0");
                            preinvApplyDet.setPactName(object.get("PactName").toString());
                            preinvApplyDet.setProductName(object.get("ProductName").toString());
                            preinvApplyDet.setRecDate(this.formatForDate(object.get("RecDate").toString()));
                            preinvApplyDet.setInvType(object.get("InvState").toString());
                            preinvApplyDet.setOpInvDate(new Date());
                            preinvApplyDet.setIsOver("0");
                            preinvApplyDet.setOpInvName(this.user.getEmployeeName());
                            preinvApplyDet.setOpInvNo(String.valueOf(this.user.getRowNo()));
                            preinvApplyDet.setInvAmout(object.get("InvAmount").toString());
                            preinvApplyDet.setBeginCycle(getYearMonth());
                            preinvApplyDet.setEndCycle(getYearMonth());
                            preinvApplyDet.setInvoice_limit(object.get("InvoiceLimit").toString());//是否申请突破限制(新)
                            preinvApplyDet.setTaxRate(object.get("taxRate").toString());
                            this.preinvApplyService.addPreinvApplyDet(preinvApplyDet);
                        } else {
                            stateTime = preinvApplyService.getNumber();
                            invNo = "KP" + stateTime;
                            JSONArray jsonArray2 = JSONArray.fromObject(object.get("ApplyCycleDet"));
                            PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
                            preinvApplyDet.setBatchNo(batchNO);
                            preinvApplyDet.setBossNo(this.user.getBossUserName());
                            preinvApplyDet.setContrctNo(object.get("ContrctNo").toString());
                            preinvApplyDet.setContrctType(object.get("ContrctType").toString());
                            preinvApplyDet.setInvAmout(object.get("InvAmount").toString());
                            preinvApplyDet.setInvNo(invNo);
                            preinvApplyDet.setInvState("0");
                            preinvApplyDet.setIsOver("0");
                            preinvApplyDet.setPhone(object.get("phone").toString());
                            preinvApplyDet.setPactName(object.get("PactName").toString());
                            preinvApplyDet.setProductName(object.get("ProductName").toString());
                            preinvApplyDet.setRecDate(this.formatForDate(object.get("RecDate").toString()));
                            preinvApplyDet.setInvType(object.get("InvState").toString());
                            preinvApplyDet.setOpInvDate(new Date());
                            preinvApplyDet.setOpInvName(this.user.getEmployeeName());
                            preinvApplyDet.setOpInvNo(String.valueOf(this.user.getRowNo()));
                            preinvApplyDet.setJson(jsonArray2.toString());
                            preinvApplyDet.setInvoice_limit(object.get("InvoiceLimit").toString());//是否申请突破限制(新)
                            for (int j = 0; j < jsonArray2.size(); ++j) {
                                object1 = jsonArray2.getJSONObject(j);
                                applyCycleDet = new ApplyCycleDet();
                                applyCycleDet.setAmout(decimalFormat.format(object1.getDouble("Amount") * 100));// 金额元转分
                                applyCycleDet.setCycleYM(object1.get("CycleYM").toString());
                                applyCycleDet.setInvNo(invNo);
                                applyCycleDet.setInvoiceType(object1.getString("invoiceType").toString());
                                this.preinvApplyService.addappApplyCycleDet(applyCycleDet);
                            }

                            Object[] strings = this.preinvApplyService.findCycle(invNo);
                            preinvApplyDet.setBeginCycle(strings[1].toString());
                            preinvApplyDet.setEndCycle(strings[0].toString());
                            preinvApplyDet.setTaxRate(object.get("taxRate").toString());

                            this.preinvApplyService.addPreinvApplyDet(preinvApplyDet);
                        }
                    }
                }
            } else if (preivType.equals("3")) {
                JSONArray valuableCardDetObjArry = JSONArray.fromObject(jsonString);
                for (int j = 0; j < valuableCardDetObjArry.size(); j++) {
                    JSONObject obj = valuableCardDetObjArry.getJSONObject(j);
                    String stateTime = preinvApplyService.getNumber();
                    String number = "YJK" + "" + stateTime;
                    ValuableCardDet vcd = new ValuableCardDet();
                    NumberFormat format = NumberFormat.getInstance();
                    Number numberFormat = format.parse(obj.getString("ORDER_PRICE"));
                    double temp = numberFormat.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    vcd.setApplyNo(batchNO);
                    vcd.setInvNo(number);
                    vcd.setOrderPrice(amount);
                    vcd.setServiceNo(obj.getString("SERVICE_NO"));
                    vcd.setLoadingAccountNo(obj.getString("LOADING_ACCOUNT_NO"));
                    vcd.setLoadingPhone(obj.getString("LOADING_PHONE"));
                    vcd.setCustName(obj.getString("CUST_NAME"));
                    vcd.setIdIccid(obj.getString("ID_ICCID"));
                    vcd.setInvContractNo(obj.getString("INV_CONTRACT_NO"));
                    preinvApplyService.addValuableCardDet(vcd);
                }
            } else {
                JSONArray internetOfThingsObjArry = JSONArray.fromObject(jsonString);
                for (int j = 0; j < internetOfThingsObjArry.size(); j++) {
                    JSONObject obj = internetOfThingsObjArry.getJSONObject(j);
                    String stateTime = preinvApplyService.getNumber();
                    String number = "WLW" + "" + stateTime;
                    InternetOfThingsDet itd = new InternetOfThingsDet();
                    itd.setInv_No(number);
                    itd.setApply_no(batchNO);
                    itd.setAcct_id(obj.getString("ACCT_ID"));
                    itd.setAcct_name(obj.getString("ACCT_NAME"));
                    itd.setValidbillcyc(obj.getString("VALIDBILLCYC"));
                    itd.setPayment_time(obj.getString("PAYMENT_TIME"));
                    itd.setPayment_seq(obj.getString("PAYMENT_SEQ"));
                    itd.setSer_name(obj.getString("SER_NAME"));
                    itd.setTax_rate(obj.getString("TAX_RATE"));
                    itd.setTax_fee(obj.getString("TAX_FEE"));
                    itd.setInclude_tax_amt(obj.getString("INCLUDE_TAX_AMT"));
                    itd.setExcluded_tax_amt(obj.getString("EXCLUDED_TAX_AMT"));
                    itd.setCollection_tax_amt("0");
                    NumberFormat format = NumberFormat.getInstance();
                    Number numberFormat = format.parse(obj.getString("SPLIT_INCLUDE_TAX_AMT"));
                    double temp = numberFormat.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    itd.setSplit_include_tax_amt(amount);
                    String starTime = obj.getString("STARTIME").replace("-", "");
                    String endTime = obj.getString("ENDTIME").replace("-", "");
                    if (obj.getString("VOUCHERTYPE").equals("0")) {
                        starTime = endTime.substring(0, 6) + "00";
                        endTime = endTime.substring(0, 6) + "00";
                    }
                    itd.setCustomer_id(obj.getString("CUSTOMER_ID"));
                    itd.setOpr_seq(obj.getString("OPR_SEQ"));
                    itd.setOrder_seq(obj.getString("ORDER_SEQ"));
                    itd.setCust_id(obj.getString("CUST_ID"));
                    itd.setCustomer_type(obj.getString("CUSTOMER_TYPE"));
                    itd.setStarTime(starTime);
                    itd.setEndTime(endTime);
                    itd.setFrank(obj.getString("FRANK"));
                    itd.setVoucherType(obj.getString("VOUCHERTYPE"));
                    if (obj.getString("VOUCHERTYPE").equals(0)) {
                        itd.setPrepVoucher(obj.getString("PREPVOUCHER"));
                    } else {
                        itd.setPrepVoucher("0");
                    }
                    itd.setCommit_Type("1");
                    preinvApplyService.addInternetOfThingsDet(itd);
                }
            }
            // 新增信息
            PreinvApply preinvApply2 = preinvApplyService.addPreinvApply(preinvApply);


            /**
             * gcy
             * 再查询一次附件信息
             * 2020-10-12
             */
            if (id != null && id != "") {
                List<SingleAndAttachment> ss = preinvApplyService.findbyOrderId(id);// 根据工单id查询附件信息
                //遍历获取的附件中间表数据
                if (ss != null) {
                    System.out.println("ss" + ss);
                    for (int i = 0; i < ss.size(); i++) {
                        if (i == 0) {
                            if (!"".equals(attachmentId)) {
                                attachmentId += "," + ss.get(i).getAttachmentId() + ",";
                            } else {
                                attachmentId += ss.get(i).getAttachmentId() + ",";
                            }
                        } else {
                            attachmentId += ss.get(i).getAttachmentId() + ",";
                        }
                    }
                }
            }
            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(preinvApply2.getUuid());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(PreinvApply.PREINVAPPLY);
                            preinvApplyService.saveSandA(sa);
                        }
                    }
                }
            }

            if (!"CG".equals(type)) {
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
                // 流程启动
                Map<String, String> map = new HashMap<>();
                String processId = "";
                map.put("decisionKey", "APPLY");
                map.put("decisionValue", role);
                // 流程启动
                processId = transferJBPMUtils.startTransfer("PreinvApplyDetermineProcessFinal", map);
                // 保存信息到流程表
                PreinvApplyFlow preinvApplyFlow = new PreinvApplyFlow();
                preinvApplyFlow.setFlowId(processId);// 流程id
                preinvApplyFlow.setFlowName("preinvApply");// 流程名称
                preinvApplyFlow.setCreator(user.getRowNo() + "");// 创建人编号
                preinvApplyFlow.setCreateDate(new Date());// 创建时间
                preinvApplyFlow.setFlowType("开票工单审批");// 流程类型
                preinvApplyFlow.setState("0");// 流程状态
                preinvApplyFlow.setBatchNo(batchNO);// 预开票工单编号
                preinvApplyFlow.setCreatorName(user.getEmployeeName());// 创建人名称
                preinvApplyFlow.setDealNo(USER.getRowNo() + "");// 下一步处理人编号
                preinvApplyFlow.setDealName(USER.getEmployeeName());// 下一步处理人名称
                preinvApplyService.addPreinvApplyFlow(preinvApplyFlow);// 保存信息到流程表
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                // 保存信息到任务表
                PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
                preinvApplyTask.setFlowId(processId);// 流程id
                preinvApplyTask.setTaskId(task.getId());// 环节id
                if ("YXRY".equals(type)) {
                    preinvApplyTask.setTaskName("一线人员");// 环节名称
                } else {
                    preinvApplyTask.setTaskName("客户经理");// 环节名称
                }
                preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
                preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
                preinvApplyTask.setCreateDate(new Date());// 创建时间
                preinvApplyTask.setDealNo(user.getRowNo() + "");// 处理人编号
                preinvApplyTask.setDealName(user.getEmployeeName());// 处理人名称
                preinvApplyTask.setDealDate(new Date());// 处理时间
                preinvApplyTask.setState("1");// 状态
                preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
                // 保存下一步任务
                Task taskTwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                PreinvApplyTask pTask = new PreinvApplyTask();
                pTask.setFlowId(processId);// 流程id
                pTask.setTaskId(taskTwo.getId());// 环节id
                if ("YXRY".equals(type)) {
                    pTask.setTaskName("客户经理");// 环节名称
                } else {
                    pTask.setTaskName(taskTwo.getActivityName());// 环节名称
                }
                pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
                pTask.setCreatorName(user.getEmployeeName());// 创建人名称
                pTask.setCreateDate(new Date());// 创建时间
                pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
                pTask.setDealName(USER.getEmployeeName());// 处理人名称
                pTask.setState("0");// 状态
                preinvApplyService.addPreinvApplyTask(pTask);// 保存信息到任务表
                commitBackLog(preinvApply2, userId, processId, "", user, pTask);// 生成待办
            }
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info(e.getMessage(), e);
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 流程进行
     */
    public void handlePreinvApply() {
        try {
            String pid = getString("processId");// 流程id
            String oldProcessId = getString("oldProcessId");// 原流程id
            String t = getString("t");// 下一步可执行流程线条值
            String userid = getString("userId");// 用户id
            String id = getString("id");// 开票id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String taskId = getString("preinvApplyTaskid");// 任务id
            String phone = getString("phone");
            String flag = getString("flag");//1:突破限制 到省公司管理员
            String flags = getString("flags");//是否一线人员发起

            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
                WaitTask waitTask = preinvApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                taskId = waitTask.getTaskId();
            }
            PreinvApplyTask ptask = preinvApplyService.getTaskList(taskId);// 根据任务id查询开票任务表信息
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            if (ptask != null) {
                ptask.setDealDate(new Date());// 操作时间
                ptask.setTaskMemo(opinion);// 处理意见
                ptask.setState("1");// 修改状态为处理完成
                preinvApplyService.updatePreinvApplyTask(ptask);// 修改任务表信息
            }
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
            WaitTask wt = service.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
            // 结束当前待办
            if (wt != null) {
                System.out.println("================处理中开始代办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            if (!"true".equals(flags)) {
                Map<String, String> map = new HashMap<>();
                String str = pid.substring(0, pid.indexOf("."));
                if ("PreinvApply".equals(str)) {
                    if ("区县分管经理".equals(task.getActivityName())) {
                        map.put("decisionKey", "ROLE_DSBM");
                        map.put("decisionValue", "NO");
                        jbpmUtil.completeTask(task.getId(), map);// 流程流转
                    } else if ("省重客客户经理室经理".equals(task.getActivityName())) {
                        map.put("decisionKey", "ROLE_SZKSM");
                        map.put("decisionValue", "NO");
                        jbpmUtil.completeTask(task.getId(), map);// 流程流转
                    } else if ("市公司政企部经理".equals(task.getActivityName())) {
                        map.put("decisionKey", "ROLE_DSSM");
                        map.put("decisionValue", "NO");
                        if (t.equals("ALL")) {
                            jbpmUtil.completeTask(task.getId(), t);
                        } else {
                            jbpmUtil.completeTask(task.getId(), map, t);
                        }
                    } else {
                        jbpmUtil.completeTask(task.getId(), t);
                    }
                } else if ("PreinvApplyLimitProcessFinal".equals(str)) {
                    if ("市公司政企部经理".equals(task.getActivityName())) {
                        map.put("node", "大于");
                        jbpmUtil.completeTask(task.getId(), map, t);
                    } else {
                        this.jbpmUtil.completeTask(task.getId(), t);
                    }
                } else {
                    if ("1".equals(flag)) {
                        map.put("decisionKey", flag);
                        jbpmUtil.completeTask(task.getId(), map, t);
                    } else {
                        if ("区县分管经理".equals(task.getActivityName())) {
                            map.put("decisionKey", "ROLE_DSBM");
                            map.put("decisionValue", "NO");
                            jbpmUtil.completeTask(task.getId(), map);// 流程流转
                        } else if ("区县业务管理员".equals(task.getActivityName())) {
                            map.put("decisionKey", "ROLE_QXDM");
                            map.put("decisionValue", "YES");
                            jbpmUtil.completeTask(task.getId(), map);// 流程流转
                        } else if ("区县政企部主任".equals(task.getActivityName())) {
                            map.put("decisionKey", "ROLE_QXSM");
                            map.put("decisionValue", "YES");
                            jbpmUtil.completeTask(task.getId(), map);// 流程流转
                        } else if ("省重客客户经理室经理".equals(task.getActivityName())) {
                            map.put("decisionKey", "ROLE_SZKSM");
                            map.put("decisionValue", "NO");
                            jbpmUtil.completeTask(task.getId(), map);// 流程流转
                        } else if ("市公司政企部经理".equals(task.getActivityName())) {
                            map.put("decisionKey", "ROLE_DSSM");
                            map.put("decisionValue", "NO");
                            if (t.equals("ALL")) {
                                jbpmUtil.completeTask(task.getId(), t);
                            } else {
                                jbpmUtil.completeTask(task.getId(), map, t);
                            }
                        } else if ("市公司客户经理室经理".equals(task.getActivityName())) {
                            if ("PreinvApplyDetermineProcessFinal".equals(str)) {
                                map.put("node", "大于");
                                jbpmUtil.completeTask(task.getId(), map, t);
                            } else {
                                this.jbpmUtil.completeTask(task.getId(), t);
                            }
                        } else if ("市公司客户经理室经理".equals(task.getActivityName())) {
                            map.put("node", "大于");
                            jbpmUtil.completeTask(task.getId(), map, t);
                        } else if ("市公司领导".equals(task.getActivityName())) {
                            map.put("node", "突破限制");
                            jbpmUtil.completeTask(task.getId(), map, t);
                        } else if ("省重客分管经理".equals(task.getActivityName())) {
                            map.put("node", "突破限制");
                            jbpmUtil.completeTask(task.getId(), map, t);
                        } else {
                            jbpmUtil.completeTask(task.getId(), t);
                        }
                    }
                }
            }

            PreinvApplyFlow flow = preinvApplyService.getPreinvApplyFlow(pid);// 根据流程id查询流程表信息
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            // 修改流程表信息
            flow.setDealName(USER.getEmployeeName());// 处理人名称
            flow.setDealNo(USER.getRowNo() + "");// 处理人编号
            flow.setUpdateDate(new Date());// 更新时间
            flow.setState("0");// 状态为审批中
            preinvApplyService.updatePreinvApplyFlow(flow);// 修改流程表信息
            // 新增任务表信息
            PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
            if (oldProcessId != null && !"".equals(oldProcessId) && !"null".equals(oldProcessId)) {
                preinvApplyTask.setFlowId(oldProcessId);// 流程id
            } else {
                preinvApplyTask.setFlowId(pid);// 流程id
            }
            preinvApplyTask.setTaskId(tasktwo.getId());// 环节id
            if ("true".equals(flags)) {
                preinvApplyTask.setTaskName(task.getActivityName());// 环节名称
            } else {
                preinvApplyTask.setTaskName(tasktwo.getActivityName());// 环节名称
            }

            preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyTask.setCreateDate(new Date());// 创建时间
            preinvApplyTask.setDealNo(USER.getRowNo() + "");// 处理人编号
            preinvApplyTask.setDealName(USER.getEmployeeName());// 处理人名称
            preinvApplyTask.setState("0");// 状态
            preinvApplyService.updatePreinvApply(preinvApply);
            preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
            commitBackLog(preinvApply, userid, pid, oldProcessId, user, preinvApplyTask);// 生成待办
            Write("YES");
        } catch (Exception e) {
            logger.error("预开票提交失败==>" + e.getMessage(), e);
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 退回方法
     */
    public void returnPreinvApply() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String oldProcessId = getString("oldProcessId");// 原流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("preinvApplyTaskid");// 任务表id
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
                WaitTask waitTask = preinvApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                taskId = waitTask.getTaskId();
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                System.out.println("================退回开始代办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================退回结束代办================");
            } else {
                Write("NO");
                return;
            }
            PreinvApplyTask pTask = preinvApplyService.getTaskList(taskId);// 根据任务表id查询任务表信息
            if (pTask != null) {
                pTask.setDealDate(new Date());// 处理时间
                pTask.setTaskMemo(opinion);// 处理意见
                pTask.setState("1");// 状态修改为已完成
                preinvApplyService.updatePreinvApplyTask(pTask);// 修改任务表信息
            }
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据开票id查询开票信息
            preinvApply.setUpdateDate(new Date());
            if (preinvApply.getOprType().equals("1")) {
                preinvApply.setReverseSatate("6");// 修改状态为作废退回

            } else {
                preinvApply.setStartState("2");// 修改状态为退回
                preinvApply.setHandleState("2");// 可重新开票
            }

            preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息

            returnBackLog(preinvApply, preinvApply.getCreatorId(), processId, oldProcessId, user, pTask);// 生成待办
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 完成方法
     */
    public void complatePreinvApply() {
        Map<String, Object> map = new HashMap<>();
        try {
            String pid = getString("processId");// 流程id
            String oldProcessId = getString("oldProcessId");// 原流程id
            String id = getString("id");// 开票id
            if (pid == null) {
                PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
                PreinvApplyFlow rap = preinvApplyService.getPid(preinvApply.getBatchNo());
                pid = rap.getFlowId();
            }
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 审批意见
            String taskId = getString("preinvApplyTaskid");// 任务表id
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
                WaitTask waitTask = preinvApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                taskId = waitTask.getTaskId();
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);// 获取待办信息
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
            System.out.println("查询出来的工单名为==" + preinvApply.getAppTitle());
            if (preinvApply.getStartState().equals("-3")) {
                map.put("flag", "NEWERROR");
                map.put("message", preinvApply.getErrorMessage());
                Write(JSONHelper.Serialize(map));
                return;
            }

            if (wt != null) {
                System.out.println("================完成开始待办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================完成结束待办================");
            } else {
                map.put("flag", "NO");
                map.put("message", "操作失败");
                Write(JSONHelper.Serialize(map));
                return;
            }
            jbpmUtil.deleteProcessInstance(pid);// 删除流程
            PreinvApplyFlow flow = preinvApplyService.getPreinvApplyFlow(pid);// 根据流程id查询流程表信息
            // 修改流程表信息
            flow.setUpdateDate(new Date());// 更新时间
            flow.setState("1");// 状态为已完成
            preinvApplyService.updatePreinvApplyFlow(flow);// 修改流程表信息

            PreinvApplyTask pTask = preinvApplyService.getTaskList(taskId);// 根据id查询任务表信息
            if (pTask != null) {
                pTask.setDealDate(new Date());// 处理日期
                pTask.setTaskMemo(opinion);// 处理意见
                pTask.setState("1");// 修改状态为已完成
                preinvApplyService.updatePreinvApplyTask(pTask);// 修改任务表信息
            }

            // 新增任务表信息
            PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
            if (oldProcessId != null && !"".equals(oldProcessId)) {
                preinvApplyTask.setFlowId(oldProcessId);// 流程id
            } else {
                preinvApplyTask.setFlowId(pid);// 流程id
            }
            preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            preinvApplyTask.setTaskName("客户经理");// 环节名称
            preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyTask.setCreateDate(new Date());// 创建时间
            preinvApplyTask.setDealNo(preinvApply.getCreatorId());// 处理人编号
            preinvApplyTask.setDealName(preinvApply.getCreatorName());// 处理人名称
            preinvApplyTask.setState("0");// 状态
            preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
            handBackLog(preinvApply, preinvApply.getCreatorId(), pid, oldProcessId, user, preinvApplyTask);// 生成待办
            map.put("flag", "YES");
            map.put("message", "操作成功");
            preinvApply.setStartState("3");//工单状态: 审批完成待推送
            preinvApply.setHANDLER_ID("");
            PreinvApply preinvApply1 = preinvApplyService.updatePreinvApply(preinvApply);
            //System.out.println("修改后的工单名为==" + JSONHelper.Serialize(map));
            Write(JSONHelper.Serialize(map));
        } catch (Exception e) {
            e.printStackTrace();
            map.put("flag", "NO");
            map.put("message", "操作失败");
            Write(JSONHelper.Serialize(map));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 作废方法
     */
    public void InvalidPreinvApply() {
        try {
            String id = getString("id");// 开票id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 作废原因
            String taskId = getString("preinvApplyTaskid");// 任务表id
//            String processId = getString("processId");// 流程id\
            //id=ff80808187b6b68d0187b79949f57a49
            // &processId=null
            // &preinvApplyTaskid=ff80808187bd20f60187c1e60cfc6cdd
            // &waitId=ff80808187bd20830187c1fe58862938
            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
                WaitTask waitTask = preinvApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                taskId = waitTask.getTaskId();
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            PreinvApplyTask pTask = preinvApplyService.getTaskList(taskId);// 根据id查询任务表信息
            if (pTask != null) {
                pTask.setDealDate(new Date());// 处理日期
                pTask.setTaskMemo(opinion);// 处理意见
                pTask.setState("-1");// 修改状态为本地作废
                preinvApplyService.updatePreinvApplyTask(pTask);// 修改任务表信息
            }
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询
            if (preinvApply.getOprType().equals("1")) {
                preinvApply.setReverseSatate("-1");// 状态修改为作废
            } else {
                preinvApply.setStartState("-1");// 状态修改为作废
            }
            preinvApply.setUpdateDate(new Date());// 更新时间
            preinvApply.setCancelMemo(opinion);// 作废原因
            preinvApply.setHANDLER_ID("");
            preinvApplyService.updatePreinvApply(preinvApply);// 修改
//            jbpmUtil.deleteProcessInstance(processId);// 删除流程
            if (wait != null) {
                System.out.println("================作废开始待办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================作废结束待办================");
            } else {
                Write("NO");
                return;
            }
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");

        }

    }

    /**
     * 阅读方法
     */
    public void readPreinvApply() {

        logger.info("运行预开票阅读方法");
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");// 待办id
            String waitId = getString("waitId");// 待办id
            String taskId = getString("preinvApplyTaskid");// 任务id
            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
                WaitTask waitTask = preinvApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                taskId = waitTask.getTaskId();
            }
            PreinvApplyTask preinvApplyTask = preinvApplyService.getTaskList(taskId);// 查询任务表信息
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
//            System.out.println(isBase64Encoded(preinvApply.getTaxAddress()));
//            System.out.println(isBase64Encoded(preinvApply.getTaxName()));
            String TaxAddress=preinvApply.getTaxAddress();
            String TaxName=preinvApply.getTaxName();
            preinvApply.setTaxAddress(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxAddress()));
            preinvApply.setTaxName(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxName()));
            if ("2".equals(preinvApply.getPreivType())) {
                //推送BOSS物联网预开票
                List<InternetOfThingsDet> list = preinvApplyService.getInternetOfThingsDetByApplyNo(preinvApply.getBatchNo());
                SystemUser sysUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(preinvApply.getCreatorId()));
                map = preinvApplyService.sWlwIssueInvApply(preinvApply, list, sysUser);
                String flag = (String) map.get("flag");
                if ("YES".equals(flag)) {
                    if (preinvApplyTask != null) {
                        preinvApplyTask.setDealDate(new Date());// 处理日期
                        preinvApplyTask.setTaskMemo("");// 处理意见
                        preinvApplyTask.setState("1");// 修改状态为已完成
                        preinvApplyService.updatePreinvApplyTask(preinvApplyTask);// 修改任务表信息
                    }
                    //1：申请冲正预开票
                    if (preinvApply.getOprType().equals("1")) {
                        PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                        preinvApply2.setHandleState("2");// 可重新开票
                        preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                    }
                    WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办任务
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        map.put("flag", "NO");
                        map.put("message", "操作失败");
                        Write(JSONHelper.Serialize(map));
                        throw new RuntimeException("事务回滚");
                    }
                    if (preinvApply.getOprType().equals("1")) {
                        preinvApply.setReverseSatate("5");
                    } else if (preinvApply.getOprType().equals("0")) {
                        preinvApply.setStartState("1");// 修改状态为已完成
                        preinvApply.setHandleState("2");// 可重新开票
                    }
                    map.put("flag", "YES");
                    map.put("message", "操作成功");
                    preinvApply.setUpdateDate(new Date());
                    preinvApply.setHANDLER_ID(null);
                    //恢复值保证更新到数据库正常
                    preinvApply.setTaxAddress(TaxAddress);
                    preinvApply.setTaxName(TaxName);
                    preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
                    Write(JSONHelper.Serialize(map));
                } else {
                    map.put("flag", "NO");
                    map.put("message", "推送BOSS失败" + map.get("message"));
                    Write(JSONHelper.Serialize(map));
                }
            } else if ("3".equals(preinvApply.getPreivType())) {
                //推送BOSS有价卡预开票
                List<ValuableCardDet> list = preinvApplyService.getValuableCardDetByApplyNo(preinvApply.getBatchNo());
                SystemUser sysUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(preinvApply.getCreatorId()));
                String bossJson = pressGetLoginMsgSvc(sysUser.getBossUserName());
                JSONObject orgInfoJson = JSONObject.fromObject(bossJson);
                String orgInfo = "";
                if (orgInfoJson.has("GROUP_ID")) {
                    orgInfo = orgInfoJson.get("GROUP_ID").toString();
                } else {
                    logger.error("查询客户经理渠道信息失败");
                    Write("NO");
                    return;
                }
                map = preinvApplyService.saveBossValuableCardDet(preinvApply, list, sysUser, orgInfo);
                String flag = (String) map.get("flag");
                if ("YES".equals(flag)) {
                    list.get(0).setReData((String) map.get("message"));
                    preinvApplyService.updateValuableCardDet(list.get(0));
                    if (preinvApplyTask != null) {
                        preinvApplyTask.setDealDate(new Date());// 处理日期
                        preinvApplyTask.setTaskMemo("");// 处理意见
                        preinvApplyTask.setState("1");// 修改状态为已完成
                        preinvApplyService.updatePreinvApplyTask(preinvApplyTask);// 修改任务表信息
                    }
                    WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办任务
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        map.put("flag", "NO");
                        map.put("message", "操作失败");
                        Write(JSONHelper.Serialize(map));
                        throw new RuntimeException("事务回滚");
                    }
                    if (preinvApply.getOprType().equals("1")) {
                        preinvApply.setReverseSatate("5");
                    } else if (preinvApply.getOprType().equals("0")) {
                        preinvApply.setStartState("1");// 修改状态为已完成
                        preinvApply.setHandleState("2");// 可重新开票
                    }
                    map.put("flag", "YES");
                    map.put("message", "操作成功");
                    preinvApply.setUpdateDate(new Date());
                    preinvApply.setHANDLER_ID(null);
                    //恢复值保证更新到数据库正常
                    preinvApply.setTaxAddress(TaxAddress);
                    preinvApply.setTaxName(TaxName);
                    preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
                    Write(JSONHelper.Serialize(map));
                } else {
                    map.put("flag", "NO");
                    map.put("message", "推送BOSS失败" + map.get("message"));
                    Write(JSONHelper.Serialize(map));
                }
            } else {
                map = preinvApplyService.savePreinvApply(preinvApply);
                logger.info("预开票推送返回==" + JSONObject.fromObject(map));
                String flag = (String) map.get("flag");
                if ("YES".equals(flag)) {
                    List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                    if (preinvApplyTask != null) {
                        preinvApplyTask.setDealDate(new Date());// 处理日期
                        preinvApplyTask.setTaskMemo("");// 处理意见
                        preinvApplyTask.setState("1");// 修改状态为已完成
                        preinvApplyService.updatePreinvApplyTask(preinvApplyTask);// 修改任务表信息
                    }
                    //1：申请冲正预开票
                    if (preinvApply.getOprType().equals("1")) {
                        if (list.size() > 0) {
                            List<PreinvApplyDet> listo = preinvApplyService.findByCommitTypeTwo(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                            for (PreinvApplyDet preinvApplyDet : listo) {
                                preinvApplyDet.setInvState("2");//状态改为已冲正
                                preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                            }
                            PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                            List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                            for (PreinvApplyDet newApplyDet : listo) {
                                for (PreinvApplyDet oldApplyDet : listdets) {
                                    // 修改原订单的账户状态
                                    if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                            && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                        oldApplyDet.setInvState("2");// 状态改为已冲正
                                        preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                                    }
                                }
                            }
                            preinvApply2.setHandleState("2");// 可重新开票
                            preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                        } else {
                            List<PreinvApplyDet> listdet = preinvApplyService.findByUUID(preinvApply.getBatchNo());// 查询账户
                            for (PreinvApplyDet preinvApplyDet : listdet) {
                                preinvApplyDet.setInvState("2");// 状态改为已冲正
                                preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                            }
                            PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                            List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                            for (PreinvApplyDet newApplyDet : listdet) {
                                for (PreinvApplyDet oldApplyDet : listdets) {
                                    // 修改原订单的账户状态
                                    if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                            && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                        oldApplyDet.setInvState("2");// 状态改为已冲正
                                        preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                                    }
                                }
                            }
                            preinvApply2.setHandleState("2");// 可重新开票
                            preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                        }
                    }
                    WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办任务
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        map.put("flag", "NO");
                        map.put("message", "操作失败");
                        Write(JSONHelper.Serialize(map));
                        throw new RuntimeException("事务回滚");
                    }
                    if (list.size() > 0) {
                        String msg = "";
                        for (int i = 0; i < list.size(); i++) {
                            msg += "账户号码:" + list.get(i).getContrctNo() + "-失败原因:" + list.get(i).getErrorMessage() + ";";
                        }
                        map.put("flag", "YES");
                        map.put("message", "操作成功!部分数据未推送到BOSS请至列表重新推送;" + msg);
                        if (preinvApply.getOprType().equals("1")) {
                            preinvApply.setReverseSatate("7");
                        } else {
                            preinvApply.setStartState("7");
                            preinvApply.setHandleState("2");// 可重新开票
                        }
                    } else {
                        if (preinvApply.getOprType().equals("1")) {
                            preinvApply.setReverseSatate("5");
                        } else {
                            preinvApply.setStartState("1");// 修改状态为已完成
                            preinvApply.setHandleState("2");// 可重新开票
                        }
                        map.put("flag", "YES");
                        map.put("message", "操作成功");
                    }
                    preinvApply.setUpdateDate(new Date());
                    preinvApply.setHANDLER_ID(null);
                    //恢复值保证更新到数据库正常
                    preinvApply.setTaxAddress(TaxAddress);
                    preinvApply.setTaxName(TaxName);
                    preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
                    Write(JSONHelper.Serialize(map));
                } else {
                    List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                    logger.info("list==" + JSONArray.fromObject(list));
                    String msg = "";
                    for (int i = 0; i < list.size(); i++) {
                        msg += "账户号码:" + list.get(i).getContrctNo() + "-失败原因:" + list.get(i).getErrorMessage() + ";";
                    }
                    map.put("flag", "NO");
                    if (list.size() > 1) {
                        map.put("message", "推送BOSS失败;" + msg);
                    } else {
                        String message = (String) map.get("message");
                        map.put("message", message);
                    }
                    Write(JSONHelper.Serialize(map));
                }
            }
        } catch (Exception e) {
            logger.error("预开票推送失败==" + e.getMessage(), e);
            e.printStackTrace();
            map.put("flag", "NO");
            map.put("message", "操作失败");
            Write(JSONHelper.Serialize(map));
            throw new RuntimeException("事务回滚");

        }
    }

    /**
     * 提交待办生成
     */
    public void commitBackLog(PreinvApply preinvApply, String userid, String processId, String oldProcessId, SystemUser user,
                              PreinvApplyTask preinvApplyTask) {
        WaitTask waitTask = new WaitTask();
        if (preinvApply.getOprType().equals("1")) {
            waitTask.setName("[预开票工单冲正]" + preinvApply.getAppTitle());// 待办名称
        } else {
            waitTask.setName("[预开票]" + preinvApply.getAppTitle());// 待办名称
        }
        waitTask.setCreationTime(new Date());// 代办生成时间
//        waitTask.setUrl("jsp/preinvApply/handlePreinvApply.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId + "&billsTaskid="
//                + preinvApplyTask.getId() + "&oldProcessId=" + oldProcessId);
        waitTask.setUrl("jsp/preinvApply/handlePreinvApply.jsp");
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode("YKP");// 标识
        waitTask.setTaskId(preinvApplyTask.getId());
        waitTask.setOrderNo(preinvApply.getBatchNo());
        service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * 完成待办生成
     */
    public void handBackLog(PreinvApply preinvApply, String userid, String processId, String oldProcessId, SystemUser user,
                            PreinvApplyTask preinvApplyTask) {
        WaitTask waitTask = new WaitTask();
        if (preinvApply.getOprType().equals("1")) {
            waitTask.setName("[预开票工单冲正]" + preinvApply.getAppTitle());// 待办名称
        } else {
            waitTask.setName("[预开票]" + preinvApply.getAppTitle());// 待办名称
        }
        waitTask.setCreationTime(new Date());// 代办生成时间
//        waitTask.setUrl("jsp/preinvApply/completePreinvApplay.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId + "&billsTaskid="
//                + preinvApplyTask.getId() + "&oldProcessId=" + oldProcessId);
        waitTask.setUrl("jsp/preinvApply/completePreinvApplay.jsp");
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
//        waitTask.setCode(PreinvApply.PREINVAPPLY);// 标识
//        waitTask.setTaskId(preinvApplyTask.getId());
        waitTask.setCode("YKP");// 标识
        waitTask.setTaskId(preinvApplyTask.getId());
        waitTask.setOrderNo(preinvApply.getBatchNo());
        service.saveWaitPushMOA(waitTask, this.getRequest());
    }


    /**
     * 转发待办生成
     */
    public void forwardToDo(PreinvApply preinvApply, String userid, String processId, PreinvApplyTask preinvApplyTask, String type, SystemUser user) {
        String ktype = "K";
        WaitTask waitTask = new WaitTask();
        if (preinvApply.getOprType().equals("1")) {
            waitTask.setName("[预开票工单冲正]" + preinvApply.getAppTitle());// 待办名称
        } else {
            waitTask.setName("[预开票]" + preinvApply.getAppTitle());// 待办名称
        }
        waitTask.setCreationTime(new Date());// 代办生成时间
//        waitTask.setUrl("jsp/preinvApply/completePreinvApplay.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId + "&billsTaskid="
//                + preinvApplyTask.getId() + "&type=" + type + "&ktype=" + ktype);
        waitTask.setUrl("jsp/preinvApply/completePreinvApplay.jsp?type=" + type + "&ktype=" + ktype);
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
//        waitTask.setCode(PreinvApply.PREINVAPPLY);// 标识
//        waitTask.setTaskId(preinvApplyTask.getId());
        waitTask.setCode("YKP");// 标识
        waitTask.setTaskId(preinvApplyTask.getId());
        waitTask.setOrderNo(preinvApply.getBatchNo());
        service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    public void daiban(ManualInvApply manualInvApply, String userid, String processId, ManualInvApplyTask manualInvApplyTask, String type) {
        String ktype = "K";
        WaitTask wt = new WaitTask();
        wt.setName("[特殊机打发票]" + manualInvApply.getOrderTitle());
        wt.setCreationTime(new Date());
        wt.setUrl("jsp/manualInvApply/handleManualInvApply.jsp?id=" + manualInvApply.getId() + "&manualInvApplyTaskId="
                + manualInvApplyTask.getUuid() + "&processId=" + processId + "&type=" + type + "&ktype=" + ktype);
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
        wt.setState(WaitTask.HANDLE);
        wt.setHandleUserId(USER.getRowNo());
        wt.setHandleUserName(USER.getEmployeeName());
        wt.setHandleLoginName(USER.getLoginName());
        wt.setCreateUserId(user.getRowNo());
        wt.setCreateUserName(user.getEmployeeName());
        wt.setCreateLoginName(user.getLoginName());
        wt.setCode(ManualInvApply.ManualInvApply);
        wt.setTaskId(manualInvApply.getId());
        service.saveWaitPushMOA(wt, this.getRequest());
    }

    /**
     * 退回待办生成
     */
    public void returnBackLog(PreinvApply preinvApply, String userid, String processId, String oldProcessId, SystemUser user,
                              PreinvApplyTask preinvApplyTask) {
        WaitTask waitTask = new WaitTask();

        waitTask.setCreationTime(new Date());// 代办生成时间
        if (preinvApply.getOprType().equals("1")) {
            waitTask.setName("[预开票冲正退回]" + preinvApply.getAppTitle());// 待办名称
//            waitTask.setUrl("jsp/preinvApply/invalidReturnPreinvApply.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId
//                    + "&preinvApplyTaskid=" + preinvApplyTask.getId() + "&oldProcessId=" + oldProcessId);
            waitTask.setUrl("jsp/preinvApply/invalidReturnPreinvApply.jsp");
        } else {
            waitTask.setName("[预开票退回]" + preinvApply.getAppTitle());// 待办名称
            waitTask.setUrl("jsp/preinvApply/returnPreinvApply.jsp");
        }
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
//        waitTask.setCode(PreinvApply.PREINVAPPLY);// 标识
//        waitTask.setTaskId(preinvApplyTask.getId());
        waitTask.setCode("YKP");// 标识
        waitTask.setTaskId(preinvApplyTask.getId());
        waitTask.setOrderNo(preinvApply.getBatchNo());
        service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * 根据id查询开票信息
     */
    public void findById() {
        try {
            String id = getString("id");
            PreinvApply preinvApply = preinvApplyService.findById(id);
            String taxAddress = preinvApply.getTaxAddress();
            String taxName = preinvApply.getTaxName();
            if ("".equals(preinvApply.getTaxAddress()) || preinvApply.getTaxAddress() == null) {
                preinvApply.setTaxAddress(DataCompilationUtil.decryptFromBase64(taxAddress));
            }

            if ("".equals(preinvApply.getTaxName()) || preinvApply.getTaxName() == null) {
                preinvApply.setTaxName(DataCompilationUtil.decryptFromBase64(taxName));
            }
            JSONObject jsonObject = JSONObject.fromObject(preinvApply);
            if (preinvApply.getCreateDate() != null) {
                jsonObject.put("createDate", getStringDatethree(preinvApply.getCreateDate()));
            }
            if (preinvApply.getRecDate() != null) {
                jsonObject.put("recDate", getStringDatethree(preinvApply.getRecDate()));
            }
            if (preinvApply.getUpdateDate() != null) {
                jsonObject.put("updateDate", getStringDatethree(preinvApply.getUpdateDate()));
            }
            Write(jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");

        }
    }

    /**
     * 查询账户信息
     *
     * @params
     */
    public void findBybatchNo() {
        try {
            String batchNo = getString("batchNo");
            String preivType = getString("preivType");
            if ("2".equals(preivType)) {
                List<InternetOfThingsDet> list = preinvApplyService.getInternetOfThingsDetByApplyNo(batchNo);
                JSONArray jsonArray = JSONArray.fromObject(list);
                Write(jsonArray.toString());
            }
            if ("3".equals(preivType)) {
                List<ValuableCardDet> list = preinvApplyService.getValuableCardDetByApplyNo(batchNo);
                JSONArray jsonArray = JSONArray.fromObject(list);
                Write(jsonArray.toString());
            } else {
                List<PreinvApplyDet> list = preinvApplyService.findByUUID(batchNo);
                JSONArray jsonArray = JSONArray.fromObject(list);

                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    PreinvApplyDet preinvApplyDet = list.get(i);
                    List<Map<String, Object>> listApply = preinvApplyService.findByInvNo(preinvApplyDet.getInvNo());
                    JSONArray array = JSONArray.fromObject(listApply);
                    jsonObject.put("ApplyCycleDet", array);
                    if (preinvApplyDet.getRecDate() != null) {
                        jsonObject.put("recDate", getStringDatethree(preinvApplyDet.getRecDate()));
                    }
                    if (preinvApplyDet.getOpInvDate() != null) {
                        jsonObject.put("opInvDate", getStringDatethree(preinvApplyDet.getOpInvDate()));
                    }
                    if (preinvApplyDet.getRealRecDate() != null)
                        jsonObject.put("realRecDate", getStringDatethree(preinvApplyDet.getRealRecDate()));
                }
                Write(jsonArray.toString());
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 查询账期信息
     *
     * @params
     */
    public void findByInvNO() {
        try {
            String invNo = getString("invNo");
            List<Map<String, Object>> list = preinvApplyService.findByInvNo(invNo);
            JSONArray jsonArray = JSONArray.fromObject(list);
            Write(jsonArray.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");

        }
    }

    /**
     * 根据ID查询跟踪处理
     *
     * @return
     * @throws ParseException
     */
    public void processtracking() throws ParseException {
        try {
            String id = getString("id");// 流程id
            List<PreinvApplyTask> p = preinvApplyService.processtracking(id);
            JSONArray jsonArray = JSONArray.fromObject(p);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                PreinvApplyTask preinvApplyTask = p.get(i);
                if (preinvApplyTask.getCreateDate() != null) {
                    jsonObject.put("createDate", getStringDateFour(preinvApplyTask.getCreateDate()));
                }
                if (preinvApplyTask.getDealDate() != null) {
                    jsonObject.put("dealDate", getStringDateFour(preinvApplyTask.getDealDate()));
                }
            }
            Write(jsonArray.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 获取附件消息
     */
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = preinvApplyService.fuJian(id, biaoshi);
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDate(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换2
     *
     * @param strDate
     * @return
     * @throws ParseException
     */
    public Date formatForDate(String strDate) throws ParseException {
        Date date = null;
        strDate = strDate + " 00:00:00";
        if (strDate != null && !"".equals(strDate)) {
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = format.parse(strDate);
        }
        return date;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatetwo(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @params
     */
    public static String getStringDateFour(String currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     * @throws ParseException
     */
    public static String getStringDatethree(Date currentTime) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd ");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     * @throws ParseException
     */
    public static String getStringDateFour(Date currentTime) throws ParseException {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 根据开票id查询流程id
     */
    public void findByPreinvId() {
        try {
            String state = getString("state");// 账单状态
            String id = getString("id");// 开票id
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询
            PreinvApplyFlow preinvApplyFlow = preinvApplyService.findbyPreinvBatchNo(preinvApply.getBatchNo(), state);
            Write(preinvApplyFlow.getFlowId());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 获取预开票ID
     */
    public void findPreinvByBatchNo() {
        try {
            String batchNo = getString("batchNo"); // 编号
            PreinvApply preinvApply = preinvApplyService.getPreinvApplyByBachNo(batchNo);
            JSONObject jsonObject = JSONObject.fromObject(preinvApply);
            Write(jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 新冲正流程
     */
    public void startCorrectPreinvApply() {

        try {
            String id = getString("id");// 开票id
            String role = getString("role");// 角色权限
            String userId = getString("userId");// 下一步任务人id
            String cancelMemo = getString("cancelMemo");// 作废原因
            String appTitle = getString("appTitle");// 工单标题
            String attachmentId = getString("attachmentId");//附件id
            PreinvApply pApply = preinvApplyService.findById(id);// 根据id查询
            PreinvApply preinvApply = new PreinvApply();
            preinvApply.setAppAmout(pApply.getAppAmout());// 总金额
            preinvApply.setAppMemo(pApply.getAppMemo());// 申请原因
            preinvApply.setAppTitle(appTitle);// 工单标题
            preinvApply.setGroupCode(pApply.getGroupCode());// 集团编码
            preinvApply.setGroupName(pApply.getGroupName());// 集团名称
            preinvApply.setOprType("1");// 开具状态

            // 为冲正
            preinvApply.setInvalidReason(cancelMemo);// 作废原因
            preinvApply.setStartState("x");// 初始值为x
            preinvApply.setInvalidTime(new Date());
            preinvApply.setReverseSatate("4");// 冲正状态为冲正中
            preinvApply.setOldId(pApply.getBatchNo());// 原开票编码
            preinvApply.setPreivType(pApply.getPreivType());
            preinvApply.setErrorMessage("无");// 错误信息初始值
            String IBM = "";
            String branchOffice = "";
            String country = "";
            List<Object[]> sone = preinvApplyService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                country = (String) sone.get(i)[0];
                branchOffice = (String) sone.get(i)[1];
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = preinvApplyService.getNumber();
            String batchNO = IBM + "" + sateTime;
            preinvApply.setBatchNo(batchNO);// 工单编码
            preinvApply.setCreateDate(new Date());// 创建日期
            preinvApply.setBranchOffice(branchOffice);// 地市
            preinvApply.setCountry(country);// 区县
            preinvApply.setCreatorId(user.getRowNo() + "");// 创建人编号
            preinvApply.setCreatorName(user.getEmployeeName());// 创建人名称
            //preinvApply.setHANDLER_ID(userId);//下一步处理人id
            if (pApply.getAppType() != null && !"".equals(pApply.getAppType())) {
                preinvApply.setAppType(pApply.getAppType());// 业务类型
            }
            if (pApply.getRecDate() != null && !"".equals(pApply.getRecDate())) {
                preinvApply.setRecDate(pApply.getRecDate());// 计划回收时间
            }
            if (notBlank(pApply.getTaxAddress())) {
                preinvApply.setTaxAddress(pApply.getTaxAddress());// 纳税人地址
            }
            if (notBlank(pApply.getTaxBankAccount())) {
                preinvApply.setTaxBankAccount(pApply.getTaxBankAccount());// 纳税人银行账号
            }
            if (notBlank(pApply.getTaxBankName())) {
                preinvApply.setTaxBankName(pApply.getTaxBankName());// 纳税人银行名称
            }
            if (notBlank(pApply.getTaxPayer())) {
                preinvApply.setTaxPayer(pApply.getTaxPayer());// 纳税人识别号
            }
            if (notBlank(pApply.getTaxPhone())) {
                preinvApply.setTaxPhone(pApply.getTaxPhone());// 纳税人电话
            }
            if (notBlank(pApply.getTaxName())) {
                preinvApply.setTaxName(pApply.getTaxName());// 纳税人名称
            }
            if ("2".equals(pApply.getPreivType())) {
                List<InternetOfThingsDet> list = preinvApplyService.getInternetOfThingsDetByApplyNo(pApply.getBatchNo());
                for (int j = 0; j < list.size(); j++) {
                    String stateTime = getStringDatetwo(new Date());
                    String number = "WLWCZ" + "" + stateTime;
                    InternetOfThingsDet itd = new InternetOfThingsDet();
                    itd.setInv_No(number);
                    itd.setApply_no(batchNO);
                    itd.setAcct_id(list.get(j).getAcct_id());
                    itd.setAcct_name(list.get(j).getAcct_name());
                    itd.setValidbillcyc(list.get(j).getValidbillcyc());
                    itd.setPayment_time(list.get(j).getPayment_time());
                    itd.setPayment_seq(list.get(j).getPayment_seq());
                    itd.setSer_name(list.get(j).getSer_name());
                    itd.setTax_rate(list.get(j).getTax_rate());
                    itd.setTax_fee(list.get(j).getTax_fee());
                    itd.setInclude_tax_amt(list.get(j).getInclude_tax_amt());
                    itd.setExcluded_tax_amt(list.get(j).getExcluded_tax_amt());
                    itd.setCollection_tax_amt("0");
                    itd.setSplit_include_tax_amt(list.get(j).getSplit_include_tax_amt());
                    itd.setCustomer_id(list.get(j).getCustomer_id());
                    itd.setOpr_seq(list.get(j).getOpr_seq());
                    itd.setOrder_seq(list.get(j).getOrder_seq());
                    itd.setCust_id(list.get(j).getCust_id());
                    itd.setCustomer_type(list.get(j).getCustomer_type());
                    itd.setStarTime(list.get(j).getStarTime());
                    itd.setEndTime(list.get(j).getEndTime());
                    itd.setFrank(list.get(j).getFrank());
                    itd.setVoucherType(list.get(j).getVoucherType());
                    itd.setPrepVoucher(list.get(j).getPrepVoucher());
                    itd.setCommit_Type("2");
                    itd.setOld_Inv_No(list.get(j).getInv_No());
                    preinvApplyService.addInternetOfThingsDet(itd);
                }
            } else {
                // 账户信息id
                String jsonString = getString("json");
                JSONArray jsonArray = JSONArray.fromObject(jsonString);
                List<PreinvApplyDet> list = preinvApplyService.findApplyDetsByUuid(jsonArray);
                for (PreinvApplyDet pDet : list) {
                    String stateTime = preinvApplyService.getNumber();
                    String invNo = "KP" + "" + stateTime;
                    PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
                    preinvApplyDet.setBatchNo(batchNO);// 批次号
                    preinvApplyDet.setBossNo(pDet.getBossNo());// boss编号
                    preinvApplyDet.setContrctNo(pDet.getContrctNo());// 账户号码
                    preinvApplyDet.setContrctType(pDet.getContrctType());// 账户类型
                    preinvApplyDet.setInvAmout(pDet.getInvAmout());// 开票金额
                    preinvApplyDet.setPhone(pDet.getPhone());// 服务号码
                    preinvApplyDet.setInvNo(invNo);// 发票编码
                    preinvApplyDet.setInvState("0");// 发票状态
                    preinvApplyDet.setPactName(pDet.getPactName());// 合同名称
                    preinvApplyDet.setProductName(pDet.getProductName());// 产品名称
                    preinvApplyDet.setRecDate(pDet.getRecDate());// 计划回收日期
                    preinvApplyDet.setInvType(pDet.getInvType());// 发票类型
                    preinvApplyDet.setOpInvDate(pDet.getOpInvDate());// 开票时间
                    preinvApplyDet.setOpInvName(pDet.getOpInvName());// 开票人名称
                    preinvApplyDet.setOpInvNo(pDet.getOpInvNo());// 开票人工号
                    preinvApplyDet.setJson(pDet.getJson());// 账期json字段
                    preinvApplyDet.setBeginCycle(pDet.getBeginCycle());// 开始账期
                    preinvApplyDet.setEndCycle(pDet.getEndCycle());// 结束账期
                    preinvApplyDet.setOldInvNo(pDet.getInvNo());// 原订单流水
                    preinvApplyDet.setTaxRate(pDet.getTaxRate());// 税率
                    preinvApplyDet.setRecDate(pDet.getRecDate());
                    List<ApplyCycleDet> cycleDets = preinvApplyService.findToInvNo(pDet.getInvNo());
                    for (ApplyCycleDet aDet : cycleDets) {
                        ApplyCycleDet applyCycleDet = new ApplyCycleDet();
                        applyCycleDet.setAmout(aDet.getAmout());// 金额
                        applyCycleDet.setCycleYM(aDet.getCycleYM());// 账期
                        applyCycleDet.setInvNo(invNo);// 发票编码
                        applyCycleDet.setInvoiceType(aDet.getInvoiceType());// 发票状态
                        preinvApplyService.addappApplyCycleDet(applyCycleDet);// 新增账期
                    }
                    preinvApplyService.addPreinvApplyDet(preinvApplyDet);// 新增开票明细

                }
            }
            // 新增信息
            PreinvApply preinvApply2 = preinvApplyService.addPreinvApply(preinvApply);
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
            // 流程启动
            Map<String, String> map = new HashMap<>();
            map.put("decisionKey", "APPLY");
            map.put("decisionValue", role);
            String processId = transferJBPMUtils.startTransfer("PreinvApplyCorrect", map);// 冲正流程启动
            // 保存信息到流程表
            PreinvApplyFlow preinvApplyFlow = new PreinvApplyFlow();
            preinvApplyFlow.setFlowId(processId);// 流程id
            preinvApplyFlow.setFlowName("PreinvApplyCorrect");// 流程名称
            preinvApplyFlow.setCreator(user.getRowNo() + "");// 创建人编号
            preinvApplyFlow.setCreateDate(new Date());// 创建时间
            preinvApplyFlow.setFlowType("冲正工单审批");// 流程类型
            preinvApplyFlow.setState("0");// 流程状态
            preinvApplyFlow.setBatchNo(preinvApply.getBatchNo());// 预开票工单编号
            preinvApplyFlow.setLogo("1");// 标记为1,代表作废流程
            preinvApplyFlow.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyFlow.setDealNo(USER.getRowNo() + "");// 下一步处理人编号
            preinvApplyFlow.setDealName(USER.getEmployeeName());// 下一步处理人名称
            preinvApplyService.addPreinvApplyFlow(preinvApplyFlow);// 保存信息到流程表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            // 保存信息到任务表
            PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
            preinvApplyTask.setFlowId(processId);// 流程id
            preinvApplyTask.setTaskId(task.getId());// 环节id
            preinvApplyTask.setTaskName("客户经理");// 环节名称
            preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyTask.setCreateDate(new Date());// 创建时间
            preinvApplyTask.setDealNo(user.getRowNo() + "");// 处理人编号
            preinvApplyTask.setDealName(user.getEmployeeName());// 处理人名称
            preinvApplyTask.setDealDate(new Date());// 处理时间
            preinvApplyTask.setState("1");// 状态
            preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
            // 保存下一步任务
            Task taskTwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            PreinvApplyTask pTask = new PreinvApplyTask();
            pTask.setFlowId(processId);// 流程id
            pTask.setTaskId(taskTwo.getId());// 环节id
            pTask.setTaskName(taskTwo.getActivityName());// 环节名称
            pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            pTask.setCreatorName(user.getEmployeeName());// 创建人名称
            pTask.setCreateDate(new Date());// 创建时间
            pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
            pTask.setDealName(USER.getEmployeeName());// 处理人名称
            pTask.setState("0");// 状态
            preinvApplyService.addPreinvApplyTask(pTask);// 保存信息到任务表
            commitBackLog(preinvApply2, userId, processId, processId, user, pTask);// 生成待办
//            List<SingleAndAttachment> sList = preinvApplyService.findbyOrderId(id);// 根据工单id查询附件信息
//            for (SingleAndAttachment singleAndAttachment : sList) {
//                SingleAndAttachment sa = new SingleAndAttachment();
//                sa.setOrderID(preinvApply2.getUuid());
//                sa.setAttachmentId(singleAndAttachment.getAttachmentId());
//                sa.setLink(PreinvApply.PREINVAPPLY);
//                preinvApplyService.saveSandA(sa);
//            }

            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(preinvApply2.getUuid());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(PreinvApply.PREINVAPPLY);
                            preinvApplyService.saveSandA(sa);
                        }
                    }
                }
            }
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 字符串非空验证
     *
     * @param str
     * @return 不等于null and 不等于"" 返回true
     */
    private boolean notBlank(String str) {
        return str != null && !"".equals(str);
    }

    /**
     * 冲正流程开始
     */
    public void startInvalidPreinvApply() {
        try {
            if (1 == 1) {
                return;
            }
            String id = getString("id");// 开票id
            String role = getString("role");// 角色权限
            String userId = getString("userId");// 下一步任务人id
            String cancelMemo = getString("cancelMemo");// 作废原因
            String appTitle = getString("appTitle");// 工单标题
            PreinvApply pApply = preinvApplyService.findById(id);// 根据id查询
            PreinvApply preinvApply = new PreinvApply();
            preinvApply.setAppAmout(pApply.getAppAmout());// 总金额
            preinvApply.setAppMemo(pApply.getAppMemo());// 申请原因
            preinvApply.setAppTitle(appTitle);// 工单标题
            preinvApply.setAppType(pApply.getAppType());// 业务类型
            preinvApply.setGroupCode(pApply.getGroupCode());// 集团编码
            preinvApply.setGroupName(pApply.getGroupName());// 集团名称
            preinvApply.setOprType("1");// 开具状态为冲正
            preinvApply.setInvalidReason(cancelMemo);// 作废原因
            preinvApply.setStartState("x");// 初始值为x
            preinvApply.setInvalidTime(new Date());
            preinvApply.setReverseSatate("4");// 冲正状态为冲正中
            preinvApply.setOldId(pApply.getBatchNo());// 原开票编码 把开票编码存入原开票编码中
            preinvApply.setErrorMessage("无");// 错误信息初始值
            String IBM = "";
            String branchOffice = "";
            String country = "";
            List<Object[]> sone = preinvApplyService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                country = (String) sone.get(i)[0];
                branchOffice = (String) sone.get(i)[1];
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = preinvApplyService.getNumber();
            String batchNO = IBM + "" + sateTime;
            preinvApply.setBatchNo(batchNO);// 工单编码
            preinvApply.setCreateDate(new Date());// 创建日期
            preinvApply.setBranchOffice(branchOffice);// 地市
            preinvApply.setCountry(country);// 区县
            preinvApply.setCreatorId(user.getRowNo() + "");// 创建人编号
            preinvApply.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApply.setRecDate(pApply.getRecDate());// 计划回收时间
            if (pApply.getTaxAddress() != null && !"".equals(pApply.getTaxAddress())) {
                preinvApply.setTaxAddress(pApply.getTaxAddress());// 纳税人地址
            }
            if (pApply.getTaxBankAccount() != null && !"".equals(pApply.getTaxBankAccount())) {
                preinvApply.setTaxBankAccount(pApply.getTaxBankAccount());// 纳税人银行账号
            }
            if (pApply.getTaxBankName() != null && !"".equals(pApply.getTaxBankName())) {
                preinvApply.setTaxBankName(pApply.getTaxBankName());// 纳税人银行名称
            }
            if (pApply.getTaxPayer() != null && !"".equals(pApply.getTaxPayer())) {
                preinvApply.setTaxPayer(pApply.getTaxPayer());// 纳税人识别号
            }
            if (pApply.getTaxPhone() != null && !"".equals(pApply.getTaxPhone())) {
                preinvApply.setTaxPhone(pApply.getTaxPhone());// 纳税人电话
            }
            if (pApply.getTaxName() != null && !"".equals(pApply.getTaxName())) {
                preinvApply.setTaxName(pApply.getTaxName());// 纳税人电话
            }
            List<PreinvApplyDet> list = preinvApplyService.findByUUID(pApply.getBatchNo());// 查询账户信息
            for (PreinvApplyDet pDet : list) {
                String stateTime = preinvApplyService.getNumber();
                String invNo = "KP" + "" + stateTime;
                PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
                preinvApplyDet.setBatchNo(batchNO);// 批次号
                preinvApplyDet.setBossNo(pDet.getBossNo());// boss编号
                preinvApplyDet.setContrctNo(pDet.getContrctNo());// 账户号码
                preinvApplyDet.setContrctType(pDet.getContrctType());// 账户类型
                preinvApplyDet.setInvAmout(pDet.getInvAmout());// 开票金额
                preinvApplyDet.setPhone(pDet.getPhone());// 服务号码
                preinvApplyDet.setInvNo(invNo);// 发票编码
                preinvApplyDet.setInvState("0");// 发票状态
                preinvApplyDet.setPactName(pDet.getPactName());// 合同名称
                preinvApplyDet.setProductName(pDet.getProductName());// 产品名称
                preinvApplyDet.setRecDate(pDet.getRecDate());// 计划回收日期
                preinvApplyDet.setInvType(pDet.getInvType());// 发票类型
                preinvApplyDet.setOpInvDate(pDet.getOpInvDate());// 开票时间
                preinvApplyDet.setOpInvName(pDet.getOpInvName());// 开票人名称
                preinvApplyDet.setOpInvNo(pDet.getOpInvNo());// 开票人工号
                preinvApplyDet.setJson(pDet.getJson());// 账期json字段
                preinvApplyDet.setBeginCycle(pDet.getBeginCycle());// 开始账期
                preinvApplyDet.setEndCycle(pDet.getEndCycle());// 结束账期
                preinvApplyDet.setOldInvNo(pDet.getInvNo());// 原订单流水
                List<ApplyCycleDet> list2 = preinvApplyService.findToInvNo(pDet.getInvNo());
                for (ApplyCycleDet aDet : list2) {
                    ApplyCycleDet applyCycleDet = new ApplyCycleDet();
                    applyCycleDet.setAmout(aDet.getAmout());// 金额
                    applyCycleDet.setCycleYM(aDet.getCycleYM());// 账期
                    applyCycleDet.setInvNo(invNo);// 发票编码
                    applyCycleDet.setInvoiceType(aDet.getInvoiceType());// 发票状态
                    preinvApplyService.addappApplyCycleDet(applyCycleDet);// 新增账期
                }
                preinvApplyService.addPreinvApplyDet(preinvApplyDet);// 新增开票明细

            }
            // 新增信息
            PreinvApply preinvApply2 = preinvApplyService.addPreinvApply(preinvApply);
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
            // 流程启动
            Map<String, String> map = new HashMap<>();
            map.put("decisionKey", "APPLY");
            map.put("decisionValue", role);
            String processId = transferJBPMUtils.startTransfer("PreinvApply", map);// 流程启动
            // 保存信息到流程表
            // http://localhost:8080/EOM/jbpmTest.jsp
            PreinvApplyFlow preinvApplyFlow = new PreinvApplyFlow();
            preinvApplyFlow.setFlowId(processId);// 流程id
            preinvApplyFlow.setFlowName("preinvApply");// 流程名称
            preinvApplyFlow.setCreator(user.getRowNo() + "");// 创建人编号
            preinvApplyFlow.setCreateDate(new Date());// 创建时间
            preinvApplyFlow.setFlowType("冲正工单审批");// 流程类型
            preinvApplyFlow.setState("0");// 流程状态
            preinvApplyFlow.setBatchNo(preinvApply.getBatchNo());// 预开票工单编号
            preinvApplyFlow.setLogo("1");// 标记为1,代表作废流程
            preinvApplyFlow.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyFlow.setDealNo(USER.getRowNo() + "");// 下一步处理人编号
            preinvApplyFlow.setDealName(USER.getEmployeeName());// 下一步处理人名称
            preinvApplyService.addPreinvApplyFlow(preinvApplyFlow);// 保存信息到流程表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            // 保存信息到任务表
            PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
            preinvApplyTask.setFlowId(processId);// 流程id
            preinvApplyTask.setTaskId(task.getId());// 环节id
            preinvApplyTask.setTaskName("客户经理");// 环节名称
            preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyTask.setCreateDate(new Date());// 创建时间
            preinvApplyTask.setDealNo(user.getRowNo() + "");// 处理人编号
            preinvApplyTask.setDealName(user.getEmployeeName());// 处理人名称
            preinvApplyTask.setDealDate(new Date());// 处理时间
            preinvApplyTask.setState("1");// 状态
            preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
            // 保存下一步任务
            Task taskTwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            PreinvApplyTask pTask = new PreinvApplyTask();
            pTask.setFlowId(processId);// 流程id
            pTask.setTaskId(taskTwo.getId());// 环节id
            pTask.setTaskName(taskTwo.getActivityName());// 环节名称
            pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            pTask.setCreatorName(user.getEmployeeName());// 创建人名称
            pTask.setCreateDate(new Date());// 创建时间
            pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
            pTask.setDealName(USER.getEmployeeName());// 处理人名称
            pTask.setState("0");// 状态
            preinvApplyService.addPreinvApplyTask(pTask);// 保存信息到任务表
            commitBackLog(preinvApply2, userId, processId, processId, user, pTask);// 生成待办
            List<SingleAndAttachment> sList = preinvApplyService.findbyOrderId(id);// 根据工单id查询附件信息
            for (SingleAndAttachment singleAndAttachment : sList) {
                SingleAndAttachment sa = new SingleAndAttachment();
                sa.setOrderID(preinvApply2.getUuid());
                sa.setAttachmentId(singleAndAttachment.getAttachmentId());
                sa.setLink(PreinvApply.PREINVAPPLY);
                preinvApplyService.saveSandA(sa);
            }
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 查询账户信息
     */
    public void findPreinvApplyDet() {
        try {
            String groupCode = getString("groupCode");// 集团编码
            String bossNo = getString("bossName"); // boss工号
            if (bossNo == "" || bossNo == null) {
                bossNo = user.getBossUserName();
            }
            String phone = user.getMobile();// 手机号
            String busiType = getString("busiType");// 查询类型
            String contractNo = getString("contractNo");// 账户号码
            Result json = preinvApplyService.queryPreinvApplyDet(groupCode, bossNo, phone, contractNo, busiType);

            Write(String.valueOf(json.getData()));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 查询账期信息
     */
    public void findApplyCycleDet() {
        try {
            String contrctNo = getString("contrctNo");

            Result json = preinvApplyService.findApplyCycleDet(contrctNo);
            Write(String.valueOf(json.getData()));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    public void issuanceOfTaxRateInquiry() {
        try {
            String prodId = getString("prodId");
            String serviceNumber = getString("serviceNumber");
            String contractNo = getString("contractNo");
            Result result = preinvApplyService.queryTaxRate(serviceNumber,contractNo,prodId);
            if(ResultCode.SUCCESS.code()==result.getCode()){
                JSONObject resObj=JSONObject.fromObject(result.getData());
                JSONObject root = resObj.getJSONObject("ROOT");
                if (root.has("RETURN_CODE") && "0".equals(root.getString("RETURN_CODE"))){
                    Write(returnPars(1,root.getJSONObject("OUT_DATA"),"查询完成"));
                }else {
                    Write(returnPars(-1,"","亲爱的同事，查询账户对应税率信息失败，原因：【"+root.getString("RETURN_MSG")+"】，请联系管理员处理！"));
                }
            }else{
                Write(returnPars(-1,"","亲爱的同事，调用BOSS资费税率接口失败【"+result.getMessage()+"】，请联系管理员处理！"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("账户对应资费税率查询失败:"+e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，账户对应资费税率查询失败，请联系管理员处理！"));
        }
    }

    /**
     * 查询该集团编码下的账号是否可开票
     */
    public void findToGroupCode() {
        Map<String, String> message = new HashMap<String, String>();// 返回结果
        try {
            String group = getString("groupCode");// 集团编码
            String contrctNo = getString("contrctNo");// 账号号码
            String bossMessage;
            List<PreinvApply> preinvApplyList = preinvApplyService.findByPreinvApplyContrctNo(contrctNo);
            if (0 < preinvApplyList.size()) {
                for (PreinvApply preinvApply : preinvApplyList) {
                    // bossMessage =
                    // preinvApplyService.findPreinvApplyDetState(group,preinvApplyDet);//查询boos账号是否能开票
                    // if(bossMessage.equals("0")){
                    if (preinvApply.getStartState().equals("0")) {// 验证服务器上的数据
                        message.put("msg", contrctNo + "账户已申请了预开票工单,工单编号为[" + preinvApply.getBatchNo() + "]，请勿重复开票");
                        // message.put("bossMessage",bossMessage);
                        message.put("bossMessage", "未到boos校验");
                        break;
                    }
                    // }else if(bossMessage.equals("接口访问成功，查询失败")) {
                    // message.put("msg", contrctNo+"账户校验失败");
                    // message.put("bossMessage",bossMessage);
                    // break;
                    // }else if(bossMessage.equals("接口访问失败")){
                    // message.put("msg", contrctNo+"账户校验失败,请稍后重试");
                    // message.put("bossMessage",bossMessage);
                    // break;
                    // }
                }
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(message));
        } catch (Exception e) {
            e.printStackTrace();
            message.put("msg", "操作失败，请重试");
            message.put("bossMessage", e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(message));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 根据280或者编号查询工单信息
     */
    public void findByGroup() {
        try {
            String groupCoding = getString("groupCoding");
            String groupName = getString("groupName");
            List<Map<String, Object>> list = preinvApplyService.findByGroup(groupCoding, groupName);
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(list);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }

    /**
     * 验证工单是否可作废
     */
    public void validation() {
        try {
            String id = getString("id");// 开票id
            //System.out.println("验证方法订单id" + id);
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询
            String message = preinvApplyService.verifyPreinvApply(preinvApply);// 验证单子是否可作废
            //System.out.println("验证方法返回值：" + message);
            // String message = "Y";
            if (message.equals("ERROR")) {
                Write("ERROR");
            } else if (message.equals("YES")) {
                Write("FAILURE");
            } else if (message.equals("Y")) {
                Write("YES");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 冲正
     */
    public void reverse() {
        HashMap map = new HashMap();
        try {
            String invNo = this.getString("invNo");
            PreinvApplyDet pDet = this.preinvApplyService.findForInvNo(invNo);//发票编码
            PreinvApply preinvApply = this.preinvApplyService.getPreinvApplyByBachNo(pDet.getBatchNo());//根据工单编码查询工单
            String stateTime = preinvApplyService.getNumber();
            String newInvNo = "KP" + stateTime;
            PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
            preinvApplyDet.setBossNo(pDet.getBossNo());
            preinvApplyDet.setContrctNo(pDet.getContrctNo());
            preinvApplyDet.setContrctType(pDet.getContrctType());
            preinvApplyDet.setInvAmout(pDet.getInvAmout());
            preinvApplyDet.setPhone(pDet.getPhone());
            preinvApplyDet.setInvNo(newInvNo);
            preinvApplyDet.setInvState("0");
            preinvApplyDet.setPactName(pDet.getPactName());
            preinvApplyDet.setProductName(pDet.getProductName());
            preinvApplyDet.setRecDate(pDet.getRecDate());
            preinvApplyDet.setInvType(pDet.getInvType());
            preinvApplyDet.setOpInvDate(pDet.getOpInvDate());
            preinvApplyDet.setOpInvName(pDet.getOpInvName());
            preinvApplyDet.setOpInvNo(pDet.getOpInvNo());
            preinvApplyDet.setJson(pDet.getJson());
            preinvApplyDet.setBeginCycle(pDet.getBeginCycle());
            preinvApplyDet.setEndCycle(pDet.getEndCycle());
            preinvApplyDet.setOldInvNo(pDet.getInvNo());
            List<ApplyCycleDet> list2 = this.preinvApplyService.findToInvNo(pDet.getInvNo());
            if (list2.size() > 0) {
                Iterator var10 = list2.iterator();

                while (var10.hasNext()) {
                    ApplyCycleDet aDet = (ApplyCycleDet) var10.next();
                    ApplyCycleDet applyCycleDet = new ApplyCycleDet();
                    applyCycleDet.setAmout(aDet.getAmout());
                    applyCycleDet.setCycleYM(aDet.getCycleYM());
                    applyCycleDet.setInvNo(newInvNo);
                    applyCycleDet.setInvoiceType(aDet.getInvoiceType());
                    this.preinvApplyService.addappApplyCycleDet(applyCycleDet);
                }
            }
            preinvApply.setTaxAddress(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxAddress()));
            preinvApply.setTaxName(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxName()));
            this.preinvApplyService.addPreinvApplyDet(preinvApplyDet);
            Map<String, Object> mapa = this.preinvApplyService.reverseInvNo(preinvApply, preinvApplyDet);
            this.Write(JSONHelper.Serialize(mapa));
        } catch (Exception var12) {
            var12.printStackTrace();
            map.put("flag", "NO");
            map.put("message", "操作失败");
            this.Write(JSONHelper.Serialize(map));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 查询工单状态
     */
    public void findByState() {
        try {
            String id = getString("id");
            PreinvApply preinvApply = preinvApplyService.findById(id);
            if (preinvApply.getHandleState().equals("0")) {
                Write("YES");
            } else {
                Write("N");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 作废提交方法
     */
    public void invalidCommit() {
        try {
            String oldProcessId = getString("oldProcessId");// 流程id
            String userId = getString("userId");// 下一步处理人id
            String id = getString("id");// 开票id
            String role = getString("role");// 权限
            String waitId = getString("waitId");// 待办id
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                System.out.println("================退回开始待办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================退回结束待办================");
            }
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
            preinvApply.setReverseSatate("4");// 修改状态为冲正审批中
            preinvApply.setUpdateDate(new Date());
            preinvApply.setHANDLER_ID("");
            preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息

            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
            // 流程启动

            Map<String, String> map = new HashMap<>();
            map.put("decisionKey", "APPLY");
            map.put("decisionValue", role);
            String processId = transferJBPMUtils.startTransfer("PreinvApplyCorrect", map);// 流程启动
            // 保存到流程表
            PreinvApplyFlow preinvApplyFlow = new PreinvApplyFlow();
            preinvApplyFlow.setFlowId(processId);// 流程id
            preinvApplyFlow.setFlowName("PreinvApplyCorrect");// 流程名称
            preinvApplyFlow.setCreator(user.getRowNo() + "");// 创建人编号
            preinvApplyFlow.setCreateDate(new Date());// 创建时间
            preinvApplyFlow.setFlowType("开票工单审批");// 流程类型
            preinvApplyFlow.setState("0");// 流程状态
            preinvApplyFlow.setBatchNo(preinvApply.getBatchNo());// 预开票工单编号
            preinvApplyFlow.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyFlow.setDealNo(USER.getRowNo() + "");// 下一步处理人编号
            preinvApplyFlow.setDealName(USER.getEmployeeName());// 下一步处理人名称
            preinvApplyService.addPreinvApplyFlow(preinvApplyFlow);// 保存信息到流程表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            // 保存信息到任务表
            PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
            preinvApplyTask.setFlowId(oldProcessId);// 流程id
            preinvApplyTask.setTaskId(task.getId());// 环节id
            preinvApplyTask.setTaskName("客户经理");// 环节名称
            preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyTask.setCreateDate(new Date());// 创建时间
            preinvApplyTask.setDealNo(user.getRowNo() + "");// 处理人编号
            preinvApplyTask.setDealName(user.getEmployeeName());// 处理人名称
            preinvApplyTask.setDealDate(new Date());// 处理日期
            preinvApplyTask.setState("1");// 状态
            preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
            // 保存下一步任务
            Task taskTwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            PreinvApplyTask pTask = new PreinvApplyTask();
            pTask.setFlowId(oldProcessId);// 流程id
            pTask.setTaskId(taskTwo.getId());// 环节id
            pTask.setTaskName(taskTwo.getActivityName());// 环节名称
            pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            pTask.setCreatorName(user.getEmployeeName());// 创建人名称
            pTask.setCreateDate(new Date());// 创建时间
            pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
            pTask.setDealName(USER.getEmployeeName());// 处理人名称
            pTask.setState("0");// 状态
            preinvApplyService.addPreinvApplyTask(pTask);// 保存信息到任务表
            commitBackLog(preinvApply, userId, processId, oldProcessId, user, pTask);// 生成待办
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 查询冲正订单
     */
    public void findByOldid() {
        try {
            String id = getString("id");
            Object total = preinvApplyService.findbyOldid(id);
            if (Integer.valueOf(total.toString()) == 0) {
                Write("YES");
            } else {
                Write("ERROR");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 根据角色查询角色下人员信息
     */
    public void sonSelectByUId() {
        String id = getString("id");
        String pid = getString("pid");// 订单id
        PreinvApply preinvApply = preinvApplyService.findById(pid);
        SystemUser USER = systemUserService.getByUserInfoRowNo(Integer.valueOf(preinvApply.getCreatorId()));
        List<Map<String, String>> sd = preinvApplyService.SelectZtreeByUId(id, USER);
        writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));
    }

    /**
     * 发票台账功能
     */

    public void ledger() {
        try {
            SystemUser user = this.user;// 获取当前用户
            List list = preinvApplyService.findByRowNo(user.getRowNo()); // 获取用户权限
            boolean flag = false;
            String json = null;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16")) {
                    flag = true;
                    break;
                }
            } // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo()); // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            String page = getString("page");// 当前页码数
            String pagesize = getString("pagesize");// 每页显示件数
            String groupCode = getString("groupCode");// 集团280
            String state = getString("state");// 发票状态
            String startTime = getString("startTime");// 开始时间
            String endTime = getString("endTime");// 结束时间
            String contractNo = getString("contractNo");// 账户号码
            PageVO pageVO = new PageVO<>();
            pageVO.setPage(Integer.parseInt(page));
            pageVO.setPageSize(Integer.parseInt(pagesize));
            if (flag) {
                if (companyCode.get(0).toString().equals("00")) {
                    json = preinvApplyService.findToPreinvApplyDet(pageVO, contractNo, groupCode, state, startTime, endTime);// 查询所有台账信息
                } else {
                    json = preinvApplyService.findToPreinvApplyDetForBatchoffice(pageVO, companyName, contractNo, groupCode, state, startTime,
                            endTime);
                }
            } else {
                json = preinvApplyService.findToPreinvApplyDetForUser(pageVO, String.valueOf(user.getRowNo()), contractNo, groupCode, state,
                        startTime, endTime);
            }
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 发票台账功能(2)
     */

    public void ledgerTow() {
        try {
            SystemUser user = this.user;// 获取当前用户
            List list = preinvApplyService.findByRowNo(user.getRowNo()); // 获取用户权限
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16") || (list.get(i).toString()).equals("2297")) {
                    flag = true;
                    break;
                }
            } // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo()); // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pageSize = getInteger("pageSize");// 每页显示件数
            String groupCode = getString("groupCode");// 集团280
            String state = getString("state");// 订单状态
            String startTime = getString("startTime");// 开始时间
            String endTime = getString("endTime");// 结束时间
            String contractNo = getString("contractNo");// 账户号码
            String invNo = getString("invNo"); // 账户流水

            String batchNo = getString("batchNo");//工单编码

            LayuiPage page = new LayuiPage(pageNo, pageSize);

            if (flag) {
                if (companyCode.get(0).toString().equals("00")) {
                    //所有台账
                    page = preinvApplyService.findToPreinvApplyDet(page, contractNo, groupCode, state, startTime, endTime, invNo, batchNo);// 查询所有台账信息
                } else {
                    //地市台账
                    page = preinvApplyService.findToPreinvApplyDetForBatchoffice(page, companyName, contractNo, groupCode, state, startTime, endTime,
                            invNo, batchNo);
                }
            } else {
                //个人台账
                page = preinvApplyService.findToPreinvApplyDetForUser(page, String.valueOf(user.getRowNo()), contractNo, groupCode, state, startTime,
                        endTime, invNo, batchNo);
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
            //System.out.println(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        } catch (Exception e) {
            logger.error("预开票台账错误:" + e.getMessage(), e);
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 页面文件上传
     */
    public void importPreinvApply() throws IOException {
        try {
            logger.info("页面导入预开票开始");
            long start = System.currentTimeMillis();
            List<ImportPreinvApply> lists = preinvApplyService.findByImportPreinvApply();// 查询所有导入账户信息
            if (lists.size() != 0) {
                preinvApplyService.deleteAll();// 删除数据
            }
            String file = getString("fileName");
            String url = PREINVAPPLY_UPLOAD + file;//EOMAPP/UploadFiles/PreinvData/ + file
            //String url = "D:\\20210204\\PREINV_DATA_A_20230108.txt";
            logger.info("预开票页面导入地址为" + url);
            File fileObj = new File(url);
            //logger.info("地址为"+"D:\\20210121\\"+s);
            // 如果文件不存在，直接返回
            if (!fileObj.exists()) {
                logger.info("importPreinvApply方法---文件不存在！");
                this.Write("NO");
                return;
            }
            FileInputStream fis = new FileInputStream(fileObj);
            BufferedReader bf = new BufferedReader(new InputStreamReader(fis, "GBK"));
            String line = null;
            List<ImportPreinvApply> list = new ArrayList<ImportPreinvApply>();
            while ((line = bf.readLine()) != null) {
                String[] item = line.split("&");
                try {
                    ImportPreinvApply importPreinvApply = new ImportPreinvApply();
                    importPreinvApply.setOrder_id(item[0]);//订单编号<->订单流水0
                    importPreinvApply.setContract_no(item[1]);//账户ID<->账户号码1
                    importPreinvApply.setAccount_type(item[2]);//账户类型2
                    importPreinvApply.setProduct_name(item[3]);//产品名称3
                    importPreinvApply.setBusiness_number(item[4]);//业务号码/成员号码4
                    importPreinvApply.setInv_code(item[5]);//发票代码5
                    importPreinvApply.setInv_number(item[6]);//发票号码6
                    importPreinvApply.setTotalAmount(item[7]);//工单总金额7
                    importPreinvApply.setAmount_cashed(item[8]);//实际回款金额8
                    importPreinvApply.setLastCollDate(formatDate(item[9]));//最后回款日期9
                    importPreinvApply.setRemainingOutAmount(item[10]);//剩余欠缴金额10
                    importPreinvApply.setDrawer(item[11]);//开票人11
                    importPreinvApply.setLogin_no(item[12]);//开票工号12
                    importPreinvApply.setInvoicedAmount(item[13]);//开票金额13
                    importPreinvApply.setUpdate_time(formatDate(item[14]));//操作日期14
                    importPreinvApply.setOrder_state(item[15]);//订单状态15
                    importPreinvApply.setRedState(item[16]);//冲红状态16
                    list.add(importPreinvApply);
                } catch (Exception e) {
                    logger.error("手动导入预开票数据错误" + e.getMessage(), e);
//                    //File f = new File(PREINVAPPLY_UPLOAD+getTime()+"log.txt");
//                    File f = new File("D:\\20210121\\"+getTime()+"log.txt");
//                    //用FileOutputSteam包装文件，并设置文件可追加
//                    OutputStream out = new FileOutputStream(f,true);
//                    String a="";
//                    for (int i=0;i<item.length;i++){
//                        a+=item[i]+"|";
//                    }
//                    out.write(a.getBytes()); //向文件中写入数据
//                    out.write('\r'); // \r\n表示换行
//                    out.write('\n');
//                    out.close(); //关闭输出流
//                    System.out.println("写入成功！");
                }
            }
            preinvApplyService.addImportPreinvApply(list);// 新增导入预开票信息
            long end = System.currentTimeMillis();
            String PreinvApply = "执行上传耗时" + (end - start) / 1000L + "s,";
            System.out.println(PreinvApply);
            this.Write("YES");
        } catch (Exception var18) {
            System.out.println(var18.getMessage());
            var18.printStackTrace();
            this.Write("NO");
        }
    }

    /**
     * 获取当前时间前一天字符串
     */
    public String getTime() {
        Date date = new Date();// 当前时间
        SimpleDateFormat sdf = new SimpleDateFormat("YYYYMMdd");// 格式化日期
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        calendar.add(Calendar.DAY_OF_MONTH, -1);
        date = (Date) calendar.getTime();
        return sdf.format(date);

    }

    public Date formatDate(String strDate) throws ParseException {
        Date date = null;
        if (strDate != null && !"".equals(strDate)) {
            StringBuilder sb = new StringBuilder(strDate);
            sb.insert(4, "-");
            sb.insert(7, "-");
            sb.insert(10, " ");
            sb.insert(13, ":");
            sb.insert(16, ":");
            SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            date = format.parse(sb.toString());
        }

        return date;
    }

    /**
     * 台账信息下载
     */

    public void uploadExcel() {
        try {
            SystemUser user = this.user;// 获取当前用户
            List list = preinvApplyService.findByRowNo(user.getRowNo()); // 获取用户权限
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                System.out.println("获取用户权限:" + list.get(i).toString());
                if ((list.get(i).toString()).equals("16")) {// 4076
                    flag = true;
                    break;
                }
            } // 获取公司编码
            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo()); // 获取公司名称
            String companyName = preinvApplyService.findCompanyName(companyCode.get(0).toString());
            String groupCode = getString("groupCode");// 集团280
            String state = getString("state");// 发票状态->改为订单状态
            String startTime = getString("startTime");// 开始时间
            String endTime = getString("endTime");// 结束时间
            String contractNo = getString("contractNo");// 账户号码
            String invNo = getString("invNo"); // 账户流水
            String batchNo = getString("batchNo");//工单编码
            List<Map<String, Object>> mapList = new ArrayList<Map<String, Object>>();
            String func = "";
            if (flag) {
                if (companyCode.get(0).equals("00")) {
                    mapList = preinvApplyService.findAll(contractNo, groupCode, state, startTime, endTime, invNo, batchNo);// 查询所有台账信息
                    func = "findAll";
                } else {
                    mapList = preinvApplyService.findAllByBranoffice(companyName, contractNo, groupCode, state, startTime, endTime, invNo, batchNo);//查询地市所有台账
                    func = "findAllByBranoffice";
                }
            } else {
                mapList = preinvApplyService.findAllByCreatorId(String.valueOf(user.getRowNo()), contractNo, groupCode, state, startTime, endTime,
                        invNo, batchNo);//查询个人所有台账
                func = "findAllByCreatorId";
            }
            System.out.println("执行方法：" + func);
            System.out.println("台账信息下载,数据条数：" + mapList.size());
            if (mapList.size() > 65000) {
                Write("导出的数据条数过多，请返回选择筛选条件");
            } else {
                //System.out.println("数据:"+JSONHelper.Serialize(mapList)); //测试数据导出
                preinvApplyService.uploadExcel(mapList);// 导出
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 查询当前登录用户boss工号
     */
    public void findBossUserName() {
        try {
            SystemUser uSystemUser = systemUserService.getUserInfoRowNo(user.getRowNo());
            if (uSystemUser.getBossUserName() != null && !"".equals(uSystemUser.getBossUserName())) {
                Write("YES");
            } else {
                Write("ERROR");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }

    /**
     * 推送数据到boss
     */
    public void push() {
        try {
            String id = getString("id");// 开票id
            String cancelMemo = getString("cancelMemo");// 作废原因
            String appTitle = getString("appTitle");// 工单标题
            PreinvApply pApply = preinvApplyService.findById(id);// 根据id查询
            PreinvApply preinvApply = new PreinvApply();
            preinvApply.setAppAmout(pApply.getAppAmout());// 总金额
            preinvApply.setAppMemo(pApply.getAppMemo());// 申请原因
            preinvApply.setAppTitle(appTitle);// 工单标题

            preinvApply.setGroupCode(pApply.getGroupCode());// 集团编码
            preinvApply.setGroupName(pApply.getGroupName());// 集团名称
            preinvApply.setOprType("1");// 开具状态为冲正
            preinvApply.setInvalidReason(cancelMemo);// 作废原因
            preinvApply.setInvalidTime(new Date());
            preinvApply.setReverseSatate("4");// 冲正状态为冲正中
            preinvApply.setOldId(pApply.getBatchNo());// 原开票编码
            String IBM = "";
            String branchOffice = "";
            String country = "";
            List<Object[]> sone = preinvApplyService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                country = (String) sone.get(i)[0];
                branchOffice = (String) sone.get(i)[1];
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = preinvApplyService.getNumber();
            String batchNO = IBM + "" + sateTime;
            preinvApply.setBatchNo(batchNO);// 工单编码
            preinvApply.setCreateDate(new Date());// 创建日期
            preinvApply.setBranchOffice(branchOffice);// 地市
            preinvApply.setCountry(country);// 区县
            preinvApply.setCreatorId(user.getRowNo() + "");// 创建人编号
            preinvApply.setCreatorName(user.getEmployeeName());// 创建人名称
            if (pApply.getAppType() != null && !"".equals(pApply.getAppType())) {
                preinvApply.setAppType(pApply.getAppType());// 业务类型
            }
            if (pApply.getRecDate() != null && !"".equals(pApply.getRecDate())) {
                preinvApply.setRecDate(pApply.getRecDate());// 计划回收时间
            }

            if (pApply.getTaxAddress() != null && !"".equals(pApply.getTaxAddress())) {
                preinvApply.setTaxAddress(pApply.getTaxAddress());// 纳税人地址
            }
            if (pApply.getTaxBankAccount() != null && !"".equals(pApply.getTaxBankAccount())) {
                preinvApply.setTaxBankAccount(pApply.getTaxBankAccount());// 纳税人银行账号
            }
            if (pApply.getTaxBankName() != null && !"".equals(pApply.getTaxBankName())) {
                preinvApply.setTaxBankName(pApply.getTaxBankName());// 纳税人银行名称
            }
            if (pApply.getTaxPayer() != null && !"".equals(pApply.getTaxPayer())) {
                preinvApply.setTaxPayer(pApply.getTaxPayer());// 纳税人识别号
            }
            if (pApply.getTaxPhone() != null && !"".equals(pApply.getTaxPhone())) {
                preinvApply.setTaxPhone(pApply.getTaxPhone());// 纳税人电话
            }
            if (pApply.getTaxName() != null && !"".equals(pApply.getTaxName())) {
                preinvApply.setTaxName(pApply.getTaxName());

            }
            List<PreinvApplyDet> list = preinvApplyService.findByUUID(pApply.getBatchNo());// 查询账户信息
            for (PreinvApplyDet pDet : list) {
                String stateTime = preinvApplyService.getNumber();
                String invNo = "KP" + "" + stateTime;
                PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
                preinvApplyDet.setBatchNo(batchNO);// 批次号
                preinvApplyDet.setBossNo(pDet.getBossNo());// boss编号
                preinvApplyDet.setContrctNo(pDet.getContrctNo());// 账户号码
                preinvApplyDet.setContrctType(pDet.getContrctType());// 账户类型
                preinvApplyDet.setInvAmout(pDet.getInvAmout());// 开票金额
                preinvApplyDet.setPhone(pDet.getPhone());// 服务号码
                preinvApplyDet.setInvNo(invNo);// 发票编码
                preinvApplyDet.setInvState("0");// 发票状态
                preinvApplyDet.setPactName(pDet.getPactName());// 合同名称
                preinvApplyDet.setProductName(pDet.getProductName());// 产品名称
                preinvApplyDet.setRecDate(pDet.getRecDate());// 计划回收日期
                preinvApplyDet.setInvType(pDet.getInvType());// 发票类型
                preinvApplyDet.setOpInvDate(pDet.getOpInvDate());// 开票时间
                preinvApplyDet.setOpInvName(pDet.getOpInvName());// 开票人名称
                preinvApplyDet.setOpInvNo(pDet.getOpInvNo());// 开票人工号
                preinvApplyDet.setJson(pDet.getJson());// 账期json字段
                preinvApplyDet.setBeginCycle(pDet.getBeginCycle());// 开始账期
                preinvApplyDet.setEndCycle(pDet.getEndCycle());// 结束账期
                preinvApplyDet.setOldInvNo(pDet.getInvNo());// 原订单流水
                List<ApplyCycleDet> list2 = preinvApplyService.findToInvNo(pDet.getInvNo());
                for (ApplyCycleDet aDet : list2) {
                    ApplyCycleDet applyCycleDet = new ApplyCycleDet();
                    applyCycleDet.setAmout(aDet.getAmout());// 金额
                    applyCycleDet.setCycleYM(aDet.getCycleYM());// 账期
                    applyCycleDet.setInvNo(invNo);// 发票编码
                    applyCycleDet.setInvoiceType(aDet.getInvoiceType());// 发票状态
                    preinvApplyService.addappApplyCycleDet(applyCycleDet);// 新增账期
                }
                preinvApplyService.addPreinvApplyDet(preinvApplyDet);// 新增开票明细

            }
            // 新增信息
            PreinvApply preinvApply2 = preinvApplyService.addPreinvApply(preinvApply);
            if (preinvApply2.getTaxAddress() != null && !"".equals(preinvApply2.getTaxAddress())) {
                preinvApply2.setTaxAddress(DataCompilationUtil.decryptFromBase64(preinvApply2.getTaxAddress()));
            }
            if (preinvApply2.getTaxName() != null && !"".equals(preinvApply2.getTaxName())) {
                preinvApply2.setTaxName(DataCompilationUtil.decryptFromBase64(preinvApply2.getTaxName()));
            }
            Map<String, Object> map = preinvApplyService.savePreinvApply(preinvApply2);// 推送到BOSS
            Write(JSONHelper.Serialize(map));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 根据uuid查询账户信息
     */
    public void findPreinvApplyDetByUUid() {
        try {
            String id = getString("id");// uuid
            PreinvApplyDet preinvApplyDet = preinvApplyService.findPreinvApplyDetByUUid(id);
            JSONObject jsonObject = JSONObject.fromObject(preinvApplyDet);
            if (preinvApplyDet.getRecDate() != null) {
                jsonObject.put("recDate", getStringDatethree(preinvApplyDet.getRecDate()));
            }
            if (preinvApplyDet.getOpInvDate() != null) {
                jsonObject.put("opInvDate", getStringDatethree(preinvApplyDet.getOpInvDate()));
            }
            if (preinvApplyDet.getRealRecDate() != null) {
                jsonObject.put("realRecDate", getStringDatethree(preinvApplyDet.getRealRecDate()));
            }
            Write(jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 根据订单编号和账户号码查询账期信息
     */
    public void findByContrctNoAndBatchNo() {
        try {
            String contrctNo = getString("contrctNo");// 账户号码
            String batchNo = getString("batchNo");// 订单编号
            List<ApplyCycleDet> list = preinvApplyService.findByContrctNo(contrctNo, batchNo);// 查询账期信息
            Write(JSONHelper.Serialize(list));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 修改账户信息
     */
    public void updatePreinvApplyDet() {
        try {
            String id = getString("id");// 账户id
            String overDueMemo = getString("memo");// 逾期原因
            String waitId = getString("waitId");// 待办id
            WaitTask wt = service.queryWaitByTaskId(waitId);// 获取待办信息
            if (wt != null) {
                System.out.println("================完成开始待办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================完成结束待办================");
            } else {
                Write("NO");
                return;
            }
            PreinvApplyDet preinvApplyDet = preinvApplyService.findPreinvApplyDetByUUid(id);
            preinvApplyDet.setOverDueMemo(overDueMemo);
            preinvApplyService.updatePreinvApplyDet(preinvApplyDet);
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    public static String getYearMonth() {
        Calendar cale = Calendar.getInstance();
        String year = String.valueOf(cale.get(1));
        int m = cale.get(2) + 1;
        String month = "";
        if (m < 10) {
            month = "0" + m;
        } else {
            month = "" + m;
        }

        return year + month;
    }

    /**
     * 在预开票申请发起时，校验帐号号码是否存在未完成的申请工单，如存在则不允许申请。
     */
    public void getPreinvApply() {
        try {
            String contractNo = getString("contractNo");// 账户id
            String[] array = contractNo.split(",");
            for (int i = 0; i < array.length; i++) {
                PreinvApply preinvApply = preinvApplyService.getPreinvApply(array[i]);
                if (preinvApply != null) {
                    Write("账户号码为:" + array[i] + "有未完成的工单,暂时不能申请,工单编号为[" + preinvApply.getBatchNo() + "]，请勿重复开票!或者删除");
                    return;
                }
            }
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * <方法名：queryAccountAndTypeMatch> <详细描述：当用户选择融合专线预开票类型时候，调用接口新增融合专线（和飞速）前打发票校验接口 >
     *
     * @Param: []
     * @return: void
     * @Author: LiYang
     * @Date: 2019/12/31 10:33
     */
    public void queryAccountAndTypeMatch() {
        try {
            String contractNo = getString("contractNo");// 账户号码
            String jsons = preinvApplyService.directLineConvergenceAccount(contractNo);

            JSONObject jsonObject = JSONObject.fromObject(jsons);
            JSONObject resObject = JSONObject.fromObject(jsonObject.getString("res"));
            JSONObject root = (JSONObject) resObject.get("ROOT");
            System.out.println(root.toString());
            String returnCode = (String) root.getString("RETURN_CODE");
            if ("0".equals(returnCode)) {
                JSONObject out_data = JSONObject.fromObject(root.get("OUT_DATA"));
                String contractatt_type = out_data.getString("CONTRACTATT_TYPE");
                String bill_flag = out_data.getString("BILL_FLAG");
                if ("27".equals(contractatt_type)) {
                    if ("Y".equals(bill_flag)) {
                        Write("OK");
                    } else {
                        Write("账户不可申请前打发票,不能选择“专线预开增值税预存发票”");
                        return;
                    }
                } else {
                    Write("OK");
                }
            } else {
                Write(root.getString("RETURN_MSG"));
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 查询工单状态
     */
    public void saveBossPreinvApply() {
        try {
            String message = "";
            String id = getString("id");
            PreinvApply preinvApply = preinvApplyService.findById(id);
            preinvApply.setTaxAddress(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxAddress()));
            preinvApply.setTaxName(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxName()));
            preinvApplyService.savePreinvApplyTwo(preinvApply);
            List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
            //1：申请冲正预开票
            if (preinvApply.getOprType().equals("1")) {
                if (list.size() > 0) {
                    List<PreinvApplyDet> listo = preinvApplyService.findByCommitTypeTwo(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                    for (PreinvApplyDet preinvApplyDet : listo) {
                        preinvApplyDet.setInvState("2");//状态改为已冲正
                        preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                    }
                    PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                    List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                    for (PreinvApplyDet newApplyDet : listo) {
                        for (PreinvApplyDet oldApplyDet : listdets) {
                            // 修改原订单的账户状态
                            if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                    && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                oldApplyDet.setInvState("2");// 状态改为已冲正
                                preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                            }
                        }
                    }
                    preinvApply2.setHandleState("2");// 可重新开票
                    preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                } else {
                    List<PreinvApplyDet> listdet = preinvApplyService.findByUUID(preinvApply.getBatchNo());// 查询账户
                    for (PreinvApplyDet preinvApplyDet : listdet) {
                        preinvApplyDet.setInvState("2");// 状态改为已冲正
                        preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                    }
                    PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                    List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                    for (PreinvApplyDet newApplyDet : listdet) {
                        for (PreinvApplyDet oldApplyDet : listdets) {
                            // 修改原订单的账户状态
                            if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                    && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                oldApplyDet.setInvState("2");// 状态改为已冲正
                                preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                            }
                        }
                    }
                    preinvApply2.setHandleState("2");// 可重新开票
                    preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                }
            }

            if (list.size() > 0) {
                if (preinvApply.getOprType().equals("1")) {
                    preinvApply.setReverseSatate("7");
                } else {
                    preinvApply.setStartState("7");
                    preinvApply.setHandleState("2");// 可重新开票
                }
                message = "操作成功!部分数据未推送到BOSS请重新推送";
            } else {
                if (preinvApply.getOprType().equals("1")) {
                    preinvApply.setReverseSatate("5");
                } else {
                    preinvApply.setStartState("1");// 修改状态为已完成
                    preinvApply.setHandleState("2");// 可重新开票
                }
                message = "操作成功";
            }
            preinvApply.setUpdateDate(new Date());
            preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
            Write(message);
        } catch (Exception e) {
            e.printStackTrace();
            Write("操作失败");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 流程结束客户经理和转发人员循环转发
     */
    public void loopForwarding() {
        Map<String, Object> map = new HashMap<>();
        try {
            String waitId = getString("waitId");
            String id = getString("id");// 账户信息id
            String taskId = getString("preinvApplyTaskid");// 任务表id
            String userid = this.getString("userId");//接收人ID
            String processId = getString("processId");
            String type = getString("type");
            String opinion = getString("opinion");//退回意见
            String phone = getString("phone");//手机端访问电话
            SystemUser user = new SystemUser();
            if (phone == null || phone.equals("") || phone.equals("null")) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.get4ALoginInfo(user.getLoginName());
            }
            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
                WaitTask waitTask = preinvApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                taskId = waitTask.getTaskId();
            }
            PreinvApplyTask task = preinvApplyService.getTaskList(taskId);// 根据id查询任务表信息
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
            // 判断是否经过Base64加密
            preinvApply.setTaxAddress(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxAddress()));
            preinvApply.setTaxName(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxName()));
            if (processId == null) {
                PreinvApplyFlow rap = preinvApplyService.getPid(preinvApply.getBatchNo());
                processId = rap.getFlowId();
            }
            if ("ZF".equals(type)) {
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.valueOf(userid));
                PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
                preinvApplyTask.setFlowId(processId);// 流程id
                preinvApplyTask.setTaskId("");// 环节id
                preinvApplyTask.setTaskName("转发审核");// 环节名称
                preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
                preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
                preinvApplyTask.setCreateDate(new Date());// 创建时间
                preinvApplyTask.setDealNo(USER.getRowNo() + "");// 处理人编号
                preinvApplyTask.setDealName(USER.getEmployeeName());// 处理人名称
                preinvApplyTask.setDealDate(new Date());// 处理时间
                preinvApplyTask.setState("0");// 状态
                PreinvApplyTask pTask = preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
                //forwardToDo(preinvApply,preinvApply.getCreatorId(), processId,pTask,"FQZF");// 生成待办
                forwardToDo(preinvApply, userid, processId, pTask, "ZF", user);// 生成待办
            } else if ("WC".equals(type)) {

            } else {
                if (preinvApply.getPreivType().equals("2")) {
                    //推送BOSS物联网预开票
                    List<InternetOfThingsDet> list = preinvApplyService.getInternetOfThingsDetByApplyNo(preinvApply.getBatchNo());
                    SystemUser sysUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(preinvApply.getCreatorId()));
                    map = preinvApplyService.sWlwIssueInvApply(preinvApply, list, sysUser);
                    String flag = (String) map.get("flag");
                    if ("YES".equals(flag)) {
                        if (preinvApply.getOprType().equals("1")) {
                            preinvApply.setReverseSatate("5");
                        } else {
                            preinvApply.setStartState("1");// 修改状态为已完成
                            preinvApply.setHandleState("2");// 可重新开票
                        }
                        preinvApply.setUpdateDate(new Date());
                        preinvApply.setHANDLER_ID(null);
                        preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
                        // 保存下一步任务
                        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                        PreinvApplyTask pTask = new PreinvApplyTask();
                        pTask.setFlowId(processId);// 流程id
                        pTask.setTaskId("");// 环节id
                        pTask.setTaskName("转发审核");// 环节名称
                        pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
                        pTask.setCreatorName(user.getEmployeeName());// 创建人名称
                        pTask.setCreateDate(new Date());// 创建时间
                        pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
                        pTask.setDealName(USER.getEmployeeName());// 处理人名称
                        pTask.setState("0");// 状态
                        PreinvApplyTask preinvApplyTask = preinvApplyService.addPreinvApplyTask(pTask);//保存信息到任务表
                        forwardToDo(preinvApply, userid, processId, preinvApplyTask, "ZF", user);// 生成待办
                    } else {
                        map.put("flag", "NO");
                        map.put("message", "物联网预开票冲正推送失败");
                        Write(JSONHelper.Serialize(map));
                        return;
                    }
                } else {
                    List<PreinvApplyDet> lists = preinvApplyService.findByUUID(preinvApply.getBatchNo());//所有账户
                    List<PreinvApplyDet> listFour = preinvApplyService.findByUUIDThree(preinvApply.getBatchNo());//所有未推送账户
                    List<PreinvApplyDet> listOne = preinvApplyService.findByCommitType(preinvApply.getBatchNo());//推送失败的账户
                    if (listFour.size() == lists.size()) {
                        map = preinvApplyService.savePreinvApply(preinvApply);
                        String flag = (String) map.get("flag");
//                        String flag = "YES";
                        if ("YES".equals(flag)) {
                            List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                            //1：申请冲正预开票
                            if (preinvApply.getOprType().equals("1")) {
                                if (list.size() > 0) {
                                    List<PreinvApplyDet> listo = preinvApplyService.findByCommitTypeTwo(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                                    for (PreinvApplyDet preinvApplyDet : listo) {
                                        preinvApplyDet.setInvState("2");//状态改为已冲正
                                        preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                                    }
                                    PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                                    List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                                    for (PreinvApplyDet newApplyDet : listo) {
                                        for (PreinvApplyDet oldApplyDet : listdets) {
                                            // 修改原订单的账户状态
                                            if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                                    && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                                oldApplyDet.setInvState("2");// 状态改为已冲正
                                                preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                                            }
                                        }
                                    }
                                    preinvApply2.setHandleState("2");// 可重新开票
                                    preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                                } else {
                                    List<PreinvApplyDet> listdet = preinvApplyService.findByUUID(preinvApply.getBatchNo());// 查询账户
                                    for (PreinvApplyDet preinvApplyDet : listdet) {
                                        preinvApplyDet.setInvState("2");// 状态改为已冲正
                                        preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                                    }
                                    PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                                    List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                                    for (PreinvApplyDet newApplyDet : listdet) {
                                        for (PreinvApplyDet oldApplyDet : listdets) {
                                            // 修改原订单的账户状态
                                            if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                                    && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                                oldApplyDet.setInvState("2");// 状态改为已冲正
                                                preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                                            }
                                        }
                                    }
                                    preinvApply2.setHandleState("2");// 可重新开票
                                    preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                                }
                            }
                            if (list.size() > 0) {
                                String msg = "";
                                for (int i = 0; i < list.size(); i++) {
                                    msg += "账户号码:" + list.get(i).getContrctNo() + "-失败原因:" + list.get(i).getErrorMessage() + ";";
                                }
                                map.put("flag", "YES");
                                map.put("message", "操作成功!部分数据未推送到BOSS请至列表重新推送;" + msg);
                                if (preinvApply.getOprType().equals("1")) {
                                    preinvApply.setReverseSatate("7");
                                } else {
                                    preinvApply.setStartState("7");
                                    preinvApply.setHandleState("2");// 可重新开票
                                }
                            } else {
                                if (preinvApply.getOprType().equals("1")) {
                                    preinvApply.setReverseSatate("5");
                                } else {
                                    preinvApply.setStartState("1");// 修改状态为已完成
                                    preinvApply.setHandleState("2");// 可重新开票
                                }
                                map.put("flag", "YES");
                                map.put("message", "操作成功");
                            }
                            preinvApply.setUpdateDate(new Date());
                            preinvApply.setHANDLER_ID(null);
                            preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
                            // 保存下一步任务
                            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                            PreinvApplyTask pTask = new PreinvApplyTask();
                            pTask.setFlowId(processId);// 流程id
                            pTask.setTaskId("");// 环节id
                            pTask.setTaskName("转发审核");// 环节名称
                            pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
                            pTask.setCreatorName(user.getEmployeeName());// 创建人名称
                            pTask.setCreateDate(new Date());// 创建时间
                            pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
                            pTask.setDealName(USER.getEmployeeName());// 处理人名称
                            pTask.setState("0");// 状态
                            PreinvApplyTask preinvApplyTask = preinvApplyService.addPreinvApplyTask(pTask);//保存信息到任务表
                            forwardToDo(preinvApply, userid, processId, preinvApplyTask, "ZF", user);// 生成待办
                            Write(JSONHelper.Serialize(map));
                        } else {
                            List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                            String msg = "";
                            for (int i = 0; i < list.size(); i++) {
                                msg += "账户号码:" + list.get(i).getContrctNo() + "-失败原因:" + list.get(i).getErrorMessage() + ";";
                            }
                            map.put("flag", "NO");
                            if (list.size() > 1) {
                                map.put("message", "推送BOSS失败!不能转发;" + msg);
                            } else {
                                String message = (String) map.get("message");
                                map.put("message", message);
                            }
                            Write(JSONHelper.Serialize(map));
                            return;
                        }
                    } else if (listOne.size() == lists.size()) {
                        map = preinvApplyService.savePreinvApply(preinvApply);
                        String flag = (String) map.get("flag");
                        if ("YES".equals(flag)) {
                            List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                            //1：申请冲正预开票
                            if (preinvApply.getOprType().equals("1")) {
                                if (list.size() > 0) {
                                    List<PreinvApplyDet> listo = preinvApplyService.findByCommitTypeTwo(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                                    for (PreinvApplyDet preinvApplyDet : listo) {
                                        preinvApplyDet.setInvState("2");//状态改为已冲正
                                        preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                                    }
                                    PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                                    List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                                    for (PreinvApplyDet newApplyDet : listo) {
                                        for (PreinvApplyDet oldApplyDet : listdets) {
                                            // 修改原订单的账户状态
                                            if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                                    && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                                oldApplyDet.setInvState("2");// 状态改为已冲正
                                                preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                                            }
                                        }
                                    }
                                    preinvApply2.setHandleState("2");// 可重新开票
                                    preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                                } else {
                                    List<PreinvApplyDet> listdet = preinvApplyService.findByUUID(preinvApply.getBatchNo());// 查询账户
                                    for (PreinvApplyDet preinvApplyDet : listdet) {
                                        preinvApplyDet.setInvState("2");// 状态改为已冲正
                                        preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
                                    }
                                    PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
                                    List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
                                    for (PreinvApplyDet newApplyDet : listdet) {
                                        for (PreinvApplyDet oldApplyDet : listdets) {
                                            // 修改原订单的账户状态
                                            if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
                                                    && newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
                                                oldApplyDet.setInvState("2");// 状态改为已冲正
                                                preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
                                            }
                                        }
                                    }
                                    preinvApply2.setHandleState("2");// 可重新开票
                                    preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
                                }
                            }
                            if (list.size() > 0) {
                                String msg = "";
                                for (int i = 0; i < list.size(); i++) {
                                    msg += "账户号码:" + list.get(i).getContrctNo() + "-失败原因:" + list.get(i).getErrorMessage() + ";";
                                }
                                map.put("flag", "YES");
                                map.put("message", "操作成功!部分数据未推送到BOSS请至列表重新推送;" + msg);
                                if (preinvApply.getOprType().equals("1")) {
                                    preinvApply.setReverseSatate("7");
                                } else {
                                    preinvApply.setStartState("7");
                                    preinvApply.setHandleState("2");// 可重新开票
                                }
                            } else {
                                if (preinvApply.getOprType().equals("1")) {
                                    preinvApply.setReverseSatate("5");
                                } else {
                                    preinvApply.setStartState("1");// 修改状态为已完成
                                    preinvApply.setHandleState("2");// 可重新开票
                                }
                                map.put("flag", "YES");
                                map.put("message", "操作成功");
                            }
                            preinvApply.setUpdateDate(new Date());
                            preinvApply.setHANDLER_ID(null);
                            preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息

                            // 保存下一步任务
                            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                            PreinvApplyTask pTask = new PreinvApplyTask();
                            pTask.setFlowId(processId);// 流程id
                            pTask.setTaskId("");// 环节id
                            pTask.setTaskName("转发审核");// 环节名称
                            pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
                            pTask.setCreatorName(user.getEmployeeName());// 创建人名称
                            pTask.setCreateDate(new Date());// 创建时间
                            pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
                            pTask.setDealName(USER.getEmployeeName());// 处理人名称
                            pTask.setState("0");// 状态
                            PreinvApplyTask preinvApplyTask = preinvApplyService.addPreinvApplyTask(pTask);//保存信息到任务表
                            forwardToDo(preinvApply, userid, processId, preinvApplyTask, "ZF", user);// 生成待办
                            Write(JSONHelper.Serialize(map));
                        } else {
                            List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
                            String msg = "";
                            for (int i = 0; i < list.size(); i++) {
                                msg += "账户号码:" + list.get(i).getContrctNo() + "-失败原因:" + list.get(i).getErrorMessage() + ";";
                            }
                            map.put("flag", "NO");
                            if (list.size() > 1) {
                                map.put("message", "推送BOSS失败!不能转发;" + msg);
                            } else {
                                String message = (String) map.get("message");
                                map.put("message", message);
                            }
                            Write(JSONHelper.Serialize(map));
                            return;
                        }
                    } else {
                        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                        // 保存下一步任务
                        PreinvApplyTask pTask = new PreinvApplyTask();
                        pTask.setFlowId(processId);// 流程id
                        pTask.setTaskId("");// 环节id
                        pTask.setTaskName("转发审核");// 环节名称
                        pTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
                        pTask.setCreatorName(user.getEmployeeName());// 创建人名称
                        pTask.setCreateDate(new Date());// 创建时间
                        pTask.setDealNo(USER.getRowNo() + "");// 处理人编号
                        pTask.setDealName(USER.getEmployeeName());//处理人名称
                        pTask.setState("0");// 状态
                        PreinvApplyTask preinvApplyTask = preinvApplyService.addPreinvApplyTask(pTask);// 保存信息到任务表
                        //daiban(manualInvApply,userid,processId,TaskBeantwo,"ZF");
                        forwardToDo(preinvApply, userid, processId, preinvApplyTask, "ZF", user);// 生成待办
                    }
                }
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                logger.info("================转发开始代办================");
                service.updateWait(wait, this.getRequest());
                logger.info("================转发结束代办================");
            } else {
                map.put("flag", "NO");
                map.put("message", "操作失败!未查询到待办");
                Write(JSONHelper.Serialize(map));
                return;
            }
            if (task != null) {
                task.setDealDate(new Date());// 处理日期
                task.setTaskMemo(opinion);// 处理意见
                task.setState("1");// 修改状态为已完成
                preinvApplyService.updatePreinvApplyTask(task);// 修改任务表信息
            }
            map.put("flag", "YES");
            map.put("message", "操作成功");
            Write(JSONHelper.Serialize(map));
        } catch (Exception e) {
            logger.error("物联网预开票推送错误:" + e.getMessage(), e);
            e.printStackTrace();
            map.put("flag", "NO");
            map.put("message", "操作失败,请联系管理员查询失败原因");
            Write(JSONHelper.Serialize(map));
        }
    }

    /**
     * 作废方法
     */
    public void completeInvalidPreinvApply() {
        try {
            String id = getString("id");// 开票id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 作废原因
            String taskId = getString("preinvApplyTaskid");// 任务表id
            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
                WaitTask waitTask = preinvApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                taskId = waitTask.getTaskId();
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            PreinvApplyTask pTask = preinvApplyService.getTaskList(taskId);// 根据id查询任务表信息
            if (pTask != null) {
                pTask.setDealDate(new Date());// 处理日期
                pTask.setTaskMemo(opinion);// 处理意见
                pTask.setState("-1");// 修改状态为本地作废
                preinvApplyService.updatePreinvApplyTask(pTask);// 修改任务表信息
            }
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询
            if (preinvApply.getOprType().equals("1")) {
                preinvApply.setReverseSatate("-1");// 状态修改为作废
            } else {
                preinvApply.setStartState("-1");// 状态修改为作废
            }
            preinvApply.setUpdateDate(new Date());// 更新时间
            preinvApply.setCancelMemo(opinion);// 作废原因
            preinvApply.setHANDLER_ID("");
            preinvApplyService.updatePreinvApply(preinvApply);// 修改
            if (wait != null) {
                System.out.println("================作废开始待办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================作废结束待办================");
            } else {
                Write("NO");
                throw new RuntimeException("事务回滚");
            }
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");

        }

    }

    public void getContracNo() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String contrctNo = getString("contrctNo");//账户
            String json = preinvApplyService.getContracNo(page, contrctNo);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询配置金额
     */
    public void getTransferCitiesData() {
        try {
            String nextTask = getString("nextTask");
            List<SystemDept> deptList = user.getSystemDept();
            String code = deptList.get(0).getSystemCompany().getCompanyCode();
            //System.out.println("这是我请求的配置金额的action");
            TransferCitiesData transferCitiesData = preinvApplyService.getTransferCitiesData(code, nextTask);
            Write(JSONHelper.SerializeWithNeedAnnotation(transferCitiesData));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }

    /**
     * 调用boss接口推送信息
     */
    public void sIssueInvoice() {
        String url = Config.getString("ESBWS_URL") + "sIssueInvoice";
        Map<String, Object> mapJson = new HashMap<>();
        try {

            //对象
            Map<String, Object> map = new HashMap<>();
            String bossJson = pressGetLoginMsgSvc(user.getBossUserName());
            JSONObject orgInfoJson = JSONObject.fromObject(bossJson);
            String orgInfo = "";
            if (orgInfoJson.has("GROUP_ID")) {
                orgInfo = orgInfoJson.get("GROUP_ID").toString();
            } else {
                logger.error("查询客户经理渠道信息失败");
                Write("NO");
            }
            String bossNo = user.getBossUserName();
            String groupCode = getString("groupCode");//集团编码
            String type = getString("type");//非空，0个人客户；  1集团客户
            String starTime = getString("starTime").replace("-", "");//账期开始日期
            String endTime = getString("endTime").replace("-", "");//账期结束日期
            String frank = getString("frank");//非空，0：专票；  1：普票；
            String prepvoucher = getString("prepvoucher");//0，非预开发票   1：预开发票
            String voucherType = getString("voucherType");//非空，0：月结发票  1：预存发票  2:营业发票
            if (voucherType.equals("0")) {
                starTime = starTime.substring(0, 6) + "00";
                endTime = endTime.substring(0, 6) + "00";
            }
            JSONObject object = new JSONObject();
            object.put("CUSTOMER_ID", groupCode);//非空，280开头的短集团编码。根据该字段获取集团客户在BBOSS的唯一标识
            object.put("CUSTOMER_TYPE", type);//非空，0个人客户；  1集团客户
            object.put("FRANK", frank);//非空，0：专票；  1：普票；
            object.put("VOUCHER_TYPE", voucherType);//非空，0：月结发票  1：预存发票  2:营业发票
            if ("0".equals(voucherType)) {
                object.put("PREP_VOUCHER", prepvoucher);//可不传，缺省默认0，非预开发票   1：预开发票。仅月结发票填写有效，发票开具不判断缴费销帐情况，即未销帐也可以开具发票
            } else {
                object.put("PREP_VOUCHER", "0");//可不传，缺省默认0，非预开发票   1：预开发票。仅月结发票填写有效，发票开具不判断缴费销帐情况，即未销帐也可以开具发票
            }
            object.put("START_TIME", starTime);//非空，格式YYYYMMDD；月结发票开具时填写开始帐期，DD固定填写00，如201707帐期填写20170700预存发票开具时填写缴费日期范围的开始时间，如20170723
            object.put("END_TIME", endTime);//非空，格式：YYYYMMDD；月结发票开具时填写结束帐期，DD固定填写00，如201709帐期填写20170900，结束帐期月-开始帐期月≤6；预存发票开具时填写缴费日期范围的结束时间，如20170730，支持开具6个月内的预存发票
            object.put("CHANNEL", "01");//非空，操作渠道来源,默认值01
            object.put("BIZ_TYPE", "0601");//非空，0601：该接口数据全部来源于物联网集中化支撑系统
            object.put("HOME_PROV", "280");//可不传，归属省省代码
            object.put("LOGIN_NO", bossNo);//操作工号
            object.put("GROUP_ID", orgInfo);//机构代码
            String json = setParamObj(object, bossNo);
            String jsonString = UrlConnection.responseGBK(url, json);
            JSONObject jsthree = JSONObject.fromObject(jsonString);
            String datatwo = jsthree.getString("res");
            JSONObject jsone = JSONObject.fromObject(datatwo);
            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                JSONObject outData = JSONObject.fromObject(jstwo.getString("OUT_DATA"));
                mapJson.put("code", 1);
                mapJson.put("data", outData.toString());
                mapJson.put("msg", "");
                Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
            } else {
                mapJson.put("code", -1);
                mapJson.put("data", "");
                mapJson.put("msg", jstwo.getString("RETURN_MSG") + jstwo.getString("DETAIL_MSG"));
                Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
            }

        } catch (Exception e) {
            e.printStackTrace();
            mapJson.put("code", -1);
            mapJson.put("data", "");
            mapJson.put("msg", "系统错误");
            Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
        }
    }

    /**
     * 查询渠道编码
     */
    public String pressGetLoginMsgSvc(String bossNo) {
        try {
            Map<String, Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(bossNo);
            String json = JSONHelper.SerializeWithOutInnerClass(mapcfm);
            logger.info("查询渠道编码：" + json);
            //String json ="{\"REGION_ID\":\"11\",\"POWER_RIGHT\":\"18\",\"GROUP_ID\":\"23\",\"RETURN_MSG\":\"ok!\",\"PROMPT_MSG\":\"\",\"RETURN_CODE\":\"0\",\"LOGIN_NO\":\"aa0002107\",\"USER_MSG\":\"处理成功!\",\"DETAIL_MSG\":\"OK!\"}";
            return json;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
    }

    public void saveBossGroupOrGroupId() {
        try {
            String bossJson = pressGetLoginMsgSvc(user.getBossUserName());
            JSONObject orgInfoJson = JSONObject.fromObject(bossJson);
            String orgInfo = "";
            if (orgInfoJson.has("GROUP_ID")) {
                orgInfo = orgInfoJson.get("GROUP_ID").toString();
            } else {
                logger.error("查询客户经理渠道信息失败");
                Write("查询客户经理渠道信息失败");
                return;
            }
            String groupcode = getString("groupCode");
            Map<String, Object> map = preinvApplyService.saveBossGroupOrGroupId(orgInfo, groupcode, user);
            String flag = (String) map.get("flag");
            if ("YES".equals(flag)) {
                Write("YES");
            } else {
                Write((String) map.get("message"));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
            Write("系统错误");
        }
    }

    // 设置参数
    protected String setParamObj(JSONObject body, String bossNo) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "14");
        routing.put("ROUTE_VALUE", bossNo);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        return root.toString();
    }

    /**
     * 预开票统计报表    权限查询
     * tangxiao  21-03-08
     */
    public void getUserCompany() {
        // 获取当前用户
        SystemUser user = this.user;
        // 获取用户权限
        List list = preinvApplyService.findByRowNo(user.getRowNo());
        Boolean flag = true;
        for (int i = 0; i < list.size(); i++) {
            if ((list.get(i).toString()).equals("16")) {
                flag = false;
                break;
            }
        }
        if (flag) {
            Write(preinvApplyService.getUserCompany(user));
        } else {
            Write("订单管理员");
        }
    }

    public void getCities() {
        String userRole = getString("userRole");
        SystemUser user = this.user;
        List<Map<String, Object>> VwUser = preinvApplyService.getVwUser(user);
        List<Map<String, String>> list = null;
        if (!"市公司".equals(userRole) && !"区县公司".equals(userRole)) {
            list = preinvApplyService.getCompay();
        } else if ("市公司".equals(userRole)) {
            list = preinvApplyService.getCOUNTY(VwUser.get(0).get("COMPANY_NAME").toString());
        }
        JSONArray jsonArray = JSONArray.fromObject(list);
        Write(jsonArray.toString());
    }

    public void getCounty() {
        String cities_shi = getString("cities_shi");
        List<Map<String, String>> list = preinvApplyService.getCOUNTY(cities_shi);
        JSONArray jsonArray = JSONArray.fromObject(list);
        Write(jsonArray.toString());
    }

    public void showPreinvApplyDate() {
        String cities_shi = getString("cities_shi");
        String cities_unit = getString("cities_unit");
        String start_Time = getString("start_Time");
        String end_Time = getString("end_Time");

        Integer pageNo = getInteger("pageNo");
        Integer pageSize = getInteger("pageSize");
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        // 获取当前用户
        SystemUser user = this.user;
        // 获取用户权限
        List list = preinvApplyService.findByRowNo(user.getRowNo());
        Boolean flag = false;
        for (int i = 0; i < list.size(); i++) {
            if ((list.get(i).toString()).equals("16")) {
                flag = true;
                break;
            }
        }
        page = preinvApplyService.showPreinvApplyDate(start_Time, end_Time, cities_unit, cities_shi, page, flag, user);
        String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
        Write(json);
    }

    public void updatePreinvApply() {
        String type = getString("type");
        if ("Y".equals(type)) {
            if ("OK".equals(preinvApplyService.updatePreinvApply())) {
                Write("数据全部更新成功！");
            } else {
                Write("更新数据失败！");
            }
        } else {
            if ("OK".equals(preinvApplyService.updatePreinvApplyTwo())) {
                Write("更新上一年数据成功！");
            } else {
                Write("更新上一年数据失败！");
            }
        }
    }


    public void findTransition() {
        try {
            String id = getString("id");
            logger.info("获取的预开票id===" + id);
            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
//        PreinvApplyFlow rap = preinvApplyService.getPid(preinvApply.getBatchNo());
            List<PreinvApplyFlow> raps = preinvApplyService.getPids(preinvApply.getBatchNo());
            //logger.info("raps=="+String.valueOf(JSONArray.fromObject(raps)));
            String pid = "";
            if (raps != null && raps.size() > 1) {
                for (int i = 0; i < raps.size(); i++) {
                    PreinvApplyFlow preinvApplyFlow = raps.get(i);
                    if (preinvApplyFlow.getLogo() != null && preinvApplyFlow.getLogo().equals("1")) {
                        pid = preinvApplyFlow.getFlowId();
                    }
                }
                //System.out.println("raps 数组包含超过一个元素");
            } else {
                pid = raps.get(0).getFlowId();
                //System.out.println("raps 数组为空或者只包含一个元素");
            }
            //pid = rap.getFlowId();//PreinvApplyProcessFinal.40600328
            //JbpmTest jt = new JbpmTest();
            logger.info("预开票pid==" + pid);
            if (!"".equals(pid) && pid != null) {
                // 获取任务对象
                try {
                    logger.info("所查询的流程ID：=======》" + pid);
                    Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                    Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                    //JSONArray jArray = new JSONArray();
                    logger.info("当前流程节点====》" + task.getActivityName());
                    JSONObject obj = new JSONObject();
                    for (String outcome : setlist) {
                        if (!outcome.equals("ALL")) {
                            obj.put("transitionName", outcome);//选人
                            logger.info("当前可执行的流程走向====》" + outcome);
                        }
                        //jArray.add(obj);
                    }
                    obj.put("dangqianrenwu", task.getActivityName());//当前角色名字
                    obj.put("pid", pid);//流程id
                    //System.out.println("=="+obj.toString());
                    writeText(obj.toString());
                } catch (Exception e) {
                    // TODO: handle exception
                    logger.error("预开票流程信息获取异常====》" + e.getMessage(), e);
                    writeText("流程信息获取异常");
                }
            } else {
                logger.info("预开票流程ID为空");
                writeText("流程ID为空");
            }
        } catch (Exception e) {
            logger.error("预开票流程方法异常====》" + e.getMessage(), e);
        }
    }

    //手机端工单页综合信息查询
    public void findComprehensiveById() {
        Result r = new Result();
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            String orderNo = getString("orderNo");
            PreinvApply preinvApply = new PreinvApply();
            if (id != null && !"".equals(id) && !"null".equals(id)) {
                preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
            } else {
                preinvApply = preinvApplyService.getPreinvApplyByBachNo(orderNo);
            }
            if ("2".equals(preinvApply.getPreivType())) {
                List<InternetOfThingsDet> list = preinvApplyService.getInternetOfThingsDetByApplyNo(preinvApply.getBatchNo());
                JSONArray jsonArray = JSONArray.fromObject(list);
                map.put("preinvApplyDet", jsonArray);
            } else if ("3".equals(preinvApply.getPreivType())) {
                List<ValuableCardDet> list = preinvApplyService.getValuableCardDetByApplyNo(preinvApply.getBatchNo());
                JSONArray jsonArray = JSONArray.fromObject(list);
                map.put("preinvApplyDet", jsonArray);
            } else {
                List<PreinvApplyDet> list = preinvApplyService.findByUUID(preinvApply.getBatchNo());
                JSONArray jsonArray = JSONArray.fromObject(list);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    PreinvApplyDet preinvApplyDet = list.get(i);
                    List<Map<String, Object>> listApply = preinvApplyService.findByInvNo(preinvApplyDet.getInvNo());
                    JSONArray array = JSONArray.fromObject(listApply);
                    jsonObject.put("ApplyCycleDet", array);
                    if (preinvApplyDet.getRecDate() != null) {
                        jsonObject.put("recDate", getStringDatethree(preinvApplyDet.getRecDate()));
                    }
                    if (preinvApplyDet.getOpInvDate() != null) {
                        jsonObject.put("opInvDate", getStringDatethree(preinvApplyDet.getOpInvDate()));
                    }
                    if (preinvApplyDet.getRealRecDate() != null)
                        jsonObject.put("realRecDate", getStringDatethree(preinvApplyDet.getRealRecDate()));
                }
                map.put("preinvApplyDet", jsonArray);
            }
            PreinvApplyFlow preinvApplyFlow = preinvApplyService.findbyPreinvBatchNo(preinvApply.getBatchNo(), null);
            List<PreinvApplyTask> p = preinvApplyService.processtracking(preinvApplyFlow.getFlowId());
            preinvApply.setTaxAddress(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxAddress()));
            preinvApply.setTaxName(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxName()));
            map.put("findById", preinvApply);
            map.put("processtracking", p);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(map);
            System.out.println("预开票信息==" + JSONHelper.SerializeWithNeedAnnotationDateFormats(r));
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(r));
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("查询详情失败");
            Write(r.toString());
        }
    }

    /**
     * 获取当前流程按钮 (手机端用)
     *
     * @throws ParseException
     */
    public void findTransitionApp() {
        logger.info("预开票运行获取当前流程按钮");
        Result r = new Result();
        String id = getString("id");
        String phone = getString("phone");
        //logger.info("id=="+id);
        //logger.info("phone=="+phone);
        List outcomes = new ArrayList<>();
        SystemUser user = new SystemUser();
        try {
            user = systemUserService.getUserByPhone(phone);
            //System.out.println("获取的loginName为=="+user.getLoginName());
            user = systemUserService.querUsers(user.getLoginName());
            if (user == null) {
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("查询人员失败，号码有误");
                Write(r.toString());
                return;
            }
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("根据号码查询人员异常");
            Write(r.toString());
            return;
        }
        PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
        PreinvApplyFlow rap = preinvApplyService.getPid(preinvApply.getBatchNo());
        String pid = rap.getFlowId();
        //JbpmTest jt = new JbpmTest();
        if (!"".equals(pid) && pid != null) {
            // 获取任务对象
            try {
                logger.info("所查询的流程ID：=======》" + pid);
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                //JSONArray jArray = new JSONArray();
                List buttons = new ArrayList<>();
                logger.info("当前流程节点====》" + task.getActivityName());
                JSONObject obj = new JSONObject();
                for (String outcome : setlist) {
                    if (!outcome.equals("结束")) {
                        outcomes.add(outcome);
                    }
                    //jArray.add(obj);
                }
                List<SystemDept> deptList = user.getSystemDept();
                //logger.info("deptList=="+JSONObject.fromObject(deptList));
                String code = deptList.get(0).getSystemCompany().getCompanyCode();
                //logger.info("code=="+code);
                String str = pid.substring(0, pid.indexOf("."));
                TransferCitiesData transferCitiesData = preinvApplyService.getTransferCitiesData(code, task.getActivityName());
                if ("PreinvApplyLimitProcessFinal".equals(str)) {
                    if (task.getActivityName().equals("市公司政企部经理")) {
                        if (Double.valueOf(transferCitiesData.getAmount()) > 0) {
                            if (Double.valueOf(preinvApply.getAppAmout()) < Double.valueOf(transferCitiesData.getAmount())) { //parseFloat(amountOfMoney) < parseFloat(amount)
                                buttons.add("完成");
                            } else if (Double.valueOf(preinvApply.getAppAmout()) >= Double.valueOf(transferCitiesData.getAmount())) {
                                buttons.add("提交");
                            }
                        } else {
                            buttons.add("提交");
                        }
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("省公司集团客户部发票管理员")) {
                        buttons.add("完成");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else {
                        buttons.add("提交");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    }
                } else {
                    if (task.getActivityName().equals("市公司客户经理室经理") || task.getActivityName().equals("区县业务管理员") || task.getActivityName().equals("区县政企部主任") || task.getActivityName().equals("区县分管经理") || task.getActivityName().equals("省重客客户经理室经理")) {
                        if ("PreinvApplyProcessFinal".equals(str)) {
                            if (Double.valueOf(transferCitiesData.getAmount()) > 0) {
                                if (Double.valueOf(preinvApply.getAppAmout()) < Double.valueOf(transferCitiesData.getAmount())) { //parseFloat(amountOfMoney) < parseFloat(amount)
                                    buttons.add("完成");
                                } else if (Double.valueOf(preinvApply.getAppAmout()) >= Double.valueOf(transferCitiesData.getAmount())) {
                                    buttons.add("提交");
                                }
                            } else {
                                buttons.add("提交");
                            }
                        } else {
                            if (task.getActivityName().equals("区县政企部主任") || task.getActivityName().equals("省重客客户经理室经理")) {
                                buttons.add("提交");
                            } else {
                                if (Double.valueOf(transferCitiesData.getAmount()) > 0) {
                                    if (Double.valueOf(preinvApply.getAppAmout()) < Double.valueOf(transferCitiesData.getAmount())) { //parseFloat(amountOfMoney) < parseFloat(amount)
                                        buttons.add("完成");
                                    } else if (Double.valueOf(preinvApply.getAppAmout()) >= Double.valueOf(transferCitiesData.getAmount())) {
                                        buttons.add("提交");
                                    }
                                } else {
                                    buttons.add("提交");
                                }
                            }
                        }
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司政企部经理")) {
                        //logger.info("运行task.getActivityName=="+task.getActivityName());
                        if (Double.valueOf(transferCitiesData.getAmount()) > 0) {

                            if (Double.valueOf(preinvApply.getAppAmout()) < Double.valueOf(transferCitiesData.getAmount())) { //parseFloat(amountOfMoney) < parseFloat(amount)
                                buttons.add("完成");
                                buttons.add("转审");
                            } else if (Double.valueOf(preinvApply.getAppAmout()) >= Double.valueOf(transferCitiesData.getAmount())) {
                                buttons.add("提交");
                                buttons.add("转审");
                            }
                        } else {
                            buttons.add("提交");
                            buttons.add("转审");
                        }
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                        //preinvApply.getAppAmout();//总申请金额
                        //transferCitiesData.getAmount();//配置的金额
                    } else if (task.getActivityName().equals("其他审核人1") || task.getActivityName().equals("其他审核人2") || task.getActivityName().equals("其他审核人3") || task.getActivityName().equals("其他审核人4")) {
                        buttons.add("提交");
                        buttons.add("转审");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司业务管理员") || task.getActivityName().equals("市公司业务管理室经理")) {
                        buttons.add("提交");
                        buttons.add("转审");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司领导") || task.getActivityName().equals("省重客分管经理") || task.getActivityName().equals("省公司管理员")) {
                        buttons.add("完成");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    }
                }
                logger.info("按钮获取的角色为==" + r.toString());
                Write(r.toString());
            } catch (Exception e) {
                // TODO: handle exception
                logger.error("预开票手机端按钮流程信息获取异常====》" + e.getMessage(), e);
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("流程信息获取异常");
                Write(r.toString());
            }
        } else {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("流程ID为空");
            Write(r.toString());
        }
    }

    //预开票工单查询冲正工单根据oldId
    public void getPreinvApplyByoldId() {
        try {
            String batchNo = getString("batchNo");//根据账户详情的批次号
            PreinvApply p = preinvApplyService.getPreinvApplyByoldIdNew(batchNo);//预开票工单
            String batchNo1 = p.getBatchNo();
            PreinvApply preinvApplyByoldId = preinvApplyService.getPreinvApplyByoldId(batchNo1);//冲正工单
            Write(JSONHelper.Serialize(preinvApplyByoldId));
        } catch (Exception e) {
            Write("");
            logger.error("根据oldId查询工单信息错误" + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    //根据冲正查询预开票   preinvApply.setOldId(pApply.getBatchNo());//原开票编码 把开票编码存入原开票编码中
    public void getPreinvApplyByoldIdNew() {
        try {

            String batchNo = getString("batchNo");//根据账户详情的批次号
            PreinvApply p = preinvApplyService.getPreinvApplyByoldIdNew(batchNo);
            String oldId = p.getOldId();//获取预开票工单原开票编码
            PreinvApply New = preinvApplyService.getPreinvApplyByoldIdNew(oldId);//获得预开票工单信息
            Write(JSONHelper.Serialize(New));
//            if(p==null){
//                Write("");
//            }else {
//                Write(p.getUuid());
//            }
        } catch (Exception e) {
            Write("");
            logger.error("根据冲正查询预开票信息错误" + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    /**
     * 预开票统计 分页查询
     */
    public void pagingQuery() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo()); // 获取公司名称
            if (companyCode.get(0).equals("00")) {//省公司
                page = this.preinvApplyService.pagingQuery(page);
            } else {
                page = this.preinvApplyService.pagingQueryTwo(page, companyCode.get(0).toString());
            }
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            this.Write(json);
        } catch (Exception var12) {
            var12.printStackTrace();
            this.Write("NO");
        }
    }

    //导出数据
    public void exportData() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            List companyCode = preinvApplyService.findCodeByRowNo(user.getRowNo()); // 获取公司名称
            if (companyCode.get(0).equals("00")) {//省公司
                List<Map<String, Object>> list = this.preinvApplyService.getExportData();
                preinvApplyService.exportData(list);
            } else {
                List<Map<String, Object>> list = this.preinvApplyService.getExportDataTwo(companyCode.get(0).toString());
                preinvApplyService.exportData(list);
            }


        } catch (Exception var12) {
            var12.printStackTrace();
            this.Write("NO");
        }
    }

    /**
     * 验证预开票检查是否可添加接口
     */
    public void verifyAddPreinvApply() {
        try {
            logger.info("运行验证预开票检查是否可添加接口");
            PreinvApplyDet preinvApplyDet = new PreinvApplyDet();
            String stateTime = preinvApplyService.getNumber();
            String groupCode = getString("groupCode");//集团编号
            String contractNo = getString("contractNo");//账户
            String phoneNo = getString("phoneNo");//电话
            preinvApplyDet.setContrctNo(contractNo);
            preinvApplyDet.setPhone(phoneNo);
            preinvApplyDet.setInvNo("KP" + "" + stateTime);
            String verifyAdd = preinvApplyService.verifyAddPreinvApply(preinvApplyDet, groupCode);
            logger.info("运行验证预开票检查是否可添加接口返回==" + verifyAdd);
            Write(verifyAdd);
//            Write("NO");
        } catch (Exception e) {
            logger.error("验证预开票检查是否可添加接口错误==" + e.getMessage(), e);
            Write("ERROR");
        }

    }

    /**
     * 根据id查询开票信息
     */
    public void findPreinvApplyByBachNo() {
        try {
            String orderNo = getString("orderNo");
            PreinvApply preinvApply = preinvApplyService.getPreinvApplyByBachNo(orderNo);
            preinvApply.setTaxAddress(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxAddress()));
            preinvApply.setTaxName(DataCompilationUtil.decryptFromBase64(preinvApply.getTaxName()));
            //taxAddress taxBankName
            JSONObject jsonObject = JSONObject.fromObject(preinvApply);
            if (preinvApply.getCreateDate() != null) {
                jsonObject.put("createDate", getStringDatethree(preinvApply.getCreateDate()));
            }
            if (preinvApply.getRecDate() != null) {
                jsonObject.put("recDate", getStringDatethree(preinvApply.getRecDate()));
            }
            if (preinvApply.getUpdateDate() != null) {
                jsonObject.put("updateDate", getStringDatethree(preinvApply.getUpdateDate()));
            }
            Write(jsonObject.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");

        }
    }

    public void preinvApplyIPreInvApplyNumQry() {
        try {
            String contractNo = getString("contractNo");//账户
            Result result = preinvApplyService.com_sitech_acctmgr_inter_invoice_IPreInvApplyNumQry_qry(contractNo);
            System.out.println(result);
            if (result.getCode() == 200) {
                JSONObject json = JSONObject.fromObject(result.getData());
                JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
                if ("0".equals(root.getString("RETURN_CODE"))) {
                    JSONObject jsonObject = root.getJSONObject("OUT_DATA");
                    Write(jsonObject.getString("BATCH_COUNT"));
//                    Write("3");
                } else {
                    Write("-1");
                }
            }
        } catch (Exception e) {
            logger.error("预开发票未回款次数查询==" + e.getMessage(), e);
            Write("-2");
        }
    }

    // 判断方法的实现
    private static boolean isBase64Encoded(String str) {
        try {
            // 尝试解码Base64
            DataCompilationUtil.encryptFromBase64(str);
            return true; // 解码成功，说明经过了Base64加密
        } catch (IllegalArgumentException e) {
            return false; // 解码失败，说明没有经过Base64加密
        }
    }


    //预开票支撑人员管理---------------------------------------------------------
    private File file;

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    /**
     * 生成响应对象
     *
     * @param state 响应状态(1:成功 , -1:失败)
     * @param data  返回对象
     * @param msg   返回信息
     * @return 响应JSON对象
     */
    private static String returnPars(int state, Object data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }


    /*
     * <AUTHOR>
     * @Date 2023/12/6 11:01
     * @Description 支撑人员管理分页查询
     **/
    public void getStaffManagementList() {
        try {
            String bossNo = getString("bossNo");          // BOSS工号
            Integer pageNo = getInteger("pageNo");      // 当前页码数
            Integer pagesize = getInteger("pageSize");  // 每页显示件数
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            page = preinvApplyService.getStaffManagementList(page, bossNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("有价卡额度管理列表查询错误:" + e.getMessage(), e);
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/12/6 15:14
     * @Description 支撑人员模版下载
     **/
    public void downloadExcel() {
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name = "支撑人员模版";
            String filepath = request.getSession().getServletContext().getRealPath("/template/StaffManagement.xlsx");
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
            logger.info("下载模板错误==>" + var9);
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/12/6 15:23
     * @Description 模版导入
     **/
    public void importExcel() {
        try {
            ExcelUtil excelReader = new ExcelUtil(this.file);
            InputStream is = new FileInputStream(this.file);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList<>();
            int i;
            HashMap<String, Object> maps;
            if (column == 4) {
                for (i = 1; i <= map.size(); ++i) {
                    maps = new HashMap<>();
                    maps.put("userName", (map.get(i)).get(0));
                    maps.put("bossNo", (map.get(i)).get(1));
                    maps.put("city", (map.get(i)).get(2));
                    maps.put("expirationDate", (map.get(i)).get(3));
                    list.add(maps);
                }
            } else {
                this.Write(returnPars(-1, null, "上传文件格式错误,请下载模板文件上传!"));
            }
            if (map.size() > 0) {
                this.Write(returnPars(1, list, "上传文件成功,共导入" + list.size() + "条数据!"));
            } else {
                this.Write(returnPars(-1, null, "上传的表格数据为空,请确认后重新上传!"));
            }
        } catch (Exception var11) {
            var11.printStackTrace();
            logger.info("导入错误==>" + var11.getMessage());
            this.Write(returnPars(-1, null, "解析文件异常,请联系管理员核查!"));
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/12/6 15:41
     * @Description 保存支撑人员信息
     **/
    public void addStaff() {
        try {
            String json = getString("json");  //成员明细
            if (!json.equals("")) {
                JSONArray jsonArray = JSONArray.fromObject(json);
                for (Object o : jsonArray) {
                    JSONObject jsonObject = JSONObject.fromObject(o);
                    StaffManagement sta = new StaffManagement();
                    sta.setUserName(jsonObject.getString("userName"));
                    sta.setBossNo(jsonObject.getString("bossNo"));
                    sta.setCity(jsonObject.getString("city"));
                    sta.setExpirationDate(jsonObject.getString("expirationDate"));
                    sta.setState("0");
                    sta.setCreateDate(new Date());
                    sta.setUpdateDate(new Date());
                    preinvApplyService.saveOrUpdateStaff(sta);
                }
                Write(returnPars(1, null, "操作成功!"));
            }
        } catch (Exception e) {
            Write(returnPars(-1, null, "操作失败！"));
            logger.error("保存支撑人员信息错误==>" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/12/6 16:17
     * @Description 根据id删除支撑人员信息
     **/
    public void deleteStaff() {
        try {
            preinvApplyService.deleteStaff(getString("id"));
            Write("YES");
        } catch (NumberFormatException e) {
            Write("NO");
            logger.error("删除支撑人员信息错误:" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/12/7 15:45
     * @Description 查询当前登录是否为支撑人员
     **/
    public void getStaff() {
        StaffManagement staff = preinvApplyService.getStaff(user.getBossUserName());
        if (staff == null) {
            Write("N");
        } else {
            Write("Y");
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/12/7 15:46
     * @Description 根据boss工号查询用户信息
     **/
    public void getUser() {
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(preinvApplyService.getUser(getString("bossNo"))));
//        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(preinvApplyService.getUser("ba8010000")));
    }

    /*
     * <AUTHOR>
     * @Date 2024/1/3 15:37
     * @Description 提交退回人
     **/
    public void submitTheReturn() {
        try {
            String id = getString("id");// 开票id
            String pid = getString("processId");// 流程id
            String userid = getString("userId");// 用户id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String taskId = getString("preinvApplyTaskid");// 任务id
            String attachmentId = getString("attachmentId");// 补充附件id

            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            //修改上一步任务信息
            PreinvApplyTask ptask = preinvApplyService.getTaskList(taskId);// 根据任务id查询开票任务表信息
            if (ptask != null) {
                ptask.setDealDate(new Date());// 操作时间
                ptask.setState("1");// 修改状态为处理完成
                preinvApplyService.updatePreinvApplyTask(ptask);// 修改任务表信息
            }

            PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
            WaitTask wt = service.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
            // 结束当前待办
            if (wt != null) {
                System.out.println("================处理中开始代办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息

            PreinvApplyFlow flow = preinvApplyService.getPreinvApplyFlow(pid);// 根据流程id查询流程表信息
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            // 修改流程表信息
            flow.setDealName(USER.getEmployeeName());// 处理人名称
            flow.setDealNo(USER.getRowNo() + "");// 处理人编号
            flow.setUpdateDate(new Date());// 更新时间
            flow.setState("0");// 状态为审批中
            preinvApplyService.updatePreinvApplyFlow(flow);// 修改流程表信息

            // 新增本次操作任务表信息
            PreinvApplyTask pat = new PreinvApplyTask();
            pat.setFlowId(pid);// 流程id
            pat.setTaskId(tasktwo.getId());// 环节id
            pat.setTaskName("客户经理");// 环节名称
            pat.setCreatorNO(USER.getRowNo() + "");// 创建人编号
            pat.setCreatorName(USER.getEmployeeName());// 创建人名称
            pat.setCreateDate(new Date());// 创建时间
            pat.setDealNo(user.getRowNo() + "");// 处理人编号
            pat.setDealName(user.getEmployeeName());// 处理人名称
            pat.setState("1");// 状态
            pat.setDealDate(new Date());// 操作时间
            pat.setTaskMemo(opinion);// 处理意见
            preinvApplyService.addPreinvApplyTask(pat);// 保存信息到任务表

            // 新增下一步任务表信息
            PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
            preinvApplyTask.setFlowId(pid);// 流程id
            preinvApplyTask.setTaskId(tasktwo.getId());// 环节id
            preinvApplyTask.setTaskName(task.getActivityName());// 环节名称
            preinvApplyTask.setCreatorNO(user.getRowNo() + "");// 创建人编号
            preinvApplyTask.setCreatorName(user.getEmployeeName());// 创建人名称
            preinvApplyTask.setCreateDate(new Date());// 创建时间
            preinvApplyTask.setDealNo(USER.getRowNo() + "");// 处理人编号
            preinvApplyTask.setDealName(USER.getEmployeeName());// 处理人名称
            preinvApplyTask.setState("0");// 状态
            preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表

            if (!StringUtils.isEmpty(attachmentId)) {
                // 判断是否上传了附件，获取前台提交的附件Id；
                String[] json = attachmentId.split(",");
                if (json.length > 0) {
                    Date date = new Date();
                    for (String value : json) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(preinvApply.getUuid());
                        sa.setAttachmentId(value);
                        sa.setLink(PreinvApply.PREINVAPPLY);
                        sa.setCreateDate(date);
                        preinvApplyService.saveSandA(sa);
                    }
                }
            }

            preinvApply.setStartState("0");
            preinvApplyService.updatePreinvApply(preinvApply);
            commitBackLog(preinvApply, userid, pid, null, user, preinvApplyTask);// 生成待办
            Write("YES");
        } catch (Exception e) {
            logger.error("预开票提交退回人操作失败错误信息==>" + e.getMessage(), e);
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    public void getContractOrPhoneNo() {
        JSONObject writeJson = new JSONObject();
        try {
            String contractNo = getString("contractNo");//账户
            String phoneNo = getString("phoneNo");//手机号码
            Result result = preinvApplyService.getContractOrPhoneNo(contractNo,phoneNo);
            if (result.getCode() == 200) {
                JSONObject json = JSONObject.fromObject(result.getData());
                JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
                if ("0".equals(root.getString("RETURN_CODE"))) {
                    if(root.has("OUT_DATA")){
                        JSONObject jsonObject = root.getJSONObject("OUT_DATA");
                        if(jsonObject.has("LIST")){
                            JSONArray list = jsonObject.getJSONArray("LIST");
                            if(list.size()>0){
                                JSONObject sumnumObj = (JSONObject) list.get(0);
                                if(sumnumObj!=null&&sumnumObj.has("SUMNUM")){
                                    Integer sumnum =sumnumObj.getInt("SUMNUM");
                                    if(sumnum>0){
                                        writeJson.put("code",200);
                                        writeJson.put("data",String.valueOf(sumnum));
                                        writeJson.put("msg","查询成功");
                                        Write(writeJson.toString());
                                    }else{
                                        writeJson.put("code",200);
                                        writeJson.put("data",0);
                                        writeJson.put("msg","查询成功");
                                        Write(writeJson.toString());
                                    }
                                }else{
                                    writeJson.put("code",1001);
                                    writeJson.put("data",0);
                                    writeJson.put("msg","未查询到SUMNUM节点");
                                    Write(writeJson.toString());
                                }
                            }else{
                                writeJson.put("code",1001);
                                writeJson.put("data",0);
                                writeJson.put("msg","LIST节点无值");
                                Write(writeJson.toString());
                            }
                        }else{
                            writeJson.put("code",1001);
                            writeJson.put("data",0);
                            writeJson.put("msg","未查询到LIST节点");
                            Write(writeJson.toString());
                        }
                    }else{
                        writeJson.put("code",1001);
                        writeJson.put("data",0);
                        writeJson.put("msg","未查询到OUT_DATA节点");
                        Write(writeJson.toString());
                    }
                } else {
                    writeJson.put("code",1001);
                    writeJson.put("data",null);
                    writeJson.put("msg",root.getString("RETURN_MSG"));
                    Write(writeJson.toString());
                }
            }else{
                writeJson.put("code",1001);
                writeJson.put("data",null);
                writeJson.put("msg",result.getMessage());
                Write(writeJson.toString());
            }
        } catch (Exception e) {
            writeJson.put("code",1001);
            writeJson.put("data",null);
            writeJson.put("msg",e.getMessage());
            Write(writeJson.toString());
        }
    }
}