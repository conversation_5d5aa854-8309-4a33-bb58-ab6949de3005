package com.xinxinsoft.action.dedicatedFlow;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.Set;

import javax.servlet.ServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.ProcessInstance;
import org.jbpm.api.task.Task;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sun.star.uno.RuntimeException;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.basetype.BusinessType;
import com.xinxinsoft.entity.basetype.ProductType;
import com.xinxinsoft.entity.boss.RESULT_DATA;
import com.xinxinsoft.entity.boss.StartPreOrderOut;
import com.xinxinsoft.entity.commonSingManagement.OrderForm;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.dedicatedFlow.BossTache;
import com.xinxinsoft.entity.dedicatedFlow.ImsChange;
import com.xinxinsoft.entity.dedicatedFlow.ImsTranslate;
import com.xinxinsoft.entity.dedicatedFlow.InternetChange;
import com.xinxinsoft.entity.dedicatedFlow.InternetThings;
import com.xinxinsoft.entity.dedicatedFlow.Opinion;
import com.xinxinsoft.entity.dedicatedFlow.OrderInformation;
import com.xinxinsoft.entity.dedicatedFlow.OrderStages;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.messages.MessagesXml;
import com.xinxinsoft.entity.order.OrderDetail;
import com.xinxinsoft.entity.ordertask.OrderTask;
import com.xinxinsoft.entity.processLink.LinkTemplate;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.sendComms.Proc4AOracle;
import com.xinxinsoft.service.commonSingManagement.CommonSingleService;
import com.xinxinsoft.service.core.DictionaryService;
import com.xinxinsoft.service.core.processService.ProcessService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.dedicatedFlow.DedicatedFlowService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.groupcustomer.GroupCustomerService;
import com.xinxinsoft.service.processLink.LinkTemplateService;
import com.xinxinsoft.service.smsPush.SmsPushService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.task.EipUserTask;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.UUIDUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

/***
 * 
 * <AUTHOR> 
 */
public class DedicatedFlowAction extends BaseAction {

	private static final Logger logger = Logger.getLogger(DedicatedFlowAction.class);
	private JbpmUtil jbpmUtil;
	private SystemUserService systemUserService;

	private static final long serialVersionUID = -9163405622027931242L;
	private DedicatedFlowService dedicatedFlowService;
	private AttachmentService attachmentService; // 附件：
	private WaitTaskService taskService;
	private List<String> pCode;
	private List<String> userId;
	private List<String> userName;
	private CMCCOpenService cmccOpenService;
	private CommonSingleService commonSingleService;
	private InternetThings things;
	private InternetChange change;
	private ImsChange imsChange;
	private ImsTranslate translate;
	private GroupCustomerService groupCustomerService;

	private LinkTemplateService lateService;

	private ProcessService processService;
	
	private  ResourceBundle s = ResourceBundle .getBundle("WebService-config");
	private String audit = s.getString("AUDIT_INTERS_ORDER_SWITCH");

	public LinkTemplateService getLateService() {
		return lateService;
	}

	public void setLateService(LinkTemplateService lateService) {
		this.lateService = lateService;
	}

	public ProcessService getProcessService() {
		return processService;
	}

	private SmsPushService smsPushService;// 短信SERVICE

	public void setProcessService(ProcessService processService) {
		this.processService = processService;
	}

	public SmsPushService getSmsPushService() {
		return smsPushService;
	}

	public void setSmsPushService(SmsPushService smsPushService) {
		this.smsPushService = smsPushService;
	}

	public GroupCustomerService getGroupCustomerService() {
		return groupCustomerService;
	}

	public void setGroupCustomerService(
			GroupCustomerService groupCustomerService) {
		this.groupCustomerService = groupCustomerService;
	}

	public InternetThings getThings() {
		return things;
	}

	public void setThings(InternetThings things) {
		this.things = things;
	}

	public InternetChange getChange() {
		return change;
	}

	public void setChange(InternetChange change) {
		this.change = change;
	}

	public ImsChange getImsChange() {
		return imsChange;
	}

	public void setImsChange(ImsChange imsChange) {
		this.imsChange = imsChange;
	}

	public ImsTranslate getTranslate() {
		return translate;
	}

	public void setTranslate(ImsTranslate translate) {
		this.translate = translate;
	}

	public CommonSingleService getCommonSingleService() {
		return commonSingleService;
	}

	public void setCommonSingleService(CommonSingleService commonSingleService) {
		this.commonSingleService = commonSingleService;
	}

	public CMCCOpenService getCmccOpenService() {
		return cmccOpenService;
	}

	public void setCmccOpenService(CMCCOpenService cmccOpenService) {
		this.cmccOpenService = cmccOpenService;
	}

	public List<String> getpCode() {
		return pCode;
	}

	public void setpCode(List<String> pCode) {
		this.pCode = pCode;
	}

	public List<String> getUserId() {
		return userId;
	}

	public void setUserId(List<String> userId) {
		this.userId = userId;
	}

	public List<String> getUserName() {
		return userName;
	}

	public void setUserName(List<String> userName) {
		this.userName = userName;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public WaitTaskService getTaskService() {
		return taskService;
	}

	public void setTaskService(WaitTaskService taskService) {
		this.taskService = taskService;
	}

	private DictionaryService dictionaryService;

	public DictionaryService getDictionaryService() {
		return dictionaryService;
	}

	public void setDictionaryService(DictionaryService dictionaryService) {
		this.dictionaryService = dictionaryService;
	}

	public AttachmentService getAttachmentService() {
		return attachmentService;
	}

	public void setAttachmentService(AttachmentService attachmentService) {
		this.attachmentService = attachmentService;
	}

	private OrderForm order;

	public OrderForm getOrder() {
		return order;
	}

	public void setOrder(OrderForm order) {
		this.order = order;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}

	public DedicatedFlowService getDedicatedFlowService() {
		return dedicatedFlowService;
	}

	public void setDedicatedFlowService(
			DedicatedFlowService dedicatedFlowService) {
		this.dedicatedFlowService = dedicatedFlowService;
	}

	public JbpmUtil getJbpmUtil() {
		return jbpmUtil;
	}

	public void setJbpmUtil(JbpmUtil jbpmUtil) {
		this.jbpmUtil = jbpmUtil;
	}

	/**
	 * 新增：
	 */
	public void saveOrder() {
		try {
			String zproductidp = getString("zproductid");//
			String titleText = getString("titleText");
			String starstate = getString("starstate"); 
			String id = getString("id");
			OrderTask orderTask = dedicatedFlowService.getOrderTask(id);
			String[] XMLid = null;
			String[] titleTexttwo = null; 
			if(zproductidp!=null){
				XMLid = zproductidp.split(",");
			}
			if(titleText!=null){
				titleTexttwo = titleText.split(",");
			}
			//String DemandName = order.getDemandName();
			String DemandName = getString("order.demandName");
			if(XMLid!=null&& starstate!=null && XMLid.length>1 && starstate.equals("1")){
				String waitId = getString("waitId");
				for(int k=0;k<XMLid.length;k++){
					OrderForm of = null;
					String taskIdentp = getString("taskIdent");//
					OrderForm ordertwo = new OrderForm();
					String ord_id = getString("ord_id");
					String zpCodep = getString("zpCode");//
					String orderReqDescription=getString("orderReqDescription");
					String orderReqDescriptionHTML=getString("orderReqDescriptionHTML");
					/*if ((zpCodep) != null) {
						if (zproductidp == null || "" == zproductidp
								|| "undefined".equals(zproductidp)
								|| "null".equals(zproductidp)) {
							return;
						}
					}*/
					String quxian="";
					String fengongsi="";
					String bumen="";
					List<Object[]> sone=dedicatedFlowService.getSystemDept(user.getRowNo());
					for(int i=0;i<sone.size();i++){
						quxian = (String)sone.get(i)[0];
						fengongsi=(String)sone.get(i)[1];
						bumen=(String)sone.get(i)[2];
					}
					if ("1".equals(taskIdentp)) {
						String sidp = getString("sid");
						WaitTask wait = taskService.queryWaitTaskIdAndCode(
								WaitTask.DEDICATED_LINE, sidp);
						if (wait == null) {
							return;
						}
						taskService.updateWait(wait,this.getRequest());
						of = dedicatedFlowService.queryId(sidp);
						OrderForm order = dedicatedFlowService.queryId(sidp);
							order.setState("-2");
							order.setUpdateTime(new Date());
							order.setOrderCompletionTime(new Date());
							dedicatedFlowService.saveOrUpdateOrder(order);//上面是正式订单，下面是需求单
					OrderForm orderX = dedicatedFlowService.queryId(order.getParentOrderNumber());
							orderX.setState("-2");
							orderX.setUpdateTime(new Date());
							orderX.setOrderCompletionTime(new Date());
						dedicatedFlowService.saveOrUpdateOrder(orderX);
						OrderDetail od =dedicatedFlowService.getOrderDetail(order.getParentOrderNumber(),zpCodep);
						if(od!=null){
							String uuid =UUIDUtil.getInstance().generatUUID(false);
									od.setOdetailId(uuid);
									od.setOrderId(null);
							dedicatedFlowService.saveObj(od);
							zproductidp=od.getOdetailId();
						}
					}
					String attachmentId = getString("attachmentId");
					if (order != null) {
			
						ordertwo.setDraftTime(new Date());
						ordertwo.setState("0");
						if ((zpCodep) != null) {
							ordertwo.setZpcode(zpCodep.trim());
						}
						ordertwo.setDraftmanId(user.getRowNo() + "");
						ordertwo.setDraftman(user.getEmployeeName());
						ordertwo.setSubmissionMode("W");
						ordertwo.setType("DI");
						ordertwo.setTransmitState("1");
						// 查不到时：是根据区域首写字母和产品类型：
					/*	if ("error".equals(commonSingleService.returnCode("000", null,
								user, false, "0"))) {
							this.Write("error");
						}
						// 根据pCode 查询产品类型
			
						// 订单编码生成：
						order.setOrderNumber(commonSingleService.returnCode("000", null,
								user, false, "0"));*/
						for (int j = 0; j < pCode.size(); j++) {
							ProductType prdt = dedicatedFlowService.queryProductType(pCode.get(j));
							// 查不到时：是根据区域首写字母和产品类型：
							if ("error".equals(commonSingleService.returnCode( pCode.get(j), prdt.getProRowNum(), user, false,"1"))) {
								this.Write("error");
							}
							// 根据pCode 查询产品类型
							// 订单编码生成：
							ordertwo.setOrderNumber(commonSingleService.returnCode(pCode.get(j), prdt.getProRowNum(), user, false,"1"));
						}
						
						
						ordertwo.setOrderTypeIdent(0);
						String orderId = getString("orderId");
						System.out.println(k);
						if((orderId!=null || !"".equals(orderId)) && k>0){
							orderId=null;
						}
						
						if(orderId==null || orderId.equals("")){
							ordertwo.setSystemDeptID(bumen);
							ordertwo.setPrantsystemDeptID(quxian);
							ordertwo.setSystemCompanyID(fengongsi);
							ordertwo.setRemarks(order.getRemarks());
							ordertwo.setOrderReqTimeLimit(order.getOrderReqTimeLimit());
							ordertwo.setDemandName(DemandName+"-"+titleTexttwo[k]);
							ordertwo.setOperationType(order.getOperationType());
							ordertwo.setGroupCustomerId(order.getGroupCustomerId());
							ordertwo.setOrderReqDescription(orderReqDescription);
							order = dedicatedFlowService.saveOrder(ordertwo);
							
						}else{
							//更新正式订单
							String ortitle=order.getDemandName()+"-"+titleTexttwo[k];
							//String orderReqDescription = order.getOrderReqDescription();
							String remarks = order.getRemarks();
							Date orderReqTimeLimit = order.getOrderReqTimeLimit();
							String groupCustomerId = order.getGroupCustomerId();
							String operationType = order.getOperationType();
							order = dedicatedFlowService.copy(orderId);
							order.setTransmitState("1");
							order.setDemandName(ortitle);
							order.setOrderReqDescription(orderReqDescription);
							order.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
							order.setRemarks(remarks);
							order.setOrderReqTimeLimit(orderReqTimeLimit);
							order.setGroupCustomerId(groupCustomerId);
							order.setOperationType(operationType);
							for (int i = 0; i < pCode.size(); i++) {
								order.setpCode(pCode.get(i));
							}
							order.setZpcode(zpCodep);
							dedicatedFlowService.saveOrUpdateOrder(order);
							//更新草稿单
							order = dedicatedFlowService.copy(order.getParentOrderNumber());
							order.setTransmitState("1");
							order.setDemandName(ortitle);
							order.setOrderReqDescription(orderReqDescription);
							order.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
							order.setRemarks(remarks);
							order.setOrderReqTimeLimit(orderReqTimeLimit);
							order.setGroupCustomerId(groupCustomerId);
							order.setOperationType(operationType);
							for (int s = 0; s < pCode.size(); s++) {
								order.setpCode(pCode.get(s));
							}
							order.setZpcode(zpCodep);
							dedicatedFlowService.saveOrUpdateOrder(order);
							//order = dedicatedFlowService.copyone(order.getOrderId());
						}
						
						if("start".equals(audit)){
							 String request= DateUtil.getIpAddr(this.getRequest());
						
									///审计接口调用
									CommLogs.requOrderquery(user.getLoginName(), user.getEmployeeName(), "0", order.getDemandName(), order.getOrderNumber(), "", "", "", "", "4", request);
							
						}
						
						if ((XMLid[k]) != null) {
							dedicatedFlowService.updateZPCode(order.getOrderId(),
									XMLid[k]);
						}
						if (of != null) {
							List<Map<String, String>> lists = dedicatedFlowService.fuJian(of.getParentOrderNumber());
							for (int j = 0; j < lists.size(); j++) {
								Map<String, String> mapStr = lists.get(j);
								SingleAndAttachment sa = new SingleAndAttachment();
								sa.setOrderID(order.getOrderId());
								sa.setAttachmentId(String.valueOf(mapStr.get("id")));
								sa.setLink(Opinion.DEMAND_APPLICATION);
								dedicatedFlowService.saveSandA(sa);
							}
							of.setState("-2");
							of.setOrderCompletionTime(new Date());
							of.setUpdateTime(new Date());
							dedicatedFlowService.saveOrUpdateOrder(of);
						}
						/*
						 * //添加附件 List<SingleAndAttachment> saa=new
						 * ArrayList<SingleAndAttachment>(); if
						 * (!StringUtils.isEmpty(attachmentId)) { if (attachmentId != null)
						 * { // 判断是否上传了附件，获取前台提交的附件Id； String[] json =
						 * attachmentId.split(","); if (json.length > 0) { for (int i = 0; i
						 * < json.length; i++) { atts.add(attachmentService
						 * .getAttachmentById(json[i])); // 得到 每个对象中的属性值 } } } } // 添加附件： if
						 * (saa.size() > 0) { order.setAttachment(atts); }
						 */
						//添加附件
							if (!StringUtils.isEmpty(attachmentId)) {
								if (attachmentId != null) {
									// 判断是否上传了附件，获取前台提交的附件Id；
									String[] json = attachmentId.split(",");
									if (json.length > 0) {
										for (int i = 0; i < json.length; i++) {
											SingleAndAttachment sa = new SingleAndAttachment();
											sa.setOrderID(order.getOrderId());
											sa.setAttachmentId(json[i]);
											sa.setLink(Opinion.DEMAND_APPLICATION);
											dedicatedFlowService.saveSandA(sa);
											// 得到 每个对象中的属性值
										}
									}
								}
							}
						
			
						// 循环添加待办
						for (int j = 0; j < pCode.size(); j++) {
							//添加数据开始*********************************************************************************
							//OrderForm orderForm = new OrderForm();
							OrderForm orderForm = new OrderForm();
							if(dedicatedFlowService.copy(orderId)!=null){
								//order = dedicatedFlowService.copy(orderId);
								System.out.println(order.getOrderId());
								orderForm=dedicatedFlowService.copyone(order.getOrderId());
								orderForm.setUserId(Integer.parseInt(userId.get(j)));
								orderForm.setUserName(userName.get(j));
								orderForm.setpCode(pCode.get(j));
								//根据产品编码 查询 业务编码并且保存
								BusinessType bt = dedicatedFlowService.queryBcode(pCode.get(j));
								orderForm.setbCode(bt.getbCode());
								orderForm.setOrderTitle(order.getOrderTitle());
								orderForm.setDemandName(order.getDemandName());
								orderForm.setParentOrderNumber(order.getOrderId());
								orderForm.setDraftTime(new Date());
								orderForm.setState("0");
								orderForm.setDraftmanId(user.getRowNo() + "");
								orderForm.setDraftman(user.getEmployeeName());
								orderForm.setSubmissionMode("W");
								orderForm.setType("DI");
								orderForm.setTransmitState(order.getTransmitState());
								orderForm.setOperationType(order.getOperationType());
								orderForm.setGroupCustomerId(order.getGroupCustomerId());
								orderForm.setOrderReqDescription(orderReqDescription);
								orderForm.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
								orderForm.setOrderReqTimeLimit(order.getOrderReqTimeLimit());
								orderForm.setSignedStatus(order.getSignedStatus());
								orderForm.setRemarks(order.getRemarks());
								orderForm.setOrderTypeIdent(1);
								orderForm.setZpcode(order.getZpcode());
								orderForm.setOrderNumber(order.getOrderNumber());
								orderForm.setSystemDeptID(bumen);
								orderForm.setPrantsystemDeptID(quxian);
								orderForm.setSystemCompanyID(fengongsi);
								dedicatedFlowService.saveOrUpdateOrder(orderForm);
							}else{
								orderForm.setUserId(Integer.parseInt(userId.get(j)));
								orderForm.setUserName(userName.get(j));
								orderForm.setpCode(pCode.get(j));
								//根据产品编码 查询 业务编码并且保存
								BusinessType bt = dedicatedFlowService.queryBcode(pCode.get(j));
								orderForm.setbCode(bt.getbCode());
								orderForm.setOrderTitle(order.getOrderTitle());
								orderForm.setDemandName(order.getDemandName());
								orderForm.setParentOrderNumber(order.getOrderId());
								orderForm.setDraftTime(new Date());
								orderForm.setState("0");
								orderForm.setDraftmanId(user.getRowNo() + "");
								orderForm.setDraftman(user.getEmployeeName());
								orderForm.setSubmissionMode("W");
								orderForm.setType("DI");
								orderForm.setTransmitState(order.getTransmitState());
								orderForm.setOperationType(order.getOperationType());
								orderForm.setGroupCustomerId(order.getGroupCustomerId());
								orderForm.setOrderReqDescription(orderReqDescription);
								orderForm.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
								orderForm.setOrderReqTimeLimit(order.getOrderReqTimeLimit());
								orderForm.setSignedStatus(order.getSignedStatus());
								orderForm.setRemarks(order.getRemarks());
								orderForm.setOrderTypeIdent(1);
								orderForm.setZpcode(order.getZpcode());
								orderForm.setOrderNumber(order.getOrderNumber());
								orderForm.setSystemDeptID(bumen);
								orderForm.setPrantsystemDeptID(quxian);
								orderForm.setSystemCompanyID(fengongsi);
								dedicatedFlowService.saveOrUpdateOrder(orderForm);
								if(orderTask!=null){
									//dsa
									dedicatedFlowService.saveBus_order(orderTask.getBusCode(),order.getOrderNumber());
								}
								
							}
							//添加数据结束**********************************************************************************
							/*
							 * h // 添加附件： if (atts.size() > 0) {
							 * orderForm.setAttachment(atts); }
							 */
			
							if(!"".equals(ord_id) && ord_id!=null&& !"null".equals(ord_id)){
								/*OrderFormOrOrdertask orderFormOrOrdertask = new OrderFormOrOrdertask();
								orderFormOrOrdertask.setOrderTaskID(ord_id);
								orderFormOrOrdertask.setOrderFormID(order.getOrderNumber());*/
								OrderTask ordertask = dedicatedFlowService.queryOrderTask(ord_id);
								if(ordertask!=null)
								dedicatedFlowService.setOrderFormOrOrdertask(ordertask.getBusCode(),order.getOrderNumber());
								//dedicatedFlowService.setOrderFormOrOrdertask(orderFormOrOrdertask);
								WaitTask wait = taskService.queryWaitId(WaitTask.ORDER_TASK, waitId);
								taskService.updateWait(wait,this.getRequest());
							}
							
							
							
								Map<String, String> map = new HashMap<String, String>();
								map.put("customerowner", user.getRowNo() + "");//
								map.put("odermanager", orderForm.getUserId() + "");// 设置下一步处理的人
								ProcessInstance processInstance = jbpmUtil.startPIByKey(
										"orderinformationprocess", map);
								List<Task> tastList = jbpmUtil.findPersonalTasks(user
										.getRowNo() + "");
								String taskId = "";
								for (Task task : tastList) {
									if (processInstance.getId().equals(
											task.getExecutionId())) {
										taskId = task.getId();
										break;
									}
								}
								if (taskId != "") {
			
									/*
									 * Map<String, String> map1 = new HashMap<String,
									 * String>(); map1.put("eid", orderForm.getOrderId());//
									 * 设置资源Id map1.put("ename", order.getOrderTitle());//
									 * 设置资源Id map1.put("ecreationTime", sdf.format(new
									 * Date()));// 创建时间 map1.put("ecreateUserName",
									 * user.getEmployeeName());// 创建人命 map1.put("eurl",
									 * "jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
									 * + order.getOrderId());
									 */
									jbpmUtil.completeTask(taskId, map);
									// 待办
									List<Task> tastList2 = jbpmUtil
											.findPersonalTasks(orderForm.getUserId() + "");
			
									String taskId2 = "";
									for (Task task : tastList2) {
										if (processInstance.getId().equals(
												task.getExecutionId())) {
											taskId2 = task.getId();
											break;
										}
									}
									Set<String> s = jbpmUtil.findOutComesByTaskId(taskId2);
			
									String orderStageId = null;
									// 新增工作台数据
									// 查询二级环节
									String pCode = orderForm.getpCode();
			
									// 根据产品类型id 环节操作类型 查询 第二级环节id
			
									// List<LinkTemplate> pt =
									// dedicatedFlowService.querylink(pCode,
									// orderForm.getOperationType());
									List<LinkTemplate> pt = dedicatedFlowService
											.queryProcFlow(pCode,
													orderForm.getOperationType());
									for (LinkTemplate l : pt) {
										OrderStages ss = new OrderStages();
										ss.setOrderId(orderForm.getOrderId());
										ss.setTwoStageId(l.getLinkTempCode());
										// 根据模板需求时限：// 预计完成时限 //WF 2016-11-26 13:31 修改：//
										if (!StringUtils.isEmpty(l.getLinkNeedLimit())) {
											ss.setExpectedCompletionTime(DateUtil
													.getDateAddHour(Integer.parseInt(l
															.getLinkNeedLimit())));
										}
										SystemUser user2 = systemUserService
												.getUserInfoRowNo(orderForm.getUserId());
										if ("1".equals(l.getBossTrackName())) {
											ss.setIsBossValue("1");
										}
										if ("需求申请".equals(l.getLinkName())) {
											if ("1".equals(l.getBossTrackName())) {
												ss.setStageState("-1");
												dedicatedFlowService.saveOrderStages(ss);
												orderStageId = l.getLinkTempCode();
											} else {
												ss.setStageState("1");
												ss.setActionUser(user2.getEmployeeName());
												ss.setActionUserPhone(user2.getMobile());
												ss.setOperTime(DateUtil.getDateone());
												dedicatedFlowService.saveOrderStages(ss);
												orderStageId = l.getLinkTempCode();
											}
										} else {
											ss.setStageState("-1");
											dedicatedFlowService.saveOrderStages(ss);
			
										}
			
									}
									WaitTask wt = new WaitTask();
									wt.setName(orderForm.getDemandName());
									wt.setCreationTime(new Date());
									// 操作类型 OTChange_2：变更 OTOpen_1：开通
									String TYPE = order.getOperationType();
									// pCode 产品编号 ims:IMS 物联网：WLW
			
									for (String str : s) {
										if("YSX".equals(pCode)){
											if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
												SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
												wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
											}else{
												wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
											}
											
										}else if ("IMS".equals(pCode)) {
											if ("OTChange_2".equals(TYPE)) {
												// ims 变更
												wt.setUrl("jsp/dedicatedFlow/demand/imsChange.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
			
											} else if ("OTOpen_1".equals(TYPE)||"OTXIH_1".equals(TYPE)||"OTSTOPJF_1".equals(TYPE)) {
												if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
													SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
													wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/demand/imsTranslate.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
												}else{
												// ims 开通
												wt.setUrl("jsp/dedicatedFlow/demand/imsTranslate.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
												}
											} else {
												wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
			
											}
										} else if ("WLW".equals(pCode)) {
											if ("OTChange_2".equals(TYPE)) {  
												// 物联网 变更
												wt.setUrl("jsp/dedicatedFlow/demand/internetChange.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
											} else if ("OTOpen_1".equals(TYPE)||"OTXIH_1".equals(TYPE)||"OTSTOPJF_1".equals(TYPE)) {
												if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
													SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
													wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/demand/internetThings.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
													
												} else {
												// 物联网 开通
												wt.setUrl("jsp/dedicatedFlow/demand/internetThings.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
												}
											} else {
												wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
			
											}
										} else {
											if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
												SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
												wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
												
											} else {
												wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
														+ orderForm.getOrderId()
														+ "&taskId="
														+ taskId2
														+ "&NextTask="
														+ str
														+ "&orderStageId=" + orderStageId);
			
											}
										}
			
									}
			
									wt.setState(WaitTask.HANDLE);
									wt.setHandleUserId(orderForm.getUserId());
									wt.setHandleUserName(orderForm.getUserName());
									SystemUser user1 = systemUserService
											.getUserInfoRowNo(orderForm.getUserId());
									wt.setHandleLoginName(user1.getLoginName());
									wt.setCreateUserId(user.getRowNo());
									wt.setCreateUserName(user.getEmployeeName());
									wt.setCreateLoginName(user.getLoginName());
									wt.setCode(WaitTask.DEDICATED_LINE);
									wt.setEndTime(orderForm.getOrderReqTimeLimit());
									wt.setTaskId(orderForm.getOrderId());
									taskService.saveWait(wt,this.getRequest());
								} else {
									this.Write("0");
								}				
						}
						
					}
				}
				if(!"".equals(waitId) && waitId!=null  && !"null".equals(waitId) && !"undefined".equals(waitId)){
					WaitTask wait = taskService.queryWaitId(WaitTask.ORDER_TASK, waitId);
					if(wait!=null){
						taskService.updateWait(wait,this.getRequest());
					}
				}
				this.Write("1");
		}else{
			
			OrderForm of = null;
			String taskIdentp = getString("taskIdent");//
			if(XMLid!=null){
			zproductidp = XMLid[0];
			}
			String ord_id = getString("ord_id");
			String zpCodep = getString("zpCode");//
			String waitId = getString("waitId");
			String orderReqDescription=getString("orderReqDescription");
			String orderReqDescriptionHTML=getString("orderReqDescriptionHTML");
			/*if ((zpCodep) != null) {
				if (zproductidp == null || "" == zproductidp
						|| "undefined".equals(zproductidp)
						|| "null".equals(zproductidp)) {
					return;
				}
			}*/
			String quxian="";
			String fengongsi="";
			String bumen="";
			List<Object[]> sone=dedicatedFlowService.getSystemDept(user.getRowNo());
			for(int i=0;i<sone.size();i++){
				quxian = (String)sone.get(i)[0];
				fengongsi=(String)sone.get(i)[1];  
				bumen=(String)sone.get(i)[2];
			}
			if ("1".equals(taskIdentp)) {
				String sidp = getString("sid");
				WaitTask wait = taskService.queryWaitTaskIdAndCode(
						WaitTask.DEDICATED_LINE, sidp);
				if (wait == null) {
					return;
				}
				taskService.updateWait(wait,this.getRequest());
				of = dedicatedFlowService.queryId(sidp);
				OrderForm order = dedicatedFlowService.queryId(sidp);
					order.setState("-2");
					order.setUpdateTime(new Date());
					order.setOrderCompletionTime(new Date());
					dedicatedFlowService.saveOrUpdateOrder(order);//上面是正式订单，下面是需求单
			OrderForm orderX = dedicatedFlowService.queryId(order.getParentOrderNumber());
					orderX.setState("-2");
					orderX.setUpdateTime(new Date());
					orderX.setOrderCompletionTime(new Date());
				dedicatedFlowService.saveOrUpdateOrder(orderX);
				OrderDetail od =dedicatedFlowService.getOrderDetail(order.getParentOrderNumber(),zpCodep);
				if(od!=null){
					String uuid =UUIDUtil.getInstance().generatUUID(false);
							od.setOdetailId(uuid);
							od.setOrderId(null);
					dedicatedFlowService.saveObj(od);
					zproductidp=od.getOdetailId();
				}
			}
			String attachmentId = getString("attachmentId");
			if (order != null) {
	
				order.setDraftTime(new Date());
				order.setState("0");
				if ((zpCodep) != null) {
					order.setZpcode(zpCodep.trim());
				}
				order.setDraftmanId(user.getRowNo() + "");
				order.setDraftman(user.getEmployeeName());
				order.setSubmissionMode("W");
				order.setType("DI");
				order.setTransmitState("1");
				// 查不到时：是根据区域首写字母和产品类型：
			/*	if ("error".equals(commonSingleService.returnCode("000", null,
						user, false, "0"))) {
					this.Write("error");
				}
				// 根据pCode 查询产品类型
	
				// 订单编码生成：
				order.setOrderNumber(commonSingleService.returnCode("000", null,
						user, false, "0"));*/
				for (int j = 0; j < pCode.size(); j++) {
					ProductType prdt = dedicatedFlowService.queryProductType(pCode.get(j));
					// 查不到时：是根据区域首写字母和产品类型：
					if ("error".equals(commonSingleService.returnCode( pCode.get(j), prdt.getProRowNum(), user, false,"1"))) {
						this.Write("error");
					}
					// 根据pCode 查询产品类型
					// 订单编码生成：
					order.setOrderNumber(commonSingleService.returnCode(pCode.get(j), prdt.getProRowNum(), user, false,"1"));
				}
				
				
				order.setOrderTypeIdent(0);
				String orderId = getString("orderId");
				if(orderId==null || orderId.equals("")){
					order.setSystemDeptID(bumen);
					order.setPrantsystemDeptID(quxian);
					order.setSystemCompanyID(fengongsi);
					order.setOrderReqDescription(orderReqDescription);
					order = dedicatedFlowService.saveOrUpdateOrder(order);
					
				}else{
					//更新正式订单
					String ortitle=order.getDemandName();
					//String orderReqDescription = order.getOrderReqDescription();
					String remarks = order.getRemarks();
					Date orderReqTimeLimit = order.getOrderReqTimeLimit();
					String groupCustomerId = order.getGroupCustomerId();
					String operationType = order.getOperationType();
					order = dedicatedFlowService.copy(orderId);
					order.setTransmitState("1");
					order.setDemandName(ortitle);
					order.setOrderReqDescription(orderReqDescription);
					order.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
					order.setRemarks(remarks);
					order.setOrderReqTimeLimit(orderReqTimeLimit);
					order.setGroupCustomerId(groupCustomerId);
					order.setOperationType(operationType);
					for (int i = 0; i < pCode.size(); i++) {
						order.setpCode(pCode.get(i));
					}
					order.setZpcode(zpCodep);
					dedicatedFlowService.saveOrUpdateOrder(order);
					//更新草稿单
					order = dedicatedFlowService.copy(order.getParentOrderNumber());
					order.setTransmitState("1");
					order.setDemandName(ortitle);
					order.setOrderReqDescription(orderReqDescription);
					order.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
					order.setRemarks(remarks);
					order.setOrderReqTimeLimit(orderReqTimeLimit);
					order.setGroupCustomerId(groupCustomerId);
					order.setOperationType(operationType);
					for (int s = 0; s < pCode.size(); s++) {
						order.setpCode(pCode.get(s));
					}
					order.setZpcode(zpCodep);
					dedicatedFlowService.saveOrUpdateOrder(order);
					//order = dedicatedFlowService.copyone(order.getOrderId());
				}
				
				if("start".equals(audit)){
					 String request= DateUtil.getIpAddr(this.getRequest());
				
							///审计接口调用
							CommLogs.requOrderquery(user.getLoginName(), user.getEmployeeName(), "0", order.getDemandName(), order.getOrderNumber(), "", "", "", "", "4", request);
					
				}
				
				if ((zproductidp) != null) {
					dedicatedFlowService.updateZPCode(order.getOrderId(),
							zproductidp);
				}
				if (of != null) {
					List<Map<String, String>> lists = dedicatedFlowService.fuJian(of.getParentOrderNumber());
					for (int j = 0; j < lists.size(); j++) {
						Map<String, String> mapStr = lists.get(j);
						SingleAndAttachment sa = new SingleAndAttachment();
						sa.setOrderID(order.getOrderId());
						sa.setAttachmentId(String.valueOf(mapStr.get("id")));
						sa.setLink(Opinion.DEMAND_APPLICATION);
						dedicatedFlowService.saveSandA(sa);
					}
					of.setState("-2");
					of.setOrderCompletionTime(new Date());
					of.setUpdateTime(new Date());
					dedicatedFlowService.saveOrUpdateOrder(of);
				}
				/*
				 * //添加附件 List<SingleAndAttachment> saa=new
				 * ArrayList<SingleAndAttachment>(); if
				 * (!StringUtils.isEmpty(attachmentId)) { if (attachmentId != null)
				 * { // 判断是否上传了附件，获取前台提交的附件Id； String[] json =
				 * attachmentId.split(","); if (json.length > 0) { for (int i = 0; i
				 * < json.length; i++) { atts.add(attachmentService
				 * .getAttachmentById(json[i])); // 得到 每个对象中的属性值 } } } } // 添加附件： if
				 * (saa.size() > 0) { order.setAttachment(atts); }
				 */
				//添加附件
					if (!StringUtils.isEmpty(attachmentId)) {
						if (attachmentId != null) {
							// 判断是否上传了附件，获取前台提交的附件Id；
							String[] json = attachmentId.split(",");
							if (json.length > 0) {
								for (int i = 0; i < json.length; i++) {
									SingleAndAttachment sa = new SingleAndAttachment();
									sa.setOrderID(order.getOrderId());
									sa.setAttachmentId(json[i]);
									sa.setLink(Opinion.DEMAND_APPLICATION);
									dedicatedFlowService.saveSandA(sa);
									// 得到 每个对象中的属性值
								}
							}
						}
					}
				
	
				// 循环添加待办
				for (int j = 0; j < pCode.size(); j++) {
					//添加数据开始*********************************************************************************
					//OrderForm orderForm = new OrderForm();
					OrderForm orderForm = new OrderForm();
					if(dedicatedFlowService.copy(orderId)!=null){
						//order = dedicatedFlowService.copy(orderId);
						System.out.println(order.getOrderId());
						orderForm=dedicatedFlowService.copyone(order.getOrderId());
						orderForm.setUserId(Integer.parseInt(userId.get(j)));
						orderForm.setUserName(userName.get(j));
						orderForm.setpCode(pCode.get(j));
						//根据产品编码 查询 业务编码并且保存
						BusinessType bt = dedicatedFlowService.queryBcode(pCode.get(j));
						orderForm.setbCode(bt.getbCode());
						orderForm.setOrderTitle(order.getOrderTitle());
						orderForm.setDemandName(order.getDemandName());
						orderForm.setParentOrderNumber(order.getOrderId());
						orderForm.setDraftTime(new Date());
						orderForm.setState("0");
						orderForm.setDraftmanId(user.getRowNo() + "");
						orderForm.setDraftman(user.getEmployeeName());
						orderForm.setSubmissionMode("W");
						orderForm.setType("DI");
						orderForm.setTransmitState(order.getTransmitState());
						orderForm.setOperationType(order.getOperationType());
						orderForm.setGroupCustomerId(order.getGroupCustomerId());
						orderForm.setOrderReqDescription(orderReqDescription);
						orderForm.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
						orderForm.setOrderReqTimeLimit(order.getOrderReqTimeLimit());
						orderForm.setSignedStatus(order.getSignedStatus());
						orderForm.setRemarks(order.getRemarks());
						orderForm.setOrderTypeIdent(1);
						orderForm.setZpcode(order.getZpcode());
						orderForm.setOrderNumber(order.getOrderNumber());
						orderForm.setSystemDeptID(bumen);
						orderForm.setPrantsystemDeptID(quxian);
						orderForm.setSystemCompanyID(fengongsi);
						dedicatedFlowService.saveOrUpdateOrder(orderForm);
					}else{
						orderForm.setUserId(Integer.parseInt(userId.get(j)));
						orderForm.setUserName(userName.get(j));
						orderForm.setpCode(pCode.get(j));
						//根据产品编码 查询 业务编码并且保存
						BusinessType bt = dedicatedFlowService.queryBcode(pCode.get(j));
						orderForm.setbCode(bt.getbCode());
						orderForm.setOrderTitle(order.getOrderTitle());
						orderForm.setDemandName(order.getDemandName());
						orderForm.setParentOrderNumber(order.getOrderId());
						orderForm.setDraftTime(new Date());
						orderForm.setState("0");
						orderForm.setDraftmanId(user.getRowNo() + "");
						orderForm.setDraftman(user.getEmployeeName());
						orderForm.setSubmissionMode("W");
						orderForm.setType("DI");
						orderForm.setTransmitState(order.getTransmitState());
						orderForm.setOperationType(order.getOperationType());
						orderForm.setGroupCustomerId(order.getGroupCustomerId());
						orderForm.setOrderReqDescription(orderReqDescription);
						orderForm.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
						orderForm.setOrderReqTimeLimit(order.getOrderReqTimeLimit());
						orderForm.setSignedStatus(order.getSignedStatus());
						orderForm.setRemarks(order.getRemarks());
						orderForm.setOrderTypeIdent(1);
						orderForm.setZpcode(order.getZpcode());
						orderForm.setOrderNumber(order.getOrderNumber());
						orderForm.setSystemDeptID(bumen);
						orderForm.setPrantsystemDeptID(quxian);
						orderForm.setSystemCompanyID(fengongsi);
						dedicatedFlowService.saveOrUpdateOrder(orderForm);
						if(orderTask!=null){
							dedicatedFlowService.saveBus_order(orderTask.getBusCode(),order.getOrderNumber());
						}
					}
					//添加数据结束**********************************************************************************
					/*
					 * h // 添加附件： if (atts.size() > 0) {
					 * orderForm.setAttachment(atts); }
					 */
	
					if(!"".equals(ord_id) && ord_id!=null&& !"null".equals(ord_id)){
						/*OrderFormOrOrdertask orderFormOrOrdertask = new OrderFormOrOrdertask();
						orderFormOrOrdertask.setOrderTaskID(ord_id);
						orderFormOrOrdertask.setOrderFormID(order.getOrderNumber());*/
						OrderTask ordertask = dedicatedFlowService.queryOrderTask(ord_id);
						if(ordertask!=null)
						dedicatedFlowService.setOrderFormOrOrdertask(ordertask.getBusCode(),order.getOrderNumber());
						//dedicatedFlowService.setOrderFormOrOrdertask(orderFormOrOrdertask);
						WaitTask wait = taskService.queryWaitId(
								WaitTask.ORDER_TASK, waitId);
						taskService.updateWait(wait,this.getRequest());
					} 
					
					if(!"".equals(waitId) && waitId!=null  && !"null".equals(waitId) && !"undefined".equals(waitId)){
						WaitTask wait = taskService.queryWaitId(
								WaitTask.ORDER_TASK, waitId);
						if(wait!=null){
							taskService.updateWait(wait,this.getRequest());
						}
					}
					
						Map<String, String> map = new HashMap<String, String>();
						map.put("customerowner", user.getRowNo() + "");//
						map.put("odermanager", orderForm.getUserId() + "");// 设置下一步处理的人
						ProcessInstance processInstance = jbpmUtil.startPIByKey(
								"orderinformationprocess", map);
						List<Task> tastList = jbpmUtil.findPersonalTasks(user
								.getRowNo() + "");
						String taskId = "";
						for (Task task : tastList) {
							if (processInstance.getId().equals(
									task.getExecutionId())) {
								taskId = task.getId();
								break;
							}
						}
						if (taskId != "") {
	
							/*
							 * Map<String, String> map1 = new HashMap<String,
							 * String>(); map1.put("eid", orderForm.getOrderId());//
							 * 设置资源Id map1.put("ename", order.getOrderTitle());//
							 * 设置资源Id map1.put("ecreationTime", sdf.format(new
							 * Date()));// 创建时间 map1.put("ecreateUserName",
							 * user.getEmployeeName());// 创建人命 map1.put("eurl",
							 * "jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
							 * + order.getOrderId());
							 */
							jbpmUtil.completeTask(taskId, map);
							// 待办
							List<Task> tastList2 = jbpmUtil
									.findPersonalTasks(orderForm.getUserId() + "");
	
							String taskId2 = "";
							for (Task task : tastList2) {
								if (processInstance.getId().equals(
										task.getExecutionId())) {
									taskId2 = task.getId();
									break;
								}
							}
							Set<String> s = jbpmUtil.findOutComesByTaskId(taskId2);
	
							String orderStageId = null;
							// 新增工作台数据
							// 查询二级环节
							String pCode = orderForm.getpCode();
	
							// 根据产品类型id 环节操作类型 查询 第二级环节id
	
							// List<LinkTemplate> pt =
							// dedicatedFlowService.querylink(pCode,
							// orderForm.getOperationType());
							List<LinkTemplate> pt = dedicatedFlowService
									.queryProcFlow(pCode,
											orderForm.getOperationType());
							for (LinkTemplate l : pt) {
								OrderStages ss = new OrderStages();
								ss.setOrderId(orderForm.getOrderId());
								ss.setTwoStageId(l.getLinkTempCode());
								// 根据模板需求时限：// 预计完成时限 //WF 2016-11-26 13:31 修改：//
								if (!StringUtils.isEmpty(l.getLinkNeedLimit())) {
									ss.setExpectedCompletionTime(DateUtil
											.getDateAddHour(Integer.parseInt(l
													.getLinkNeedLimit())));
								}
								SystemUser user2 = systemUserService
										.getUserInfoRowNo(orderForm.getUserId());
								if ("1".equals(l.getBossTrackName())) {
									ss.setIsBossValue("1");
								}
								if ("需求申请".equals(l.getLinkName())) {
									if ("1".equals(l.getBossTrackName())) {
										ss.setStageState("-1");
										dedicatedFlowService.saveOrderStages(ss);
										orderStageId = l.getLinkTempCode();
									} else {
										ss.setStageState("1");
										ss.setActionUser(user2.getEmployeeName());
										ss.setActionUserPhone(user2.getMobile());
										ss.setOperTime(DateUtil.getDateone());
										dedicatedFlowService.saveOrderStages(ss);
										orderStageId = l.getLinkTempCode();
									}
								} else {
									ss.setStageState("-1");
									dedicatedFlowService.saveOrderStages(ss);
	
								}
	
							}
							WaitTask wt = new WaitTask();
							wt.setName(orderForm.getDemandName());
							wt.setCreationTime(new Date());
							// 操作类型 OTChange_2：变更 OTOpen_1：开通
							String TYPE = order.getOperationType();
							// pCode 产品编号 ims:IMS 物联网：WLW
	
							for (String str : s) {
								if("YSX".equals(pCode)){
									if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
										SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
										wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
									}else{
										wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
									}
									
								}else if ("IMS".equals(pCode)) {
									if ("OTChange_2".equals(TYPE)) {
										// ims 变更
										wt.setUrl("jsp/dedicatedFlow/demand/imsChange.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
	
									} else if ("OTOpen_1".equals(TYPE)||"OTXIH_1".equals(TYPE)||"OTSTOPJF_1".equals(TYPE)) {
										if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
											SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
											wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/demand/imsTranslate.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
										}else{
										// ims 开通
										wt.setUrl("jsp/dedicatedFlow/demand/imsTranslate.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
										}
									} else {
										wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
	
									}
								} else if ("WLW".equals(pCode)) {
									if ("OTChange_2".equals(TYPE)) {  
										// 物联网 变更
										wt.setUrl("jsp/dedicatedFlow/demand/internetChange.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
									} else if ("OTOpen_1".equals(TYPE)||"OTXIH_1".equals(TYPE)||"OTSTOPJF_1".equals(TYPE)) {
										if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
											SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
											wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/demand/internetThings.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
											
										} else {
										// 物联网 开通
										wt.setUrl("jsp/dedicatedFlow/demand/internetThings.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
										}
									} else {
										wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
	
									}
								} else {
									if (!StringUtils.isEmpty(zpCodep) && zpCodep!=null && !"".equals(zpCodep)) {
										SystemUser syu = systemUserService.getUserInfoByRowNo(Integer.parseInt(userId.get(j)));
										wt.setUrl("http://10.113.193.18:1306/tailor/http://10.113.156.86:8080/EOM/jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId+"&un="+syu.getLoginName()+"&T="+System.currentTimeMillis());
										
									} else {
										wt.setUrl("jsp/dedicatedFlow/order/trialOrderInformation.jsp?id="
												+ orderForm.getOrderId()
												+ "&taskId="
												+ taskId2
												+ "&NextTask="
												+ str
												+ "&orderStageId=" + orderStageId);
	
									}
								}
	
							}
	
							wt.setState(WaitTask.HANDLE);
							wt.setHandleUserId(orderForm.getUserId());
							wt.setHandleUserName(orderForm.getUserName());
							SystemUser user1 = systemUserService
									.getUserInfoRowNo(orderForm.getUserId());
							wt.setHandleLoginName(user1.getLoginName());
							wt.setCreateUserId(user.getRowNo());
							wt.setCreateUserName(user.getEmployeeName());
							wt.setCreateLoginName(user.getLoginName());
							wt.setCode(WaitTask.DEDICATED_LINE);
							wt.setEndTime(orderForm.getOrderReqTimeLimit());
							wt.setTaskId(orderForm.getOrderId());
							taskService.saveWait(wt,this.getRequest());
						} else {
							this.Write("0");
						}				
				}
				this.Write("1");
			}
			
			
		}
	} catch (Exception e) {
			this.Write("0");
			e.printStackTrace();
			throw new RuntimeException("事务回滚，aop");	
			}

	}

	// ---------------------
	/**
	 * 查询订单工作台页面显示信息：
	 */
	public void selectStagesInfo() {
		try {
			String oid = this.getString("id");

			// 判断 订单oid 是否为空：
			if (!StringUtils.isEmpty(oid)) {
				String jsonStr = dedicatedFlowService.selectStagesInfoJSON(oid
						.trim());// , user.getRowNo()
				ServletResponse response = ServletActionContext.getResponse();

				response.setCharacterEncoding("UTF-8");
				this.Write(jsonStr);
				ayncBossLink(oid);
			}
		} catch (Exception e) {
			e.printStackTrace();
			this.Write("");
		}
	}

	// 展示列表
	@Override
	public PageResponse doList(PageRequest page) {
		final String number = getString("number");

		final String name = getString("name");
		final String auditP= getString("auditP");
		String state = getString("state");
		if("start".equals(audit)){
			  String unl=String.valueOf(this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
				 String cun=String.valueOf(this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
				 String ip =DateUtil.getIpAddr(this.getRequest());
		
					if(!StringUtils.isEmpty(auditP)){
						String order_type_name="";
						
						if(!StringUtils.isEmpty(number)){
							
							order_type_name+="查询订单编号为："+number+ " ";
						}
						if(!StringUtils.isEmpty(name)){
							if(!"".equals(order_type_name)){
								order_type_name+="订单名称为："+name+ " ";
							}else{
								order_type_name+="查询订单名称为："+name+ " ";
							}
						}
						if(!"".equals(order_type_name)){
							CommLogs.requOrderquery(unl, cun, "0", order_type_name, "", "", "", "", "我的专用订单筛选", "15", ip);
						}
					}
			
		}
		
		return dedicatedFlowService.dolist(page, name, user, number,state);
		
	}

	// 需求单展示
	public  void  demandList() {
		try {
			PageRequest page = new PageRequest(getRequest());
			String name = getString("name");
			String demandNumber = getString("demandNumber");
			String auditP= getString("auditP");
			
			PageResponse response = dedicatedFlowService.demandList(
					demandNumber, name, page, user);
			String json = com.xinxinsoft.utils.easyh.JSONHelper
					.SerializeWithNeedAnnotationDateFormats(response);
			Write(json);
			if("start".equals(audit)){
				if(!StringUtils.isEmpty(auditP)){
					String order_type_name="";
					 String unl=String.valueOf(this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
						String cun=String.valueOf(this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUsername()));
						if(!StringUtils.isEmpty(demandNumber)){
							
							order_type_name+="查询需求单编号为："+demandNumber+ " ";
						}
						if(!StringUtils.isEmpty(name)){
							if(!"".equals(order_type_name)){
								order_type_name+="需求单名称为："+name+ " ";
							}else{
								order_type_name+="查询需求单名称为："+name+ " ";
							}
							
						}
						if(!"".equals(order_type_name)){
							CommLogs.requOrderquery(unl, cun, "0", order_type_name, "", "", "", "", "我的需求筛选", "15", DateUtil.getIpAddr(this.getRequest()));
						}
				}
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	// 需求单草稿展示
	public void demandListone() {
		try {
			PageRequest page = new PageRequest(getRequest());
			String name = getString("name");
			String demandNumber = getString("demandNumber");
			PageResponse response = dedicatedFlowService.demandListone(demandNumber, name, page, user);
			String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
			Write(json);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 判断当前登陆人是否是订单的 订单经理
	 */
	public void queryOrderUser() {
		String id = getString("id");
		String i = dedicatedFlowService.queryOrderUser(id, user.getRowNo());
		writeText(i);
	}

	// 订单删除 展示 订单
	public void deleteList() {
		try {
			PageRequest page = new PageRequest(getRequest());
			String name = getString("name");
			String orderNumber = getString("orderNumber");

			PageResponse response = dedicatedFlowService.deleteList(order,
					orderNumber, name, page);
			String json = com.xinxinsoft.utils.easyh.JSONHelper
					.SerializeWithNeedAnnotationDateFormats(response);
			Write(json);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	// 列表展示 展示 当前订单经理的已经通过的订单
	public void eaDolist() {
		try {
			PageRequest page = new PageRequest(getRequest());
			PageResponse response = dedicatedFlowService.eaDolist(order,
					user.getRowNo(), page);
			String json = com.xinxinsoft.utils.easyh.JSONHelper
					.SerializeWithNeedAnnotationDateFormats(response);
			Write(json);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * 根据id 获取订单
	 */
	public void queryId() {
		String id = getString("id");
		order = dedicatedFlowService.queryId(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(order));
	}
	
	
	/**
	 * 更具webservice接口代办查询订单;
	 */
	public void queryIdtwo() {
		String id = getString("id");
		OrderTask orderTask = dedicatedFlowService.queryIdtwo(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(orderTask));
	}
	
	

	/**
	 * 根据父id 获取订单集合
	 */
	public void queryIdList() {
		String id = getString("id");
		List<Map<String, String>> o = dedicatedFlowService.queryIdList(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(o));
	}

	/**
	 * 获取当前环节附件 id name 信息
	 */
	public void fuJian() {
		String id = getString("id");
		List<Map<String, String>> s = dedicatedFlowService.fuJian(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}
	/**
	 * 获取商机编号：Code
	 */
	public void getBusCode(){
		String orderNumber = this.getString("orderNumber");
		List<Map<String, String>> s = dedicatedFlowService.queryBusCodeByOrdernum(orderNumber);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}

	// 处理
	public void opinion() {
		try {

			String taskId = getString("taskId");//

			if (taskId != null) {
				// jbpmUtil.completeTask(taskId);
				String id = getString("id");
				String state = getString("state");
				String opinion = getString("opinion");

				String emergencySituation = getString("emergencySituation");
				String demandArea = getString("demandArea");
				String sourceChannel = getString("sourceChannel");
				String requestType = getString("requestType");
				String city = getString("city");
				String   isPushBoss =getString("isPushBoss");
				String orderTitle = getString("orderTitle");
				String orderReqDescription = getString("orderReqDescription");
				String orderReqDescriptionHTML = getString("orderReqDescriptionHTML");
				String orderReqTimeLimit = getString("orderReqTimeLimit");
				String orderStageId = getString("orderStageId");
				order = dedicatedFlowService.queryId(id);
				GroupCustomer customer = groupCustomerService
						.getGroupCustomerSQLById(order.getGroupCustomerId());
				order.setState(state);
				order.setOrderStatus(0);
				if (!"2".equals(state)) {
					// 获取是字典表的数据 并且查询实体保存
					order.setOrderTitle(orderTitle);
					OrderInformation orderinfor = new OrderInformation();

					if (emergencySituation != null
							&& !"".equals(emergencySituation)) {
						orderinfor.setEmergencySituation(emergencySituation);
					}
					if (requestType != null && !"".equals(requestType)) {
						orderinfor.setRequestType(requestType);
					}
					if (demandArea != null && !"".equals(demandArea)) {
						orderinfor.setDemandArea(demandArea);
					}
					if (sourceChannel != null && !"".equals(sourceChannel)) {
						orderinfor.setSourceChannel(sourceChannel);
					}
					if (city != null && !"".equals(city)) {
						orderinfor.setCity(city);
					}
					orderinfor.setOrderId(order.getOrderId());
					// order.setDraftTime(new Date());
					order.setOrderReqDescription(orderReqDescription);
					order.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
					order.setOrderReqTimeLimit(new SimpleDateFormat(
							"yyyy-MM-dd").parse(orderReqTimeLimit));
					if(!"true".equals(isPushBoss)){//不推向BOSS，只有当选择了子产品；
						logger.info("===========================================推向BOSS============================================================");
						ResourceBundle s = ResourceBundle
								.getBundle("WebService-config");
						String BOSSSwitch = s.getString("BOSSSwitch");
						/**
						 * 查询预受理状态
						 * 
						 * @param so_no
						 *            订单系统订单号
						 * @param oper_no
						 *            工号ID
						 * @param oper_name
						 *            工号名称
						 * @param grpcust_id
						 *            集团编码
						 * @param busi_req_type
						 *            业务请求类型
						 * @param chn_id
						 *            渠道
						 * @param region_code
						 *            地市编码
						 * @param group_id
						 *            区域编码
						 * @param obtain_time
						 *            业务获取时间
						 * @param chance_name
						 *            业务请求名称
						 * @param pri_code
						 *            紧急程度
						 * @param demand_desc
						 *            需求描述
						 * @throws Exception
						 *             * @param person_no si工号
						 * 
						 *             groupCoding
						 */
						if ("start".equals(BOSSSwitch)) {
							// GroupCustomer
							// customer=groupCustomerService.getGroupCustomerSQLById(order.getGroupCustomerId());
							try {
								if ("".equals(user.getBossUserName())
										|| "null".equals(user.getBossUserName())
										|| null == user.getBossUserName()) {
									Write("-1");
									return;
								}
								StartPreOrderOut orderOut = CMCCOpenService
										.getInstance().startPreOrder(
												order.getOrderNumber(),
												user.getOperNo(),
												user.getOperName(),
												customer.getGroupCoding(),
												orderinfor.getRequestType(),
												orderinfor.getSourceChannel(),
												orderinfor.getCity(),
												orderinfor.getDemandArea(),
												new SimpleDateFormat(
														"yyyy-MM-dd hh:mm:ss")
														.format(new Date()),
												order.getOrderTitle(),
												orderinfor.getEmergencySituation(),
												"", user.getBossUserName());
								// URLEncoder.encode(order.getOrderReqDescription(),
								// "UTF-8")
								if ("0000000".equals(orderOut.getResCode())) {
									order.setBossState(1);
									order.setBossFormNo(orderOut.getBossNo());
									logger.info("BOSS推送成功：参数getBossNo："+orderOut.getBossNo());
								} else {
									order.setBossState(0);
									Write("3");
									
									return;
								}
							} catch (Exception e) {
								e.printStackTrace();
								logger.info("+BOSS推送失败："+e.getMessage());
								Write("3"+e.getMessage());
								return;
							}
	
						}
					}
					dedicatedFlowService.saveOrderinfor(orderinfor);
					String attachmentId = getString("attachmentId");
					dedicatedFlowService.saveOrUpdateOrder(order);
					if (!StringUtils.isEmpty(attachmentId)) {
						if (attachmentId != null) {
							// 判断是否上传了附件，获取前台提交的附件Id；
							String[] json = attachmentId.split(",");
							if (json.length > 0) {
								for (int i = 0; i < json.length; i++) {
									SingleAndAttachment sa = new SingleAndAttachment();
									sa.setOrderID(order.getParentOrderNumber());
									sa.setAttachmentId(json[i]);
									sa.setLink(Opinion.DEMAND_APPLICATION);
									dedicatedFlowService.saveSandA(sa);
									// 得到 每个对象中的属性值
								}
							}
						}
					}
					WaitTask wait = taskService.queryWaitTaskIdAndCode(
							WaitTask.DEDICATED_LINE, order.getOrderId());
					if(wait!=null){
						Opinion opinion2 = new Opinion();
						opinion2.setCreationTime(new Date());
						opinion2.setOpinion(opinion);
						opinion2.setOrderId(order.getOrderId());
						opinion2.setPersonnel(user.getEmployeeName());
						opinion2.setPersonnelNo(user.getRowNo());
						opinion2.setIdentification(Opinion.DEMAND_APPLICATION);
						opinion2.setRole("[订单经理]");
						if ("2".equals(state)) {
							opinion2.setWhetherThrough("退回");
							// 修改工作台状态
							/*
							 * dedicatedFlowService.updateOrder(order.getOrderId(),
							 * orderStageId, "-1");
							 */
							processService.updateOrder(order.getOrderId(),
									orderStageId, "-1");
	
						} else {
							opinion2.setWhetherThrough("通过");
							/*
							 * dedicatedFlowService.updateOrder(order.getOrderId(),
							 * orderStageId, "1");
							 */
							// processService.updateOrder(order.getOrderId(),
							// orderStageId, "1",new Date(),"","");
							/*
							 * dedicatedFlowService.updateOrderdateid(
							 * orderStageId); //
							 */
							processService.updateOrder(order.getOrderId(),
									orderStageId, "1", new Date(), "", "");
	
						}
						dedicatedFlowService.saveOpinion(opinion2);

					jbpmUtil.completeTask(taskId);
					taskService.updateWait(wait,this.getRequest());
					}else{
						throw new Error("首页待办查询失败！orderId:"+order.getOrderId());
					}
				} else {// 退回处理

					String attachmentId = getString("attachmentId");

					WaitTask wait = taskService.queryWaitTaskIdAndCode(
							WaitTask.DEDICATED_LINE, order.getOrderId());
					if(wait!=null){
					
					if (!StringUtils.isEmpty(attachmentId)) {
						if (attachmentId != null) {
							// 判断是否上传了附件，获取前台提交的附件Id；
							String[] json = attachmentId.split(",");
							if (json.length > 0) {
								for (int i = 0; i < json.length; i++) {
									SingleAndAttachment sa = new SingleAndAttachment();
									sa.setOrderID(order.getParentOrderNumber());
									sa.setAttachmentId(json[i]);
									sa.setLink(Opinion.DEMAND_APPLICATION);
									dedicatedFlowService.saveSandA(sa);
									// 得到 每个对象中的属性值
								}
							}
						}
					}
					Opinion opinion2 = new Opinion();
					opinion2.setCreationTime(new Date());
					opinion2.setOpinion(opinion);
					opinion2.setOrderId(order.getOrderId());
					opinion2.setPersonnel(user.getEmployeeName());
					opinion2.setPersonnelNo(user.getRowNo());
					opinion2.setIdentification(Opinion.DEMAND_APPLICATION);
					opinion2.setRole("[订单经理]");
					opinion2.setWhetherThrough("退回");
					processService.updateOrder(order.getOrderId(),
							orderStageId, "-1");
					dedicatedFlowService.saveOpinion(opinion2);
					jbpmUtil.completeTask(taskId);
					WaitTask wt = new WaitTask();
					wt.setName(order.getDemandName());
					wt.setCreationTime(new Date());
					wt.setCreateUserId(user.getRowNo());
					wt.setCreateUserName(user.getEmployeeName());
					wt.setCreateLoginName(user.getLoginName());
					wt.setCreationTime(new Date());
					SystemUser get_user = systemUserService
							.getUserInfoRowNo(Integer.parseInt(order
									.getDraftmanId()));
					wt.setHandleUserId(get_user.getRowNo());
					wt.setHandleLoginName(get_user.getLoginName());
					wt.setHandleUserName(get_user.getEmployeeName());
					wt.setCode(WaitTask.DEDICATED_LINE);
					wt.setEndTime(order.getOrderReqTimeLimit());
					wt.setTaskId(order.getOrderId());
					wt.setState(WaitTask.HANDLE);
					wt.setUrl("jsp/dedicatedFlow/back.jsp?oid="
							+ order.getParentOrderNumber() + "&sid="
							+ order.getOrderId() + "&T="
							+ System.currentTimeMillis());
					taskService.saveWait(wt,this.getRequest());// /首页和EIP 代办：
					taskService.updateWait(wait,this.getRequest());
					dedicatedFlowService.saveOrUpdateOrder(order);
					OrderForm orderX = dedicatedFlowService.queryId(order.getParentOrderNumber());
					orderX.setState("2");
					orderX.setUpdateTime(new Date());
					dedicatedFlowService.saveOrUpdateOrder(orderX);
					}else{ 
							throw new Error("首页待办查询失败！orderId:"+order.getOrderId());
					}
				}

			}

		}catch(Error e){
			logger.error(e.getMessage());
			//e.printStackTrace();
			writeText("0");
			throw new RuntimeException("给事务回滚，自定义"); 
		} catch (Exception e) {
			e.printStackTrace();
			writeText("0");
			throw new RuntimeException(" 给事务回滚，自定义"); 
		}

	}

	/**
	 * 获取当前子订单的 父订单 产品类型
	 */
	public void queryProduectBName() {
		String id = getString("id");
		List<Map<String, String>> s = dedicatedFlowService
				.queryProduectBName(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}

	/**
	 * 获取当前订单的 产品类型
	 */
	public void queryProduectName() {
		String id = getString("id");
		List<Map<String, String>> s = dedicatedFlowService
				.queryProduectName(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}

	/**
	 * 获取当前订单的 业务和产品类型
	 */
	public void queryBasAndProName() {
		String id = getString("id");
		List<Map<String, String>> s = dedicatedFlowService
				.queryBaAndProtypeName(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}

	/**
	 * 根据子订单 获取父id的订单
	 */
	public void queryFId() {
		String id = getString("id");
		order = dedicatedFlowService.queryFId(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(order));

	}

	public void queryOrderInformation() {
		String id = getString("id");
		OrderInformation orderInformation = dedicatedFlowService
				.queryOrderInformation(id);
		Write(JSONHelper
				.SerializeWithNeedAnnotationDateFormat(orderInformation));
	}

	public void Basics() {
		Map<String, String> userInfo = dedicatedFlowService
				.queryBasics(getInteger("id"));

		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(userInfo));

	}

	/**
	 * 处理 ims wfw
	 */
	public void imswfw() {
		try {

			String taskId = getString("taskId");//
			if (taskId != null) {
				// jbpmUtil.completeTask(taskId);
				String id = getString("id");
				String state = getString("state");
				String opinion = getString("opinion");

				/*
				 * String emergencySituation = getString("emergencySituation");
				 * String demandArea = getString("demandArea"); String
				 * sourceChannel = getString("sourceChannel"); String
				 * requestType = getString("requestType"); String city =
				 * getString("city");
				 * 
				 * String orderTitle = getString("orderTitle"); String
				 * orderReqDescription = getString("orderReqDescription");
				 * String orderReqDescriptionHTML =
				 * getString("orderReqDescriptionHTML"); String
				 * orderReqTimeLimit = getString("orderReqTimeLimit");
				 */
				String orderStageId = getString("orderStageId");
				String nage = getString("nage");
				order = dedicatedFlowService.queryId(id);
				String orderTitle = getString("orderTitle");
				String orderReqDescription=getString("orderReqDescription");
				String orderReqDescriptionHTML=getString("orderReqDescriptionHTML");
				order.setOrderTitle(orderTitle);
				order.setOrderReqDescription(orderReqDescription);
				order.setOrderReqDescriptionHTML(orderReqDescriptionHTML);
				order.setState(state);
				order.setOrderStatus(0);

				if (!"2".equals(state)) {
					// 获取是字典表的数据 并且查询实体保存
					/*
					 * private InternetThings things; private InternetChange
					 * change; private ImsChange imsChange; private ImsTranslate
					 * translate;
					 */
					if ("things1".equals(nage)) {
						things.setOrderId(order.getOrderId());
						dedicatedFlowService.saveThings(things);
					} else if ("change1".equals(nage)) {
						change.setOrderId(order.getOrderId());
						dedicatedFlowService.savechange(change);

					} else if ("imsChange1".equals(nage)) {
						imsChange.setOrderId(order.getOrderId());
						dedicatedFlowService.saveimsChange(imsChange);

					} else if ("translate1".equals(nage)) {
						translate.setOrderId(order.getOrderId());
						dedicatedFlowService.saveimTranslate(translate);

					}

					dedicatedFlowService.saveOrUpdateOrder(order);
					String attachmentId = getString("attachmentId");
					if (!StringUtils.isEmpty(attachmentId)) {
						if (attachmentId != null) {
							// 判断是否上传了附件，获取前台提交的附件Id；
							String[] json = attachmentId.split(",");
							if (json.length > 0) {
								for (int i = 0; i < json.length; i++) {
									SingleAndAttachment sa = new SingleAndAttachment();
									sa.setOrderID(order.getParentOrderNumber());
									sa.setAttachmentId(json[i]);
									sa.setLink(Opinion.DEMAND_APPLICATION);
									dedicatedFlowService.saveSandA(sa);
									// 得到 每个对象中的属性值
								}
							}
						}
					}

					WaitTask wait = taskService.queryWaitTaskIdAndCode(
							WaitTask.DEDICATED_LINE, order.getOrderId());
					if(wait!=null){
						taskService.updateWait(wait,this.getRequest());
						Opinion opinion2 = new Opinion();
						opinion2.setCreationTime(new Date());
						opinion2.setStarTime(new Date());
						opinion2.setOpinion(opinion);
						opinion2.setOrderId(order.getOrderId());
						opinion2.setPersonnel(user.getEmployeeName());
						opinion2.setIdentification(Opinion.DEMAND_APPLICATION);
						opinion2.setRole("[订单经理]");
						if ("2".equals(state)) {
							opinion2.setWhetherThrough("退回");
							// 修改工作台状态
							/*
							 * dedicatedFlowService.updateOrder(order.getOrderId(),
							 * orderStageId, "-1");
							 */
							processService.updateOrder(order.getOrderId(),
									orderStageId, "-1");
	
						} else {
							opinion2.setWhetherThrough("通过");
							/*
							 * dedicatedFlowService.updateOrder(order.getOrderId(),
							 * orderStageId, "1");
							 */
							processService.updateOrder(order.getOrderId(),
									orderStageId, "1", new Date(), "", "");
	
						}
						dedicatedFlowService.saveOpinion(opinion2);
	
						jbpmUtil.completeTask(taskId);
					}else{
						
					}

				} else {

					WaitTask wait = taskService.queryWaitTaskIdAndCode(
							WaitTask.DEDICATED_LINE, order.getOrderId());
					taskService.updateWait(wait,this.getRequest());
					dedicatedFlowService.saveOrUpdateOrder(order);
					OrderForm orderX = dedicatedFlowService.queryId(order.getParentOrderNumber());
					orderX.setState("2");
					orderX.setUpdateTime(new Date());
					dedicatedFlowService.saveOrUpdateOrder(orderX);
					String attachmentId = getString("attachmentId");
					if (!StringUtils.isEmpty(attachmentId)) {
						if (attachmentId != null) {
							// 判断是否上传了附件，获取前台提交的附件Id；
							String[] json = attachmentId.split(",");
							if (json.length > 0) {
								for (int i = 0; i < json.length; i++) {
									SingleAndAttachment sa = new SingleAndAttachment();
									sa.setOrderID(order.getParentOrderNumber());
									sa.setAttachmentId(json[i]);
									sa.setLink(Opinion.DEMAND_APPLICATION);
									dedicatedFlowService.saveSandA(sa);
									// 得到 每个对象中的属性值
								}
							}
						}
					}
					Opinion opinion2 = new Opinion();
					opinion2.setCreationTime(new Date());
					opinion2.setStarTime(new Date());
					opinion2.setOpinion(opinion);
					opinion2.setOrderId(order.getOrderId());
					opinion2.setPersonnel(user.getEmployeeName());
					opinion2.setPersonnelNo(user.getRowNo());
					opinion2.setIdentification(Opinion.DEMAND_APPLICATION);
					opinion2.setRole("[订单经理]");
					opinion2.setWhetherThrough("退回");
					processService.updateOrder(order.getOrderId(),orderStageId, "-1");
					dedicatedFlowService.saveOpinion(opinion2);
					jbpmUtil.completeTask(taskId);
					WaitTask wt = new WaitTask();
					wt.setName(order.getDemandName());
					wt.setCreationTime(new Date());
					wt.setCreateUserId(user.getRowNo());
					wt.setCreateUserName(user.getEmployeeName());
					wt.setCreateLoginName(user.getLoginName());
					wt.setCreationTime(new Date());
					SystemUser get_user = systemUserService
							.getUserInfoRowNo(Integer.parseInt(order
									.getDraftmanId()));
					wt.setHandleUserId(get_user.getRowNo());
					wt.setHandleLoginName(get_user.getLoginName());
					wt.setHandleUserName(get_user.getEmployeeName());
					wt.setCode(WaitTask.DEDICATED_LINE);
					wt.setEndTime(order.getOrderReqTimeLimit());
					wt.setTaskId(order.getOrderId());
					wt.setState(WaitTask.HANDLE);
					wt.setUrl("jsp/dedicatedFlow/back.jsp?oid="
							+ order.getParentOrderNumber() + "&sid="
							+ order.getOrderId() + "&T="
							+ System.currentTimeMillis());
					taskService.saveWait(wt,this.getRequest());// /首页和EIP 代办：
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			// order.setBossState(0);
			// dedicatedFlowService.saveOrUpdateOrder(order);
			writeText("0");
			throw new RuntimeException("自定义事务回滚：aop");
		}
	}

	public void queryImsChange() {
		String id = getString("id");
		Write(JSONHelper
				.SerializeWithNeedAnnotationDateFormat(dedicatedFlowService
						.queryImsChange(id)));
	}

	public void queryImsTranslate() {
		String id = getString("id");
		Write(JSONHelper
				.SerializeWithNeedAnnotationDateFormat(dedicatedFlowService
						.queryImsTranslate(id)));
	}

	public void queryInternetChange() {
		String id = getString("id");
		Write(JSONHelper
				.SerializeWithNeedAnnotationDateFormat(dedicatedFlowService
						.queryInternetChange(id)));
	}

	public void queryInternetThings() {
		String id = getString("id");

		Write(JSONHelper
				.SerializeWithNeedAnnotationDateFormat(dedicatedFlowService
						.queryInternetThings(id)));
	}

	/**
	 * 催办：
	 */
	public void sendMessage() {
		String parameterOne = this.getString("userName");// 用户名称
		String orderLinkName = this.getString("orderLinkName");//
		String orderNum = this.getString("orderNum");//
		String phoneNumber = this.getString("userPhone");// 提示的名称：
		try {
			if (StringUtils.isEmpty(parameterOne)
					|| StringUtils.isEmpty(orderLinkName)
					|| StringUtils.isEmpty(phoneNumber)
					|| StringUtils.isEmpty(orderNum)) {
				this.Write("error");
			} else if ("null".equalsIgnoreCase(parameterOne)
					|| "null".equalsIgnoreCase(orderNum)
					|| "null".equalsIgnoreCase(phoneNumber)) {
				this.Write("error");
			}
			String message = "订单编号为：" + orderNum + "的" + orderLinkName
					+ "待办未处理";
			smsPushService.savePush_0_0001(parameterOne, message, phoneNumber);
			this.Write("success");
		} catch (Exception e) {
			e.printStackTrace();
			this.Write("error");
		}
	}

	/**
	 * 结束退回订单信息：
	 */
	public void queryOver() {
		String idp = this.getString("sid");
		try {
			if (!StringUtils.isEmpty(idp)) {
				OrderForm of = dedicatedFlowService.queryId(idp);
				if (of != null) {
					WaitTask wait = taskService.queryWaitTaskIdAndCode(
							WaitTask.DEDICATED_LINE, idp);
					if (wait == null) {
						return;
					}
					taskService.updateWait(wait,this.getRequest());
					of.setState("-2");
					of.setUpdateTime(new Date());
					of.setOrderCompletionTime(new Date());
					dedicatedFlowService.saveOrUpdateOrder(of);
					OrderForm ofX = dedicatedFlowService.queryId(of.getParentOrderNumber());
								ofX.setState("-2");
								ofX.setUpdateTime(new Date());
								ofX.setOrderCompletionTime(new Date());
								dedicatedFlowService.saveOrUpdateOrder(ofX);
					// /dedicatedFlowService.queryDeleteOrderStates(idp);
					this.Write("1");
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			this.Write("-1");
			throw new RuntimeException("事务回滚：AOP");
		}
	}

	/**
	 * 根据操作类型和产品类型查相应环节
	 */
	public void getOTAndPT() {
		String otp = this.getString("operationType");
		String ptp = this.getString("productType");
		String json = com.xinxinsoft.utils.easyh.JSONHelper
				.SerializeWithNeedAnnotation(dedicatedFlowService.queryOTAndPT(
						otp, ptp));
		Write(json);
	}

	/**
	 * 获取产品类型：
	 */
	public void getProType() {
		String json = com.xinxinsoft.utils.easyh.JSONHelper
				.SerializeWithNeedAnnotation(dedicatedFlowService.queryPType());
		Write(json);
	}

	/**
	 * 
	 */
	public void findSpeialOrder() {
		String otp = this.getString("operationType");
		String ptp = this.getString("productType");
		String company_ibm = this.getString("company_ibm");
		String deptno = this.getString("deptno");
		String userno = this.getString("userno");
		String leval = this.getString("leval");

		String statTime = this.getString("starDate");
		String endTime = this.getString("endDate");
		
		try {
			PageRequest page = new PageRequest(getRequest());
			Map<String, String> map = new HashMap<String, String>();
			map.put("otp", otp);
			map.put("ptp", ptp);
			map.put("company_ibm", company_ibm);
			map.put("deptno", deptno);
			map.put("endTime", endTime);
			map.put("statTime", statTime);
			map.put("userno", userno);
			map.put("leval", leval);
			map.put("page", (getRequest().getParameter("page")));
			map.put("pagesize", (getRequest().getParameter("pagesize")));
			PageResponse response = dedicatedFlowService.querySpeialOrder(map,
					page);
			String json = com.xinxinsoft.utils.easyh.JSONHelper
					.SerializeWithNeedAnnotationDateFormats(response);
			Write(json);
			page.setPageIndex(1);
			page.setPageSize(999999);
			PageResponse responseli = dedicatedFlowService.querySpeialOrder(
					map, page);
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> listmap = ((List<Map<String, Object>>) responseli
					.getList());
			this.getRequest().getSession().setAttribute("listmap_DI", listmap);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	public void findSpeialOrder2() {
		String otp = this.getString("operationType");
		String ptp = this.getString("productType");
		String company_ibm = this.getString("company_ibm");
		String deptno = this.getString("deptno");
		String userno = this.getString("userno");
		String leval = this.getString("leval");
		
		String statTime = this.getString("starDate");
		String endTime = this.getString("endDate");
		
		try {
			Integer pageNo = Integer.valueOf(getString("pageNo"));
			Integer pageSize = Integer.valueOf(getString("pageSize"));
			LayuiPage page =new LayuiPage(pageNo,pageSize);
			Map<String, String> map = new HashMap<String, String>();
			map.put("otp", otp);
			map.put("ptp", ptp);
			map.put("company_ibm", company_ibm);
			map.put("deptno", deptno);
			map.put("endTime", endTime);
			map.put("statTime", statTime);
			map.put("userno", userno);
			map.put("leval", leval);
			map.put("page", (getRequest().getParameter("page")));
			map.put("pagesize", (getRequest().getParameter("pagesize")));
			LayuiPage response = dedicatedFlowService.querySpeialOrder(map,page);
			String json = com.xinxinsoft.utils.easyh.JSONHelper
					.SerializeWithNeedAnnotationDateFormats(response);
			Write(json);
			LayuiPage responseli = dedicatedFlowService.querySpeialOrder(map, page);
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> listmap = ((List<Map<String, Object>>) responseli
					.getData());
			this.getRequest().getSession().setAttribute("listmap_DI", listmap);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 
	 */
	public void findSpeialOrderform() {
		String ordernumber = this.getString("ordernumber");
		try {
			PageRequest page = new PageRequest(getRequest());
			Map<String, String> map = new HashMap<String, String>();
			map.put("ordernumber", ordernumber);
			PageResponse response = dedicatedFlowService.getOrderlists(map,
					page);
			String json = com.xinxinsoft.utils.easyh.JSONHelper
					.SerializeWithNeedAnnotation(response);
			Write(json);
			page.setPageIndex(1);
			page.setPageSize(999999);
			PageResponse responseli = dedicatedFlowService.getOrderlists(map,
					page);
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> listmap = ((List<Map<String, Object>>) responseli
					.getList());
			this.getRequest().getSession().setAttribute("listmap_DI", listmap);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 
	 */
	public void findSpeialOrderform2() {
		String ordernumber = this.getString("ordernumber");
		try {
			Integer pageNo = Integer.valueOf(getString("pageNo"));
			Integer pageSize = Integer.valueOf(getString("pageSize"));
			LayuiPage page =new LayuiPage(pageNo,pageSize);
			Map<String, String> map = new HashMap<String, String>();
			map.put("ordernumber", ordernumber);
			 dedicatedFlowService.getOrderlists(map,page);
			 page.setCode(0);
			String json = com.xinxinsoft.utils.easyh.JSONHelper
					.SerializeWithNeedAnnotation(page);
			Write(json);
			dedicatedFlowService.getOrderlists(map,
					page);
			@SuppressWarnings("unchecked")
			List<Map<String, Object>> listmap = ((List<Map<String, Object>>) page
					.getData());
			this.getRequest().getSession().setAttribute("listmap_DI", listmap);
		} catch (Exception e) {
			logger.info(e.getMessage());
		}
	}

	private static Map<String, String> mapU = new HashMap<String, String>();

	public void getProgressSvr() {
		try {
			String str = String.valueOf(mapU.get("mess"));
			Write(str);
		} catch (Exception e) {
			e.printStackTrace();
			Write("-1");
		}
	}

	@SuppressWarnings("unchecked")
	public void findOrderFormAsync() {
		try {
			List<OrderForm> orderlist = new ArrayList<OrderForm>();
			orderlist = commonSingleService.findeOrderByBoss();
			if (null != orderlist && orderlist.size() > 0) {
				for (OrderForm order : orderlist) {
					List<RESULT_DATA> qplist = new ArrayList<RESULT_DATA>();
					List<OrderStages> oslist = new ArrayList<OrderStages>();
					// 调用Boss接口获得环节信息
					// 并取得订单系统的订单环节信息
					if (null != order) {
						qplist = (List<RESULT_DATA>) CMCCOpenService
								.getInstance()
								.QryPreDeal(order.getBossFormNo());
						if (qplist != null) {
							JSONArray info = JSONArray.fromObject(qplist);
							String jso = "{\"OrderNumber\":"
									+ order.getOrderNumber()
									+ ",\"BossFormNo\":"
									+ order.getBossFormNo() + ",\"info\":"
									+ String.valueOf(info) + "}";
							mapU.put("mess", jso);
							commonSingleService
									.addMessXml(order.getOrderNumber(),
											order.getBossFormNo(),
											String.valueOf(info));
						}
						if (null != qplist) {
							oslist = lateService.queryOrderStages(order
									.getOrderId());
						}
					}
					// 比对相同的环节 修改订单信息
					if (null != oslist && oslist.size() > 0) {
						int overnum = 0;
						for (RESULT_DATA qd : qplist) {
							if (qd.getTASK_STATUS().equals("已办")) {
								overnum += 1;
							}
							for (OrderStages tl : oslist) {
								// 根据环节ID查询环节名称
								List<BossTache> bts = lateService
										.queryBossByLinkCode(tl.getTwoStageId());
								for (BossTache bt : bts) {
									if (qd.getFLOW_TACHE_NAME().trim()
											.equals(bt.getActName().trim())) {
										// tl.setActionUser(qd.getDEAL_NAME());
										tl.setActionUserPhone(qd
												.getDEAL_PHONE());
										SimpleDateFormat sdf = new SimpleDateFormat(
												"yyyy-MM-dd HH:mm:ss");
										Date date;
										Date dateStat = null;
										Date dateo = null;
										try {
											
											dateStat = sdf.parse(qd.getSTART_DATE());
										} catch (ParseException e) {
											dateStat = null;
											e.printStackTrace();
										}
										try {
											
											date = sdf.parse(qd.getFINAL_DATE());
										} catch (ParseException e) {
											date = null;
											e.printStackTrace();
										}
										try {
											
											dateo = sdf.parse(qd.getOPER_DATE());
										} catch (ParseException e) {
											dateo = null;
											e.printStackTrace();
										}

										tl.setOperTime(dateStat);// 开始时间
										// 当环节名称相等时修改订单信息
										if (qd.getTASK_STATUS().trim()
												.equals("代办")) {
											tl.setStageState("0");
											tl.setActualCompletionTime(null);// 完成时间
										} else if (qd.getTASK_STATUS().trim()
												.equals("已办")) {
											tl.setStageState("1");
											tl.setActualCompletionTime(dateo);// 完成时间
										}
										tl.setExpectedCompletionTime(date);

										dedicatedFlowService
												.updateOrderStagesBoss(tl);
									}
								}
							}
						}
						if (overnum == qplist.size()) {
							commonSingleService.updateBossStatus(order
									.getOrderNumber());
						}
					}
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("1");
		}
	}

	/**
	 * 根据bossNO 同步相关信息：
	 * 
	 * @return
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public void findOrderFormByBossNo() throws Exception {
		List<OrderForm> orderlist = new ArrayList<OrderForm>();
		String bossNo = this.getString("bossNo");
		if (StringUtils.isEmpty(bossNo) || "".equals(bossNo)
				|| "null".equals(bossNo) || null == bossNo)
			return;

		orderlist = commonSingleService.findeOrderByBossNo(bossNo);
		if (null != orderlist && orderlist.size() > 0) {
			for (OrderForm order : orderlist) {
				List<RESULT_DATA> qplist = new ArrayList<RESULT_DATA>();
				List<OrderStages> oslist = new ArrayList<OrderStages>();
				// 调用Boss接口获得环节信息
				// 并取得订单系统的订单环节信息
				if (null != order) {
					qplist = (List<RESULT_DATA>) CMCCOpenService.getInstance() .QryPreDeal(order.getBossFormNo());
					if (qplist != null) {
						JSONArray info = JSONArray.fromObject(qplist);
						String jso = "{\"OrderNumber\":"
								+ order.getOrderNumber() + ",\"BossFormNo\":"
								+ order.getBossFormNo() + ",\"info\":"
								+ String.valueOf(info) + "}";
						mapU.put("mess", jso);
						commonSingleService.addMessXml(order.getOrderNumber(),
								order.getBossFormNo(), String.valueOf(info));
					}
					if (null != qplist) {
						oslist = lateService.queryOrderStages(order
								.getOrderId());
					}
				}
				// 比对相同的环节 修改订单信息
				if (null != oslist && oslist.size() > 0) {
					int overnum = 0;
					for (RESULT_DATA qd : qplist) {
						if (qd.getTASK_STATUS().equals("已办")) {
							overnum += 1;
						}
						for (OrderStages tl : oslist) {
							// 根据环节ID查询环节名称
							// Map<Object,Object> lates =
							// lateService.queryLinkId(tl.getTwoStageId(),"0");
							List<BossTache> bts = lateService
									.queryBossByLinkCode(tl.getTwoStageId());
							for (BossTache bt : bts) {
								if (qd.getFLOW_TACHE_NAME().trim()
										.equals(bt.getActName().trim())) {
									//
									tl.setActionUser(qd.getDEAL_NAME());
									tl.setActionUserPhone(qd.getDEAL_PHONE());
									SimpleDateFormat sdf = new SimpleDateFormat(
											"yyyy-MM-dd HH:mm:ss");
									Date date;
									Date dateStat = null;
									Date dateOper=null;
									try {
										
										dateStat = sdf.parse(qd.getSTART_DATE());
									} catch (ParseException e) {
										dateStat = null;
										e.printStackTrace();
									}
									try {
										
										date = sdf.parse(qd.getFINAL_DATE());
									} catch (ParseException e) {
										date = null;
										e.printStackTrace();
									}
									try {
										
										dateOper = sdf.parse(qd.getOPER_DATE());
									} catch (ParseException e) {
										dateOper = null;
										e.printStackTrace();
									}


									tl.setOperTime(dateStat);// 开始时间
									// 当环节名称相等时修改订单信息
									if (qd.getTASK_STATUS().trim().equals("代办")) {
										tl.setStageState("0");
										tl.setActualCompletionTime(null);// 完成时间
									} else if (qd.getTASK_STATUS().trim()
											.equals("已办")) {
										tl.setStageState("1");
										tl.setActualCompletionTime(dateOper);// 完成时间
									}
									tl.setExpectedCompletionTime(date);
									dedicatedFlowService.updateOrderStagesBoss(tl);
								}
							}
						}
					}
					if (overnum == qplist.size()) {
						commonSingleService.updateBossStatus(order
								.getOrderNumber());
						this.Write("同步成功！相应数据修改成功。");
					}
				}
			}
		}
	}

	public void toBossFlow() {
		String ordernum = this.getString("number");
		OrderForm of = dedicatedFlowService.queryOrderByNum(ordernum);

		GroupCustomer customer = groupCustomerService.getGroupCustomerById(of
				.getGroupCustomerId());
		OrderInformation orderinfor = dedicatedFlowService
				.queryOrderInformation(of.getOrderId());
		SystemUser r_user = systemUserService.getUserInfoRowNo(of.getUserId());
		StartPreOrderOut orderOut;
		try {
			if ("".equals(r_user.getBossUserName())
					|| "null".equals(r_user.getBossUserName())
					|| null == r_user.getBossUserName()) {
				Write("推送失败！(你推送的订单经理没配置BOSS工号)");
				return;
			}
			orderOut = CMCCOpenService.getInstance().startPreOrder(
					of.getOrderNumber(),
					r_user.getOperNo(),
					r_user.getOperName(),
					customer.getGroupCoding(),
					orderinfor.getRequestType(),
					orderinfor.getSourceChannel(),
					orderinfor.getCity(),
					orderinfor.getDemandArea(),
					new SimpleDateFormat("yyyy-MM-dd hh:mm:ss")
							.format(new Date()), of.getOrderTitle(),
					orderinfor.getEmergencySituation(), "",
					r_user.getBossUserName());
			if ("0000000".equals(orderOut.getResCode())) {
				of.setBossState(1);
				of.setBossFormNo(orderOut.getBossNo());
				dedicatedFlowService.saveOrUpdateOrder(order);
				Write("推送成功！(OK)");
			} else {
				of.setBossState(0);
				dedicatedFlowService.saveOrUpdateOrder(of);
				Write("推送失败！(NO)");
			}

		} catch (Exception e) {
			e.printStackTrace();
			Write("0");
		}

	}

	void ayncBossLink(String params) {
		System.out.print("------------ayncBossstate-----------------------"
				+ new Date());
		try {
			OrderForm of = dedicatedFlowService.queryOrderformById(params);
			// Object[] mx =new Object[10];
			if (of == null)
				return; // 判断是否存在订单信息
			List<MessagesXml> mxlist = dedicatedFlowService
					.queryMessagesXmlByNumAndBossNo_1(of.getOrderNumber(),
							of.getBossFormNo());
			// mx = (Object[])
			// dedicatedFlowService.queryMessagesXmlByNumAndBossNo(of.getOrderNumber(),of.getBossFormNo());
			if (mxlist.isEmpty())
				return;
			Object obj = mxlist.get(0).getMessTextXml(); // 获取BOSS反馈的JOSN流程进度
			if (null == obj)
				return;
			Gson gson = new Gson();
			// 将JSON转换成LIST
			List<RESULT_DATA> qplist = gson.fromJson(obj.toString(),
					new TypeToken<List<RESULT_DATA>>() {
					}.getType());
			// List<RESULT_DATA> qplist = new ArrayList<RESULT_DATA>();
			if (null == qplist)
				return;
			// 获取订单对应的工作台环节
			List<OrderStages> oslist = lateService.queryOrderStages(of
					.getOrderId());
			// 比对相同的环节 修改订单信息
			if (null != oslist && oslist.size() > 0) {
				int overnum = 0;
				int countNotover = 0;// 代办个数
				for (RESULT_DATA qd : qplist) {
					// 处理BOSS对应的工作台环节
					for (OrderStages tl : oslist) {
						// 根据环节ID查询环节名称
						List<BossTache> bts = lateService
								.queryBossByLinkCode(tl.getTwoStageId());
						for (BossTache bt : bts) {
							if (qd.getFLOW_TACHE_NAME().trim().equals(bt.getActName().trim())) {
								tl.setActionUser(qd.getDEAL_NAME()); // 设置环节处理人名称
								tl.setActionUserPhone(qd.getDEAL_PHONE()); // 设置环节处理人电话号码
								SimpleDateFormat sdf = new SimpleDateFormat(
										"yyyy-MM-dd HH:mm:ss");
								Date date = null; // 完成时间Or预计处理时间
								Date dateStat = null; // 开始时间
								Date dateo=null;
								try {
									dateStat = sdf.parse(qd.getSTART_DATE());
								} catch (ParseException e) {
									date = null;
									e.printStackTrace();
								} 
								try {
									date = sdf.parse(qd.getFINAL_DATE());
									dateo = sdf.parse(qd.getOPER_DATE());
								} catch (ParseException e) {
									e.printStackTrace();

								}

								tl.setOperTime(dateStat);// 开始时间
								// 当环节名称相等时修改订单信息
								if (qd.getTASK_STATUS().trim().equals("代办")) {
									tl.setStageState("0");
									tl.setActualCompletionTime(null);// 完成时间
								} else if (qd.getTASK_STATUS().trim()
										.equals("已办")) {
									tl.setStageState("1");
									tl.setActualCompletionTime(dateo);// 完成时间
								}
								tl.setExpectedCompletionTime(date); // 预计处理时间
								dedicatedFlowService.updateOrderStagesBoss(tl);
							}
						}
					}
					// 获取流程次数信息
					if (qd.getTASK_STATUS().equals("已办")) {
						overnum += 1;
					} else {
						countNotover += 1;
					}
				}

				dedicatedFlowService.updateMessagesXmlByNumAndBossNo(
						of.getOrderNumber(), of.getBossFormNo(),
						String.valueOf(countNotover));
				if (overnum == qplist.size()) {
					commonSingleService.updateBossStatus(of.getOrderNumber());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();

		}

		System.out.print("------------ayncBossend-----------------------"
				+ new Date());
	}

	public void isBossBu() {
		String id = this.getString("id");
		if (StringUtils.isEmpty(id)) {
			Write("0");
			return;
		}
		String str = dedicatedFlowService.isBossBu(id);
		Write(str);
	}

	
	public void usersAsyncEIP(){
		//Write(EIPoOrganization.GetAllUsersBy_isR());
		Write(Proc4AOracle.get4AUser_isR());
		//Write();
	}
	
	//复制方法
	public synchronized void copy(){
		try{
			//获取需求单ID
			String orderId =  getString("orderId");
			//根据ID查询需求单信息以及附件信息和产品信息
			OrderForm or = dedicatedFlowService.copy(orderId);
			OrderForm orn = dedicatedFlowService.copyone(orderId);
			OrderForm orone = new OrderForm();
			//需求单复制
			ProductType prdt = dedicatedFlowService.queryProductType(orn.getpCode());
			orone.setOrderNumber(commonSingleService.returnCode(orn.getpCode(), prdt.getProRowNum(), user, false,"1"));
			orone.setTransmitState("0");
			orone.setbCode(or.getbCode());
			//orone.setBossFormNo(or.getBossFormNo());
			//orone.setBossState(or.getBossState());
			orone.setCustomerLinkman(or.getCustomerLinkman());
			orone.setDemandName(or.getDemandName());
			orone.setDraftman(or.getDraftman());
			orone.setDraftmanId(or.getDraftmanId());
			orone.setDraftTime(new Date());
			orone.setEmergency(or.getEmergency());
			orone.setGroupCustomerId(or.getGroupCustomerId());
			orone.setIndividualCustomerId(or.getIndividualCustomerId());
			orone.setIsFinish(or.getIsFinish());
			orone.setLinkTimeLimit(or.getLinkTimeLimit());
			orone.setOperationType(or.getOperationType());
			//orone.setOrderCompletionTime(or.getOrderCompletionTime());
			orone.setOrderReqDescription(or.getOrderReqDescription());
			orone.setOrderReqDescriptionHTML(or.getOrderReqDescriptionHTML());
			orone.setOrderReqTimeLimit(or.getOrderReqTimeLimit());
			orone.setOrderStatus(or.getOrderStatus());
			orone.setOrderTelephone(or.getOrderTelephone());
			orone.setOrderTitle(or.getOrderTitle());
			orone.setOrderType(or.getOrderType());
			orone.setOrderTypeIdent(or.getOrderTypeIdent());
			orone.setParentOrderNumber(or.getParentOrderNumber());
			orone.setpCode(or.getpCode());
			orone.setRemarks(or.getRemarks());
			orone.setSignedStatus(or.getSignedStatus());
			orone.setsOrderId(or.getsOrderId());
			orone.setState("0");
			orone.setSubmissionMode(or.getSubmissionMode());
			orone.setType(or.getType());
			orone.setUpdateTime(or.getUpdateTime());
			orone.setUserId(or.getUserId());
			orone.setUserName(or.getUserName());
			orone.setZpcode(or.getZpcode());
			orone.setPrantsystemDeptID(or.getPrantsystemDeptID());
			orone.setSystemDeptID(or.getSystemDeptID());
			orone.setSystemCompanyID(or.getSystemCompanyID());
			order = dedicatedFlowService.saveOrderform(orone);
			//正式订单复制*************************************************************************************
			OrderForm orc = dedicatedFlowService.copyone(orderId);
			OrderForm ortwo = new OrderForm();
			ortwo.setOrderNumber(order.getOrderNumber());
			ortwo.setTransmitState("0");
			ortwo.setbCode(orc.getbCode());
			//ortwo.setBossFormNo(orc.getBossFormNo());
			//ortwo.setBossState(orc.getBossState());
			ortwo.setCustomerLinkman(orc.getCustomerLinkman());
			ortwo.setDemandName(orc.getDemandName());
			ortwo.setDraftman(orc.getDraftman());
			ortwo.setDraftmanId(orc.getDraftmanId());
			ortwo.setDraftTime(new Date());
			ortwo.setEmergency(orc.getEmergency());
			ortwo.setGroupCustomerId(orc.getGroupCustomerId());
			ortwo.setIndividualCustomerId(orc.getIndividualCustomerId());
			ortwo.setIsFinish(orc.getIsFinish());
			ortwo.setLinkTimeLimit(orc.getLinkTimeLimit());
			ortwo.setOperationType(orc.getOperationType());
			//ortwo.setOrderCompletionTime(orc.getOrderCompletionTime());
			ortwo.setOrderReqDescription(orc.getOrderReqDescription());
			ortwo.setOrderReqDescriptionHTML(orc.getOrderReqDescriptionHTML());
			ortwo.setOrderReqTimeLimit(orc.getOrderReqTimeLimit());
			ortwo.setOrderStatus(orc.getOrderStatus());
			ortwo.setOrderTelephone(orc.getOrderTelephone());
			ortwo.setOrderTitle(orc.getOrderTitle());
			ortwo.setOrderType(orc.getOrderType());
			ortwo.setOrderTypeIdent(orc.getOrderTypeIdent());
			ortwo.setParentOrderNumber(order.getOrderId());
			ortwo.setpCode(orc.getpCode());
			ortwo.setRemarks(orc.getRemarks());
			ortwo.setSignedStatus(orc.getSignedStatus());
			ortwo.setsOrderId(orc.getsOrderId());
			ortwo.setState("0");
			ortwo.setSubmissionMode(orc.getSubmissionMode());
			ortwo.setType(orc.getType());
			ortwo.setUpdateTime(orc.getUpdateTime());
			ortwo.setUserId(orc.getUserId());
			ortwo.setUserName(orc.getUserName());
			ortwo.setZpcode(orc.getZpcode());
			ortwo.setPrantsystemDeptID(orc.getPrantsystemDeptID());
			ortwo.setSystemDeptID(orc.getSystemDeptID());
			ortwo.setSystemCompanyID(orc.getSystemCompanyID());
			//正式订单复制结束***********************************************************************************
			order = dedicatedFlowService.saveOrderform(ortwo);
			//根据之前的需求单ID查询附件中间表数据
			List<SingleAndAttachment> ss = dedicatedFlowService.getSingleAndAttachment(orderId);
			//遍历获取的附件中间表数据
			for (int i=0; i<ss.size();i++){
				SingleAndAttachment s = new SingleAndAttachment();
				s.setOrderID(order.getParentOrderNumber());
				s.setAttachmentId(ss.get(i).getAttachmentId());
				s.setLink(Opinion.DEMAND_APPLICATION);
				dedicatedFlowService.saveSandA(s);
			}
			//查询产品
			OrderDetail orderDetail =dedicatedFlowService.getOrderDetail(orderId);
			String uuid = UUIDUtil.getInstance().generatUUID(false);
			if(orderDetail!=null){
				OrderDetail orderDetailone = new OrderDetail();
				orderDetailone.setCreateTime(new Date());
				orderDetailone.setOdetailId(uuid);
				orderDetailone.setOrderId(order.getOrderId());
				orderDetailone.setModifyTime(new Date());
				orderDetailone.setXmlContent(orderDetail.getXmlContent());
				orderDetailone.setZpcode(orderDetail.getZpcode());
				dedicatedFlowService.addOrderDetail(orderDetailone);
			}
			this.Write(order.getOrderId()+","+order.getParentOrderNumber());
		}catch(Exception e){
			e.printStackTrace();
			this.Write("NO");
		}
	}
	
	public void getOdetailId(){
		String id = getString("id");
		String zpcode = getString("zpcode");
		OrderDetail orderDetail=null;
		String zproductidShow = getString("zproductidShow");
		if(!zproductidShow.equals("") && zproductidShow!=null  && !zpcode.equals("") && zpcode!=null){
			orderDetail = dedicatedFlowService.getOrderDetailOdetailId(zproductidShow,zpcode);
		}else{
			orderDetail = dedicatedFlowService.getOrderDetail(id,zpcode);
		}
		
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(orderDetail));
	}
	
	public void getOdetailIdone(){
		String id = getString("id");
		OrderDetail orderDetail = dedicatedFlowService.getOrderDetail(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(orderDetail));
	}
	
	public void deleteCopy(){
		try{
			String id = getString("orderId");
			String orid = getString("orid");
			dedicatedFlowService.deleteCopy(orid); 
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getOrderForm(){
		String id = getString("id");
		OrderForm derForm = dedicatedFlowService.copyone(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(derForm));
	}
	/**
	 * 
	* @Title：byStateDate 
	* @Description：专用创建时间一键生成
	* @param ： 
	* @return ：void 
	* @throws
	 */
	public void byStateDate(){
		Boolean isR = dedicatedFlowService.queryByStateDate();
				if(isR){
					Write("执行成功");
				}else{
					Write("执行失败");
				}
	}
	/**
	 * 
	* @Title：runDates 
	* @Description：订单跑时间
	* @param ： 
	* @return ：void 
	* @throws
	 */
	public void runDates(){
		List<OrderForm> orders = dedicatedFlowService.queryRunDates();
		
		Boolean isR = true;
		 int threadCount=10;
		 int blockSize = orders.size()/threadCount;
		try{
				for (int i = 0; i < threadCount; i++) {
					int startThread = i * blockSize;
					int endThread = (i + 1) * blockSize - 1;
					if (i == blockSize - 1) endThread = orders.size() - 1;
					new Thread((new runThread(orders, i, startThread, endThread, dedicatedFlowService))).start();
				}
		}catch(Exception e){
			isR=false;
		}
		if(isR){
			Write("执行成功");
		}else{
			Write("执行失败");
		}
	}
	
	/**
	 * 
	* @ClassName:：runThread 
	* @Description： 线程跑数据
	* <AUTHOR>  
	* @date ：2017年11月16日 下午12:28:29 
	*
	 */
	
	public static class runThread implements Runnable {
		private int threadId;
		private int endThread;
		private int startThread;
		private  DedicatedFlowService service;
		
		private List<OrderForm> orders;
		public runThread(List<OrderForm> orders,int threadId,int startThread,int endThread,DedicatedFlowService service){
			this.orders=orders;
			this.threadId=threadId;
			this.startThread=startThread;
			this.endThread=endThread;
			this.service=service;
		}
		

		private Integer updateDates(OrderForm order){
			Integer count = 0;
			 if(order.getOrderCompletionTime()!=null){
					long day = DateUtil.getDaySub(order.getDraftTime(), order.getOrderCompletionTime());
					long counday =service.queryCount(String.valueOf(order.getDraftTime()).split(" ")[0], String.valueOf(order.getOrderCompletionTime()).split(" ")[0]);
					if(day>counday){
						long  um = day-counday;
						count=service.updateTime_consume(order,um);
					}else{
						count=service.updateTime_consume(order,0);
					}
					
				 }
			 return count;
		}
		
		@Override
		public  void run() {
			try{
					synchronized (orders) {
						   if(!orders.isEmpty()){
							 for(int i =startThread;i < endThread;i++){
								  updateDates(orders.get(i));
							 }
							 System.err.println("理论线程:"+threadId+",开始位置:"+startThread+",结束位置:"+endThread+"线程" + Thread.currentThread().getName()+ "正在执行。。" );
						   }
					}
				
			}catch(Exception e){
				e.printStackTrace();
			}finally{
				
			}
		}
		
	}
	
	public void copyOdetail(){
		try{
			String id = getString("id");
			OrderDetail orderDetail = dedicatedFlowService.getOrderDetailtwo(id);
			OrderDetail orderDetailtwo = new OrderDetail();
			String uuid= UUIDUtil.getInstance().generatUUID(false);
			orderDetailtwo.setCreateTime(new Date());
			orderDetailtwo.setOdetailId(uuid);
			orderDetailtwo.setXmlContent(orderDetail.getXmlContent());
			orderDetailtwo.setZpcode(orderDetail.getZpcode());
			OrderDetail OrderDetailthree=dedicatedFlowService.addOrderDetailtwo(orderDetailtwo);
			this.Write(OrderDetailthree.getOdetailId());
		}catch(Exception e){
			e.printStackTrace();
			this.Write("ON");
		}
	}
	
	public void deleteOdetail(){
		try{
			String id = getString("id");
			String[] str = id.split(",");
			for(int i=0;i<str.length;i++){
				dedicatedFlowService.deleteOdetail(str[i]);
			}
			this.Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			this.Write("NO");
		}
	} 
}
