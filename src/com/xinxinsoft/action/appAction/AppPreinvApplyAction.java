package com.xinxinsoft.action.appAction;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.log4j.Logger;
import org.jbpm.api.task.Task;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.PreinvApply.PreinvApply;
import com.xinxinsoft.entity.PreinvApply.PreinvApplyDet;
import com.xinxinsoft.entity.PreinvApply.PreinvApplyFlow;
import com.xinxinsoft.entity.PreinvApply.PreinvApplyTask;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.PreinvApply.PreinvApplyService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

public class AppPreinvApplyAction extends BaseAction {
	private static final long serialVersionUID = 1820845463088370062L;
	private static final Logger LOGGER = Logger.getLogger(AppPreinvApplyAction.class);

	private TransferJBPMUtils transferJBPMUtils;
	private WaitTaskService service;
	private SystemUserService systemUserService;
	private PreinvApplyService preinvApplyService;
	private JbpmUtil jbpmUtil;

	public TransferJBPMUtils getTransferJBPMUtils() {
		return transferJBPMUtils;
	}

	public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
		this.transferJBPMUtils = transferJBPMUtils;
	}

	public WaitTaskService getService() {
		return service;
	}

	public void setService(WaitTaskService service) {
		this.service = service;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public PreinvApplyService getPreinvApplyService() {
		return preinvApplyService;
	}

	public void setPreinvApplyService(PreinvApplyService preinvApplyService) {
		this.preinvApplyService = preinvApplyService;
	}

	public JbpmUtil getJbpmUtil() {
		return jbpmUtil;
	}

	public void setJbpmUtil(JbpmUtil jbpmUtil) {
		this.jbpmUtil = jbpmUtil;
	}

	/**
	 * 根据登录人名称查询预开票待办信息列表
	 */
	public void findWaitByLoginNameSrv() {
		try {
			LOGGER.info("根据登录人名称查询预开票待办信息。执行方法findWaitByLoginNameSrv()");

			String loginName = getString("loginName");// 登录人名称
			PageRequest page = new PageRequest(getRequest());
			PageResponse response = preinvApplyService.findByLoginName(loginName, page);// 根据登录人名称查询预开票待办信息

			Write(JSONHelper.SerializeWithNeedAnnotation(response));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 根据工单编号获取预开票详情信息
	 */
	public void findPreinvApplyDetsByBatchNoSrv() {
		try {
			LOGGER.info("根据工单编号获取预开票详情信息。执行方法findPreinvApplyDetsByBatchNoSrv()");

			String id = getString("id"); // 工单编号
			String loginName = getString("loginName");// 登录名
			String typey = getString("typey");// 判断是否为审批中的流程的标识

			PreinvApplyTask preinvApplyTemp = preinvApplyService.getTaskList(id);
			PreinvApplyFlow prinvFlowTemp = preinvApplyService.getPreinvApplyFlow(preinvApplyTemp.getFlowId());
			PreinvApply preinvApply = preinvApplyService.getPreinvApplyByBachNo(prinvFlowTemp.getBatchNo());
			PreinvApplyFlow preinvApplyFlow = preinvApplyService.findbyPreinvBatchNo(preinvApply.getBatchNo(), "");
			SystemUser user = systemUserService.getUserInfoByLoginName(loginName); // 根据登录名获取用户信息
			String processTracking = JSONHelper
					.SerializeWithNeedAnnotationDateFormat(preinvApplyService.processtracking(preinvApplyFlow.getFlowId()));// 流程跟踪

			PreinvApplyTask preinvApplyTask = preinvApplyService.getPreinvApplyTaskById(preinvApplyFlow.getFlowId(), user.getRowNo()); // 当前任务
			String handleTaskJson = JSONHelper.SerializeWithNeedAnnotationDateFormat(preinvApplyTask);
			String preinvApplyDets = JSONHelper.SerializeWithNeedAnnotationDateFormat(preinvApplyService.findByUUID(preinvApply.getBatchNo()));

			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(preinvApplyFlow.getFlowId()).uniqueResult();

			List<String> buttons = new ArrayList<>(); // 返回的按钮标识
			if (task != null) {
				// 当前节点名称
				String activityName = task.getActivityName();
				/*
				 * th: 退回 tj: 提交 zs: 转审 zf: 转发 wc: 完成
				 */
				// 默认返回提交和退回
				buttons.add("th");
				buttons.add("tj");

				switch (activityName) {
				case "区县分管经理":
					// 工单金额小于100000，不用再提交下一步了，可以完成了
					if (Double.parseDouble(preinvApply.getAppAmout()) < 100000) {
						buttons.add("ty");
						buttons.remove("tj");
					}
					break;
				case "市公司业务管理员":
					buttons.add("zs"); // 添加转审按钮
					break;
				case "市公司业务管理室经理":

					break;
				case "市公司政企部经理":
					buttons.add("zs");
					if (Double.parseDouble(preinvApply.getAppAmout()) < 500000) {
						buttons.add("ty");
						buttons.remove("tj");
					}
					break;
				case "市公司领导":
					buttons.add("ty");
					buttons.remove("tj");
					break;
				case "省重客客户经理室经理":
					if (Double.parseDouble(preinvApply.getAppAmout()) < 500000) {
						buttons.add("ty");
						buttons.remove("tj");
					}
					break;
				case "省重客分管经理":
					buttons.add("ty");
					buttons.remove("tj");
					break;
				default:
					break;
				}
				// 给其他审批人添加转审按钮
				if (activityName.contains("其他")) {
					buttons.add("zs");
				}
				// 冲正工单
				if ("PreinvApplyCorrect".equals(preinvApplyFlow.getFlowName())) {
					buttons.clear();
					buttons.add("th");
					buttons.add("ty");
				}
			} else {
				buttons.add("zf");// 转发
				buttons.add("wc");// 完成
			}

			String taskname = "";
			// 获取线条值
			if (!"COMPLETERECEIVEAPPLY".equals(typey) && !"RETURNRECEIVEAPPLY".equals(typey) && !"".equals(typey)) {
				taskname = findTaskName(preinvApplyFlow.getFlowId()); // 根据流程id查询任务名称
			}
			Map<String, Object> map = preinvApplyService.findByBatchNo(preinvApply.getBatchNo());
			JSONObject json = JSONObject.fromObject(JSONHelper.SerializeWithNeedAnnotationDateFormats(map));
			json.put("preinvApplyDets", preinvApplyDets); // 账户详情
			json.put("preinvApplyFlow", JSONHelper.SerializeWithNeedAnnotationDateFormat(preinvApplyFlow)); // 应收未收流程
			json.put("handleTask", handleTaskJson); // 处理的任务
			json.put("processTracking", processTracking); // 跟踪记录
			json.put("taskName", taskname); // 线条值
			json.put("buttons", buttons); // 按钮

			Write(json.toString());

		} catch (Exception e) {
			e.printStackTrace();
			Write("ON");
		}
	}

	/**
	 * 查询线条值
	 * 
	 * @param flowId
	 *            流程id
	 * @return
	 */
	private String findTaskName(String flowId) {
		// 获取任务对象
		Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(flowId).uniqueResult();
		JSONArray jArray = new JSONArray();
		JSONObject objtwo = new JSONObject();

		if (task != null) {
			Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
			for (String outcome : setlist) {
				JSONObject obj = new JSONObject();
				obj.put("transitionName", outcome);
				jArray.add(obj);
			}
			objtwo.put("processNode", task.getName());
			objtwo.put("nextStep", jArray.toString());
		} else {

			objtwo.put("processNode", "");
			objtwo.put("nextStep", jArray.toString());

		}

		return JSONHelper.SerializeWithNeedAnnotation(objtwo);
	}

	/**
	 * 流程处理
	 */
	public void handlePreinvApplySrv() {
		LOGGER.info("流程处理。执行handlePreinvApplySrv()");
		try {
			String pid = getString("processId");// 流程id
			String oldProcessId = getString("oldProcessId");// 原流程id
			String taskName = getString("taskName");// 下一步可执行流程线条值
			String uuid = getString("id");// 开票id
			String opinion = getString("opinion");// 审批意见
			String waitId = getString("waitId");// 待办id
			String taskId = getString("preinvApplyTaskId");// 任务id
			String name = getString("loginName"); // 登录用户名
			String userId = getString("userId");// 用户id
			SystemUser loginUser = systemUserService.get4ALoginInfo(name);// 登录用户
			SystemUser nextHandleUser = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));// 下一步处理人

			PreinvApplyTask ptask = preinvApplyService.getTaskList(taskId); // 任务对象
			if (ptask != null) {
				ptask.setDealDate(new Date());// 操作时间
				ptask.setTaskMemo(opinion);// 处理意见
				ptask.setState("1");// 修改状态为处理完成
				preinvApplyService.updatePreinvApplyTask(ptask);// 修改任务表信息
			}
			PreinvApply preinvApply = preinvApplyService.findById(uuid);// 根据id查询开票信息
			WaitTask wt = service.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
			// 结束当前待办
			if (wt != null) {
				LOGGER.info("================处理中开始代办================");
				service.updateWait(wt, this.getRequest());
				LOGGER.info("================处理中结束代办================");
			} else {
				throw new Error("待办ID==========：" + waitId);
			}
			Map<String, String> map = new HashMap<>();
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
			if ("区县分管经理".equals(task.getActivityName())) {
				map.put("decisionKey", "ROLE_DSBM");
				if (Double.parseDouble(preinvApply.getAppAmout()) < 100000) {
					map.put("decisionValue", "YES");
				} else {
					map.put("decisionValue", "NO");
				}
				jbpmUtil.completeTask(task.getId(), map);// 流程流转
			} else if ("省重客客户经理室经理".equals(task.getActivityName())) {
				map.put("decisionKey", "ROLE_SZKSM");
				if (Double.parseDouble(preinvApply.getAppAmout()) < 500000) {
					map.put("decisionValue", "YES");
				} else {
					map.put("decisionValue", "NO");
				}
				jbpmUtil.completeTask(task.getId(), map);// 流程流转
			} else if ("市公司政企部经理".equals(task.getActivityName())) {
				map.put("decisionKey", "ROLE_DSSM");
				if (Double.parseDouble(preinvApply.getAppAmout()) < 500000) {
					map.put("decisionValue", "YES");
				} else {
					map.put("decisionValue", "NO");
				}
				if (taskName.equals("ALL")) {
					jbpmUtil.completeTask(task.getId(), taskName);
				} else {
					jbpmUtil.completeTask(task.getId(), map, taskName);
				}
			} else {
				jbpmUtil.completeTask(task.getId(), taskName);
			}

			PreinvApplyFlow flow = preinvApplyService.getPreinvApplyFlow(pid);// 根据流程id查询流程表信息
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
			// 修改流程表信息
			flow.setDealName(nextHandleUser.getEmployeeName());// 处理人名称
			flow.setDealNo(nextHandleUser.getRowNo() + "");// 处理人编号
			flow.setUpdateDate(new Date());// 更新时间
			flow.setState("0");// 状态为审批中
			preinvApplyService.updatePreinvApplyFlow(flow);// 修改流程表信息
			// 新增任务表信息
			PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
			if (oldProcessId != null & !"".equals(oldProcessId)) {
				preinvApplyTask.setFlowId(oldProcessId);// 流程id
			} else {
				preinvApplyTask.setFlowId(pid);// 流程id
			}
			preinvApplyTask.setTaskId(tasktwo.getId());// 环节id
			preinvApplyTask.setTaskName(tasktwo.getActivityName());// 环节名称
			preinvApplyTask.setCreatorNO(loginUser.getRowNo() + "");// 创建人编号
			preinvApplyTask.setCreatorName(loginUser.getEmployeeName());// 创建人名称
			preinvApplyTask.setCreateDate(new Date());// 创建时间
			preinvApplyTask.setDealNo(nextHandleUser.getRowNo() + "");// 处理人编号
			preinvApplyTask.setDealName(nextHandleUser.getEmployeeName());// 处理人名称
			preinvApplyTask.setState("0");// 状态
			preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
			submitWaitGenerate(preinvApply, userId, pid, oldProcessId, loginUser, preinvApplyTask);// 生成待办
			Write("OK");
		} catch (Error e) {
			Write("NO");
			throw new RuntimeException(" 给事务回滚，自定义");
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");
		}
	}

	/**
	 * 退回
	 */
	public void returnPreinvApplySrv() {
		LOGGER.info("退回。执行returnPreinvApplySrv()");

		try {
			String name = getString("loginName");// 登录人名称
			SystemUser loginUser = systemUserService.getUserInfoByLoginName(name);// 获取用户信息

			String id = getString("id");// 开票id
			String processId = getString("processId");// 流程id
			String oldProcessId = getString("oldProcessId");// 原流程id
			String waitId = getString("waitId");// 待办id
			String opinion = getString("opinion");// 退回意见
			String taskId = getString("preinvApplyTaskId");// 任务表id
			WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
			if (wt != null) {
				service.updateWait(wt, this.getRequest());
			} else {
				Write("NO");
				return;
			}
			PreinvApplyTask pTask = preinvApplyService.getTaskList(taskId);// 根据任务表id查询任务表信息
			if (pTask != null) {
				pTask.setDealDate(new Date());// 处理时间
				pTask.setTaskMemo(opinion);// 处理意见
				pTask.setState("1");// 状态修改为已完成
				preinvApplyService.updatePreinvApplyTask(pTask);// 修改任务表信息
			}
			PreinvApply preinvApply = preinvApplyService.findById(id);// 根据开票id查询开票信息
			preinvApply.setUpdateDate(new Date());
			if (preinvApply.getOprType().equals("1")) {
				preinvApply.setReverseSatate("6");// 修改状态为作废退回

			} else {
				preinvApply.setStartState("2");// 修改状态为退回
				preinvApply.setHandleState("2");// 可重新开票
			}
			preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息

			returnWaitGenerate(preinvApply, preinvApply.getCreatorId(), processId, oldProcessId, loginUser, pTask);// 生成待办
			Write("OK");
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");
		}
	}

	/**
	 * 完成
	 */
	public void completePreinvApplySrv() {
		LOGGER.info("完成。执行completePreinvApplySrv()");
		Map<String, Object> map = new HashMap<>();
		try {
			String name = getString("loginName");// 登录人名称
			SystemUser loginUser = systemUserService.getUserInfoByLoginName(name);// 获取用户信息

			String pid = getString("processId");// 流程id
			String oldProcessId = getString("oldProcessId");// 原流程id
			String id = getString("id");// 开票id
			String waitId = getString("waitId");// 待办id
			String opinion = getString("opinion");// 审批意见
			String taskId = getString("preinvApplyTaskId");// 任务表id
			WaitTask wt = service.queryWaitByTaskId(waitId);// 获取待办信息
			PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
			if (preinvApply.getStartState().equals("-3")) {
				map.put("flag", "NEWERROR");
				map.put("message", preinvApply.getErrorMessage());
				Write(JSONHelper.Serialize(map));
				return;
			}
			/*boolean bl = preinvApplyService.savePreinvApply(preinvApply);
			// map.put("flag", "YES");
			// map.put("message", "操作成功");
			if (map.get("flag").equals("ERROR")) {
				Write(JSONHelper.Serialize(map));
				return;
			}
			if (map.get("flag").equals("Y")) {
				Write(JSONHelper.Serialize(map));
				return;
			}*/
			if (wt != null) {
				service.updateWait(wt, this.getRequest());
			} else {
				map.put("flag", "NO");
				map.put("message", "操作失败");
				Write(JSONHelper.Serialize(map));
				return;
			}
			jbpmUtil.deleteProcessInstance(pid);// 删除流程
			PreinvApplyFlow flow = preinvApplyService.getPreinvApplyFlow(pid);// 根据流程id查询流程表信息
			// 修改流程表信息
			flow.setUpdateDate(new Date());// 更新时间
			flow.setState("1");// 状态为已完成
			preinvApplyService.updatePreinvApplyFlow(flow);// 修改流程表信息
			PreinvApplyTask pTask = preinvApplyService.getTaskList(taskId);// 根据id查询任务表信息
			if (pTask != null) {
				pTask.setDealDate(new Date());// 处理日期
				pTask.setTaskMemo(opinion);// 处理意见
				pTask.setState("1");// 修改状态为已完成
				preinvApplyService.updatePreinvApplyTask(pTask);// 修改任务表信息
			}
			/*// 1：申请冲正预开票
			if (preinvApply.getOprType().equals("1")) {
				preinvApply.setReverseSatate("5");// 修改状态为作废完成
				List<PreinvApplyDet> listdet = preinvApplyService.findByUUID(preinvApply.getBatchNo());// 查询账户
				for (PreinvApplyDet preinvApplyDet : listdet) {
					preinvApplyDet.setInvState("2");// 状态改为已冲正
					preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
				}
				PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
				List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户

				for (PreinvApplyDet newApplyDet : listdet) {
					for (PreinvApplyDet oldApplyDet : listdets) {
						// 修改原订单的账户状态
						if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
								&& newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
							oldApplyDet.setInvState("2");// 状态改为已冲正
							preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
						}
					}
				}

				preinvApply2.setHandleState("2");// 可重新开票
				preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息

			} else {
				preinvApply.setStartState("1");// 修改状态为已完成
				preinvApply.setHandleState("2");// 可重新开票
			}
			preinvApply.setUpdateDate(new Date());
			preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
*/
			// 新增任务表信息
			PreinvApplyTask preinvApplyTask = new PreinvApplyTask();
			if (oldProcessId != null && !"".equals(oldProcessId) && !"null".equals(oldProcessId)) {
				preinvApplyTask.setFlowId(oldProcessId);// 流程id
			} else {
				preinvApplyTask.setFlowId(pid);// 流程id
			}
			preinvApplyTask.setCreatorNO(loginUser.getRowNo() + "");// 创建人编号
			preinvApplyTask.setTaskName("客户经理");// 环节名称
			preinvApplyTask.setCreatorName(loginUser.getEmployeeName());// 创建人名称
			preinvApplyTask.setCreateDate(new Date());// 创建时间
			preinvApplyTask.setDealNo(preinvApply.getCreatorId());// 处理人编号
			preinvApplyTask.setDealName(preinvApply.getCreatorName());// 处理人名称
			preinvApplyTask.setState("0");// 状态
			preinvApplyService.addPreinvApplyTask(preinvApplyTask);// 保存信息到任务表
			completeWaitGenerate(preinvApply, preinvApply.getCreatorId(), pid, oldProcessId, loginUser, preinvApplyTask);// 生成待办
			map.put("flag", "YES");
			map.put("message", "操作成功");
			Write(JSONHelper.Serialize(map));
		} catch (Exception e) {
			e.printStackTrace();
			map.put("flag", "NO");
			map.put("message", "操作失败");
			Write(JSONHelper.Serialize(map));
			throw new RuntimeException("事务回滚");
		}
	}

	/**
	 * 获取附件信息
	 */
	public void getAttachmentSrv() {
		LOGGER.info("获取附件信息。执行getAttachmentSrv()");
		try {
			String id = getString("id"); // 预开票ID
			PreinvApplyTask preinvApplyTemp = preinvApplyService.getTaskList(id);
			PreinvApplyFlow prinvFlowTemp = preinvApplyService.getPreinvApplyFlow(preinvApplyTemp.getFlowId());
			PreinvApply preinvApply = preinvApplyService.getPreinvApplyByBachNo(prinvFlowTemp.getBatchNo());
			List<Map<String, String>> s = preinvApplyService.fuJian(preinvApply.getUuid(), PreinvApply.PREINVAPPLY);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
		} catch (Exception e) {
			e.printStackTrace();
			Write("ON");
		}
	}

	/**
	 * 根据角色获取用户
	 */
	public void getRoleSystemUser() {
		LOGGER.info("根据角色获取用户。执行getRoleSystemUser()");
		try {
			String rolenName = getString("roleName"); // 角色名
			String loginName = getString("loginName"); // 登录人名称

			SystemUser user = systemUserService.get4ALoginInfo(loginName);// 获取用户信息
			JSONObject jsonObject = new JSONObject();
			Role role = preinvApplyService.getRoleName(rolenName); // 获取角色
			List<Map<String, String>> sd = preinvApplyService.SelectZtreeByUId(rolenName, user);
			jsonObject.put("roleName", role.getCname());
			jsonObject.put("systemUserList", sd);
			Write(jsonObject.toString());
		} catch (Exception e) {
			e.printStackTrace();
			Write("ON");
		}
	}

	/**
	 * 多转发
	 */
	public void manyForwardSrv() {
		LOGGER.info("转发。执行manyForwardSrv()");
		try {
			String loginName = getString("loginName"); // 登录人名称
			String waitId = getString("waitId"); // 待办ID
			String id = getString("id"); // 开票ID
			String taskId = getString("preinvApplyTaskId"); // 任务ID
			String approvalId = this.getString("approvalId");// 接收人ID
			PreinvApplyTask preinvApplyTask = preinvApplyService.getTaskList(taskId);// 查询任务表信息
			if (preinvApplyTask != null) {
				preinvApplyTask.setDealDate(new Date());// 处理日期
				preinvApplyTask.setTaskMemo("");// 处理意见
				preinvApplyTask.setState("1");// 修改状态为已完成
				preinvApplyService.updatePreinvApplyTask(preinvApplyTask);// 修改任务表信息
			}
			WaitTask wait = service.queryWaitByTaskId(waitId);
			if (wait != null) {
				service.updateWait(wait, this.getRequest());
			} else {
				Write("NO");
				return;
			}
			PreinvApply preinvApply = preinvApplyService.findById(id);// 根据id查询开票信息
			String temp = null;
			if (approvalId != null) {
				String a = approvalId.replace("[", "");
				String b = a.replace("]", "");
				String c = b.replace("\"", "");
				temp = c.replace("\"", "");
			}
			String[] appid = null;
			if (temp != null) {
				appid = temp.split(",");
			}
			SystemUser loginUser = systemUserService.getUserInfoByLoginName(loginName); // 根据名称查询对应用户信息
			if (appid != null) {
				for (int i = 0; i < appid.length; i++) {
					forwardWaitGenerate(preinvApply, appid[i], loginUser, "forward", preinvApplyTask.getId(), preinvApplyTask.getFlowId());
				}
			}
			Write("OK");
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 阅读结束
	 */
	public void readEndSrv() {
		LOGGER.info("阅读。执行readEndSrv()");
		try {
			String waitId = getString("waitId");// 待办id
			String taskId = getString("preinvApplyTaskId");// 任务id
			PreinvApplyTask preinvApplyTask = preinvApplyService.getTaskList(taskId);// 查询任务表信息
			PreinvApplyFlow plf= preinvApplyService.getPreinvApplyFlow(preinvApplyTask.getFlowId());
			PreinvApply preinvApply= preinvApplyService.findByBatchNoTwo(plf.getBatchNo());
			Map<String, Object> map = new HashMap<>();
			map = preinvApplyService.savePreinvApply(preinvApply);
			String flag=(String) map.get("flag");
			if("YES".equals(flag)){
				List<PreinvApplyDet> list = preinvApplyService.findByCommitType(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
				if (preinvApplyTask != null) {
					preinvApplyTask.setDealDate(new Date());// 处理日期
					preinvApplyTask.setTaskMemo("");// 处理意见
					preinvApplyTask.setState("1");// 修改状态为已完成
					preinvApplyService.updatePreinvApplyTask(preinvApplyTask);// 修改任务表信息
				}
				//1：申请冲正预开票
				if (preinvApply.getOprType().equals("1")) {
					if(list.size()>0){
						List<PreinvApplyDet> listo = preinvApplyService.findByCommitTypeTwo(preinvApply.getBatchNo());// 根据申请工单编码查询账户信息
						for (PreinvApplyDet preinvApplyDet : listo) {
							preinvApplyDet.setInvState("2");//状态改为已冲正
							preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
						}
						PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
						List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
						for (PreinvApplyDet newApplyDet : listo) {
							for (PreinvApplyDet oldApplyDet : listdets) {
								// 修改原订单的账户状态
								if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
										&& newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
									oldApplyDet.setInvState("2");// 状态改为已冲正
									preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
								}
							}
						}
						preinvApply2.setHandleState("2");// 可重新开票
						preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
					}else{
						List<PreinvApplyDet> listdet = preinvApplyService.findByUUID(preinvApply.getBatchNo());// 查询账户
						for (PreinvApplyDet preinvApplyDet : listdet) {
							preinvApplyDet.setInvState("2");// 状态改为已冲正
							preinvApplyService.updatePreinvApplyDet(preinvApplyDet);// 修改
						}
						PreinvApply preinvApply2 = preinvApplyService.getPreinvApplyByBachNo(preinvApply.getOldId());// 查询原订单
						List<PreinvApplyDet> listdets = preinvApplyService.findByUUID(preinvApply2.getBatchNo());// 查询账户
						for (PreinvApplyDet newApplyDet : listdet) {
							for (PreinvApplyDet oldApplyDet : listdets) {
								// 修改原订单的账户状态
								if (newApplyDet.getContrctNo().equals(oldApplyDet.getContrctNo())
										&& newApplyDet.getBeginCycle().equals(oldApplyDet.getBeginCycle())) {
									oldApplyDet.setInvState("2");// 状态改为已冲正
									preinvApplyService.updatePreinvApplyDet(oldApplyDet);// 修改
								}
							}
						}
						preinvApply2.setHandleState("2");// 可重新开票
						preinvApplyService.updatePreinvApply(preinvApply2);// 修改开票信息
					}
				}
				WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办任务
				if (wt != null) {
					service.updateWait(wt, this.getRequest());
				}else{
					Write("NO");
					throw new RuntimeException("事务回滚");
				}
				if(list.size()>0){
					if (preinvApply.getOprType().equals("1")) {
						preinvApply.setReverseSatate("7");
					}else{
						preinvApply.setStartState("7");
						preinvApply.setHandleState("2");// 可重新开票
					}
				}else{
					if (preinvApply.getOprType().equals("1")) {
						preinvApply.setReverseSatate("5");
					}else{
						preinvApply.setStartState("1");// 修改状态为已完成
						preinvApply.setHandleState("2");// 可重新开票
					}
				}
				preinvApply.setUpdateDate(new Date());
				preinvApplyService.updatePreinvApply(preinvApply);// 修改开票信息
				Write("YES");
			}else{
				Write("NO");
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");
		}
	}

	/**
	 * 查询账期信息
	 */
	public void findByInvNO() {
		try {
			String invNo = getString("invNo");
			List<Map<String, Object>> list = preinvApplyService.findByInvNo(invNo);
			JSONArray jsonArray = JSONArray.fromObject(list);
			Write(jsonArray.toString());
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
			throw new RuntimeException("事务回滚");

		}
	}

	/**
	 * 转发待办生成
	 */
	private void forwardWaitGenerate(PreinvApply preinvApply, String userId, SystemUser user, String type, String taskId, String processId) {
		WaitTask wt = new WaitTask();
		wt.setName("[预开票]" + preinvApply.getAppTitle());
		wt.setCreationTime(new Date());
		SystemUser handleUser = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
		wt.setUrl("jsp/preinvApply/completePreinvApplay.jsp?id=" + preinvApply.getUuid() + "&type=" + type + "&processId=" + processId);
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(handleUser.getRowNo());
		wt.setHandleUserName(handleUser.getEmployeeName()); // 处理人名称
		wt.setHandleLoginName(handleUser.getLoginName()); // 处理人登录名
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName()); // 创建人名称
		wt.setCreateLoginName(user.getLoginName()); // 创建人登录名
		wt.setCode(PreinvApply.PREINVAPPLY);
		wt.setTaskId(taskId);
		service.saveWait(wt, this.getRequest());

	}

	/**
	 * 提交待办生成
	 */
	private void submitWaitGenerate(PreinvApply preinvApply, String userid, String processId, String oldProcessId, SystemUser user,
			PreinvApplyTask preinvApplyTask) {
		WaitTask waitTask = new WaitTask();
		if (preinvApply.getOprType().equals("1")) {
			waitTask.setName("[预开票工单冲正]" + preinvApply.getAppTitle());// 待办名称
		} else {
			waitTask.setName("[预开票]" + preinvApply.getAppTitle());// 待办名称
		}
		waitTask.setCreationTime(new Date());// 代办生成时间
		waitTask.setUrl("jsp/preinvApply/handlePreinvApply.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId + "&billsTaskid="
				+ preinvApplyTask.getId() + "&oldProcessId=" + oldProcessId);
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
		waitTask.setState(WaitTask.HANDLE);// 状态为待处理
		waitTask.setHandleUserId(USER.getRowNo());// 处理人id
		waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
		waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
		waitTask.setCreateUserId(user.getRowNo());// 创建人id
		waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
		waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
		waitTask.setCode(PreinvApply.PREINVAPPLY);// 标识
		waitTask.setTaskId(preinvApplyTask.getId());

		service.saveWait(waitTask, this.getRequest());
	}

	/**
	 * 完成待办生成
	 */
	private void completeWaitGenerate(PreinvApply preinvApply, String userId, String processId, String oldProcessId, SystemUser user,
			PreinvApplyTask preinvApplyTask) {
		WaitTask waitTask = new WaitTask();
		if (preinvApply.getOprType().equals("1")) {
			waitTask.setName("[预开票工单冲正]" + preinvApply.getAppTitle());// 待办名称
		} else {
			waitTask.setName("[预开票]" + preinvApply.getAppTitle());// 待办名称
		}
		waitTask.setCreationTime(new Date());// 代办生成时间
		waitTask.setUrl("jsp/preinvApply/completePreinvApplay.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId + "&billsTaskid="
				+ preinvApplyTask.getId() + "&oldProcessId=" + oldProcessId);
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));// 获取下一步处理人信息
		waitTask.setState(WaitTask.HANDLE);// 状态为待处理
		waitTask.setHandleUserId(USER.getRowNo());// 处理人id
		waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
		waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
		waitTask.setCreateUserId(user.getRowNo());// 创建人id
		waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
		waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
		waitTask.setCode(PreinvApply.PREINVAPPLY);// 标识
		waitTask.setTaskId(preinvApplyTask.getId());

		service.saveWait(waitTask, this.getRequest());

	}

	/**
	 * 退回待办生成
	 */
	private void returnWaitGenerate(PreinvApply preinvApply, String userId, String processId, String oldProcessId, SystemUser user,
			PreinvApplyTask preinvApplyTask) {
		WaitTask waitTask = new WaitTask();
		waitTask.setCreationTime(new Date());// 代办生成时间
		if (preinvApply.getOprType().equals("1")) {
			waitTask.setName("[预开票工单冲正]" + preinvApply.getAppTitle());// 待办名称
			waitTask.setUrl("jsp/preinvApply/invalidReturnPreinvApply.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId
					+ "&preinvApplyTaskid=" + preinvApplyTask.getId() + "&oldProcessId=" + oldProcessId);
		} else {
			waitTask.setName("[预开票]" + preinvApply.getAppTitle());// 待办名称
			waitTask.setUrl("jsp/preinvApply/returnPreinvApply.jsp?id=" + preinvApply.getUuid() + "&processId=" + processId + "&preinvApplyTaskid="
					+ preinvApplyTask.getId());
		}
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));// 获取下一步处理人信息
		waitTask.setState(WaitTask.HANDLE);// 状态为待处理
		waitTask.setHandleUserId(USER.getRowNo());// 处理人id
		waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
		waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
		waitTask.setCreateUserId(user.getRowNo());// 创建人id
		waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
		waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
		waitTask.setCode(PreinvApply.PREINVAPPLY);// 标识
		waitTask.setTaskId(preinvApplyTask.getId());

		service.saveWait(waitTask, this.getRequest());
	}

}
