package com.xinxinsoft.action.appAction;

import com.google.gson.Gson;
import com.google.gson.GsonBuilder;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.action.contract.CustomClauseContractAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.contractUniformity.Con_OrderForm;
import com.xinxinsoft.entity.contractUniformity.ContractInfo;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.oms.*;
import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.entity.pms.PmsProductInfo;
import com.xinxinsoft.entity.pms.PmsProductLabel;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.NewGroupCustomerService;
import com.xinxinsoft.sendComms.OmsSellOrderService.OmsSellOrderSrv;
import com.xinxinsoft.sendComms.groupOrderService.GrpOrderIdAcceptSrv;
import com.xinxinsoft.sendComms.omsService.BusiOppService;
import com.xinxinsoft.sendComms.omsService.OrderEsbService;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.contract.CustomClauseContractService;
import com.xinxinsoft.service.core.user.StructureOfPersonnelService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.groupcustomer.GroupCustomerService;
import com.xinxinsoft.service.oms.OmsOrderProductService;
import com.xinxinsoft.service.oms.OmsOrderWorkbenchService;
import com.xinxinsoft.service.oms.OmsSellOrderService;
import com.xinxinsoft.service.smsPush.SmsPushService;
import com.xinxinsoft.service.v2.integrationService.IntegrationService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.HDFS.utils.HDFSUtils;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.net.URLDecoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.sql.Clob;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.xml.rpc.ParameterMode;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 销售工单（客户经理需求单）
 */
public class AppOmsSellOrderAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(AppOmsSellOrderAction.class);
    private OmsSellOrderService omsSellOrderService;
    private OmsOrderWorkbenchService omsOrderWorkbenchService;
    private OmsOrderProductService omsOrderProductService;
    private AttachmentService attachmentService;
    private Bpms_riskoff_service taskService;
    private WaitTaskService service;//待办
    private SystemUserService systemUserService;//系统人员工具
    private ClaimForFundsService claimForFundsService;
    @Resource(name = "CustomClauseContractService")
    private CustomClauseContractService customClauseContractService;
    @Resource(name = "GroupCustomerService")
    private GroupCustomerService groupCustomerService;//集团客户
    @Resource(name = "IntegrationService")
    private IntegrationService integrationService;
    @Resource(name = "StructureOfPersonnelService")
    private StructureOfPersonnelService structureOfPersonnelService;
    @Resource(name="smsPushService")
    private SmsPushService smsPushService;
    private File file1;
    private static Boolean isES = false;
    static {
        if("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())){
            isES = true;
        }
    }
    public OmsSellOrderService getOmsSellOrderService() {
        return omsSellOrderService;
    }

    public void setOmsSellOrderService(OmsSellOrderService omsSellOrderService) {
        this.omsSellOrderService = omsSellOrderService;
    }

    public OmsOrderWorkbenchService getOmsOrderWorkbenchService() {
        return omsOrderWorkbenchService;
    }

    public void setOmsOrderWorkbenchService(OmsOrderWorkbenchService omsOrderWorkbenchService) {
        this.omsOrderWorkbenchService = omsOrderWorkbenchService;
    }

    public OmsOrderProductService getOmsOrderProductService() {
        return omsOrderProductService;
    }

    public void setOmsOrderProductService(OmsOrderProductService omsOrderProductService) {
        this.omsOrderProductService = omsOrderProductService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public ClaimForFundsService getClaimForFundsService() {
        return claimForFundsService;
    }

    public void setClaimForFundsService(ClaimForFundsService claimForFundsService) {
        this.claimForFundsService = claimForFundsService;
    }

    public CustomClauseContractService getCustomClauseContractService() {
        return customClauseContractService;
    }

    public void setCustomClauseContractService(CustomClauseContractService customClauseContractService) {
        this.customClauseContractService = customClauseContractService;
    }

    public GroupCustomerService getGroupCustomerService() {
        return groupCustomerService;
    }

    public void setGroupCustomerService(GroupCustomerService groupCustomerService) {
        this.groupCustomerService = groupCustomerService;
    }

    public IntegrationService getIntegrationService() {
        return integrationService;
    }

    public void setIntegrationService(IntegrationService integrationService) {
        this.integrationService = integrationService;
    }

    public File getFile1() {
        return file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }
    /**
     * @Description: 工单手动推送超时预警
     * @return: void
     * @Author: TX
     * @Date: 2021/10/14 11:32
     */
    public void CountOmsTimeoutTemind(){
        Map<String,Object> map =new HashMap<String,Object>();
        try{
            String linkOrderNo = getString("linkOrderNo");
            List<Map<String,Object>> list = omsSellOrderService.QueryOrderByLinkStatus(linkOrderNo);
            Map<String,Object> obj;
            if (list.size()==1){
                obj = list.get(0);
            }else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","获取工单信息失败,工单信息异常,请联系管理员处理!");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            SystemUser usertwo = systemUserService.getByUserInfoRowNo(Integer.parseInt(obj.get("OPER_NO").toString()));
            if (usertwo.getMobile()==null||usertwo.getMobile().equals("")){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","环节处理人的电话为空,推送提醒信息失败");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            omsSellOrderService.saveddyjPush_0_0001(obj.get("CREATOR_NAME").toString(),obj.get("CREATOR_DATE").toString(),obj.get("TITLE").toString(),usertwo.getMobile());
            map.put("code",1);
            map.put("data","");
            map.put("msg","信息推送成功!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            map.put("code",-1);
            map.put("data","");
            map.put("msg","推送提醒信息失败,程序异常,请联系管理员处理!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }
    /**
     * @Description: 获取指定小时后的日期
     * @Param: [date:起始时间, hours:小时数]
     * @return: java.util.Date
     * @Author: TX
     * @Date: 2021/10/8 16:42
     */
    public Date getTargetDate(Date date,Integer hours){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //判断当前日期是否为周末,是的话获取后一天0点
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.set(Calendar. HOUR_OF_DAY , 0);
            calendar.set(Calendar. MINUTE , 0);
            calendar.set(Calendar. SECOND , 0);
            calendar.set(Calendar. MILLISECOND , 0);
            calendar.add(Calendar. DAY_OF_MONTH , 1);
        }
        //判断配置时间是否超过一天
        while (hours>=24){
            //当前时间加一天,并判断加一天后是否未周末,如果是继续加一天
            calendar.add(Calendar.DAY_OF_MONTH, +1);
            while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
                calendar.add(Calendar.DAY_OF_MONTH, +1);
            }
            hours-=24;
        }
        //未超过一天(24小时)直接加到当前时间的上,并判断加上后时间是否为周末,如果是再加一天
        calendar.add(Calendar.HOUR_OF_DAY, +hours);
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.add(Calendar.DAY_OF_MONTH, +1);
        }
        return calendar.getTime();
    }

    /**
     * 查询产品
     */
    public void getProduct(){
        Map<String,Object> map =new HashMap<>();
        try {
            String identification = getString("identification");
            List<Map<String,String>> list = omsSellOrderService.getProduct(identification);
            map.put("code",1);
            map.put("msg","查询成功");
            map.put("data",list);
        } catch (Exception e) {
            logger.error("查询产品异常",e.getMessage());
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","查询异常");
            map.put("data","");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * 查询子产品
     * 根据主产品的ID查询
     */
    public void getSubProducts(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String identification = getString("identification");
            List<Map<String,String>> list=new ArrayList<>();
            if("DGOODS003".equals(id)){
                list=omsSellOrderService.getPmsPriceinfo(identification);
                for(int i=0;i<list.size();i++){
                    list.get(i).put("PARENTID","DGOODS003");
                }
            }else{
                list=omsSellOrderService.getSubProducts(id,identification);
            }
            map.put("code",1);
            map.put("msg","查询成功");
            map.put("data",list);
        } catch (Exception e) {
            logger.error("查询子产品异常",e.getMessage());
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","查询异常");
            map.put("data","");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));

    }

    /**
     * 查询资费
     * 根据子产品的ID查询
     */
    public void getProdPrice(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String labelId = getString("labelId");
            String identification = getString("identification");
            if("DGOODS003".equals(labelId)){
                String name = URLDecoder.decode(id,"UTF-8");
                List<Map<String,String>> list=omsSellOrderService.getProdPmsPriceinfo(name,identification);
                map.put("code",1);
                map.put("msg","查询成功");
                map.put("data",list);
            }else{
                List<Map<String,String>> list=omsSellOrderService.getProdPriceInfo(id,labelId,identification);
                map.put("code",1);
                map.put("msg","查询成功");
                map.put("data",list);
            }

        } catch (Exception e) {
            logger.error("查询资费异常",e.getMessage());
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","查询异常");
            map.put("data","");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));

    }


    /**
     * 查询合同集合
     */
    public void queryContractInfoList(){
        Map<String,Object> map =new HashMap<>();
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String contractName = getString("contractName");
            String groupCode = getString("groupCode");
            String contractid = getString("contractid");
            String rowNo = getString("rowNo");
            String pageStr = omsSellOrderService.queryBossFormInfo(page,Integer.valueOf(rowNo),contractName,groupCode,contractid);
            map.put("code",1);
            map.put("msg","查询成功");
            map.put("data",pageStr);
        } catch (Exception e) {
            logger.error("查询合同集合异常",e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","查询异常");
            map.put("data","");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));

    }

    /**
     * 查询列表--手机在途列表
     * @return
     * 可删除
     */
    public void getOmsSellOrderList(){
        Map<String,Object> map =new HashMap<>();
        try {
            String orderNo=getString("orderNo");//工单编码
            String title=getString("title");//需求名称
            String unitId=getString("unitId");//集团280
            String userId=getString("userId");//用户ID
            String type=getString("type");//类型用户判断是否分配订单经理
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));// 获取下一步处理人信息
            PageRequest page = new PageRequest(getRequest());
            PageResponse response =omsSellOrderService.getAPPOmsSellOrderList(page,USER,orderNo,title,unitId,type);
            Collection<Map<String,Object>> list = response.getList();
            if(null != list && !list.isEmpty()){
                for (Map<String, Object> stringObjectMap : list) {
                    List<OmsOrderWorkbench> omsWorkbench = omsSellOrderService.getOmsOrderWorkbenchList(stringObjectMap.get("ORDER_NO").toString());
                    if(omsWorkbench.size() > 0){
                        for (OmsOrderWorkbench workbench : omsWorkbench) {
                            if(workbench.getState().equals("0") || workbench.getState().equals("2")){
                                stringObjectMap.put("isAllot","false");//判断是否已经完成
                                break;
                            }else{
                                stringObjectMap.put("isAllot","true");
                            }
                        }
                    }else{
                        stringObjectMap.put("isAllot","true");
                    }
                }
            }
            map.put("code",1);
            map.put("msg","查询成功");
            map.put("data",response);
        } catch (Exception e) {
            logger.error("查询资费异常",e.getMessage());
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","查询异常");
            map.put("data","");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));

    }

    /**
     * @author: liyang
     * @date: 2021/8/31 10:26
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 保存需求单
     */
    public void addOmsSellOrder(){
        Map<String,Object> mapJson =new HashMap<>();
        try {
            HttpServletResponse resp = getResponse();
            resp.setHeader("Access-Control-Allow-Origin", "*");
            resp.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
            resp.setHeader("Access-Control-Max-Age", "7200");
            resp.setHeader("Access-Control-Allow-Headers", "x-requested-with, Content-Type");
            resp.setHeader("Access-Control-Allow-Credentials", "true");
            String json = getString("json");//资费json
            String attachmentId = getString("attachmentId") == null?"":getString("attachmentId");// 附件id
            String attachmentUrl = getString("attachmentUrl")==null?"":getString("attachmentUrl");//附件地址
            String userid = getString("userId");// 登陆人ID
            String oldOrderNo = getString("orderId");//
            String orderType = getString("orderType");
            String isUrgent = getString("isUrgent");//是否加急
            String isSupport = getString("isSupport");//是否需要市综调中心
            String unitId = getString("unitId");
            Integer demandType = getInteger("demandType");
            Integer whetherManager = getInteger("whetherManager");
            String attId="";
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(userid));
            if (!StringUtils.isEmpty(attachmentId)) {
                //判断是否上传了附件,获取前台提交的附件Id；
                String[] fileId = attachmentId.split(",");
                if (fileId.length > 0) {
                    for (int i = 0; i < fileId.length; i++) {
                        String url = EncryptionUtils.decrypt(fileId[i]);
                        String[] fie = url.split("\\\\");
                        int s = fie.length;
                        String name = fie[s-1];
                        //获取毫秒数
                        Long time = System.currentTimeMillis();
                        String pixstr =FileUpload.getFilePix(name);
                        String endFileName=time+pixstr;
                        String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                        StorageCfg storageCfg= attachmentService.queryStorageCfg();
                        String endUrl=storageCfg.getFileName()+urlDate;
                        File headPath = new File(endUrl);//获取文件夹路径
                        if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                            headPath.mkdirs();
                        }
                        logger.info("这是预受理APP已存储文件地址："+FileUpload.getPaperConURL()+url);
                        logger.info("这是预受理APP需要移动的文件地址："+endUrl+endFileName);
                        try{
                            Files.move(Paths.get(FileUpload.getPaperConURL()+url),Paths.get(endUrl+endFileName),
                                    StandardCopyOption.REPLACE_EXISTING);
                        }catch (Exception e){
                            logger.error("第一次移动文件出错"+e.getMessage(),e);
                            mapJson.put("code",-1);
                            mapJson.put("data","");
                            mapJson.put("msg","文件移动错误："+e.getMessage());
                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                            return;
                        }
                        logger.info("移动文件成功");
                        Attachment attachmentEntity = new Attachment();
                        attachmentEntity.setAttachmentName(endFileName);// 防重名
                        attachmentEntity.setAttachmentUrl(urlDate+endFileName);
                        attachmentEntity.setUploadDate(new Date());
                        attachmentEntity.setRealName(endFileName);
                        attachmentEntity.setUploadUser(user);
                        attachmentEntity.setVersion(storageCfg.getId());
                        attId +=attachmentService.addEntity(attachmentEntity)+",";
                    }
                }
            }
            String fileId="";
            if (attachmentUrl.length()>1) {
                if (attachmentUrl != null) {
                    String[] urlJson = attachmentUrl.split("_");
                    if (urlJson.length > 0) {
                        for (int i = 0; i < urlJson.length; i++) {
                            String url = EncryptionUtils.decrypt(urlJson[i]);
                            String[] fie = url.split("\\\\");
                            int s = fie.length;
                            String name = fie[s-1];
                            //获取毫秒数
                            Long time = System.currentTimeMillis();
                            String pixstr = FileUpload.getFilePix(name);
                            String endFileName=time+pixstr;
                            String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                            StorageCfg storageCfg= attachmentService.queryStorageCfg();
                            String endUrl=storageCfg.getFileName()+urlDate;
                            File headPath = new File(endUrl);//获取文件夹路径
                            if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                                headPath.mkdirs();
                            }
                            logger.info("这是预受理APP已存储文件地址二次："+FileUpload.getPaperConURL()+url);
                            logger.info("这是预受理APP需要移动的文件地址二次："+endUrl+endFileName);
                            try{
                                Files.move(Paths.get(FileUpload.getPaperConURL()+url),Paths.get(endUrl+endFileName),
                                        StandardCopyOption.REPLACE_EXISTING);
                            }catch (Exception e){
                                logger.error("第二次移动文件出错"+e.getMessage(),e);
                                mapJson.put("code",-1);
                                mapJson.put("data","");
                                mapJson.put("msg","二次文件移动错误："+e.getMessage());
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                                return;
                            }
                            logger.info("移动文件成功");
                            Attachment attachmentEntity = new Attachment();
                            attachmentEntity.setAttachmentName(endFileName);// 防重名
                            attachmentEntity.setAttachmentUrl(urlDate+endFileName);
                            attachmentEntity.setUploadDate(new Date());
                            attachmentEntity.setRealName(endFileName);
                            attachmentEntity.setUploadUser(user);
                            attachmentEntity.setVersion(storageCfg.getId());
                            fileId +=attachmentService.addEntity(attachmentEntity)+",";
                        }
                    }
                }
            }
            List<Map<String,String>> userMap=omsSellOrderService.getVwUserinfoByRowno(String.valueOf(user.getRowNo()));
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String pmsinfoName="";
            Integer score=0;
            String orderNo = IBM + Bpms_riskoff_service.getUnlockedNumber();
            String unitName = getString("unitName");
            logger.info("集团名称："+getString("unitName"));
            if (!"".equals(json) && json != null && json.length() > 0) {
                JSONArray jsonArray = JSONArray.fromObject(json);
                for (int i = 0; i < jsonArray.size(); i++) {
                    String jsonArrayStr = jsonArray.getString(i);
                    JSONObject obj = JSONObject.fromObject(jsonArrayStr);
                    PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(obj.getString("prodPriceNo"));
                    pmsinfoName += pmsinfo.getPrcName()+",";
                }
                String str =  jsonArray.getString(0);
                JSONObject obj = JSONObject.fromObject(str);
                PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(obj.getString("prodPriceNo"));
                if(jsonArray.size()>0){
                    pmsinfoName=pmsinfo.getPrcName()+"...";
                }else{
                    pmsinfoName=pmsinfo.getPrcName();
                }
            }
            pmsinfoName = pmsinfoName.substring(0,pmsinfoName.length()-1);
            String title = ""+unitName+"关于（"+pmsinfoName+"）需求单";
            if("NEW".equals(orderType)){//是复制的需求单，先作废需求单，在增加
                OmsSellOrder order = omsSellOrderService.getOmsSellOrderByOrderNo(oldOrderNo);
                if(!StringUtils.isEmpty(attachmentId)){
                    List<SingleAndAttachment> singleAndAttachmentList = omsSellOrderService.getSingleAndAttachmentList(order.getId());
                    for (SingleAndAttachment singleAndAttachment : singleAndAttachmentList) {
                        attachmentId += singleAndAttachment.getAttachmentId() + ",";
                    }
                }
            }
            StringBuilder prcName= new StringBuilder();
            StringBuilder prodName= new StringBuilder();
            StringBuilder labelName= new StringBuilder();
            /**
             *集团280：unitId
             *合同ID :contractId
             *是否建档:isUnit（0->否；1->是）
             *需求名称:title
             *完成时间：endTime
             *备注：memo
             *需求描述：description
             * 客户联系人名字：customerName
             * 客户联系人号码：customerPhone
             * 附件地址：attachmentUrl
             * 附件ID：attachmentId
             * 资费json：json(json数组对应的字段：资费ID：prodPriceNo；优惠金额 :amount；数量:number；安装地址:installationAddress；折扣：discount)
             */
            if (!"".equals(json) && json != null && json.length() > 0) {
                JSONArray jsonArray = JSONArray.fromObject(json);
                for (int i = 0; i < jsonArray.size(); i++) {
                    OmsOrderProduct omsProduct = new OmsOrderProduct();
                    String jsonArrayStr = jsonArray.getString(i);
                    JSONObject obj = JSONObject.fromObject(jsonArrayStr);
                    PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(obj.getString("prodPriceNo"));
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    PmsProductLabel pmslabel = omsSellOrderService.queryPmsProductLabel(pmsproduct.getLabelId());
                    String moneyNo = orderNo + i;
                    pmsinfoName += pmsinfo.getPrcName()+",";
                    score += pmslabel.getScore();
                    omsProduct.setPrcName(pmsinfo.getPrcName());
                    omsProduct.setSerialNumber(moneyNo);
                    omsProduct.setOrderNo(orderNo);
                    omsProduct.setLabelNo(pmslabel.getLabelId());
                    omsProduct.setProdNo(pmsproduct.getProdId());
                    omsProduct.setPrcNo(pmsinfo.getPrcId());
                    omsProduct.setIsCont(pmsproduct.getConType());
                    if(obj.containsKey("installationAddress")){
                        omsProduct.setInstallationAddress(obj.getString("installationAddress"));
                    }

                    omsProduct.setScore(pmslabel.getScore());
                    if(obj.containsKey("temlates")){
                        omsProduct.setTemlates(obj.getJSONArray("temlates").toString());
                    }
                    omsProduct.setDescription(obj.getString("description"));
                    omsProduct.setDiscount(obj.getString("discount"));
                    omsProduct.setHandleNo(String.valueOf(user.getRowNo()));
                    omsProduct.setHandleName(user.getEmployeeName());
                    omsProduct.setState(OmsOrderProduct.STATE_WAITING);
                    omsProduct.setHandleDate(new Date());
                    omsProduct.setScore(pmslabel.getScore());
                    if("1".equals(pmsinfo.getIsPushBoss())){
                        Result result= GrpOrderIdAcceptSrv.getInstance().createGrpOrder(unitId,moneyNo,"D001",
                                pmsinfo.getPrcName(),
                                user.getBossUserName(),
                                String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", new Date()),pmslabel.getSubLabelType());
                        if(ResultCode.SUCCESS.code()==result.getCode()){
                            JSONObject root=JSONObject.fromObject(JSONObject.fromObject(result.getData()).get("ROOT"));
                            JSONObject body=JSONObject.fromObject(root.get("BODY"));
                            if("0".equals(body.get("RETURN_CODE"))) {
                                JSONObject outData = JSONObject.fromObject(JSONObject.fromObject(root.get("BODY")).get("OUT_DATA"));
                                omsProduct.setGrpOrdId(outData.get("grpOrdId").toString());
                                omsProduct.setIsPushBoss(0);
                            }else{
                                mapJson.put("code", -1);
                                mapJson.put("data", "");
                                mapJson.put("msg", "资费："+pmsinfo.getPrcName()+"注册统一ID失败" + body.get("RETURN_MSG"));
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                                return;
                            }
                        }else {
                            mapJson.put("code", -1);
                            mapJson.put("data", "");
                            mapJson.put("msg", "资费："+pmsinfo.getPrcName()+"注册统一ID失败" + result.getMessage());
                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                            return;
                        }
                    }
                    omsSellOrderService.saveOrupdateOmsOrderProduct(omsProduct);
                    prcName.append(pmsinfo.getPrcName()).append("|");
                    prodName.append(pmsproduct.getProdName()).append("|");
                    labelName.append(pmslabel.getLabelName()).append("|");
                }
            }
            OmsSellOrder oms = new OmsSellOrder();
            if (unitId != null && !"".equals(unitId) && !"undefined".equals(unitId)) {
                oms.setUnitId(unitId);//集团280
                oms.setUnitName(unitName);//集团名称
            }
            if (getString("contractId") != null && !"".equals(getString("contractId")) && !"undefined".equals(getString("contractId"))) {
                oms.setContractId(getString("contractId"));//合同ID
                //关联中间表
                ContractInfo contractInfo= omsSellOrderService.queryContractInfo(getString("contractId"));
                Con_OrderForm cof = new Con_OrderForm();
                cof.setContractId(contractInfo.getContractId());
                cof.setFormNo(orderNo);
                cof.setFormTableName(OmsSellOrder.OMSSELLORDER);
                cof.setState(0);
                omsSellOrderService.saveOrUpdateCon_OrderForm(cof);
            }
            oms.setDemandType(demandType);//需求类型
            oms.setOrderNo(orderNo);
            oms.setIsUnit(getString("isUnit"));//是否为建档集团
            oms.setTitle(title);//需求标题
            oms.setCompanyNo(userMap.get(0).get("COMPANY_CODE"));
            oms.setPrcName(prcName.toString());//资费名称
            oms.setProdName(prodName.toString());//产品名称
            oms.setLabelName(labelName.toString());//产品大类名称
            oms.setCreateDate(new Date());
            oms.setExpectDate(strToDate(plusDay2(score)));
            oms.setDeleteState(OmsSellOrder.DELETE_FALSE);
            oms.setState(OmsSellOrder.TATE_WAITING);
            oms.setIsUrgent(isUrgent);
            oms.setIsSupport(isSupport);
            oms.setScore(score);
            oms.setMemo(getString("memo"));
            oms.setCustomerName(getString("customerName"));//工单对应下集团280中对应的客户联系人名字
            oms.setCustomerPhone(getString("customerPhone"));//工单对应下集团280中对应的客户联系人电话
            oms.setCompanyName(userMap.get(0).get("COMPANY_NAME"));
            oms.setCountyName(userMap.get(0).get("COUNTY_NAME"));
            oms.setCountyNo(userMap.get(0).get("COUNTY_NO"));
            oms.setVersionNumber(2);
            oms.setWhetherManager(whetherManager);
            Map<String,String> map = new HashMap<>();
            oms.setCreateName(user.getEmployeeName());
            oms.setCreateNo(String.valueOf(user.getRowNo()));
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("1","工单生成",oms.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return ;
                }
            }
            List<OmsOrderLink> existence1 =omsSellOrderService.getOmsOrderLinkByCode("2","工单派发",oms.getOrderNo());
            if(existence1!=null) {
                if (existence1.size() > 0) {
                    mapJson.put("code", -1);
                    mapJson.put("data", "");
                    mapJson.put("msg", "当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return;
                }
            }
            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"1");
            OmsOrderLink link = new OmsOrderLink();
            link.setCreator_name(user.getEmployeeName());//发起人
            link.setCreator_no(user.getRowNo());//发起人工号
            link.setCreator_date(new Date());//发起人时间(当前时间)
            link.setOper_name(user.getEmployeeName());//操作人
            link.setOper_no(user.getRowNo());//操作人工号
            link.setOper_date(new Date());//操作时间(当前时间)
            link.setStatus(1);//状态(状态根据环节确定)
            link.setLinkCode("1");//环节编码或者固定的环节编码
            link.setLinkName("工单生成");//环节名称
            link.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
            link.setLinkOrderNo(IBM + taskService.getNumber());
            link.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            if(whetherManager==0){//是自行审批
                int arry[]={2,3,4};
                for(int i=0;i<arry.length;i++){
                    String linkCode="";
                    String linkName="";
                    Integer status=0;
                    if(arry[i]==2){
                        linkCode=String.valueOf(arry[i]);
                        linkName="工单派发";
                        status=1;
                    }else if(arry[i]==3){
                        linkCode=String.valueOf(arry[i]);
                        linkName="工单确认";
                        status=1;
                    }else if(arry[i]==4){
                        linkCode=String.valueOf(arry[i]);
                        linkName="工单审批";
                        status=0;
                    }
                    OmsPretreatmentDate PretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),linkCode);
                    OmsOrderLink nextStepLink = new OmsOrderLink();
                    nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                    nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                    nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                    nextStepLink.setOper_name(user.getEmployeeName());//操作人
                    nextStepLink.setOper_no(user.getRowNo());//操作人工号
                    nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                    nextStepLink.setStatus(status);//状态(状态根据环节确定)
                    nextStepLink.setLinkCode(linkCode);//环节编码或者固定的环节编码
                    nextStepLink.setLinkName(linkName);//环节名称
                    nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                    nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                    nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(PretreatmentDate.getPretreatment_date())));
                    omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                    if(arry[i]==4){
                        oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                    }
                }
            }else{
                OmsPretreatmentDate pretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"2");
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name("");//操作人
                nextStepLink.setOper_no(0);//操作人工号
                nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                nextStepLink.setStatus(-1);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("2");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("工单派发");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(pretreatmentDate.getPretreatment_date())));
                oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
            }
            OmsSellOrder omsr= omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            if(whetherManager==0){
                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(user.getEmployeeName());
                dig.setOper_no(user.getRowNo());
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(omsr.getLinkOrderNo());
                dig.setOper_role("ROLE_CUMR");//处理角色客户经理
                dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
                OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                commitOmsSellOrderData(omsr, user.getRowNo(), user, "", rdig.getId());
            }
            if(omsr==null){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","需求单创建失败！写入参数失败，请联系管理员");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }else{
                boolean bl = getProductIsContract(omsr);
                if(bl){
                    omsr.setIsContract("Y");
                }else{
                    omsr.setIsContract("N");
                }
                omsSellOrderService.saveOrupdateOmsSellOrder(omsr);
                if(attId.length()>0){
                    String[] attIdArray =attId.split(",");
                    if(attIdArray.length>0){
                        for(int j=0;j<attIdArray.length;j++){
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(omsr.getId());
                            sa.setAttachmentId(attIdArray[j]);
                            sa.setLink(OmsSellOrder.OMSSELLORDER);
                            claimForFundsService.saveSandA(sa);
                        }
                    }
                }
                if(fileId.length()>0){
                    String[] fileIdArray =fileId.split(",");
                    if(fileIdArray.length>0) {
                        for (int j = 0; j < fileIdArray.length; j++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(omsr.getId());
                            sa.setAttachmentId(fileIdArray[j]);
                            sa.setLink(OmsSellOrder.OMSSELLORDER);
                            claimForFundsService.saveSandA(sa);
                        }
                    }
                }
                mapJson.put("code",1);
                mapJson.put("data",orderNo);
                mapJson.put("msg","需求单创建成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("APP创建需求单异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","需求单提交失败!"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 11:16
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 查询需求数据生成工作台
     */
    public void queryOmsSellOrder(){
        try {
            String id = getString("id");//需求单ID
            Map<String, Object> map = new HashMap<>();
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            List<OmsOrderLink> links = omsSellOrderService.getOmsOrderLinkByOrderNumberList(oms.getOrderNo());
            JSONArray linkArry=new JSONArray();
            for(int i=0;i<links.size();i++){
                JSONObject obj = JSONObject.fromObject(JSONHelper.SerializeWithNeedAnnotationDateFormat(links.get(i)));
                List<OmsLinkDialogue> digList = omsSellOrderService.getOmsLinkDialogueByLinkOrderNoList(links.get(i).getLinkOrderNo());
                obj.put("omslinkdialogueList",JSONHelper.SerializeWithNeedAnnotationDateFormat(digList));
                linkArry.add(obj);
            }
            List<OmsOrderProduct> omsProduct = omsSellOrderService.getOmsOrderProductList(oms.getOrderNo());
            if(linkArry.size()>0){
                map.put("OmsOrderLink", linkArry);
            }else{
                map.put("OmsOrderLink", "");
            }
            if (oms.getContractId() != null) {
                ContractInfo contract = omsSellOrderService.queryContractInfo(oms.getContractId());
                map.put("ContractInfo", contract);
            } else {
                map.put("ContractInfo", "");
            }
            if (oms.getUnitId() != null) {
                GroupCustomer group = omsSellOrderService.getGroupCustomer(oms.getUnitId());
                map.put("GroupCustomer", group);
            } else {
                map.put("GroupCustomer", "");
            }
            List<OmsOrderWorkbench> omsWorkbench = omsSellOrderService.getOmsOrderWorkbenchList(oms.getOrderNo());
            if (omsWorkbench.size() > 0) {
                List<Object> list = new ArrayList();
                for(int i=0;i<omsWorkbench.size();i++){
                    Map<String,Object> omsMap = new HashMap<>();
                    Map<String,String> mapUserName=omsSellOrderService.getApprovalUserList(omsWorkbench.get(i).getId(),"SH");
                    omsMap.put("approvalUser",mapUserName.get("SPUSER"));
                    omsMap.put("omsOrderWorkbench",omsWorkbench.get(i));
                    List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(omsWorkbench.get(i).getId());//任务信息
                    if(null ==taskList || taskList.size() == 0){
                        omsMap.put("taskList","");
                    }else{
                        omsMap.put("taskList",taskList);
                    }
                    list.add(omsMap);
                }
                map.put("omsWorkbenchList", list);
            } else {
                map.put("omsWorkbenchList", "");
            }
            List<FollowUpOrder> followUpOrderList = omsSellOrderService.getFollowUpOrderByOrderNo(oms.getOrderNo());
            if(null != followUpOrderList && !followUpOrderList.isEmpty()){
                map.put("followUpOrderList", followUpOrderList);
            }else{
                map.put("followUpOrderList", "");
            }
            List<Map<String, Object>> maps = omsSellOrderService.selectListAtt(oms.getId(), OmsSellOrder.OMSSELLORDER);
            if(null != maps && !maps.isEmpty()){
                map.put("attList", maps);
            }else{
                map.put("attList", "");
            }
            map.put("OmsSellOrder", oms);
            map.put("OmsOrderProductList", omsProduct);
            map.put("code", 0);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询需求数据生成工作台异常"+e.getMessage(),e);
            Map<String, Object> map = new HashMap<>();
            map.put("ContractInfo", "");//合同信息
            map.put("GroupCustomer", "");//集团信息
            map.put("omsWorkbenchList", "");//工作台审批信息
            map.put("OmsSellOrder", "");//需求单信息
            map.put("OmsOrderProductList", "");//资费信息
            map.put("OmsOrderLink", "");//环节信息
            map.put("msg", "查询错误");
            map.put("code", -1);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }

    }


    public static Date strToDate(String str) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Date date = null;
        try {
            date = format.parse(str);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return date;
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:14
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批查询详情信息
     */
    public void getOmsSellOrderById(){
        String workbenchId = getString("workbenchId");
        String status = getString("status");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(workbenchId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Map<String,Object> resultMap=omsSellOrderService.getOmsOrderWorkbenchAndBpms_riskoff_taskByOmsSellOrderId(workbenchId);
            if(null == resultMap){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到数据");
            }else{
                String processSign = (String)resultMap.get("PROCESS_SIGN");
                if(null != processSign && !"".equals(processSign) && !"null".equals(processSign)){
                    List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(workbenchId);//任务信息
                    resultMap.put("list",taskList);
                }
                String contractId = (String)resultMap.get("ORER_CONTRACT_ID");
                ContractInfo contractInfo= omsSellOrderService.queryContractInfo(contractId);//合同信息
                String orderNo = (String)resultMap.get("ORDER_NO");
                List<OmsOrderProduct> omsProduct = omsSellOrderService.getOmsOrderProductList(orderNo);//资费信息
                List<FollowUpOrder> followUpOrderList = omsSellOrderService.getFollowUpOrderByOrderNo(orderNo);
                if(null != followUpOrderList && !followUpOrderList.isEmpty()){
                    resultMap.put("followUpOrderList", followUpOrderList);
                }else{
                    resultMap.put("followUpOrderList", "");
                }
                String orderId = (String)resultMap.get("ORDER_ID");
                List<Map<String, Object>> maps = omsSellOrderService.selectListAtt(orderId, OmsSellOrder.OMSSELLORDER);
                if(null != maps && !maps.isEmpty()){
                    resultMap.put("attList", maps);
                }else{
                    resultMap.put("attList", "");
                }
                String memoHtmlStr = ClobToString((Clob) resultMap.get("MEMO_HTML"));
                String memoStr = ClobToString((Clob) resultMap.get("MEMO"));

                resultMap.remove("MEMO_HTML");
                resultMap.remove("MEMO");

                resultMap.put("MEMO_HTML",memoHtmlStr);
                resultMap.put("MEMO",memoStr);
                resultMap.put("contractInfo",contractInfo);
                resultMap.put("omsProductList",omsProduct);
                Object objId = resultMap.get("PRC_ID");
                String prcName= "";
                /*if(null != objId && !objId.equals("")){
                    JSONArray products = JSONArray.fromObject(objId);
                    if(products.size() > 0){//判断是否有抄送人或者审核人
                        for (int i = 0; i < products.size(); i++) {//循环增加抄送或审核任务数据
                            Object obj = products.get(i);
                            String proId = obj.toString();
                            PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(proId);
                            PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId());
                            prcName += pmsproduct.getProdName()+"("+pmsinfo.getPrcName()+")，";
                        }
                        prcName = prcName.substring(0, prcName.length() - 1);
                    }
                }*/
                resultMap.put("appendProduct",prcName);
                map.put("code",1);
                map.put("data",resultMap);
                map.put("msg","");
            }
        } catch (Exception e) {
            logger.error("审批数据查询异常："+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:17
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批通过保存数据 转审他人保存数据
     */
    public void saveBpms_riskoff_task(){
        String workbenchId = getString("workbenchId");
        String taskId = getString("taskId");
        String desc = getString("desc");
        //Integer status = getInteger("status");//页面传值为2
        String type = getString("type");
        String jsonUser = getString("jsonUser");
        String loginUserId = getString("userId");
        OmsOrderWorkbench work =omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
        logger.info("审批单状态："+work.getState());
        OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(work.getOrderNo());
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(workbenchId) || !StringHelper.isBlank(taskId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            //修改当前人的任务数据状态
            Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task(desc, 2, taskId);
            if(null == bpms_riskoff_task){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            //联表查询工作台流程表数据
            Map<String,Object> resultMap=omsSellOrderService.getOmsOrderWorkbenchAndBpms_riskoff_taskByOmsSellOrderId(workbenchId);
            if(null == resultMap){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到工作台或工单数据或流程数据，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(loginUserId));
            String processId = (String)resultMap.get("PROCESS_SIGN");
            String title = (String)resultMap.get("TITLE");
            String orderTitle = (String)resultMap.get("ORDER_TITLE");
            Object obj = resultMap.get("STATUS");//流程表状态
            String pstatus = obj.toString();
            JSONArray jsonArray = null;
            if(null != jsonUser && !"".equals(jsonUser)){
                jsonArray = JSONArray.fromObject(jsonUser);
            }
            String typeName = "";
            if(type.equals("CS")){
                typeName = "抄送";
            }else{
                typeName = "审核";
            }
            if(null != jsonArray && jsonArray.size() > 0){//判断是否有抄送人或者审核人
                for (int i = 0; i < jsonArray.size(); i++) {//循环增加抄送或审核任务数据
                    JSONObject jsonObject = jsonArray.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String id = taskService.setBpms_riskoff_taskSaveAll(processId,"",1,type,"预受理发起"+typeName,userId,user,
                    (Integer.parseInt(bpms_riskoff_task.getBak1())+1)+"",bpms_riskoff_task.getId(),null);
                    String url = "jsp/demandOrder/approvalShow.jsp?workbenchId=" + workbenchId+ "&taskId=" +id ;
                    commitBackLogData(id,"["+oms.getUnitName()+"]"+title,userId,user,url);//给转审人发起代办
                }
            }
            if(type.equals("CS")){//如果是抄送，判断是否还有审核数据
                List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskListByStatus(processId, "1","SH");//查询是否还有审批数据
                if(taskList.size() == 0){//无审核数据修改状态
                    String operateNo = (String)resultMap.get("OPERATE_NO");//工作台发起人ID
                    String id = taskService.setBpms_riskoff_taskSaveAll(processId,"",1,"SH","预受理发起(工单审核完成待办任务)",Integer.valueOf(operateNo),user,
                            (Integer.parseInt(bpms_riskoff_task.getBak1())+1)+"",bpms_riskoff_task.getId(),"2");
                    String url = "jsp/demandOrder/approvalShow.jsp?workbenchId=" + workbenchId+ "&taskId=" +
                            id +"&orderType=0";
                    commitBackLogData(id,"["+oms.getUnitName()+"]"+title,Integer.valueOf(operateNo),user,url);//工单审核完成发给发起人代办+"（工单审核完成）"
                }
            }
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,taskId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            if(type.equals("CS")){
                map.put("code",1);
                map.put("data","");
                map.put("msg","审批成功");
            }else{
                map.put("code",1);
                map.put("data","");
                map.put("msg","转审成功");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("审批/转审异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批工单被驳回
     */
    public void saveOverrule(){
        String workbenchId = getString("workbenchId");
        String desc = getString("desc");
        String taskId = getString("taskId");
        String loginUserId = getString("userId");
        String rejectUserId = getString("rejectUserId");//驳回人员ID
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(workbenchId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task(desc, 0, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            Bpms_riskoff_process bpms_riskoff_process = taskService.getbpms_riskoff_processBizid(workbenchId);
            OmsOrderWorkbench omsOrderWorkbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            //查询未处理的任务表中数据
            String bak="1";//判断为1的话就是审批的人，为0的时候就是发起人
            if(rejectUserId.equals(omsOrderWorkbench.getOperateNo())){
                bak="0";
            }
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(loginUserId));
            String id = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"SH","预受理发起(工单审核驳回待办任务)",Integer.valueOf(rejectUserId),user,
                    String.valueOf(Integer.parseInt(bpms_riskoff_task.getBak1())+1),bpms_riskoff_task.getId(),bak);
            String url = "jsp/demandOrder/approvalShow.jsp?workbenchId=" + workbenchId+ "&taskId=" +
                    id +"&orderNo="+omsOrderWorkbench.getOrderNo();
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(omsOrderWorkbench.getOrderNo());
            commitBackLogData(id,"["+omsSellOrder.getUnitName()+"]"+omsOrderWorkbench.getTitle(),Integer.valueOf(rejectUserId),user,url);//审核驳回发起代办+"（审核驳回）"
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,taskId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","驳回成功");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("审批否决异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:20
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 回复审批
     */
    public void replyOrder(){
        String workbenchId = getString("workbenchId");
        String desc = getString("desc");
        String taskId = getString("taskId");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(workbenchId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Bpms_riskoff_task bpms_riskoff_task =taskService.updateBpms_riskoff_task(desc, 2, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            SystemUser user = systemUserService.getByUserInfoRowNo(bpms_riskoff_task.getOper_no());
            Bpms_riskoff_process bpms_riskoff_process = taskService.getbpms_riskoff_processBizid(workbenchId);
            OmsOrderWorkbench omsOrderWorkbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            String id = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"SH","预受理发起",Integer.valueOf(bpms_riskoff_task.getCreator_no()),user,
                    String.valueOf(Integer.parseInt(bpms_riskoff_task.getBak1())+1),bpms_riskoff_task.getId(),null);
            String url = "jsp/demandOrder/approvalShow.jsp?workbenchId=" + workbenchId+"&taskId=" +id;
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(omsOrderWorkbench.getOrderNo());
            commitBackLogData(id,"["+omsSellOrder.getUnitName()+"]"+omsOrderWorkbench.getTitle(),Integer.valueOf(bpms_riskoff_task.getCreator_no()),user,url);//审核驳回发起代办+"（审核驳回）"
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,taskId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","回复成功");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("审批否决异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","回复异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批完成客户经理结束和抄送审阅结束
     */
    public void reviewBpms_riskoff_task(){
        String taskId = getString("taskId");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(taskId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            taskService.updateBpms_riskoff_task("已阅", 2, taskId);//修改本条数据
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,taskId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","已审阅");
        } catch (Exception e) {
            logger.error("审阅异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/12/27 14:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 审批完成客户经理结归档
     */
    public void orderArchiving(){
        String taskId = getString("taskId");
        String workbenchId = getString("workbenchId");
        OmsOrderWorkbench work =omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
        OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(work.getOrderNo());
        Map<String,Object> map =new HashMap<>();
        if("0".equals(work.getBak1())&&oms.getOperateNo()==null){
            map.put("code",-1);
            map.put("msg","当前为无协议报建审批,自动推送业务办理环节，但是未找到订单经理");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        if(!StringHelper.isBlank(taskId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            return ;
        }
        try {
            Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task("已归档", 2, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                map.put("code",-1);
                map.put("data","");
                map.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            omsOrderWorkbenchService.updateOmsOrderWorkbench(workbenchId,1);
            taskService.updatebpmsRiskoffProcess(workbenchId,2);
            String msg="已归档";
            if("0".equals(work.getBak1())){
                SystemUser cruser = systemUserService.getUserInfoRowNo(Integer.parseInt(work.getOperateNo()));// 获取下一步处理人信息
                String IBM = "";
                List<Object[]> sone = taskService.getCompayIBM(cruser.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("7","业务办理",oms.getOrderNo());
                if(existence!=null){
                    if(existence.size()>0){
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return ;
                    }
                }

                OmsOrderLink link =omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                List<OmsLinkDialogue> odgList = omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
                for(OmsLinkDialogue odg: odgList){
                    odg.setStatus(1);
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                    WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,odg.getId());//获取待办信息
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","未查询到待办信息,请联系管理员核对");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return ;
                    }
                }
                link.setOper_date(new Date());
                link.setOper_name(work.getOperateName());
                link.setOper_no(Integer.parseInt(work.getOperateNo()));
                link.setStatus(1);
                omsSellOrderService.saveOrupdateOmsOrderLink(link);

                OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"7");
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(work.getOperateName());//发起人
                nextStepLink.setCreator_no(Integer.parseInt(work.getOperateNo()));//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name(oms.getOperateName());//操作人
                nextStepLink.setOper_no(Integer.parseInt(oms.getOperateNo()));//操作人工号
                nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                nextStepLink.setStatus(0);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("7");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("业务办理");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);

                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(oms.getCreateNo()));// 获取下一步处理人信息
                OmsLinkDialogue digTwo = new OmsLinkDialogue();
                digTwo.setCreator_name(oms.getCreateName());
                digTwo.setCreator_no(Integer.parseInt(oms.getCreateNo()));
                digTwo.setCreator_date(new Date());
                digTwo.setOper_name(oms.getCreateName());
                digTwo.setOper_no(Integer.parseInt(oms.getCreateNo()));
                digTwo.setOper_date(new Date());
                digTwo.setStatus(0);//1已处理，0未处理
                digTwo.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                digTwo.setOper_role("ROLE_CUMR");//处理角色客户经理
                digTwo.setCreator_role("ROLE_ODMR");//发起角色订单经理
                OmsLinkDialogue rdigTwo = omsSellOrderService.saveOrupdateOmsLinkDialogue(digTwo);
                commitOmsSellOrderData(oms, Integer.parseInt(oms.getCreateNo()), USER, "", rdigTwo.getId());

                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(oms.getCreateName());
                dig.setCreator_no(Integer.parseInt(oms.getCreateNo()));
                dig.setCreator_date(new Date());
                dig.setOper_name(oms.getOperateName());
                dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig= omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);

                commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),USER,"",rdig.getId());//提交代办
                msg="归档完成，当前审批为无协议报建，归档以后会到业务办理环节，悉知！";
            }

            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,taskId);//获取待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息,请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg",msg);
        } catch (Exception e) {
            logger.error("审阅异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","归档异常");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * 提交待办生成
     *
     */
    public void commitBackLogData(String taskId, String title,Integer userid,SystemUser user, String url) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + title);//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl(url);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     *  需求单生成待办给订单经理
     * @param order 需求单
     * @param userid 分配人员ID
     */
    public void commitOmsSellOrderData(OmsSellOrder order,Integer userid,SystemUser user,String isContract,String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + order.getTitle());//待办名称
        waitTask.setCreationTime(new Date());//代办生成时间
        waitTask.setUrl("jsp/demandOrder/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 需求单和合同关联
     * 若有新上传合同附件则上传合同附件，到hadoop
     */

    public void correlationOmsSellOrderAndContractInfo(){
        Map<String,Object> map = new HashMap<>();
        try {
            String omsSellOrderId = getString("omsSellOrderId");
            String attachmentIds = getString("attachmentIds");
            String contractInfoId = getString("contractInfoId");
            String trueRatio = getString("trueRatio");
            String dialogueLinkId = getString("dialogueLinkId");//添加参数
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(omsSellOrderId);//合同ID和需求单关联
            if(oms.getOperateNo()==null){
                map.put("code",-1);
                map.put("msg","当前需求单未选择订单经理，暂时不能关联合同");
                map.put("data","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,dialogueLinkId);//根据待办id查询待办信息
            OmsLinkDialogue odg=omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(odg.getLinkOrderNo());
            ContractInfo contractInfo =  customClauseContractService.queryContractInfo(contractInfoId);//获取合同
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(oms.getCreateNo()));
            if(null == contractInfo){
                map.put("code",-1);
                map.put("msg","合同获取失败");
                map.put("data","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            oms.setContractId(contractInfo.getId());
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            //关联中间表
            Con_OrderForm cof = new Con_OrderForm();
            cof.setContractId(contractInfo.getContractId());
            cof.setFormNo(oms.getOrderNo());
            cof.setFormTableName(OmsSellOrder.OMSSELLORDER);
            cof.setState(0);
            omsSellOrderService.saveOrUpdateCon_OrderForm(cof);
            if(contractInfo.getContractType().equals("PRC")){
                contractInfo.setSTATE(4);
                omsSellOrderService.saveOrupdateContractInfo(contractInfo);
            }
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            if("5".equals(link.getLinkCode())&&"工单合同关联".equals(link.getLinkName())){
                List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("6","工单合同确认",oms.getOrderNo());
                if(existence==null||existence.size()==0) {
                    OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"6");
                    OmsOrderLink nextStepLink = new OmsOrderLink();
                    nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                    nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                    nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                    nextStepLink.setOper_name(oms.getOperateName());//操作人
                    nextStepLink.setOper_no(Integer.parseInt(oms.getOperateNo()));//操作人工号
                    nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                    nextStepLink.setStatus(0);//状态(状态根据环节确定)
                    nextStepLink.setLinkCode("6");//环节编码或者固定的环节编码
                    nextStepLink.setLinkName("工单合同确认");//环节名称
                    nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                    nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                    nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                    OmsOrderLink nextLink= omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                    oms.setLinkOrderNo(nextLink.getLinkOrderNo());//环节编码
                    omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                    OmsLinkDialogue omsDig = omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
                    if (omsDig != null) {
                        omsDig.setStatus(1);
                        omsDig.setOper_date(new Date());
                        omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                    }
                    OmsLinkDialogue dig = new OmsLinkDialogue();
                    dig.setCreator_name(user.getEmployeeName());
                    dig.setCreator_no(user.getRowNo());
                    dig.setCreator_date(new Date());
                    dig.setOper_name(oms.getOperateName());
                    dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                    dig.setOper_date(new Date());
                    dig.setStatus(0);//1已处理，0未处理
                    dig.setLinkOrderNo(nextLink.getLinkOrderNo());
                    dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                    dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                    OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                    link.setStatus(1);
                    link.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    commitOmsSellOrderData(oms, Integer.parseInt(oms.getOperateNo()), user, "1", rdig.getId());
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        map.put("code", -1);
                        map.put("data", "");
                        map.put("msg", "未查询到待办信息,请联系管理员核对");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return;
                    }
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前工单合同确认环节重复！请联系系统管理员排查原因");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }else if("6".equals(link.getLinkCode())&&"工单合同确认".equals(link.getLinkName())){
                OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
                if(omsDig!=null){
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                }
                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(oms.getOperateName());
                dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(link.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),user,"1",rdig.getId());
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }else{
                List<OmsOrderLink> existenceT =omsSellOrderService.getOmsOrderLinkByCode("5","工单合同关联",oms.getOrderNo());
                if(existenceT!=null){
                    if(existenceT.size()>0){
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return ;
                    }
                }
                OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"5");
                OmsOrderLink currentLink = new OmsOrderLink();
                currentLink.setCreator_name(user.getEmployeeName());//发起人
                currentLink.setCreator_no(user.getRowNo());//发起人工号
                currentLink.setCreator_date(new Date());//发起人时间(当前时间)
                currentLink.setOper_name(user.getEmployeeName());//操作人
                currentLink.setOper_no(user.getRowNo());//操作人工号
                currentLink.setOper_date(new Date());//操作时间(当前时间)
                currentLink.setStatus(1);//状态(状态根据环节确定)
                currentLink.setLinkCode("5");//环节编码或者固定的环节编码
                currentLink.setLinkName("工单合同关联");//环节名称
                currentLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                currentLink.setLinkOrderNo(IBM + taskService.getNumber());
                currentLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                omsSellOrderService.saveOrupdateOmsOrderLink(currentLink);

                link.setStatus(1);
                link.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsOrderLink(link);

                OmsPretreatmentDate PretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"6");
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name(oms.getOperateName());//操作人
                nextStepLink.setOper_no(Integer.parseInt(oms.getOperateNo()));//操作人工号
                nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                nextStepLink.setStatus(0);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("6");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("工单合同确认");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(PretreatmentDate.getPretreatment_date())));
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                if(!"0".equals(oms.getNoTreaty())) {
                    oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                    omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                }

                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(oms.getOperateName());
                dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),user,"1",rdig.getId());
                OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                if(omsDig!=null){
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                }
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            map.put("code",1);
            map.put("msg","成功");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("需求单合同关联异常："+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","异常");
            map.put("data","");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public boolean zipFiles(List<File> srcFiles, File zipFile) {
        // 创建 FileOutputStream 对象
        FileOutputStream fileOutputStream = null;
        // 创建 ZipOutputStream
        ZipOutputStream zipOutputStream = null;
        // 创建 FileInputStream 对象
        FileInputStream fileInputStream = null;
        try {
            // 实例化 FileOutputStream 对象
            fileOutputStream = new FileOutputStream(zipFile);
            // 实例化 ZipOutputStream 对象
            zipOutputStream = new ZipOutputStream(fileOutputStream);
            // 创建 ZipEntry 对象
            ZipEntry zipEntry = null;
            // 遍历源文件数组
            for (int i = 0; i < srcFiles.size(); i++) {
                // 将源文件数组中的当前文件读入 FileInputStream 流中
                fileInputStream = new FileInputStream(srcFiles.get(i));
                // 实例化 ZipEntry 对象，源文件数组中的当前文件
                String fileName = srcFiles.get(i).getName();
                String ps = FileUpload.getFilePix(srcFiles.get(i).getName());
                zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                // 该变量记录每次真正读的字节个数
                int len;
                // 定义每次读取的字节数组
                byte[] buffer = new byte[1024];
                while ((len = fileInputStream.read(buffer)) > 0) {
                    zipOutputStream.write(buffer, 0, len);
                }
            }
            zipOutputStream.closeEntry();
            zipOutputStream.close();
            fileInputStream.close();
            fileOutputStream.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/6 16:17
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 及时分配订单经理
     */
    public void randomlyAssignOrderManagers(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");//订单编号
            String userId = getString("userId");//登陆人ID
            String managerId = getString("managerId");//订单经理ID
            Map<String,String> map = new HashMap<>();
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            OmsOrderLink link =omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(managerId));// 获取下一步处理人信息
            if(managerId==null || "".equals(managerId) || "undefined".equals(managerId)){
                List<Map<String, String>> mapList = queryFuzzyUser("ROLE","ROLE_ODMR","",user,oms.getIsSupport(),oms.getIsUrgent());
                if(mapList.size() == 0 || mapList.isEmpty()){
                    oms.setModifyDate(new Date());
                    oms.setState(OmsSellOrder.TATE_CONSTRUCTION);
                    omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                    link.setOper_date(new Date());
                    link.setStatus(0);
                    omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    mapJson.put("code",1);
                    mapJson.put("data",oms);
                    mapJson.put("msg","需求单已提交到系统");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return;
                }
                int[] arry = new int[mapList.size()];
                for (int i = 0; i < mapList.size(); i++) {
                    Object rowno = mapList.get(i).get("ROWNO");
                    int count = omsSellOrderService.getOmsSellOrderScoreByUserId(Integer.valueOf(rowno.toString()));
                    arry[i] = count;
                    map.put(String.valueOf(Integer.valueOf(rowno.toString())), String.valueOf(count));
                }
                if(arry.length==0){
                    managerId = mapList.get(new Random().nextInt(mapList.size())).get("ROWNO")+"";
                }else{
                    int min = arry[0];
                    for (int i = 0; i < arry.length; i++) {
                        if (arry[i] < min) {
                            min = arry[i];
                        }
                    }
                    for (Map.Entry entry : map.entrySet()) {
                        if (String.valueOf(min).equals(entry.getValue())) {
                            managerId = String.valueOf(entry.getKey());
                        }
                    }
                }
            }
            oms.setOperateName(USER.getEmployeeName());
            oms.setOperateNo(String.valueOf(USER.getRowNo()));
            oms.setOperateDate(new Date());
            oms.setOperateHandleDate(new Date());
            if(oms.getWhetherManager()==0){
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            }else {
                link.setOper_date(new Date());
                link.setOper_name(USER.getEmployeeName());
                link.setOper_no(USER.getRowNo());
                link.setStatus(1);
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                String IBM = "";
                List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                List<OmsOrderLink> existence = omsSellOrderService.getOmsOrderLinkByCode("3", "工单确认", oms.getOrderNo());
                if (existence != null) {
                    if (existence.size() > 0) {
                        mapJson.put("code", -1);
                        mapJson.put("data", "");
                        mapJson.put("msg", "当前环节已完毕,环节异常,请联系系统管理员");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                        return;
                    }
                }
                OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(), "3");
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name(USER.getEmployeeName());//操作人
                nextStepLink.setOper_no(USER.getRowNo());//操作人工号
                nextStepLink.setOper_date(new Date());//setLinkName("工单确认")(当前时间)
                nextStepLink.setStatus(0);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("3");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("工单确认");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(), Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                oms.setLinkOrderNo(oms.getLinkOrderNo());
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);

                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(USER.getEmployeeName());
                dig.setOper_no(USER.getRowNo());
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                commitOmsSellOrderData(oms, Integer.parseInt(managerId), user, "", rdig.getId());
            }
            mapJson.put("code",1);
            mapJson.put("data",oms);
            mapJson.put("msg","需求单已分配至订单经理『 "+USER.getEmployeeName()+"』处");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error("订单经理分配异常："+e.getMessage(),e);
            e.printStackTrace();
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","订单经理分配异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:51
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否有订单经理
     * ALL(查询全省);
     * ROLE(查询角色);
     * CITY(查询地市);
     * COUNTY(查询区县);
     * COLLECTION(收藏)--type
     * 角色CODE--roleName
     * 模糊查询名字--name
     * isAllot：是否是市公司渠道 是：2
     * isUrgent：是否加急，加急是2
     */
    public List<Map<String, String>> queryFuzzyUser(String type,String roleName,String name,SystemUser user,String isAllot,String isUrgent){
        List<Map<String, String>> listMap= new ArrayList<>();
        try {
            if ("ALL".equals(type)) {//查询全省
                if (!"".equals(name) && name != null) {
                    //根据页面输入名称模糊查询人员
                    listMap = structureOfPersonnelService.queryVwUserinfo(name);
                }
            } else if ("ROLE".equals(type)) {//查询角色
                if (!"".equals(roleName) && roleName != null) {
                    //根据角色code查询角色下面的人员（区分地市和区县）
                    List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
                    if(userlistMap.size() > 0){
                        if(isAllot.equals("2")){
                            listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName,user,userlistMap,name,"D");
                        }else {
                            if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {
                                logger.info("这是区县" + "是否需要市综调中心值：" + isAllot);
                                listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "A");
                            } else {
                                if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                                    logger.info("这是省公司");
                                    listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "S");
                                } else {
                                    logger.info("这是市公司");
                                    listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "D");
                                }
                            }
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error(e.getMessage(),e);
        }
        return listMap;
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:58
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 查询审批人员
     *
     * type: ROLE
     * roleName: ROLE_ODMR
     * queryTag: 1
     * name: 何浩
     * userId: 37929
     *
     */
    public void queryApprovalUser(){
        List<Map<String, String>> listMap= new ArrayList<>();
        Map<String,Object> map = new HashMap<>();
        try {
            String name = getString("name");//模糊查询数据（根据名称）
            String userId = getString("userId");//当前登录人ID
            String roleName = getString("roleName");//角色CODE
            String type = getString("type");//ALL(查询全省);ROLE(查询角色);CITY(查询地市);COUNTY(查询区县),COLLECTION(收藏)
            String queryTag = getString("queryTag");//查询状态，根据此状态查询区县/省公司/市公司-->0，查询区县+地市-->1，查询市公司-->2
            if(null != name && !"".equals(name)){
                StringBuffer tb = new StringBuffer();
                for (int i = 0; i < name.length(); i++) {
                    tb.append(name.charAt(i));
                    if (i < name.length() - 1) {
                        tb.append("%");
                    }
                }
                name = tb.toString();
            }
            if ("ALL".equals(type)) {//查询全省
                if (!"".equals(name) && name != null) {
                    //根据页面输入名称模糊查询人员
                    listMap = structureOfPersonnelService.queryVwUserinfo(name);
                }
            } else if ("ROLE".equals(type)) {//查询角色
                if (!"".equals(roleName) && roleName != null) {
                    //根据角色code查询角色下面的人员（区分地市和区县）
                    SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(userId));
                    List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
                    if(userlistMap.size() > 0){
                        if(null != queryTag && !"".equals(queryTag) && !"undefined".equals(queryTag) && !"null".equals(queryTag)){
                            if("0".equals(queryTag)){//正常状态
                                if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {
                                    logger.info("这是区县");
                                    listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "Q");
                                } else {
                                    if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                                        logger.info("这是省公司");
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "S");
                                    } else {
                                        logger.info("这是市公司");
                                        listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "D");
                                    }
                                }
                            }else if("1".equals(queryTag)){//加急状态
                                logger.info("这是市公司+这是区县");
                                listMap = structureOfPersonnelService.queryRoleUserInfo(roleName, user, userlistMap, name, "A");
                            }else if("2".equals(queryTag)){//市公司支撑状态
                                logger.info("这是区县+是否需要市综调中心值："+queryTag);
                                listMap = structureOfPersonnelService.queryRoleUserInfoAndSignIn(roleName, user, userlistMap, name, "D");
                            }
                        }
                    }
                }
            } else if ("CITY".equals(type)) {//查询地市

            } else if ("COUNTY".equals(type)) {//查询区县

            } else if ("COLLECTION".equals(type)) {//查询收藏人
                //根据当前登录人查询收藏的人员
                listMap = structureOfPersonnelService.getTopcontactsUser(userId, name);
            }
            List<Map<String, String>> collectedList = structureOfPersonnelService.getTopcontactsUser(userId, name);
            for (Map<String, String> stringMap : listMap) {
                String employee_name = stringMap.get("EMPLOYEE_NAME");
                String pinyinInitials ="";
                if(null != employee_name && !"null".equals(employee_name) && !"".equals(employee_name)){
                    if(employee_name.indexOf("_") != -1){
                        pinyinInitials = employee_name.substring(employee_name.lastIndexOf("_")+1);
                    }else{
                        pinyinInitials = employee_name;
                    }
                }
                if(!"".equals(pinyinInitials)){
                    String pinyinInitials1 = getPinyin(pinyinInitials,"_");
                    String toHump = lineToHump(pinyinInitials1);
                    toHump =toHump.substring(0, 1).toUpperCase() + toHump.substring(1);
                    stringMap.put("pinyin",toHump);
                }else{
                    stringMap.put("pinyin","");
                }
                if(null != collectedList && !collectedList.isEmpty()){
                    for (Map<String, String> collected : collectedList) {
                        Object mapRowno = stringMap.get("ROWNO");
                        Object collRowno = collected.get("ROWNO");
                        if(String.valueOf(mapRowno).equals(String.valueOf(collRowno))){
                            stringMap.put("collected","true");
                            break;
                        }else{
                            stringMap.put("collected","false");
                        }
                    }
                }else{
                    stringMap.put("collected","false");
                }
            }
            map.put("code",1);
            map.put("data",listMap);
            map.put("msg","查询人员成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("审批人员查询异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","查询人员失败");
            map.put("msg","查询人员失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:59
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 添加收藏人
     */
    public void addCollectionUser(){
        String id = getString("collectionUserId");//收藏人ID
        String userId = getString("userId");//登陆人ID
        Map<String,Object> map = new HashMap<>();
        SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(userId));
        try {
            List<Object> list = structureOfPersonnelService.getList();
            for(int j = 0 ; j < list.size() ; j++){
                Object[] jj=(Object[]) list.get(j);
                boolean b = structureOfPersonnelService.getSystemUser_etp(id,Integer.parseInt(String.valueOf(jj[0])),user);
                if(b==false){
                    structureOfPersonnelService.setDept(id,Integer.parseInt(String.valueOf(jj[0])),user.getRowNo());
                }
            }
            map.put("code",1);
            map.put("data","收藏成功");
            map.put("msg","收藏成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("收藏异常："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","收藏失败");
            map.put("msg","收藏失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * 删除收藏人
     */
    public void deleteCollectionUser(){
        String id = getString("collectionUserId");
        String userId = getString("userId");
        Map<String,Object> map = new HashMap<>();
        SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(userId));
        try {
            List<Object> list = structureOfPersonnelService.getList();
            for(int j = 0 ; j < list.size() ; j++){
                Object[] jj=(Object[]) list.get(j);
                boolean b = structureOfPersonnelService.getSystemUser_etp(id,Integer.parseInt(String.valueOf(jj[0])),user);
                if(b){
                    structureOfPersonnelService.deleteCollectionUser(id,Integer.parseInt(String.valueOf(jj[0])),user.getRowNo());
                }
            }
            map.put("code",1);
            map.put("data","移除成功");
            map.put("msg","移除成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("删除收藏异常："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","移除失败");
            map.put("msg","移除失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }
    /**
     * 获取汉字首字母
     *
     * @param text 文本
     * @return {@link String}
     */
    public static String getPinyinInitials(String text) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < text.length(); i++) {
            char ch = text.charAt(i);
            String[] s = PinyinHelper.toHanyuPinyinStringArray(ch);
            if (s != null) {
                sb.append(s[0].charAt(0));
            } else {
                sb.append(ch);
            }
        }
        return sb.toString();
    }
    /**
     * 将汉字转换为全拼
     *
     * @param text 文本
     * @param separator 分隔符
     * @return {@link String}
     */
    public static String getPinyin(String text, String separator) {
        char[] chars = text.toCharArray();
        HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
        // 设置大小写
        format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
        // 设置声调表示方法
        format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
        // 设置字母u表示方法
        format.setVCharType(HanyuPinyinVCharType.WITH_V);
        String[] s;
        String rs = StringUtils.EMPTY;
        try {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < chars.length; i++) {
                // 判断是否为汉字字符
                if (String.valueOf(chars[i]).matches("[\\u4E00-\\u9FA5]+")) {
                    s = PinyinHelper.toHanyuPinyinStringArray(chars[i], format);
                    if (s != null) {
                        sb.append(s[0]).append(separator);
                        continue;
                    }
                }
                sb.append(String.valueOf(chars[i]));
                if ((i + 1 >= chars.length) || String.valueOf(chars[i + 1]).matches("[\\u4E00-\\u9FA5]+")) {
                    sb.append(separator);
                }
            }
            rs = sb.substring(0, sb.length()-1);
        } catch (BadHanyuPinyinOutputFormatCombination e) {
            e.printStackTrace();
            logger.error("拼音转换异常"+e.getMessage(),e);
        }
        return rs;
    }
    private static Pattern linePattern = Pattern.compile("_(\\w)");
    /** 下划线转驼峰 */
    public static String lineToHump(String str) {
        str = str.toLowerCase();
        Matcher matcher = linePattern.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (matcher.find()) {
            matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
        }
        matcher.appendTail(sb);
        return sb.toString();
    }
    /**
     * 提交工作台
     */
    public void saveOmsOrderWorkbench(){
        Map<String,Object> map =new HashMap<>();
        try {
            HttpServletResponse resp = getResponse();
            resp.setHeader("Access-Control-Allow-Origin", "*");
            resp.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
            resp.setHeader("Access-Control-Max-Age", "7200");
            resp.setHeader("Access-Control-Allow-Headers", "x-requested-with, Content-Type");
            resp.setHeader("Access-Control-Allow-Credentials", "true");
            String type = getString("type");//判断是否是代办
            String orderId = getString("orderId");
            String title = getString("title");
            String mome = getString("mome");
            String submitJson = getString("submitJson");
            String CCUserJson = getString("CCUserJson");
            String custom = getString("custom");
            String loginUserId = getString("userId");
            String productIdArray = getString("productIdArray");
            String noTreaty = getString("noTreaty");//是否无协议报建  //添加参数
            String attachmentId = getString("attachmentId");//附件ID
            if(!StringHelper.isBlank(orderId)){
                map.put("code",-1);
                map.put("msg","参数错误");
                map.put("data","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return ;
            }
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(loginUserId));
            if("2".equals(type)){
                String taskId = getString("taskId");
                List<WaitTask> waitTaskList = service.queryListWaitByTaskId(taskId);//获取待办信息
                for (WaitTask waitTask : waitTaskList) {
                    if(!WaitTask.HAS_BEEN_COMPLETED.equals(waitTask.getState())){
                        service.updateWait(waitTask, this.getRequest());
                    }
                }
                String workbenchId = getString("workbenchId");
                OmsOrderWorkbench workbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
                workbench.setState("-1");
                workbench.setBak1("审批作废，重新发起审批");
                omsOrderWorkbenchService.saveOmsOrderWorkbench(workbench);
            }
            StringBuilder prcName= new StringBuilder();
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderByOrderNo(orderId);
            if( null != productIdArray && !"".equals(productIdArray) && !"null".equals(productIdArray) && !"undefined".equals(productIdArray)){
                JSONArray products = JSONArray.fromObject(productIdArray);
                if(products.size() > 0){//判断是否有资费ID
                    for (int i = 0; i < products.size(); i++) {//循环获取资费
                        Object obj = products.get(i);
                        String proId = obj.toString();
                        PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(proId);
                        prcName.append(pmsinfo.getPrcName()).append("|");
                    }
                }
            }
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String orderNo = IBM + Bpms_riskoff_service.getUnlockedNumber();
            if (noTreaty == null || "".equals(noTreaty.trim()) || "null".equals(noTreaty.trim())||"undefined".equals(noTreaty.trim())||"1".equals(noTreaty.trim())) {
                noTreaty=null;
            }else{
                if("0".equals(noTreaty)){
                    List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("5","工单合同关联",orderId);
                    if(existence!=null){
                        if(existence.size()>0){
                            for(int i=0;i<existence.size();i++){
                                OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(existence.get(i).getLinkOrderNo());
                                if(omsDig!=null){
                                    omsDig.setStatus(1);
                                    omsDig.setOper_date(new Date());
                                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                                }
                                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                                if (wt != null) {
                                    service.updateWait(wt, this.getRequest());
                                }
                                omsSellOrderService.deleteOmsOrderLink(existence.get(i).getId());
                            }
                        }
                    }
                    List<OmsOrderLink> existenceT =omsSellOrderService.getOmsOrderLinkByCode("4","工单审批",orderId);
                    if(existenceT!=null){
                        if(existenceT.size()==1){

                            OmsOrderLink link = existenceT.get(0);
                            link.setStatus(0);
                            omsSellOrderService.saveOrupdateOmsOrderLink(link);
                            omsSellOrder.setLinkOrderNo(link.getLinkOrderNo());
                            if(omsSellOrder.getWhetherManager()!=0) {
                                OmsLinkDialogue dig = new OmsLinkDialogue();
                                dig.setCreator_name(user.getEmployeeName());
                                dig.setCreator_no(user.getRowNo());
                                dig.setCreator_date(new Date());
                                dig.setOper_name(omsSellOrder.getCreateName());
                                dig.setOper_no(Integer.parseInt(omsSellOrder.getCreateNo()));
                                dig.setOper_date(new Date());
                                dig.setStatus(0);//1已处理，0未处理
                                dig.setLinkOrderNo(link.getLinkOrderNo());
                                dig.setOper_role("ROLE_CUMR");//处理角色客户经理
                                dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
                                OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                                commitOmsSellOrderData(omsSellOrder, Integer.parseInt(omsSellOrder.getCreateNo()), user, "", rdig.getId());

                                OmsLinkDialogue digTwo = new OmsLinkDialogue();
                                digTwo.setCreator_name(user.getEmployeeName());
                                digTwo.setCreator_no(user.getRowNo());
                                digTwo.setCreator_date(new Date());
                                digTwo.setOper_name(omsSellOrder.getOperateName());
                                digTwo.setOper_no(Integer.parseInt(omsSellOrder.getOperateNo()));
                                digTwo.setOper_date(new Date());
                                digTwo.setStatus(0);//1已处理，0未处理
                                digTwo.setLinkOrderNo(link.getLinkOrderNo());
                                digTwo.setOper_role("ROLE_ODMR");//处理角色订单经理
                                digTwo.setCreator_role("ROLE_ODMR");//发起角色订单经理
                                OmsLinkDialogue rdigTwo = omsSellOrderService.saveOrupdateOmsLinkDialogue(digTwo);
                                commitOmsSellOrderData(omsSellOrder, Integer.parseInt(omsSellOrder.getOperateNo()), user, "", rdigTwo.getId());
                            }
                        }
                    }
                }
            }
            OmsOrderWorkbench workbench = omsOrderWorkbenchService.saveOmsOrderWorkbench(orderId,title,mome,mome,custom,orderNo, prcName.toString(), productIdArray,OmsOrderWorkbench.TATE_WAITING,user,noTreaty);
            if(null == workbench){
                map.put("code",-1);
                map.put("msg","审批数据创建失败");
                map.put("data","");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                throw new RuntimeException("工作台数据创建失败");
            }
            String process_sign="OmsOrderWorkbench."+Bpms_riskoff_service.getLockUpNumber();
            Bpms_riskoff_process bpms_riskoff_process = taskService.setBpms_riskoff_process(workbench.getId(),process_sign,1,user);
            omsSellOrder.setNoTreaty(noTreaty);
            omsSellOrderService.saveOrupdateOmsSellOrder(omsSellOrder);
            JSONArray array = JSONArray.fromObject(submitJson);
            String launchTask = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",2,"SH","预受理发起",user.getRowNo(),user,"0",null,null);
            if(array.size() > 0){//判断是否有抄送人或者审核人
                for (int i = 0; i < array.size(); i++) {//循环增加抄送或审核任务数据
                    JSONObject jsonObject = array.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String taskId = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"SH","预受理发起",userId,user,"1",launchTask,null);
                    String url = "jsp/demandOrder/approvalShow.jsp?workbenchId=" + workbench.getId()+ "&taskId=" + taskId ;
                    commitBackLogData(taskId,"["+omsSellOrder.getUnitName()+"]"+omsSellOrder.getTitle(),userId,user,url);
                }
            }
            JSONArray ccarray = JSONArray.fromObject(CCUserJson);
            if(ccarray.size() > 0){//判断是否有抄送人或者审核人
                for (int i = 0; i < ccarray.size(); i++) {//循环增加抄送或审核任务数据
                    JSONObject jsonObject = ccarray.getJSONObject(i);
                    int userId = jsonObject.getInt("id");
                    String taskId = taskService.setBpms_riskoff_taskSaveAll(bpms_riskoff_process.getProcess_sign(),"",1,"CS","预受理发起",userId,user,"1",launchTask,null);
                    String url = "jsp/demandOrder/approvalShow.jsp?workbenchId=" + workbench.getId()+ "&taskId=" + taskId ;
                    commitBackLogData(taskId,"["+omsSellOrder.getUnitName()+"]"+omsSellOrder.getTitle(),userId,user,url);
                }
            }

            if (!StringUtils.isEmpty(attachmentId)) {
                //判断是否上传了附件,获取前台提交的附件Id；
                String[] fileId = attachmentId.split(",");
                if (fileId.length > 0) {
                    for (int i = 0; i < fileId.length; i++) {
                        String url = EncryptionUtils.decrypt(fileId[i]);
                        String[] fie = url.split("\\\\");
                        int s = fie.length;
                        String name = fie[s-1];
                        //获取毫秒数
                        Long time = System.currentTimeMillis();
                        String pixstr =FileUpload.getFilePix(name);
                        String endFileName=time+pixstr;
                        String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                        StorageCfg storageCfg= attachmentService.queryStorageCfg();
                        //String endUrl=FileUpload.getFtpURL()+urlDate;
                        String endUrl=storageCfg.getFileName()+urlDate;
                        File headPath = new File(endUrl);//获取文件夹路径
                        if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                            headPath.mkdirs();
                        }
                        File startFile = new File(FileUpload.getPaperConURL()+url);
                        File endFile = new File(endUrl+endFileName);//获取文件夹路径
                        logger.info("这是预受理APP已存储文件地址："+FileUpload.getPaperConURL()+url);
                        logger.info("这是预受理PP需要移动的文件地址："+endUrl+endFileName);
                        try{
                            Files.move(Paths.get(FileUpload.getPaperConURL()+url),Paths.get(endUrl+endFileName),
                                    StandardCopyOption.REPLACE_EXISTING);
                            logger.info("移动文件成功");
                            Attachment attachmentEntity = new Attachment();
                            attachmentEntity.setAttachmentName(endFileName);// 防重名
                            attachmentEntity.setAttachmentUrl(urlDate+endFileName);
                            attachmentEntity.setUploadDate(new Date());
                            attachmentEntity.setRealName(endFileName);
                            attachmentEntity.setUploadUser(user);
                            attachmentEntity.setVersion(storageCfg.getId());
                            String attId = attachmentService.addEntity(attachmentEntity);
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(omsSellOrder.getId());
                            sa.setAttachmentId(attId);
                            sa.setLink(OmsSellOrder.OMSSELLORDER);
                            claimForFundsService.saveSandA(sa);
                        }catch (Exception e){
                            logger.error("预受理APP移动文件出错："+e.getMessage(),e);
                            e.printStackTrace();
                        }
                    }
                }
            }

            map.put("code",1);
            map.put("data","");
            map.put("msg","审批发起成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("提交工作台异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * 查询手机页面当前代办任务
     * 手机的待审批TAB
     */
    public void getApprovalList(){
        Map<String,Object> map =new HashMap<>();
        try {
            PageRequest page = new PageRequest(getRequest());
            String orderNo=getString("orderNo");//工单编码
            String createUserName=getString("createUserName");
            String userId=getString("userId");
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));// 获取下一步处理人信息
            PageResponse response =omsSellOrderService.getApprovalList(page,USER,orderNo,createUserName);
//            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(response));
            map.put("code",1);
            map.put("data",response);
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e) {
            logger.error("查询手机代办异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * 根据用户ID查询电话
     */
    public void getUserPhone(){
        Map<String,Object> map =new HashMap<>();
        try {
            String userId=getString("userId");
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            map.put("code",1);
            map.put("data",USER.getMobile());
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e) {
            logger.error("电话查询异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }
    /**
     * 作废工单
     * 可删除
     */
    public void orderCancellation(){
        Map<String,Object> mapJson = new HashMap<>();
        try {

            String orderNo = getString("orderNo");
            String mome = getString("mome");
            String waitId = getString("waitId");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            List<OmsOrderWorkbench> omsOrderWorkbenchByOrderNoAndStateList = omsSellOrderService.getOmsOrderWorkbenchList(orderNo);
            for (OmsOrderWorkbench workbench : omsOrderWorkbenchByOrderNoAndStateList) {
                List<Bpms_riskoff_task> publicEntityTaskList = taskService.getPublicEntityTaskList(workbench.getId());
                for (Bpms_riskoff_task bpmsRiskoffTask : publicEntityTaskList) {
                    if(bpmsRiskoffTask.getStatus() == 1){
                        taskService.updateBpms_riskoff_task(mome,-1,bpmsRiskoffTask.getId());
                    }
                    List<WaitTask> wtList = service.queryListWaitByTaskId(bpmsRiskoffTask.getId());//获取待办信息
                    for (WaitTask wt : wtList) {
                        if(!WaitTask.HAS_BEEN_COMPLETED.equals(wt.getState())){
                            service.updateWait(wt,this.getRequest());
                        }
                    }
                }
            }
            oms.setState(OmsSellOrder.TATE_INVALID);
            oms.setMemo(mome);
            oms.setModifyDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            mapJson.put("code",1);
            mapJson.put("data",oms);
            mapJson.put("msg","需求单已被作废");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("工单作废异常："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","需求单作废异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/12/23 14:37
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 作废审批工单(这里要发送短信到还未审批完成的单子)
     */
    public void approvalRevoked(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String workbenchId = getString("workbenchId");
            String bak1 = getString("desc");//添加参数
            String taskId = getString("taskId");
            Bpms_riskoff_process bpms_riskoff_process = taskService.updatebpmsRiskoffProcess(workbenchId, -1);//修改审批流程表
            Bpms_riskoff_task bpms_riskoff_task =taskService.updateBpms_riskoff_task(bak1, -1, taskId);//修改本条数据
            if(null == bpms_riskoff_task){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","任务错误，请联系管理员核对");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return ;
            }
            OmsOrderWorkbench workbench = omsOrderWorkbenchService.getOmsOrderWorkbenchById(workbenchId);
            workbench.setBak1(null);
            workbench.setState(OmsOrderWorkbench.STATE_INVALID);
            omsOrderWorkbenchService.saveOmsOrderWorkbench(workbench);
            List<Bpms_riskoff_task> publicEntityTaskList = taskService.getPublicEntityTaskList(bpms_riskoff_process.getBiz_id());
            if(null != publicEntityTaskList && publicEntityTaskList.size() > 0){
                for (Bpms_riskoff_task riskoffTask : publicEntityTaskList) {
                    if(riskoffTask.getStatus() == 1||riskoffTask.getStatus() == -1){
                        //这里调用短信接口
                        taskService.updateBpms_riskoff_task("审批发起人已作废工单", -1, riskoffTask.getId());
                        WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,riskoffTask.getId());
                        SystemUser systemUser=systemUserService.getUserInfoRowNo(wt.getHandleUserId());
                        //结束当前待办
                        if (wt != null) {
                            service.updateWait(wt, this.getRequest());
                            JSONObject obj = new JSONObject();
                            obj.put("title",workbench.getTitle());//工单标题
                            obj.put("rejectUser",bpms_riskoff_task.getCreator_name());//驳回人员
                            obj.put("creator",workbench.getOperateName());//发起人员
                            smsPushService.sendOmsSellOrder(obj.toString(),systemUser.getMobile(),"20421152");
                        }
                    }
                }
            }
            if("0".equals(workbench.getBak1())){
                OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(workbench.getOrderNo());
                oms.setNoTreaty(null);
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            }
            mapJson.put("code",1);
            mapJson.put("data","");
            mapJson.put("msg","审批已被作废");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("审批作废异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","审批作废异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }


    /**
     * 查询我的
     * 可删除
     */
    public void getMyOmsSellOrderList(){
        Map<String,Object> map =new HashMap<>();
        try {
            String orderNo=getString("orderNo");//工单编码
            String title=getString("title");//需求名称
            String unitId=getString("unitId");//集团280
            String userId=getString("userId");//用户ID
            String type=getString("type");//区分我的还是我发起的--1代表我发起的，2代表我经手的
            String status=getString("status");//区分完成还是未完成--1代办完成，2代表未完成
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));// 获取下一步处理人信息
            PageRequest page = new PageRequest(getRequest());
            PageResponse response =omsSellOrderService.getMyOmsSellOrderList(page,USER,orderNo,title,unitId,type,status);
            map.put("code",1);
            map.put("msg","查询成功");
            map.put("data",response);
        } catch (Exception e) {
            logger.error("查询资费异常",e.getMessage());
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","查询异常");
            map.put("data","");
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:31
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 获取资费明细信息 获取模板数据
     */
    public void getCustomTemplate(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String productId = getString("productId");
            String name = URLDecoder.decode(getString("name"),"UTF-8");
            OmsCustomDetails templateList = omsSellOrderService.getCustomTemplateByProductId(productId,"0");
            if(null != templateList ){
                mapJson.put("code",1);
                mapJson.put("data",templateList);
                mapJson.put("msg","查询成功");
            }else{
                if("互联网专线".equals(name)){
                    String con ="[{\"templateName\":\"安装地址\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"accessAdd\"},{\"templateName\":\"带宽(单位:M)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"申请资费(元/月)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"申请资费单价(元/M)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"标准资费(元/月)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"标准资费单价(元/M)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"体验期\",\"templateValue\":\"\",\"templateType\":\"select\",\"bossKey\":\"null\",\"data\":[{\"templateValue\":\"无\",\"templateText\":\"无\"},{\"templateValue\":\"1\",\"templateText\":\"1个月\"},{\"templateValue\":\"2\",\"templateText\":\"2个月\"},{\"templateValue\":\"3\",\"templateText\":\"3个月\"}]},{\"templateName\":\"接入方式\",\"templateValue\":\"\",\"templateType\":\"select\",\"bossKey\":\"null\",\"data\":[{\"templateValue\":\"PON\",\"templateText\":\"PON\"},{\"templateValue\":\"PTN\",\"templateText\":\"PTN\"},{\"templateValue\":\"OTN\",\"templateText\":\"OTN\"},{\"templateValue\":\"SPN\",\"templateText\":\"SPN\"}]}]";
                    JSONObject obj= new JSONObject();
                    obj.put("id",UUID.randomUUID().toString());
                    obj.put("title","互联网专线属性");
                    obj.put("content",con);
                    obj.put("productId",productId);
                    obj.put("createDate","2020-12-29 15:25:29");
                    obj.put("updateDate","2020-12-29 15:25:29");
                    obj.put("state","0");
                    obj.put("deleteState","0");
                    mapJson.put("code",1);
                    mapJson.put("data",obj);
                    mapJson.put("msg","查询成功");
                }else if("数据专线".equals(name)){
                    JSONObject obj= new JSONObject();
                    String con ="[{\"templateName\":\"A端安装地址\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"portADetailAdd\"},{\"templateName\":\"Z端安装地址\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"portZDetailAdd\"},{\"templateName\":\"带宽(单位:M)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"申请资费(元/月)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"标准资费(元/月)\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"折扣\",\"templateValue\":\"\",\"templateType\":\"input\",\"bossKey\":\"null\"},{\"templateName\":\"体验期\",\"templateValue\":\"\",\"templateType\":\"select\",\"bossKey\":\"null\",\"data\":[{\"templateValue\":\"无\",\"templateText\":\"无\"},{\"templateValue\":\"1\",\"templateText\":\"1个月\"},{\"templateValue\":\"2\",\"templateText\":\"2个月\"},{\"templateValue\":\"3\",\"templateText\":\"3个月\"}]},{\"templateName\":\"接入方式\",\"templateValue\":\"\",\"templateType\":\"select\",\"bossKey\":\"null\",\"data\":[{\"templateValue\":\"PON\",\"templateText\":\"PON\"},{\"templateValue\":\"PTN\",\"templateText\":\"PTN\"},{\"templateValue\":\"OTN\",\"templateText\":\"OTN\"},{\"templateValue\":\"SPN\",\"templateText\":\"SPN\"}]}]";
                    obj.put("id",UUID.randomUUID().toString());
                    obj.put("title","数据专线属性");
                    obj.put("content",con);
                    obj.put("productId",productId);
                    obj.put("createDate","2020-12-29 15:25:29");
                    obj.put("updateDate","2020-12-29 15:25:29");
                    obj.put("state","0");
                    obj.put("deleteState","0");
                    mapJson.put("code",1);
                    mapJson.put("data",obj);
                    mapJson.put("msg","查询成功");
                }else{
                    templateList = new OmsCustomDetails();
                    templateList.setContent("[]");
                    templateList.setProductId(productId);
                    mapJson.put("code",1);
                    mapJson.put("data",templateList);
                    mapJson.put("msg","查询无数据");
                }
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("模板获取异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    public void addCustomTemplate(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            Integer count = omsSellOrderService.deleteOmsCustomDetails();
            logger.info("删除自定义产品模板条数："+count);
            List<Map<String,Object>> labelList = omsSellOrderService.selectProductList("PMS_PRODUCT_LABEL");
            for (Map<String, Object> stringObjectMap : labelList) {
                String label_name = (String)stringObjectMap.get("LABEL_NAME");
                String label_id = (String)stringObjectMap.get("LABEL_ID");
                if("VPMN".equals(label_name) || "ICT信息化".equals(label_name) || "IDC托管".equals(label_name) || "省内移动云".equals(label_name)
                        || "物联网产品".equals(label_name)|| "全网云".equals(label_name)|| "类行业号卡".equals(label_name)|| "政企云".equals(label_name)
                        || "云MAS".equals(label_name)|| "云办公".equals(label_name)|| "集团建档".equals(label_name)|| "云视讯".equals(label_name)
                        || "泛智能终端".equals(label_name)|| "集团统付".equals(label_name)|| "集团彩铃".equals(label_name)|| "集团v网".equals(label_name)){
                    continue;
                }else{
                    if("集团个人业务".equals(label_name)){
                        List<PmsProductInfo> infoList = omsSellOrderService.selectPmsProductInfoList(label_id);
                        for (PmsProductInfo pmsProductInfo : infoList) {
                            if(pmsProductInfo.getProdName().equals("家庭宽带")){
                                OmsCustomDetails temlate = new OmsCustomDetails();
                                temlate.setTitle(pmsProductInfo.getProdName());
                                temlate.setContent("[{\"templateName\":\"安装地址\",\"templateValue\":\"\"}]");
                                temlate.setProductId(pmsProductInfo.getProdId());
                                temlate.setCreateDate(new Date());
                                temlate.setDeleteState("0");
                                temlate.setState("0");
                                temlate.setUpdateDate(new Date());
                                omsSellOrderService.saveOrupdateOmsCustomDetails(temlate);
                            }
                        }
                    }else{
                        List<PmsProductInfo> infoList = omsSellOrderService.selectPmsProductInfoList(label_id);
                        for (PmsProductInfo pmsProductInfo : infoList) {
                            if(pmsProductInfo.getProdName().equals("本地传输专线(异地受理)") || pmsProductInfo.getProdName().equals("本地传输专线") || pmsProductInfo.getProdName().equals("本地MPLS-VPN")){
                                OmsCustomDetails temlate = new OmsCustomDetails();
                                temlate.setTitle(pmsProductInfo.getProdName());
                                temlate.setContent("[{\"templateName\":\"A端安装地址\",\"templateValue\":\"\"},{\"templateName\":\"Z端安装地址\",\"templateValue\":\"\"}]");
                                temlate.setProductId(pmsProductInfo.getProdId());
                                temlate.setCreateDate(new Date());
                                temlate.setDeleteState("0");
                                temlate.setState("0");
                                temlate.setUpdateDate(new Date());
                                omsSellOrderService.saveOrupdateOmsCustomDetails(temlate);
                            }else{
                                OmsCustomDetails temlate = new OmsCustomDetails();
                                temlate.setTitle(pmsProductInfo.getProdName());
                                temlate.setContent("[{\"templateName\":\"安装地址\",\"templateValue\":\"\"}]");
                                temlate.setProductId(pmsProductInfo.getProdId());
                                temlate.setCreateDate(new Date());
                                temlate.setDeleteState("0");
                                temlate.setState("0");
                                temlate.setUpdateDate(new Date());
                                omsSellOrderService.saveOrupdateOmsCustomDetails(temlate);
                            }
                        }
                    }
                }
            }
            mapJson.put("code",1);
            mapJson.put("data","");
            mapJson.put("msg","数据增加成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("模板增加异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","数据增加异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     *  当前日期加上天数后的日期
     * @param num 为增加的天数
     * @return
     */
    public static String plusDay2(int num){
        Date d = new Date();
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String currdate = format.format(d);
        System.out.println("现在的日期是：" + currdate);
        Calendar ca = Calendar.getInstance();
        ca.add(Calendar.DATE, num);// num为增加的天数，可以改变的
        d = ca.getTime();
        String enddate = format.format(d);
        System.out.println("增加天数以后的日期：" + enddate);
        return enddate;
    }

    /**
     * 支付商订单查询接口
     */
    public void orderQuery() {
        String jsonInfo2 = getString("jsonInfo");
        if (jsonInfo2 == null || "".equals(jsonInfo2)) {
            Write("参数不能为空");
            return;
        }
        try {
            // 创建一个服务(service)调用(call)
            Service service = new Service();
            Call call = (Call) service.createCall();// 通过service创建call对象
            // 设置service所在URL
            call.setTargetEndpointAddress(new java.net.URL("http://*************:8080/EOM/services/voucherCenterWebService?wsdl"));
            // 方法名(processService)与MyService.java方法名保持一致
            call.setOperationName("selectOrder");
            call.addParameter("jsonInfo", XMLType.XSD_STRING, ParameterMode.IN);
            call.setReturnType(XMLType.XSD_STRING); // 返回值类型：String
            // Object 数组封装了参数，参数为"This is Test!",调用processService(String arg)
            Object[] obj = { jsonInfo2 };
            String ret = (String) call.invoke(obj);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(ret));
        }
        catch (Exception e) {
            logger.error("支付商订单查询接口失败:"+e.getMessage(),e);
            Write("调用接口失败");
        }
    }

    public void updatePRCContractInfo(){
        String id = getString("id");
        try {
            List<Map<String,Object>> list = omsSellOrderService.getOmsSellOrderAndContractInfo(id);
            List<String>  ls = new ArrayList<String>();
            for (Map<String, Object> stringStringMap : list) {
                String pdfatturl = stringStringMap.get("PDFATTURL").toString();
                String contractId = stringStringMap.get("ID").toString();
                String pdfName = stringStringMap.get("PDFURL").toString();
                if(null != pdfatturl && !"".equals(pdfatturl) && !"null".equals(pdfatturl)){
                    if(pdfatturl.indexOf("/EOMAPP") != -1){
                        File headPath = new File(pdfatturl);//获取文件夹路径
                        if(headPath.exists()){//判断文件是否存在
                            Map<String,String > rMap = HDFSUtils.readLocalFile2Hadoop(pdfatturl,pdfName);
                            ContractInfo contractInfo = customClauseContractService.queryContractInfoById(contractId);
                            contractInfo.setpDFAttUrl(rMap.get("file_path"));
                            customClauseContractService.saveContractInfo(contractInfo);
                        }else{
                            ls.add(contractId);
                        }
                    }
                }
            }
            logger.info("未修改合同数据条数："+ls.size()+"，未修改数据："+ls.toString());
            Write("未修改合同数据条数："+ls.size()+"，未修改数据："+ls.toString());
        } catch (Exception e) {
            logger.error("修改合同异常:"+e.getMessage(),e);
            e.printStackTrace();
            Write("修改合同异常:"+e.getMessage());
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:37
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 增加跟进内容
     */
    public void addFollowUp(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");//订单编号
            String description = getString("description");//描述
            String attachmentId = getString("attachmentId");//附件ID
            String userId = getString("userId");//登陆人ID
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderByOrderNo(orderNo);
            FollowUpOrder followUpOrder = new FollowUpOrder();
            followUpOrder.setAttmentIds(attachmentId);
            followUpOrder.setCreateDate(new Date());
            followUpOrder.setCreateName(USER.getEmployeeName());
            followUpOrder.setCreateNo(USER.getRowNo()+"");
            followUpOrder.setDescription(description);
            followUpOrder.setOrderId(oms.getId());
            followUpOrder.setDescriptionHtml(description);
            followUpOrder.setOrderNo(orderNo);
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
            /*if(oms.getState().equals(OmsSellOrder.TATE_RETURN)){//判断是否时订单经理驳回
                List<OmsOrderWorkbench> omsOrderWorkbenchByOrderNoAndStateList = omsSellOrderService.getOmsOrderWorkbenchList(orderNo);
                String workbenchId ="";
                for (OmsOrderWorkbench workbench : omsOrderWorkbenchByOrderNoAndStateList) {
                    if(null != workbench.getBak1() && workbench.getBak1().equals("DDBH") && !workbench.getState().equals("-1")){//判断是否时订单经理驳回
                        workbenchId = workbench.getId();
                        List<Bpms_riskoff_task> publicEntityTaskList = taskService.getPublicEntityTaskList(workbench.getId());
                        for (Bpms_riskoff_task bpmsRiskoffTask : publicEntityTaskList) {
                            if(null != bpmsRiskoffTask.getMemo() && bpmsRiskoffTask.getMemo().equals("预受理订单经理驳回发起流程")){//修改任务表数据
                                taskService.updateBpms_riskoff_task("添加跟进数据时修改流程",-1,bpmsRiskoffTask.getId());
                                List<WaitTask> waitTaskList = service.queryListWaitByTaskId(bpmsRiskoffTask.getId());//获取待办信息
                                for (WaitTask waitTask : waitTaskList) {
                                    if(!WaitTask.HAS_BEEN_COMPLETED.equals(waitTask.getState())){
                                        service.updateWait(waitTask, this.getRequest());
                                    }
                                }
                            }
                        }
                        taskService.updatebpmsRiskoffProcess(workbench.getId(),-1);//修改流程表数据
                        omsOrderWorkbenchService.updateOmsOrderWorkbench(workbench.getId(),-1);//修改工作台数据
                        commitOmsSellOrderData(oms,Integer.valueOf(oms.getOperateNo()),user);
                    }
                }
                oms.setState(OmsSellOrder.TATE_CONSTRUCTION);
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                followUpOrder.setType("1");
                followUpOrder.setWorkbenchId(workbenchId);
            }else{*/
                followUpOrder.setType("0");
            //}
            omsSellOrderService.saveFollowUpOrder(followUpOrder);
            if (!StringUtils.isEmpty(attachmentId)) {
                //判断是否上传了附件,获取前台提交的附件Id；
                String[] fileId = attachmentId.split(",");
                if (fileId.length > 0) {
                    for (int i = 0; i < fileId.length; i++) {
                        String url = EncryptionUtils.decrypt(fileId[i]);
                        String[] fie = url.split("\\\\");
                        int s = fie.length;
                        String name = fie[s-1];
                        //获取毫秒数
                        Long time = System.currentTimeMillis();
                        String pixstr =FileUpload.getFilePix(name);
                        String endFileName=time+pixstr;
                        String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                        StorageCfg storageCfg= attachmentService.queryStorageCfg();
                        //String endUrl=FileUpload.getFtpURL()+urlDate;
                        String endUrl=storageCfg.getFileName()+urlDate;
                        File headPath = new File(endUrl);//获取文件夹路径
                        if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                            headPath.mkdirs();
                        }
                        File startFile = new File(FileUpload.getPaperConURL()+url);
                        File endFile = new File(endUrl+endFileName);//获取文件夹路径
                        logger.info("这是预受理APP已存储文件地址："+FileUpload.getPaperConURL()+url);
                        logger.info("这是预受理PP需要移动的文件地址："+endUrl+endFileName);
                        try{
                            Files.move(Paths.get(FileUpload.getPaperConURL()+url),Paths.get(endUrl+endFileName),
                                    StandardCopyOption.REPLACE_EXISTING);
                            logger.info("移动文件成功");
                            Attachment attachmentEntity = new Attachment();
                            attachmentEntity.setAttachmentName(endFileName);// 防重名
                            attachmentEntity.setAttachmentUrl(urlDate+endFileName);
                            attachmentEntity.setUploadDate(new Date());
                            attachmentEntity.setRealName(endFileName);
                            attachmentEntity.setUploadUser(user);
                            attachmentEntity.setVersion(storageCfg.getId());
                            String attId = attachmentService.addEntity(attachmentEntity);
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(oms.getId());
                            sa.setAttachmentId(attId);
                            sa.setLink(OmsSellOrder.OMSSELLORDER);
                            claimForFundsService.saveSandA(sa);
                        }catch (Exception e){
                            logger.error("预受理APP移动文件出错："+e.getMessage(),e);
                            e.printStackTrace();
                        }
                    }
                }
            }
            mapJson.put("code",1);
            mapJson.put("data","");
            mapJson.put("msg","跟进成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            logger.error("跟进异常"+e.getMessage(),e);
            e.printStackTrace();
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","跟进异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * APP查看PDF或图片
     */
    public void downLoadFile(){
        try {
            String id = getString("id");
            String type = getString("type");
            if(type.equals("HT")){
                ContractInfo contractInfo = customClauseContractService.queryContractInfoById(id);
                if(contractInfo.getContractType().equals("PRC")){
                    byte[] data = HDFSUtils.readFileToByte(contractInfo.getpDFAttUrl());
                    String base64 = Base64.encodeBase64String(data);
                    String	base64Code = ZipUtils.gzip(base64);
                    this.Write(contractInfo.getPdfUrl().substring(0, contractInfo.getPdfUrl().lastIndexOf("."))+".pdf"+"@"+base64Code);
                }else{
                    byte[] data = FileUtil.toByteArray2(contractInfo.getPdfUrl());//"E:\\sftp\\7c9fa039-fdb6-4a7d-aef0-a9f4d830984a.pdf"
                    String base64 = Base64.encodeBase64String(data);
                    String	base64Code = ZipUtils.gzip(base64);
                    this.Write(contractInfo.getPdfUrl().substring(0, contractInfo.getPdfUrl().lastIndexOf("."))+".pdf"+"@"+base64Code);
                }
            }else{
                final Attachment entity = attachmentService.getAttachmentById(id);
                StorageCfg storageCfg= attachmentService.queryStorageCfgById(entity.getVersion());
                String path = storageCfg.getFileName()+entity.getAttachmentUrl();
                byte[] data = FileUtil.toByteArray2(path);
                String base64 = Base64.encodeBase64String(data);
                String	base64Code = ZipUtils.gzip(base64);
                String realName = entity.getRealName();
                String str1=realName.substring(0, realName.lastIndexOf("."));
                String str2=realName.substring(str1.length()+1, realName.length());
                if(str2.indexOf("jpg") != -1 || str2.indexOf("png") != -1 || str2.indexOf("jpeg") != -1){
                    this.Write(realName.substring(0, realName.lastIndexOf("."))+"."+str2+"@"+base64Code);
                }else{
                    this.Write("{\"result\":\"NO\",\"msg\":\"操作失败！\"}");
                }
            }
        } catch (Exception e) {
            logger.error("查看图片或PDF异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    /**
     * 手动更新纸质合同上传doop
     */
    public void uploadPRCContractFile(){
        try {
            List<Map<String, String>> listMap = omsSellOrderService.getContractInfoId();
            if(listMap.size()>0){
                for(int i=0;i<listMap.size();i++){
                    ContractInfo conInfo= omsSellOrderService.getContractInfoId(listMap.get(i).get("ID"));
                    File file = new File(conInfo.getpDFAttUrl());
                    if(file.exists()) {
                        String pdfname = "";
                        if (conInfo.getPdfUrl() == null) {
                            Long time = System.currentTimeMillis();
                            pdfname = time + ".pdf";
                        } else {
                            pdfname = conInfo.getPdfUrl();
                        }
                        Map<String, String> rMap = HDFSUtils.readLocalFile2Hadoop(conInfo.getpDFAttUrl(), pdfname);
                        if(!"".equals(rMap.get("file_path"))){
                            conInfo.setpDFAttUrl(rMap.get("file_path"));
                            conInfo.setSTATE(4);
                            customClauseContractService.saveContractInfo(conInfo);
                            file.delete();
                        }
                    }
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("纸质合同上传错误信息："+e.getMessage(),e);
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:52
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 获取工单中的产品列表
     */
    public void getProductsByOrderNo(){
        Map<String,Object> mapJson = new HashMap<>();
        try {
            String orderNo = getString("orderNo");
            List<Map<String, Object>> list = omsSellOrderService.getOmsOrderProductListByOrderNo(orderNo);
            if(null == list || list.isEmpty()){
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","未查询到产品数据");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            Map<String, Object> map = new HashMap<>();
            for (Map<String, Object> product : list) {
                PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(product.get("PROD_NO").toString(),product.get("LABEL_NO").toString());
                product.put("PROD_NAME",pmsproduct.getProdName());
            }
            mapJson.put("code",1);
            mapJson.put("data",list);
            mapJson.put("msg","产品查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("获取产品列表异常"+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","产品查询异常");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 15:54
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 查询集团
     */
    public void appQueryCustomer(){
        String groupCoding=getString("groupCoding");
        String groupName=getString("groupName");
        Map<String,Object> mapJson = new HashMap<>();
        try {
            if(isES) {
                //接口调用反馈数据信息
                GroupCustomer customer = NewGroupCustomerService.getInstance().getCustInfoQuery(groupCoding, groupName);
                ///判断集团是否存在
                if (customer.getGroupCoding() != null && customer.getGroupName() != null) {
                    GroupCustomer findDBCustomer = new GroupCustomer();
                    findDBCustomer = groupCustomerService.queryGroup(customer.getGroupCoding().trim());
                    //判断数据库是否存在集团客户信息
                    if (null == findDBCustomer) {
                        groupCustomerService.addGroupCustomer(customer);
                        findDBCustomer = customer; //赋值
                    } else {
                        //数据库存在数据则更新数据信息
                        findDBCustomer.setGroupCoding(customer.getGroupCoding());
                        findDBCustomer.setGroupName(customer.getGroupName());
                        findDBCustomer.setGroupLevel(customer.getGroupLevel());
                        findDBCustomer.setHomeRegion(customer.getHomeRegion());
                        findDBCustomer.setContactAddress(customer.getContactAddress());
                        findDBCustomer.setContacts(customer.getContacts());
                        findDBCustomer.setContactPhone(customer.getContactPhone());
                        findDBCustomer.setCity(customer.getCity());
                        findDBCustomer.setUser_name(customer.getUser_name());
                        findDBCustomer.setChinese_name(customer.getChinese_name());
                        findDBCustomer.setMobile_phone(customer.getMobile_phone());
                        findDBCustomer.setLongitude(customer.getLongitude());
                        findDBCustomer.setLatitude(customer.getLatitude());
                        groupCustomerService.updateGroupCustomer(findDBCustomer);
                    }
                    List<GroupCustomer> list = new ArrayList<GroupCustomer>();
                    list.add(findDBCustomer);
                    mapJson.put("code",1);
                    mapJson.put("data",list);
                    mapJson.put("msg","集团查询成功");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                }
            }else {
                List<GroupCustomer> map =integrationService.dolist(groupCoding,groupName);
                mapJson.put("code",1);
                mapJson.put("data",map);
                mapJson.put("msg","集团查询成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("App集团查询异常"+e.getMessage(),e);
            List<GroupCustomer> map =integrationService.dolist(groupCoding,groupName);
            mapJson.put("code",1);
            mapJson.put("data",map);
            mapJson.put("msg","集团查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }

    }

    //这个方法待确认
    public void addProduct(){
        Map<String,Object> mapJson =new HashMap<>();
        try {
            //判断上传文件的名称是否为空
            if (file1 != null) {
                InputStream is = new FileInputStream(file1);
                Workbook wb = new HSSFWorkbook(is);
                Sheet sheet = wb.getSheetAt(0);
                int column = sheet.getRow(0).getPhysicalNumberOfCells();
                System.out.println("这是列数" + column);
                //对读取Excel表格内容测试
                Map<Integer, Map<Integer,Object>> map = new HashMap<Integer, Map<Integer,Object>>();
                // 得到总行数
                int rowNum = sheet.getLastRowNum();
                Row row = sheet.getRow(0);
                int colNum = row.getPhysicalNumberOfCells();
                // 正文内容应该从第二行开始,第一行为表头的标题
                for (int i = 1; i <= rowNum; i++) {
                    row = sheet.getRow(i);
                    int j = 0;
                    Map<Integer,Object> cellValue = new HashMap<Integer, Object>();
                    while (j < colNum) {
                        Cell cell = row.getCell(j);
                        cell.setCellType(cell.CELL_TYPE_STRING);
                        Object obj = getCellFormatValue(cell);
                        cellValue.put(j, obj);
                        j++;
                    }
                    map.put(i, cellValue);
                }
                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                if (column > 0) {
                    for (int i = 1; i <= map.size(); i++) {
                        Map<String, Object> maps = new HashMap<String, Object>();
                        maps.put("prodName", map.get(i).get(0));// 号码
                        maps.put("prodId", map.get(i).get(1));// 用途
                        list.add(maps);
                    }
                }
                SimpleDateFormat sdf =   new SimpleDateFormat( " yyyy-MM-dd HH:mm:ss " );
                List<PmsProdPriceInfo> listPro = new ArrayList<>();
                for (Map<String, Object> objectMap : list) {
                    PmsProdPriceInfo priceInfo = new PmsProdPriceInfo();
                    if(objectMap.get("prodName").toString().indexOf("和办公") != -1){
                        priceInfo.setPrice("0");
                        priceInfo.setStatus(1);
                        priceInfo.setChineseIndex("HBG2.0ZF");
                        priceInfo.setEffDate(new Date());
                        priceInfo.setPrcDesc(objectMap.get("prodName").toString());
                        priceInfo.setPrcId(objectMap.get("prodId").toString());
                        priceInfo.setType("SD");
                        priceInfo.setSaleFlag("T");
                        priceInfo.setProdId("EAPCZ00004");
                        priceInfo.setPrcName(objectMap.get("prodName").toString());
                        priceInfo.setUpdateDate(new Date());
                    }
                    if(objectMap.get("prodName").toString().indexOf("泛智能") != -1){
                        priceInfo.setPrice("0");
                        priceInfo.setStatus(1);
                        priceInfo.setChineseIndex("FZN2.0GNF");
                        priceInfo.setEffDate(new Date());
                        priceInfo.setPrcDesc(objectMap.get("prodName").toString());
                        priceInfo.setPrcId(objectMap.get("prodId").toString());
                        priceInfo.setType("SD");
                        priceInfo.setSaleFlag("T");
                        priceInfo.setProdId("EAPCZ00007");
                        priceInfo.setPrcName(objectMap.get("prodName").toString());
                        priceInfo.setUpdateDate(new Date());
                    }
                    if(objectMap.get("prodName").toString().indexOf("集团统付") != -1){
                        priceInfo.setPrice("0");
                        priceInfo.setStatus(1);
                        priceInfo.setChineseIndex("JTTFZHYXHD");
                        priceInfo.setEffDate(new Date());
                        priceInfo.setPrcDesc(objectMap.get("prodName").toString());
                        priceInfo.setPrcId(objectMap.get("prodId").toString());
                        priceInfo.setType("SD");
                        priceInfo.setSaleFlag("T");
                        priceInfo.setProdId("EAPCZ00008");
                        priceInfo.setPrcName(objectMap.get("prodName").toString());
                        priceInfo.setUpdateDate(new Date());
                    }
                    listPro.add(priceInfo);
                }
                if(listPro.size() > 0 && !listPro.isEmpty()){
                    for (PmsProdPriceInfo priceInfo : listPro) {
                        omsSellOrderService.savePmsProdPriceInfo(priceInfo);
                    }
                }
                mapJson.put("code",1);
                mapJson.put("data","");
                mapJson.put("msg","插入成功，条数："+listPro.size());
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            } else {
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","文件为空");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            e.printStackTrace();
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","异常"+e);
            throw new RuntimeException("异常"+e);
        }
    }


    /**
     * @author: liyang
     * @date: 2021/8/31 15:56
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 保存评分评价
     */
    public void addScore() {
        HashMap json = new HashMap();

        try {
            String fid = this.getString("fid");
            String score1 = this.getString("score1");
            String score2 = this.getString("score2");
            String score3 = this.getString("score3");
            String opinion = this.getString("opinion");
            Map<String, Object> oso = this.omsSellOrderService.getOmsSellOrderById2(fid);
            Object state = oso.get("STATE");
            if (!state.equals("3") && !state.equals("4")) {
                if (!state.equals("5") && !state.equals("6")) {
                    json.put("msg", "评价失败\n\n该工单尚未完成");
                    this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
                } else {
                    json.put("msg", "评价失败!\n\n该工单已作废或被驳回");
                    this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
                }
            } else {
                OmsScore os = new OmsScore();
                os.setFID(fid);
                os.setScore1(score1);
                os.setScore2(score2);
                os.setScore3(score3);
                os.setOpinion(opinion);
                os.setTime(new Date());
                this.omsSellOrderService.saveScore(os);
                this.omsSellOrderService.updateOmsSellOrderState(fid);
                json.put("msg", "评价成功!\n\n感谢您的评价!");
                this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
            }
        } catch (Exception var10) {
            var10.printStackTrace();
        }

    }

    /**
     * 根据fid获取评分评价
     */
    public void getScore() {
        HashMap json = new HashMap();

        try {
            String fid = this.getString("fid");
            Map<String, Object> omss = this.omsSellOrderService.findByFid(fid);
            json.put("data", omss);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        } catch (Exception var4) {
            var4.printStackTrace();
        }
    }

    /**
     * 超时工单自动归档自动评分评价
     * 可删除
     */
    public void automaticGrading() {
        new HashMap();

        try {
            List<Map<String, Object>> list = this.omsSellOrderService.get5DaysOData();

            for(int i = 0; i < list.size(); ++i) {
                Map<String, Object> map = (Map)list.get(i);
                String state = (String)map.get("STATE");
                if (state.equals("3")) {
                    String id = (String)map.get("ID");
                    Map<String, Object> omss = this.omsSellOrderService.findByFid(id);
                    if (omss == null) {
                        OmsScore os = new OmsScore();
                        os.setFID(id);
                        os.setScore1("5");
                        os.setScore2("5");
                        os.setScore3("5");
                        os.setOpinion("工单超时未归档! 已自动归档,评价为非常满意");
                        os.setTime(new Date());
                        this.omsSellOrderService.saveScore(os);
                        this.omsSellOrderService.updateOmsSellOrderState(id);
                    }
                }
            }
        } catch (Exception var8) {
            var8.printStackTrace();
        }

    }



    private Object getCellFormatValue(Cell cell) {
        Object cellvalue = "";
        if (cell != null) {
            // 判断当前Cell的Type
            switch (cell.getCellType()) {
                case Cell.CELL_TYPE_NUMERIC:// 如果当前Cell的Type为NUMERIC
                case Cell.CELL_TYPE_FORMULA: {
                    // 判断当前的cell是否为Date
                    if (org.apache.poi.ss.usermodel.DateUtil.isCellDateFormatted(cell)) {
                        // 如果是Date类型则，转化为Data格式
                        // data格式是带时分秒的：2013-7-10 0:00:00
                        // cellvalue = cell.getDateCellValue().toLocaleString();
                        // data格式是不带带时分秒的：2013-7-10
                        Date date = cell.getDateCellValue();
                        cellvalue = date;
                    } else {// 如果是纯数字

                        // 取得当前Cell的数值
                        cellvalue = String.valueOf(cell.getNumericCellValue());
                    }
                    break;
                }
                case Cell.CELL_TYPE_STRING:// 如果当前Cell的Type为STRING
                    // 取得当前的Cell字符串
                    cellvalue = cell.getRichStringCellValue().getString();
                    break;
                default:// 默认的Cell值
                    cellvalue = "";
            }
        } else {
            cellvalue = "";
        }
        return cellvalue;
    }

    /*=============================================新增方法====================================================*/

    /**
     * @author: liyang
     * @date: 2021/8/24 16:18
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询待处理的环节待办
     */
    public void getOderSubLink(){
        Map<String,Object> mapJson=new HashMap<>();
        try{
            String userId=getString("userId");//用户ID
            String groupCode=getString("groupCode");//集团280
            String isUrgent=getString("isUrgent");//是否加急
            String orderSort = getString("orderSort");//排序1正序；2倒序
            PageRequest page = new PageRequest(getRequest());
            PageResponse response =omsSellOrderService.getOmsSellOrderPageBylink(page,userId,groupCode,isUrgent,orderSort);
            mapJson.put("code",1);
            mapJson.put("data",response);
            mapJson.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error("查询我创建的，我经手的列表错误信息："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }


    /**
     * @author: liyang
     * @date: 2021/8/24 16:18
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询我创建的，我经手的
     */
    public void getMyDemandList(){
        Map<String,Object> mapJson=new HashMap<>();
        try{
            String userId=getString("userId");//用户ID
            String tabDivide=getString("tabDivide");//tab卡，1我创建的，2我经手的
            String groupCode=getString("groupCode");//集团280
            String orderLink=getString("orderLink");//工单环节
            String isUrgent=getString("isUrgent");//加急状态
            String rangeTime=getString("rangeTime");//提交日期
            PageRequest page = new PageRequest(getRequest());
            PageResponse response =omsSellOrderService.getMyDemandList(page,userId,groupCode,isUrgent,tabDivide,orderLink,rangeTime);
            mapJson.put("code",1);
            mapJson.put("data",response);
            mapJson.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error("查询我创建的，我经手的列表错误信息："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/1 11:34
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询我的审批待审批列表
     */
    public void getOderMyApproval(){
        Map<String,Object> mapJson=new HashMap<>();
        try{
            String userId=getString("userId");//用户ID
            String userName=getString("userName");//审批发起人名称
            String isUrgent=getString("isUrgent");//加急状态
            String rangeTime=getString("rangeTime");//提交日期
            PageRequest page = new PageRequest(getRequest());
            PageResponse response =omsSellOrderService.getOderMyApproval(page,userId,userName,isUrgent,rangeTime);
            mapJson.put("code",1);
            mapJson.put("data",response);
            mapJson.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error("查询我创建的，我经手的列表错误信息："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/1 11:34
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询我的审批(我审批的，抄送我的)
     */
    public void getOderMyApprovalShOrCs(){
        Map<String,Object> mapJson=new HashMap<>();
        try{
            String userId=getString("userId");//用户ID
            String tabDivide=getString("tabDivide");//tab卡，1已审批，2抄送的
            String userName=getString("userName");//审批发起人名称
            String state=getString("state");//审批状态
            String orderState=getString("orderState");//工单状态
            String rangeTime=getString("rangeTime");//提交日期
            PageRequest page = new PageRequest(getRequest());
            PageResponse response =omsSellOrderService.getOderMyApprovalShOrCs(page,userId,userName,state,orderState,tabDivide,rangeTime);
            mapJson.put("code",1);
            mapJson.put("data",response);
            mapJson.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error("查询我创建的，我经手的列表错误信息："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","查询失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/9 14:55
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 更改需求单
     */
    public void updateOmsSellOrder(){
        Map<String,Object> mapJson=new HashMap<>();
        try{
            String id = getString("id");//需求单ID
            String json = getString("json");//资费json
            String userId = getString("userId");//登陆人ID
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(order.getLinkOrderNo());
            List<SingleAndAttachment> singleAndAttachmentList = omsSellOrderService.getSingleAndAttachmentList(order.getId());
            if (!"".equals(json) && json != null && json.length() > 0) {
                JSONArray jsonArray = JSONArray.fromObject(json);
                for (int i = 0; i < jsonArray.size(); i++) {
                    String jsonArrayStr = jsonArray.getString(i);
                    JSONObject obj = JSONObject.fromObject(jsonArrayStr);
                    OmsOrderProduct omsProduct = omsSellOrderService.getOmsOrderProduct(obj.getString("id"));
                    PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(obj.getString("prodPriceNo"));
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    PmsProductLabel pmslabel = omsSellOrderService.queryPmsProductLabel(pmsproduct.getLabelId());
                    omsProduct.setPrcName(pmsinfo.getPrcName());
                    omsProduct.setLabelNo(pmslabel.getLabelId());
                    omsProduct.setProdNo(pmsproduct.getProdId());
                    omsProduct.setPrcNo(pmsinfo.getPrcId());
                    omsProduct.setIsCont(pmsproduct.getConType());
                    omsProduct.setScore(pmslabel.getScore());
                    if(obj.containsKey("temlates")){
                        omsProduct.setTemlates(obj.getJSONArray("temlates").toString());
                    }
                    omsProduct.setDiscount(obj.getString("discount"));
                    omsProduct.setDescription(obj.getString("description"));
                    omsProduct.setHandleNo(String.valueOf(user.getRowNo()));
                    omsProduct.setHandleName(user.getEmployeeName());
                    omsProduct.setState(OmsOrderProduct.STATE_WAITING);
                    omsProduct.setHandleDate(new Date());
                    omsSellOrderService.saveOrupdateOmsOrderProduct(omsProduct);
                }
            }
            OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            if(omsDig!=null) {
                omsDig.setStatus(1);
                omsDig.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
            }
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(order.getOperateName());
            dig.setOper_no(Integer.parseInt(order.getOperateNo()));
            dig.setOper_date(new Date());
            dig.setMessage("");
            dig.setStatus(0);//1已处理，0未处理
            dig.setOper_role("ROLE_ODMR");//处理角色订单经理ROLE_ODMR
            dig.setCreator_role("ROLE_CUMR");//发起角色客户经理ROLE_CUMR
            dig.setLinkOrderNo(link.getLinkOrderNo());
            OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//根据待办id查询待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
            commitOmsSellOrderData(order,Integer.parseInt(order.getOperateNo()),user,"",rdig.getId());
            mapJson.put("code",1);
            mapJson.put("data",order.getOrderNo());
            mapJson.put("msg","需求单更改成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error("需求单更改错误信息："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","需求单更改失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/19 17:19
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 通知签订协议
     */
    public void noticeIsContract(){
        Map<String,Object> mapJson=new HashMap<>();
        try{
            String id = getString("id");//需求单ID
            String userId = getString("userId");//登陆人ID
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(order.getLinkOrderNo());
            List<OmsLinkDialogue> odgList = omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("5","工单合同关联",order.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return ;
                }
            }
            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(order.getCompanyNo(),"5");
            OmsOrderLink currentLink = new OmsOrderLink();
            currentLink.setCreator_name(user.getEmployeeName());//发起人
            currentLink.setCreator_no(user.getRowNo());//发起人工号
            currentLink.setCreator_date(new Date());//发起人时间(当前时间)
            currentLink.setOper_name(order.getCreateName());//操作人
            currentLink.setOper_no(Integer.parseInt(order.getCreateNo()));//操作人工号
            currentLink.setOper_date(new Date());//操作时间(当前时间)
            currentLink.setStatus(0);//状态(状态根据环节确定)
            currentLink.setLinkCode("5");//环节编码或者固定的环节编码
            currentLink.setLinkName("工单合同关联");//环节名称
            currentLink.setOrderNumber(order.getOrderNo());//需求单ID或者编码
            currentLink.setLinkOrderNo(IBM + taskService.getNumber());
            currentLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            omsSellOrderService.saveOrupdateOmsOrderLink(currentLink);
            order.setLinkOrderNo(currentLink.getLinkOrderNo());
            omsSellOrderService.saveOrupdateOmsSellOrder(order);

            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(order.getCreateName());
            dig.setOper_no(Integer.parseInt(order.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setMessage("");
            dig.setStatus(0);//1已处理，0未处理
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
            dig.setLinkOrderNo(currentLink.getLinkOrderNo());
            OmsLinkDialogue rdig =omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(order,Integer.parseInt(order.getCreateNo()),user,"1",rdig.getId());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            for(OmsLinkDialogue odg: odgList){
                odg.setStatus(1);
                omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,odg.getId());//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return ;
                }
            }

            mapJson.put("code",1);
            mapJson.put("data","1");
            mapJson.put("msg","通知客户经理【"+order.getCreateName()+"】签订协议成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }catch (Exception e){
            logger.error("通知签订协议失败信息："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","通知签订协议失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/16 9:52
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同确认   雷原加参数
     */
    public void confirmContract(){
        Map<String,Object> map =new HashMap<>();
        try {
            map.put("code",1);
            map.put("data","操作成功");
            map.put("msg","操作成功");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            map.put("code",-1);
            map.put("data","操作失败！"+e.getMessage());
            map.put("msg","操作失败！"+e.getMessage());
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/16 9:55
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同驳回  雷原加参数
     */
    public void rejectContract(){
        Map<String,Object> map = new HashMap<>();
        try {
            String id = getString("id");
            String mome = getString("desc");
            String userId = getString("userId");//登陆人ID
            String dialogueLinkId = getString("dialogueLinkId");//添加参数
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,dialogueLinkId);//根据待办id查询待办信息
            OmsLinkDialogue odg=null;
            if(wt!=null){
                odg = omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
            }else{
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderById(id);
            String linkOrderNo="";
            if(odg!=null){
                linkOrderNo=odg.getLinkOrderNo();
            }else{
                linkOrderNo=omsSellOrder.getLinkOrderNo();
            }
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(linkOrderNo);
            OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            if(omsDig!=null) {
                omsDig.setStatus(1);
                omsDig.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
            }
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(omsSellOrder.getCreateName());
            dig.setOper_no(Integer.parseInt(omsSellOrder.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setMessage(mome);
            dig.setStatus(0);//1已处理，0未处理
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
            dig.setLinkOrderNo(link.getLinkOrderNo());
            OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            String url = "jsp/demandOrder/orderInformation.jsp?id="+omsSellOrder.getId()+"&contractReject=1";
            commitBackLogData(rdig.getId(),omsSellOrder.getTitle(),Integer.valueOf(omsSellOrder.getCreateNo()),user,url);//订单经理确认驳回
            link.setStatus(0);
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","驳回合同成功");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("订单经理驳回异常",e.getMessage());
            e.printStackTrace();
            map.put("code",-1);
            map.put("data","");
            map.put("msg","驳回合同异常"+e.getMessage());
        }
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
    }

    /**
     * @author: liyang
     * @date: 2021/8/19 16:51
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否关联合同
     */
    public boolean getProductIsContract(OmsSellOrder order){
        try {
            List<OmsOrderProduct> listProduct = omsSellOrderService.getOmsOrderProductList(order.getOrderNo());
            if(listProduct.isEmpty()){
                return false;
            }else{
                int count=0;
                for(OmsOrderProduct oms : listProduct){
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(oms.getProdNo(),oms.getLabelNo());
                    if(!"0".equals(pmsproduct.getConType())){//如果为0的时候就不关联
                        ++count;
                    }
                }
                if(count>0){
                    return true;
                }else{
                    return false;
                }
            }
        }catch (Exception e){
            logger.error("查询是否订单经理发起审批工单被驳回错误信息："+e.getMessage(),e);
            return false;
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 9:58
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 跨环节进入业务办理环节
     */
    public void handleBus(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");
            String userId = getString("userId");//登陆人ID
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            List<OmsLinkDialogue> odgList = omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("7","业务办理",oms.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"7");
            OmsOrderLink nextStepLink = new OmsOrderLink();
            nextStepLink.setCreator_name(user.getEmployeeName());//发起人
            nextStepLink.setCreator_no(user.getRowNo());//发起人工号
            nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
            nextStepLink.setOper_name(oms.getOperateName());//操作人
            nextStepLink.setOper_no(Integer.parseInt(oms.getOperateNo()));//操作人工号
            nextStepLink.setOper_date(new Date());//操作时间(当前时间)
            nextStepLink.setStatus(0);//状态(状态根据环节确定)
            nextStepLink.setLinkCode("7");//环节编码或者固定的环节编码
            nextStepLink.setLinkName("业务办理");//环节名称
            nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
            nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
            nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);

            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(oms.getOperateName());
            dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
            dig.setOper_date(new Date());
            dig.setStatus(0);//1已处理，0未处理
            dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            dig.setOper_role("ROLE_ODMR");//处理角色订单经理
            dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
            OmsLinkDialogue rdig= omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),user,"",rdig.getId());//提交代办
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            for(OmsLinkDialogue odg: odgList){
                odg.setStatus(1);
                omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,odg.getId());//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("工单审批跳转业务办理环节错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 14:50
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 订单经理关闭工单
     */
    public void closeWorkOrder(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String mome = getString("desc");
            String userId = getString("userId");//登陆人ID
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            oms.setState("-1");
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("9","工单关闭",oms.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            List<OmsLinkDialogue> odgList = omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            for(OmsLinkDialogue odg: odgList){
                odg.setStatus(1);
                omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,odg.getId());//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }

            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"9");
            OmsOrderLink nextStepLink = new OmsOrderLink();
            nextStepLink.setCreator_name(user.getEmployeeName());//发起人
            nextStepLink.setCreator_no(user.getRowNo());//发起人工号
            nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
            nextStepLink.setOper_name(user.getEmployeeName());//操作人
            nextStepLink.setOper_no(user.getRowNo());//操作人工号
            nextStepLink.setOper_date(new Date());//操作时间(当前时间)
            nextStepLink.setStatus(0);//状态(状态根据环节确定)
            nextStepLink.setLinkCode("9");//环节编码或者固定的环节编码
            nextStepLink.setLinkName("工单关闭");//环节名称
            nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
            nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
            nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);

            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(oms.getCreateName());
            dig.setOper_no(Integer.parseInt(oms.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setStatus(0);//1已处理，0未处理
            dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            dig.setMessage(mome);
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理ROLE_ODMR
            OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(oms,Integer.parseInt(oms.getCreateNo()),user,"",rdig.getId());//提交代办
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("订单经理关闭工单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 15:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 客户经理作废需求单 (工单确认环节)(待办相关改变)
     */
    public void orderToVoid(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String mome = getString("desc");
            String userId = getString("userId");//登陆人ID
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("9","工单关闭",oms.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            OmsOrderLink nextStepLink = new OmsOrderLink();
            nextStepLink.setCreator_name(user.getEmployeeName());//发起人
            nextStepLink.setCreator_no(user.getRowNo());//发起人工号
            nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
            nextStepLink.setOper_name(user.getEmployeeName());//操作人
            nextStepLink.setOper_no(user.getRowNo());//操作人工号
            nextStepLink.setOper_date(new Date());//操作时间(当前时间)
            nextStepLink.setStatus(1);//状态(状态根据环节确定)
            nextStepLink.setLinkCode("9");//环节编码或者固定的环节编码
            nextStepLink.setLinkName("工单关闭");//环节名称
            nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
            nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
            oms.setState("-1");
            oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);

            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(user.getEmployeeName());
            dig.setOper_no(user.getRowNo());
            dig.setOper_date(new Date());
            dig.setStatus(1);//1已处理，0未处理
            dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            dig.setMessage(mome);
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            dig.setCreator_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            if(omsDigList.size()>0) {
                for(OmsLinkDialogue omsDig:omsDigList) {
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                    WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                    //结束当前待办
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        throw new Exception("查询待办失败，为null");
                    }
                }
            }

            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("客户经理作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 15:56
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 客户经理阅读作废需求单(工单关闭环节)
     */
    public void readOmsOrderSell(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String userId = getString("userId");//登陆人ID
            SystemUser user = systemUserService.getUserInfoRowNo(Integer.valueOf(userId));
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            oms.setState("-1");
            omsSellOrderService.saveOrupdateOmsSellOrder(oms);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            if(omsDig!=null) {
                omsDig.setStatus(1);
                omsDig.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
            }
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//根据待办id查询待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("客户经理阅读作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/3 17:16
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询配置的产品审批名称集合
     */
    public void getOmsProductApproveList(){
        Map<String,Object> map =new HashMap<>();
        try {
            String prcId = getString("prcId");
            String ste="";
            if(prcId.indexOf(",")!=-1){//包含
                String[] prArray = prcId.split(",");
                for(int i=0;i<prArray.length;i++){
                    PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(prArray[i]);
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    ste+=pmsproduct.getProdId()+",";
                }
            }else{//不包含
                if(prcId.length()>1){
                    PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(prcId);
                    PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
                    ste=pmsproduct.getProdId();
                }
            }
            String[] str=ste.split(",");
            List<Map<String,Object>> list= omsSellOrderService.getOmsProductApproveList(str);
            map.put("code",1);
            map.put("data",JSONHelper.SerializeWithNeedAnnotationDateFormat(list));
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("客户经理阅读作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/9/9 15:40
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 工单归档客户经理阅读
     */
    public void fileRead(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
            link.setStatus(1);
            link.setOper_date(new Date());
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            if(omsDig!=null) {
                omsDig.setStatus(1);
                omsDig.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
            }
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//根据待办id查询待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功!工单已完成归档");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("客户经理阅读作废需求单错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","操作失败!工单归档未完成");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/19 17:29
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 工单确认(待办相关改变)
     */
    public void processingToDo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String loginUserId = getString("userId");
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(loginUserId));
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(order.getLinkOrderNo());
            link.setOper_date(new Date());
            link.setStatus(1);
            omsSellOrderService.saveOrupdateOmsOrderLink(link);
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("4","工单审批",order.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(order.getCompanyNo(),"4");
            OmsOrderLink nextStepLink = new OmsOrderLink();
            nextStepLink.setCreator_name(user.getEmployeeName());//发起人
            nextStepLink.setCreator_no(user.getRowNo());//发起人工号
            nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
            nextStepLink.setOper_name(user.getEmployeeName());//操作人
            nextStepLink.setOper_no(user.getRowNo());//操作人工号
            nextStepLink.setOper_date(new Date());//操作时间(当前时间)
            nextStepLink.setStatus(0);//状态(状态根据环节确定)
            nextStepLink.setLinkCode("4");//环节编码或者固定的环节编码
            nextStepLink.setLinkName("工单审批");//环节名称
            nextStepLink.setOrderNumber(order.getOrderNo());//需求单ID或者编码
            nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
            nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
            order.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
            omsSellOrderService.saveOrupdateOmsSellOrder(order);
            omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(order.getCreateName());
            dig.setOper_no(Integer.parseInt(order.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setStatus(0);//1已处理，0未处理
            dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
            OmsLinkDialogue rdig = omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(order, Integer.parseInt(order.getCreateNo()), user, "", rdig.getId());

            OmsLinkDialogue digTwo = new OmsLinkDialogue();
            digTwo.setCreator_name(user.getEmployeeName());
            digTwo.setCreator_no(user.getRowNo());
            digTwo.setCreator_date(new Date());
            digTwo.setOper_name(order.getOperateName());
            digTwo.setOper_no(Integer.parseInt(order.getOperateNo()));
            digTwo.setOper_date(new Date());
            digTwo.setStatus(0);//1已处理，0未处理
            digTwo.setLinkOrderNo(nextStepLink.getLinkOrderNo());
            digTwo.setOper_role("ROLE_ODMR");//处理角色订单经理
            digTwo.setCreator_role("ROLE_ODMR");//发起角色订单经理
            OmsLinkDialogue rdigTwo = omsSellOrderService.saveOrupdateOmsLinkDialogue(digTwo);
            commitOmsSellOrderData(order, Integer.parseInt(order.getOperateNo()), user, "", rdigTwo.getId());
            List<OmsLinkDialogue> omsDigList= omsSellOrderService.getOmsLinkDialogueList(link.getLinkOrderNo());
            if(omsDigList.size()>0) {
                for(OmsLinkDialogue omsDig:omsDigList) {
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                    WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                    //结束当前待办
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        throw new Exception("查询待办失败，为null");
                    }
                }
            }
            map.put("code",1);
            map.put("data","操作成功");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error(e.getMessage(),e);
            map.put("code",-1);
            map.put("data","操作失败！"+e.getMessage());
            map.put("msg","操作失败！"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/5 17:24
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 工单驳回(待办相关改变)
     */
    public void orderManagerRejected(){
        Map<String,Object> map = new HashMap<>();
        try {
            String id = getString("id");
            String mome = getString("desc");
            String loginUserId = getString("userId");
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(loginUserId));
            OmsSellOrder omsSellOrder = omsSellOrderService.getOmsSellOrderById(id);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(omsSellOrder.getLinkOrderNo());
            OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(omsSellOrder.getCreateName());
            dig.setOper_no(Integer.parseInt(omsSellOrder.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setMessage(mome);
            dig.setStatus(0);//1已处理，0未处理
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理
            dig.setLinkOrderNo(link.getLinkOrderNo());
            OmsLinkDialogue rdig= omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            String url = "jsp/demandOrder/returnDemand.jsp?id="+omsSellOrder.getId();
            commitBackLogData(rdig.getId(),omsSellOrder.getTitle(),Integer.valueOf(omsSellOrder.getCreateNo()),user,url);//订单经理确认驳回
            if(omsDig!=null) {
                omsDig.setOper_date(new Date());
                omsDig.setStatus(1);
                omsSellOrderService.updateOmsLinkDialogue(omsDig);
            }
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","驳回成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("订单经理驳回异常",e.getMessage());
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常！"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }

    }

    /**
     * @author: liyang
     * @date: 2021/8/26 10:11
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 更改合同
     */
    public void undateOrderContract(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String omsOrderId = getString("omsOrderId");
            String userid = getString("userId");// 登陆人ID
            String dialogueLinkId = getString("dialogueLinkId");//添加参数
            ContractInfo contractInfo= omsSellOrderService.queryContractInfo(id);
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(omsOrderId);
            if(contractInfo!=null && omsOrderId!=null && !"".equals(omsOrderId)&& !"undefined".equals(omsOrderId)){
                SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(userid));
                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,dialogueLinkId);//根据待办id查询待办信息
                OmsLinkDialogue odg=null;
                if(wt!=null){
                    odg = omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
                String linkOrderNo="";
                if(odg!=null){
                    linkOrderNo=odg.getLinkOrderNo();
                }else{
                    linkOrderNo=oms.getLinkOrderNo();
                }
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(linkOrderNo);
                oms.setContractId(contractInfo.getId());
                oms.setModifyDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
                if(omsDig!=null){
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                }
                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(oms.getOperateName());
                dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(link.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
                commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),user,"1",rdig.getId());
            }else{
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到合同信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","更改合同成功，等待订单经理"+oms.getOperateName()+"确认!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("更改合同对象异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","更改合同失败"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            Write("1");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 18:48
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 客户经理关联合同
     */
    public void queryContractInfo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id = getString("id");
            String omsOrderId = getString("omsOrderId");
            String userid = getString("userId");// 登陆人ID
            ContractInfo contractInfo= omsSellOrderService.queryContractInfo(id);
            OmsSellOrder oms = omsSellOrderService.getOmsSellOrderById(omsOrderId);
            if(contractInfo!=null && omsOrderId!=null && !"".equals(omsOrderId)&& !"undefined".equals(omsOrderId)){
                SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(userid));
                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(oms.getLinkOrderNo());
                oms.setContractId(contractInfo.getId());
                oms.setModifyDate(new Date());
                link.setStatus(1);
                link.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                String IBM = "";
                List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("6","工单合同确认",oms.getOrderNo());
                if(existence!=null){
                    if(existence.size()>0){
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return ;
                    }
                }
                OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(oms.getCompanyNo(),"6");
                OmsOrderLink nextStepLink = new OmsOrderLink();
                nextStepLink.setCreator_name(user.getEmployeeName());//发起人
                nextStepLink.setCreator_no(user.getRowNo());//发起人工号
                nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                nextStepLink.setOper_name(oms.getOperateName());//操作人
                nextStepLink.setOper_no(Integer.parseInt(oms.getOperateNo()));//操作人工号
                nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                nextStepLink.setStatus(0);//状态(状态根据环节确定)
                nextStepLink.setLinkCode("6");//环节编码或者固定的环节编码
                nextStepLink.setLinkName("工单合同确认");//环节名称
                nextStepLink.setOrderNumber(oms.getOrderNo());//需求单ID或者编码
                nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                oms.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码
                omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                omsSellOrderService.saveOrupdateOmsSellOrder(oms);
                OmsLinkDialogue omsDig= omsSellOrderService.getOmsLinkDialogueByLinkOrderNo(link.getLinkOrderNo());
                if(omsDig!=null){
                    omsDig.setStatus(1);
                    omsDig.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(omsDig);
                }
                OmsLinkDialogue dig = new OmsLinkDialogue();
                dig.setCreator_name(user.getEmployeeName());
                dig.setCreator_no(user.getRowNo());
                dig.setCreator_date(new Date());
                dig.setOper_name(oms.getOperateName());
                dig.setOper_no(Integer.parseInt(oms.getOperateNo()));
                dig.setOper_date(new Date());
                dig.setStatus(0);//1已处理，0未处理
                dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,omsDig.getId());//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息,请联系管理员核对");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
                commitOmsSellOrderData(oms,Integer.parseInt(oms.getOperateNo()),user,"1",rdig.getId());
            }else{
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到合同信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data",contractInfo);
            map.put("msg","合同信息添加成功,等待订单经理"+oms.getOperateName()+"确认!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("查询合同对象异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","合同信息补录失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }
    public String ClobToString(Clob clob) throws SQLException, IOException {
        String reString = "";
        Reader is = clob.getCharacterStream();
        BufferedReader br = new BufferedReader(is);
        String s = br.readLine();
        StringBuffer sb = new StringBuffer();
        while (s != null) {
            sb.append(s);
            s = br.readLine();
        }
        reString = sb.toString();
        if(br!=null){
            br.close();
        }
        if(is!=null){
            is.close();
        }
        return reString;
    }

    /**
     * @author: liyang
     * @date: 2021/12/28 14:51
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询驳回人员信息
     */
    public void getRejectUserList(){//新方法
        Map<String,Object> map = new HashMap<>();
        try {
            String taskId = getString("taskId");
            String userId = getString("userId");
            String id = getString("id");
            logger.info("查询驳回人员信息登录人ID"+userId);
            //查询审批人
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(userId));
            logger.info("查询驳回人员信息登录人信息:"+user.getEmployeeName());
            Bpms_riskoff_task task = taskService.getBpms_riskoff_task(taskId);//查询本身任务
            JSONArray arry = new JSONArray();
            if(task.getBak2()==null){
                JSONObject obj = new JSONObject();
                obj.put("userName",task.getOper_name());
                obj.put("userId",task.getOper_no());
                arry.add(obj);
            }else{
                boolean falg =true;
                while (falg){
                    if(task!=null&&user!=null){
                        logger.info("查询驳回人员信息task.getBak2():"+task.getBak2());
                        logger.info("查询驳回人员信息task.getOper_no:"+task.getOper_no());
                        logger.info("查询驳回人员信息user.getRowNo():"+user.getRowNo());
                        if(task.getBak2()==null&&task.getOper_no()!=user.getRowNo()){
                            JSONObject obj = new JSONObject();
                            obj.put("userName",task.getOper_name());
                            obj.put("userId",task.getOper_no());
                            arry.add(obj);
                            falg=false;
                        }else{
                            if(!taskId.equals(task.getId())&&task.getOper_no()!=user.getRowNo()){
                                JSONObject obj = new JSONObject();
                                obj.put("userName",task.getOper_name());
                                obj.put("userId",task.getOper_no());
                                task=taskService.getBpms_riskoff_task(task.getBak2());
                                arry.add(obj);
                                falg=true;
                            }else{
                                task=taskService.getBpms_riskoff_task(task.getBak2());
                                falg=true;
                            }
                        }
                    }else{
                        OmsOrderWorkbench oms =omsSellOrderService.getOmsOrderWorkbench(id);
                        JSONObject obj = new JSONObject();
                        obj.put("userName",oms.getOperateName());
                        obj.put("userId",oms.getOperateNo());
                        arry.add(obj);
                        falg=false;
                    }
                }
            }
            map.put("code",1);
            map.put("data",removeRepeatData(arry));
            map.put("msg","查询驳回人员列表成功");
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        } catch (Exception e) {
            logger.error("查询驳回人员列表异常："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询驳回人员列表失败");
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }

    public static JSONArray removeRepeatData(JSONArray array) {//新方法
        JSONArray arrayTemp = new JSONArray();
        for(int i = 0;i < array.size();i++){
            if(i==0){
                arrayTemp.add(array.get(i));
            }else{
                int numJ = 0;
                for(int j = 0;j < arrayTemp.size(); j++){
                    JSONObject newJsonObjectI = (JSONObject)array.get(i);
                    JSONObject newJsonObjectJ = (JSONObject)arrayTemp.get(j);
                    String index_idI = newJsonObjectI.get("userId").toString();
                    String valueI = newJsonObjectI.get("userName").toString();
                    String index_idJ = newJsonObjectJ.get("userId").toString();
                    if(index_idI.equals(index_idJ)){
                        arrayTemp.remove(j);
                        JSONObject newObject = new JSONObject();
                        newObject.put("userId", index_idI);
                        newObject.put("userName", valueI);
                        arrayTemp.add(newObject);
                        break;
                    }
                    numJ++;
                }
                if(numJ-1 == arrayTemp.size()-1){
                    arrayTemp.add(array.get(i));
                }
            }
        }
        return arrayTemp;
    }


    /**
     * @author: liyang
     * @date: 2022/1/10 16:49
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 更改业务办理人员
     */
    public void businessUser(){//新方法
        Map<String,Object> map =new HashMap<>();
        try {
            String dialogueLinkId=getString("dialogueLinkId");
            String userId=getString("userId");//登录人ID
            String businessUserId=getString("businessUserId");//业务办理人员ID
            SystemUser user =systemUserService.getByUserInfoRowNo(Integer.parseInt(userId));
            SystemUser businessUser =systemUserService.getByUserInfoRowNo(Integer.parseInt(businessUserId));
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,dialogueLinkId);//获取待办信息
            if (wt != null) {
                OmsLinkDialogue dig=omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                OmsOrderLink link=omsSellOrderService.getOmsOrderLinkByLinkOrderNo(dig.getLinkOrderNo());
                OmsSellOrder order = omsSellOrderService.getOmsSellOrderByOrderNo(link.getOrderNumber());
                if(dig!=null){
                    dig.setOper_no(businessUser.getRowNo());
                    dig.setOper_name(businessUser.getEmployeeName());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                    link.setOper_no(businessUser.getRowNo());
                    link.setOper_name(businessUser.getEmployeeName());
                    omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    order.setOperateNo(String.valueOf(businessUser.getRowNo()));
                    order.setOperateName(businessUser.getEmployeeName());
                    omsSellOrderService.saveOrupdateOmsSellOrder(order);
                    service.updateWait(wt, this.getRequest());
                    commitOmsSellOrderData(order,businessUser.getRowNo(),user,"",dig.getId());//提交代办
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","查询当前子环节失败");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","更改业务办理人员成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("更改业务办理人员失败异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","更改业务办理人员失败"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 10:40
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 补录资费BOSS信息
     */
    public void supplementary(){//新方法
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");
            String bossNo=getString("bossNo");
            String bossPhoneNo=getString("bossPhoneNo");
            OmsOrderProduct oms = omsSellOrderService.getOmsOrderProduct(id);
            oms.setBossNo(bossNo);
            oms.setBossPhoneNo(bossPhoneNo);
            omsSellOrderService.saveOrupdateOmsOrderProduct(oms);
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderByOrderNo(oms.getOrderNo());
            omsOrder.setModifyDate(new Date());
            omsOrder.setOperateHandleDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);
            map.put("code",1);
            map.put("data","");
            map.put("msg","BOSS信息补录成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("补录资费BOSS信息异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","BOSS信息补录失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 16:05
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 推送BOSS资费数据
     */
    public void pushBoss(){//新方法
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");
            OmsOrderProduct oms = omsSellOrderService.getOmsOrderProduct(id);
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderByOrderNo(oms.getOrderNo());
            omsOrder.setModifyDate(new Date());
            omsOrder.setOperateHandleDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);
            PmsProdPriceInfo pmsinfo = omsSellOrderService.queryPmsProdPriceInfo(oms.getPrcNo());
            PmsProductInfo pmsproduct = omsSellOrderService.queryPmsProductInfo(pmsinfo.getProdId(),pmsinfo.getLabelId());
            PmsProductLabel pmslabel = omsSellOrderService.queryPmsProductLabel(pmsproduct.getLabelId());
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getOperateNo()));
            ContractInfo contract=null;
            if(omsOrder.getContractId()!=null){
                contract = omsSellOrderService.getContractInfoId(omsOrder.getContractId());
            }
            if("1".equals(pmsinfo.getIsPushBoss())){
                SystemUser accountOpenUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getCreateNo()));
                Map<String,Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(accountOpenUser.getBossUserName());
                Result resultOne= BusiOppService.getInstance().AccountOpenArrears(accountOpenUser.getBossUserName(),
                        omsOrder.getUnitId(),
                        "6622",
                        mapcfm.get("REGION_ID").toString(),
                        accountOpenUser.getBossUserName(),
                        "6633");
                if(ResultCode.SUCCESS.code()==resultOne.getCode()){
                    JSONObject jsonObject = JSONObject.fromObject(resultOne);
                    JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
                    String chk_flag = data.get("CHK_FLAG").toString();
                    String chk_msg = data.get("CHK_MSG").toString();
                    if("Y".equals(chk_flag)){
                        if("1".equals(oms.getGrpOrdId())){
                            String grpOrdId ="";
                            Result result= GrpOrderIdAcceptSrv.getInstance().createGrpOrder(omsOrder.getUnitId(),oms.getSerialNumber(),"D001",
                                    pmsinfo.getPrcName(),
                                    //"ahhgoa",
                                    user.getBossUserName(),
                                    String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS", new Date()),pmslabel.getSubLabelType());
                            logger.info("一键甩单注册统一接收参数:"+result.getData());
                            if(ResultCode.SUCCESS.code()==result.getCode()){
                                JSONObject root=JSONObject.fromObject(JSONObject.fromObject(result.getData()).get("ROOT"));
                                JSONObject body=JSONObject.fromObject(root.get("BODY"));
                                if("0".equals(body.get("RETURN_CODE"))){
                                    JSONObject outData=JSONObject.fromObject(JSONObject.fromObject(root.get("BODY")).get("OUT_DATA"));
                                    grpOrdId = outData.get("grpOrdId").toString();
                                    //grpOrdId=taskService.getNumber();
                                    logger.info("一键甩单注册统一ID:"+grpOrdId);
                                    if("".equals(grpOrdId)){
                                        map.put("code",-1);
                                        map.put("data","");
                                        map.put("msg","统一ID注册失败,grpOrdId字段为空");
                                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                        return;
                                    }else{
                                        SystemUser createUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getCreateNo()));
                                        Result returnResult =OmsSellOrderSrv.getInstance().addPreOrderInfoEntity(grpOrdId,omsOrder.getUnitId(),omsOrder.getTitle(),
                                                oms.getSerialNumber(),pmsproduct.getProdId(),pmsinfo.getPrcId(),
                                                pmslabel.getSubLabelType(),contract==null?"":contract.getContractId(),pmsproduct.getProdName(),pmsinfo.getPrcName(),
                                                //"ahhgoa",
                                                user.getBossUserName(),
                                                createUser.getBossUserName(),
                                                oms.getDescription(),
                                                oms.getTemlates(),pmslabel.getCategoryId(),pmslabel.getLabelId());
                                        if(ResultCode.SUCCESS.code()==returnResult.getCode()){
                                            JSONObject returnRoot=JSONObject.fromObject(JSONObject.fromObject(returnResult.getData()).get("ROOT"));
                                            JSONObject returnBody=JSONObject.fromObject(returnRoot.get("BODY"));
                                            if("0".equals(returnBody.getString("RETURN_CODE"))){
                                                oms.setGrpOrdId(grpOrdId);
                                                oms.setIsPushBoss(1);
                                                omsSellOrderService.saveOrupdateOmsOrderProduct(oms);
                                            }else{
                                                map.put("code",-1);
                                                map.put("data","");
                                                map.put("msg","推送BOSS失败："+returnBody.get("RETURN_MSG"));
                                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                                return;
                                            }
                                        }else{
                                            map.put("code",-1);
                                            map.put("data","");
                                            map.put("msg","推送BOSS失败："+returnResult.getMessage());
                                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                            return;
                                        }
                                    }
                                }else{
                                    map.put("code",-1);
                                    map.put("data","");
                                    map.put("msg","统一ID注册失败"+body.get("RETURN_MSG"));
                                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                    return;
                                }
                            }else{
                                map.put("code",-1);
                                map.put("data","");
                                map.put("msg","统一ID注册失败"+result.getMessage());
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                return;
                            }
                        }else{
                            SystemUser createUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getCreateNo()));
                            Result returnResult =OmsSellOrderSrv.getInstance().addPreOrderInfoEntity(oms.getGrpOrdId(),omsOrder.getUnitId(),omsOrder.getTitle(),
                                    oms.getSerialNumber(),pmsproduct.getProdId(),pmsinfo.getPrcId(),
                                    pmslabel.getSubLabelType(),contract==null?"":contract.getContractId(),pmsproduct.getProdName(),pmsinfo.getPrcName(),
                                    //"ahhgoa",
                                    user.getBossUserName(),
                                    createUser.getBossUserName(),
                                    oms.getDescription(),
                                    oms.getTemlates(),pmslabel.getCategoryId(),pmslabel.getLabelId());
                            if(ResultCode.SUCCESS.code()==returnResult.getCode()){
                                JSONObject returnRoot=JSONObject.fromObject(JSONObject.fromObject(returnResult.getData()).get("ROOT"));
                                JSONObject returnBody=JSONObject.fromObject(returnRoot.get("BODY"));
                                if("0".equals(returnBody.getString("RETURN_CODE"))){
                                    oms.setIsPushBoss(1);
                                    omsSellOrderService.saveOrupdateOmsOrderProduct(oms);
                                }else{
                                    map.put("code",-1);
                                    map.put("data","");
                                    map.put("msg","推送BOSS失败："+returnBody.get("RETURN_MSG"));
                                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                    return;
                                }
                            }else{
                                map.put("code",-1);
                                map.put("data","");
                                map.put("msg","推送BOSS失败："+returnResult.getMessage());
                                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                                return;
                            }
                        }
                    }else{
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","当前集团已欠费，请联系客户经理处理欠费以后再推送："+chk_msg);
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return;
                    }
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg",resultOne.getMessage());
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","推送BOSS成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("推送资费BOSS信息异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","推送BOSS失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/11 16:28
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否能归档
     */
    public void queryWhetherItCanBeArchived(){//新方法
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");//需求单ID
            String dialogueLinkId = getString("dialogueLinkId");//当前列表代办ID
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderById(id);
            List<OmsOrderProduct> list = omsSellOrderService.getOmsOrderProductList(omsOrder.getOrderNo());
            List<OmsOrderLink> links = omsSellOrderService.getOmsOrderLinkByOrderNumberList(omsOrder.getOrderNo());
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,dialogueLinkId);//根据待办id查询待办信息
            OmsLinkDialogue odg=omsSellOrderService.getOmsLinkDialogueById(dialogueLinkId);
            OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(odg.getLinkOrderNo());
            int type=0;
            if(list.size()>0){
                for(OmsOrderProduct pro:list){
                    if(pro.getGrpOrdId()!=null){
                        if(omsOrder.getDemandType()==0){
                            if("1".equals(pro.getGrpOrdId())){
                                type=1;
                            }else{
                                if(pro.getIsPushBoss()==0){
                                    type=1;
                                }
                            }
                        }else{
                            if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                                type=1;
                            }
                        }
                    }else{
                        if(pro.getBossNo()==null&&pro.getBossPhoneNo()==null){
                            type=1;
                        }
                    }
                }
            }
            if(type==0){
                if(odg!=null) {
                    odg.setStatus(1);
                    odg.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                }
                link.setStatus(1);
                link.setOper_date(new Date());
                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                //结束当前待办
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","未查询到待办信息");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
                int linkCode=0;
                int linkStatus=0;
                String data="0";
                if(omsOrder.getContractId()==null){
                    data="1";
                }else{
                    for(OmsOrderLink l:links){
                        if("6".equals(l.getLinkCode())){
                            linkCode=1;
                            if(l.getStatus()==0){
                                linkStatus=1;
                            }
                        }
                    }
                    if(linkCode!=1&&linkStatus!=1){
                        data="1";
                    }else{
                        //这里可能有两种情况
                        //1：全部推送和补录以后 推送的数据BOSS没有回调，那么我们就只能结束代办不能归档
                        //2：全部推送和补录以后 推送的数据BOSS全部回调，那么我们就可以结束代办和归档
                        for(OmsOrderProduct pro:list){
                            if(pro.getGrpOrdId()!=null){
                                if(omsOrder.getDemandType()==0){
                                    if(!"1".equals(pro.getGrpOrdId())){
                                        if(pro.getIsPushBoss()==1){
                                            if(pro.getBossCallback()==null){
                                                data="1";
                                            }else{
                                                if(pro.getBossCallback()==1){
                                                    data="1";
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                map.put("code",1);
                map.put("data",data);
                map.put("msg","操作成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }else{
                OmsOrderLink linkTwo = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(omsOrder.getLinkOrderNo());
                if("6".equals(linkTwo.getLinkCode())){
                    String IBM = "";
                    List<Object[]> sone = taskService.getCompayIBM(Integer.parseInt(omsOrder.getCreateNo()));
                    for (int i = 0; i < sone.size(); i++) {
                        IBM = (String) sone.get(i)[2];
                    }
                    OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(omsOrder.getCompanyNo(),"7");
                    OmsOrderLink nextStepLink = new OmsOrderLink();
                    nextStepLink.setCreator_name(omsOrder.getCreateName());//发起人
                    nextStepLink.setCreator_no(Integer.parseInt(omsOrder.getCreateNo()));//发起人工号
                    nextStepLink.setCreator_date(new Date());//发起人时间(当前时间)
                    nextStepLink.setOper_name(omsOrder.getOperateName());//操作人
                    nextStepLink.setOper_no(Integer.parseInt(omsOrder.getOperateNo()));//操作人工号
                    nextStepLink.setOper_date(new Date());//操作时间(当前时间)
                    nextStepLink.setStatus(0);//状态(状态根据环节确定)
                    nextStepLink.setLinkCode("7");//环节编码或者固定的环节编码
                    nextStepLink.setLinkName("业务办理");//环节名称
                    nextStepLink.setOrderNumber(omsOrder.getOrderNo());//需求单ID或者编码
                    nextStepLink.setLinkOrderNo(IBM + taskService.getNumber());
                    nextStepLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                    omsOrder.setLinkOrderNo(nextStepLink.getLinkOrderNo());//环节编码

                    OmsLinkDialogue dig = new OmsLinkDialogue();
                    dig.setCreator_name(omsOrder.getCreateName());
                    dig.setCreator_no(Integer.parseInt(omsOrder.getCreateNo()));
                    dig.setCreator_date(new Date());
                    dig.setOper_name(omsOrder.getOperateName());
                    dig.setOper_no(Integer.parseInt(omsOrder.getOperateNo()));
                    dig.setOper_date(new Date());
                    dig.setStatus(0);//1已处理，0未处理
                    dig.setLinkOrderNo(nextStepLink.getLinkOrderNo());
                    dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                    dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                    OmsLinkDialogue rdig= omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                    SystemUser hanDuser=systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getCreateNo()));
                    commitOmsSellOrderData(omsOrder,Integer.parseInt(omsOrder.getOperateNo()),hanDuser,"",rdig.getId());//提交代办
                    omsSellOrderService.saveOrupdateOmsOrderLink(nextStepLink);
                    if(odg!=null) {
                        odg.setStatus(1);
                        odg.setOper_date(new Date());
                        omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                    }
                    link.setStatus(1);
                    link.setOper_date(new Date());
                    omsSellOrderService.saveOrupdateOmsOrderLink(link);
                    //结束当前待办
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        map.put("code",-1);
                        map.put("data","");
                        map.put("msg","未查询到待办信息");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                        return;
                    }
                }else{
                    if("6".equals(link.getLinkCode())){
                        if(odg!=null) {
                            odg.setStatus(1);
                            odg.setOper_date(new Date());
                            omsSellOrderService.saveOrupdateOmsLinkDialogue(odg);
                        }
                        link.setStatus(1);
                        link.setOper_date(new Date());
                        omsSellOrderService.saveOrupdateOmsOrderLink(link);
                        //结束当前待办
                        if (wt != null) {
                            service.updateWait(wt, this.getRequest());
                        } else {
                            map.put("code",-1);
                            map.put("data","");
                            map.put("msg","未查询到待办信息");
                            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                            return;
                        }
                    }
                }
                map.put("code",1);
                map.put("data","1");
                map.put("msg","操作成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }
        }catch (Exception e){
            logger.error("推送资费BOSS归档信息异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/11 16:25
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO  自动归档方法
     */
    public void orderComplete(){//新方法
        Map<String,Object> map =new HashMap<>();
        try {
            String id=getString("id");
            OmsSellOrder omsOrder = omsSellOrderService.getOmsSellOrderById(id);
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(omsOrder.getOperateNo()));
            String IBM = "";
            List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            List<OmsOrderLink> existence =omsSellOrderService.getOmsOrderLinkByCode("8","工单归档",omsOrder.getOrderNo());
            if(existence!=null){
                if(existence.size()>0){
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","当前环节已完毕,环节异常,请联系系统管理员");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return ;
                }
            }
            OmsOrderLink endLink = new OmsOrderLink();
            endLink.setCreator_name(user.getEmployeeName());//发起人
            endLink.setCreator_no(user.getRowNo());//发起人工号
            endLink.setCreator_date(new Date());//发起人时间(当前时间)
            endLink.setOper_name(user.getEmployeeName());//操作人
            endLink.setOper_no(user.getRowNo());//操作人工号
            endLink.setOper_date(new Date());//操作时间(当前时间)
            endLink.setStatus(0);//状态(状态根据环节确定)
            endLink.setLinkCode("8");//环节编码或者固定的环节编码
            endLink.setLinkName("工单归档");//环节名称
            endLink.setOrderNumber(omsOrder.getOrderNo());//需求单ID或者编码
            endLink.setLinkOrderNo(IBM + taskService.getNumber());
            omsSellOrderService.saveOrupdateOmsOrderLink(endLink);
            omsOrder.setState("1");
            omsOrder.setLinkOrderNo(endLink.getLinkOrderNo());
            omsOrder.setCompleteDate(new Date());
            omsSellOrderService.saveOrupdateOmsSellOrder(omsOrder);
            //发待办
            OmsLinkDialogue dig = new OmsLinkDialogue();
            dig.setCreator_name(user.getEmployeeName());
            dig.setCreator_no(user.getRowNo());
            dig.setCreator_date(new Date());
            dig.setOper_name(omsOrder.getCreateName());
            dig.setOper_no(Integer.parseInt(omsOrder.getCreateNo()));
            dig.setOper_date(new Date());
            dig.setStatus(0);//1已处理，0未处理
            dig.setLinkOrderNo(endLink.getLinkOrderNo());
            dig.setOper_role("ROLE_CUMR");//处理角色客户经理ROLE_CUMR
            dig.setCreator_role("ROLE_ODMR");//发起角色订单经理ROLE_ODMR
            OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
            commitOmsSellOrderData(omsOrder,Integer.parseInt(omsOrder.getCreateNo()),user,"",rdig.getId());//提交代办
            map.put("code",1);
            map.put("data","");
            map.put("msg","需求单已归档");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("推送资费BOSS归档信息异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","异常"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/1/14 19:59
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 根据代办查询当前的任务属于哪个环节
     */
    public void queryOmsOrderLink(){//新方法
        Map<String,Object> map =new HashMap<>();
        try {
            String DIALOGUELINKID = getString("dialogueLinkId");
            WaitTask wt = service.queryWaitTaskIdAndCode(OmsSellOrder.OMSSELLORDER,DIALOGUELINKID);//根据待办id查询待办信息
            if (wt != null) {
                OmsLinkDialogue dig=omsSellOrderService.getOmsLinkDialogueById(wt.getTaskId());
                OmsOrderLink link=omsSellOrderService.getOmsOrderLinkByLinkOrderNo(dig.getLinkOrderNo());
                if(dig!=null){
                    map.put("OmsLinkDialogue",dig);
                    map.put("OmsOrderLink",link);
                }else{
                    map.put("code",-1);
                    map.put("data","");
                    map.put("msg","查询当前子环节失败");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                    return;
                }
            }
            map.put("code",1);
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("查询合同对象异常"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询失败"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/3/14 10:46
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 关联订单经理
     */
    public void approveLinkPick(){
        Map<String,Object> map =new HashMap<>();
        try {
            String userId=getString("userId");
            String id = getString("id");
            String isSupport = getString("isSupport");
            OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(id);
            SystemUser user=systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
            if(order!=null){
                order.setOperateNo(String.valueOf(user.getRowNo()));
                order.setOperateName(user.getEmployeeName());
                order.setIsSupport(isSupport);
                order.setOperateDate(new Date());
                omsSellOrderService.saveOrupdateOmsSellOrder(order);
            }else{
                map.put("code",-1);
                map.put("data","");
                map.put("msg","查询当前工单失败");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","提交成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } catch (Exception e) {
            logger.error("提交失败"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","提交失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }
}
