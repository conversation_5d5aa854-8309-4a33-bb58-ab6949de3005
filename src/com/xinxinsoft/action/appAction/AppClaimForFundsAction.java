package com.xinxinsoft.action.appAction;

import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.xinxinsoft.entity.GroupAccount.GroupHipAccount;
import com.xinxinsoft.entity.GroupAccount.GroupRelations;
import com.xinxinsoft.entity.PreinvApply.InternetOfThingsDet;
import com.xinxinsoft.entity.PreinvApply.PreinvApplyDet;
import com.xinxinsoft.entity.PreinvApply.ValuableCardDet;
import com.xinxinsoft.entity.claimForFundModel.ClaimForFundModel;
import com.xinxinsoft.entity.claimForFundModel.ClaimForFundModelDet;
import com.xinxinsoft.entity.claimForFundModel.ClaimReplaceModelDet;
import com.xinxinsoft.entity.claimForFunds.*;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.marketingActivities.QuotaWorkHandle;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.*;

import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.sendComms.claimFundsService.GroupAccountSrv;
import com.xinxinsoft.sendComms.unitService.UnitInfoSrv;
import com.xinxinsoft.service.claimForFunds.claimForFundModelService;
import com.xinxinsoft.service.core.user.StructureOfPersonnelService;
import com.xinxinsoft.service.groupAccountService.GroupAccountService;
import com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesService;
import com.xinxinsoft.service.v2.integrationService.IntegrationService;
import com.xinxinsoft.service.valuableCard.ValuableCardService;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import net.sf.json.JsonConfig;
import net.sourceforge.pinyin4j.PinyinHelper;
import net.sourceforge.pinyin4j.format.HanyuPinyinCaseType;
import net.sourceforge.pinyin4j.format.HanyuPinyinOutputFormat;
import net.sourceforge.pinyin4j.format.HanyuPinyinToneType;
import net.sourceforge.pinyin4j.format.HanyuPinyinVCharType;
import net.sourceforge.pinyin4j.format.exception.BadHanyuPinyinOutputFormatCombination;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.util.Base64;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;

import static com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats;

/**
 * @Description TODO 资金认领APP接口管理类
 * <AUTHOR>
 * @Date 2022/6/9 15:53
 **/
public class AppClaimForFundsAction extends BaseAction{
	private static final long serialVersionUID = 1L;
	private static final Logger logger = LoggerFactory.getLogger(AppClaimForFundsAction.class);
	private static final String ECMSERVER_URL = "http://*************:8080/ecmServer?wsdl";
	@Resource(name="claimForFundModelService")
	private claimForFundModelService modelService;
	@Resource(name = "StructureOfPersonnelService")
	private StructureOfPersonnelService structureOfPersonnelService;
	@Resource(name = "GroupCustomerService")
	private com.xinxinsoft.service.groupcustomer.GroupCustomerService groupCustomerService;//集团客户
	@Resource(name = "IntegrationService")
	private IntegrationService integrationService;
	@Resource(name = "ClaimForFundsService")
	private ClaimForFundsService claimForFundsService;
	@Resource(name = "WaitTaskService")
	private WaitTaskService	service;				// 代办
	@Resource(name = "SystemUserService")
	private SystemUserService systemUserService;
	@Resource(name = "JBPMUtil")
	private JbpmUtil jbpmUtil;
	@Resource(name = "TransferJBPMUtils")
	private TransferJBPMUtils transferJBPMUtils;
	@Resource(name = "Bpms_riskoff_service")
	private Bpms_riskoff_service taskService;
	@Resource(name = "AttachmentService")
	private AttachmentService attachmentService;
	@Resource(name = "customerServices")
	private GroupCustomerService customerServiceXml;
	@Resource(name = "GroupCustomerService")
	private com.xinxinsoft.service.groupcustomer.GroupCustomerService customerService;

	@Resource(name = "MarketingActivitiesService")
	private MarketingActivitiesService marketingActivitiesService;
	@Resource(name = "ValuableCardService")
	private ValuableCardService valuableCardService;

	@Resource(name = "GroupAccountService")
	private GroupAccountService groupAccountService;

	private static Boolean isES = false;
	static {
		if("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())){
			isES = true;
		}
	}

	/**
	 * @Description TODO 手动解除资金id内存锁
	 * <AUTHOR>
	 * @Date 2023/5/5 15:12
	 **/
	public void setQuerymoneyTotalIdKey(){
		Map<String, Object> resMap;
		try {
			String moneyTotalId = getString("moneyTotalId");
			String type = getString("type");
			if ("query".equals(type)){
				resMap = claimForFundsService.querymoneyTotalIdKey();
			}else {
				claimForFundsService.delQuerymoneyTotalIdKey(moneyTotalId);
				resMap = new HashMap<>();
				resMap.put("code",1);
				resMap.put("data",moneyTotalId);
				resMap.put("msg","删除成功！");
			}
			Write(JSONHelper.SerializeWithNeedAnnotation(resMap));
		}catch (Exception e){
			e.printStackTrace();
		}
	}

	/**
	 * @Description 新建快捷缴费工单
	 * <AUTHOR>
	 * @Date 2024/06/04 17:02
	 **/
	public void fastPaymentOfFunds(){
		try {
			String id = getString("id");//资金池ID
			String jsonString = getString("jsonString");//账户信息json
			String lateFeeMoney = getString("lateFeeMoney");//滞纳金金额
			String outMoney = getString("outMoney");//申请金额

			String title = getString("title");//标题
			String explain = getString("explain");//申请说明
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			claimForFundsService.getMoneyTotalSerialNoAndSubGroup(moneyTotal.getSerialNo(),0);
			if (moneyTotal==null){
				Write(returnPars(-1,"","亲爱的同事,资金编号["+id+"]查询异常,未查询到对应资金信息,请联系管理员处理!"));
				return;
			} else if(moneyTotal.getState()!=1){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]状态异常(当前状态为:"+moneyTotal.getState()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			}else if (moneyTotal.getUserid()!=user.getRowNo()){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]信息异常(当前归属用户为:"+moneyTotal.getUserid()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			}else if(Long.parseLong(moneyTotal.getOverAmount())<Long.parseLong(outMoney)){
				Write(returnPars(-1,"","亲爱的同事,当前资金为:"+Long.parseLong(moneyTotal.getOverAmount())/100+" 已不足以申请"));
				return;
			}

			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			Map<String, Object> resMap = claimForFundsService.setQuerymoneyTotalIdKey(moneyTotal.getId());
			if ("-1".equals(String.valueOf(resMap.get("code")))){
				Write(JSONHelper.SerializeWithNeedAnnotation(resMap));
				return;
			}

			moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) - Long.parseLong(outMoney)));
			moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) + Long.parseLong(outMoney)));
			claimForFundsService.updateMoneyTotal(moneyTotal);


			MoneyApply myly = new MoneyApply();
			String IBM = "";
			List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
			for (Object[] objects : sone) {
				IBM = (String) objects[2];
			}
			String sateTime = taskService.getNumber();
			String batchNO = IBM +"KJ"+ sateTime;
			myly.setApplyNo(batchNO);
			myly.setTitle((title==null || Objects.equals(title, ""))?"关于"+moneyTotal.getGroupName()+"的缴费":title);
			myly.setApplyMemo((explain==null || Objects.equals(explain, ""))?"关于"+moneyTotal.getGroupName()+"的快捷缴费":explain);
			myly.setApplyAmount(outMoney);
			myly.setCreatorId(String.valueOf(user.getRowNo()));
			myly.setCreatorName(user.getEmployeeName());
			myly.setCreateDate(new Date());
			myly.setState("1");
			myly.setGroupCode(moneyTotal.getGroupCode());
			myly.setGroupName(moneyTotal.getGroupName());
			myly.setOpType("1");
			myly.setMa_type("1");

			myly.setLateFeeMoney(lateFeeMoney);
			myly.setMoneyTotal_id(moneyTotal.getId());
			myly.setSerialNo(moneyTotal.getSerialNo());

			boolean flag = false;
			List<MoneyApplyDet> moneyApplyDets = new ArrayList<>();
			if (jsonString != null && jsonString.length() > 0) {
				JSONArray jsonArray = JSONArray.fromObject(jsonString);
				for (int i = 0; i < jsonArray.size(); i++) {
					String s = jsonArray.getString(i);
					JSONObject data2 = JSONObject.fromObject(s);
					if (data2.containsKey("lateFee") && "1.00".equals(data2.getString("lateFee")) && !flag){
						flag = true;
					}

					//由于义务限制,单笔金额超过99999的会被拆分
					Integer amout = Integer.parseInt(data2.getString("amount"));
					String PhoneNo = "";
					Date CreateTime = getStringDateFour(getStringDatetwo(new Date()));
					int o = 0;
					do {
						MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
						String moneyNo = ("2".equals(data2.getString("orderType"))?"JT":"GR") + sateTime + i;
						if ("".equals(PhoneNo)){
							PhoneNo = sateTime + i;
						}

						//工单关联信息
						moneyApplyDet.setMoneyNo(moneyNo+o);
						moneyApplyDet.setApplyNo(myly.getApplyNo());
						moneyApplyDet.setSerialNo(moneyTotal.getSerialNo());
						//集团信息
						moneyApplyDet.setGroupCode(moneyTotal.getGroupCode());
						moneyApplyDet.setGroupName(moneyTotal.getGroupName());
						//创建人信息
						moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
						moneyApplyDet.setCreateDate(CreateTime);
						//工单信息
						moneyApplyDet.setOpType("1");		//审批明细(1.审批明细/2.免审明细)
						moneyApplyDet.setOrderType(data2.getString("orderType"));	//工单类型(1.个人业务/2.集团业务)
						moneyApplyDet.setState("-1");		//未完成：审批中状态

						moneyApplyDet.setContrctNo(data2.getString("contrctNo"));
						moneyApplyDet.setContrctType(data2.getString("contrctType"));
						moneyApplyDet.setUseType(data2.getString("useType"));
						moneyApplyDet.setInvNo(data2.getString("invNo"));

						if ("1".equals(data2.getString("useType")) || "08".equals(data2.getString("useType")) || "09".equals(data2.getString("useType"))
								|| "10".equals(data2.getString("useType")) ||data2.getString("useType").contains("05")){       //预开票和缴费单笔最大金额为99999
							if (amout>9999900){
								moneyApplyDet.setAmount("9999900");
								amout-= 9999900;
							}else {
								moneyApplyDet.setAmount(String.valueOf(amout));
								amout = 0;
							}
						}else if ("06".equals(data2.getString("useType"))){                                             //有价卡单笔最大金额为50000
							if (amout>2500000){
								moneyApplyDet.setAmount("2500000");
								amout-= 2500000;
							}else {
								moneyApplyDet.setAmount(String.valueOf(amout));
								amout = 0;
							}
						}else {
							moneyApplyDet.setAmount(String.valueOf(amout));
							amout = 0;
						}

						//集团缴费时可能存在纳税人信息
						if ("2".equals(data2.getString("orderType")) && "1".equals(data2.getString("useType")) && data2.has("fomInvoiceType") && data2.getBoolean("fomInvoiceType")){
							moneyApplyDet.setUnitTaxplayer(data2.getString("unitTaxPlayerJson"));
						}

						if (("1".equals(data2.getString("orderType")) && "01".equals(data2.getString("useType"))) || "07".equals(data2.getString("useType"))){
							moneyApplyDet.setBossNo(data2.getString("AgenPayment"));
						}

						if (data2.getString("useType").contains("12") && !"".equals(data2.getString("customerNumber"))){
							moneyApplyDet.setCustomerNumber(data2.getString("customerNumber"));
						}

						if (data2.getString("useType").contains("05")){
							moneyApplyDet.setBeginCycle(data2.getString("beginCycle"));
							moneyApplyDet.setSpecialLineNo(data2.getString("specialLineNo"));
							moneyApplyDet.setUserIdNo(data2.getString("userIdNo"));
						}

						if ("1".equals(data2.getString("orderType")) && ("1".equals(data2.getString("useType")) || "01".equals(data2.getString("useType")))){
							moneyApplyDet.setCtrlFlag(("true".equals(data2.getString("ctrlFlag"))?"0":"1"));
						}

						moneyApplyDet.setLateFee(data2.containsKey("lateFee")?data2.getString("lateFee"):"0");
						moneyApplyDet.setLateFeeMoney(data2.containsKey("lateFeeMoney")?data2.getString("lateFeeMoney"):"0");

						moneyApplyDet.setPhoneNo(PhoneNo);

						//个人账户对是否B库成员进行校验
						if (data2.getString("orderType").equals("1")){
							List<Map<String,String>> list = claimForFundsService.queryGroupAssociation(moneyTotal.getGroupCode(),data2.getString("contrctNo"));
							if (list.size()<=0){
								moneyApplyDet.setBillNote("1");
							}else {
								moneyApplyDet.setBillNote("");
							}
						}else {
							moneyApplyDet.setBillNote("");
						}

						moneyApplyDet.setProductName("");
						moneyApplyDet.setProductNmb("");
						moneyApplyDet.setContrctName("");

						moneyApplyDets.add(moneyApplyDet);

						if ("2".equals(data2.getString("useType")) && !"".equals(data2.getString("quotaWorkHandleCode"))){
							if (!updaetQuotaWorkHandleByCode(data2.getString("quotaWorkHandleCode"),moneyApplyDet.getMoneyNo(),"2")){
								throw new Exception("营销活动修改失败！");
							}
						}

						if ("06".equals(data2.getString("useType")) && !"".equals(data2.getString("invNo"))){
							if (!updateValuableCard(data2.getString("invNo"),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"SAVA")){
								throw new Exception("有价卡修改失败！");
							}
						}

						if ("12B".equals(data2.getString("useType")) && !"".equals(data2.getString("invNo"))){
							if (!updateInternetOfThingsDet(data2.getString("invNo"),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"SAVA")){
								throw new Exception("物联网预开票修改失败！");
							}
						}

						o+=1;
					}while (amout>0);
				}
			}

			if (flag){
				MoneyApply my = claimForFundsService.addMoneyApply(myly);
				for (MoneyApplyDet moneyApplyDet : moneyApplyDets) {
					claimForFundsService.addMoneyApplyDet(moneyApplyDet);
				}
				String attachmentId = getString("attachmentId");// 附件id
				String role = getString("role");// 角色权限
				Integer userId = getInteger("userId");// 下一步任务人id

				if (!StringUtils.isEmpty(attachmentId)) {
					if (attachmentId != null) {
						// 判断是否上传了附件，获取前台提交的附件Id；
						String[] json = attachmentId.split(",");
						if (json.length > 0) {
							for (int i = 0; i < json.length; i++) {
								SingleAndAttachment sa = new SingleAndAttachment();
								sa.setOrderID(my.getId());
								sa.setAttachmentId(json[i]);
								sa.setLink(MoneyTotal.MONEYTOTAL);
								claimForFundsService.saveSandA(sa);
							}
						}
					}
				}

				// 流程启动
				String node="";
				if("SZK".equals(role)){
					node="ROLE_SZKKHJL";
				}else if("SGS".equals(role)){
					node="ROLE_SGSKHJL";
				}else{
					node="ROLE_QXDM";
				}
				Map<String, String> map = new HashMap<>();
				map.put("node", node);
				String processId = transferJBPMUtils.startTransfer("ClaimForFundUsePrs", map);// 流程启动
				if(processId !=null){
					Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
					taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
					taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
					String taskid= taskService.setBpms_riskoff_task(processId,null, 1, "SH", task.getActivityName(),userId, user);//预存下一步任务
					JSONObject obj=taskService.getApproval_message(my.getApplyNo(), BigDecimal.
									valueOf(Long.valueOf(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
							task.getActivityName(), "",userId, user);
					taskService.setReminder_information_tbl(my.getGroupCode(),"1","30", "资金认领", obj.toString(), "资金认领");
					commitBackLog(my, userId, processId, user, taskid);//生成待办
					claimForFundsService.delQuerymoneyTotalIdKey(moneyTotal.getId());
					Write(returnPars(1,"","亲爱的同事,工单新建成功,请等待审批结果!"));
				}else {
					claimForFundsService.delQuerymoneyTotalIdKey(moneyTotal.getId());
					Write(returnPars(-1,"","亲爱的同事,流程实例启动异常,请联系管理员处理!"));
				}
			}else {
				String processId = "KJJF_"+sateTime;
				int pushOkCount = 0;
				Boolean whetherToPush = true;
				for (MoneyApplyDet moneyApplyDet : moneyApplyDets) {
					String login_no;
					if (("01".equals(moneyApplyDet.getUseType()) || "07".equals(moneyApplyDet.getUseType())) && moneyApplyDet.getBossNo() != null) {
						SystemUser BossUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
						if (BossUser != null && BossUser.getBossUserName() != null) {
							login_no = BossUser.getBossUserName();
						} else {
							pushOkCount++;
							moneyApplyDet.setBossState("1");
							moneyApplyDet.setBossMsg("代理缴费用户信息异常，不能进行缴费，请核实！");
							claimForFundsService.addMoneyApplyDet(moneyApplyDet);
							continue;
						}
					} else {
						login_no = pushBossUser.getBossUserName();
					}
					String unit_id = moneyApplyDet.getGroupCode();
					String op_type = "03"; //使用申请
					String out_sys_accept = moneyApplyDet.getMoneyNo();
					String busi_flag = "";
					String contract_no = "";
					String phone_no = "";
					if ("1".equals(moneyApplyDet.getOrderType())) {
						busi_flag = "P";
						phone_no = moneyApplyDet.getContrctNo();
					} else if ("2".equals(moneyApplyDet.getOrderType())) {
						busi_flag = "G";
						contract_no = moneyApplyDet.getContrctNo();
					}
					String prod_name = moneyApplyDet.getMoneyNo() + "-" + moneyApplyDet.getAmount();
					String prod_num = "0";
					String apply_login = pushBossUser.getBossUserName();
					String apply_note = myly.getApplyMemo() == null ? "无" : myly.getApplyMemo();
					String bill_note = "-";
					String bank_account = moneyTotal.getOtherAccNumber() == null ? "" : moneyTotal.getOtherAccNumber();
					String bank_account_name = moneyTotal.getOtherName() == null ? "" : moneyTotal.getOtherName();

					String purpose = moneyTotal.getMemo() == null ? "无" : moneyTotal.getMemo();
					String busi_fee = BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
					String pay_type = "0";
					String busi_type = "";
					String delay_rate = "0";
					String pre_invoice_accept = "";
					String route_key = "";
					String userIdNo = "";
					String customerNumber = "";
					if ("1".equals(moneyApplyDet.getUseType()) || "01".equals(moneyApplyDet.getUseType())) { //缴费
						if ("01".equals(moneyApplyDet.getUseType())) {
							route_key = "10";
						}
						busi_type = "00";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
					} else if ("2".equals(moneyApplyDet.getUseType())) { //存送
						busi_type = "02";
						if ("2".equals(moneyApplyDet.getOrderType())) {       //集团存送时需推送一个办理号码
							phone_no = moneyApplyDet.getInvNo();
						}
					} else if ("3".equals(moneyApplyDet.getUseType())) { //终端
						busi_type = "01";
					} else if (moneyApplyDet.getUseType().contains("05")) { //预开票
						busi_type = "05";
						//专线预开票
						if ("05A".equals(moneyApplyDet.getUseType())) {
							pay_type = "22";
							phone_no = moneyApplyDet.getSpecialLineNo();
							userIdNo = moneyApplyDet.getUserIdNo();
						}
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
					} else if ("06".equals(moneyApplyDet.getUseType())) { //有价卡
						busi_type = "06";
						pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
					} else if ("07".equals(moneyApplyDet.getUseType())) { //物联网
						busi_type = "07";
					} else if ("08".equals(moneyApplyDet.getUseType())) { //ICT设备销售
						busi_type = "08";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo() != null && moneyApplyDet.getInvNo().length() > 0) {
							pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
						}
					} else if ("09".equals(moneyApplyDet.getUseType())) { //ICT终端销售
						busi_type = "09";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo() != null && moneyApplyDet.getInvNo().length() > 0) {
							pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
						}
					} else if ("10".equals(moneyApplyDet.getUseType())) { //ICT软件销售
						busi_type = "10";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo() != null && moneyApplyDet.getInvNo().length() > 0) {
							pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
						}
					} else if ("11".equals(moneyApplyDet.getUseType())) {
						busi_type = "11";
						pre_invoice_accept = moneyApplyDet.getInvNo();
						userIdNo = moneyApplyDet.getUserIdNo();
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
					} else if (moneyApplyDet.getUseType().contains("12")) {
						busi_type = "12";
						customerNumber = moneyApplyDet.getCustomerNumber();
					} else {
						busi_type = moneyApplyDet.getUseType();
					}
					String out_back_accept = moneyApplyDet.getMoneyNo();
					String ctrlFlag = moneyApplyDet.getCtrlFlag();
					String beginCycle = moneyApplyDet.getBeginCycle();
					if (whetherToPush) {
						Result applyRes = ClaimFundsOpenSrv.getInstance().applyForFunds(login_no, unit_id, op_type, out_sys_accept, contract_no,
								busi_flag, prod_name, prod_num, apply_login, apply_note, bill_note,
								bank_account, bank_account_name, phone_no, purpose, busi_fee, busi_type,
								out_back_accept, delay_rate, pre_invoice_accept, route_key, pay_type, userIdNo, customerNumber, ctrlFlag);
						logger.info(moneyApplyDet.getMoneyNo() + "推送资金使用明细结果：" + applyRes.toString());
						if (ResultCode.SUCCESS.code() == applyRes.getCode()) {  //判断当前请求是否成功
							JSONObject applyObj = JSONObject.fromObject(applyRes.getData());
							JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
							//循环推送申请工单中的明细记录，成功并记录成功和失败数据
							if ("0".equals(ROOT.getString("RETURN_CODE"))) {
								moneyApplyDet.setBossState("0");
								moneyApplyDet.setState("1");
								moneyApplyDet.setPushDate(new Date());
								JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
								if (OUT_DATA.has("PAYMENT_ACCEPT")) {
									if (OUT_DATA.getString("PAYMENT_ACCEPT").length() > 0) {
										moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
										InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
										invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
										invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
										if (moneyApplyDet.getUnitTaxplayer() != null && moneyApplyDet.getUnitTaxplayer().length() > 0) {
											try {
												JSONObject UnitTaxplayer = JSONObject.fromObject(moneyApplyDet.getUnitTaxplayer());
												invoiceMiddle.setMsgRecvPhone(UnitTaxplayer.getString("RECEIVENUMBER"));
												invoiceMiddle.setTaxpayerId(UnitTaxplayer.getString("TAXPAYER_ID"));
												invoiceMiddle.setBankName(UnitTaxplayer.getString("BANK_NAME"));
												invoiceMiddle.setBankAccount(UnitTaxplayer.getString("BANK_ACCOUNT"));
												invoiceMiddle.setAddress(UnitTaxplayer.getString("ADDRESS"));
												invoiceMiddle.setPhoneNo(UnitTaxplayer.getString("PHONE_NO"));
												invoiceMiddle.setPrintType("1");
											} catch (Exception e) {
												invoiceMiddle.setPrintType("3");
												invoiceMiddle.setBossMsg("数据解析异常：" + e.getMessage());
											}
										} else {
											invoiceMiddle.setPrintType("0");
										}
										claimForFundsService.addInvoiceMiddle(invoiceMiddle);
									}
								}
							} else {
								pushOkCount += 1;
								moneyApplyDet.setBossState("1");
								moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG"));
								if (moneyApplyDet.getUseType().contains("05") && beginCycle != null && beginCycle.length() > 0) {
									whetherToPush = false;
								}
							}
							claimForFundsService.addMoneyApplyDet(moneyApplyDet);
						} else {
							moneyApplyDet.setBossState("-1");
							//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
							moneyApplyDet.setBossMsg(applyRes.getMessage().length() < 200 ? applyRes.getMessage() : applyRes.getMessage().substring(0, 200));
							claimForFundsService.addMoneyApplyDet(moneyApplyDet);
							if (moneyApplyDet.getUseType().contains("05") && beginCycle != null && beginCycle.length() > 0) {
								whetherToPush = false;
							}
						}
					} else {
						pushOkCount += 1;
						moneyApplyDet.setBossState("1");
						moneyApplyDet.setBossMsg("该账期前还有其他账期未推送");
						claimForFundsService.addMoneyApplyDet(moneyApplyDet);
					}
				}

				if(pushOkCount>0){
					myly.setState("3");
				}else{
					myly.setState("0");
				}
				MoneyApply my = claimForFundsService.addMoneyApply(myly);

				taskService.setBpms_riskoff_process(my.getId(), processId, 2, user);
				String rtaskid = taskService.setBpms_riskoff_task(processId, "发起工单", (pushOkCount>0?1:2), "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
				if(pushOkCount>0){
					commitBackLog(my,Integer.parseInt(my.getCreatorId()), processId, user, rtaskid);// 生成待办
				}

				claimForFundsService.delQuerymoneyTotalIdKey(moneyTotal.getId());
				Write(returnPars(1,"","亲爱的同事,工单提交成功，推送结果请前往工单列表查看!"));
			}
		}catch (Exception e){
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			logger.info("新建资金快捷缴费工单异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,新建资金快捷缴费工单异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**---------------------------------------------(资金认领流程)--------------------------------------------------------------
	 * @Description TODO 用于发起资金认领的审批流程
	 * <AUTHOR>
	 * @Date 2022/6/9 17:22
	 **/
	public void addClaimData(){
		try {
			String id = getString("id");//资金池ID
			String title = getString("title");//标题
			String explain = getString("explain");//申请说明
			String switchTest = getString("switchTest");

			String groupName = getString("groupName");//集团名称
			String group = getString("group");//集团编码

			String subGroup_json =  getString("subGroup_json"); //分配的子集团Json
			String subGroup_amount = getString("subGroup_amount");//分子子集团的金额

			String useJson = getString("useJson");
			String lateFeeMoney = getString("lateFeeMoney");
			String useMoney = getString("useMoney");

			String attachmentUrl = getString("attachmentUrl");//附件地址
			String attachmentId = getString("attachmentId");//附件id

			String role = getString("role");//角色权限
			Integer userId = getInteger("userId");//下一步处理用户id
			String bossHandler = getString("bossHandler");//下一步处理用户工号

			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			String replcaceId = getString("replcaceId");//集团代理关联编号

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyTotal moneyTotal=claimForFundsService.getMoneyTotal(id);
			claimForFundsService.getMoneyTotalSerialNoAndSubGroup(moneyTotal.getSerialNo(),0);
			SystemUser pushUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(bossHandler));
			if(moneyTotal!=null){
				Map<String, Object> resMap = claimForFundsService.setQuerymoneyTotalIdKey(moneyTotal.getId());
				if ("-1".equals(String.valueOf(resMap.get("code")))){
					Write(JSONHelper.SerializeWithNeedAnnotation(resMap));
					return;
				}
				if ("2".equals(switchTest)){
					useMoney = subGroup_amount;
					if(Long.parseLong(moneyTotal.getOverAmount())<Long.parseLong(subGroup_amount)){
						Write(returnPars(-1,"","亲爱的同事,资金认领划拨数据异常，当前金额:【"+Long.parseLong(moneyTotal.getOverAmount())/100+"】,小于划拨金额【"+Long.parseLong(subGroup_amount)/100+"】,请确认划拨金额和实际金额。"));
						return;
					}
				}else if ("3".equals(switchTest)){
					if(Long.parseLong(moneyTotal.getOverAmount())<Long.parseLong(useMoney)){
						Write(returnPars(-1,"","亲爱的同事,资金认领使用数据异常，当前金额:【"+Long.parseLong(moneyTotal.getOverAmount())/100+"】,小于使用金额【"+Long.parseLong(useMoney)/100+"】,请确认使用金额和实际金额。"));
						return;
					}
				}
				if (pushUser==null || pushUser.getBossUserName()==null || "".equals(pushUser.getBossUserName())){
					Write(returnPars(-1,"","亲爱的同事，资金推送人员信息异常【推送人员编号："+bossHandler+"】，请联系管理员处理。"));
					return;
				}
				if(moneyTotal.getState().equals(0) || moneyTotal.getState().equals(6)){   //当资金为未申领状态时，对财务稽核系统实现暂挂状态，预防双方使用当前记录问题
					if ("4".equals(switchTest)){
						moneyTotal.setPushUserName(pushUser.getRowNo());
						moneyTotal.setPushBossUserName(pushUser.getBossUserName());
						moneyTotal.setIs_sub_group(0);
						moneyTotal.setUserid(user.getRowNo());
						moneyTotal.setState(5);//初始使用资金时候暂挂
						moneyTotal=claimForFundsService.updateMoneyTotalTwo(moneyTotal);
					}else {
						List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(pushUser.getRowNo()));
						if (mapList==null){
							Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
							return;
						}
						String county = mapList.get(0).get("COUNTY_NAME");
						String region = mapList.get(0).get("COMPANY_NAME");
						String fullName = mapList.get(0).get("EMPLOYEE_NAME");
						Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("6",moneyTotal.getSerialNo(),county,region,fullName);
						logger.info("财务接口调用结果===>"+result.toString());
						if(ResultCode.SUCCESS.code()==result.getCode()){  //判断当前请求是否成功
							JSONObject res=JSONObject.fromObject(result.getData());
							if("success".equals(res.getString("code"))){
								//如果推送财务接口成功，将财务数据状态调整为暂挂
								moneyTotal.setPushUserName(pushUser.getRowNo());
								moneyTotal.setPushBossUserName(pushUser.getBossUserName());
								moneyTotal.setIs_sub_group(0);
								moneyTotal.setUserid(user.getRowNo());
								moneyTotal.setState(5);//初始使用资金时候暂挂
								moneyTotal=claimForFundsService.updateMoneyTotalTwo(moneyTotal);
								JSONObject obj=taskService.getInitial_message(changeF2Y(moneyTotal.getAmount()), "",user.getRowNo());
								taskService.setReminder_information_tbl(group,"1","32", "资金认领", obj.toString(), "资金认领");
							}else {
								Write(returnPars(-1,"","亲爱的同事,请求财务稽核系统结果异常,请确认:" + res.getString("info")));
								return;
							}
						}else{
							Write(returnPars(-1,"","亲爱的同事,调用财务接口失败,调用信息:"+result.getMessage()));
							return;
						}
					}
				}else{
					Write(returnPars(-1,"","亲爱的同事,资金状态异常("+moneyTotal.getState()+"),请联系管理员处理!"));
					return;
				}
				//1、入表申请工单
				String IBM = "";
				List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
				for (Object[] objects : sone) {
					IBM = (String) objects[2];
				}
				String sateTime = taskService.getNumber();
				String batchNO = IBM + sateTime;
				MoneyApply myly = new MoneyApply();
				myly.setApplyNo(batchNO);
				myly.setTitle(title);
				myly.setApplyMemo(explain);
				myly.setApplyAmount(useMoney);
				myly.setLateFeeMoney(lateFeeMoney);
				myly.setCreatorId(String.valueOf(user.getRowNo()));
				myly.setCreatorName(user.getEmployeeName());
				myly.setCreateDate(new Date());
				myly.setState("1");
				myly.setGroupCode(group);
				myly.setGroupName(groupName);
				myly.setOpType("3");
				myly.setMa_type(switchTest);
				myly.setSerialNo(moneyTotal.getSerialNo());
				myly.setSubGroup_amount(subGroup_amount);
				myly.setMoneyTotalId(subGroup_json);
				myly.setMoneyTotal_id(moneyTotal.getId());
				MoneyApply my = claimForFundsService.addMoneyApply(myly);
				if ("1".equals(switchTest)){        //仅认领
					moneyTotal.setOverAmount(moneyTotal.getOverAmount());
					moneyTotal.setUseAmount("0");
					moneyTotal.setSub_overAmount(moneyTotal.getOverAmount());
				}else if ("2".equals(switchTest)){  //认领并划拨
					JSONArray groupJsonArray = JSONArray.fromObject(subGroup_json);
					for (int i = 0; i < groupJsonArray.size(); i++) {
						String s = groupJsonArray.getString(i);
						JSONObject data2 = JSONObject.fromObject(s);
						Integer pushUserRow = pushUser.getRowNo();
						String pushUserName = pushUser.getBossUserName();
						try {
							List<Map<String, Object>> map = claimForFundsService.findDept(Integer.parseInt(data2.getString("userId")));
							String county_name = map.get(0).get("TWODNAME").toString();
							String company_name = map.get(0).get("COMPANY_NAME").toString();
							List<Map<String, String>> sd = claimForFundsService.SelectZtreeByUId("ROLE_ZJRLSPGLY", company_name, county_name);
							if (sd.size() == 1) {
								if (!"".equals(sd.get(0).get("BOSSUSERNAME")) && sd.get(0).get("BOSSUSERNAME") != null && !"null".equals(sd.get(0).get("BOSSUSERNAME"))) {
									pushUserRow = Integer.valueOf(sd.get(0).get("ROWNO"));
									pushUserName = sd.get(0).get("BOSSUSERNAME");
								}
							}
						} catch (Exception e) {
							e.printStackTrace();
							pushUserRow = pushUser.getRowNo();
							pushUserName = pushUser.getBossUserName();
						}
						//2、入表子集团的申请金额
						MoneyTotal subMoneyTotal = new MoneyTotal();
						subMoneyTotal.setSerialNo(moneyTotal.getSerialNo());//(唯一标识号)
						subMoneyTotal.setOtherAccNumber(moneyTotal.getOtherAccNumber());//(对方账号)
						subMoneyTotal.setOtherName(moneyTotal.getOtherName());//(对方户名)
						subMoneyTotal.setOtherBank(moneyTotal.getOtherBank());//(对方开户行)
						subMoneyTotal.setAccNumber(moneyTotal.getAccNumber());//(公司账号)
						subMoneyTotal.setTranDate(moneyTotal.getTranDate());//strs[5]//(交易时间)
						subMoneyTotal.setAmount(data2.getString("amount"));//(转账金额)
						subMoneyTotal.setUseAmount("0");//(使用金额)
						subMoneyTotal.setOverAmount(data2.getString("amount"));//(剩余金额)
						subMoneyTotal.setReceiverSCompany(moneyTotal.getReceiverSCompany());
						subMoneyTotal.setMemo(moneyTotal.getMemo());//(摘要注释)
						subMoneyTotal.setUseMemo(moneyTotal.getUseMemo());//(用途注释)
						subMoneyTotal.setCompanyCode(moneyTotal.getCompanyCode());//(地市编码)
						subMoneyTotal.setCompanyName(moneyTotal.getCompanyName());//(地市名称)
						subMoneyTotal.setCreateDate(new Date());//(创建时间)
						subMoneyTotal.setState(5);//(状态)
						subMoneyTotal.setIsThe(0);//记录申领次数；
						subMoneyTotal.setTypeUserId(moneyTotal.getTypeUserId());
						subMoneyTotal.setType(1);
						subMoneyTotal.setGroupCode(data2.getString("groupCode"));
						subMoneyTotal.setGroupName(data2.getString("groupName"));
						subMoneyTotal.setIs_sub_group(1);
						subMoneyTotal.setUserid(Integer.parseInt(data2.getString("userId")));
						subMoneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo()) + taskService.getNumber());//(系统编号)
						subMoneyTotal.setHour_min_second(moneyTotal.getHour_min_second());
						subMoneyTotal.setSub_overAmount(data2.getString("amount"));
						subMoneyTotal.setPushUserName(pushUserRow);
						subMoneyTotal.setPushBossUserName(pushUserName);
						claimForFundsService.saveProcessList(subMoneyTotal);
						JSONObject obj = taskService.getInitial_message(data2.getString("amount"), "", Integer.parseInt(data2.getString("userId")));
						taskService.setReminder_information_tbl(data2.getString("groupCode"), "1", "32", "资金认领", obj.toString(), "资金子集团分配");
						/**
						 * 保存分配集团信息详细信息对应申请工单
						 */
						SystemUser user_sub = systemUserService.getByUserInfoRowNo(Integer.parseInt(data2.getString("userId")));
						MoneyApplySubGroup mas = new MoneyApplySubGroup();
						mas.setApply_no(IBM + taskService.getNumber());
						mas.setGroup_code(data2.getString("groupCode"));
						mas.setGroup_name(data2.getString("groupName"));
						mas.setAmount(data2.getString("amount"));
						mas.setSerial_no(moneyTotal.getSerialNo());
						mas.setMoneyTotal_id(subMoneyTotal.getId());
						mas.setMoneyApply_id(my.getId());
						mas.setUser_name(user_sub.getEmployeeName());
						mas.setUser_id(String.valueOf(user_sub.getRowNo()));
						mas.setCrateUser_id(String.valueOf(user.getRowNo()));
						mas.setCrate_date(new Date());
						mas.setBoss_State(0); //默认设置为0，方式字段为空等问题
						claimForFundsService.saveMoneyApplySubGroup(mas);
					}
					//分配完成后调整原认领记录的金额
					moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) - Long.parseLong(subGroup_amount)));
					moneyTotal.setUseAmount(String.valueOf(Long.parseLong(subGroup_amount)));
					moneyTotal.setSub_overAmount(moneyTotal.getOverAmount());
				}else if ("3".equals(switchTest)){      //认领并使用
					JSONArray jsonArray = JSONArray.fromObject(useJson);
					for (int i = 0; i < jsonArray.size(); i++) {
						String s = jsonArray.getString(i);
						JSONObject data2 = JSONObject.fromObject(s);
						//由于义务限制,单笔金额超过99999的会被拆分
						Integer amout = Integer.parseInt(data2.getString("amount"));
						String PhoneNo = "";
						Date CreateTime = getStringDateFour(getStringDatetwo(new Date()));
						int o = 0;
						do {
							MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
							String moneyNo = ("2".equals(data2.getString("orderType"))?"JT":"GR") + sateTime + i;
							if ("".equals(PhoneNo)){
								PhoneNo = sateTime + i;
							}

							//工单关联信息
							moneyApplyDet.setMoneyNo(moneyNo+o);
							moneyApplyDet.setApplyNo(my.getApplyNo());
							moneyApplyDet.setSerialNo(moneyTotal.getSerialNo());
							//集团信息
							moneyApplyDet.setGroupCode(group);
							moneyApplyDet.setGroupName(groupName);
							//创建人信息
							moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
							moneyApplyDet.setCreateDate(CreateTime);
							//工单信息
							moneyApplyDet.setOpType("1");		//审批明细(1.审批明细/2.免审明细)
							moneyApplyDet.setOrderType(data2.getString("orderType"));	//工单类型(1.个人业务/2.集团业务)
							moneyApplyDet.setState("-1");		//未完成：审批中状态

							moneyApplyDet.setContrctNo(data2.getString("contrctNo"));
							moneyApplyDet.setContrctType(data2.getString("contrctType"));
							moneyApplyDet.setUseType(data2.getString("useType"));
							moneyApplyDet.setInvNo(data2.getString("invNo"));
							moneyApplyDet.setSpecialLineNo(data2.getString("specialLineNo"));
							moneyApplyDet.setUserIdNo(data2.getString("userIdNo"));
							if ("1".equals(data2.getString("useType")) || "08".equals(data2.getString("useType")) || "09".equals(data2.getString("useType"))
									|| "10".equals(data2.getString("useType")) ||data2.getString("useType").contains("05")){       //预开票和缴费单笔最大金额为99999
								if (amout>9999900){
									moneyApplyDet.setAmount("9999900");
									amout-= 9999900;
								}else {
									moneyApplyDet.setAmount(String.valueOf(amout));
									amout = 0;
								}
							}else if ("06".equals(data2.getString("useType"))){                                             //有价卡单笔最大金额为25000
								if (amout>2500000){
									moneyApplyDet.setAmount("2500000");
									amout-= 2500000;
								}else {
									moneyApplyDet.setAmount(String.valueOf(amout));
									amout = 0;
								}
							}else {
								moneyApplyDet.setAmount(String.valueOf(amout));
								amout = 0;
							}

							//集团缴费时可能存在纳税人信息
							if ("2".equals(data2.getString("orderType")) && "1".equals(data2.getString("useType")) && data2.has("fomInvoiceType") && data2.getBoolean("fomInvoiceType")){
								moneyApplyDet.setUnitTaxplayer(data2.getString("unitTaxPlayerJson"));
							}

							if (("1".equals(data2.getString("orderType")) && "01".equals(data2.getString("useType"))) || "07".equals(data2.getString("useType"))){
								moneyApplyDet.setBossNo(data2.getString("AgenPayment"));
							}

							if (data2.getString("useType").contains("12") && !"".equals(data2.getString("customerNumber"))){
								moneyApplyDet.setCustomerNumber(data2.getString("customerNumber"));
							}

							if (data2.getString("useType").contains("05")){
								moneyApplyDet.setBeginCycle(data2.getString("beginCycle"));
							}

							if ("1".equals(data2.getString("orderType")) && ("1".equals(data2.getString("useType")) || "01".equals(data2.getString("useType")))){
								moneyApplyDet.setCtrlFlag(("true".equals(data2.getString("ctrlFlag"))?"0":"1"));
							}

							moneyApplyDet.setLateFee(data2.getString("lateFee"));
							moneyApplyDet.setLateFeeMoney(data2.getString("lateFeeMoney"));

							moneyApplyDet.setPhoneNo(PhoneNo);

							//个人账户对是否B库成员进行校验
							if ("1".equals(data2.getString("orderType"))){
								List<Map<String,String>> list = claimForFundsService.queryGroupAssociation(group,data2.getString("contrctNo"));
								if (list.size()<=0){
									moneyApplyDet.setBillNote("1");
								}else {
									moneyApplyDet.setBillNote("");
								}
							}else {
								moneyApplyDet.setBillNote("");
							}

							moneyApplyDet.setProductName("");
							moneyApplyDet.setProductNmb("");
							moneyApplyDet.setContrctName("");
							claimForFundsService.addMoneyApplyDet(moneyApplyDet);

							if ("2".equals(data2.getString("useType")) && !"".equals(data2.getString("quotaWorkHandleCode"))){
								if (!updaetQuotaWorkHandleByCode(data2.getString("quotaWorkHandleCode"),moneyApplyDet.getMoneyNo(),"2")){
									throw new Exception("营销活动修改失败！");
								}
							}

							if ("06".equals(data2.getString("useType")) && !"".equals(data2.getString("invNo"))){
								if (!updateValuableCard(data2.getString("invNo"),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"SAVA")){
									throw new Exception("有价卡修改失败！");
								}
							}

							if ("12B".equals(data2.getString("useType")) && !"".equals(data2.getString("invNo"))){
								if (!updateInternetOfThingsDet(data2.getString("invNo"),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"SAVA")){
									throw new Exception("物联网预开票修改失败！");
								}
							}
							o+=1;
						}while (amout>0);
					}
					//创建成功后修改资金剩余金额和使用金额
					moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) - Long.parseLong(useMoney)));
					moneyTotal.setUseAmount(String.valueOf(Long.parseLong(useMoney)));
				}

				moneyTotal.setGroupCode(group);
				moneyTotal.setGroupName(groupName);
				moneyTotal.setReplcaceId(replcaceId);
				claimForFundsService.updateMoneyTotal(moneyTotal);

				if (!StringUtils.isEmpty(attachmentId)) {
					if (attachmentId != null) {
						// 判断是否上传了附件，获取前台提交的附件Id；
						String[] json = attachmentId.split(",");
						if (json.length > 0) {
							for (int i = 0; i < json.length; i++) {
								SingleAndAttachment sa = new SingleAndAttachment();
								sa.setOrderID(my.getId());
								sa.setAttachmentId(json[i]);
								sa.setLink(MoneyTotal.MONEYTOTAL);
								claimForFundsService.saveSandA(sa);
							}
						}
					}
				}

				String processId;
				if ("3".equals(switchTest)){
					String node="";
					if("SZK".equals(role)){
						node="ROLE_SZKKHJL";
					}else if("SGS".equals(role)){
						node="ROLE_SGSKHJL";
					}else{
						node="ROLE_QXDM";
					}
					Map<String, String> map = new HashMap<>();
					map.put("node", node);
					processId = transferJBPMUtils.startTransfer("ClaimForFundUsePrs", map);
				}else if ("4".equals(switchTest)){
					String node="";
					if("SZK".equals(role)){
						node="ROLE_SZKKHJL";
					}else if("SGS".equals(role)){
						node="ROLE_SGSKHJL";
					}else{
						node="ROLE_QXDM";
					}
					Map<String, String> map = new HashMap<>();
					map.put("node", node);
					processId = transferJBPMUtils.startTransfer("ReturnClaimForFunds_01", map);
				}else {
					Map<String, String> map = new HashMap<>();
					map.put("decisionKey", "APPLY");
					map.put("decisionValue", role);
					processId = transferJBPMUtils.startTransfer("claimForFundsChange", map);
				}
				Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
				taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
				taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
				String taskid= taskService.setBpms_riskoff_task(processId,null, 1, "SH", task.getActivityName(),userId, user);//预存下一步任务
				JSONObject obj=taskService.getApproval_message(my.getApplyNo(),BigDecimal.
								valueOf(Long.parseLong(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
						task.getActivityName(), "",userId, user);
				taskService.setReminder_information_tbl(my.getGroupCode(),"1","30", "资金认领", obj.toString(), "资金认领");
				commitBackLogData(my, userId, processId, user, taskid);//生成待办
				claimForFundsService.delQuerymoneyTotalIdKey(moneyTotal.getId());
				Write(returnPars(1,"","亲爱的同事,工单:"+my.getTitle()+" 已成功发起审批了!"));
			}else{
				Write(returnPars(-1,"","亲爱的同事,资金信息异常("+id+"),请关闭页面重试或联系管理员处理!"));
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("新建资金认领工单异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,新建资金认领工单异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金认领   流程进行(流转)方法
	 * <AUTHOR>
	 * @Date 2022/6/13 10:34 
	 **/
	public void handleClaimData(){
		try {
			Integer userid = getInteger("userId");//下一步处理用户id
			String id = getString("id");
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}
			int lateFee=0;
			boolean ifOffSite = false;
			String AgenPayment = "";
			SystemUser agenUser = null;
			if ("3".equals(my.getMa_type())){
				List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
				for(int i=0;i<moneyApplyDet.size();i++){
					//是否存在异地缴费
					if ("01".equals(moneyApplyDet.get(i).getUseType()) && !ifOffSite){
						ifOffSite = true;
						if ("".equals(AgenPayment)){
							AgenPayment = moneyApplyDet.get(i).getBossNo();
						}
					}

					if("1.00".equals(moneyApplyDet.get(i).getLateFee())){
						lateFee=1;
					}

					if (lateFee==1 && ifOffSite && !"".equals(AgenPayment)){
						break;
					}
				}

				//获取异地缴费代理人信息
				if (ifOffSite){
					if("".equals(AgenPayment)){
						Write(returnPars(-1,"","亲爱的同事,异地缴费代理缴费人员信息异常,联系系统管理员"));
						throw new RuntimeException("异地缴费代理缴费人员信息异常");
					}else {
						agenUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(AgenPayment));
						if (agenUser==null){
							Write(returnPars(-1,"","亲爱的同事,异地缴费代理缴费人员信息异常,联系系统管理员"));
							throw new RuntimeException("异地缴费代理缴费人员信息异常");
						}else {
							userid = agenUser.getRowNo();
						}
					}
				}
			}
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			GroupCustomer groupCustomer = claimForFundsService.queryGroupCustomerById(moneyTotal.getGroupCode());
			Map<String, String> map = new HashMap<>();
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
			String code =claimForFundsService.getVwUserinf(my.getCreatorId()).get(0).get("COMPANY_CODE");

			TransferCitiesData transferCitiesData= claimForFundsService.getTransferCitiesData(code,task.getActivityName());
			if (transferCitiesData == null) {
				transferCitiesData = new TransferCitiesData();
				transferCitiesData.setAmount("0");
			}
			LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,task.getActivityName());
			if (lateFeeMoneyData == null) {
				lateFeeMoneyData = new LateFeeMoneyData();
				lateFeeMoneyData.setAmount("0");
			}
			if ("区县政企部主任".equals(task.getActivityName())) {
				if ("3".equals(my.getOpType())&&"3".equals(my.getMa_type())){
					if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
						if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
								Double.parseDouble(my.getLateFeeMoney()) / 100 <= Double.parseDouble(lateFeeMoneyData.getAmount()) ) {
							if (ifOffSite){
								map.put("node", "异地缴费转代理人员");
							}else {
								map.put("node", "END");
							}
						}else{
							map.put("node", "符合继续审批要求");
						}
					}else {
						if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
								Double.parseDouble(my.getLateFeeMoney()) / 100 <= Double.parseDouble(lateFeeMoneyData.getAmount()) ) {
							map.put("node", "END");
						}else{
							map.put("node", "金额大于等于10000");
						}
					}
					jbpmUtil.completeTask(task.getId(), map);//流程流转
				}else if ("3".equals(my.getOpType())&&"4".equals(my.getMa_type())){
					jbpmUtil.completeTask(task.getId());
				}else {
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 >= Double.parseDouble("10000")) {
						map.put("decisionKey", "ROLE_QXSM");
						map.put("decisionValue", "NO");
					}else{
						map.put("decisionKey", "ROLE_QXSM");
						map.put("decisionValue", "YES");
					}
					jbpmUtil.completeTask(task.getId(), map);//流程流转
				}
			}else if("区县分管经理".equals(task.getActivityName())){
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if(Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney()) / 100 <= Double.parseDouble(lateFeeMoneyData.getAmount()) ){
						if (ifOffSite){
							map.put("node", "异地缴费转代理人员");
						}else {
							map.put("node", "END");
						}
					}else{
						map.put("node", "符合继续审批要求");
					}
				}else {
					if(lateFee==0 || queryGroupLevel(groupCustomer.getGroupLevel()) ){
						map.put("node", "END");
					}else{
						map.put("node", "减免滞纳金");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);
			} else if ("省重客客户经理室经理".equals(task.getActivityName())) {
				if ("3".equals(my.getOpType())&&"3".equals(my.getMa_type())){
					if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
						if (Double.parseDouble(my.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) &&
								Double.parseDouble(my.getLateFeeMoney())/100<=Double.parseDouble(lateFeeMoneyData.getAmount())){
							if (ifOffSite){
								map.put("node", "异地缴费转代理人员");
							}else {
								map.put("node", "END");
							}
						}else{
							if(lateFee==0){
								map.put("node", "符合继续审批要求_1");
							}else{
								map.put("node", "符合继续审批要求_2");
							}
						}
					}else {
						if (Double.parseDouble(my.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) && Double.parseDouble(my.getLateFeeMoney())/100<=Double.parseDouble(lateFeeMoneyData.getAmount())){
							map.put("node", "END");
						}else{
							if((Double.parseDouble(my.getLateFeeMoney())/100)<=Double.parseDouble(lateFeeMoneyData.getAmount()) || queryGroupLevel(groupCustomer.getGroupLevel())){
								map.put("node", "金额和滞纳金大于设定金额并且不减免滞纳金");
							}else{
								map.put("node", "金额和滞纳金大于设定金额并且减免滞纳金");
							}
						}
					}
					jbpmUtil.completeTask(task.getId(), map);//流程流转
				}else if ("3".equals(my.getOpType())&&"4".equals(my.getMa_type())){
					jbpmUtil.completeTask(task.getId());
				}else {
					if(Double.parseDouble(moneyTotal.getAmount())/100>=Double.parseDouble("10000")){
						map.put("decisionKey", "ROLE_SZKSM");
						map.put("decisionValue", "NO");
					}else{
						map.put("decisionKey", "ROLE_SZKSM");
						map.put("decisionValue", "YES");
					}
					jbpmUtil.completeTask(task.getId(), map);//流程流转
				}
			} else if ("市公司客户经理室经理".equals(task.getActivityName())) {
				if ("3".equals(my.getOpType())&&"3".equals(my.getMa_type())){
					if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
						if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
								Double.parseDouble(my.getLateFeeMoney())/100<=Double.parseDouble(lateFeeMoneyData.getAmount())) {
							if (ifOffSite){
								map.put("node", "异地缴费转代理人员");
							}else {
								map.put("node", "END");
							}
						}else{
							if(lateFee==0){
								map.put("node", "符合继续审批要求_1");
							}else{
								map.put("node", "符合继续审批要求_2");
							}
						}
					}else {
						if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) && Double.parseDouble(my.getLateFeeMoney())/100<=Double.parseDouble(lateFeeMoneyData.getAmount())) {
							map.put("node", "END");
						}else{
							if((Double.parseDouble(my.getLateFeeMoney())/100)<=Double.parseDouble(lateFeeMoneyData.getAmount()) || queryGroupLevel(groupCustomer.getGroupLevel())){
								map.put("node", "金额和滞纳金大于设定金额并且不减免滞纳金");
							}else{
								map.put("node", "金额和滞纳金大于设定金额并且减免滞纳金");
							}
						}
					}
					jbpmUtil.completeTask(task.getId(), map);
				}else if ("3".equals(my.getOpType())&&"4".equals(my.getMa_type())){
					jbpmUtil.completeTask(task.getId());
				}else {
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 >= Double.parseDouble("10000")) {
						map.put("decisionKey", "ROLE_DSDM");
						map.put("decisionValue", "NO");
					}else{
						map.put("decisionKey", "ROLE_DSDM");
						map.put("decisionValue", "YES");
					}
					jbpmUtil.completeTask(task.getId(), map);
				}
			}else if("市公司政企部经理".equals(task.getActivityName())){
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if(Double.parseDouble(my.getLateFeeMoney())/100>=Double.parseDouble(lateFeeMoneyData.getAmount())){
						map.put("node", "符合继续审批要求");
					}else{
						if (ifOffSite){
							map.put("node", "异地缴费转代理人员");
						}else {
							map.put("node", "END");
						}
					}
				}else {
					if(Double.parseDouble(my.getLateFeeMoney())/100>=Double.parseDouble(lateFeeMoneyData.getAmount())){
						map.put("node", "滞纳金大于设定金额");
					}else{
						map.put("node", "END");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if("省重客分管经理".equals(task.getActivityName()) || "市公司领导".equals(task.getActivityName())){
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if (ifOffSite){
						map.put("node", "异地缴费转代理人员");
					}else {
						map.put("node", "END");
					}
				}else {
					map.put("node", "END");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else{
				jbpmUtil.completeTask(task.getId());
			}
			service.updateWait(wt, this.getRequest());
			JSONObject obj=taskService.getApproval_message(my.getApplyNo(),BigDecimal.
							valueOf(Long.valueOf(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
					task.getActivityName(), opinion, userid, user);
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			taskService.setReminder_information_tbl(my.getGroupCode(),"1","30", "资金认领", obj.toString(), "资金认领");
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
			String rtaskid =taskService.setBpms_riskoff_task(process.getProcess_sign(),"",1,"SH",tasktwo.getActivityName(),userid, user);
			commitBackLogData(my,userid, process.getProcess_sign(), user, rtaskid);// 生成待办
			Write(returnPars(1,"","亲爱的同事,工单提交成功!"));
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.info("资金认领提交异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,资金认领提交异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金认领   流程进行(完成)方法
	 * <AUTHOR>
	 * @Date 2022/6/13 10:37
	 **/
	public void complateClaimData(){
		try {
			String id = getString("id");//开票id
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			String financialPushVerify =  getString("financialPushVerify");//财务推送校验标识
			if (!"1".equals(financialPushVerify)) {
				Write(returnPars(-1,"","亲爱的同事,您当前使用版本为历史版本缺少财务推送接口,请清理缓存后重试！"));
				return;
			}
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}
			MoneyTotal moneyTotal =claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			//用于判断当前资金是否推送财务状态为已使用
			int pushOkCount=0;
			if(moneyTotal.getIsThe() == 0 && moneyTotal.getState()==5) {
				if ("4".equals(my.getMa_type())){
					if (mapList==null){
						Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
						return;
					}
					String county = mapList.get(0).get("COUNTY_NAME");
					String region = mapList.get(0).get("COMPANY_NAME");
					String fullName = mapList.get(0).get("EMPLOYEE_NAME");
					Result result= ClaimFundsOpenSrv.getInstance().returnOfFunds(moneyTotal.getSerialNo(),county,region,fullName);
					logger.info("财务退回接口调用结果===>"+result.toString());
					if(ResultCode.SUCCESS.code()==result.getCode()){
						JSONObject res=JSONObject.fromObject(result.getData());
						if("200".equals(res.getString("RETURN_CODE"))){
							JSONArray jsonArray = res.getJSONArray("SERIAL_LIST");
							if (jsonArray!=null){
								JSONObject json = jsonArray.getJSONObject(0);
								if ("200".equals(json.getString("RETURN_CODE"))){
									moneyTotal.setIsThe(moneyTotal.getIsThe() + 1);
									moneyTotal.setState(-1);
									moneyTotal.setBoss_State(1); //设置BOSS状态为成功
									moneyTotal.setBoss_Msg(json.getString("RETURN_MSG"));
									moneyTotal.setPushDate(new Date());
								}else {
									pushOkCount++;
									moneyTotal.setBoss_State(0);
									moneyTotal.setBoss_Msg("财务返回信息失败:"+json.getString("RETURN_MSG"));
								}
							}else {
								pushOkCount++;
								moneyTotal.setBoss_State(0);
								moneyTotal.setBoss_Msg("财务返回信息异常:"+res.toString());
							}
						}else {
							pushOkCount++;
							moneyTotal.setBoss_State(0);
							moneyTotal.setBoss_Msg("财务退回失败:"+res.getString("RETURN_MSG"));
						}
					}else {
						pushOkCount++;
						moneyTotal.setBoss_State(0);
						moneyTotal.setBoss_Msg("财务退回失败:"+result.getMessage());
					}
				}else {
					if (!"0".equals(moneyTotal.getSub_overAmount())) {
						String op_fee = BigDecimal.valueOf(Long.parseLong(moneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
						Result accountRes = ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(pushBossUser.getBossUserName(), moneyTotal.getGroupCode(),
								moneyTotal.getBatchNo(), moneyTotal.getOtherAccNumber(), moneyTotal.getOtherName(), moneyTotal.getUseMemo(), moneyTotal.getSerialNo(), op_fee);
						logger.info("调用资金入账接口反馈===>" + accountRes.toString());
						if (ResultCode.SUCCESS.code() == accountRes.getCode()) {  //判断当前请求是否成功
							JSONObject accountResObj = JSONObject.fromObject(accountRes.getData());
							//推送BOSS账户资金结果判断
							if ("0".equals(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_CODE"))) {
								moneyTotal.setIsThe(moneyTotal.getIsThe() + 1);
								moneyTotal.setState(1);
								moneyTotal.setBoss_State(1); //设置BOSS状态为成功
								moneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
								moneyTotal.setPushDate(new Date());
							} else {
								pushOkCount++;
								moneyTotal.setBoss_State(0);
								moneyTotal.setBoss_Msg(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_MSG"));
							}
						} else {
							pushOkCount++;
							moneyTotal.setIsThe(moneyTotal.getIsThe() + 1);
							moneyTotal.setBoss_State(0);
							moneyTotal.setBoss_Msg(accountRes.getMessage());

						}
					} else { //当主认领资金无需推送BOSS资金认账，则直接调整BOSS状态为推送状态
						moneyTotal.setState(1);
						moneyTotal.setBoss_State(1); //设置BOSS状态为成功
						moneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
					}
				}
				moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
			}
			if(moneyTotal.getBoss_State()==1){
				if ("2".equals(my.getMa_type())){
					//判断是否存在划拨集团
					List<MoneyApplySubGroup> subGroupList = claimForFundsService.getMoneyApplySubGroup(my.getId());
					if(subGroupList!=null){
						//实现循环推送BOSS入账，如果调用接口失败则进行下一条推送。将整体子集团认领数据推送一次
						for (int m = 0; m < subGroupList.size(); m++) {
							MoneyTotal subMoneyTotal = claimForFundsService.getMoneyTotal(subGroupList.get(m).getMoneyTotal_id());
							MoneyApplySubGroup moneyApplySubGroup=subGroupList.get(m);
							if(subMoneyTotal.getState()==5 && subMoneyTotal.getIsThe()==0){ //判断数据是否推送过BOSS，如果未推送则执行推送接口
								SystemUser subUser = systemUserService.getUserInfoRowNo(subMoneyTotal.getPushUserName());
								String sub_op_fee=BigDecimal.valueOf(Long.parseLong(subMoneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
								Result accountsubRes=ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(subUser.getBossUserName(),subMoneyTotal.getGroupCode(),
										subMoneyTotal.getBatchNo(),subMoneyTotal.getOtherAccNumber(),subMoneyTotal.getOtherName(),subMoneyTotal.getUseMemo(),subMoneyTotal.getSerialNo(),sub_op_fee);
								logger.info("调用资金入账接口反馈===>"+accountsubRes.toString());
								if(ResultCode.SUCCESS.code()==accountsubRes.getCode()){  //判断当前请求是否成功
									JSONObject accountsubResObj=JSONObject.fromObject(accountsubRes.getData());
									subMoneyTotal.setIsThe(subMoneyTotal.getIsThe() + 1);
									subMoneyTotal=	claimForFundsService.updateMoneyTotalTwo(subMoneyTotal);
									//推送BOSS账户资金结果判断
									if("0".equals(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_CODE"))){
										subMoneyTotal.setState(1);
										subMoneyTotal.setBoss_State(1); //设置BOSS状态为成功
										subMoneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										moneyTotal.setPushDate(new Date());
										moneyApplySubGroup.setBoss_State(1);
										moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
									}else{
										pushOkCount++;
										subMoneyTotal.setBoss_State(0);
										subMoneyTotal.setBoss_Msg(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG"));
										moneyApplySubGroup.setBoss_State(0);
										moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										logger.info("资金入账接口反馈异常【"+JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG")+"】");
									}
								}else{
									pushOkCount++;
									subMoneyTotal.setBoss_State(0);
									subMoneyTotal.setBoss_Msg(accountsubRes.getMessage());
									moneyApplySubGroup.setBoss_State(0);
									moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
									logger.info("调用BOSS资金入账接口异常【"+accountsubRes.getMessage()+"】");
								}
								claimForFundsService.updateMoneyTotalTwo(subMoneyTotal);
								claimForFundsService.updateMoneyApplySubGroup(moneyApplySubGroup);
							}
						}
					}else {
						Write(returnPars(-1,"","亲爱的同事,划拨资金信息异常[获取子集团信息失败:"+my.getId()+"],请联系管理员处理!"));
						return;
					}
				}else if ("3".equals(my.getMa_type())){
					List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDet(my.getApplyNo());
					Boolean whetherToPush = true;
					for (int l = 0; l < moneyApplyDetList.size(); l++) {
						MoneyApplyDet moneyApplyDet=moneyApplyDetList.get(l);
						String login_no;
						if (("01".equals(moneyApplyDet.getUseType()) || "07".equals(moneyApplyDet.getUseType())) && moneyApplyDet.getBossNo()!=null){
							SystemUser BossUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
							if (BossUser!=null && BossUser.getBossUserName()!=null){
								login_no=BossUser.getBossUserName();
							}else {
								pushOkCount++;
								moneyApplyDet.setBossState("1");
								moneyApplyDet.setBossMsg("代理缴费用户信息异常，不能进行缴费，请核实！");
								claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
								continue;
							}
						}else {
							login_no=pushBossUser.getBossUserName();
						}
						String unit_id=moneyApplyDet.getGroupCode();
						String op_type="03"; //使用申请
						String out_sys_accept=moneyApplyDet.getMoneyNo();
						String busi_flag="";
						String contract_no="";
						String phone_no="";
						if("1".equals(moneyApplyDet.getOrderType())){
							busi_flag="P";
							phone_no=moneyApplyDet.getContrctNo();
						}else if("2".equals(moneyApplyDet.getOrderType())){
							busi_flag="G";
							contract_no=moneyApplyDet.getContrctNo();
						}
						String prod_name=moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount();
						String prod_num="0";
						String apply_login=pushBossUser.getBossUserName();
						String apply_note=my.getApplyMemo()==null?"无":my.getApplyMemo();
						String bill_note="-";
						String bank_account=moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber();
						String bank_account_name=moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName();

						String purpose=moneyTotal.getMemo()==null?"无":moneyTotal.getMemo();
						String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
						String pay_type = "0";
						String busi_type="";
						String delay_rate="0";
						String pre_invoice_accept="";
						String route_key = "";
						String userIdNo = "";
						String customerNumber = "";
						if("1".equals(moneyApplyDet.getUseType()) || "01".equals(moneyApplyDet.getUseType())){ //缴费
							if ("01".equals(moneyApplyDet.getUseType())){
								route_key = "10";
							}
							busi_type="00";
							delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
						}else if("2".equals(moneyApplyDet.getUseType())){ //存送
							busi_type="02";
							if("2".equals(moneyApplyDet.getOrderType())){       //集团存送时需推送一个办理号码
								phone_no=moneyApplyDet.getInvNo();
							}
						}else if("3".equals(moneyApplyDet.getUseType())){ //终端
							busi_type="01";
						}else if(moneyApplyDet.getUseType().contains("05")){ //预开票
							busi_type="05";
							//专线预开票
							if ("05A".equals(moneyApplyDet.getUseType())){
								pay_type = "22";
								phone_no = moneyApplyDet.getSpecialLineNo();
								userIdNo = moneyApplyDet.getUserIdNo();
							}
							delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
							pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
						}else if("06".equals(moneyApplyDet.getUseType())){ //有价卡
							busi_type="06";
							pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
						}else if("07".equals(moneyApplyDet.getUseType())){ //物联网
							busi_type="07";
						}else if("08".equals(moneyApplyDet.getUseType())){ //ICT设备销售
							busi_type="08";
							delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
							if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
								pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
							}
						}else if("09".equals(moneyApplyDet.getUseType())){ //ICT终端销售
							busi_type="09";
							delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
							if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
								pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
							}
						}else if("10".equals(moneyApplyDet.getUseType())){ //ICT软件销售
							busi_type="10";
							delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
							if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
								pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
							}
						}else if ("11".equals(moneyApplyDet.getUseType())){
							busi_type="11";
							pre_invoice_accept = moneyApplyDet.getInvNo();
							userIdNo = moneyApplyDet.getUserIdNo();
							delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
						}else if (moneyApplyDet.getUseType().contains("12")){
							busi_type="12";
							customerNumber = moneyApplyDet.getCustomerNumber();
						}else {
							busi_type = moneyApplyDet.getUseType();
						}
						String out_back_accept=moneyApplyDet.getMoneyNo();
						String ctrlFlag = moneyApplyDet.getCtrlFlag();
						String beginCycle = moneyApplyDet.getBeginCycle();

						if (whetherToPush){
							Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(login_no, unit_id, op_type, out_sys_accept, contract_no,
									busi_flag, prod_name, prod_num, apply_login, apply_note, bill_note,
									bank_account, bank_account_name, phone_no, purpose, busi_fee, busi_type,
									out_back_accept, delay_rate, pre_invoice_accept,route_key,pay_type,userIdNo,customerNumber,ctrlFlag);
							logger.info(moneyApplyDet.getMoneyNo()+"推送资金使用明细结果："+applyRes.toString());
							if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
								JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
								JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
								//循环推送申请工单中的明细记录，成功并记录成功和失败数据
								if("0".equals(ROOT.getString("RETURN_CODE"))){
									moneyApplyDet.setBossState("0");
									moneyApplyDet.setState("1");
									moneyApplyDet.setPushDate(new Date());
									JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
									if (OUT_DATA.has("PAYMENT_ACCEPT")){
										if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
											moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
											InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
											invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
											invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
											if (moneyApplyDet.getUnitTaxplayer()!=null && moneyApplyDet.getUnitTaxplayer().length()>0){
												try {
													JSONObject UnitTaxplayer = JSONObject.fromObject(moneyApplyDet.getUnitTaxplayer());
													invoiceMiddle.setMsgRecvPhone(UnitTaxplayer.getString("RECEIVENUMBER"));
													invoiceMiddle.setTaxpayerId(UnitTaxplayer.getString("TAXPAYER_ID"));
													invoiceMiddle.setBankName(UnitTaxplayer.getString("BANK_NAME"));
													invoiceMiddle.setBankAccount(UnitTaxplayer.getString("BANK_ACCOUNT"));
													invoiceMiddle.setAddress(UnitTaxplayer.getString("ADDRESS"));
													invoiceMiddle.setPhoneNo(UnitTaxplayer.getString("PHONE_NO"));
													invoiceMiddle.setPrintType("1");
												}catch (Exception e){
													invoiceMiddle.setPrintType("3");
													invoiceMiddle.setBossMsg("数据解析异常："+e.getMessage());
												}
											}else {
												invoiceMiddle.setPrintType("0");
											}
											claimForFundsService.addInvoiceMiddle(invoiceMiddle);
										}
									}
								}else {
									pushOkCount++;
									moneyApplyDet.setBossState("1");
									moneyApplyDet.setState("0");
									moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG"));
									if (moneyApplyDet.getUseType().contains("05") && beginCycle!=null && beginCycle.length()>0){
										whetherToPush = false;
									}
								}
								claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
							}else{
								moneyApplyDet.setBossState("-1");
								moneyApplyDet.setState("0");
								//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
								moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
								claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
								if (moneyApplyDet.getUseType().contains("05") && beginCycle!=null && beginCycle.length()>0){
									whetherToPush = false;
								}
							}
						}else {
							pushOkCount++;
							moneyApplyDet.setBossState("1");
							moneyApplyDet.setBossMsg("该账期前还有其他账期未推送");
							claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
						}
					}
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事,资金推送资金入账结果异常,请联系管理员处理!"));
				return;
			}

			Map<String, String> map = new HashMap<>();
			if ("3".equals(my.getMa_type())){
				if(tasktwo.getActivityName().contains("代理缴费")){
					jbpmUtil.completeTask(tasktwo.getId(), "END");
				}else {
					map.put("node", "END");
					jbpmUtil.completeTask(tasktwo.getId(), map);
				}
			}else {
				String t = "";
				if ("区县政企部主任".equals(tasktwo.getActivityName())) {
					t = "ROLE_QXSM";
					map.put("decisionKey", t);
					map.put("decisionValue", "YES");
					jbpmUtil.completeTask(tasktwo.getId(), map, t);
				} else if ("市公司客户经理室经理".equals(tasktwo.getActivityName())) {
					t = "ROLE_DSDM";
					map.put("decisionKey", t);
					map.put("decisionValue", "YES");
					jbpmUtil.completeTask(tasktwo.getId(), map, t);
				} else if ("省重客客户经理室经理".equals(tasktwo.getActivityName())) {
					t = "ROLE_SZKSM";
					map.put("decisionKey", t);
					map.put("decisionValue", "YES");
					jbpmUtil.completeTask(tasktwo.getId(), map, t);
				} else {
					jbpmUtil.completeTask(tasktwo.getId(), "END");
				}
			}

			if (pushOkCount > 0) {
				my.setState("3");
			} else {
				my.setState("0");
			}
			claimForFundsService.updateMoneyApply(my);
			JSONObject obj = taskService.getCompleted_message(my.getApplyNo(), BigDecimal.
							valueOf(Long.valueOf(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
					tasktwo.getActivityName(), opinion, user.getRowNo());
			taskService.setReminder_information_tbl(my.getGroupCode(), "1", "30", "资金认领", obj.toString(), "资金认领");
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", "起草人", Integer.valueOf(my.getCreatorId()), user);
			commitBackLogData(my, Integer.parseInt(my.getCreatorId()), process.getProcess_sign(), user, rtaskid);// 生成待办
			service.updateWait(wt, this.getRequest());
			Write(returnPars(1,"","亲爱的同事,数据推送成功,工单已完成!"));
		} catch (Exception e) {
			logger.info("资金认领错误信息：" + e.getMessage(), e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,资金认领完成异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金认领 流程退回方法
	 * <AUTHOR>
	 * @Date 2022/6/13 10:52 
	 **/
	public void returnClaimData(){
		try {
			String id = getString("id");//开票id
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			my.setState("2");// 修改状态为退回
			MoneyApply may= claimForFundsService.updateMoneyApply(my);
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			service.updateWait(wt, this.getRequest());
			String rtaskid =taskService.setBpms_riskoff_task(process.getProcess_sign(),"",1,"SH","起草人",Integer.valueOf(my.getCreatorId()), user);
			commitBackLogData(may, Integer.parseInt(may.getCreatorId()),process.getProcess_sign(), user,rtaskid);//生成待办
			jbpmUtil.deleteProcessInstance(process.getProcess_sign());//删除流程
			Write(returnPars(1,"","亲爱的同事,工单已退回!"));
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金认领退回异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,资金认领退回异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金认领 流程作废方法
	 * <AUTHOR>
	 * @Date 2022/6/13 11:20
	 **/
	public void InvalidClaimData() {
		try {
			String id = getString("id");//开票id
			String userPhoneNum = getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			if (moneyTotal.getState()==5){
				List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
				if (mapList==null){
					Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
					return;
				}
				String county = mapList.get(0).get("COUNTY_NAME");
				String region = mapList.get(0).get("COMPANY_NAME");
				String fullName = mapList.get(0).get("EMPLOYEE_NAME");
				Result result=ClaimFundsOpenSrv.getInstance().updateIncomeState("0",moneyTotal.getSerialNo(),county,region,fullName);
				logger.info("财务预占接口调用结果===>"+result.toString());
				if(ResultCode.SUCCESS.code()==result.getCode()){  //判断当前请求是否成功
					JSONObject res=JSONObject.fromObject(result.getData());
					if("success".equals(res.getString("code"))){
						//如果推送财务接口成功，将财务数据状态调整未使用
						Long resmoney=0L;
						if ("3".equals(my.getMa_type())){
							List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
							for(int i=0;i<moneyApplyDet.size();i++){
								moneyApplyDet.get(i).setState("4");
								claimForFundsService.updateMoneyApplyDet(moneyApplyDet.get(i));
								if ("2".equals(moneyApplyDet.get(i).getUseType()) && !updaetQuotaWorkHandleByMoneyNo(moneyApplyDet.get(i).getMoneyNo(),"0")){
									throw new Exception("修改营销活动失败！");
								}else if("06".equals(moneyApplyDet.get(i).getUseType()) && !updateValuableCard(moneyApplyDet.get(i).getInvNo(),moneyApplyDet.get(i).getContrctNo(),moneyApplyDet.get(i).getAmount(),"DELECT")){
									throw new Exception("修改有价卡失败！");
								}else if ("12B".equals(moneyApplyDet.get(i).getUseType()) && !updateInternetOfThingsDet(moneyApplyDet.get(i).getInvNo(),moneyApplyDet.get(i).getContrctNo(),moneyApplyDet.get(i).getAmount(),"DELECT")){
									throw new Exception("修改物联网预开票失败！");
								};
								resmoney=resmoney+Long.parseLong(moneyApplyDet.get(i).getAmount());
							}
						}else if ("2".equals(my.getMa_type())){
							List<MoneyTotal> moneyTotalList = claimForFundsService.getMoneyTotalSerialNoList(my.getSerialNo());
							for (int i = 0; i < moneyTotalList.size(); i++) {
								if (moneyTotalList.get(i).getIs_sub_group()!=null && moneyTotalList.get(i).getIs_sub_group() == 1) {
									claimForFundsService.deleteMoneyTotal(moneyTotalList.get(i).getId());
									resmoney=resmoney+Long.parseLong(moneyTotalList.get(i).getOverAmount());
								}
							}
						}
						//判断划拨的剩余金额+认领总资金的金额是否总金额值
						if(resmoney+Long.parseLong(moneyTotal.getOverAmount())==Long.parseLong(moneyTotal.getAmount())){
							moneyTotal.setPushUserName(null);
							moneyTotal.setPushBossUserName(null);
							moneyTotal.setIsThe(0);
							moneyTotal.setGroupCode(null);
							moneyTotal.setGroupName(null);
							moneyTotal.setUserid(null);
							moneyTotal.setState(0);
							moneyTotal.setUseAmount("0");
							moneyTotal.setOverAmount(moneyTotal.getAmount());
							moneyTotal.setSub_overAmount(moneyTotal.getAmount());
						}else{
							Write(returnPars(-1,"","亲爱的同事,认领资金["+moneyTotal.getSerialNo()+"]金额异常,请联系管理员处理!"));
							throw new Exception("认领资金["+moneyTotal.getSerialNo()+"]金额异常");
						}
						claimForFundsService.updateMoneyTotal(moneyTotal);
						taskService.updateBpms_riskoff_task("工单已作废!", 2, btask.getId());
						my.setState("-1");//状态修改为作废
						claimForFundsService.updateMoneyApply(my);
						service.updateWait(wt, this.getRequest());
						Write(returnPars(1,"","亲爱的同事,工单作废成功!"));
					}else {
						Write(returnPars(-1,"","亲爱的同事,请求财务稽核系统预占结果失败,请确认:" + res.getString("info")));
					}
				}else{
					Write(returnPars(-1,"","亲爱的同事,调用财务预占接口失败，失败原因["+result.getMessage()+"]"));

				}
			}else{
				Write(returnPars(-1,"","亲爱的同事,资金数据状态异常，请联系管理员！"));
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("认领工单作废异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,认领工单作废异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金认领流程 工单结束方法
	 * <AUTHOR>
	 * @Date 2022/6/13 10:43 
	 **/
	public void AgainClaimData() {
		try {
			String id = getString("id");//开票id
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wait = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wait == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			List<MoneyTotal> moneyTotalList = claimForFundsService.getMoneyTotalSerialNoList(my.getSerialNo());
			if ("3".equals(my.getState())){
				if (moneyTotal.getBoss_State()==1){      //判断认领是否成功
					Boolean flag = false;
					SystemUser zcUser = null;
					long resmoney=0;
					if ("3".equals(my.getMa_type())){
						List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
						for (MoneyApplyDet moneyApplyDet : moneyApplyDetList) {
							if ("1".equals(moneyApplyDet.getBossState())) {
								moneyApplyDet.setState("4");
								claimForFundsService.updateMoneyApplyDet(moneyApplyDet);
								if ("2".equals(moneyApplyDet.getUseType()) && !updaetQuotaWorkHandleByMoneyNo(moneyApplyDet.getMoneyNo(), "0")) {
									throw new Exception("修改营销活动失败！");
								} else if ("06".equals(moneyApplyDet.getUseType()) && !updateValuableCard(moneyApplyDet.getInvNo(), moneyApplyDet.getContrctNo(), moneyApplyDet.getAmount(), "DELECT")) {
									throw new Exception("修改有价卡失败！");
								} else if ("12B".equals(moneyApplyDet.getUseType()) && !updateInternetOfThingsDet(moneyApplyDet.getInvNo(), moneyApplyDet.getContrctNo(), moneyApplyDet.getAmount(), "DELECT")) {
									throw new Exception("修改物联网预开票失败！");
								}
								;
								resmoney = resmoney + Long.parseLong(moneyApplyDet.getAmount());
							} else if ("07".equals(moneyApplyDet.getUseType()) && moneyApplyDet.getBossNo() != null) {
								zcUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
								if (zcUser != null) {
									flag = true;
								}
							}
						}
					}else if ("2".equals(my.getMa_type())){
						//判断划拨推送记录   如果存在一条失败的划拨记录   就作废全部认领记录
						for (MoneyTotal total : moneyTotalList) {
							if (total.getIs_sub_group() != null && total.getIs_sub_group() == 1 && total.getBoss_State().equals(0)) {
								resmoney = resmoney + Long.parseLong(total.getOverAmount());
								break;
							}
						}
					}
					if (resmoney>0){
						//当工单为认领划拨时  划拨记录推送失败将冲正认领记录
						if ("2".equals(my.getMa_type())){
							for (MoneyTotal total : moneyTotalList) {
								SystemUser pushUser = systemUserService.getByUserInfoRowNo(total.getPushUserName());
								Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(total.getBatchNo(), pushUser.getBossUserName());
								if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
									JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
									JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
									if ("0".equals(root.getString("RETURN_CODE"))) {
										moneyTotal.setBak1(moneyTotal.getBatchNo());
										moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo()) + taskService.getNumber());
										//boss推送成功后调用财务接口修改财务数据，实现实时冲正
										List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
										if (mapList==null){
											Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
											return;
										}
										String county = mapList.get(0).get("COUNTY_NAME");
										String region = mapList.get(0).get("COMPANY_NAME");
										String fullName = mapList.get(0).get("EMPLOYEE_NAME");
										Result result = ClaimFundsOpenSrv.getInstance().updateIncomeState("0", moneyTotal.getSerialNo(),county,region,fullName);
										logger.info("财务接口调用结果===>" + result.toString());
										if (ResultCode.SUCCESS.code() == result.getCode()) {
											moneyTotal.setPushUserName(null);
											moneyTotal.setPushBossUserName(null);
											moneyTotal.setIsThe(0);
											moneyTotal.setGroupCode(null);
											moneyTotal.setGroupName(null);
											moneyTotal.setUserid(null);
											moneyTotal.setState(0);
											moneyTotal.setUseAmount("0");
											moneyTotal.setOverAmount(moneyTotal.getAmount());
											moneyTotal.setSub_overAmount(moneyTotal.getAmount());
											moneyTotal.setBoss_Msg("上次操作人：" + user.getEmployeeName() + "在: " + new Date() + "因为工单划拨失败,认领作废!");
											claimForFundsService.updateMoneyTotal(moneyTotal);
										} else {
											moneyTotal.setState(3);
											moneyTotal.setBoss_Msg("上次操作人：" + user.getEmployeeName() + "在" + getStringDatetwo(new Date()) + "因为工单划拨失败，进行了冲正操作,推送财务接口异常，财务报文：【" + result + " 】");
											claimForFundsService.updateMoneyTotal(moneyTotal);
											Write(returnPars(-1, "", "亲爱的同事,冲正认领资金未完成,修改财务数据失败，请联系管理员处理!"));
										}
									} else {
										logger.info("资金账户:" + moneyTotal.getBatchNo() + "冲正失败" + JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
										moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
										claimForFundsService.updateMoneyTotal(moneyTotal);
										Write(returnPars(-1, "", "资金认领冲正接口反馈异常【" + JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG") + "】"));
										return;
									}
								} else {
									Write(returnPars(-1, "", "调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】"));
									return;
								}
							}
							//当所以资金全部冲正后  删除子集团信息
							for (MoneyTotal total : moneyTotalList) {
								if (total.getIs_sub_group() != null && total.getIs_sub_group() == 1) {
									claimForFundsService.deleteMoneyTotal(total.getId());
								}
							}
						}else {
							moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) - resmoney));
							moneyTotal.setOverAmount(String.valueOf(resmoney + Long.parseLong(moneyTotal.getOverAmount())));
							claimForFundsService.updateMoneyTotal(moneyTotal);
						}

						my.setState("0");
						claimForFundsService.updateMoneyApply(my);

						taskService.updateBpms_riskoff_task("工单部分推送成功,"+("3".equals(my.getMa_type())?"资金使用":"子集团划拨")+" 失败部分金额已归还到资金信息中,请核实!", 2, btask.getId());
						service.updateWait(wait, this.getRequest());
						if (flag && "SH".equals(btask.getType()) ){
							String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CS", "物联网支撑", zcUser.getRowNo(), user);
							commitBackLog(my,zcUser.getRowNo(), process.getProcess_sign(), user, rtaskid);// 生成待办
						}
						Write(returnPars(1,"","亲爱的同事,工单部分推送成功,"+("3".equals(my.getMa_type())?"资金使用失败的部分金额已归还到资金信息中,请核实!":"集团划拨失败,资金信息已还原,请核实")));
					}else {
						taskService.updateBpms_riskoff_task("工单数据推送成功,已完结!", 2, btask.getId());
						service.updateWait(wait, this.getRequest());
						if (flag && "SH".equals(btask.getType()) ){
							String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CS", "物联网支撑", zcUser.getRowNo(), user);
							commitBackLog(my,zcUser.getRowNo(), process.getProcess_sign(), user, rtaskid);// 生成待办
						}
						Write(returnPars(1,"","亲爱的同事,工单数据推送成功,已完结!"));
					}
				}else{
					List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
					if (mapList==null){
						Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
						return;
					}
					String county = mapList.get(0).get("COUNTY_NAME");
					String region = mapList.get(0).get("COMPANY_NAME");
					String fullName = mapList.get(0).get("EMPLOYEE_NAME");
					Result result=ClaimFundsOpenSrv.getInstance().updateIncomeState("0",moneyTotal.getSerialNo(),county,region,fullName);
					logger.info("财务预占接口调用结果===>"+result.toString());
					if(ResultCode.SUCCESS.code()==result.getCode()){  //判断当前请求是否成功
						JSONObject res=JSONObject.fromObject(result.getData());
						if("success".equals(res.getString("code"))){
							//如果推送财务接口成功，将财务数据状态调整未使用
							Long resmoney=0L;
							if ("3".equals(my.getMa_type())){
								List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
								for (MoneyApplyDet applyDet : moneyApplyDet) {
									applyDet.setState("4");
									applyDet.setBossMsg("工单认领未成功,已结束工单资金信息已还原，失败原因：" + applyDet.getBossMsg());
									claimForFundsService.updateMoneyApplyDet(applyDet);
									if ("2".equals(applyDet.getUseType()) && !updaetQuotaWorkHandleByMoneyNo(applyDet.getMoneyNo(), "0")) {
										throw new Exception("修改营销活动失败！");
									} else if ("06".equals(applyDet.getUseType()) && !updateValuableCard(applyDet.getInvNo(), applyDet.getContrctNo(), applyDet.getAmount(), "DELECT")) {
										throw new Exception("修改有价卡失败！");
									} else if ("12B".equals(applyDet.getUseType()) && !updateInternetOfThingsDet(applyDet.getInvNo(), applyDet.getContrctNo(), applyDet.getAmount(), "DELECT")) {
										throw new Exception("修改物联网预开票失败！");
									}
									;
									resmoney = resmoney + Long.parseLong(applyDet.getAmount());
								}
							}else if ("2".equals(my.getMa_type())){
								for (MoneyTotal total : moneyTotalList) {
									if (total.getIs_sub_group() != null && total.getIs_sub_group() == 1) {
										claimForFundsService.deleteMoneyTotal(total.getId());
										resmoney = resmoney + Long.parseLong(total.getOverAmount());
									}
								}
							}
							//判断划拨的剩余金额+认领总资金的金额是否总金额值
							if(resmoney+Long.parseLong(moneyTotal.getOverAmount())==Long.parseLong(moneyTotal.getAmount())){
								moneyTotal.setPushUserName(null);
								moneyTotal.setPushBossUserName(null);
								moneyTotal.setIsThe(0);
								moneyTotal.setGroupCode(null);
								moneyTotal.setGroupName(null);
								moneyTotal.setUserid(null);
								moneyTotal.setState(0);
								moneyTotal.setUseAmount("0");
								moneyTotal.setOverAmount(moneyTotal.getAmount());
								moneyTotal.setSub_overAmount(moneyTotal.getAmount());
							}else{
								Write(returnPars(-1,"","亲爱的同事,认领资金["+moneyTotal.getSerialNo()+"]金额异常,请联系管理员处理!"));
								throw new Exception("认领资金["+moneyTotal.getSerialNo()+"]金额异常");
							}
							claimForFundsService.updateMoneyTotal(moneyTotal);
							taskService.updateBpms_riskoff_task("工单认领未成功,已结束工单资金信息已还原!", 2, btask.getId());
							my.setState("0");
							claimForFundsService.updateMoneyApply(my);
							service.updateWait(wait, this.getRequest());
							Write(returnPars(1,"","亲爱的同事,工单认领未成功,资金信息已还原,请重新认领!"));
						}else {
							Write(returnPars(-1,"","亲爱的同事,请求财务稽核系统预占结果失败,请确认:" + res.getString("info")));
						}
					}else{
						Write(returnPars(-1,"","亲爱的同事,调用财务预占接口失败，失败原因["+result.getMessage()+"]"));
					}
				}
			}else {
				Boolean flag = false;
				SystemUser zcUser = null;
				if ("3".equals(my.getMa_type())){
					List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
					for (MoneyApplyDet moneyApplyDet : moneyApplyDetList) {
						if ("07".equals(moneyApplyDet.getUseType()) && moneyApplyDet.getBossNo() != null) {
							zcUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
							if (zcUser != null) {
								flag = true;
								break;
							}
						}
					}
				}
				taskService.updateBpms_riskoff_task("工单数据推送成功,已完结!", 2, btask.getId());
				service.updateWait(wait, this.getRequest());
				if (flag && "SH".equals(btask.getType())){
					String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CS", "物联网支撑", zcUser.getRowNo(), user);
					commitBackLog(my,zcUser.getRowNo(), process.getProcess_sign(), user, rtaskid);// 生成待办
				}
				Write(returnPars(1,"","亲爱的同事,工单数据推送成功,已完结!"));
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金认领结束异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,资金认领结束异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金认领流程 工单部分推送成功再次推送方法
	 * <AUTHOR>  
	 * @Date 2022/6/13 10:46 
	 **/
	public void AgainSetClaimData() {
		try {
			String id = getString("id");//开票id
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wait = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wait == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			if (moneyTotal.getBoss_State()==0){     //判断认领是否成功
				if ("4".equals(my.getMa_type())){
					if (mapList==null){
						Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
						return;
					}
					String county = mapList.get(0).get("COUNTY_NAME");
					String region = mapList.get(0).get("COMPANY_NAME");
					String fullName = mapList.get(0).get("EMPLOYEE_NAME");
					Result result= ClaimFundsOpenSrv.getInstance().returnOfFunds(moneyTotal.getSerialNo(),county,region,fullName);
					logger.info("财务退回接口调用结果===>"+result.toString());
					if(ResultCode.SUCCESS.code()==result.getCode()){
						JSONObject res=JSONObject.fromObject(result.getData());
						if("200".equals(res.getString("RETURN_CODE"))){
							JSONArray jsonArray = res.getJSONArray("SERIAL_LIST");
							if (jsonArray!=null){
								JSONObject json = jsonArray.getJSONObject(0);
								if ("200".equals(json.getString("RETURN_CODE"))){
									moneyTotal.setIsThe(moneyTotal.getIsThe() + 1);
									moneyTotal.setState(-1);
									moneyTotal.setBoss_State(1); //设置BOSS状态为成功
									moneyTotal.setBoss_Msg(json.getString("RETURN_MSG"));
									moneyTotal.setPushDate(new Date());
								}else {
									moneyTotal.setBoss_State(0);
									moneyTotal.setBoss_Msg("财务返回信息失败:"+json.getString("RETURN_MSG"));
									Write(returnPars(-1,"","财务返回信息失败:"+json.getString("RETURN_MSG")));
									return;
								}
							}else {
								moneyTotal.setBoss_State(0);
								moneyTotal.setBoss_Msg("财务返回信息异常:"+res.toString());
								Write(returnPars(-1,"","财务返回信息异常:"+res.toString()));
								return;
							}
						}else {
							moneyTotal.setBoss_State(0);
							moneyTotal.setBoss_Msg("财务退回失败:"+res.getString("RETURN_MSG"));
							Write(returnPars(-1,"","财务退回失败:"+res.getString("RETURN_MSG")));
							return;
						}
					}else {
						moneyTotal.setBoss_State(0);
						moneyTotal.setBoss_Msg("财务退回失败:"+result.getMessage());
						Write(returnPars(-1,"","财务退回失败:"+result.getMessage()));
						return;
					}
				}else {
					String op_fee = BigDecimal.valueOf(Long.parseLong(moneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
					Result accountRes = ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(pushBossUser.getBossUserName(), moneyTotal.getGroupCode(),
							moneyTotal.getBatchNo(), moneyTotal.getOtherAccNumber(), moneyTotal.getOtherName(), moneyTotal.getUseMemo(), moneyTotal.getSerialNo(), op_fee);
					logger.info("调用资金入账接口反馈===>" + accountRes.toString());
					if (ResultCode.SUCCESS.code() == accountRes.getCode()) {  //判断当前请求是否成功
						JSONObject accountResObj = JSONObject.fromObject(accountRes.getData());
						moneyTotal.setIsThe(moneyTotal.getIsThe() + 1);
						moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
						//推送BOSS账户资金结果判断
						if ("0".equals(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_CODE"))) {
							moneyTotal.setState(1);
							moneyTotal.setBoss_State(1); //设置BOSS状态为成功
							moneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
							moneyTotal.setPushDate(new Date());
							moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
						} else {
							moneyTotal.setBoss_State(0);
							moneyTotal.setBoss_Msg(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_MSG"));
							claimForFundsService.updateMoneyTotalTwo(moneyTotal);
							Write(returnPars(-1,"",JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_MSG")));
							return;
						}
					} else {
						moneyTotal.setBoss_State(0);
						moneyTotal.setBoss_Msg(accountRes.getMessage());
						claimForFundsService.updateMoneyTotalTwo(moneyTotal);
						Write(returnPars(-1,"",accountRes.getMessage()));
						return;
					}
				}
			}
			int countSubBoss=0;
			StringBuilder str = new StringBuilder();
			SystemUser zcUser = null;
			Boolean flag = false;
			if(moneyTotal.getBoss_State()==1){
				if ("2".equals(my.getMa_type())){
					List<MoneyApplySubGroup> subGroupList = claimForFundsService.getMoneyApplySubGroup(my.getId());
					if(subGroupList!=null){
						//实现循环推送BOSS入账，如果调用接口失败则进行下一条推送。将整体子集团认领数据推送一次
						for (int m = 0; m < subGroupList.size(); m++) {
							MoneyTotal subMoneyTotal = claimForFundsService.getMoneyTotal(subGroupList.get(m).getMoneyTotal_id());
							MoneyApplySubGroup moneyApplySubGroup=subGroupList.get(m);
							if(subMoneyTotal.getBoss_State().equals(0)){ //判断子集团是否推送成功,不成功再推送一次
								SystemUser subUser = systemUserService.getUserInfoRowNo(subMoneyTotal.getPushUserName());
								String sub_op_fee=BigDecimal.valueOf(Long.parseLong(subMoneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
								Result accountsubRes=ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(subUser.getBossUserName(),subMoneyTotal.getGroupCode(),
										subMoneyTotal.getBatchNo(),subMoneyTotal.getOtherAccNumber(),subMoneyTotal.getOtherName(),subMoneyTotal.getUseMemo(),subMoneyTotal.getSerialNo(),sub_op_fee);
								logger.info("调用资金入账接口反馈===>"+accountsubRes.toString());
								if(ResultCode.SUCCESS.code()==accountsubRes.getCode()){  //判断当前请求是否成功
									JSONObject accountsubResObj=JSONObject.fromObject(accountsubRes.getData());
									subMoneyTotal.setIsThe(subMoneyTotal.getIsThe() + 1);
									subMoneyTotal=	claimForFundsService.updateMoneyTotalTwo(subMoneyTotal);
									//推送BOSS账户资金结果判断
									if("0".equals(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_CODE"))){
										subMoneyTotal.setState(1);
										subMoneyTotal.setBoss_State(1); //设置BOSS状态为成功
										subMoneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										moneyTotal.setPushDate(new Date());
										moneyApplySubGroup.setBoss_State(1);
										moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
									}else{
										subMoneyTotal.setBoss_State(0);
										subMoneyTotal.setBoss_Msg(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG"));
										moneyApplySubGroup.setBoss_State(0);
										moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										++countSubBoss;
										str.append("资金入账接口反馈异常【").append(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG")).append("】、");
									}
								}else{
									subMoneyTotal.setBoss_State(0);
									subMoneyTotal.setBoss_Msg(accountsubRes.getMessage());
									moneyApplySubGroup.setBoss_State(0);
									moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
									++countSubBoss;
									str.append("资金入账接口反馈异常【").append(accountsubRes.getMessage()).append("】、");

								}
								claimForFundsService.updateMoneyTotalTwo(subMoneyTotal);
								claimForFundsService.updateMoneyApplySubGroup(moneyApplySubGroup);
							}
						}
					}else {
						Write(returnPars(-1,"","亲爱的同事,划拨资金信息异常[获取子集团信息失败:"+my.getId()+"],请联系管理员处理!"));
						return;
					}
				}else if ("3".equals(my.getMa_type())){
					List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
					Boolean whetherToPush = true;
					for (int l = 0; l < moneyApplyDetList.size(); l++) {
						MoneyApplyDet moneyApplyDet=moneyApplyDetList.get(l);
						if ("1".equals(moneyApplyDet.getBossState())){
							String login_no;
							if (("01".equals(moneyApplyDet.getUseType()) || "07".equals(moneyApplyDet.getUseType())) && moneyApplyDet.getBossNo()!=null){
								SystemUser BossUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
								if (BossUser!=null && BossUser.getBossUserName()!=null){
									login_no=BossUser.getBossUserName();
								}else {
									countSubBoss++;
									moneyApplyDet.setBossState("1");
									moneyApplyDet.setBossMsg("代理缴费用户信息异常，不能进行缴费，请核实！");
									claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
									continue;
								}
							}else {
								login_no=pushBossUser.getBossUserName();
							}
							String unit_id=moneyApplyDet.getGroupCode();
							String op_type="03"; //使用申请
							String out_sys_accept=moneyApplyDet.getMoneyNo();
							String busi_flag="";
							String contract_no="";
							String phone_no="";
							if("1".equals(moneyApplyDet.getOrderType())){
								busi_flag="P";
								phone_no=moneyApplyDet.getContrctNo();
							}else if("2".equals(moneyApplyDet.getOrderType())){
								busi_flag="G";
								contract_no=moneyApplyDet.getContrctNo();
							}
							String prod_name=moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount();
							String prod_num="0";
							String apply_login=pushBossUser.getBossUserName();
							String apply_note=my.getApplyMemo()==null?"无":my.getApplyMemo();
							String bill_note="-";
							String bank_account=moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber();
							String bank_account_name=moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName();

							String purpose=moneyTotal.getMemo()==null?"无":moneyTotal.getMemo();
							String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
							String pay_type = "0";
							String busi_type="";
							String delay_rate="0";
							String pre_invoice_accept="";
							String route_key = "";
							String userIdNo = "";
							String customerNumber = "";
							if("1".equals(moneyApplyDet.getUseType()) || "01".equals(moneyApplyDet.getUseType())){ //缴费
								if ("01".equals(moneyApplyDet.getUseType())){
									route_key = "10";
								}
								busi_type="00";
								delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
							}else if("2".equals(moneyApplyDet.getUseType())){ //存送
								busi_type="02";
								if("2".equals(moneyApplyDet.getOrderType())){       //集团存送时需推送一个办理号码
									phone_no=moneyApplyDet.getInvNo();
								}
							}else if("3".equals(moneyApplyDet.getUseType())){ //终端
								busi_type="01";
							}else if(moneyApplyDet.getUseType().contains("05")){ //预开票
								busi_type="05";
								//专线预开票
								if ("05A".equals(moneyApplyDet.getUseType())){
									pay_type = "22";
									phone_no = moneyApplyDet.getSpecialLineNo();
									userIdNo = moneyApplyDet.getUserIdNo();
								}
								delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
								pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
							}else if("06".equals(moneyApplyDet.getUseType())){ //有价卡
								busi_type="06";
								pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
							}else if("07".equals(moneyApplyDet.getUseType())){ //物联网
								busi_type="07";
							}else if("08".equals(moneyApplyDet.getUseType())){ //ICT设备销售
								busi_type="08";
								delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
								if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
									pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
								}
							}else if("09".equals(moneyApplyDet.getUseType())){ //ICT终端销售
								busi_type="09";
								delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
								if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
									pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
								}
							}else if("10".equals(moneyApplyDet.getUseType())){ //ICT软件销售
								busi_type="10";
								delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
								if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
									pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
								}
							}else if ("11".equals(moneyApplyDet.getUseType())){
								busi_type="11";
								pre_invoice_accept = moneyApplyDet.getInvNo();
								userIdNo = moneyApplyDet.getUserIdNo();
								delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
							}else if (moneyApplyDet.getUseType().contains("12")){
								busi_type="12";
								customerNumber = moneyApplyDet.getCustomerNumber();
							}else {
								busi_type = moneyApplyDet.getUseType();
							}
							String out_back_accept=moneyApplyDet.getMoneyNo();
							String ctrlFlag = moneyApplyDet.getCtrlFlag();
							String beginCycle = moneyApplyDet.getBeginCycle();

							if (whetherToPush){
								Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(login_no, unit_id, op_type, out_sys_accept, contract_no,
										busi_flag, prod_name, prod_num, apply_login, apply_note, bill_note,
										bank_account, bank_account_name, phone_no, purpose, busi_fee, busi_type,
										out_back_accept, delay_rate, pre_invoice_accept,route_key,pay_type,userIdNo,customerNumber,ctrlFlag);
								logger.info(moneyApplyDet.getMoneyNo()+"推送资金使用明细结果："+applyRes.toString());
								if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
									JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
									JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
									//循环推送申请工单中的明细记录，成功并记录成功和失败数据
									if("0".equals(ROOT.getString("RETURN_CODE"))){
										if ("07".equals(moneyApplyDet.getUseType()) && moneyApplyDet.getBossNo()!=null){
											zcUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
											if (zcUser!=null){
												flag = true;
											}
										}
										moneyApplyDet.setBossState("0");
										moneyApplyDet.setState("1");
										moneyApplyDet.setPushDate(new Date());
										JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
										if (OUT_DATA.has("PAYMENT_ACCEPT")){
											if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
												moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
												InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
												invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
												invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
												if (moneyApplyDet.getUnitTaxplayer()!=null && moneyApplyDet.getUnitTaxplayer().length()>0){
													try {
														JSONObject UnitTaxplayer = JSONObject.fromObject(moneyApplyDet.getUnitTaxplayer());
														invoiceMiddle.setMsgRecvPhone(UnitTaxplayer.getString("RECEIVENUMBER"));
														invoiceMiddle.setTaxpayerId(UnitTaxplayer.getString("TAXPAYER_ID"));
														invoiceMiddle.setBankName(UnitTaxplayer.getString("BANK_NAME"));
														invoiceMiddle.setBankAccount(UnitTaxplayer.getString("BANK_ACCOUNT"));
														invoiceMiddle.setAddress(UnitTaxplayer.getString("ADDRESS"));
														invoiceMiddle.setPhoneNo(UnitTaxplayer.getString("PHONE_NO"));
														invoiceMiddle.setPrintType("1");
													}catch (Exception e){
														invoiceMiddle.setPrintType("3");
														invoiceMiddle.setBossMsg("数据解析异常："+e.getMessage());
													}
												}else {
													invoiceMiddle.setPrintType("0");
												}
												claimForFundsService.addInvoiceMiddle(invoiceMiddle);
											}
										}
									}else {
										moneyApplyDet.setBossState("1");
										moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG"));
										++countSubBoss;
										str.append("推送资金使用明细异常【").append(ROOT.getString("RETURN_MSG")).append("】、");
										if (moneyApplyDet.getUseType().contains("05") && beginCycle!=null && beginCycle.length()>0){
											whetherToPush = false;
										}
									}
									claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
								}else{
									++countSubBoss;
									moneyApplyDet.setBossState("-1");
									//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
									moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
									claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
									str.append("资金明细推送异常【").append(applyRes.getMessage().length() < 200 ? applyRes.getMessage() : applyRes.getMessage().substring(0, 200)).append("】、");
									if (moneyApplyDet.getUseType().contains("05") && beginCycle!=null && beginCycle.length()>0){
										whetherToPush = false;
									}
								}
							}else {
								++countSubBoss;
								moneyApplyDet.setBossState("1");
								moneyApplyDet.setBossMsg("该账期前还有其他账期未推送");
								claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
							}

						}else if ("07".equals(moneyApplyDet.getUseType()) && moneyApplyDet.getBossNo()!=null){
							zcUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
							if (zcUser!=null){
								flag = true;
							}
						}
					}
				}
				if(countSubBoss==0){
					my.setState("0");
					claimForFundsService.updateMoneyApply(my);
					taskService.updateBpms_riskoff_task("工单数据推送成功,已完结!", 2, btask.getId());
					service.updateWait(wait, this.getRequest());
					if (flag && "SH".equals(btask.getType())){
						String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CS", "物联网支撑", zcUser.getRowNo(), user);
						commitBackLog(my,zcUser.getRowNo(), process.getProcess_sign(), user, rtaskid);// 生成待办
					}
					Write(returnPars(1,"","亲爱的同事,工单数据推送成功,已完结!"));
				}else {
					Write(returnPars(-1,"","亲爱的同事,"+str));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事,资金推送资金入账结果异常,请联系管理员处理!"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("资金认领再次推送异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,资金认领再次推送异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 认领待办生成
	 * <AUTHOR>
	 * @param moneyApply 	工单对象
	 * @param userid		下一步处理人
	 * @param processId		流程编号
	 * @param user			当前登录人
	 * @param taskid		待办编号
	 * @Date 2022/6/13 10:25
	 **/
	public void commitBackLogData(MoneyApply moneyApply, Integer userid, String processId, SystemUser user, String taskid) {
		WaitTask waitTask = new WaitTask();
		waitTask.setName("[资金认领]" + moneyApply.getTitle());//待办名称
		waitTask.setCreationTime(new Date());// 代办生成时间
		waitTask.setUrl("jsp/claimForFunds/handleClaimData.jsp?id=" + moneyApply.getId());
		SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
		waitTask.setState(WaitTask.HANDLE);// 状态为待处理
		waitTask.setHandleUserId(USER.getRowNo());// 处理人id
		waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
		waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
		waitTask.setCreateUserId(user.getRowNo());// 创建人id
		waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
		waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
		waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
		waitTask.setTaskId(moneyApply.getId());
		service.saveWait(waitTask, this.getRequest());
	}

	/**---------------------------------------------(资金使用流程)--------------------------------------------------------------
	 * @Description TODO 用于发起资金认领的使用审批流程(添加使用工单)
	 * <AUTHOR>
	 * @Date 2022/6/9 17:22
	 **/
	public void addClaimForFunds(){
		try {
			String id = getString("id");//资金池ID
			String title = getString("title");//标题
			String outMoney = getString("outMoney");//申请金额
			String explain = getString("explain");//申请说明
			String groupName = getString("groupName");//集团名称
			String group = getString("group");//集团编码
			String jsonString = getString("jsonString");//账户信息json
			String lateFeeMoney = getString("lateFeeMoney");//滞纳金金额

			String attachmentId = getString("attachmentId");//附件id

			String role = getString("role");//角色权限
			Integer userId = getInteger("userId");//下一步处理用户id

			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyTotal moneyTotal=claimForFundsService.getMoneyTotal(id);
			claimForFundsService.getMoneyTotalSerialNoAndSubGroup(moneyTotal.getSerialNo(),0);
			if (moneyTotal==null){
				Write(returnPars(-1,"","亲爱的同事,资金编号["+id+"]查询异常,未查询到对应资金信息,请联系管理员处理!"));
				return;
			} else if(moneyTotal.getState()!=1){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]状态异常(当前状态为:"+moneyTotal.getState()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			}else if (moneyTotal.getUserid()!=user.getRowNo()){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]信息异常(当前归属用户为:"+moneyTotal.getUserid()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			}else if (!moneyTotal.getGroupCode().equals(group)){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]信息异常(当前归属集团为:"+moneyTotal.getGroupCode()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			} else if(Long.parseLong(moneyTotal.getOverAmount())<Long.parseLong(outMoney)){
				Write(returnPars(-1,"","亲爱的同事,当前资金为:"+Long.parseLong(moneyTotal.getOverAmount())/100+" 已不足以申请"));
				return;
			}

			Map<String, Object> resMap = claimForFundsService.setQuerymoneyTotalIdKey(moneyTotal.getId());
			if ("-1".equals(String.valueOf(resMap.get("code")))){
				Write(JSONHelper.SerializeWithNeedAnnotation(resMap));
				return;
			}

			moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) - Long.parseLong(outMoney)));
			moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) + Long.parseLong(outMoney)));
			claimForFundsService.updateMoneyTotal(moneyTotal);

			MoneyApply myly = new MoneyApply();
			String IBM = "";
			List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
			for (int i = 0; i < sone.size(); i++) {
				IBM = (String) sone.get(i)[2];
			}
			String sateTime = taskService.getNumber();
			String batchNO = IBM + "" + sateTime;
			myly.setApplyNo(batchNO);
			myly.setTitle(title);
			myly.setApplyMemo(explain);
			myly.setApplyAmount(outMoney);
			myly.setCreatorId(String.valueOf(user.getRowNo()));
			myly.setCreatorName(user.getEmployeeName());
			myly.setCreateDate(new Date());
			myly.setState("1");
			myly.setGroupCode(group);
			myly.setGroupName(groupName);
			myly.setOpType("1");
			myly.setMa_type("1");
			myly.setSerialNo(moneyTotal.getSerialNo());
			myly.setMoneyTotal_id(moneyTotal.getId());
			myly.setLateFeeMoney(lateFeeMoney);
			MoneyApply my= claimForFundsService.addMoneyApply(myly);

			if (jsonString != null && jsonString.length() > 0) {
				JSONArray jsonArray = JSONArray.fromObject(jsonString);
				for (int i = 0; i < jsonArray.size(); i++) {
					String s = jsonArray.getString(i);
					JSONObject data2 = JSONObject.fromObject(s);
					//由于义务限制,单笔金额超过99999的会被拆分
					Integer amout = Integer.parseInt(data2.getString("amount"));
					String PhoneNo = "";
					Date CreateTime = getStringDateFour(getStringDatetwo(new Date()));
					int o = 0;
					do {
						MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
						String moneyNo = ("2".equals(data2.getString("orderType"))?"JT":"GR") + sateTime + i;
						if ("".equals(PhoneNo)){
							PhoneNo = sateTime + i;
						}

						//工单关联信息
						moneyApplyDet.setMoneyNo(moneyNo+o);
						moneyApplyDet.setApplyNo(my.getApplyNo());
						moneyApplyDet.setSerialNo(moneyTotal.getSerialNo());
						//集团信息
						moneyApplyDet.setGroupCode(group);
						moneyApplyDet.setGroupName(groupName);
						//创建人信息
						moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
						moneyApplyDet.setCreateDate(CreateTime);
						//工单信息
						moneyApplyDet.setOpType("1");		//审批明细(1.审批明细/2.免审明细)
						moneyApplyDet.setOrderType(data2.getString("orderType"));	//工单类型(1.个人业务/2.集团业务)
						moneyApplyDet.setState("-1");		//未完成：审批中状态

						moneyApplyDet.setContrctNo(data2.getString("contrctNo"));
						moneyApplyDet.setContrctType(data2.getString("contrctType"));
						moneyApplyDet.setUseType(data2.getString("useType"));
						moneyApplyDet.setInvNo(data2.getString("invNo"));
						moneyApplyDet.setSpecialLineNo(data2.getString("specialLineNo"));
						moneyApplyDet.setUserIdNo(data2.getString("userIdNo"));
						if ("1".equals(data2.getString("useType")) || "08".equals(data2.getString("useType")) || "09".equals(data2.getString("useType"))
								|| "10".equals(data2.getString("useType")) ||data2.getString("useType").contains("05")){       //预开票和缴费单笔最大金额为99999
							if (amout>9999900){
								moneyApplyDet.setAmount("9999900");
								amout-= 9999900;
							}else {
								moneyApplyDet.setAmount(String.valueOf(amout));
								amout = 0;
							}
						}else if ("06".equals(data2.getString("useType"))){                                             //有价卡单笔最大金额为50000
							if (amout>2500000){
								moneyApplyDet.setAmount("2500000");
								amout-= 2500000;
							}else {
								moneyApplyDet.setAmount(String.valueOf(amout));
								amout = 0;
							}
						}else {
							moneyApplyDet.setAmount(String.valueOf(amout));
							amout = 0;
						}

						//集团缴费时可能存在纳税人信息
						if ("2".equals(data2.getString("orderType")) && "1".equals(data2.getString("useType")) && data2.has("fomInvoiceType") && data2.getBoolean("fomInvoiceType")){
							moneyApplyDet.setUnitTaxplayer(data2.getString("unitTaxPlayerJson"));
						}

						if (("1".equals(data2.getString("orderType")) && "01".equals(data2.getString("useType"))) || "07".equals(data2.getString("useType"))){
							moneyApplyDet.setBossNo(data2.getString("AgenPayment"));
						}

						if (data2.getString("useType").contains("12") && !"".equals(data2.getString("customerNumber"))){
							moneyApplyDet.setCustomerNumber(data2.getString("customerNumber"));
						}

						if (data2.getString("useType").contains("05")){
							moneyApplyDet.setBeginCycle(data2.getString("beginCycle"));
						}

						if ("1".equals(data2.getString("orderType")) && ("1".equals(data2.getString("useType")) || "01".equals(data2.getString("useType")))){
							moneyApplyDet.setCtrlFlag(("true".equals(data2.getString("ctrlFlag"))?"0":"1"));
						}

						moneyApplyDet.setLateFee(data2.getString("lateFee"));
						moneyApplyDet.setLateFeeMoney(data2.getString("lateFeeMoney"));

						moneyApplyDet.setPhoneNo(PhoneNo);

						//个人账户对是否B库成员进行校验
						if (data2.getString("orderType").equals("1")){
							List<Map<String,String>> list = claimForFundsService.queryGroupAssociation(group,data2.getString("contrctNo"));
							if (list.size()<=0){
								moneyApplyDet.setBillNote("1");
							}else {
								moneyApplyDet.setBillNote("");
							}
						}else {
							moneyApplyDet.setBillNote("");
						}

						moneyApplyDet.setProductName("");
						moneyApplyDet.setProductNmb("");
						moneyApplyDet.setContrctName("");

						claimForFundsService.addMoneyApplyDet(moneyApplyDet);

						if ("2".equals(data2.getString("useType")) && !"".equals(data2.getString("quotaWorkHandleCode"))){
							if (!updaetQuotaWorkHandleByCode(data2.getString("quotaWorkHandleCode"),moneyApplyDet.getMoneyNo(),"2")){
								throw new Exception("营销活动修改失败！");
							}
						}

						if ("06".equals(data2.getString("useType")) && !"".equals(data2.getString("invNo"))){
							if (!updateValuableCard(data2.getString("invNo"),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"SAVA")){
								throw new Exception("有价卡修改失败！");
							}
						}

						if ("12B".equals(data2.getString("useType")) && !"".equals(data2.getString("invNo"))){
							if (!updateInternetOfThingsDet(data2.getString("invNo"),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"SAVA")){
								throw new Exception("物联网预开票修改失败！");
							}
						}

						o+=1;
					}while (amout>0);
				}
			}

			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] json = attachmentId.split(",");
					if (json.length > 0) {
						for (int i = 0; i < json.length; i++) {
							SingleAndAttachment sa = new SingleAndAttachment();
							sa.setOrderID(my.getId());
							sa.setAttachmentId(json[i]);
							sa.setLink(MoneyTotal.MONEYTOTAL);
							claimForFundsService.saveSandA(sa);
						}
					}
				}
			}

			// 流程启动
			String node="";
			if("SZK".equals(role)){
				node="ROLE_SZKKHJL";
			}else if("SGS".equals(role)){
				node="ROLE_SGSKHJL";
			}else{
				node="ROLE_QXDM";
			}
			Map<String, String> map = new HashMap<>();
			map.put("node", node);
			String processId = transferJBPMUtils.startTransfer("ClaimForFundUsePrs", map);// 流程启动
			if(processId !=null){
				Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
				taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
				taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
				String taskid= taskService.setBpms_riskoff_task(processId,null, 1, "SH", task.getActivityName(),userId, user);//预存下一步任务
				JSONObject obj=taskService.getApproval_message(my.getApplyNo(), BigDecimal.
								valueOf(Long.valueOf(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
						task.getActivityName(), "",userId, user);
				taskService.setReminder_information_tbl(my.getGroupCode(),"1","30", "资金认领", obj.toString(), "资金认领");
				commitBackLog(my, userId, processId, user, taskid);//生成待办
				claimForFundsService.delQuerymoneyTotalIdKey(moneyTotal.getId());
				Write(returnPars(1,"","亲爱的同事,工单新建成功,请等待审批结果!"));
			}else {
				claimForFundsService.delQuerymoneyTotalIdKey(moneyTotal.getId());
				Write(returnPars(-1,"","亲爱的同事,流程实例启动异常,请联系管理员处理!"));
			}
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			logger.error("新建资金使用工单异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,新建资金使用工单异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金使用流程  流程进行(流转)方法
	 * <AUTHOR>
	 * @Date 2022/6/10 10:23
	 **/
	public void handleClaimForFunds(){
		try {
			String id = getString("id");//资金认领id
			Integer userid = getInteger("userId");//下一步处理用户id
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
			int lateFee=0;
			boolean ifOffSite = false;
			String AgenPayment = "";
			SystemUser agenUser = null;
			for(int i=0;i<moneyApplyDet.size();i++){
				//是否存在异地缴费
				if ("01".equals(moneyApplyDet.get(i).getUseType()) && !ifOffSite){
					ifOffSite = true;
					if ("".equals(AgenPayment)){
						AgenPayment = moneyApplyDet.get(i).getBossNo();
					}
				}

				if("1.00".equals(moneyApplyDet.get(i).getLateFee())){
					lateFee=1;
				}

				if (lateFee==1 && ifOffSite && !"".equals(AgenPayment)){
					break;
				}
			}

			//获取异地缴费代理人信息
			if (ifOffSite){
				if("".equals(AgenPayment)){
					Write(returnPars(-1,"","亲爱的同事,异地缴费代理缴费人员信息异常,联系系统管理员"));
					throw new RuntimeException("异地缴费代理缴费人员信息异常");
				}else {
					agenUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(AgenPayment));
					if (agenUser==null){
						Write(returnPars(-1,"","亲爱的同事,异地缴费代理缴费人员信息异常,联系系统管理员"));
						throw new RuntimeException("异地缴费代理缴费人员信息异常");
					}else {
						userid = agenUser.getRowNo();
					}
				}
			}
			GroupCustomer groupCustomer = claimForFundsService.queryGroupCustomerById(moneyTotal.getGroupCode());
			String code =claimForFundsService.getVwUserinf(my.getCreatorId()).get(0).get("COMPANY_CODE");
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
			TransferCitiesData transferCitiesData= claimForFundsService.getTransferCitiesData(code,task.getActivityName());
			if (transferCitiesData == null) {
				transferCitiesData = new TransferCitiesData();
				transferCitiesData.setAmount("0");
			}
			LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,task.getActivityName());
			if (lateFeeMoneyData == null) {
				lateFeeMoneyData = new LateFeeMoneyData();
				lateFeeMoneyData.setAmount("0");
			}
			Map<String, String> map = new HashMap<>();
			if ("区县政企部主任".equals(task.getActivityName())) {
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney()) / 100 <= Double.parseDouble(lateFeeMoneyData.getAmount()) ) {
						if (ifOffSite){
							map.put("node", "异地缴费转代理人员");
						}else {
							map.put("node", "END");
						}
					}else{
						map.put("node", "符合继续审批要求");
					}
				}else {
					if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney()) / 100 <= Double.parseDouble(lateFeeMoneyData.getAmount()) ) {
						map.put("node", "END");
					}else{
						map.put("node", "金额大于等于10000");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);//流程流转
			}else if("区县分管经理".equals(task.getActivityName())){
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if(Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney()) / 100 <= Double.parseDouble(lateFeeMoneyData.getAmount())){
						if (ifOffSite){
							map.put("node", "异地缴费转代理人员");
						}else {
							map.put("node", "END");
						}
					}else{
						map.put("node", "符合继续审批要求");
					}
				}else {
					if(lateFee==0 || queryGroupLevel(groupCustomer.getGroupLevel()) ){
						map.put("node", "END");
					}else{
						map.put("node", "减免滞纳金");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if ("市公司客户经理室经理".equals(task.getActivityName())) {
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney()) / 100 <= Double.parseDouble(lateFeeMoneyData.getAmount())) {
						if (ifOffSite){
							map.put("node", "异地缴费转代理人员");
						}else {
							map.put("node", "END");
						}
					}else{
						if(lateFee==0){
							map.put("node", "符合继续审批要求_1");
						}else{
							map.put("node", "符合继续审批要求_2");
						}
					}
				}else {
					if (Double.parseDouble(my.getApplyAmount()) / 100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney())/100<=Double.parseDouble(lateFeeMoneyData.getAmount())) {
						map.put("node", "END");
					}else{
						if((Double.parseDouble(my.getLateFeeMoney())/100)<=Double.parseDouble(lateFeeMoneyData.getAmount()) ||
								queryGroupLevel(groupCustomer.getGroupLevel())){
							map.put("node", "金额和滞纳金大于设定金额并且不减免滞纳金");
						}else{
							map.put("node", "金额和滞纳金大于设定金额并且减免滞纳金");
						}
					}
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if("市公司政企部经理".equals(task.getActivityName())){
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if(Double.parseDouble(my.getLateFeeMoney())/100>=Double.parseDouble(lateFeeMoneyData.getAmount())){
						map.put("node", "符合继续审批要求");
					}else{
						if (ifOffSite){
							map.put("node", "异地缴费转代理人员");
						}else {
							map.put("node", "END");
						}
					}
				}else {
					if(Double.parseDouble(my.getLateFeeMoney())/100>=Double.parseDouble(lateFeeMoneyData.getAmount())){
						map.put("node", "滞纳金大于设定金额");
					}else{
						map.put("node", "END");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if ("省重客客户经理室经理".equals(task.getActivityName())) {
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if (Double.parseDouble(my.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney())/100<=Double.parseDouble(lateFeeMoneyData.getAmount())){
						if (ifOffSite){
							map.put("node", "异地缴费转代理人员");
						}else {
							map.put("node", "END");
						}
					}else{
						if(lateFee==0){
							map.put("node", "符合继续审批要求_1");
						}else{
							map.put("node", "符合继续审批要求_2");
						}
					}
				}else {
					if (Double.parseDouble(my.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) &&
							Double.parseDouble(my.getLateFeeMoney())/100<=Double.parseDouble(lateFeeMoneyData.getAmount())){
						map.put("node", "END");
					}else{
						if((Double.parseDouble(my.getLateFeeMoney())/100)<=Double.parseDouble(lateFeeMoneyData.getAmount()) ||
								queryGroupLevel(groupCustomer.getGroupLevel())){
							map.put("node", "金额和滞纳金大于设定金额并且不减免滞纳金");
						}else{
							map.put("node", "金额和滞纳金大于设定金额并且减免滞纳金");
						}
					}
				}
				jbpmUtil.completeTask(task.getId(), map);
			} else if("省重客分管经理".equals(task.getActivityName()) || "市公司领导".equals(task.getActivityName())){
				if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
					if (ifOffSite){
						map.put("node", "异地缴费转代理人员");
					}else {
						map.put("node", "END");
					}
				}else {
					map.put("node", "END");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else{
				jbpmUtil.completeTask(task.getId());
			}
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			service.updateWait(wt, this.getRequest());
			String rtaskid =taskService.setBpms_riskoff_task(process.getProcess_sign(),"",1,"SH",tasktwo.getActivityName(),userid, user);
			JSONObject obj=taskService.getApproval_message(my.getApplyNo(),BigDecimal.
							valueOf(Long.valueOf(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
					task.getActivityName(), opinion, userid, user);
			taskService.setReminder_information_tbl(my.getGroupCode(),"1","30", "资金认领", obj.toString(), "资金认领");
			commitBackLog(my,userid, process.getProcess_sign(), user, rtaskid);// 生成待办
			Write(returnPars(1,"","亲爱的同事,工单提交已成功!"));
		}catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金使用工单提交异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,资金使用工单提交异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金使用流程  流程完成方法
	 * <AUTHOR>
	 * @Date 2022/6/13 10:04
	 **/
	public void complatePreinvApply(){
		try {
			String id = getString("id");//开票id
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			List<MoneyApplyDet> moneyApplyDetList =claimForFundsService.getMoneyApplyDet(my.getApplyNo());
			MoneyTotal moneyTotal =claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
			if(tasktwo==null){
				Write(returnPars(-1,"","亲爱的同事,流程实例["+process.getProcess_sign()+"]异常,请联系系统管理员"));
				return;
			}

			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			//执行资金使用记录推送流程
			MoneyTotal moneyTotalTwo = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			int pushOkCount=0;
			if(moneyApplyDetList!=null&&moneyTotalTwo.getState()==1){
				Boolean whetherToPush = true;
				for (MoneyApplyDet moneyApplyDet : moneyApplyDetList) {
					String login_no;
					if (("01".equals(moneyApplyDet.getUseType()) || "07".equals(moneyApplyDet.getUseType())) && moneyApplyDet.getBossNo() != null) {
						SystemUser BossUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
						if (BossUser != null && BossUser.getBossUserName() != null) {
							login_no = BossUser.getBossUserName();
						} else {
							pushOkCount++;
							moneyApplyDet.setBossState("1");
							moneyApplyDet.setBossMsg("代理缴费用户信息异常，不能进行缴费，请核实！");
							claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
							continue;
						}
					} else {
						login_no = pushBossUser.getBossUserName();
					}
					String unit_id = moneyApplyDet.getGroupCode();
					String op_type = "03"; //使用申请
					String out_sys_accept = moneyApplyDet.getMoneyNo();
					String busi_flag = "";
					String contract_no = "";
					String phone_no = "";
					if ("1".equals(moneyApplyDet.getOrderType())) {
						busi_flag = "P";
						phone_no = moneyApplyDet.getContrctNo();
					} else if ("2".equals(moneyApplyDet.getOrderType())) {
						busi_flag = "G";
						contract_no = moneyApplyDet.getContrctNo();
					}
					String prod_name = moneyApplyDet.getMoneyNo() + "-" + moneyApplyDet.getAmount();
					String prod_num = "0";
					String apply_login = pushBossUser.getBossUserName();
					String apply_note = my.getApplyMemo() == null ? "无" : my.getApplyMemo();
					String bill_note = "-";
					String bank_account = moneyTotal.getOtherAccNumber() == null ? "" : moneyTotal.getOtherAccNumber();
					String bank_account_name = moneyTotal.getOtherName() == null ? "" : moneyTotal.getOtherName();

					String purpose = moneyTotal.getMemo() == null ? "无" : moneyTotal.getMemo();
					String busi_fee = BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
					String pay_type = "0";
					String busi_type = "";
					String delay_rate = "0";
					String pre_invoice_accept = "";
					String route_key = "";
					String userIdNo = "";
					String customerNumber = "";
					if ("1".equals(moneyApplyDet.getUseType()) || "01".equals(moneyApplyDet.getUseType())) { //缴费
						if ("01".equals(moneyApplyDet.getUseType())) {
							route_key = "10";
						}
						busi_type = "00";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
					} else if ("2".equals(moneyApplyDet.getUseType())) { //存送
						busi_type = "02";
						if ("2".equals(moneyApplyDet.getOrderType())) {       //集团存送时需推送一个办理号码
							phone_no = moneyApplyDet.getInvNo();
						}
					} else if ("3".equals(moneyApplyDet.getUseType())) { //终端
						busi_type = "01";
					} else if (moneyApplyDet.getUseType().contains("05")) { //预开票
						busi_type = "05";
						//专线预开票
						if ("05A".equals(moneyApplyDet.getUseType())) {
							pay_type = "22";
							phone_no = moneyApplyDet.getSpecialLineNo();
							userIdNo = moneyApplyDet.getUserIdNo();
						}
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
					} else if ("06".equals(moneyApplyDet.getUseType())) { //有价卡
						busi_type = "06";
						pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
					} else if ("07".equals(moneyApplyDet.getUseType())) { //物联网
						busi_type = "07";
					} else if ("08".equals(moneyApplyDet.getUseType())) { //ICT设备销售
						busi_type = "08";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo() != null && moneyApplyDet.getInvNo().length() > 0) {
							pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
						}
					} else if ("09".equals(moneyApplyDet.getUseType())) { //ICT终端销售
						busi_type = "09";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo() != null && moneyApplyDet.getInvNo().length() > 0) {
							pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
						}
					} else if ("10".equals(moneyApplyDet.getUseType())) { //ICT软件销售
						busi_type = "10";
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo() != null && moneyApplyDet.getInvNo().length() > 0) {
							pre_invoice_accept = moneyApplyDet.getInvNo().split("_")[0];
						}
					} else if ("11".equals(moneyApplyDet.getUseType())) {
						busi_type = "11";
						pre_invoice_accept = moneyApplyDet.getInvNo();
						userIdNo = moneyApplyDet.getUserIdNo();
						delay_rate = moneyApplyDet.getLateFee(); //减免滞纳金
					} else if (moneyApplyDet.getUseType().contains("12")) {
						busi_type = "12";
						customerNumber = moneyApplyDet.getCustomerNumber();
					} else {
						busi_type = moneyApplyDet.getUseType();
					}
					String out_back_accept = moneyApplyDet.getMoneyNo();
					String ctrlFlag = moneyApplyDet.getCtrlFlag();
					String beginCycle = moneyApplyDet.getBeginCycle();
					if (whetherToPush) {
						Result applyRes = ClaimFundsOpenSrv.getInstance().applyForFunds(login_no, unit_id, op_type, out_sys_accept, contract_no,
								busi_flag, prod_name, prod_num, apply_login, apply_note, bill_note,
								bank_account, bank_account_name, phone_no, purpose, busi_fee, busi_type,
								out_back_accept, delay_rate, pre_invoice_accept, route_key, pay_type, userIdNo, customerNumber, ctrlFlag);
						logger.info(moneyApplyDet.getMoneyNo() + "推送资金使用明细结果：" + applyRes.toString());
						if (ResultCode.SUCCESS.code() == applyRes.getCode()) {  //判断当前请求是否成功
							JSONObject applyObj = JSONObject.fromObject(applyRes.getData());
							JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
							//循环推送申请工单中的明细记录，成功并记录成功和失败数据
							if ("0".equals(ROOT.getString("RETURN_CODE"))) {
								moneyApplyDet.setBossState("0");
								moneyApplyDet.setState("1");
								moneyApplyDet.setPushDate(new Date());
								JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
								if (OUT_DATA.has("PAYMENT_ACCEPT")) {
									if (OUT_DATA.getString("PAYMENT_ACCEPT").length() > 0) {
										moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
										InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
										invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
										invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
										if (moneyApplyDet.getUnitTaxplayer() != null && moneyApplyDet.getUnitTaxplayer().length() > 0) {
											try {
												JSONObject UnitTaxplayer = JSONObject.fromObject(moneyApplyDet.getUnitTaxplayer());
												invoiceMiddle.setMsgRecvPhone(UnitTaxplayer.getString("RECEIVENUMBER"));
												invoiceMiddle.setTaxpayerId(UnitTaxplayer.getString("TAXPAYER_ID"));
												invoiceMiddle.setBankName(UnitTaxplayer.getString("BANK_NAME"));
												invoiceMiddle.setBankAccount(UnitTaxplayer.getString("BANK_ACCOUNT"));
												invoiceMiddle.setAddress(UnitTaxplayer.getString("ADDRESS"));
												invoiceMiddle.setPhoneNo(UnitTaxplayer.getString("PHONE_NO"));
												invoiceMiddle.setPrintType("1");
											} catch (Exception e) {
												invoiceMiddle.setPrintType("3");
												invoiceMiddle.setBossMsg("数据解析异常：" + e.getMessage());
											}
										} else {
											invoiceMiddle.setPrintType("0");
										}
										claimForFundsService.addInvoiceMiddle(invoiceMiddle);
									}
								}
							} else {
								pushOkCount += 1;
								moneyApplyDet.setBossState("1");
								moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG"));
								if (moneyApplyDet.getUseType().contains("05") && beginCycle != null && beginCycle.length() > 0) {
									whetherToPush = false;
								}
							}
							claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
						} else {
							moneyApplyDet.setBossState("-1");
							//判断BOSS反馈信息，如果大于数据库字段长度则截取存储
							moneyApplyDet.setBossMsg(applyRes.getMessage().length() < 200 ? applyRes.getMessage() : applyRes.getMessage().substring(0, 200));
							claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
							if (moneyApplyDet.getUseType().contains("05") && beginCycle != null && beginCycle.length() > 0) {
								whetherToPush = false;
							}
						}
					} else {
						pushOkCount += 1;
						moneyApplyDet.setBossState("1");
						moneyApplyDet.setBossMsg("该账期前还有其他账期未推送");
						claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
					}
				}
			}

			Map<String, String> map = new HashMap<>();
			if(tasktwo.getActivityName().contains("代理缴费")){
				jbpmUtil.completeTask(tasktwo.getId(), "END");
			}else {
				map.put("node", "END");
				jbpmUtil.completeTask(tasktwo.getId(), map);
			}

			if(pushOkCount>0){
				my.setState("3");
			}else{
				my.setState("0");
			}
			claimForFundsService.updateMoneyApply(my);
			JSONObject obj=taskService.getCompleted_message(my.getApplyNo(),BigDecimal.
							valueOf(Long.valueOf(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
					tasktwo.getActivityName(), opinion, user.getRowNo());
			taskService.setReminder_information_tbl(my.getGroupCode(),"1","30", "资金认领", obj.toString(), "资金认领");
			String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(),"",1,"SH","起草人",Integer.valueOf(my.getCreatorId()), user);
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			service.updateWait(wt, this.getRequest());
			commitBackLog(my,Integer.parseInt(my.getCreatorId()), process.getProcess_sign(), user, rtaskid);// 生成待办
			Write(returnPars(1,"","亲爱的同事,操作成功,工单已完成!"));
		} catch (Exception e) {
			logger.error("资金使用工单完成异常：" + e.getMessage(), e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,资金使用工单完成异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金使用流程  流程退回方法
	 * <AUTHOR>  
	 * @Date 2022/6/13 10:05 
	 **/
	public void returnClaimForFunds(){
		try {
			String id = getString("id");// 开票id
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			my.setState("2");//修改状态为退回
			MoneyApply may= claimForFundsService.updateMoneyApply(my);
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			service.updateWait(wt, this.getRequest());
			jbpmUtil.deleteProcessInstance(process.getProcess_sign());// 删除流程
			String rtaskid =taskService.setBpms_riskoff_task(process.getProcess_sign(),"",1,"SH","客户经理",Integer.valueOf(my.getCreatorId()), user);
			commitBackLog(my,Integer.parseInt(my.getCreatorId()), process.getProcess_sign(), user, rtaskid);// 生成待办
			Write(returnPars(1,"","亲爱的同事,工单已退回至:"+may.getCreatorName()+"!"));
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("使用工单退回异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,使用工单退回异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金使用流程  工单作废方法
	 * <AUTHOR>
	 * @Date 2022/6/13 10:16
	 **/
	public void InvalidMoneyApply() {
		try {
			String id = getString("id");//开票id
			String userPhoneNum = getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my= claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal =claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			Bpms_riskoff_task riskoff_task=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!riskoff_task.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!riskoff_task.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+riskoff_task.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			List<MoneyApplyDet> moneyApplyDet =claimForFundsService.getMoneyApplyDet(my.getApplyNo());
			for(int i=0;i<moneyApplyDet.size();i++){
				moneyApplyDet.get(i).setState("4");
				claimForFundsService.updateMoneyApplyDet(moneyApplyDet.get(i));

				if ("2".equals(moneyApplyDet.get(i).getUseType()) && !updaetQuotaWorkHandleByMoneyNo(moneyApplyDet.get(i).getMoneyNo(),"0")){
					throw new Exception("修改营销活动失败！");
				}else if("06".equals(moneyApplyDet.get(i).getUseType()) && !updateValuableCard(moneyApplyDet.get(i).getInvNo(),moneyApplyDet.get(i).getContrctNo(),moneyApplyDet.get(i).getAmount(),"DELECT")){
					throw new Exception("修改有价卡失败！");
				}else if ("12B".equals(moneyApplyDet.get(i).getUseType()) && !updateInternetOfThingsDet(moneyApplyDet.get(i).getInvNo(),moneyApplyDet.get(i).getContrctNo(),moneyApplyDet.get(i).getAmount(),"DELECT")){
					throw new Exception("修改物联网预开票失败！");
				};
			}
			my.setState("-1");// 状态修改为作废
			MoneyApply may= claimForFundsService.updateMoneyApply(my);
			moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) - Long.parseLong(may.getApplyAmount())));
			moneyTotal.setOverAmount(String.valueOf(Long.parseLong(may.getApplyAmount()) + Long.parseLong(moneyTotal.getOverAmount())));
			claimForFundsService.updateMoneyTotal(moneyTotal);

			taskService.updateBpms_riskoff_task("工单已作废!", 2,riskoff_task.getId());
			service.updateWait(wt, this.getRequest());
			Write(returnPars(1,"","亲爱的同事,工单作废成功资金也退回,请核实资金金额!"));
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("使用工单作废异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,使用工单作废异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 使用工单推送失败(部分推送失败)时再次推送方法
	 * <AUTHOR>
	 * @Date 2022/6/13 10:40
	 **/
	public void pushAgainPreinvApply(){
		try {
			String id = getString("id");//开票id
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my = claimForFundsService.getMoneyApply(id);//申请工单信息
			List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());

			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			SystemUser zcUser = null;
			Boolean flag = false;
			int pushOkCount=0;
			Boolean whetherToPush = true;
			for (int l = 0; l < moneyApplyDetList.size(); l++) {
				MoneyApplyDet moneyApplyDet=moneyApplyDetList.get(l);
				if ("1".equals(moneyApplyDet.getBossState())){
					String login_no;
					if (("01".equals(moneyApplyDet.getUseType()) || "07".equals(moneyApplyDet.getUseType())) && moneyApplyDet.getBossNo()!=null){
						SystemUser BossUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
						if (BossUser!=null && BossUser.getBossUserName()!=null){
							login_no=BossUser.getBossUserName();
						}else {
							pushOkCount++;
							moneyApplyDet.setBossState("1");
							moneyApplyDet.setBossMsg("代理缴费用户信息异常，不能进行缴费，请核实！");
							claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
							continue;
						}
					}else {
						login_no=pushBossUser.getBossUserName();
					}
					String unit_id=moneyApplyDet.getGroupCode();
					String op_type="03"; //使用申请
					String out_sys_accept=moneyApplyDet.getMoneyNo();
					String busi_flag="";
					String contract_no="";
					String phone_no="";
					if("1".equals(moneyApplyDet.getOrderType())){
						busi_flag="P";
						phone_no=moneyApplyDet.getContrctNo();
					}else if("2".equals(moneyApplyDet.getOrderType())){
						busi_flag="G";
						contract_no=moneyApplyDet.getContrctNo();
					}
					String prod_name=moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount();
					String prod_num="0";
					String apply_login=pushBossUser.getBossUserName();
					String apply_note=my.getApplyMemo()==null?"无":my.getApplyMemo();
					String bill_note="-";
					String bank_account=moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber();
					String bank_account_name=moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName();

					String purpose=moneyTotal.getMemo()==null?"无":moneyTotal.getMemo();
					String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
					String pay_type = "0";
					String busi_type="";
					String delay_rate="0";
					String pre_invoice_accept="";
					String route_key = "";
					String userIdNo = "";
					String customerNumber = "";
					if("1".equals(moneyApplyDet.getUseType()) || "01".equals(moneyApplyDet.getUseType())){ //缴费
						if ("01".equals(moneyApplyDet.getUseType())){
							route_key = "10";
						}
						busi_type="00";
						delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
					}else if("2".equals(moneyApplyDet.getUseType())){ //存送
						busi_type="02";
						if("2".equals(moneyApplyDet.getOrderType())){       //集团存送时需推送一个办理号码
							phone_no=moneyApplyDet.getInvNo();
						}
					}else if("3".equals(moneyApplyDet.getUseType())){ //终端
						busi_type="01";
					}else if(moneyApplyDet.getUseType().contains("05")){ //预开票
						busi_type="05";
						//专线预开票
						if ("05A".equals(moneyApplyDet.getUseType())){
							pay_type = "22";
							phone_no = moneyApplyDet.getSpecialLineNo();
							userIdNo = moneyApplyDet.getUserIdNo();
						}
						delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
						pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
					}else if("06".equals(moneyApplyDet.getUseType())){ //有价卡
						busi_type="06";
						pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
					}else if("07".equals(moneyApplyDet.getUseType())){ //物联网
						busi_type="07";
					}else if("08".equals(moneyApplyDet.getUseType())){ //ICT设备销售
						busi_type="08";
						delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
							pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
						}
					}else if("09".equals(moneyApplyDet.getUseType())){ //ICT终端销售
						busi_type="09";
						delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
							pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
						}
					}else if("10".equals(moneyApplyDet.getUseType())){ //ICT软件销售
						busi_type="10";
						delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
						if (moneyApplyDet.getInvNo()!=null && moneyApplyDet.getInvNo().length()>0){
							pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
						}
					}else if ("11".equals(moneyApplyDet.getUseType())){
						busi_type="11";
						pre_invoice_accept = moneyApplyDet.getInvNo();
						userIdNo = moneyApplyDet.getUserIdNo();
						delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
					}else if (moneyApplyDet.getUseType().contains("12")){
						busi_type = "12";
						customerNumber = moneyApplyDet.getCustomerNumber();
					}else {
						busi_type = moneyApplyDet.getUseType();
					}
					String out_back_accept=moneyApplyDet.getMoneyNo();
					String ctrlFlag = moneyApplyDet.getCtrlFlag();
					String beginCycle = moneyApplyDet.getBeginCycle();
					if (whetherToPush){
						Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(login_no, unit_id, op_type, out_sys_accept, contract_no,
								busi_flag, prod_name, prod_num, apply_login, apply_note, bill_note,
								bank_account, bank_account_name, phone_no, purpose, busi_fee, busi_type,
								out_back_accept, delay_rate, pre_invoice_accept,route_key,pay_type,userIdNo,customerNumber,ctrlFlag);
						logger.info(moneyApplyDet.getMoneyNo()+"再次推送资金使用明细结果："+applyRes.toString());
						if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
							JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
							JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
							//循环推送申请工单中的明细记录，成功并记录成功和失败数据
							if("0".equals(ROOT.getString("RETURN_CODE"))){
								if ("07".equals(moneyApplyDet.getUseType()) && moneyApplyDet.getBossNo()!=null){
									zcUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
									if (zcUser!=null){
										flag = true;
									}
								}
								moneyApplyDet.setBossState("0");
								moneyApplyDet.setState("1");
								moneyApplyDet.setPushDate(new Date());
								JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
								if (OUT_DATA.has("PAYMENT_ACCEPT")){
									if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
										moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
										InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
										invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
										invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
										if (moneyApplyDet.getUnitTaxplayer()!=null && moneyApplyDet.getUnitTaxplayer().length()>0){
											try {
												JSONObject UnitTaxplayer = JSONObject.fromObject(moneyApplyDet.getUnitTaxplayer());
												invoiceMiddle.setMsgRecvPhone(UnitTaxplayer.getString("RECEIVENUMBER"));
												invoiceMiddle.setTaxpayerId(UnitTaxplayer.getString("TAXPAYER_ID"));
												invoiceMiddle.setBankName(UnitTaxplayer.getString("BANK_NAME"));
												invoiceMiddle.setBankAccount(UnitTaxplayer.getString("BANK_ACCOUNT"));
												invoiceMiddle.setAddress(UnitTaxplayer.getString("ADDRESS"));
												invoiceMiddle.setPhoneNo(UnitTaxplayer.getString("PHONE_NO"));
												invoiceMiddle.setPrintType("1");
											}catch (Exception e){
												invoiceMiddle.setPrintType("3");
												invoiceMiddle.setBossMsg("数据解析异常："+e.getMessage());
											}
										}else {
											invoiceMiddle.setPrintType("0");
										}
										claimForFundsService.addInvoiceMiddle(invoiceMiddle);
									}
								}
							}else {
								pushOkCount+=1;
								moneyApplyDet.setBossState("1");
								moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG"));
								if (moneyApplyDet.getUseType().contains("05") && beginCycle!=null && beginCycle.length()>0){
									whetherToPush = false;
								}
							}
							claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
						}else{
							pushOkCount+=1;
							moneyApplyDet.setBossState("-1");
							moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
							claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
							if (moneyApplyDet.getUseType().contains("05") && beginCycle!=null && beginCycle.length()>0){
								whetherToPush = false;
							}
						}
					}else {
						pushOkCount+=1;
						moneyApplyDet.setBossState("1");
						moneyApplyDet.setBossMsg("该账期前还有其他账期未推送");
						claimForFundsService.updateCommitMoneyApplyDet(moneyApplyDet);
					}

				}else if ("07".equals(moneyApplyDet.getUseType()) && moneyApplyDet.getBossNo()!=null){
					zcUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
					if (zcUser!=null){
						flag = true;
					}
				}
			}

			if (pushOkCount == 0) {
				my.setState("0");
				claimForFundsService.updateMoneyApply(my);
				taskService.updateBpms_riskoff_task("工单已完成!", 2, btask.getId());
				service.updateWait(wt, this.getRequest());
				if (flag && btask.getType().equals("SH")){
					String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CS", "物联网支撑", zcUser.getRowNo(), user);
					commitBackLog(my,zcUser.getRowNo(), process.getProcess_sign(), user, rtaskid);// 生成待办
				}
				Write(returnPars(1,"","亲爱的同事,资金信息推送成功,工单已完成!"));
			}else {
				Write(returnPars(-1,"","亲爱的同事,资金信息仍有"+pushOkCount+"条明细未推送成功,请确认!"));
			}
		} catch (Exception e) {
			logger.error("使用工单再次推送数据异常：" + e.getMessage(), e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,使用工单再次推送数据异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 用于使用工单 结束工单(工单存在失败明细退回失败金额)
	 * <AUTHOR>  
	 * @Date 2022/6/13 10:10 
	 **/
	public void endMoneyApply() {
		try {
			String id = getString("id");//开票id
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());

			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			Boolean flag = false;
			SystemUser zcUser = null;
			long amount = 0;
			for (int i = 0; i < moneyApplyDetList.size(); i++) {
				MoneyApplyDet moneyApplyDet=moneyApplyDetList.get(i);
				if ("1".equals(moneyApplyDet.getBossState())){
					amount += Long.parseLong(moneyApplyDet.getAmount());
					moneyApplyDet.setState("4");
					claimForFundsService.updateMoneyApplyDet(moneyApplyDet);
					if ("2".equals(moneyApplyDet.getUseType()) && !updaetQuotaWorkHandleByMoneyNo(moneyApplyDet.getMoneyNo(),"0")){
						throw new Exception("修改营销活动失败！");
					}else if("06".equals(moneyApplyDet.getUseType()) && !updateValuableCard(moneyApplyDet.getInvNo(),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"DELECT")){
						throw new Exception("修改有价卡失败！");
					}else if ("12B".equals(moneyApplyDet.getUseType()) && !updateInternetOfThingsDet(moneyApplyDet.getInvNo(),moneyApplyDet.getContrctNo(),moneyApplyDet.getAmount(),"DELECT")){
						throw new Exception("修改物联网预开票失败！");
					};
				}else if ("07".equals(moneyApplyDet.getUseType()) && moneyApplyDet.getBossNo()!=null){
					zcUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(moneyApplyDet.getBossNo()));
					if (zcUser!=null){
						flag = true;
					}
				}
			}

			moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) - amount));
			moneyTotal.setOverAmount(String.valueOf(amount + Long.parseLong(moneyTotal.getOverAmount())));
			claimForFundsService.updateMoneyTotal(moneyTotal);

			my.setState("0");
			claimForFundsService.updateMoneyApply(my);

			service.updateWait(wt, this.getRequest());
			taskService.updateBpms_riskoff_task("工单已完成!", 2, btask.getId());
			if (flag && "SH".equals(btask.getType())){
				String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CS", "物联网支撑", zcUser.getRowNo(), user);
				commitBackLog(my,zcUser.getRowNo(), process.getProcess_sign(), user, rtaskid);// 生成待办
			}
			if (amount>0){
				Write(returnPars(1,"","操作成功,工单已完成 工单中推送失败金额:"+(amount/100)+",已退回到未使用资金中!"));
			}else {
				Write(returnPars(1,"","操作成功,工单已完成!"));
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("使用工单结束异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,使用工单结束异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金使用免审推送方法
	 * <AUTHOR>
	 * @Date 2022/6/13 11:00 
	 **/
	public void pushPreinvApply() {
		try {
			String id = getString("id");//资金池ID
			String groupName = getString("groupName");//集团名称
			String group = getString("group");//集团编码
			String orderType = getString("orderType");//申用类型2:政企业务，1个人成员业务
			String contrctNo = getString("contrctNo");      //账户号码
			String contrctType = getString("contrctType");  //账户类型
			String useType = getString("useType");          //使用类型  1：缴费  05：预开票  05A:专线预开票
			String amount = getString("amount");            //金额
			String lateFee = getString("lateFee");          //是否减免  0 标识不减免、1.00标识减免全部
			String lateFeeMoney = getString("lateFeeMoney");//滞纳金

			String unitTaxplayer = getString("unitTaxplayer");  //纳税人信息
			String invNo = getString("invNo");                  //预开票关联编号
			//专线预开票信息
			String idNo = getString("idNo");                    //客户编号
			String specialLineNo = getString("specialLineNo");  //专线号码

			String batchNumber = getString("batchNumber");      //批次号

			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);

			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			if (moneyTotal==null){
				Write(returnPars(-1,"","亲爱的同事,资金编号["+id+"]查询异常,未查询到对应资金信息,请联系管理员处理!"));
				return;
			} else if(moneyTotal.getState()!=1){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]状态异常(当前状态为:"+moneyTotal.getState()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			}else if (moneyTotal.getUserid()!=user.getRowNo()){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]信息异常(当前归属用户为:"+moneyTotal.getUserid()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			}else if (!moneyTotal.getGroupCode().equals(group)){
				Write(returnPars(-1,"","亲爱的同事,资金["+moneyTotal.getSerialNo()+"]信息异常(当前归属集团为:"+moneyTotal.getGroupCode()+"),暂不能发起使用工单,请联系管理员处理!"));
				return;
			} else if(Long.parseLong(moneyTotal.getOverAmount())<Long.parseLong(amount)){
				Write(returnPars(-1,"","亲爱的同事,当前剩余资金为:"+Long.parseLong(moneyTotal.getOverAmount())/100+" 已不足以申请，请核实！"));
				return;
			}

			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			String sateTime = taskService.getNumber();
			if (batchNumber==null){
				batchNumber = sateTime;
			}
			MoneyApplyDet det;

			MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
			String moneyNo = ("2".equals(orderType)?"JT":"GR") + sateTime+"M";

			//工单关联信息
			moneyApplyDet.setMoneyNo(moneyNo);
			moneyApplyDet.setSerialNo(moneyTotal.getSerialNo());
			//集团信息
			moneyApplyDet.setGroupCode(group);
			moneyApplyDet.setGroupName(groupName);
			//创建人信息
			moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
			moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
			//工单信息
			moneyApplyDet.setOpType("2");		//审批明细(1.审批明细/2.免审明细)
			moneyApplyDet.setOrderType(orderType);	//工单类型(1.个人业务/2.集团业务)
			moneyApplyDet.setState("-1");		//未完成：审批中状态

			moneyApplyDet.setPhoneNo(batchNumber);

			moneyApplyDet.setContrctNo(contrctNo);
			moneyApplyDet.setContrctType(contrctType);
			moneyApplyDet.setUseType(useType);
			moneyApplyDet.setInvNo(invNo);
			moneyApplyDet.setAmount(amount);

			moneyApplyDet.setSpecialLineNo(specialLineNo);
			moneyApplyDet.setUserIdNo(idNo);

			moneyApplyDet.setLateFee(lateFee);
			moneyApplyDet.setLateFeeMoney(lateFeeMoney);
			//集团缴费时可能存在纳税人信息
			if ("2".equals(orderType) && "1".equals(useType) && !"".equals(unitTaxplayer)){
				moneyApplyDet.setUnitTaxplayer(unitTaxplayer);
			}

			String login_no=pushBossUser.getBossUserName();
			String unit_id=moneyApplyDet.getGroupCode();
			String op_type="03"; //使用申请
			String out_sys_accept=moneyApplyDet.getMoneyNo();
			String phone_no="";
			String contract_no="";
			String busi_flag="";
			if("1".equals(moneyApplyDet.getOrderType())){
				phone_no = moneyApplyDet.getContrctNo();
				busi_flag="P";
			}else if("2".equals(moneyApplyDet.getOrderType())){
				contract_no = moneyApplyDet.getContrctNo();
				busi_flag="G";
			}
			String prod_name=moneyApplyDet.getMoneyNo()+"-"+moneyApplyDet.getAmount();
			String prod_num="0";
			String apply_login=pushBossUser.getBossUserName();
			String apply_note="无";
			String bill_note="-";
			String bank_account=moneyTotal.getOtherAccNumber()==null?"":moneyTotal.getOtherAccNumber();
			String bank_account_name=moneyTotal.getOtherName()==null?"":moneyTotal.getOtherName();

			String purpose=moneyTotal.getMemo()==null?"无":moneyTotal.getMemo();
			String busi_fee=BigDecimal.valueOf(Long.parseLong(moneyApplyDet.getAmount())).divide(new BigDecimal(100)).toString();
			String pay_type = "0";
			String busi_type="";
			String delay_rate="0";
			String pre_invoice_accept="";
			String route_key = "";
			String userIdNo = "";
			String customerNumber = "";
			if("1".equals(moneyApplyDet.getUseType()) || "01".equals(moneyApplyDet.getUseType())){ //缴费
				if ("01".equals(moneyApplyDet.getUseType())){
					route_key = "10";
				}
				busi_type="00";
				delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
			}else if(moneyApplyDet.getUseType().contains("05")){ //预开票
				busi_type="05";
				//专线预开票
				if ("05A".equals(moneyApplyDet.getUseType())){
					pay_type = "22";
					phone_no = moneyApplyDet.getSpecialLineNo();
					userIdNo = moneyApplyDet.getUserIdNo();
				}
				delay_rate=moneyApplyDet.getLateFee(); //减免滞纳金
				pre_invoice_accept=moneyApplyDet.getInvNo().split("_")[0];
			}else {
				busi_type = moneyApplyDet.getUseType();
			}
			String out_back_accept=moneyApplyDet.getMoneyNo();
			String ctrlFlag = moneyApplyDet.getCtrlFlag();
			Result applyRes=ClaimFundsOpenSrv.getInstance().applyForFunds(login_no, unit_id, op_type, out_sys_accept, contract_no,
					busi_flag, prod_name, prod_num, apply_login, apply_note, bill_note,
					bank_account, bank_account_name, phone_no, purpose, busi_fee, busi_type,
					out_back_accept, delay_rate, pre_invoice_accept,route_key,pay_type,userIdNo,customerNumber,ctrlFlag);
			logger.info(moneyApplyDet.getMoneyNo()+"推送资金使用免审明细结果："+applyRes.toString());
			if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
				JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
				JSONObject ROOT = JSONObject.fromObject(applyObj.getString("ROOT"));
				//循环推送申请工单中的明细记录，成功并记录成功和失败数据
				if("0".equals(ROOT.getString("RETURN_CODE"))){
					moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) - Long.parseLong(moneyApplyDet.getAmount())));
					moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) + Long.parseLong(moneyApplyDet.getAmount())));
					claimForFundsService.updateMoneyTotal(moneyTotal);
					moneyApplyDet.setBossState("0");
					moneyApplyDet.setState("1");
					moneyApplyDet.setPushDate(new Date());
					det = claimForFundsService.addMoneyApplyDet(moneyApplyDet);
					JSONObject OUT_DATA = JSONObject.fromObject(ROOT.getString("OUT_DATA"));
					if (OUT_DATA.has("PAYMENT_ACCEPT")){
						if (OUT_DATA.getString("PAYMENT_ACCEPT").length()>0){
							moneyApplyDet.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
							InvoiceMiddle invoiceMiddle = new InvoiceMiddle();
							invoiceMiddle.setPaymentAccept(OUT_DATA.getString("PAYMENT_ACCEPT"));
							invoiceMiddle.setAssociationNumber(moneyApplyDet.getMoneyNo());
							if (moneyApplyDet.getUnitTaxplayer()!=null && moneyApplyDet.getUnitTaxplayer().length()>0){
								try {
									JSONObject UnitTaxplayer = JSONObject.fromObject(moneyApplyDet.getUnitTaxplayer());
									invoiceMiddle.setMsgRecvPhone(UnitTaxplayer.getString("RECEIVENUMBER"));
									invoiceMiddle.setTaxpayerId(UnitTaxplayer.getString("TAXPAYER_ID"));
									invoiceMiddle.setBankName(UnitTaxplayer.getString("BANK_NAME"));
									invoiceMiddle.setBankAccount(UnitTaxplayer.getString("BANK_ACCOUNT"));
									invoiceMiddle.setAddress(UnitTaxplayer.getString("ADDRESS"));
									invoiceMiddle.setPhoneNo(UnitTaxplayer.getString("PHONE_NO"));
									invoiceMiddle.setPrintType("1");
								}catch (Exception e){
									invoiceMiddle.setPrintType("3");
									invoiceMiddle.setBossMsg("数据解析异常："+e.getMessage());
								}
							}else {
								invoiceMiddle.setPrintType("0");
							}
							claimForFundsService.addInvoiceMiddle(invoiceMiddle);
						}
					}
					Write(returnPars(1,det.getMoneyNo(),"推送成功"));
				}else {
					moneyApplyDet.setBossState("1");
					moneyApplyDet.setState("4");
					moneyApplyDet.setBossMsg(ROOT.getString("RETURN_MSG"));
					claimForFundsService.addMoneyApplyDet(moneyApplyDet);
					Write(returnPars(-1,"",ROOT.getString("RETURN_MSG")));
				}
			}else{
				moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) - Long.parseLong(amount)));
				moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) + Long.parseLong(amount)));
				claimForFundsService.updateMoneyTotal(moneyTotal);
				moneyApplyDet.setBossState("-1");
				moneyApplyDet.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
				claimForFundsService.addMoneyApplyDet(moneyApplyDet);
				Write(returnPars(-1,"","推送接口反馈异常:"+(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200))));
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("免审明细推送异常：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,免审明细推送异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 生成认领工单待办
	 * <AUTHOR>
	 * @param moneyApply 	工单对象
	 * @param userid		下一步处理人编号
	 * @param processId		流程编号
	 * @param user			当前登录人
	 * @param taskid 		任务编号
	 * @Date 2022/6/9 17:15
	 **/
	public void commitBackLog(MoneyApply moneyApply, Integer userid, String processId, SystemUser user, String taskid) {
		WaitTask waitTask = new WaitTask();
		waitTask.setName("[资金认领]" + moneyApply.getTitle());//待办名称
		waitTask.setCreationTime(new Date());// 代办生成时间
		waitTask.setUrl("jsp/claimForFunds/handleClaimForFunds.jsp?id=" + moneyApply.getId() );
		SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
		waitTask.setState(WaitTask.HANDLE);// 状态为待处理
		waitTask.setHandleUserId(USER.getRowNo());// 处理人id
		waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
		waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
		waitTask.setCreateUserId(user.getRowNo());// 创建人id
		waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
		waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
		waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
		waitTask.setTaskId(moneyApply.getId());
		service.saveWait(waitTask, this.getRequest());
	}

	/**---------------------------------------------(资金冲正流程)--------------------------------------------------------------
	 * @Description TODO 资金冲正前置校验
	 * <AUTHOR>
	 * @Date 2022/6/13 14:18
	 **/
	public void verifyWhetherItCanBeCorrected() {
		try {
			String id = getString("id");
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			List<MoneyTotal> monList = claimForFundsService.getMoneyTotalSerialNoList(moneyTotal.getSerialNo());
			List<MoneyApplyDet> canItBeVerifiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(moneyTotal.getSerialNo(),moneyTotal.getGroupCode());
			if(canItBeVerifiedMoneyApply.size()==0){
				if (moneyTotal.getOverAmount().equals(moneyTotal.getSub_overAmount())){
					SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
					if (moneyTotal.getPushDate()!=null){		//是否有记录时间
						if (dateFormat.format(moneyTotal.getPushDate()).equals(dateFormat.format(new Date()))){
							if (monList.size()>1){      //是否为划拨资金
								if (moneyTotal.getIs_sub_group().equals(0)){
									if (moneyTotal.getSub_overAmount().equals(moneyTotal.getOverAmount())){
										for (MoneyTotal total:monList){
											if (total.getIs_sub_group().equals(1)){
												List<MoneyApplyDet> fiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(total.getSerialNo(),total.getGroupCode());
												if(!total.getAmount().equals(total.getOverAmount()) || fiedMoneyApply.size() != 0){
													Write(returnPars(-1, "", "亲爱的同事，子集团:"+total.getGroupCode()+" 尚有未冲正的使用金额,请确认所有子集团是否完成使用冲正!"));
													return;
												}
											}
										}
										Write(returnPars(1, "1", ""));
									}else {
										Write(returnPars(-1, "", "亲爱的同事,资金金额异常[划拨后金额:"+moneyTotal.getSub_overAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
									}
								}else {
									//划拨冲正由主集团发起
									Write(returnPars(-1, "", "亲爱的同事，当前资金已无可冲正的使用明细,因当前为划拨资金如需冲正认领记录请到主资金进行操作!"));
								}
							}else {
								if (moneyTotal.getAmount().equals(moneyTotal.getOverAmount())){
									Write(returnPars(1, "1", ""));//未跨月允许冲正
								}else {
									Write(returnPars(-1, "", "亲爱的同事,资金金额异常[转账金额:"+moneyTotal.getAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
								}
							}
						}else {
							if (monList.size()>1){
								if (moneyTotal.getIs_sub_group().equals(0)){
									if (moneyTotal.getSub_overAmount().equals(moneyTotal.getOverAmount())){
										for (MoneyTotal total:monList){
											if (total.getIs_sub_group().equals(1)){
												List<MoneyApplyDet> fiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(total.getSerialNo(),total.getGroupCode());
												if(!total.getAmount().equals(total.getOverAmount()) || fiedMoneyApply.size() != 0){
													Write(returnPars(-1, "", "亲爱的同事，子集团:"+total.getGroupCode()+" 尚有未冲正的使用金额,请确认所有子集团是否完成使用冲正!"));
													return;
												}
											}
										}
										Write(returnPars(1, "2", "亲爱的同事，由于业务规则限制认领记录跨月冲正，需指定新的认领集团，请确认！"));//跨月冲正
									}else {
										Write(returnPars(-1, "", "亲爱的同事,资金金额异常[划拨后金额:"+moneyTotal.getSub_overAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
									}
								}else {
									//划拨冲正由主集团发起
									Write(returnPars(-1, "", "亲爱的同事，当前资金已无可冲正的使用明细,因当前为划拨资金如需冲正认领记录请到主资金进行操作!"));
								}
							}else {
								Write(returnPars(1, "2", "亲爱的同事，由于业务规则限制认领记录跨月冲正，需指定新的认领集团，请确认！"));//跨月冲正
							}
						}
					}else {
						SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
						try {		//判断boss_msg是否为推送时间
							Date date = format.parse(moneyTotal.getBoss_Msg());
							if (dateFormat.format(date).equals(dateFormat.format(new Date()))){
								if (monList.size()>1){      //是否为划拨资金
									if (moneyTotal.getIs_sub_group().equals(0)){
										if (moneyTotal.getSub_overAmount().equals(moneyTotal.getOverAmount())){
											for (MoneyTotal total:monList){
												if (total.getIs_sub_group().equals(1)){
													List<MoneyApplyDet> fiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(total.getSerialNo(),total.getGroupCode());
													if(!total.getAmount().equals(total.getOverAmount()) || fiedMoneyApply.size() != 0){
														Write(returnPars(-1, "", "亲爱的同事，子集团:"+total.getGroupCode()+" 尚有未冲正的使用金额,请确认所有子集团是否完成使用冲正!"));
														return;
													}
												}
											}
											Write(returnPars(1, "1", ""));
										}else {
											Write(returnPars(-1, "", "亲爱的同事,资金金额异常[划拨后金额:"+moneyTotal.getSub_overAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
										}
									}else {
										//划拨冲正由主集团发起
										Write(returnPars(-1, "", "亲爱的同事，当前资金已无可冲正的使用明细,因当前为划拨资金如需冲正认领记录请到主资金进行操作!"));
									}
								}else {
									if (moneyTotal.getAmount().equals(moneyTotal.getOverAmount())){
										Write(returnPars(1, "1", ""));//未跨月允许冲正
									}else {
										Write(returnPars(-1, "", "亲爱的同事,资金金额异常[转账金额:"+moneyTotal.getAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
									}
								}
							}else {
								if (monList.size()>1){
									if (moneyTotal.getIs_sub_group().equals(0)){
										if (moneyTotal.getSub_overAmount().equals(moneyTotal.getOverAmount())){
											for (MoneyTotal total:monList){
												if (total.getIs_sub_group().equals(1)){
													List<MoneyApplyDet> fiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(total.getSerialNo(),total.getGroupCode());
													if(!total.getAmount().equals(total.getOverAmount()) || fiedMoneyApply.size() != 0){
														Write(returnPars(-1, "", "亲爱的同事，子集团:"+total.getGroupCode()+" 尚有未冲正的使用金额,请确认所有子集团是否完成使用冲正!"));
														return;
													}
												}
											}
											Write(returnPars(1, "2", "亲爱的同事，由于业务规则限制认领记录跨月冲正，需指定新的认领集团，请确认！"));//跨月冲正
										}else {
											Write(returnPars(-1, "", "亲爱的同事,资金金额异常[划拨后金额:"+moneyTotal.getSub_overAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
										}
									}else {
										//划拨冲正由主集团发起
										Write(returnPars(-1, "", "亲爱的同事，当前资金已无可冲正的使用明细,因当前为划拨资金如需冲正认领记录请到主资金进行操作!"));
									}
								}else {
									Write(returnPars(1, "2", "亲爱的同事，由于业务规则限制认领记录跨月冲正，需指定新的认领集团，请确认！"));//跨月冲正
								}
							}
						}catch (Exception e){		//获取最新工单的创建1时间
							List<MoneyApply> moneyApplyList = claimForFundsService.GetMoneyApplyListBySerialNo(moneyTotal.getSerialNo());
							if (moneyApplyList.size()<1){
								Write(returnPars(-1, "", "数据查询异常,未查询到最新的推送工单！"));
							}else {
								if (dateFormat.format(moneyApplyList.get(0).getCreateDate()).equals(dateFormat.format(new Date()))){
									if (monList.size()>1){      //是否为划拨资金
										if (moneyTotal.getIs_sub_group().equals(0)){
											if (moneyTotal.getSub_overAmount().equals(moneyTotal.getOverAmount())){
												for (MoneyTotal total:monList){
													if (total.getIs_sub_group().equals(1)){
														List<MoneyApplyDet> fiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(total.getSerialNo(),total.getGroupCode());
														if(!total.getAmount().equals(total.getOverAmount()) || fiedMoneyApply.size() != 0){
															Write(returnPars(-1, "", "亲爱的同事，子集团:"+total.getGroupCode()+" 尚有未冲正的使用金额,请确认所有子集团是否完成使用冲正!"));
															return;
														}
													}
												}
												Write(returnPars(1, "1", ""));
											}else {
												Write(returnPars(-1, "", "亲爱的同事,资金金额异常[划拨后金额:"+moneyTotal.getSub_overAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
											}
										}else {
											//划拨冲正由主集团发起
											Write(returnPars(-1, "", "亲爱的同事，当前资金已无可冲正的使用明细,因当前为划拨资金如需冲正认领记录请到主资金进行操作!"));
										}
									}else {
										if (moneyTotal.getAmount().equals(moneyTotal.getOverAmount())){
											Write(returnPars(1, "1", ""));//未跨月允许冲正
										}else {
											Write(returnPars(-1, "", "亲爱的同事,资金金额异常[转账金额:"+moneyTotal.getAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
										}
									}
								}else {
									if (monList.size()>1){
										if (moneyTotal.getIs_sub_group().equals(0)){
											if (moneyTotal.getSub_overAmount().equals(moneyTotal.getOverAmount())){
												for (MoneyTotal total:monList){
													if (total.getIs_sub_group().equals(1)){
														List<MoneyApplyDet> fiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(total.getSerialNo(),total.getGroupCode());
														if(!total.getAmount().equals(total.getOverAmount()) || fiedMoneyApply.size() != 0){
															Write(returnPars(-1, "", "亲爱的同事，子集团:"+total.getGroupCode()+" 尚有未冲正的使用金额,请确认所有子集团是否完成使用冲正!"));
															return;
														}
													}
												}
												Write(returnPars(1, "2", "亲爱的同事，由于业务规则限制认领记录跨月冲正，需指定新的认领集团，请确认！"));//跨月冲正
											}else {
												Write(returnPars(-1, "", "亲爱的同事,资金金额异常[划拨后金额:"+moneyTotal.getSub_overAmount()+" 当前金额:"+moneyTotal.getOverAmount()+" ]请联系管理员处理!"));
											}
										}else {
											//划拨冲正由主集团发起
											Write(returnPars(-1, "", "亲爱的同事，当前资金已无可冲正的使用明细,因当前为划拨资金如需冲正认领记录请到主资金进行操作!"));
										}
									}else {
										Write(returnPars(1, "2", "亲爱的同事，由于业务规则限制认领记录跨月冲正，需指定新的认领集团，请确认！"));//跨月冲正
									}
								}
							}
						}
					}
				}else {
					Write(returnPars(-1, "", "亲爱的同事，当前资金尚有明细资金未完成冲正，请核实!"));
				}
			}else {
				Write(returnPars(1, "0", ""));//0就是使用冲正
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("资金冲正校验异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，资金冲正校验异常："+e.getMessage()+",请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 查询可冲正明细信息
	 * <AUTHOR>
	 * @Date 2022/6/13 14:27 
	 **/
	public void getMoneyApplyDetlist() {
		try {
			String id = getString("id");
			String unit = getString("nnit");
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			List<MoneyApplyDet> canItBeVerifiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReformTwo(moneyTotal.getSerialNo(),unit);
			List<MoneyApplyDet> amountList = claimForFundsService.getMoneyApplyDetlistInReform(moneyTotal.getSerialNo());
			JsonConfig jsonConfig = new JsonConfig();
			jsonConfig.registerJsonValueProcessor(Date.class, new JsonDateValueProcessor());
			JSONObject root =new JSONObject();
			Map<String,Object> map = new HashMap<>();
			if(canItBeVerifiedMoneyApply.size()==0){
				if(amountList.size()>0){
					map.put("RectificationInList",amountList);
				}else{
					map.put("RectificationInList","");
				}
				map.put("RectificationList","");
				root.putAll(map,jsonConfig);
				Write(returnPars(1, root.toString(), "未查询到资金需要冲正的明细信息"));
			}else {
				if(amountList.size()>0){
					map.put("RectificationInList",amountList);
				}else{
					map.put("RectificationInList","");
				}
				map.put("RectificationList",canItBeVerifiedMoneyApply);
				root.putAll(map,jsonConfig);
				Write(returnPars(1, root.toString(), ""));
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("资金冲正列表查询异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,资金冲正列表查询异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金明细冲正  流程开始
	 * <AUTHOR>
	 * @Date 2022/6/13 14:29
	 **/
	public void addCapitalRectification(){
		try{
			String id = getString("id");
			String detList = getString("detList");
			String userId = getString("userId");
			String[] detArray = detList.split(",");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			String explain = getString("explain");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			List<String> bossArray = new ArrayList<>();
			for (int i = 0; i < detArray.length; i++) {
				MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(detArray[i]);
				if ("0".equals(mad.getBossState())) {
					if ("3".equals(mad.getUseType())|| "06".equals(mad.getUseType())) {
						bossArray.add(detArray[i]);
					}
				}
			}
			SystemUser systemUser = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
			if (systemUser==null){
				Write(returnPars(-1, "", "亲爱的同事,您选择的下一步处理人信息异常,请重新选择或联系管理员处理!"));
				return;
			}
			String[] boss = new String[bossArray.size()];
			bossArray.toArray(boss);
			String tys ="YES";
			if (boss.length>0){
				tys =capitalCampOn(boss);
			}
			if ("YES".equals(tys)) {        //调用冲正暂挂
				String IBM = "";
				Integer number = 0;
				List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
				for (int i = 0; i < sone.size(); i++) {
					IBM = (String) sone.get(i)[2];
				}
				MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
				//生成新的冲正工单
				MoneyApply myly = new MoneyApply();
				String sateTime = taskService.getNumber();
				String batchNO = IBM + "CZ" + sateTime;
				myly.setApplyNo(batchNO);
				myly.setSerialNo(moneyTotal.getSerialNo());
				myly.setMoneyTotal_id(moneyTotal.getId());

				myly.setTitle("资金冲正-" + moneyTotal.getGroupName());
				myly.setApplyMemo(explain);

				myly.setCreatorId(user.getRowNo() + "");
				myly.setCreatorName(user.getEmployeeName());
				myly.setCreateDate(new Date());

				myly.setGroupCode(moneyTotal.getGroupCode());
				myly.setGroupName(moneyTotal.getGroupName());

				myly.setOpType("2");
				myly.setState("1");

				long amount = 0;
				for (String det : detArray) {
					MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(det);
					if (mad != null) {
						MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
						String datatime = taskService.getNumber();
						String moneyNo = "JTCZ" + datatime + number.toString();
						moneyApplyDet.setMoneyNo(moneyNo);
						moneyApplyDet.setApplyNo(myly.getApplyNo());
						moneyApplyDet.setSerialNo(mad.getSerialNo());
						moneyApplyDet.setSerialNo(mad.getSerialNo());

						moneyApplyDet.setState("-1");
						moneyApplyDet.setOpType("1");
						moneyApplyDet.setOrderType(mad.getOrderType());
						moneyApplyDet.setUseType(mad.getUseType());

						moneyApplyDet.setContrctNo(mad.getContrctNo());
						moneyApplyDet.setContrctType(mad.getContrctType());
						moneyApplyDet.setAmount(mad.getAmount());

						moneyApplyDet.setCreatorId(String.valueOf(user.getRowNo()));
						moneyApplyDet.setContrctName(mad.getContrctName());
						moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));

						moneyApplyDet.setGroupCode(mad.getGroupCode());
						moneyApplyDet.setGroupName(mad.getGroupName());

						moneyApplyDet.setSpecialLineNo(mad.getSpecialLineNo());
						moneyApplyDet.setUserIdNo(mad.getUserIdNo());
						moneyApplyDet.setCustomerNumber(mad.getCustomerNumber());

						moneyApplyDet.setParentId(mad.getId());

						amount += Long.parseLong(mad.getAmount());
						MoneyApplyDet moneyApplyDet1 = claimForFundsService.addMoneyApplyDet(moneyApplyDet);

						//修改原工单
						mad.setRectificaState(mad.getState());
						mad.setState("5");
						mad.setOldMoneyNo(moneyApplyDet1.getMoneyNo());
						claimForFundsService.updateMoneyApplyDet(mad);
					}
					++number;
				}
				myly.setApplyAmount(String.valueOf(amount));
				MoneyApply my = claimForFundsService.addMoneyApply(myly);
				//生成任务信息
				String processId = "claimForFundsChange." + sateTime;
				taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
				taskService.setBpms_riskoff_task(processId, "发起工单", 2, "CZ", "起草人", user.getRowNo(), user);//先保存自己本身的任务
				String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "CZ", systemUser.getEmployeeName(), systemUser.getRowNo(), user);//预存下一步任务
				//生成审批待办
				WaitTask waitTask = new WaitTask();
				waitTask.setName("[资金冲正]" + my.getTitle());//待办名称
				waitTask.setCreationTime(new Date());// 代办生成时间
				waitTask.setUrl("jsp/claimForFunds/handleClaim.jsp?id=" + my.getId() + "&applyNo=" + my.getApplyNo() + "&taskId=" + taskid);
				waitTask.setState(WaitTask.HANDLE);// 状态为待处理
				waitTask.setHandleUserId(systemUser.getRowNo());// 处理人id
				waitTask.setHandleUserName(systemUser.getEmployeeName());// 处理人名称
				waitTask.setHandleLoginName(systemUser.getLoginName());// 处理人登录名
				waitTask.setCreateUserId(user.getRowNo());// 创建人id
				waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
				waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
				waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
				waitTask.setTaskId(my.getId());
				service.saveWait(waitTask, this.getRequest());
				Write(returnPars(1, "", "您的订单已推送：" + systemUser.getEmployeeName() + ",请等待审批！"));
			} else {
				Write(returnPars(-1, "", "亲爱的同事,工单预占未通过,请确认！"));
			}
		}catch (Exception e){
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.info("资金冲正工单创建异常："+e.getMessage(),e);
			Write(returnPars(-1, "", "亲爱的同事，资金冲正工单创建异常【"+e.getMessage()+"】，请联系系统管理员"));
		}
	}

	/**
	 * @Description TODO 资金明细冲正 审批完成方法
	 * <AUTHOR>
	 * @Date 2022/6/13 14:40
	 **/
	public void handleCapitalRectification(){
		try {
			String id = getString("id");//资金认领id
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程

			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());//查询冲正的对应明细
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());//查询工单对应的资金池
			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}
			int count=0;
			String login_no = pushBossUser.getBossUserName();       //BOSS工号
			String unit_id = moneyTotal.getGroupCode();             //集团280
			String purpose = (moneyTotal.getMemo()!=null?moneyTotal.getMemo():"资金冲正");               //资金用途
			String bank_account = moneyTotal.getOtherAccNumber();   //资金池的对方账户
			String bank_account_name = moneyTotal.getOtherName();   //资金池的对方户名
			String pay_type = "0";
			for(int i=0;i<moneyApplyDet.size();i++){		//循环明细-->冲正
				MoneyApplyDet mad = moneyApplyDet.get(i);
				MoneyApplyDet dettwo = claimForFundsService.getMoneyApplyDetById(mad.getParentId());
				if (("01".equals(dettwo.getUseType()) || "12".equals(dettwo.getUseType())) && dettwo.getBossNo()!=null && !"".equals(dettwo.getBossNo())){
					SystemUser pushUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(dettwo.getBossNo()));
					if (pushUser != null && pushUser.getBossUserName()!=null){
						login_no = pushUser.getBossUserName();
					}else {
						count+=1;
						mad.setBossState("1");
						mad.setBossMsg("冲正失败:代理缴费人员的工号为空，请核实！");
						claimForFundsService.updateMoneyApplyDet(mad);
						continue;
					}
				}

				String out_back_accept = dettwo.getMoneyNo();    //需要冲正工单的流水
				String route_key = "";
				String contract_no = "";
				String phone_no = "";
				String userIdNo = "";
				String customerNumber = "";

				String busi_type;          //业务类型
				String busi_flag;          //标识集团还是个人认领： G集团，P个人
				if ("1".equals(dettwo.getOrderType())){
					busi_flag = "P";
					phone_no = dettwo.getContrctNo();
				}else {
					busi_flag = "G";
					contract_no = dettwo.getContrctNo();
				}

				switch (dettwo.getUseType()){
					case "01":
						route_key = "10";
						busi_type = "00";
						break;
					case "1":
						busi_type = "00";
						break;
					case "3":
						busi_type = "01";
						break;
					case "2":
						busi_type = "02";
						break;
					case "05A":
						busi_type = "05";
						pay_type = "22";
						phone_no = dettwo.getSpecialLineNo();
						userIdNo = dettwo.getUserIdNo();
						break;
					case "12":
					case "12A":
					case "12B":
						busi_type = "12";
						customerNumber = dettwo.getCustomerNumber();
						break;
					default:
						busi_type = dettwo.getUseType();
						break;
				}
				//金额（元）
				String busi_fee = BigDecimal.valueOf(Long.valueOf(dettwo.getAmount())).divide(new BigDecimal(100)).toString();
				String delay_rate = dettwo.getLateFee();         //是否减免滞纳金
				String out_sys_accept = mad.getMoneyNo();     //冲正工单流水

				Result applyRes=ClaimFundsOpenSrv.getInstance().ReversalApplyForFunds(login_no,unit_id,out_back_accept,bank_account,bank_account_name,busi_type,
						busi_fee,busi_flag,contract_no,delay_rate,purpose,out_sys_accept,route_key,pay_type,phone_no,userIdNo,customerNumber);
				logger.info("====>推送资金使用明细冲正结果："+applyRes.toString());
				if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
					JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
					//循环推送申请工单中的明细记录，成功并记录成功和失败数据
					if("0".equals(JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_CODE"))){
						mad.setState("1");
						mad.setBossState("0");
						mad.setBossMsg("冲正完成");
						claimForFundsService.updateMoneyApplyDet(mad);
						dettwo.setState("3");
						dettwo.setRectificaState(null);
						claimForFundsService.updateMoneyApplyDet(dettwo);

						if ("2".equals(dettwo.getUseType()) && !updaetQuotaWorkHandleByMoneyNo(dettwo.getMoneyNo(),"0")){
							throw new Exception("修改营销活动失败！");
						}else if("06".equals(dettwo.getUseType()) && !updateValuableCard(dettwo.getInvNo(),dettwo.getContrctNo(),dettwo.getAmount(),"DELECT")){
							throw new Exception("修改有价卡失败！");
						}else if ("12B".equals(dettwo.getUseType()) && !updateInternetOfThingsDet(dettwo.getInvNo(),dettwo.getContrctNo(),dettwo.getAmount(),"DELECT")){
							throw new Exception("修改物联网预开票失败！");
						};

						moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) - Long.parseLong(dettwo.getAmount())));
						moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) + Long.parseLong(dettwo.getAmount())));
						claimForFundsService.updateMoneyTotal(moneyTotal);
					}else {
						count+=1;
						mad.setBossState("1");
						if (JSONObject.fromObject(applyObj.getString("ROOT")).has("RETURN_MSG")){
							mad.setBossMsg("冲正失败:"+JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_MSG"));
						}else {
							mad.setBossMsg("冲正失败:未获取到失败原因,失败编号:"+JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_CODE"));
						}
						claimForFundsService.updateMoneyApplyDet(mad);
					}
				}else{
					count+=1;
					mad.setBossState("1");
					mad.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
					claimForFundsService.updateMoneyApplyDet(mad);
				}
			}
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (wt != null) {
				service.updateWait(wt, this.getRequest());
			}
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			SystemUser USER=systemUserService.getUserInfoRowNo(moneyTotal.getUserid());//不是的话就用审批人
			String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CZ", "起草人",
					Integer.parseInt(my.getCreatorId()), user);
			WaitTask waitTask = new WaitTask();
			waitTask.setName("[资金冲正]" + my.getTitle());//待办名称
			waitTask.setCreationTime(new Date());// 代办生成时间
			waitTask.setUrl("jsp/claimForFunds/handleClaimtwo.jsp?id="+my.getId()+"&applyNo="+my.getApplyNo()+"&taskId="+rtaskid);
			waitTask.setState(waitTask.HANDLE);// 状态为待处理
			waitTask.setHandleUserId(USER.getRowNo());// 处理人id
			waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
			waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
			waitTask.setCreateUserId(user.getRowNo());// 创建人id
			waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
			waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
			waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
			waitTask.setTaskId(my.getId());
			service.saveWait(waitTask, this.getRequest());
			if(count>0){
				my.setState("3");
			}else{
				my.setState("0");
			}
			claimForFundsService.updateMoneyApply(my);
			Write(returnPars(1, "", "操作成功"));
		}catch (Exception e){
			e.printStackTrace();
			logger.error("明细冲正完成推送异常："+e.getMessage(),e);
			Write(returnPars(-1, "", "亲爱的同事，明细冲正完成推送异常【"+e.getMessage()+"】，请联系系统管理员"));
		}
	}

	/**
	 * @Description TODO 资金明细冲正 审批完成推送失败再次推送方法
	 * <AUTHOR>
	 * @Date 2022/6/13 14:43
	 **/
	public void complateCapitalRectification() {
		try {
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			WaitTask wait = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务

			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());//查询冲正的对应明细
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());//查询工单对应的资金池
			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}
			int num = 0;
			String login_no = pushBossUser.getBossUserName();       //BOSS工号
			String unit_id = moneyTotal.getGroupCode();             //集团280
			String purpose = (moneyTotal.getMemo()!=null?moneyTotal.getMemo():"资金冲正");               //资金用途
			String bank_account = moneyTotal.getOtherAccNumber();   //资金池的对方账户
			String bank_account_name = moneyTotal.getOtherName();   //资金池的对方户名
			String pay_type = "0";
			for (int i = 0; i < moneyApplyDet.size(); i++) {        //循环明细-->冲正
				MoneyApplyDet mad = moneyApplyDet.get(i);
				if ("1".equals(mad.getBossState())) {        //推送boss的数据
					MoneyApplyDet dettwo = claimForFundsService.getMoneyApplyDetById(mad.getParentId());
					if ( ("01".equals(dettwo.getUseType()) || "12".equals(dettwo.getUseType())) && dettwo.getBossNo()!=null && !"".equals(dettwo.getBossNo())){
						SystemUser pushUser = systemUserService.getByUserInfoRowNo(Integer.valueOf(dettwo.getBossNo()));
						if (pushUser != null && pushUser.getBossUserName()!=null){
							login_no = pushUser.getBossUserName();
						}else {
							num+=1;
							mad.setBossState("1");
							mad.setBossMsg("冲正失败:代理缴费人员的工号为空，请核实！");
							claimForFundsService.updateMoneyApplyDet(mad);
							continue;
						}
					}
					String out_back_accept = dettwo.getMoneyNo();    //需要冲正工单的流水
					String route_key = "";
					String contract_no = "";
					String phone_no = "";
					String userIdNo = "";
					String customerNumber = "";

					String busi_type;          //业务类型
					String busi_flag;          //标识集团还是个人认领： G集团，P个人
					if ("1".equals(dettwo.getOrderType())){
						busi_flag = "P";
						phone_no = dettwo.getContrctNo();
					}else {
						busi_flag = "G";
						contract_no = dettwo.getContrctNo();
					}
					switch (dettwo.getUseType()){
						case "01":
							route_key = "10";
							busi_type = "00";
							break;
						case "1":
							busi_type = "00";
							break;
						case "3":
							busi_type = "01";
							break;
						case "2":
							busi_type = "02";
							break;
						case "05A":
							busi_type = "05";
							pay_type = "22";
							phone_no = dettwo.getSpecialLineNo();
							userIdNo = dettwo.getUserIdNo();
							break;
						case "12":
						case "12A":
						case "12B":
							busi_type = "12";
							customerNumber = dettwo.getCustomerNumber();
							break;
						default:
							busi_type = dettwo.getUseType();
							break;
					}
					//金额（元）
					String busi_fee = BigDecimal.valueOf(Long.valueOf(dettwo.getAmount())).divide(new BigDecimal(100)).toString();
					String delay_rate = dettwo.getLateFee();         //是否减免滞纳金
					String out_sys_accept = mad.getMoneyNo();     //冲正工单流水

					Result applyRes=ClaimFundsOpenSrv.getInstance().ReversalApplyForFunds(login_no,unit_id,out_back_accept,bank_account,bank_account_name,busi_type,
							busi_fee,busi_flag,contract_no,delay_rate,purpose,out_sys_accept,route_key,pay_type,phone_no,userIdNo,customerNumber);
					logger.info("====>再次推送资金使用明细冲正结果："+applyRes.toString());
					if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
						JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
						//循环推送申请工单中的明细记录，成功并记录成功和失败数据
						if("0".equals(JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_CODE"))){
							mad.setState("1");
							mad.setBossState("0");
							mad.setBossMsg("冲正完成");
							claimForFundsService.updateMoneyApplyDet(mad);
							dettwo.setState("3");
							dettwo.setRectificaState(null);
							claimForFundsService.updateMoneyApplyDet(dettwo);

							if ("2".equals(dettwo.getUseType()) && !updaetQuotaWorkHandleByMoneyNo(dettwo.getMoneyNo(),"0")){
								throw new Exception("修改营销活动失败！");
							}else if("06".equals(dettwo.getUseType()) && !updateValuableCard(dettwo.getInvNo(),dettwo.getContrctNo(),dettwo.getAmount(),"DELECT")){
								throw new Exception("修改有价卡失败！");
							}else if ("12B".equals(dettwo.getUseType()) && !updateInternetOfThingsDet(dettwo.getInvNo(),dettwo.getContrctNo(),dettwo.getAmount(),"DELECT")){
								throw new Exception("修改物联网预开票失败！");
							};

							moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) - Long.parseLong(dettwo.getAmount())));
							moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) + Long.parseLong(dettwo.getAmount())));
							claimForFundsService.updateMoneyTotal(moneyTotal);
						}else {
							num+=1;
							mad.setBossState("1");
							if (JSONObject.fromObject(applyObj.getString("ROOT")).has("RETURN_MSG")){
								mad.setBossMsg("冲正失败:"+JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_MSG"));
							}else {
								mad.setBossMsg("冲正失败:未获取到失败原因,失败编号:"+JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_CODE"));
							}
							claimForFundsService.updateMoneyApplyDet(mad);
						}
					}else{
						num+=1;
						mad.setBossState("1");
						mad.setBossMsg(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200));
						claimForFundsService.updateMoneyApplyDet(mad);
					}
				}
			}
			if (num==0){
				my.setState("0");
				claimForFundsService.updateMoneyApply(my);
				taskService.updateBpms_riskoff_task("", 2, btask.getId());
				taskService.updatebpmsRiskoffProcess(id,2);
				if (wait != null) {
					//完成首页待办
					service.updateWait(wait, this.getRequest());
				}
				Write(returnPars(1, "", "操作成功,工单已推送完成"));
			}else {
				Write(returnPars(-1, "", "工单中扔有"+num+"条明细,未成功推送,失败信息请刷新页面查看！"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.error("资金冲正再次推送异常："+e.getMessage(),e);
			Write(returnPars(-1, "", "亲爱的同事，资金冲正再次推送异常【"+e.getMessage()+"】，请联系系统管理员"));
		}
	}

	/**
	 * @Description TODO 资金明细冲正 结束工单 如工单存在失败明细 进行作废
	 * <AUTHOR>
	 * @Date 2022/6/13 15:47
	 **/
	public void endOverruleClaim() {
		try{
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			WaitTask wait = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询冲正信息
			List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());//查询冲正的对应明细
			int num = 0;
			for(int i=0;i<moneyApplyDet.size();i++){
				MoneyApplyDet mad = moneyApplyDet.get(i);
				//循环明细信息  判断是否推送失败
				if (mad!=null&&"1".equals(mad.getBossState())){
					MoneyApplyDet madtwo = claimForFundsService.getMoneyApplyDetById(mad.getParentId());
					if ("3".equals(mad.getUseType())|| "06".equals(mad.getUseType())) {
						if ("YES".equals(relieveCapitalCampOn(madtwo.getMoneyNo()))){
							mad.setState("4");
							madtwo.setState("1");
							madtwo.setRectificaState(null);
							madtwo.setOldMoneyNo(null);
							claimForFundsService.updateMoneyApplyDet(madtwo);
							claimForFundsService.updateMoneyApplyDet(mad);
						}else {
							++num;
						}
					}else {
						mad.setState("4");
						madtwo.setState("1");
						madtwo.setRectificaState(null);
						madtwo.setOldMoneyNo(null);
						claimForFundsService.updateMoneyApplyDet(madtwo);
						claimForFundsService.updateMoneyApplyDet(mad);
					}
				}
			}
			if (num==0){
				my.setState("0");
				claimForFundsService.updateMoneyApply(my);
				taskService.updateBpms_riskoff_task("工单已完成!", -1, btask.getId());
				if(wait!=null){
					service.updateWait(wait, this.getRequest());
				}
				Write(returnPars(1, "", "亲爱的同事,工单已完成!"));
			}else {
				Write(returnPars(-1, "", "亲爱的同事,工单中有"+num+"条明细,未能成功解除预占！"));
			}
		}catch (Exception e){
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("明细冲正结束异常："+e.getMessage(),e);
			Write(returnPars(-1, "", "亲爱的同事，明细冲正结束异常【"+e.getMessage()+"】，请联系系统管理员"));
		}
	}

	/**
	 * @Description TODO 资金明细冲正  流程退回
	 * <AUTHOR>
	 * @Date 2022/6/13 14:35
	 **/
	public void returnCapitalRectification(){
		try{
			String id = getString("id");
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());//查询工单对应的资金池
			SystemUser USER = systemUserService.getUserInfoRowNo(moneyTotal.getUserid());
			my.setState("2");
			claimForFundsService.updateMoneyApply(my);
			taskService.updateBpms_riskoff_task(opinion, 0, btask.getId());
			taskService.setBpms_riskoff_task(my.getId(),opinion,0,"CZ","起草人",USER.getRowNo(), user);
			String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CZ", "起草人",
					Integer.parseInt(my.getCreatorId()), user);
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (wt != null) {
				service.updateWait(wt, this.getRequest());
			}
			WaitTask waitTask = new WaitTask();
			waitTask.setName("[资金冲正-退回]" + my.getTitle());//待办名称
			waitTask.setCreationTime(new Date());// 代办生成时间
			waitTask.setUrl("jsp/claimForFunds/handleClaimtwo.jsp?id="+my.getId()+"&applyNo="+my.getApplyNo()+"&taskId="+rtaskid);
			waitTask.setState(waitTask.HANDLE);// 状态为待处理
			waitTask.setHandleUserId(USER.getRowNo());// 处理人id
			waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
			waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
			waitTask.setCreateUserId(user.getRowNo());// 创建人id
			waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
			waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
			waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
			waitTask.setTaskId(my.getId());
			service.saveWait(waitTask, this.getRequest());
			Write(returnPars(1, "", "退回成功"));
		}catch (Exception e){
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("明细冲正退回异常："+e.getMessage(),e);
			Write(returnPars(-1, "", "亲爱的同事，明细冲正退回异常【"+e.getMessage()+"】，请联系系统管理员"));
		}
	}
	
	/**
	 * @Description TODO 资金明细冲正  流程作废
	 * <AUTHOR>
	 * @Date 2022/6/13 15:51 
	 **/
	public void openOverruleClaim() {
		try{
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码

			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			WaitTask wait = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询冲正信息
			List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());//查询冲正的对应明细
			int num = 0;
			for(int i=0;i<moneyApplyDet.size();i++){
				MoneyApplyDet mad = moneyApplyDet.get(i);
				if (mad!=null){
					MoneyApplyDet madtwo = claimForFundsService.getMoneyApplyDetById(mad.getParentId());
					if ("3".equals(mad.getUseType())|| "06".equals(mad.getUseType())) {
						if ("YES".equals(relieveCapitalCampOn(madtwo.getMoneyNo()))){
							mad.setState("4");
							madtwo.setState("1");
							madtwo.setRectificaState(null);
							madtwo.setOldMoneyNo(null);
							claimForFundsService.updateMoneyApplyDet(madtwo);
							claimForFundsService.updateMoneyApplyDet(mad);
						}else {
							++num;
						}
					}else {
						mad.setState("4");
						madtwo.setState("1");
						madtwo.setRectificaState(null);
						madtwo.setOldMoneyNo(null);
						claimForFundsService.updateMoneyApplyDet(madtwo);
						claimForFundsService.updateMoneyApplyDet(mad);
					}
				}
			}
			if (num==0){
				my.setState("-1");
				claimForFundsService.updateMoneyApply(my);
				taskService.updateBpms_riskoff_task("作废", -1, btask.getId());
				if(wait!=null){
					service.updateWait(wait, this.getRequest());
				}
				Write(returnPars(1, "", "作废成功"));
			}else {
				Write(returnPars(-1, "", "工单中有"+num+"条明细,未解除预占！"));
			}
		}catch (Exception e){
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("明细冲正作废异常："+e.getMessage(),e);
			Write(returnPars(-1, "", "亲爱的同事，明细冲正作废异常【"+e.getMessage()+"】，请联系系统管理员"));
		}
	}
	
	/**
	 * @Description TODO 认领资金（划拨资金）冲正
	 * <AUTHOR>
	 * @Date 2022/6/13 14:25 
	 **/
	public void reChargeUnitAccountRectification() {
		try {
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser users = claimForFundsService.querUsers(userPhoneNum);
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			SystemUser pushBossUser = systemUserService.getByUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}
			List<MoneyTotal> monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
			if (monList.size()>0){
				for (MoneyTotal value : monList) {
					value.setState(3);
					claimForFundsService.updateMoneyTotal(value);
				}
				monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
				//循环冲正子集团
				int num = 0;
				for (int i = 0, monListSize = monList.size(); i < monListSize; i++) {
					MoneyTotal total = monList.get(i);
					SystemUser subPushUser = systemUserService.getByUserInfoRowNo(total.getPushUserName());
					Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(total.getBatchNo(), subPushUser.getBossUserName());
					if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
						JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
						JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
						if ("0".equals(root.getString("RETURN_CODE"))) {
							total.setState(-1);
							total.setBoss_Msg("上次操作人：" + users.getEmployeeName() + "在" + getStringDatetwo(new Date()) + "进行了冲正操作");
							claimForFundsService.updateMoneyTotal(total);
						} else {
							num += 1;
							logger.info("子集团资金冲正失败:" + JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
							total.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
							claimForFundsService.updateMoneyTotal(total);
						}
					} else {
						num += 1;
						logger.info("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
						total.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
						claimForFundsService.updateMoneyTotal(total);
					}
				}
				if (num>0){
					moneyTotal.setState(4);
					moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了冲正操作,尚有"+num+"条划拨资金未完成冲正!");
					claimForFundsService.updateMoneyTotal(moneyTotal);
					Write(returnPars(-1, "", "亲爱的同事,冲正划拨资金未完成,尚有"+num+"条划拨资金未完成冲正!"));
				}else {
					if (!moneyTotal.getSub_overAmount().equals("0")){
						Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(moneyTotal.getBatchNo(),pushBossUser.getBossUserName());
						if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
							JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
							JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
							if ("0".equals(root.getString("RETURN_CODE"))) {
								moneyTotal.setBak1(moneyTotal.getBatchNo());
								moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
								//boss推送成功后调用财务接口修改财务数据，实现实时冲正
								List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
								if (mapList==null){
									Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
									return;
								}
								String county = mapList.get(0).get("COUNTY_NAME");
								String region = mapList.get(0).get("COMPANY_NAME");
								String fullName = mapList.get(0).get("EMPLOYEE_NAME");
								Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("2",moneyTotal.getSerialNo(),county,region,fullName);
								logger.info("财务接口调用结果===>"+result.toString());
								if(ResultCode.SUCCESS.code()==result.getCode()){
									moneyTotal.setPushUserName(null);
									moneyTotal.setPushBossUserName(null);
									moneyTotal.setIsThe(0);
									moneyTotal.setGroupCode(null);
									moneyTotal.setGroupName(null);
									moneyTotal.setUserid(null);
									moneyTotal.setState(0);
									moneyTotal.setUseAmount("0");
									moneyTotal.setSub_overAmount(moneyTotal.getAmount());
									moneyTotal.setOverAmount(moneyTotal.getAmount());
									moneyTotal.setInformation("");
									moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了冲正操作");
									claimForFundsService.updateMoneyTotal(moneyTotal);
									//冲正成功后删除划拨资金信息
									monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
									for (int a = 0 ; a < monList.size(); a++) {
										claimForFundsService.deleteMoneyTotal(monList.get(a).getId());
									}
									Write(returnPars(1, "", "户名为("+moneyTotal.getOtherName()+")的资金数据已冲正成功;如需使用请重新认领"));
								}else {
									moneyTotal.setState(3);
									moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"，进行了冲正操作,推送财务接口异常，财务报文：【"+result+" 】");
									claimForFundsService.updateMoneyTotal(moneyTotal);
									Write(returnPars(-1, "", "亲爱的同事,冲正认领资金未完成,修改财务数据失败，请联系管理员处理!"));
								}
							}else {
								moneyTotal.setState(4);
								moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
								claimForFundsService.updateMoneyTotal(moneyTotal);
								Write(returnPars(-1, "", "亲爱的同事,冲正主资金失败,失败信息:"+JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG")));
							}
						}else{
							moneyTotal.setState(4);
							moneyTotal.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
							claimForFundsService.updateMoneyTotal(moneyTotal);
							Write(returnPars(-1, "", "亲爱的同事,冲正主资金失败,失败信息:调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】"));
						}
					}else {
						List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
						if (mapList==null){
							Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
							return;
						}
						String county = mapList.get(0).get("COUNTY_NAME");
						String region = mapList.get(0).get("COMPANY_NAME");
						String fullName = mapList.get(0).get("EMPLOYEE_NAME");
						Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("2",moneyTotal.getSerialNo(),county,region,fullName);
						logger.info("财务接口调用结果===>"+result.toString());
						if(ResultCode.SUCCESS.code()==result.getCode()){
							moneyTotal.setPushUserName(null);
							moneyTotal.setPushBossUserName(null);
							moneyTotal.setIsThe(0);
							moneyTotal.setGroupCode(null);
							moneyTotal.setGroupName(null);
							moneyTotal.setUserid(null);
							moneyTotal.setState(0);
							moneyTotal.setUseAmount("0");
							moneyTotal.setSub_overAmount(moneyTotal.getAmount());
							moneyTotal.setOverAmount(moneyTotal.getAmount());
							moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了冲正操作");
							moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
							claimForFundsService.updateMoneyTotal(moneyTotal);
							//冲正成功后删除划拨资金信息
							monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
							for (int a = 0 ; a < monList.size(); a++) {
								claimForFundsService.deleteMoneyTotal(monList.get(a).getId());
							}
							Write(returnPars(1, "", "户名为("+moneyTotal.getOtherName()+")的资金数据已冲正成功;如需使用请重新认领"));
						}else {
							moneyTotal.setState(3);
							moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"，进行了冲正操作,推送财务接口异常，财务报文：【"+result+" 】");
							claimForFundsService.updateMoneyTotal(moneyTotal);
							Write(returnPars(-1, "", "亲爱的同事,冲正认领资金未完成,修改财务数据失败，请联系管理员处理!"));
						}
					}
				}
			}else {
				Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(moneyTotal.getBatchNo(),pushBossUser.getBossUserName());
				if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
					JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
					JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
					if ("0".equals(root.getString("RETURN_CODE"))) {
						moneyTotal.setBak1(moneyTotal.getBatchNo());
						moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
						//boss推送成功后调用财务接口修改财务数据，实现实时冲正
						List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
						if (mapList==null){
							Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
							return;
						}
						String county = mapList.get(0).get("COUNTY_NAME");
						String region = mapList.get(0).get("COMPANY_NAME");
						String fullName = mapList.get(0).get("EMPLOYEE_NAME");
						Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("2",moneyTotal.getSerialNo(),county,region,fullName);
						logger.info("财务接口调用结果===>"+result.toString());
						if(ResultCode.SUCCESS.code()==result.getCode()){
							moneyTotal.setPushUserName(null);
							moneyTotal.setPushBossUserName(null);
							moneyTotal.setIsThe(0);
							moneyTotal.setGroupCode(null);
							moneyTotal.setGroupName(null);
							moneyTotal.setUserid(null);
							moneyTotal.setState(0);
							moneyTotal.setUseAmount("0");
							moneyTotal.setSub_overAmount(moneyTotal.getAmount());
							moneyTotal.setOverAmount(moneyTotal.getAmount());
							moneyTotal.setInformation("");
							moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了冲正操作");
							claimForFundsService.updateMoneyTotal(moneyTotal);
							Write(returnPars(1, "", "户名为("+moneyTotal.getOtherName()+")的资金数据已冲正成功;如需使用请重新认领"));
						}else {
							moneyTotal.setState(3);
							moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"，进行了冲正操作,推送财务接口异常，财务报文：【"+result+" 】");
							claimForFundsService.updateMoneyTotal(moneyTotal);
							Write(returnPars(-1, "", "亲爱的同事,冲正认领资金未完成,修改财务数据失败，请联系管理员处理!"));
						}
					}else {
						logger.info("资金账户冲正失败======================="+JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
						moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
						claimForFundsService.updateMoneyTotal(moneyTotal);
						Write(returnPars(-1, "", "资金入账接口反馈异常【" + JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG") + "】"));
					}
				}else{
					Write(returnPars(-1, "", "调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】"));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("认领冲正异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，认领冲正异常："+e.getMessage()+",请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 认领冲正再次推送(冲正划拨资金时   如果存在未推送成功时使用)
	 * <AUTHOR>
	 * @Date 2022/6/13 15:56
	 **/
	public void reChargeUnitAccountRectificPushation() {
		try {
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser users = claimForFundsService.querUsers(userPhoneNum);
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			SystemUser pushBossUser = systemUserService.getByUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}
			List<MoneyTotal> monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
			//循环冲正子集团
			int num = 0;
			for (int i = 0, monListSize = monList.size(); i < monListSize; i++) {
				MoneyTotal total = monList.get(i);
				if (total.getState().equals(3)) {
					SystemUser subPushUser = systemUserService.getByUserInfoRowNo(total.getPushUserName());
					Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(total.getBatchNo(), subPushUser.getBossUserName());
					if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
						JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
						JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
						if ("0".equals(root.getString("RETURN_CODE"))) {
							total.setState(-1);
							total.setBoss_Msg("上次操作人：" + users.getEmployeeName() + "在" + getStringDatetwo(new Date()) + "进行了冲正操作");
							claimForFundsService.updateMoneyTotal(total);
						} else {
							num += 1;
							logger.info("子集团资金冲正失败:" + JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
							total.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
							claimForFundsService.updateMoneyTotal(total);
						}
					} else {
						num += 1;
						logger.info("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
						total.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
						claimForFundsService.updateMoneyTotal(total);
					}
				}
			}
			if (num>0){
				moneyTotal.setState(4);
				moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了冲正操作,尚有"+num+"条划拨资金未完成冲正!");
				claimForFundsService.updateMoneyTotal(moneyTotal);
				Write(returnPars(-1, "", "亲爱的同事,冲正划拨资金未完成,尚有"+num+"条划拨资金未完成冲正!"));
			}else {
				Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(moneyTotal.getBatchNo(),pushBossUser.getBossUserName());
				if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
					JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
					JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
					if ("0".equals(root.getString("RETURN_CODE"))) {
						moneyTotal.setBak1(moneyTotal.getBatchNo());
						moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
						//boss推送成功后调用财务接口修改财务数据，实现实时冲正
						List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
						if (mapList==null){
							Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
							return;
						}
						String county = mapList.get(0).get("COUNTY_NAME");
						String region = mapList.get(0).get("COMPANY_NAME");
						String fullName = mapList.get(0).get("EMPLOYEE_NAME");
						Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("2",moneyTotal.getSerialNo(),county,region,fullName);
						logger.info("财务接口调用结果===>"+result.toString());
						if(ResultCode.SUCCESS.code()==result.getCode()){
							moneyTotal.setPushUserName(null);
							moneyTotal.setPushBossUserName(null);
							moneyTotal.setIsThe(0);
							moneyTotal.setGroupCode(null);
							moneyTotal.setGroupName(null);
							moneyTotal.setUserid(null);
							moneyTotal.setState(0);
							moneyTotal.setUseAmount("0");
							moneyTotal.setSub_overAmount(moneyTotal.getAmount());
							moneyTotal.setOverAmount(moneyTotal.getAmount());
							moneyTotal.setInformation("");
							moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了冲正操作");
							claimForFundsService.updateMoneyTotal(moneyTotal);
							//冲正成功后删除划拨资金信息
							monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
							for (int a = 0 ; a < monList.size(); a++) {
								claimForFundsService.deleteMoneyTotal(monList.get(a).getId());
							}
							Write(returnPars(1, "", "户名为("+moneyTotal.getOtherName()+")的资金数据已冲正成功;如需使用请重新认领"));
						}else {
							moneyTotal.setState(3);
							moneyTotal.setBoss_Msg("上次操作人："+users.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了冲正操作,推送财务接口异常，财务报文：【"+result+" 】");
							claimForFundsService.updateMoneyTotal(moneyTotal);
							Write(returnPars(-1, "", "亲爱的同事,冲正认领资金未完成,修改财务数据失败，请联系管理员处理!"));
						}
					}else {
						moneyTotal.setState(4);
						moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
						claimForFundsService.updateMoneyTotal(moneyTotal);
						Write(returnPars(-1, "", "亲爱的同事,冲正主资金失败,失败信息:"+JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG")));
					}
				}else{
					moneyTotal.setState(4);
					moneyTotal.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
					claimForFundsService.updateMoneyTotal(moneyTotal);
					Write(returnPars(-1, "", "亲爱的同事,冲正主资金失败,失败信息:调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】"));
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("认领冲正再次推送异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，认领冲正再次推送异常："+e.getMessage()+",请联系管理员处理！"));
		}
	}

	//-----------------------------------------------------------跨月冲正-------------------------------------------------------
	/**
	 * @Description: 资金认领流程进行方法
	 * @return: void
	 * @Date: 2021/11/9 10:26
	 */
	public void spanMonthHandleClaimData() {
		try {
			String id = getString("id");//资金认领id
			String opinion = getString("opinion");//审批意见
			Integer userid = getInteger("userId");//下一步处理用户id
			String userName = getString("userName");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常【"+userPhoneNum+" 】,请刷新页面重试或联系管理员处理！"));
				throw new RuntimeException("用户信息异常");
			}
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			int lateFee=0;
			if ("3".equals(my.getMa_type())){
				List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(my.getApplyNo());
				for(int i=0;i<moneyApplyDet.size();i++){
					if("1.00".equals(moneyApplyDet.get(i).getLateFee())){
						lateFee=1;
						break;
					}
				}
			}

			Map<String, String> map = new HashMap<>();
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
			String code =claimForFundsService.getVwUserinf(my.getCreatorId()).get(0).get("COMPANY_CODE");
			if ("区县政企部主任".equals(task.getActivityName())) {
				if ("3".equals(my.getMa_type())){
					TransferCitiesData transferCitiesData= claimForFundsService.getTransferCitiesData(code,task.getActivityName());
					LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,task.getActivityName());
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 >= Double.parseDouble(transferCitiesData.getAmount())
							&& Double.parseDouble(my.getLateFeeMoney())/100>Double.parseDouble(lateFeeMoneyData.getAmount())) {
						map.put("node", "金额大于等于10000");
					}else{
						map.put("node", "END");
					}
				}else {
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 >= Double.parseDouble("10000")) {
						map.put("decisionKey", "ROLE_QXSM");
						map.put("decisionValue", "NO");
					}else{
						map.put("decisionKey", "ROLE_QXSM");
						map.put("decisionValue", "YES");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);//流程流转
			} else if ("省重客客户经理室经理".equals(task.getActivityName())) {
				if ("3".equals(my.getMa_type())){
					TransferCitiesData transferCitiesData= claimForFundsService.getTransferCitiesData(code,task.getActivityName());
					LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,task.getActivityName());
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 > Double.parseDouble(transferCitiesData.getAmount())
							&& Double.parseDouble(my.getLateFeeMoney())/100>Double.parseDouble(lateFeeMoneyData.getAmount())) {
						if(lateFee==1){
							map.put("node", "金额和滞纳金大于设定金额并且减免滞纳金");
						}else{
							map.put("node", "金额和滞纳金大于设定金额并且不减免滞纳金");
						}
					}else{
						map.put("node", "END");
					}
				}else {
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 >= Double.parseDouble("50000000")) {
						map.put("decisionKey", "ROLE_SZKSM");
						map.put("decisionValue", "NO");
					}else{
						map.put("decisionKey", "ROLE_SZKSM");
						map.put("decisionValue", "YES");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);//流程流转
			} else if ("市公司客户经理室经理".equals(task.getActivityName())) {
				if ("3".equals(my.getMa_type())){
					TransferCitiesData transferCitiesData= claimForFundsService.getTransferCitiesData(code,task.getActivityName());
					LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,task.getActivityName());
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 > Double.parseDouble(transferCitiesData.getAmount())
							&& Double.parseDouble(my.getLateFeeMoney())/100>Double.parseDouble(lateFeeMoneyData.getAmount())) {
						if(lateFee==1){
							map.put("node", "金额和滞纳金大于设定金额并且减免滞纳金");
						}else{
							map.put("node", "金额和滞纳金大于设定金额并且不减免滞纳金");
						}
					}else{
						map.put("node", "END");
					}
				}else {
					if (Double.parseDouble(moneyTotal.getAmount()) / 100 >= Double.parseDouble("10000")) {
						map.put("decisionKey", "ROLE_DSDM");
						map.put("decisionValue", "NO");
					}else{
						map.put("decisionKey", "ROLE_DSDM");
						map.put("decisionValue", "YES");
					}
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if("区县分管经理".equals(task.getActivityName())){
				if(lateFee==1){
					map.put("node", "减免滞纳金");
				}else{
					map.put("node", "END");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else if("市公司政企部经理".equals(task.getActivityName())){
				LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,task.getActivityName());
				if(Double.parseDouble(my.getLateFeeMoney())/100>Double.parseDouble(lateFeeMoneyData.getAmount())){
					map.put("node", "滞纳金大于设定金额");
				}else{
					map.put("node", "END");
				}
				jbpmUtil.completeTask(task.getId(), map);
			}else{
				jbpmUtil.completeTask(task.getId());
			}
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();//获取流程任务表信息
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			service.updateWait(wt, this.getRequest());
			String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", tasktwo.getActivityName(), userid, user);
			JSONObject obj = taskService.getApproval_message(my.getApplyNo(), BigDecimal.
							valueOf(Long.parseLong(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
					task.getActivityName(), opinion, userid, user);
			taskService.setReminder_information_tbl(my.getGroupCode(), "1", "30", "资金认领", obj.toString(), "资金认领");
			commitBackLogPachMonth(my, userid, process.getProcess_sign(), user, rtaskid);// 生成待办
			Write(returnPars(1,"","亲爱的同事，工单处理完成，现已提交至："+userName+"处!"));
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金认领错误信息：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,系统处理异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}


	/**
	 * @Description TODO 跨月资金冲正完成方法(先冲正再进行认领)
	 * <AUTHOR>
	 * @Date 2022/4/13 15:12
	 **/
	public void spanMonthComplateClaimData() {
		try {
			String id = getString("id");//开票id
			String opinion = getString("opinion");//审批意见
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常【"+userPhoneNum+" 】,请刷新页面重试或联系管理员处理！"));
				throw new RuntimeException("用户信息异常");
			}
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息

			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}
			if(tasktwo==null){
				Write(returnPars(-1,"","亲爱的同事,流程实例["+process.getProcess_sign()+"]异常,请联系系统管理员"));
				return;
			}

			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());  //申请工单对应总资金信息
			List<MoneyTotal> monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());

			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
			if (mapList==null){
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			String county = mapList.get(0).get("COUNTY_NAME");
			String region = mapList.get(0).get("COMPANY_NAME");
			String fullName = mapList.get(0).get("EMPLOYEE_NAME");
			Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("9",moneyTotal.getSerialNo(),county,region,fullName);
			logger.info("财务接口调用结果===>"+result.toString());
			if(ResultCode.SUCCESS.code()==result.getCode()) {
				JSONObject res = JSONObject.fromObject(result.getData());
				if ("success".equals(res.getString("code"))) {
					//判断是否存在划拨资金
					int num = 0;
					if (monList.size()>0){
						//如果存在先循环冲正子集团
						for (int i = 0, monListSize = monList.size(); i < monListSize; i++) {
							MoneyTotal total = monList.get(i);
							SystemUser subUser = systemUserService.getUserInfoRowNo(total.getPushUserName());
							Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(total.getBatchNo(), subUser.getBossUserName());
							if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
								JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
								JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
								if ("0".equals(root.getString("RETURN_CODE"))) {
									total.setState(-1);
									total.setBoss_Msg("上次操作在" + getStringDatetwo(new Date()) + "进行了跨月冲正操作");
									claimForFundsService.updateMoneyTotal(total);
								} else {
									num += 1;
									logger.info("子集团资金冲正失败:" + JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
									total.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
									claimForFundsService.updateMoneyTotal(total);
								}
							} else {
								num += 1;
								logger.info("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
								total.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
								claimForFundsService.updateMoneyTotal(total);
							}
						}
						if (num>0){
							moneyTotal.setBoss_Msg("上次操作在"+getStringDatetwo(new Date())+"进行了跨月冲正操作,尚有"+num+"条划拨资金未完成冲正!");
							claimForFundsService.updateMoneyTotal(moneyTotal);
						}else {
							if (!moneyTotal.getSub_overAmount().equals("0")){
								Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(moneyTotal.getBatchNo(),pushBossUser.getBossUserName());
								if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
									JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
									JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
									if ("0".equals(root.getString("RETURN_CODE"))) {
										moneyTotal.setPushUserName(null);
										moneyTotal.setPushBossUserName(null);
										moneyTotal.setIsThe(0);
										moneyTotal.setGroupCode(null);
										moneyTotal.setGroupName(null);
										moneyTotal.setUserid(null);
										moneyTotal.setState(2);
										moneyTotal.setUseAmount("0");
										moneyTotal.setSub_overAmount(moneyTotal.getAmount());
										moneyTotal.setOverAmount(moneyTotal.getAmount());
										moneyTotal.setInformation("");
										moneyTotal.setBoss_Msg("上次操作人："+user.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了跨月冲正操作，暂未认领");
										moneyTotal.setBak1(moneyTotal.getBatchNo());
										moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
										claimForFundsService.updateMoneyTotal(moneyTotal);
									}else {
										num += 1;
										moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
										claimForFundsService.updateMoneyTotal(moneyTotal);
									}
								}else{
									num += 1;
									moneyTotal.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
									claimForFundsService.updateMoneyTotal(moneyTotal);
								}
							}else {
								moneyTotal.setPushUserName(null);
								moneyTotal.setPushBossUserName(null);
								moneyTotal.setIsThe(0);
								moneyTotal.setGroupCode(null);
								moneyTotal.setGroupName(null);
								moneyTotal.setUserid(null);
								moneyTotal.setState(2);
								moneyTotal.setUseAmount("0");
								moneyTotal.setSub_overAmount(moneyTotal.getAmount());
								moneyTotal.setOverAmount(moneyTotal.getAmount());
								moneyTotal.setBoss_Msg("上次操作人："+user.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了跨月冲正操作，暂未认领");
								moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
								claimForFundsService.updateMoneyTotal(moneyTotal);
							}
						}
					}else {
						Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(moneyTotal.getBatchNo(),pushBossUser.getBossUserName());
						if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
							JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
							JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
							if ("0".equals(root.getString("RETURN_CODE"))) {
								moneyTotal.setPushUserName(null);
								moneyTotal.setPushBossUserName(null);
								moneyTotal.setIsThe(0);
								moneyTotal.setGroupCode(null);
								moneyTotal.setGroupName(null);
								moneyTotal.setUserid(null);
								moneyTotal.setState(2);
								moneyTotal.setUseAmount("0");
								moneyTotal.setOverAmount(moneyTotal.getAmount());
								moneyTotal.setSub_overAmount(moneyTotal.getAmount());
								moneyTotal.setInformation("");
								moneyTotal.setBoss_Msg("上次操作人："+user.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了跨月冲正操作，暂未认领");
								moneyTotal.setBak1(moneyTotal.getBatchNo());
								moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
								claimForFundsService.updateMoneyTotal(moneyTotal);
							}else {
								num += 1;
								logger.info("资金账户冲正失败："+JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
								moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
								claimForFundsService.updateMoneyTotal(moneyTotal);
							}
						}else{
							num += 1;
							moneyTotal.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
							claimForFundsService.updateMoneyTotal(moneyTotal);
						}
					}

					SystemUser pushUsers = systemUserService.getUserInfoRowNo(Integer.parseInt(my.getCreatorId()));
					int pushUserRow = pushUsers.getRowNo();
					String pushUserName = pushUsers.getBossUserName();
					try {
						List<Map<String, Object>> map = claimForFundsService.findDept(pushUserRow);
						String county_name = map.get(0).get("TWODNAME").toString();
						String company_name = map.get(0).get("COMPANY_NAME").toString();
						List<Map<String, String>> sd = claimForFundsService.SelectZtreeByUId("ROLE_ZJRLSPGLY", company_name, county_name);
						if (sd.size() == 1) {
							if (!"".equals(sd.get(0).get("BOSSUSERNAME")) && sd.get(0).get("BOSSUSERNAME") != null && !"null".equals(sd.get(0).get("BOSSUSERNAME"))) {
								pushUserRow = Integer.parseInt(String.valueOf(sd.get(0).get("ROWNO")));
								pushUserName = sd.get(0).get("BOSSUSERNAME");
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
						pushUserRow = pushUsers.getRowNo();
						pushUserName = pushUsers.getBossUserName();
					}
					if (my.getMa_type().equals("1")){        //仅认领
						moneyTotal.setOverAmount(moneyTotal.getOverAmount());
						moneyTotal.setUseAmount("0");
						moneyTotal.setSub_overAmount(moneyTotal.getOverAmount());
					}else if (my.getMa_type().equals("2")){  //认领并划拨
						JSONArray groupJsonArray = JSONArray.fromObject(my.getMoneyTotalId());
						for (int i = 0; i < groupJsonArray.size(); i++) {
							String s = groupJsonArray.getString(i);
							JSONObject data2 = JSONObject.fromObject(s);
							SystemUser pushUser = systemUserService.getByUserInfoRowNo(Integer.parseInt(data2.getString("userId")));
							int subPushUserRow = pushUser.getRowNo();
							String subPushUserName = pushUser.getBossUserName();
							try {
								List<Map<String, Object>> map = claimForFundsService.findDept(Integer.parseInt(data2.getString("userId")));
								String county_name = map.get(0).get("TWODNAME").toString();
								String company_name = map.get(0).get("COMPANY_NAME").toString();
								List<Map<String, String>> sd = claimForFundsService.SelectZtreeByUId("ROLE_ZJRLSPGLY", company_name, county_name);
								if (sd.size() == 1) {
									if (!"".equals(sd.get(0).get("BOSSUSERNAME")) && sd.get(0).get("BOSSUSERNAME") != null && !"null".equals(sd.get(0).get("BOSSUSERNAME"))) {
										subPushUserRow = Integer.valueOf(String.valueOf(sd.get(0).get("ROWNO")));
										subPushUserName = sd.get(0).get("BOSSUSERNAME");
									}
								}
							} catch (Exception e) {
								e.printStackTrace();
								subPushUserRow = pushUser.getRowNo();
								subPushUserName = pushUser.getBossUserName();
							}
							//2、入表子集团的申请金额
							MoneyTotal subMoneyTotal = new MoneyTotal();
							subMoneyTotal.setSerialNo(moneyTotal.getSerialNo());//(唯一标识号)
							subMoneyTotal.setOtherAccNumber(moneyTotal.getOtherAccNumber());//(对方账号)
							subMoneyTotal.setOtherName(moneyTotal.getOtherName());//(对方户名)
							subMoneyTotal.setOtherBank(moneyTotal.getOtherBank());//(对方开户行)
							subMoneyTotal.setAccNumber(moneyTotal.getAccNumber());//(公司账号)
							subMoneyTotal.setTranDate(moneyTotal.getTranDate());//strs[5]//(交易时间)
							subMoneyTotal.setAmount(data2.getString("amount"));//(转账金额)
							subMoneyTotal.setUseAmount("0");//(使用金额)
							subMoneyTotal.setOverAmount(data2.getString("amount"));//(剩余金额)
							subMoneyTotal.setReceiverSCompany(moneyTotal.getReceiverSCompany());
							subMoneyTotal.setMemo(moneyTotal.getMemo());//(摘要注释)
							subMoneyTotal.setUseMemo(moneyTotal.getUseMemo());//(用途注释)
							subMoneyTotal.setCompanyCode(moneyTotal.getCompanyCode());//(地市编码)
							subMoneyTotal.setCompanyName(moneyTotal.getCompanyName());//(地市名称)
							subMoneyTotal.setCreateDate(new Date());//(创建时间)
							subMoneyTotal.setState(5);//(状态)
							subMoneyTotal.setIsThe(0);//记录申领次数；
							subMoneyTotal.setTypeUserId(moneyTotal.getTypeUserId());
							subMoneyTotal.setType(1);
							subMoneyTotal.setGroupCode(data2.getString("groupCode"));
							subMoneyTotal.setGroupName(data2.getString("groupName"));
							subMoneyTotal.setIs_sub_group(1);
							subMoneyTotal.setUserid(Integer.parseInt(data2.getString("userId")));
							subMoneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo()) + taskService.getNumber());//(系统编号)
							subMoneyTotal.setHour_min_second(moneyTotal.getHour_min_second());
							subMoneyTotal.setSub_overAmount(data2.getString("amount"));
							subMoneyTotal.setPushUserName(subPushUserRow);
							subMoneyTotal.setPushBossUserName(subPushUserName);
							subMoneyTotal = claimForFundsService.saveProcessList(subMoneyTotal);
							JSONObject obj = taskService.getInitial_message(data2.getString("amount"), "", Integer.parseInt(data2.getString("userId")));
							taskService.setReminder_information_tbl(data2.getString("groupCode"), "1", "32", "资金认领", obj.toString(), "资金子集团分配");
							/**
							 * 保存分配集团信息详细信息对应申请工单
							 */
							SystemUser user_sub = systemUserService.getByUserInfoRowNo(Integer.parseInt(data2.getString("userId")));
							MoneyApplySubGroup moneyApplySubGroup = new MoneyApplySubGroup();
							moneyApplySubGroup.setApply_no("KY" + taskService.getNumber());
							moneyApplySubGroup.setGroup_code(data2.getString("groupCode"));
							moneyApplySubGroup.setGroup_name(data2.getString("groupName"));
							moneyApplySubGroup.setAmount(data2.getString("amount"));
							moneyApplySubGroup.setSerial_no(moneyTotal.getSerialNo());
							moneyApplySubGroup.setMoneyTotal_id(subMoneyTotal.getId());
							moneyApplySubGroup.setMoneyApply_id(my.getId());
							moneyApplySubGroup.setUser_name(user_sub.getEmployeeName());
							moneyApplySubGroup.setUser_id(String.valueOf(user_sub.getRowNo()));
							moneyApplySubGroup.setCrateUser_id(my.getCreatorId());
							moneyApplySubGroup.setCrate_date(new Date());
							moneyApplySubGroup.setBoss_State(0); //默认设置为0，方式字段为空等问题
							claimForFundsService.saveMoneyApplySubGroup(moneyApplySubGroup);
						}
						//分配完成后调整原认领记录的金额
						moneyTotal.setOverAmount(Long.parseLong(moneyTotal.getOverAmount()) - Long.parseLong(my.getApplyAmount()) + "");
						moneyTotal.setUseAmount(Long.parseLong(my.getApplyAmount()) + "");
						moneyTotal.setSub_overAmount(moneyTotal.getOverAmount());
					}
					moneyTotal.setUserid(Integer.valueOf(my.getCreatorId()));
					moneyTotal.setGroupCode(my.getGroupCode());
					moneyTotal.setGroupName(my.getGroupName());
					moneyTotal.setPushUserName(pushUserRow);
					moneyTotal.setPushBossUserName(pushUserName);
					claimForFundsService.updateMoneyTotal(moneyTotal);
					moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());  //申请工单对应总资金信息
					if (num<=0){
						moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());  //申请工单对应总资金信息
						if (!"0".equals(moneyTotal.getSub_overAmount())){        //仅认领
							pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
							String op_fee = BigDecimal.valueOf(Long.parseLong(moneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
							Result accountRes = ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(pushBossUser.getBossUserName(), moneyTotal.getGroupCode(),
									moneyTotal.getBatchNo(), moneyTotal.getOtherAccNumber(), moneyTotal.getOtherName(), moneyTotal.getUseMemo(), moneyTotal.getSerialNo(), op_fee);
							logger.info("调用资金入账接口反馈===>" + accountRes.toString());
							if (ResultCode.SUCCESS.code() == accountRes.getCode()) {  //判断当前请求是否成功
								JSONObject accountResObj = JSONObject.fromObject(accountRes.getData());
								moneyTotal.setIsThe(moneyTotal.getIsThe() + 1);
								claimForFundsService.updateMoneyTotal(moneyTotal);
								//推送BOSS账户资金结果判断
								if ("0".equals(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_CODE"))) {
									moneyTotal.setState(1);
									moneyTotal.setBoss_State(1); //设置BOSS状态为成功
									moneyTotal.setBoss_Msg(DateUtil.getDate());
									moneyTotal = claimForFundsService.updateMoneyTotal(moneyTotal);
								} else {
									num += 1;
									moneyTotal.setBoss_State(0);
									moneyTotal.setBoss_Msg(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_MSG"));
									moneyTotal = claimForFundsService.updateMoneyTotal(moneyTotal);
								}
							} else {
								num += 1;
								moneyTotal.setBoss_State(0);
								moneyTotal.setBoss_Msg(accountRes.getMessage());
								moneyTotal = claimForFundsService.updateMoneyTotal(moneyTotal);
							}
						}else {
							moneyTotal.setState(1);
							moneyTotal.setBoss_State(1); //设置BOSS状态为成功
							moneyTotal = claimForFundsService.updateMoneyTotal(moneyTotal);
						}

						//循环推送子集团信息
						List<MoneyApplySubGroup> subGroupList = claimForFundsService.getMoneyApplySubGroup(my.getId());
						if (num<=0 && subGroupList!=null){
							//实现循环推送BOSS入账，如果调用接口失败则进行下一条推送。将整体子集团认领数据推送一次
							for (int m = 0; m < subGroupList.size(); m++) {
								MoneyApplySubGroup moneyApplySubGroup=subGroupList.get(m);
								MoneyTotal subMoneyTotal = claimForFundsService.getMoneyTotal(moneyApplySubGroup.getMoneyTotal_id());
								if(subMoneyTotal.getState().equals(5) && subMoneyTotal.getIsThe().equals(0)){ //判断数据是否推送过BOSS，如果未推送则执行推送接口
									SystemUser subUser = systemUserService.getUserInfoRowNo(subMoneyTotal.getPushUserName());
									String sub_op_fee=BigDecimal.valueOf(Long.parseLong(subMoneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
									Result accountsubRes=ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(subUser.getBossUserName(),subMoneyTotal.getGroupCode(),
											subMoneyTotal.getBatchNo(),subMoneyTotal.getOtherAccNumber(),subMoneyTotal.getOtherName(),subMoneyTotal.getUseMemo(),subMoneyTotal.getSerialNo(),sub_op_fee);
									logger.info("调用资金入账接口反馈===>"+accountsubRes.toString());
									if(ResultCode.SUCCESS.code()==accountsubRes.getCode()){  //判断当前请求是否成功
										JSONObject accountsubResObj=JSONObject.fromObject(accountsubRes.getData());
										subMoneyTotal.setIsThe(subMoneyTotal.getIsThe() + 1);
										subMoneyTotal=	claimForFundsService.updateMoneyTotal(subMoneyTotal);
										//推送BOSS账户资金结果判断
										if("0".equals(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_CODE"))){
											subMoneyTotal.setState(1);
											subMoneyTotal.setBoss_State(1); //设置BOSS状态为成功
											subMoneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
											subMoneyTotal.setPushDate(new Date());
											moneyApplySubGroup.setBoss_State(1);
											moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										}else{
											num+=1;
											subMoneyTotal.setBoss_State(0);
											subMoneyTotal.setBoss_Msg(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG"));
											moneyApplySubGroup.setBoss_State(0);
											moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
											logger.info("资金入账接口反馈异常【"+JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG")+"】");
										}
									}else{
										num+=1;
										subMoneyTotal.setBoss_State(0);
										subMoneyTotal.setBoss_Msg(accountsubRes.getMessage());
										moneyApplySubGroup.setBoss_State(0);
										moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										logger.info("调用BOSS资金入账接口异常【"+accountsubRes.getMessage()+"】");
									}
									claimForFundsService.updateMoneyTotal(subMoneyTotal);
									claimForFundsService.updateMoneyApplySubGroup(moneyApplySubGroup);
								}
							}
						}
					}

					if (num > 0) {
						my.setState("3");
					} else {
						//冲正成功后删除划拨资金信息
						for (int a = 0 ; a < monList.size(); a++) {
							claimForFundsService.deleteMoneyTotal(monList.get(a).getId());
						}
						my.setState("0");
					}
					claimForFundsService.updateMoneyApply(my);

					Map<String, String> map = new HashMap<>();
					if (my.getMa_type().equals("3")){
						if(tasktwo.getActivityName().contains("代理缴费")){
							jbpmUtil.completeTask(tasktwo.getId(), "END");
						}else {
							map.put("node", "END");
							jbpmUtil.completeTask(tasktwo.getId(), map);
						}
					}else {
						String t = "";
						if ("区县政企部主任".equals(tasktwo.getActivityName())) {
							t = "ROLE_QXSM";
							map.put("decisionKey", t);
							map.put("decisionValue", "YES");
							jbpmUtil.completeTask(tasktwo.getId(), map, t);
						} else if ("市公司客户经理室经理".equals(tasktwo.getActivityName())) {
							t = "ROLE_DSDM";
							map.put("decisionKey", t);
							map.put("decisionValue", "YES");
							jbpmUtil.completeTask(tasktwo.getId(), map, t);
						} else if ("省重客客户经理室经理".equals(tasktwo.getActivityName())) {
							t = "ROLE_SZKSM";
							map.put("decisionKey", t);
							map.put("decisionValue", "YES");
							jbpmUtil.completeTask(tasktwo.getId(), map, t);
						} else {
							jbpmUtil.completeTask(tasktwo.getId(), "END");
						}
					}
					taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
					service.updateWait(wt, this.getRequest());
					claimForFundsService.updateMoneyApply(my);
					JSONObject obj = taskService.getCompleted_message(my.getApplyNo(), BigDecimal.
									valueOf(Long.parseLong(my.getApplyAmount())).divide(new BigDecimal(100)).toString(),
							tasktwo.getActivityName(), opinion, user.getRowNo());
					taskService.setReminder_information_tbl(my.getGroupCode(), "1", "30", "资金认领", obj.toString(), "资金认领");
					String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", "起草人", Integer.valueOf(my.getCreatorId()), user);
					commitBackLogPachMonth(my, Integer.parseInt(my.getCreatorId()), process.getProcess_sign(), user, rtaskid);// 生成待办
					Write(returnPars(1,"","亲爱的同事,工单处理完成!"));
				}else {
					Write(returnPars(-1,"","亲爱的同事,推送财务稽核反馈异常【"+res.getString("info")+"】,请核实!"));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事,推送财务稽核异常【"+result.getMessage()+"】,请稍后重试或联系管理员处理!"));
			}
		} catch (Exception e) {
			logger.info("资金认领跨月冲正错误信息：" + e.getMessage(), e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,系统处理异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 跨月冲正退回
	 * <AUTHOR>
	 * @Date 2022/4/14 10:27
	 **/
	public void spanMonthReturnClaimData() {
		try {
			String id = getString("id");// 开票id
			String opinion = getString("opinion");// 退回意见
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常【"+userPhoneNum+" 】,请刷新页面重试或联系管理员处理！"));
				throw new RuntimeException("用户信息异常");
			}
			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			my.setState("2");// 修改状态为退回
			MoneyApply may = claimForFundsService.updateMoneyApply(my);
			taskService.updateBpms_riskoff_task(opinion, 2, btask.getId());
			service.updateWait(wt, this.getRequest());
			String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", "起草人", Integer.valueOf(my.getCreatorId()), user);
			commitBackLogPachMonth(may, Integer.parseInt(may.getCreatorId()), process.getProcess_sign(), user, rtaskid);// 生成待办
			jbpmUtil.deleteProcessInstance(process.getProcess_sign());// 删除流程
			Write(returnPars(1,"","亲爱的同事,工单处理完成，工单已退回发起人!"));
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金认领错误信息：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,系统处理异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description: 跨月冲正 工单作废方法
	 * @return: void
	 * @Date: 2021/11/9 10:29
	 */
	public void spanMonthInvalidClaimData() {
		try {
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常【"+userPhoneNum+" 】,请刷新页面重试或联系管理员处理！"));
				throw new RuntimeException("用户信息异常");
			}
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			if (moneyTotal.getState().equals(3)){

				List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
				if (mapList==null){
					Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
					return;
				}
				String county = mapList.get(0).get("COUNTY_NAME");
				String region = mapList.get(0).get("COMPANY_NAME");
				String fullName = mapList.get(0).get("EMPLOYEE_NAME");
				Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("7",moneyTotal.getSerialNo(),county,region,fullName);
				logger.info("财务接口调用结果===>"+result.toString());
				if(ResultCode.SUCCESS.code()==result.getCode()){
					List<MoneyTotal> moneyTotalList = claimForFundsService.getMoneyTotalSerialNoList(moneyTotal.getSerialNo());
					for (MoneyTotal value : moneyTotalList) {
						if (value.getState().equals(3)){
							value.setState(1);
							claimForFundsService.updateMoneyTotal(value);
						}else {
							Write(returnPars(-1,"","亲爱的同事,资金数据状态异常，请联系管理员处理！"));
							throw new Exception("资金数据状态异常");
						}
					}
				}else {
					moneyTotal.setState(3);
					moneyTotal.setBoss_Msg("推送财务接口异常，财务报文：【"+result+" 】");
					claimForFundsService.updateMoneyTotal(moneyTotal);
					Write(returnPars(-1, "", "亲爱的同事,修改财务数据失败，请联系管理员处理!"));
					return;
				}

				taskService.updateBpms_riskoff_task("工单已作废!", 2, btask.getId());
				my.setState("-1");//状态修改为作废
				claimForFundsService.updateMoneyApply(my);
				service.updateWait(wt, this.getRequest());
				Write(returnPars(1,"","亲爱的同事,工单处理完成，该工地已作废!"));
			}else{
				Write(returnPars(-1,"","亲爱的同事,资金数据状态异常，请联系管理员处理！"));
			}

		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金认领跨月冲正作废异常信息：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,系统处理异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 跨月冲正再次推送
	 * <AUTHOR>
	 * @Date 2022/4/14 11:07
	 **/
	public void spanMonthAgainSetClaimData() {
		try {
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常【"+userPhoneNum+" 】,请刷新页面重试或联系管理员处理！"));
				throw new RuntimeException("用户信息异常");
			}
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}

			MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
			List<MoneyTotal> monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
			SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
			if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}

			int num = 0;
			List<Map<String,String>> mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
			if (mapList==null){
				Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
				return;
			}
			String county = mapList.get(0).get("COUNTY_NAME");
			String region = mapList.get(0).get("COMPANY_NAME");
			String fullName = mapList.get(0).get("EMPLOYEE_NAME");
			Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("9",moneyTotal.getSerialNo(),county,region,fullName);
			logger.info("财务接口调用结果===>"+result.toString());
			if(ResultCode.SUCCESS.code()==result.getCode()) {
				JSONObject res = JSONObject.fromObject(result.getData());
				if ("success".equals(res.getString("code"))) {
					if (monList.size()>0){
						//如果存在先循环冲正子集团
						for (int i = 0, monListSize = monList.size(); i < monListSize; i++) {
							MoneyTotal total = monList.get(i);
							if (total.getState().equals(3)){
								SystemUser subPushBossUser = systemUserService.getUserInfoRowNo(total.getPushUserName());
								Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(total.getBatchNo(), subPushBossUser.getBossUserName());
								if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
									JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
									JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
									if ("0".equals(root.getString("RETURN_CODE"))) {
										total.setState(-1);
										total.setBoss_Msg("上次操作在" + getStringDatetwo(new Date()) + "进行了跨月冲正操作");
										claimForFundsService.updateMoneyTotal(total);
									} else {
										num += 1;
										logger.info("子集团资金冲正失败:" + JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
										total.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
										claimForFundsService.updateMoneyTotal(total);
									}
								} else {
									num += 1;
									logger.info("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
									total.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
									claimForFundsService.updateMoneyTotal(total);
								}
							}
						}

						if (num>0){
							moneyTotal.setBoss_Msg("上次操作在"+getStringDatetwo(new Date())+"进行了跨月冲正操作,尚有"+num+"条划拨资金未完成冲正!");
							claimForFundsService.updateMoneyTotal(moneyTotal);
						}else {
							if (moneyTotal.getState().equals(3)){
								if (!moneyTotal.getSub_overAmount().equals("0")){
									Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(moneyTotal.getBatchNo(),pushBossUser.getBossUserName());
									if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
										JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
										JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
										if ("0".equals(root.getString("RETURN_CODE"))) {
											moneyTotal.setPushUserName(null);
											moneyTotal.setPushBossUserName(null);
											moneyTotal.setIsThe(0);
											moneyTotal.setGroupCode(null);
											moneyTotal.setGroupName(null);
											moneyTotal.setUserid(null);
											moneyTotal.setState(2);
											moneyTotal.setUseAmount("0");
											moneyTotal.setSub_overAmount(moneyTotal.getAmount());
											moneyTotal.setOverAmount(moneyTotal.getAmount());
											moneyTotal.setInformation("");
											moneyTotal.setBoss_Msg("上次操作人："+user.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了跨月冲正操作，暂未认领");
											moneyTotal.setBak1(moneyTotal.getBatchNo());
											moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
											claimForFundsService.updateMoneyTotal(moneyTotal);
											//冲正成功后删除划拨资金信息
											monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
											for (int a = 0 ; a < monList.size(); a++) {
												claimForFundsService.deleteMoneyTotal(monList.get(a).getId());
											}
										}else {
											num += 1;
											moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
											claimForFundsService.updateMoneyTotal(moneyTotal);
										}
									}else{
										num += 1;
										moneyTotal.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
										claimForFundsService.updateMoneyTotal(moneyTotal);
									}
								}else {
									moneyTotal.setPushUserName(null);
									moneyTotal.setPushBossUserName(null);
									moneyTotal.setIsThe(0);
									moneyTotal.setGroupCode(null);
									moneyTotal.setGroupName(null);
									moneyTotal.setUserid(null);
									moneyTotal.setState(2);
									moneyTotal.setUseAmount("0");
									moneyTotal.setSub_overAmount(moneyTotal.getAmount());
									moneyTotal.setOverAmount(moneyTotal.getAmount());
									moneyTotal.setBoss_Msg("上次操作人："+user.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了跨月冲正操作，暂未认领");
									moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
									claimForFundsService.updateMoneyTotal(moneyTotal);
								}
							}
						}
					}else {
						if (moneyTotal.getState().equals(3)){
							Result cancelRechargeRes = ClaimFundsOpenSrv.getInstance().cancelRecharge(moneyTotal.getBatchNo(),pushBossUser.getBossUserName());
							if (ResultCode.SUCCESS.code() == cancelRechargeRes.getCode()) {
								JSONObject cancelRechargeObj = JSONObject.fromObject(cancelRechargeRes.getData());
								JSONObject root = JSONObject.fromObject(cancelRechargeObj.getString("ROOT"));
								if ("0".equals(root.getString("RETURN_CODE"))) {
									moneyTotal.setPushUserName(null);
									moneyTotal.setPushBossUserName(null);
									moneyTotal.setIsThe(0);
									moneyTotal.setGroupCode(null);
									moneyTotal.setGroupName(null);
									moneyTotal.setUserid(null);
									moneyTotal.setState(2);
									moneyTotal.setUseAmount("0");
									moneyTotal.setOverAmount(moneyTotal.getAmount());
									moneyTotal.setSub_overAmount(moneyTotal.getAmount());
									moneyTotal.setInformation("");
									moneyTotal.setBoss_Msg("上次操作人："+user.getEmployeeName()+"在"+getStringDatetwo(new Date())+"进行了跨月冲正操作，暂未认领");
									moneyTotal.setBak1(moneyTotal.getBatchNo());
									moneyTotal.setBatchNo(getEng(moneyTotal.getBatchNo())+taskService.getNumber());
									claimForFundsService.updateMoneyTotal(moneyTotal);
								}else {
									num += 1;
									logger.info("资金账户冲正失败："+JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
									moneyTotal.setBoss_Msg(JSONObject.fromObject(cancelRechargeObj.getString("ROOT")).getString("RETURN_MSG"));
									claimForFundsService.updateMoneyTotal(moneyTotal);
								}
							}else{
								num += 1;
								moneyTotal.setBoss_Msg("调用BOSS资金冲正接口异常【" + cancelRechargeRes.getMessage() + "】");
								claimForFundsService.updateMoneyTotal(moneyTotal);
							}
						}
					}

					moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());  //申请工单对应总资金信息
					if (num==0 && moneyTotal.getState().equals(2)){
						if (!"0".equals(moneyTotal.getSub_overAmount())){        //仅认领
							String op_fee = BigDecimal.valueOf(Long.parseLong(moneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
							Result accountRes = ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(pushBossUser.getBossUserName(), moneyTotal.getGroupCode(),
									moneyTotal.getBatchNo(), moneyTotal.getOtherAccNumber(), moneyTotal.getOtherName(), moneyTotal.getUseMemo(), moneyTotal.getSerialNo(), op_fee);
							logger.info("调用资金入账接口反馈===>" + accountRes.toString());
							if (ResultCode.SUCCESS.code() == accountRes.getCode()) {  //判断当前请求是否成功
								JSONObject accountResObj = JSONObject.fromObject(accountRes.getData());
								moneyTotal.setIsThe(moneyTotal.getIsThe() + 1);
								moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
								//推送BOSS账户资金结果判断
								if ("0".equals(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_CODE"))) {
									moneyTotal.setState(1);
									moneyTotal.setBoss_State(1); //设置BOSS状态为成功
									moneyTotal.setBoss_Msg(DateUtil.getDate());
									moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
								} else {
									num += 1;
									moneyTotal.setBoss_State(0);
									moneyTotal.setBoss_Msg(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_MSG"));
									moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
								}
							} else {
								num += 1;
								moneyTotal.setBoss_State(0);
								moneyTotal.setBoss_Msg(accountRes.getMessage());
								moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
							}
						}else {
							moneyTotal.setState(1);
							moneyTotal.setBoss_State(1); //设置BOSS状态为成功
							moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);
						}

						//循环推送子集团信息
						List<MoneyApplySubGroup> subGroupList = claimForFundsService.getMoneyApplySubGroup(my.getId());
						if (subGroupList!=null){
							//实现循环推送BOSS入账，如果调用接口失败则进行下一条推送。将整体子集团认领数据推送一次
							for (int m = 0; m < subGroupList.size(); m++) {
								MoneyTotal subMoneyTotal = claimForFundsService.getMoneyTotal(subGroupList.get(m).getMoneyTotal_id());
								MoneyApplySubGroup moneyApplySubGroup=subGroupList.get(m);
								if(subMoneyTotal.getState().equals(5)){ //判断数据是否推送过BOSS，如果未推送则执行推送接口
									SystemUser subUser = systemUserService.getUserInfoRowNo(subMoneyTotal.getPushUserName());
									String sub_op_fee=BigDecimal.valueOf(Long.parseLong(subMoneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
									Result accountsubRes=ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(subUser.getBossUserName(),subMoneyTotal.getGroupCode(),
											subMoneyTotal.getBatchNo(),subMoneyTotal.getOtherAccNumber(),subMoneyTotal.getOtherName(),subMoneyTotal.getUseMemo(),subMoneyTotal.getSerialNo(),sub_op_fee);
									logger.info("调用资金入账接口反馈===>"+accountsubRes.toString());
									if(ResultCode.SUCCESS.code()==accountsubRes.getCode()){  //判断当前请求是否成功
										JSONObject accountsubResObj=JSONObject.fromObject(accountsubRes.getData());
										subMoneyTotal.setIsThe(subMoneyTotal.getIsThe() + 1);
										subMoneyTotal=	claimForFundsService.updateMoneyTotalTwo(subMoneyTotal);
										//推送BOSS账户资金结果判断
										if("0".equals(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_CODE"))){
											subMoneyTotal.setState(1);
											subMoneyTotal.setBoss_State(1); //设置BOSS状态为成功
											subMoneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
											subMoneyTotal.setPushDate(new Date());
											moneyApplySubGroup.setBoss_State(1);
											moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										}else{
											num+=1;
											subMoneyTotal.setBoss_State(0);
											subMoneyTotal.setBoss_Msg(JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG"));
											moneyApplySubGroup.setBoss_State(0);
											moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
											logger.info("资金入账接口反馈异常【"+JSONObject.fromObject(accountsubResObj.getString("ROOT")).getString("RETURN_MSG")+"】");
										}
									}else{
										num+=1;
										subMoneyTotal.setBoss_State(0);
										subMoneyTotal.setBoss_Msg(accountsubRes.getMessage());
										moneyApplySubGroup.setBoss_State(0);
										moneyApplySubGroup.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
										logger.info("调用BOSS资金入账接口异常【"+accountsubRes.getMessage()+"】");
									}
									claimForFundsService.updateMoneyTotalTwo(subMoneyTotal);
									claimForFundsService.updateMoneyApplySubGroup(moneyApplySubGroup);
								}
							}
						}
					}
				}else {
					Write(returnPars(-1,"","亲爱的同事,推送财务稽核反馈异常【"+res.getString("info")+"】,请核实!"));
					return;
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事,推送财务稽核异常【"+result.getMessage()+"】,请稍后重试或联系管理员处理!"));
				return;
			}


			if (num > 0) {
				Write(returnPars(-1,"","亲爱的同事,工单推送失败未完成全部推送，请核实！"));
			} else {
				//冲正成功后删除划拨资金信息
				monList = claimForFundsService.getSubMoneyTotalList(moneyTotal.getSerialNo());
				for (int a = 0 ; a < monList.size(); a++) {
					if (monList.get(a).getState().equals(-1)){
						claimForFundsService.deleteMoneyTotal(monList.get(a).getId());
					}
				}
				my.setState("0");
				claimForFundsService.updateMoneyApply(my);
				taskService.updateBpms_riskoff_task("工单数据推送成功,已完结!", 2, btask.getId());
				service.updateWait(wt, this.getRequest());
				Write(returnPars(1,"","亲爱的同事,工单数据推送成功,工单已完成!"));
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金认领错误信息：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,系统处理异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * @Description: 资金认领流程 工单结束方法
	 * @return: void
	 * @Date: 2021/11/9 10:29
	 */
	public void spanMonthAgainClaimData() {
		try {
			String id = getString("id");//开票id
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常【"+userPhoneNum+" 】,请刷新页面重试或联系管理员处理！"));
				throw new RuntimeException("用户信息异常");
			}
			Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
			WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息

			if (!btask.getStatus().equals(1)){
				Write(returnPars(-1,"","亲爱的同事,当前待办已处理完成,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}else if (!btask.getOper_no().equals(user.getRowNo())){
				Write(returnPars(-1,"","亲爱的同事,当前待办已提交至:"+btask.getOper_name()+" 处,请关闭页面不要重复提交！"));
				throw new RuntimeException("任务信息异常");
			}
			if (wt == null) {
				Write(returnPars(-1,"","亲爱的同事,待办信息异常,联系系统管理员"));
				throw new RuntimeException("待办信息异常");
			}
			taskService.updateBpms_riskoff_task("工单数据推送成功,已完结!", 2, btask.getId());
			service.updateWait(wt, this.getRequest());
			Write(returnPars(1,"","亲爱的同事,工单数据推送成功,已完结!"));
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			e.printStackTrace();
			logger.error("资金认领错误信息：" + e.getMessage(), e);
			Write(returnPars(-1,"","亲爱的同事,系统处理异常:"+e.getMessage()+",请联系管理员处理!"));
		}
	}

	/**
	 * 提交待办生成
	 */
	public void commitBackLogPachMonth(MoneyApply moneyApply, Integer userid, String processId, SystemUser user, String taskid) {
		WaitTask waitTask = new WaitTask();
		waitTask.setName("[资金认领]" + moneyApply.getTitle());//待办名称
		waitTask.setCreationTime(new Date());// 代办生成时间
		waitTask.setUrl("jsp/claimForFunds/spanMonthHandleClaim.jsp?id=" + moneyApply.getId());
		SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
		waitTask.setState(WaitTask.HANDLE);// 状态为待处理
		waitTask.setHandleUserId(USER.getRowNo());// 处理人id
		waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
		waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
		waitTask.setCreateUserId(user.getRowNo());// 创建人id
		waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
		waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
		waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
		waitTask.setTaskId(moneyApply.getId());
		service.saveWait(waitTask, this.getRequest());
	}


	/**---------------------------------------------(资金查询接口)--------------------------------------------------------------
	 * @Description TODO 查询地市所有未被认领的资金
	 * <AUTHOR>
	 * @Date 2022/6/9 16:17
	 **/
	public void getClaimForFunds(){
		try {
			String title = getString("name");
			String money =getString("money");
			String tranDate = getString("tranDate");

			String userPhoneNum = getString("userPhoneNum");

			String AmountSorting = getString("AmountSorting");		//金额排序
			String CreateDateSorting = getString("orderSort");		//时间排序
			PageRequest page = new PageRequest(getRequest());
			SystemUser systemUser = null;
			if (userPhoneNum==null){
				Write(returnPars(-1,"","亲爱的同事,缺失关键参数数据查询异常,请刷新页面重试!"));
				return;
			}else {
				systemUser = claimForFundsService.querUsers(userPhoneNum);
				if (systemUser==null){
					Write(returnPars(-1,"","亲爱的同事,登陆用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
			}

			if (AmountSorting==null|| AmountSorting.isEmpty()){
				AmountSorting = "DESC";
			}
			if (CreateDateSorting==null|| CreateDateSorting.isEmpty()){
				CreateDateSorting = "DESC";
			}
			List<Map<String,String>> vwUserList = claimForFundsService.getVwUserinf(String.valueOf(systemUser.getRowNo()));
			String companyCode =vwUserList.get(0).get("COMPANY_CODE");
			PageResponse response = claimForFundsService.findByCompanyCode(title,money,companyCode,tranDate,page,systemUser,AmountSorting,CreateDateSorting);
			Write(SerializeWithNeedAnnotationDateFormats(response));
		} catch (Exception e) {
			e.printStackTrace();
			logger.info("查询地市所有未被认领的资金异常:"+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,查询地市所有未被认领的资金异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据用户号码查询已认领（已完结）资金信息
	 * <AUTHOR>
	 * @Date 2022/6/9 16:51
	 **/
	public void getUserClaimForFunds(){
		try {
			String userPhoneNum = getString("userPhoneNum");
			String type = getString("type");

			String money =getString("money");
			String title = getString("name");
			String tranDate = getString("tranDate");

			String AmountSorting = getString("AmountSorting");		//金额排序
			String CreateDateSorting = getString("orderSort");		//时间排序

			PageRequest page = new PageRequest(getRequest());
			SystemUser systemUser = null;
			if (userPhoneNum==null){
				Write(returnPars(-1,"","亲爱的同事,缺失关键参数数据查询异常,请刷新页面重试!"));
				return;
			}else {
				systemUser = claimForFundsService.querUsers(userPhoneNum);
				if (systemUser==null){
					Write(returnPars(-1,"","亲爱的同事,登陆用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
			}

			if (AmountSorting==null||"".equals(AmountSorting)){
				AmountSorting = "DESC";
			}
			if (CreateDateSorting==null||"".equals(CreateDateSorting)){
				CreateDateSorting = "DESC";
			}
			PageResponse response = claimForFundsService.findByUser(title,money,tranDate,systemUser.getRowNo(),type,page,AmountSorting,CreateDateSorting);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(response));
		}catch(Exception e){
			e.printStackTrace();
			logger.info("查询当前登录人已认领资金:"+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,查询已认领的资金信息异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 自动认领确认推送方法
	 * <AUTHOR>
	 * @Date 2022/8/12 15:30
	 **/
	public void pushMoneyTaxByCode(){
		try{
			String id = getString("id");
			String groupCode = getString("groupCode");

			String userPhoneNum = getString("userPhoneNum");
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			claimForFundsService.getMoneyTotalSerialNoAndSubGroup(moneyTotal.getSerialNo(),0);

			SystemUser systemUser = null;
			if (userPhoneNum==null){
				Write(returnPars(-1,"","亲爱的同事,缺失关键参数数据查询异常,请刷新页面重试!"));
				return;
			}else {
				systemUser = claimForFundsService.querUsers(userPhoneNum);
				if (systemUser==null){
					Write(returnPars(-1,"","亲爱的同事,登陆用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
			}
			if (!moneyTotal.getState().equals(6)){
				Write(returnPars(-1,moneyTotal,"亲爱的同事，当前资金已被处理，请确认！"));
			}else {
				//先修改状态防止重复提交
				moneyTotal.setState(5);
				moneyTotal = claimForFundsService.updateMoneyTotalTwo(moneyTotal);

				Taxpayer taxpayer = claimForFundsService.getTaxpayerByBatchNo(moneyTotal.getBatchNo(),groupCode,systemUser.getRowNo());
				if (taxpayer.getUserId().equals(systemUser.getRowNo())){

					int pushUserRow = systemUser.getRowNo();
					String pushUserName = systemUser.getBossUserName();
					try {
						List<Map<String, Object>> map = claimForFundsService.findDept(pushUserRow);
						String county_name = map.get(0).get("TWODNAME").toString();
						String company_name = map.get(0).get("COMPANY_NAME").toString();
						List<Map<String, String>> sd = claimForFundsService.SelectZtreeByUId("ROLE_ZJRLSPGLY", company_name, county_name);
						if (sd.size() == 1) {
							if (!"".equals(sd.get(0).get("BOSSUSERNAME")) && sd.get(0).get("BOSSUSERNAME") != null && !"null".equals(sd.get(0).get("BOSSUSERNAME"))) {
								pushUserRow = Integer.parseInt(String.valueOf(sd.get(0).get("ROWNO")));
								pushUserName = sd.get(0).get("BOSSUSERNAME");
							}
						}
					} catch (Exception e) {
						e.printStackTrace();
						pushUserRow = systemUser.getRowNo();
						pushUserName = systemUser.getBossUserName();
					}

					SystemUser pushBossUser = systemUserService.getByUserInfoRowNo(pushUserRow);
					if (pushBossUser==null){
						Write(returnPars(-1,"","亲爱的同事，推送用户信息异常【"+pushBossUser+"】，请联系管理员处理"));
						return;
					}

					Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState("1",moneyTotal.getSerialNo());
					logger.info("财务预占接口调用结果===>"+result.toString());
					if(ResultCode.SUCCESS.code()==result.getCode()){  //判断当前请求是否成功
						JSONObject res=JSONObject.fromObject(result.getData());
						if("success".equals(res.getString("code"))){

							String op_fee = BigDecimal.valueOf(Long.parseLong(moneyTotal.getSub_overAmount())).divide(new BigDecimal(100)).toString();
							Result accountRes = ClaimFundsOpenSrv.getInstance().reChargeUnitAccount(pushBossUser.getBossUserName(), taxpayer.getGroupCode(),
									moneyTotal.getBatchNo(), moneyTotal.getOtherAccNumber(), moneyTotal.getOtherName(), moneyTotal.getUseMemo(), moneyTotal.getSerialNo(), op_fee);
							logger.info("调用资金入账接口反馈===>" + accountRes.toString());
							if (ResultCode.SUCCESS.code() == accountRes.getCode()) {  //判断当前请求是否成功
								JSONObject accountResObj = JSONObject.fromObject(accountRes.getData());
								//推送BOSS账户资金结果判断
								if ("0".equals(JSONObject.fromObject(accountResObj.getString("ROOT")).getString("RETURN_CODE"))) {
									moneyTotal.setGroupCode(taxpayer.getGroupCode());
									moneyTotal.setGroupName(taxpayer  .getGroupName());
									moneyTotal.setUserid(systemUser.getRowNo());
									moneyTotal.setIsThe(1);
									moneyTotal.setState(1);
									moneyTotal.setBoss_State(1); //设置BOSS状态为成功
									moneyTotal.setBoss_Msg(DateUtil.getDate()); //当成功设置为推送时间
									moneyTotal.setPushDate(new Date());
									moneyTotal.setInformation("["+DateUtil.getDate()+"]确认认领成功");
									moneyTotal.setPushUserName(pushUserRow);
									moneyTotal.setPushBossUserName(pushUserName);
									moneyTotal.setIs_sub_group(0);
									claimForFundsService.updateMoneyTotalTwo(moneyTotal);

									try{
										 if (taxpayer.getAccountState().equals(0)){
											 JSONObject json = GroupAccountSrv.getInstance().UpdateGroupAccount(taxpayer.getGroupCode(),taxpayer.getOtherAccNumber(),"","","","","1");
											 JSONObject hander = json.getJSONObject("HEADER");
											 JSONObject response = hander.getJSONObject("RESPONSE");
											 if (response.has("CODE") && response.getString("CODE").equals("0000")){
												 logger.info(taxpayer.getGroupCode()+"修改绑定状态成功！");
											 }else {
												 logger.info(taxpayer.getGroupCode()+"修改绑定状态失败："+response.getString("DESC"));
											 }
										 }
									}catch (Exception e){
										logger.info(taxpayer.getGroupCode()+"修改账户状态失败："+e.getMessage());
										e.printStackTrace();
									}finally {
										taxpayer.setUpdateDate(new Date());
										taxpayer.setState(2);
										claimForFundsService.updateTaxpayer(taxpayer);
										Write(returnPars(1,moneyTotal,"亲爱的同事，资金认领成功，请确认！"));
									}
								} else {
									moneyTotal.setState(6);
									claimForFundsService.updateMoneyTotalTwo(moneyTotal);
									ClaimFundsOpenSrv.getInstance().updateIncomeState("0",moneyTotal.getSerialNo());
									Write(returnPars(-1,"","亲爱的同事,亲爱的同事,认领信息推送BOSS失败【"+accountResObj.getJSONObject("ROOT").getString("RETURN_MSG")+"】,请确认!"));
								}
							} else {
								moneyTotal.setState(6);
								claimForFundsService.updateMoneyTotalTwo(moneyTotal);
								ClaimFundsOpenSrv.getInstance().updateIncomeState("0",moneyTotal.getSerialNo());
								Write(returnPars(-1,"","亲爱的同事,亲爱的同事,认领信息推送BOSS失败【"+accountRes.getMessage()+"】,请联系管理员处理!"));
							}

						}else {
							moneyTotal.setState(6);
							claimForFundsService.updateMoneyTotalTwo(moneyTotal);
							Write(returnPars(-1,"","亲爱的同事,请求财务稽核系统预占结果失败【"+res.getString("info")+"】,请确认!" ));
						}
					}else{
						moneyTotal.setState(6);
						claimForFundsService.updateMoneyTotalTwo(moneyTotal);
						Write(returnPars(-1,"","亲爱的同事,调用财务预占接口失败【"+result.getMessage()+"】,请联系管理员处理!"));
					}
				}else {
					moneyTotal.setState(6);
					claimForFundsService.updateMoneyTotalTwo(moneyTotal);
					Write(returnPars(-1,"","亲爱的同事,认领用户信息异常,请联系管理员处理!"));
				}
			}
		}catch (Exception e){
			logger.info("认领推送异常:"+e.getMessage());
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,认领推送异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 取消自动认领预占
	 * <AUTHOR>
	 * @Date 2022/8/12 15:30
	 **/
	public void cancelMoneyTaxByCode(){
		try{
			String id = getString("id");
			String userPhoneNum = getString("userPhoneNum");
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			SystemUser systemUser = null;
			if (userPhoneNum==null){
				Write(returnPars(-1,"","亲爱的同事,缺失关键参数数据查询异常,请刷新页面重试!"));
				return;
			}else {
				systemUser = claimForFundsService.querUsers(userPhoneNum);
				if (systemUser==null){
					Write(returnPars(-1,"","亲爱的同事,登陆用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
			}

			List<Taxpayer> taxpayer = claimForFundsService.getTaxpayerByUserRow(moneyTotal.getBatchNo(),String.valueOf(systemUser.getRowNo()));
			if (taxpayer!=null && taxpayer.size()>0){
				for (Taxpayer ta:taxpayer){
					ta.setState(-2);
					ta.setUpdateDate(new Date());
					claimForFundsService.updateTaxpayer(ta);
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事，您与当前资金已无预占关系，请确认！"));
				return;
			}

			List<Taxpayer> taxpayers = claimForFundsService.getTaxpayerList(moneyTotal.getBatchNo());
			if (( taxpayers == null || taxpayers.size()==0 ) && moneyTotal.getState().equals(6)){
				moneyTotal.setState(0);
				claimForFundsService.updateMoneyTotal(moneyTotal);
				Write(returnPars(1,"","亲爱的同事，您与当前资金的预占关系已取消，由于资金已无其他预占集团，资金已退还资金池，如需使用请自行认领！"));
			}else {
				Write(returnPars(1,"","亲爱的同事，您与当前资金的预占关系已取消，请确认！"));
			}
		}catch (Exception e){
			logger.info("取消自动认领预占异常:"+e.getMessage());
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,取消自动认领预占异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 解除自动认领关系方法
	 * <AUTHOR>
	 * @Date 2022/8/12 15:30
	 **/
	public void delectMoneyTaxByCode(){
		try{
			String id = getString("id");
			String groupCode = getString("groupCode");
			String userPhoneNum = getString("userPhoneNum");
			SystemUser systemUser = null;
			if (userPhoneNum==null){
				Write(returnPars(-1,"","亲爱的同事,缺失关键参数数据查询异常,请刷新页面重试!"));
				return;
			}else {
				systemUser = claimForFundsService.querUsers(userPhoneNum);
				if (systemUser==null){
					Write(returnPars(-1,"","亲爱的同事,登陆用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
			}
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);

			Taxpayer taxpayer = claimForFundsService.getTaxpayerByBatchNo(moneyTotal.getBatchNo(),groupCode,systemUser.getRowNo());
			if (taxpayer!=null){
				JSONObject json = GroupAccountSrv.getInstance().DelectGroupAccount(taxpayer.getGroupCode(),taxpayer.getOtherAccNumber());
				JSONObject hander = json.getJSONObject("HEADER");
				JSONObject response = hander.getJSONObject("RESPONSE");
				if (response.has("CODE") && response.getString("CODE").equals("0000")){
					taxpayer.setState(-1);
					taxpayer.setUpdateDate(new Date());
					claimForFundsService.updateTaxpayer(taxpayer);
				}else {
					Write(returnPars(-1,json,"亲爱的同事，取消管理关系失败【"+response.getString("DESC")+"】，请确认！"));
					return;
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事，数据查询失败，请刷新页面重试或联系管理员处理！"));
				return;
			}

			List<Taxpayer> taxpayers = claimForFundsService.getTaxpayerList(moneyTotal.getBatchNo());
			if (( taxpayers == null || taxpayers.size()==0 ) && moneyTotal.getState().equals(6)){
				moneyTotal.setState(0);
				claimForFundsService.updateMoneyTotal(moneyTotal);
				Write(returnPars(1,"","亲爱的同事，集团账户关系解除成功，由于资金已无其他预占集团，资金已退还资金池，如需使用请自行认领！"));
			}else {
				Write(returnPars(1,"","亲爱的同事，集团账户关系解除成功，请确认！"));
			}
		}catch (Exception e){
			logger.info("取消管理关系异常:"+e.getMessage());
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,取消管理关系异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据登录人查询当前登陆人待审批工代和已审批工单
	 * <AUTHOR>
	 * @Date 2022/6/9 17:03
	 **/
	public void getNotApprovedState(){
		try {
			String groupCode = getString("groupCode");
			String opType = getString("opType");
			String AmountSorting = getString("AmountSorting");
			String CreateDateSorting = getString("orderSort");

			String userId = getString("userId");

			if (AmountSorting==null||"".equals(AmountSorting)){
				AmountSorting = "DESC";
			}
			if (CreateDateSorting==null||"".equals(CreateDateSorting)){
				CreateDateSorting = "DESC";
			}

			PageRequest page = new PageRequest(getRequest());
			String userPhoneNum = getString("userPhoneNum");
			if (userPhoneNum!=null){
				SystemUser user = claimForFundsService.querUsers(userPhoneNum);
				userId = String.valueOf(user.getRowNo());
			}
			PageResponse response = claimForFundsService.getNotApprovedState(groupCode,opType,AmountSorting,CreateDateSorting,Integer.parseInt(userId),page);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(response));
		}
		catch (Exception e) {
			e.printStackTrace();
			logger.info("查询待审批工代和已审批工单异常:"+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,查询审批工单异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据工单编号查询工单详细信息和处理按钮
	 * <AUTHOR>
	 * @Date 2022/6/10 10:28
	 **/
	public void findIDCApply(){
		try{
			String id = getString("id");
			//String userPhoneNum = getString("userPhoneNum");
			//SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			String role = "";
			MoneyApply moneyApply = claimForFundsService.getMoneyApply(id);
			List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDet(moneyApply.getApplyNo());		//未推送的明细
			List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNo(moneyApply.getApplyNo());//全部明细
			int lateFee=0;
			boolean ifOffSite = false;
			for(int i=0;i<moneyApplyDet.size();i++){
				if("1.00".equals(moneyApplyDet.get(i).getLateFee())){
					lateFee=1;
				}

				//是否存在异地缴费
				if ("01".equals(moneyApplyDet.get(i).getUseType())){
					ifOffSite = true;
				}

				if (lateFee==1 && ifOffSite){
					break;
				}
			}
			MoneyTotal p=claimForFundsService.getMoneyTotal(moneyApply.getMoneyTotal_id());
			GroupCustomer groupCustomer = claimForFundsService.queryGroupCustomerById(p.getGroupCode());
			MoneytotalProvisional provisional = null;
			if (p.getProvisionalId()!=null && !"".equals(p.getProvisionalId())){
				provisional = claimForFundsService.getProvisionalByCode(p.getProvisionalId());
			}

			List<MoneyApplySubGroup> subGroupList = claimForFundsService.getMoneyApplySubGroup(moneyApply.getId());
			List<Map<String, String>> fileList = claimForFundsService.fuJian(id,MoneyTotal.MONEYTOTAL);
			List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(id);//根据ID查询根踪处理
			Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
			String taskName = "";
			List<String> buttons = new ArrayList<>();
			String code =claimForFundsService.getVwUserinf(moneyApply.getCreatorId()).get(0).get("COMPANY_CODE");
			if(task!=null){//任务不能为空,如果任务为空了那么流程审批结束
				TransferCitiesData transferCitiesData= claimForFundsService.getTransferCitiesData(code,task.getActivityName());
				if (transferCitiesData == null) {
					transferCitiesData = new TransferCitiesData();
					transferCitiesData.setAmount("0");
				}
				LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,task.getActivityName());
				if (lateFeeMoneyData == null) {
					lateFeeMoneyData = new LateFeeMoneyData();
					lateFeeMoneyData.setAmount("0");
				}
				taskName = task.getActivityName();
				Set<String>  nextLine = jbpmUtil.findOutComesByTaskId(task.getId());
				switch (taskName){
					case "省重客客户经理室经理":
						if (("3".equals(moneyApply.getOpType()) || "4".equals(moneyApply.getOpType())) && ("1".equals(moneyApply.getMa_type()) || "2".equals(moneyApply.getMa_type()))){
							if(Double.parseDouble(p.getAmount())/100>=Double.parseDouble("50000000")){
								buttons.add("SUBMIT");		//提交
								role="ROLE_SZKSM";
							}else{
								buttons.add("THROUGH");		//通过
							}
						}else if ("3".equals(moneyApply.getOpType()) && "4".equals(moneyApply.getMa_type())){
							buttons.add("SUBMIT");		//提交
							role="ROLE_SZKZXFKGLY";
						}else {
							if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
								if (Double.parseDouble(moneyApply.getApplyAmount())/100 <= Double.parseDouble(transferCitiesData.getAmount()) &&
										(Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount())){
									if (ifOffSite){
										buttons.add("SUBMIT");		//提交
										role="ROLE_DLJF";
									}else {
										buttons.add("THROUGH");		//通过
									}
								}else{
									buttons.add("SUBMIT");		//提交
									if(lateFee==0){
										role="ROLE_SZKSM";
									}else{
										role="ROLE_SZKYWGLY";
									}
								}
							}else {
								if (Double.parseDouble(moneyApply.getApplyAmount())/100 <= Double.parseDouble(transferCitiesData.getAmount()) && (Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount())){
									buttons.add("THROUGH");		//通过
								}else{
									buttons.add("SUBMIT");		//提交
									if((Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount()) || queryGroupLevel(groupCustomer.getGroupLevel())){
										role="ROLE_SZKSM";
									}else{
										role="ROLE_SZKYWGLY";
									}
								}
							}
						}
						break;
					case "省重客业务管理员":
						if ("3".equals(moneyApply.getOpType()) && "4".equals(moneyApply.getMa_type())){
							buttons.add("THROUGH");		//通过
						}else {
							buttons.add("SUBMIT");		//提交
							role="ROLE_SZKSM";
							break;
						}
					case "省公司业务管理员":
						buttons.add("THROUGH");		//通过
						break;
					case "省重客分管经理":
					case "市公司领导":
						if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
							if (ifOffSite){
								buttons.add("SUBMIT");		//提交
								role="ROLE_DLJF";
							}else {
								buttons.add("THROUGH");		//通过
							}
						}else {
							buttons.add("THROUGH");		//通过
						}
						break;
					case "区县政企部主任":
						if (("3".equals(moneyApply.getOpType()) || "4".equals(moneyApply.getOpType())) && ("1".equals(moneyApply.getMa_type()) || "2".equals(moneyApply.getMa_type()))){
							if(Double.parseDouble(p.getAmount())/100>=Double.parseDouble("10000")){
								buttons.add("SUBMIT");		//提交
								role="ROLE_QXSM";
							}else{
								buttons.add("THROUGH");		//通过
							}
						}else if ("3".equals(moneyApply.getOpType()) && "4".equals(moneyApply.getMa_type())){
							buttons.add("SUBMIT");		//提交
							role="ROLE_DSBM";
						}else {
							if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
								if (Double.parseDouble(moneyApply.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) &&
										(Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount())){
									if (ifOffSite){
										buttons.add("SUBMIT");		//提交
										role="ROLE_DLJF";
									}else {
										buttons.add("THROUGH");		//通过
									}
								}else {
									buttons.add("SUBMIT");		//提交
									role="ROLE_QXSM";
								}
							}else {
								if (Double.parseDouble(moneyApply.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) && (Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount())){
									buttons.add("THROUGH");		//通过
								}else {
									buttons.add("SUBMIT");		//提交
									role="ROLE_QXSM";
								}
							}
						}
						break;
					case "区县分管经理":
						if (("3".equals(moneyApply.getOpType()) || "4".equals(moneyApply.getOpType())) && ("1".equals(moneyApply.getMa_type()) || "2".equals(moneyApply.getMa_type()))){
							buttons.add("THROUGH");		//通过
						}else {
							if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
								if(Double.parseDouble(moneyApply.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) &&
										(Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount())){
									if (ifOffSite){
										buttons.add("SUBMIT");		//提交
										role="ROLE_DLJF";
									}else {
										buttons.add("THROUGH");		//通过
									}
								}else{
									role="ROLE_DSZJGL";
									buttons.add("SUBMIT");		//提交
								}
							}else {
								if(lateFee==0 || queryGroupLevel(groupCustomer.getGroupLevel())){
									buttons.add("THROUGH");		//通过
								}else{
									role="ROLE_DSBM";
									buttons.add("SUBMIT");		//提交
								}
							}
						}
						break;
					case "市公司业务管理员":
						buttons.add("SUBMIT");		//提交
						role="ROLE_DSDM";
						break;
					case "市公司政企部经理":
						if (("3".equals(moneyApply.getOpType()) || "4".equals(moneyApply.getOpType())) && ("1".equals(moneyApply.getMa_type()) || "2".equals(moneyApply.getMa_type()))){
							buttons.add("THROUGH");		//通过
						}else {
							if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
								if((Double.parseDouble(moneyApply.getLateFeeMoney())/100)>Double.parseDouble(lateFeeMoneyData.getAmount())){
									role="ROLE_DSSM";
									buttons.add("SUBMIT");		//提交
								}else{
									if (ifOffSite){
										buttons.add("SUBMIT");		//提交
										role="ROLE_DLJF";
									}else {
										buttons.add("THROUGH");		//通过
									}
								}
							}else {
								if((Double.parseDouble(moneyApply.getLateFeeMoney())/100)>Double.parseDouble(lateFeeMoneyData.getAmount())){
									role="ROLE_DSSM";
									buttons.add("SUBMIT");		//提交
								}else{
									buttons.add("THROUGH");		//通过
								}
							}
						}
						break;
					case "市公司客户经理室经理":
						if (("3".equals(moneyApply.getOpType()) || "4".equals(moneyApply.getOpType())) && ("1".equals(moneyApply.getMa_type()) || "2".equals(moneyApply.getMa_type()))){
							if(Double.parseDouble(p.getAmount())/100>=Double.parseDouble("10000")){
								buttons.add("SUBMIT");		//提交
								role="ROLE_DSDM";
							}else{
								buttons.add("THROUGH");		//通过
							}
						}else if ("3".equals(moneyApply.getOpType()) && "4".equals(moneyApply.getMa_type())){
							buttons.add("SUBMIT");		//提交
							role="ROLE_DSBM";
						}else {
							if (process.getProcess_sign().contains("ClaimForFundUsePrs")){
								if (Double.parseDouble(moneyApply.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) &&
										(Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount())){
									if (ifOffSite){
										buttons.add("SUBMIT");		//提交
										role="ROLE_DLJF";
									}else {
										buttons.add("THROUGH");		//通过
									}
								}else {
									buttons.add("SUBMIT");		//提交
									if(lateFee==0){
										role="ROLE_DSDM";
									}else{
										role="ROLE_DSZJGL";
									}
								}
							}else {
								if (Double.parseDouble(moneyApply.getApplyAmount())/100<=Double.parseDouble(transferCitiesData.getAmount()) && (Double.parseDouble(moneyApply.getLateFeeMoney())/100) <= Double.parseDouble(lateFeeMoneyData.getAmount())){
									buttons.add("THROUGH");		//通过
								}else {
									buttons.add("SUBMIT");		//提交
									if((Double.parseDouble(moneyApply.getLateFeeMoney())/100)<=Double.parseDouble(lateFeeMoneyData.getAmount()) || queryGroupLevel(groupCustomer.getGroupLevel())){
										role="ROLE_DSDM";
									}else{
										role="ROLE_DSBM";
									}
								}
							}
						}
						break;
					case "省重客中心风控管理员":
						buttons.add("SUBMIT");		//提交
						role="ROLE_SZKZXYYSJL";
						break;
					case "省重客中心运营支撑室经理":
					case "市公司业务管理室经理":
						buttons.add("SUBMIT");		//提交
						role="ROLE_SGSYWGLY";
						break;
					default:
						buttons.add("THROUGH");		//通过
						break;
				}
				buttons.add("RETURN");		//驳回

				String btu="";
				for(int j=0;j<buttons.size();j++){
					if("THROUGH".equals(buttons.get(j))){
						btu=buttons.get(j);
					}
				}
				if(!"THROUGH".equals(btu)){
					if(role == null||role == ""){
						role = nextLine.toArray()[0].toString();
						buttons.add("SUBMIT");
					}
				}
			}else{
				if(moneyApply.getOpType().equals("2")){		//冲正工单
					if (moneyApply.getState().equals("1")){	//审批中
						buttons.add("THROUGH");		//通过
						buttons.add("RETURN");		//驳回
					}else if (moneyApply.getState().equals("2")){	//被否决
						buttons.add("INVALID");		//作废
					}else if (moneyApply.getState().equals("3")){	//部分推送失败
						buttons.add("PUSHAGAIN");		//再次推送
						buttons.add("CLOSE");			//作废
					}else{
						buttons.add("CLOSE");		//关闭
					}
				}else if (moneyApply.getOpType().equals("4")){
					if (moneyApply.getState().equals("2")){	//被否决
						buttons.add("INVALID");		//作废
					}else if (moneyApply.getState().equals("3")){	//部分推送失败
						buttons.add("PUSHAGAIN");		//再次推送
					}else if (moneyApply.getState().equals("0")){
						buttons.add("CLOSE");			//完成
					}
				}else {
					if (moneyApply.getState().equals("2")){	//被否决
						buttons.add("INVALID");		//作废
					}else if (moneyApply.getState().equals("3")){	//部分推送失败
						buttons.add("PUSHAGAIN");		//再次推送
						buttons.add("CLOSE");			//作废
					}else if (true){
						buttons.add("CLOSE");		//关闭
					}
				}
			}
			Map<String,Object> result = new HashMap<String, Object>();
			result.put("flag","0");
			result.put("moneyApply",moneyApply);				//工单对象
			result.put("moneyApplyDetAllList",moneyApplyDet);	//全部明细
			result.put("moneyApplyDetList",moneyApplyDetList);	//未推送的明细
			result.put("role", role);							//下一步审批人角色
			result.put("buttons", buttons);						//按钮
			result.put("taskName", taskName);					//当前角色名称
			result.put("MoneyTotal", p);						//资金池信息
			result.put("taskList", taskList);					//流程跟踪信息(流程任务)
			result.put("fileList",fileList);					//附件信息
			result.put("subGroupList",subGroupList);			//资金划拨信息
			result.put("moneytotalProvisional",provisional);    //暂收款信息
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		}catch(Exception e){
			logger.error("查询工单详细信息异常:"+e.getMessage(),e);
			e.printStackTrace();
			Map<String,Object> result = new HashMap<String, Object>();
			result.put("flag","1");
			result.put("moneyApply","");
			result.put("moneyApplyDetList","");
			result.put("role","");
			result.put("buttons","");
			result.put("taskName","");
			result.put("taskList","");
			result.put("fileList","");
			result.put("subGroupList","");
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		}
	}

	private Boolean queryGroupLevel(String groupLevel){
		try {
			if (groupLevel.equals("A1") || groupLevel.equals("A2") || groupLevel.equals("B1") || groupLevel.equals("B2")){
				return true;
			}else {
				return false;
			}
		}catch (Exception e){
			logger.info("集团等级查询异常："+e.getMessage());
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * @Description TODO 根据集团客户和当前登录人员的boss工号来查询账户
	 * <AUTHOR>
	 * @Date 2022/6/10 11:00
	 **/
	public void getAccountQuery(){
		try {
			String groupCode=getString("groupCode");
			String userPhoneNum = getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			Result result= ClaimFundsOpenSrv.getInstance().getUnitInfo(user.getBossUserName(),groupCode);
			logger.info("请求getUnitInfo接口结果===>"+result.toString());
			if(ResultCode.SUCCESS.code()==result.getCode()){
				JSONObject resObj=JSONObject.fromObject(result.getData());
				Write(resObj.getString("ROOT"));
			}else{
				Write(returnPars(-1,"","亲爱的同事，账户查询异常，请联系管理员处理！"));
			}
		}
		catch (Exception e) {
			logger.info("账户查询异常:"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，账户查询异常，请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 根据集团编号查询账户信息
	 * <AUTHOR>
	 * @Date 2023/8/11 15:32
	 **/
	public void queryAccountByGroup(){
		try {
			String groupCode = getString("groupCode");
			String otherAccNumber = getString("otherAccNumber");

			String bossName = getString("bossName");
			if (bossName==null || "".equals(bossName) || "null".equals(bossName)){
				String userPhoneNum = getString("userPhoneNum");
				SystemUser user = claimForFundsService.querUsers(userPhoneNum);
				bossName = user.getBossUserName();
			}



			GroupRelations relations = groupAccountService.queryGroupRelations(groupCode,otherAccNumber);
			if (relations==null){
				Write(returnPars(-1,"","亲爱的同事，集团："+groupCode+" 与账户："+otherAccNumber+" 绑定信息异常，请联系管理员处理！"));
			}else {
				List<GroupHipAccount> groupHipAccountList = groupAccountService.queryGroupHipAccountByHip(relations.getId());
				Result result= ClaimFundsOpenSrv.getInstance().getUnitInfo(bossName,groupCode);
				logger.info("请求getUnitInfo接口结果===>"+result.toString());
				if(ResultCode.SUCCESS.code()==result.getCode()){
					JSONObject resObj=JSONObject.fromObject(result.getData());
					JSONObject root = resObj.getJSONObject("ROOT");
					if (root.has("RETURN_CODE") && "0".equals(root.getString("RETURN_CODE"))){
						JSONObject outData = root.getJSONObject("OUT_DATA");
						JSONObject outInfo = outData.getJSONObject("INFO");

						JSONObject cmiotInfo = outInfo.getJSONObject("CMIOT_INFO");
						JSONArray cmiotList = new JSONArray();
						if (cmiotInfo!=null && cmiotInfo.has("CMIOT_ACCT_LIST")){
							cmiotList = cmiotInfo.getJSONArray("CMIOT_ACCT_LIST");
						}
						JSONArray contractList = outInfo.getJSONArray("CONTRACT_LIST");

						JSONArray cmiotArray = new JSONArray();
						JSONArray contractArray = new JSONArray();
						JSONArray phoneArray = new JSONArray();
						for (GroupHipAccount groupHipAccount:groupHipAccountList) {
							switch (groupHipAccount.getContractType()){
								case "CMIOT账户":
									for (int i = 0;i<cmiotList.size();i++){
										JSONObject cmiot = cmiotList.getJSONObject(i);
										if (cmiot.getString("ACCT_CODE").equals(groupHipAccount.getContractNo())){
											cmiotArray.add(cmiot);
											cmiotList.remove(i);
											break;
										}
									}
									break;
								case "成员个人账户":
								case "省外跨区账户":
									phoneArray.add(groupHipAccount);
									break;
								default:
									for (int i = 0;i<contractList.size();i++){
										JSONObject contract = contractList.getJSONObject(i);
										if (contract.getString("CONTRACT_NO").equals(groupHipAccount.getContractNo())){
											contractArray.add(contract);
											contract.remove(i);
											break;
										}
									}
									break;
							}
						}

						outInfo.getJSONObject("CMIOT_INFO").put("CMIOT_ACCT_LIST",cmiotArray);
						outInfo.put("CONTRACT_LIST",contractArray);
						outInfo.put("PHONE_LIST",phoneArray);
						Write(returnPars(1,root,"操作成功!"));
					}else {
						Write(returnPars(-1,"","亲爱的同事，查询BOSS集团信息失败:"+root.getString("RETURN_MSG")+" ，请联系管理员处理！"));
					}
				}else{
					Write(returnPars(-1,"","亲爱的同事，集团："+groupCode+" 查询BOSS集团信息失败，请联系管理员处理！"));
				}
			}

		}catch (Exception e){
			e.printStackTrace();
			logger.info("账户信息查询失败："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，账户信息查询失败【"+e.getMessage()+"】，请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 提供app查询资金状态进行前置校验
	 * <AUTHOR>
	 * @Date 2022/6/9 16:54
	 **/
	public void appQueryMoneytotalStateByid(){
		try{
			String id = getString("id");
			if (id.length()<=0){
				Write(returnPars(-1, "", "操作失败，参数异常未获取到对于资金编号！"));
				return;
			}
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			if (moneyTotal==null){
				Write(returnPars(-1, "", "查询失败，参数异常未查询对于资金信息,请输入正确的资金编号！"));
				return;
			}
			Write(returnPars(1, moneyTotal.getState(), "查询成功!"));
		}catch (Exception e){
			e.printStackTrace();
			Write(returnPars(-1, "", "亲爱的同事,资金状态查询异常，请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 根据资金id查询资金户名是否为客户经理
	 * <AUTHOR>
	 * @Date 2022/6/9 17:00
	 **/
	public void queryMoneyTotalUser(){
		try{
			String id = getString("id");    //资金id
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			if (moneyTotal==null){
				Write(returnPars(-1,"","亲爱的同事,资金信息获取失败[资金编号:"+id+"],请联系管理员处理!"));
				return;
			}
			List<Map<String,String>> list = claimForFundsService.getUserByName(moneyTotal.getOtherName());
			if (list.size()>0){
				Write(returnPars(-1,list,"你认领的资金疑是员工转账，存在风险，请核实!"));
			}else {
				Write(returnPars(1,"","资金数据正常!"));
			}
		}catch (Exception e){
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,资金风险校验异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 查询推荐集团数据 --> type == '1 | 2' '可能是你的 | 你认领过的'
	 * <AUTHOR>
	 * @Date 2022/6/13 10:00
	 **/
	public void queryGroupCode(){
		try{
			Integer type = getInteger("type");//开票id
			String otherAccNumber = getString("otherAccNumber");//对方账户号码
			List<Map<String, String>> listMap=null;
			if(type==2){
				listMap=claimForFundsService.getMoneyTotalGroup(otherAccNumber);
			}
			Write(JSONHelper.SerializeWithNeedAnnotation(listMap));
		}catch(Exception e){
			logger.info("查询推荐集团数据异常:"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,查询推荐集团数据异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据账户号码查询预开票的账期金额
	 * <AUTHOR>
	 * @Date 2022/6/13 10:21
	 **/
	public void getCycleAmount(){
		try {
			String contractNo = getString("contractNo");
			List<PreinvApplyDet> pd = claimForFundsService.getPreinvApplyDet(contractNo);
			System.out.println("查询账期信息：====="+JSONHelper.SerializeWithNeedAnnotationDateFormat(pd).toString());
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(pd));
		}catch (Exception e) {
			logger.info("查询预开票的账期金额异常:"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,查询预开票的账期金额异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 查询子集团信息
	 * <AUTHOR>  
	 * @Date 2022/6/13 11:22 
	 **/
	public void callSubGroup(){
		try{
			if (isES){
				String group_id = getString("groupCode");
				String resxml=GroupCustomerService.getInstance().queryRelaCust(group_id);
				JSONObject obj = XmlUtil.xml2Json(resxml);
				JSONObject body = JSONObject.fromObject(obj.get("Body"));
				JSONObject response = JSONObject.fromObject(body.get("commonServiceResponse"));
				String commonServiceReturn = response.getString("commonServiceReturn");
				Write(XmlUtil.xml2Json(commonServiceReturn).toString());
			}else {
				String json = "{\"HEADER\":{\"RESPONSE\":{\"DESC\":\"流程处理成功\",\"CODE\":\"0000\"},\"TRANSACTION_ID\":\"202008261736160912509556\"}," +
					"\"RESULT\":{\"RELA_CUST_LIST\":[{\"CUST_NAME\":\"四川省泸州市龙马潭区地方税务局\"," +
					"\"CUST_CODE\":\"2802738409\"},{\"CUST_NAME\":\"国家税务总局泸州市江阳区税务局\",\"CUST_CODE\":\"2800024117\"}," +
					"{\"CUST_NAME\":\"国家税务总局叙永县税务局\",\"CUST_CODE\":\"2800005606\"},{\"CUST_NAME\":\"四川省泸州市纳溪区地方税务局\"," +
					"\"CUST_CODE\":\"2800003640\"},{\"CUST_NAME\":\"泸州市古蔺县地方税务局\",\"CUST_CODE\":\"2800068381\"}," +
					"{\"CUST_NAME\":\"合江县地税\",\"CUST_CODE\":\"2800033747\"},{\"CUST_NAME\":\"泸州市泸县地方税务局\"," +
					"\"CUST_CODE\":\"2803148700\"},{\"CUST_NAME\":\"四川省地方税务局\",\"CUST_CODE\":\"2800052347\"}," +
					"{\"CUST_NAME\":\"四川省泸州市地方税务局第二直属税务分局\",\"CUST_CODE\":\"2807366504\"}," +
					"{\"CUST_NAME\":\"四川省泸州市地方税务局第一直属税务分局\",\"CUST_CODE\":\"2807366497\"}," +
					"{\"CUST_NAME\":\"四川省泸州市地方税务局第二直属税务分局\",\"CUST_CODE\":\"2801902362\"}]}}";
			Write(json);
			}
		}catch(Exception e){
			logger.info("查询子集团信息异常:"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，查询子集团信息异常，请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 集团关系查询
	 * <AUTHOR>
	 * @Date 2022/11/8 10:05
	 **/
	public void getCustRelation(){
		try{
			String groupCode = getString("groupCode");
			String relationType = getString("relationType");
			if (groupCode==null || relationType==null){
				Write(returnPars(-1,"","亲爱的同事,集团关系查询缺少关键信息,请刷新页面重试或联系管理员处理!"));
				return;
			}

			JSONObject RelaCust = null;
			JSONArray custRelation = null;
			if (relationType.equals("0")){  //父子集团关系
				RelaCust = GroupAccountSrv.getInstance().queryRelaCust(groupCode);

			}else {                         //其他关系
				RelaCust = GroupAccountSrv.getInstance().queryCustRelation(relationType,groupCode);
			}
			JSONObject hander = RelaCust.getJSONObject("HEADER");
			JSONObject response = hander.getJSONObject("RESPONSE");
			if (response.has("CODE") && response.getString("CODE").equals("0000") && RelaCust.has("RESULT")) {
				custRelation = RelaCust.getJSONArray("RESULT");
			}else {
				Write(returnPars(-1,RelaCust,"亲爱的同事，集团关系查询接口异常【"+(response.has("DESC")?response.getString("DESC"):"接口调用信息异常")+"】，请联系管理员处理！"));
				return;
			}
			Write(returnPars(1,custRelation,"查询成功！"));
		}catch (Exception e){
			e.printStackTrace();
			logger.info("集团关系查询异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,集团关系查询异常["+e.getMessage()+"],请联系管理员处理!"));
		}
	}

	public String qryCustCodeByXml(String group_id) {
		StringBuilder sb = new StringBuilder();
		sb.append("<ROOT>");
		sb.append("<HEADER>");
		sb.append("<TRANSACTION_ID>" + EOMCODEUtil.MakeOrderNo(10) + "</TRANSACTION_ID>");
		sb.append("<SERVICE_CODE>queryRelaCust</SERVICE_CODE>");
		sb.append("<CLIENT_NAME>SCCRM0</CLIENT_NAME>");
		sb.append("<PASSWORD>123456</PASSWORD>");
		sb.append("</HEADER>");
		sb.append("<BODY>");
		sb.append("<CUST_CODE>" + group_id + "</CUST_CODE>");
		sb.append("</BODY>");
		sb.append("</ROOT>");
		return sb.toString();
	}

	/**
	 * @Description TODO 根据集团编码获取集团下的纳税人基础信息
	 * <AUTHOR>
	 * @Date 2022/6/13 11:22
	 **/
	public void appQueryUnitTaxPlayerInfo(){
		try{
			String groupCode = getString("groupCode");
			String phone = getString("phone");//手机号码
			SystemUser user = claimForFundsService.querUsers(phone);
			if (groupCode.length()<=0){
				Write(returnPars(-1,"","亲爱的同事,集团信息异常,为获取到有效的集团编号,请联系管理员处理!"));
			}
			if (user.getBossUserName().length()<=0){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常,查询到当前用户未设置BOSS工号,请联系管理员配置工号后操作!"));
			}
			Result result = UnitInfoSrv.getInstance().getUnitTaxPlayerInfo(groupCode,user.getBossUserName());
			if (result.getCode()==ResultCode.SUCCESS.code()){
				JSONObject json = JSONObject.fromObject(result.getData());
				JSONObject root = JSONObject.fromObject(json.get("ROOT"));
				if (root.getString("RETURN_CODE").equals("0")){
					JSONObject OUT_DATA = root.getJSONObject("OUT_DATA");
					if (OUT_DATA.has("DATA")){
						if (OUT_DATA.get("DATA") instanceof JSONArray){
							Write(returnPars(1,DesUtil.encrypt(OUT_DATA.getString("DATA")),"查询成功！"));
						}else if (OUT_DATA.get("DATA") instanceof JSONObject){
							JSONArray jsonArr = JSONArray.fromObject(OUT_DATA.get("DATA"));
							Write(returnPars(1,DesUtil.encrypt(jsonArr.toString()),"查询成功！"));
						}else {
							Write(returnPars(-1,OUT_DATA.get("DATA"),"亲爱的同事，查询到的纳税人信息格式异常，请联系管理员处理！"));
						}
					}else {
						Write(returnPars(-1,"","亲爱的同事，根据集团编号未查询到可用的纳税人信息，请核实！"));
					}
				}else {
					Write(returnPars(-1,"",root.getString("RETURN_MSG")));
				}

			}else {
				Write(returnPars(-1,"","亲爱的同事,纳税人信息查询接口异常["+result.getMessage()+"],请联系管理员处理!"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("纳税人信息获取异常:"+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,纳税人信息获取异常["+e.getMessage()+"],请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据所选子集团查询默认归属人
	 * <AUTHOR>  
	 * @Date 2022/6/13 11:27 
	 **/
	public void queryGroupUserById(){
		List<Map<String,Object>> resultList = new ArrayList<>();
		JSONObject obj = new JSONObject();
		boolean bl = true;
		String message="";
		try {
			String groupCoding = getString("groupCode");
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser users = claimForFundsService.querUsers(userPhoneNum);
			GroupCustomer customer=customerServiceXml.getCustInfoQuery(groupCoding,"");
			//GroupCustomer customer= customerService.queryGroup(groupCoding);
			if(customer!=null) {
				String userCompanyCode = users.getSystemDept().get(0).getSystemCompany().getCompanyCode();
				if (customer.getUser_name() == null || "".equals(customer.getUser_name())) {
					bl = false;
					message = "亲爱的同事，你选择的集团未查询到客户经理工号。";
				} else {
					SystemUser groupUser = systemUserService.queryByBoss(customer.getUser_name());
					List<SystemDept> sdList = systemUserService.getDeptListByRowNo(groupUser.getRowNo());
					String groupCompanyCode = sdList.get(0).getSystemCompany().getCompanyCode();
					if (userCompanyCode.equals(groupCompanyCode)) {
						Map<String,Object> userMap = new HashMap<>();
						userMap.put("user_id", String.valueOf(groupUser.getRowNo()));
						userMap.put("user_name", groupUser.getEmployeeName());
						userMap.put("isGroup",true);
						resultList.add(userMap);
						List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDetByGroup(groupCoding);
						if(!replaceModelDets.isEmpty()){
							Map<String,Object> map;
							for (ClaimReplaceModelDet replaceModelDet : replaceModelDets) {
								List<Map<String, Object>> userLists = claimForFundsService.getCountyByUserID(replaceModelDet.getCreationRow().toString());
								boolean flag = false;
								for (Map<String, Object> stringObjectMap : resultList) {
									if (stringObjectMap.get("user_id").toString().equals(userLists.get(0).get("ROWNO").toString()))
										flag = true;
									;
								}
								if (!userLists.isEmpty() && userLists.get(0).get("BOSSUSERNAME") != null && !flag) {
									map = new HashMap<>();
									map.put("user_id", String.valueOf(userLists.get(0).get("ROWNO")));
									map.put("user_name", userLists.get(0).get("EMPLOYEE_NAME"));
									map.put("isGroup", false);
									resultList.add(map);
								}
							}
						}
						bl = true;
						message = "ok!";
					} else {
						bl = false;
						message = "亲爱的同事，你选择的集团归属客户经理不属于你所在地市，请确认！";
					}
				}
				obj.put("user_type", bl);
				obj.put("user_message", message);
				obj.put("user_obj", resultList);
				Write(JSONHelper.SerializeWithNeedAnnotation(obj));
			}else{
				obj.put("user_type",false);
				obj.put("user_message","亲爱的同事，集团【"+groupCoding+"】信息未同步订单系统，请联想系统管理员处理！");
				obj.put("user_obj",resultList);
				Write(JSONHelper.SerializeWithNeedAnnotation(obj));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("归属人信息获取异常:"+e.getMessage(),e);
			obj.put("user_type",false);
			obj.put("user_message","亲爱的同事，集团信息查询失败【"+e.getMessage()+"】，请联系管理员处理！");
			obj.put("user_obj",resultList);
			Write(JSONHelper.SerializeWithNeedAnnotation(obj));
		}
	}

	/**
	 * @Description TODO 查询政企资金缴费处理人
	 * <AUTHOR>
	 * @Date 2022/6/13 11:30
	 **/
	public void selectUser(){
		try{
			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			String role = getString("role");
			List<Map<String, Object>> map = claimForFundsService.findDept(user.getRowNo());
			String county_name = map.get(0).get("TWODNAME").toString();
			String company_name = map.get(0).get("COMPANY_NAME").toString();
			List<Map<String, String>> sd = claimForFundsService.SelectZtreeByUId(role,company_name, county_name);
			if(sd.size()>1){
				Write(returnPars(-1,sd,"亲爱的同事，查询到多个缴费工号，请核实！"));//查询到本公司有多个BOSS处理人,请删除
			}else {
				if(sd.isEmpty()){
					Write(returnPars(-1,sd,"亲爱的同事，未查询到缴费工号，请核实！"));//未查询到本公司有配置BOSS处理人员
				}else {
					if("".equals(sd.get(0).get("BOSSUSERNAME"))||sd.get(0).get("BOSSUSERNAME")==null||"null".equals(sd.get(0).get("BOSSUSERNAME"))){
						Write(returnPars(-1,sd,"亲爱的同事，查询本公司配置BOSS处理人员未配置BOSS工号，请核实！"));//未查询到本公司有配置BOSS处理人员
					}else{
						Write(returnPars(1,sd.get(0),"查询完成！"));

					}
				}
			}
		}catch(Exception e){
			e.printStackTrace();
			logger.info("查询政企资金缴费处理人异常:"+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，查询政企资金缴费处理人异常，请核实！"));//查询到本公司有多个BOSS处理人,请删除

		}
	}

	/**
	 * @Description TODO 用于校验用户使用资金缴费或者预开票状态时，确认对应账户是否允许用于缴费
	 * <AUTHOR>
	 * @Date 2022/6/13 11:32
	 **/
	public void validPreInv(){
		//400
		Result outMsg= ResultGenerator.genFailResult("账户号码校验异常");
		try {
			String contractNo=getString("contractNo");
			String phoneNo=getString("phone_no");
			String loginNo=getString("login_no");

			String userPhoneNum = getString("userPhoneNum");//发起人手机号码
			SystemUser users = claimForFundsService.querUsers(userPhoneNum);
			Result result = ClaimFundsOpenSrv.getInstance().s8000ValidPreInv(phoneNo,contractNo,users.getBossUserName());
			if(ResultCode.SUCCESS.code()==result.getCode()){
				JSONObject resDate=JSONObject.fromObject(result.getData());
				JSONObject rootDate=JSONObject.fromObject(resDate.get("ROOT"));
				if(rootDate.getInt("RETURN_CODE")==0){
					//200
					//outMsg=ResultGenerator.genFailResult("亲爱的同事，您的账户号码存在预开票未结清,对应流水号["+rootDate.getJSONObject("OUT_DATA").getString("PRE_ORDER_ID")+"]");
					if(rootDate.has("OUT_DATA")){
						JSONObject outData = rootDate.getJSONObject("OUT_DATA");
						JSONObject preinyList = outData.getJSONObject("PREINV_LIST");
						if (preinyList.get("PREINV_INFO") instanceof JSONObject){
							JSONArray preinvInfo = new JSONArray();
							preinvInfo.add(preinyList.getJSONObject("PREINV_INFO"));
							preinyList.put("PREINV_INFO",preinvInfo);
						}
						outMsg=ResultGenerator.genSuccessResult(outData.toString());
					}else{
						outMsg=ResultGenerator.genFailResult("未查询到预开票记录,不能预开票交费,请转至普通交费");
					}
				}else{
					//400
					//outMsg=ResultGenerator.genSuccessResult();
					outMsg= ResultGenerator.genFailResult(rootDate.getString("DETAIL_MSG"));
				}
			}
			Write(outMsg.toString());
		}catch (Exception e){
			logger.error("调用校验是否允许缴费失败：" + e.getMessage(), e);
			outMsg=ResultGenerator.genFailResult("未知错误："+e.getMessage());
		}
		Write(outMsg.toString());
	}

	/**
	 * @Description TODO 查询有价卡数据
	 * <AUTHOR>  
	 * @Date 2022/6/13 11:35 
	 **/
	public void getValuableCarCompare(){
		try {
			String invContractNo = getString("invContractNo");
			List<ValuableCardDet> list = claimForFundsService.getValuableCarCompare(invContractNo);
			if(null != list && list.size() > 0){
				Write(com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(ResultGenerator.genSuccessResult(list)));
			}else{
				Write(ResultGenerator.genFailResult("未查询到有效数据").toString());
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("有价卡查询数据异常"+e.getMessage(),e);
			Write(ResultGenerator.genFailResult("数据查询异常").toString());
		}
	}

	/**
	 * @Description TODO 根据CMIOT物联网账户查询预开票记录
	 * <AUTHOR>
	 * @Date 2023/6/8 15:53
	 **/
	public void getInternetOfThingsDet(){
		try {
			String contractNo = getString("contractNo");        //CMIOT账户
			List<Map<String,String>> internetOfThingsDetList = claimForFundsService.queryInternetOfThingsDetList(contractNo);
			if (internetOfThingsDetList.size()>0){
				Write(returnPars(1,internetOfThingsDetList,"数据查询完成！"));
			}else {
				Write(returnPars(2,"","亲爱的同事,根据账户号码未查询到有效物联网预开票数据,请核实!"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("查询物联网预开票数据异常"+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事,查询物联网预开票数据异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 缴费时判断账户/手机号   是否存在欠费
	 * <AUTHOR>
	 * @Date 2022/6/13 16:13
	 **/
	public void sConFeeOweQry(){
		Result result=ResultGenerator.genFailResult("亲爱的同事,账户校验是否存在未缴纳金额时出现异常,请联系管理员处理!");
		try{
			String PHONE_NO=getString("phoneNo");		//手机号码 可不传，帐号和服务号码(手机号码)必须至少传一个。只输入手机号码时，查询优先级最高的非集团付费账户
			String CONTRACT_NO=getString("contractNo");	//账户 可不传，帐号和服务号码(手机号码)必须至少传一个
			String QRY_TYPE = getString("QRY_TYPE");		//查询类型 可不传，1：专线业务公共账户查询（account_type=1的账户）
			String SELECT_TYPE = getString("SELECT_TYPE");//集团余额查询标志可不传。 传入该字段时，必须传入集团账户contract_no。 0：集团余额明细查询； 1：进查询集团余额。值为1时，输出仅一个字段UNIT_PREPAY_FEE

			String phoneNumber = getString("phoneNumber");
			SystemUser user = claimForFundsService.querUsers(phoneNumber);

			if (PHONE_NO==null){
				PHONE_NO = "";
			}
			if (CONTRACT_NO==null){
				CONTRACT_NO = "";
			}
			if (QRY_TYPE==null){
				QRY_TYPE = "";
			}
			if (SELECT_TYPE==null){
				SELECT_TYPE = "";
			}
			result = ClaimFundsOpenSrv.getInstance().sConFeeOweQry(CONTRACT_NO,PHONE_NO,QRY_TYPE,SELECT_TYPE,user.getBossUserName());//user.getBossUserName()
			Write(result.toString());
		}catch (Exception e){
			logger.info("账户信息查询异常：" + e.getMessage(), e);
			Write(result.toString());
		}
	}

	/**
	 * @Description TODO 欠费信息查询接口
	 * <AUTHOR>
	 * @Date 2022/7/20 15:46
	 **/
	public void sOweFeeQry(){
		Result result=ResultGenerator.genFailResult("亲爱的同事,账户信息查询异常,请联系管理员处理!");
		try{
			String phoneNo = getString("phoneNo");
			String contractNo = getString("contractNo");
			String qryType = getString("qryType");
			if (phoneNo==null){
				phoneNo = "";
			}
			if (contractNo==null){
				contractNo = "";
			}
			if (qryType==null){
				qryType = "1";
			}
			String bossName = getString("bossName");
			if (bossName==null || bossName.equals("")){
				bossName = user.getBossUserName();
			}
			result = ClaimFundsOpenSrv.getInstance().sOweFeeQry(phoneNo,contractNo,bossName,qryType);
			JSONObject object = JSONObject.fromObject(result.getData());
			JSONObject root = object.getJSONObject("ROOT");
			if (root.has("OUT_DATA") && root.getJSONObject("OUT_DATA").has("OUTMSG")){
				JSONObject date = root.getJSONObject("OUT_DATA");
				date.remove("OUTMSG");
				root.put("OUT_DATA",date);
				object.put("ROOT",root);
				result.setData(object.toString());
			}
			Write(result.toString());
		}catch (Exception e){
			e.printStackTrace();
			logger.info("欠费信息查询异常："+e.getMessage());
			Write(result.toString());
		}
	}

	/**
	 * @Description TODO 资金认领查询滞纳金
	 * <AUTHOR>
	 * @Date 2022/6/13 16:55 
	 **/
	public void getLateFeeCount(){
		try {
			String contractNo = getString("contractNo");
			String phoneNo = getString("phoneNo");
			String userPhoneNum =getString("userPhoneNum");
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			Map<String, Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
			String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
			//String json1 = "{\"REGION_ID\":\"11\",\"POWER_RIGHT\":\"18\",\"GROUP_ID\":\"23\",\"RETURN_MSG\":\"ok!\",\"PROMPT_MSG\":\"\",\"RETURN_CODE\":\"0\",\"LOGIN_NO\":\"aa0002107\",\"USER_MSG\":\"处理成功!\",\"DETAIL_MSG\":\"OK!\"}";
			JSONObject obj = JSONObject.fromObject(json);
			Result result = ClaimFundsOpenSrv.getInstance().qryFeeInfo(phoneNo,contractNo, obj.getString("GROUP_ID"), user.getBossUserName());
			//Result result =ClaimFundsOpenSrv.getInstance().qryFeeInfo("","13011002083800","111211","aagh38");
			logger.info("资金认领查询滞纳金返回数据"+result.toString());
			String money ="0";
			if (ResultCode.SUCCESS.code() == result.getCode()) {
				JSONObject data=JSONObject.fromObject(result.getData());
				JSONObject root = JSONObject.fromObject(data.get("ROOT"));
				JSONObject outData = JSONObject.fromObject(root.get("OUT_DATA"));
				Object outMsg = outData.get("OUTMSG");
				if(outMsg instanceof JSONArray){//判断OUTMSG节点是不是数组
					JSONArray array =JSONArray.fromObject(outData.get("OUTMSG"));
					for(int i=0;i<array.size();i++){
						JSONObject fobj=JSONObject.fromObject(array.get(i));
						if(fobj!= null && !fobj.isEmpty() && !fobj.isNullObject() && !"null".equals(fobj) && !"{}".equals(fobj) && fobj.size()>0){
							JSONArray owefeeinfo=JSONArray.fromObject(fobj.get("OWEFEEINFO"));
							for (int j=0;j<owefeeinfo.size();j++){
								JSONObject owefeeinfoObj = JSONObject.fromObject(owefeeinfo.get(j));
								money=(Long.parseLong(money)+owefeeinfoObj.getInt("DELAY_FEE"))+"";
							}
						}
					}
				}else if(outMsg instanceof JSONObject){//判断OUTMSG节点是不是对象
					JSONObject object=JSONObject.fromObject(outData.get("OUTMSG"));
					if(object!= null && !object.isEmpty() && !object.isNullObject() && !"null".equals(object) && !"{}".equals(object) && object.size()>0) {
						JSONArray owefeeinfo = JSONArray.fromObject(object.get("OWEFEEINFO"));
						for (int j = 0; j < owefeeinfo.size(); j++) {
							JSONObject owefeeinfoObj = JSONObject.fromObject(owefeeinfo.get(j));
							money=(Long.parseLong(money) + owefeeinfoObj.getInt("DELAY_FEE"))+"";
						}
					}
				}
			}
			Write(returnPars(1,money,"查询成功！"));
		}catch (Exception e){
			logger.error("资金认领查询滞纳金金额错误信息："+e.getMessage(),e);
			Write(returnPars(-1,0,"亲爱的同事,资金认领查询滞纳金金额失败"+e.getMessage()+",请刷新页面重试!"));
		}
	}


	/**
	 * @Description: 根据用户查询工单信息
	 * @return: void
	 * @Author: TX
	 * @Date: 2021/11/24 18:35
	 */
	public void getAllMoneyApply() {
		try {
			String phoneNo = getString("phoneNo");					//登陆人电话号码
			String opType = getString("opType");						//工单类型
			String groupCode = getString("groupCode");				//集团编号
			String AmountSorting = getString("AmountSorting");		//金额排序
			String CreateDateSorting = getString("orderSort");		//时间排序
			String type = getString("querytype");						//查询类型
			PageRequest page = new PageRequest(getRequest());
			SystemUser systemUser = null;
			if (phoneNo==null || type==null){
				Write(returnPars(-1,"","亲爱的同事,缺失关键参数数据查询异常,请刷新页面重试!"));
				return;
			}else {
				systemUser = claimForFundsService.querUsers(phoneNo);
				if (systemUser==null){
					Write(returnPars(-1,"","亲爱的同事,登陆用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
			}
			if (AmountSorting==null||"".equals(AmountSorting)){
				AmountSorting = "DESC";
			}
			if (CreateDateSorting==null||"".equals(CreateDateSorting)){
				CreateDateSorting = "DESC";
			}
			PageResponse response = claimForFundsService.getAllMoneyApply(systemUser,opType,AmountSorting,CreateDateSorting,type,groupCode,page);
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response));
		} catch (Exception e) {
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,工单数据查询异常,请联系管理员处理!"));
		}
	}


	/**
	 * @Description TODO 根据资金流水号和集团编号查询明细信息
	 * <AUTHOR>
	 * @Date 2022/6/13 16:20
	 **/
	public void getAllMoneyApplyDet() {
		try {
			String SerialNo = getString("SerialNo");		//流水号
			String unit = getString("unit");			//集团编号
			String type = getString("querytype");			//查询类型
			if (SerialNo==null||unit==null){
				Write(returnPars(-1,"","亲爱的同事,参数格式异常,请刷新页面重试!"));
				return;
			}
			List<Map<String,String>> amountList = claimForFundsService.getAllMoneyApplyDet(SerialNo,unit,type);//查询可冲正的额数据
			if (amountList.size()>0){
				Write(returnPars(1,JSONHelper.SerializeWithNeedAnnotationDateFormat(amountList),"查询成功!"));
			}else {
				Write(returnPars(-1,"","亲爱的同事,当前资金:"+SerialNo+" 中未查询对应的明细信息!"));
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,资金明细数据查询异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据工单编号查询明细列表
	 * <AUTHOR>
	 * @Date 2022/6/13 14:16
	 **/
	public void getMoneyApplyDet(){
		Result result = ResultGenerator.genSuccessResult();
		try {
			String id = getString("id");
			MoneyApply moneyApply = claimForFundsService.getMoneyApply(id);
			List<MoneyApplyDet> list = claimForFundsService.getMoneyApplyDetByApplyNo(moneyApply.getApplyNo());
			if (list.size()>0){
				result.setCode(ResultCode.SUCCESS);
				result.setMessage("成功！");
				result.setData(list);
			}else {
				result.setCode(ResultCode.FAIL);
				result.setMessage("未查询到对应的明细信息！");
			}
			Write(result.toString());
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("明细信息查询数据异常"+e.getMessage(),e);
			result.setCode(ResultCode.FAIL);
			result.setMessage("明细信息查询数据异常");
			Write(result.toString());
		}
	}

	/**
	 * @Description TODO 根据资金工号查询是否为移动通用账户
	 * <AUTHOR>
	 * @Date 2022/10/19 16:41
	 **/
	public void queryTotalUnlimitedAccountById() {
		try{
			String id = getString("id");
			MoneyTotal p = claimForFundsService.getMoneyTotal(id);
			if (p!=null){
				Map<String,String> accountMap = claimForFundsService.getUnlimitedAccountByNumber(p.getOtherAccNumber());
				if (accountMap!=null){
					Write(returnPars(1,accountMap,"亲爱的同事，检测到当前资金账户为财务资金通用账户,由于规则限制该类账户无法绑定固定集团，请手动进行认领！ "));
				}else {
					Write(returnPars(2,"","亲爱的同事，由于业务规则限制，现已关闭集团认领渠道，请到政企订单系统集团账户管理模块进行账户关系绑定后再进行认领操作，如已绑定了关系请点击下方【更新数据】按钮更新资金信息！ "));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事，未查询到当前资金信息：【"+id+"】，请刷新页面重试或联系管理员处理！ "));
			}
		}catch (Exception e){
			logger.info("查询资金信息异常:"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，查询资金信息异常【"+e.getMessage()+"】，请联系管理员处理！ "));
		}
	}

	/**
	 * @Description TODO 根据资金id更新自动认领信息
	 * <AUTHOR>
	 * @Date 2022/10/19 16:50
	 **/
	public void queryTotalUpdateById() {
		try {
			String id = getString("id");
			MoneyTotal p = claimForFundsService.getMoneyTotal(id);
			if (p!=null){
				if (p.getState()!=6 && p.getState()!=0){
					Write(returnPars(-1,p,"亲爱的同事，当前资金已被处理不能进行更新数据，请确认！ "));
					return;
				}else {
					claimForFundsService.deleteTaxpayer(p.getBatchNo(),p.getSerialNo());
				}
				//是否为财务资金通用账户
				Map<String,String> accountMap = claimForFundsService.getUnlimitedAccountByNumber(p.getOtherAccNumber());
				if (accountMap!=null){
					//是否已经被预占  已经预占需解除预占
					if (p.getState()==6){
						p.setState(0);
						claimForFundsService.updateMoneyTotal(p);
					}
					Write(returnPars(3,accountMap,"亲爱的同事，检测到当前资金账户为财务资金通用账户,由于规则限制该类账户无法绑定固定集团，如需使用该资金请手动进行认领！ "));
				}else{
					JSONArray groupArray =getGroupAccountByAccount(p.getOtherAccNumber());
					if (groupArray.size()>0){
						for (int i = 0;i<groupArray.size();i++){
							JSONObject json = groupArray.getJSONObject(i);
							List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDetByGroup(json.getString("CUST_CODE"));
							if(replaceModelDets.size()>0){
								for (int r = 0;r<replaceModelDets.size();r++){
									ClaimReplaceModelDet replaceModelDet = replaceModelDets.get(r);
									List<Map<String,Object>> userLists=claimForFundsService.getCountyByUserID(replaceModelDet.getCreationRow().toString());
									if (userLists.size()>0 && userLists.get(0).get("BOSSUSERNAME")!=null){
										String companycCode = String.valueOf(userLists.get(0).get("COMPANY_CODE"));
										if ("00".equals(companycCode)){
											companycCode = "01";
										}
										if (companycCode.equals(p.getCompanyCode())){
											Taxpayer taxpayer = new Taxpayer();
											taxpayer.setBatchNo(p.getBatchNo());
											taxpayer.setSerialNo(p.getSerialNo());
											taxpayer.setOtherAccNumber(p.getOtherAccNumber());
											taxpayer.setGroupCode(json.getString("CUST_CODE"));
											taxpayer.setGroupName(json.getString("CUST_NAME"));
											taxpayer.setUserId(replaceModelDet.getCreationRow());
											taxpayer.setUserBossName(String.valueOf(userLists.get(0).get("BOSSUSERNAME")));
											taxpayer.setCreateDate(new Date());
											taxpayer.setState(0);
											taxpayer.setAccountState(3);
											taxpayer.setReplcaceId(replaceModelDet.getId());
											claimForFundsService.saveTaxpayer(taxpayer);
										}
									}else {
										logger.info("资金认领更新预占信息，代理用户不存在或未配置工号："+replaceModelDet.getCreationRow().toString());
									}
								}
							}else {
								SystemUser user=claimForFundsService.querUsersByBossNo(json.getString("BOSS"));
								if (user!=null){
									String code = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
									if ("00".equals(code)){
										code = "01";
									}
									if (code.equals(p.getCompanyCode())){
										Taxpayer taxpayer = new Taxpayer();
										taxpayer.setBatchNo(p.getBatchNo());
										taxpayer.setSerialNo(p.getSerialNo());
										taxpayer.setOtherAccNumber(p.getOtherAccNumber());
										taxpayer.setGroupCode(json.getString("CUST_CODE"));
										taxpayer.setGroupName(json.getString("CUST_NAME"));
										taxpayer.setUserId(user.getRowNo());
										taxpayer.setUserBossName(user.getBossUserName());
										taxpayer.setCreateDate(new Date());
										taxpayer.setState(0);
										taxpayer.setAccountState(json.getInt("BANK_ACCOUNT_STATUS"));
										claimForFundsService.saveTaxpayer(taxpayer);
									}
								}else {
									logger.info("资金认领更新预占信息，工号不存在："+json.getString("BOSS"));
								}
							}
						}
					}
					List<Taxpayer> taxpayersTwo = claimForFundsService.getTaxpayerList(p.getBatchNo());
					if (p.getState()==0 && taxpayersTwo.size()>0){
						p.setState(6);
						claimForFundsService.updateMoneyTotal(p);
					}else if (p.getState()==6 && taxpayersTwo.size()==0){
						p.setState(0);
						claimForFundsService.updateMoneyTotal(p);
					}
					if (taxpayersTwo.size()>0){
						Write(returnPars(1,taxpayersTwo,"亲爱的同事，数据更新完成，当前账户现有绑定关系："+taxpayersTwo.size()+" 条！"));
					}else {
						Write(returnPars(2,new JSONArray(),"亲爱的同事，数据更新完成，当前账户暂未查询到绑定关系！"));
					}
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事，未查询到当前资金信息：【"+id+"】，请刷新页面重试或联系管理员处理！ "));
			}
		} catch (Exception e) {
			TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
			logger.info("更新资金信息异常:"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，更新资金信息异常【"+e.getMessage()+"】，请联系管理员处理！ "));
		}
	}

	public JSONArray getGroupAccountByAccount(String bankAccount){
		JSONArray group = new JSONArray();
		try{
			if (bankAccount!=null && !"".equals(bankAccount)){
				JSONObject json = GroupAccountSrv.getInstance().QueryGroupAccountByAccount(bankAccount);
				JSONObject hander = json.getJSONObject("HEADER");
				JSONObject response = hander.getJSONObject("RESPONSE");
				if (response.has("CODE") && response.getString("CODE").equals("0000") && json.has("RESULT")) {
					group = json.getJSONArray("RESULT");
					if (group.size() > 0) {
						Iterator<JSONObject> iter = group.iterator();
						while (iter.hasNext()) {
							JSONObject result = iter.next();
							SystemUser user = claimForFundsService.querUsersByBossNo(result.getString("BOSS"));
							if (user == null) {
								iter.remove();
							} else {
								if (claimForFundsService.queryGroupCustomerById(result.getString("CUST_CODE")) == null) {
									GroupCustomer findDBCustomer = new GroupCustomer();
									findDBCustomer.setGroupCoding(result.getString("CUST_CODE"));
									findDBCustomer.setGroupName(result.getString("CUST_NAME"));
									findDBCustomer.setGroupLevel(result.getString("CUST_VALUE"));
									findDBCustomer.setContactAddress(result.getString("CUST_ADDR"));
									findDBCustomer.setCity(result.getString("CITY"));
									findDBCustomer.setUser_name(user.getBossUserName());
									findDBCustomer.setChinese_name(user.getEmployeeName());
									findDBCustomer.setMobile_phone(user.getMobile());
									claimForFundsService.savaGroupCustomer(findDBCustomer);
									logger.info("添加集团成功！");
								}
							}
						}
					}
				}
			}
			return group;
		}catch (Exception e){
			e.printStackTrace();
			logger.info("资金自动认领错误信息："+e.getMessage());
			return group;
		}
	}

	/**
	 * @Description TODO 根据资金id获取资金信息
	 * <AUTHOR>
	 * @Date 2022/6/13 16:25
	 **/
	public void getMoneyTotal() {
		try {
			String Totalid = getString("Totalid");
			MoneyTotal p = claimForFundsService.getMoneyTotal(Totalid);
			Map<String,Object> retMap = beanToMap(p);
			if (p.getReplcaceId()!=null && !p.getReplcaceId().equals("")){
				ClaimReplaceModelDet replaceModelDet = modelService.queryReplaceModelDetById(p.getReplcaceId());
				retMap.put("replaceModel",replaceModelDet);
			}
			if (p.getProvisionalId()!=null && !p.getProvisionalId().equals("")){
				MoneytotalProvisional provisional = claimForFundsService.getProvisionalByCode(p.getProvisionalId());
				retMap.put("moneytotalProvisional",provisional);
			}
			Write(returnPars(1, retMap, "查询成功"));
		} catch (Exception e) {
			e.printStackTrace();
			Write(returnPars(-1, "", "查询失败,查询资金信息异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据用户和资金查询自动认领集团信息
	 * <AUTHOR>
	 * @Date 2022/8/16 16:13
	 **/
	public void querClaimTax() {
		try {
			String id = getString("id");                      //资金id
			String phoneNo = getString("phoneNo");					//登陆人电话号码
			SystemUser systemUser = null;
			if (phoneNo==null){
				Write(returnPars(-1,"","亲爱的同事,缺失关键参数数据查询异常,请刷新页面重试!"));
				return;
			}else {
				systemUser = claimForFundsService.querUsers(phoneNo);
				if (systemUser==null){
					Write(returnPars(-1,"","亲爱的同事,登陆用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
			}
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			if (moneyTotal == null) {
				Write(returnPars(-1, "", "亲爱的同事，资金信息异常，请刷新页面重试或联系管理员处理！"));
			} else if (moneyTotal.getState().equals(6)) {
				Map<String,Object> returnMap = new HashMap<>();
				//可以操作的集团
				List<Map<String, String>> taxpayersOne = claimForFundsService.getTaxpayerListTwo(moneyTotal.getBatchNo(),String.valueOf(systemUser.getRowNo()));
				//不能操作的集团
				List<Map<String, String>> taxpayersTwo = claimForFundsService.getTaxpayerListFour(moneyTotal.getBatchNo(),String.valueOf(systemUser.getRowNo()));

				returnMap.put("myTaxpayers",taxpayersOne);
				returnMap.put("taxpayers",taxpayersTwo);
				if (taxpayersOne.size()>0){
					returnMap.put("flag",true);
				}else {
					returnMap.put("flag",false);
				}
				Write(returnPars(1,returnMap,"查询成功！"));
			} else {
				Write(returnPars(2, moneyTotal, "亲爱的同事，当前资金预占已解除【" + moneyTotal.getState() + "】，请确认！"));
			}
		} catch (Exception e) {
			logger.info("查询认领集团信息异常"+e.getMessage());
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，查询认领集团信息异常,请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 根据暂收款编号查询暂收款信息
	 * <AUTHOR>
	 * @Date 2022/7/12 10:05
	 **/
	public void queryMoneytotalProvisional(){
		try {
			String provisionalId = getString("provisionalId");                      //暂收款编号
			if (provisionalId==null || "".equals(provisionalId)){
				Write(returnPars(-1,"","亲爱的同事，暂收款信息查询失败【id:"+provisionalId+"】,请联系管理员处理！"));
			}
			MoneytotalProvisional provisional = claimForFundsService.getProvisionalByCode(provisionalId);
			if (provisional!=null){
				Write(returnPars(1,provisional,"查询成功！"));
			}else {
				Write(returnPars(-1,"","亲爱的同事，资金对应暂收款信息异常【id:"+provisionalId+"】,请联系管理员处理！"));
			}
		}catch (Exception e){
			logger.info("查询暂收款信息异常"+e.getMessage());
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，查询暂收款信息异常,请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 查询当前用户代理集团信息
	 * <AUTHOR>
	 * @Date 2022/5/9 15:13
	 **/
	public void appQueryReplaceGroup(){
		try{
			String groupCoding = getString("groupCoding");
			String groupName = getString("groupName");
			String userPhoneNum = getString("userPhoneNum");
			PageRequest page = new PageRequest(getRequest());
			if (userPhoneNum.length()<1){
				Write(returnPars(-1, "", "亲爱的同事，请求异常获取用户信息失败，请联系管理员处理！"));
				return;
			}
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1, "", "亲爱的同事，获取用户【"+userPhoneNum+"】信息失败，请联系管理员处理！"));
				return;
			}
			PageResponse response = modelService.queryReplaceModelDetsByUserRow(groupCoding,groupName,user.getRowNo(),page);
			Write(returnPars(1,response,"查询成功！"));
		}catch (Exception e){
			logger.info("查询代理集团信息异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，查询代理集团信息异常，请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 根据代理集团编号获取集团信息并更新数据库
	 * <AUTHOR>
	 * @Date 2022/5/11 12:06
	 **/
	public void queryCustomer(){
		String id=getString("id");
		Map<String,Object> mapJson = new HashMap<>();
		ClaimReplaceModelDet replaceModelDet = modelService.queryReplaceModelDetById(id);
		if (replaceModelDet==null){
			mapJson.put("code",-1);
			mapJson.put("data","");
			mapJson.put("msg","亲爱的同事，代理集团查询数据获取失败，请联系管理员处理！");
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
			return;
		}
		try {
			if(isES) {
				//接口调用反馈数据信息
				GroupCustomer customer = NewGroupCustomerService.getInstance().getCustInfoQuery(replaceModelDet.getGroupCode(), replaceModelDet.getGroupName());
				///判断集团是否存在
				if (customer.getGroupCoding() != null && customer.getGroupName() != null) {
					GroupCustomer findDBCustomer = groupCustomerService.queryGroup(customer.getGroupCoding().trim());
					//判断数据库是否存在集团客户信息
					if (null == findDBCustomer) {
						groupCustomerService.addGroupCustomer(customer);
						findDBCustomer = customer; //赋值
					} else {
						//数据库存在数据则更新数据信息
						findDBCustomer.setGroupCoding(customer.getGroupCoding());
						findDBCustomer.setGroupName(customer.getGroupName());
						findDBCustomer.setGroupLevel(customer.getGroupLevel());
						findDBCustomer.setHomeRegion(customer.getHomeRegion());
						findDBCustomer.setContactAddress(customer.getContactAddress());
						findDBCustomer.setContacts(customer.getContacts());
						findDBCustomer.setContactPhone(customer.getContactPhone());
						findDBCustomer.setCity(customer.getCity());
						findDBCustomer.setUser_name(customer.getUser_name());
						findDBCustomer.setChinese_name(customer.getChinese_name());
						findDBCustomer.setMobile_phone(customer.getMobile_phone());
						findDBCustomer.setLongitude(customer.getLongitude());
						findDBCustomer.setLatitude(customer.getLatitude());
						findDBCustomer.setReal_type(customer.getReal_type());
						groupCustomerService.updateGroupCustomer(findDBCustomer);
					}
					replaceModelDet.setGroupName(customer.getGroupName());
					replaceModelDet.setLblLevel(customer.getGroupLevel());
					replaceModelDet.setContactAddress(customer.getContactAddress());
					replaceModelDet.setAscriptionName(customer.getChinese_name());
					replaceModelDet.setAscriptionBossName(customer.getUser_name());
					replaceModelDet.setAscriptionPhone(customer.getMobile_phone());
					modelService.updateClaimReplaceModelDet(replaceModelDet);

					List<GroupCustomer> list = new ArrayList<GroupCustomer>();
					list.add(findDBCustomer);
					mapJson.put("code",1);
					mapJson.put("data",list);
					mapJson.put("msg","集团查询成功");
					Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
				}
			}else {
				List<GroupCustomer> map =integrationService.dolist(replaceModelDet.getGroupCode(), replaceModelDet.getGroupName());
				mapJson.put("code",1);
				mapJson.put("data",map);
				mapJson.put("msg","集团查询成功");
				Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
			}
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("App集团查询异常"+e.getMessage(),e);
			List<GroupCustomer> map =integrationService.dolist(replaceModelDet.getGroupCode(), replaceModelDet.getGroupName());
			mapJson.put("code",1);
			mapJson.put("data",map);
			mapJson.put("msg","集团查询成功");
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
		}
	}

	/**
	 * @Description TODO 根据流水查询资金信息
	 * <AUTHOR>
	 * @Date 2022/10/25 14:42
	 **/
	public void qryMoneyTotalBySerialNo(){
		try {
			String serialNo = getString("serialNo");
			List<MoneyTotal> totals = claimForFundsService.getMoneyTotalSerialNoList(serialNo);
			if (totals.size()>0){
				SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				List<Map<String,String>> retList = new ArrayList<>();
				for (MoneyTotal moneyTotal:totals){
					Map<String,String> retMap = new HashMap<>();
					retMap.put("uuid",moneyTotal.getId());
					retMap.put("otherName",moneyTotal.getOtherName());
					retMap.put("otherAccNumber",moneyTotal.getOtherAccNumber());
					retMap.put("tranDate",formatter.format(moneyTotal.getTranDate()));
					retMap.put("amount",moneyTotal.getAmount());
					retMap.put("companyName",moneyTotal.getCompanyName());
					retMap.put("isSubGroup",String.valueOf(moneyTotal.getIs_sub_group()));

					retMap.put("state",String.valueOf(moneyTotal.getState()));
					if (moneyTotal.getState().equals(1) || moneyTotal.getState().equals(5)){
						SystemUser user = systemUserService.getByUserInfoRowNo(moneyTotal.getUserid());
						retMap.put("userName",user.getEmployeeName());
						retMap.put("groupCode",moneyTotal.getGroupCode());
						retMap.put("groupName",moneyTotal.getGroupName());
					}else {
						retMap.put("userName","");
						retMap.put("groupCode","");
						retMap.put("groupName","");
					}

					retList.add(retMap);
				}
				Write(returnPars(1,retList,"查询成功！"));
			}else {
				Write(returnPars(2,new JSONArray(),"亲爱的同事，根据资金流水："+serialNo+" ,未查询到资金信息！"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("根据流水查询资金信息异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，根据流水查询资金信息异常,请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 根据集团280编号查询280下可用的缴费计划
	 * <AUTHOR>
	 * @Date 2022/4/9 10:32
	 **/
	public void queryClaimForFundModel(){
		try {
			String groupcode = getString("groupcode");
			if (groupcode==null || groupcode.equals("")){
				Write(returnPars(-1,"","亲爱的同事，获取计划信息失败[集团编号异常："+groupcode+"],请联系管理员处理！"));
				return;
			}
			List<ClaimForFundModel> models = modelService.getClaimForFundModelByGroupCode(groupcode);
			if (models.size()>0){
				Write(returnPars(1,models,"数据查询成功！"));
			}else {
				Write(returnPars(-1,"","亲爱的同事，当前集团编号【"+groupcode+"】暂无可使用的缴费模型,请确认！"));
			}
		}catch (Exception e){
			logger.info("资金缴费计划获取异常："+e,e);
			Write(returnPars(-1,"","亲爱的同事，资金缴费计划获取异常,请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 根据缴费计划编号查询计划详细信息
	 * <AUTHOR>
	 * @Date 2022/4/8 12:46
	 **/
	public void queryClaimFoundModel(){
		try{
			String orderId = getString("id");                               //计划编号
			String otherAccountNumber = getString("otherAccountNumber");    //对方打款账户
			if (orderId==null || "".equals(orderId)){
				Write(returnPars(-1,"","亲爱的同事，资金缴费计划数据查询失败，获取计划编号信息失败,请联系管理员处理！"));
				return;
			}
			ClaimForFundModel model = modelService.getClaimForFundModelById(orderId);
			if (model==null){
				Write(returnPars(-1,"","亲爱的同事，计划编号异常，获取计划信息失败,请联系管理员处理！"));
				return;
			}
			GroupRelations groupRelations = groupAccountService.queryGroupRelations(model.getApplyGruopNo(),otherAccountNumber);
			List<ClaimForFundModelDet> dets = modelService.getClaimForFundModelDetbyApplyNo(model.getApplyNo());
			List<ClaimForFundModelDet> detList = new ArrayList<>();
			List<GroupHipAccount> groupHipAccount = new ArrayList<>();
			Double amout = 0.00;
			for (ClaimForFundModelDet det:dets){
				if ("1".equals(det.getContrctType())){
					groupHipAccount = groupAccountService.queryGroupHipAccount(groupRelations.getId(),"",det.getContrctNo());
				}else {
					groupHipAccount = groupAccountService.queryGroupHipAccount(groupRelations.getId(),det.getContrctNo(),"");
				}

				if (groupHipAccount.size()>0) {
					amout +=Double.parseDouble(det.getAmount());
					detList.add(det);
				}
			}
			model.setApplyAmount(amout.toString());
			Map<String,Object> map = new HashMap<String,Object>();
			map.put("ClaimForFundModel",model);
			map.put("ClaimForFundModelDet",detList);
			Write(returnPars(1,map,"数据查询成功！"));
		}catch (Exception e){
			logger.info("资金缴费计划详细信息获取异常："+e,e);
			Write(returnPars(-1,"","亲爱的同事，资金缴费计划详细信息获取异常,请联系管理员处理！"));
		}
	}

	/**
	 * @Description: app获取人员
	 * @return: void
	 * @Author: TX
	 * @Date: 2021/12/7 16:53
	 */
	public void queryImpetUser(){
		List<Map<String, String>> listMap= new ArrayList<>();
		try {
			String name = getString("name");//模糊查询数据（根据名称）

			String roleName = getString("roleName");//角色CODE
			String Collection = getString("Collection");//是否查询收藏 1:是    0:不是

			String userId = getString("userId");//当前登录人ID
			List<Map<String, String>> systemUser = claimForFundsService.getVwUserinf(userId);
			if (systemUser.size()!=1){
				Write(returnPars(-1,"","亲爱的同事,用户视图信息异常,未查询到[工号为:"+userId+"]的用户,请联系管理员处理!"));
				return;
			}
			if (roleName==null||"".equals(roleName)){
				Write(returnPars(-1,"","亲爱的同事,查询角色["+roleName+"]异常,请刷新重试或联系管理员处理!"));
				return;
			}else if (roleName.equals("ALL")){
				if (name != null && !"".equals(name)) {
					listMap = claimForFundsService.queryVwUserinfo(name,systemUser.get(0),null,Collection);
				}else {
					Write(returnPars(-1,"","亲爱的同事,请输入用户名称进行查询!"));
					return;
				}
			}else{
				Role role = claimForFundsService.getRoleByCode(roleName);
				if (role==null){
					Write(returnPars(-1,"","亲爱的同事,角色["+roleName+"]信息异常,请联系管理员处理!"));
					return;
				}
				listMap = claimForFundsService.queryVwUserinfo(name,systemUser.get(0),role,Collection);
			}
			List<Map<String, String>> collectedList = structureOfPersonnelService.getTopcontactsUser(userId, name);
			for (Map<String, String> stringMap : listMap) {
				String employee_name = stringMap.get("EMPLOYEE_NAME");
				String pinyinInitials ="";
				if(null != employee_name && !"null".equals(employee_name) && !"".equals(employee_name)){
					if(employee_name.indexOf("_") != -1){
						pinyinInitials = employee_name.substring(employee_name.lastIndexOf("_")+1);
					}else{
						pinyinInitials = employee_name;
					}
				}
				if(!"".equals(pinyinInitials)){
					String pinyinInitials1 = getPinyin(pinyinInitials,"_");
					String toHump = lineToHump(pinyinInitials1);
					toHump =toHump.substring(0, 1).toUpperCase() + toHump.substring(1);
					stringMap.put("pinyin",toHump);
				}else{
					stringMap.put("pinyin","");
				}
				if(null != collectedList && !collectedList.isEmpty()){
					for (Map<String, String> collected : collectedList) {
						Object mapRowno = stringMap.get("ROWNO");
						Object collRowno = collected.get("ROWNO");
						if(String.valueOf(mapRowno).equals(String.valueOf(collRowno))){
							stringMap.put("collected","true");
							break;
						}else{
							stringMap.put("collected","false");
						}
					}
				}else{
					stringMap.put("collected","false");
				}
			}
			Write(returnPars(1,listMap,"查询完成!"));
		}catch (Exception e){
			e.printStackTrace();
			Write(returnPars(-1, "", "查询失败,查询人员信息异常,请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 根据集团编号和电话号码查询集团对应关系
	 * <AUTHOR>
	 * @Date 2022/6/13 17:08
	 **/
	public void appGroupAssociation(){
		String groupCode = getString("groupCode");
		String phone = getString("phone");
		if (groupCode.length()<1){
			Write(returnPars(-1, "", "亲爱的同事，请求数据异常获取集团编号失败！"));
			return;
		}
		if (phone.length()<1){
			Write(returnPars(-1, "", "亲爱的同事，请求数据异常获取用户号码失败！"));
			return;
		}
		List<Map<String,String>> list = claimForFundsService.queryGroupAssociation(groupCode,phone);
		if (list.size()>0){
			Write(returnPars(1, list, "校验成功!"));
		}else {
			Write(returnPars(-1, "", "亲爱的同事，用户["+phone+"] 不是集团["+groupCode+"] 的B库成员，无法缴费，请确认！"));
		}
	}

	/**
	 * @Description TODO 根据账户号码查询账户信息
	 * <AUTHOR>
	 * @Date 2022/5/26 15:13
	 **/
	public void qryAoSvc(){
		try {
			String contractNo = getString("contractNo");
			String userPhone = getString("userPhone");//手机号码
			SystemUser user = claimForFundsService.querUsers(userPhone);
			if (contractNo==null){
				Write(returnPars(-1,"","亲爱的同事，账户信息获取异常，请刷新页面重试或联系管理员处理！"));
				return;
			}
			if (user==null || user.getBossUserName()==null){
				Write(returnPars(-1,"","亲爱的同事，当前登录用户信息异常，请联系管理员处理！"));
				return;
			}
			List<Map<String,String>> vwUserList = claimForFundsService.getVwUserinf(String.valueOf(user.getRowNo()));
			if (vwUserList.size()!=1){
				Write(returnPars(-1,"","亲爱的同事,用户信息异常["+vwUserList.size()+"],请联系管理员处理!"));
				return;
			}
			Result result = ClaimFundsOpenSrv.getInstance().qryAoSvc("d181800001","CONTRACT_NO",contractNo,vwUserList.get(0).get("COMPANY_CODE"),user.getBossUserName());
			if (result.getCode()==ResultCode.SUCCESS.code()){
				JSONObject json = JSONObject.fromObject(result.getData());
				JSONObject root = JSONObject.fromObject(json.get("ROOT"));
				if ("0".equals(root.getString("RETURN_CODE"))){
					JSONObject OUT_DATA = root.getJSONObject("OUT_DATA");
					if (OUT_DATA.has("LIST")){
						JSONArray LIST = JSONArray.fromObject(OUT_DATA.get("LIST"));
						if (LIST.size()>0){
							Write(returnPars(1,LIST,"查询成功"));
						}else {
							Write(returnPars(1,LIST,"亲爱的同事，检测到当前账户为空账户，缴费到账后将无法冲正，请再次确认缴费信息！！！"));
						}
					}else {
						Write(returnPars(1,new ArrayList<>(),"亲爱的同事，检测到当前账户为空账户，缴费到账后将无法冲正，请再次确认缴费信息！！！"));
					}
				}else {
					Write(returnPars(-1,"",root.getString("RETURN_MSG")));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事,调用账户信息查询接口异常["+result.getMessage()+"],请联系管理员处理!"));
			}
		}catch (Exception e){
			logger.info("账户信息查询接口异常:"+e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,账户信息查询接口异常["+e.getMessage()+"],请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 资金回退（提供给财务系统接口，回退未使用的资金）
	 * <AUTHOR>
	 * @Date 2022/11/18 16:11
	 **/
	public void capitalFallback(){
		JSONObject detailmsg = new JSONObject();
		try{
			String content = UrlConnection.getRequestData(getRequest());
			if ("".equals(content)) {
				logger.info("资金回退接口传递参数异常");
				detailmsg.put("RETURN_CODE", "400");
				detailmsg.put("RETURN_MSG", "资金回退传递参数异常");
				detailmsg.put("RETURN_TIME", taskService.getNumber());
				Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
			}else{
				logger.info("资金回退接口调用数据信息:" + content);
				String json = URLDecoder.decode(content, "UTF-8");
				JSONObject obj = JSONObject.fromObject(json);
				if (obj.has("TRANSACTION_ID")){
					detailmsg.put("TRANSACTION_ID", obj.getString("TRANSACTION_ID"));
				}else {
					detailmsg.put("RETURN_CODE", "400");
					detailmsg.put("RETURN_MSG", "资金回退传递参数异常,TRANSACTION_ID为空");
					detailmsg.put("RETURN_TIME", taskService.getNumber());
					Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
					return;
				}

				if (obj.has("SERIAL_NO") && !"".equals(obj.getString("SERIAL_NO"))){
					String serialNo = obj.getString("SERIAL_NO");
					MoneyTotal total = claimForFundsService.getMoneyTotalSerialNoAndSubGroup(serialNo,0);
					Moneytotal_Temporary temporary = claimForFundsService.getMoneytotalTemporary(serialNo);
					if (total!=null){
						if (total.getState().equals(6) || total.getState().equals(0)){
							total.setState(-1);
							total.setBoss_Msg("数据已于"+getStringDatetwo(new Date())+"通过财务资金退回接口进行退回，财务调用接口请求标识："+obj.getString("TRANSACTION_ID"));
							claimForFundsService.updateMoneyTotal(total);

							detailmsg.put("RETURN_CODE", "200");
							detailmsg.put("RETURN_MSG", "处理成功，已对资金："+total.getSerialNo()+"进行回退！");
							detailmsg.put("RETURN_TIME", taskService.getNumber());
							Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
						}else {
							detailmsg.put("RETURN_CODE", "201");
							detailmsg.put("RETURN_MSG", "资金："+total.getSerialNo()+"已在订单系统进行使用，不能进行回退！");
							detailmsg.put("RETURN_TIME", taskService.getNumber());
							Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
						}
					}else if (temporary!=null){
						if ("1".equals(temporary.getInsertState())){
							total = claimForFundsService.getMoneyTotalSerialNoAndSubGroup(serialNo,0);
							if (total!=null){
								if (total.getState().equals(6) || total.getState().equals(0)){
									total.setState(-1);
									total.setBoss_Msg("数据已于"+getStringDatetwo(new Date())+"通过财务资金退回接口进行退回，财务调用接口请求标识："+obj.getString("TRANSACTION_ID"));
									claimForFundsService.updateMoneyTotal(total);

									detailmsg.put("RETURN_CODE", "200");
									detailmsg.put("RETURN_MSG", "处理成功，已对资金："+total.getSerialNo()+"进行回退！");
									detailmsg.put("RETURN_TIME", taskService.getNumber());
									Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
								}else {
									detailmsg.put("RETURN_CODE", "201");
									detailmsg.put("RETURN_MSG", "资金："+total.getSerialNo()+"已在订单系统进行使用，不能进行回退！");
									detailmsg.put("RETURN_TIME", taskService.getNumber());
									Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
								}
							}else {
								detailmsg.put("RETURN_CODE", "202");
								detailmsg.put("RETURN_MSG", "资金："+serialNo+"数据异常，请联系管理员处理！");
								detailmsg.put("RETURN_TIME", taskService.getNumber());
								Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
							}
						}else {
							temporary.setInsertState("-1");
							claimForFundsService.updateTemporary(temporary);

							detailmsg.put("RETURN_CODE", "200");
							detailmsg.put("RETURN_MSG", "处理成功，已对资金："+total.getSerialNo()+"进行回退！");
							detailmsg.put("RETURN_TIME", taskService.getNumber());
							Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
						}
					}else {
						detailmsg.put("RETURN_CODE", "203");
						detailmsg.put("RETURN_MSG", "资金："+serialNo+"尚未写入订单资金，请确认！");
						detailmsg.put("RETURN_TIME", taskService.getNumber());
						Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
					}
				}else if(obj.has("SERIAL_LIST")){
					JSONArray serialList = obj.getJSONArray("SERIAL_LIST");
					if (serialList.size()>0){
						for (int i = 0;i<serialList.size();i++){
							JSONObject serial = serialList.getJSONObject(i);
							if (serial.has("SERIAL_NO") && !"".equals(serial.getString("SERIAL_NO"))){
								String serial_no = serial.getString("SERIAL_NO");
								MoneyTotal serialTotal = claimForFundsService.getMoneyTotalSerialNoAndSubGroup(serial_no,0);
								Moneytotal_Temporary temporary = claimForFundsService.getMoneytotalTemporary(serial_no);
								if (serialTotal!=null){
									if (serialTotal.getState().equals(6) || serialTotal.getState().equals(0)){
										serialTotal.setState(-1);
										serialTotal.setBoss_Msg("数据已于"+getStringDatetwo(new Date())+"通过财务资金退回接口进行退回，财务调用接口请求标识："+obj.getString("TRANSACTION_ID"));
										claimForFundsService.updateMoneyTotal(serialTotal);

										serial.put("RETURN_CODE", "200");
										serial.put("RETURN_MSG", "处理成功，已对资金："+serialTotal.getSerialNo()+"进行回退！");
									}else {
										serial.put("RETURN_CODE", "201");
										serial.put("RETURN_MSG", "资金："+serialTotal.getSerialNo()+"已在订单系统进行使用，不能进行回退！");
									}
									serialList.set(i,serial);
								}else if (temporary!=null){
									if ("1".equals(temporary.getInsertState())){
										serialTotal = claimForFundsService.getMoneyTotalSerialNoAndSubGroup(serial_no,0);
										if (serialTotal!=null){
											if (serialTotal.getState().equals(6) || serialTotal.getState().equals(0)){
												serialTotal.setState(-1);
												serialTotal.setBoss_Msg("数据已于"+getStringDatetwo(new Date())+"通过财务资金退回接口进行退回，财务调用接口请求标识："+obj.getString("TRANSACTION_ID"));
												claimForFundsService.updateMoneyTotal(serialTotal);

												serial.put("RETURN_CODE", "200");
												serial.put("RETURN_MSG", "处理成功，已对资金："+serialTotal.getSerialNo()+"进行回退！");
											}else {
												serial.put("RETURN_CODE", "201");
												serial.put("RETURN_MSG", "资金："+serialTotal.getSerialNo()+"已在订单系统进行使用，不能进行回退！");
											}
											serialList.set(i,serial);
										}else {
											serial.put("RETURN_CODE", "202");
											serial.put("RETURN_MSG", "资金："+serial_no+"数据异常，请联系管理员处理！");
											serialList.set(i,serial);
										}
									}else {
										temporary.setInsertState("-1");
										claimForFundsService.updateTemporary(temporary);

										serial.put("RETURN_CODE", "200");
										serial.put("RETURN_MSG", "处理成功，已对资金："+serial_no+"进行回退！");
										serialList.set(i,serial);
									}
								}else {
									serial.put("RETURN_CODE", "203");
									serial.put("RETURN_MSG", "资金："+serial_no+"尚未写入订单资金，请确认！");
									serialList.set(i,serial);
								}
							}
						}
						detailmsg.put("RETURN_CODE", "200");
						detailmsg.put("RETURN_MSG", "批量退回处理完成，结果信息请查看处理明细！");
						detailmsg.put("SERIAL_LIST",serialList);
						detailmsg.put("RETURN_TIME", taskService.getNumber());
						Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
					}else {
						detailmsg.put("RETURN_CODE", "400");
						detailmsg.put("RETURN_MSG", "资金回退传递参数异常,缺少关键流水信息");
						detailmsg.put("RETURN_TIME", taskService.getNumber());
						Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
					}
				}else {
					detailmsg.put("RETURN_CODE", "400");
					detailmsg.put("RETURN_MSG", "资金回退传递参数异常,缺少关键流水信息");
					detailmsg.put("RETURN_TIME", taskService.getNumber());
					Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
				}
			}
		}catch (Exception e){
			logger.info("资金回退接口异常："+e.getMessage(),e);
			e.printStackTrace();
			detailmsg.put("RETURN_CODE", "-1");
			detailmsg.put("RETURN_MSG", "资金回退接口异常："+e);
			detailmsg.put("RETURN_TIME", taskService.getNumber());
			Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
		}
	}

	/**
	 * @Description TODO 提供给营销活动冲正存送（营销活动）接口
	 * <AUTHOR>
	 * @Date 2023/6/20 9:58
	 **/
	public void rectificMarketCapital() {
		JSONObject outmsg = new JSONObject();
		JSONObject detailmsg = new JSONObject();
		try {
			String content = UrlConnection.getRequestData(getRequest());
			if ("".equals(content)) {
				logger.info("营销活动冲正接口传递参数异常");
				detailmsg.put("RETURN_CODE", "501");
				detailmsg.put("RETURN_MSG", "营销活动冲正接口传递参数异常");
				detailmsg.put("RETURN_TIME", taskService.getNumber());
				Write(JSONHelper.SerializeWithNeedAnnotation(detailmsg));
			} else {
				logger.info("营销活动冲正接口调用数据信息:" + content);
				String json = URLDecoder.decode(content, "UTF-8");
				JSONObject obj = JSONObject.fromObject(json);
				JSONObject root = obj.getJSONObject("ROOT");
				JSONObject data = root.getJSONObject("BODY");
				outmsg.put("HEADER", root.getJSONObject("HEADER"));
				if (data.has("TRANSACTION_ID")) {
					detailmsg.put("TRANSACTION_ID", data.getString("TRANSACTION_ID"));
				} else {
					detailmsg.put("RETURN_CODE", "502");
					detailmsg.put("RETURN_MSG", "缺少参数字段：TRANSACTION_ID为空");
					detailmsg.put("RETURN_TIME", taskService.getNumber());
					outmsg.put("BODY", detailmsg);
					Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
					return;
				}

				if (data.has("FLUSH_NUMBER") && data.has("OPERATE_TYPE") ) {
					String flushNumber = data.getString("FLUSH_NUMBER");			//冲正流水
					String operateType = data.getString("OPERATE_TYPE");			//操作类型
					if (flushNumber.isEmpty() || operateType.isEmpty()){
						detailmsg.put("RETURN_CODE", "503");
						detailmsg.put("RETURN_MSG", "营销活动冲正接口传递参数异常,传递数据为空，请核实");
						detailmsg.put("RETURN_TIME", taskService.getNumber());
						outmsg.put("BODY", detailmsg);
						Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
					}else {
						MoneyApplyDet det = claimForFundsService.getMoneyApplyDetByNumber(flushNumber);
						if (det==null){
							detailmsg.put("RETURN_CODE", "504");
							detailmsg.put("RETURN_MSG", "未查询到流水："+flushNumber+",对应缴费信息！");
							detailmsg.put("RETURN_TIME", taskService.getNumber());
							outmsg.put("BODY", detailmsg);
							Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
						}else if ("2".equals(det.getState())){
							detailmsg.put("RETURN_CODE", "505");
							detailmsg.put("RETURN_MSG", "查询到流水："+flushNumber+",未完成冲正流程！！");
							detailmsg.put("RETURN_TIME", taskService.getNumber());
							outmsg.put("BODY", detailmsg);
							Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
						}else {
							if ("01".equals(operateType)){
								MoneyApply my = claimForFundsService.getMoneyApplyByApplyNo(det.getApplyNo());//查询工单信息
								MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());//查询工单对应的资金池
								SystemUser pushBossUser = systemUserService.getUserInfoRowNo(moneyTotal.getPushUserName());
								if (pushBossUser.getBossUserName() == null || "".equals(pushBossUser.getBossUserName())) {
									detailmsg.put("RETURN_CODE", "506");
									detailmsg.put("RETURN_MSG", "冲正用户信息异常，编号："+moneyTotal.getPushUserName());
									detailmsg.put("RETURN_TIME", taskService.getNumber());
									outmsg.put("BODY", detailmsg);
									Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
									return;
								}

								String login_no = pushBossUser.getBossUserName();       //BOSS工号
								String unit_id = moneyTotal.getGroupCode();             //集团280
								String purpose = (moneyTotal.getMemo()!=null?moneyTotal.getMemo():"资金冲正");               //资金用途
								String bank_account = moneyTotal.getOtherAccNumber();   //资金池的对方账户
								String bank_account_name = moneyTotal.getOtherName();   //资金池的对方户名
								String pay_type = "0";
								String out_back_accept = det.getMoneyNo();    //需要冲正工单的流水
								String busi_flag;               //标识集团还是个人认领： G集团，P个人
								String busi_type = "02";               //业务类型
								String route_key = "";
								String contract_no = "";
								String phone_no = "";
								String userIdNo = "";
								String customerNumber = "";

								if ("1".equals(det.getOrderType())){
									busi_flag = "P";
									phone_no = det.getContrctNo();
								}else {
									busi_flag = "G";
									contract_no = det.getContrctNo();
								}

								//金额（元）
								String busi_fee = BigDecimal.valueOf(Long.parseLong(det.getAmount())).divide(new BigDecimal(100)).toString();
								String delay_rate = det.getLateFee();         //是否减免滞纳金
								String out_sys_accept = det.getOldMoneyNo();     //冲正工单流水

								Result applyRes=ClaimFundsOpenSrv.getInstance().ReversalApplyForFunds(login_no,unit_id,out_back_accept,bank_account,bank_account_name,busi_type,
										busi_fee,busi_flag,contract_no,delay_rate,purpose,out_sys_accept,route_key,pay_type,phone_no,userIdNo,customerNumber);
								logger.info("====>营销活动接口冲正存送信息："+applyRes.toString());
								if(ResultCode.SUCCESS.code()==applyRes.getCode()) {  //判断当前请求是否成功
									JSONObject applyObj=JSONObject.fromObject(applyRes.getData());
									//循环推送申请工单中的明细记录，成功并记录成功和失败数据
									if("0".equals(JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_CODE"))){

										det.setState("3");
										det.setRectificaState(null);
										try {
											updaetQuotaWorkHandleByMoneyNo(det.getMoneyNo(),"0");
											det.setBossMsg("于"+taskService.getNumber()+"通过营销活动接口冲正");
										}catch (Exception e){
											logger.info("营销活动接口冲正异常,数据修改失败："+det.getMoneyNo());
											det.setBossMsg("于"+taskService.getNumber()+"通过营销活动接口冲正,修改营销活动数据失败!");
										}
										claimForFundsService.updateMoneyApplyDet(det);

										moneyTotal.setUseAmount(String.valueOf(Long.parseLong(moneyTotal.getUseAmount()) - Long.parseLong(det.getAmount())));
										moneyTotal.setOverAmount(String.valueOf(Long.parseLong(moneyTotal.getOverAmount()) + Long.parseLong(det.getAmount())));
										claimForFundsService.updateMoneyTotal(moneyTotal);

										detailmsg.put("RETURN_CODE", "200");
										detailmsg.put("RETURN_MSG", "冲正成功");
									}else {
										detailmsg.put("RETURN_CODE", "507");
										detailmsg.put("RETURN_MSG", "冲正失败："+JSONObject.fromObject(applyObj.getString("ROOT")).getString("RETURN_MSG"));
									}
									detailmsg.put("RETURN_TIME", taskService.getNumber());
									outmsg.put("BODY", detailmsg);
									Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
								}else{
									detailmsg.put("RETURN_CODE", "507");
									detailmsg.put("RETURN_MSG", "冲正失败："+(applyRes.getMessage().length()<200?applyRes.getMessage():applyRes.getMessage().substring(0,200)));
									detailmsg.put("RETURN_TIME", taskService.getNumber());
									outmsg.put("BODY", detailmsg);
									Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
								}
							}else if ("02".equals(operateType)){
								Result result = ClaimFundsOpenSrv.getInstance().relieveCapitalCampOn(det.getMoneyNo());
								JSONObject jsthree = JSONObject.fromObject(result);
								String datatwo = jsthree.getString("data");
								JSONObject jsone = JSONObject.fromObject(datatwo);
								JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
								if ("0".equals(jstwo.getString("RETURN_CODE"))) {
									det.setState("1");
									det.setRectificaState(null);
									det.setOldMoneyNo(null);
									claimForFundsService.updateMoneyApplyDet(det);

									detailmsg.put("RETURN_CODE", "200");
									detailmsg.put("RETURN_MSG", "冲正解除成功");
								} else {
									detailmsg.put("RETURN_CODE", "507");
									detailmsg.put("RETURN_MSG", "冲正解除失败："+jstwo.getString("RETURN_MSG"));
								}
								detailmsg.put("RETURN_TIME", taskService.getNumber());
								outmsg.put("BODY", detailmsg);
								Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
							}else {
								detailmsg.put("RETURN_CODE", "508");
								detailmsg.put("RETURN_MSG", "未定义类型："+operateType+"！");
								detailmsg.put("RETURN_TIME", taskService.getNumber());
								outmsg.put("BODY", detailmsg);
								Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
							}
						}
					}
				} else {
					detailmsg.put("RETURN_CODE", "509");
					detailmsg.put("RETURN_MSG", "营销活动冲正接口传递参数异常,缺少关键信息");
					detailmsg.put("RETURN_TIME", taskService.getNumber());
					outmsg.put("BODY", detailmsg);
					Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
				}
			}
		} catch (Exception e) {
			logger.info("营销活动冲正接口异常：" + e.getMessage(), e);
			e.printStackTrace();
			detailmsg.put("RETURN_CODE", "500");
			detailmsg.put("RETURN_MSG", "营销活动冲正接口异常");
			detailmsg.put("RETURN_TIME", taskService.getNumber());
			outmsg.put("BODY", detailmsg);
			Write(JSONHelper.SerializeWithNeedAnnotation(outmsg));
		}
	}

	/**
	 * @Description TODO APP查看PDF或图片
	 * <AUTHOR>
	 * @Date 2022/6/13 16:29
	 **/
	public void downLoadFile(){
		try {
			String id = getString("id");
			final Attachment entity = attachmentService.getAttachmentById(id);
			StorageCfg storageCfg= attachmentService.queryStorageCfg();
			String path = storageCfg.getFileName()+entity.getAttachmentUrl();
			byte[] data = FileUtil.toByteArray2(path);
			String base64 = Base64.encodeBase64String(data);
			String	base64Code = ZipUtils.gzip(base64);
			String realName = entity.getRealName();
			String str1=realName.substring(0, realName.lastIndexOf("."));
			String str2=realName.substring(str1.length()+1, realName.length());
			if(str2.indexOf("jpg") != -1 || str2.indexOf("png") != -1 || str2.indexOf("jpeg") != -1){
				this.Write(realName.substring(0, realName.lastIndexOf("."))+"."+str2+"@"+base64Code);
			}else{
				this.Write("{\"result\":\"NO\",\"msg\":\"操作失败！\"}");
			}
		} catch (Exception e) {
			logger.error("查看图片或PDF异常"+e.getMessage(),e);
			e.printStackTrace();
		}
	}

	/**
	 * @author: liyang
	 * @date: 2021/3/8 14:27
	 * @Version: 1.0
	 * @param:
	 * @return:
	 * @Description: TODO 冲正资金认领BOSS端暂挂
	 */
	public String capitalCampOn(String[] moneyNo) {
		String moneyNothree = "";
		try {
			int ty = moneyNo.length;
			int ts = 0;
			for (int i = 0; i < moneyNo.length; i++) {
				Result json = ClaimFundsOpenSrv.getInstance().capitalCampOn(moneyNo[i]);
				JSONObject jsthree = JSONObject.fromObject(json);
				String datatwo = jsthree.getString("data");
				JSONObject jsone = JSONObject.fromObject(datatwo);
				JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
				if ("0".equals(jstwo.getString("RETURN_CODE"))) {
					ts++;
				} else {
					moneyNothree = moneyNo[i];
					return moneyNothree;
				}
			}
			if (ty == ts) {
				moneyNothree = "YES";
			} else {
				moneyNothree = "NO";
			}
			return moneyNothree;
		} catch (Exception e) {
			e.printStackTrace();
			return "NO";
		}
	}

	/**
	 * @author: liyang
	 * @date: 2021/3/8 14:27
	 * @Version: 1.0
	 * @param:
	 * @return:
	 * @Description: TODO 冲正资金认领BOSS端解除暂挂
	 */
	public String relieveCapitalCampOn(String moneyNo) {
		String moneyNothree = "";
		try {
			Result json = ClaimFundsOpenSrv.getInstance().relieveCapitalCampOn(moneyNo);
			JSONObject jsthree = JSONObject.fromObject(json);
			String datatwo = jsthree.getString("data");
			JSONObject jsone = JSONObject.fromObject(datatwo);
			JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
			if ("0".equals(jstwo.getString("RETURN_CODE"))) {
				moneyNothree = "YES";
			} else {
				moneyNothree = moneyNo;
			}
			return moneyNothree;
		} catch (Exception e) {
			e.printStackTrace();
			return "NO";
		}
	}

	/**
	 * @Description TODO 根据手机号查询号码信息
	 * <AUTHOR>
	 * @Date 2022/11/15 9:33
	 **/
	public void pQryUserBaseMsg(){
		try{
			String phoneNum = getString("phoneNum");
			String userPhoneNum = getString("userPhoneNum");//手机号码
			if (phoneNum==null){
				Write(returnPars(-1,"","亲爱的同事，手机号信息获取异常，请刷新页面重试或联系管理员处理！"));
				return;
			}
			if (userPhoneNum.length()<1){
				Write(returnPars(-1, "", "亲爱的同事，请求异常获取用户信息失败，请联系管理员处理！"));
				return;
			}
			SystemUser user = claimForFundsService.querUsers(userPhoneNum);
			if (user==null){
				Write(returnPars(-1, "", "亲爱的同事，获取用户【"+userPhoneNum+"】信息失败，请联系管理员处理！"));
				return;
			}
			Result result = ClaimFundsOpenSrv.getInstance().pQryUserBaseMsg(phoneNum);
			if (result.getCode()==ResultCode.SUCCESS.code()){
				JSONObject json = JSONObject.fromObject(result.getData());
				JSONObject root = JSONObject.fromObject(json.get("ROOT"));
				if (root.getString("RETURN_CODE").equals("0")){
					JSONObject OUT_DATA = root.getJSONObject("OUT_DATA");
					List<Map<String,String>> vwUserList = claimForFundsService.getVwUserinf(String.valueOf(user.getRowNo()));
					boolean flag;
					if (OUT_DATA.getString("REGION_NAME").contains("成都") && (vwUserList.get(0).get("COMPANY_NAME").equals("天府新区分公司")
							|| vwUserList.get(0).get("COMPANY_NAME").equals("省公司") || vwUserList.get(0).get("COMPANY_NAME").equals("成都分公司"))){
						flag = true;
					}else {
						flag = vwUserList.get(0).get("COMPANY_NAME").contains(OUT_DATA.getString("REGION_NAME"));
					}
					Write(returnPars(1,flag,"查询成功！"));
				}else {
					Write(returnPars(-1,"",root.getString("RETURN_MSG")));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事,调用手机号信息查询接口异常["+result.getMessage()+"],请联系管理员处理!"));
			}
		}catch (Exception e){
			logger.info("手机号信息查询异常:"+e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,手机号信息查询异常["+e.getMessage()+"],请联系管理员处理!"));
		}
	}

	/**
	 * @Description 省外号码信息查询
	 * <AUTHOR>
	 * @Date 2023/10/25 14:59
	 **/
	public void queryInquiryOfFeesDue(){
		try{
			String phoneNum = getString("phoneNum");
			String bossName = getString("bossName");
			if (bossName==null || "".equals(bossName)){
				bossName = user.getBossUserName();
				if (bossName==null || "".equals(bossName)){
					Write(returnPars(-1,"","亲爱的同事，用户工号信息获取异常，请刷新页面重试或联系管理员处理！"));
					return;
				}
			}
			if (phoneNum==null){
				Write(returnPars(-1,"","亲爱的同事，手机号信息获取异常，请刷新页面重试或联系管理员处理！"));
				return;
			}

			String orgInfo;
			Result result = ClaimFundsOpenSrv.getInstance().sGetLoginMsg(bossName);
			//判断当前请求是否成功
			if(ResultCode.SUCCESS.code() == result.getCode()){
				JSONObject resObj = JSONObject.fromObject(result.getData());
				JSONObject rootObj = resObj.getJSONObject("ROOT");
				if ("0".equals(rootObj.getString("RETURN_CODE"))) {
					JSONObject out_data = rootObj.getJSONObject("OUT_DATA");
					orgInfo = out_data.getString("GROUP_ID");
				}else {
					Write(returnPars(-1, "", "亲爱的同事，查询工号【"+bossName+"】渠道失败【"+rootObj.getString("RETURN_MSG")+"】，请联系管理员处理！"));
					return;
				}
			}else {
				Write(returnPars(-1, "", "亲爱的同事，查询工号【"+bossName+"】渠道失败【"+result.getMessage()+"】，请联系管理员处理！"));
				return;
			}

			Result resultTwo = ClaimFundsOpenSrv.getInstance().inquiryOfFeesDue(bossName,orgInfo,phoneNum);
			if (resultTwo.getCode()==ResultCode.SUCCESS.code()){
				JSONObject json = JSONObject.fromObject(resultTwo.getData());
				JSONObject root = JSONObject.fromObject(json.get("ROOT"));
				if ("0".equals(root.getString("RETURN_CODE"))){
					JSONObject data = root.getJSONObject("OUT_DATA").getJSONObject("RESULT_INFO");
					Write(returnPars(1,data,"查询成功！"));
				}else {
					Write(returnPars(-1,"","亲爱的同事,查询号码信息失败["+root.getString("RETURN_MSG")+"],请联系管理员处理!"));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事,查询号码信息失败["+result.getMessage()+"],请联系管理员处理!"));
			}
		}catch (Exception e){
			logger.info("省外号码信息查询异常:"+e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事,省外号码信息查询异常["+e.getMessage()+"],请联系管理员处理!"));
		}
	}

	/**
	 * @Description TODO 查询用户渠道是否与当前用户一致
	 * <AUTHOR>
	 * @Date 2022/12/5 14:09
	 **/
	public void queryUsersGetLoginMsg(){
		try{
			int userNo = getInteger("userNo");
			String bossName = getString("bossName");
			SystemUser user = systemUserService.getByUserInfoRowNo(userNo);
			if (user==null || user.getBossUserName()==null){
				Write(returnPars(-1,user,"亲爱的同事，用户信息异常【未查询到用户信息或用户未配置工号信息】，请核实！"));
				return;
			}

			String orgInfo;
			Result result = ClaimFundsOpenSrv.getInstance().sGetLoginMsg(user.getBossUserName());
			//判断当前请求是否成功
			if(ResultCode.SUCCESS.code() == result.getCode()){
				JSONObject resObj = JSONObject.fromObject(result.getData());
				JSONObject rootObj = resObj.getJSONObject("ROOT");
				if ("0".equals(rootObj.getString("RETURN_CODE"))) {
					JSONObject out_data = rootObj.getJSONObject("OUT_DATA");
					orgInfo = out_data.getString("GROUP_ID");
				}else {
					Write(returnPars(-1, "", "亲爱的同事，查询缴费代理人工号渠道信息失败【"+rootObj.getString("RETURN_MSG")+"】，请联系管理员处理！"));
					return;
				}
			}else {
				Write(returnPars(-1, "", "亲爱的同事，查询缴费代理人工号渠道信息失败【"+result.getMessage()+"】，请联系管理员处理！"));
				return;
			}

			Result resultTwo = ClaimFundsOpenSrv.getInstance().sGetLoginMsg(bossName);
			//判断当前请求是否成功
			if(ResultCode.SUCCESS.code() == resultTwo.getCode()){
				JSONObject resObj = JSONObject.fromObject(resultTwo.getData());
				JSONObject rootObj = resObj.getJSONObject("ROOT");
				if ("0".equals(rootObj.getString("RETURN_CODE"))) {
					JSONObject out_data = rootObj.getJSONObject("OUT_DATA");
					if (orgInfo.equals(out_data.getString("GROUP_ID"))){
						Write(returnPars(1,"","查询完成！"));
					}else {
						Write(returnPars(-1, "", "亲爱的同事，您选择的缴费代理人【渠道代码："+orgInfo+"】与您【渠道代码："+out_data.getString("GROUP_ID")+"】不在同一渠道，请重新选择缴费代理人！"));

					}
				}else {
					Write(returnPars(-1, "", "亲爱的同事，查询渠道信息失败【"+rootObj.getString("RETURN_MSG")+"】，请联系管理员处理！"));
				}
			}else {
				Write(returnPars(-1, "", "亲爱的同事，查询渠道信息失败【"+result.getMessage()+"】，请联系管理员处理！"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("工号渠道信息校验异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，工号渠道信息校验异常【"+e.getMessage()+"】，请联系管理员处理!"));
		}
	}

	/**
	 * @Description 资金认领集团账户关系校验
	 * <AUTHOR>
	 * @Date 2023/10/16 10:53
	 **/
	public void claimRelationshipVerification(){
		try{
			String groupCode = getString("groupCode");                                              //集团编号
			String otherAccnumber = getString("otherAccnumber");                                    //对方打款账户
			String otherAccName = getString("otherAccName");                                        //对方打款账户名称

			if (otherAccnumber.isEmpty()){
				Write(returnPars(-1,"","亲爱的同事，校验参数异常【打款账户为空】,请联系管理员处理！"));
				return;
			}

			GroupRelations groupRelations = groupAccountService.queryGroupRelations(groupCode,otherAccnumber);
			if (groupRelations==null){
				Write(returnPars(-1,"","亲爱的同事，集团账户关系信息查询失败,请联系管理员处理！"));
				return;
			}else {
				String regularExpression = "[^a-zA-Z0-9\\u4E00-\\u9FA5]";
				String newOtherAccName = otherAccName.replaceAll(regularExpression, "");
				if ("6".equals(groupRelations.getAccountBankType())){
					String newCustomerName = groupRelations.getCustomerName().replaceAll(regularExpression, "");
					if (!newOtherAccName.equals(newCustomerName)){
						Write(returnPars(-1,"","亲爱的同事，客户打款账户名【"+otherAccName+"】与集团绑定银行账户名【"+groupRelations.getCustomerName()+"】不一致,请核实处理后认领！"));
						return;
					}
				}else{
					String newAccountName = groupRelations.getAccountName().replaceAll(regularExpression, "");
					if (!newOtherAccName.equals(newAccountName)){
						Write(returnPars(-1,"","亲爱的同事，客户打款账户名【"+otherAccName+"】与集团绑定银行账户名【"+groupRelations.getAccountName()+"】不一致,请核实处理后认领！"));
						return;
					}
				}
			}

			JSONObject result = GroupAccountSrv.getInstance().QueryGroupAccountByAccount(otherAccnumber,groupCode);
			JSONObject hander = result.getJSONObject("HEADER");
			JSONObject response = hander.getJSONObject("RESPONSE");
			if (response.has("CODE") && "0000".equals(response.getString("CODE")) && result.has("RESULT")) {
				JSONArray groupArray = result.getJSONArray("RESULT");
				if (groupArray.size()>0){
					String returnStr = returnPars(-1,"","亲爱的同事，根据账户【"+otherAccnumber+"】未查询到有与集团【"+groupCode+"】建立绑定关系,请核实！");
					for (int i = 0;i<groupArray.size();i++){
						JSONObject json = groupArray.getJSONObject(i);
						if (groupCode.equals(json.getString("CUST_CODE"))){
							String auditFlag = json.getString("AUDIT_FLAG");
							if ("0".equals(auditFlag)){
								returnStr = returnPars(-1,"","亲爱的同事，由于账户名称与集团名称不一致，提交稽核校验失败，请核实账户信息重新申请绑定！");
							}else {
								returnStr = returnPars(1,"","校验成功");
							}
							break;
						}
					}
					Write(returnStr);
				}else {
					Write(returnPars(-1,"","亲爱的同事，根据账户【"+otherAccnumber+"】未查询到有与集团【"+groupCode+"】建立绑定关系,请核实！"));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事，根据账户【"+otherAccnumber+"】与集团【"+groupCode+"】查询绑定关系异常："+response.getString("DESC")+",请联系管理员处理！"));
			}
		}catch (Exception e){
			logger.info("集团账户关系校验异常"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，集团账户关系校验异常,请联系管理员处理！"));
		}
	}

	/**
	 * @Description 划拨集团（集团账户绑定关系）查询
	 * <AUTHOR>
	 * @Date 2023/11/28 10:31
	 **/
	public void subgroupQueries(){
		try {
			String groupCode = getString("groupCode");                                              //集团编号
			String otherAccnumber = getString("otherAccnumber");                                    //对方打款账户
			String accountBankType = getString("accountBankType");
			String otherAccName = getString("otherAccName");                                        //对方打款账户名称

			if (otherAccnumber==null || Objects.equals(otherAccnumber, "")){
				Write(returnPars(-1,"","亲爱的同事，打款账户信息异常,请刷新页面重试或联系管理员处理！"));
				return;
			}

			List<GroupRelations> groupRelations = groupAccountService.queryGroupRelations(groupCode,otherAccnumber,accountBankType);
			List<GroupRelations> groupRelationsList = new ArrayList<>();
			String regularExpression = "[^a-zA-Z0-9\\u4E00-\\u9FA5]";
			String newOtherAccName = otherAccName.replaceAll(regularExpression, "");
			for (GroupRelations groupRelation:groupRelations) {
				if ("6".equals(groupRelation.getAccountBankType())){
					String newCustomerName = groupRelation.getCustomerName().replaceAll(regularExpression, "");
					if (newOtherAccName.equals(newCustomerName)){
						groupRelationsList.add(groupRelation);
					}
				}else{
					String newAccountName = groupRelation.getAccountName().replaceAll(regularExpression, "");
					if (newOtherAccName.equals(newAccountName)){
						groupRelationsList.add(groupRelation);
					}
				}
			}
			Write(returnPars(1,groupRelationsList,"查询完成"));

		}catch (Exception e){
			logger.info("划拨集团信息查询异常"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，划拨集团信息查询异常,请联系管理员处理！"));
		}
	}

	/**
	 * @author: liyang
	 * @date: 2021/1/20 11:21
	 * @Version: 1.0
	 * @param: String
	 * @return: String
	 * @Description: TODO 返回参数生成
	 */
	private static String returnPars(int state,Object data,String msg){
		Map<String, Object> mapJson = new HashMap<>();
		mapJson.put("code",state);
		mapJson.put("data",data);
		mapJson.put("msg",msg);
		return JSONHelper.SerializeWithNeedAnnotation(mapJson);
	}

	/**
	 * 获取汉字首字母
	 *
	 * @param text 文本
	 * @return {@link String}
	 */
	public static String getPinyinInitials(String text) {
		StringBuilder sb = new StringBuilder();
		for (int i = 0; i < text.length(); i++) {
			char ch = text.charAt(i);
			String[] s = PinyinHelper.toHanyuPinyinStringArray(ch);
			if (s != null) {
				sb.append(s[0].charAt(0));
			} else {
				sb.append(ch);
			}
		}
		return sb.toString();
	}

	/**
	 * 将汉字转换为全拼
	 *
	 * @param text 文本
	 * @param separator 分隔符
	 * @return {@link String}
	 */
	public static String getPinyin(String text, String separator) {
		char[] chars = text.toCharArray();
		HanyuPinyinOutputFormat format = new HanyuPinyinOutputFormat();
		// 设置大小写
		format.setCaseType(HanyuPinyinCaseType.LOWERCASE);
		// 设置声调表示方法
		format.setToneType(HanyuPinyinToneType.WITHOUT_TONE);
		// 设置字母u表示方法
		format.setVCharType(HanyuPinyinVCharType.WITH_V);
		String[] s;
		String rs = StringUtils.EMPTY;
		try {
			StringBuilder sb = new StringBuilder();
			for (int i = 0; i < chars.length; i++) {
				// 判断是否为汉字字符
				if (String.valueOf(chars[i]).matches("[\\u4E00-\\u9FA5]+")) {
					s = PinyinHelper.toHanyuPinyinStringArray(chars[i], format);
					if (s != null) {
						sb.append(s[0]).append(separator);
						continue;
					}
				}
				sb.append(String.valueOf(chars[i]));
				if ((i + 1 >= chars.length) || String.valueOf(chars[i + 1]).matches("[\\u4E00-\\u9FA5]+")) {
					sb.append(separator);
				}
			}
			rs = sb.substring(0, sb.length()-1);
		} catch (BadHanyuPinyinOutputFormatCombination e) {
			e.printStackTrace();
			logger.error("拼音转换异常"+e.getMessage(),e);
		}
		return rs;
	}

	private static Pattern linePattern = Pattern.compile("_(\\w)");
	/** 下划线转驼峰 */
	public static String lineToHump(String str) {
		str = str.toLowerCase();
		Matcher matcher = linePattern.matcher(str);
		StringBuffer sb = new StringBuffer();
		while (matcher.find()) {
			matcher.appendReplacement(sb, matcher.group(1).toUpperCase());
		}
		matcher.appendTail(sb);
		return sb.toString();
	}

	/**
	 * @Description TODO 时间字符串转时间（yyyy-MM-dd HH:mm:ss）
	 * <AUTHOR>
	 * @param currentTime 时间字符串
	 * @return java.util.Date
	 * @Date 2022/6/9 17:12
	 **/
	public static Date getStringDateFour(String currentTime) {
		Date dateString = null;
		try {
			SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			dateString = formatter.parse(currentTime);
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return dateString;
	}

	/**
	 * @Description TODO 时间转字符串（yyyy-MM-dd HH:mm:ss）
	 * <AUTHOR>
	 * @param currentTime 时间
	 * @return java.lang.String
	 * @Date 2022/6/9 17:13
	 **/
	public static String getStringDatetwo(Date currentTime) {
		SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		String dateString = formatter.format(currentTime);
		return dateString;
	}

	/**
	 * @Description TODO 分转元，转换为bigDecimal在toString
	 * <AUTHOR>
	 * @param price 金额
	 * @return java.lang.String
	 * @Date 2022/6/13 10:24
	 **/
	public static String changeF2Y(String price) {
		return BigDecimal.valueOf(Long.valueOf(price)).divide(new BigDecimal(100)).toString();
	}

	/**
	 * @Description TODO 对象转map
	 * <AUTHOR>
	 * @param object 对象
	 * @return java.util.Map
	 * @Date 2022/5/12 17:14
	 **/
	public static Map beanToMap(Object object) throws IllegalAccessException {
		Map<String, Object> map = new HashMap<String, Object>();
		Field[] fields = object.getClass().getDeclaredFields();
		for (Field field : fields) {
			field.setAccessible(true);
			map.put(field.getName(), field.get(object));
		}
		return map;
	}

	public static String getEng(String a){
		StringBuffer sb = new StringBuffer();
		for(int i = 0;i<a.length();i++){
			char c = a.charAt(i);
			if((c<='z'&&c>='a')||(c<='Z'&&c>='A')){
				sb.append(c);
			}
		}
		return sb.toString();
	}

	/**
	 * @Description TODO 根据账户和有价卡流水修改有价卡信息
	 * <AUTHOR>
	 * @param invNo         有价卡流水
	 * @param contrctNo     账户号码
	 * @param Amount        金额
	 * @param updateType    修改类型 缴纳金额：SAVA，冲正金额：其他
	 * @return java.lang.Boolean
	 * @Date 2022/12/2 10:28
	 **/
	public Boolean updateValuableCard(String invNo,String contrctNo,String Amount,String updateType){
		try {
			ValuableCardDet valuableCard = valuableCardService.getValuableCardDetByInvNo(invNo,contrctNo);
			if (valuableCard!=null){
				int cardAmount = Integer.parseInt(valuableCard.getOrderPrice());            //申请金额
				int cardMoneyPayPrice = Integer.parseInt(valuableCard.getMoneyPayPrice());  //资金认领已缴纳金额
				Integer newCardMoneyPayPrice = 0;
				if ("SAVA".equals(updateType)){
					newCardMoneyPayPrice = cardMoneyPayPrice+Integer.parseInt(Amount);
				}else {
					newCardMoneyPayPrice = cardMoneyPayPrice-Integer.parseInt(Amount);
				}
				if (cardAmount>=newCardMoneyPayPrice){
					valuableCard.setMoneyPayPrice(newCardMoneyPayPrice.toString());
					valuableCardService.updateValuableCardDet(valuableCard);
					return true;
				}else {
					return false;
				}
			}else {
				return false;
			}
		}catch (Exception e){
			logger.info("有价卡:"+invNo+"金额修改失败："+e.getMessage(),e);
			e.printStackTrace();
			return false;
		}
	}

	/**
	 * @Description TODO 根据账户和物联网预开票流水修改物联网预开票信息
	 * <AUTHOR>
	 * @param invNo         物联网预开票流水
	 * @param contrctNo     开票账户
	 * @param Amount        缴费金额
	 * @param updateType    修改类型 缴纳金额：SAVA，冲正金额：其他
	 * @return java.lang.Boolean
	 * @Date 2023/6/9 15:29
	 **/
	public Boolean updateInternetOfThingsDet(String invNo,String contrctNo,String Amount,String updateType){
		try{
			InternetOfThingsDet internetOfThingsDet = claimForFundsService.queryInternetOfThingsDet(invNo);
			if (internetOfThingsDet!=null){
				int cardAmount = Integer.parseInt(internetOfThingsDet.getInclude_tax_amt());            //申请金额
				int cardMoneyPayPrice = Integer.parseInt(internetOfThingsDet.getCollection_tax_amt());  //资金认领已缴纳金额
				Integer newCardMoneyPayPrice = 0;
				if ("SAVA".equals(updateType)){
					newCardMoneyPayPrice = cardMoneyPayPrice+Integer.parseInt(Amount);
				}else {
					newCardMoneyPayPrice = cardMoneyPayPrice-Integer.parseInt(Amount);
				}
				if (cardAmount>=newCardMoneyPayPrice){
					internetOfThingsDet.setCollection_tax_amt(newCardMoneyPayPrice.toString());
					return claimForFundsService.updateInternetOfThingsDet(internetOfThingsDet) != null;
				}else {
					return false;
				}
			}else {
				return false;
			}
		}catch (Exception e){
			logger.info("物联网预开票:"+invNo+"金额修改失败："+e.getMessage(),e);
			e.printStackTrace();
			return false;
		}
	}

	/**----------------------------------------------------(营销活动关联方法)-------------------------------------------------------------
	 * @Description TODO 查询营销活动明细
	 * <AUTHOR>
	 * @Date 2022/6/16 11:02
	 **/
	public  void getQuotaWorkHandles(){
		try{
			String contractNo = getString("contractNo");
			String groupCode = getString("groupCode");
			String bossName = getString("bossName");
			if (contractNo==null || groupCode==null){
				Write(returnPars(-1,"","亲爱的同事，营销活动信息查询失败【参数获取异常】，请刷新页面或联系管理员处理！"));
				return;
			}
			if (bossName==null || bossName==""){
				Write(returnPars(-1,"","亲爱的同事，当前用户未配置工号信息不能进行查询，请核实！"));
				return;
			}
			//根据账户号码和集团编号查询查询营销活动明细
			/**
			 * 查询营销活动详情工单信息
			 * @param contractNo 账户
			 * @param userRow boss工号
			 * @param groupCode 280
			 * @return
			 */
			List<QuotaWorkHandle> quotHandles = marketingActivitiesService.getQuotHandles(contractNo, bossName, groupCode);
			if (quotHandles.size()>0){
				Write(returnPars(1,quotHandles,"查询完成！"));
			}else {
				Write(returnPars(-1,"","亲爱的同事，当前账户未查询到营销活动缴费，请核实！"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("营销活动信息查询异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，营销活动信息查询异常，请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 提供营销活动查询是否允许操作方法
	 * <AUTHOR>
	 * @Date 2022/6/16 17:30
	 **/
	public void queryMoneyByCode(){
		try{
			String moneyNo = getString("moneyNo");          //营销活动对应资金缴费流水
			MoneyApplyDet applyDet = claimForFundsService.getMoneyApplyDetId(moneyNo);
			if (applyDet!=null){
				//资金状态为  3:已冲正   4:已作废   营销活动允许操作
				if (applyDet.getState().equals("3") || applyDet.getState().equals("4")){
					Write(returnPars(1,applyDet,"查询完成！"));
				}else {
					Write(returnPars(-1,"","亲爱的同事，流水对应资金缴费（当前状态："+applyDet.getState()+"）未完成，请核实！"));
				}
			}else {
				Write(returnPars(-1,"","亲爱的同事，流水对应资金明细异常，请联系管理员处理！"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("资金缴费信息查询异常："+e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，资金缴费信息查询异常，请联系管理员处理！"));
		}
	}

	/**
	 * @Description TODO 根据营销活动明细流水修改营销活动明细
	 * <AUTHOR>
	 * @param id        营销活动id
	 * @param moneyNo   资金缴费流水
	 * @param state     状态
	 * @return java.lang.Boolean
	 * @Date 2022/6/16 17:40
	 **/
	public Boolean updaetQuotaWorkHandleByCode(String id,String moneyNo,String state) throws Exception{
		QuotaWorkHandle handle = marketingActivitiesService.getQuotHandleById(id);
		if (handle!=null){
			handle.setSerialNumber(moneyNo);
			handle.setWorkState(state);//工单状态 0.未使用 1.已使用 2.正在使用 -1.已作废
			if (marketingActivitiesService.upDateQuotaWorkHandle(handle)!=null){
				return true;
			}else {
				return false;
			}
		}else {
			return true;
		}
	}

	/**
	 * @Description TODO 根据资金缴费流水修改营销活动明细
	 * <AUTHOR>
	 * @param moneyNo   资金缴费流水
	 * @param state     状态
	 * @return java.lang.Boolean
	 * @Date 2022/6/16 17:44
	 **/
	public Boolean updaetQuotaWorkHandleByMoneyNo(String moneyNo,String state) throws Exception{
		try {
			QuotaWorkHandle handle = marketingActivitiesService.getQuotHandleByNumber(moneyNo);
			if (handle!=null){
				handle.setWorkState(state);
				if (marketingActivitiesService.upDateQuotaWorkHandle(handle)!=null){
					return true;
				}else {
					return false;
				}
			}else {
				return true;
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("根据资金缴费流水修改营销活动明细异常："+e.getMessage(),e);
			return false;
		}
	}

	/**
	 * @Description 资金认领推送财务系统状态变更
	 * <AUTHOR>
	 * @Date 2022/11/1 11:06
	 **/
	public void updateIncomeState() {
		try {
			String id = getString("id");
			String status = getString("status");
			String pushUser = getString("pushUser");
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
			if (moneyTotal==null){
				Write(returnPars(-1,"","亲爱的同事,未获取资金信息,请刷新页面重试或联系管理员处理!"));
				return;
			}
			List<Map<String,String>> mapList;
			if (pushUser==null || "".equals(pushUser)){
				if (moneyTotal.getPushUserName()==null){
					Write(returnPars(-1,"","亲爱的同事,资金归属用户信息异常,请刷新页面重试或联系管理员处理!"));
					return;
				}
				mapList = claimForFundsService.getVwUserinf(String.valueOf(moneyTotal.getPushUserName()));
				if (mapList==null){
					Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
					return;
				}
			}else {
				mapList = claimForFundsService.getVwUserinf(pushUser);
				if (mapList==null){
					Write(returnPars(-1,"","亲爱的同事,未获取处理人信息,请联系管理员处理!"));
					return;
				}
			}

			String county = mapList.get(0).get("COUNTY_NAME");
			String region = mapList.get(0).get("COMPANY_NAME");
			String fullName = mapList.get(0).get("EMPLOYEE_NAME");
			Result result= ClaimFundsOpenSrv.getInstance().updateIncomeState(status,moneyTotal.getSerialNo(),county,region,fullName);
			logger.info("财务接口调用结果===>"+result.toString());
			if(ResultCode.SUCCESS.code()==result.getCode()){
				JSONObject res = JSONObject.fromObject(result.getData());
				if ("success".equals(res.getString("code"))) {
					moneyTotal.setBak1("财务状态也更新为："+status+",更新时间："+getStringDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
					claimForFundsService.updateMoneyTotal(moneyTotal);
					Write(returnPars(1, "", "更新财务数据完成！"));
				}else {
					moneyTotal.setBak1(res.getString("info"));
					claimForFundsService.updateMoneyTotal(moneyTotal);
					Write(returnPars(-1, "", "亲爱的同事,资金信息实时推送财务系统失败【"+res.getString("info")+"】，请联系管理员处理!"));
				}
			}else {
				moneyTotal.setBak1(result.getMessage());
				claimForFundsService.updateMoneyTotal(moneyTotal);
				Write(returnPars(-1, "", "亲爱的同事,资金信息实时推送财务系统失败【"+result.getMessage()+"】，请联系管理员处理!"));
			}
		} catch (Exception e) {
			logger.info("资金信息实时推送财务系统异常"+e.getMessage(),e);
			e.printStackTrace();
			Write(returnPars(-1,"","亲爱的同事，资金信息实时推送财务系统异常【"+e.getMessage()+"】，请联系管理员处理!"));
		}
	}

	/**
	 * @Description 快捷缴费 -查询集团代缴账户及代缴金额
	 * <AUTHOR>
	 * @Date 2024/05/20 14:47
	 **/
	public void enquireAboutPendingBills(){
		try {
			String groupCode = getString("groupCode");
			String otherAccNumber = getString("otherAccNumber");
			String paymentIdentification = getString("whetherOrNotToPayAValueCard");
			String bossName = getString("bossName");
			if (bossName==null || bossName.isEmpty() || "null".equals(bossName)){
				if (user==null){
					String userPhoneNum = getString("userPhoneNum");
					SystemUser user = claimForFundsService.querUsers(userPhoneNum);
					bossName = user.getBossUserName();
				}else {
					bossName = user.getBossUserName();
				}
			}

			//账户绑定信息
			GroupRelations relations = groupAccountService.queryGroupRelations(groupCode,otherAccNumber);
			if (relations==null){
				Write(returnPars(-1,"","亲爱的同事，集团："+groupCode+" 与账户："+otherAccNumber+" 绑定信息异常，请联系管理员处理！"));
			}else {
				//有价卡待缴费信息
				List<Map<String, Object>> valuableCardInquiry = claimForFundsService.valuableCardInquiry(groupCode,paymentIdentification);
				//预开票待缴费信息（BOSS接口获取）
				Result result = ClaimFundsOpenSrv.getInstance().iPreinvApplyRecdQry(groupCode,bossName);
				if(ResultCode.SUCCESS.code() == result.getCode()){
					JSONObject resObj = JSONObject.fromObject(result.getData());
					JSONObject rootObj = resObj.getJSONObject("ROOT");
					if ("0".equals(rootObj.getString("RETURN_CODE"))) {
						JSONObject outData = rootObj.getJSONObject("OUT_DATA");
						if (outData.has("INV_APPLY_LIST") && outData.getInt("COUNT")>0){
							JSONArray applyList = outData.getJSONArray("INV_APPLY_LIST");
							for (int u = 0;u < applyList.size();u++){
								JSONObject object = applyList.getJSONObject(u);

								boolean falg = true;
								for (int y = 0; y < valuableCardInquiry.size(); y++) {
									Map<String, Object> valuableMap = valuableCardInquiry.get(y);

									if (valuableMap.get("CONTRACT_NO").equals(object.getString("CONTRACT_NO"))){
										String preinvamount = valuableMap.get("PREINVAMOUNT").toString();
										Integer amount = Integer.parseInt(preinvamount)+object.getInt("UNPAYEDOWE");
										valuableMap.put("PREINVAMOUNT",amount);
										valuableCardInquiry.set(y,valuableMap);
										falg = false;
										break;
									}
								}

								if (falg){
									Map<String, Object> valuableNewMap = new HashMap<>();
									valuableNewMap.put("CONTRACT_NO",object.getString("CONTRACT_NO"));
									valuableNewMap.put("PREINVAMOUNT",object.getInt("UNPAYEDOWE"));
									valuableNewMap.put("VALUABLECARDAMOUNT",0);
									valuableCardInquiry.add(valuableNewMap);
								}
							}
						}
					}
				}

				String busiType = "B";
				List<Map<String, Object>> returnMap = new ArrayList<>();
				if (valuableCardInquiry!=null && !valuableCardInquiry.isEmpty()){
					for (Map<String, Object> dataMap:valuableCardInquiry) {
						List<GroupHipAccount> groupHipAccountList = groupAccountService.queryGroupHipAccount(relations.getId(),dataMap.get("CONTRACT_NO").toString(),"");

						boolean flag = false;
						for (GroupHipAccount groupHipAccount:groupHipAccountList) {
							if (!"CMIOT账户".equals(groupHipAccount.getContractType())
									&&!"成员个人账户".equals(groupHipAccount.getContractType())
									&&!"省外跨区账户".equals(groupHipAccount.getContractType())){
								flag = true;
								break;
							}
						}
						dataMap.put("WHETHERORNOTTOBIND",(flag?"1":"-1"));
                        Result numberQry = ClaimFundsOpenSrv.getInstance().sTaxpayerNumberQry(groupCode,bossName,dataMap.get("CONTRACT_NO").toString(),busiType);
                        if(ResultCode.SUCCESS.code()==numberQry.getCode()){
                            JSONObject resDate=JSONObject.fromObject(numberQry.getData());
                            JSONObject rootDate=JSONObject.fromObject(resDate.get("ROOT"));
                            if(rootDate.getInt("RETURN_CODE")==0){
                                if(rootDate.has("OUT_DATA") && !"".equals(rootDate.getString("OUT_DATA"))){
                                    JSONObject outData=rootDate.getJSONObject("OUT_DATA");
                                    dataMap.put("CONTRACTATT_TYPE",outData.getString("CONTRACTATT_TYPE"));
                                    returnMap.add(dataMap);
                                }
                            }
                        }
					}
				}
				Write(returnPars(1,returnMap,"查询成功！"));
			}
		}catch (Exception e){
			e.printStackTrace();
			logger.info("根据集团编号查询待缴费信息失败：{}",e.getMessage(),e);
			Write(returnPars(-1,"","亲爱的同事，根据集团编号查询待缴费信息失败："+e.getMessage()+" ,请联系管理员处理！"));
		}
	}
}
