package com.xinxinsoft.action.claimForFunds;

import java.math.BigDecimal;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.xinxinsoft.entity.claimForFunds.*;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.GroupCustomerService;
import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.service.claimForFunds.LateFeeMoneyDataService;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.PreinvApply.PreinvApplyDet;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

public class ClaimForFundsAction  extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(ClaimForFundsAction.class);
    private ClaimForFundsService claimForFundsService;
    private WaitTaskService service;                // 代办
    private SystemUserService systemUserService;
    private JbpmUtil jbpmUtil;
    private TransferJBPMUtils transferJBPMUtils;
    private Bpms_riskoff_service taskService;
    private LateFeeMoneyDataService lateFeeMoneyDataService;
    public ClaimForFundsService getClaimForFundsService() {
        return claimForFundsService;
    }

    public void setClaimForFundsService(ClaimForFundsService claimForFundsService) {
        this.claimForFundsService = claimForFundsService;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public JbpmUtil getJbpmUtil() {
        return jbpmUtil;
    }

    public void setJbpmUtil(JbpmUtil jbpmUtil) {
        this.jbpmUtil = jbpmUtil;
    }

    public TransferJBPMUtils getTransferJBPMUtils() {
        return transferJBPMUtils;
    }

    public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
        this.transferJBPMUtils = transferJBPMUtils;
    }

    public Bpms_riskoff_service getTaskService() {
        return taskService;
    }

    public void setTaskService(Bpms_riskoff_service taskService) {
        this.taskService = taskService;
    }

    public LateFeeMoneyDataService getLateFeeMoneyDataService() {
        return lateFeeMoneyDataService;
    }

    public void setLateFeeMoneyDataService(LateFeeMoneyDataService lateFeeMoneyDataService) {
        this.lateFeeMoneyDataService = lateFeeMoneyDataService;
    }

    /**
     * 查询所有未被认领的资金
     */
//	public void getClaimForFunds() {
//		try {
//			Integer type = getInteger("type");
//			Integer pageNo = getInteger("pageNo");// 当前页码数
//			Integer pagesize = getInteger("pageSize");// 每页显示件数
//			String number = getString("Number");
//			LayuiPage page = new LayuiPage(pageNo, pagesize);
//			LayuiPage json = null;
//			if (type == 0) {
//				String companyCode = "";
//				List<SystemDept> deptList = user.getSystemDept();
//				companyCode = deptList.get(0).getSystemCompany().getCompanyCode();
//				json = claimForFundsService.getClaimForFunds(number, page, companyCode, user);
//			} else {
//				json = claimForFundsService.getUserClaimForFunds(number, page, user);
//			}
//			Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
//		} catch (Exception e) {
//			e.printStackTrace();
//			Write("NO");
//		}
//	}

    /**
     * 根据账户查询账期
     */
    public void getCycleAmount() {
        try {
            String contractNo = getString("contractNo");
            List<PreinvApplyDet> pd = claimForFundsService.getPreinvApplyDet(contractNo);
            System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormat(pd));
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(pd));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询申请中，审批过，创建的列表
     *
     * @return
     */
    public void getUserClaimForFunds() {
        try {
            Integer type = getInteger("type");
            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数
            String groupCode = getString("groupCode");
            String optype = getString("optype");
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            LayuiPage json = null;
            if (type == 0) {//审批中
                json = claimForFundsService.canItBeVerifiedMoneyApplyUser(groupCode, optype, page, user);
            } else if (type == 1) {//我创建的
                json = claimForFundsService.completedCanItBeVerifiedMoneyApplyUser(groupCode, optype, page, user);
            } else {//审批过的
                json = claimForFundsService.approvalMoneyApplyUser(groupCode, optype, page, user);
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(json));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 用于校验用户使用资金缴费或者预开票状态时，确认对应账户是否允许用于缴费
     */
    public void validPreInv(){
        Result outMsg=ResultGenerator.genFailResult("账户号码校验异常");
        try {
            String phoneNo=getString("phone_no");
            String contractNo=getString("contract_no");
            Result result = ClaimFundsOpenSrv.getInstance().s8000ValidPreInv(phoneNo,contractNo,user.getBossUserName());
            if(ResultCode.SUCCESS.code()==result.getCode()){
                JSONObject resDate=JSONObject.fromObject(result.getData());
                JSONObject rootDate=JSONObject.fromObject(resDate.get("ROOT"));
                if(rootDate.getInt("RETURN_CODE")==0){
                    if(rootDate.has("OUT_DATA")){
                        outMsg=ResultGenerator.genSuccessResult(rootDate.getString("OUT_DATA"));
                    }else{
                        outMsg=ResultGenerator.genFailResult("未查询到预开票记录,不能预开票交费,请转至普通缴费");
                    }
                }else{
                    //400
                    //outMsg=ResultGenerator.genSuccessResult();
                    outMsg=ResultGenerator.genFailResult(rootDate.getString("DETAIL_MSG"));
                }
            }
            Write(outMsg.toString());
        }catch (Exception e){
            logger.error("调用校验是否允许缴费失败：" + e.getMessage(), e);
            outMsg.setMessage(e.getMessage());
        }
        Write(outMsg.toString());
    }



    /**
     * 查询当前登录人已完成资金认领的数据
     */
    public void getCompleteClaimForFunds() {
        try {
            PageRequest page = new PageRequest(getRequest());
            String number = getString("Number");
            PageResponse response = claimForFundsService.getCompleteClaimForFunds(number, page, user);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void getMoneyTotalSerialNo() {
        try {
            String id = getString("id");
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            List<MoneyApplyDet> moneyApplyDetList = claimForFundsService.getMoneyApplyDet(my.getApplyNo());
            MoneyTotal p = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void getMoneyTotalSerialNoTwo() {
        try {
            String id = getString("id");
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            MoneyTotal p = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * @params
     */
    public static Date getStringDateFour(String currentTime) {
        Date dateString = null;
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateString = formatter.parse(currentTime);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return dateString;
    }

    /**
     * 资金认领工单完成 关闭代办
     * @params
     */
    public void closeWait() {
        try{
            String waitId = getString("waitId");//待办id
            String id = getString("id");//开票id
            Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
            Bpms_riskoff_task btask = taskService.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            if (btask != null&&wt != null&&process!=null) {
                taskService.updateBpms_riskoff_task("完成", 2, btask.getId());
                taskService.updatebpmsRiskoffProcess(id,2);
                service.updateWait(wt, this.getRequest());
            }else {
                Write(returnPars(-1, "", "操作失败，未查询到对应的待办信息！"));
                return;
            }
            Write(returnPars(1, "", "操作成功！"));
        }catch (Exception e) {
            e.printStackTrace();
            Write(returnPars(-1, "", "操作失败，待办信息异常！"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 日期转换
     *
     * @return
     */
    public static String getStringDatethree() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, 2);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

    /**
     * 日期转换
     *
     * @return
     */
    public static String getStringDatetwo() {
        Calendar calendar = Calendar.getInstance();
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatetwo(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 根据id查询资金认领信息
     */
    public void getMoneyApply() {
        try {
            String id = getString("id");
            MoneyApply moneyApply = claimForFundsService.getMoneyApply(id);
            JSONObject json = JSONObject.fromObject(JSONHelper.SerializeWithNeedAnnotationDateFormats(moneyApply));
            Write(json.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 根据ID查询跟踪处理
     *
     * @return
     */
    public void processtrackingSrv() {
        try {
            String id = getString("id");
            List<MoneyApplyTask> p = claimForFundsService.processtracking(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询认领详情
     *
     * @return
     */
    public void getMoneyApplyDet() {
        try {
            String id = getString("id");
            List<MoneyApplyDet> p = claimForFundsService.getMoneyApplyDetByApplyNo(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询认领详情
     * @return
     */
    public void getMoneyApplyDethree(){
        try {
            String id = getString("id");
            List<MoneyApplyDet> p = claimForFundsService.getMoneyApplyDetByApplyNo(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        }catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询资金池信息
     *
     * @return
     */
    public void getMoneyTotal() {
        try {
            String id = getString("id");
            MoneyTotal p = claimForFundsService.getMoneyTotal(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询资金池信息
     *
     * @return
     */
    public void getMoneyTotalById() {
        try {
            String id = getString("id");
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            MoneyTotal p = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询资金池信息
     *
     * @return
     */
    public void getMoneyTotaltwo() {
        try {
            String id = getString("id");
            MoneyTotal p = claimForFundsService.getMoneyTotalSerialNo(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void getMoneyApplyDetlist() {
        try {
            String id = getString("id");
            MoneyTotal p = claimForFundsService.getMoneyTotal(id);
            List<MoneyApplyDet> canItBeVerifiedMoneyApply = claimForFundsService.getMoneyApplyDetlist(p.getSerialNo());
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(canItBeVerifiedMoneyApply);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 获取附件消息
     */
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = claimForFundsService.fuJian(id, biaoshi);
        Write(JSONHelper.Serialize(s));
    }

    /**
     * 提供精确减法运算的sub方法
     *
     * @param value1 被减数
     * @param value2 减数
     * @return 两个参数的差
     */
    public static double sub(double value1, double value2) {
        BigDecimal b1 = new BigDecimal(Double.valueOf(value1));
        BigDecimal b2 = new BigDecimal(Double.valueOf(value2));
        return b1.subtract(b2).doubleValue();
    }

    /**
     * 提供精确加法计算的add方法
     *
     * @param value1 被加数
     * @param value2 加数
     * @return 两个参数的和
     */
    public static double add(double value1, double value2) {
        BigDecimal b1 = new BigDecimal(Double.valueOf(value1));
        BigDecimal b2 = new BigDecimal(Double.valueOf(value2));
        return b1.add(b2).doubleValue();
    }

    /**
     * 根据集团客户和当前登录人员的boss工号来查询账户
     * 修改了调用方式 2020-11-03 11:12:11（zhangwei）
     */
    public void getAccountQuery() {
        String groupCode = getString("groupCode");
        Result result= ClaimFundsOpenSrv.getInstance().getUnitInfo(user.getBossUserName(),groupCode);
        logger.info("请求getUnitInfo接口结果===>"+result.toString());
        if(ResultCode.SUCCESS.code()==result.getCode()){
            JSONObject resObj=JSONObject.fromObject(result.getData());
            Write(resObj.getString("ROOT"));
        }else{
            Write("NO");
        }
    }
    //======================================================下面是冲正代码==================================================

    /**
     * 查询可冲证账户
     *
     * @return
     */
    public void canItBeVerifiedMoneyApplyDet() {
        try {
            String groupCode = getString("groupCode");
            String orderType = getString("orderType");
            String number = getString("number");
            List<MoneyApplyDet> canItBeVerifiedMoneyApply = claimForFundsService.canItBeVerifiedMoneyApply(groupCode, orderType, number, user);
            if (canItBeVerifiedMoneyApply.size() == 0) {
                Write("NON");
            } else {
                String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(canItBeVerifiedMoneyApply);
                Write(json);
            }

        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询冲证账户
     *
     * @returnqryUseRecordState
     */
    public void getMoneyApplyDetId() {
        try {
            String moneyNo = getString("moneyNo");//账户ID
            MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(moneyNo);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(mad);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * W未使用 R已冲正 U已使用 M预占 C冲正  T暂挂
     */
    public void qryUseRecordState() {
        try {
            String moneyNo = getString("moneyNoId");//账户ID
            String[] moneyNotwo = moneyNo.split(",");
            List<Map<String, String>> list = new ArrayList<Map<String, String>>();
            for (int i = 0; i < moneyNotwo.length; i++) {
                Map<String, String> map = new HashMap<String, String>();
                MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(moneyNotwo[i]);
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(mad.getCreatorId()));// 获取下一步处理人信息
                String json = claimForFundsService.qryUseRecordState(USER, mad.getGroupCode(), moneyNotwo[i]);
                JSONObject js = JSONObject.fromObject(json);
                String data1 = js.getString("res");
                JSONObject js2 = JSONObject.fromObject(data1);
                JSONObject js3 = JSONObject.fromObject(js2.getString("ROOT"));
                JSONObject js4 = JSONObject.fromObject(js3.getString("OUT_DATA"));
                JSONArray js5 = JSONArray.fromObject(js4.getString("APPLY_INFO"));
                JSONObject js6 = JSONObject.fromObject(js5.get(0));
                String js7 = js6.getString("STATUS_CODE");
                //String js7="N";
                if ("U".equals(js7)) {
                    mad.setState("2");
                    claimForFundsService.updateMoneyApplyDet(mad);
                    map.put("status_code", js7);
                    map.put("moneyNo", moneyNotwo[i]);
                    list.add(map);
                }
            }
            if (list.size() > 0) {
                String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(list);
                Write(json);
            } else {
                Write("YES");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 资金暂挂
     */
    public String capitalCampOn(String[] moneyNo) {
        String moneyNothree = "";
        try {
            int ty = moneyNo.length;
            int ts = 0;
            for (int i = 0; i < moneyNo.length; i++) {
                Result json = ClaimFundsOpenSrv.getInstance().capitalCampOn(moneyNo[i]);
                JSONObject jsthree = JSONObject.fromObject(json);
                String datatwo = jsthree.getString("data");
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    ts++;
                } else {
                    moneyNothree = moneyNo[i];
                    return moneyNothree;
                }
            }
            if (ty == ts) {
                moneyNothree = "YES";
            } else {
                moneyNothree = "NO";
            }
            return moneyNothree;
        } catch (Exception e) {
            e.printStackTrace();
            return "NO";
        }
    }

    /**
     * 解除暂挂
     */
    public String relieveCapitalCampOn(String moneyNo) {
        String moneyNothree = "";
        try {
            Result json = ClaimFundsOpenSrv.getInstance().relieveCapitalCampOn(moneyNo);
            JSONObject jsthree = JSONObject.fromObject(json);
            String datatwo = jsthree.getString("data");
            JSONObject jsone = JSONObject.fromObject(datatwo);
            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                moneyNothree = "YES";
            } else {
                moneyNothree = moneyNo;
            }
            return moneyNothree;
        } catch (Exception e) {
            e.printStackTrace();
            return "NO";
        }
    }

    /**
     * 冲证开始流程
     */
    public void correctClaimForFunds() {
        try {
            String type = "";
            String title = getString("title");//冲证标题
            String explain = getString("explain");//冲证说明
            String role = getString("role");// 角色权限
            Integer userId = getInteger("userID");// 下一步任务人id
            String attachmentId = getString("attachmentId");// 附件id
            String moneyApplyDetmoneyNo = getString("moneyNoId");//账户ID
            String waitId = getString("waitId");// 待办id
            String taskId = getString("taskId");//任务ID
            String parentId = getString("parentId");//父ID
            String[] moneyNoId = moneyApplyDetmoneyNo.split(",");
            String tys = capitalCampOn(moneyNoId);
            if ("YES".equals(tys)) {
                String IBM = "";
                String orderType = "";
                String groupCode = "";
                String groupName = "";
                int amount = 0;
                for (int i = 0; i < moneyNoId.length; i++) {
                    MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(moneyNoId[i]);
                    orderType = mad.getOrderType();
                    groupCode = mad.getGroupCode();
                    groupName = mad.getGroupName();
                    amount = Integer.parseInt(mad.getAmount()) + amount;
                }
                List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                String sateTime = Bpms_riskoff_service.getUnlockedNumber();
                String batchNO = IBM + "CZ" + sateTime;
                MoneyApply myly = new MoneyApply();
                myly.setApplyNo(batchNO);
                myly.setTitle(title);
                myly.setApplyMemo(explain);
                myly.setCreatorId(user.getRowNo() + "");
                myly.setCreatorName(user.getEmployeeName());
                myly.setCreateDate(new Date());
                myly.setState("1");
                //myly.setApplyType("0");
                myly.setOrderType(orderType);
                myly.setGroupCode(groupCode);
                myly.setGroupName(groupName);
                myly.setApplyAmount(amount + "");
                myly.setOpType("2");
                MoneyApply my = claimForFundsService.addMoneyApply(myly);
                for (int i = 0; i < moneyNoId.length; i++) {
                    MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(moneyNoId[i]);
                    mad.setState("5");
                    claimForFundsService.updateMoneyApplyDet(mad);
                    if (Integer.parseInt(mad.getOrderType()) == 2) {
                        MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                        String datatime = Bpms_riskoff_service.getUnlockedNumber();
                        String moneyNo = "JTCZ" + IBM + datatime + i;
                        moneyApplyDet.setMoneyNo(moneyNo);
                        moneyApplyDet.setSerialNo(mad.getSerialNo());
                        moneyApplyDet.setOrderType("2");
                        moneyApplyDet.setContrctNo(mad.getContrctNo());
                        moneyApplyDet.setContrctType(mad.getContrctType());
                        moneyApplyDet.setProductName(mad.getProductName());
                        moneyApplyDet.setProductNmb(mad.getProductNmb());
                        moneyApplyDet.setAmount(mad.getAmount());
                        moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                        moneyApplyDet.setApplyNo(my.getApplyNo());
                        moneyApplyDet.setUseType(mad.getUseType());
                        moneyApplyDet.setBillNote(mad.getBillNote());
                        moneyApplyDet.setOpType("2");
                        moneyApplyDet.setGroupCode(mad.getGroupCode());
                        moneyApplyDet.setGroupName(mad.getGroupName());
                        moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                        moneyApplyDet.setState("5");
                        moneyApplyDet.setCreatorId(user.getRowNo() + "");
                        moneyApplyDet.setContrctName(mad.getContrctName());
                        moneyApplyDet.setParentId(moneyNoId[i]);
                        claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                    } else {
                        String datatime = Bpms_riskoff_service.getUnlockedNumber();
                        String moneyNo = "GRCZ" + IBM + datatime + i;
                        MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                        moneyApplyDet.setMoneyNo(moneyNo);
                        moneyApplyDet.setSerialNo(mad.getSerialNo());
                        moneyApplyDet.setOrderType("1");
                        moneyApplyDet.setProductName(mad.getProductName());
                        moneyApplyDet.setProductNmb(mad.getProductNmb());
                        moneyApplyDet.setAmount(mad.getAmount());
                        moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                        moneyApplyDet.setApplyNo(my.getApplyNo());
                        moneyApplyDet.setPhoneNo(mad.getPhoneNo());
                        moneyApplyDet.setUseType(mad.getUseType());
                        moneyApplyDet.setOpType("2");
                        moneyApplyDet.setBillNote(mad.getBillNote());
                        moneyApplyDet.setGroupCode(mad.getGroupCode());
                        moneyApplyDet.setGroupName(mad.getGroupName());
                        moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                        moneyApplyDet.setState("5");
                        moneyApplyDet.setCreatorId(user.getRowNo() + "");
                        moneyApplyDet.setContrctName(mad.getContrctName());
                        moneyApplyDet.setParentId(moneyNoId[i]);
                        claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                    }
                }
                SystemUser USER = systemUserService.getUserInfoRowNo(userId);
                //流程启动
                Map<String, String> map = new HashMap<>();
                map.put("decisionKey", "APPLY");
                map.put("decisionValue", role);
                String processId = transferJBPMUtils.startTransfer("claimForFundsChange", map);// 流程启动
                if (!StringUtils.isEmpty(attachmentId)) {
                    if (attachmentId != null) {
                        // 判断是否上传了附件，获取前台提交的附件Id；
                        String[] json = attachmentId.split(",");
                        if (json.length > 0) {
                            for (int i = 0; i < json.length; i++) {
                                SingleAndAttachment sa = new SingleAndAttachment();
                                sa.setOrderID(my.getId());
                                sa.setAttachmentId(json[i]);
                                sa.setLink(MoneyTotal.MONEYTOTAL);
                                claimForFundsService.saveSandA(sa);
                            }
                        }
                    }
                }
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                taskService.updateBpms_riskoff_task("", 2, taskId);
                taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
                taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
                String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), userId, user);//预存下一步任务
                WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                }
                correctCommitBackLog(my, userId, processId, user, taskid);// 生成待办
                Write("YES");
            } else {
                Write("NON");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 冲证重复开始流程
     */
    public void returnCorrectClaimForFund() {
        try {
            String title = getString("title");//冲证标题
            String explain = getString("explain");//冲证说明
            String role = getString("role");// 角色权限
            Integer userId = getInteger("userID");// 下一步任务人id
            String attachmentId = getString("attachmentId");// 附件id
            String moneyApplyDetmoneyNo = getString("moneyNoId");//列表编码ID
            String waitId = getString("waitId");
            String taskId = getString("taskId");//任务ID
            String parentId = getString("parentId");//父ID
            String[] moneyNoId = moneyApplyDetmoneyNo.split(",");
            String IBM = "";
            String orderType = "";
            String groupCode = "";
            String groupName = "";
            int amount = 0;
            for (int i = 0; i < moneyNoId.length; i++) {
                MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(moneyNoId[i]);
                orderType = mad.getOrderType();
                groupCode = mad.getGroupCode();
                groupName = mad.getGroupName();
                amount = Integer.parseInt(mad.getAmount()) + amount;
            }
            List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = Bpms_riskoff_service.getUnlockedNumber();
            String batchNO = IBM + "CZ" + sateTime;
            MoneyApply myly = new MoneyApply();
            myly.setApplyNo(batchNO);
            myly.setTitle(title);
            myly.setApplyMemo(explain);
            myly.setCreatorId(user.getRowNo() + "");
            myly.setCreatorName(user.getEmployeeName());
            myly.setCreateDate(new Date());
            myly.setState("1");
            //myly.setApplyType("0");
            myly.setOrderType(orderType);
            myly.setGroupCode(groupCode);
            myly.setGroupName(groupName);
            myly.setApplyAmount(amount + "");
            myly.setOpType("2");
            MoneyApply my = claimForFundsService.addMoneyApply(myly);
            for (int i = 0; i < moneyNoId.length; i++) {
                MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(moneyNoId[i]);
                mad.setState("5");
                claimForFundsService.updateMoneyApplyDet(mad);
                if (Integer.parseInt(mad.getOrderType()) == 2) {
                    MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                    String datatime = Bpms_riskoff_service.getUnlockedNumber();
                    String moneyNo = "JTCZ" + IBM + datatime + i;
                    moneyApplyDet.setMoneyNo(moneyNo);
                    moneyApplyDet.setSerialNo(mad.getSerialNo());
                    moneyApplyDet.setOrderType("2");
                    moneyApplyDet.setContrctNo(mad.getContrctNo());
                    moneyApplyDet.setContrctType(mad.getContrctType());
                    moneyApplyDet.setProductName(mad.getProductName());
                    moneyApplyDet.setProductNmb(mad.getProductNmb());
                    moneyApplyDet.setAmount(mad.getAmount());
                    moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                    moneyApplyDet.setApplyNo(my.getApplyNo());
                    moneyApplyDet.setUseType(mad.getUseType());
                    moneyApplyDet.setBillNote(mad.getBillNote());
                    moneyApplyDet.setOpType("2");
                    moneyApplyDet.setGroupCode(mad.getGroupCode());
                    moneyApplyDet.setGroupName(mad.getGroupName());
                    moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                    moneyApplyDet.setState("5");
                    moneyApplyDet.setCreatorId(user.getRowNo() + "");
                    moneyApplyDet.setContrctName(mad.getContrctName());
                    moneyApplyDet.setParentId(moneyNoId[i]);
                    claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                } else {
                    String datatime = Bpms_riskoff_service.getUnlockedNumber();
                    String moneyNo = "GRCZ" + IBM + datatime + i;
                    MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                    moneyApplyDet.setMoneyNo(moneyNo);
                    moneyApplyDet.setSerialNo(mad.getSerialNo());
                    moneyApplyDet.setOrderType("1");
                    moneyApplyDet.setProductName(mad.getProductName());
                    moneyApplyDet.setProductNmb(mad.getProductNmb());
                    moneyApplyDet.setAmount(mad.getAmount());
                    moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                    moneyApplyDet.setApplyNo(my.getApplyNo());
                    moneyApplyDet.setPhoneNo(mad.getPhoneNo());
                    moneyApplyDet.setUseType(mad.getUseType());
                    moneyApplyDet.setOpType("2");
                    moneyApplyDet.setBillNote(mad.getBillNote());
                    moneyApplyDet.setGroupCode(mad.getGroupCode());
                    moneyApplyDet.setGroupName(mad.getGroupName());
                    moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                    moneyApplyDet.setState("5");
                    moneyApplyDet.setCreatorId(user.getRowNo() + "");
                    moneyApplyDet.setContrctName(mad.getContrctName());
                    moneyApplyDet.setParentId(moneyNoId[i]);
                    claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                }
            }
            SystemUser USER = systemUserService.getUserInfoRowNo(userId);
            //流程启动
            Map<String, String> map = new HashMap<>();
            map.put("decisionKey", "APPLY");
            map.put("decisionValue", role);
            String processId = transferJBPMUtils.startTransfer("claimForFundsChange", map);// 流程启动
            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(my.getId());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(MoneyTotal.MONEYTOTAL);
                            claimForFundsService.saveSandA(sa);
                        }
                    }
                }
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            taskService.updateBpms_riskoff_task("", 2, taskId);
            taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
            taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
            String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), userId, user);//预存下一步任务
            WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            }
            correctCommitBackLog(my, userId, processId, user, taskid);// 生成待办
            Write("YES");
        } catch (Error e) {
            Write("NO");
            throw new RuntimeException("事务回滚");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 流程进行中
     */
    public void handleCorrectClaimForFunds() {
        try {
            String pid = getString("processId");//流程id
            String t = getString("t");// 下一步可执行流程线条值
            Integer userid = getInteger("userId");//下一步处理用户id
            String id = getString("id");//冲正id
            String opinion = getString("opinion");//审批意见
            String waitId = getString("waitId");//待办id
            String taskId = getString("taskId");//任务id
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
            if (wt != null) {//结束当前待办
                service.updateWait(wt, this.getRequest());
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            Map<String, String> map = new HashMap<>();
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            if ("区县政企部主任".equals(task.getActivityName())) {
                if (Double.parseDouble(my.getApplyAmount()) / 100 >= Double.parseDouble("10000")) {
                    map.put("decisionKey", "ROLE_DSBM");
                    map.put("decisionValue", "NO");
                }
                jbpmUtil.completeTask(task.getId(), map);//流程流转
            } else if ("省重客客户经理室经理".equals(task.getActivityName())) {
                if (Double.parseDouble(my.getApplyAmount()) / 100 >= Double.parseDouble("10000")) {
                    map.put("decisionKey", "ROLE_SZKSM");
                    map.put("decisionValue", "NO");
                }
                jbpmUtil.completeTask(task.getId(), map);//流程流转
            } else if ("市公司客户经理室经理".equals(task.getActivityName())) {
                if (Double.parseDouble(my.getApplyAmount()) / 100 >= Double.parseDouble("10000")) {
                    map.put("decisionKey", "ROLE_DSSM");
                    map.put("decisionValue", "NO");
                }
                jbpmUtil.completeTask(task.getId(), map);
            }
            MoneyApplyFlow mflow = claimForFundsService.getMoneyApplyFlow(pid);// 根据流程id查询流程表信息
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            String rtaskid = taskService.setBpms_riskoff_task(pid, "", 1, "SH", tasktwo.getActivityName(), userid, user);
            correctCommitBackLog(my, userid, pid, user, rtaskid);//生成待办
            Write("YES");
        } catch (Error e) {
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 提交待办生成
     */
    public void correctCommitBackLog(MoneyApply moneyApply, Integer userid, String processId, SystemUser user,
                                     String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[资金账户冲正]" + moneyApply.getTitle());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/claimForFunds/handleCorrectClaimForFunds.jsp?id=" + moneyApply.getId() + "&processId=" + processId
                + "&taskId=" + taskid + "&applyNo=" + moneyApply.getApplyNo());
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
        waitTask.setTaskId(moneyApply.getId());
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 完成方法
     */
    public void complateCorrectClaimForFunds() {
        try {
            String pid = getString("processId");// 流程id
            String id = getString("id");
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 审批意见
            String taskId = getString("taskId");// 任务表id
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDet(my.getApplyNo());
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotalSerialNo(moneyApplyDet.get(0).getSerialNo());
            SystemUser USER = systemUserService.getUserInfoRowNo(moneyTotal.getUserid());// 获取下一步处理人信息
            String json = claimForFundsService.getCorrectApplyForFunds(moneyTotal.getGroupCode(), my, moneyTotal, moneyApplyDet, USER);
            JSONObject jsthree = JSONObject.fromObject(json);
            String datatwo = jsthree.getString("res");
            JSONObject jsone = JSONObject.fromObject(datatwo);
            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));

            //String s="0";
            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                //if("0".equals(s)){
                for (int i = 0; i < moneyApplyDet.size(); i++) {
                    MoneyApplyDet md = moneyApplyDet.get(i);
                    MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(md.getOldMoneyNo());
                    mad.setState("3");
                    claimForFundsService.updateMoneyApplyDet(mad);
                    md.setState("3");
                    claimForFundsService.updateMoneyApplyDet(md);
                }
                WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    Write("NO");
                    return;
                }
                Map<String, String> map = new HashMap<>();
                Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
                String t = "";
                if ("区县政企部主任".equals(tasktwo.getActivityName())) {
                    t = "ROLE_QXSM";
                    map.put("decisionKey", t);
                    map.put("decisionValue", "YES");
                    jbpmUtil.completeTask(tasktwo.getId(), map, t);
                } else if ("市公司客户经理室经理".equals(tasktwo.getActivityName())) {
                    t = "ROLE_DSDM";
                    map.put("decisionKey", t);
                    map.put("decisionValue", "YES");
                    jbpmUtil.completeTask(tasktwo.getId(), map, t);
                } else if ("省重客客户经理室经理".equals(tasktwo.getActivityName())) {
                    t = "ROLE_SZKSM";
                    map.put("decisionKey", t);
                    map.put("decisionValue", "YES");
                    jbpmUtil.completeTask(tasktwo.getId(), map, t);
                } else {
                    jbpmUtil.completeTask(tasktwo.getId(), "END");
                }
                taskService.updateBpms_riskoff_task(opinion, 2, taskId);
                Task taskD = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
                my.setState("0");
                MoneyApply may = claimForFundsService.updateMoneyApply(my);
                moneyTotal.setUseAmount((Long.parseLong(moneyTotal.getUseAmount()) - Long.parseLong(may.getApplyAmount())) + "");
                moneyTotal.setOverAmount((Long.parseLong(moneyTotal.getOverAmount()) + Long.parseLong(may.getApplyAmount())) + "");
                claimForFundsService.updateMoneyTotal(moneyTotal);
                Write("YES");
            } else {
                throw new Error("资金冲正接口==========");
            }
        } catch (Error ee) {
            Write("NON");
            throw new RuntimeException(" 给事务回滚，自定义");
        } catch (Exception e) {
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 作废流程
     */
    public void InvalidCorrectMoneyApply() {
        try {
            String id = getString("id");
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 作废原因
            String processId = getString("processId");// 流程id
            String taskId = getString("taskId");// 任务表id
            WaitTask wait = service.queryWaitByTaskId(waitId);
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            my.setState("-1");// 状态修改为作废
            MoneyApply may = claimForFundsService.updateMoneyApply(my);
            List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDet(may.getApplyNo());
            int s = 0;
            int y = moneyApplyDet.size();
            for (int i = 0; i < moneyApplyDet.size(); i++) {
                moneyApplyDet.get(i).setOpType("5");
                moneyApplyDet.get(i).setState("6");
                MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(moneyApplyDet.get(i).getOldMoneyNo());
                String[] moneyno = null;
                mad.setOpType("1");
                mad.setState("1");
                claimForFundsService.updateMoneyApplyDet(moneyApplyDet.get(i));
                claimForFundsService.updateMoneyApplyDet(mad);
                String str = relieveCapitalCampOn(mad.getMoneyNo());
                if ("YES".equals(str)) {
                    s++;
                }
            }
            if (s == y) {
                taskService.updateBpms_riskoff_task(opinion, 2, taskId);
                if (wait != null) {
                    service.updateWait(wait, this.getRequest());
                } else {
                    Write("NO");
                    throw new RuntimeException("事务回滚");
                }
                Write("YES");
            } else {
                throw new RuntimeException("事务回滚");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }

    /**
     * 流程退回
     */
    public void returnCorrectClaimForFunds() {
        try {
            String id = getString("id");
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("taskId");// 任务表id
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            my.setState("2");// 修改状态为退回
            MoneyApply may = claimForFundsService.updateMoneyApply(my);
            List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDet(may.getApplyNo());
            for (int i = 0; i < moneyApplyDet.size(); i++) {
                moneyApplyDet.get(i).setOpType("4");
                claimForFundsService.updateMoneyApplyDet(moneyApplyDet.get(i));
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);//查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                Write("NO");
                throw new RuntimeException("事务回滚");
            }
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(my.getCreatorId()), user);
            returnCorrecBackLog(may, may.getCreatorId(), processId, user, rtaskid);// 生成待办
            jbpmUtil.deleteProcessInstance(processId);//删除流程
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 退回待办生成
     */
    public void returnCorrecBackLog(MoneyApply moneyApply, String userid, String processId, SystemUser user, String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[资金账户冲正]" + moneyApply.getTitle());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/claimForFunds/returnCorrectClaimForFunds.jsp?id=" + moneyApply.getId()
                + "&processId=" + processId + "&applyNo=" + moneyApply.getApplyNo() + "&taskId=" + taskId);
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());//处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());//处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());//处理人登录名
        waitTask.setCreateUserId(user.getRowNo());//创建人id
        waitTask.setCreateUserName(user.getEmployeeName());//创建人名称
        waitTask.setCreateLoginName(user.getLoginName());//创建人登录名
        waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
        waitTask.setTaskId(moneyApply.getId());
        service.saveWait(waitTask, this.getRequest());
    }

    //=====================================================资金池退款流程===================================================================

    /**
     * 退款开始流程
     */
    public void refundMoneyTotal() {
        try {
            String title = getString("title");//退款标题
            String explain = getString("explain");//退款说明
            String role = getString("role");// 角色权限
            Integer userId = getInteger("userID");// 下一步任务人id
            String attachmentId = getString("attachmentId");//附件id
            String moneyTotalId = getString("id");//资金池ID
            String waitId = getString("waitId");// 待办id
            String outMoney = getString("outMoney");
            String taskId = getString("taskId");//任务ID
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(moneyTotalId);//查询资金池
            moneyTotal.setState(3);
            claimForFundsService.updateMoneyTotal(moneyTotal);
            String IBM = "";
            int amount = 0;
            List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = Bpms_riskoff_service.getUnlockedNumber();
            String batchNO = IBM + "TK" + sateTime;
            MoneyApply myly = new MoneyApply();
            myly.setApplyNo(batchNO);
            myly.setTitle(title);
            myly.setApplyMemo(explain);
            myly.setCreatorId(user.getRowNo() + "");
            myly.setCreatorName(user.getEmployeeName());
            myly.setCreateDate(new Date());
            myly.setState("1");
            //myly.setApplyType("0");
            myly.setOrderType("4");
            myly.setGroupCode(moneyTotal.getGroupCode());
            myly.setGroupName(moneyTotal.getGroupName());
            myly.setApplyAmount(outMoney);
            myly.setOpType("3");
            myly.setMoneyTotalId(moneyTotal.getId());
            MoneyApply my = claimForFundsService.addMoneyApply(myly);
            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(my.getId());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(MoneyTotal.MONEYTOTAL);
                            claimForFundsService.saveSandA(sa);
                        }
                    }
                }
            }
            SystemUser USER = systemUserService.getUserInfoRowNo(userId);
            //流程启动
            Map<String, String> map = new HashMap<>();
            map.put("decisionKey", "APPLY");
            map.put("decisionValue", role);
            String processId = transferJBPMUtils.startTransfer("claimForFundsChange", map);// 流程启动

            WaitTask wt = service.queryWaitByTaskId(waitId);//查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            taskService.updateBpms_riskoff_task("", 2, taskId);
            taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
            taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
            String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), userId, user);//预存下一步任务
            refundMoneyTotalBackLog(my, userId, processId, user, taskid);//生成待办
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 退款流程进行中
     */
    public void handleRefundMoneyTotal() {
        try {
            String pid = getString("processId");//流程id
            String t = getString("t");// 下一步可执行流程线条值
            Integer userid = getInteger("userId");//下一步处理用户id
            String id = getString("id");//退款id
            String opinion = getString("opinion");//审批意见
            String waitId = getString("waitId");//待办id
            String taskId = getString("taskId");//任务id
            SystemUser USER = systemUserService.getUserInfoRowNo(userid);
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            Map<String, String> map = new HashMap<>();
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            if ("区县政企部主任".equals(task.getActivityName())) {
                if (Double.parseDouble(my.getApplyAmount()) / 100 >= Double.parseDouble("10000")) {
                    map.put("decisionKey", "ROLE_DSBM");
                    map.put("decisionValue", "NO");
                }
                jbpmUtil.completeTask(task.getId(), map);//流程流转
            } else if ("省重客客户经理室经理".equals(task.getActivityName())) {
                if (Double.parseDouble(my.getApplyAmount()) / 100 >= Double.parseDouble("10000")) {
                    map.put("decisionKey", "ROLE_SZKSM");
                    map.put("decisionValue", "NO");
                }
                jbpmUtil.completeTask(task.getId(), map);//流程流转
            } else if ("市公司客户经理室经理".equals(task.getActivityName())) {
                if (Double.parseDouble(my.getApplyAmount()) / 100 >= Double.parseDouble("10000")) {
                    map.put("decisionKey", "ROLE_DSSM");
                    map.put("decisionValue", "NO");
                }
                jbpmUtil.completeTask(task.getId(), map);
            }

            MoneyApplyFlow mflow = claimForFundsService.getMoneyApplyFlow(pid);// 根据流程id查询流程表信息
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            String rtaskid = taskService.setBpms_riskoff_task(pid, "", 1, "SH", tasktwo.getActivityName(), userid, user);
            refundMoneyTotalBackLog(my, userid, pid, user, rtaskid);// 生成待办
            Write("YES");
        } catch (Error e) {
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 完成方法
     */
    public void complateRefundMoneyTotal() {
        try {
            String pid = getString("processId");// 流程id
            String id = getString("id");//退款ID
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 审批意见
            String taskId = getString("taskId");// 任务表id
            WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotalId());
            String json = claimForFundsService.cancelRecharge(moneyTotal.getBatchNo());
            JSONObject jsthree = JSONObject.fromObject(json);
            String datatwo = jsthree.getString("res");
            JSONObject jsone = JSONObject.fromObject(datatwo);
            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
            String s = "0";
            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                //if("0".equals(s)){
                moneyTotal.setState(0);
                moneyTotal.setUserid(null);
                moneyTotal.setGroupCode(null);
                moneyTotal.setGroupName(null);
                moneyTotal.setUseAmount(null);
                moneyTotal.setOverAmount(moneyTotal.getAmount());
                claimForFundsService.updateMoneyTotal(moneyTotal);
				/*String jsontwo = claimForFundsService.updateIncomeState("0",moneyTotal.getSerialNo());
				JSONObject jsthree1 = JSONObject.fromObject(jsontwo);
				String datatwo1 = jsthree1.getString("res");
				JSONObject jsone1 = JSONObject.fromObject(datatwo1);*/
                String ss = "success";
                //if("success".equals(jsone1.getString("code"))){
                if ("success".equals(ss)) {
                    Map<String, String> map = new HashMap<>();
                    Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
                    String t = "";
                    if ("区县政企部主任".equals(tasktwo.getActivityName())) {
                        t = "ROLE_QXSM";
                        map.put("decisionKey", t);
                        map.put("decisionValue", "YES");
                        jbpmUtil.completeTask(tasktwo.getId(), map, t);
                    } else if ("市公司客户经理室经理".equals(tasktwo.getActivityName())) {
                        t = "ROLE_DSDM";
                        map.put("decisionKey", t);
                        map.put("decisionValue", "YES");
                        jbpmUtil.completeTask(tasktwo.getId(), map, t);
                    } else if ("省重客客户经理室经理".equals(tasktwo.getActivityName())) {
                        t = "ROLE_SZKSM";
                        map.put("decisionKey", t);
                        map.put("decisionValue", "YES");
                        jbpmUtil.completeTask(tasktwo.getId(), map, t);
                    } else {
                        jbpmUtil.completeTask(tasktwo.getId(), "END");
                    }
                    Task taskD = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
                    taskService.updateBpms_riskoff_task(opinion, 2, taskId);
                    my.setState("0");
                    MoneyApply may = claimForFundsService.updateMoneyApply(my);
                    if (wt != null) {
                        service.updateWait(wt, this.getRequest());
                    } else {
                        Write("NO");
                        throw new RuntimeException(" 给事务回滚，自定义");
                    }
                }
                Write("YES");
            } else {
                Write("NON");
            }
        } catch (Exception e) {
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 流程退回
     */
    public void returnRefundMoneyTotal() {
        try {
            String id = getString("id");//退款ID
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("taskId");// 任务表id
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            my.setState("2");// 修改状态为退回
            MoneyApply may = claimForFundsService.updateMoneyApply(my);
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(my.getCreatorId()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);//查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                Write("NO");
                throw new RuntimeException("事务回滚");
            }
            returnMoneyTotalBackLog(may, Integer.parseInt(may.getCreatorId()), processId, user, rtaskid);// 生成待办
            jbpmUtil.deleteProcessInstance(processId);// 删除流程
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 作废流程
     */
    public void InvalidRefundMoneyTotal() {
        try {
            String id = getString("id");//退款ID
            String waitId = getString("waitId");//待办id
            String opinion = getString("opinion");//作废原因
            String processId = getString("processId");//流程id
            String taskId = getString("taskId");// 任务表id
            WaitTask wait = service.queryWaitByTaskId(waitId);
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            my.setState("-1");// 状态修改为作废
            MoneyApply may = claimForFundsService.updateMoneyApply(my);
            MoneyTotal mt = claimForFundsService.getMoneyTotal(my.getMoneyTotalId());
            mt.setState(1);
            claimForFundsService.updateMoneyTotal(mt);
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            if (wait != null) {
                service.updateWait(wait, this.getRequest());
            } else {
                Write("NO");
                throw new RuntimeException("事务回滚");
            }
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }

    }

    /**
     * 退款提交待办生成
     */
    public void refundMoneyTotalBackLog(MoneyApply moneyApply, Integer userid, String processId, SystemUser user,
                                        String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[资金退款]" + moneyApply.getTitle());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/claimForFunds/handleRefund.jsp?id=" + moneyApply.getId() + "&processId=" + processId
                + "&taskId=" + taskid + "&applyNo=" + moneyApply.getApplyNo());
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
        waitTask.setTaskId(moneyApply.getId());
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 退回待办生成
     */
    public void returnMoneyTotalBackLog(MoneyApply moneyApply, Integer userid, String processId, SystemUser user, String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[资金退款]" + moneyApply.getTitle());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/claimForFunds/returnRefund.jsp?id=" + moneyApply.getId()
                + "&processId=" + processId + "&applyNo=" + moneyApply.getApplyNo() + "&taskId=" + taskId);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(MoneyTotal.MONEYTOTAL);// 标识
        waitTask.setTaskId(moneyApply.getId());
        service.saveWait(waitTask, this.getRequest());
    }

    public void queryforRefundable() {
        String id = getString("id");
        MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
        if ("0".equals(moneyTotal.getUseAmount())) {
            if (moneyTotal.getAmount().equals(moneyTotal.getOverAmount())) {
                Write("YES");
            }
        } else {
            int dex = 0;
            List<MoneyApplyDet> list = claimForFundsService.getMoneyApplyDetSerialNo(moneyTotal.getSerialNo());
            for (int i = 0; i < list.size(); i++) {
                if ("3".equals(list.get(i).getState())) {
                    dex++;
                }
            }
            int s = list.size() - dex;
            Write(s + "");
        }
    }

    public void getMoneyApplyDettwo() {
        try {
            String applyNo = getString("applyNo");
            List<MoneyApplyDet> list = claimForFundsService.getMoneyApplyDet(applyNo);
            List<MoneyApplyDet> listjson = new ArrayList<>();
            for (int i = 0; i < list.size(); i++) {
                MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(list.get(i).getOldMoneyNo());
                listjson.add(mad);
            }
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(listjson);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void getMoneyApplyFlow() {
        try {
            String applyNo = getString("applyNo");
            MoneyApplyFlow mad = claimForFundsService.getMoneyApplyFlowTwo(applyNo);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(mad);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void manualSynchronization() {
        try {
            String type = claimForFundsService.downloadFtpFile(dateOfThePreviousDay(), "/", "TransferDetail" + dateOfThePreviousDay());
            Write(type);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void manualSynchronizationtwo() {
        try {
            //"20190508","/","TransferDetail20190110"
            String type = claimForFundsService.downloadFtpFile();
            Write(type);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void downloadBossFtpFile() {
        try {
            String type = claimForFundsService.downloadBossFtpFile(dateOfThePreviousDay());
            Write(type);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void uploadFtpFile() {
        try {
            boolean type = claimForFundsService.uploadTxt();
            System.out.println("文件上传状态：" + type);
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 日期转换
     *
     * @return
     */
    public static String dateOfThePreviousDay() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DATE, 0);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMdd");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

    public void setBossType() {
        try {
            String id = getString("id");
            String jsontwo = claimForFundsService.updateIncomeState("0", id);
            JSONObject jsthree1 = JSONObject.fromObject(jsontwo);
            String datatwo1 = jsthree1.getString("res");
            JSONObject jsone1 = JSONObject.fromObject(datatwo1);
            if ("success".equals(jsone1.getString("code"))) {
                Write(jsone1.getString("code"));
            } else {
                Write("解除预占失败");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("解除预占失败");
        }
    }

    public void updateIncomeState() {
        try {
            String serialNo = getString("serialNo");
            String status = getString("status");
            Result jsontwo = ClaimFundsOpenSrv.getInstance().updateIncomeState(status,serialNo);
            logger.info("资金推送财务使用状态推送数据返回信息"+jsontwo);
            JSONObject jsthree1 = JSONObject.fromObject(jsontwo);
            String datatwo1 = jsthree1.getString("res");
            JSONObject jsone1 = JSONObject.fromObject(datatwo1);
            if ("success".equals(jsone1.getString("code"))) {
                Write(jsone1.getString("code"));
            } else {
                Write("操作失败,资金推送财务使用状态推送数据返回信息:"+jsontwo);
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("操作失败");
        }
    }


    public void selectUser() {
        try {
            String ROLE = getString("ROLE");
            List<Map<String, Object>> map = claimForFundsService.findDept(user.getRowNo());
            String county_name = map.get(0).get("TWODNAME").toString();
            String company_name = map.get(0).get("COMPANY_NAME").toString();
            List<Map<String, String>> sd = claimForFundsService.SelectZtreeByUId(ROLE, company_name, county_name);
            if (sd.size() > 1) {
                Write("NON");
            } else {
                if (sd.size() == 0) {
                    Write("NO");
                } else {
                    if ("".equals(sd.get(0).get("BOSSUSERNAME")) || sd.get(0).get("BOSSUSERNAME") == null || "null".equals(sd.get(0).get("BOSSUSERNAME"))) {
                        Write("NON2");
                    } else {
                        Write(JSONHelper.SerializeWithNeedAnnotation(sd.get(0).get("ROWNO")));//.get("ROWNO"));
                    }
                    Write("NO");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NON3");
        }
    }

    public void callSubGroup() {
        try {
            String group_id = getString("group_id");
            String resxml=GroupCustomerService.getInstance().queryRelaCust(group_id);
            JSONObject obj = XmlUtil.xml2Json(resxml);
            JSONObject body = JSONObject.fromObject(obj.get("Body"));
            JSONObject response = JSONObject.fromObject(body.get("commonServiceResponse"));
            String commonServiceReturn = response.getString("commonServiceReturn");
            Write(XmlUtil.xml2Json(commonServiceReturn).toString());
			/*String json = "{\"HEADER\":{\"RESPONSE\":{\"DESC\":\"流程处理成功\",\"CODE\":\"0000\"},\"TRANSACTION_ID\":\"202008261736160912509556\"}," +
					"\"RESULT\":{\"RELA_CUST_LIST\":[{\"CUST_NAME\":\"四川省泸州市龙马潭区地方税务局\"," +
					"\"CUST_CODE\":\"2802738409\"},{\"CUST_NAME\":\"国家税务总局泸州市江阳区税务局\",\"CUST_CODE\":\"2800024117\"}," +
					"{\"CUST_NAME\":\"国家税务总局叙永县税务局\",\"CUST_CODE\":\"2800005606\"},{\"CUST_NAME\":\"四川省泸州市纳溪区地方税务局\"," +
					"\"CUST_CODE\":\"2800003640\"},{\"CUST_NAME\":\"泸州市古蔺县地方税务局\",\"CUST_CODE\":\"2800068381\"}," +
					"{\"CUST_NAME\":\"合江县地税\",\"CUST_CODE\":\"2800033747\"},{\"CUST_NAME\":\"泸州市泸县地方税务局\"," +
					"\"CUST_CODE\":\"2803148700\"},{\"CUST_NAME\":\"四川省地方税务局\",\"CUST_CODE\":\"2800052347\"}," +
					"{\"CUST_NAME\":\"四川省泸州市地方税务局第二直属税务分局\",\"CUST_CODE\":\"2807366504\"}," +
					"{\"CUST_NAME\":\"四川省泸州市地方税务局第一直属税务分局\",\"CUST_CODE\":\"2807366497\"}," +
					"{\"CUST_NAME\":\"四川省泸州市地方税务局第二直属税务分局\",\"CUST_CODE\":\"2801902362\"}]}}";
			Write(json);*/
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }


    public static String getEng(String a) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < a.length(); i++) {
            char c = a.charAt(i);
            if ((c <= 'z' && c >= 'a') || (c <= 'Z' && c >= 'A')) {
                sb.append(c);
            }
        }
        return sb.toString();
    }

    /**
     * 查询认领详情
     *
     * @return
     */
    public void getMoneyApplySubGroup() {
        try {
            String id = getString("id");
            List<MoneyApplySubGroup> p = claimForFundsService.getMoneyApplySubGroup(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 获取集团详细
     */
    public void queryGroupUserById() {
        Map<String, Object> map = new HashMap<>();
        JSONObject obj = new JSONObject();
        boolean bl = true;
        String message = "";
        try {
            String groupCoding = getString("groupCode");
            GroupCustomer customer = GroupCustomerService.getInstance().getCustInfoQuery(groupCoding, "");

            if (customer != null) {
                String userCompanyCode = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
                if (customer.getUser_name() == null || "".equals(customer.getUser_name())) {
                    map.put("user_id", "");
                    map.put("user_name", "");
                    bl = false;
                    message = "亲爱的同事，你选择的集团未查询到客户经理工号。";
                } else {
                    SystemUser groupUser = systemUserService.queryByBoss(customer.getUser_name());
                    List<SystemDept> sdList = systemUserService.getDeptListByRowNo(groupUser.getRowNo());
                    String groupCompanyCode = sdList.get(0).getSystemCompany().getCompanyCode();
                    if (userCompanyCode.equals(groupCompanyCode)) {
                        map.put("user_id", String.valueOf(groupUser.getRowNo()));
                        map.put("user_name", groupUser.getEmployeeName());
                        bl = true;
                        message = "ok!";
                    } else {
                        map.put("user_id", "");
                        map.put("user_name", "");
                        bl = false;
                        message = "亲爱的同事，你选择的集团归属客户经理不属于你所在地市。";
                    }
                }
                obj.put("user_type", bl);
                obj.put("user_message", message);
                obj.put("user_obj", map);
                Write(JSONHelper.SerializeWithNeedAnnotation(obj));
            } else {
                map.put("user_id", "");
                map.put("user_name", "");
                obj.put("user_type", false);
                obj.put("user_message", "未查询到子集团");
                obj.put("user_obj", map);
                Write(JSONHelper.SerializeWithNeedAnnotation(obj));
            }
        } catch (Exception e) {
            e.printStackTrace();
            map.put("user_id", "");
            map.put("user_name", "");
            obj.put("user_type", false);
            obj.put("user_message", "查询出错了");
            obj.put("user_obj", map);
            Write(JSONHelper.SerializeWithNeedAnnotation(obj));
        }
    }

    //=======================================================手动冲正=======================================================================

    /**
     * W未使用 R已冲正 U已使用 M预占 C冲正  T暂挂
     */
    public void queryVerification() {
        try {
            String serialNo = getString("serialNo");//账户ID
            List<Map<String, String>> list = new ArrayList<Map<String, String>>();
            List<MoneyApplyDet> madList = claimForFundsService.getMoneyApplyDetSerialNo(serialNo);
            for (int i = 0; i < madList.size(); i++) {
                Map<String, String> map = new HashMap<String, String>();
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(madList.get(i).getCreatorId()));// 获取下一步处理人信息
                String json = claimForFundsService.qryUseRecordState(USER, madList.get(i).getGroupCode(), madList.get(i).getMoneyNo());
                JSONObject js = JSONObject.fromObject(json);
                String data1 = js.getString("res");
                JSONObject js2 = JSONObject.fromObject(data1);
                JSONObject js3 = JSONObject.fromObject(js2.getString("ROOT"));
                JSONObject js4 = JSONObject.fromObject(js3.getString("OUT_DATA"));
                JSONArray js5 = JSONArray.fromObject(js4.getString("APPLY_INFO"));
                JSONObject js6 = JSONObject.fromObject(js5.get(0));
                String js7 = js6.getString("STATUS_CODE");
                if ("U".equals(js7)) {
                    madList.get(i).setState("2");
                    claimForFundsService.updateMoneyApplyDet(madList.get(i));
                    map.put("status_code", js7);
                    map.put("moneyNo", madList.get(i).getMoneyNo());
                    list.add(map);
                }
            }
            if (list.size() > 0) {
                String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(list);
                Write(json);
            } else {
                Write("YES");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("资金认领错误信息：" + e.getMessage(), e);
            Write("NO");
        }
    }


    /**
     * 资金暂挂
     */
    public String fundsSuspended() {
        try {
            int ts = 0;
            StringBuffer sub = new StringBuffer();
            StringBuffer failSub = new StringBuffer();
            String serialNo = getString("serialNo");//账户ID
            List<MoneyApplyDet> madList = claimForFundsService.getMoneyApplyDetSerialNo(serialNo);
            for (int i = 0; i < madList.size(); i++) {
                Result json = ClaimFundsOpenSrv.getInstance().capitalCampOn(madList.get(i).getMoneyNo());
                JSONObject jsthree = JSONObject.fromObject(json);
                String datatwo = jsthree.getString("data");
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    ts++;
                    sub.append(madList.get(i).getMoneyNo() + ";");
                } else {
                    failSub.append(madList.get(i).getMoneyNo() + "暂挂失败:" + jstwo.getString("RETURN_MSG"));
                }
            }
            if (madList.size() == ts) {
                return "YES";
            } else {
                return sub.toString() + "暂挂成功;" + failSub.toString();
            }
        } catch (Exception e) {
            e.printStackTrace();
            return e.getMessage();
        }
    }


    public void insertBossGroup() {
        try {
            String id = getString("id");//开票id4
			/*MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
			List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDet(my.getApplyNo());
			MoneyTotal moneyTotal = claimForFundsService.getMoneyTotalSerialNo(moneyApplyDet.get(0).getSerialNo());
			SystemUser USER = systemUserService.getUserInfoRowNo(moneyTotal.getUserid());// 获取下一步处理人信息
			String json = claimForFundsService.getCorrectApplyForFunds(moneyTotal.getGroupCode(), my, moneyTotal, moneyApplyDet, USER);*/
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(my.getMoneyTotal_id());
            int count = 0;
            StringBuffer bossMsg = new StringBuffer();
            List<MoneyApplySubGroup> sub = claimForFundsService.getMoneyApplySubGroup(my.getId());
            for (int m = 0; m < sub.size(); m++) {
                MoneyTotal subMoneyTotal = claimForFundsService.getMoneyTotal(sub.get(m).getMoneyTotal_id());
                SystemUser subUser = systemUserService.getUserInfoRowNo(Integer.parseInt(my.getApprovalNumber()));
                String json = claimForFundsService.getReChargeUnitAccount(subMoneyTotal.getGroupCode(), "", my, subMoneyTotal, subUser);
                JSONObject js = JSONObject.fromObject(json);
                String data1 = js.getString("res");
                JSONObject js2 = JSONObject.fromObject(data1);
                JSONObject js3 = JSONObject.fromObject(js2.getString("ROOT"));
                logger.info("资金BOSS推送子集团金额返回数据:" + json);
                if ("0".equals(js3.getString("RETURN_CODE"))) {
                    ++count;
                    sub.get(m).setBoss_State(1);
                    subMoneyTotal.setBoss_State(1);
                    subMoneyTotal.setState(1);
                    subMoneyTotal.setIsThe(subMoneyTotal.getIsThe() + 1);
                } else {
                    bossMsg.append(sub.get(m).getGroup_code() + ":" + js3.getString("RETURN_MSG") + ";");
                    sub.get(m).setBoss_State(0);
                    sub.get(m).setBoss_Msg(js3.getString("RETURN_MSG"));
                    subMoneyTotal.setBoss_State(0);
                    subMoneyTotal.setState(1);
                    subMoneyTotal.setBoss_Msg(js3.getString("RETURN_MSG"));
                }
                claimForFundsService.updateMoneyApplySubGroup(sub.get(m));
                claimForFundsService.updateMoneyTotalTwo(subMoneyTotal);
            }
            if (count < sub.size()) {
                Write("分配子集团推送失败:" + bossMsg.toString());
                return;
            }else{
                Write("推送成功");
            }
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
            Write("程序错误");
        }
    }

    /**
     * 生成资金认领-->使用资金冲正待办
     */
    public void capitalCorrection(){
        try{
            String id = getString("id");
            String detList = getString("listJson");
            String[] detArray = detList.split(",");
            List<String> bossArray = new ArrayList<>();
            for (int i = 0;i<detArray.length;i++){
                MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(detArray[i]);
                if ("0".equals(mad.getBossState())){
                    if (!"1".equals(mad.getUseType())&&!"05".equals(mad.getUseType())){
                        bossArray.add(detArray[i]);
                    }
                }
            }
            String[] boss = new String[bossArray.size()];
            bossArray.toArray(boss);
            String tys =capitalCampOn(boss);
            if ("YES".equals(tys)) {
                String IBM = "";
                Integer number = 0;
                List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                MoneyApply moneyApply= claimForFundsService.getMoneyApply(id);//查询认领信息
                //生成新的冲正工单
                MoneyApply myly = new MoneyApply();
                String sateTime = Bpms_riskoff_service.getUnlockedNumber();
                String batchNO = IBM+"CZ"+sateTime;
                myly.setApplyNo(batchNO);
                myly.setTitle("【冲正工单】"+moneyApply.getTitle());
                myly.setApplyMemo(moneyApply.getApplyMemo());
                myly.setApplyAmount(moneyApply.getApplyAmount());
                myly.setCreatorId(user.getRowNo()+"");
                myly.setCreatorName(user.getEmployeeName());
                myly.setCreateDate(new Date());
                myly.setState("1");
                myly.setGroupCode(moneyApply.getGroupCode());
                myly.setGroupName(moneyApply.getGroupName());
                myly.setOpType("2");
                myly.setApprovalNumber(moneyApply.getApprovalNumber());
                myly.setOrderType(moneyApply.getOrderType());
                myly.setSerialNo(moneyApply.getSerialNo());
                myly.setMa_type(moneyApply.getMa_type());
                myly.setMoneyTotal_id(moneyApply.getMoneyTotal_id());
                myly.setBefore_ApplyNo(moneyApply.getApplyNo());
                myly.setSubGroup_amount(moneyApply.getSubGroup_amount());
                myly.setLateFeeMoney(moneyApply.getLateFeeMoney());
                MoneyApply my= claimForFundsService.addMoneyApply(myly);
                //修改工单对应的冲正明细
                for (String det:detArray){
                    MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(det);
                    if (mad!=null){
                        MoneyApplyDet moneyApplyDet= new MoneyApplyDet();
                        if(Integer.parseInt(mad.getOrderType())==2){
                            String datatime = Bpms_riskoff_service.getUnlockedNumber();
                            String moneyNo ="JTCZ"+ datatime+number.toString();
                            moneyApplyDet.setMoneyNo(moneyNo);
                            moneyApplyDet.setSerialNo(mad.getSerialNo());
                            moneyApplyDet.setOrderType("2");
                            moneyApplyDet.setContrctNo(mad.getContrctNo());
                            moneyApplyDet.setContrctType(mad.getContrctType());
                            moneyApplyDet.setProductName(mad.getProductName());
                            moneyApplyDet.setProductNmb(mad.getProductNmb());
                            moneyApplyDet.setAmount(mad.getAmount());
                            moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                            moneyApplyDet.setApplyNo(my.getApplyNo());
                            moneyApplyDet.setUseType(mad.getUseType());
                            moneyApplyDet.setBillNote(mad.getBillNote());
                            moneyApplyDet.setOpType("2");
                            moneyApplyDet.setGroupCode(mad.getGroupCode());
                            moneyApplyDet.setGroupName(mad.getGroupName());
                            moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                            moneyApplyDet.setState("5");
                            moneyApplyDet.setCreatorId(user.getRowNo()+"");
                            moneyApplyDet.setContrctName(mad.getContrctName());
                            moneyApplyDet.setParentId(mad.getId());
                            moneyApplyDet.setSerialNo(mad.getSerialNo());
                            moneyApplyDet.setLateFeeMoney(mad.getLateFeeMoney());
                            moneyApplyDet.setBossState(mad.getBossState());
                        }else{
                            String datatime = Bpms_riskoff_service.getUnlockedNumber();
                            String moneyNo ="GRCZ"+datatime+number.toString();
                            moneyApplyDet.setMoneyNo(moneyNo);
                            moneyApplyDet.setSerialNo(mad.getSerialNo());
                            moneyApplyDet.setOrderType("1");
                            moneyApplyDet.setProductName(mad.getProductName());
                            moneyApplyDet.setProductNmb(mad.getProductNmb());
                            moneyApplyDet.setAmount(mad.getAmount());
                            moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                            moneyApplyDet.setApplyNo(my.getApplyNo());
                            moneyApplyDet.setPhoneNo(mad.getPhoneNo());
                            moneyApplyDet.setUseType(mad.getUseType());
                            moneyApplyDet.setOpType("2");
                            moneyApplyDet.setBillNote(mad.getBillNote());
                            moneyApplyDet.setGroupCode(mad.getGroupCode());
                            moneyApplyDet.setGroupName(mad.getGroupName());
                            moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                            moneyApplyDet.setState("5");
                            moneyApplyDet.setCreatorId(user.getRowNo()+"");
                            moneyApplyDet.setContrctName(mad.getContrctName());
                            moneyApplyDet.setParentId(mad.getId());
                            moneyApplyDet.setSerialNo(mad.getSerialNo());
                            moneyApplyDet.setLateFeeMoney(mad.getLateFeeMoney());
                            moneyApplyDet.setBossState(mad.getBossState());

                        }
                        MoneyApplyDet moneyApplyDet1 = claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                        mad.setRectificaState(mad.getState());
                        mad.setState("5");
                        mad.setOldMoneyNo(moneyApplyDet1.getMoneyNo());
                        claimForFundsService.updateMoneyApplyDet(mad);
                    }
                    number++;
                }
                //生成审批待办
                WaitTask waitTask = new WaitTask();
                waitTask.setName("[资金冲正]" + my.getTitle());//待办名称
                waitTask.setCreationTime(new Date());// 代办生成时间
                waitTask.setUrl("jsp/claimForFunds/handleClaim.jsp?id="+my.getId()+"&applyNo="+my.getApplyNo());
                List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(id);
                Integer userid=null;
                for (Bpms_riskoff_task task:taskList){
                    if ("区县政企部主任".equals(task.getName())
                            ||"市公司客户经理室经理".equals(task.getName())
                            ||"省重客客户经理室经理".equals(task.getName())){
                        userid =task.getOper_no();
                    }
                }
                SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
                waitTask.setState(waitTask.HANDLE);// 状态为待处理
                waitTask.setHandleUserId(USER.getRowNo());// 处理人id
                waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
                waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
                waitTask.setCreateUserId(user.getRowNo());// 创建人id
                waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
                waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
                waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
                waitTask.setTaskId(my.getId());
                service.saveWait(waitTask, this.getRequest());
                Write("您的订单已推送："+USER.getEmployeeName()+",请等待审批！");
            }else {
                Write("亲爱的同事,工单预占未通过,请确认！");
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("资金认领错误信息："+e.getMessage(),e);
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }
    /**
     * 资金认领-->使用资金冲正
     */


    public void openPassOn(){
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            WaitTask wait = service.queryWaitByTaskId(waitId);
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNoTwo(my.getApplyNo());//查询冲正的对应明细
            int num = 0;
            for (int i = 0; i < moneyApplyDet.size(); i++) {//循环明细
                MoneyApplyDet mad = moneyApplyDet.get(i);
                MoneyApplyDet madtwo = claimForFundsService.getMoneyApplyDetId(mad.getOldMoneyNo());
                if ("0".equals(madtwo.getBossState())){
                    if (!"1".equals(madtwo.getUseType())&&!"05".equals(madtwo.getUseType())){
                        if (!"YES".equals(relieveCapitalCampOn(mad.getOldMoneyNo()))){
                            num++;
                        }
                    }
                }
            }
            if (num==0){
                if(wait!=null){
                    //完成首页待办
                    service.updateWait(wait, this.getRequest());
                }
                Write("YES");
            }else {
                Write("工单中有"+num+"条明细,未解除预占！");
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("资金认领错误信息："+e.getMessage(),e);
            Write(e.getMessage());
            throw new RuntimeException("事务回滚");
        }
    }

    /***
     * 冲正取消方法
     */
    public void openOverruleClaim() {
        try{
            String id = getString("id");
            String waitId = getString("waitId");
            WaitTask wait = service.queryWaitByTaskId(waitId);
            Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询冲正信息
            List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNoTwo(my.getApplyNo());//查询冲正的对应明细
            int num = 0;
            for(int i=0;i<moneyApplyDet.size();i++){
                MoneyApplyDet mad = moneyApplyDet.get(i);
                MoneyApplyDet madtwo = claimForFundsService.getMoneyApplyDetByNumber(mad.getOldMoneyNo());
                if (mad!=null){
                    if (!"1".equals(madtwo.getUseType())&&!"05".equals(madtwo.getUseType())){
                        if ("YES".equals(relieveCapitalCampOn(mad.getOldMoneyNo()))){
                            mad.setState("4");
                            madtwo.setState(madtwo.getRectificaState());
                            madtwo.setRectificaState(null);
                            madtwo.setOldMoneyNo(null);
                            claimForFundsService.updateMoneyApplyDet(madtwo);
                            claimForFundsService.updateMoneyApplyDet(mad);
                        }else {
                            ++num;
                        }
                    }
                }
            }
            if (num==0){
                my.setState("-1");
                claimForFundsService.updateMoneyApply(my);
                taskService.updateBpms_riskoff_task("作废", -1, btask.getId());
                if(wait!=null){
                    service.updateWait(wait, this.getRequest());
                }
                Write(returnPars(1, "", "作废成功"));
            }else {
                Write(returnPars(-1, "", "工单中有"+num+"条明细,未解除预占！"));
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("资金认领错误信息："+e.getMessage(),e);
            Write(e.getMessage());
            Write(returnPars(-1, "", "未知错误"+e.getMessage()));
            throw new RuntimeException("事务回滚");
        }
    }

    public void  processtrackingClaim(){
        String id = getString("id");
        List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(id);//任务信息
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(taskList));
    }

    /**
     * 分转元，转换为bigDecimal在toString
     * @return
     */
    public static String changeF2Y(String price) {
        return BigDecimal.valueOf(Long.valueOf(price)).divide(new BigDecimal(100)).toString();
    }

    public void getMoneyApplyDetlistReform() {
        try {
            String id = getString("id");
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
            List<MoneyApplyDet> canItBeVerifiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReform(moneyTotal.getSerialNo());
            if(canItBeVerifiedMoneyApply.size()==0){
                Write(returnPars(-1, "", "未查询到资金需要冲正的明细信息"));
            }else {
                String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(canItBeVerifiedMoneyApply);
                Write(returnPars(1, json, ""));
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write(returnPars(-1,"","查询资金明细信息失败,请联系系统管理员"));
        }
    }

    public void verifyWhetherItCanBeCorrected() {
        try {
            String id = getString("id");
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
            List<MoneyTotal> monList = claimForFundsService.getMoneyTotalSerialNoList(moneyTotal.getSerialNo());
            List<MoneyApplyDet> canItBeVerifiedMoneyApply = claimForFundsService.getMoneyApplyDetlistReform(moneyTotal.getSerialNo());
            if(monList.size()>1){//大于1就是分配
                if(canItBeVerifiedMoneyApply.size()==0){
                    Write(returnPars(-1, "", "用户您好!当前划拨集团资金暂不支持全部冲正哦"));
                }else {
                    Write(returnPars(1, "0", ""));//0就是使用冲正
                }
            }else{//不分配
                if(moneyTotal.getAmount().equals(moneyTotal.getOverAmount())&&canItBeVerifiedMoneyApply.size()==0){
                    SimpleDateFormat dateFormat = new SimpleDateFormat("yyyyMM");
                    if (moneyTotal.getPushDate()!=null){		//是否有记录时间
                        if (dateFormat.format(moneyTotal.getPushDate()).equals(dateFormat.format(new Date()))){
                            Write(returnPars(1, "1", ""));//未跨月允许冲正
                        }else {
                            Write(returnPars(-1, "", "亲爱的同事，由于业务规则限制认领记录跨月不允许冲正"));//已跨月不允许冲正
                        }
                    }else {
                        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH-mm-ss");
                        try {		//判断boss_msg是否为推送时间
                            Date date = format.parse(moneyTotal.getBoss_Msg());
                            if (dateFormat.format(date).equals(dateFormat.format(new Date()))){
                                Write(returnPars(1, "1", ""));//未跨月允许冲正
                            }else {
                                Write(returnPars(-1, "", "亲爱的同事，由于业务规则限制认领记录跨月不允许冲正"));//已跨月不允许冲正
                            }
                        }catch (Exception e){		//获取最新工单的创建1时间
                            List<MoneyApply> moneyApplyList = claimForFundsService.GetMoneyApplyListBySerialNo(moneyTotal.getSerialNo());
                            if (moneyApplyList.size()<1){
                                Write(returnPars(-1, "", "数据查询异常,未查询到最新的推送工单！"));
                            }else {
                                if (dateFormat.format(moneyApplyList.get(0).getCreateDate()).equals(dateFormat.format(new Date()))){
                                    Write(returnPars(1, "1", ""));//未跨月允许冲正
                                }else {
                                    Write(returnPars(-1, "", "亲爱的同事，由于业务规则限制认领记录跨月不允许冲正"));//已跨月不允许冲正
                                }
                            }
                        }
                    }
                }else {
                    Write(returnPars(1, "0", ""));
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("冲正BOSS资金总金额错误："+e.getMessage(),e);
            Write(returnPars(-1,"","查询资金明细信息失败,请联系系统管理员"));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description: TODO 返回参数生成
     */
    private static String returnPars(int state,String data,String msg){
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code",state);
        mapJson.put("data",data);
        mapJson.put("msg",msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }


    /**
     * @author: liyang
     * @date: 2021/3/8 14:25
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 资金认领冲正开始
     */
    public void addCapitalRectification(){
        try{
            String id = getString("id");
            String type = getString("type");
            String detList = getString("listJson");
            if("-1".equals(type)){
                MoneyApply moneyApply=claimForFundsService.getMoneyApply(id);
                WaitTask wait = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
                List<MoneyApplyDet> list = claimForFundsService.getMoneyApplyDetByApplyNo(moneyApply.getApplyNo());
                List<String> bossArray = new ArrayList<>();
                for(int j=0;j<list.size();j++){
                    bossArray.add(list.get(j).getOldMoneyNo());
                }
                String[] boss = new String[bossArray.size()];
                bossArray.toArray(boss);
                List<MoneyApply> oneyApplymList = claimForFundsService.getMoneyApplyList(boss);
                MoneyApply monDet = null;
                if (oneyApplymList.size() > 0) {
                    Random random = new Random();
                    int n = random.nextInt(oneyApplymList.size());
                    monDet = oneyApplymList.get(n);
                } else {
                    Write(returnPars(-1, "", "亲爱的同事,未查询到下一步处理人,请联系系统管理员"));
                    return;
                }
                String IBM = "";
                Integer number = 0;
                List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                MoneyApply myly = new MoneyApply();
                String sateTime = Bpms_riskoff_service.getUnlockedNumber();
                String batchNO = IBM + "CZ" + sateTime;
                myly.setApplyNo(batchNO);
                myly.setTitle(moneyApply.getTitle());
                myly.setApplyMemo("");
                myly.setCreatorId(moneyApply.getCreatorId());
                myly.setCreatorName(moneyApply.getCreatorName());
                myly.setCreateDate(new Date());
                myly.setState("1");
                myly.setGroupCode(moneyApply.getGroupCode());
                myly.setGroupName(moneyApply.getGroupName());
                myly.setOpType("2");
                myly.setOrderType("3");
                myly.setSerialNo(moneyApply.getSerialNo());
                myly.setMa_type("3");
                myly.setMoneyTotal_id(moneyApply.getMoneyTotal_id());
                myly.setBefore_ApplyNo("");
                myly.setSubGroup_amount("");
                myly.setLateFeeMoney(moneyApply.getLateFeeMoney());
                //修改工单对应的冲正明细
                for (MoneyApplyDet mad : list) {
                    MoneyApplyDet det = claimForFundsService.getMoneyApplyDetId(mad.getOldMoneyNo());
                    if (det != null) {
                        MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                        if (Integer.parseInt(det.getOrderType()) == 2) {
                            String datatime = Bpms_riskoff_service.getUnlockedNumber();
                            String moneyNo = "JTCZ" + datatime + number.toString();
                            moneyApplyDet.setMoneyNo(moneyNo);
                            moneyApplyDet.setSerialNo(det.getSerialNo());
                            moneyApplyDet.setOrderType("2");
                            moneyApplyDet.setContrctNo(det.getContrctNo());
                            moneyApplyDet.setContrctType(det.getContrctType());
                            moneyApplyDet.setProductName(det.getProductName());
                            moneyApplyDet.setProductNmb(det.getProductNmb());
                            moneyApplyDet.setAmount(det.getAmount());
                            moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                            moneyApplyDet.setApplyNo(batchNO);
                            moneyApplyDet.setUseType(det.getUseType());
                            moneyApplyDet.setBillNote(det.getBillNote());
                            moneyApplyDet.setOpType("2");
                            moneyApplyDet.setGroupCode(det.getGroupCode());
                            moneyApplyDet.setGroupName(det.getGroupName());
                            moneyApplyDet.setOldMoneyNo(det.getMoneyNo());
                            moneyApplyDet.setState("5");
                            moneyApplyDet.setCreatorId(user.getRowNo() + "");
                            moneyApplyDet.setContrctName(det.getContrctName());
                            moneyApplyDet.setParentId(det.getId());
                            moneyApplyDet.setSerialNo(det.getSerialNo());
                            moneyApplyDet.setLateFeeMoney(det.getLateFeeMoney());
                            moneyApplyDet.setBossState(det.getBossState());
                        } else {
                            String datatime = Bpms_riskoff_service.getUnlockedNumber();
                            String moneyNo = "GRCZ" + datatime + number.toString();
                            moneyApplyDet.setMoneyNo(moneyNo);
                            moneyApplyDet.setSerialNo(det.getSerialNo());
                            moneyApplyDet.setOrderType("1");
                            moneyApplyDet.setProductName(det.getProductName());
                            moneyApplyDet.setProductNmb(det.getProductNmb());
                            moneyApplyDet.setAmount(det.getAmount());
                            moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                            moneyApplyDet.setApplyNo(batchNO);
                            moneyApplyDet.setPhoneNo(det.getPhoneNo());
                            moneyApplyDet.setUseType(det.getUseType());
                            moneyApplyDet.setOpType("2");
                            moneyApplyDet.setBillNote(det.getBillNote());
                            moneyApplyDet.setGroupCode(det.getGroupCode());
                            moneyApplyDet.setGroupName(det.getGroupName());
                            moneyApplyDet.setOldMoneyNo(det.getMoneyNo());
                            moneyApplyDet.setState("5");
                            moneyApplyDet.setCreatorId(user.getRowNo() + "");
                            moneyApplyDet.setContrctName(det.getContrctName());
                            moneyApplyDet.setParentId(det.getId());
                            moneyApplyDet.setSerialNo(det.getSerialNo());
                            moneyApplyDet.setLateFeeMoney(det.getLateFeeMoney());
                            moneyApplyDet.setBossState(det.getBossState());
                        }
                        claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                        det.setOldMoneyNo(moneyApplyDet.getMoneyNo());
                        mad.setState("4");
                        claimForFundsService.updateMoneyApplyDet(det);
                        claimForFundsService.updateMoneyApplyDet(mad);
                    }
                    ++number;
                }
                myly.setApplyAmount(moneyApply.getApplyAmount());
                MoneyApply my = claimForFundsService.addMoneyApply(myly);
                List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(monDet.getId());
                Integer userid=null;
                String taskName = "";
                for (Bpms_riskoff_task task:taskList){
                    if ("区县政企部主任".equals(task.getName())
                            ||"市公司客户经理室经理".equals(task.getName())
                            ||"省重客客户经理室经理".equals(task.getName())){
                        userid =task.getOper_no();
                        taskName=task.getName();
                    }
                }
                Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
                taskService.updateBpms_riskoff_task("退回", 0, btask.getId());
                //生成任务信息
                String processId = "claimForFundsChange." + sateTime;
                taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
                taskService.setBpms_riskoff_task(processId, "发起工单", 2, "CZ", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
                String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "CZ", taskName, userid, user);//预存下一步任务
                //生成审批待办
                WaitTask waitTask = new WaitTask();
                waitTask.setName("[资金冲正]" + my.getTitle());//待办名称
                waitTask.setCreationTime(new Date());// 代办生成时间
                waitTask.setUrl("jsp/claimForFunds/handleClaim.jsp?id=" + my.getId() + "&applyNo=" + my.getApplyNo() + "&taskId=" + taskid);
                SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
                waitTask.setState(waitTask.HANDLE);// 状态为待处理
                waitTask.setHandleUserId(USER.getRowNo());// 处理人id
                waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
                waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
                waitTask.setCreateUserId(user.getRowNo());// 创建人id
                waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
                waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
                waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
                waitTask.setTaskId(my.getId());
                service.saveWait(waitTask, this.getRequest());
                if (wait != null) {
                    service.updateWait(wait, this.getRequest());
                }
                Write(returnPars(1, "", "您的订单已推送：" + USER.getEmployeeName() + ",请等待审批！"));
            }else {
                String[] detArray = detList.split(",");
                List<String> bossArray = new ArrayList<>();
                for (int i = 0; i < detArray.length; i++) {
                    MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(detArray[i]);
                    if ("0".equals(mad.getBossState())) {
                        if (!"1".equals(mad.getUseType()) && !"05".equals(mad.getUseType())) {
                            bossArray.add(detArray[i]);
                        }
                    }
                }
                List<MoneyApply> oneyApplymList = claimForFundsService.getMoneyApplyList(detArray);
                MoneyApply monDet = null;
                if (oneyApplymList.size() > 0) {
                    Random random = new Random();
                    int n = random.nextInt(oneyApplymList.size());
                    monDet = oneyApplymList.get(n);
                } else {
                    Write(returnPars(-1, "", "亲爱的同事,未查询到下一步处理人,请联系系统管理员"));
                    return;
                }
                String[] boss = new String[bossArray.size()];
                bossArray.toArray(boss);
                String tys = capitalCampOn(boss);
                if ("YES".equals(tys)) {
                    String IBM = "";
                    Integer number = 0;
                    List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
                    for (int i = 0; i < sone.size(); i++) {
                        IBM = (String) sone.get(i)[2];
                    }
                    MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
                    //生成新的冲正工单
                    MoneyApply myly = new MoneyApply();
                    String sateTime = Bpms_riskoff_service.getUnlockedNumber();
                    String batchNO = IBM + "CZ" + sateTime;
                    myly.setApplyNo(batchNO);
                    myly.setTitle("资金冲正-" + moneyTotal.getGroupName());
                    myly.setApplyMemo("");
                    myly.setCreatorId(user.getRowNo() + "");
                    myly.setCreatorName(user.getEmployeeName());
                    myly.setCreateDate(new Date());
                    myly.setState("1");
                    myly.setGroupCode(moneyTotal.getGroupCode());
                    myly.setGroupName(moneyTotal.getGroupName());
                    myly.setOpType("2");
                    myly.setApprovalNumber("");
                    myly.setOrderType("3");
                    myly.setSerialNo(moneyTotal.getSerialNo());
                    myly.setMa_type("3");
                    myly.setMoneyTotal_id(moneyTotal.getId());
                    myly.setBefore_ApplyNo("");
                    myly.setSubGroup_amount("");
                    myly.setLateFeeMoney("0");
                    //修改工单对应的冲正明细
                    long amount = 0;
                    for (String det : detArray) {
                        MoneyApplyDet mad = claimForFundsService.getMoneyApplyDetId(det);
                        if (mad != null) {
                            MoneyApplyDet moneyApplyDet = new MoneyApplyDet();
                            if (Integer.parseInt(mad.getOrderType()) == 2) {
                                String datatime = Bpms_riskoff_service.getUnlockedNumber();
                                String moneyNo = "JTCZ" + datatime + number.toString();
                                moneyApplyDet.setMoneyNo(moneyNo);
                                moneyApplyDet.setSerialNo(mad.getSerialNo());
                                moneyApplyDet.setOrderType("2");
                                moneyApplyDet.setContrctNo(mad.getContrctNo());
                                moneyApplyDet.setContrctType(mad.getContrctType());
                                moneyApplyDet.setProductName(mad.getProductName());
                                moneyApplyDet.setProductNmb(mad.getProductNmb());
                                moneyApplyDet.setAmount(mad.getAmount());
                                moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                                moneyApplyDet.setApplyNo(batchNO);
                                moneyApplyDet.setUseType(mad.getUseType());
                                moneyApplyDet.setBillNote(mad.getBillNote());
                                moneyApplyDet.setOpType("2");
                                moneyApplyDet.setGroupCode(mad.getGroupCode());
                                moneyApplyDet.setGroupName(mad.getGroupName());
                                moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                                moneyApplyDet.setState("5");
                                moneyApplyDet.setCreatorId(user.getRowNo() + "");
                                moneyApplyDet.setContrctName(mad.getContrctName());
                                moneyApplyDet.setParentId(mad.getId());
                                moneyApplyDet.setSerialNo(mad.getSerialNo());
                                moneyApplyDet.setBossState(mad.getBossState());
                                moneyApplyDet.setLateFeeMoney(mad.getLateFeeMoney());
                                amount = amount + Long.parseLong(mad.getAmount());
                            } else {
                                String datatime = Bpms_riskoff_service.getUnlockedNumber();
                                String moneyNo = "GRCZ" + datatime + number.toString();
                                moneyApplyDet.setMoneyNo(moneyNo);
                                moneyApplyDet.setSerialNo(mad.getSerialNo());
                                moneyApplyDet.setOrderType("1");
                                moneyApplyDet.setProductName(mad.getProductName());
                                moneyApplyDet.setProductNmb(mad.getProductNmb());
                                moneyApplyDet.setAmount(mad.getAmount());
                                moneyApplyDet.setCreateDate(getStringDateFour(getStringDatetwo(new Date())));
                                moneyApplyDet.setApplyNo(batchNO);
                                moneyApplyDet.setPhoneNo(mad.getPhoneNo());
                                moneyApplyDet.setUseType(mad.getUseType());
                                moneyApplyDet.setOpType("2");
                                moneyApplyDet.setBillNote(mad.getBillNote());
                                moneyApplyDet.setGroupCode(mad.getGroupCode());
                                moneyApplyDet.setGroupName(mad.getGroupName());
                                moneyApplyDet.setOldMoneyNo(mad.getMoneyNo());
                                moneyApplyDet.setState("5");
                                moneyApplyDet.setCreatorId(user.getRowNo() + "");
                                moneyApplyDet.setContrctName(mad.getContrctName());
                                moneyApplyDet.setParentId(mad.getId());
                                moneyApplyDet.setSerialNo(mad.getSerialNo());
                                moneyApplyDet.setLateFeeMoney(mad.getLateFeeMoney());
                                moneyApplyDet.setBossState(mad.getBossState());
                                amount = amount + Long.parseLong(mad.getAmount());
                            }
                            MoneyApplyDet moneyApplyDet1 = claimForFundsService.addMoneyApplyDet(moneyApplyDet);
                            mad.setRectificaState(mad.getState());
                            mad.setState("5");
                            mad.setOldMoneyNo(moneyApplyDet1.getMoneyNo());
                            claimForFundsService.updateMoneyApplyDet(mad);
                        }
                        ++number;
                    }
                    myly.setApplyAmount(String.valueOf(amount));
                    MoneyApply my = claimForFundsService.addMoneyApply(myly);
                    List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(monDet.getId());
                    Integer userid=null;
                    String taskName = "";
                    for (Bpms_riskoff_task task:taskList){
                        if ("区县政企部主任".equals(task.getName())
                                ||"市公司客户经理室经理".equals(task.getName())
                                ||"省重客客户经理室经理".equals(task.getName())){
                            userid =task.getOper_no();
                            taskName=task.getName();
                        }
                    }
                    //生成任务信息
                    String processId = "claimForFundsChange." + sateTime;
                    taskService.setBpms_riskoff_process(my.getId(), processId, 1, user);
                    taskService.setBpms_riskoff_task(processId, "发起工单", 2, "CZ", "客户经理", user.getRowNo(), user);//先保存自己本身的任务
                    String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "CZ", taskName, userid, user);//预存下一步任务
                    //生成审批待办
                    WaitTask waitTask = new WaitTask();
                    waitTask.setName("[资金冲正]" + my.getTitle());//待办名称
                    waitTask.setCreationTime(new Date());// 代办生成时间
                    waitTask.setUrl("jsp/claimForFunds/handleClaim.jsp?id=" + my.getId() + "&applyNo=" + my.getApplyNo() + "&taskId=" + taskid);
                    SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
                    waitTask.setState(waitTask.HANDLE);// 状态为待处理
                    waitTask.setHandleUserId(USER.getRowNo());// 处理人id
                    waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
                    waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
                    waitTask.setCreateUserId(user.getRowNo());// 创建人id
                    waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
                    waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
                    waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
                    waitTask.setTaskId(my.getId());
                    service.saveWait(waitTask, this.getRequest());
                    Write(returnPars(1, "", "您的订单已推送：" + USER.getEmployeeName() + ",请等待审批！"));
                } else {
                    Write(returnPars(-1, "", "亲爱的同事,工单预占未通过,请确认！"));
                }
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.error("资金认领冲正错误信息："+e.getMessage(),e);
            Write(returnPars(-1, "", "未知错误"+e.getMessage()+"请联系系统管理员"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 资金使用冲正退回方法
     */
    public void returnCapitalRectification(){
        try{
            String id = getString("id");
            String opinion = getString("opinion");//审批意见
            Bpms_riskoff_task btask=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
            Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            List<MoneyApplyDet> moneyApplyDet = claimForFundsService.getMoneyApplyDetByApplyNoTwo(my.getApplyNo());
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotalSerialNo(moneyApplyDet.get(0).getSerialNo());//查询工单对应的资金池
            SystemUser USER = systemUserService.getUserInfoRowNo(moneyTotal.getUserid());
            my.setState("2");
            claimForFundsService.updateMoneyApply(my);
            taskService.updateBpms_riskoff_task(opinion, 0, btask.getId());
            taskService.setBpms_riskoff_task(my.getId(),opinion,0,"CZ","客户经理",USER.getRowNo(), user);
            String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CZ", "客户经理",
                    Integer.parseInt(my.getCreatorId()), user);
            WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName("[资金冲正-退回]" + my.getTitle());//待办名称
            waitTask.setCreationTime(new Date());// 代办生成时间
            waitTask.setUrl("jsp/claimForFunds/handleClaimtwo.jsp?id="+my.getId()+"&applyNo="+my.getApplyNo()+"&taskId="+rtaskid);
            waitTask.setState(waitTask.HANDLE);// 状态为待处理
            waitTask.setHandleUserId(USER.getRowNo());// 处理人id
            waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
            waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
            waitTask.setCreateUserId(user.getRowNo());// 创建人id
            waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
            waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
            waitTask.setCode(MoneyTotal.MONEYTOTAL);//标识
            waitTask.setTaskId(my.getId());
            service.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "退回成功"));
        }catch (Exception e){
            e.printStackTrace();
            logger.error("资金认领错误信息："+e.getMessage(),e);
            Write(returnPars(-1, "", "未知错误："+e.getMessage()));
            throw new RuntimeException("事务回滚");
        }
    }

    public void comprehensive(){
        try {
            String id = getString("id");
            String unit = getString("groupcode");
            MoneyTotal moneyTotal = claimForFundsService.getMoneyTotal(id);
            List<MoneyApplyDet> amountList = claimForFundsService.getMoneyApplyDetlistReformTwo(moneyTotal.getSerialNo(),unit);//查询可冲正的额数据
            Map<String,Object> result = new HashMap<String, Object>();
            result.put("moneyTotal",moneyTotal);
            result.put("moneyApplyDetList", amountList);
            Write(returnPars(1, JSONHelper.SerializeWithNeedAnnotationDateFormat(result), "查询成功"));
        }catch (Exception e){
            logger.error(e.getMessage(),e);
            Write(returnPars(-1, "", "未知错误："+e.getMessage()));
        }
    }

    //要解除注释
    public void getLateFeeCount(){
        try {
            String contractNo = getString("contractNo");
            String phoneNo = getString("phoneNo");
            Map<String, Object> mapcfm = CMCC1000OpenService.getInstance().pressGetLoginMsgSvc(user.getBossUserName());
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithOutInnerClass(mapcfm);
            //String json = "{\"REGION_ID\":\"11\",\"POWER_RIGHT\":\"18\",\"GROUP_ID\":\"23\",\"RETURN_MSG\":\"ok!\",\"PROMPT_MSG\":\"\",\"RETURN_CODE\":\"0\",\"LOGIN_NO\":\"aa0002107\",\"USER_MSG\":\"处理成功!\",\"DETAIL_MSG\":\"OK!\"}";
            JSONObject obj = JSONObject.fromObject(json);
            Result result = ClaimFundsOpenSrv.getInstance().qryFeeInfo(phoneNo,contractNo,obj.getString("GROUP_ID"), user.getBossUserName());
            //Result result =ClaimFundsOpenSrv.getInstance().qryFeeInfo("","13011002083800","111211","aagh38");
            logger.info("资金认领查询滞纳金返回数据"+result.toString());
            String money ="0";
            if (ResultCode.SUCCESS.code() == result.getCode()) {
                JSONObject data=JSONObject.fromObject(result.getData());
                JSONObject root = JSONObject.fromObject(data.get("ROOT"));
                JSONObject outData = JSONObject.fromObject(root.get("OUT_DATA"));
                Object outMsg = outData.get("OUTMSG");
                if(outMsg instanceof JSONArray){//判断OUTMSG节点是不是数组
                    JSONArray array =JSONArray.fromObject(outData.get("OUTMSG"));
                    for(int i=0;i<array.size();i++){
                        JSONObject fobj=JSONObject.fromObject(array.get(i));
                        if(fobj!= null && !fobj.isEmpty() && !fobj.isNullObject() && !"null".equals(fobj) && !"{}".equals(fobj) && fobj.size()>0){
                            JSONArray owefeeinfo=JSONArray.fromObject(fobj.get("OWEFEEINFO"));
                            for (int j=0;j<owefeeinfo.size();j++){
                                JSONObject owefeeinfoObj = JSONObject.fromObject(owefeeinfo.get(j));
                                money=(Long.parseLong(money)+owefeeinfoObj.getInt("DELAY_FEE"))+"";
                            }
                        }
                    }
                }else if(outMsg instanceof JSONObject){//判断OUTMSG节点是不是对象
                    JSONObject object=JSONObject.fromObject(outData.get("OUTMSG"));
                    if(object!= null && !object.isEmpty() && !object.isNullObject() && !"null".equals(object) && !"{}".equals(object) && object.size()>0) {
                        JSONArray owefeeinfo = JSONArray.fromObject(object.get("OWEFEEINFO"));
                        for (int j = 0; j < owefeeinfo.size(); j++) {
                            JSONObject owefeeinfoObj = JSONObject.fromObject(owefeeinfo.get(j));
                            money=(Long.parseLong(money) + owefeeinfoObj.getInt("DELAY_FEE"))+"";
                        }
                    }
                }
            }
            Write(money);
        }catch (Exception e){
            logger.error("资金认领查询滞纳金金额错误信息："+e.getMessage(),e);
            Write("0");
        }
    }

    public void innitLateFeeMoneyData(){
        try {
            lateFeeMoneyDataService.InitializeTransferCitiesData();
            //lateFeeMoneyDataService.InitializeTransferFourCitiesData();
            lateFeeMoneyDataService.InitializeLateFeeMoneyData();
            //lateFeeMoneyDataService.InitiaTransferLizeLateFeeMoneyData();
            Write("初始化成功");
        }catch (Exception e){
            logger.error("资金认领初始化金额和滞纳金金额错误："+e.getMessage(),e);
            Write("初始化失败");
        }

    }

    public void getTransferCitiesData(){
        try{
            String dangqianrenwu = getString("dangqianrenwu");
            String id= getString("id");
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            String code =claimForFundsService.getCountyByUserID(my.getCreatorId()).get(0).get("COMPANY_CODE").toString();
            TransferCitiesData transferCitiesData= claimForFundsService.getTransferCitiesData(code,dangqianrenwu);
            Write(JSONHelper.SerializeWithNeedAnnotation(transferCitiesData));
        }catch(Exception e){
            e.printStackTrace();
            Write("ON");
        }
    }

    public void getLateFeeMoneyData(){
        try{
            String dangqianrenwu = getString("dangqianrenwu");
            String id= getString("id");
            MoneyApply my = claimForFundsService.getMoneyApply(id);//查询认领信息
            String code =claimForFundsService.getCountyByUserID(my.getCreatorId()).get(0).get("COMPANY_CODE").toString();
            LateFeeMoneyData lateFeeMoneyData= claimForFundsService.getLateFeeMoneyData(code,dangqianrenwu);
            Write(JSONHelper.SerializeWithNeedAnnotation(lateFeeMoneyData));
        }catch(Exception e){
            e.printStackTrace();
            Write("ON");
        }
    }
}
