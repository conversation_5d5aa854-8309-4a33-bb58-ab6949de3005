package com.xinxinsoft.action.claimForFunds;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.GroupAccount.GroupHipAccount;
import com.xinxinsoft.entity.GroupAccount.GroupRelations;
import com.xinxinsoft.entity.claimForFundModel.ClaimForFundModel;
import com.xinxinsoft.entity.claimForFundModel.ClaimForFundModelDet;
import com.xinxinsoft.entity.claimForFundModel.ClaimReplaceModel;
import com.xinxinsoft.entity.claimForFundModel.ClaimReplaceModelDet;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.GroupCustomerService;
import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.claimForFunds.claimForFundModelService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.groupAccountService.GroupAccountService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * @ClassName: claimForFundModelAction
 * @Description: TODO 资金模型管理类
 * @Author: 33228
 * @Date: 2022/4/7 18:46
 * @Version: 1.0
 */
public class claimForFundModelAction extends BaseAction {

    private static final Logger logger = LoggerFactory.getLogger(claimForFundModelAction.class);

    @Resource(name="ClaimForFundsService")
    private ClaimForFundsService claimForFundsService;
    @Resource(name = "GroupAccountService")
    private GroupAccountService groupAccountService;
    @Resource(name="SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name="Bpms_riskoff_service")
    private Bpms_riskoff_service taskService;
    @Resource(name="claimForFundModelService")
    private claimForFundModelService modelService;
    @Resource(name="WaitTaskService")
    private WaitTaskService service;
    @Resource(name="TransferJBPMUtils")
    private TransferJBPMUtils transferJBPMUtils;
    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;
    private File file;
    public File getFile() {
        return file;
    }
    public void setFile(File file) {
        this.file = file;
    }

    /**
     * @Description TODO 新增缴费计划模型
     * <AUTHOR>
     * @Date 2022/4/8 10:38
     **/
    public void addClaimFoundModel(){
        try {
            String unitId = getString("unitId");//标题
            String unitName = getString("unitName");//申请说明
            String Title = getString("Title");//申请说明
            String useMoney = getString("useMoney");
            String Time = getString("Time");
            String explain = getString("explain");
            String json = getString("json");

            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            Date stateDate = formatter.parse(Time.substring(0,10));
            Date endDate = formatter.parse(Time.substring(13));
            Date newDate =  new Date();
            String IBM = "";
            List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = taskService.getNumber();
            String orderNO = IBM + "" + sateTime;
            ClaimForFundModel model = new ClaimForFundModel();
            model.setApplyNo(orderNO);
            model.setApplyTitle(Title);
            model.setApplyAmount(useMoney);
            model.setStartTime(stateDate);
            model.setEndTime(endDate);
            model.setApplyGruopNo(unitId);
            model.setApplyGroupName(unitName);
            model.setApplyFounder(String.valueOf(user.getRowNo()));
            model.setApplyCreationTime(newDate);
            model.setApplyUpdateTime(newDate);
            model.setApplyMome(explain);
            if (stateDate.getTime()<=newDate.getTime() && endDate.getTime()>newDate.getTime()){
                model.setApplyState("1");
            }else if (stateDate.getTime() > newDate.getTime()){
                model.setApplyState("0");
            }else{
                model.setApplyState("2");
            }
            model = modelService.saveClaimForFundModel(model);
            if (model!=null){
                JSONArray detJson = JSONArray.fromObject(json);
                if (detJson.size()>0){
                    for (int i =0;i<detJson.size();i++){
                        String str = detJson.getString(i);
                        JSONObject data = JSONObject.fromObject(str);
                        ClaimForFundModelDet det = new ClaimForFundModelDet();
                        det.setApplyNo(model.getApplyNo());
                        det.setApplyGruopNo(unitId);
                        det.setApplyGroupName(unitName);
                        det.setContrctNo(data.getString("CONTRCTNO").replaceAll(" ", ""));
                        det.setContrctType(data.getString("CONTRCTTYPE"));
                        det.setUseType(data.getString("USETYPE"));
                        det.setAmount(data.getString("AMOUNT"));
                        det.setCreationTime(newDate);
                        det.setUpdateTime(newDate);
                        det.setState("1");
                        modelService.saveClaimForFundModelDet(det);
                    }
                }
                Write(returnPars(1,"","亲爱的同事，缴费计划创建成功！"));
            }else {
                Write(returnPars(-1,"","亲爱的同事，缴费计划创建失败，请联系管理员处理！"));
            }
        }catch (Exception e){
            logger.info("缴费计划创建异常："+e);
            Write(returnPars(-1,"","亲爱的同事，缴费计划创建异常，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 集团代理申请功能
     * <AUTHOR>
     * @Date 2022/5/7 15:57
     **/
    public void addClaimReplaceModel(){
        try {
            String Title = getString("Title");
            String explain = getString("explain");
            String jsonOne = getString("json");

            String userID = getString("userID");
            String role = getString("role");

            String attachmentId = getString("attachmentId");//附件id

            SystemUser systemUser = systemUserService.getUserInfoRowNo(Integer.parseInt(userID));
            if (systemUser==null){
                Write(returnPars(-1, "", "亲爱的同事,您选择的审批人信息异常,请重新选择或联系管理员处理!"));
                return;
            }

            String IBM = "";
            List<Object[]> sone = claimForFundsService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = taskService.getNumber();
            String orderNO = IBM + "" + sateTime;
            ClaimReplaceModel replaceModel = new ClaimReplaceModel();
            replaceModel.setReplcaceNo(orderNO);
            replaceModel.setReplaceTital(Title);
            replaceModel.setCreationRow(user.getRowNo());
            replaceModel.setCreationName(user.getEmployeeName());
            replaceModel.setCreationDate(new Date());
            replaceModel.setUpdateTime(new Date());
            replaceModel.setReplcaceMome(explain);
            replaceModel.setReplcaceState(0);
            replaceModel = modelService.saveClaimReplaceModel(replaceModel);

            JSONArray jsonArray = JSONArray.fromObject(jsonOne);
            for (int i = 0; i < jsonArray.size(); i++) {
                String s = jsonArray.getString(i);
                JSONObject data2 = JSONObject.fromObject(s);
                ClaimReplaceModelDet replaceModelDet = new ClaimReplaceModelDet();
                replaceModelDet.setReplcaceNo(replaceModel.getReplcaceNo());
                replaceModelDet.setGroupCode(data2.getString("groupCode"));
                replaceModelDet.setGroupName(data2.getString("groupName"));
                replaceModelDet.setLblLevel(data2.getString("lblLevel"));
                replaceModelDet.setContactAddress(data2.getString("contactAddress"));
                List<Map<String, String>> userList = modelService.getVwUserinfoByBossName(data2.getString("bossUserName"));
                if (userList.size()!=1){
                    continue;
                }else {
                    replaceModelDet.setAscriptionRow(String.valueOf(userList.get(0).get("ROWNO")));
                    replaceModelDet.setAscriptionName(userList.get(0).get("EMPLOYEE_NAME"));
                    replaceModelDet.setAscriptionBossName(userList.get(0).get("BOSSUSERNAME"));
                    replaceModelDet.setAscriptionPhone(userList.get(0).get("MOBILE"));
                }
                replaceModelDet.setCreationRow(user.getRowNo());
                replaceModelDet.setCreationName(user.getEmployeeName());
                replaceModelDet.setOrderState("0");
                modelService.saveClaimReplaceModelDet(replaceModelDet);
            }

            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(replaceModel.getId());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(ClaimReplaceModel.CLAIMREPLACEMODEL);
                            claimForFundsService.saveSandA(sa);
                        }
                    }
                }
            }

            String node="";
            if("SZK".equals(role)){
                node="ROLE_SZKYWGLY";
            }else if("SGS".equals(role)){
                node="ROLE_DSBM";
            }else{
                node="ROLE_QXYW";
            }
            Map<String, String> map = new HashMap<>();
            map.put("node", node);
            String processId = transferJBPMUtils.startTransfer("claimForFundsReplace", map);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            taskService.setBpms_riskoff_process(replaceModel.getId(), processId, 1, user);
            taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "起草人", user.getRowNo(), user);//先保存自己本身的任务
            String taskid = taskService.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), systemUser.getRowNo(), user);//预存下一步任务
            //生成审批待办
            setReplaceModelWaitTask("[集团代理]",replaceModel,systemUser,taskid);
            Write(returnPars(1, "", "您的订单已推送：" + systemUser.getEmployeeName() + ",请等待处理！"));

        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("集团代理申请功能异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，集团代理申请功能异常:【"+e.getMessage()+"】，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 集团代理申请提交功能
     * <AUTHOR>
     * @Date 2022/5/7 15:57
     **/
    public void replaceModelHandleClaimData() {
        try {
            Integer userid = getInteger("userId");//下一步处理用户id
            String id = getString("id");//资金认领id
            String opinion = getString("opinion");//审批意见
            String taskId = getString("taskId");//任务id
            String waitId = getString("waitId");//任务id
            ClaimReplaceModel replaceModel = modelService.queryReplaceById(id);
            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_task(taskId);
            if (riskoff_task==null || !riskoff_task.getOper_name().equals(user.getEmployeeName()) || !riskoff_task.getStatus().equals(1)){
                Write(returnPars(-1,"","亲爱的同事,当前任务已被处理,请不要重复提交!"));
            }
            SystemUser systemUser = systemUserService.getUserInfoRowNo(userid);
            if (systemUser==null){
                Write(returnPars(-1, "", "亲爱的同事,您选择的审批人信息异常,请重新选择或联系管理员处理!"));
                return;
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);;//根据待办id查询待办信息
            if (wt == null) {
                wt = taskService.queryWaitByTaskId(id,user.getRowNo());
                if (wt == null) {
                    Write(returnPars(-1,"","亲爱的同事,待办信息异常,请联系管理员处理!"));
                    throw new RuntimeException("未查询到待办信息");
                }
            }
            Bpms_riskoff_process process = taskService.getbpmsRiskoffProcess(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            String taskName = task.getActivityName();
            String role;
            switch (taskName){
                case "区县业务管理员":
                    role="SJFJL_CITY";
                    break;
                case "区县政企部主任":
                    role="ROLE_QXSM";
                    break;
                case "市公司业务管理员":
                    role="ROLE_DSDM";
                    break;
                case "省重客业务管理员":
                    role="ROLE_SZKYWGLSJL";
                    break;
                default:
                    logger.info("集团代理流程任务异常：" + taskName);
                    throw new Exception("任务异常,给事务回滚");
            }
            jbpmUtil.completeTask(task.getId(),role);
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();//获取流程任务表信息
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", tasktwo.getActivityName(), userid, user);
            setReplaceModelWaitTask("[集团代理]",replaceModel,systemUser,rtaskid);
            service.updateWait(wt, this.getRequest());
            Write(returnPars(1, "", "亲爱的同事,处理完成已提交至：" + systemUser.getEmployeeName() + "处！"));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.error("集团代理申请提交功能异常：" + e.getMessage(), e);
            Write(returnPars(-1,"","亲爱的同事,集团代理申请提交功能异常:"+e.getMessage()+",请联系管理员处理!"));
        }
    }

    /**
     * @Description TODO 集团代理申请完成功能
     * <AUTHOR>
     * @Date 2022/5/7 15:56
     **/
    public void replaceModelComplateClaimData() {
        try {
            String id = getString("id");//资金认领id
            String opinion = getString("opinion");//审批意见
            String taskId = getString("taskId");//任务id
            String waitId = getString("waitId");//任务id
            ClaimReplaceModel replaceModel = modelService.queryReplaceById(id);
            List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDets(replaceModel.getReplcaceNo());
            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_task(taskId);
            if (riskoff_task==null || !riskoff_task.getOper_name().equals(user.getEmployeeName()) || !riskoff_task.getStatus().equals(1)){
                Write(returnPars(-1,"","亲爱的同事,当前任务已被处理,请不要重复提交!"));
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);;//根据待办id查询待办信息
            if (wt == null) {
                wt = taskService.queryWaitByTaskId(id,user.getRowNo());
                if (wt == null) {
                    Write(returnPars(-1,"","亲爱的同事,待办信息异常,请联系管理员处理!"));
                    throw new RuntimeException("未查询到待办信息");
                }
            }
            Bpms_riskoff_process process = taskService.getbpmsRiskoffProcess(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            String taskName = task.getActivityName();
            if (taskName.equals("区县分管经理") || taskName.equals("市公司政企部经理") || taskName.equals("省重客业务管理室经理")){
                jbpmUtil.completeTask(task.getId(),"END");
                for (int a = 0; a<replaceModelDets.size();a++){
                    ClaimReplaceModelDet det = replaceModelDets.get(a);
                    det.setOrderState("1");
                    modelService.updateClaimReplaceModelDet(det);
                }
                replaceModel.setReplcaceState(1);
                modelService.updateClaimReplaceModel(replaceModel);
            }else {
                Write(returnPars(-1,"","亲爱的同事,流程异常当前任务为【"+taskName+"】,请联系管理员处理!"));
                throw new RuntimeException("流程异常当前任务为【"+taskName+"】 ");
            }
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            service.updateWait(wt, this.getRequest());
            Write(returnPars(1, "", "亲爱的同事,处理成功工单已完成！"));
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.error("集团代理申请完成功能异常：" + e.getMessage(), e);
            Write(returnPars(-1,"","亲爱的同事,集团代理申请完成功能异常:"+e.getMessage()+",请联系管理员处理!"));
        }
    }

    /**
     * @Description TODO 集团代理申请驳回
     * <AUTHOR>
     * @Date 2022/5/7 15:55
     **/
    public void returnReplaceModelation(){
        try{
            String id = getString("id");
            String opinion = getString("opinion");//审批意见
            String taskId = getString("taskId");//任务id
            String waitId = getString("waitId");//任务id
            ClaimReplaceModel replaceModel = modelService.queryReplaceById(id);
            List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDets(replaceModel.getReplcaceNo());
            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_task(taskId);
            SystemUser systemUser = systemUserService.getByUserInfoRowNo(replaceModel.getCreationRow());
            if (riskoff_task==null || !riskoff_task.getOper_name().equals(user.getEmployeeName()) || !riskoff_task.getStatus().equals(1)){
                Write(returnPars(-1,"","亲爱的同事,当前任务已被处理,请不要重复提交!"));
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);;//根据待办id查询待办信息
            if (wt == null) {
                wt = taskService.queryWaitByTaskId(id,user.getRowNo());
                if (wt == null) {
                    Write(returnPars(-1,"","亲爱的同事,待办信息异常,请联系管理员处理!"));
                    throw new RuntimeException("未查询到待办信息");
                }
            }
            Bpms_riskoff_process process = taskService.getbpmsRiskoffProcess(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            if (task!=null){
                jbpmUtil.deleteProcessInstance(process.getProcess_sign());// 删除流程
            }
            for (int a = 0; a<replaceModelDets.size();a++){
                ClaimReplaceModelDet det = replaceModelDets.get(a);
                det.setOrderState("-1");
                modelService.updateClaimReplaceModelDet(det);
            }
            replaceModel.setReplcaceState(2);
            modelService.updateClaimReplaceModel(replaceModel);
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            String rtaskid = taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", "起草人", replaceModel.getCreationRow(), user);
            setReplaceModelWaitTask("[集团代理-驳回]",replaceModel,systemUser,rtaskid);
            service.updateWait(wt, this.getRequest());
            Write(returnPars(1, "", "亲爱的同事,工单已驳回到："+replaceModel.getCreationName()+"处！"));
        }catch (Exception e){
            e.printStackTrace();
            logger.error("集团代理申请驳回异常："+e.getMessage(),e);
            Write(returnPars(-1, "", "亲爱的同事，集团代理申请驳回异常：【"+e.getMessage()+"】，请联系管理员处理"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * @Description TODO 集团代理申请作废功能
     * <AUTHOR>
     * @Date 2022/5/7 15:54
     **/
    public void invalidReplaceModelation(){
        try{
            String id = getString("id");
            String taskId = getString("taskId");//任务id
            String waitId = getString("waitId");//任务id
            ClaimReplaceModel replaceModel = modelService.queryReplaceById(id);
            Bpms_riskoff_task riskoff_task = taskService.getBpms_riskoff_task(taskId);
            if (riskoff_task==null || !riskoff_task.getOper_name().equals(user.getEmployeeName()) || !riskoff_task.getStatus().equals(1)){
                Write(returnPars(-1,"","亲爱的同事,当前任务已被处理,请不要重复提交!"));
            }
            WaitTask wt = service.queryWaitByTaskId(waitId);;//根据待办id查询待办信息
            if (wt == null) {
                wt = taskService.queryWaitByTaskId(id,user.getRowNo());
                if (wt == null) {
                    Write(returnPars(-1,"","亲爱的同事,待办信息异常,请联系管理员处理!"));
                    throw new RuntimeException("未查询到待办信息");
                }
            }
            replaceModel.setReplcaceState(3);
            modelService.updateClaimReplaceModel(replaceModel);
            taskService.updateBpms_riskoff_task("工单作废", 2, taskId);
            service.updateWait(wt, this.getRequest());
            Write(returnPars(1, "", "亲爱的同事,工单已作废！"));
        }catch (Exception e){
            e.printStackTrace();
            logger.error("集团代理申请作废功能异常："+e.getMessage(),e);
            Write(returnPars(-1, "", "亲爱的同事，集团代理申请作废功能异常：【"+e.getMessage()+"】，请联系管理员处理"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * @Description TODO 取消集团代理关系
     * <AUTHOR>
     * @Date 2022/5/12 16:27
     **/
    public void invenClaimReplace(){
        try {
            String id = getString("id");
            ClaimReplaceModel replaceModel = modelService.queryReplaceById(id);
            List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDets(replaceModel.getReplcaceNo());
            for (int a = 0; a<replaceModelDets.size();a++){
                ClaimReplaceModelDet det = replaceModelDets.get(a);
                det.setOrderState("-1");
                modelService.updateClaimReplaceModelDet(det);
            }
            replaceModel.setReplcaceState(3);
            modelService.updateClaimReplaceModel(replaceModel);
            Write(returnPars(1, "", "亲爱的同事,工单已作废！"));
        }catch (Exception e){
            e.printStackTrace();
            logger.error("集团代理申请作废功能异常："+e.getMessage(),e);
            Write(returnPars(-1, "", "亲爱的同事，集团代理申请作废功能异常：【"+e.getMessage()+"】，请联系管理员处理"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * @Description TODO 生成待办
     * <AUTHOR>
     * @param replaceModel 工单对象
     * @param systemUser 下一步处理人对象
     * @Date 2022/5/6 17:20
     **/
    public Integer setReplaceModelWaitTask(String WaitTatle,ClaimReplaceModel replaceModel,SystemUser systemUser,String taskid){
        WaitTask waitTask = new WaitTask();
        waitTask.setName(WaitTatle + replaceModel.getReplaceTital());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/claimForFundReplace/handleClaimReplace.jsp?id=" + replaceModel.getId() + "&taskId=" + taskid);
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(systemUser.getRowNo());// 处理人id
        waitTask.setHandleUserName(systemUser.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(systemUser.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(ClaimReplaceModel.CLAIMREPLACEMODEL);//标识
        waitTask.setTaskId(replaceModel.getId());
        return service.saveWait(waitTask, this.getRequest());
    }

    /**
     * @Description TODO 根据集团编号和名称查询集团详细信息
     * <AUTHOR>
     * @Date 2022/5/4 14:40
     **/
    public void queryGroupUserById() {
        Map<String, Object> map = new HashMap<>();
        JSONObject obj = new JSONObject();
        boolean bl = true;
        String message = "";
        try {
            String groupCoding = getString("groupCode");
            GroupCustomer customer = GroupCustomerService.getInstance().getCustInfoQuery(groupCoding, "");

            if (customer != null) {
                String userCompanyCode = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
                if (customer.getUser_name() == null || "".equals(customer.getUser_name())) {
                    map.put("user_id", "");
                    map.put("user_name", "");
                    bl = false;
                    message = "亲爱的同事，你选择的集团未查询到客户经理工号。";
                } else {
                    SystemUser groupUser = systemUserService.queryByBoss(customer.getUser_name());
                    List<SystemDept> sdList = systemUserService.getDeptListByRowNo(groupUser.getRowNo());
                    String groupCompanyCode = sdList.get(0).getSystemCompany().getCompanyCode();
                    if (userCompanyCode.equals(groupCompanyCode)) {
                        map.put("user_id", String.valueOf(groupUser.getRowNo()));
                        map.put("user_name", groupUser.getEmployeeName());
                        bl = true;
                        message = "ok!";
                    } else {
                        map.put("user_id", "");
                        map.put("user_name", "");
                        bl = false;
                        message = "亲爱的同事，你选择的集团归属客户经理不属于你所在地市。";
                    }
                }
                obj.put("user_type", bl);
                obj.put("user_message", message);
                obj.put("user_obj", map);
                Write(JSONHelper.SerializeWithNeedAnnotation(obj));
            } else {
                map.put("user_id", "");
                map.put("user_name", "");
                obj.put("user_type", false);
                obj.put("user_message", "未查询到集团信息");
                obj.put("user_obj", map);
                Write(JSONHelper.SerializeWithNeedAnnotation(obj));
            }
        } catch (Exception e) {
            e.printStackTrace();
            map.put("user_id", "");
            map.put("user_name", "");
            obj.put("user_type", false);
            obj.put("user_message", "查询出错了");
            obj.put("user_obj", map);
            Write(JSONHelper.SerializeWithNeedAnnotation(obj));
        }
    }

    /**
     * @Description TODO 根据编号查询代理工单信息
     * <AUTHOR>
     * @Date 2022/4/28 14:24
     **/
    public void queryReplaceList(){
        try{
            String id = getString("id");
            String taskId = getString("taskId");
            Bpms_riskoff_process process = taskService.getbpmsRiskoffProcess(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            ClaimReplaceModel replaceModel = modelService.queryReplaceById(id);
            if (replaceModel==null){
                Write(returnPars(-1,"","亲爱的同事，数据查询失败工单编号【"+id+"】异常，请联系管理员处理！"));
                return;
            }
            //任务不能为空,如果任务为空了那么流程审批结束
            String taskName = "";
            String role = "";
            List<String> buttons = new ArrayList<>();
            if(task!=null && !taskId.equals("")){
                Set<String>  nextLine = jbpmUtil.findOutComesByTaskId(task.getId());
                taskName = task.getActivityName();
                switch (taskName){
                    case "区县业务管理员":
                        buttons.add("SUBMIT");		//提交
                        role="ROLE_QXDM";
                        break;
                    case "区县政企部主任":
                        buttons.add("SUBMIT");		//提交
                        role="ROLE_QXSM";
                        break;
                    case "市公司业务管理员":
                        buttons.add("SUBMIT");		//提交
                        role="ROLE_DSDM";
                        break;
                    case "省重客业务管理员":
                        buttons.add("SUBMIT");		//提交
                        role="ROLE_SZKYWGLSJL";
                        break;
                    default:
                        buttons.add("THROUGH");		//通过
                        break;
                }
                buttons.add("RETURN");		//驳回
                String btu="";
                for(int j=0;j<buttons.size();j++){
                    if("THROUGH".equals(buttons.get(j))){
                        btu=buttons.get(j);
                    }
                }
                if(!"THROUGH".equals(btu)){
                    if(role == null||role == ""){
                        role = nextLine.toArray()[0].toString();
                        buttons.add("SUBMIT");
                    }
                }
            }else {
                if (replaceModel.getReplcaceState().equals(2) && !taskId.equals("")){
                    buttons.add("ZUOFEI");
                }
            }
            List<ClaimReplaceModelDet> replaceModelDets = modelService.queryReplaceModelDets(replaceModel.getReplcaceNo());
            Map<String,Object> map = new HashMap<String,Object>();
            map.put("replaceModel",replaceModel);
            map.put("replaceModelDets",replaceModelDets);
            map.put("role",role);
            map.put("buttons",buttons);
            map.put("taskName",taskName);
            Write(returnPars(1,map,"查询完成！"));
        }catch (Exception e){
            logger.error("根据编号查询代理工单信息异常："+e.getMessage(),e);
            e.printStackTrace();
            Write(returnPars(-1,"","亲爱的同事，根据编号查询代理工单信息异常，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 资金模型列表查询
     * <AUTHOR>
     * @Date 2022/4/8 11:21
     **/
    public void queryMoneyModelList(){
        try{
            String applyGruopNo = getString("applyGruopNo");               //集团编号
            String applyGroupName = getString("applyGroupName");            //集团名称
            String applyNo = getString("applyNo");                          //计划编号
            String applyState = getString("applyState");                    //计划状态

            String stateCreatorDate = getString("stateCreatorDate");        //开始时间
            String endCreatorDate = getString("endCreatorDate");            //结束时间

            Integer pageNo = getInteger("pageNo");// 当前页码数
            Integer pagesize = getInteger("pageSize");// 每页显示件数
            LayuiPage page = new LayuiPage(pageNo, pagesize);
            page = modelService.queryMoneyModelList(page,applyGruopNo,applyGroupName,applyNo,applyState,stateCreatorDate,endCreatorDate,user);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        }catch(Exception e){
            logger.info("资金缴费计划列表查询异常："+e,e);
            Write("资金缴费计划列表查询异常");
        }
    }

    /**
     * @Description TODO 资金缴费计划作废
     * <AUTHOR>
     * @Date 2022/6/7 11:02
     **/
    public void giveUpMoneyModel(){
        try{
            String modelId = getString("modelId");
            ClaimForFundModel claimForFundModel = modelService.getClaimForFundModelById(modelId);
            claimForFundModel.setApplyState("-1");
            claimForFundModel = modelService.updateClaimForFundModel(claimForFundModel);
            Write(returnPars(1,claimForFundModel,"亲爱的同事，已对缴费计划：【"+claimForFundModel.getApplyTitle()+"】进行作废，请确认！"));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.info("资金缴费计划作废异常："+e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，工单作废失败【"+e.getMessage()+"】，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 代认领模型列表查询
     * <AUTHOR>
     * @Date 2022/4/8 11:21
     **/
    public void queryReplaceModelList(){
        try{
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            String replcaceNo = getString("replcaceNo");        //工单编号
            String replaceTital = getString("replaceTital");        //工单名称
            String replcaceState = getString("replcaceState");    //工单状态
            String stateCreatorDate = getString("stateCreatorDate");    //工单创建时间   开始时间
            String endCreatorDate = getString("endCreatorDate");        //工单创建时间   结束时间

            String tableType = getString("tableType");        //列表状态
            page = modelService.findAllByPage(page,replcaceNo,replaceTital,replcaceState,stateCreatorDate,endCreatorDate,tableType,user);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        }catch(Exception e){
            logger.info("资金缴费计划列表查询异常："+e,e);
            Write("资金缴费计划列表查询异常");
        }
    }

    /**
     * @Description TODO 根据集团280编号查询280下可用的缴费计划
     * <AUTHOR>
     * @Date 2022/4/9 10:32
     **/
    public void queryClaimForFundModel(){
        try {
            String groupcode = getString("groupcode");
            if (groupcode==null || groupcode.equals("")){
                Write(returnPars(-1,"","亲爱的同事，获取计划信息失败[集团编号异常："+groupcode+"],请联系管理员处理！"));
                return;
            }
            List<ClaimForFundModel> models = modelService.getClaimForFundModelByGroupCode(groupcode);
            if (models.size()>0){
                Write(returnPars(1,models,"数据查询成功！"));
            }else {
                Write(returnPars(-1,"","亲爱的同事，当前集团编号【"+groupcode+"】暂无可使用的缴费模型,请确认！"));
            }
        }catch (Exception e){
            logger.info("根据280查询缴费计划异常："+e,e);
            Write(returnPars(-1,"","亲爱的同事，资金缴费计划查询异常,请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 根据计划编号查询计划详细信息
     * <AUTHOR>
     * @Date 2022/4/8 12:46
     **/
    public void queryClaimFoundModel(){
        try{
            String orderId = getString("id");                               //计划编号
            String otherAccountNumber = getString("otherAccountNumber");    //对方打款账户
            if (orderId==null || "".equals(orderId)){
                Write(returnPars(-1,"","亲爱的同事，资金缴费计划数据查询失败，获取计划编号信息失败,请联系管理员处理！"));
                return;
            }
            ClaimForFundModel model = modelService.getClaimForFundModelById(orderId);
            if (model==null){
                Write(returnPars(-1,"","亲爱的同事，计划编号异常，获取计划信息失败,请联系管理员处理！"));
                return;
            }
            GroupRelations groupRelations = groupAccountService.queryGroupRelations(model.getApplyGruopNo(),otherAccountNumber);
            List<ClaimForFundModelDet> dets = modelService.getClaimForFundModelDetbyApplyNo(model.getApplyNo());
            List<ClaimForFundModelDet> detList = new ArrayList<>();
            List<GroupHipAccount> groupHipAccount = new ArrayList<>();
            Double amout = 0.00;
            for (ClaimForFundModelDet det:dets){
                if ("1".equals(det.getContrctType())){
                    groupHipAccount = groupAccountService.queryGroupHipAccount(groupRelations.getId(),"",det.getContrctNo());
                }else {
                    groupHipAccount = groupAccountService.queryGroupHipAccount(groupRelations.getId(),det.getContrctNo(),"");
                }

                if (groupHipAccount.size()>0) {
                    amout +=Double.parseDouble(det.getAmount());
                    detList.add(det);
                }
            }
            model.setApplyAmount(amout.toString());
            Map<String,Object> map = new HashMap<String,Object>();
            map.put("ClaimForFundModel",model);
            map.put("ClaimForFundModelDet",detList);
            Write(returnPars(1,map,"数据查询成功！"));
        }catch (Exception e){
            logger.info("资金缴费计划数据查询异常："+e,e);
            Write(returnPars(-1,"","亲爱的同事，资金缴费计划数据查询异常,请联系管理员处理！"));
        }
    }

    public void queryClaimFoundModelTwo(){
        try{
            String orderId = getString("id");       //计划编号
            if (orderId==null || orderId.equals("")){
                Write(returnPars(-1,"","亲爱的同事，资金缴费计划数据查询失败，获取计划编号信息失败,请联系管理员处理！"));
                return;
            }
            ClaimForFundModel model = modelService.getClaimForFundModelById(orderId);
            if (model==null){
                Write(returnPars(-1,"","亲爱的同事，计划编号异常，获取计划信息失败,请联系管理员处理！"));
                return;
            }
            List<ClaimForFundModelDet> dets = modelService.getClaimForFundModelDetbyApplyNo(model.getApplyNo());
            Map<String,Object> map = new HashMap<String,Object>();
            map.put("ClaimForFundModel",model);
            map.put("ClaimForFundModelDet",dets);
            Write(returnPars(1,map,"数据查询成功！"));
        }catch (Exception e){
            logger.info("资金缴费计划数据查询异常："+e,e);
            Write(returnPars(-1,"","亲爱的同事，资金缴费计划数据查询异常,请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 资金模型模板下载
     * <AUTHOR>
     * @Date 2022/4/26 14:07
     **/
    public void downLoadClaimFoundModel(){
        try{
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest req = ServletActionContext.getRequest();
            String name = "账户信息模板"+formatter.format(new Date());
            String filepath = req.getRealPath("/template/ClaimFoundModel.xlsx");
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name+".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\""+fileName+"\"");
            response.addHeader("Content-Length", ""+data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
        }catch(Exception e){
            e.printStackTrace();
            logger.error("转账模板下载错误信息："+e.getMessage(),e);
        }
    }

    /**
     * 导入文件数据
     */
    public void importTemplateFile() {
        try {
            String groupCode = getString("groupCode");
            String bossUser = getString("bossUser");
            if (groupCode.isEmpty()){
                this.Write(returnPars(-1,"","亲爱的同事，未获取到集团信息，请选择集团后再进行导入！"));
            }else {
                Set<String> contractSet = new HashSet<>();
                try {
                    Result result= ClaimFundsOpenSrv.getInstance().getUnitInfo(bossUser,groupCode);
                    logger.info("请求getUnitInfo接口结果===>"+result.toString());
                    if(ResultCode.SUCCESS.code()==result.getCode()){
                        JSONObject resObj=JSONObject.fromObject(result.getData());
                        JSONObject rootObj = resObj.getJSONObject("ROOT");
                        if ("0".equals(rootObj.getString("RETURN_CODE"))){
                            JSONArray accountAry = rootObj.getJSONObject("OUT_DATA").getJSONObject("INFO").getJSONArray("CONTRACT_LIST");
                            for (int j = 0; j < accountAry.size(); j++) {
                                JSONObject obj = accountAry.getJSONObject(j);
                                contractSet.add(obj.getString("CONTRACT_NO"));
                            }
                        }else {
                            this.Write(returnPars(-1,"","亲爱的同事，查询集团账户信息异常【"+rootObj.getString("RETURN_MSG")+"】，请稍后重试或联系管理员处理！"));
                            return;
                        }
                    }else{
                        this.Write(returnPars(-1,"","亲爱的同事，查询集团账户信息失败，请稍后重试或联系管理员处理！"));
                        return;
                    }
                }catch (Exception e){
                    logger.info("模板导入集团数据查询异常：："+e.getMessage(),e);
                    e.printStackTrace();
                    this.Write(returnPars(-1,"","亲爱的同事，查询集团账户信息异常【"+e.getMessage()+"】，请稍后重试或联系管理员处理！"));
                    return;
                }
                ExcelUtil excelReader = new ExcelUtil(this.file);
                InputStream is = new FileInputStream(this.file);
                Workbook wb = new XSSFWorkbook(is);
                Sheet sheet = wb.getSheetAt(0);
                int column = sheet.getRow(0).getPhysicalNumberOfCells();
                Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
                List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
                int i;
                HashMap maps = null;
                if (column == 4) {
                    for (i = 1; i <= map.size(); ++i) {
                        maps = new HashMap();
                        String contrctType = String.valueOf(((Map) map.get(i)).get(0));
                        String contrctNo = String.valueOf(((Map) map.get(i)).get(1));
                        if ("个人账户".equals(contrctType)){
                            boolean flag = false;
                            Result result = ClaimFundsOpenSrv.getInstance().pQryUserBaseMsg(contrctNo);
                            if (result.getCode()==ResultCode.SUCCESS.code()){
                                JSONObject json = JSONObject.fromObject(result.getData());
                                JSONObject root = JSONObject.fromObject(json.get("ROOT"));
                                if ("0".equals(root.getString("RETURN_CODE"))){
                                    JSONObject OUT_DATA = root.getJSONObject("OUT_DATA");
                                    List<Map<String,String>> vwUserList = claimForFundsService.getVwUserinf(String.valueOf(user.getRowNo()));

                                    if (OUT_DATA.getString("REGION_NAME").contains("成都") && ("天府新区分公司".equals(vwUserList.get(0).get("COMPANY_NAME"))
                                            || "省公司".equals(vwUserList.get(0).get("COMPANY_NAME")) || "成都分公司".equals(vwUserList.get(0).get("COMPANY_NAME")))){
                                        flag = true;
                                    }else {
                                        flag = vwUserList.get(0).get("COMPANY_NAME").contains(OUT_DATA.getString("REGION_NAME"));
                                    }

                                }
                            }
                            if (flag){
                                maps.put("CONTRCTTYPE", contrctType);
                                maps.put("CONTRCTNO", contrctNo);
                            }else {
                                continue;
                            }
                        }else {
                            if (contractSet.contains(contrctNo)){
                                maps.put("CONTRCTTYPE", contrctType);
                                maps.put("CONTRCTNO", contrctNo);
                            }else {
                                continue;
                            }
                        }

                        String usetype = "";
                        switch (map.get(i).get(2).toString()){
                            case "缴费":
                                usetype = "1";
                                break;
                            case "存送":
                                usetype = "2";
                                break;
                            case "终端":
                                usetype = "3";
                                break;
                            case "预开票":
                                usetype = "05";
                                break;
                            case "有价卡":
                                usetype = "06";
                                break;
                            case "物联网":
                                usetype = "07";
                                break;
                            case "ICT设备销售":
                                usetype = "08";
                                break;
                            case "ICT终端销售":
                                usetype = "09";
                                break;
                            case "ICT软件销售":
                                usetype = "10";
                                break;
                        }
                        maps.put("USETYPE", usetype);
                        maps.put("AMOUNT", ((Map) map.get(i)).get(3));
                        list.add(maps);
                    }
                    Write(returnPars(1,list,"导入成功！"));
                } else {
                    this.Write(returnPars(-1,"","亲爱的同事，文件格式异常，请确认文件格式！"));
                }
            }
        }catch (Exception e) {
            logger.info("文件导入失败："+e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，文件上传异常，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 根据工单编号和模块标识查询附件信息
     * <AUTHOR>
     * @Date 2022/4/22 17:27
     **/
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = modelService.fuJian(id, biaoshi);
        Write(JSONHelper.Serialize(s));
    }

    /**
     * @Description TODO 时间字符串转为时间格式
     * <AUTHOR>
     * @param currentTime 时间字符串
     * @return java.util.Date
     * @Date 2022/4/8 11:22
     **/
    public static Date getStringDateFour(String currentTime) {
        Date dateString = null;
        try {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            dateString = formatter.parse(currentTime);
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        return dateString;
    }

    /**
     * @Description TODO 日期转换
     * <AUTHOR>
     * @param currentTime 时间
     * @return java.lang.String
     * @Date 2022/4/8 15:20
     **/
    public static String getStringDatetwo(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description: TODO 返回参数生成
     */
    private static String returnPars(int state,Object data,String msg){
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code",state);
        mapJson.put("data",data);
        mapJson.put("msg",msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }
}
