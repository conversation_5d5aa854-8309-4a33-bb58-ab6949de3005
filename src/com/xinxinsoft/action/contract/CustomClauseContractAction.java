package com.xinxinsoft.action.contract;
import java.math.BigDecimal;
import java.util.Date;

import com.google.gson.Gson;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfWriter;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.claimForFunds.MoneyTotal;
import com.xinxinsoft.entity.contract.MobileCorporation;
import com.xinxinsoft.entity.contractTariffConfig.*;
import com.xinxinsoft.entity.contractUniformity.*;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.oms.*;
import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.entity.pms.PmsProdPriceInfoReserve;
import com.xinxinsoft.entity.pms.PmsProductInfo;
import com.xinxinsoft.entity.pms.PmsProductLabel;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.sign.Cert;
import com.xinxinsoft.entity.sign.Seal;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.entity.transfer.TransferAccountInformation;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.AttachmentService.AttachmentSrv;
import com.xinxinsoft.sendComms.contract.CustomClauseContractSrv;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.service.core.user.StructureOfPersonnelService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.oms.OmsOrderWorkbenchService;
import com.xinxinsoft.service.oms.OmsSellOrderService;
import com.xinxinsoft.service.oms.V2OmsSellOrderService;
import com.xinxinsoft.service.sign.SignService;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.contract.CustomClauseContractService;
import com.xinxinsoft.service.contractUniformityService.ContractUniformityService;
import com.xinxinsoft.service.sign.SignService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.HDFS.utils.HDFSUtils;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;

import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import it.sauronsoftware.base64.Base64;
import net.sf.json.JSON;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.collections.MapUtils;
import org.apache.commons.io.FileUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;
import org.hibernate.transform.Transformers;
import org.jbpm.api.task.Task;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import sun.security.krb5.EncryptedData;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import javax.script.ScriptEngine;
import javax.script.ScriptEngineManager;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.jsp.PageContext;

import java.awt.image.BufferedImage;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 自定义条款合同ACtion
 */
public class CustomClauseContractAction extends BaseAction {
    private static final Logger logger = Logger.getLogger(CustomClauseContractAction.class);
    private final static String SIGN_DOWNLOADPDF = Config.getString("SIGN_DOWNLOADPDF");//#签章文件下载地址
    private final static String SIGN_UPLOADPDF = Config.getString("SIGN_UPLOADPDF");//#签章文件上传地址
    private final static boolean IS_CONTRACT_SWITCH = Config.getBoolean("IS_CONTRACT_SWITCH");//合同流程开关
    private static Boolean isES = false;

    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }

    private static final String serverPath = FileUpload.getFtpURL();
    private CustomClauseContractService customClauseContractService;
    private ContractUniformityService contractUniformityService;
    private SignService signService;
    private AttachmentService attachmentService;
    @Resource(name = "StructureOfPersonnelService")
    private StructureOfPersonnelService structureOfPersonnelService;
    private JbpmUtil jbpmUtil;
    @Resource(name="Bpms_riskoff_service")
    private Bpms_riskoff_service taskService;
    @Resource(name="WaitTaskService")
    private WaitTaskService service;
    @Resource(name="SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name="V2OmsSellOrderService")
    private V2OmsSellOrderService omsSellOrderService;
    @Resource(name="OmsOrderWorkbenchService")
    private OmsOrderWorkbenchService omsOrderWorkbenchService;
    public CustomClauseContractService getCustomClauseContractService() {
        return customClauseContractService;
    }
    public JbpmUtil getJbpmUtil() {
        return jbpmUtil;
    }

    public void setJbpmUtil(JbpmUtil jbpmUtil) {
        this.jbpmUtil = jbpmUtil;
    }
    // 使用列表接收上传文件
    private List<File> files;
    // 使用列表保存多个上传文件的文件名
    private List<String> filesFileName;

    public List<File> getFiles() {
        return files;
    }

    public void setFiles(List<File> files) {
        this.files = files;
    }

    public List<String> getFilesFileName() {
        return filesFileName;
    }

    public void setFilesFileName(List<String> filesFileName) {
        this.filesFileName = filesFileName;
    }

    public void setCustomClauseContractService(CustomClauseContractService customClauseContractService) {
        this.customClauseContractService = customClauseContractService;
    }

    public ContractUniformityService getContractUniformityService() {
        return contractUniformityService;
    }

    public void setContractUniformityService(ContractUniformityService contractUniformityService) {
        this.contractUniformityService = contractUniformityService;
    }

    public SignService getSignService() {
        return signService;
    }

    public void setSignService(SignService signService) {
        this.signService = signService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }


    /**
     * 根据产品，操作类型，和状态查询 模板
     */
    @Deprecated
    public void queryContractTmpls() {
        String pcode = this.getString("pcode");
        String opertype = this.getString("opertype");
        String state = this.getString("state");
        String page = this.getString("page");
        String pageSize = this.getString("pageSize");
        Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryContractTmplSer(pcode, opertype, state, page, pageSize)));
    }


    /**
     * 替换合同模板(查看)
     */
    public void previewContracts() {
        Map<String, String> mapJson = new HashMap<>();
        try {
            Map<String, String> map = new HashMap<>();
            String request_info = UrlConnection.getRequestData(getRequest());
            request_info = URLDecoder.decode(request_info, "UTF-8");
            String[] str = request_info.split("&");
            for (String strr : str) {
                try {
                    map.put("${" + strr.split("=")[0] + "}", (strr.split("=")[1] == null || StringUtils.isBlank(strr.split("=")[1])) ? "      " : strr.split("=")[1]);
                } catch (IndexOutOfBoundsException e) {
                    map.put("${" + strr.split("=")[0] + "}", "     ");
                }
            }
            CityContractTemplate city = customClauseContractService.queryCityContractTemplate(super.user.getSystemDept().get(0).getCompanyCode());
            if (!"".equals(map.get("${productJson}")) && map.get("${productJson}") != null
                    && !"null".equals(map.get("${productJson}")) && !"undefined".equals(map.get("${productJson}"))) {
                JSONArray arry = JSONArray.fromObject(map.get("${productJson}"));
                StringBuffer explain = new StringBuffer();
                for (int i = 0; i < arry.size(); i++) {
                    JSONObject obj = JSONObject.fromObject(arry.get(i));
                    PmsProductInfo pmsProductInfo = customClauseContractService.queryPmsProductInfo(obj.get("PROD_CODE").toString(),obj.get("LABELID").toString());
                    if (pmsProductInfo != null) {
                        if (pmsProductInfo.getProduct_explain() != null) {
                            explain.append(pmsProductInfo.getProduct_explain() + "/n");
                        }
                    }
                }
                if (explain.length() > 0) {
                    System.out.println("这是进入有参数的判断");
                    //String explainStr =explain.toString().substring(0,explain.toString().length()-2);
                    map.put("${explain}", explain.toString());
                } else {
                    map.put("${explain}", "   ");
                }
            } else {
                map.put("${explain}", "  ");
            }
            logger.info("模板名称：" + city.getContract_name());
            map.put("${ContractNumber}", getContractNum());
            map.put("${url}", city.getContract_name());
            map.remove("${productJson}");
            Long time = System.currentTimeMillis();
            StorageCfg storageCfg = attachmentService.queryStorageCfg();
            //String endUrl=FileUpload.getFtpURL()+urlDate;
            String ftpUrl = storageCfg.getFileName() + "NEWCONTR/ECSEE/" + time + "/";
            File headPath = new File(ftpUrl);//获取文件夹路径
            if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                headPath.mkdirs();
            }
            String srcPath = ServletActionContext.getServletContext().getRealPath("/contractTemplate/"+map.get("${url}"));
            //String srcPath = storageCfg.getFileName() + "NEWCONTR/" + map.get("${url}");
            logger.info("模板地址：" + srcPath);
            logger.info("生成docx地址：" + ftpUrl + time + ".docx");
            FileUtil.seekAndReplace(srcPath, ftpUrl + time + ".docx", map);
            try {
                DocConverter converter = new DocConverter(ftpUrl + time + ".docx", "订单系统合同预览", 0);
                converter.converdTp();
                /**
                 * 以下做签章处理
                 */
                Map<String, Object> paramMap = new HashMap<>();
                Map<String, Object> map1 = new HashMap<>();
                if ("00".equals(user.getSystemDept().get(0).getSystemCompany().getCompanyCode())) {
                    //map1.put("departNumber","10008");
                    map1.put("departNumber", "01");
                } else {
                    map1.put("departNumber", user.getSystemDept().get(0).getSystemCompany().getCompanyCode());//机构编码
                }
                map1.put("applyNo", "zqdzyz");//账号
                map1.put("typeCode", "2");
                Map<String, Object> stringObjectMap = signService.getSealImgList(map1);
                if (stringObjectMap.containsKey("datas")) {
                    JSONArray array = (JSONArray) stringObjectMap.get("datas");
                    if (array.size() > 0) {
                        JSONObject jsonObject = array.getJSONObject(0);
                        String sealId = (String) jsonObject.get("sealId");
                        /**
                         * 关键字签章
                         */
                        paramMap.put("departNumber", "10008");//机构编码 地市编码
                        paramMap.put("departName", "四川");//机构名称 地市名称
                        paramMap.put("applyNo", "zqdzyz");//gert.getApplyNo()//申请人账号
                        paramMap.put("userName", "杨玉峰");//gert.getUserName()//申请人姓名
                        paramMap.put("signseal", "1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
                        paramMap.put("chapterType", "2");//章类型；1.个人章，2 机构章
                        paramMap.put("key", map.get("${PartyB}") + "（盖章）");//关键字内容（PDF 内容中的关键字）
                        paramMap.put("offsetX", 0);//非必填--》Integer 横向偏移量，单位毫米，正数向右偏移， 负数向左偏移
                        paramMap.put("offsetY", -20);//非必填--》Integer 纵向偏移量，单位毫米，正数向上偏移， 负数向下偏移
                        paramMap.put("sealId", sealId);//seal.getSealId()印章 id（生成章返回的编码） sealId

                        /**
                         * 骑缝签章
                         */
                        /*paramMap.put("departNumber","8256");//机构编码
                        paramMap.put("departName","网络部");//机构名称
                        paramMap.put("applyNo","lizhi_ya");//申请人账号
                        paramMap.put("userName","雅安_宝兴_李志");//申请人姓名
                        paramMap.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
                        paramMap.put("chapterType","2");//章类型；1.个人章，2 机构章
                        paramMap.put("leftRight","1");//1:靠左，2. 靠右（PDF 做骑缝签章 有靠左签章与靠右签章）
                        paramMap.put("sealWidth",41);//非必填--》Integer 印章宽度，单位毫米，默认为 41 毫米。 注意，仅当印章类型为机构章时，该值有 效
                        paramMap.put("sealHeight",100);//骑缝章高度（章距离 pdf 顶部的距 离）
                        paramMap.put("sealId","a387599261dc4dda8806aeedea6548d8");//印章 id（生成章返回的编码）*/

                        /**
                         * 单坐标签章
                         */
                        /*paramMap.put("departNumber","8256");//机构编码
                        paramMap.put("departName","网络部");//机构名称
                        paramMap.put("applyNo","lizhi_ya");//申请人账号
                        paramMap.put("userName","雅安_宝兴_李志");//申请人姓名
                        paramMap.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
                        paramMap.put("chapterType","2");//章类型；1.个人章，2 机构章
                        paramMap.put("xy","3,200,170");//坐标（page,x,y,指的在 PDF 中的 坐标；以 PDF 坐下角为 0 坐标，x 指距离左下角横向距离 x 像素，y 指距离左下角纵向距离 y 像素， page 指 pdf 页数；例如参数：1， 100,100 坐标指在 pdf 文档第一 页，距离左下角横向距离 100 像 素，距离左下角纵向距离 100 像素 的坐标）
                        paramMap.put("sealWidth",41);//非必填--》Integer 印章宽度，单位毫米，默认为 41 毫米。 注意，仅当印章类型为机构章时，该值有 效
                        paramMap.put("sealId","a387599261dc4dda8806aeedea6548d8");//印章 id（生成章返回的编码）*/
                        /*File file=new File(ftpUrl + time + "(1).pdf");
                        if(checkFileSize(file,2,"M")){
                            String buFile = GetFile.PDFToBase64(file);
                            paramMap.put("reqPdf",buFile);//Pdf 文件传输方式，fileUrl 通过 get 请求 获取文件，reqPdf 通过 base64 字符串 传输文件。两种方式二选一，如果同时传 值，优先使用 reqPdf
                        }else{
                            paramMap.put("fileUrl",SIGN_DOWNLOADPDF+ftpUrl + time + "(1).pdf");
                            paramMap.put("uploadUrl",SIGN_UPLOADPDF);
                        }*/
                        File file = new File(ftpUrl + time + "(1).pdf");
                        if (checkFileSize(file, 2, "M")) {
                            String buFile = GetFile.PDFToBase64(file);
                            paramMap.put("reqPdf", buFile);//Pdf 文件传输方式，fileUrl 通过 get 请求 获取文件，reqPdf 通过 base64 字符串 传输文件。两种方式二选一，如果同时传 值，优先使用 reqPdf
                        }/*else{
                            paramMap.put("fileUrl",SIGN_DOWNLOADPDF+ftpUrl + time + "(1).pdf");
                            paramMap.put("uploadUrl",SIGN_UPLOADPDF);
                        }*/
                        Map<String, Object> pdfMap = signService.sealByKey(paramMap);//关键字签章
                        //Map<String,Object> pdfMap= signService.sealByStraddle(paramMap);//骑缝签章
                        //Map<String,Object> pdfMap= signService.sealByXy(paramMap);//单坐标签章
                        if (pdfMap != null) {
                            if (!"-1".equals(pdfMap.get("resp_code")) && !"1".equals(pdfMap.get("resp_code"))) {
                                JSONObject datas = JSONObject.fromObject(pdfMap.get("datas"));
                                if (datas.getString("fileId").equals("null") || datas.getString("fileId") == null || "".equals(datas.getString("fileId"))) {
                                    String reBase64 = (String) datas.get("reBase64");
                                    logger.info("这是获取的PDF文件BASE64编码：" + reBase64);
                                    GetFile.base64StringToPdf(reBase64, ftpUrl + time + "(1).pdf");
                                }/*else {
                                    Attachment attachment = attachmentService.getAttachmentById(datas.get("fileId").toString());
                                    if(attachment!=null){

                                    }else{
                                        Write(com.xinxinsoft.utils.JSONHelper.Serialize("数据获取错误"));
                                    }
                                }*/
                            }
                        }
                    }
                }
                mapJson.put("code", "1");
                mapJson.put("dirUrl", ftpUrl);
                mapJson.put("pdfUrl", ftpUrl + time + "(1).pdf");
                Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
            } catch (Exception e) {
                e.printStackTrace();
                //出现错误，就立即删除文件和当前目录
                deleteDir(ftpUrl.substring(0, ftpUrl.lastIndexOf("/")), true);
                mapJson.put("code", "1");
                mapJson.put("dirUrl", "");
                mapJson.put("pdfUrl", "");
                Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error(e.getMessage());
            mapJson.put("code", "1");
            mapJson.put("dirUrl", "");
            mapJson.put("pdfUrl", "");
            Write(JSONHelper.SerializeWithNeedAnnotation(mapJson));
        }
    }

    /***
     * 查询资费信息
     */
    @Deprecated
    public void tariffTableItems() {
        String code = this.getString("code");//资费code
        Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryItems(code)));
    }

    /**
     * 资费选择生成HTML
     *
     * @param
     * @return {@link }
     * @throws
     * @description: tariffTableHtml
     * <AUTHOR>
     * @version 1.1.0
     * @data 2020/3/10 16:45
     */
    public void tariffTableHtml() {
        String code = this.getString("code");
        Write(customClauseContractService.queryTariffTableHtml(code));
    }

    /**
     * 查询自定义条款信息
     */
    public void queryCustomClauses() {
        String id = this.getString("id");
        Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryCustomClauses(id, Integer.valueOf(super.user.getSystemDept().get(0).getCompanyCode()))));
    }

    /***
     *  生成合同编号
     * @return
     */
    private synchronized String getContractNum() {
        String shiqu = "";
        String quxian = "";
        String IBM = "";
        List<Object[]> sone = contractUniformityService.getbumen(user.getRowNo());
        for (int j = 0; j < sone.size(); j++) {
            quxian = (String) sone.get(j)[0];
            shiqu = (String) sone.get(j)[1];
            IBM = (String) sone.get(j)[2];
        }
        SimpleDateFormat df = new SimpleDateFormat("yyyyMMdd");
        String newsNo = df.format(new Date());//时间
        String deptStr = "";//公司
        String bumen = "";//部门
        if (user.getLoginName().equals("admin")) {
            deptStr = "QT";
        } else {
            List<SystemDept> deptList = user.getSystemDept();
            deptStr = deptList.get(0).getSystemCompany().getCompanyName();
            bumen = deptList.get(0).getDepartmentName();
        }
        int count = contractUniformityService.getCtractCount();
        String csa = "00000";
        DecimalFormat d = new DecimalFormat(csa);
        String number = d.format(count + 1);
        String ContractNumber = "【" + shiqu + "-" + quxian + "】" + newsNo + number;//合同编号
        return ContractNumber;
    }

    /**
     * 作废合同
     */
    public void nullifyContract() {
        String contCode = this.getString("id");
        try {
            ContractInfo cinfo = customClauseContractService.queryContractInfo(contCode);
            if (cinfo != null) {
                cinfo.setSTATE(-1);//作废状态，目前不清楚作废状态为什么 暂时为4
                cinfo.setUpdateDate(new Date());
                customClauseContractService.updateContractInfo(cinfo);
                this.Write("S");
            } else {
                this.Write("E");
            }
        } catch (Exception e) {
            this.Write("E");
        }
    }


    /***
     * 普通合同电子合同保存
     */
    public void savePaperContract_old() {
        try {
            String ContractNumber = getContractNum();
            if(IS_CONTRACT_SWITCH){
                String request_info = UrlConnection.getRequestData(getRequest());
                request_info = URLDecoder.decode(request_info, "UTF-8");
                String[] str = request_info.split("&");
                Map<String, String> map = new HashMap<>();
                for (String strr : str) {
                    try {
                        map.put("${" + strr.split("=")[0] + "}", (strr.split("=")[1] == null || StringUtils.isBlank(strr.split("=")[1])) ? "      " : strr.split("=")[1]);
                    } catch (IndexOutOfBoundsException e) {
                        map.put("${" + strr.split("=")[0] + "}", "     ");
                    }
                }
                String IBM = "";
                List<Object[]> sone = customClauseContractService.getbumen(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                String contractId = IBM + Bpms_riskoff_service.getUnlockedNumber();
                Map<String, String> obj = new HashMap<>();
                obj.put("contractID",contractId);//合同编码
                obj.put("contractNumber",ContractNumber);//合同号
                obj.put("contractName",map.get("${contractName}"));//合同名称
                obj.put("firstParty",map.get("${PartyA}"));//甲方名称
                obj.put("templateId",map.get("${templateId}"));//甲方名称
                obj.put("secondParty",map.get("${PartyB}"));//乙方名称
                obj.put("contractDate",DateUtil.convertDateToString(new Date(), "yyyyMMddHHmmss"));//合同签约时间
                obj.put("validDate",DateUtil.convertDateToString(DateUtil.stringToDate(map.get("${EffectiveDate}"), "yyyy-MM-dd"), "yyyyMMddHHmmss"));//生效日期
                obj.put("expireDate",DateUtil.convertDateToString(DateUtil.stringToDate(map.get("${TerminationDate}"), "yyyy-MM-dd"), "yyyyMMddHHmmss"));//失效日期
                obj.put("contractStatus","0");//合同状态
                System.out.println(obj);
                Result result= CustomClauseContractSrv.getInstance().Mgt_contractCreate(map,user.getMobile(),user.getBossUserName(),"0",obj);
                if(ResultCode.SUCCESS.code()==result.getCode()){
                    JSONObject body=JSONObject.fromObject(result.getData());
                    System.out.println(body.toString());
                    if("0000".equals(body.get("resultCode"))){
                        this.Write("1");
                    }else{
                        logger.error("生产环境调用合同生成服务错误："+body.get("bizDesc"));
                        this.Write("合同生成服务错误："+body.get("bizDesc"));
                    }
                }
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("普通合同电子合同保存程序异常==>", e);
            this.Write("error");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/10/14 17:07
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同审批处理页面
     */
    public void handleContract(){
        Map<String,Object> mapJson =new HashMap<>();
        try{
            String processId = getString("processId");//流程id
            String id= getString("id");
            String taskId= getString("taskId");
            String desc= getString("desc");
            String waitId= getString("waitId");
            String userId= getString("userId");
            String mark = getString("mark");
            ContractInfo contractInfo = customClauseContractService.queryContractInfoById(id);
            if("2".equals(mark)){//驳回
                contractInfo.setSTATE(-1);
                customClauseContractService.updateContractInfo(contractInfo);
                taskService.updateBpms_riskoff_task(desc, 0, taskId);
                WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("msg","未查询到待办信息,请确认!");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return ;
                }
                mapJson.put("code",1);
                mapJson.put("data","");
                mapJson.put("msg","处理成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }else{
                int companyLevel = getUserJurisdiction(user);
                ContractProductTariff cpt = customClauseContractService.queryContractProductTariff(contractInfo.getContractId());
                List<Map<String,String>>  approvalCondition = customClauseContractService.getApprovalCondition(
                        cpt.getProductCode(),cpt.getLabelCode(),companyLevel);
                JSONArray array = JSONArray.fromObject(contractInfo.getApprovalElements());
                JSONObject jsonObject = JSONObject.fromObject(array.get(0));
                Iterator it = jsonObject.keys();
                Map<String, Object> variables = new HashMap<>();
                while(it.hasNext()){
                    String key = (String) it.next();// 获得key
                    String value = jsonObject.getString(key);//获得value
                    variables.put(key,value);
                }
                boolean bl=false;
                String role = "";
                for(int i=0;i<approvalCondition.size();i++){
                    ScriptEngineManager manager = new ScriptEngineManager();
                    ScriptEngine engine = manager.getEngineByName("js");
                    for(String key:variables.keySet()){
                        String value = variables.get(key).toString();
                        engine.put(key, value);
                    }
                    Object result = engine.eval(approvalCondition.get(i).get("CONTENT").trim());
                    bl=Boolean.parseBoolean(result.toString());
                    if(bl){
                        role=approvalCondition.get(i).get("ROLE");
                        break;
                    }
                }

                Object list = systemUserService.getPriority(String.valueOf(user.getRowNo()));
                JSONArray jsonArray=JSONArray.fromObject(list);
                int type=0;
                for(int i=0;i<jsonArray.size();i++){
                    JSONObject object = JSONObject.fromObject(jsonArray.get(i));
                    if(role.equals(object.getString("NAME"))){
                        type=1;
                    }
                }

                if(type==1){
                    Integer signatureType= contract_signature(contractInfo);
                    if(signatureType==1){
                        taskService.updateBpms_riskoff_task(desc, 2, taskId);
                        contractInfo.setSTATE(2);
                        customClauseContractService.updateContractInfo(contractInfo);
                    }else{
                        mapJson.put("code",-1);
                        mapJson.put("data","");
                        mapJson.put("msg","合同签章失败！请联系系统管理员");
                        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                        return ;
                    }
                }else{
                    taskService.updateBpms_riskoff_task(desc, 2, taskId);
                    String rtaskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH","审批", Integer.parseInt(userId), user);
                    commitBackLog(contractInfo, Integer.parseInt(userId), processId, user, rtaskid);//生成待办
                }
                WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
                if (wt != null) {
                    service.updateWait(wt, this.getRequest());
                } else {
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("msg","未查询到待办信息,请确认!");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return ;
                }
                mapJson.put("code",1);
                mapJson.put("data","");
                mapJson.put("msg","处理成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
            }
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("合同处理错误："+e.getMessage(),e);
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("msg","合同处理错误"+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/30 15:56
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 客户经理阅读代办任务
     */
    public void readCcontractInfo(){
        Map<String,Object> map =new HashMap<>();
        try {
            String id= getString("id");
            String taskId= getString("taskId");
            String waitId= getString("waitId");
            taskService.updateBpms_riskoff_task("已阅", 2, taskId);
            WaitTask wt = service.queryWaitByTaskId(waitId);//根据待办id查询待办信息
            //结束当前待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                map.put("code",-1);
                map.put("data","");
                map.put("msg","未查询到待办信息");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
                return;
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","处理成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("客户经理阅读作废合同错误信息："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","处理失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }


    /**
     * 提交待办生成
     */
    public void commitBackLog(ContractInfo info, Integer userid, String processId, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[电子合同]" + info.getContractName());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/MyContractsManage/handleContract.jsp?id=" + info.getId() + "&processId=" +
                processId + "&taskId=" + taskid);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);//状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(ContractInfo.ContractInfo);//标识
        waitTask.setTaskId(taskid);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * @author: liyang
     * @date: 2021/10/11 16:26
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询当前按钮（更改以后是查询当前是普通审批还是数据库配置的角色审批人审批）
     */
    public void queryJudgeButton(){
        Map<String, String> map = new HashMap<>();
        try {
            String id = getString("id");
            int companyLevel = getUserJurisdiction(user);
            ContractInfo con =customClauseContractService.queryContractInfoById(id);
            ContractProductTariff cpt = customClauseContractService.queryContractProductTariff(con.getContractId());
            List<Map<String,String>>  approvalCondition = customClauseContractService.getApprovalCondition(
                    cpt.getProductCode(),cpt.getLabelCode(),companyLevel);
            JSONArray array = JSONArray.fromObject(con.getApprovalElements());
            JSONObject jsonObject = JSONObject.fromObject(array.get(0));
            Iterator it = jsonObject.keys();
            Map<String, Object> variables = new HashMap<>();
            while(it.hasNext()){
                String key = (String) it.next();// 获得key
                String value = jsonObject.getString(key);//获得value
                variables.put(key,value);
            }
            boolean bl=false;
            String role = "";
            for(int i=0;i<approvalCondition.size();i++){
                ScriptEngineManager manager = new ScriptEngineManager();
                ScriptEngine engine = manager.getEngineByName("js");
                for(String key:variables.keySet()){
                    String value = variables.get(key).toString();
                    engine.put(key, value);
                }
                Object result = engine.eval(approvalCondition.get(i).get("CONTENT").trim());
                bl=Boolean.parseBoolean(result.toString());
                if(bl){
                    role=approvalCondition.get(i).get("ROLE");
                    break;
                }
                /*bl = ProcessUtils.processJudge(variables,approvalCondition.get(i).get("CONTENT"));
                if(bl){
                    role=approvalCondition.get(i).get("ROLE");
                    break;
                }*/
            }
            Object list = systemUserService.getPriority(String.valueOf(user.getRowNo()));
            JSONArray jsonArray=JSONArray.fromObject(list);
            int type=0;
            for(int i=0;i<jsonArray.size();i++){
                JSONObject object = JSONObject.fromObject(jsonArray.get(i));
                if(role.equals(object.getString("NAME"))){
                    type=1;
                }
            }
            JSONObject obj = new JSONObject();
            if(type==1){
                obj.put("button","END");
            }else{
                obj.put("button","SUBMIT");
            }
            Write(obj.toString());
            /*JSONArray jsonArray=findTransition(pid);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();// 获取流程任务表信息
            String code =customClauseContractService.getCountyByUserID(contractInfo.getCreator()).get(0).get("COMPANY_CODE").toString();
            TransferCitiesData transferCitiesData = customClauseContractService.getTransferCitiesData(code, task.getActivityName());
            TransferCitiesData transferCitiesDataRatio = customClauseContractService.getTransferCitiesDataRatio(code, task.getActivityName());
            JSONObject obj = new JSONObject();
            if(task==null){
                obj.put("button","COMPLETE");
                obj.put("role","");
                Write(JSONHelper.SerializeWithNeedAnnotation(map));
            }else{
                if(jsonArray.size()>0){
                    JSONObject jsonObject= JSONObject.fromObject(jsonArray.get(0));
                    if(jsonObject.get("transitionName")=="END"){//有END证明当前节点是最后一个审批节点
                        obj.put("button","END");
                        obj.put("role","");
                    }else if((jsonObject.get("transitionName").toString()).indexOf("decision")!=-1){//有decision字符串证明当前是判断节点需要判断金额
                        if(Double.parseDouble(contractInfo.getALLMONEY()) > Double.parseDouble(transferCitiesData.getAmount())){
                            obj.put("button","SUBMIT");
                            String[] roleArray=jsonObject.get("transitionName").toString().split(",");
                            obj.put("role",roleArray[0]);
                        }else{//不大于的时候不需要就结束当前流程
                            obj.put("button","END");
                            obj.put("role","");
                        }
                    }else{//没有END并且没有decision字符串证明是正常单节点
                        if("END".equals(jsonObject.get("transitionName").toString())){
                            obj.put("button","END");
                            obj.put("role","");
                        }else{
                            obj.put("button","SUBMIT");
                            obj.put("role",jsonObject.get("transitionName").toString());
                        }
                    }
                }
                logger.info("电子合同审批下一步操作："+obj.toString());
                Write(obj.toString());
            }*/
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("查询按钮错误："+e.getMessage(),e);
        }
    }

    /**
     * @author: liyang
     * @date: 2021/6/22 14:42
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同续签服务
     */
    public void MgtContractRenewModify(){
        String contractId=getString("id");
        String RenewExpireDate = getString("renewExpireDate");
        String effectDate = getString("effectDate");
        if(IS_CONTRACT_SWITCH){
            Result result= CustomClauseContractSrv.getInstance().Mgt_contractRenewModify(contractId,RenewExpireDate,effectDate,"续签："+RenewExpireDate+"到"+ effectDate,user.getBossUserName(),user.getMobile());
            if(ResultCode.SUCCESS.code()==result.getCode()){
                //JSONObject root=JSONObject.fromObject(JSONObject.fromObject(result.getData()).get("ROOT"));
                //JSONObject body=JSONObject.fromObject(root.get("BODY"));
                JSONObject data=JSONObject.fromObject(result.getData());
                if("0".equals(data.get("resultCode"))){
                    this.Write("1");
                }else{
                    logger.error("电子合同续签："+result.getData());
                    this.Write("error");
                }
            }
        }else{
            ContractInfo contractInfo = customClauseContractService.queryContractInfo(contractId);
            String retStr = copyContractInfo(contractInfo,effectDate,RenewExpireDate);
            if("0".equals(retStr)){
                this.Write("1");
            }else{
                this.Write("error");
            }
        }
    }

    /**
     * @author: liyang
     * @date: 2021/6/22 14:42
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同预览服务
     */
    public void MgtContractItemInfoQuery(){
        String contractId = getString("id");
        HttpServletResponse response = ServletActionContext.getResponse();
        try {
            if (IS_CONTRACT_SWITCH) {
                Result result = CustomClauseContractSrv.getInstance().Mgt_contractItemInfoQuery(contractId, user.getBossUserName());
                System.out.println(result.getData());
                if (ResultCode.SUCCESS.code() == result.getCode()) {
                    if(!"".equals(JSONObject.fromObject(result.getData()).get("contractFileBase64Str"))
                            &&JSONObject.fromObject(result.getData()).get("contractFileBase64Str")!=null){
                        String reBase64 = JSONObject.fromObject(result.getData()).get("contractFileBase64Str").toString();
                        StorageCfg storageCfg = attachmentService.queryStorageCfg();
                        Long time = System.currentTimeMillis();
                        String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                        String ftpUrl = storageCfg.getFileName()+urlDate;
                        File headPath = new File(ftpUrl);//获取文件夹路径
                        if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                            headPath.mkdirs();
                        }
                        GetFile.base64StringToPdf(reBase64, ftpUrl + time + ".pdf");
                        response.setContentType("application/pdf");
                        FileInputStream in = new FileInputStream(ftpUrl + time + ".pdf");
                        OutputStream out = response.getOutputStream();
                        byte[] b = new byte[1024];
                        while ((in.read(b)) != -1) {
                            out.write(b);
                        }
                        out.flush();
                        in.close();
                        out.close();
                        File file = new File(ftpUrl+time+".pdf");
                        file.delete();
                    } else {
                        this.Write("error");
                    }
                }
            } else {
                ContractInfo contractInfo = customClauseContractService.queryContractInfo(contractId);
                if (contractInfo.getpDFAttUrl() != null) {
                    byte[] data = HDFSUtils.readFileToByte(contractInfo.getpDFAttUrl());
                    response.setContentType("application/pdf");
                    OutputStream out = response.getOutputStream();
                    out.write(data);
                    out.flush();
                    out.close();
                } else {
                    response.setContentType("application/pdf");
                    FileInputStream in = new FileInputStream(contractInfo.getPdfUrl());
                    OutputStream out = response.getOutputStream();
                    byte[] b = new byte[1024];
                    while ((in.read(b)) != -1) {
                        out.write(b);
                    }
                    out.flush();
                    in.close();
                    out.close();
                }
            }
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("合同预览位置错误："+e.getMessage(),e);
            this.Write("error");
        }
    }

    /**
     * @author: liyang
     * @date: 2021/4/23 15:52
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 生成电子协议服务
     */
    public String copyContractInfo(ContractInfo contractInfo,String EffectDate,String LostEffectDate) {
        try{
            ContractRenewal renewal= customClauseContractService.getContractRenewal(contractInfo.getContractId());
            CityContractTemplate city= customClauseContractService.queryCityContractTemplate(user.getSystemDept().get(0).getCompanyCode());
            StorageCfg storageCfg = attachmentService.queryStorageCfg();
            String ContractNumber = getContractNum();
            ContractBasisInfo binfo = new ContractBasisInfo();
            ContractInfo info = new ContractInfo();
            String renewalContent = renewal.getContent();
            Map<String, String> map = ConvertJSONToMap(renewalContent);
            map.put("${EffectiveDate}",EffectDate);
            map.put("${TerminationDate}",LostEffectDate);
            String code="";
            if (!"".equals(map.get("${productJson}")) && map.get("${productJson}") != null
                    && !"null".equals(map.get("${productJson}")) && !"undefined".equals(map.get("${productJson}"))) {
                JSONArray arry = JSONArray.fromObject(map.get("${productJson}"));
                StringBuffer explain = new StringBuffer();
                for (int i = 0; i < arry.size(); i++) {
                    JSONObject obj = JSONObject.fromObject(arry.get(i));
                    code += obj.getString("PROD_CODE") + "、";
                    ProductInfos productInfos = customClauseContractService.queryProductInfos(obj.get("PROD_CODE").toString());
                    if (productInfos != null) {
                        if (productInfos.getProduct_explain() != null) {
                            explain.append(productInfos.getProduct_explain() + "/n");
                        }
                    }
                }
                if (explain.length() > 0) {
                    map.put("${explain}", explain.toString());
                } else {
                    map.put("${explain}", "  ");
                }
            } else {
                map.put("${explain}", "  ");
            }
            map.put("${ContractNumber}", ContractNumber);
            if("1".equals(renewal.getContractType())){
                map.put("${url}","mas20210330.docx");
            }else{
                map.put("${url}", city.getContract_name());
            }
            String json = map.get("${json}");
            map.remove("${productJson}");
            map.remove("${json}");
            Long time = System.currentTimeMillis();
            String ftpUrl = storageCfg.getFileName() + "NEWCONTR/ECSEE/" + time + "/";
            File headPath = new File(ftpUrl);//获取文件夹路径
            if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                headPath.mkdirs();
            }
            String srcPath = ServletActionContext.getServletContext().getRealPath("/contractTemplate/"+map.get("${url}"));
            //String srcPath = storageCfg.getFileName() + "NEWCONTR/" + map.get("${url}");
            FileUtil.seekAndReplace(srcPath, ftpUrl + time + ".docx", map);
            int type = 0;
            String reBase64="";
            try {
                DocConverter converter = new DocConverter(ftpUrl + time + ".docx", ContractNumber, 0);
                converter.converdTp();
                //以下做签章处理
                Map<String, Object> paramMap = new HashMap<>();
                Map<String, Object> map1 = new HashMap<>();
                if ("00".equals(user.getSystemDept().get(0).getSystemCompany().getCompanyCode())) {
                    map1.put("departNumber", "01");
                } else {
                    map1.put("departNumber", user.getSystemDept().get(0).getSystemCompany().getCompanyCode());//机构编码
                }
                map1.put("applyNo", "zqdzyz");//账号
                map1.put("typeCode", "2");
                Map<String, Object> stringObjectMap = signService.getSealImgList(map1);
                if (stringObjectMap.containsKey("datas")) {
                    JSONArray array = (JSONArray) stringObjectMap.get("datas");
                    if (array.size() > 0) {
                        JSONObject jsonObject = array.getJSONObject(0);
                        String sealId = (String) jsonObject.get("sealId");
                        //关键字签章
                        paramMap.put("departNumber", "10008");//机构编码 地市编码
                        paramMap.put("departName", "四川");//机构名称 地市名称
                        paramMap.put("applyNo", "zqdzyz");//gert.getApplyNo()//申请人账号
                        paramMap.put("userName", "杨玉峰");//gert.getUserName()//申请人姓名
                        paramMap.put("signseal", "1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
                        paramMap.put("chapterType", "2");//章类型；1.个人章，2 机构章
                        paramMap.put("key", map.get("${PartyB}") + "（盖章）");//关键字内容（PDF 内容中的关键字）
                        paramMap.put("offsetX", 0);//非必填--》Integer 横向偏移量，单位毫米，正数向右偏移， 负数向左偏移
                        paramMap.put("offsetY", -20);//非必填--》Integer 纵向偏移量，单位毫米，正数向上偏移， 负数向下偏移
                        paramMap.put("sealId", sealId);//seal.getSealId()印章 id（生成章返回的编码） sealId
                        File file = new File(ftpUrl + time + "(1).pdf");
                        if (checkFileSize(file, 2, "M")) {
                            String buFile = GetFile.PDFToBase64(file);
                            paramMap.put("reqPdf", buFile);//Pdf 文件传输方式，fileUrl 通过 get 请求 获取文件，reqPdf 通过 base64 字符串 传输文件。两种方式二选一，如果同时传 值，优先使用 reqPdf
                        }
                        Map<String, Object> pdfMap = signService.sealByKey(paramMap);//关键字签章
                        if (pdfMap != null) {
                            if (!"-1".equals(pdfMap.get("resp_code")) && !"1".equals(pdfMap.get("resp_code"))) {
                                JSONObject datas = JSONObject.fromObject(pdfMap.get("datas"));
                                if (datas.getString("fileId").equals("null") || datas.getString("fileId") == null || "".equals(datas.getString("fileId"))) {
                                    reBase64 = (String) datas.get("reBase64");
                                    GetFile.base64StringToPdf(reBase64, ftpUrl + time + "(1).pdf");
                                    type = 1;
                                }
                            }
                        }
                    }
                }
            } catch (Exception e) {
                logger.error("电子协议服务签章失败!请联系系统管理员异常==>" + e.getMessage(),e);
                return "电子协议服务签章失败!请联系系统管理员"+e.getMessage();
            }
            if(type==1&&reBase64.length()>0){
                String jsonObject = JSONHelper.SerializeWithNeedAnnotation(map);
                JSONObject JSON = JSONObject.fromObject(jsonObject);
                Map<String, String> mapTwo = JSON;
                String IBM = "";
                List<Object[]> sone = customClauseContractService.getbumen(user.getRowNo());
                for (int i = 0; i < sone.size(); i++) {
                    IBM = (String) sone.get(i)[2];
                }
                String contractId = IBM + Bpms_riskoff_service.getUnlockedNumber();
                binfo.setCbi_create_date(new Date());
                binfo.setCbi_content(jsonObject);
                binfo.setCbi_contract_num(contractId);
                code = code.substring(0, code.length() - 1);
                info.setContractNumber(ContractNumber);
                info.setStampStatus(type);
                info.setPcode(code);
                info.setContractId(contractId);
                info.setCreateDate(new Date());
                info.setPayType(map.get("${PaymentMethod}"));
                info.setOrgPayMode(map.get("${PayMethod}"));
                info.setContractName(map.get("${contractName}"));
                info.setContractType("EC");// 合同类型电子合同
                info.setOpType(1);
                info.setSTATE(2);
                info.setPayCycle(Integer.parseInt(map.get("${PayDay}")));
                info.setEffectDate(DateUtil.stringToDate(map.get("${EffectiveDate}"), "yyyy-MM-dd"));
                info.setLostEffectDate(DateUtil.stringToDate(map.get("${TerminationDate}"), "yyyy-MM-dd"));
                info.setCreator(String.valueOf(user.getRowNo()));//创建人id
                info.setCreateName(user.getEmployeeName());//创建人名称
                info.setCompanyCode(user.getSystemDept().get(0).getCompanyCode());
                //info.setpDFAttUrl(ftpUrl + time + "(2).pdf");//pdf 地址
                info.setPdfUrl(ftpUrl + time + "(1).pdf");
                info.setUNIT_ID(map.get("${groupCoding}"));
                info.setUNIT_NAME(map.get("${PartyA}"));
                info.setContact(map.get("${Aprincipal}"));
                info.setContactTel(map.get("${AContactInformation}"));
                info.setContactAds(map.get("${contactAddress}"));
                info.setRenewalContractId(contractInfo.getContractId());
                if (!"".equals(json) && json != null && json.length() > 0) {
                    JSONArray jsonArray = JSONArray.fromObject(json);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        ContractProductTariff cpt = new ContractProductTariff();
                        String jsonArrayStr = jsonArray.getString(i);
                        JSONObject obj = JSONObject.fromObject(jsonArrayStr);
                        cpt.setProductName(obj.getString("productName"));
                        cpt.setPostageName(obj.getString("postageName"));
                        cpt.setPostageCode(obj.getString("postageCode"));
                        cpt.setContractNumber(contractId);
                        customClauseContractService.saveContractProductTariff(cpt);
                    }
                }
                ContractRenewal contractRenewal = new ContractRenewal();
                contractRenewal.setContractId(info.getContractId());
                contractRenewal.setContent(renewalContent);
                try {
                    customClauseContractService.saveContractBasisInfo(binfo);
                    customClauseContractService.saveContractInfo(info);
                    customClauseContractService.saveContractRenewal(contractRenewal);
                } catch (Exception e) {
                    e.printStackTrace();
                    logger.error("电子协议服务保存数据失败!请联系系统管理员!数据保存异常==>" + e,e);
                    return "电子协议服务保存数据失败!请联系系统管理员!"+e.getMessage();
                }
                return "0";
            }else{
                logger.error("电子合同签章失败!请联系系统管理员,电子合同签章失败");
                return "电子合同签章失败!请联系系统管理员";
            }
        }catch(Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error(e.getMessage()+"发生未知错误!请联系系统管理员,未知错误",e);
            return "发生未知错误!请联系系统管理员"+e.getMessage();
        }
    }


    /***
     * 纸质合同保存
     */
    public void savePaperContract() {
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
            //System.out.println(request_info);
            JSONObject request_info_objec = JSONObject.fromObject(request_info);
            //System.out.println(request_info_objec.getString("filePdfUrl"));
            ContractBasisInfo binfo = null;
            ContractInfo info = null;
            if (StringUtils.isBlank(request_info_objec.getString("contractNumber"))) {
                binfo = new ContractBasisInfo();
                info = new ContractInfo();
                String ContractNumber = getContractNum();
                String quxian = "";
                String contractId = "";
                List<Object[]> sone = contractUniformityService.getbumen(user.getRowNo());
                for (int j = 0; j < sone.size(); j++) {
                    quxian = (String) sone.get(j)[0];
                    contractId = sone.get(j)[2] + DateUtil.convertDateToString(new Date(), "yyyyMMddHHmmssSSS");
                }
                binfo.setCbi_contract_num(contractId);//合同编号
                info.setContractNumber(ContractNumber);//合同编号
                info.setContractId(contractId);
                info.setRegion(quxian);
            } else {
                binfo = customClauseContractService.queryContractBasisInfo(request_info_objec.getString("contractNumber"));
                info = customClauseContractService.queryContractInfo(request_info_objec.getString("contractNumber"));
            }
            /*if (request_info_objec.getString("filePdfUrl") != null || request_info_objec.getString("filePdfUrl") != "") {
                logger.info("路径"+request_info_objec.getString("filePdfUrl"));
                logger.info("名字"+request_info_objec.getString("filePdfName"));
                //保存到hadoop上
                Map<String, String> rMap = HDFSUtils.readLocalFile2Hadoop(request_info_objec.getString("filePdfUrl"), request_info_objec.getString("filePdfName"));//1.路径，2.名字
                info.setpDFAttUrl(rMap.get("file_path")); //存hadoop的地址
            }*/
            binfo.setCbi_create_date(new Date());
            binfo.setCbi_content(request_info);
            info.setCreateDate(new Date());
            info.setContractName(request_info_objec.getString("title"));
            info.setContractType("PRC");// 合同类型纸质合同
            info.setOpType(Integer.valueOf(request_info_objec.getString("opType")));
            info.setSTATE(4);
            info.setPayType(request_info_objec.getString("PayMethod"));
            info.setOrgPayMode(request_info_objec.getString("PaymentMethod"));
            info.setPayCycle(request_info_objec.getInt("PayDay"));
            info.setEffectDate(DateUtil.stringToDate(request_info_objec.getString("EffectiveDate"), "yyyy-MM-dd"));
            info.setLostEffectDate(DateUtil.stringToDate(request_info_objec.getString("TerminationDate"), "yyyy-MM-dd"));
            info.setCreator(String.valueOf(user.getRowNo()));//创建人id
            info.setCreateName(user.getEmployeeName());//创建人名称
            info.setCompanyCode(user.getSystemDept().get(0).getCompanyCode());
            info.setUNIT_ID(request_info_objec.getString("groupCoding"));
            info.setUNIT_NAME(request_info_objec.getString("groupName"));
            info.setContact(request_info_objec.getString("contacts"));
            info.setContactTel(request_info_objec.getString("contactPhone"));
            info.setContactAds(request_info_objec.getString("contactAddr"));
            String[] attid = request_info_objec.getString("attachmentId1").split("-");
            try {
                customClauseContractService.saveContractBasisInfo(binfo);
                customClauseContractService.saveContractInfo(info);
                Write("{\"code\":\"0\",\"data\":\"" + info.getContractId() + "\"}");
            } catch (Exception e) {
                logger.error("异常==>" + e);
                Write("{\"code\":\"1\"}");
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            }
            this.Write("0");
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("异常==>", e);
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            Write("{\"code\":\"1\"}");
        }

    }

    /**
     * 将目录下除盖章pdf 之外全部删除
     *
     * @param dir 地址
     * @return
     */
    private static boolean deleteDir(String dir, Boolean isQDel) {
        File file = new File(dir);
        boolean delete = true;
        if (file.isDirectory()) {
            String[] children = file.list();
            if (children.length > 0) {
                /**递归删除目录中的子目录下*/
                for (int i = 0; i < children.length; i++) {
                    boolean success = deleteDir(file.getPath() + "/" + children[i], true);
                    if (!success) {
                        return false;
                    }
                }
            }
            delete = file.delete();
        } else {
            if (file.getName().indexOf("-sing") == -1 && !isQDel) {
                delete = file.delete();
                logger.info("删除文件" + delete);
            } else if (isQDel) {
                delete = file.delete();
            } else {
                delete = true;
            }
        }
        return delete;
    }


    private File file;
    private String fileFileName;// 上传文件名
    private String fileContentType;// 上传文件类型

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    public String getFileFileName() {
        return fileFileName;
    }

    public void setFileFileName(String fileFileName) {
        this.fileFileName = fileFileName;
    }

    public String getFileContentType() {
        return fileContentType;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }


    /**
     * 合同提交到hadoop 上去
     *
     * @param
     * @return {@link }
     * @throws 纸质合同附件上传，并且上传到Hadoop上
     * @description: uploadPaper
     * <AUTHOR>
     * @version 1.1.0
     * @data 2020/4/11 17:01
     */
    public void uploadPaper() {
        String id = this.getString("conid");//合同id
        ContractInfo contractInfo = customClauseContractService.queryContractInfo(id);//查询合同信息
        ContractBasisInfo binfo = customClauseContractService.queryContractBasisInfo(id);//根据合同编号查询
        try {
            Map<String, String> rMap = HDFSUtils.readLocalFile2Hadoop(file.getPath(), fileFileName);
            contractInfo.setpDFAttUrl(rMap.get("file_path"));
            contractInfo.setPdfUrl(rMap.get("file_name"));
            contractInfo.setUpdateDate(new Date());
            contractInfo.setAuditDate(new Date());
            contractInfo.setSTATE(4);
            customClauseContractService.saveContractInfo(contractInfo);
            ContractInfo cinfo = customClauseContractService.queryContractInfo(id);//查询合同信息
            if(rMap.get("file_path")==null||"".equals(rMap.get("file_path"))){
                customClauseContractService.deleteEntity(contractInfo);
                customClauseContractService.deleteEntity(binfo);
                Write("{\"code\":\"1\"}");
            }else{
                String token="";
                Map<String,Object> paramMap= new HashMap<>();
                paramMap.put("grant_type","client_credentials");
                paramMap.put("client_id","eom");
                paramMap.put("client_secret","602641ef6ffe4e66972fab36724d88bb");
                paramMap.put("scope","profile:all");
                String str= AttachmentSrv.get138AccessToken(paramMap);
                logger.info("token返回参数："+str);
                JSONObject json = JSONObject.fromObject(str);
                if(json.containsKey("access_token")){
                    token=json.getString("access_token");
                }
                Map<String,Object> paramMapTwo= new HashMap<>();
                paramMapTwo.put("token",token);
                paramMapTwo.put("classId","19");
                paramMapTwo.put("profileSource","3");
                paramMapTwo.put("custCode",cinfo.getUNIT_ID());
                paramMapTwo.put("effTime",DateUtil.convertDateToString(cinfo.getEffectDate(),"yyyy-MM-dd HH:mm:ss"));
                paramMapTwo.put("expTime",DateUtil.convertDateToString(cinfo.getLostEffectDate(),"yyyy-MM-dd HH:mm:ss"));
                paramMapTwo.put("contractName",cinfo.getContractName());
                paramMapTwo.put("contractCode",cinfo.getContractId());
                paramMapTwo.put("signTime",DateUtil.convertDateToString(cinfo.getEffectDate(),"yyyy-MM-dd HH:mm:ss"));
                paramMapTwo.put("fileName",fileFileName);
                paramMapTwo.put("active","1");
                if(file==null){
                    logger.info("这是纸质合同上传到138，但是附件为空了");
                }else{
                    logger.info("这是纸质合同上传到138，文件名称："+fileFileName);
                }
                String strTwo= AttachmentSrv.upload138FileContractByHttpClient(paramMapTwo,file,
                        fileFileName);
                logger.error("纸质合同归档成功信息");
                Write("{\"code\":\"0\"}");
            }
        } catch (Throwable e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("纸质合同归档错误信息"+e.getMessage(),e);
            Write("{\"code\":\"1\"}");
            customClauseContractService.deleteEntity(contractInfo);
            customClauseContractService.deleteEntity(binfo);
        }
        Write("{\"code\":\"0\"}");
    }

    /**
     * 上传合同附件，到hadoop 上并修改对应的id 合同数据
     * @param
     * @return {@link  }
     * @throws
     * @description: hdfsUploadByContId
     * <AUTHOR>
     * @version 1.1.0
     * @data 2020/4/8 17:15  电子合同归档
     */

    public void hdfsUploadByContId() {
        String id = this.getString("id");//合同id
        String waitId = this.getString("waitId");//代办ID
        String orderId = this.getString("orderId");//需求单ID
        String userId = this.getString("userId");//订单经理ID
        String userName = this.getString("userName");//订单经理名称
        String workbenchId = getString("workbenchId");
        String onATaskId = getString("taskId");
        String omsReturnType = getString("omsReturnType");
        String contractSource = getString("contractSource");
        String trueRatio = this.getString("trueRatio");//验真比例
        String probability=this.getString("probability"); //印章校验置信度
        String path = uploadFile(file);
        try {
            ContractInfo contractInfo = customClauseContractService.queryContractInfoById(id);
            if(IS_CONTRACT_SWITCH){
                OmsSellOrder order = omsSellOrderService.getOmsSellOrderById(orderId);
                File filePdf = new File(path);
                String base64 = GetFile.PDFToBase64(filePdf);
                filePdf.delete();
                Result result=CustomClauseContractSrv.getInstance().Mgt_Contractfiling(contractInfo.getContractId(),contractInfo.getContractName(),
                        "4",URLEncoder.encode(base64, "UTF-8"),user.getBossUserName()
                        ,"0","1"
                );
                if(ResultCode.SUCCESS.code()==result.getCode()){
                    //JSONObject root=JSONObject.fromObject(JSONObject.fromObject(result.getData()).get("ROOT"));
                    //JSONObject body=JSONObject.fromObject(root.get("BODY"));
                    JSONObject data=JSONObject.fromObject(result.getData());
                    if("0000".equals(data.get("bizCode"))){
                        //如果需求单不为空的时候这里跳转需求单环节
                        if(order!=null){
                            order.setModifyDate(new Date());
                            if(!"0".equals(omsReturnType)){
                                String IBM = "";
                                List<Object[]> sone = taskService.getCompayIBM(user.getRowNo());
                                for (int i = 0; i < sone.size(); i++) {
                                    IBM = (String) sone.get(i)[2];
                                }
                                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(order.getLinkOrderNo());
                                link.setStatus(1);
                                link.setOper_date(new Date());
                                omsSellOrderService.saveOrupdateOmsOrderLink(link);
                                OmsPretreatmentDate omsPretreatmentDate = omsSellOrderService.QueryOmsPretreatmentDate(order.getCompanyNo(),"5");
                                OmsOrderLink currentLink = new OmsOrderLink();
                                currentLink.setCreator_name(user.getEmployeeName());//发起人
                                currentLink.setCreator_no(user.getRowNo());//发起人工号
                                currentLink.setCreator_date(new Date());//发起人时间(当前时间)
                                currentLink.setOper_name(order.getCreateName());//操作人
                                currentLink.setOper_no(Integer.parseInt(order.getCreateNo()));//操作人工号
                                currentLink.setOper_date(new Date());//操作时间(当前时间)
                                currentLink.setStatus(1);//状态(状态根据环节确定)
                                currentLink.setLinkCode("3");//环节编码或者固定的环节编码
                                currentLink.setLinkName("协议签订");//环节名称
                                currentLink.setOrderNumber(order.getOrderNo());//需求单ID或者编码
                                currentLink.setLinkOrderNo(IBM + taskService.getNumber());
                                currentLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                                omsSellOrderService.saveOrupdateOmsOrderLink(currentLink);
                                Thread.sleep(500);
                                OmsOrderLink nextLink = new OmsOrderLink();
                                nextLink.setCreator_name(user.getEmployeeName());//发起人
                                nextLink.setCreator_no(user.getRowNo());//发起人工号
                                nextLink.setCreator_date(new Date());//发起人时间(当前时间)
                                nextLink.setOper_name(userName);//操作人
                                nextLink.setOper_no(Integer.parseInt(userId));//操作人工号
                                nextLink.setOper_date(new Date());//操作时间(当前时间)
                                nextLink.setStatus(0);//状态(状态根据环节确定)
                                nextLink.setLinkCode("4");//环节编码或者固定的环节编码
                                nextLink.setLinkName("需求确认");//环节名称
                                nextLink.setOrderNumber(order.getOrderNo());//需求单ID或者编码
                                nextLink.setLinkOrderNo(IBM + taskService.getNumber());
                                nextLink.setPretreatment_date(this.getTargetDate(new Date(),Integer.parseInt(omsPretreatmentDate.getPretreatment_date())));
                                omsSellOrderService.saveOrupdateOmsOrderLink(nextLink);

                                OmsLinkDialogue dig = new OmsLinkDialogue();
                                dig.setCreator_name(user.getEmployeeName());
                                dig.setCreator_no(user.getRowNo());
                                dig.setCreator_date(new Date());
                                dig.setOper_name(userName);
                                dig.setOper_no(Integer.parseInt(userId));
                                dig.setOper_date(new Date());
                                dig.setStatus(0);//1已处理，0未处理
                                dig.setLinkOrderNo(nextLink.getLinkOrderNo());
                                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理

                                order.setLinkOrderNo(nextLink.getLinkOrderNo());
                                order.setState("0");
                                if(!"undefined".equals(userId)&&userId!=null&&userId.length()>0&&!"null".equals(userId)){
                                    order.setOperateNo(userId);
                                    order.setOperateName(userName);
                                    order.setOperateDate(new Date());
                                }else{
                                    throw  new Exception("订单经理为空，请确认");
                                }
                                omsSellOrderService.saveOrupdateOmsSellOrder(order);
                                if(!"undefined".equals(onATaskId)&&onATaskId!=null&&onATaskId.length()>0&&!"null".equals(onATaskId)) {
                                    Bpms_riskoff_task bpms_riskoff_task = taskService.updateBpms_riskoff_task("已归档", 2, onATaskId);//修改本条数据
                                    if(null == bpms_riskoff_task){
                                        throw new Exception("未查询到当前任务信息，请确认当前任务是否存在");
                                    }
                                }
                                if(!"undefined".equals(workbenchId)&&workbenchId!=null&&workbenchId.length()>0&&!"null".equals(workbenchId)) {
                                    omsOrderWorkbenchService.updateOmsOrderWorkbench(workbenchId,1);
                                    taskService.updatebpmsRiskoffProcess(workbenchId,2);
                                }else{
                                    throw new Exception("未查询到当前审批工单ID信息，请确认当前审批是否正常");
                                }
                                WaitTask wt = service.queryWaitByTaskId(waitId);//获取待办信息
                                if (wt != null) {
                                    service.updateWait(wt, this.getRequest());
                                }else{
                                    throw new Exception("未查询到待办信息,待办信息为空");
                                }
                                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                                commitOmsSellOrderData(order,Integer.parseInt(userId),order.getTitle(),"1",rdig.getId());
                            }else{
                                OmsOrderLink link = omsSellOrderService.getOmsOrderLinkByLinkOrderNo(order.getLinkOrderNo());
                                OmsLinkDialogue dig = new OmsLinkDialogue();
                                dig.setCreator_name(user.getEmployeeName());
                                dig.setCreator_no(user.getRowNo());
                                dig.setCreator_date(new Date());
                                dig.setOper_name(order.getOperateName());
                                dig.setOper_no(Integer.parseInt(order.getOperateNo()));
                                dig.setOper_date(new Date());
                                dig.setStatus(0);//1已处理，0未处理
                                dig.setLinkOrderNo(link.getLinkOrderNo());
                                dig.setOper_role("ROLE_ODMR");//处理角色订单经理
                                dig.setCreator_role("ROLE_CUMR");//发起角色客户经理
                                OmsLinkDialogue rdig=omsSellOrderService.saveOrupdateOmsLinkDialogue(dig);
                                commitOmsSellOrderData(order,Integer.parseInt(order.getOperateNo()),order.getTitle(),"1",rdig.getId());
                                if(contractInfo!=null){
                                    order.setContractId(contractInfo.getId());
                                }else{
                                    throw  new Exception("合同查询为空，请确认");
                                }
                                omsSellOrderService.saveOrupdateOmsSellOrder(order);
                            }
                        }
                        Write("{\"code\":\"0\",\"msg\":\"附件上传成功\"}");
                    }else{
                        logger.error("电子合同归档返回参数："+result.getData());
                        Write("{\"code\":\"1\",\"msg\":\"附件上传失败\"}");
                    }
                }
            }else {
                if(!"0".equals(path)){
                    Map<String, String> rMap = HDFSUtils.readLocalFile2Hadoop(path, fileFileName);
                    contractInfo.setpDFAttUrl(rMap.get("file_path"));
                    contractInfo.setUpdateDate(new Date());
                    contractInfo.setAuditDate(new Date());
                    contractInfo.setSTATE(4);
                    customClauseContractService.saveContractInfo(contractInfo);
                    File file = new File(path);
                    file.delete();
                    Write("{\"code\":\"0\",\"msg\":\"附件上传成功\"}");
                }else{
                    Write("{\"code\":\"1\",\"msg\":\"附件上传失败\"}");
                }
            }
        } catch (Throwable e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            logger.error("合同归档错误："+e.getMessage(),e);
            e.printStackTrace();
            Write("{\"code\":\"1\",\"msg\":\"附件上传异常\"}");

        }
        /*String id = this.getString("id");//合同id
        try {
            ContractInfo contractInfo = customClauseContractService.queryContractInfoById(id);
            *//*List<File> listFile =new ArrayList<>();
            listFile.add(new File(contractInfo.getPdfUrl()));
            listFile.add(file);
            StorageCfg storageCfg= attachmentService.queryStorageCfg();
            String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
            Long time = System.currentTimeMillis();
            String path = storageCfg.getFileName()+urlDate+time+".zip";
            File headPath = new File(storageCfg.getFileName()+urlDate);//获取文件夹路径
            if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                headPath.mkdirs();
            }
            boolean bl = zipFiles(listFile,new File(path));
            if(bl){*//*
            Map<String, String> rMap = HDFSUtils.readLocalFile2Hadoop(file.getPath(), fileFileName);
            contractInfo.setpDFAttUrl(rMap.get("file_path"));
            contractInfo.setUpdateDate(new Date());
            contractInfo.setAuditDate(new Date());
            contractInfo.setSTATE(4);
            customClauseContractService.saveContractInfo(contractInfo);
                *//*File file = new File(path);
                file.delete();*//*
            Write("{\"code\":\"0\",\"msg\":\"附件上传成功\"}");
            *//*}else{
                Write("{\"code\":\"1\",\"msg\":\"附件上传异常\"}");
            }*//*
        } catch (Throwable e) {
            e.printStackTrace();
            Write("{\"code\":\"1\",\"msg\":\"附件上传异常\"}");
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
        }
        *//*List<File> file = new ArrayList<>();
        file.add(new File("F:\\壁纸幻灯片\\smwlblog.com_0057.jpg"));
        file.add(new File("F:\\壁纸幻灯片\\smwlblog.com_0048.jpg"));
        file.add(new File("F:\\壁纸幻灯片\\smwlblog.com_0036.jpg"));
        file.add(new File("F:\\壁纸幻灯片\\smwlblog.com_0033.jpg"));
        file.add(new File("F:\\壁纸幻灯片\\smwlblog.com_0005 (2).jpg"));
        String pdfPath = "F:\\CDS.pdf";
        try {
            imagesToPdf(pdfPath,file);
            Write("{\"code\":\"1\",\"msg\":\"附件上传异常\"}");
        } catch (Exception e) {
            e.printStackTrace();
        }*/
    }

    /**
     *  需求单生成待办给订单经理
     * @param order 需求单
     * @param userid 分配人员ID
     */
    public void commitOmsSellOrderData(OmsSellOrder order,Integer userid,String title,String isContract,String taskId) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[预受理]" + title);//待办名称
        waitTask.setCreationTime(new Date());//代办生成时间
        waitTask.setUrl("jsp/demandOrderTwo/orderInformation.jsp?id="+order.getId()+"&isContract="+isContract);
        SystemUser USER = systemUserService.getUserInfoRowNo(userid);// 获取下一步处理人信息
        waitTask.setState(waitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(USER.getRowNo());// 处理人id
        waitTask.setHandleUserName(USER.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(USER.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode(OmsSellOrder.OMSSELLORDER);//标识
        waitTask.setTaskId(taskId);
        service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 附件上传：
     */
    //
    public String uploadFile(File file) {
        try {
            if (file != null) {
                Long time = System.currentTimeMillis();
                String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                StorageCfg storageCfg= attachmentService.queryStorageCfg();
                String ftpUrl=storageCfg.getFileName()+urlDate;
                File headPath = new File(ftpUrl);//获取文件夹路径
                if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                String pixstr =FileUpload.getFilePix(fileFileName);
                if(StringUtils.isEmpty(pixstr)){
                    return "0";
                }
                if (FileUpload.upload(ftpUrl, file, time + pixstr)) {
                    return ftpUrl+time + pixstr;
                } else {
                    return "0";
                }
            } else {
                return "0";
            }

        } catch (Exception e) {
            e.printStackTrace();
            return "0";
        }
    }

    /**
     * @param outPdfFilepath 生成pdf文件路径
     * @param imageFiles     需要转换的图片File类Array,按array的顺序合成图片
     */
    public static void imagesToPdf(String outPdfFilepath, List<File> imageFiles) throws Exception {
        logger.info("进入图片合成PDF工具方法");
        File file = new File(outPdfFilepath);
        // 第一步：创建一个document对象。
        Document document = new Document();
        //设置PDF页边距 为0的时候是没有边距的，数字越大边距越大
        document.setMargins(0, 0, 0, 0);
        // 第二步：
        // 创建一个PdfWriter实例，创建空白PDF文档
        PdfWriter.getInstance(document, new FileOutputStream(file));
        // 第三步：打开文档。
        document.open();
        // 第四步：在文档中增加图片。
        for (int i = 0; i < imageFiles.size(); i++) {
            if (imageFiles.get(i).getName().toLowerCase().endsWith(".bmp")
                    || imageFiles.get(i).getName().toLowerCase().endsWith(".jpg")
                    || imageFiles.get(i).getName().toLowerCase().endsWith(".jpeg")
                    || imageFiles.get(i).getName().toLowerCase().endsWith(".gif")
                    || imageFiles.get(i).getName().toLowerCase().endsWith(".png")) {
                String temp = imageFiles.get(i).getPath();
                Image img = Image.getInstance(temp);
                img.setAlignment(Image.ALIGN_CENTER);
                img.scaleAbsolute(img.getWidth(), img.getHeight());//设定图片尺寸
                // 根据图片大小设置页面，一定要先设置页面，再newPage（）否则无效 大小可自定义
                document.setPageSize(new Rectangle(img.getWidth(), img.getHeight()));
                document.newPage();
                document.add(img);
            }
        }
        // 第五步：关闭文档。
        document.close();
        logger.info("图片合成PDF完成");
    }


    public boolean zipFiles(List<File> srcFiles, File zipFile) {
        // 创建 FileOutputStream 对象
        FileOutputStream fileOutputStream = null;
        // 创建 ZipOutputStream
        ZipOutputStream zipOutputStream = null;
        // 创建 FileInputStream 对象
        FileInputStream fileInputStream = null;
        try {
            // 实例化 FileOutputStream 对象
            fileOutputStream = new FileOutputStream(zipFile);
            // 实例化 ZipOutputStream 对象
            zipOutputStream = new ZipOutputStream(fileOutputStream);
            // 创建 ZipEntry 对象
            ZipEntry zipEntry = null;
            // 遍历源文件数组
            for (int i = 0; i < srcFiles.size(); i++) {
                // 将源文件数组中的当前文件读入 FileInputStream 流中
                fileInputStream = new FileInputStream(srcFiles.get(i));
                // 实例化 ZipEntry 对象，源文件数组中的当前文件
                String fileName = "";
                String ps = FileUpload.getFilePix(srcFiles.get(i).getName());
                if (".tmp".equals(FileUpload.getFilePix(srcFiles.get(i).getName()))) {
                    fileName = fileFileName;
                } else {
                    fileName = srcFiles.get(i).getName();
                }
                zipEntry = new ZipEntry(fileName);
                zipOutputStream.putNextEntry(zipEntry);
                // 该变量记录每次真正读的字节个数
                int len;
                // 定义每次读取的字节数组
                byte[] buffer = new byte[1024];
                while ((len = fileInputStream.read(buffer)) > 0) {
                    zipOutputStream.write(buffer, 0, len);
                }
            }
            zipOutputStream.closeEntry();
            zipOutputStream.close();
            fileInputStream.close();
            fileOutputStream.close();
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }


    /***
     *  根据文件路径删除文件和文件夹
     */
    public void deleFilePath() {
        String path = this.getString("path");
        try {
            if (!StringUtils.isBlank(path)) {
                boolean isDele = deleteDir(EncryptionUtils.decrypt(path), false);
                Write("" + isDele);
            }
            Write("false");
        } catch (Exception e) {
            Write("false");
        }
    }

    /***
     *  根据文件路径删除文件和文件夹
     */
    public void deletePaperFilePath() {
        String path = this.getString("path");
        try {
            String serverPath = FileUpload.getFtpURL();
            String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
            path = path.substring(0, path.lastIndexOf("/"));
            if (path.indexOf("NEWCONTR/PRC/" + urlDate) > -1 || path.indexOf("NEWCONTR/EC/" + urlDate) > -1 || path.indexOf("NEWCONTR/ECSEE/" + urlDate) > -1) {
                FileUtil.delFolder(serverPath + path);
                Write("{\"code\":\"0\"}");
                return;
            }
            Write("{\"code\":\"1\"}");
        } catch (Exception e) {
            Write("{\"code\":\"1\"}");
        }
    }

    /*
     * 查询我的合同信息 列表
     * @description: queryMyContracts
     * <AUTHOR>
     * @version 1.1.0
     * @data 2020/3/6 16:06
     * @param
     * @return {@link void}
     * @throws
     * gcy 2020-9-29 多添加两个查询条件
     */
    public void queryMyContracts() {
        Integer pageNo = getInteger("pageNo");
        Integer pageSize = getInteger("pageSize");
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        String orderNum = getString("orderNum");
        String createDate = getString("createDate");
        String endDate = getString("endDate");
        Integer status = getInteger("status");

        String unitId = getString("unitId");//集团280编号
        String unitName = getString("unitName");//集团名字

        String pageStr = customClauseContractService.gainQuickQuery(page, orderNum, createDate, endDate, Integer.valueOf(user.getRowNo()), getRoleLevel(), status, unitId, unitName);
        Write(pageStr);
    }

    public void queryArchiveMyContracts() {
        Integer pageNo = getInteger("pageNo");
        Integer pageSize = getInteger("pageSize");
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        String orderNum = getString("orderNum");
        String createDate = getString("createDate");
        String endDate = getString("endDate");
        Integer status = getInteger("status");

        String unitId = getString("unitId");//集团280编号
        String unitName = getString("unitName");//集团名字

        String pageStr = customClauseContractService.queryArchiveMyContracts(page, orderNum, createDate, endDate, Integer.valueOf(user.getRowNo()), status, unitId, unitName);
        Write(pageStr);
    }

    public void queryApproveMyContracts() {
        Integer pageNo = getInteger("pageNo");
        Integer pageSize = getInteger("pageSize");
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        String orderNum = getString("orderNum");
        String createDate = getString("createDate");
        String endDate = getString("endDate");
        String unitId = getString("unitId");//集团280编号
        String unitName = getString("unitName");//集团名字
        String pageStr = customClauseContractService.gainQuickQueryApprove(page, orderNum, createDate, endDate, Integer.valueOf(user.getRowNo()), unitId, unitName);
        Write(pageStr);
    }

    /**
     * 查询合同模板
     *
     * @param
     * @return {@link  }
     * @throws
     * @description: Temp
     * <AUTHOR>
     * @version 1.1.0
     * @data 2020/3/10 15:17
     */
    @Deprecated
    public void selectContractsTemp() {
        Integer pageNo = getInteger("page");
        Integer pageSize = getInteger("limit");
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        String contractName = getString("contractName");
        Write(customClauseContractService.queryContractTemplate(page, contractName, super.user.getSystemDept().get(0).getCompanyCode()));
    }

    /**
     * 根据合同编号查询 合同基本信息
     *
     * @param
     * @return {@link }
     * @throws
     * @description: queryByContractCodeInfo
     * <AUTHOR>
     * @version 1.1.0
     * @data 2020/3/13 15:26
     */
    @Deprecated
    public void queryByContractCodeInfo() {
        String code = this.getString("contCode");
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryByContractBasisInfo(code)));
    }

    //附件下载，根据合同编号下载附件
    public void downloadEnclosure() {
        String code = this.getString("contCode");
        Map<String, byte[]> files = new HashMap();
        try {
            if (!StringUtils.isBlank(code)) {
                ContractInfo contractInfo = customClauseContractService.queryContractInfoById(code);
                HttpServletResponse response = this.getResponse();
                byte[] data = null;
                if (StringUtils.isEmpty(contractInfo.getpDFAttUrl())) {
                    data = FileUtil.getContent(contractInfo.getPdfUrl());
                } else {
                    data = HDFSUtils.readFileToByte(contractInfo.getpDFAttUrl());
                    //data = FileUtil.getContent(contractInfo.getPdfUrl());
                }
                Long time = System.currentTimeMillis();
                String pdfname = time+".pdf";
                logger.info("合同下载名称："+pdfname);
                response.reset();
                response.setHeader("Content-Disposition", "attachment; filename=\"" + pdfname + "\"");
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream;charset=UTF-8");
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();
                Write("success");
            } else {
                logger.error("参数错误：" + code);
                Write("error");
            }
        } catch (Exception ex) {
            logger.error("下载合同："+ex.getMessage(),ex);
            Write("error");
        }
    }

    /**
     * 根据产品类型查询所有产品信息
     */
    public void queryProductOrFuse() {
        String prod_type = this.getString("type");
        String classifyCode = this.getString("classifyCode");
        System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryProductOrFuse(prod_type, classifyCode)));
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryProductOrFuse(prod_type, classifyCode)));
    }

    /**
     * 根据产品类型查询所有产品信息
     */
    public void queryProductinfos() {
        String prod_type = this.getString("type");
        System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryProductOrFuse(prod_type)));
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(customClauseContractService.queryProductOrFuse(prod_type)));
    }

    /**
     * 根据产品类型查询产品
     */
    public void queryProdList() {
        String prodType = this.getString("prodType");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.queryProdList(prodType));
        Write(res);
    }

    /**
     * 根据主产品查询子产品信息
     */
    public void querySubProdList() {
        String prodCode = this.getString("prodCode");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.querySubProdList(prodCode));
        Write(res);
    }

    /**
     * 根据产品查询产品属性
     * 查询表头
     */
    public void queryPropList() {
        String prodCode = this.getString("prod_Code");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.queryPropList(prodCode));
        Write(res);
    }

    /**
     * 根据融合产品下组合产品属性
     */
    public void queryProdSubPropList() {
        String prodCode = this.getString("prodCode");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.queryProdSubPropList(prodCode));
        Write(res);
    }

    /**
     * 获取属性的初始值
     */
    public void queryIntoVal() {
        String prodCode = this.getString("prodCode");
        String propCode = this.getString("propCode");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.queryIntoVal(prodCode, propCode));
        Write(res);
    }

    /**
     * 获取属性的初始值
     * 查询表格内容
     */
    public void queryIntoValTwo() {
        String prodCode = this.getString("prodCode");
        String propCode = this.getString("propCode");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.queryIntoValTwo(prodCode, propCode));
        Write(res);
    }

    /**
     * 获取联动的对应值的变化
     */
    public void queryDynamicVal() {
        String defaultVal_Code = this.getString("defaultVal_Code");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.queryDynamicVal(defaultVal_Code));
        Write(res);
    }

    /**
     * 根据code获取产品分类信息
     */
    public void queryPmsProductLabel() {
        String labelid = this.getString("labelid");
        String res = com.xinxinsoft.utils.JSONHelper.SerializeWithNeedAnnotationDateFormats(customClauseContractService.queryPmsProductLabel(labelid));
        Write(res);
    }


    /**
     * 预览PDF文件
     *
     * @throws IOException
     */
    public void viewPDFFile() {
        try {
            String pdfUrl = getString("pdfUrl");
            String dirUrl = getString("dirUrl");
            HttpServletResponse response = ServletActionContext.getResponse();
            response.setContentType("application/pdf");//"F:\\FTP\\NEWCONTR\\ECSEE\\1598949191309\\1598949191309(1).pdf"
            FileInputStream in = new FileInputStream(pdfUrl);
            OutputStream out = response.getOutputStream();
            byte[] b = new byte[1024];
            while ((in.read(b)) != -1) {
                out.write(b);
            }
            out.flush();
            in.close();
            out.close();
            if (!"".equals(dirUrl) && dirUrl != null) {
                deleteDir(dirUrl.substring(0, dirUrl.lastIndexOf("/")), true);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 查看PDF文件
     * @throws IOException
     */
    public void viewPDFFileById() {
        try {
            String id = getString("id");
            ContractInfo contractInfo = customClauseContractService.queryContractInfoById(id);
            //if (contractInfo.getContractType().equals("PRC")) {
                HttpServletResponse response = ServletActionContext.getResponse();
                if(contractInfo.getpDFAttUrl()!=null){
                    byte[] data = HDFSUtils.readFileToByte(contractInfo.getpDFAttUrl());
                    response.setContentType("application/pdf");
                    OutputStream out = response.getOutputStream();
                    out.write(data);
                    out.flush();
                    out.close();
                }else{
                    response.setContentType("application/pdf");
                    FileInputStream in = new FileInputStream(contractInfo.getPdfUrl());
                    OutputStream out = response.getOutputStream();
                    byte[] b = new byte[1024];
                    while ((in.read(b)) != -1) {
                        out.write(b);
                    }
                    out.flush();
                    in.close();
                    out.close();
                }
            /*} else {
                HttpServletResponse response = ServletActionContext.getResponse();
                response.setContentType("application/pdf");
                FileInputStream in = new FileInputStream(contractInfo.getPdfUrl());
                OutputStream out = response.getOutputStream();
                byte[] b = new byte[1024];
                while ((in.read(b)) != -1) {
                    out.write(b);
                }
                out.flush();
                in.close();
                out.close();
            }*/
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("预览合同：" + e.getMessage(), e);
        }
    }

    /**
     * 初始化模板表数据
     *
     * @throws Exception
     */
    public void buildTemplate() {
        try {
            List<Map<String, String>> list = customClauseContractService.querySystemCompany();
            for (int i = 0; i < list.size(); i++) {
                String code = list.get(i).get("COMPANY_CODE");
                CityContractTemplate city = new CityContractTemplate();
                city.setCity_code(code);
                city.setContract_name("htywmb_" + code + "_20191115.docx");
                city.setCreation_time(new Date());
                city.setContract_state(1);
                customClauseContractService.saveCityContractTemplate(city);
            }
            Write("0");
        } catch (Exception e) {
            e.printStackTrace();
            Write("1");
        }
    }

    /**
     * 判断文件大小
     *
     * @param file 文件
     * @param size 限制大小
     * @param unit 限制单位（B,K,M,G）
     * @return
     */
    public boolean checkFileSize(File file, int size, String unit) {
        long len = file.length();
        double fileSize = 0;
        if ("B".equals(unit.toUpperCase())) {
            fileSize = (double) len;
        } else if ("K".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1024;
        } else if ("M".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1048576;
        } else if ("G".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1073741824;
        }
        if (fileSize > size) {
            return false;
        }
        return true;
    }

    /*public void getPmsProductInfo() {
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            PmsProductInfo ppi = customClauseContractService.queryPmsProductInfo(id);
            Write(JSONHelper.SerializeWithNeedAnnotation(ppi));
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
        }
    }*/

    public void getTableValues() {
        Map<String, Object> map = new HashMap<>();
        try {
            String prcId = getString("prcId");
            String id = getString("id");
            String labelId = getString("labelId");
            PmsProductInfo ppi = customClauseContractService.queryPmsProductInfo(id,labelId);
            List<TariffHeader> proList = customClauseContractService.getTariffHeader(prcId);
            List<TariffFormInformation> defList = customClauseContractService.getTariffFormInformation(prcId);
            if (proList.size() > 0 && defList.size() > 0) {
                map.put("code", 1);
                map.put("data", JSONHelper.SerializeWithNeedAnnotation(ppi));
                map.put("msg", "");
            } else {
                map.put("code", -1);
                map.put("data", "");
                map.put("msg", "");
            }
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
        }
    }

    /**
     * gcy
     * 图片转pdf
     * 根据前端传过来的图片id集合
     * 获取图片地址转换成pdf
     */
    public void addpDf() {
        //前端接受附件id集合
        String attachmentId = getString("attachmentId");
        //判断是否上传了附件,获取前台提交的附件Id；
        String[] fileId = attachmentId.split(",");
        //创建个存放图片地址的集合
        List<String> imageUrlList = new ArrayList();
        //根据附件id循环获取
        String path = "";
        for (int i = 0; i < fileId.length; i++) {
            Attachment attachment = attachmentService.getAttachment(fileId[i]);//根据附件id查询路径
            StorageCfg storageCfg = attachmentService.queryStorageCfgById(attachment.getVersion());//查询实时路径
            path = storageCfg.getFileName() + attachment.getAttachmentUrl();//拼接路径
            imageUrlList.add(path); //添加图片地址到集合
        }
        try {
            //存放pdf文件的路径
            //获取毫秒数
            Long time = System.currentTimeMillis();
            //根据当天日期生成文件夹：名称：
            String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
            String ftpUrl = "";
            String url = "";
            StorageCfg storageCfg = attachmentService.queryStorageCfg();
            ftpUrl = storageCfg.getFileName() + urlDate + time + ".pdf";//pdf文件路径
            //ftpUrl = "D:\\" + urlDate + time + ".pdf";//pdf文件路径
            //url = "D:\\" + urlDate;//pdf文件夹路径
            File headPath = new File(url);//获取文件夹路径
            if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                headPath.mkdirs();
            }
            String pdfUrl = ftpUrl;//存放路径
            File file = pdf(imageUrlList, pdfUrl);//生成pdf
            file.createNewFile();
            //把pdf文件路径存入合同表
            System.out.println("pdf路径为" + pdfUrl);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * gcy
     * 图片转pdf2
     *
     * @param h
     * @param w
     * @return
     */
    public static int getPercent2(float h, float w) {
        int p = 0;
        float p2 = 0.0f;
        p2 = 530 / w * 100;
        p = Math.round(p2);
        return p;
    }

    public static File pdf(List<String> imageUrllist, String pdfUrl) {
        //new一个pdf文档
        Document doc = new Document(PageSize.A4, 20, 20, 20, 20);
        try {
            //pdf写入
            PdfWriter.getInstance(doc, new FileOutputStream(pdfUrl));
            //打开文档
            doc.open();
            //遍历集合，将图片放在pdf文件
            for (int i = 0; i < imageUrllist.size(); i++) {
                //在pdf创建一页：此处为每一张图片是pdf文件的一页
                doc.newPage();
                //通过文件路径获取image
                Image png1 = Image.getInstance(imageUrllist.get(i));
                // 获取图片的宽高
                float imageHeight = png1.getScaledHeight();
                float imageWidth = png1.getScaledWidth();
                // 设置页面宽高与图片一致
                Rectangle rectangle = new Rectangle(imageWidth, imageHeight);
                doc.setPageSize(rectangle);
                // 图片居中
                png1.setAlignment(Image.ALIGN_CENTER);
                // 新建一页添加图片
                doc.newPage();
                doc.add(png1);
            }
            doc.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        //输出流
        File mOutputPdfFile = new File(pdfUrl);
        if (!mOutputPdfFile.exists()) {
            mOutputPdfFile.deleteOnExit();
            return null;
        }
        //反回文件输出流
        return mOutputPdfFile;
    }


    /**
     * @author: liyang
     * @date: 2022/7/20 16:10
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 纸质合同归档
     */
    public void uploadFilePhoto() {
        Map<String, Object> map = new HashMap<>();
        try {
            Integer fileCount = getInteger("fileCount");
            Integer fileNum = getInteger("fileNum");
            logger.info((fileNum+1)==fileCount);
            String attachmentId = getString("attachmentId");
            String id = this.getString("id");//合同id
            String trueRatio = this.getString("trueRatio");//验真比例
            String probability=this.getString("probability"); //印章校验置信度
            ContractInfo contractInfo = customClauseContractService.queryContractInfoById(id);
            if(contractInfo!=null){
                if (file != null && fileFileName != null) {
                    Long time = System.currentTimeMillis();
                    String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                    String ftpUrl = "";
                    StorageCfg storageCfg = attachmentService.queryStorageCfg();
                    ftpUrl = storageCfg.getFileName() + urlDate;
                    File headPath = new File(ftpUrl);//获取文件夹路径
                    if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                        headPath.mkdirs();
                    }
                    String pixstr = FileUpload.getFilePix(fileFileName);
                    String photoName = time + pixstr; //防止太快，文件名加上了循环的i
                    String attId="";
                    if (FileUpload.upload(ftpUrl, file, photoName)) {
                        final Attachment attachmentEntity = new Attachment();
                        attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                        attachmentEntity.setAttachmentUrl(urlDate+time + pixstr);
                        attachmentEntity.setUploadDate(new Date());
                        attachmentEntity.setRealName(fileFileName);
                        attachmentEntity.setVersion(storageCfg.getId());
                        attachmentEntity.setUploadUser(user);
                        attId = this.attachmentService.addEntity(attachmentEntity);
                    }

                    if((fileNum+1)==fileCount&&attId.length()>0){
                        if(fileCount==1){
                            attachmentId=attId;
                        }else{
                            attachmentId=attachmentId+"_"+attId;
                        }
                        List<String> imageUrlList = new ArrayList();
                        String[] idStr = attachmentId.split("_");
                        for(int i=0;i<idStr.length;i++){
                            final Attachment entity = attachmentService.getAttachmentById(idStr[i]);
                            StorageCfg photoStorageCfg = attachmentService.queryStorageCfgById(entity.getVersion());
                            imageUrlList.add(photoStorageCfg.getFileName() + entity.getAttachmentUrl());
                        }
                        String name = time + ".pdf";
                        String pdfUrl = storageCfg.getFileName() + urlDate + name;//pdf文件路径
                        String pdfHead = storageCfg.getFileName() + urlDate;//pdf文件夹路径
                        File pdfHeadPath = new File(pdfHead);//获取文件夹路径
                        if (!pdfHeadPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                            pdfHeadPath.mkdirs();
                        }
                        File pdfFile = pdf(imageUrlList, pdfUrl);//生成pdf
                        pdfFile.createNewFile();
                        String base64 = GetFile.PDFToBase64(pdfFile);
                        pdfFile.delete();
                        for(int j=0;j<imageUrlList.size();j++){
                            File imgFile = new File(imageUrlList.get(j));
                            imgFile.delete();
                        }
                        Result result=CustomClauseContractSrv.getInstance().Mgt_Contractfiling(contractInfo.getContractId(),contractInfo.getContractName(),
                                "4",URLEncoder.encode(base64, "UTF-8"),user.getBossUserName()
                                ,trueRatio,probability
                                );
                        if(ResultCode.SUCCESS.code()==result.getCode()){
                            JSONObject data=JSONObject.fromObject(result.getData());
                            if("0000".equals(data.get("bizCode"))){
                                map.put("code", 1);
                                map.put("data", "");
                                map.put("type", 1);
                                map.put("msg", data.get("bizDesc"));
                                Write(JSONHelper.SerializeWithNeedAnnotation(map));
                            }else{
                                throw new Exception(data.getString("bizDesc"));
                            }
                        }
                    }else{
                        if(attId.length()>0){
                            String attidStr = "";
                            if(attachmentId!=null&&!"".equals(attachmentId)&&
                                    !"undefined".equals(attachmentId)&&!"null".equals(attachmentId)){
                                attidStr =attachmentId+"_"+attId;
                            }else{
                                attidStr=attId;
                            }
                            map.put("code", 1);
                            map.put("data", attidStr);
                            map.put("type", 0);
                            map.put("msg", "");
                            Write(JSONHelper.SerializeWithNeedAnnotation(map));
                        }else{
                            throw new Exception("第"+fileNum+1+"张图片上传失败");
                        }
                    }
                }
            }else{
                throw new Exception("未查询到合同工单信息，请确认");
            }
        } catch (Exception e) {
            logger.error("电子合同拍照上传归档："+e.getMessage(),e);
            map.put("code", -1);
            map.put("data", "");
            map.put("msg", "电子合同拍照上传归档失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }

    //删除指定文件夹下所有文件
    //param path 文件夹完整绝对路径
    public static boolean delAllFile(String path) {
        boolean flag = false;
        File file = new File(path);
        if (!file.exists()) {
            return flag;
        }
        if (!file.isDirectory()) {
            return flag;
        }
        String[] tempList = file.list();
        File temp = null;
        for (int i = 0; i < tempList.length; i++) {
            if (path.endsWith(File.separator)) {
                temp = new File(path + tempList[i]);
            } else {
                temp = new File(path + File.separator + tempList[i]);
            }
            if (temp.isFile()) {
                temp.delete();
            }
            if (temp.isDirectory()) {
                delAllFile(path + "/" + tempList[i]);//先删除文件夹里面的文件
                delFolder(path + "/" + tempList[i]);//再删除空文件夹
                flag = true;
            }
        }
        return flag;
    }

    //删除文件夹
    //param folderPath 文件夹完整绝对路径
    public static void delFolder(String folderPath) {
        try {
            delAllFile(folderPath); //删除完里面所有内容
            String filePath = folderPath;
            filePath = filePath.toString();
            java.io.File myFilePath = new java.io.File(filePath);
            myFilePath.delete(); //删除空文件夹
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * @author: liyang
     * @date: 2021/6/18 16:18
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 获取properties文件值
     */
    /*public void getProperties(){
        try {
            //使用Class类的getResourceAsStream()方法获取文件 并返回InputStream类的一个对象
            InputStream inputStream = ServletActionContext.getServletContext().getResourceAsStream("/WEB-INF/classes/WebService-config.properties");
            //实例化Properties类
            Properties properties = new Properties();
            //调用load()方法加载properties文件，load里面传入InputSteam类型的参数或者Reader类型的参数
            properties.load(inputStream);
            String isSwitch = properties.getProperty("IS_CONTRACT_SWITCH");
        }catch (Exception e){
            logger.error(e.getMessage(),e);
        }
    }*/

    /**
     * @author: liyang
     * @date: 2021/6/21 16:25
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO json转map
     */
    private static Map<String, String> ConvertJSONToMap(String json){
        JSONObject obj = JSONObject.fromObject(json);
        Map<String, String> map = new HashMap<>();
        Iterator it_color = obj.keys();
        while (it_color.hasNext()) {
            String key = it_color.next().toString();
            String value = obj.getString(key);
            map.put(key,value);
        }
        return map;
    }

    /**
     * @author: liyang
     * @date: 2021/8/20 10:31
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 合同到期提醒服务
     */
    public void mgtContractExpireRemindCreate() {
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            ContractInfo con = customClauseContractService.queryContractInfoById(id);
            Result result= CustomClauseContractSrv.getInstance().Mgt_contractExpireRemindCreate(con,user);
            if(ResultCode.SUCCESS.code()==result.getCode()){
                JSONObject body=JSONObject.fromObject(result.getData());
                if(body.getInt("RspCode")==0){
                    this.Write("0");
                }else{
                    logger.error("合同提醒服务错误："+body.get("RspDesc"));
                    this.Write("合同提醒服务错误："+body.get("RspDesc"));
                }
            }
        } catch (Exception e) {
            logger.info(e.getMessage(), e);
            this.Write("合同提醒服务错误："+e.getMessage());
        }
    }

    /**
     * @author: liyang
     * @date: 2021/8/31 14:51
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询当前登录人员的归属权限
     */
    public Integer getUserJurisdiction(SystemUser user){
        Map<String,Object> map =new HashMap<>();
        int type=0;
        try {
            List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
            if(userlistMap.size() > 0){
                if (userlistMap.get(0).get("COUNTY_NAME").contains("分公司")) {
                    logger.info("这是区县");
                    type=1;
                } else {
                    if ("00".equals(userlistMap.get(0).get("COMPANY_CODE"))) {
                        logger.info("这是省公司");
                        type= 3;
                    } else {
                        logger.info("这是市公司");
                        type= 2;
                    }
                }
            }
        }catch (Exception e){
            logger.error("查询人员权限错误信息："+e.getMessage(),e);
            type= 0;
        }
        return type;
    }

    // 获取线条值
    public JSONArray findTransition(String processId) {
        try {
            logger.info("所查询的流程ID：=======》" + processId);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
            JSONArray jArray = new JSONArray();
            for (String outcome : setlist) {
                JSONObject obj = new JSONObject();
                obj.put("transitionName", outcome);
                jArray.add(obj);
            }
            return jArray;
        } catch (Exception e) {
            logger.error("电子合同流程信息获取异常====》" + e.getMessage(),e);
            return new JSONArray();
        }
    }

    /**
     * @author: liyang
     * @date: 2021/10/12 17:19
     * @Version: 1.0
     * @param: 
     * @return: 
     * @Description: TODO 处理页面查询数据
     */
    public void getContractInfoContent(){
        Map<String,Object> map =new HashMap<>();
        try{
            String id = getString("id");
            ContractInfo contractInfo = customClauseContractService.queryContractInfoById(id);
            List<Map<String, String>> userlistMap = structureOfPersonnelService.getUserPowers(user);
            GroupCustomer groupCustomer = customClauseContractService.getGroupCustomer(contractInfo.getUNIT_ID());
            MobileCorporation mobileCorporation = customClauseContractService.getMobileCorporationObj(userlistMap.get(0).get("COMPANY_CODE"));
            List<ContractProductTariff> list=customClauseContractService.getContractProductTariffList(contractInfo.getContractId());
            Map<String,Object> jsonMap =new HashMap<>();
            Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);
            if(process!=null){
                List<Bpms_riskoff_task> taskList = taskService.getPublicEntityTaskList(contractInfo.getId());//任务信息
                if(taskList.size()>0){
                    jsonMap.put("tasklist",taskList);
                }else{
                    jsonMap.put("tasklist","");
                }
            }else{
                jsonMap.put("tasklist","");
            }

            if(list.size()>0){
                jsonMap.put("ContractProductTariffList",list);
            }else{
                jsonMap.put("ContractProductTariffList","");
            }

            if(mobileCorporation!=null){
                jsonMap.put("MobileCorporation",mobileCorporation);
            }else{
                jsonMap.put("MobileCorporation","");
            }

            if(contractInfo!=null){
                jsonMap.put("ContractInfo",contractInfo);
            }else{
                jsonMap.put("ContractInfo",contractInfo);
            }

            if(groupCustomer!=null){
                jsonMap.put("GroupCustomer", groupCustomer);
            }else{
                jsonMap.put("GroupCustomer", groupCustomer);
            }
            map.put("code",1);
            map.put("data",jsonMap);
            map.put("msg","查询数据成功!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("电子合同查询数据失败："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询数据失败!");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    public void innitLateFeeMoneyData(){
        try {
            customClauseContractService.InitializeTransferFourCitiesData();//合同金额
            customClauseContractService.InitializeTransferFourCitiesDataTwo();//优惠比例
            Write("初始化成功");
        }catch (Exception e){
            logger.error("资金认领初始化金额和滞纳金金额错误："+e.getMessage(),e);
            Write("初始化失败");
        }
    }

    public void getInformationTemplate(){
        Map<String,Object> map = new HashMap<>();
        try{
            String productCode=getString("productCode");
            String priceCode = getString("priceCode");
            String labelId = getString("labelId");
            Map<String,String> approval= customClauseContractService.getApprovalElements(productCode,labelId);//查询产品审批要素
            ProductKeyInformation product= customClauseContractService.getProductKeyInformation(productCode,labelId);//查询产品配置的关键信息
            PriceKeyInformation price= customClauseContractService.getPriceKeyInformation(priceCode,labelId);//查询资费配置的关键信息
            if(product!=null){
                List<SubProductKeyInformation> subProduct= customClauseContractService.getSubProductKeyInformationList(product.getProductCode(),product.getLabelId());//查询产品配置的关键信息
                map.put("SubProductKeyInformation",subProduct);
            }else{
                map.put("SubProductKeyInformation","");
            }
            map.put("code",1);
            map.put("ApprovalElements",approval);
            map.put("ProductKeyInformation",product);
            map.put("PriceKeyInformation",price);
            map.put("msg","查询数据成功");
            System.out.println(JSONHelper.SerializeWithNeedAnnotation(map));
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }catch (Exception e){
            logger.error("查询电子合同配置详情信息错误"+e.getMessage(),e);
            map.put("code",-1);
            map.put("msg","查询数据失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }

    public void getContractMobileAccount(){
        String number = getString("number");
        List<Map<String,String>> userMap=customClauseContractService.getVwUserinfoByRowno(String.valueOf(user.getRowNo()));
        List<Map<String, String>> list= customClauseContractService.getContractMobileAccount(userMap.get(0).get("COMPANY_CODE"),number);
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(list));
    }

    public void getContractMobileAccountCount(){
        Map<String,Object> map = new HashMap<>();
        try{
            String number = getString("number");
            List<Map<String,String>> userMap=customClauseContractService.getVwUserinfoByRowno(String.valueOf(user.getRowNo()));
            List<Map<String, String>> list= customClauseContractService.getContractMobileAccountCount(userMap.get(0).get("COMPANY_CODE"),number);
            map.put("code",1);
            map.put("data",list.size());
            map.put("msg","查询成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询移动公司存在的账户错误"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询数据失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }



    /**
     * @author: liyang
     * @date: 2022/3/22 14:30
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 判断合同新流程
     */
    public void getContractApprovalCondition(){
        Map<String,Object> map = new HashMap<>();
        try{
            String productJson = getString("productJson");
            String approvalPostage = getString("approvalPostage");
            String processJudgment = getString("processJudgment");
            if("0".equals(processJudgment)){
                JSONArray arry = JSONArray.fromObject(productJson);
                JSONObject obj = JSONObject.fromObject(arry.get(0));
                int companyLevel = getUserJurisdiction(user);
                List<Map<String,String>>  approvalCondition = customClauseContractService.getApprovalCondition(
                        obj.getString("PROD_CODE"),obj.getString("LABELID"),companyLevel);
                JSONArray array = JSONArray.fromObject(approvalPostage);
                JSONObject jsonObject = JSONObject.fromObject(array.get(0));
                Iterator it = jsonObject.keys();
                Map<String, Object> variables = new HashMap<>();
                while(it.hasNext()){
                    String key = (String) it.next();// 获得key
                    String value = jsonObject.getString(key);//获得value
                    variables.put(key,value);
                }
                boolean bl=false;
                for(int i=0;i<approvalCondition.size();i++){
                    ScriptEngineManager manager = new ScriptEngineManager();
                    ScriptEngine engine = manager.getEngineByName("js");
                    for(String key:variables.keySet()){
                        String value = variables.get(key).toString();
                        engine.put(key, value);
                    }
                    Object result = engine.eval(approvalCondition.get(i).get("CONTENT").trim());
                    bl=Boolean.parseBoolean(result.toString());
                    if(bl){
                        break;
                    }
                    /*bl = ProcessUtils.processJudge(variables,approvalCondition.get(i).get("CONTENT"));
                    if(bl){
                        break;
                    }*/
                }
                map.put("code",1);
                map.put("data",bl);
                map.put("msg","查询流程数据成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }else{
                map.put("code",1);
                map.put("data",false);
                map.put("msg","查询流程数据成功");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
            }
        }catch (Exception e){
            logger.error("查询移动公司存在的账户错误"+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询流程数据失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }

    public Integer contract_signature(ContractInfo contractInfo){
        /*int type=0;
        try {
            SystemUser user = systemUserService.getByUserInfoRowNo(Integer.parseInt(contractInfo.getCreator()));
            Map<String, Object> paramMap = new HashMap<>();
            Map<String, Object> map1 = new HashMap<>();
            if ("00".equals(user.getSystemDept().get(0).getSystemCompany().getCompanyCode())) {
                map1.put("departNumber", "01");
            } else {
                map1.put("departNumber", user.getSystemDept().get(0).getSystemCompany().getCompanyCode());//机构编码
            }
            map1.put("applyNo", "zqdzyz");//账号
            map1.put("typeCode", "2");
            Map<String, Object> stringObjectMap = signService.getSealImgList(map1);
            if (stringObjectMap.containsKey("datas")) {
                JSONArray array = (JSONArray) stringObjectMap.get("datas");
                if (array.size() > 0) {
                    JSONObject jsonObject = array.getJSONObject(0);
                    String sealId = (String) jsonObject.get("sealId");
                    //关键字签章
                    paramMap.put("departNumber", "10008");//机构编码 地市编码
                    paramMap.put("departName", "四川");//机构名称 地市名称
                    paramMap.put("applyNo", "zqdzyz");//gert.getApplyNo()//申请人账号
                    paramMap.put("userName", "杨玉峰");//gert.getUserName()//申请人姓名
                    paramMap.put("signseal", "1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
                    paramMap.put("chapterType", "2");//章类型；1.个人章，2 机构章
                    paramMap.put("key", "乙方（盖章）");//关键字内容（PDF 内容中的关键字）
                    paramMap.put("offsetX", 0);//非必填--》Integer 横向偏移量，单位毫米，正数向右偏移， 负数向左偏移
                    paramMap.put("offsetY", -20);//非必填--》Integer 纵向偏移量，单位毫米，正数向上偏移， 负数向下偏移
                    paramMap.put("sealId", sealId);//seal.getSealId()印章 id（生成章返回的编码） sealId
                    File file = new File(contractInfo.getPdfUrl());
                    if (checkFileSize(file, 2, "M")) {
                        String buFile = GetFile.PDFToBase64(file);
                        paramMap.put("reqPdf", buFile);//Pdf 文件传输方式，fileUrl 通过 get 请求 获取文件，reqPdf 通过 base64 字符串 传输文件。两种方式二选一，如果同时传 值，优先使用 reqPdf
                    }
                    Map<String, Object> pdfMap = signService.sealByKey(paramMap);//关键字签章
                    if (pdfMap != null) {
                        if (!"-1".equals(pdfMap.get("resp_code")) && !"1".equals(pdfMap.get("resp_code"))) {
                            JSONObject datas = JSONObject.fromObject(pdfMap.get("datas"));
                            if (datas.getString("fileId").equals("null") || datas.getString("fileId") == null || "".equals(datas.getString("fileId"))) {
                                String reBase64 = (String) datas.get("reBase64");
                                GetFile.base64StringToPdf(reBase64, contractInfo.getPdfUrl());
                                type = 1;
                            }
                        }
                    }
                }
            }
            return type;
        }catch (Exception e){
            logger.error("合同签章失败："+e.getMessage(),e);
            return type;
        }*/
        return 1;
    }

    public void queryPmsProdPriceInfoByPrcIdAndLableId(){
        Map<String,Object> map = new HashMap<>();
        try {
            String prcId = getString("prcId");
            String[] strArry = prcId.split("-");
            PmsProdPriceInfo pmsinfo = customClauseContractService.queryPmsProdPriceInfoByPrcIdAndLableId(strArry[0],
                    strArry[1], strArry[2]);
            PmsProductInfo pmsproduct = customClauseContractService.queryPmsProductInfo(pmsinfo.getProdId(), pmsinfo.getLabelId());
            PmsProductLabel pmslabel = customClauseContractService.queryPmsProductLabel(pmsproduct.getLabelId());
            JSONObject obj = new JSONObject();
            obj.put("id",pmsinfo.getPrcId());
            obj.put("name",pmsinfo.getPrcName());
            obj.put("pid",pmsproduct.getProdId());
            obj.put("price","");
            obj.put("pname",pmsproduct.getProdName());
            obj.put("labelid",pmslabel.getLabelId());
            obj.put("labelname",pmslabel.getLabelName());
            JSONArray arry = new JSONArray();
            arry.add(obj);
            map.put("code",1);
            map.put("data",arry);
            map.put("msg","查询流程数据成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            logger.error("查询资费错误："+e.getMessage(),e);
            map.put("code",-1);
            map.put("data","");
            map.put("msg","查询资费信息失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }
    }

    /**
     * @Description: 获取指定小时后的日期
     * @Param: [date:起始时间, hours:小时数]
     * @return: java.util.Date
     * @Author: TX
     * @Date: 2021/10/8 16:42
     */
    public Date getTargetDate(Date date,Integer hours){
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(date);
        //判断当前日期是否为周末,是的话获取后一天0点
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.set(Calendar. HOUR_OF_DAY , 0);
            calendar.set(Calendar. MINUTE , 0);
            calendar.set(Calendar. SECOND , 0);
            calendar.set(Calendar. MILLISECOND , 0);
            calendar.add(Calendar. DAY_OF_MONTH , 1);
        }
        //判断配置时间是否超过一天
        while (hours>=24){
            //当前时间加一天,并判断加一天后是否未周末,如果是继续加一天
            calendar.add(Calendar.DAY_OF_MONTH, +1);
            while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
                calendar.add(Calendar.DAY_OF_MONTH, +1);
            }
            hours-=24;
        }
        //未超过一天(24小时)直接加到当前时间的上,并判断加上后时间是否为周末,如果是再加一天
        calendar.add(Calendar.HOUR_OF_DAY, +hours);
        while (calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SATURDAY || calendar.get(Calendar.DAY_OF_WEEK) == Calendar.SUNDAY){
            calendar.add(Calendar.DAY_OF_MONTH, +1);
        }
        return calendar.getTime();
    }

    public void getSubPriceKeyInformationList(){
        Map<String,Object> map = new HashMap<>();
        try{
            String productCode=getString("productCode");
            String labelId = getString("labelId");
            List<SubPriceKeyInformation> subPrice= customClauseContractService.getSubPriceKeyInformationList(productCode,labelId);//查询产品配置的关键信息
            if(subPrice!=null){
                map.put("SubPriceKeyInformation",subPrice);
            }else{
                map.put("SubPriceKeyInformation","");
            }
            map.put("code",1);
            map.put("msg","查询数据成功");
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }catch (Exception e){
            logger.error("查询电子合同配置详情信息错误"+e.getMessage(),e);
            map.put("code",-1);
            map.put("msg","查询数据失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }

    public void getSubProductInformationById(){
        Map<String,Object> map = new HashMap<>();
        try{
            String id=getString("id");
            SubProductKeyInformation subPrice= customClauseContractService.getSubProductInformationById(id);//查询产品配置的关键信息
            if(subPrice!=null){
                map.put("SubProductKeyInformation",subPrice);
            }else{
                map.put("SubProductKeyInformation","");
            }
            map.put("code",1);
            map.put("msg","查询数据成功");
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }catch (Exception e){
            logger.error("查询电子合同配置详情信息错误"+e.getMessage(),e);
            map.put("code",-1);
            map.put("msg","查询数据失败："+e.getMessage());
            Write(JSONHelper.SerializeWithNeedAnnotation(map));
        }
    }

    /**
     * @author: liyang
     * @date: 2022/6/14 14:56
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 查询是否存在电子合同
     */
    public void getElectronicContract(){
        Map<String,Object> map =new HashMap<>();
        try{
            String prcId= getString("prcId");//资费ID
            String prodId= getString("prodId");//产品ID
            String labelId= getString("labelId");//产品类目ID
            PriceKeyInformation prcData= customClauseContractService.getPriceKeyInformation(prcId,labelId);
            ProductKeyInformation prodData= customClauseContractService.getProductKeyInformation(prodId,labelId);
            if(prcData!=null&&prodData!=null){
                map.put("code",1);
                map.put("data",0);
                map.put("msg","查询成功");
            }else{
                Map<String, String> data= customClauseContractService.getContractTemplateHtmlUrl(prcId,prodId,labelId);
                if(data!=null){
                    map.put("code",1);
                    map.put("data",1);
                    map.put("template_url",data.get("TEMPLATE_URL").toString());
                    map.put("html_url",data.get("HTML_URL").toString());
                    map.put("msg","查询成功");
                }else{
                    map.put("code",1);
                    map.put("data",-1);
                    map.put("msg","查询成功");
                }
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));//这是不存在电子合同
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            logger.error("查询当前资费是否存在电子合同"+e.getMessage(),e);
            Write(renturnMapStr(-1,false,"查询失败："+e.getMessage()));
        }
    }

    public static String renturnMapStr(Object code,Object data,Object msg){
        Map<String,Object> map =new HashMap<>();
        map.put("code",code);
        map.put("data",data);
        map.put("msg",msg);
        return JSONHelper.SerializeWithNeedAnnotationDateFormat(map);
    }

    /**
     * 查询合同集合
     */
    public void queryContractTemplateList(){
        try {
            Integer pageNo = getInteger("pageNo");//第几页
            Integer pageSize = getInteger("pageSize");//每页显示多少数据
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String templateName = getString("templateName");
            String pageStr = customClauseContractService.queryContractTemplateList(page,templateName);
            this.Write(pageStr);
        } catch (Exception e) {
            logger.error("查询合同集合异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    public void queryContractRivateProductList(){
        Map<String,Object> map =new HashMap<>();
        try{
            String id= getString("id");//资费ID
            List<ContractRivateProduct> prcData= customClauseContractService.getContractRivateProductList(id);
            if(prcData!=null&&prcData!=null){
                map.put("code",1);
                map.put("data",prcData);
                map.put("msg","查询成功");
            }else{
                map.put("code",-1);
                map.put("data",0);
                map.put("msg","未查询到资费配置数据");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));//这是不存在电子合同
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            Write(renturnMapStr(-1,false,"查询失败："+e.getMessage()));
        }
    }

    public void queryContractRivatePriceList(){
        Map<String,Object> map =new HashMap<>();
        try{
            String id= getString("id");//资费ID
            List<ContractRivatePrice> prodData= customClauseContractService.getContractRivatePriceList(id);
            if(prodData!=null&&prodData!=null){
                map.put("code",1);
                map.put("data",prodData);
                map.put("msg","查询成功");
            }else{
                map.put("code",-1);
                map.put("data",0);
                map.put("msg","未查询到产品配置数据");
            }
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));//这是不存在电子合同
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            Write(renturnMapStr(-1,false,"查询失败："+e.getMessage()));
        }
    }

    public void queryContractRivatePricePageList(){
        try {
            Integer pageNo = getInteger("pageNo");//第几页
            Integer pageSize = getInteger("pageSize");//每页显示多少数据
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String prcName = getString("prcName");
            String id = getString("id");
            String pageStr = customClauseContractService.queryContractRivatePricePageList(page,prcName,id);
            this.Write(pageStr);
        } catch (Exception e) {
            logger.error("查询合同集合异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    public void addPmsProdPriceInfoAllList(){
        Map<String,Object> map =new HashMap<>();
        try {
            List<PmsProdPriceInfo> pmsinfo = customClauseContractService.queryPmsProdPriceInfoAllList();
            for (int i = 0; i < pmsinfo.size(); i++) {
                PmsProdPriceInfo info = pmsinfo.get(i);
                info.setId(UUID.randomUUID().toString());
                customClauseContractService.addPmsProdPriceInfo(info);
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            Write(renturnMapStr(-1,"","操作失败："+e.getMessage()));
        }
    }

    //contractAct_addContractRivatePrice.action
    public void addContractRivatePrice(){
        Map<String,Object> map =new HashMap<>();
        try {
            List<PriceKeyInformation> pmsinfo = customClauseContractService.queryPriceKeyInformationAllList();
            for(int i=0;i<pmsinfo.size();i++){
                PriceKeyInformation info = pmsinfo.get(i);
                PmsProdPriceInfo pms = customClauseContractService.queryPmsProdPriceInfoList(info.getPriceCode(),info.getLabelId());
                ContractRivatePrice pr = new ContractRivatePrice();
                //pr.setOperatingExpenses();
                //pr.setTaxRate();
                //pr.setBillingCycle();
                //pr.setId();
                //pr.setRivateProductId();
                pr.setPriceCode(pms.getPrcId());
                pr.setPriceName(pms.getPrcName());
                /*pr.setPriceMoney();
                pr.setTemplateName();*/
                pr.setPriceState("0");
                pr.setIsDelete("1");
                pr.setIsMandatory("1");
                pr.setCreatDate(new Date());
                pr.setCreatUser("李阳");
                pr.setUpdateDate(new Date());
                pr.setUpdateUser("李阳");
                pr.setLabelId(info.getLabelId());
                customClauseContractService.addContractRivatePrice(pr);
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            Write(renturnMapStr(-1,"","操作失败："+e.getMessage()));
        }
    }

    //获取产品-根据合同模板字段templateId获取产品
    public void queryContractRivateProductByTemplateId(){
        try {
            String templateId = getString("templateId");
            List<ContractRivateProduct> contractRivatePrices = customClauseContractService.queryContractRivateProductByTemplateId(templateId);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(contractRivatePrices));
        }catch (Exception e){
            logger.error("查询产品异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    //获取产品资费-根据产品id获取产品资费
    public void queryContractRivatePriceByRivateproducId(){
        try {
            String rivateproductId = getString("id");
            List<ContractRivatePrice> contractRivatePrices = customClauseContractService.queryContractRivatePriceByRivateproducId(rivateproductId);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(contractRivatePrices));
        }catch (Exception e){
            logger.error("查询产品资费异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }

    //contractAct_addContractRivateProduct.action
    public void addContractRivateProduct(){
        Map<String,Object> map =new HashMap<>();
        try {
            List<ProductKeyInformation> pmsinfo = customClauseContractService.queryProductKeyInformationAllList();
            for(int i=0;i<pmsinfo.size();i++){
                ProductKeyInformation info = pmsinfo.get(i);
                PmsProductInfo pmsproduct = customClauseContractService.queryPmsProductInfo(info.getProductCode(), info.getLabelId());
                ContractRivateProduct pr = new ContractRivateProduct();
                //pr.setAssociationId();
                //pr.setId();
                pr.setProdCode(pmsproduct.getProdId());
                pr.setProdName(pmsproduct.getProdName());
                //pr.setTemplateName();
                //pr.setTemplateId();
                pr.setProdState("0");
                pr.setIsDelete("1");
                pr.setIsMandatory("1");
                pr.setCreatDate(new Date());
                pr.setCreatUser("李阳");
                pr.setUpdateDate(new Date());
                pr.setUpdateUser("李阳");
                //pr.setRemarks();
                customClauseContractService.addContractRivateProduct(pr);
                //customClauseContractService.addPmsProdPriceInfo(info);
            }
            map.put("code",1);
            map.put("data","");
            map.put("msg","操作成功");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();//手动回滚事物
            Write(renturnMapStr(-1,"","操作失败："+e.getMessage()));
        }
    }


    //获取产品资费-根据产品id获取产品资费(特例只有新和飞速使用)
    public void queryhfsContractRivatePriceByRivateproducId(){
        try {
            SystemUser user = this.user;
            String rivateproductId = getString("id");
            List<ContractRivatePrice> contractRivatePrices;
            if (user.getAddress()=="成都"){
                 contractRivatePrices = customClauseContractService.queryhfsContractRivatePriceByRivateproducId(rivateproductId);

            }else {
                contractRivatePrices = customClauseContractService.queryContractRivatePriceByRivateproducId(rivateproductId);

            }


            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(contractRivatePrices));
        }catch (Exception e){
            logger.error("查询产品资费异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }


    //根据合同(contractInfo的id)id查询合同模板(contractrivateTemplate)
    public void queryContractTemplate(){
        try{
            String infoId = getString("id");
            ContractInfo contractInfo = customClauseContractService.ContractinfoGetById(infoId);
            if( StringUtil.isNull(contractInfo.getTemplateId())){
                System.out.println("合同表里的templateId为null,可能提交时没提交上");
            }
            ContractRivateTemplate contractRivateTemplate = customClauseContractService.contractRivateTemplateGetById(contractInfo.getTemplateId());

            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(contractRivateTemplate));

        }catch (Exception e){
            logger.error("查询产品资费异常"+e.getMessage(),e);
            e.printStackTrace();
        }
    }


}
