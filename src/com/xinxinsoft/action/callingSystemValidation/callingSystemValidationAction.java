package com.xinxinsoft.action.callingSystemValidation;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.sendComms.callingSystemValidationService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.utils.easyh.JSONHelper;
import net.sf.json.JSONObject;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.dispatcher.multipart.MultiPartRequestWrapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.io.File;
import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * @Description: TODO 图片校验类
 * @Author: TX
 * @Date: 2023/8/14 10:42
 * @Version: 1.0
 */
public class callingSystemValidationAction extends BaseAction {

    private static final Logger logger = LoggerFactory.getLogger(callingSystemValidationAction.class);

    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;

    private final Map<String, String> tokenMap = new ConcurrentHashMap<>();

    private File file;

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    /**
     * @Description TODO 纸质合同印章验真能力
     * <AUTHOR>
     * @Date 2023/8/14 11:18
     **/
    public void checkPaperDocSealFile() {
        try {
            String token = getToken();
            if (Objects.equals(token, "")) {
                Write(returnPars(-1, "", "亲爱的同事，获取鉴权服务密钥失败，请联系管理员处理！"));
            }

            String groupCode = getString("groupCode");      //集团编码(必传)
            String majorWords = getString("majorWords");    //集团名称(必传)

            String bossNo = getString("bossNo");
            if ("".equals(bossNo)) {
                bossNo = user.getBossUserName();
            }
            String oprPhone = getString("oprPhone");
            if ("".equals(bossNo)) {
                oprPhone = user.getMobile();
            }

            String getStr = concatString("groupCode", groupCode, "majorWords", majorWords, "bossNo", bossNo, "oprPhone", oprPhone, "token", token);
            String resultStr = callingSystemValidationService.getInstance().checkPaperDocSeal(file, getStr, token);
            Write(returnPars(1, resultStr, "操作成功"));
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("");
        }
    }

    //计划函件校验码校验
    public void checkPaperText() {
        try {
            String token = getToken();
            if (Objects.equals(token, "")) {
                Write(returnPars(-1, "", "亲爱的同事，获取鉴权服务密钥失败，请联系管理员处理！"));
            }
            HttpServletRequest request = ServletActionContext.getRequest();

            // 判断当前请求是否是一个多部分请求
            if (request instanceof MultiPartRequestWrapper) {
                MultiPartRequestWrapper multiPartRequest = (MultiPartRequestWrapper) request;
                String batchNo = multiPartRequest.getParameter("batchNo");
                // 获取所有上传文件的参数名
                Enumeration<String> fileParameterNames = multiPartRequest.getFileParameterNames();
                if (fileParameterNames != null && fileParameterNames.hasMoreElements()) {
                    String fileParameterName = fileParameterNames.nextElement();

                    // 获取上传文件的File对象数组
                    File[] files = multiPartRequest.getFiles(fileParameterName);
                    if (files != null && files.length > 0) {
                        File file = files[0];
                        String getStr = concatString("bossNo", user.getBossUserName(), "oprPhone", user.getMobile(), "token", token, "sysModule", getString("sysModule"), "batchNo", batchNo, "bizId", batchNo);
                        String resultStr = callingSystemValidationService.getInstance().text(file, getStr, token);
                        Write(resultStr);
                    }
                }
            } else {
                Write(returnPars(500, "", "操作失败"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //身份证校验
    public void checkPaperIdCard() {
        try {
            String token = getToken();
            if (Objects.equals(token, "")) {
                Write(returnPars(-1, "", "亲爱的同事，获取鉴权服务密钥失败，请联系管理员处理！"));
            }
            HttpServletRequest request = ServletActionContext.getRequest();

            // 判断当前请求是否是一个多部分请求
            if (request instanceof MultiPartRequestWrapper) {
                MultiPartRequestWrapper multiPartRequest = (MultiPartRequestWrapper) request;
                String batchNo = multiPartRequest.getParameter("batchNo");
                // 获取所有上传文件的参数名
                Enumeration<String> fileParameterNames = multiPartRequest.getFileParameterNames();
                if (fileParameterNames != null && fileParameterNames.hasMoreElements()) {
                    String fileParameterName = fileParameterNames.nextElement();

                    // 获取上传文件的File对象数组
                    File[] files = multiPartRequest.getFiles(fileParameterName);
                    if (files != null && files.length > 0) {
                        File file = files[0];
                        String getStr = concatString("bossNo", user.getBossUserName(), "oprPhone", user.getMobile(), "token", token, "sysModule", getString("sysModule"), "batchNo", batchNo, "bizId", batchNo);
                        String resultStr = callingSystemValidationService.getInstance().idCard(file, getStr, token);
                        Write(resultStr);
                    }
                }
            } else {
                Write(returnPars(500, "", "操作失败"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //合同盖章校验
    public void checkPaperStamp() {
        try {
            String token = getToken();
            if (Objects.equals(token, "")) {
                Write(returnPars(-1, "", "亲爱的同事，获取鉴权服务密钥失败，请联系管理员处理！"));
            }
            HttpServletRequest request = ServletActionContext.getRequest();

            // 判断当前请求是否是一个多部分请求
            if (request instanceof MultiPartRequestWrapper) {
                MultiPartRequestWrapper multiPartRequest = (MultiPartRequestWrapper) request;
                String groupCode = multiPartRequest.getParameter("groupCode");
                String majorWords = multiPartRequest.getParameter("majorWords");
                String batchNo = multiPartRequest.getParameter("batchNo");
                // 获取所有上传文件的参数名
                Enumeration<String> fileParameterNames = multiPartRequest.getFileParameterNames();
                if (fileParameterNames != null && fileParameterNames.hasMoreElements()) {
                    String fileParameterName = fileParameterNames.nextElement();

                    // 获取上传文件的File对象数组
                    File[] files = multiPartRequest.getFiles(fileParameterName);
                    if (files != null && files.length > 0) {
                        File file = files[0];
                        String getStr = concatString("groupCode", groupCode, "majorWords", majorWords, "bossNo", user.getBossUserName(), "oprPhone", user.getMobile(), "sysModule", getString("sysModule"), "batchNo", batchNo, "bizId", batchNo);
                        String resultStr = callingSystemValidationService.getInstance().checkPaperStamp(file, getStr, token);
                        Write(resultStr);
                    }
                }
            } else {
                Write(returnPars(500, "", "操作失败"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    //二维码识别
    public void checkPaperQrcode() {
        try {
            String token = getToken();
            if (Objects.equals(token, "")) {
                Write(returnPars(-1, "", "亲爱的同事，获取鉴权服务密钥失败，请联系管理员处理！"));
            }
            HttpServletRequest request = ServletActionContext.getRequest();

            // 判断当前请求是否是一个多部分请求
            if (request instanceof MultiPartRequestWrapper) {
                MultiPartRequestWrapper multiPartRequest = (MultiPartRequestWrapper) request;
                String batchNo = multiPartRequest.getParameter("batchNo");
                // 获取所有上传文件的参数名
                Enumeration<String> fileParameterNames = multiPartRequest.getFileParameterNames();
                if (fileParameterNames != null && fileParameterNames.hasMoreElements()) {
                    String fileParameterName = fileParameterNames.nextElement();

                    // 获取上传文件的File对象数组
                    File[] files = multiPartRequest.getFiles(fileParameterName);
                    if (files != null && files.length > 0) {
                        File file = files[0];
                        String getStr = concatString("bossNo", user.getBossUserName(), "oprPhone", user.getMobile(), "token", token, "sysModule", getString("sysModule"), "batchNo", batchNo, "bizId", batchNo);
                        String resultStr = callingSystemValidationService.getInstance().qrcode(file, getStr, token);
                        Write(resultStr);
                    }
                }
            } else {
                Write(returnPars(500, "", "操作失败"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public void stamp() {
        try {
            String token = getToken();
            if (Objects.equals(token, "")) {
                Write(returnPars(-1, "", "亲爱的同事，获取鉴权服务密钥失败，请联系管理员处理！"));
            }
//            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String batchNo = getString("batchNo");
            // 判断当前请求是否是一个多部分请求
            if (request instanceof MultiPartRequestWrapper) {
//                Part groupCode = request.getPart("groupCode");
                MultiPartRequestWrapper multiPartRequest = (MultiPartRequestWrapper) request;
                String groupCode = multiPartRequest.getParameter("groupCode");
                String majorWords = multiPartRequest.getParameter("majorWords");
                // 获取所有上传文件的参数名
                Enumeration<String> fileParameterNames = multiPartRequest.getFileParameterNames();
                if (fileParameterNames != null && fileParameterNames.hasMoreElements()) {
                    String fileParameterName = fileParameterNames.nextElement();

                    // 获取上传文件的File对象数组
                    File[] files = multiPartRequest.getFiles(fileParameterName);
                    if (files != null && files.length > 0) {
                        File file = files[0];
                        String getStr = concatString("groupCode", groupCode, "majorWords", majorWords, "bossNo", user.getBossUserName(), "oprPhone", user.getMobile(), "sysModule", "eom", "batchNo", batchNo);
                        String resultStr = callingSystemValidationService.getInstance().stamp(file, getStr, token);
                        Write(resultStr);
                    }
                }
            } else {
                Write(returnPars(500, "", "操作失败"));
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * @return java.lang.String
     * @Description TODO 获取token字符串
     * <AUTHOR>
     * @Date 2023/8/14 11:20
     **/
    private String getToken() {
        try {
            String dataStr = getDataStr("yyyyMMdd");
            if (tokenMap.containsKey(dataStr)) {
                return tokenMap.get(dataStr);
            } else {
                tokenMap.clear();
                String resultStr = callingSystemValidationService.getInstance().getSysToken();
                JSONObject result = JSONObject.fromObject(resultStr);
                if (result.has("code") && "200".equals(result.getString("code"))) {
                    JSONObject dataJson = result.getJSONObject("data");
                    if (dataJson != null && dataJson.has("token")) {
                        tokenMap.put(dataStr, dataJson.getString("token"));
                    } else {
                        return "";
                    }
                } else {
                    logger.info("Token生成服务调用失败：" + resultStr);
                    return "";
                }
                return tokenMap.get(dataStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("获取Token失败：" + e.getMessage(), e);
            return "";
        }
    }

    /**
     * @return java.lang.String
     * @Description TODO 获取日期
     * <AUTHOR>
     * @Date 2023/8/14 11:20
     **/
    public String getDataStr(String timeFormat) {
        SimpleDateFormat formatter = new SimpleDateFormat(timeFormat);
        return formatter.format(new Date());
    }

    public static String concatString(String... strings) {
        StringBuilder sb = new StringBuilder();

        for (int i = 0; i < strings.length; i += 2) {
            String name = strings[i];
            String value = strings[i + 1];
            if (name.equals("majorWords")) {
                try {
                    value = URLEncoder.encode(value, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    throw new RuntimeException(e);
                }
            }
            if (i + 2 == strings.length) {
                sb.append(name).append("=").append(value);
            } else {
                sb.append(name).append("=").append(value).append("&");
            }
        }

        return sb.toString();
    }

    /**
     * @param state 返回状态  1：成功   -1：失败
     * @param data  返回对象
     * @param msg   返回信息
     * @return java.lang.String
     * @Description TODO 返回数据生成
     * <AUTHOR>
     * @Date 2022/7/28 10:04
     **/
    private static String returnPars(int state, Object data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

}
