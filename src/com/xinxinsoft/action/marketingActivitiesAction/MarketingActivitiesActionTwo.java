package com.xinxinsoft.action.marketingActivitiesAction;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.marketingActivities.*;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesServiceTwo;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 营销活动管理Action
 */
public class MarketingActivitiesActionTwo extends BaseAction {
    private final Logger logger = LoggerFactory.getLogger(MarketingActivitiesActionTwo.class);
    @Resource(name = "MarketingActivitiesServiceTwo")
    private MarketingActivitiesServiceTwo marketingActivitiesService;
    @Resource(name = "WaitTaskService")
    private WaitTaskService waitTaskService;
    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "Bpms_riskoff_service")
    private Bpms_riskoff_service bpms_riskoff_service;
    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;

    //营销活动配额管理列表查询
    public void getQuotaManagementList() {
        try {
            List<Map<String, Object>> listtwo = marketingActivitiesService.getVwUser(user);
            String COMPANY_NAME = "";
            String areaType = "";
            if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                areaType = "D";
            } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (listtwo.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                areaType = "D";
            } else {
                COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
                areaType = "Q";
            }

            if (checkOrderAdmin(marketingActivitiesService.findByRowNo(user.getRowNo()), "16062731")) {   //判断是否为营销活动省公司管理员
                areaType = "S";
            }
            String cycle = getString("cycle");
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            page = marketingActivitiesService.findByPage(page, user, areaType, COMPANY_NAME, cycle);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(page));
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }
    }

    /**
     * 获取初始化地市部门   返回   code为1：成功   -1；失败
     *
     * @return JSON字符串   returnPars
     * @auther TX
     * @date 2021-5-19
     */
    public void FillInTheData() {
        String type = getString("type");
        List<Map<String, String>> companyList = marketingActivitiesService.getCompanyList();
        if (companyList.size() > 0) {
            Write(returnPars(1, companyList, ""));
        } else {
            Write(returnPars(-1, "", "获取地市部门失败，请联系管理员查看！"));
        }
    }


    //年度初始化,保存配额类型总表,配额类型明细表
    public void addQuotaIniallzation() {
        try {
            String retrieve = getString("retrieve");                    //配额类型
            String ComentTotalAmount = getString("ComentTotalAmount");  //本次划拨金额
            String RemainAmount = getString("RemainAmount");            //剩余总金额
            String json = getString("orderJson");                       //地市配额明细字符串
            //获取年份
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
            String year = simpleDateFormat.format(new Date());
            DecimalFormat decimalFormat = new DecimalFormat("0");
            //查询当前周期的配总表
            QuotaTypeManagementTable managementTable = marketingActivitiesService.getQuotaTypeManagementTable(year, retrieve);
            if (managementTable == null) { //如果没有就创建
                managementTable = new QuotaTypeManagementTable();
                if (retrieve.equals("JKZHTXYWB")) {
                    managementTable.setTypeName("集客综合通信业务包");//配额类型名称
                    managementTable.setTypeCode(retrieve);//配额类型编号
                }
                managementTable.setRemainingAmount(decimalFormat.format(Double.parseDouble(RemainAmount) * 100));//剩余金额    页面填报为元  转换为分
                managementTable.setAssignedAmount(decimalFormat.format(Double.parseDouble(ComentTotalAmount) * 100));//已分配金额    页面填报为元  转换为分
                managementTable.setTypeCycle(year);//周期
                managementTable.setCreator(user.getEmployeeName());//创建人
                managementTable.setCreatorId(String.valueOf(user.getRowNo()));//创建人编号
                managementTable.setCreateDate(new Date());//创建时间
            } else {//有就修改
                managementTable.setRemainingAmount(decimalFormat.format(Double.parseDouble(RemainAmount) * 100 + Double.parseDouble(managementTable.getRemainingAmount())));// 本次初始化总额 + 原有总额
                managementTable.setAssignedAmount(decimalFormat.format(Double.parseDouble(ComentTotalAmount) * 100 + Double.parseDouble(managementTable.getAssignedAmount())));//本次分配总额 + 原有分配总额
                managementTable.setUpdator(user.getEmployeeName());//更新人
                managementTable.setUpdatorId(String.valueOf(user.getRowNo()));//更新人编号
                managementTable.setUpdateDate(new Date());//更新时间
            }
            QuotaTypeManagementTable table = marketingActivitiesService.saveOrUpdate(managementTable);
            if (table == null) {
                logger.info("保存配额类型总表失败");
                Write(returnPars(-1, null, "保存配额类型总表失败,请联系管理员处理!"));
                throw new RuntimeException("事务回滚");
            }

            JSONArray jsonArray = JSONArray.fromObject(json);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                //查询配额类型明细表
                QuotaTypeDetailsTable detTable = marketingActivitiesService.getQuotaTypeDetailsTable(table.getTypeCycle(), object.getString("CompanyName"));
                if (detTable == null) {//如果没有就创建
                    detTable = new QuotaTypeDetailsTable();
                    detTable.setTypeName(table.getTypeName());//配额类型名称
                    detTable.setTypeCode(table.getTypeCode());//配额类型编号
                    detTable.setTypeCycle(table.getTypeCycle());//配额周期
                    detTable.setAreaType("D");//区域类型      (Q：区县 | D：地市)
                    detTable.setCity(object.getString("CompanyName"));//地市分公司
                    detTable.setCityCode(object.getString("CompanyCode"));//地市分公司编号
                    detTable.setRemainingAmount(decimalFormat.format(Double.parseDouble(object.getString("CountAmount")) * 100));//剩余总额度     (金额单位：分)
                    detTable.setUsedAmount("0");//已使用总额度    (金额单位：分)
                    detTable.setPreEmptAmount("0");//已预占总额度    (金额单位：分)
                    detTable.setCreator(user.getEmployeeName());//创建人
                    detTable.setCreatorId(String.valueOf(user.getRowNo()));//创建人编号
                    detTable.setCreateDate(new Date());//创建时间
                } else {//有就修改
                    detTable.setRemainingAmount(decimalFormat.format(Double.parseDouble(object.getString("CountAmount")) * 100 + Double.parseDouble(detTable.getRemainingAmount())));//本次分配金额 + 原有金额    (金额单位：分)
                    detTable.setUpdator(user.getEmployeeName());//更新人
                    detTable.setUpdatorId(String.valueOf(user.getRowNo()));//更新人编号
                    detTable.setUpdateDate(new Date());//更新时间
                }
                QuotaTypeDetailsTable detailsTable = marketingActivitiesService.saveOrUpdate(detTable);

                //保存额度变更记录 划入
                QuotaChangeRecordForm form = new QuotaChangeRecordForm();
                form.setPid(detailsTable.getId());//配额类型明细ID
                form.setOperateType("I");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
                form.setOperateAmount(decimalFormat.format(Double.parseDouble(object.getString("CountAmount")) * 100));//变更额度金额 单位：分
                form.setOperator(user.getEmployeeName());//操作人
                form.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
                form.setOperateDate(new Date());//操作时间
                form.setOperateState("1");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
                QuotaChangeRecordForm recordForm = marketingActivitiesService.saveQuotaChangeRecordForm(form);
            }
            Write(returnPars(1, "", "初始化成功!"));
        } catch (Exception e) {
            Write(returnPars(-1, "", "初始化失败，请联系管理员处理！"));
            logger.error("年度初始化失败:" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }


    //地市分配区县
    public void addCountQuotaIniallzation() {
        try {
            String id = getString("id");                    //配额类型
            String ComentTotalAmount = getString("ComentTotalAmount");  //本次划拨金额
            String RemainAmount = getString("RemainAmount");            //剩余总金额
            String json = getString("orderJson");                       //地市配额明细字符串
            DecimalFormat decimalFormat = new DecimalFormat("0");

            //获取地市配额明细
            QuotaTypeDetailsTable table = marketingActivitiesService.getQuotaTypeDetailsTable(id);

            JSONArray jsonArray = JSONArray.fromObject(json);
            for (int i = 0; i < jsonArray.size(); i++) {
                JSONObject object = jsonArray.getJSONObject(i);
                //查询配额类型明细表
                QuotaTypeDetailsTable detTable = marketingActivitiesService.getQuotaTypeDetailsTableTwo(table.getTypeCycle(), object.getString("CompanyName"));
                if (detTable == null) {//如果没有就创建
                    detTable = new QuotaTypeDetailsTable();
                    detTable.setTypeName(table.getTypeName());//配额类型名称
                    detTable.setTypeCode(table.getTypeCode());//配额类型编号
                    detTable.setTypeCycle(table.getTypeCycle());//配额周期
                    detTable.setAreaType("Q");//区域类型      (Q：区县 | D：地市)
                    detTable.setCity(table.getCity());//地市分公司
                    detTable.setCityCode(table.getCityCode());//地市分公司编号
                    detTable.setCounty(object.getString("CompanyName"));
                    detTable.setCountyCode(object.getString("CompanyCode"));
                    detTable.setRemainingAmount(decimalFormat.format(Double.parseDouble(object.getString("CountAmount")) * 100));//剩余总额度     (金额单位：分)
                    detTable.setUsedAmount("0");//已使用总额度    (金额单位：分)
                    detTable.setPreEmptAmount("0");//已预占总额度    (金额单位：分)
                    detTable.setCreator(user.getEmployeeName());//创建人
                    detTable.setCreatorId(String.valueOf(user.getRowNo()));//创建人编号
                    detTable.setCreateDate(new Date());//创建时间
                } else {//有就修改
                    detTable.setRemainingAmount(decimalFormat.format(Double.parseDouble(object.getString("CountAmount")) * 100 + Double.parseDouble(detTable.getRemainingAmount())));//本次分配金额 + 原有金额    (金额单位：分)
                    detTable.setUpdator(user.getEmployeeName());//更新人
                    detTable.setUpdatorId(String.valueOf(user.getRowNo()));//更新人编号
                    detTable.setUpdateDate(new Date());//更新时间
                }
                QuotaTypeDetailsTable detailsTable = marketingActivitiesService.saveOrUpdate(detTable);
                //保存额度变更记录,划入
                QuotaChangeRecordForm form = new QuotaChangeRecordForm();
                form.setPid(detailsTable.getId());//配额类型明细ID
                form.setOperateType("I");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
                form.setOperateAmount(decimalFormat.format(Double.parseDouble(object.getString("CountAmount")) * 100));//变更额度金额 单位：分
                form.setOperator(user.getEmployeeName());//操作人
                form.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
                form.setOperateDate(new Date());//操作时间
                form.setOperateState("1");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
                QuotaChangeRecordForm recordForm = marketingActivitiesService.saveQuotaChangeRecordForm(form);
            }
            //保存额度变更记录,划出
            QuotaChangeRecordForm form1 = new QuotaChangeRecordForm();
            form1.setPid(table.getId());//配额类型明细ID
            form1.setOperateType("O");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
            form1.setOperateAmount(decimalFormat.format(Double.parseDouble(ComentTotalAmount) * 100));//变更额度金额 单位：分
            form1.setOperator(user.getEmployeeName());//操作人
            form1.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
            form1.setOperateDate(new Date());//操作时间
            form1.setOperateState("1");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
            QuotaChangeRecordForm recordForm1 = marketingActivitiesService.saveQuotaChangeRecordForm(form1);

            table.setRemainingAmount(decimalFormat.format(Double.parseDouble(RemainAmount) * 100));//剩余总额度     (金额单位：分)
            QuotaTypeDetailsTable detailsTable = marketingActivitiesService.saveOrUpdate(table);
            Write(returnPars(1, "", "分配成功!"));
        } catch (Exception e) {
            Write(returnPars(-1, "", "分配失败，请联系管理员处理！"));
            logger.error("地市分配区县失败:" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 根据部门名称查询配额信息
     *
     * @return JSON字符串   returnPars
     * @auther TX
     * @date 2021-5-17
     */
    public void QueryMarketQuotaIniallzationByCompanyName() {
        try {
            String cycle = getString("cycle");
            List<Map<String, Object>> listtwo = marketingActivitiesService.getVwUser(user);
            String COMPANY_NAME = "";
            String areaType = "";
            if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                areaType = "D";
            } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (listtwo.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                areaType = "D";
            } else {
                COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
                areaType = "Q";
            }
            QuotaTypeDetailsTable table = marketingActivitiesService.getMarketQuotaIniallzationByCompanyName(COMPANY_NAME, areaType, cycle);
            if (table != null) {
                Map<String, Object> map = new HashMap<>();
                boolean flag = false;
                boolean flagTwo = false;
                if (checkOrderAdmin(marketingActivitiesService.findByRowNo(user.getRowNo()), "16062731")) {   //判断是否为营销活动省公司管理员
                    QuotaTypeManagementTable managementTable = marketingActivitiesService.getQuotaTypeManagementTable(cycle);
                    map.put("managementTable", managementTable);
                    flag = true;
                }
                if (checkOrderAdmin(marketingActivitiesService.findByRowNo(user.getRowNo()), "16062725")) {       //判断是否未营销活动市公司管理员
                    flagTwo = true;
                }
                map.put("flag", flag);
                map.put("flagTwo", flagTwo);
                Write(returnPars(1, table, map));
            } else if (checkOrderAdmin(marketingActivitiesService.findByRowNo(user.getRowNo()), "16062731")) { //没有配额信息，又是省公司
                logger.info("营销活动省公司管理员2");
                Map<String, Object> map = new HashMap<>();
                boolean flag = true;
                boolean flagTwo = false;
                map.put("flag", flag);
                map.put("flagTwo", flagTwo);
                Write(returnPars(2, COMPANY_NAME, map));
            } else {
                Write(returnPars(-1, COMPANY_NAME, "当前" + COMPANY_NAME + "暂无营销活动配额！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 配额划拨查询部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransfer() {
        try {
            String id = getString("id");
            if (id.equals("")) {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            QuotaTypeDetailsTable oneself = marketingActivitiesService.getMarketQuotaIniallzationByCompanyCode(id);
            if (oneself != null) {
                Write(returnPars(1, oneself, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }


    /**
     * 配额划拨查询子部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransferListByVwUserinfo() {
        try {
            String COMPANY_CODE = getString("COMPANY_CODE");
            if (COMPANY_CODE.equals("")) {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            QuotaTypeDetailsTable oneself = marketingActivitiesService.getMarketQuotaIniallzationByCompanyCode(COMPANY_CODE);
            if (oneself != null) {
                List<Map<String, Object>> countyList = marketingActivitiesService.QueryCountyName(oneself.getCity());
                Map<String, Object> map = new HashMap<>();
                map.put("oneself", oneself);
                map.put("countyList", countyList);
                Write(returnPars(1, map, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 配额划拨查询上一级部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransferByAttributionCompanyCode() {
        try {
            String cityCode = getString("cityCode");
            if (cityCode.equals("")) {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            QuotaTypeDetailsTable oneself = marketingActivitiesService.getDetailsTable(cityCode);
            if (oneself != null) {
                Write(returnPars(1, oneself, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }


    /**
     * 配额划拨查询同级级部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransferListByAttributionCompanyCode() {
        try {
            String id = getString("id");
            if (id.equals("")) {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            QuotaTypeDetailsTable oneself = marketingActivitiesService.getMarketQuotaIniallzationByCompanyCode(id);

            List<QuotaTypeDetailsTable> table = marketingActivitiesService.getMarketQuotaIniallzationListByCompanyCode(oneself);
            if (table.size() > 0) {
                Write(returnPars(1, table, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    //拆借申请,配额申请
    public void ApplyQuotaIniallzation() {
        try {
            String orderName = getString("orderName");                              //工单标题
            String OrderAttribute = getString("OrderAttribute");                    //申请类型
            String id = getString("id");                                            //自身id
            String sjid = getString("sjid");                                        //上级id
            String Amount = getString("Amount");                                    //工单申请金额
            String lendingCompanyName = getString("lendingCompanyName");            //拆借公司ID
            String attachmentId = getString("attachmentId");                        //附件ID
            String userId = getString("userId");
            String role = getString("role");
            String orderMemo = getString("orderMemo");                              //工单描述
            DecimalFormat decimalFormat = new DecimalFormat("0");

            //申请部门
            QuotaTypeDetailsTable table1 = marketingActivitiesService.getMarketQuotaIniallzationByCompanyCode(id);

            //新建工单
            QuotaWorkOrderTable order = new QuotaWorkOrderTable();
            order.setOrderTitle(orderName);//工单标题
            order.setOrderDesc(orderMemo);//工单描述
            order.setCreator(user.getEmployeeName());//申请人
            order.setCreatorId(String.valueOf(user.getRowNo()));//申请人编号
            order.setCreateDate(new Date());//申请时间
            order.setOrderState("0");//工单状态 审批中
            order.setAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));//申请金额
            if (OrderAttribute.equals("2")) {//配额申请
                order.setOrderType("SQ");
            } else if (OrderAttribute.equals("3")) {//配额拆借
                order.setOrderType("CJ");
            }
            QuotaWorkOrderTable quotasWorkOrder = marketingActivitiesService.addQuotaWorkOrderTable(order);

            if (OrderAttribute.equals("2")) {//配额申请
                if (sjid.equals("CSH")) {
                    //保存额度变更记录,划入
                    QuotaChangeRecordForm form = new QuotaChangeRecordForm();
                    form.setPid(table1.getId());//配额类型明细ID
                    form.setOperateType("I");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
                    form.setOperateAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));//变更额度金额 单位：分
                    form.setOperator(user.getEmployeeName());//操作人
                    form.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
                    form.setOperateDate(new Date());//操作时间
                    form.setOperateState("0");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
                    QuotaChangeRecordForm recordForm2 = marketingActivitiesService.saveQuotaChangeRecordForm(form);
                    //关联工单和额度变更记录
                    QuotaIntermediateTable intermediateTable2 = new QuotaIntermediateTable();
                    intermediateTable2.setOrderId(quotasWorkOrder.getId());//工单id
                    intermediateTable2.setUpdateId(recordForm2.getId());//额度变更记录id
                    marketingActivitiesService.saveIntermediateTable(intermediateTable2);
                } else {
                    //上级部门
                    QuotaTypeDetailsTable table2 = marketingActivitiesService.getMarketQuotaIniallzationByCompanyCode(sjid);
                    table2.setRemainingAmount(decimalFormat.format(Double.parseDouble(table2.getRemainingAmount()) - Double.parseDouble(Amount) * 100));// 上级部门 剩余金额减少 , 到预占金额
                    table2.setPreEmptAmount(decimalFormat.format(Double.parseDouble(table2.getPreEmptAmount()) + Double.parseDouble(Amount) * 100));//上级部门 原预占金额 + 本次预占金额
                    marketingActivitiesService.saveOrUpdate(table2);

                    //保存额度变更记录,划出
                    QuotaChangeRecordForm form1 = new QuotaChangeRecordForm();
                    form1.setPid(table2.getId());//配额类型明细ID
                    form1.setOperateType("O");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
                    form1.setOperateAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));//变更额度金额 单位：分
                    form1.setOperateState("0");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
                    QuotaChangeRecordForm recordForm1 = marketingActivitiesService.saveQuotaChangeRecordForm(form1);
                    //关联工单和额度变更记录
                    QuotaIntermediateTable intermediateTable1 = new QuotaIntermediateTable();
                    intermediateTable1.setOrderId(quotasWorkOrder.getId());//工单id
                    intermediateTable1.setUpdateId(recordForm1.getId());//额度变更记录id
                    marketingActivitiesService.saveIntermediateTable(intermediateTable1);

                    //保存额度变更记录,划入
                    QuotaChangeRecordForm form2 = new QuotaChangeRecordForm();
                    form2.setPid(table1.getId());//配额类型明细ID
                    form2.setOperateType("I");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
                    form2.setOperateAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));//变更额度金额 单位：分
                    form2.setOperator(user.getEmployeeName());//操作人
                    form2.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
                    form2.setOperateDate(new Date());//操作时间
                    form2.setOperateState("0");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
                    QuotaChangeRecordForm recordForm2 = marketingActivitiesService.saveQuotaChangeRecordForm(form2);
                    //关联工单和额度变更记录
                    QuotaIntermediateTable intermediateTable2 = new QuotaIntermediateTable();
                    intermediateTable2.setOrderId(quotasWorkOrder.getId());//工单id
                    intermediateTable2.setUpdateId(recordForm2.getId());//额度变更记录id
                    marketingActivitiesService.saveIntermediateTable(intermediateTable2);
                }
            } else if (OrderAttribute.equals("3")) {//配额拆借
                //被拆借公司
                QuotaTypeDetailsTable table2 = marketingActivitiesService.getMarketQuotaIniallzationByCompanyCode(lendingCompanyName);
                table2.setRemainingAmount(decimalFormat.format(Double.parseDouble(table2.getRemainingAmount()) - Double.parseDouble(Amount) * 100));// 被拆借公司 剩余金额减少 , 到预占金额
                table2.setPreEmptAmount(decimalFormat.format(Double.parseDouble(table2.getPreEmptAmount()) + Double.parseDouble(Amount) * 100));//被拆借公司 原预占金额 + 本次预占金额
                marketingActivitiesService.saveOrUpdate(table2);

                //保存额度变更记录,借出
                QuotaChangeRecordForm form1 = new QuotaChangeRecordForm();
                form1.setPid(table2.getId());//配额类型明细ID
                form1.setOperateType("JO");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
                form1.setOperateAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));//变更额度金额 单位：分
                form1.setOperateState("0");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
                QuotaChangeRecordForm recordForm1 = marketingActivitiesService.saveQuotaChangeRecordForm(form1);
                //关联工单和额度变更记录
                QuotaIntermediateTable intermediateTable1 = new QuotaIntermediateTable();
                intermediateTable1.setOrderId(quotasWorkOrder.getId());//工单id
                intermediateTable1.setUpdateId(recordForm1.getId());//额度变更记录id
                marketingActivitiesService.saveIntermediateTable(intermediateTable1);

                //保存额度变更记录,借入
                QuotaChangeRecordForm form2 = new QuotaChangeRecordForm();
                form2.setPid(table1.getId());//配额类型明细ID
                form2.setOperateType("JI");//变更类型  使用类型：(使用-U | 预占-Z | 划出-O | 划入-I | 借出-JO | 借入-JI)
                form2.setOperateAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));//变更额度金额 单位：分
                form2.setOperator(user.getEmployeeName());//操作人
                form2.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
                form2.setOperateDate(new Date());//操作时间
                form2.setOperateState("0");//操作状态  操作状态：(未完成-0 | 已完成-1 | 已作废--1)
                QuotaChangeRecordForm recordForm2 = marketingActivitiesService.saveQuotaChangeRecordForm(form2);
                //关联工单和额度变更记录
                QuotaIntermediateTable intermediateTable2 = new QuotaIntermediateTable();
                intermediateTable2.setOrderId(quotasWorkOrder.getId());//工单id
                intermediateTable2.setUpdateId(recordForm2.getId());//额度变更记录id
                marketingActivitiesService.saveIntermediateTable(intermediateTable2);
            }


            if (!StringUtils.isEmpty(attachmentId)) {
                if (!attachmentId.equals("")) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] attachmentjson = attachmentId.split(",");
                    if (attachmentjson.length > 0) {
                        for (int i = 0; i < attachmentjson.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(order.getId());
                            sa.setAttachmentId(attachmentjson[i]);
                            sa.setLink(QuotaWorkOrderTable.QUOTAWORKORDERTABLE);
                            marketingActivitiesService.saveSandA(sa);
                        }
                    }
                }
            }


            if (quotasWorkOrder != null) {
                Map<String, String> map = new HashMap<>();
                map.put("node", role);
                String processId = jbpmUtil.startPIByKey("QuotaApply", map).getId();
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
                bpms_riskoff_service.setBpms_riskoff_process(quotasWorkOrder.getId(), processId, 1, user);
                Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "营销活动管理员", this.user.getRowNo(), this.user);
                String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), USER.getRowNo(), this.user);
                WaitTask waitTask = new WaitTask();
                waitTask.setName("[营销活动配额申请]" + quotasWorkOrder.getOrderTitle());
                waitTask.setCreationTime(new Date());
                waitTask.setUrl("jsp/marketingActivities/handQuotaInitializationThree.jsp?id=" + quotasWorkOrder.getId() + "&processId=" + processId + "&taskId=" + taskid);
                waitTask.setState(WaitTask.HANDLE);
                waitTask.setHandleUserId(USER.getRowNo());
                waitTask.setHandleUserName(USER.getEmployeeName());
                waitTask.setHandleLoginName(USER.getLoginName());
                waitTask.setCreateUserId(user.getRowNo());
                waitTask.setCreateUserName(user.getEmployeeName());
                waitTask.setCreateLoginName(user.getLoginName());
                waitTask.setCode("MarketingActivities");
                waitTask.setTaskId(quotasWorkOrder.getOrderTitle());
                this.waitTaskService.saveWait(waitTask, this.getRequest());
                Write(returnPars(1, "", "工单：" + quotasWorkOrder.getOrderTitle() + "添加成功，已推送至：" + USER.getEmployeeName() + "处，请等待审批！"));
            } else {
                Write(returnPars(-1, "", "添加工单记录时出现错误，请联系管理员处理！"));
                throw new RuntimeException("事务回滚");
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "新建工单异常，请联系管理员处理！"));
            e.printStackTrace();
            logger.error("拆借申请,配额申请" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * ----------------------------------------------------------------------------------------------------------------------------   基础功能代码
     * /**
     * 初始化加载地市配额信息
     *
     * @return returnPars
     * @auther TX
     * @date 2021-5-13
     */
    public void findRepairOrder() {
        try {
            String id = getString("id");
            if (!id.equals("")) {
                //中间表
                List<QuotaIntermediateTable> list = marketingActivitiesService.findQuotaIntermediateTable(id);
                //工单
                QuotaWorkOrderTable o = marketingActivitiesService.findOrderById(id);
                Map<String, Object> map = new HashMap<>();
                map.put("repairOrder", o);
                for (QuotaIntermediateTable t : list) {
                    //配额变更记录
                    QuotaChangeRecordForm changeRecordForm = marketingActivitiesService.findChangeRecordById(t.getUpdateId());
                    if (changeRecordForm.getOperateType().equals("O") || changeRecordForm.getOperateType().equals("JO")) {//出钱方记录
                        map.put("outAmount", marketingActivitiesService.getQuotaTypeDetailsTable(changeRecordForm.getPid()));
                    } else if (changeRecordForm.getOperateType().equals("I") || changeRecordForm.getOperateType().equals("JI")) {//收钱方记录
                        map.put("incomeAmount", marketingActivitiesService.getQuotaTypeDetailsTable(changeRecordForm.getPid()));
                    }
                }
                Write(returnPars(1, map, "查询成功！"));
            } else {
                Write(returnPars(-1, "", "获取数据失败，请刷新页面重试！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }


    /**
     * 同意按钮完成金额的划拨,额度变更记录
     */
    public void ComplateUpdateQuotaIniallzation() {
        try {
            String id = getString("id");// 工单id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 处理意见
            String taskId = this.getString("TaskId");
            String outId = this.getString("outId");//出钱方id
            String incomeId = this.getString("incomeId");//收钱方id
            DecimalFormat decimalFormat = new DecimalFormat("0");
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            QuotaWorkOrderTable o = marketingActivitiesService.findOrderById(id);//获取工单

            //中间表
            List<QuotaIntermediateTable> list = marketingActivitiesService.findQuotaIntermediateTable(id);
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (Whitetask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId); //修改任务表
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = bpms_riskoff_service.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(o.getCreatorId()), user);
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                o.setOrderState("1");//工单已完成
                marketingActivitiesService.addQuotaWorkOrderTable(o);

                QuotaTypeDetailsTable income = marketingActivitiesService.getQuotaTypeDetailsTable(incomeId);//获取收钱地市明细
                //剩余金额加上收钱方从预占金额中划拨过来的金额等于当前剩余金额
                income.setRemainingAmount(decimalFormat.format(Double.parseDouble(income.getRemainingAmount()) + Double.parseDouble(o.getAmount())));
                marketingActivitiesService.saveOrUpdate(income);

                if (outId != null) {//说明是区县申请
                    QuotaTypeDetailsTable out = marketingActivitiesService.getQuotaTypeDetailsTable(outId);//获取出钱地市明细
                    //预占金额减去,划拨到收钱方的剩余金额中
                    out.setPreEmptAmount(decimalFormat.format(Double.parseDouble(out.getPreEmptAmount()) - Double.parseDouble(o.getAmount())));
                    marketingActivitiesService.saveOrUpdate(out);
                } else {//说明是地市申请,改变年度总表
                    QuotaTypeManagementTable managementTable = marketingActivitiesService.getQuotaTypeManagementTable(income.getTypeCycle(), income.getTypeCode());
                    // 已分配金额 + 申请金额
                    managementTable.setAssignedAmount(decimalFormat.format(Double.parseDouble(managementTable.getAssignedAmount()) + Double.parseDouble(o.getAmount())));
                }

                //修改配额变更记录为已完成
                for (QuotaIntermediateTable t : list) {
                    QuotaChangeRecordForm changeRecordForm = marketingActivitiesService.findChangeRecordById(t.getUpdateId());//查询配额变更记录
                    if (changeRecordForm.getOperateType().equals("O") || changeRecordForm.getOperateType().equals("JO")) {//出钱方记录
                        changeRecordForm.setOperator(user.getEmployeeName());//操作人
                        changeRecordForm.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
                        changeRecordForm.setOperateDate(new Date());//操作时间
                    }
                    changeRecordForm.setOperateState("1");//额度变更记录表 已完成
                    marketingActivitiesService.saveQuotaChangeRecordForm(changeRecordForm);
                }

                waitTaskService.updateWait(wt, this.getRequest());//结束待办
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "END");
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                for (String outcome : setlist) {
                    this.jbpmUtil.completeTask(task.getId(), map, outcome);//结束流程
                    break;
                }
                hintBackLogData(o, process.getCreator_no(), processId, user, rtaskid);// 生成待办
                Write(returnPars(1, "", "操作成功，审批已通过！"));
            } else {
                logger.info("代办信息有误==" + wt);
                Write("该代办已处理,请不要重复操作");
                throw new RuntimeException("未查询到待办信息==" + waitId);
            }
        } catch (Exception e) {
            logger.error("同意按钮完成金额的划拨错误==>" + e.getMessage(), e);
            e.printStackTrace();
            Write("操作失败!");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成完成提示待办
     *
     * @param order
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void hintBackLogData(QuotaWorkOrderTable order, Integer userid, String processId, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[营销活动配额申请(同意)]" + order.getOrderTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/marketingActivities/handQuotaInitializationThree.jsp?id=" + order.getId() + "&processId=" + processId + "&taskId=" + taskid);
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("MarketingActivities");
        waitTask.setTaskId(order.getId());
        this.waitTaskService.saveWait(waitTask, this.getRequest());
    }


    /**
     * 工单审批完成 通知代办关闭
     *
     * @params
     */
    public void closeWait() {
        try {
            String waitId = getString("waitId");//待办id
            String id = getString("id");
            Bpms_riskoff_task btask = bpms_riskoff_service.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);//获取待办信息
            if (btask != null && wt != null) {
                bpms_riskoff_service.updateBpms_riskoff_task("完成", 2, btask.getId());
                waitTaskService.updateWait(wt, this.getRequest());//结束待办
            } else {
                Write(returnPars(-1, "", "操作失败，未查询到对应的待办或任务信息！"));
                return;
            }
            Write(returnPars(1, "", "操作成功！"));
        } catch (Exception e) {
            logger.error("工单审批完成错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "操作失败，待办信息异常！"));
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 驳回
     */
    public void ReturnUpdateQuotaIniallzation() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("TaskId");// 任务表id
            String outId = this.getString("outId");//出钱方id
            DecimalFormat decimalFormat = new DecimalFormat("0");
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            QuotaWorkOrderTable o = marketingActivitiesService.findOrderById(id);//获取工单
            //中间表
            List<QuotaIntermediateTable> list = marketingActivitiesService.findQuotaIntermediateTable(id);

            bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);     //修改任务表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = bpms_riskoff_service.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(o.getCreatorId()), user);
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                o.setOrderState("-1");//工单退回
                marketingActivitiesService.addQuotaWorkOrderTable(o);
                if (outId != null) {//说明是区县申请,区县申请被驳回回调预占金额,地市申请不做改变
                    QuotaTypeDetailsTable out = marketingActivitiesService.getQuotaTypeDetailsTable(outId);//获取出钱地市明细
                    //预占金额 - 申请金额
                    out.setPreEmptAmount(decimalFormat.format(Double.parseDouble(out.getPreEmptAmount()) - Double.parseDouble(o.getAmount())));
                    //剩余金额 + 申请金额
                    out.setRemainingAmount(decimalFormat.format(Double.parseDouble(out.getRemainingAmount()) + Double.parseDouble(o.getAmount())));
                    marketingActivitiesService.saveOrUpdate(out);
                }

                //修改配额变更记录为已完成
                for (QuotaIntermediateTable t : list) {
                    QuotaChangeRecordForm changeRecordForm = marketingActivitiesService.findChangeRecordById(t.getUpdateId());//查询配额变更记录
                    if (changeRecordForm.getOperateType().equals("O") || changeRecordForm.getOperateType().equals("JO")) {//出钱方记录
                        changeRecordForm.setOperator(user.getEmployeeName());//操作人
                        changeRecordForm.setOperatorId(String.valueOf(user.getRowNo()));//操作人编号
                        changeRecordForm.setOperateDate(new Date());//操作时间
                    }
                    changeRecordForm.setOperateState("-1");//额度变更记录表 已完成
                    marketingActivitiesService.saveQuotaChangeRecordForm(changeRecordForm);
                }

                waitTaskService.updateWait(wt, this.getRequest());//结束待办
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            handBackLog(o, process.getCreator_no(), processId, user, rtaskid);// 生成待办
            Write(returnPars(1, "", "工单退回成功!"));
        } catch (Exception e) {
            logger.error("流程退回错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "工单退回失败！"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成退回待办
     *
     * @param order
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void handBackLog(QuotaWorkOrderTable order, Integer userid, String processId, SystemUser user, String
            taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[营销活动配额申请(退回)]" + order.getOrderTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/marketingActivities/handQuotaInitializationThree.jsp?id=" + order.getId() + "&processId=" + processId + "&taskId=" + taskid);
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("MarketingActivities");
        waitTask.setTaskId(order.getId());
        this.waitTaskService.saveWait(waitTask, this.getRequest());
    }


    /**
     * 作废工单
     */
    public void invalidRepairOrder() {
        try {
            String waitId = this.getString("waitId");//待办id
            String processId = this.getString("processId");//流程id
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
            // 结束当前待办
            if (wt != null) {
                System.out.println("================处理中开始代办================");
                waitTaskService.updateWait(wt, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            jbpmUtil.deleteProcessInstance(processId);//结束流程
            Write(returnPars(1, "", "工单作废成功！"));
        } catch (Exception var7) {
            logger.error("作废工单错误信息：" + var7.getMessage(), var7);
            Write(returnPars(-1, "", "工单作废失败！"));
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }
    /**
     * 获取附件消息
     */
    public void fuJian() {
        try {
            String id = getString("id");
            String biaoshi = getString("biaoshi");
            List<Map<String, String>> s = marketingActivitiesService.fuJian(id, biaoshi);
            Write(JSONHelper.Serialize(s));
        } catch (Exception e) {
            logger.info("获取附件消息错误==>" + e);
            e.printStackTrace();
        }
    }

    /**
     * <方法序号：2.1 > <方法名：checkOrderAdmin> <详细描述：循环遍历查询是否为规定的角色>
     *
     * @Param: [list, no]
     * @return: boolean
     * @Author: ChuHongQuan
     * @Date: 2019/10/11 15:57
     */
    public boolean checkOrderAdmin(List<Map<String, Object>> list, String no) {

        boolean flag = false;
        for (int i = 0; i < list.size(); i++) {
            if ((list.get(i).get("ROLE_ID").toString()).equals(no)) {// 16
                flag = true;
                break;
            }
        }
        return flag;
    }

    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description: TODO 返回参数生成
     */
    private static String returnPars(int state, Object data, Object msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }
}
