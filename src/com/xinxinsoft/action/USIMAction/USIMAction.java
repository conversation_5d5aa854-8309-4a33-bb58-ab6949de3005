package com.xinxinsoft.action.USIMAction;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.SIM.SIMNumber;
import com.xinxinsoft.entity.USIM.*;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.claimFundsService.ClaimFundsOpenSrv;
import com.xinxinsoft.sendComms.unitService.UnitInfoSrv;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.USIMService.USIMService;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

public class USIMAction extends BaseAction {

    private final Logger logger = LoggerFactory.getLogger(USIMAction.class);
    @Resource(name = "USIMService")
    private USIMService usimService;
    @Resource(name = "TransferJBPMUtils")
    private TransferJBPMUtils transferJBPMUtils;
    @Resource(name = "ClaimForFundsService")
    private ClaimForFundsService claimForFundsService;
    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;
    @Resource(name = "Bpms_riskoff_service")
    private Bpms_riskoff_service bpms_riskoff_service;
    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "WaitTaskService")
    private WaitTaskService service;

    private File file1;

    public File getFile1() {
        return file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }

    //USIM列表分页查询
    public void queryUSIMPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String cardType = getString("cardType");                            //制卡类型
            String cardSize = getString("cardSize");                            //制卡尺寸
            String state = getString("state");                                  //当前状态
            String cardMerchant = getString("cardMerchant");                    //卡商
            String segmentProperties = getString("segmentProperties");          //号段属性
            String startTime = getString("startTime");                          //开始时间
            String endTime = getString("endTime");                              //结束时间
            page = usimService.findAllByPage(page, user, cardType, cardSize, state, cardMerchant, segmentProperties, startTime, endTime);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(page));
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }
    }

    //根据号段分页查询
    public void queryUSIMNumberSegment() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String numberSegment = getString("numberSegment");                  //号段
            String cardSystem = getString("cardSystem");                        //开卡系统
            String city = getString("city");                                    //地市
            String cardType = getString("cardType");                            //制卡类型
            String cardSize = getString("cardSize");                            //制卡尺寸
            String state = getString("state");                                  //当前状态
            String cardMerchant = getString("cardMerchant");                    //卡商
            String voice = getString("voice");                                  //语音卡否

            page = usimService.queryUSIMNumberSegment(page, numberSegment, cardSystem, city, cardType, cardSize, state, cardMerchant);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(page));
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }
    }

    //根据号段查询号码分页
    public void queryUSIMCardNumberInfo() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String numberSegment = getString("numberSegment");                  //号段
            String orderNo = getString("orderNo");                  //工单编号

            page = usimService.queryUSIMCardNumberInfo(page, numberSegment, orderNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(page));
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }
    }

    //USIM物联网卡项目申请列表查询
    public void queryUSIMProjectOrderList() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String tableType = getString("tableType");    //菜单类型
            String orderNo = getString("orderNo");        //工单编号
            String orderName = getString("orderName");    //工单标题
            String groupCode = getString("groupCode");//集团编号
            String creatorPhone = getString("creatorPhone");//申请人电话
            String state = getString("state");  //工单状态
            String startTime = getString("startTime");//开始时间
            String endTime = getString("endTime");//结束时间
            page = usimService.queryUSIMProjectOrderList(page, orderNo, groupCode, orderName, creatorPhone, state, startTime, endTime, tableType, user);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(page));
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }
    }

    //USIM物联网卡号卡申请列表查询
    public void queryUSIMCardNumberOrderList() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String tableType = getString("tableType");          //菜单类型
            String orderNo = getString("orderNo");              //工单编号
            String orderName = getString("orderName");          //工单标题
            String groupCode = getString("groupCode");          //集团编号
            String creatorPhone = getString("creatorPhone");    //申请人电话
            String state = getString("state");                  //工单状态
            String startTime = getString("startTime");          //开始时间
            String endTime = getString("endTime");              //结束时间
            page = usimService.queryUSIMCardNumberOrderList(page, orderNo, groupCode, creatorPhone, orderName, state, startTime, endTime, tableType, user);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(page));
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }
    }


    /**
     * 下载模板
     */
    public void downloadTemplate() {
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name = "USIM物联网卡模板";
            String filepath = request.getSession().getServletContext().getRealPath("/template/USIM.xlsx");
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
            logger.info("下载模板错误==>" + var9);
        }
    }

    //导入模板
    public void importTemplate() {
        try {
            ExcelUtil excelReader = new ExcelUtil(this.file1);
            InputStream is = new FileInputStream(this.file1);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContentTwo();
            List<Map<String, Object>> list = new ArrayList<>();
            HashMap<String, Object> maps;
            if (column == 14) {
                for (int i = 1; i <= map.size(); ++i) {
                    maps = new HashMap<>();
                    maps.put("numberSegment", (map.get(i)).get(0));
                    maps.put("cardSystem", (map.get(i)).get(1));
                    maps.put("city", (map.get(i)).get(2));
                    maps.put("startNumber", (map.get(i)).get(3));
                    maps.put("endNumber", (map.get(i)).get(4));
                    maps.put("cardType", (map.get(i)).get(5));
                    maps.put("cardSize", (map.get(i)).get(6));
                    maps.put("state", (map.get(i)).get(7));
                    maps.put("allocateBalance", (map.get(i)).get(8));
                    maps.put("allocateTime", (map.get(i)).get(9));
                    maps.put("cardMerchant", (map.get(i)).get(10));
                    maps.put("segmentProperties", (map.get(i)).get(11));
                    maps.put("ICCID", (map.get(i)).get(12));
                    maps.put("remark", (map.get(i)).get(13));
                    list.add(maps);
                }
            } else {
                this.Write(returnPars(-1, null, "上传文件格式错误,请下载模板文件上传!"));
            }
            if (map.size() > 0) {
                this.Write(returnPars(1, list, "上传文件成功,共导入" + list.size() + "条数据!"));
            } else {
                this.Write(returnPars(-1, null, "上传的表格数据为空,请确认后重新上传!"));
            }
        } catch (Exception var11) {
            var11.printStackTrace();
            logger.error("导入错误==>" + var11.getMessage(), var11);
            this.Write(returnPars(-1, null, "解析文件异常,请联系管理员核查!"));
        }
    }


    //保存导入的号段数据
    public void saveUSIM() {
        try {
            String jsonone = getString("jsonone");
            JSONArray jsonArr = JSONArray.fromObject(jsonone);
            if (jsonone != null) {
                for (int i = 0; i < jsonArr.size(); i++) {
                    JSONObject obj = jsonArr.getJSONObject(i);
                    String numberSegment = obj.get("numberSegment").toString();//号段
                    String startNumber = obj.get("startNumber").toString();//开始尾号
                    String endNumber = obj.get("endNumber").toString();//结束尾号
                    //添加号段
                    USIMNumberSegment usim = new USIMNumberSegment();
                    usim.setCreateTime(new Date()); //创建时间
                    usim.setCreatorName(user.getEmployeeName());//创建人姓名
                    usim.setCreatorId(String.valueOf(user.getRowNo()));//创建人ID
                    usim.setNumberSegment(obj.get("numberSegment").toString());//号段(万)
                    usim.setCardSystem(obj.get("cardSystem").toString());//开卡系统
                    usim.setCity(obj.get("city").toString());//地市
                    usim.setCardType(obj.get("cardType").toString()); //制卡类型
                    usim.setCardSize(obj.get("cardSize").toString());//制卡尺寸
                    if (obj.get("state").toString().equals("已分配")){
                        usim.setState("1");  //当前状态 0:未分配  1:已分配
                    }else {
                        usim.setState("0");  //当前状态 0:未分配  1:已分配
                    }
                    usim.setAllocateBalance(obj.get("allocateBalance").toString());//数量
                    usim.setAllocateTime(obj.get("allocateTime").toString());//调拨时间
                    usim.setCardMerchant(obj.get("cardMerchant").toString());//卡商
                    usim.setSegmentProperties(obj.get("segmentProperties").toString());//号段属性
                    usim.setICCID(obj.get("ICCID").toString());//ICCID起止
                    usim.setRemark(obj.get("remark").toString());//备注
                    usim.setStartNumber(startNumber);//开始尾号
                    usim.setEndNumber(endNumber);//结束尾号
                    this.usimService.addUSIM(usim);

                    //根据号段 生成 号码
                    for (int j = Integer.parseInt(startNumber); j <= Integer.parseInt(endNumber); j++) {
                        USIMCardNumberInfo info = new USIMCardNumberInfo();
                        info.setNumberSegment(numberSegment); //号段
                        if (String.valueOf(j).length() == 1) {
                            info.setCardNumber(numberSegment + "000" + j);
                        } else if (String.valueOf(j).length() == 2) {
                            info.setCardNumber(numberSegment + "00" + j);
                        } else if (String.valueOf(j).length() == 3) {
                            info.setCardNumber(numberSegment + "0" + j);
                        } else {
                            info.setCardNumber(numberSegment + j);
                        }
                        info.setState("0"); //状态 0:未分配 1:已分配
                        this.usimService.addUSIMCardNumberInfo(info);
                    }
                }
            }
            this.Write("YES");
        } catch (Exception e) {
            logger.error("保存导入的号段数据错误==>" + e.getMessage(), e);
            e.printStackTrace();
        }
    }


    //保存新建号段数据
    public void addUSIM() {
        try {
            String numberSegment = getString("numberSegment");//号段(万)
            String startNumber = getString("startNumber");//开始尾号
            String endNumber = getString("endNumber");//结束尾号
            //保存USIM号段表
            USIMNumberSegment usim = new USIMNumberSegment();
            usim.setCreateTime(new Date()); //创建时间
            usim.setCreatorName(user.getEmployeeName());//创建人姓名
            usim.setCreatorId(String.valueOf(user.getRowNo()));//创建人ID
            usim.setNumberSegment(numberSegment);//号段(万)
            usim.setCardType(getString("cardType")); //制卡类型
            usim.setCardSize(getString("cardSize"));//制卡尺寸
            usim.setCardMerchant(getString("cardMerchant"));//卡商
            usim.setSegmentProperties(getString("segmentProperties"));//号段属性
            usim.setState(getString("state"));//当前状态
            usim.setAllocateBalance(getString("allocateBalance"));//数量
            usim.setAllocateTime(getString("allocateTime"));//调拨时间
            usim.setICCID(getString("ICCID"));//ICCID起止
            usim.setRemark(getString("remark"));//备注
            usim.setCardSystem(getString("cardSystem"));//开卡系统
            usim.setCity(getString("city"));//地市
            usim.setStartNumber(startNumber);//开始尾号
            usim.setEndNumber(endNumber);//结束尾号
            this.usimService.addUSIM(usim);

            //根据号段 生成 号码
            for (int i = Integer.parseInt(startNumber); i <= Integer.parseInt(endNumber); i++) {
                USIMCardNumberInfo info = new USIMCardNumberInfo();
                info.setNumberSegment(numberSegment); //号段
                if (String.valueOf(i).length() == 1) {
                    info.setCardNumber(numberSegment + "000" + i);
                } else if (String.valueOf(i).length() == 2) {
                    info.setCardNumber(numberSegment + "00" + i);
                } else if (String.valueOf(i).length() == 3) {
                    info.setCardNumber(numberSegment + "0" + i);
                } else {
                    info.setCardNumber(numberSegment + i);
                }
                info.setState("0"); //状态 0:未分配 1:已分配
                this.usimService.addUSIMCardNumberInfo(info);
            }
            this.Write("YES");
        } catch (Exception e) {
            logger.error("保存新建号段数据错误==>" + e.getMessage(), e);
            e.printStackTrace();
        }
    }


    /**
     * 发起项目申请流程
     */
    public void saveUSIMProjectOrder() {
        try {
            String role = this.getString("role");//角色
            Integer userId = this.getInteger("userId");//下一步处理人id
            String attachmentId = this.getString("attachmentId");//附件id

            List<Map<String, String>> companyList = usimService.getVwUserinfoByRowno(user.getRowNo());
            String county = "";
            if (companyList.size() > 0) {
                for (int a = 0; a < companyList.size(); a++) {
                    if (companyList.get(a).get("ISMAINDPT").equals("true")) {
                        county = companyList.get(a).get("COMPANY_NAME");
                    }
                }
            } else {
                Write(returnPars(-1, "", "用户视图未查询到对应地市信息，请与管理员联系完整信息后重试！"));
                throw new RuntimeException("事务回滚");
            }
            String orderNo = "";//申请编码前面的字母
            List<Object[]> sone = usimService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                orderNo = (String) sone.get(i)[2];
            }
            //项目申请工单信息
            USIMProjectOrder order = new USIMProjectOrder();
            order.setOrderNo(orderNo + getUnlockedNumber());//工单编号
            order.setOrderName(getString("orderName"));//工单标题
            order.setGroupCode(getString("batchUnitId"));//集团编码
            order.setGroupName(getString("batchUnitName"));//集团名称
            order.setCreateTime(new Date());//创建时间
            order.setCreatorName(user.getEmployeeName());//创建人姓名
            order.setCreatorId(String.valueOf(user.getRowNo()));//创建人id
            order.setCreatorPhone(user.getMobile());//创建人电话
            order.setCompanyCode(county);//所属地市
            order.setState("1");//工单状态   1:审批中工单
            order.setOpeningCharges(getString("openingCharges"));//开通资费
            order.setChargesDiscount(getString("chargesDiscount"));//资费折扣
            order.setBottomLine(getString("bottomLine"));//是否低于集团折扣底线选择
            order.setMaxQuantity(getString("maxQuantity"));//项目最大开卡量
            order.setExpirationTime(getString("expirationTime"));//折扣资费协议到期时间
            order.setSafetyClassification(getString("safetyClassification"));//安全分类登记及技术管控
            order.setRemark(getString("remark"));//不符合以上场景
            order.setUsageQuantity("0");//累计开卡量

            //安全承诺书
            USIMSafetyCommitmentLetter letter = new USIMSafetyCommitmentLetter();
            letter.setUnitType(getString("unitType"));//单位类型
            letter.setHistoryCooperation(getString("historyCooperation"));//是否有合作历史
            letter.setTradingCertificate(getString("tradingCertificate"));//营业执照注册时间
            letter.setRegisterTime(getString("registerTime"));//注册时间
            letter.setRegisteredCapital(getString("registeredCapital"));//注册资金 (单选框)
            letter.setRegisterCapital(getString("registerCapital"));//注册资金
            letter.setUnitNature(getString("unitNature"));//单位性质
            letter.setSiteInspection(getString("siteInspection"));//是否进行现场考察
            letter.setUsableRange(getString("usableRange"));//使用范围
            letter.setProvince(getString("province"));//使用省
            letter.setUsageScenario(getString("usageScenario"));//使用场景
            letter.setUseFunction(getString("useFunction"));//使用功能
            letter.setNote(getString("note"));//开通原因 (短信)
            letter.setVoice(getString("voice"));//开通原因 (语音)
            letter.setCardQuantity1(getString("cardQuantity1"));//协议期内开卡数量
            letter.setCardQuantity(getString("cardQuantity"));//开卡数量
            letter.setPackageTraffic(getString("packageTraffic"));//套餐流量
            letter.setSetMeal(getString("setMeal"));//本项目最高流量套餐
            letter.setDiscountTariff(getString("discountTariff"));//资费折扣
            letter.setLowestDiscount(getString("lowestDiscount"));//本项目最低折扣的资费报价
            letter.setCataloguePrice(getString("cataloguePrice"));//目录价
            letter.setLowPrice(getString("lowPrice"));//市公司管控低价
            letter.setSoftMeasures(getString("softMeasures"));//软措施
            letter.setHardMeasures(getString("HardMeasures"));//硬措施
            letter.setOperatingCapability(getString("operatingCapability"));//单位是否有安全管控能力
            letter.setTechnologicalMeans(getString("technologicalMeans"));//请阐述具体技术手段
            letter.setOther(getString("other"));//其他
            this.usimService.addUSIMLetter(letter);
            order.setSafetyId(letter.getId());//安全承诺书id
            this.usimService.addUSIMOrder(order);

            Map<String, String> map = new HashMap<>();
            map.put("node", role);
            String processId = this.transferJBPMUtils.startTransfer("USIMProjectOrder", map);
            if (!StringUtils.isEmpty(attachmentId) && attachmentId != null) {
                String[] jsontwo = attachmentId.split(",");
                if (jsontwo.length > 0) {
                    for (int i = 0; i < jsontwo.length; ++i) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(order.getId());
                        sa.setAttachmentId(jsontwo[i]);
                        sa.setLink(USIMProjectOrder.USIMProjectOrder);
                        this.claimForFundsService.saveSandA(sa);
                    }
                }
            }
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            this.bpms_riskoff_service.updateBpms_riskoff_task("", 2, task.getId());
            this.bpms_riskoff_service.setBpms_riskoff_process(order.getId(), processId, 1, this.user);
            this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", this.user.getRowNo(), this.user);
            String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, (String) null, 1, "SH", task.getActivityName(), userId, this.user);
            this.commitBackLogData(order, userId, processId, this.user, taskid);
            this.Write("YES");
        } catch (Exception var16) {
            logger.info("USIM项目申请工单发起失败:" + var16);
            var16.printStackTrace();
            this.Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成待办(项目)
     */
    public void commitBackLogData(USIMProjectOrder order, Integer userid, String processId, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[USIM物联网卡项目申请]" + order.getOrderName());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/USIM/USIMProjectOrderApproval.jsp?id=" + order.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNo=" + order.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("USIMProjectOrder");
        waitTask.setTaskId(order.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }

    /**
     * 生成待办(号卡)
     */
    public void commitBackLogDataTwo(USIMCardNumberOrder order, Integer userid, String processId, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[USIM物联网卡号卡申请]" + order.getOrderName());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/USIM/USIMCardNumberOrderApproval.jsp?id=" + order.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNo=" + order.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("USIMCardNumberOrder");
        waitTask.setTaskId(order.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }


    //查询项目申请工单信息
    public void queryProjectOrder() {
        try {
            String id = getString("id");
            if (id.equals("")) {
                Write(returnPars(-1, "", "参数获取失败,请刷新页面重试或联系管理员处理！"));
            } else {
                USIMProjectOrder order = usimService.queryProjectOrder(id);
                if (order != null) {
                    USIMSafetyCommitmentLetter letter = usimService.querySafetyCommitmentLetter(order.getSafetyId());
                    Map<String, Object> pareMap = new HashMap<String, Object>();
                    pareMap.put("USIMProjectOrder", order);
                    pareMap.put("USIMSafetyCommitmentLetter", letter);
                    Write(returnPars(1, pareMap, "数据查询成功!"));
                } else {
                    Write(returnPars(-1, "", "参数:" + id + "错误!,查询工单信息失败!"));
                }
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "获取工单信息异常！"));
            e.printStackTrace();
        }
    }

    /**
     * 获取附件消息
     */
    public void fuJian() {
        try {
            String id = getString("id");
            String biaoshi = getString("biaoshi");
            List<Map<String, String>> s = usimService.fuJian(id, biaoshi);
            Write(JSONHelper.Serialize(s));
        } catch (Exception e) {
            logger.info("获取附件消息错误==>" + e);
            e.printStackTrace();
        }
    }


    //项目申请工单 流程进行 (提交)
    public void handleUSIMProjectApplication() {
        try {
            String pid = this.getString("processId");
            String id = this.getString("id");
            String t = this.getString("juese");
            String userid = this.getString("userId");
            String opinion = this.getString("opinion");
            String waitId = this.getString("waitId");
            String taskId = this.getString("TaskId");
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            USIMProjectOrder order = usimService.getUSIMProjectOrder(id);//获取工单
            if (Whitetask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            if ("ROLE_DSSM".equals(t)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "是");
                this.jbpmUtil.completeTask(task.getId(), map, t);
            } else if ("ROLE_SZKSM".equals(t)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "是");
                this.jbpmUtil.completeTask(task.getId(), map, t);
            } else {
                this.jbpmUtil.completeTask(task.getId(), t);
            }
            String rtaskid = "";
            //获取修改后的
            Task taskTwo = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(pid, "", 1, "SH", taskTwo.getActivityName(), Integer.parseInt(userid), this.user);
            WaitTask wt = this.service.queryWaitByTaskId(waitId);
            if (wt == null) {
                throw new Error("待办ID==========：" + waitId);
            }
            System.out.println("================处理中开始代办================");
            this.service.updateWait(wt, this.getRequest());
            System.out.println("================处理中结束代办================");
            this.commitBackLogData(order, Integer.parseInt(userid), pid, this.user, rtaskid);
            this.Write("YES");
        } catch (Exception var16) {
            this.Write("NO");
            logger.error("统付流程进行错误==>" + var16.getMessage(), var16);
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 项目申请工单 同意按钮
     */
    public void returnHintBackLogData() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 意见
            String taskId = this.getString("TaskId");
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            USIMProjectOrder order = usimService.getUSIMProjectOrder(id);//获取工单
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (Whitetask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId); //修改任务表
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = bpms_riskoff_service.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(order.getCreatorId()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
                order.setState("3");//3.审批完成待确定
                USIMProjectOrder o = usimService.updateOrder(order);
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "end");
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                for (String outcome : setlist) {
                    this.jbpmUtil.completeTask(task.getId(), map, outcome);//结束
                    break;
                }
                hintBackLogData(o, process.getCreator_no(), processId, user, rtaskid);// 生成待办
                Write("YES");
            } else {
                logger.info("代办信息有误==" + wt);
                Write("该代办已处理,请不要重复操作");
                throw new RuntimeException("未查询到待办信息==" + waitId);
            }
        } catch (Exception e) {
            logger.error("统付成员同意错误==>" + e.getMessage(), e);
            e.printStackTrace();
            Write("操作失败!");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成完成提示待办
     *
     * @param order
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void hintBackLogData(USIMProjectOrder order, Integer userid, String processId, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[USIM物联网卡项目申请提示]" + order.getOrderName());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/USIM/USIMProjectOrderApproval.jsp?id=" + order.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNo=" + order.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("USIMProjectOrder");
        waitTask.setTaskId(order.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }


    /**
     * 确定按钮 关闭代办(项目)
     */
    public void USIMProjectOrderWanCheng() {
        try {
            String id = this.getString("id");       //工单id
            String waitId = this.getString("waitId");   //待办id
            String taskId = this.getString("TaskId");   //任务id
            USIMProjectOrder order = usimService.getUSIMProjectOrder(id);//获取工单
            WaitTask wt = this.service.queryWaitByTaskId(waitId); //待办
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (wt != null) {
                if (Whitetask != null) {
                    this.bpms_riskoff_service.updateBpms_riskoff_task("已确定", 2, taskId);
                }
                this.service.updateWait(wt, this.getRequest());//关闭代办
                order.setState("0");     //0:完成工单
                usimService.updateOrder(order);
                this.Write("YES");
            } else {
                throw new RuntimeException("未查询到待办信息==" + waitId);
            }
        } catch (Exception e) {
            logger.error("USIM项目申请确定出错：" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 流程退回(项目)
     */
    public void returnClaimData() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("TaskId");// 任务表id
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            USIMProjectOrder order = usimService.getUSIMProjectOrder(id);//获取工单
            order.setState("2");// 修改状态为退回
            USIMProjectOrder o = usimService.updateOrder(order);
            bpms_riskoff_service.updateBpms_riskoff_task(opinion + " (退回意见)", 2, taskId);     //修改任务表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = bpms_riskoff_service.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(order.getCreatorId()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            handBackLog(o, process.getCreator_no(), processId, user, rtaskid);// 生成待办
            Write("YES");
        } catch (Exception e) {
            logger.info("流程退回错误==>" + e);
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成退回待办(项目)
     *
     * @param order
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void handBackLog(USIMProjectOrder order, Integer userid, String processId, SystemUser user, String
            taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[USIM物联网卡项目申请退回]" + order.getOrderName());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/USIM/USIMProjectOrderReturn.jsp?id=" + order.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNO=" + order.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("USIMProjectOrder");
        waitTask.setTaskId(order.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }

    //作废(项目)
    public void Invalid() {
        try {
            String id = this.getString("id");
            String waitId = this.getString("waitId");//待办id
            String processId = this.getString("processId");//流程id
            String taskId = getString("TaskId");// 任务表id
            USIMProjectOrder order = usimService.getUSIMProjectOrder(id);//获取工单
            WaitTask wt = service.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            // 结束当前待办
            if (wt != null) {
                if (Whitetask != null) {
                    this.bpms_riskoff_service.updateBpms_riskoff_task("已作废", 2, taskId);
                }
                order.setState("-1"); //-1作废工单
                usimService.updateOrder(order);
                System.out.println("================处理中开始代办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            jbpmUtil.deleteProcessInstance(processId);//结束流程
            this.Write("OK");
        } catch (Exception var7) {
            logger.info("作废错误==>" + var7);
            var7.printStackTrace();
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }


    /**
     * 查询任务信息
     */
    public void queryProcessTrackingById() {
        try {
            String id = this.getString("id");
            List<Bpms_riskoff_task> RedList = this.bpms_riskoff_service.getPublicEntityTaskList(id);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(RedList));
        } catch (Exception e) {
            logger.info("查询任务信息错误==>" + e);
            e.printStackTrace();
        }
    }


    //查询项目
    public void queryUSIMProjectOrder() {
        try {
            String unit_id = getString("UNIT_ID");//集团编码
            List<USIMProjectOrder> orders = usimService.queryUSIMProjectOrder(unit_id, user);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(orders));
        } catch (Exception e) {
            logger.info("查询项目错误==>" + e);
            e.printStackTrace();
        }
    }

    //查询安全承诺书
    public void querySafety() {
        try {
            String id = getString("id");
            if (id.equals("")) {
                Write(returnPars(-1, "", "参数获取失败,请刷新页面重试或联系管理员处理！"));
            } else {
                USIMSafetyCommitmentLetter letter = usimService.querySafetyCommitmentLetter(id);
                if (letter != null) {
                    Write(returnPars(1, letter, "数据查询成功!"));
                } else {
                    Write(returnPars(-1, "", "参数:" + id + "错误!,查询信息失败!"));
                }
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "获取工单信息异常！"));
            e.printStackTrace();
        }
    }


    /**
     * 发起号卡申请流程
     */
    public void saveUSIMCardNumberOrder() {
        try {
            String role = this.getString("role");//角色
            Integer userId = this.getInteger("userId");//下一步处理人id
            String attachmentId = this.getString("attachmentId");//附件id
            String projectId = getString("orderId");//项目id

            List<Map<String, String>> companyList = usimService.getVwUserinfoByRowno(user.getRowNo());
            String county = "";
            if (companyList.size() > 0) {
                for (int a = 0; a < companyList.size(); a++) {
                    if (companyList.get(a).get("ISMAINDPT").equals("true")) {
                        county = companyList.get(a).get("COMPANY_NAME");
                    }
                }
            } else {
                Write(returnPars(-1, "", "用户视图未查询到对应地市信息，请与管理员联系完整信息后重试！"));
                throw new RuntimeException("事务回滚");
            }
            String orderNo = "";//申请编码前面的字母
            List<Object[]> sone = usimService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                orderNo = (String) sone.get(i)[2];
            }
            //项目申请工单信息
            USIMCardNumberOrder order = new USIMCardNumberOrder();
            order.setOrderNo(orderNo + getUnlockedNumber());//工单编号
            order.setOrderName(getString("orderName"));//工单标题
            order.setGroupCode(getString("groupCode"));//集团编码
            order.setGroupName(getString("groupName"));//集团名称
            order.setCreateTime(new Date());//创建时间
            order.setCreatorName(user.getEmployeeName());//创建人姓名
            order.setCreatorId(String.valueOf(user.getRowNo()));//创建人id
            order.setCreatorPhone(user.getMobile());//创建人电话
            order.setCompanyCode(county);//所属地市
            order.setState("1");//工单状态   1:审批中工单
            order.setContractPattern(getString("contractPattern"));//合同模式 1.有合同 0.无合同
            order.setSystem(getString("system"));//归属系统
            order.setUsageScenario(getString("usageScenario"));//使用场景
            order.setCardQuantity(getString("cardQuantity"));//号卡数量
            order.setCardType(getString("cardType"));//制卡类型
            order.setCardSize(getString("cardSize"));//制卡尺寸
            order.setSegmentProperties(getString("segmentProperties"));//号段属性
            order.setStockType(getString("stockType"));//库存卡类型
            order.setStockQuantity(getString("stockQuantity"));//库存卡数量
            order.setCardFee(getString("cardFee"));//USIM物联网卡费
            order.setExpectationTime(getString("expectationTime"));//期望拿卡时间
            order.setTheDeadline(getString("theDeadline"));//最晚开卡时间
            order.setRemark(getString("remark"));//需求描述
            order.setExpirationTime(getString("expirationTime"));//无合同模式到期时间
            order.setProjectId(projectId);//关联项目id
            this.usimService.addUSIMCardNumberOrder(order);

            USIMProjectOrder projectOrder = usimService.getUSIMProjectOrder(projectId);//获取工单
            if (projectOrder != null) {
                projectOrder.setUsageQuantity(Integer.parseInt(getString("cardQuantity")) + Integer.parseInt(projectOrder.getUsageQuantity()) + "");//累计开卡数
                this.usimService.updateOrder(projectOrder);
            }

            Map<String, String> map = new HashMap<>();
            map.put("node", role);
            String processId = this.transferJBPMUtils.startTransfer("USIMCardNumberOrder", map);
            if (!StringUtils.isEmpty(attachmentId) && attachmentId != null) {
                String[] jsontwo = attachmentId.split(",");
                if (jsontwo.length > 0) {
                    for (int i = 0; i < jsontwo.length; ++i) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(order.getId());
                        sa.setAttachmentId(jsontwo[i]);
                        sa.setLink(USIMCardNumberOrder.USIMCardNumberOrder);
                        this.claimForFundsService.saveSandA(sa);
                    }
                }
            }
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            this.bpms_riskoff_service.updateBpms_riskoff_task("", 2, task.getId());
            this.bpms_riskoff_service.setBpms_riskoff_process(order.getId(), processId, 1, this.user);
            this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", this.user.getRowNo(), this.user);
            String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, (String) null, 1, "SH", task.getActivityName(), userId, this.user);
            this.commitBackLogDataTwo(order, userId, processId, this.user, taskid);
            this.Write("YES");
        } catch (Exception var16) {
            logger.info("USIM号卡申请工单发起失败:" + var16);
            var16.printStackTrace();
            this.Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    //查询号卡申请工单信息
    public void queryCardNumberOrder() {
        try {
            String id = getString("id");
            if (id.equals("")) {
                Write(returnPars(-1, "", "参数获取失败,请刷新页面重试或联系管理员处理！"));
            } else {
                USIMCardNumberOrder order = usimService.queryCardNumberOrder(id);
                if (order != null) {
                    Map<String, Object> pareMap = new HashMap<String, Object>();
                    pareMap.put("USIMCardNumberOrder", order);
                    Write(returnPars(1, pareMap, "数据查询成功!"));
                } else {
                    Write(returnPars(-1, "", "参数:" + id + "错误!,查询工单信息失败!"));
                }
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "获取工单信息异常！"));
            e.printStackTrace();
        }
    }


    //查询号段分配信息
    public void queryAllocationRecord() {
        try {
            String id = getString("id");
            if (id.equals("")) {
                Write(returnPars(-1, "", "参数获取失败,请刷新页面重试或联系管理员处理！"));
            } else {
                USIMCardNumberOrder order = usimService.queryCardNumberOrder(id);
                List<USIMAllocationRecord> records = usimService.queryAllocationRecord(order.getOrderNo());
                if (order != null) {
                    Map<String, Object> pareMap = new HashMap<String, Object>();
                    pareMap.put("records", records);
                    Write(returnPars(1, pareMap, "数据查询成功!"));
                } else {
                    Write(returnPars(-1, "", "参数:" + id + "错误!,查询工单信息失败!"));
                }
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "获取工单信息异常！"));
            e.printStackTrace();
        }
    }


    //号卡申请工单 流程进行 (提交)
    public void handleCardNumberApplication() {
        try {
            String pid = this.getString("processId");
            String id = this.getString("id");
            String t = this.getString("juese");
            String userid = this.getString("userId");
            String opinion = this.getString("opinion");
            String waitId = this.getString("waitId");
            String taskId = this.getString("TaskId");
            String deptStr = this.getString("deptStr");//所属公司
            String jsonone = this.getString("jsonone");//号段分配明细
            String dangqianrenwu = this.getString("dangqianrenwu");//号段分配明细
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            USIMCardNumberOrder order = usimService.queryCardNumberOrder(id);//获取工单
            if (Whitetask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }

            //分配
            if ("市公司业务管理员".equals(dangqianrenwu)) {
                JSONArray array = JSONArray.fromObject(jsonone);
                for (int i = 0; i < array.size(); i++) {
                    JSONObject object = array.getJSONObject(i);
                    String numberSegment = object.getString("numberSegment");//号段
                    String allotment = object.getString("allotment");//数量
                    USIMNumberSegment segment = usimService.findByNumberSegment(numberSegment);
                    if (segment.getAllocateBalance().equals(allotment)) {//全部分配
                        segment.setState("1");//当前状态 0:未分配  1:已分配
                        segment.setOrderNo(order.getOrderNo());//关联工单
                        this.usimService.saveOrUpdateUSIMOrder(segment);

                        //分配号码
                        List<USIMCardNumberInfo> infos = usimService.queryNumberInfo(numberSegment);
                        for (int j = 0; j < Integer.parseInt(allotment); j++) {
                            USIMCardNumberInfo info = infos.get(j);
                            info.setState("1");//状态 0:未分配 1:已分配
                            info.setOrderNo(order.getOrderNo());//工单编号
                            this.usimService.updateNumberInfo(info);
                        }
                    } else {//部分分配
                        segment.setAllocateBalance(String.valueOf(Integer.parseInt(segment.getAllocateBalance()) - Integer.parseInt(allotment)));


                        USIMNumberSegment usim = new USIMNumberSegment();
                        usim.setCreateTime(new Date()); //创建时间
                        usim.setCreatorName(user.getEmployeeName());//创建人姓名
                        usim.setCreatorId(String.valueOf(user.getRowNo()));//创建人ID
                        usim.setNumberSegment(numberSegment);//号段(万)
                        usim.setCardType(segment.getCardType()); //制卡类型
                        usim.setCardSize(segment.getCardSize());//制卡尺寸
                        usim.setCardMerchant(segment.getCardMerchant());//卡商
                        usim.setSegmentProperties(segment.getSegmentProperties());//号段属性
                        usim.setState("1");//当前状态 0:未分配  1:已分配
                        usim.setAllocateBalance(allotment);//数量
                        usim.setAllocateTime(segment.getAllocateTime());//调拨时间
                        usim.setICCID(segment.getICCID());//ICCID起止
                        usim.setRemark(segment.getRemark());//备注
                        usim.setCardSystem(segment.getCardSystem());//开卡系统
                        usim.setCity(segment.getCity());//地市
                        usim.setOrderNo(order.getOrderNo());//关联工单

                        //分配号码
                        List<USIMCardNumberInfo> infos = usimService.queryNumberInfo(numberSegment);
                        for (int j = 0; j < Integer.parseInt(allotment); j++) {
                            USIMCardNumberInfo info = infos.get(j);
                            info.setState("1");//状态 0:未分配 1:已分配
                            info.setOrderNo(order.getOrderNo());//工单编号
                            this.usimService.updateNumberInfo(info);

                            if (j == 0) {
                                String str = info.getCardNumber();
                                usim.setStartNumber(str.substring(str.length() - 4));//开始尾号
                            } else if (j == Integer.parseInt(allotment) - 1) {
                                String str = info.getCardNumber();
                                usim.setEndNumber(str.substring(str.length() - 4));//结束尾号


                                USIMCardNumberInfo numberInfo = infos.get(Integer.parseInt(allotment));
                                String str2 = numberInfo.getCardNumber();
                                segment.setStartNumber(str2.substring(str2.length() - 4));//开始尾号
                            }
                        }
                        this.usimService.saveOrUpdateUSIMOrder(segment);
                        this.usimService.saveOrUpdateUSIMOrder(usim);
                    }


                    //分配操作记录
                    USIMAllocationRecord record = new USIMAllocationRecord();
                    record.setOperationTime(new Date());//分配时间
                    record.setOperationName(user.getEmployeeName());//分配人姓名
                    record.setOperationId(String.valueOf(user.getRowNo()));//分配人id
                    record.setNumberSegment(numberSegment);//号段
                    record.setCity(segment.getCity());//地市
                    record.setCardType(segment.getCardType());//制卡类型
                    record.setCardSize(segment.getCardSize()); //制卡尺寸
                    record.setState("1"); //当前状态 0:未分配  1:已分配
                    record.setAllocateBalance(allotment);//数量
                    record.setSegmentProperties(segment.getSegmentProperties());//号段属性
                    record.setCardMerchant(segment.getCardMerchant()); //卡商
                    record.setAllocateTime(segment.getAllocateTime()); //调拨时间
                    record.setICCID(segment.getICCID()); //ICCID起止
                    record.setOrderNo(order.getOrderNo()); //工单编号 (关联号卡申请工单)
                    this.usimService.saveAllocationRecord(record);
                }
            }

            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            if ("ROLE_DSSM".equals(t)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "是");
                this.jbpmUtil.completeTask(task.getId(), map, t);
            } else if ("ROLE_SGSBM".equals(t)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "ROLE_SGSBM");
                this.jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
            } else if ("ROLE_SZKSM".equals(t)) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "是");
                this.jbpmUtil.completeTask(task.getId(), map, t);
            } else if ("ROLE_DSBM".equals(t) && deptStr.equals("省公司")) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "ROLE_DSBM");
                this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SZKSM");
            } else {
                this.jbpmUtil.completeTask(task.getId(), t);
            }
            String rtaskid = "";
            //获取修改后的
            Task taskTwo = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(pid, "", 1, "SH", taskTwo.getActivityName(), Integer.parseInt(userid), this.user);
            WaitTask wt = this.service.queryWaitByTaskId(waitId);
            if (wt == null) {
                throw new Error("待办ID==========：" + waitId);
            }
            System.out.println("================处理中开始代办================");
            this.service.updateWait(wt, this.getRequest());
            System.out.println("================处理中结束代办================");
            this.commitBackLogDataTwo(order, Integer.parseInt(userid), pid, this.user, rtaskid);
            this.Write("YES");
        } catch (Exception var16) {
            this.Write("NO");
            logger.error("号卡申请工单流程进行==>" + var16.getMessage(), var16);
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 号卡申请工单 同意按钮
     */
    public void consentCardNumber() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 意见
            String taskId = this.getString("TaskId");
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            USIMCardNumberOrder order = usimService.queryCardNumberOrder(id);//获取工单
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (Whitetask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId); //修改任务表
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = bpms_riskoff_service.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(order.getCreatorId()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
                order.setState("3");// 3.审批完成,待确定(待归档)
                USIMCardNumberOrder o = usimService.updateCardNumberOrder(order);
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "end");
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                for (String outcome : setlist) {
                    this.jbpmUtil.completeTask(task.getId(), map, outcome);//结束
                    break;
                }
                commitBackLogDataTwo(o, process.getCreator_no(), processId, user, rtaskid);// 生成待办
                Write("YES");
            } else {
                logger.info("代办信息有误==" + wt);
                Write("该代办已处理,请不要重复操作");
                throw new RuntimeException("未查询到待办信息==" + waitId);
            }
        } catch (Exception e) {
            logger.error("统付成员同意错误==>" + e.getMessage(), e);
            e.printStackTrace();
            Write("操作失败!");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 号卡申请工单 确定按钮
     */
    public void USIMCardNumberOrderWanCheng() {
        try {
            String id = this.getString("id");       //工单id
            String waitId = this.getString("waitId");   //待办id
            String taskId = this.getString("TaskId");   //任务id
            USIMCardNumberOrder order = usimService.queryCardNumberOrder(id);//获取工单
            WaitTask wt = this.service.queryWaitByTaskId(waitId); //待办
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (wt != null) {
                //判断是否关联合同
                if (order.getProjectId() != null) {
                    if (Whitetask != null) {
                        this.bpms_riskoff_service.updateBpms_riskoff_task("已确定", 2, taskId);
                    }
                    this.service.updateWait(wt, this.getRequest());//关闭代办
                    order.setState("0");     //0:完成工单
                    usimService.updateCardNumberOrder(order);
                    this.Write("YES");
                } else {
                    this.Write("未关联合同,无法归档!");
                }
            } else {
                throw new RuntimeException("未查询到待办信息==" + waitId);
            }
        } catch (Exception e) {
            logger.error("USIM项目申请确定出错：" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 号卡申请工单 关联项目按钮
     */
    public void associatedProject() {
        try {
            String id = this.getString("id");       //工单id
            String projectId = getString("projectId");//项目id
            USIMCardNumberOrder numberOrder = usimService.queryCardNumberOrder(id);//获取号卡工单
            USIMProjectOrder projectOrder = usimService.queryProjectOrder(projectId);//获取项目
            if (Integer.parseInt(numberOrder.getCardQuantity()) <= Integer.parseInt(projectOrder.getMaxQuantity())) {
                int i = Integer.parseInt(projectOrder.getMaxQuantity()) - Integer.parseInt(projectOrder.getUsageQuantity());//最大开卡量 - 累计开卡数 = 剩余开卡量
                if (Integer.parseInt(numberOrder.getCardQuantity()) <= i) {
                    projectOrder.setUsageQuantity(String.valueOf(Integer.parseInt(projectOrder.getUsageQuantity()) + Integer.parseInt(numberOrder.getCardQuantity())));//累计开卡数
                    usimService.updateOrder(projectOrder);
                    numberOrder.setProjectId(projectId);//关联项目id
                    usimService.updateCardNumberOrder(numberOrder);
                    this.Write("YES");
                } else {
                    this.Write("关联失败,本次申请数量大于该项目剩余开卡量!");
                }
            } else {
                this.Write("关联失败,本次申请数量大于该项目最大开卡量!");
            }

        } catch (Exception e) {
            logger.error("关联项目按钮出错：" + e.getMessage(), e);
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 流程退回(号卡)
     */
    public void returnCardNumber() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("TaskId");// 任务表id
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            USIMCardNumberOrder order = usimService.queryCardNumberOrder(id);//获取工单
            order.setState("2");// 修改状态为退回
            USIMCardNumberOrder o = usimService.updateCardNumberOrder(order);
            bpms_riskoff_service.updateBpms_riskoff_task(opinion + " (退回意见)", 2, taskId);     //修改任务表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = bpms_riskoff_service.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(order.getCreatorId()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            handBackLogTwo(o, process.getCreator_no(), processId, user, rtaskid);// 生成待办
            Write("YES");
        } catch (Exception e) {
            logger.info("流程退回错误==>" + e);
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成退回待办(项目)
     *
     * @param order
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void handBackLogTwo(USIMCardNumberOrder order, Integer userid, String processId, SystemUser user, String
            taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[USIM物联网卡号卡申请退回]" + order.getOrderName());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/USIM/USIMCardNumberReturn.jsp?id=" + order.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNO=" + order.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("USIMCardNumberOrder");
        waitTask.setTaskId(order.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }

    //作废(号卡)
    public void InvalidTwo() {
        try {
            String id = this.getString("id");
            String waitId = this.getString("waitId");//待办id
            String processId = this.getString("processId");//流程id
            String taskId = getString("TaskId");// 任务表id
            USIMCardNumberOrder order = usimService.queryCardNumberOrder(id);//获取工单
            USIMProjectOrder projectOrder = usimService.getUSIMProjectOrder(order.getProjectId());//获取工单
            WaitTask wt = service.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
            Bpms_riskoff_task Whitetask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            // 结束当前待办
            if (wt != null) {
                if (Whitetask != null) {
                    this.bpms_riskoff_service.updateBpms_riskoff_task("已作废", 2, taskId);
                }
                if (projectOrder != null) {
                    projectOrder.setUsageQuantity(Integer.parseInt(projectOrder.getUsageQuantity()) - Integer.parseInt(order.getCardQuantity()) + "");//累计开卡数
                    this.usimService.updateOrder(projectOrder);
                }
                order.setState("-1"); //-1作废工单
                this.usimService.updateCardNumberOrder(order);
                System.out.println("================处理中开始代办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            jbpmUtil.deleteProcessInstance(processId);//结束流程
            this.Write("OK");
        } catch (Exception var7) {
            logger.info("作废错误==>" + var7);
            var7.printStackTrace();
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }


    //查询号段
    public void queryNumberSegment() {
        try {
            String cardType = getString("cardType");
            String cardSize = getString("cardSize");
            String segmentProperties = getString("segmentProperties");
            List<USIMNumberSegment> segment = usimService.queryNumberSegment(cardType, cardSize, segmentProperties);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(segment));
        } catch (Exception e) {
            logger.info("查询号段错误==>" + e);
            e.printStackTrace();
        }
    }

    //查询是否有未归档的无合同模式号卡申请
    public void queryProject() {
        USIMCardNumberOrder order = usimService.queryProject(String.valueOf(user.getRowNo()));
        if (order == null) {
            this.Write("yes");
        } else {
            this.Write("亲爱的同事,当前已有无合同模式申请存在,请关归档后再进行申请!");
        }
    }

    //查询集团信息
    public void qryEnterpriseInfo() {
        try {
            String groupName = getString("groupName");
            Result result = ClaimFundsOpenSrv.getInstance().sGetLoginMsg(user.getBossUserName());
            //Result result = ClaimFundsOpenSrv.getInstance().sGetLoginMsg("aagh38");
            JSONObject object = JSONObject.fromObject(result.getData());
            JSONObject root = object.getJSONObject("ROOT");
            JSONObject outData = root.getJSONObject("OUT_DATA");

            //TODO 集团接口-企业信息查询服务
            HashMap<String, String> map = new HashMap<>();
            Result result2 = UnitInfoSrv.getInstance().qryEnterpriseInfo(user.getBossUserName(), outData.getString("GROUP_ID"), "1", groupName);
            //Result result2 = UnitInfoSrv.getInstance().qryEnterpriseInfo("aagh38", "17", "1", "成都方天赤兔科技有限公司");
            JSONObject object2 = JSONObject.fromObject(result2.getData());
            JSONObject root2 = object2.getJSONObject("ROOT");
            if (root2.getString("RETURN_CODE").equals("0")) {
                JSONObject outData2 = root2.getJSONObject("OUT_DATA");
                JSONObject info = outData2.getJSONObject("EC_DETAIL_INFO");

                map.put("REG_CAP", info.getString("REG_CAP"));//注册资本
                map.put("ES_DATE", info.getString("ES_DATE"));//成立日期
                map.put("REGISTERED_PROVINCE", info.getString("REGISTERED_PROVINCE"));//注册省
                this.Write(returnPars(1, map, "数据查询成功!"));
            } else {
                Write(returnPars(-1, null, "查询集团信息异常！"));
            }
        } catch (Exception e) {
            Write(returnPars(-1, null, "查询集团信息异常！"));
            logger.error("查询集团信息异常：" + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    //查询号码详情
    public void sFreeMinQry() {
        try {
            String card_number = getString("CARD_NUMBER");
            Result result = usimService.sFreeMinQry(card_number, "", "", user.getBossUserName());
            //Result result = usimService.sFreeMinQry("18828021134", "", "", "aagh38");
            System.out.println(result);
            JSONObject object = JSONObject.fromObject(result.getData());
            JSONObject root = object.getJSONObject("ROOT");
            if (root.getString("RETURN_CODE").equals("0")) {
                JSONObject outData = root.getJSONObject("OUT_DATA");
                this.Write(returnPars(1, outData, "数据查询成功!"));
            } else {
                this.Write(returnPars(-1, null, "未查询到号码详情!"));
            }
        } catch (Exception e) {
            Write(returnPars(-1, null, "查询号码详情异常！"));
            logger.error("查询号码详情异常：" + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    public static String getUnlockedNumber() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(new Date());
        return dateString;
    }

    /**
     * 生成响应对象
     *
     * @param state 响应状态(1:成功 , -1:失败)
     * @param data  返回对象
     * @param msg   返回信息
     * @return 响应JSON对象
     */
    private static String returnPars(int state, Object data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }
}
