package com.xinxinsoft.action.appOpenAction;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemCompany;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.sendComms.omsService.BusiOppService;
import com.xinxinsoft.sendComms.omsService.CMCCMASOpenService;
import com.xinxinsoft.sendComms.omsService.CMCCRHOrderOpenService;
import com.xinxinsoft.sendComms.omsService.CommonDates;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.sendComms.omsService.common.PackOprInfoVO;
import com.xinxinsoft.service.appOpenService.PMSService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.groupcustomer.GroupCustomerService;
import com.xinxinsoft.utils.CodecUtils;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.HDFS.utils.HDFSUtils;
import com.xinxinsoft.utils.MD5;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.util.*;

/**
 * 　　 ,?iヽ..
 * 　　ノ?,, ヽミ
 * 　(?,,／ ) 　ヽ?～—～′′ヾ?ミミミ彡
 * 　　　　 　    　  ）
 * 　　　　(　、 ..）_＿彡( ,,.ノ
 * 　　　　/／（ ?　　　 ?.ノ (
 * 　 　　 //　　＼Ｙ?　.. 〆　.い
 * 　　 （?　　　　　 く?　　 //
 * 　　　　　　　　　　　 く?
 * ----------------------------------------------------------
 * 　　　　　　　　　　　有一只没头没心的鸟。
 * ----------------------------------------------------------
 *
 * @path: com.xinxinsoft.action.appOpenAction.PMSAction
 * @description: 产品管理访问Action
 * @author: WF
 * @date: 2020-04-14 14:56
 **/
public class PMSAction  extends BaseAction {
    @Resource(name = "GroupCustomerService")
    GroupCustomerService groupCustomerService;

    private static final Logger logger = Logger.getLogger(PMSAction.class);
    private SystemUserService systemUserService;

    private PMSService pmsService;


    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public PMSService getPmsService() {
        return pmsService;
    }

    public void setPmsService(PMSService pmsService) {
        this.pmsService = pmsService;
    }

    /**
     * 公共处理请求参数
     * @param map
     */
    private PackOprInfoVO getReqPatarms(Map<String, String> map){

        HttpServletResponse resp = getResponse();
        resp.setHeader("Access-Control-Allow-Origin", "*");
        resp.setHeader("Access-Control-Allow-Methods", "POST, GET, PUT, OPTIONS, DELETE");
        resp.setHeader("Access-Control-Max-Age", "7200");
        resp.setHeader("Access-Control-Allow-Headers", "x-requested-with, Content-Type");
        resp.setHeader("Access-Control-Allow-Credentials", "true");

        PackOprInfoVO oprInfoVO = new PackOprInfoVO();
            oprInfoVO.setGroup_id(StringUtils.isBlank(getString("u_group_id"))?"":getString("u_group_id"));
        oprInfoVO.setLogin_no(StringUtils.isBlank(getString("u_login_no"))?"":getString("u_login_no"));
        oprInfoVO.setOp_code(StringUtils.isBlank(getString("u_op_code"))? "":getString("u_op_code"));
        oprInfoVO.setPower_right(StringUtils.isBlank(getString("u_power_right"))?"":getString("u_power_right"));
        oprInfoVO.setRegion_id(StringUtils.isBlank(getString("u_region_id"))?"":getString("u_region_id"));
        oprInfoVO.setOp_note(StringUtils.isBlank(getString("u_op_note"))?"":getString("u_op_note"));

        if (!StringUtils.isBlank(getString("u_group_id"))){
            map.put("u_group_id",getString("u_group_id"));
        }
        if (!StringUtils.isBlank(getString("u_login_no"))){
            map.put("u_login_no",getString("u_login_no"));
        }
        if (!StringUtils.isBlank(getString("u_op_code"))){
            map.put("u_op_code",getString("u_op_code"));
        }
        if (!StringUtils.isBlank(getString("u_power_right"))){
            map.put("u_power_right",getString("u_power_right"));
        }
        if (!StringUtils.isBlank(getString("u_region_id"))){
            map.put("u_region_id",getString("u_region_id"));
        }
        if (!StringUtils.isBlank(getString("u_op_note"))){
            map.put("u_op_note",getString("u_op_note"));
        }
        return oprInfoVO;
    }
    /**
     * 产品属性查询接口
     */
    public void queryProdDynamicprodAttrInitSvcEntity(){
        Result result = ResultGenerator.genSuccessResult();
        String region_code = this.getString("region_code");
        String login_no = this.getString("login_no");
        String region_id = this.getString("region_id");
        String unit_id = this.getString("unit_id");
        String busi_req_class = this.getString("busi_req_class");
        String prod_id = this.getString("prod_id");
        String prod_prcid = this.getString("prod_prcid");
        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank(region_code)){
            map.put("region_code",region_code);
        }
        if (!StringUtils.isBlank(login_no)){
            map.put("login_no",login_no);
        }
        if (!StringUtils.isBlank(region_id)){
            map.put("region_id",region_id);
        }
        if (!StringUtils.isBlank(unit_id)){
            map.put("unit_id",unit_id);
        }  if (!StringUtils.isBlank(busi_req_class)){
            map.put("busi_req_class",busi_req_class);
        }  if (!StringUtils.isBlank(prod_id)){
            map.put("prod_id",prod_id);
        }  if (!StringUtils.isBlank(prod_prcid)){
            map.put("prod_prcid",prod_prcid);
        }
            result = CMCCRHOrderOpenService.getInstance().ProdDynamicprodAttrInitSvcEntity(region_code,login_no,region_id,unit_id,busi_req_class,prod_id,prod_prcid);

            if(result.getCode()==200){
                result.setData(CommonDates.setProdParams(result.getData()));
            }

        Write(result.toString());
    }

    
    
    //******************************融合产品***************************************
    /**
     * 集团产品分类服务（已测试）
     */
    public void qryGroupLabelInfoSvr() {
        Result result = ResultGenerator.genSuccessResult();
        String label_class_id = this.getString("p_label_class_id");
        String label_id = StringUtils.isBlank(this.getString("p_label_id")) ? "RHGOODS001" : this.getString("p_label_id");

        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( label_class_id)){
            map.put("p_label_class_id",label_class_id);
        }
        if (!StringUtils.isBlank(label_id)){
            map.put("p_label_id",label_id);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);
       // if(MD5.isSing(map, sing)) {
            result = CMCCRHOrderOpenService.getInstance().qryGroupLabelInfoSvr(label_class_id, label_id,oprInfoVO);

        //}else{
          //  result = ResultGenerator.genFailResult("参数校验异常");
        //}
        Write(result.toString());

        if(result.getCode() == 200 ){
            try {
                pmsService.analyticalDealMasterProInfo(result.getData());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }
    /**
     * 集团产品构成查询（已测试）
     */
    public void qryProdDetailAndMbrSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String unit_id = this.getString("p_unit_id");
        String packge_prc = this.getString("p_packge_prc");
        String page_amount = this.getString("p_page_amount");
        String page_num = this.getString("p_page_num");
        String prod_id = this.getString("p_prod_id");
        String team_id = this.getString("p_team_id");
        String group_type = this.getString("p_group_type");
        String p_group_id = this.getString("p_group_id");
        String p_cust_id = this.getString("p_cust_id");
        String p_id_no = this.getString("p_id_no");

        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( unit_id)){
            map.put("p_unit_id",unit_id);
        }if (!StringUtils.isBlank( packge_prc)){
            map.put("p_packge_prc",packge_prc);
        }
        if (!StringUtils.isBlank(page_amount)){
            map.put("p_page_amount",page_amount);
        } if (!StringUtils.isBlank(page_num)){
            map.put("p_page_num",page_num);
        } if (!StringUtils.isBlank(prod_id)){
            map.put("p_prod_id",prod_id);
        } if (!StringUtils.isBlank(team_id)){
            map.put("p_team_id",team_id);
        } if (!StringUtils.isBlank(group_type)){
            map.put("p_group_type",group_type);
        }
        if (!StringUtils.isBlank(p_id_no)){
            map.put("p_id_no",p_id_no);
        }
        if (!StringUtils.isBlank(p_cust_id)){
            map.put("p_cust_id",p_cust_id);
        }
        if (!StringUtils.isBlank(p_group_id)){
            map.put("p_group_id",p_group_id);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);
       // if(MD5.isSing(map, sing)) {
            result =CMCCRHOrderOpenService.getInstance().qryProdDetailAndMbrSvr(unit_id, packge_prc, page_amount, page_num, prod_id, team_id, group_type, p_cust_id,p_group_id,p_id_no,oprInfoVO);
       // }else{
          //  result = ResultGenerator.genFailResult("参数校验异常");
       // }
        Write(result.toString());

        if(result.getCode() == 200 ){
            try {
                pmsService.analyticalMembershipInfo(packge_prc,prod_id,group_type,team_id,result.getData());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 集团产品查资费服务（已测试）
     */
    public void qryGroupGoodsPrcSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String label_class_id = this.getString("p_label_class_id");
        String label_id = StringUtils.isBlank(this.getString("p_label_id")) ? "RHGOODS001" : this.getString("p_label_id");
        String cur_page = this.getString("p_cur_page");
        String page_size = this.getString("p_page_size");

        
        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( label_class_id)){
            map.put("p_label_class_id",label_class_id);
        }
        if (!StringUtils.isBlank(label_id)){
            map.put("p_label_id",label_id);
        }if (!StringUtils.isBlank(cur_page)){
            map.put("p_cur_page",cur_page);
        }if (!StringUtils.isBlank(page_size)){
            map.put("p_page_size",page_size);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);
        //if(MD5.isSing(map, sing)) {
            result = CMCCRHOrderOpenService.getInstance().qryGroupGoodsPrcSvr(label_class_id, label_id, cur_page, page_size,oprInfoVO);
        //}else{
         //   result = ResultGenerator.genFailResult("参数校验异常");
       // }
        Write(result.toString());

        if(result.getCode() == 200 ){
            try {
                pmsService.analyticalDealProductInfo(result.getData());
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 产品||成员属性查询展示（已测试）
     */
    public void qryIAttrStyleCtrlCoSvc(){
        Result result = ResultGenerator.genSuccessResult();
        String id_no = this.getString("p_id_no");
        String prod_id = this.getString("p_prod_id");
        String master_serv_id = this.getString("p_master_serv_id");
        String prod_prcid = this.getString("p_prod_prcid");
        String member_role_id = this.getString("p_member_role_id");
        String grp_no = this.getString("p_grp_no");
        String group_type = this.getString("p_group_type");
        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( id_no)){
            map.put("p_id_no",id_no);
        }
        if (!StringUtils.isBlank(prod_id)){
            map.put("p_prod_id",prod_id);
        } if (!StringUtils.isBlank(master_serv_id)){
            map.put("p_master_serv_id",master_serv_id);
        } if (!StringUtils.isBlank(prod_prcid)){
            map.put("p_prod_prcid",prod_prcid);
        } if (!StringUtils.isBlank(member_role_id)){
            map.put("p_member_role_id",member_role_id);
        } if (!StringUtils.isBlank(grp_no)){
            map.put("p_grp_no",grp_no);
        }if (!StringUtils.isBlank(group_type)){
            map.put("p_group_type",group_type);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);
       // if(MD5.isSing(map, sing)) {
            result = CMCCRHOrderOpenService.getInstance().qryAttrStyleCtrlSvr(id_no,prod_id,master_serv_id,prod_prcid,member_role_id,grp_no,group_type,oprInfoVO);
       // }else{
       //     result = ResultGenerator.genFailResult("参数校验异常");
       // }
        Write(result.toString());
    }

    /**
     * 资费校验接口（已测试）
     */
    public void qryChkBusiRelSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String brand_id= this.getString("p_brand_id");
        String unit_id= this.getString("p_unit_id");
        String eff_date= this.getString("p_eff_date");
        String exp_date= this.getString("p_exp_date");
        String op_code= this.getString("p_op_code");
        String op_type  = this.getString("p_op_type");
        String prod_id = this.getString("p_prod_id");
        String prod_prcid = this.getString("p_prod_prcid");
        String p_cust_id = this.getString("p_cust_id");

        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( brand_id)){
            map.put("p_brand_id",brand_id);
        }
        if (!StringUtils.isBlank(unit_id)){
            map.put("p_unit_id",unit_id);
        } if (!StringUtils.isBlank(eff_date)){
            map.put("p_eff_date",eff_date);
        } if (!StringUtils.isBlank(exp_date)){
            map.put("p_exp_date",exp_date);
        } if (!StringUtils.isBlank(op_code)){
            map.put("p_op_code",op_code);
        }if (!StringUtils.isBlank(op_type)){
            map.put("p_op_type",op_type);
        }if (!StringUtils.isBlank(prod_id)){
            map.put("p_prod_id",prod_id);
        }if (!StringUtils.isBlank(prod_prcid)){
            map.put("p_prod_prcid",prod_prcid);
        }if (!StringUtils.isBlank(p_cust_id)){
            map.put("p_cust_id",p_cust_id);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);

       // if(MD5.isSing(map, sing)) {
            result = CMCCRHOrderOpenService.getInstance().qryChkBusiRelSvr(brand_id, unit_id, eff_date, exp_date, op_code, op_type, prod_id, prod_prcid,p_cust_id, oprInfoVO);
       // }else{
       //     result = ResultGenerator.genFailResult("参数校验异常");
       // }
        Write(result.toString());
    }

    /**
     * 已有成员信息详细查询（已测试）
     * @Title: qryPUserBaseInfoSvr
     * @Description: TODO(主要用于添加已有成员时，校验成员是否存在)
     * @throws
     */
    public void qryPUserBaseInfoSvr(){
    	 Result result = ResultGenerator.genSuccessResult();
         String phone_no = this.getString("p_phone_no");
         String id_no = StringUtils.isBlank(this.getString("p_id_no")) ? "0" : this.getString("p_id_no");

         String sing = this.getString("sing");
         Map<String, String> map = new HashMap<String, String>();
         if (!StringUtils.isBlank( phone_no)){
             map.put("p_phone_no",phone_no);
         }
         if (!StringUtils.isBlank(id_no)){
             map.put("p_id_no",id_no);
         }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);

       // if(MD5.isSing(map, sing)) {
             result = CMCCRHOrderOpenService.getInstance().qryPUserBaseInfoSvr(id_no,phone_no,oprInfoVO);

       //  }else{
       //      result = ResultGenerator.genFailResult("参数校验异常");
      //   }
         Write(result.toString());
    }
    
    /**
     * 已有专线号码查询（已测试）
     * @Title: qryLinesMbrInfoSvr
     * @Description: TODO(主要用于添加已有专线成员时查询已有专线成员信息)
     * @param:    
     * @return: void   
     * @throws
     */
    public void qryLinesMbrInfoSvr(){
   	 Result result = ResultGenerator.genSuccessResult();
        String cust_id = this.getString("p_unit_id");
        String member_role_id = this.getString("p_member_role_id");
        String par_prod_prcid = this.getString("p_par_prod_prcid");
        String p_cust_id = this.getString("p_cust_id");


        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( cust_id)){
            map.put("p_cust_id",cust_id);
        }
        if (!StringUtils.isBlank(member_role_id)){
            map.put("p_member_role_id",member_role_id);
        }
        if (!StringUtils.isBlank(par_prod_prcid)){
            map.put("p_par_prod_prcid",par_prod_prcid);
        }        if (!StringUtils.isBlank(p_cust_id)){
            map.put("p_cust_id",p_cust_id);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);

      //  if(MD5.isSing(map, sing)) {

            result = CMCCRHOrderOpenService.getInstance().qryLinesMbrInfoSvr(cust_id,p_cust_id, member_role_id, par_prod_prcid, oprInfoVO);
     //   }else{
     //       result = ResultGenerator.genFailResult("参数校验异常");
      //  }
        Write(result.toString());
//        Write("{\"code\":200,\"message\":\"\",\"data\":{\"LINE_LIST\":[{\"LINE_ADDRESS\":\"成都市金牛区迎宾大道366号总经理办公室\",\"LINE_PHONE_NO\":\"10011159670\"}]}}");
   }
    
   /**
    * 新开户固话号码数据查询
    * @Title: qryPhListSvr
    * @Description: TODO(主要用于新开户固话号码数据查询)
    * @param:    
    * @return: void   
    * @throws
    */
    public void qryPhListSvr(){
      	 Result result = ResultGenerator.genSuccessResult();
           String current_page = this.getString("p_current_page");
           String page_size = this.getString("p_page_size");
           String query_flag = this.getString("p_query_flag");
           String sub_phone = this.getString("p_sub_phone");

           String sing = this.getString("sing");
           Map<String, String> map = new HashMap<String, String>();
           if (!StringUtils.isBlank( current_page)){
               map.put("p_current_page",current_page);
           }
           if (!StringUtils.isBlank(page_size)){
               map.put("p_page_size",page_size);
           }
           if (!StringUtils.isBlank(query_flag)){
               map.put("p_query_flag",query_flag);
           }   if (!StringUtils.isBlank(sub_phone)){
               map.put("p_sub_phone",sub_phone);
           }
        PackOprInfoVO oprInfoVO = getReqPatarms(map);
           if(MD5.isSing(map, sing)) {
               result = CMCCRHOrderOpenService.getInstance().qryPhListSvr(current_page, page_size, query_flag, oprInfoVO,sub_phone);

           }else{
               result = ResultGenerator.genFailResult("参数校验异常");
           }
           Write(result.toString());
      }


      /**
       * 查询序列
       * @description: qrypubGetSeqSvr
       * <AUTHOR>
       * @version 1.1.0
       * @data 2020/4/26 15:55
       * @param
       * @return {@link }
       * @throws
       */
      public void qrypubGetSeqSvr(){
          Result result = ResultGenerator.genSuccessResult();
          String p_accept_type = this.getString("p_accept_type");
          String p_region_id = this.getString("p_region_id");
          String sing = this.getString("sing");
          Map<String, String> map = new HashMap<String, String>();
          if (!StringUtils.isBlank( p_accept_type)){
              map.put("p_accept_type",p_accept_type);
          }
          if (!StringUtils.isBlank(p_region_id)){
              map.put("p_region_id",p_region_id);
          }
          PackOprInfoVO oprInfoVO=   getReqPatarms(map);
          if(MD5.isSing(map, sing)) {
                result = CMCCRHOrderOpenService.getInstance().qrypubGetSeqSvr(p_accept_type,p_region_id,oprInfoVO);
          }else{
              result = ResultGenerator.genFailResult("参数校验异常");
          }
          Write(result.toString());
      }
      
      /**
       *
       *生成服务号码接口
       * @description: qryAutNoSvr
       * <AUTHOR>
       * @version 1.1.0
       * @data 2020/4/26 18:18 
       * @param 
       * @return {@link  }
       * @throws
       */
    public void qryAutNoSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String p_seq_type = this.getString("p_seq_type");
        String p_master_serv_id = this.getString("p_master_serv_id");
        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( p_seq_type)){
            map.put("p_seq_type",p_seq_type);
        }
        if (!StringUtils.isBlank(p_master_serv_id)){
            map.put("p_master_serv_id",p_master_serv_id);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);
        if(MD5.isSing(map, sing)) {
            result = CMCCRHOrderOpenService.getInstance().qryAutNoSvr(p_seq_type,p_master_serv_id,oprInfoVO);
        }else{
            result = ResultGenerator.genFailResult("参数校验异常");
        }
        Write(result.toString());
    }





    /**
     *
     * @Title: qryIDynamicSqlQryAoSvr（已测试）
     * @Description: TODO(根据集团查询集团固话营业执照信息查询)
     */
    public void qryIDynamicSqlQryAoSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String p_code_value = this.getString("p_cust_id");
        String p_svc_name = this.getString("p_svc_name");
        String sing = this.getString("sing");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( p_code_value)){
            map.put("p_cust_id",p_code_value);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);
        result = CMCCRHOrderOpenService.getInstance().qryIDynamicSqlQryAoSvr(p_code_value,p_svc_name, oprInfoVO);

        Write(result.toString());
    }

    /**
     *
     * @Title: qryNumQryAoSvr
     * @Description:
     */
    public void qryNumQryAoSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String p_code_value = this.getString("p_code_value");
        String p_svc_name = this.getString("p_svc_name");
        String p_id_no = this.getString("p_id_no");
        String p_authen_op_code = this.getString("p_authen_op_code");
        String t_is_substr  = this.getString("t_is_substr");
        Map<String, String> map = new HashMap<String, String>();
        Map<String, Object> maps =getReqPartams();
        PackOprInfoVO oprInfoVO = getReqPatarms(map);
        result = CMCCRHOrderOpenService.getInstance().qryNumQryAoSvr(p_authen_op_code,p_id_no,p_code_value,p_svc_name, oprInfoVO,maps);

        if(result.getCode()==200 && "1".equals(t_is_substr) ){
            JSONArray newList = new JSONArray();
                JSONObject list= JSONObject.fromObject(result.getData());
                if(CMCCOpenService.having(list, "LIST")){
                    JSONArray jsonArray = JSONArray.fromObject(list.get("LIST"));
                    Iterator iterator = jsonArray.iterator();
                    while (iterator.hasNext()){
                        JSONObject jsonObject = JSONObject.fromObject(iterator.next());
                        JSONObject object =new JSONObject();
                              object.put("CODE_NAME",jsonObject.getString("CODE_NAME").substring(1,jsonObject.getString("CODE_NAME").length()));
                        newList.add(object);
                    }
                    result.setData(newList);
                }else{
                    result = ResultGenerator.genFailResult("DATA 节点下没找到LIST节点");

                }
            }

        Write(result.toString());
    }

    /**
     *
     * @Title: qryIDynamicSqlQryAoSvr（已测试）
     * @Description: TODO(根据营业执照号码获取固话责任人信息查询)
     */
    public void qryGrpCredentialSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String p_id_iccid = this.getString("p_id_iccid");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( p_id_iccid)){
            map.put("p_id_iccid",p_id_iccid);
        }
        PackOprInfoVO oprInfoVO=  getReqPatarms(map);
             result = CMCCRHOrderOpenService.getInstance().qryGrpCredentialSvr(p_id_iccid, oprInfoVO);

        Write(result.toString());
    }

    /**
     *
     * @Title: qrypPubBaseInfoSvr （已测试）
     * @Description: TODO(根据身份证号码查询固话实际使用人根据证件信息查询)
     */
    public void qrypPubBaseInfoSvr(){
        Result result = ResultGenerator.genSuccessResult();
        String p_id_iccid = this.getString("p_id_iccid");
        Map<String, String> map = new HashMap<String, String>();
        if (!StringUtils.isBlank( p_id_iccid)){
            map.put("p_id_iccid",p_id_iccid);
        }
        PackOprInfoVO oprInfoVO=   getReqPatarms(map);
        result = CMCCRHOrderOpenService.getInstance().qrypPubBaseInfoSvr(p_id_iccid, oprInfoVO);
        Write(result.toString());
    }

    /**
     * 查询cust_id
     */
    public void qryAoSvc(){
            Result result = ResultGenerator.genSuccessResult();
            String p_unit_id = this.getString("p_unit_id");
            String sing = this.getString("sing");
            Map<String, String> map = new HashMap<String, String>();
            if (!StringUtils.isBlank( p_unit_id)){
                map.put("p_unit_id",p_unit_id);
            }
            PackOprInfoVO oprInfoVO=   getReqPatarms(map);
            result = CMCCRHOrderOpenService.getInstance().qryAoSvc(p_unit_id, oprInfoVO);

            Write(result.toString());
        }
   /**
     * 查询付费账户
     */
    public void qryAoCSvc(){
            Result result = ResultGenerator.genSuccessResult();
            String p_cust_id = this.getString("p_cust_id");
            String p_page_no = this.getString("p_page_no");
            String p_page_size = this.getString("p_page_size");
            String masterServId = this.getString("p_masterServId");
            String sing = this.getString("sing");
            Map<String, String> map = new HashMap<String, String>();

            PackOprInfoVO oprInfoVO=   getReqPatarms(map);
                    result = CMCCRHOrderOpenService.getInstance().qryAoCSvc(p_cust_id,p_page_no,p_page_size, StringUtils.isBlank(masterServId)?"2530":masterServId, oprInfoVO);

            Write(result.toString());
        }

    /**
     * 固话新开报文格式
     * @Title: fixPhoneSubmitSvr
     * @Description: TODO(固话新开报文格式，用于包装在fixPhoneSubmitSvr服务中使用)
     * @param:  oprInfoVO 操作信息
     * @return: String
     * @throws
     */
    public void fixPhoneSubmitSvr(){
            Result result = ResultGenerator.genSuccessResult();
            String sing = this.getString("sing");
            Map<String, String> map = new HashMap<String, String>();
            PackOprInfoVO oprInfoVO=   getReqPatarms(map);
            // if(MD5.isSing(map, sing)) {
                 result = CMCCRHOrderOpenService.getInstance().fixPhoneSubmitSvr(oprInfoVO);
            // }else{
        //        result = ResultGenerator.genFailResult("参数校验异常");
            // }
            Write(result.toString());
        }

    /**
     * 酒店固话新开报文
     */
    public void hotelFixPhoneSubmintSvr(){
            Map<String, String> map = new HashMap<String, String>();
            PackOprInfoVO oprInfoVO=   getReqPatarms(map);
            Write(CMCCRHOrderOpenService.getInstance().hotelFixPhoneSubmintSvr(oprInfoVO).toString());
        }

    /**
     * 酒店宽带新开报文
     */
    public void hotelBroadbandSubmintSvr() {
        Map<String, String> map = new HashMap<String, String>();
        Map<String, Object> maps =getReqPartams();
        PackOprInfoVO oprInfoVO = getReqPatarms(map);
        if (MapUtils.isNotEmpty(map) && map.size() > 0) {
            Write(CMCCRHOrderOpenService.getInstance().hotelBroadbandSubmintSvr(MapUtils.getString(maps, "p_parameter_json"), oprInfoVO).toString());
        }else{
            Write(ResultGenerator.genFailResult("为获取到参数").toString());
        }
    }
    /**
     * 酒店成员报文
     */
    public void hotelFixMemberSvc() {
        Map<String, String> map = new HashMap<String, String>();
        Map<String, Object> maps =getReqPartams();
        PackOprInfoVO oprInfoVO = getReqPatarms(map);
        if (MapUtils.isNotEmpty(map) && map.size() > 0) {
            Write(CMCCRHOrderOpenService.getInstance().hotelFixMemberSvc(oprInfoVO,
                    MapUtils.getString(maps, "trans_team_id"),
                    MapUtils.getString(maps, "grp_phone_no")
                ,MapUtils.getString(maps, "mbr_flag","0")).toString());
        }else{
            Write(ResultGenerator.genFailResult("为获取到参数").toString());
        }
    }

    /**
     *查询订单信息
     */
    public void qryOrderInfoSvr(){
        Result result = ResultGenerator.genSuccessResult();
        Map<String , Object> parMap = getReqPartams();
        if(parMap.size() >0){
            result.setData(JSONObject.fromObject(pmsService.qryOrderLists(parMap)));
        }else{
            result = ResultGenerator.genFailResult("参数异常");
        }
        Write(result.toString());
    }

    public void qryOrderTrackInfoSvr(){
        Result result = ResultGenerator.genSuccessResult();
        Map<String , Object> parMap = getReqPartams();
        if(parMap.size() >0){
            result.setData((pmsService.qryOrderTransDataByOrderNo(parMap)));
        }else{
            result = ResultGenerator.genFailResult("参数异常");
        }
        Write(result.toString());
    }




    /**
     * 查询专线成员资费
     */
    public void inquirySpecialPostageConfigureSvc() {
        Result result = ResultGenerator.genSuccessResult();
        String p_svc_name = this.getString("p_svc_name");
        String p_cust_id = this.getString("p_cust_id");
        String p_code_name = this.getString("p_code_name");
        String p_begin_value = this.getString("p_begin_value");
        String p_page_no = this.getString("p_page_no");
        String p_page_size = this.getString("p_page_size");
        Map<String, String> map = new HashMap<String, String>();
            PackOprInfoVO oprInfoVO=   getReqPatarms(map);
            result = CMCCRHOrderOpenService.getInstance().inquirySpecialPostageConfigureSvc( p_cust_id,p_svc_name, p_begin_value, p_page_no, p_page_size, p_code_name,  oprInfoVO);
        Write(result.toString());
    }
    /**
     * 专线号码获取服务
     */
    public void getLinesMbraddSvr() {
        Result result = ResultGenerator.genSuccessResult();
        String p_seq_type = this.getString("p_seq_type");
        String p_master_serv_id = this.getString("p_master_serv_id");
        String p_code_class = this.getString("p_code_class");
        String p_new_region_id = this.getString("p_new_region_id");
        String p_prod_id = this.getString("p_prod_id");
        Map<String, String> map = new HashMap<String, String>();
            PackOprInfoVO oprInfoVO=   getReqPatarms(map);
             result = CMCCRHOrderOpenService.getInstance().getLinesMbraddSvr(p_seq_type,p_master_serv_id,p_code_class, p_new_region_id,p_prod_id,oprInfoVO);
        Write(result.toString());
    }

    /**
     * 服务代码，企业代码查询接口
     */
    public void qryResSvcNumEntity() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(map) && oprInfoVO != null){
            result = CMCCMASOpenService.getInstance().qryResSvcNumEntity(MapUtils.getString(map,"p_query_flag","0")
                    ,MapUtils.getString(map,"p_svc_no")
                    ,MapUtils.getString(map,"p_sub_value","J0000082")
                    ,MapUtils.getString(map,"p_op_code","6427")
                    ,MapUtils.getString(map,"p_req_num","13"),oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

      Write(result.toString());
    }

    /**
     * 业务代码生成接口
     */
    public void qryBusiCodeByProdEntity() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();

        if(MapUtils.isNotEmpty(map)){
            result = CMCCMASOpenService.getInstance().qryBusiCodeByProdEntity(MapUtils.getString(map,"u_login_no")
                    ,MapUtils.getString(map,"p_provi_code")
                    ,MapUtils.getString(map,"p_show_id")
                    ,MapUtils.getString(map,"p_op_code")
                    ,MapUtils.getString(map,"p_prod_id")
                    ,MapUtils.getString(map,"p_prc_id") );
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }

    /**
     * 业务保障等级  计算
     */
    public void qryICommonServiceAoSvc() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        if(MapUtils.isNotEmpty(map)){
            result = CMCCRHOrderOpenService.getInstance().qryICommonServiceAoSvc(MapUtils.getString(map,"p_bandwidth")
                    ,MapUtils.getString(map,"p_prod_id")
                    ,MapUtils.getString(map,"p_cust_level","4")  );
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }
    /**
     * 查询宽带主询费
     */
    public void qryKdMainPrcSvc() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(map)){
            result = CMCCRHOrderOpenService.getInstance().qryKdMainPrc(MapUtils.getString(map,"p_authen_op_code","6622")
                    ,MapUtils.getString(map,"p_role_id","50065")
                    ,MapUtils.getString(map,"p_package_prc","ACCZ65928")
                    ,MapUtils.getString(map,"p_code_class","PDDEAL") ,oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }
    /**
     * 批量开户固话号码预占接口
     */
    public void qryAndUseFixPhoneSvc() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(map)){
            result = CMCCRHOrderOpenService.getInstance().qryAndUseFixPhoneSvc(MapUtils.getString(map,"p_room_num","0") ,oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }

    /**
     * 判断该集团下是否存在V网
     */
    public void queryIsVWSvc() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(map)){
            result = CMCCRHOrderOpenService.getInstance().queryIsVWSvc(MapUtils.getString(map,"p_authen_op_code","1436"),
                    MapUtils.getString(map,"p_inParamA")  ,
                    MapUtils.getString(map,"p_inParamB","") ,
                    MapUtils.getString(map,"p_routeName","") ,
                    MapUtils.getString(map,"p_id_no","") ,
                    MapUtils.getString(map,"p_inParamA") ,oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }
    /**
     * 查询CUST_CD
     */
    public void iQryCustInfoSvc() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(map)){
            result = CMCCRHOrderOpenService.getInstance().iQryCustInfoSvc(
                    MapUtils.getString(map,"p_contact_name",""),
                    MapUtils.getString(map,"p_unit_id")  ,
                    MapUtils.getIntValue(map,"p_page_num",1) ,
                    MapUtils.getString(map,"p_op_type","A") ,
                    MapUtils.getString(map,"p_contact_phone","") ,
                    MapUtils.getString(map,"p_cust_address","") ,
                    MapUtils.getIntValue(map,"p_op_mode",2) ,
                    MapUtils.getString(map,"p_owned_chnl_id") ,
                    MapUtils.getString(map,"p_cust_level","") ,
                    MapUtils.getIntValue(map,"p_total_count",0) ,
                    MapUtils.getIntValue(map,"p_query_mode",2) ,
                    MapUtils.getIntValue(map,"p_type_code",3) ,
                    MapUtils.getString(map,"p_cust_name","") ,
                    MapUtils.getString(map,"p_province_group","10008") ,
                    oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }


    /**
     * 新开固话号码查询 PHONE_NO
     */
    public void qryNoByTDSvc() {
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(map) && oprInfoVO != null){
            result = CMCCRHOrderOpenService.getInstance().qryNoByTDSvc(
                    MapUtils.getString(map,"p_phone_no")
                    ,MapUtils.getString(map,"p_no_flag","1"),oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }


    /**
     * 查询xx资费
     */
    public void qryTariiSvc(){
        Result result = ResultGenerator.genSuccessResult();
        Map<String, String> mapss = new HashMap<>();
            mapss.put("SVC_NAME",StringUtils.isBlank(this.getString("p_svc_name"))?"d6622A007":this.getString("p_svc_name"));
            mapss.put("PROD_PRCID",this.getString("p_prod_prcid"));
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(mapss) && oprInfoVO != null){
            result = CMCCRHOrderOpenService.getInstance().queryIsVWParmSvc(
                    StringUtils.isBlank(this.getString("p_authen_op_code"))?"4585":this.getString("p_authen_op_code")
                    ,mapss,oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }

    /**
     *  boss接口
     * 协同单列表查询
     * @return
     */
    public void listQuery(){
        Result result = ResultGenerator.genSuccessResult();
        String grpNo = this.getString("grpNo");
        String orderId = this.getString("orderId");
        String phoneNo = this.getString("phoneNo");
        //分页
        String pageSize = this.getString("pageSize");
        String pageNum = this.getString("pageNum");
        Map<String, String> map = new HashMap<String, String>();
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if (!StringUtils.isBlank(grpNo)){
            map.put("grpNo",grpNo);
        }
        if (!StringUtils.isBlank(orderId)){
            map.put("orderId",orderId);
        }
        if (!StringUtils.isBlank(phoneNo)){
            map.put("phoneNo",phoneNo);
        }
        if (!StringUtils.isBlank(pageSize)){
            map.put("pageSize",pageSize);
        }
        if (!StringUtils.isBlank(pageNum)){
            map.put("pageNum",pageNum);
        }
        result = CMCCRHOrderOpenService.getInstance().listQueryAttrInitSvcEntity(this.getString("grpNo"),
                this.getString("orderId"),
                this.getString("phoneNo"),
                this.getString("pageSize"),
                this.getString("pageNum"),
                this.getString("startTime"),
                this.getString("endTime"),oprInfoVO);
        Write(result.toString());

    }

    /**
     * boss接口
     * 订单详情查询
     * @return
     */
    public void OrderDetails(){
        Result result = ResultGenerator.genSuccessResult();
        Map<String, String> mapss = new HashMap<>();
        mapss.put("ORDER_ID",this.getString("orderId"));
        Map<String, String> maps = new HashMap<>();
        PackOprInfoVO oprInfoVO = getReqPatarms(maps);
        if(MapUtils.isNotEmpty(mapss) && oprInfoVO != null){
            result = CMCCRHOrderOpenService.getInstance().OrderDetailsAttrInitSvcEntity(this.getString("orderId"),oprInfoVO);
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }

        Write(result.toString());
    }

    /**
     * 导出数据
     */
    public void exportExcel()
    {
        //Map{phoneNo=789789, end_time=2020-09-17, pageNo=1, pageSize=10, start_time=2020-09-01, grpNo=123123, orderId=456456}
        Map<String,Object> map=new HashMap<>();
        try
        {
            String orderId = this.getString("orderId");
            String grpNo = this.getString("grpNo");
            String phoneNo = this.getString("phoneNo");
            String start_time = this.getString("start_time");
            String end_time = this.getString("end_time");
            String company_code = this.getString("company_code");
            String prod_id = this.getString("prod_id");
            map.put("orderId",orderId);
            map.put("grpNo",grpNo);
            map.put("phoneNo",phoneNo);
            map.put("company_code",company_code);
            map.put("prod_id",prod_id);
            map.put("start_time",start_time);
            map.put("end_time",end_time);
            //System.out.println("接收时的"+map);
            List<Map<String, Object>>  queryList = pmsService.findQueryList(map, user);
            //System.out.println(queryList);
            pmsService.exportExcelToJxl(queryList);
        }
        catch(Exception e)
        {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询订单
     */
    public void qryOrders(){
        try{
            Map<String, Object> map =getReqPartams();
            //System.out.println("是否传入"+map.get("company_code"));
            //System.out.println("Map"+map);
            LayuiPage  layuiPage = pmsService.qureyListOrders(map,this.user);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(layuiPage);
            Write(json);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 查询协同订单信息
     */
    public void qryTransparencyOrder(){
        try{
            Map<String, Object> map = getReqPartams();
            LayuiPage  layuiPage = pmsService.qryTransparencyOrder(map,this.user);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(layuiPage);
            Write(json);
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    /**
     * 根据流程ID 查询流程
     */
    public void getWfInfoSvr(){
        Result result = ResultGenerator.genSuccessResult();
        Map<String, Object> map =getReqPartams();
        Map<String, String> maps = new HashMap<>();
        if(MapUtils.isNotEmpty(map) && user!=null){
            result = CMCCRHOrderOpenService.getInstance().getWfInfo(MapUtils.getString(map,"p_wfId"),user.getBossUserName());
        }else{
            result.setCode(ResultCode.FAIL);
            result.setMessage("为获取到请求的参数");
        }
        Write(result.toString());
    }
    /**
     * 获取附件消息
     */
    public void qryContractInfo() {
        String id = getString("id");
        List<Map<String, String>> s = pmsService.qryContractInfo(id,this.user);
        Write(JSONHelper.Serialize(s));
    }

    /**
     * 根据订单编号查询 hdfs 的路径，再hdfs 获取文件字节，然后下载
     */
    public void downContractHdfs(){
        Result result = ResultGenerator.genSuccessResult();
        try {
            String id = getString("id");
            Map<String, String> map = pmsService.qryDownContPath(id,this.user);
            if(!StringUtils.isBlank(MapUtils.getString(map,"PDFATTURL"))) {
                HttpServletResponse response = ServletActionContext.getResponse();
                if (HDFSUtils.isExistsFile(MapUtils.getString(map,"PDFATTURL"))) {
                    byte[] data = HDFSUtils.readFileToByte(MapUtils.getString(map,"PDFATTURL"));
                    String fileName = URLEncoder.encode(MapUtils.getString(map,"CONTRACTNAME"), "UTF-8");
                    String filePix= "."+FileUpload.getLastFilePix(MapUtils.getString(map,"PDFATTURL"));
                    response.reset();
                    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName+filePix + "\"");
                    response.addHeader("Content-Length", "" + data.length);
                    response.setContentType("application/octet-stream;charset=UTF-8");
                    OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                    outputStream.write(data);
                    outputStream.flush();
                    outputStream.close();
                    response.flushBuffer();
                }else{
                    result.setCode(ResultCode.NOT_FOUND);
                    result.setMessage("文件不存在");
                }
            }else{
                result.setCode(ResultCode.FAIL);
                result.setMessage("数据异常");
            }
        }catch (Exception e){
            result.setCode(ResultCode.FAIL);
            result.setMessage("异常"+e.getMessage());
        }
        Write(result.toString());
    }

    /**
     * 根据订单编号查询 hdfs 的路径，再hdfs 获取文件字节，然后下载
     */
    public void downContractHdfsNew(){
        Result result = ResultGenerator.genSuccessResult();
        try {
            String id = getString("id");
            Map<String, String> map = pmsService.qryDownContPath(id,this.user);
            if(!StringUtils.isBlank(MapUtils.getString(map,"PDFATTURL"))) {
                HttpServletResponse response = ServletActionContext.getResponse();
                if (HDFSUtils.isExistsFile(MapUtils.getString(map,"PDFATTURL"))) {
                    byte[] data = HDFSUtils.readFileToByte(MapUtils.getString(map,"PDFATTURL"));
                    String fileName = URLEncoder.encode(MapUtils.getString(map,"CONTRACTNAME"), "UTF-8");
                    //logger.info("下载文件up=="+fileName);
                    String[] split = fileName.split("_");
                    //logger.info("获取的280为=="+split[0]);
                    GroupCustomer findDBCustomer=groupCustomerService.queryGroup(split[0]);
                    //logger.info("获取的280数据为=="+JSONObject.fromObject(findDBCustomer));
                    JSONObject jsonObject = JSONObject.fromObject(findDBCustomer);
                    String groupNameTwo = jsonObject.getString("groupName");
                    String groupName = findDBCustomer.getGroupName();
                    //logger.info("获取的280数据为1=="+findDBCustomer.getGroupName());
                    //logger.info("获取的280数据为2=="+groupNameTwo);
                    String filePix= "."+FileUpload.getLastFilePix(MapUtils.getString(map,"PDFATTURL"));
                    fileName=split[0]+"_"+groupNameTwo+filePix;
                    //logger.info("名字为=="+fileName);
                    //logger.info("名字为=="+split[0]+"_"+groupName+filePix);
                    response.reset();
                    response.setHeader("Content-Disposition", "attachment; filename=\"" + URLEncoder.encode(fileName, "UTF-8") + "\"");
                    //response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName+filePix + "\"");
                    response.addHeader("Content-Length", "" + data.length);
                    response.setContentType("application/octet-stream;charset=UTF-8");
                    OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                    outputStream.write(data);
                    outputStream.flush();
                    outputStream.close();
                    response.flushBuffer();
                }else{
                    result.setCode(ResultCode.NOT_FOUND);
                    result.setMessage("文件不存在");
                }
            }else{
                result.setCode(ResultCode.FAIL);
                result.setMessage("数据异常");
            }
        }catch (Exception e){
            result.setCode(ResultCode.FAIL);
            result.setMessage("异常"+e.getMessage());
        }
        Write(result.toString());
    }

    /**
     * 下载hdfs测试使用
     */
    public void downHdfsByURL(){
        Result result = ResultGenerator.genSuccessResult();
        try {
            String url = getString("url");

            if(!StringUtils.isBlank(url)) {
                HttpServletResponse response = ServletActionContext.getResponse();
                if (HDFSUtils.isExistsFile(url)) {
                    byte[] data = HDFSUtils.readFileToByte(url);
                    String fileName = URLEncoder.encode("hdfs_test", "UTF-8");
                    String filePix= "."+FileUpload.getLastFilePix(url);
                    response.reset();
                    response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName+filePix + "\"");
                    response.addHeader("Content-Length", "" + data.length);
                    response.setContentType("application/octet-stream;charset=UTF-8");
                    OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                    outputStream.write(data);
                    outputStream.flush();
                    outputStream.close();
                    response.flushBuffer();
                }else{
                    result.setCode(ResultCode.NOT_FOUND);
                    result.setMessage("文件不存在");
                }
            }else{
                result.setCode(ResultCode.FAIL);
                result.setMessage("数据异常");
            }
        }catch (Exception e){
            result.setCode(ResultCode.FAIL);
            result.setMessage("异常"+e.getMessage());
        }
        Write(result.toString());
    }

    /**
     * 获取项目信息
     * gcy
     * 2020-9-28
     */
    public void qryChanceProjectInfo(){
            Result result = ResultGenerator.genSuccessResult();
            Map<String, String> maps = new HashMap<>();
            maps.put("LOGIN_NO",this.getString("oper_no"));
            maps.put("UNIT_ID",this.getString("unit_id"));
            /**
             * 验证开户欠费
             * @param bossNo boss工号
             * @param unitId 集团编码
             * @param authenOpCode
             * @param regionId
             * @param loginNo
             * @param opCode
             * @return
             */
            if(MapUtils.isNotEmpty(maps)){
                result = CMCCRHOrderOpenService.getInstance().qryChanceProjectInfo(this.getString("oper_no"),
                        this.getString("unit_id"),
                        this.getString("bossNo"));
            }else{
                result.setCode(ResultCode.FAIL);
                result.setMessage("未获取到请求的参数");
            }
            Write(result.toString());
//            JSONObject jsonObject = JSONObject.fromObject(result);
//            //System.out.println(result.getCode());
//            if("200".equals(String.valueOf(result.getCode()))){
//                //System.out.println("111");
//                JSONObject data = JSONObject.fromObject(jsonObject.get("data"));
//                String chk_flag = data.get("CHK_FLAG").toString();
//                //System.out.println("输出"+chk_flag);
//                Write(chk_flag);
//            }else {
//                //System.out.println("222");
//                Write(result.getMessage());
//            }
    }

    /**
     * 手输资费带宽规则校验
     * gcy
     */
    public void bandwidthRuleCheck(){
        Result result = ResultGenerator.genSuccessResult();
        try {
            Map<String, String> maps = new HashMap<>();
            maps.put("LOGIN_NO",this.getString("login_no"));
            maps.put("PRC_ID",this.getString("prc_id"));
            maps.put("PROD_ID",this.getString("prod_id"));
            maps.put("BAND_WIDTH",this.getString("band_width"));
            maps.put("PRC_MONEY",this.getString("prc_money"));
            if(MapUtils.isNotEmpty(maps)) {
                    result = CMCCRHOrderOpenService.getInstance().bandwidthRuleCheckEntity(this.getString("login_no"),
                            this.getString("prc_id"),
                            this.getString("prod_id"),
                            this.getString("band_width"),
                            this.getString("prc_money"));
            }else{
                result.setCode(ResultCode.FAIL);
                result.setMessage("未获取到请求的参数");
            }
        }catch (Exception e){
            result.setCode(ResultCode.INTERNAL_SERVER_ERROR);
            result.setMessage("异常"+e.getMessage());
        }
        Write(result.toString());
    }

    /**
     * 一键下单项目专线，查询项目接口
     * gcy
     */
    public void queryItemsOrder(){
        Result result = ResultGenerator.genSuccessResult();
        try {
            String project_name = this.getString("project_name");//项目名称，模糊查询，非必填
            String group_name = this.getString("group_name");//区县分公司，必填
            logger.info("===>区县分公司" + group_name);
            String unit_id = this.getString("unit_id");//集团280，非必填
            String project_id = this.getString("project_id");//项目id 非必填
            //PAGE_NUM 1 PAGE_SIZE 4
            String page_num = this.getString("page_num");//页码 1
            String page_size = this.getString("page_size");//条数 4
            if(this.getString("group_name")!=null&&this.getString("group_name").equals("")==false) {
                result = CMCCRHOrderOpenService.getInstance().queryItemsOrderEntity(project_name,
                        group_name,
                        unit_id,
                        project_id,
                        page_num,
                        page_size
                );
            }else{
                result.setCode(ResultCode.FAIL);
                result.setMessage("未获取到请求的参数");
            }
        }catch (Exception e){
            result.setCode(ResultCode.INTERNAL_SERVER_ERROR);
            result.setMessage("异常"+e.getMessage());
        }
        Write(result.toString());
    }

    public void specialLineProjectQryEntity(){
        Result result = ResultGenerator.genSuccessResult();
        try {
            String REGION_CODE = getString("REGION_CODE");
            result = CMCCRHOrderOpenService.getInstance().specialLineProjectQryEntity(REGION_CODE);
            Write(result.toString());
        }catch (Exception e){
            e.printStackTrace();
            result.setCode(ResultCode.FAIL);
            result.setMessage("亲爱的同事，信息查询错误！");
            Write(result.toString());
        }
    }

    /**
     *  gcy
     *  2020-1-5
     * 查询区县乡镇面积
     */
    public void queryArea(){
        Result result = ResultGenerator.genSuccessResult();
        try {
            String province = this.getString("province");//省
            String city = this.getString("city");//市
            //logger.info("===>获取参数" + province);
            String county = this.getString("county");//区
            String tows = this.getString("tows");//街道
            String s = CMCCOpenService.getInstance().queryAreaService(province, city, county, tows);
            logger.info("查询区县乡镇面积接口 :"+s);
            Write(s);
        }catch (Exception e){
            result.setCode(ResultCode.INTERNAL_SERVER_ERROR);
            result.setMessage("异常"+e.getMessage());
        }
        Write(result.toString());
    }
}
