package com.xinxinsoft.action.pay;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.pay.payment.*;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.pay.OrderQuerySendService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.StringHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import net.sf.json.JSONObject;
import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.axis.encoding.XMLType;

import javax.xml.rpc.ParameterMode;
import java.text.SimpleDateFormat;
import java.util.*;

public class PayProviderInfoAction extends BaseAction {
    private final static String endpoint = Config.getString("APP_PAY_VOUCHER_CENTER");//支付请求WebService

    private OrderQuerySendService orderQuerySendService;
    private SystemUserService systemUserService;

    public OrderQuerySendService getOrderQuerySendService() {
        return orderQuerySendService;
    }

    public void setOrderQuerySendService(OrderQuerySendService orderQuerySendService) {
        this.orderQuerySendService = orderQuerySendService;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    /**
     * 查询页面列表
     * 支付基本信息
     */
    public void findByPage() {
        try {
            LOG.info("进入支付列表查询方法");
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String startOrderTime = getString("startOrderTime");
            String endOrderTime = getString("endOrderTime");
            String partnerPayId = getString("partnerPayId");
            String status = getString("status");
            String groupCode = getString("groupCode");
            String loginName = user.getLoginName();
            List<Map<String, String>> listRole = systemUserService.getPriorityTwo(user.getRowNo()+"");
            boolean flag=false;
            for(int i=0;i<listRole.size();i++){
                if("系统管理员".equals(listRole.get(i).get("CNAME"))){
                    flag=true;
                    break;
                }
            }
            //此处查询是为了得到查询参数循环调用webService接口
            if(flag){
                page=orderQuerySendService.findByPage(page,startOrderTime,endOrderTime,partnerPayId,status,groupCode,null);
            }else{
                page=orderQuerySendService.findByPage(page,startOrderTime,endOrderTime,partnerPayId,status,groupCode,loginName);
            }
            Collection data = page.getData();
            if(null != data){
                List<Map<String,Object>> list =new ArrayList<Map<String,Object>>(data);
                //格式化时间
                for (Map<String,Object> m : list) {
                    if(null != m.get("ORDERTIME")){
                        try {
                            Date date = new Date();
                            date = new SimpleDateFormat("yyyyMMddHHmmss").parse(m.get("ORDERTIME").toString());
                            m.put("ORDERTIME",new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(date));
                        } catch (Exception e) {
                            e.printStackTrace();
                            LOG.error("返回数据时间转换异常！");
                        }
                    }
                }
            }
            String json = com.xinxinsoft.utils.easyh.JSONHelper.Serialize(page);
            Write(json);
//            Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        }
        catch (Exception e) {
            e.printStackTrace();
            LOG.error("支付列表查询异常"+e.getMessage(),e);
            Write("NO");
        }
    }

    /**
     * 获取缴费结果信息
     */
    public void getPayOrderInfo(){
        LOG.info("进入缴费结果信息查询方法");
        String outOrderId = getString("outOrderId");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(outOrderId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            Write(JSONHelper.Serialize(map));
            return ;
        }
        try {
            JSONObject jsonReq =new JSONObject();
            jsonReq.put("orderId",outOrderId);
            String ret = callWebService(jsonReq.toString(), "selectOrder");
            LOG.info("缴费结果信息result:"+ret);
//            if("".equals(ret)){
//                map.put("code",-1);
//                map.put("msg","接口调用出错");
//            }else{
//                net.sf.json.JSONObject jsonObj = net.sf.json.JSONObject.fromObject(ret);
//                if(!"-1".equals(jsonObj.getString("retCode"))){
//                    map.put("code",1);
//                    map.put("msg",jsonObj.getString("error"));
//                }else{
//                    map.put("code",-1);
//                    map.put("msg",jsonObj.getString("error"));
//                }
//            }
            PayOrderInfo payOrderInfo=orderQuerySendService.getPayOrderInfoByOutOrderId(outOrderId);
            if(null == payOrderInfo){
                map.put("code",-1);
                map.put("msg","未查询到缴费数据");
            }else{
                map.put("code",1);
                map.put("data",payOrderInfo);
            }

        } catch (Exception e) {
            LOG.error("缴费结果信息查询方法异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","异常");
        }
        Write(JSONHelper.Serialize(map));
    }

    /**
     * 获取商品信息
     */
    public void getGoodsInfo(){
        LOG.info("进入商品信息查询方法");
        String outOrderId = getString("outOrderId");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(outOrderId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            Write(JSONHelper.Serialize(map));
            return ;
        }
        try {
            GoodsInfo goodsInfo=orderQuerySendService.getGoodsInfoByOutOrderId(outOrderId);
            if(null == goodsInfo){
                map.put("code",-1);
                map.put("msg","未查询到商品数据");
            }else{
                map.put("code",1);
                map.put("data",goodsInfo);
            }

        } catch (Exception e) {
            LOG.error("商品信息查询方法异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","异常");
        }
        Write(JSONHelper.Serialize(map));
    }

    /**
     * 获取支付返回结果
     */
    public void getPayResults(){
        LOG.info("进入支付返回结果查询方法");
        String outOrderId = getString("outOrderId");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(outOrderId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            Write(JSONHelper.Serialize(map));
            return ;
        }
        try {
            JSONObject jsonReq =new JSONObject();
            jsonReq.put("orderId",outOrderId);
            String ret = callWebService(jsonReq.toString(), "selectOrder");
            LOG.info("支付返回结果result:"+ret);
//            if("".equals(ret)){
//                map.put("code",-1);
//                map.put("msg","接口调用出错");
//            }else{
//                net.sf.json.JSONObject jsonObj = net.sf.json.JSONObject.fromObject(ret);
//                if(!"-1".equals(jsonObj.getString("retCode"))){
//                    map.put("code",1);
//                    map.put("msg",jsonObj.getString("error"));
//                }else{
//                    map.put("code",-1);
//                    map.put("msg",jsonObj.getString("error"));
//                }
//            }
            PayResults payResults=orderQuerySendService.getPayResultsByOutOrderId(outOrderId);
            if(null == payResults){
                map.put("code",-1);
                map.put("msg","未查询到数据");
            }else{
                map.put("code",1);
                map.put("data",payResults);
            }

        } catch (Exception e) {
            LOG.error("支付返回结果查询方法异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","异常");
        }
        Write(JSONHelper.Serialize(map));
    }

    /**
     * 查询PaymentResultInfo列表
     */
    public void findPaymentResultInfoByPage(){
        try {
            LOG.info("进入支付返回详情信息方法");
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            String outOrderId = getString("outOrderId");

            LayuiPage page = new LayuiPage(pageNo, pageSize);
            page=orderQuerySendService.findPaymentResultInfoByPage(page,outOrderId);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.Serialize(page);
            Write(json);
        }
        catch (Exception e) {
            LOG.error("支付返回详情信息方法异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 查询冲正明细
     */
    public void findCancelItemInfoByPage(){
        try {
            LOG.info("进入冲正明细查询方法方法");
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            String outOrderId = getString("outOrderId");

            LayuiPage page = new LayuiPage(pageNo, pageSize);
            page=orderQuerySendService.findCancelItemInfoByPage(page,outOrderId);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.Serialize(page);
            Write(json);
        }
        catch (Exception e) {
            LOG.error("冲正明细查询方法方法异常"+e.getMessage(),e);
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 获取冲正撤销请求和响应实体
     */
    public void getPayCancelInfoAndRefundResultInfo(){
        LOG.info("进入获取冲正撤销请求和响应实体方法");
        String outOrderId = getString("outOrderId");
        Map<String,Object> map =new HashMap<>();
        if(!StringHelper.isBlank(outOrderId)){
            map.put("code",-1);
            map.put("msg","参数错误");
            Write(JSONHelper.Serialize(map));
            return ;
        }
        try {
            JSONObject jsonReq =new JSONObject();
            jsonReq.put("outOrderId",outOrderId);
            String ret = callWebService(jsonReq.toString(), "cancelQuery");
            LOG.info("冲正撤销请求和响应实体result:"+ret);
//            if("".equals(ret)){
//                map.put("code",-1);
//                map.put("msg","接口调用出错");
//            }else{
//                net.sf.json.JSONObject jsonObj = net.sf.json.JSONObject.fromObject(ret);
//                if(!"-1".equals(jsonObj.getString("retCode"))){
//                    map.put("code",1);
//                    map.put("msg",jsonObj.getString("error"));
//                }else{
//                    map.put("code",-1);
//                    map.put("msg",jsonObj.getString("error"));
//                }
//            }
            Map<String,Object> resultMap=orderQuerySendService.getPayCancelInfoAndRefundResultInfo(outOrderId);
            if(null == resultMap){
                map.put("code",-1);
                map.put("msg","未查询到数据");
            }else{
                map.put("code",1);
                map.put("data",resultMap);
            }

        } catch (Exception e) {
            LOG.error("获取冲正撤销请求和响应实体方法异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","异常");
        }
        Write(JSONHelper.Serialize(map));
    }

    /**
     * PC发起冲正
     */
    public void cancelOrder(){
        LOG.info("进入PC发起冲正方法");
        String outOrderId = getString("outOrderId");
        String remark = getString("remark");
        Map<String,Object> map = new HashMap<>();
        if (remark == null || "".equals(remark) || outOrderId == null || "".equals(outOrderId)) {
            map.put("code",-1);
            map.put("msg","参数不能为空");
            Write(JSONHelper.Serialize(map));
            return;
        }
        try {
            PayProviderInfo payProviderInfo = orderQuerySendService.getPayProviderInfoByOutOrderId(outOrderId);
            //{"remark":"测试取消","cancelFee":"1",”outOrderId“:"300200120200921428067315","partnerId":"3002001","notifyUrl":"http://10.113.183.13:8080/EOM/Payment_cancelNotifyUrl.action"}
            /**
             * 冲正发起请求参数：
             * partnerId--》商户号，由支付中心分配 Y  由webservice提供
             * outOrderId--》外部订单标识 outOrderId和orderId必须填一个。N
             * orderId--》订单标识 N
             * cancelFee--》冲正总金额，单位为分 和订单总金额保持一致 Y
             * remark--》冲正原因 Y
             * notifyUrl--》冲正时退款成功的结果通知地址，通知退款结果。一般要求传。N 由webservice提供
             */
            PayCancelInfo payCancelInfo = new PayCancelInfo();
            payCancelInfo.setRemark(remark);
            payCancelInfo.setCancelFee(payProviderInfo.getTotalFee());
            payCancelInfo.setOrderId(outOrderId);
            List<PayCancelInfo> list =new ArrayList<>();
            list.add(payCancelInfo);
            JSONObject jsonReq =new JSONObject();
            jsonReq.put("payCancelInfo",list);
            String ret = callWebService(jsonReq.toString(), "orderCancel");
            LOG.info("PC发起冲正result:"+ret);
            map.remove("payCancelInfo");
            if("".equals(ret)){
                map.put("code",-1);
                map.put("msg","冲正失败");
            }else{
                net.sf.json.JSONObject jsonObj = net.sf.json.JSONObject.fromObject(ret);
                if(!"-1".equals(jsonObj.getString("retCode"))){
                    map.put("code",1);
                    map.put("msg","冲正成功");
                }else{
                    map.put("code",-1);
                    map.put("msg","冲正失败");
                }
            }
        }catch (Exception e) {
            LOG.error("PC发起冲正方法异常"+e.getMessage(),e);
            e.printStackTrace();
            map.put("code",-1);
            map.put("msg","冲正调用接口失败");
        }
        Write(JSONHelper.Serialize(map));
    }

    /**
     * 调用webService服务
     * @param jsonInfo2 参数
     * @param methodName 方法名
     * @return
     */
    public String callWebService(String jsonInfo2,String methodName){
        String ret= "";
        try {
            // 创建一个服务(service)调用(call)
            Service service = new Service();
            Call call = (Call) service.createCall();// 通过service创建call对象
            // 设置service所在URL
            call.setTargetEndpointAddress(new java.net.URL(endpoint));
            // 方法名(processService)与MyService.java方法名保持一致
            call.setOperationName(methodName);
            call.addParameter("jsonInfo", XMLType.XSD_STRING, ParameterMode.IN);
             call.setReturnType(XMLType.XSD_STRING); // 返回值类型：String
            // Object 数组封装了参数，参数为"This is Test!",调用processService(String arg)
            Object[] obj = { jsonInfo2 };
            ret = (String) call.invoke(obj);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ret;
    }
}
