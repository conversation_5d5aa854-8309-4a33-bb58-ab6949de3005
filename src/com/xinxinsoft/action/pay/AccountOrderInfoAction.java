package com.xinxinsoft.action.pay;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.pay.AccountOrderInfoService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.PageVO;

/**
 * 快速开户产品，业务合同信息action
 * 
 * <AUTHOR>
 * 
 */
public class AccountOrderInfoAction extends BaseAction {
	private AccountOrderInfoService accountOrderInfoService;

	public void setAccountOrderInfoService(AccountOrderInfoService accountOrderInfoService) {
		this.accountOrderInfoService = accountOrderInfoService;
	}

	/**
	 * 根据条件查询数据
	 */
	public void findByPage() {
		PageVO pageVO = new PageVO<>();
		try {
			int page = getInteger("page");
			int pageSize = getInteger("pagesize");
			pageVO.setPage(page);
			pageVO.setPageSize(pageSize);
			String createName = getString("createName");// 创建人名称
			String groupCode = getString("groupCode");// 集团编码
			String orderNum = getString("orderNum");// 订单编码
			String contractNum = getString("contractNum");// 合同编码
			String bossFormNo = getString("bossFormNo");// boss编号
			String state = getString("state");// 状态
			String json = accountOrderInfoService.findByPage(pageVO, createName, groupCode, orderNum, contractNum, bossFormNo, state);
			Write(json);
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 查询角色
	 */
	public void findRole() {
		try {
			SystemUser user = this.user;
			List list = accountOrderInfoService.findRole(user.getRowNo());
			Map<String, String> map = new HashMap<>();
			boolean flag = false;

			for (int i = 0; i < list.size(); i++) {
				if ((list.get(i).toString()).equals("4076")) {
					flag = true;
					break;
				}
			}
			if (flag) {
				map.put("role", "Y");
				map.put("id", user.getEmployeeName());
			} else {
				map.put("role", "N");
				map.put("id", user.getEmployeeName());
			}
			Write(JSONHelper.Serialize(map));
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	public void upload() {
		try {
			String id = getString("id");
			accountOrderInfoService.upload(id);
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
}
