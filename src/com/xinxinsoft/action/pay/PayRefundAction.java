package com.xinxinsoft.action.pay;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.pay.Refunds.RefundInfos;
import com.xinxinsoft.entity.pay.Refunds.RefundResultInfo;
import com.xinxinsoft.entity.pay.Refunds.Refunds;
import com.xinxinsoft.entity.pay.payment.PayProviderInfo;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.service.pay.PayRefundService;
import com.xinxinsoft.service.pay.PaymentService;
import com.xinxinsoft.utils.UrlConnection;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.util.Date;
import java.util.List;

/***
 * 支付退款后台异步通知 调用接口：
 */
public class PayRefundAction  extends BaseAction {

    private static final Logger logger = Logger.getLogger(PayRefundAction.class);

    private PayRefundService payRefundService;

    private PaymentService paymentService;

    public PayRefundService getPayRefundService() {
        return payRefundService;
    }

    public void setPayRefundService(PayRefundService payRefundService) {
        this.payRefundService = payRefundService;
    }

    public PaymentService getPaymentService() {
        return paymentService;
    }

    public void setPaymentService(PaymentService paymentService) {
        this.paymentService = paymentService;
    }

    /***
     * 支付退款后台异步通知调用接口：
     */
    public void payRefundNotify(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
            logger.info(" 支付退款后台异步通知调用接口，请求参数：" + request_info);
            if(!StringUtils.isEmpty(request_info)){
                JSONObject reqInfoObj = JSONObject.fromObject(request_info);
                if("000000".equals(reqInfoObj.getString("retCode")) && "true".equals(reqInfoObj.getString("success"))) {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));
                        RefundResultInfo refundResultInfo = (RefundResultInfo)JSONObject.toBean(reqData , RefundResultInfo.class);

                        // 根据退款工单标识查询记录是否存在，
                        RefundResultInfo rrinfo = payRefundService.queryRefundsByRId(refundResultInfo.getRefundId());
                            if(rrinfo ==null){
                                refundResultInfo.setCreateDate(new Date());
                                refundResultInfo.setUpdateDate(new Date());
                                payRefundService.save(refundResultInfo);
                            }else{
                                // 记录存在就只做更新处理
                                rrinfo.setStatus(refundResultInfo.getStatus());
                                rrinfo.setRefundNo(refundResultInfo.getRefundNo());
                                rrinfo.setRefundFee(refundResultInfo.getRefundFee());
                                rrinfo.setPartnerId(refundResultInfo.getPartnerId());
                                rrinfo.setPayId(refundResultInfo.getPayId());
                                rrinfo.setUpdateDate(new Date());
                                payRefundService.save(rrinfo);
                            }


                        //退款 相关信息更新 根据退款订单编号 查询退款订单信息
                        Refunds rs = payRefundService.queryRefundsByRefundId(refundResultInfo.getRefundId());
                            if(rs !=null) {
                                rs.setUpdataDate(new Date());
                                if ("1".equals(refundResultInfo.getStatus())) {
                                    rs.setStatusInfo("退款成功");
                                } else {
                                    rs.setStatusInfo("退款失败");
                                }
                                rs.setStatus(Integer.parseInt(refundResultInfo.getStatus()));
                                rs.setFinishTime(refundResultInfo.getFinishTime());
                                payRefundService.save(rs);
                            }else{
                                logger.error("退款表信息更新失败!,退款流水号："+refundResultInfo.getRefundId());
                            }
                         //更新支付表状态 根据支付订单编号 查询支付订单信息
                        PayProviderInfo  ppifon = paymentService.queryByOrderId(refundResultInfo.getPartnerPayId());
                                    if(rs !=null) {
                                             ppifon.setStatus("1".equals(refundResultInfo.getStatus()) ? -2 : -3);
                                             ppifon.setUpdateDate(new Date());
                                             paymentService.save(ppifon);
                                    }else{
                                        logger.error("退款表信息更新失败!,订单号："+refundResultInfo.getPartnerPayId());
                                    }
                        writeText("success");
                    }else{
                        logger.info("=======》支付中心传递参数(data)问题");
                        writeText("error");
                    }
                }else{
                    logger.info("=======》支付中心传递参数(retCode或success)问题");
                    writeText("error");
                }
            }else{
                logger.info("=======》支付中心传递参数问题");
                writeText("error");
            }
        }catch (Exception   e){
            logger.error("支付后台异步通知调用接口，异常：" + e.getMessage());
            writeText("error");
        }

    }

}
