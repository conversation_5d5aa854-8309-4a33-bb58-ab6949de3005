package com.xinxinsoft.action.pay;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.pay.Refunds.RefundResultInfo;
import com.xinxinsoft.entity.pay.apiPay.*;
import com.xinxinsoft.entity.pay.payment.*;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.sendComms.accountService.UnitAccountInfoSrv;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.service.pay.PayApiService;
import com.xinxinsoft.service.pay.PaymentService;
import com.xinxinsoft.service.webService.pay.PayHttpConection;
import com.xinxinsoft.utils.CodecUtils;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.result.Result;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;

import java.net.URLDecoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 支付操作Action
 *
 */
public class PaymentAction  extends BaseAction {

    private static final Logger logger = Logger.getLogger(PaymentAction.class);

    private static final String ESB_URL_38 = "http://10.113.171.38:51000/esbWS/rest/";

    private PaymentService  paymentService;

    private PayApiService payApiService;

    public PaymentService getPaymentService() {
        return paymentService;
    }

    public void setPaymentService(PaymentService paymentService) {
        this.paymentService = paymentService;
    }

    public PayApiService getPayApiService() {
        return payApiService;
    }

    public void setPayApiService(PayApiService payApiService) {
        this.payApiService = payApiService;
    }

    private static Boolean isES = false;
    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }

    /***
     * 支付后台异步通知调用接口：
     */
    public void PaymentNotify(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
            logger.info("支付后台异步通知调用接口，请求参数：" + request_info);
            if(!StringUtils.isEmpty(request_info)){
                JSONObject reqInfoObj = JSONObject.fromObject(request_info);
                if("000000".equals(reqInfoObj.getString("retCode")) && "true".equals(reqInfoObj.getString("success"))) {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));
                        PayProviderInfo payProviderInfo = paymentService.queryPaymentInfoByIdAndPId(reqData.getString("partnerPayId"));
                        payProviderInfo.setStatus(reqData.getInt("status"));
                            if (CMCCOpenService.having(reqData, "paymentInfos")) {
                                JSONArray jsonArray = JSONArray.fromObject(reqData.get("paymentInfos"));
                                List<PaymentResultInfo> paymentResultInfos = paymentService.queryPaymentInfosByOrderId(reqData.getString("partnerPayId"));
                                if (paymentResultInfos.size() == 0) {
                                    for (Object obj : jsonArray) {
                                        PaymentResultInfo paymentResultInfo = (PaymentResultInfo) JSONObject.toBean(JSONObject.fromObject(obj), PaymentResultInfo.class);
                                        paymentResultInfo.setOrderId(reqInfoObj.getString("partnerPayId"));
                                        paymentResultInfo.setCreateDate(new Date());
                                        paymentService.save(paymentResultInfo);
                                    }
                            } else {
                                for (Object obj : jsonArray) {
                                    JSONObject jsonObject =  JSONObject.fromObject(obj);
                                    for (PaymentResultInfo paymentResultInfo : paymentResultInfos) {
                                        PaymentResultInfo prinfo = paymentResultInfo;
                                        if (paymentResultInfo.getOrderId().equals(reqData.getString("partnerPayId")) && paymentResultInfo.getPayItemId().equals(jsonObject.getString("payItemId"))){
                                            prinfo.setStatus(jsonObject.getString("status"));
                                        }
                                        paymentService.save(prinfo);
                                    }
                                }
                            }
                        }

                            if("2".equals(payProviderInfo.getStatus()) || 2== payProviderInfo.getStatus()) {
                                //8000支付情况
                                PayOrderInfo payOrderInfo = paymentService.queryPayOrderInfoByOrderId(payProviderInfo.getPartnerPayId());
                                if (payOrderInfo != null && "0000010".equals(payOrderInfo.getPay_type()) && !"0".equals(payOrderInfo.getPay_eight_status())) {
                                    Map<String, Object> mapcfm = CMCCOpenService.getInstance().pres8000CfmCardSvc(payOrderInfo.getPay_eight_noteDes());
                                    if (mapcfm.containsKey("return_code")) {
                                        if ("0".equals(mapcfm.get("return_code"))) {
                                            payOrderInfo.setPay_eight_status("0");
                                            payOrderInfo.setPay_accept(mapcfm.get("pay_accept")+"");
                                        } else {
                                            payOrderInfo.setPay_eight_result(mapcfm.get("return_msg") + "");
                                            payOrderInfo.setPay_eight_status("1");
                                        }
                                    } else {
                                        payOrderInfo.setPay_eight_result(mapcfm.get("res") + "");
                                        payOrderInfo.setPay_eight_status("1");
                                    }
                                    paymentService.save(payOrderInfo);
                                }
                            }
                        payProviderInfo.setUpdateDate(new Date());
                        paymentService.save(payProviderInfo);
                        writeText("success");
                    }else{
                        logger.info("=======》支付中心传递参数(data)问题");
                        writeText("error");
                    }
                }else{
                    logger.info("=======》支付中心传递参数(retCode或success)问题");
                    writeText("error");
                }
            }else{
                logger.info("=======》支付中心传递参数问题");
                writeText("error");
            }
        }catch (Exception   e){
            logger.error("支付后台异步通知调用接口，异常：" + e.getMessage());
            writeText("error");
        }

    }

    /**
     * 支付成功回调
     * 2020-09
     */
    public void notifyUrl(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
            logger.info("支付成功回调，请求参数：" + request_info);
            if(!StringUtils.isEmpty(request_info)){
                JSONObject reqInfoObj = JSONObject.fromObject(request_info);
                if("000000".equals(reqInfoObj.getString("retCode")) && "true".equals(reqInfoObj.getString("success"))) {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));
                        PayProviderInfo payProviderInfo = paymentService.queryPaymentInfoByIdAndPId(reqData.getString("partnerPayId"));
                        payProviderInfo.setStatus(reqData.getInt("status"));//支付基本信息状态，此实体有缴费状态，仅修改支付结果状态
                        //支付信息,支持混合支付场景
                        if (CMCCOpenService.having(reqData, "paymentInfos")) {
                            JSONArray jsonArray = JSONArray.fromObject(reqData.get("paymentInfos"));
                            List<PaymentResultInfo> paymentResultInfos = paymentService.queryPaymentInfosByOrderId(reqData.getString("partnerPayId"));
                            if (paymentResultInfos.size() == 0) {
                                for (Object obj : jsonArray) {
                                    PaymentResultInfo paymentResultInfo = (PaymentResultInfo) JSONObject.toBean(JSONObject.fromObject(obj), PaymentResultInfo.class);
                                    paymentResultInfo.setOrderId(reqData.getString("partnerPayId"));
                                    paymentResultInfo.setCreateDate(new Date());
                                    paymentService.save(paymentResultInfo);//此处仅有支付结果状态
                                }
                            } else {
                                for (Object obj : jsonArray) {
                                    JSONObject jsonObject =  JSONObject.fromObject(obj);
                                    for (PaymentResultInfo paymentResultInfo : paymentResultInfos) {
                                        PaymentResultInfo prinfo = paymentResultInfo;
                                        if (paymentResultInfo.getOrderId().equals(reqData.getString("partnerPayId")) && paymentResultInfo.getPayItemId().equals(jsonObject.getString("payItemId"))){
                                            prinfo.setStatus(jsonObject.getString("status"));
                                        }
                                        paymentService.save(prinfo);//此处仅有支付结果状态
                                    }
                                }
                            }
                        }
                        PayResults payResults = paymentService.queryPayResultsByOrderId(payProviderInfo.getPartnerPayId());
                        payResults.setStatus(reqData.getString("status"));//修改支付结果状态，此实体有缴费状态，此次只修改支付结果状态
                        payResults.setPayId(reqData.getString("payId"));
                        payResults.setPayTime(reqData.getString("payTime"));
                        payResults.setPayFee(reqData.getString("payFee"));

                        paymentService.save(payResults);
                        payProviderInfo.setUpdateDate(new Date());
                        paymentService.save(payProviderInfo);
                        writeText("success");
                    }else{
                        logger.info("=======》支付中心传递参数(data)问题");
                        writeText("error");
                    }
                }else{
                    logger.info("=======》支付中心传递参数(retCode或success)问题");
                    writeText("error");
                }
            }else{
                logger.info("=======》支付中心传递参数问题");
                writeText("error");
            }
        }catch (Exception   e){
            logger.error("支付后台异步通知调用接口，异常：" + e.getMessage(),e);
            writeText("error");
        }
    }

    /**
     * 缴费成功回调
     * 2020-09
     */
    public void busiNotifyUrl(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
            logger.info("缴费成功回调，请求参数：" + request_info);
            if(!StringUtils.isEmpty(request_info)){
                JSONObject reqInfoObj = JSONObject.fromObject(request_info);
                //缴费成功返回
                if("000000".equals(reqInfoObj.getString("retCode")) && "true".equals(reqInfoObj.getString("success"))) {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));
                        PayProviderInfo payProviderInfo = paymentService.queryPaymentInfoByIdAndPId(reqData.getString("outOrderId"));
                        payProviderInfo.setPayStatus("2");
                        payProviderInfo.setOrderId(reqData.getString("orderId"));

                        PayOrderInfo payOrderInfo = paymentService.queryPayOrderInfoByOutOrderId(payProviderInfo.getPartnerPayId());
                        setPayOrderInfo(payOrderInfo,reqData);
                        payOrderInfo.setPay_eight_status("2");//缴费状态，此处仅有缴费状态
                        paymentService.save(payOrderInfo);

                        payProviderInfo.setUpdateDate(new Date());
                        paymentService.save(payProviderInfo);
                        writeText("success");
                    }else{
                        logger.info("=======》缴费支付中心传递参数(data)问题");
                        writeText("error");
                    }
                    //缴费出问题返回
                }else {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));
                        PayProviderInfo payProviderInfo = paymentService.queryPaymentInfoByIdAndPId(reqData.getString("outOrderId"));
                        PayOrderInfo payOrderInfo = paymentService.queryPayOrderInfoByOutOrderId(payProviderInfo.getPartnerPayId());
                        if ("900001".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("3");//缴费状态，无效订单号
                        } else if ("900002".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("4");//缴费状态，订单已过期
                        } else if ("900003".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("5");//缴费状态，重复订单（订单已处理）
                        } else if ("900004".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("6");//缴费状态，无效订单-订单数字签名错误
                        } else if ("900005".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("7");//缴费状态，支付错误-商户验证失败
                        } else if ("900006".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("8");//缴费状态，支付错误-支付商支付失败
                        } else if ("900011".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("9");//缴费状态，无效订单-缺失必要的参数
                        } else if ("999999".equals(reqInfoObj.getString("retCode"))) {
                            setPayOrderInfo(payOrderInfo,reqData);
                            payOrderInfo.setPay_eight_status("0");//缴费状态，其他未定义错误
                        } else {
                            logger.info("=======》缴费支付中心传递参数(retCode或success)问题");
                            writeText("error");
                        }
                        paymentService.save(payOrderInfo);
                        writeText("success");
                    }else{
                        logger.info("=======》缴费支付中心传递参数(data)问题");
                        writeText("error");
                    }
                }
            }else{
                logger.info("=======》缴费支付中心传递参数问题");
                writeText("error");
            }
        }catch (Exception   e){
            logger.error("缴费后台异步通知调用接口，异常：" + e.getMessage(),e);
            writeText("error");
        }

    }

    public void setPayOrderInfo(PayOrderInfo payOrderInfo, JSONObject reqData){
        payOrderInfo.setOrderId(reqData.getString("orderId"));
        payOrderInfo.setOrderItemId(reqData.getString("orderItemId"));
        payOrderInfo.setPayFee(reqData.getString("payTotalFee"));//接口返回字段payTotalFee
        payOrderInfo.setPhoneNo(reqData.getString("phoneNo"));
        payOrderInfo.setFaceAmount(reqData.getString("faceAmount"));
        payOrderInfo.setFaceUnit(reqData.getString("faceUnit"));
        payOrderInfo.setUserId(reqData.getString("userId"));
        payOrderInfo.setFinishTime(reqData.getString("finishTime"));

    }

    /**
     * 冲正时退款成功的结果通知地址，通知退款结果。
     */
    public void cancelNotifyUrl(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
//            String request_info = "{\"data\":{\"cancelId\":\"300200120210130000000019\",\"orderId\":\"300200120210129364158072\",\"outOrderId\":\"300200120210129364158072\",\"refundFee\":\"1\",\"refundFinishTime\":\"2021-01-30 11:21:51\",\"status\":\"3\"},\"retCode\":\"000000\",\"retMsg\":\"success\",\"sign\":\"33c94494079e273887e7931939b76763\",\"success\":true}";
            logger.info("冲正时退款成功的结果通知地址，请求参数：" + request_info);
            if(!StringUtils.isEmpty(request_info)){
                JSONObject reqInfoObj = JSONObject.fromObject(request_info);
                if("000000".equals(reqInfoObj.getString("retCode")) && "true".equals(reqInfoObj.getString("success"))) {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));

//                        RefundResultInfo refundResultInfo = paymentService.queryRefundResultInfoByOutOrderIdOrOrderId(reqData.getString("outOrderId"),reqData.getString("orderId"));
                        RefundResultInfo refundResultInfo = paymentService.queryRefundResultInfoByCancelId(reqData.getString("cancelId"));
                        if(null == refundResultInfo){
                            refundResultInfo= (RefundResultInfo) JSONObject.toBean(JSONObject.fromObject(reqData), RefundResultInfo.class);
                            refundResultInfo.setFinishTime(refundResultInfo.getRefundFinishTime());
                        }else{
                            refundResultInfo.setStatus(reqData.getString("status"));
                            refundResultInfo.setFinishTime(reqData.getString("refundFinishTime"));
                            refundResultInfo.setRefundFee(reqData.getString("refundFee"));
                        }
                        paymentService.save(refundResultInfo);
//                        PayProviderInfo payProviderInfo = paymentService.queryByOrderId(reqData.getString("outOrderId"));
//                        payProviderInfo.setRefundStatus(reqData.getString("status"));//修改退款状态
//                        paymentService.save(payProviderInfo);
                        writeText("success");
                    }else{
                        logger.info("=======》冲正支付中心传递参数(data)问题");
                        writeText("error");
                    }
                }else{
                    logger.info("=======》冲正支付中心传递参数(retCode或success)问题");
                    writeText("error");
                }
            }else{
                logger.info("=======》冲正支付中心传递参数问题");
                writeText("error");
            }
        }catch (Exception   e){
            logger.error("冲正后台异步通知调用接口，异常：" + e.getMessage(),e);
            writeText("error");
        }
    }
    /**
     *本地测试服务连接专用
     */
    public void responsePost(){
        String url= this.getString("url");
        String cont = this.getString("cont");
        String chare = this.getString("chare");
        logger.info("URL:"+url);
        logger.info("cont:"+cont);
        url= CodecUtils.b64Decode(url);
        cont= CodecUtils.b64Decode(cont);
        logger.info("URL:"+url);
        logger.info("cont:"+cont);
        if("utf-8".equalsIgnoreCase(chare)){
            Write(PayHttpConection.httpURLConnectionPOST(url, cont));
        }else if("gbk".equalsIgnoreCase(chare)){
            Write(UrlConnection.responseGBK(url, cont));
        }else{
            if(!StringUtils.isBlank(chare)){
                Write(UrlConnection.responseCharset(url, cont, chare));
            }else{
                Write("编码参数不能为空！");
            }
        }
    }


    /**
     * 支付成功回调
     * 支付中心v4.5.0
     *  *  * 统一接入规范 (V2.1.5)支付API成功回调通知
     */
    public void notifyAPIUrl(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
//            String request_info = "{\"data\":{\"partnerId\":\"3002001\",\"partnerPayId\":\"300200120210129364158072\",\"payId\":\"300200120210129364158072\",\"payFee\":\"1\",\"payTime\":\"2021-01-30 11:21:51\",\"status\":\"3\",\"paymentInfos\":[{\"orgPayId\":\"02802021110400382931491972643732\",\"payFee\":20000,\"payItemId\":\"20203332021110436334267000\",\"payModeId\":\"911002\",\"payTime\":\"2021-11-04 00:38:43\",\"status\":\"2\",\"statusCode\":null,\"statusInfo\":null}]},\"retCode\":\"000000\",\"retMsg\":\"success\",\"sign\":\"33c94494079e273887e7931939b76763\",\"success\":true}";
            logger.info("支付成功回调，请求参数：" + request_info);
            if(!StringUtils.isEmpty(request_info)){
                JSONObject reqInfoObj = JSONObject.fromObject(request_info);
                if("000000".equals(reqInfoObj.getString("retCode")) && "true".equals(reqInfoObj.getString("success"))) {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));
                        PayApiPayProviderInfo payProviderInfo = payApiService.queryPayApiPayProviderInfoById(reqData.getString("partnerPayId"));
                        payProviderInfo.setStatus(Integer.parseInt(reqData.getString("status")));//支付基本信息状态，此实体有缴费状态，仅修改支付结果状态
                        payProviderInfo.setOrderStatus(reqData.getString("status"));
                        payProviderInfo.setUpdateDate(new Date());
                        //支付信息,支持混合支付场景
                        if (CMCCOpenService.having(reqData, "paymentInfos")) {
                            JSONArray jsonArray = JSONArray.fromObject(reqData.get("paymentInfos"));
                            List<PayApiPaymentResultInfo> paymentResultInfos = payApiService.queryPaymentInfosByOrderId(reqData.getString("partnerPayId"));
                            if (paymentResultInfos.size() == 0) {
                                for (Object obj : jsonArray) {
                                    PayApiPaymentResultInfo paymentResultInfo = (PayApiPaymentResultInfo) JSONObject.toBean(JSONObject.fromObject(obj), PayApiPaymentResultInfo.class);
                                    paymentResultInfo.setOrderId(reqData.getString("partnerPayId"));
                                    paymentResultInfo.setCreateDate(new Date());
                                    payApiService.saveOrUpdate(paymentResultInfo);//此处仅有支付结果状态
                                }
                            } else {
                                for (Object obj : jsonArray) {
                                    JSONObject jsonObject =  JSONObject.fromObject(obj);
                                    for (PayApiPaymentResultInfo paymentResultInfo : paymentResultInfos) {
                                        PayApiPaymentResultInfo prinfo = paymentResultInfo;
                                        if (paymentResultInfo.getOrderId().equals(reqData.getString("partnerPayId")) && paymentResultInfo.getPayItemId().equals(jsonObject.getString("payItemId"))){
                                            prinfo.setStatus(jsonObject.getString("status"));
                                        }
                                        payApiService.saveOrUpdate(prinfo);//此处仅有支付结果状态
                                    }
                                }
                            }
                        }
                        PayApiPayResults payResults = payApiService.queryPayResultsByOrderId(payProviderInfo.getPartnerPayId());
                        payResults.setStatus(reqData.getString("status"));//修改支付结果状态，此实体有缴费状态，此次只修改支付结果状态
                        payResults.setPayId(reqData.getString("payId"));
                        payResults.setPayTime(reqData.getString("payTime"));
                        payResults.setPayFee(reqData.getString("payFee"));

                        payApiService.saveOrUpdate(payResults);
                        payProviderInfo.setUpdateDate(new Date());
                        payApiService.saveOrUpdate(payProviderInfo);
                        logger.info("支付回调成功");
                        if(isES){

                            PayApiPaymentInfo paymentInfo = payApiService.queryPayApiPaymentInfo(reqData.getString("partnerPayId"));
                            String params = setParams(paymentInfo, payProviderInfo);

                            HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"Payment_PayInfoSave", params,"UTF-8");

                            PayApiPayOrderInfo payOrderInfo = payApiService.getPayApiPayOrderInfoByOutOrderId(payProviderInfo.getPartnerPayId());
                            Result result = HttpURLConnectClientFactory.responseByCharset(ESB_URL_38 + "s8000CfmCard", payOrderInfo.getPay_eight_noteDes(), "UTF-8");
                            if(null != result){
                                if(result.getCode() == 200){
                                    JSONObject json = JSONObject.fromObject(result.getData());
                                    try {
                                        JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
                                        if(null != root){
                                            Long return_code = root.getLong("RETURN_CODE");
                                            if(null != return_code && return_code.longValue() == 0L){
                                                payOrderInfo.setPay_eight_status(String.valueOf(return_code));
                                                payProviderInfo.setPayStatus(String.valueOf(return_code));
                                                if(CMCCOpenService.having(root,"OUT_DATA")){
                                                    JSONObject out_data = root.getJSONObject("OUT_DATA");
                                                    payOrderInfo.setPay_accept(out_data.getString("PAY_ACCEPT"));
                                                    payOrderInfo.setFinishTime(out_data.getInt("TOTAL_DATE")+"");
                                                    logger.info("缴费成功");
                                                }
                                            }else{
                                                payOrderInfo.setPay_eight_status("1");
                                                logger.info("缴费失败return_code!=0");
                                            }
                                            payApiService.saveOrUpdate(payOrderInfo);
                                        }else{
                                            payOrderInfo.setPay_eight_status("1");
                                            payProviderInfo.setPayStatus("1");
                                            payApiService.saveOrUpdate(payOrderInfo);
                                            logger.info("缴费失败root为空");
                                        }
                                    } catch (Exception e) {
                                        payOrderInfo.setPay_eight_status("1");
                                        payProviderInfo.setPayStatus("1");
                                        payApiService.saveOrUpdate(payOrderInfo);
                                        logger.error("缴费异常：" + e.getMessage(),e);
                                        throw new Exception();
                                    }
                                }else{//缴费失败
                                    payOrderInfo.setPay_eight_status("1");
                                    payProviderInfo.setPayStatus("1");
                                    payApiService.saveOrUpdate(payOrderInfo);
                                    logger.info("缴费失败code返回失败");
                                }
                            }
                        }

                        writeText("success");
                    }else{
                        logger.info("=======》支付中心传递参数(data)问题");
                        writeText("error");
                    }
                }else{
                    logger.info("=======》支付中心传递参数(retCode或success)问题");
                    writeText("error");
                }
            }else{
                logger.info("=======》支付中心传递参数问题");
                writeText("error");
            }
        }catch (Exception   e){
            logger.error("支付后台异步通知调用接口，异常：" + e.getMessage(),e);
            writeText("error");
        }
    }


    /**
     * 退款成功回调
     * 支付中心v4.5.0
     *  *  * 统一接入规范 (V2.1.5)支付API成功回调通知
     */
    public void cancelNotifyAPI(){
        try {
            String request_info = UrlConnection.getRequestData(getRequest());
//            String request_info = "{\"data\":{\"refundId\":\"300200120210130000000019\",\"partnerPayId\":\"300200120210129364158072\",\"payId\":\"300200120210129364158072\",\"refundFee\":\"1\",\"finishTime\":\"2021-01-30 11:21:51\",\"status\":\"3\"},\"retCode\":\"000000\",\"retMsg\":\"success\",\"sign\":\"33c94494079e273887e7931939b76763\",\"success\":true}";
            logger.info("冲正时退款成功的结果通知地址，请求参数：" + request_info);
            if(!StringUtils.isEmpty(request_info)){
                JSONObject reqInfoObj = JSONObject.fromObject(request_info);
                if("000000".equals(reqInfoObj.getString("retCode")) && "true".equals(reqInfoObj.getString("success"))) {
                    if (CMCCOpenService.having(reqInfoObj, "data")) {
                        JSONObject reqData = JSONObject.fromObject(reqInfoObj.getString("data"));

                        PayApiRefundResultInfo refundResultInfo = payApiService.queryPayApiRefundResultInfoByRefundId(reqData.getString("refundId"));
                        if(null == refundResultInfo){
                            refundResultInfo= (PayApiRefundResultInfo) JSONObject.toBean(JSONObject.fromObject(reqData), PayApiRefundResultInfo.class);
                        }else{
                            refundResultInfo.setStatus(reqData.getString("status"));
                            refundResultInfo.setFinishTime(reqData.getString("finishTime"));
                            refundResultInfo.setRefundFee(reqData.getString("refundFee"));
                        }
                        payApiService.saveOrUpdate(refundResultInfo);
                        PayApiPayProviderInfo payProviderInfo = payApiService.queryByOrderId(reqData.getString("partnerPayId"));
                        payProviderInfo.setRefundStatus(reqData.getString("status"));//修改退款状态
                        payProviderInfo.setOrderStatus("-"+reqData.getString("status"));
                        payProviderInfo.setUpdateDate(new Date());
                        payApiService.saveOrUpdate(payProviderInfo);
                        logger.info("退款成功");
                        writeText("success");
                    }else{
                        logger.error("=======》冲正支付中心传递参数(data)问题");
                        writeText("error");
                    }
                }else if("111111".equals(reqInfoObj.getString("retCode"))){
                    logger.info("=======》原支付单已经退款成功");
                    writeText("原支付单已经退款成功");
                }else{
                    logger.info("=======》冲正支付中心传递参数(retCode或success)问题");
                    writeText("error");
                }
            }else{
                logger.info("=======》冲正支付中心传递参数问题");
                writeText("error");
            }
        }catch (Exception   e){
            logger.error("冲正后台异步通知调用接口，异常：" + e.getMessage(),e);
            writeText("error");
        }
    }

    public void send(){
       /* PayApiPayProviderInfo payProviderInfo = payApiService.queryPayApiPayProviderInfoById("300200120211117682145703");
        PayApiPaymentInfo paymentInfo = payApiService.queryPayApiPaymentInfo("300200120211117682145703");
        String params = setParams(paymentInfo, payProviderInfo);
        System.out.println(params);
        HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"Payment_PayInfoSave", params,"UTF-8");*/
        Result result = UnitAccountInfoSrv.getInstance().PaymentBalanceQuery("aa0010104", "11", "**********", "", "***********");
        System.out.println(result.getCode());
        System.out.println(result.getMessage());
        System.out.println(result.getData());
        this.Write(result.toString());
//        String ESB_URL_172= "http://**************:51000/esbWS/rest/";
//        PayApiPayOrderInfo payOrderInfo = payApiService.getPayApiPayOrderInfoByOutOrderId("300200120211117723840651");
//        String pay_eight_noteDes = payOrderInfo.getPay_eight_noteDes();
//        JSONObject jsonss = JSONObject.fromObject(pay_eight_noteDes);
//        String root = jsonss.getString("ROOT");
//        JSONObject jsons = JSONObject.fromObject(root);
//        String string = jsons.getString("HEADER");
//        JSONObject HEADER = JSONObject.fromObject(string);
//        String routing = HEADER.getString("ROUTING");
//        JSONObject routings = JSONObject.fromObject(routing);
//        routings.put("ROUTE_VALUE","fge4l3230");
//        String BODY = jsons.getString("BODY");
//        JSONObject bod = JSONObject.fromObject(BODY);
//        bod.put("PHONE_NO","**************");
//        String resultStr= CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172+"s8000CfmCard", jsonss.toString());
//        Result result=HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
//        logger.info(result);
//        if(null != result){
//            if(result.getCode() == 200){
//                JSONObject json = JSONObject.fromObject(result.getData());
//                try {
//                    JSONObject roots = JSONObject.fromObject(json.getString("ROOT"));
//                    Long return_code = roots.getLong("RETURN_CODE");
//                    if(null != return_code && return_code.longValue() == 0L){
//                        if(CMCCOpenService.having(roots,"OUT_DATA")){
//                            JSONObject out_data = roots.getJSONObject("OUT_DATA");
//                            payOrderInfo.setPay_accept(out_data.getString("PAY_ACCEPT"));
//                            payOrderInfo.setFinishTime(out_data.getInt("TOTAL_DATE")+"");
//                        }
//                    }
//                } catch (Exception e) {
//                    logger.error("缴费异常：" + e.getMessage(),e);
//                }
//            }
//        }
    }


    public void PayInfoSave(){
        try {
            logger.info("进入PayInfoSave服务");
            String request_info = UrlConnection.getRequestData(getRequest());
            logger.info("Payment_PayInfoSave：接收数据：==>" + request_info);
//            String request_info = "{\"ROOT\":{\"HEADER\":{\"POOL_ID\":\"31\",\"DB_ID\":\"\",\"ENV_ID\":\"1\",\"CONTACT_ID\":\"21292426241637204188229\",\"CHANNEL_ID\":\"155\",\"USERNAME\":\"zqddxt\",\"PASSWORD\":\"123456\",\"ENDUSRLOGINID\":\"\",\"ENDUSRIP\":\"\",\"ROUTING\":{\"ROUTE_KEY\":\"14\",\"ROUTE_VALUE\":\"aa0002107\"}},\"BODY\":{\"traded\":\"300200120211117682145703\",\"acctType\":\"311\",\"payCardNo\":\"\",\"payFee\":\"1\",\"payFeeDetail\":{\"payModeId\":\"311051\",\"accountId\":\"null\",\"accountType\":\"311\",\"tradeFee\":\"1\",\"realFee\":\"1\",\"discountId\":\"null\",\"exchangeRuleId\":\"null\",\"extParam\":\"null\"},\"payDesc\":\"H5\"}}}\n";
            if ("".equals(request_info)) {
                this.Write("{\"ROOT\":{\"RETURN_CODE\":\"-1\",\"RETURN_MSG\":\"请求参数异常\",\"DETAIL_MSG\":\"请求参数异常\"}}");
                return;
            }
            try {
                request_info = URLDecoder.decode(request_info.toString(), "UTF-8");
                JSONObject request_info_objec = JSONObject.fromObject(request_info);
                JSONObject root = JSONObject.fromObject(request_info_objec.getString("ROOT"));
                JSONObject body = root.getJSONObject("BODY");
                if (CMCCOpenService.having(body,"traded") && StringUtils.isNotBlank(body.getString("traded"))) {
                    String traded = body.getString("traded");
                    PayApiPayResults payApiPayResults = payApiService.queryPayResultsByOrderId(traded);
                    if(null == payApiPayResults){
                        this.Write("{\"ROOT\":{\"RETURN_CODE\":\"-1\",\"RETURN_MSG\":\"查询异常\",\"DETAIL_MSG\":\"未查询到订单生成\"}}");
                        return;
                    }else{
                        PayApiPayProviderInfo payApiPayProviderInfo = payApiService.queryPayApiPayProviderInfoById(traded);
                        JSONObject json = new JSONObject();
                        json.put("phoneNo", payApiPayProviderInfo.getPhoneNo());
                        json.put("payTotalFee", payApiPayProviderInfo.getPayTotalFee());
                        json.put("currency", payApiPayProviderInfo.getCurrency());
                        json.put("operator", payApiPayProviderInfo.getOperator());
                        json.put("orderDesc", payApiPayProviderInfo.getOrderDesc());
                        Map<String,Object> map = new HashMap<>();
                        map.put("traded",payApiPayResults.getOrderid());//支付工单编号
                        map.put("paySeqNo",payApiPayResults.getPayId());//支付流水号
                        map.put("payPara",json.toString());//支付参数
                        map.put("urlStr",payApiPayResults.getRedirectUrl());//二维码URL
                        this.Write("{\"ROOT\":{\"RETURN_CODE\":\"0\",\"RETURN_MSG\":\"OK\",\"DETAIL_MSG\":\"成功\",\"OUT_DATA\":" + JSONHelper.SerializeWithNeedAnnotationDateFormat(map) + "}}");
                        logger.info("PayInfoSave服务返回成功");
                        return;
                    }
                } else {
                    this.Write("{\"ROOT\":{\"RETURN_CODE\":\"-1\",\"RETURN_MSG\":\"JSON异常\",\"DETAIL_MSG\":\"JSON对象没找到traded元素\"}}");
                    return;
                }
            } catch (Exception e) {
                logger.info("json对象转换失败" + e.getMessage(),e);
                this.Write("{\"ROOT\":{\"RETURN_CODE\":\"-1\",\"RETURN_MSG\":\"JSON异常\",\"DETAIL_MSG\":\"" + e.getMessage() + "\"}}");
                e.printStackTrace();
                return;
            }
        } catch (Exception e) {
            logger.info("异常==>" + e.getMessage(),e);
            this.Write("{\"ROOT\":{\"RETURN_CODE\":\"-1\",\"RETURN_MSG\":\"异常\",\"DETAIL_MSG\":\"" + e.getMessage() + "\"}}");
        }
    }

    private String setParams(PayApiPaymentInfo paymentInfo, PayApiPayProviderInfo payProviderInfo){
        JSONObject roots = new JSONObject();
        roots.put("traded", payProviderInfo.getPartnerPayId());
        roots.put("acctType", paymentInfo.getAccountType());
        roots.put("acctId", payProviderInfo.getPhoneNo());
        roots.put("payCardNo", "");
        roots.put("payFee", paymentInfo.getTradeFee());
        //[xx]用户对[xxx]账号在[xxx]时，缴费[xxxx]分。
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        roots.put("payFeeDetail","["+ payProviderInfo.getOperator() + "]用户对[" + payProviderInfo.getPhoneNo() + "]账号在[" + formatter.format(payProviderInfo.getCreateDate())+"]时缴费"+payProviderInfo.getPayTotalFee()+"分");
        roots.put("payDesc", payProviderInfo.getAccessMode());
        return CMCCOpenService.getInstance().setParamObj(roots,payProviderInfo.getOperator());
    }
}
