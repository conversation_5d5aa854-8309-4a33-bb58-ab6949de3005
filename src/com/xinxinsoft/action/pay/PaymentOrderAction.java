package com.xinxinsoft.action.pay;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringWriter;
import java.io.Writer;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.util.Date;
import java.util.Map;

import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.pay.PaymentOrder;
import com.xinxinsoft.entity.pay.PaymentProvider;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.service.appOpenService.VariousSqlQueryService;
import com.xinxinsoft.service.pay.PaymentOrderService;
import com.xinxinsoft.service.pay.PaymentProviderService;

/**
 * 支付通知
 * 
 * <AUTHOR>
 * 
 */
public class PaymentOrderAction extends BaseAction {

	private static final long serialVersionUID = 1L;
	private String resultXml; // 返回的支付结果

	public String getResultXml() {
		return resultXml;
	}

	public void setResultXml(String resultXml) {
		this.resultXml = resultXml;
	}

	private PaymentOrderService paymentOrderService;
	private PaymentProviderService paymentProviderService;
	private VariousSqlQueryService variousSqlQueryService;

	public VariousSqlQueryService getVariousSqlQueryService() {
		return variousSqlQueryService;
	}

	public void setVariousSqlQueryService(
			VariousSqlQueryService variousSqlQueryService) {
		this.variousSqlQueryService = variousSqlQueryService;
	}

	public PaymentProviderService getPaymentProviderService() {
		return paymentProviderService;
	}

	public void setPaymentProviderService(
			PaymentProviderService paymentProviderService) {
		this.paymentProviderService = paymentProviderService;
	}

	public PaymentOrderService getPaymentOrderService() {
		return paymentOrderService;
	}

	public void setPaymentOrderService(PaymentOrderService paymentOrderService) {
		this.paymentOrderService = paymentOrderService;
	}

	/**
	 * 保存支付订单结果
	 */
	public void savePaymentOrder() {
		InputStream inputStream = null;
		Reader input = null;
		Writer output = new StringWriter();
		try {
			inputStream = getRequest().getInputStream();
			input = new InputStreamReader(inputStream);
			char[] buffer = new char[1024 * 4];
			int n = 0;
			while (-1 != (n = input.read(buffer))) {
				output.write(buffer, 0, n);
			}

			if (output == null || "".equals(output)) {
				System.out.println("=======》支付中心传递参数问题");
				writeText("error");
			} else {
				System.out.println("====》支付中心接口调用:" + output.toString());
				String out = URLDecoder.decode(output.toString(), "UTF-8");
				PaymentOrder po = new PaymentOrder();
				Document doc = DocumentHelper.parseText(out);
				Element rootElement = doc.getRootElement();// 获取根节点
				Element data = rootElement.element("data");
				String ret_code = data.attributeValue("ret_code");
				// String error_message = data.attributeValue("error_message");
				String sign = rootElement.elementText("sign");
				// String data_text = rootElement.elementText("data");
				if ("0".equals(ret_code)) {
					System.out.println("支付成功");
					String order_id = data.elementText("order_id");
					String status = data.elementText("status");
					String user = data.elementText("user");
					String goods_info = data.elementText("goods_info");
					String merchant = data.elementText("merchant");
					String busi_type = data.elementText("busi_type");
					String amount = data.elementText("amount");
					String sum_value = data.elementText("sum_value");
					String operator = data.elementText("operator");
					String payTime = data.elementText("payTime");
					po.setOrder_id(order_id);
					po.setStatus(status);
					po.setUser(user);
					po.setGoods_info(goods_info);
					po.setMerchant(merchant);
					po.setBusi_type(busi_type);
					po.setAmount(amount);
					po.setSum_value(sum_value);
					po.setOperator(operator);
					po.setPay_time(payTime);
					po.setSign(sign);
					po.setCreateTime(new Date());
					PaymentProvider payProvider = paymentProviderService
							.getorderId(order_id);// 根据order_id查询数据
					if ( payProvider != null && "2".equals(status) && !"0".equals(payProvider.getPay_eight_status())){
						try{
							if(!StringUtils.isEmpty(payProvider.getPay_eight_note())) {
								Map<String, Object> mapcfm = CMCCOpenService.getInstance().pres8000CfmCardSvc(payProvider.getPay_eight_noteDes());
								if (mapcfm.containsKey("return_code")) {
									if ("0".equals(mapcfm.get("return_code"))) {
										payProvider.setPay_eight_status("0");
										if(mapcfm.containsKey("PAY_ACCEPT")){
											payProvider.setPay_eight_result( "{\"PAY_ACCEPT\":\""+mapcfm.get("PAY_ACCEPT")+"\",\"TOTAL_DATE\":\""+mapcfm.get("TOTAL_DATE")+"\"}");
										}else{
											payProvider.setPay_eight_result(""+mapcfm);
										}
									} else {
										payProvider.setPay_eight_result(mapcfm.get("return_msg") + "");
										payProvider.setPay_eight_status("1");
									}
								} else {
									payProvider.setPay_eight_result(mapcfm.get("res") + "");
									payProvider.setPay_eight_status("1");
								}
								paymentProviderService.updatePaymentProvider(payProvider);
							}else{
								System.out.println("本次支付不支持8000！" );
							}
//							AccountOrderInfo accInfo = variousSqlQueryService
//									.queryAccount(payProvider.getBus_order_id());// 根据uuid查询信息
//							accInfo.setUpdateDate(new Date());// 更新数据时间
//							accInfo.setPlayDate(new Date());// 支付时间
//							accInfo.setState("3");// 支付状态
//							variousSqlQueryService.updateAccount(accInfo);// 更新
//						
//						variousSqlQueryService.sendZtSrv(
//								payProvider.getBus_order_id(),
//								parseAmout(payProvider.getAmount()));
						}catch(Exception e){
							System.out.println("异常：快速下单信息更新失败！");
						}
					}
					paymentOrderService.save(po);
				}
				writeText("success");
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			writeText("error");
		}
	}

	public static void main(String[] args) {
		HttpPost post = new HttpPost(
				"http://localhost:8080/EOM/PaymentOrder!savePaymentOrder.action");

		try {
			String XML = "%3C%3Fxml+version%3D%221.0%22+encoding%3D%22UTF-8%22%3F%3E%3Croot%3E%3Cdata+ret_code%3D%220%22+error_message%3D%22request+success%22%3E%3Corder_id%3E500000120180824406152873%3C%2Forder_id%3E%3Cbank_type%3E999001%3C%2Fbank_type%3E%3Cstatus%3E2%3C%2Fstatus%3E%3Cgoods_info%3E%5B%7B%22id%22%3A%*************%22%2C%22name%22%3A%22%E8%9E%8D%E5%90%88%E4%BA%92%E8%81%94%E7%BD%91%E4%B8%93%E7%BA%BF1000%E5%85%83500M%22%2C%22quantity%22%3A%221%22%2C%22price%22%3A1%2C%22value%22%3A1%7D%5D%3C%2Fgoods_info%3E%3Camount%3E1%3C%2Famount%3E%3CpayTime%3E20180824103636%3C%2FpayTime%3E%3Cbank_trade_no%3E401520298420201808241270431141%3C%2Fbank_trade_no%3E%3Cbank_trade_status%3ETRADE_SUCCESS%3C%2Fbank_trade_status%3E%3Cbank_refund_status%3Enull%3C%2Fbank_refund_status%3E%3Crefund_operate_time%3Enull%3C%2Frefund_operate_time%3E%3Cother_info%3Enull%3C%2Fother_info%3E%3C%2Fdata%3E%3Csign%3E20071fa5b32ef650c632d864100499a9%3C%2Fsign%3E%3C%2Froot%3E";

			HttpClient client = new DefaultHttpClient();
			/* 设置参数 */
			post.setEntity(new StringEntity(XML, "UTF-8"));
			HttpResponse response = client.execute(post);
			HttpEntity entity = response.getEntity();
			String returnMsg = EntityUtils.toString(entity, "UTF-8");
			System.out.println(returnMsg);
		} catch (Exception e) {

			e.printStackTrace();
		} finally {
			/* 释放链接 */
			post.releaseConnection();
		}
	}

	/**
	 * 将金额以万元为单位
	 */
	public String parseAmout(String amout) {
		Double money = Double.valueOf(amout);
		BigDecimal cash = new BigDecimal(String.valueOf(money / 1000000));
		return cash.toPlainString();
	}
}
