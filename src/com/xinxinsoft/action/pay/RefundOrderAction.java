package com.xinxinsoft.action.pay;

import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringWriter;
import java.io.Writer;
import java.net.URLDecoder;
import java.util.Date;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.appOpenInfo.AccountOrderInfo;
import com.xinxinsoft.entity.pay.PaymentProvider;
import com.xinxinsoft.entity.pay.RefundOrder;
import com.xinxinsoft.service.appOpenService.VariousSqlQueryService;
import com.xinxinsoft.service.pay.PaymentProviderService;
import com.xinxinsoft.service.pay.RefundOrderService;

/**
 * 退款结果后台通知
 * 
 * <AUTHOR>
 * 
 */
public class RefundOrderAction extends BaseAction {

	private static final long serialVersionUID = 1L;
	private String resultXml; // 接口返回的退款结果

	public String getResultXml() {
		return resultXml;
	}

	public void setResultXml(String resultXml) {
		this.resultXml = resultXml;
	}

	private RefundOrderService refundOrderService;
	private PaymentProviderService paymentProviderService;
	private VariousSqlQueryService variousSqlQueryService;

	public VariousSqlQueryService getVariousSqlQueryService() {
		return variousSqlQueryService;
	}

	public void setVariousSqlQueryService(
			VariousSqlQueryService variousSqlQueryService) {
		this.variousSqlQueryService = variousSqlQueryService;
	}

	public PaymentProviderService getPaymentProviderService() {
		return paymentProviderService;
	}

	public void setPaymentProviderService(
			PaymentProviderService paymentProviderService) {
		this.paymentProviderService = paymentProviderService;
	}

	public RefundOrderService getRefundOrderService() {
		return refundOrderService;
	}

	public void setRefundOrderService(RefundOrderService refundOrderService) {
		this.refundOrderService = refundOrderService;
	}

	/**
	 * 保存退款结果后台通知
	 */
	public void saveRefundOrder() {
		InputStream inputStream = null;
		Reader input = null;
		Writer output = new StringWriter();
		try {
			inputStream = getRequest().getInputStream();
			input = new InputStreamReader(inputStream);
			char[] buffer = new char[1024 * 4];
			int n = 0;
			while (-1 != (n = input.read(buffer))) {
				output.write(buffer, 0, n);
			}
			if (output == null || "".equals(output)) {
				System.out.println("=======》支付中心传递参数问题");
				writeText("error");
			} else {
				System.out.println("====》支付中心接口调用:" + output.toString());
				String out = URLDecoder.decode(output.toString(), "UTF-8");
				RefundOrder ro = new RefundOrder();
				System.out.println("====》转码后的参数:" + out.toString());
				Document doc = DocumentHelper.parseText(out);
				Element rootElement = doc.getRootElement();// 获取根节点
				Element data = rootElement.element("data");
				String ret_code = data.attributeValue("ret_code");
				// String error_message = data.attributeValue("error_message");
				// String sign = rootElement.elementText("sign");
				// String data_text = rootElement.elementText("data");

				if ("0".equals(ret_code)) {
					System.out.println("退款成功");
					String order_id = data.elementText("order_id");
					String status = data.elementText("status");
					ro.setOrder_id(data.elementText("order_id"));
					ro.setStatus(data.elementText("status"));
					ro.setRefund_no(data.elementText("refund_no"));
					ro.setRefund_amount(data.elementText("refund_amount"));
					ro.setFinish_time(data.elementText("finish_time"));
					ro.setCreateTime(new Date());
					PaymentProvider payProvider = paymentProviderService
							.getorderId(order_id);// 根据order_id查询数据
					if (payProvider != null && "-1".equals(status)) {
						AccountOrderInfo accInfo = variousSqlQueryService
								.queryAccount(payProvider.getBus_order_id(),
										"0");// 根据uuid查询信息
						accInfo.setUpdateDate(new Date());// 更新数据时间
						accInfo.setState("3");// 支付状态
						variousSqlQueryService.updateAccount(accInfo);// 更新
					}
					refundOrderService.save(ro);
				}
				writeText("success");
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			writeText("error");
		}
	}

}
