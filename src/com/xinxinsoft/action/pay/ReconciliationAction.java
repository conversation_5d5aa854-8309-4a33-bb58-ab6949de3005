package com.xinxinsoft.action.pay;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.appOpenInfo.AccountOrderInfo;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.service.appOpenService.VariousSqlQueryService;
import com.xinxinsoft.service.pay.ReconciliationService;
import com.xinxinsoft.service.pay.SettlementService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.MD5;
import com.xinxinsoft.utils.PageVO;
import com.xinxinsoft.utils.UrlConnection;
import org.apache.commons.lang.StringUtils;

public class ReconciliationAction extends BaseAction {
	private ReconciliationService reconciliationService;
	private SettlementService settlementService;
	private VariousSqlQueryService variousSqlQueryService;

	public void setVariousSqlQueryService(VariousSqlQueryService variousSqlQueryService) {
		this.variousSqlQueryService = variousSqlQueryService;
	}

	public void setSettlementService(SettlementService settlementService) {
		this.settlementService = settlementService;
	}

	public void setReconciliationService(ReconciliationService reconciliationService) {
		this.reconciliationService = reconciliationService;
	}

	/**
	 * 查看对账单相关信息
	 */
	public void findByAll() {
		try {
			PageVO pageVO = new PageVO();
			int page = Integer.valueOf(getString("page"));// 页码数
			int pageSize = Integer.valueOf(getString("pagesize"));// 每页显示件数
			pageVO.setPage(page);
			pageVO.setPageSize(pageSize);
			String orderId = getString("orderId");// 订单编号
			String transactionDate = getString("time");// 交易日期
			String transactionDateEnd = getString("time1");// 交易结束日期
			String startAmout = getString("startAmout");// 最小金额
			String endAmout = getString("endAmout");// 最大金额
			String state = getString("state");// 状态
			String json = reconciliationService.findByAll(pageVO, transactionDate, transactionDateEnd, orderId, startAmout, endAmout, state);// 查询相关对账单信息
			Write(json);
		} catch (Exception e) {
			e.printStackTrace();
			Write("No");
		}
	}

	/**
	 * 下载为excel文件
	 */
	public void uploadExcel() {
		try {
			String orderId = getString("orderId");// 订单编号
			String time = getString("time");// 交易开始时间
			String timeEnd = getString("timeEnd");// 交易结束时间
			String startAmout = getString("startAmout");// 最小金额
			String endAmout = getString("endAmout");// 最大金额
			String state = getString("state");// 状态
			List<Map<String, Object>> list = reconciliationService.findByDate(time, timeEnd, orderId, startAmout, endAmout, state);
			List<Map<String, Object>> list2 = settlementService.findByDate(time, timeEnd);
			reconciliationService.uploadExcel(list, list2);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 根据条件查询支付订单信息
	 */
	public void findByCondition() {
		try {
			PageVO pageVO = new PageVO();
			int page = Integer.valueOf(getString("page"));// 页码数
			int pageSize = Integer.valueOf(getString("pagesize"));// 每页显示件数
			pageVO.setPage(page);
			pageVO.setPageSize(pageSize);
			String orderId = getString("orderId");// 订单编号
			String time = getString("time");// 交易开始时间
			String timeEnd = getString("time1");// 交易结束时间
			String startAmout = getString("startAmout");// 最小金额
			String endAmout = getString("endAmout");// 最大金额
			String state = getString("state");// 状态
			String json = reconciliationService.findPaymentOrder(pageVO, time, timeEnd, orderId, startAmout, endAmout, state);
			Write(json);

		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 根据uuid查询订单详细信息
	 */
	public void findByOrderId() {
		try {
			String uuid = getString("uuid");
			AccountOrderInfo accountOrderInfo = variousSqlQueryService.queryAccount(uuid);
			String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(accountOrderInfo);
			Write(json);
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 导出支付单信息
	 */
	public void uploadPayExcel() {
		try {
			String orderId = getString("orderId");// 订单编号
			String time = getString("time");// 交易开始时间
			String timeEnd = getString("timeEnd");// 交易结束时间
			String startAmout = getString("startAmout");// 最小金额
			String endAmout = getString("endAmout");// 最大金额
			String state = getString("state");// 状态
			List<Map<String, Object>> list = reconciliationService.findByPayOrder(time, timeEnd, orderId, startAmout, endAmout, state);
			reconciliationService.uploadPayExcel(list);
		} catch (Exception e) {
			e.printStackTrace();
		}
	}

	/**
	 * 调用接口
	 */
	public void httpUrl() {
		UrlConnection urlConnection = new UrlConnection();
		try {
			String name = getString("name");
			String url = "http://10.113.183.51:52000/esbWS/rest/" + name;
			System.out.println(url);
			String json = getString("json");
			String jsonString = urlConnection.responseGBK(url, json);
			System.out.println(jsonString);
			Write(jsonString);
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}

	/**
	 * 查询最近最后缴费次数
	 */
	public void recentPaymentHistory(){
		String play_type = this.getString("play_type");//小类活动代码
		String num = this.getString("num");//短信编码
		String  user_phone = this.getString("user_phone");
		String  pay_userno = this.getString("pay_userno");
		String  groupcode = this.getString("groupcode");
		String  query_type = this.getString("query_type");
		String sing = this.getString("sing");
		try {
			Map<String, String> map = new HashMap<String, String>();
			if(!StringUtils.isEmpty(play_type)){
				map.put("play_type", play_type);
			}
			if (!StringUtils.isBlank(num)){
				map.put("num",num);
			}
			if (!StringUtils.isBlank(user_phone)){
				map.put("user_phone",user_phone);
			}
			if (!StringUtils.isBlank(pay_userno)){
				map.put("pay_userno",pay_userno);
			}
			if (!StringUtils.isBlank(groupcode)){
				map.put("groupcode",groupcode);
			}
			if (!StringUtils.isBlank(query_type)){
				map.put("query_type",query_type);
			}

			if(MD5.isSing(map, sing)){
				Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(reconciliationService.findPlayHistory(play_type,num,user_phone,pay_userno,groupcode,query_type)));
			}else{
				Write("{\"RETURN_MSG\":\""+MD5.MD5Status.EXCEPTION_MSG.getMessage()+"\",\"RETURN_CODE\":\"-1\"}");
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("{\"RETURN_MSG\":\"请求异常："+e.getMessage()+"\",\"RETURN_CODE\":\"-1\"}");
		}
	}
}
