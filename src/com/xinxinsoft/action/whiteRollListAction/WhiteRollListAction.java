
package com.xinxinsoft.action.whiteRollListAction;

import com.google.gson.GsonBuilder;
import com.sun.scenario.effect.impl.sw.sse.SSEBlend_SRC_OUTPeer;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.action.test.JbpmTest;
import com.xinxinsoft.entity.claimForFunds.LateFeeMoneyData;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.transfer.*;
import com.xinxinsoft.entity.whiteRollList.WhiteDetList;
import com.xinxinsoft.entity.whiteRollList.WhiteRollList;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.accountService.UnitAccountInfoSrv;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.jbpmProcess.JbpmProcessService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.whiteRollListService.WhiteRollListService;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.WsdlUtil;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.components.If;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.io.*;
import java.net.URLEncoder;
import java.text.DateFormat;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class WhiteRollListAction extends BaseAction {
    @Resource(name = "WhiteRollListService")
    private WhiteRollListService whiteRollListService;
    private File file1;
    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;
    @Resource(name = "WaitTaskService")
    private WaitTaskService service;
    @Resource(name = "TransferJBPMUtils")
    private TransferJBPMUtils transferJBPMUtils;//TransferJBPMUtils
    @Resource(name = "Bpms_riskoff_service")
    private Bpms_riskoff_service taskService;
    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "ClaimForFundsService")
    private ClaimForFundsService claimForFundsService;


    private Logger logger = LoggerFactory.getLogger(WhiteRollListAction.class);

    private static final String ECMSERVER_URL = "http:// 10.113.228.58:8080/ecmServer?wsdl";

    public File getFile1() {
        return this.file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }

    public static String getUnlockedNumber() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(new Date());
        return dateString;
    }


    /**
     * 查询白名单列表
     */
    public void findAllByPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String WhiteListNo = this.getString("WhiteListNo");
            String WhiteListTitle = this.getString("WhiteListTitle");
            String creatorPhone = this.getString("creatorPhone");
            String startTime = this.getString("startTime");
            String endTime = this.getString("endTime");
            String statetwo = this.getString("statetwo");
            String state = this.getString("state");
            if (state != null && !"".equals(state)) {
                page = this.whiteRollListService.findAllTwo(page, WhiteListNo, WhiteListTitle, creatorPhone, startTime, endTime, this.user, statetwo, state);
                String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
                this.Write(json);
            } else {
                this.Write("NO");
            }
        } catch (Exception var12) {
            var12.printStackTrace();
            this.Write("NO");
        }

    }


    /**
     * 手机端查询白名单列表
     */
    public void findAllByPageNew() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String WhiteListNo = this.getString("WhiteListNo");
            String WhiteListTitle = this.getString("WhiteListTitle");
            String creatorPhone = this.getString("creatorPhone");
            String startTime = this.getString("startTime");
            String endTime = this.getString("endTime");
            String statetwo = this.getString("statetwo");
            String state = this.getString("state");
            String phone = this.getString("phone");
            if ("".equals(phone) || null == phone) {
                this.Write("NO");
            } else {
                SystemUser userByPhone = systemUserService.getUserByPhone(phone);
                if (state != null && !"".equals(state)) {
                    page = this.whiteRollListService.findAllNew(page, WhiteListNo, WhiteListTitle, creatorPhone, startTime, endTime, userByPhone, statetwo, state);
                    System.out.println(JSONHelper.Serialize(page));
                    String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
                    this.Write(json);
                } else {
                    this.Write("NO");
                }
            }
        } catch (Exception var12) {
            logger.info("手机端查询白名单错误" + var12.getMessage(), var12);
            var12.printStackTrace();
            this.Write("NO");
        }

    }


    /**
     * 查询白名单列表导出Cvs
     */
    public void findAllByCvs() {
        try {
            String whiteListNo = this.getString("whiteListNo");
            String whiteListTitle = this.getString("whiteListTitle");
            String creatorPhone = this.getString("creatorPhone");
            String startTime = this.getString("startTime");
            String endTime = this.getString("endTime");
            String statetwo = this.getString("statetwo");
            String state = this.getString("state");
            if (state != null && !"".equals(state)) {
                List<Map<String, Object>> list = this.whiteRollListService.findAll(whiteListNo, whiteListTitle, creatorPhone, startTime, endTime, this.user, statetwo, state);
                whiteRollListService.findAllWhiteListCvs(list);
            } else {
                this.Write("NO");
            }
        } catch (Exception var12) {
            var12.printStackTrace();
            this.Write("NO");
        }

    }

    /**
     * 导出模板信息
     */
    public void WhiteListInform() {
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name = "";
            String filepath = null;

            name = "白名单模板";
            filepath = request.getSession().getServletContext().getRealPath("/template/WhiteRollList.xlsx");
            //System.out.println(filepath);
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
        }
    }


    /**
     * 白名单导入
     */
    public void importWhiteListDet() {
        try {
            ExcelUtil excelReader = new ExcelUtil(this.file1);
            InputStream is = new FileInputStream(this.file1);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            System.out.println("这是列数" + column);
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList();
            int i;
            HashMap maps;
            if (column == 8) {
                for (i = 1; i <= map.size(); ++i) {
                    maps = new HashMap();
                    maps.put("batchUnitId", ((Map) map.get(i)).get(0));
                    maps.put("batchUnitName", ((Map) map.get(i)).get(1));
                    maps.put("batchUnitGrade", ((Map) map.get(i)).get(2));
                    maps.put("batchExpDate", ((Map) map.get(i)).get(3));
                    maps.put("batchType", ((Map) map.get(i)).get(4));
                    maps.put("batchExtensionlimt", ((Map) map.get(i)).get(5));
                    maps.put("batchLength", ((Map) map.get(i)).get(6));
                    maps.put("batchRecycleDate", ((Map) map.get(i)).get(7));
                    list.add(maps);
                }
            }
            String json2 = JSONHelper.SerializeWithNeedAnnotation(list);
            System.out.println(json2);
            System.out.println(map.size());
            if (map.size() > 0) {
                this.Write(json2);
            } else {
                this.Write("NULL");
            }
        } catch (Exception var11) {
            var11.printStackTrace();
            this.Write("NO");
        }

    }


    /**
     * 查询任务信息
     */
    public void queryProcessTrackingById() {
        String id = this.getString("id");
        List<Bpms_riskoff_task> WhiteList = this.taskService.getPublicEntityTaskList(id);
        //System.out.println(JSONHelper.Serialize(WhiteList));
        this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(WhiteList));
    }

    /**
     * 查询产品信息
     */
    public void getWhiteListDetList() {
        try {
            String id = this.getString("id");
            List<WhiteDetList> whiteListDets = this.whiteRollListService.getWhiteListDetList(id);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(whiteListDets));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("ON");
        }

    }

    /**
     * 查询工单信息
     */
    public void queryWhiteListById() {
        try {
            String orderNo = this.getString("orderNo");
            Map<String, Object> map = this.whiteRollListService.findWhiteListByRowNo(orderNo);
            this.Write((new GsonBuilder()).serializeNulls().setDateFormat("yyyy-MM-dd HH:mm:ss").excludeFieldsWithoutExposeAnnotation().create().toJson(map));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("NO");
        }
    }

    /**
     * <方法序号：11 > <方法名：Invalid> <详细描述：作废工单及其审批流程>
     *
     * @Param: []
     * @return: void
     * @Author: gcy
     */
    public void Invalid() {
        try {
            System.out.println("作废执行");
            String id = this.getString("id");
            String waitId = this.getString("waitId");//待办id
            String processId = this.getString("processId");//流程id
            WhiteRollList whiteRollList = this.whiteRollListService.getWhiteRollList(id);
            String whiteId = whiteRollList.getId();
            //-2作废工单
            whiteRollList.setState(-2);
            this.whiteRollListService.updateWhiteList(whiteRollList);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
            // 结束当前待办
            if (wt != null) {
                System.out.println("================处理中开始代办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            jbpmUtil.deleteProcessInstance(processId);//结束流程
            this.Write("YES");
        } catch (Exception var7) {
            var7.printStackTrace();
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    /**
     * 生成待办
     *
     * @param whiteRollList
     * @param userid
     * @param user
     * @param taskid
     */
    public void commitBackLogData(WhiteRollList whiteRollList, Integer userid, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[白名单]" + whiteRollList.getWhiteListTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/whiteRollList/whiteRollApproval.jsp");
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("BMD");
        waitTask.setTaskId(taskid);
        waitTask.setOrderNo(whiteRollList.getWhiteListNo());
        this.service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * 生成抄送待办
     *
     * @param whiteRollList
     * @param userid
     * @param user
     * @param taskid
     */
    public void commitBackLogDataCopy(WhiteRollList whiteRollList, Integer userid, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[白名单抄送]" + whiteRollList.getWhiteListTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/whiteRollList/whiteRollCopy.jsp");
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("BMD");
        waitTask.setTaskId(taskid);
        waitTask.setOrderNo(whiteRollList.getWhiteListNo());
        this.service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * 生成完成提示待办
     *
     * @param whiteRollList
     * @param userid
     * @param user
     * @param taskid
     */
    public void hintBackLogData(WhiteRollList whiteRollList, Integer userid, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[白名单提示]" + whiteRollList.getWhiteListTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/whiteRollList/whiteRollApproval.jsp");
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("BMD");
        waitTask.setTaskId(taskid);
        waitTask.setOrderNo(whiteRollList.getWhiteListNo());
        this.service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * 完成生成待办
     */
    public void returnHintBackLogData() {
        logger.info("白名单,同意");
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 意见
            String taskId = this.getString("TaskId");

            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
            WhiteRollList rl = whiteRollListService.getWhiteRollList(id); //查询工单信息
            rl.setState(0);
            Bpms_riskoff_task Whitetask = this.taskService.getBpms_riskoff_task(taskId);
            if (Whitetask != null) {
                this.taskService.updateBpms_riskoff_task(opinion, 2, taskId); //修改任务表
            }

            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息

            String rtaskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH", "客户经理", Integer.valueOf(rl.getCreator()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
                rl.setState(3);
                WhiteRollList rrl = whiteRollListService.updateWhiteRollList(rl);
                hintBackLogData(rrl, process.getCreator_no(), user, rtaskid);// 生成待办
                Write("YES");
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
        } catch (Exception e) {
            logger.info("推送Boss接口错误信息==>" + e);
            e.printStackTrace();
            Write("NO!推送Boss接口出错");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成退回待办
     *
     * @param whiteRollList
     * @param userid
     * @param user
     * @param taskid
     */
    public void handBackLog(WhiteRollList whiteRollList, Integer userid, SystemUser user, String taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[白名单退回]" + whiteRollList.getWhiteListTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/whiteRollList/whiteListReturn.jsp");
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("BMD");
        waitTask.setTaskId(taskid);
        waitTask.setOrderNo(whiteRollList.getWhiteListNo());
        this.service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * 流程退回
     */
    public void returnClaimData() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("taskId");// 任务表id

            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
            WhiteRollList rl = whiteRollListService.getWhiteRollList(id); //查询工单信息
            rl.setState(2);// 修改状态为退回
            WhiteRollList rrl = whiteRollListService.updateWhiteRollList(rl);
            Bpms_riskoff_task aa = taskService.updateBpms_riskoff_task(opinion, 2, taskId);     //修改任务表
            System.out.println("====>" + JSONHelper.Serialize(aa));
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(rl.getCreator()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            handBackLog(rrl, process.getCreator_no(), user, rtaskid);// 生成待办
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 开始流程
     */
    public void saveWhiteRoll() {
        logger.info("白名单开始流程");
        try {
            String waitId = getString("waitId");
            String orderNo = "";//申请编码前面的字母
            List<Object[]> sone = whiteRollListService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                orderNo = (String) sone.get(i)[2];
            }
            WaitTask wt = this.service.queryWaitByTaskId(waitId);
            if (wt != null) {
                this.service.updateWait(wt, this.getRequest());
            }
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String role = this.getString("role");
            Integer userId = this.getInteger("userId");
            String attachmentId = this.getString("attachmentId");//附件id
            String json = this.getString("json");
            WhiteRollList whiteRollList = new WhiteRollList();
            whiteRollList.setWhiteListNo(orderNo + getUnlockedNumber());
            whiteRollList.setWhiteListTitle(this.getString("whiteListTitle"));
            whiteRollList.setWhiteListMemo(this.getString("whiteListMemo"));
            whiteRollList.setCompanyCode(this.getString("companyCode"));//地市
            whiteRollList.setCreatorPhone(this.getString("creatorPhone"));
            whiteRollList.setRegion(this.getString("region"));
            whiteRollList.setCreator(this.getString("creator"));//
            whiteRollList.setCreatorName(this.getString("creatorName"));
            whiteRollList.setState(0);//审批中
            whiteRollList.setSUBMISSION("0");//未推送
            whiteRollList.setCreateDate(new Date());
            String id = this.whiteRollListService.addWhiteRollList(whiteRollList);
            boolean whether = false;
            JSONArray jsonArr = JSONArray.fromObject(json);
            if (json != null) {
                for (int i = 0; i < jsonArr.size(); ++i) {
                    JSONObject job = jsonArr.getJSONObject(i);
                    WhiteDetList whiteListDet = new WhiteDetList();
                    whiteListDet.setBatchUnitId(job.get("batchUnitId").toString());
                    whiteListDet.setBatchUnitName(job.get("batchUnitName").toString());
                    whiteListDet.setBatchUnitGrade(job.get("batchUnitGrade").toString());
                    whiteListDet.setBatchDay(job.get("batchDay").toString());
                    whiteListDet.setUpDate(new Date());
                    if (job.getString("batchExtensionlimt").equals("0")) {
                        whiteListDet.setBatchType("1");
                    } else {
                        whiteListDet.setBatchType("0");
                    }
                    whiteListDet.setBatchExtensionlimt(job.get("batchExtensionlimt").toString());
                    whiteListDet.setBatchLength(job.get("batchLength").toString());
                    whiteListDet.setSUBMISSION("0");
                    whiteListDet.setWhiteId(id);
                    whiteListDet.setBatchRecycleDate(simpleDateFormat.parse(job.get("batchRecycleDate").toString()));
                    whiteListDet.setBlacklistInformation(job.get("blacklistInformation").toString());
                    if (!"否".equals(job.get("blacklistInformation").toString())) {
                        whether = true;
                    }
                    this.whiteRollListService.addWhiteListDet(whiteListDet);
                }
            }

            if (whether) {
                WhiteRollList roll = whiteRollListService.getWhiteRollList(id);
                roll.setWhetherBlacklist("0");//是否为黑名单 0.是 1.否
                whiteRollListService.updateWhiteRollList(roll);
            }

            if (!StringUtils.isEmpty(attachmentId) && attachmentId != null) {
                String[] jsontwo = attachmentId.split(",");
                if (jsontwo.length > 0) {
                    for (int i = 0; i < jsontwo.length; ++i) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(whiteRollList.getId());
                        sa.setAttachmentId(jsontwo[i]);
                        sa.setLink(WhiteRollList.WHITEROLLLIST);
                        this.claimForFundsService.saveSandA(sa);
                    }
                }
            }
            String node = "";
            if ("QX".equals(role)) {
                node = "ROLE_QXDM";
            } else if ("QX2".equals(role)) {
                node = "ROLE_QXYW";
            } else if ("SGS".equals(role)) {
                node = "ROLE_SGSKHJL";
            } else if ("SZK".equals(role)) {
                node = "ROLE_SZKKHJL";
            }
            Map<String, String> map = new HashMap<String, String>();
            map.put("node", node);
            //String processId = this.transferJBPMUtils.startTransfer("WhiteRollList", map); // WhiteRollList
            //String processId = this.transferJBPMUtils.startTransfer("WhiteProcessFinal", map); // WhiteRollList
            //String processId = this.transferJBPMUtils.startTransfer("WhiteNewProcessFinal", map); // WhiteRollList WhiteNewProcessFinal WhiteProcessFinalNew
            String processId = this.transferJBPMUtils.startTransfer("WhiteProcessUltimate", map); // 2023-04-13 白名单终极版流程图
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            this.taskService.updateBpms_riskoff_task("", 2, task.getId());
            this.taskService.setBpms_riskoff_process(whiteRollList.getId(), processId, 1, this.user);
            this.taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", this.user.getRowNo(), this.user);
            String taskid = this.taskService.setBpms_riskoff_task(processId, (String) null, 1, "SH", task.getActivityName(), userId, this.user);
            this.commitBackLogData(whiteRollList, userId, this.user, taskid);
            this.Write("YES");
        } catch (Exception var16) {
            logger.info("白名单流程发起失败" + var16);
            var16.printStackTrace();
            this.Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 抄送
     */
    public void whiteRollSrvCopy() {
        try {
            String pid = this.getString("processId");
            String id = this.getString("id");
            String userid = this.getString("userId");
            String phone = getString("phone");
            String[] split = userid.split(",");
            WhiteRollList whiteRollList = this.whiteRollListService.getWhiteRollList(id);
            SystemUser user = new SystemUser();

            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }


            for (int i = 0; i < split.length; i++) {
                String taskId = taskService.setBpms_riskoff_task(pid, "", 1, "CS", "白名单抄送", Integer.parseInt(split[i]), user);
                this.commitBackLogDataCopy(whiteRollList, Integer.parseInt(split[i]), user, taskId);
            }
            this.Write("YES");
        } catch (Error var15) {
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        } catch (Exception var16) {
            var16.printStackTrace();
            this.Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 关闭抄送
     */
    public void closeWhiteRollCopy() {
        try {
            String waitId = this.getString("waitId");//获取代办id
            String taskId = this.getString("TaskId");
            Bpms_riskoff_task Whitetask = this.taskService.getBpms_riskoff_task(taskId);
            if (Whitetask != null) {
                this.taskService.updateBpms_riskoff_task("抄送,已确认", 2, taskId);
            }
            WaitTask wt = this.service.queryWaitByTaskId(waitId);//获取代办
            if (wt == null) {
                throw new Error("待办ID==========：" + "waitId");
            }
            //判断代办
            this.service.updateWait(wt, this.getRequest());
            this.Write("YES");
        } catch (Error var15) {
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        } catch (Exception var16) {
            var16.printStackTrace();
            this.Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 流程进行Two
     * 转审
     */
    public void handleWhiteRollSrvTwo() {
        System.out.println("白名单流程进行中");
        try {
            String pid = this.getString("processId");
            String id = this.getString("id");
            String t = this.getString("zhuanshentwo");
            String userid = this.getString("userId");
            String opinion = this.getString("opinion");
            String waitId = this.getString("waitId");
            String taskId = this.getString("TaskId");
            String Judge = getString("Judge");

            String phone = getString("phone");//当前审批人电话,手机端参数
            String flags = getString("flags");//是否为退回市公司业务管理室经理
            String attId = getString("attachmentId");//其他附件ID

            //手机端获取taskId
            Bpms_riskoff_task Whitetask;
            if (taskId == null || taskId.equals("")) {
                Whitetask = whiteRollListService.getTask(pid);
                taskId = Whitetask.getId();
            } else {
                Whitetask = this.taskService.getBpms_riskoff_task(taskId);
            }

            //手机端获取waitId
            if (waitId == null || waitId.equals("")) {
                WaitTask waitTask = whiteRollListService.getWait(taskId);
                waitId = waitTask.getWaitId();
            }


//            Bpms_riskoff_task Whitetask = this.taskService.getBpms_riskoff_task(taskId);
//            if (waitId == null || waitId.equals("") || taskId == null || taskId.equals("")) {
//                WaitTask waitTask = whiteRollListService.getWaitTask(id);
//                waitId = waitTask.getWaitId();
//                String str = waitTask.getUrl();
//                taskId = str.substring(str.lastIndexOf("=") + 1);
//            }


            WhiteRollList whiteRollList = this.whiteRollListService.getWhiteRollList(id);

            if (Whitetask != null) {
                this.taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            logger.info("流程节点为==>" + task.getActivityName());
            logger.info("前端传递为==>" + t);
            String str = pid.substring(0, pid.indexOf("."));

            if ("WhiteProcessFinal".equals(str)) {
                Map<String, Object> map = new HashMap<String, Object>();
                if (task.getActivityName().equals("省公司政企业务管理室")) {
                    map.put("node", "大于");
                    this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                } else {
                    this.jbpmUtil.completeTask(task.getId(), t);
                }
            } else if ("WhiteProcessFinalNew".equals(str) || "WhiteNewProcessFinal".equals(str)) {
                Map<String, Object> map = new HashMap<String, Object>();
                if (task.getActivityName().equals("省公司政企业务管理室")) {
                    map.put("node", "大于");
                    this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                } else if (task.getActivityName().equals("省公司管理员(白名单)")) {
                    map.put("node", "大于");
                    this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSMR");
                } else {
                    this.jbpmUtil.completeTask(task.getId(), t);
                }
            } else if ("WhiteProcessUltimate".equals(str)) {
                Map<String, Object> map = new HashMap<String, Object>();
                if (task.getActivityName().equals("区县分管经理")) {
                    map.put("node", "大于");
                    this.jbpmUtil.completeTask(task.getId(), map, "ROLE_DSBM");
                } else if (task.getActivityName().equals("市公司政企部经理")) {
                    if (flags != null && flags.equals("YES")) {
                        whiteRollList.setState(4);//退回市公司业务管理室经理
                        this.jbpmUtil.completeTask(task.getId(), "退回");
                    } else {
                        map.put("node", "大于");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
                    }
                } else if (task.getActivityName().equals("市公司领导")) {
                    if (flags != null && flags.equals("YES")) {
                        whiteRollList.setState(4);//退回市公司业务管理室经理
                        this.jbpmUtil.completeTask(task.getId(), "退回");
                    } else {
                        this.jbpmUtil.completeTask(task.getId(), t);
                    }
                } else if (task.getActivityName().equals("省公司管理员(白名单)")) {
                    if (flags != null && flags.equals("YES")) {
                        whiteRollList.setState(4);//退回市公司业务管理室经理
                        this.jbpmUtil.completeTask(task.getId(), "退回");
                    } else {
                        map.put("node", "大于");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSMR");
                    }
                } else if (task.getActivityName().equals("省公司政企业务管理室")) {
                    if (flags != null && flags.equals("YES")) {
                        whiteRollList.setState(4);//退回市公司业务管理室经理
                        this.jbpmUtil.completeTask(task.getId(), "退回");
                    } else {
                        map.put("node", "大于");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                    }
                } else if (task.getActivityName().equals("省公司政企客户部经理")) {
                    if (flags != null && flags.equals("YES")) {
                        whiteRollList.setState(4);//退回市公司业务管理室经理
                        this.jbpmUtil.completeTask(task.getId(), "退回");
                    } else {
                        this.jbpmUtil.completeTask(task.getId(), t);
                    }
                } else {
                    this.jbpmUtil.completeTask(task.getId(), t);
                }
            } else {
                this.jbpmUtil.completeTask(task.getId(), t);
            }
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            String rtaskid = "";
            //获取修改后的
            Task taskTwo = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            if (Judge.equals("zhuanshen")) {
                rtaskid = this.taskService.setBpms_riskoff_task(pid, "", 1, "ZS", taskTwo.getActivityName(), Integer.parseInt(userid), user);
            } else {
                rtaskid = this.taskService.setBpms_riskoff_task(pid, "", 1, "SH", taskTwo.getActivityName(), Integer.parseInt(userid), user);
            }

            //添加其他附件
            if (!StringUtils.isEmpty(attId)) {
                if (!attId.equals("")) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] jsons = attId.split(",");
                    if (jsons.length > 0) {
                        for (String json : jsons) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(whiteRollList.getId());
                            sa.setAttachmentId(json);
                            sa.setLink(WhiteRollList.WHITEROLLLIST);
                            whiteRollListService.saveSandA(sa);
                        }
                    }
                }
            }
            if (flags == null) {
                whiteRollList.setState(0);
            }
            whiteRollListService.updateWhiteRollList(whiteRollList);
            WaitTask wt = this.service.queryWaitByTaskId(waitId);
            if (wt == null) {
                throw new Error("待办ID==========：" + waitId);
            }
            System.out.println("================处理中开始代办================");
            this.service.updateWait(wt, this.getRequest());
            System.out.println("================处理中结束代办================");
            this.commitBackLogData(whiteRollList, Integer.parseInt(userid), user, rtaskid);
            this.Write("YES");
            //System.out.println("白名单流程完成");
        } catch (Exception e) {
            logger.error("白名单提交失败==" + e.getMessage(), e);
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    /**
     * 完成流程
     * 省公司
     */
    public void complateTwoClaimForFunds() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmss");
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String pid = this.getString("processId");   //流程id
            String id = this.getString("id");       //工单id
            String waitId = this.getString("waitId");   //待办id
            String opinion = this.getString("opinion");     //处理意见
            String taskId = this.getString("TaskId");   //任务id

            WhiteRollList rl = this.whiteRollListService.getWhiteRollList(id);    //工单
            WaitTask wt = this.service.queryWaitByTaskId(waitId);       //待办
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();     //流程
            Bpms_riskoff_task Whitetask = this.taskService.getBpms_riskoff_task(taskId);
            boolean a = true;
            if (wt != null) {
                List<Map<String, Object>> whiteListDet = whiteRollListService.getWhiteListDet(id);
                for (int i = 0; i < whiteListDet.size(); i++) {
                    //判断是否推送成功
                    if ("1".equals(whiteListDet.get(i).get("SUBMISSION").toString())) {
                        //推送成功的不再推送
                        //推送未推送的和推送失败的
                    } else {

                        //当前时间 + 欠费天数=最终欠费时间
                        Calendar ca = Calendar.getInstance();
                        ca.add(Calendar.DATE, Integer.valueOf(whiteListDet.get(i).get("BATCH_DAY").toString()));
                        Date date = ca.getTime();
                        //保存最终欠费时间
                        WhiteDetList detList = whiteRollListService.getWhiteListTwo(whiteListDet.get(i).get("ID").toString());
                        detList.setBatchExpDate(date);
                        WhiteDetList wdl = whiteRollListService.updateList(detList);

                        try {
                            Result result = whiteRollListService.groupWhiteAccounts(whiteListDet.get(i).get("BATCH_UNITID").toString(),
                                    user.getBossUserName(),
                                    formatter.format(wdl.getBatchExpDate()),
                                    rl.getWhiteListTitle());
                            if (result.getCode() == 200) {
                                String message = result.getData().toString();
                                JSONObject jsonObject = JSONObject.fromObject(message);
                                JSONObject root = jsonObject.getJSONObject("ROOT");
                                if (root.getString("RETURN_CODE").equals("0")) {
                                    WhiteDetList WhiteDet = whiteRollListService.getWhiteListTwo(whiteListDet.get(i).get("ID").toString());
                                    WhiteDet.setSUBMISSION("1");
                                    WhiteDet.setUpDate(new Date());
                                    whiteRollListService.updateList(WhiteDet);
                                } else {
                                    WhiteDetList WhiteDet = whiteRollListService.getWhiteListTwo(whiteListDet.get(i).get("ID").toString());
                                    WhiteDet.setSUBMISSION("2");
                                    WhiteDet.setUpDate(new Date());
                                    whiteRollListService.updateList(WhiteDet);
                                }
                            } else {
                                WhiteDetList WhiteDet = whiteRollListService.getWhiteListTwo(whiteListDet.get(i).get("ID").toString());
                                WhiteDet.setSUBMISSION("2");
                                WhiteDet.setUpDate(new Date());
                                whiteRollListService.updateList(WhiteDet);
                            }
                        } catch (Exception e) {
                            logger.info("推送集团统付出错" + e.getMessage());
                        }
                    }
                }
                List<Map<String, Object>> whiteListTwo = whiteRollListService.getWhiteListDet(id);
                for (int i = 0; i < whiteListTwo.size(); i++) {
                    if ("2".equals(whiteListTwo.get(i).get("SUBMISSION").toString()) || "0".equals(whiteListTwo.get(i).get("SUBMISSION").toString())) {
                        //System.out.println("推送==>" + whiteListTwo.get(i).get("SUBMISSION").toString());
                        a = false;
                        break;
                    }
                }
                //判断是否有失败的推送
                if (a) {
                    if (Whitetask != null) {
                        this.taskService.updateBpms_riskoff_task("已确认", 2, taskId);
                    }
                    this.service.updateWait(wt, this.getRequest());     //结束待办
                    this.taskService.updateBpms_riskoff_task("已确认", 2, taskId);   //结束任务

                    String str = pid.substring(0, pid.indexOf("."));
                    if ("WhiteProcessFinal".equals(str)) {
                        if (task.getActivityName().equals("省公司政企业务管理室")) {
                            Map<String, Object> map = new HashMap<String, Object>();
                            map.put("node", "小于");
                            this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                        } else {
                            this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                        }
                    } else if ("WhiteProcessFinalNew".equals(str) || "WhiteNewProcessFinal".equals(str)) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        if (task.getActivityName().equals("省公司政企业务管理室")) {
                            map.put("node", "小于");
                            this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                            //this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                        } else if (task.getActivityName().equals("省公司管理员(白名单)")) {
                            map.put("node", "小于");
                            this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSMR");
                        } else {
                            this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                        }
                    } else if ("WhiteProcessUltimate".equals(str)) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        if (task.getActivityName().equals("区县分管经理")) {
                            map.put("node", "END");
                            this.jbpmUtil.completeTask(task.getId(), map, "ROLE_DSBM");
                        } else if (task.getActivityName().equals("市公司政企部经理")) {
                            map.put("node", "END");
                            this.jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
                        } else if (task.getActivityName().equals("省公司管理员(白名单)")) {
                            map.put("node", "END");
                            this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSMR");
                        } else if (task.getActivityName().equals("省公司政企业务管理室")) {
                            map.put("node", "END");
                            this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                        } else {
                            this.jbpmUtil.completeTask(task.getId(), "END");
                        }
                    } else {
                        this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                    }

                    rl.setState(1);     //修改工单状态为已完成
                    whiteRollListService.updateWhiteRollList(rl);
                    this.Write("YES");
                } else {
                    this.Write("PUSH");
                }
            } else {
                this.Write("NO");
            }
        } catch (Exception var12) {
            logger.error("白名单推送失败!" + var12.getMessage(), var12);
            this.Write("NO!推送boss接口失败");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 结束推送
     * 完成工单
     */
    public void processEnd() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String pid = this.getString("processId");   //流程id
            String id = this.getString("id");       //工单id
            String waitId = this.getString("waitId");   //待办id
            String opinion = this.getString("opinion");     //处理意见
            String taskId = this.getString("TaskId");   //任务id
            WhiteRollList rl = this.whiteRollListService.getWhiteRollList(id);    //工单
            WaitTask wt = this.service.queryWaitByTaskId(waitId);       //待办
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();     //流程
            Bpms_riskoff_task Whitetask = this.taskService.getBpms_riskoff_task(taskId);
            String str = pid.substring(0, pid.indexOf("."));
            if (Whitetask != null) {
                this.taskService.updateBpms_riskoff_task("已确认", 2, taskId);
            }
            if (wt != null) {
                this.service.updateWait(wt, this.getRequest());     //结束待办
                this.taskService.updateBpms_riskoff_task("已确认", 2, taskId);   //结束任务
                if ("WhiteProcessFinal".equals(str)) {
                    if (task.getActivityName().equals("省公司政企业务管理室")) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("node", "小于");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                    } else {
                        this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                    }
                } else if ("WhiteProcessFinalNew".equals(str) || "WhiteNewProcessFinal".equals(str)) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    if (task.getActivityName().equals("省公司政企业务管理室")) {
                        map.put("node", "小于");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                        //this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                    } else if (task.getActivityName().equals("省公司管理员(白名单)")) {
                        map.put("node", "小于");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSMR");
                    } else {
                        this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                    }
                } else if ("WhiteProcessUltimate".equals(str)) {
                    Map<String, Object> map = new HashMap<String, Object>();
                    if (task.getActivityName().equals("区县分管经理")) {
                        map.put("node", "END");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_DSBM");
                    } else if (task.getActivityName().equals("市公司政企部经理")) {
                        map.put("node", "END");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
                    } else if (task.getActivityName().equals("省公司管理员(白名单)")) {
                        map.put("node", "END");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSMR");
                    } else if (task.getActivityName().equals("省公司政企业务管理室")) {
                        map.put("node", "END");
                        this.jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSSM");
                    } else {
                        this.jbpmUtil.completeTask(task.getId(), "END");
                    }
                } else {
                    this.jbpmUtil.completeTask(task.getId(), "END");    //结束流程
                }
                rl.setState(1);     //修改工单状态为已完成
                whiteRollListService.updateWhiteRollList(rl);
                this.Write("YES");
            } else {
                this.Write("NO");
            }
        } catch (Exception var12) {
            this.Write("NO!推送boss接口失败");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 获取附件消息
     */
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = whiteRollListService.fuJian(id, biaoshi);
        Write(JSONHelper.Serialize(s));
    }


    /**
     * 白名单工单详情查询
     */
    public void findPage() {
        System.out.println("运行");
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String batchUnitId = getString("batchUnitId");        //集团280
            String batchUnitName = getString("batchUnitName");        //集团名称
            String stat = getString("stat");  //是否有效

            // 获取用户权限
            List list = whiteRollListService.findByRowNo(user.getRowNo());
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("ROLE_REPM")) { //订单管理员
                    flag = true;
                    break;
                }
            }

            page = whiteRollListService.findActiveCodeByPage(page, batchUnitId, batchUnitName, stat, this.user, flag);
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            //System.out.println(json);
            this.Write(json);
        } catch (Exception e) {
            System.out.println(e);
            logger.info(e.toString());
            this.Write("列表加载失败！");
        }
    }


    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description: TODO 返回参数生成
     */
    private static String returnPars(int state, String data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }


    /**
     * 根据280查询产品
     */
    public void querySqryzxProdInfo() {
        try {
            String batchUnitID = getString("batchUnitID");// 集团编码
            System.out.println(batchUnitID);
            Result result = UnitAccountInfoSrv.getInstance().sZqQryProInfo(batchUnitID, "", "", user.getBossUserName());

            //Result result = QueryProductSvr.getInstance().getProductByGroupCode(batchUnitID, "aagh38");
            if (result.getCode() == 200) {
                JSONObject json = JSONObject.fromObject(result.getData());
                System.out.println("json =  " + json.toString());
                JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
                if ("0".equals(root.getString("RETURN_CODE"))) {
                    JSONObject outData = JSONObject.fromObject(root.getString("OUT_DATA"));
                    if (outData.size() > 0) {
                        JSONArray dataList = JSONArray.fromObject(outData.getString("DATA_LIST"));
                        JSONObject prodData = JSONObject.fromObject(dataList.getString(0));
                        if (JSONArray.fromObject(prodData.getString("DATA")).size() > 0) {
                            JSONArray datas = JSONArray.fromObject(prodData.getString("DATA"));

                            long maxDay = 0;
                            for (int i = 0; i < datas.size(); i++) {
                                String s = datas.getString(i);
                                JSONObject jsonObject = JSONObject.fromObject(s);
                                if (jsonObject.get("RUN_CODE").equals("D")) {
                                    SimpleDateFormat format1 = new SimpleDateFormat("yyyyMMddHHmmss");
                                    Date date = null;
                                    String str = null;
                                    long timeStemp1 = 0;
                                    long timeStemp2 = 0;
                                    long day = 0;

                                    Date datenew = new Date();
                                    str = jsonObject.get("RUN_TIME").toString();//     设定未来某一时间段

                                    date = format1.parse(str);

                                    timeStemp1 = date.getTime();
                                    timeStemp2 = datenew.getTime();
                                    day = (timeStemp2 - timeStemp1) / (24 * 60 * 60 * 1000);
                                    if (day > maxDay) {
                                        maxDay = day;
                                    }
                                }
                            }
                            Write(returnPars(1, String.valueOf(maxDay), ""));
                        } else {
                            Write(returnPars(-1, "", "未查询到可用的产品信息,请联系系统管理员核实核实"));
                        }
                    } else {
                        Write(returnPars(-1, "", "未查询到可用的产品信息,请联系系统管理员核实核实"));
                    }
                } else {
                    Write(returnPars(-1, "", "获取产品数据异常：" + root.getString("DETATL_MSG")));
                }
            } else {
                Write(returnPars(-1, "", "产品查询接口异常！"));
            }
        } catch (Exception e) {
            logger.error("红名单查询产品错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "产品查询错误：" + e.getMessage()));
            e.printStackTrace();
        }
    }

    //根据280查询 欠费时长
    public void findBy280() {
        String groupCoding = getString("groupCoding");
        try {
            List<Map<String, Object>> list = whiteRollListService.getArrearsDetNew(groupCoding);

            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            JSONObject pars = new JSONObject();
            //获取当前日期
            Date nowDate = new Date();
            long maxDay = 0;
            double money = 0.0;

            if (list.size() > 0) {
                if (list.get(0).get("SUB_FEE") != null) {//有销账
                    money = Double.parseDouble(String.valueOf(list.get(0).get("SUB_FEE")));
                } else {//无销账
                    List<Map<String, Object>> listTwo = whiteRollListService.getArrearsDetNewTwo(groupCoding);
                    money = Double.parseDouble(String.valueOf(listTwo.get(0).get("SUB_FEE")));
                }
            }

            List<Map<String, Object>> period = whiteRollListService.getArrearsPeriod(groupCoding);
            for (int i = 0; i < period.size(); i++) {
                String month = period.get(i).get("OWE_MONTH") + "01";
                Date date = sdf.parse(month);
                //转为毫秒值进行换算
                long nowSecond = nowDate.getTime();
                long birthdaySecond = date.getTime();
                //计算
                long second = nowSecond - birthdaySecond;
                long day = second / 1000 / 60 / 60 / 24;
                //获取最大天数
                if (day > maxDay) {
                    maxDay = day;
                }
            }

            DecimalFormat df = new DecimalFormat("#.00");
            pars.put("maxDay", maxDay);
            if (money != 0) {
                pars.put("money", df.format(money));
            } else {
                pars.put("money", money);
            }
            Write(pars.toString());
        } catch (Exception e) {
            logger.error("白名单查询欠费时长错误：" + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    //白名单手机端按钮
    public void findTransitionApp() {
        Result r = new Result();
        String id = getString("id");
        String phone = getString("phone");
        List<String> outcomes = new ArrayList<>();
        try {
            user = systemUserService.getUserByPhone(phone);
            user = systemUserService.querUsers(user.getLoginName());
            if (user == null) {
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("查询人员失败，号码有误");
                Write(r.toString());
                return;
            }
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("根据号码查询人员异常");
            Write(r.toString());
            return;
        }

        Bpms_riskoff_process rap = whiteRollListService.getPid(id);
        WhiteRollList whiteRollList = whiteRollListService.getWhiteRollList(id);
        List<Map<String, Object>> whiteListDet = whiteRollListService.getWhiteListDet(id);
        String pid = rap.getProcess_sign();
        if (!"".equals(pid) && pid != null) {
            // 获取任务对象
            try {
                logger.info("所查询的流程ID：=======》" + pid);
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                logger.info("当前流程节点====》" + task.getActivityName());
                List<String> buttons = new ArrayList<>();
                JSONObject obj = new JSONObject();
                for (String outcome : setlist) {
                    if (!outcome.equals("结束")) {
                        outcomes.add(outcome);
                    }
                }

                String str = pid.substring(0, pid.indexOf("."));
                if (str.equals("WhiteProcessUltimate")) {
                    if (task.getActivityName().equals("区县业务管理员")
                            || task.getActivityName().equals("区县政企部主任")
                            || task.getActivityName().equals("区县分管经理")
                            || task.getActivityName().equals("市公司客户经理室经理")
                            || task.getActivityName().equals("市公司业务管理室经理")
                            || task.getActivityName().equals("市公司领导")
                            || task.getActivityName().equals("省重客客户经理室经理")
                            || task.getActivityName().equals("省重客分管经理")
                            || task.getActivityName().equals("省重客业务管理员")) {
                        buttons.add("提交");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司政企部经理")
                            || task.getActivityName().equals("省公司政企业务管理室")) {
                        List<SystemDept> deptList = user.getSystemDept();
                        String code = deptList.get(0).getSystemCompany().getCompanyCode();
                        TransferCitiesData tcd = whiteRollListService.getTransferCitiesData(code, task.getActivityName());
                        double moneys = 0.0;//最大金额
                        int day = 0;//最大天数
                        for (int i = 0; i < whiteListDet.size(); i++) {
                            Map<String, Object> stringObjectMap = whiteListDet.get(i);
                            if (Double.parseDouble(String.valueOf(stringObjectMap.get("BATCH_EXTENSIONLIMT"))) > moneys) {
                                moneys = Double.parseDouble(String.valueOf(stringObjectMap.get("BATCH_EXTENSIONLIMT")));
                            }

                            if (Integer.parseInt(String.valueOf(stringObjectMap.get("BATCH_LENGTH"))) > day) {
                                day = Integer.parseInt(String.valueOf(stringObjectMap.get("BATCH_LENGTH")));
                            }
                        }
                        if (task.getActivityName().equals("市公司政企部经理")) {
                            if (moneys > Double.parseDouble(tcd.getAmount()) || day > 180 || "0".equals(whiteRollList.getWhetherBlacklist())) { //是否为黑名单 是.0 否.1或者空
                                buttons.add("提交");
                                buttons.add("退回");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            } else {
                                buttons.add("同意");
                                buttons.add("退回");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            }
                        } else {
                            if (moneys > Double.parseDouble(tcd.getAmount()) || day > 365 || "0".equals(whiteRollList.getWhetherBlacklist())) {//是否为黑名单 是.0 否.1或者空
                                buttons.add("提交");
                                buttons.add("退回");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            } else {
                                buttons.add("同意");
                                buttons.add("退回");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            }
                        }

                    } else if (task.getActivityName().equals("市公司业务管理员")
                            || task.getActivityName().equals("其他审核人1")
                            || task.getActivityName().equals("其他审核人2")
                            || task.getActivityName().equals("其他审核人3")
                            || task.getActivityName().equals("其他审核人4")) {
                        buttons.add("提交");
                        buttons.add("转审");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("省公司政企客户部经理")) {
                        buttons.add("同意");
                        buttons.add("退回");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("省公司管理员(白名单)")) {
                        double moneys = 0.0;
                        List<SystemDept> deptList = user.getSystemDept();
                        String code = deptList.get(0).getSystemCompany().getCompanyCode();
                        TransferCitiesData tcd = whiteRollListService.getTransferCitiesData(code, task.getActivityName());
                        //判断金额
                        for (int i = 0; i < whiteListDet.size(); i++) {
                            Map<String, Object> stringObjectMap = whiteListDet.get(i);
                            Object batchExtensionlimt = stringObjectMap.get("BATCH_EXTENSIONLIMT");
                            moneys += Double.parseDouble(String.valueOf(batchExtensionlimt));
                        }
                        if (moneys > Double.parseDouble(tcd.getAmount())) {
                            buttons.add("提交");
                            buttons.add("退回");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else {
                            buttons.add("同意");
                            buttons.add("退回");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        }
                    }
                } else {
                    //(提交)         区县业务管理员 区县政企部主任 区县分管经理 市公司客户经理室经理 市公司业务管理室经理 市公司领导 省重客客户经理室经理 省重客分管经理
                    //(提交,转审)     市公司业务管理员 审核人1 审核人2 市公司政企部经理 审核人3 审核人4
                    //(判断添加生成按钮)省公司管理员(白名单) 省公司政企业务管理室
                    //(结束)         省公司政企部经理
                    if (task.getActivityName().equals("区县业务管理员") || task.getActivityName().equals("区县政企部主任") || task.getActivityName().equals("区县分管经理") || task.getActivityName().equals("市公司客户经理室经理")
                            || task.getActivityName().equals("市公司业务管理室经理") || task.getActivityName().equals("市公司领导") || task.getActivityName().equals("省重客客户经理室经理") || task.getActivityName().equals("省重客分管经理")
                            || task.getActivityName().equals("市公司政企部经理") || task.getActivityName().equals("省重客业务管理员")) {
                        buttons.add("提交");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司业务管理员") || task.getActivityName().equals("其他审核人1") || task.getActivityName().equals("其他审核人2") || task.getActivityName().equals("其他审核人3") || task.getActivityName().equals("其他审核人4")) {
                        buttons.add("提交");
                        buttons.add("转审");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("省公司管理员(白名单)") || task.getActivityName().equals("省公司政企业务管理室")) {
                        double moneys = 0.0;
                        //判断金额
                        for (int i = 0; i < whiteListDet.size(); i++) {
                            Map<String, Object> stringObjectMap = whiteListDet.get(i);
                            //System.out.println(JSONHelper.Serialize(stringObjectMap));
                            //System.out.println(String.valueOf(stringObjectMap.get("BATCH_EXTENSIONLIMT")));
                            Object batchExtensionlimt = stringObjectMap.get("BATCH_EXTENSIONLIMT");
                            moneys += Double.parseDouble(String.valueOf(batchExtensionlimt));
                        }
                        if (task.getActivityName().equals("省公司管理员(白名单)")) {
                            if (whiteRollList.getState() == 3) {
                                buttons.add("完成");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                //obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            } else {
                                if (moneys > 500000) {
                                    buttons.add("提交");
                                    obj.put("pid", pid);
                                    obj.put("buttons", buttons);
                                    obj.put("outcomes", outcomes);
                                    r.setCode(ResultCode.SUCCESS);
                                    r.setMessage("成功");
                                    r.setData(obj);
                                } else {
                                    buttons.add("同意");
                                    obj.put("pid", pid);
                                    obj.put("buttons", buttons);
                                    obj.put("outcomes", outcomes);
                                    r.setCode(ResultCode.SUCCESS);
                                    r.setMessage("成功");
                                    r.setData(obj);
                                }
                            }
                        }
                        if (task.getActivityName().equals("省公司政企业务管理室")) {
                            if (whiteRollList.getState() == 3) {
                                buttons.add("完成");
                                obj.put("pid", pid);
                                obj.put("buttons", buttons);
                                //obj.put("outcomes", outcomes);
                                r.setCode(ResultCode.SUCCESS);
                                r.setMessage("成功");
                                r.setData(obj);
                            } else {
                                if (moneys > 1000000) {
                                    buttons.add("提交");
                                    obj.put("pid", pid);
                                    obj.put("buttons", buttons);
                                    obj.put("outcomes", outcomes);
                                    r.setCode(ResultCode.SUCCESS);
                                    r.setMessage("成功");
                                    r.setData(obj);
                                } else {
                                    buttons.add("同意");
                                    obj.put("pid", pid);
                                    obj.put("buttons", buttons);
                                    obj.put("outcomes", outcomes);
                                    r.setCode(ResultCode.SUCCESS);
                                    r.setMessage("成功");
                                    r.setData(obj);
                                }
                            }
                        }
                    } else if (task.getActivityName().equals("省公司政企部经理")) {
                        if (whiteRollList.getState() == 3) {
                            buttons.add("完成");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            //obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else {
                            buttons.add("同意");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        }
                    }
                }


                logger.info("白名单手机端获取的角色为==" + r.toString());
                Write(r.toString());
            } catch (Exception e) {
                // TODO: handle exception
                logger.info("转账手机端按钮流程信息获取异常====》" + e);
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("流程信息获取异常");
                Write(r.toString());
            }
        } else {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("流程ID为空");
            Write(r.toString());
        }
    }

    public void getListLength() {
        try {
            String phone = getString("phone");
            user = systemUserService.getUserByPhone(phone);
            List<Map<String, Object>> listLength = whiteRollListService.getListLength(String.valueOf(user.getRowNo()));
            Write(String.valueOf(listLength.size()));
        } catch (Exception e) {
            logger.info("白名单代办数量查询错误" + e.getMessage(), e);
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/4/14 11:38
     * @Description TODO 获取当前地市金额
     **/
    public void getAmount() {
        try {
            String pid = this.getString("processId");
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            List<SystemDept> deptList = user.getSystemDept();
            String code = deptList.get(0).getSystemCompany().getCompanyCode();
            TransferCitiesData transferCitiesData = whiteRollListService.getTransferCitiesData(code, task.getActivityName());
            if (transferCitiesData != null) {
                Write(transferCitiesData.getAmount());
            } else {
                Write("0");
            }
        } catch (Exception e) {
            logger.error("白名单获取地市金额异常：" + e.getMessage(), e);
            e.printStackTrace();
        }
    }


    /*
     * <AUTHOR>
     * @Date 2023/5/8 10:40
     * @Description TODO 获取pid
     **/
    public void getPid() {
        try {
            String id = getString("id");
            Bpms_riskoff_process rap = this.whiteRollListService.getPid(id);
            if (rap != null) {
                Write(rap.getProcess_sign());
            }
        } catch (Exception e) {
            this.logger.error("获取pid错误" + e.getMessage(), e);
        }
    }

    /**
     * 查询固化白名单
     */
    public void findQueryExport() {
        try {
            HttpServletRequest request = ServletActionContext.getRequest();
            HttpSession session = request.getSession();

            Result result = new Result();
            String jsonone = getString("jsonone");//多个
            String phone = getString("phone");//单个
            Result queryExport = whiteRollListService.findQueryExport(phone, jsonone, user);
            if (queryExport.getCode() == 200) {
                JSONObject json = JSONObject.fromObject(queryExport.getData());
                JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
                if ("0".equals(root.getString("RETURN_CODE"))) {
                    JSONArray outData = root.getJSONArray("OUT_DATA");
                    result.setCode(ResultCode.SUCCESS);
                    result.setData(outData);
                    session.setAttribute("OUT_DATA", outData);
                } else {
                    //接口返回失败
                    result.setCode(ResultCode.FAIL);
                    result.setData("接口返回失败");
                }
            } else {
                //接口调用失败
                result.setCode(ResultCode.INTERNAL_SERVER_ERROR);
                result.setData("接口调用失败");
            }
            Write(result.toString());
        } catch (Exception e) {
            this.Write("NO");
            logger.error("查询固化白名单异常：" + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    public void WhiteListInQueryExport() {
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name = "";
            String filepath = null;

            name = "固化白名单模板";
            filepath = request.getSession().getServletContext().getRealPath("/template/WhiteListInQueryExport.xlsx");
            //System.out.println(filepath);
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
        }
    }

    /**
     * 固化白名单导入
     */
    public void importWhiteListQueryExport() {
        try {
            HttpServletRequest request = ServletActionContext.getRequest();
            HttpSession session = request.getSession();
            Result result = new Result();
            ExcelUtil excelReader = new ExcelUtil(this.file1);
            InputStream is = new FileInputStream(this.file1);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            System.out.println("这是列数" + column);
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            System.out.println("这是条数数" + map.size());
            if (map.size() > 100) {
                result.setCode(ResultCode.NOT_FOUND);
                result.setData("导出的数据条数过多，请分批次导入");
                Write(result.toString());
                return;
            }
            JSONArray a = new JSONArray();
            JSONObject b = new JSONObject();
            for (int i = 1; i <= map.size(); ++i) {
                b.put("phone", ((Map) map.get(i)).get(0));
                a.add(b);
            }
            Result queryExport = whiteRollListService.findQueryExport("", a.toString(), user);
            if (queryExport.getCode() == 200) {
                JSONObject json = JSONObject.fromObject(queryExport.getData());
                JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
                if ("0".equals(root.getString("RETURN_CODE"))) {
                    JSONArray outData = root.getJSONArray("OUT_DATA");
                    result.setCode(ResultCode.SUCCESS);
                    result.setData(outData);
                    session.setAttribute("OUT_DATA", outData);
                } else {
                    //接口返回失败
                    result.setCode(ResultCode.FAIL);
                    result.setData("接口返回失败");
                }
            } else {
                //接口调用失败
                result.setCode(ResultCode.INTERNAL_SERVER_ERROR);
                result.setData("接口调用失败");
            }
            Write(result.toString());
        } catch (Exception var11) {
            var11.printStackTrace();
            this.Write("NO");
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/10/16 15:08
     * @Description 固话号码导出
     **/
    public void findWhiteListQueryExport() {
        try {
            HttpServletRequest request = ServletActionContext.getRequest();
            HttpSession session = request.getSession();
            JSONArray json = (JSONArray) session.getAttribute("OUT_DATA");
            if (json != null) {
                whiteRollListService.findWhiteListQueryExport(json);
                session.removeAttribute("OUT_DATA");
            } else {
                this.Write("数据已导出,请重新查询后再进行导出操作");
            }
        } catch (Exception var12) {
            var12.printStackTrace();
            this.Write("系统错误");
        }
    }

    //查询黑名单接口
    public void findWhetherBlacklist() {
        Result result = new Result();
        try {
            String groupCoding = getString("groupCoding");
            Result whetherBlacklist = whiteRollListService.findWhetherBlacklist(groupCoding, user);
            if (whetherBlacklist.getCode() == 200) {
                JSONObject json = JSONObject.fromObject(whetherBlacklist.getData());
                //logger.info("json=="+json);
//                String aaa="{\n" +
//                        "    \"ROOT\":{\n" +
//                        "        \"RETURN_MSG\":\"OK\",\n" +
//                        "        \"PROMPT_MSG\":\"\",\n" +
//                        "        \"OUT_DATA\":{\n" +
//                        "            \"DATA\":[\n" +
//                        "                {\n" +
//                        "                    \"AGE\":39\n" +
//                        "                },\n" +
//                        "                {\n" +
//                        "                    \"BLACK_NAME\":\"欠费销户\",\n" +
//                        "                    \"AGE\":32,\n" +
//                        "                    \"ID_TYPE\":\"1\",\n" +
//                        "                    \"ID_TYPE_NAME\":\"居民身份证\",\n" +
//                        "                    \"ID_ICCID\":\"513426199011293910\",\n" +
//                        "                    \"BLACK_TYPE\":11\n" +
//                        "                }\n" +
//                        "            ]\n" +
//                        "        },\n" +
//                        "        \"RETURN_CODE\":\"0\",\n" +
//                        "        \"USER_MSG\":\"OK\",\n" +
//                        "        \"RUN_IP\":\"*************\",\n" +
//                        "        \"DETAIL_MSG\":\"OK\"\n" +
//                        "    }\n" +
//                        "}";
//                JSONObject json = JSONObject.fromObject(aaa);
//                System.out.println("json =  " + json.toString());
                //JSONObject root = JSONObject.fromObject(json.getString("ROOT"));
                JSONObject root = json.getJSONObject("ROOT");
                if ("0".equals(root.getString("RETURN_CODE"))) {
                    result.setCode(ResultCode.SUCCESS);
                    JSONObject out_data = root.getJSONObject("OUT_DATA");
                    JSONArray data = out_data.getJSONArray("DATA");
                    if (data.size() > 1) {
                        String age = data.getJSONObject(1).getString("BLACK_TYPE");
                        if ("17".equals(age)) {
                            result.setData("疑似涉案黑名单-本省");
                        } else if ("18".equals(age)) {
                            result.setData("低活性号码");
                        } else if ("19".equals(age)) {
                            result.setData("集客黑名单");
                        } else if ("20".equals(age)) {
                            result.setData("入网证件黑名单-管局");
                        } else if ("21".equals(age)) {
                            result.setData("高危证件");
                        } else if ("22".equals(age)) {
                            result.setData("涉案黑名单");
                        } else if ("23".equals(age)) {
                            result.setData("集客个人类证件");
                        } else if ("24".equals(age)) {
                            result.setData("异网涉案黑名单-友商共享");
                        } else if ("25".equals(age)) {
                            result.setData("外呼疑似涉诈黑名单");
                        } else {
                            result.setData("否");
                        }
                    } else {
                        result.setData("否");
                    }
//                    17：疑似涉案黑名单-本省
//                    18：低活性号码
//                    19：集客黑名单
//                    20：入网证件黑名单-管局
//                    21：高危证件
//                    22：涉案黑名单
//                    23：集客个人类证件
//                    24：异网涉案黑名单-友商共享
//                    25：外呼疑似涉诈黑名单
                } else {
                    //接口返回失败
                    result.setCode(ResultCode.FAIL);
                    result.setData("接口返回失败");
                }
            } else {
                //接口调用失败
                result.setCode(ResultCode.INTERNAL_SERVER_ERROR);
                result.setData("接口调用失败");
            }
            Write(result.toString());
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("查询黑名单接口失败:" + e.getMessage(), e);
            result.setCode(ResultCode.INTERNAL_SERVER_ERROR);
            result.setData("调用接口失败:" + e.getMessage());
            Write(result.toString());
        }
    }
}
