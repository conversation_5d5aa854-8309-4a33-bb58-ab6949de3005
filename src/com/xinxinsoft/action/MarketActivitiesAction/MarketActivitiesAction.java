package com.xinxinsoft.action.MarketActivitiesAction;

import com.google.gson.GsonBuilder;
import com.google.gson.annotations.Expose;
import com.sun.scenario.effect.impl.sw.sse.SSEBlend_SRC_OUTPeer;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.MarketActivities.*;
import com.xinxinsoft.entity.SIM.SIMWorkOrder;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.redList.RedRollList;
import com.xinxinsoft.entity.repairOrder.RepairOrder;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.entity.whiteRollList.WhiteDetList;
import com.xinxinsoft.entity.whiteRollList.WhiteRollList;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.sendComms.accountService.UnitAccountInfoSrv;
import com.xinxinsoft.service.MarketActivitiesService.MarketActivitiesService;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.RepairOrderService.RepairOrderService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;

import java.util.*;

public class MarketActivitiesAction extends BaseAction {
    private Logger logger = LoggerFactory.getLogger(MarketActivitiesAction.class);
    @Resource(name = "MarketActivitiesService")
    private MarketActivitiesService marketActivitiesService;
    @Resource(name = "RepairOrderService")
    private RepairOrderService repairOrderService;
    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "WaitTaskService")
    private WaitTaskService waitTaskService;
    @Resource(name = "Bpms_riskoff_service")
    private Bpms_riskoff_service bpms_riskoff_service;
    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;

    private File file1;

    public File getFile1() {
        return file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }

    /**
     * 导入存量统存集团客户库
     */
    public void importClientLibrary() {
        try {
            ExcelUtil excelReader = new ExcelUtil(this.file1);
            InputStream is = new FileInputStream(this.file1);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            System.out.println("这是列数" + column);
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList();//保存正常数据
            List<Integer> list2 = new ArrayList();//保存问题数据的行数
            HashMap maps;
            for (int i = 1; i <= map.size(); ++i) {
                try {
                    maps = new HashMap();

                    //区县
                    if (map.get(i).get(1) == null || map.get(i).get(1) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("area_lvl1_name", ((Map) map.get(i)).get(1));
                    }

                    //区县公司
                    if (map.get(i).get(2) == null || map.get(i).get(2) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("area_lvl2_name", ((Map) map.get(i)).get(2));
                    }

                    //客户经理
                    if (map.get(i).get(3) == null || map.get(i).get(3) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("account_name", ((Map) map.get(i)).get(3));
                    }

                    //客户经理工号
                    if (map.get(i).get(4) == null || map.get(i).get(4) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("account_no", ((Map) map.get(i)).get(4));
                    }

                    //集团编码
                    if (map.get(i).get(5) == null || map.get(i).get(5) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("unit_id", ((Map) map.get(i)).get(5));
                    }

                    //集团名称
                    if (map.get(i).get(6) == null || map.get(i).get(6) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("unit_name", ((Map) map.get(i)).get(6));
                    }

                    //集团价值
                    if (map.get(i).get(7) == null || map.get(i).get(7) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("group_value", ((Map) map.get(i)).get(7));
                    }

                    //统存营销比例
                    if (map.get(i).get(8) == null || map.get(i).get(8) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("market_proportion", ((Map) map.get(i)).get(8));
                    }

                    //统付账户
                    if (map.get(i).get(9) == null || map.get(i).get(9) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("unified_payment_account", ((Map) map.get(i)).get(9));
                    }

                    //预存总额(元)
                    if (map.get(i).get(10) == null || map.get(i).get(10) == "") {
                        list2.add(i + 1);
                    } else {
                        double give_amount = Double.parseDouble(map.get(i).get(10).toString());
                        maps.put("deposit_amount", give_amount);
                    }

                    //赠费总额(元)
                    if (map.get(i).get(11) == null || map.get(i).get(11) == "") {
                        list2.add(i + 1);
                    } else {
                        double give_amount = Double.parseDouble(map.get(i).get(11).toString());
                        maps.put("give_amount", give_amount);
                    }

                    //已累计使用额度(元)
                    if (map.get(i).get(12) == null || map.get(i).get(12) == "") {
                        list2.add(i + 1);
                    } else {
                        double use_amount = Double.parseDouble(map.get(i).get(12).toString());
                        maps.put("use_amount", use_amount);
                    }

                    //合约期(始)
                    if (map.get(i).get(13) == null || map.get(i).get(13) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("contract_period_start", ((Map) map.get(i)).get(13));
                    }

                    //合约期(止)
                    if (map.get(i).get(14) == null || map.get(i).get(14) == "") {
                        list2.add(i + 1);
                    } else {
                        maps.put("contract_period_end", ((Map) map.get(i)).get(14));
                    }

                    list.add(maps);
                } catch (Exception e) {
                    list2.add(i + 1);
                    e.printStackTrace();
                }
            }
            if (list2.size() > 0) {
                String s = JSONHelper.SerializeWithNeedAnnotation(list2);
                Write(returnPars(0, s, "导入数据有误!"));
            } else {
                String s = JSONHelper.SerializeWithNeedAnnotation(list);
                Write(returnPars(1, s, "导入成功!"));
            }
        } catch (Exception var11) {
            logger.error("导入错误：" + var11.getMessage(), var11);
            var11.printStackTrace();
            this.Write(returnPars(2, "", "导入失败!"));
        }
    }

    /**
     * 保存存量统存集团客户库
     */
    public void addClientLibrary() {
        try {
            String jsonone = getString("jsonone");
            JSONArray jsonArr = JSONArray.fromObject(jsonone);
            this.marketActivitiesService.deleteClientLibrary();
            if (jsonone != null) {
                for (int i = 0; i < jsonArr.size(); i++) {
                    JSONObject obj = jsonArr.getJSONObject(i);
                    MarketStockSystemGroup mssg = new MarketStockSystemGroup();
                    mssg.setSerial_number("0");
                    mssg.setArea_lvl1_name(obj.get("area_lvl1_name").toString());
                    mssg.setArea_lvl2_name(obj.get("area_lvl2_name").toString());
                    mssg.setAccount_name(obj.get("account_name").toString());
                    mssg.setAccount_no(obj.get("account_no").toString());
                    mssg.setUnit_id(obj.get("unit_id").toString());
                    mssg.setUnit_name(obj.get("unit_name").toString());
                    mssg.setGroup_value(obj.get("group_value").toString());
                    mssg.setMarket_proportion(obj.get("market_proportion").toString());
                    mssg.setUnified_payment_account(obj.get("unified_payment_account").toString());
                    mssg.setDeposit_amount(obj.get("deposit_amount").toString());
                    mssg.setGive_amount(obj.get("give_amount").toString());
                    mssg.setUse_amount(obj.get("use_amount").toString());
                    mssg.setContract_period_start(obj.get("contract_period_start").toString());
                    mssg.setContract_period_end(obj.get("contract_period_end").toString());
                    this.marketActivitiesService.addClientLibrary(mssg);
                }
            }
            this.Write("YES");
        } catch (Exception e) {
            this.Write("NO");
            logger.error("导入错误：" + e.getMessage(), e);
            e.printStackTrace();
        }
    }

    /**
     * 导出模板信息
     */
    public void exportMarketStockSystemGroup() {
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name = "";
            String filepath = null;

            name = "存量统存集团客户库模板";
            filepath = request.getSession().getServletContext().getRealPath("/template/MarketStockSystemGroup.xlsx");

            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
        }
    }

    /**
     * 营销活动部门配额新建申请，拆借工单         code为1：成功   -1；失败
     *
     * @return JSON字符串    returnPars
     * @auther TX
     * @date 2021-5-13
     */
    public void ApplyQuotaIniallzation() {
        try {
            //权限验证  验证是否有权限进行操作   需要营销活动省公司管理员进行操作
            if (!checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062731") && !checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062725")) {
                Write(returnPars(-1, "", "亲爱的同事，当前操作需要营销活动管理员权限，账号权限不足不能进行操作！"));
                return;
            }
            String orderName = getString("orderName");                              //工单标题
            String CompanyCode = getString("CompanyCode");
            String Amount = getString("Amount");                                    //工单申请金额
            String OrderAttribute = getString("OrderAttribute");                    //申请类型
            String AttributionCompanyCode = getString("AttributionCompanyCode");    //上级部门id
            String applyCountAmount = getString("applyCountAmount");                //上级部门金额
            String lendingCompanyName = getString("lendingCompanyName");            //拆借公司ID
            String lendingCountAmount = getString("lendingCountAmount");            //拆借公司金额
            String attachmentId = getString("attachmentId");                        //附件ID
            String userId = getString("userId");
            String role = getString("role");
            String orderMemo = getString("orderMemo");                              //工单描述
            String IBM = "";                                                              //申请编码前面的字母
            List<Object[]> sone = marketActivitiesService.getbumen(user.getRowNo());
            DecimalFormat decimalFormat = new DecimalFormat("0");
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            MarketQuotaIniallzation marketQuotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(CompanyCode);
            MarketQuotasWorkOrder order = new MarketQuotasWorkOrder();
            order.setOrderNo(IBM + getStringDate(new Date(), "yyyyMMddHHmmssSSS"));
            order.setOrderName("[营销活动配额申请工单]" + orderName);
            order.setOrderAttribute(OrderAttribute);
            order.setCompanyCode(marketQuotaIniallzation.getCompanyCode());     //操作部门ID（必须与MarketQuotaIniallzation实体中的部门编号相对应）
            order.setCompanyName(marketQuotaIniallzation.getCompanyName());     //操作部门名称（必须与MarketQuotaIniallzation实体中的部门名称相对应）
            order.setTotalAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));   //工单本次申请总金额
            order.setOriginalAmount(marketQuotaIniallzation.getCountAmount());   //部门原总金额
            order.setCountAmount(decimalFormat.format(Double.parseDouble(marketQuotaIniallzation.getRemainAmount()) + (Double.parseDouble(Amount) * 100)));  //部门总金额，
            order.setComentTotalAmount("0");      //本次划拨金额（划拨金额总和）
            order.setRemainAmount(marketQuotaIniallzation.getRemainAmount());        //剩余总金额
            order.setOrderEnclosureid(attachmentId);
            order.setOrderMome(orderMemo);
            MarketQuotasWorkOrder quotasWorkOrder = marketActivitiesService.addMarketQuotasWorkOrder(order);
            if (!"省公司".equals(marketQuotaIniallzation.getCompanyName())) {       //为省公司申请配额(省公司只能申请新增配额，不能进行拆借操作)
                MarketQuotaIniallzation quotaIniallzation;
                if ("2".equals(OrderAttribute)) {        //配额申请
                    quotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(AttributionCompanyCode);
                } else if ("3".equals(OrderAttribute)) {  //拆借申请
                    quotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(lendingCompanyName);
                } else {         //类型异常
                    Write(returnPars(-1, "", "亲爱的同事，检测到工单：" + orderName + "的申请类型异常，请刷新页面重新提交！"));
                    return;
                }
                MarketQuotasWorkDet det = new MarketQuotasWorkDet();
                det.setOrderNo(quotasWorkOrder.getOrderNo());
                det.setCompanyCode(quotaIniallzation.getCompanyCode());
                det.setCompanyName(quotaIniallzation.getCompanyName());
                det.setOriginalAmount(quotaIniallzation.getRemainAmount());
                det.setComentTotalAmount(decimalFormat.format(Double.parseDouble(Amount) * 100));
                marketActivitiesService.addMarketQuotasWorkDet(det);
            }
            //新建成功后创建关联的主工单对象
            RepairOrder repairOrder = new RepairOrder();
            repairOrder.setOrderLogo(MarketQuotasWorkOrder.MarketQuotasWorkOrder);
            repairOrder.setOrderNo(quotasWorkOrder.getOrderNo());
            repairOrder.setOrderName(quotasWorkOrder.getOrderName());
            repairOrder.setOrderType("1");
            repairOrder.setOrderState("0");
            repairOrder.setPushState("0");
            repairOrder.setCreatorId(String.valueOf(user.getRowNo()));
            repairOrder.setCreatorName(user.getEmployeeName());
            repairOrder.setCreatorDate(new Date());
            repairOrder.setEnableState("0");
            RepairOrder onsefr = repairOrderService.addRepairOrder(repairOrder);
            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] attachmentjson = attachmentId.split(",");
                    if (attachmentjson.length > 0) {
                        for (int i = 0; i < attachmentjson.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(onsefr.getId());
                            sa.setAttachmentId(attachmentjson[i]);
                            sa.setLink(onsefr.getOrderLogo());
                            marketActivitiesService.saveSandA(sa);
                        }
                    }
                }
            }
            if (onsefr != null) {
                String process = "";
                if ("00".equals(CompanyCode.substring(0, 2))) {
                    process = "ActivitieQuotaCompany";
                } else {
                    if ("2".equals(OrderAttribute)) {        //配额申请
                        process = "ActivitieQuotaCountyUpdate";
                    } else if ("3".equals(OrderAttribute)) {  //拆借申请
                        process = "ActivitieQuotaCountyApply";
                    }
                }
                Map<String, String> map = new HashMap<>();
                map.put("node", role);
                String processId = jbpmUtil.startPIByKey(process, map).getId();
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
                bpms_riskoff_service.setBpms_riskoff_process(onsefr.getId(), processId, 1, user);
                Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "营销活动管理员", this.user.getRowNo(), this.user);
                String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), USER.getRowNo(), this.user);
                WaitTask waitTask = new WaitTask();
                waitTask.setName(quotasWorkOrder.getOrderName());
                waitTask.setCreationTime(new Date());
                waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitializationThree.jsp?repair=" + onsefr.getId() + "&task=" + taskid);
                waitTask.setState(WaitTask.HANDLE);
                waitTask.setHandleUserId(USER.getRowNo());
                waitTask.setHandleUserName(USER.getEmployeeName());
                waitTask.setHandleLoginName(USER.getLoginName());
                waitTask.setCreateUserId(user.getRowNo());
                waitTask.setCreateUserName(user.getEmployeeName());
                waitTask.setCreateLoginName(user.getLoginName());
                waitTask.setCode(onsefr.getOrderLogo());
                waitTask.setTaskId(taskid);
                this.waitTaskService.saveWait(waitTask, this.getRequest());
                Write(returnPars(1, "", "工单：" + onsefr.getOrderName() + "添加成功，已推送至：" + USER.getEmployeeName() + "处，请等待审批！"));
//            Write(returnPars(1,"","工单：添加成功，已推送至：处，请等待审批！"));
            } else {
                Write(returnPars(-1, "", "添加工单记录时出现错误，请联系管理员处理！"));
                throw new RuntimeException("事务回滚");
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "新建工单异常，请联系管理员处理！"));
            e.printStackTrace();
            logger.info(e.toString());
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * ----------------------------------------------------------------------------------------------------------------------   配额新建划拨流程代码
     * /**
     * 营销活动部门配额新建划拨工单         code为1：成功   -1；失败
     *
     * @return JSON字符串    returnPars
     * @auther TX
     * @date 2021-5-13
     */
    public void UpdateQuotaIniallzation() {
        try {
            //权限验证  验证是否有权限进行操作   需要营销活动省公司管理员进行操作
            if (!checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062725") && !checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062731")) {
                Write(returnPars(-1, "", "亲爱的同事，划拨配额需要营销活动管理员权限，当前权限不足不能进行操作！"));
                return;
            }
            String orderName = getString("orderName");                            //工单标题
            String CompanyCode = getString("COMPANY_CODE");                      //上级公司编号
            String TotalAmount = getString("TotalAmount");                        //划拨总金额总金额

            String orderMemo = getString("orderMemo");                            //工单描述
            String RemainAmountTwo = getString("RemainAmountTwo");                //剩余总金额
            String attachmentId = getString("attachmentId");                        //附件ID
            String userId = getString("userId");
            String role = getString("role");
            String json = getString("orderJson");                                 //配额明细信息字符串
            String IBM = "";                                                              //申请编码前面的字母
            List<Object[]> sone = marketActivitiesService.getbumen(user.getRowNo());
            DecimalFormat decimalFormat = new DecimalFormat("0");
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            //创建主工单  用于记录初始化信息
            MarketQuotaIniallzation marketQuotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(CompanyCode);
            if (Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                    < Double.parseDouble(decimalFormat.format(Double.parseDouble(TotalAmount) * 100))) {
                Write(returnPars(-1, "", "亲爱的同事，当前公司剩余额度已不足以进行本次划拨，请重新申请配额或调整划拨金额后进行操作！"));
                return;
            }
            MarketQuotasWorkOrder order = new MarketQuotasWorkOrder();
            order.setOrderNo(IBM + getStringDate(new Date(), "yyyyMMddHHmmssSSS"));
            order.setOrderName("[营销活动配额划拨工单]" + orderName);
            order.setOrderAttribute("1");
            order.setCompanyCode(marketQuotaIniallzation.getCompanyCode());     //操作部门ID（必须与MarketQuotaIniallzation实体中的部门编号相对应）
            order.setCompanyName(marketQuotaIniallzation.getCompanyName());                                                         //操作部门名称（必须与MarketQuotaIniallzation实体中的部门名称相对应）
            order.setTotalAmount("0");   //工单本次申请总金额    页面填报为万元  转换为元
            order.setOriginalAmount(marketQuotaIniallzation.getRemainAmount());   //部门原金额（已申请并且未使用的金额，如果是初始化为0）
            order.setCountAmount(marketQuotaIniallzation.getRemainAmount());  //部门总金额，初始化时总金额为本次申请金额  页面填报为万元  转换为元
            order.setComentTotalAmount(decimalFormat.format(Double.parseDouble(TotalAmount) * 100));      //本次划拨金额（划拨金额总和）    页面填报为万元  转换为元
            order.setRemainAmount(decimalFormat.format(Double.parseDouble(RemainAmountTwo) * 100));        //剩余总金额（部门总金额-本次划拨金额） 页面填报为万元  转换为元
            order.setOrderMome(orderMemo);
            order.setOrderEnclosureid(attachmentId);
            MarketQuotasWorkOrder quotasWorkOrder = marketActivitiesService.addMarketQuotasWorkOrder(order);
            if (quotasWorkOrder != null) {
//                marketQuotaIniallzation.setPreemptedAmount(
//                        decimalFormat.format(Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
//                                + Double.parseDouble(quotasWorkOrder.getComentTotalAmount()))
//                );
//                marketQuotaIniallzation.setRemainAmount(
//                        decimalFormat.format(
//                                Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
//                                        - Double.parseDouble(quotasWorkOrder.getComentTotalAmount())
//                        )
//                );
//                marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaIniallzation);
                if (json != null) {           //初始化部门配额信息并创建明细记录信息
                    JSONArray jsonArray = JSONArray.fromObject(json);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject det = JSONObject.fromObject(jsonArray.get(i));
                        MarketQuotasWorkDet marketQuotasWorkDet = new MarketQuotasWorkDet();
                        marketQuotasWorkDet.setOrderNo(quotasWorkOrder.getOrderNo());
                        marketQuotasWorkDet.setCompanyCode(det.getString("CompanyCode"));
                        marketQuotasWorkDet.setCompanyName(det.getString("CompanyName"));
                        marketQuotasWorkDet.setOriginalAmount(decimalFormat.format(Double.parseDouble(det.getString("OriginalAmount")) * 100));
                        marketQuotasWorkDet.setComentTotalAmount(decimalFormat.format(Double.parseDouble(det.getString("ComentTotalAmount")) * 100));
                        if (marketActivitiesService.addMarketQuotasWorkDet(marketQuotasWorkDet) == null) {
                            Write(returnPars(-1, "", "地市配额工单创建失败，请联系管理员检查！"));
                            throw new RuntimeException("事务回滚");
                        }
                    }
                } else {
                    Write(returnPars(-1, "", "未检测到部门配额信息，请确认！"));
                    throw new RuntimeException("事务回滚");
                }
                //新建成功后创建关联的主工单对象
                RepairOrder repairOrder = new RepairOrder();
                repairOrder.setOrderLogo(MarketQuotasWorkOrder.MarketQuotasWorkOrder);
                repairOrder.setOrderNo(quotasWorkOrder.getOrderNo());
                repairOrder.setOrderName(quotasWorkOrder.getOrderName());
                repairOrder.setOrderType("1");
                repairOrder.setOrderState("0");
                repairOrder.setPushState("0");
                repairOrder.setCreatorId(String.valueOf(user.getRowNo()));
                repairOrder.setCreatorName(user.getEmployeeName());
                repairOrder.setCreatorDate(new Date());
                repairOrder.setEnableState("0");
                RepairOrder onsefr = repairOrderService.addRepairOrder(repairOrder);
                if (!StringUtils.isEmpty(attachmentId)) {
                    if (attachmentId != null) {
                        // 判断是否上传了附件，获取前台提交的附件Id；
                        String[] attachmentjson = attachmentId.split(",");
                        if (attachmentjson.length > 0) {
                            for (int i = 0; i < attachmentjson.length; i++) {
                                SingleAndAttachment sa = new SingleAndAttachment();
                                sa.setOrderID(onsefr.getId());
                                sa.setAttachmentId(attachmentjson[i]);
                                sa.setLink(onsefr.getOrderLogo());
                                marketActivitiesService.saveSandA(sa);
                            }
                        }
                    }
                }
                if (onsefr != null) {
                    String process = "";
                    if ("00".equals(CompanyCode.substring(0, 2))) {
                        process = "ActivitieQuotaCompany";
                    } else {
                        process = "ActivitieQuotaCounty";
                    }
                    Map<String, String> map = new HashMap<>();
                    map.put("node", role);
                    String processId = jbpmUtil.startPIByKey(process, map).getId();
                    SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
                    bpms_riskoff_service.setBpms_riskoff_process(onsefr.getId(), processId, 1, user);
                    Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                    this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "营销活动管理员", this.user.getRowNo(), this.user);
                    String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), USER.getRowNo(), this.user);
                    WaitTask waitTask = new WaitTask();
                    waitTask.setName(order.getOrderName());
                    waitTask.setCreationTime(new Date());
                    waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitializationTwo.jsp?repair=" + onsefr.getId() + "&task=" + taskid);
                    waitTask.setState(WaitTask.HANDLE);
                    waitTask.setHandleUserId(USER.getRowNo());
                    waitTask.setHandleUserName(USER.getEmployeeName());
                    waitTask.setHandleLoginName(USER.getLoginName());
                    waitTask.setCreateUserId(user.getRowNo());
                    waitTask.setCreateUserName(user.getEmployeeName());
                    waitTask.setCreateLoginName(user.getLoginName());
                    waitTask.setCode(onsefr.getOrderLogo());
                    waitTask.setTaskId(taskid);
                    this.waitTaskService.saveWait(waitTask, this.getRequest());
                    Write(returnPars(1, "", "工单：" + onsefr.getOrderName() + "添加成功，已推送至：" + USER.getEmployeeName() + "处，请等待审批！"));
                } else {
                    Write(returnPars(-1, "", "添加工单记录时出现错误，请联系管理员处理！"));
                    throw new RuntimeException("事务回滚");
                }
            } else {
                Write(returnPars(-1, "", "数据初始化失败，请联系管理员处理！"));
                throw new RuntimeException("事务回滚");
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "新建工单异常，请联系管理员处理！"));
            e.printStackTrace();
            logger.info(e.toString());
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 营销活动： 省公司配额增加申请同时统一配置市州分公司额度（或省公司总额增加申请）流程进行
     * 营销活动部门配额新建划拨工单
     *
     * @return JSON字符串  returnPars  code为1：成功   -1；失败
     * @auther TX
     * @date 2021-5-13
     */
    public void HandUpdateQuotaIniallzation() {
        try {
            String repair = getString("repair");
            String juese = getString("juese");  // 下一步可执行流程线条值
            String userid = getString("userId");// 用户id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String taskId = getString("TaskId");
            RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(repairOrder.getId());
            Bpms_riskoff_task webtask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (webtask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            jbpmUtil.completeTask(task.getId(), juese);
            Task newtask = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            String rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", newtask.getActivityName(), USER.getRowNo(), this.user);
            WaitTask wait = waitTaskService.queryWaitByTaskId(waitId);
            if (wait != null) {
                waitTaskService.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID（WaitTask）为空了==========：" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName(repairOrder.getOrderName());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitializationThree.jsp?repair=" + repairOrder.getId() + "&task=" + rtaskid);
            //waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitializationTwo.jsp?repair=" + repairOrder.getId() + "&task=" + rtaskid);
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(repairOrder.getOrderLogo());
            waitTask.setTaskId(rtaskid);
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "审批提交成功，已提交至：" + USER.getEmployeeName() + " 进行审批！"));
        } catch (Exception e) {
            Write(returnPars(-1, "", "审批提交失败！"));
            e.printStackTrace();
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }

    /**
     * 营销活动： 配额增加申请同时统一配置分公司额度（或省公司总额增加申请）流程结束
     * 营销活动部门配额新建划拨工单
     *
     * @return JSON字符串  returnPars  code为1：成功   -1；失败
     * @auther TX
     * @date 2021-5-13
     */
    public void ComplateUpdateQuotaIniallzation() {
        try {
            String repair = getString("repair");//工单id
            String waitId = getString("waitId");//待办id
            String opinion = getString("opinion");//审批意见
            String taskId = getString("TaskId");//任务表id
            DecimalFormat decimalFormat = new DecimalFormat("0");
            RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(repair);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            if (task == null) {
                Write(returnPars(-1, "", "流程实例异常,请联系系统管理员！"));
                return;
            }
            MarketQuotasWorkOrder marketQuotasWorkOrder = marketActivitiesService.QueryMarketQuotasWorkOrderByOrderNo(repairOrder.getOrderNo());
            if (marketQuotasWorkOrder == null) {
                Write(returnPars(-1, "", "工单数据异常，未查询到工单信息！"));
                return;
            }
            //System.out.println("工单数据==>" + JSONHelper.Serialize(marketQuotasWorkOrder));
            //System.out.println("工单数据的钱==>"+JSONHelper.Serialize(marketQuotasWorkOrder.getComentTotalAmount()));
            //List<MarketQuotasWorkDet> list = marketActivitiesService.QueryMarketQuotasWorkDetListByOrderNo(repairOrder.getOrderNo());
            List<MarketQuotasWorkDet> marketQuotasWorkDetList = marketActivitiesService.QueryMarketQuotasWorkDetListByOrderNo(marketQuotasWorkOrder.getOrderNo());
//            if (marketQuotasWorkDetList.size() < 1) {
//                Write(returnPars(-1, "", "工单数据异常，根据工单未查询到明细信息！"));
//                return;
//            }
            //System.out.println("工单明细信息==>" + JSONHelper.Serialize(marketQuotasWorkDetList));
            MarketQuotaIniallzation marketQuotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkOrder.getCompanyCode());
            //System.out.println("配额信息==>" + JSONHelper.Serialize(marketQuotaIniallzation));
            //System.out.println("配额信息的钱==>"+JSONHelper.Serialize(marketQuotaIniallzation.getPreemptedAmount()));
            if (Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                    < Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())) {
                Write(returnPars(-1, "", "预占数据异常，请联系管理员排查！"));
                throw new RuntimeException(" 给事务回滚，自定义1");
            }
            //System.out.println("类别=====" + marketQuotasWorkOrder.getOrderAttribute());
            if (marketQuotasWorkOrder.getOrderAttribute().equals("2")) {//申请·（向上一级公司申请额度）无明细信息
                //判断是否为省公司
                //System.out.println("查询的数据=="+JSONObject.fromObject(marketQuotasWorkOrder));
                if (marketQuotasWorkOrder.getCompanyName().equals("省公司")) {
                    MarketQuotaIniallzation companyCode = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkOrder.getCompanyCode());
                    companyCode.setRemainAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyCode.getRemainAmount())
                                            + Double.parseDouble(marketQuotasWorkOrder.getTotalAmount())//本次申请的金额
                            )
                    );//剩余额度
                    companyCode.setCountAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyCode.getCountAmount())
                                            + Double.parseDouble(marketQuotasWorkOrder.getTotalAmount())//本次申请的金额
                            )
                    );//总额度
                    marketActivitiesService.UpdateMarketQuotaIniallzation(companyCode);
                } else {
                    for (int i = 0; i < marketQuotasWorkDetList.size(); i++) {
                        //
                        //MarketQuotaIniallzation quotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkDetList.get(i).getCompanyCode());
                        //System.out.println("根据操作明细查询的数据==>"+JSONHelper.Serialize(quotaIniallzation));
                        if (Double.parseDouble(marketQuotasWorkDetList.get(i).getOriginalAmount())
                                < Double.parseDouble(marketQuotasWorkDetList.get(i).getComentTotalAmount())) {
                            Write(returnPars(-1, "", "数据异常，请联系管理员排查！"));
                            throw new RuntimeException(" 给事务回滚，自定义1");
                        } else {
                            String attributionCompanyName = marketQuotaIniallzation.getAttributionCompanyName();//获取上级公司名称
                            //System.out.println(attributionCompanyName);
                            MarketQuotaIniallzation marketQuotaOne = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(attributionCompanyName);
                            marketQuotaOne.setRemainAmount(
                                    decimalFormat.format(
                                            Double.parseDouble(marketQuotaOne.getRemainAmount())
                                                    - Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                    )//剩余的额度 单位为分
                            );
                            marketQuotaOne.setComentTotalAmount(
                                    decimalFormat.format(
                                            Double.parseDouble(marketQuotaOne.getComentTotalAmount())
                                                    + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                    )//已分配的额度 单位为分
                            );

                            marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaOne);

                            String companyName = marketQuotaIniallzation.getCompanyName();//获取本公司名称
                            System.out.println(companyName);
                            MarketQuotaIniallzation marketQuotaTwo = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(companyName);
                            marketQuotaTwo.setRemainAmount(
                                    decimalFormat.format(
                                            Double.parseDouble(marketQuotaTwo.getRemainAmount())
                                                    + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                    )
                            ); //剩余的额度 单位为分
                            marketQuotaTwo.setCountAmount(
                                    decimalFormat.format(
                                            Double.parseDouble(marketQuotaTwo.getCountAmount())
                                                    + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                    )//总额度（分公司为已申请通过和已划拨成功的总额度   ，总公司为总额）   单位为分
                            );
                            marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaTwo);
                        }
                    }
                }
            } else if (marketQuotasWorkOrder.getOrderAttribute().equals("3")) {
                //拆借 （向同级公司拆解额度）明细表为拆借的同级公司信息
                for (int i = 0; i < marketQuotasWorkDetList.size(); i++) {
                    //
                    //MarketQuotaIniallzation quotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkDetList.get(i).getCompanyCode());
                    //System.out.println("根据操作明细查询的数据==>"+JSONHelper.Serialize(quotaIniallzation));
                    if (Double.parseDouble(marketQuotasWorkDetList.get(i).getOriginalAmount())
                            < Double.parseDouble(marketQuotasWorkDetList.get(i).getComentTotalAmount())) {
                        Write(returnPars(-1, "", "数据异常，请联系管理员排查！"));
                        throw new RuntimeException(" 给事务回滚，自定义1");
                    } else {
                        //String attributionCompanyName = marketQuotaIniallzation.getAttributionCompanyName();//获取上级公司名称
                        //System.out.println(attributionCompanyName);
                        MarketQuotaIniallzation marketQuotaOne = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(marketQuotasWorkDetList.get(i).getCompanyName());
                        marketQuotaOne.setRemainAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaOne.getRemainAmount())
                                                - Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )//剩余的额度 单位为分
                        );
                        marketQuotaOne.setComentTotalAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaOne.getComentTotalAmount())
                                                + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )//已分配的额度 单位为分
                        );
                        marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaOne);

                        String companyName = marketQuotaIniallzation.getCompanyName();//获取本公司名称
                        System.out.println(companyName);
                        MarketQuotaIniallzation marketQuotaTwo = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(companyName);
                        marketQuotaTwo.setRemainAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaTwo.getRemainAmount())
                                                + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )
                        ); //剩余的额度 单位为分
                        marketQuotaTwo.setCountAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaTwo.getCountAmount())
                                                + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )//总额度（分公司为已申请通过和已划拨成功的总额度   ，总公司为总额）   单位为分
                        );
                        marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaTwo);
                    }
                }
            } else if (marketQuotasWorkOrder.getOrderAttribute().equals("1")) {
                //分配 （向下一级公司公司分配额度）明细表为下一级公司信息
                for (int i = 0; i < marketQuotasWorkDetList.size(); i++) {
                    //
                    //MarketQuotaIniallzation quotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkDetList.get(i).getCompanyCode());
                    //System.out.println("根据操作明细查询的数据==>"+JSONHelper.Serialize(quotaIniallzation));
                    if (Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                            < Double.parseDouble(marketQuotasWorkDetList.get(i).getOriginalAmount())) {
                        Write(returnPars(-1, "", "数据异常，请联系管理员排查！"));
                        throw new RuntimeException(" 给事务回滚，自定义1");
                    } else {
                        //String attributionCompanyName = marketQuotaIniallzation.getAttributionCompanyName();//获取上级公司名称
                        //System.out.println(attributionCompanyName);
                        //MarketQuotaIniallzation marketQuotaOne = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(marketQuotasWorkDetList.get(i).getCompanyName());

                        marketQuotaIniallzation.setRemainAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                                                - Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )//剩余的额度 单位为分
                        );
                        marketQuotaIniallzation.setComentTotalAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaIniallzation.getComentTotalAmount())
                                                + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )//已分配的额度 单位为分
                        );


                        marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaIniallzation);

                        String companyName = marketQuotasWorkDetList.get(i).getCompanyName();//公司名称
                        System.out.println(companyName);
                        MarketQuotaIniallzation marketQuotaTwo = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(companyName);
                        marketQuotaTwo.setRemainAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaTwo.getRemainAmount())
                                                + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )
                        ); //剩余的额度 单位为分
                        marketQuotaTwo.setCountAmount(
                                decimalFormat.format(
                                        Double.parseDouble(marketQuotaTwo.getCountAmount())
                                                + Double.parseDouble(marketQuotasWorkDetList.get(0).getComentTotalAmount())
                                )//总额度（分公司为已申请通过和已划拨成功的总额度   ，总公司为总额）   单位为分
                        );
                        marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaTwo);
                    }
                }

            }
            repairOrder.setOrderState("1");
            repairOrderService.updateRepairOrder(repairOrder);
            Bpms_riskoff_task webtask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (webtask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            jbpmUtil.completeTask(task.getId(), "END");
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(repairOrder.getCreatorId()));
            String rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(process.getProcess_sign(), "审批通过!", 1, "CS", "营销活动管理员", USER.getRowNo(), this.user);
            WaitTask wait = waitTaskService.queryWaitByTaskId(waitId);
            if (wait != null) {
                waitTaskService.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID（WaitTask）为空了==========：" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName(repairOrder.getOrderName());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitializationThree.jsp?repair=" + repairOrder.getId() + "&task=" + rtaskid);
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(repairOrder.getOrderLogo());
            waitTask.setTaskId(rtaskid);
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "操作成功，审批已通过！"));
        } catch (Exception e) {
            logger.error("数据错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", e.getMessage()));
            e.printStackTrace();
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }

    /**
     * 流程退回
     */
    public void ReturnUpdateQuotaIniallzation() {
        try {
            String repair = getString("repair");// 工单id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("TaskId");// 任务表id
            DecimalFormat decimalFormat = new DecimalFormat("0");
            RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
            MarketQuotasWorkOrder marketQuotasWorkOrder = marketActivitiesService.QueryMarketQuotasWorkOrderByOrderNo(repairOrder.getOrderNo());
            MarketQuotaIniallzation marketQuotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkOrder.getCompanyCode());
            if (Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
                    < Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())) {
                Write(returnPars(-1, "", "预占数据异常，请联系管理员排查！"));
                throw new RuntimeException(" 给事务回滚，自定义1");
            }
            marketQuotaIniallzation.setPreemptedAmount(
                    decimalFormat.format(
                            Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
                                    - Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())
                    )
            );
            marketQuotaIniallzation.setRemainAmount(
                    decimalFormat.format(
                            Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                                    + Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())
                    )
            );
            marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaIniallzation);
            repairOrder.setOrderState("2");
            repairOrderService.updateRepairOrder(repairOrder);
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(repairOrder.getCreatorId()));
            bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                waitTaskService.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName(repairOrder.getOrderName());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitialization.jsp?repair=" + repairOrder.getId());
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(repairOrder.getOrderLogo());
            waitTask.setTaskId("");
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "工单退回成功！工单已退回至: " + USER.getEmployeeName()));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "工单退回失败！"));
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }

    /**
     * ----------------------------------------------------------------------------------------------------------------------   配额初始化流程代码
     * /**
     * 营销活动部门额度申请（省公司初始化）           code为1：成功   -1；失败
     *
     * @return JSON字符串    returnPars
     * @auther TX
     * @date 2021-5-13
     */
    public void addQuotaIniallzation() {
        try {
            //权限验证  验证是否有权限进行操作   需要营销活动省公司管理员进行操作 16062731 省公司 16062725 市公司
            if (!checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062731")) {
                Write(returnPars(-1, "", "亲爱的同事，初始化数据需要营销活动省公司管理员权限，当前权限不足不能进行操作！"));
                return;
            }
            //验证是否已经存在数据
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
            //System.out.println("数据==>"+marketActivitiesService.GetMarketQuotaInialVersionNumber());
            //System.out.println("数据==>"+marketActivitiesService.GetMarketQuotaInialVersionNumber()!=null);
            //System.out.println("数据==>"+marketActivitiesService.GetMarketQuotaInialVersionNumber()!="null");
            //System.out.println("数据==>"+!marketActivitiesService.GetMarketQuotaInialVersionNumber().equals("null"));
            if (!marketActivitiesService.GetMarketQuotaInialVersionNumber().equals("null") && marketActivitiesService.GetMarketQuotaInialVersionNumber() != null) {
                //System.out.println("运行="+marketActivitiesService.GetMarketQuotaInialVersionNumber());
                //System.out.println("运行1="+marketActivitiesService.GetMarketQuotaInialVersionNumber()!="null");
                //System.out.println("运行2="+marketActivitiesService.GetMarketQuotaInialVersionNumber()!=null);
                if (Double.parseDouble(simpleDateFormat.format(new Date())) <= Double.parseDouble(marketActivitiesService.GetMarketQuotaInialVersionNumber())) {
                    Write(returnPars(-1, "", "亲爱的同事，今年数据已初始化完成，不能进行操作！"));
                    return;
                }
            }

            String orderName = getString("orderName");      //工单标题
            String TotalAmount = getString("TotalAmount");  //工单申请总金额（本次申请金额）

            String CountAmount = getString("CountAmount");      //部门总金额，初始化时总金额为本次申请金额
            String ComentTotalAmount = getString("ComentTotalAmount");      //本次划拨金额（划拨金额总和）
            String RemainAmount = getString("RemainAmount");                //剩余总金额（部门总金额-本次划拨金额） 页面计算金额，需要与后台计算金额进行比对
            String json = getString("orderJson");               //下属部门配额明细字符串
            String userId = getString("userId");                  //审批人ID
            String role = getString("role");                      //流程发起角色编号
            String IBM = "";                                            //申请编码前面的字母
            List<Object[]> sone = marketActivitiesService.getbumen(user.getRowNo());
            DecimalFormat decimalFormat = new DecimalFormat("0");
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            //创建主工单  用于记录初始化信息
            MarketQuotasWorkOrder order = new MarketQuotasWorkOrder();
            order.setOrderNo(IBM + getStringDate(new Date(), "yyyyMMddHHmmssSSS"));
            order.setOrderName("[营销活动省公司初始化工单]" + orderName);
            order.setOrderAttribute("4");
            order.setCompanyCode("00@" + getStringDate(new Date(), "yyyyMMddHHmmssSSS"));     //操作部门ID（必须与MarketQuotaIniallzation实体中的部门编号相对应）
            order.setCompanyName("省公司");                                                         //操作部门名称（必须与MarketQuotaIniallzation实体中的部门名称相对应）
            order.setTotalAmount(decimalFormat.format(Double.parseDouble(TotalAmount) * 1000000));   //工单本次申请总金额    页面填报为万元  转换为元
            order.setOriginalAmount("0");   //部门原金额（已申请并且未使用的金额，如果是初始化为0）
            order.setCountAmount(decimalFormat.format(Double.parseDouble(CountAmount) * 1000000));  //部门总金额，初始化时总金额为本次申请金额  页面填报为万元  转换为元
            order.setComentTotalAmount(decimalFormat.format(Double.parseDouble(ComentTotalAmount) * 1000000));      //本次划拨金额（划拨金额总和）    页面填报为万元  转换为元
            order.setRemainAmount(decimalFormat.format(Double.parseDouble(RemainAmount) * 1000000));        //剩余总金额（部门总金额-本次划拨金额） 页面填报为万元  转换为元
            MarketQuotasWorkOrder quotasWorkOrder = marketActivitiesService.addMarketQuotasWorkOrder(order);
            if (quotasWorkOrder != null) {
                if (json != null) {           //初始化部门配额信息并创建明细记录信息
                    JSONArray jsonArray = JSONArray.fromObject(json);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject det = JSONObject.fromObject(jsonArray.get(i));
                        MarketQuotasWorkDet marketQuotasWorkDet = new MarketQuotasWorkDet();
                        marketQuotasWorkDet.setOrderNo(quotasWorkOrder.getOrderNo());
                        marketQuotasWorkDet.setCompanyCode(det.getString("CompanyCode") + "@" + getStringDate(new Date(), "yyyyMMddHHmmssSSS"));
                        marketQuotasWorkDet.setCompanyName(det.getString("CompanyName"));
                        marketQuotasWorkDet.setOriginalAmount("0");
                        marketQuotasWorkDet.setComentTotalAmount(decimalFormat.format(Double.parseDouble(det.getString("CountAmount")) * 1000000));
                        marketQuotasWorkDet.setIntoTable("Y");
                        if (marketActivitiesService.addMarketQuotasWorkDet(marketQuotasWorkDet) == null) {
                            Write(returnPars(-1, "", "地市配额工单创建失败，请联系管理员检查！"));
                            throw new RuntimeException("事务回滚");
                        }
                    }
                } else {
                    Write(returnPars(-1, "", "未检测到部门配额信息，请确认！"));
                    throw new RuntimeException("事务回滚");
                }
                //新建成功后创建关联的主工单对象
                RepairOrder repairOrder = new RepairOrder();
                repairOrder.setOrderLogo(MarketQuotasWorkOrder.MarketQuotasWorkOrder);
                repairOrder.setOrderNo(quotasWorkOrder.getOrderNo());
                repairOrder.setOrderName(quotasWorkOrder.getOrderName());
                repairOrder.setOrderType("1");
                repairOrder.setOrderState("0");
                repairOrder.setPushState("0");
                repairOrder.setCreatorId(String.valueOf(user.getRowNo()));
                repairOrder.setCreatorName(user.getEmployeeName());
                repairOrder.setCreatorDate(new Date());
                repairOrder.setEnableState("0");
                RepairOrder onsefr = repairOrderService.addRepairOrder(repairOrder);
                if (onsefr != null) {
                    Map<String, String> map = new HashMap<>();
                    map.put("node", role);
                    String processId = jbpmUtil.startPIByKey("ActivitieQuotaCompany", map).getId();
                    SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
                    bpms_riskoff_service.setBpms_riskoff_process(onsefr.getId(), processId, 1, user);
                    Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                    this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "营销活动管理员", this.user.getRowNo(), this.user);
                    String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), USER.getRowNo(), this.user);
                    WaitTask waitTask = new WaitTask();
                    waitTask.setName(order.getOrderName());
                    waitTask.setCreationTime(new Date());
                    waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitialization.jsp?repair=" + onsefr.getId() + "&task=" + taskid);
                    waitTask.setState(WaitTask.HANDLE);
                    waitTask.setHandleUserId(USER.getRowNo());
                    waitTask.setHandleUserName(USER.getEmployeeName());
                    waitTask.setHandleLoginName(USER.getLoginName());
                    waitTask.setCreateUserId(user.getRowNo());
                    waitTask.setCreateUserName(user.getEmployeeName());
                    waitTask.setCreateLoginName(user.getLoginName());
                    waitTask.setCode(onsefr.getOrderLogo());
                    waitTask.setTaskId(taskid);
                    this.waitTaskService.saveWait(waitTask, this.getRequest());
                    Write(returnPars(1, "", "工单：" + onsefr.getOrderName() + "添加成功，已推送至：" + USER.getEmployeeName() + "处，请等待审批！"));
                } else {
                    Write(returnPars(-1, "", "添加工单记录时出现错误，请联系管理员处理！"));
                    throw new RuntimeException("事务回滚");
                }
            } else {
                Write(returnPars(-1, "", "数据初始化失败，请联系管理员处理！"));
                throw new RuntimeException("事务回滚");
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "新建工单异常，请联系管理员处理！"));
            e.printStackTrace();
            logger.info(e.toString());
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 营销活动部门额度申请（地市公司初始化）           code为1：成功   -1；失败
     *
     * @return JSON字符串    returnPars
     * @auther TX
     * @date 2021-5-13
     */
    public void addCountQuotaIniallzation() {
        try {
            //权限验证  验证是否有权限进行操作   需要营销活动省公司管理员进行操作
            if (!checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062725")) {
                Write(returnPars(-1, "", "亲爱的同事，初始化数据需要营销活动市公司管理员权限，当前权限不足不能进行操作！"));
                return;
            }

            String orderName = getString("orderName");      //工单标题
            String COMPANY_CODE = getString("COMPANY_CODE");  //工单申请总金额（本次申请金额）

            String CountAmount = getString("CountAmount");      //部门总金额，初始化时总金额为本次申请金额
            String ComentTotalAmount = getString("ComentTotalAmount");      //本次划拨金额（划拨金额总和）
            String RemainAmount = getString("RemainAmount");                //剩余总金额（部门总金额-本次划拨金额） 页面计算金额，需要与后台计算金额进行比对
            String json = getString("orderJson");               //下属部门配额明细字符串
            String userId = getString("userId");                  //审批人ID
            String role = getString("role");                      //流程发起角色编号
            String IBM = "";                                            //申请编码前面的字母
            List<Object[]> sone = marketActivitiesService.getbumen(user.getRowNo());
            DecimalFormat decimalFormat = new DecimalFormat("0");
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            //创建主工单  用于记录初始化信息
            MarketQuotaIniallzation marketQuotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(COMPANY_CODE);
            if (Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                    < Double.parseDouble(decimalFormat.format(Double.parseDouble(ComentTotalAmount) * 1000000))) {
                Write(returnPars(-1, "", "亲爱的同事，当前公司剩余额度已不足以进行本次划拨，请重新申请配额或调整划拨金额后进行操作！"));
                return;
            }
            MarketQuotasWorkOrder order = new MarketQuotasWorkOrder();
            order.setOrderNo(IBM + getStringDate(new Date(), "yyyyMMddHHmmssSSS"));
            order.setOrderName("[营销活动地市分公司初始化工单]" + orderName);
            order.setOrderAttribute("4");
            order.setCompanyCode(marketQuotaIniallzation.getCompanyCode());     //操作部门ID（必须与MarketQuotaIniallzation实体中的部门编号相对应）
            order.setCompanyName(marketQuotaIniallzation.getCompanyName());                                                         //操作部门名称（必须与MarketQuotaIniallzation实体中的部门名称相对应）
            order.setTotalAmount("0");   //工单本次申请总金额    页面填报为万元  转换为元
            order.setOriginalAmount(decimalFormat.format(Double.parseDouble(CountAmount) * 1000000));   //部门原金额（已申请并且未使用的金额，如果是初始化为0）
            order.setCountAmount(decimalFormat.format(Double.parseDouble(CountAmount) * 1000000));  //部门总金额，初始化时总金额为本次申请金额  页面填报为万元  转换为元
            order.setComentTotalAmount(decimalFormat.format(Double.parseDouble(ComentTotalAmount) * 1000000));      //本次划拨金额（划拨金额总和）    页面填报为万元  转换为元
            order.setRemainAmount(decimalFormat.format(Double.parseDouble(RemainAmount) * 1000000));        //剩余总金额（部门总金额-本次划拨金额） 页面填报为万元  转换为元
            MarketQuotasWorkOrder quotasWorkOrder = marketActivitiesService.addMarketQuotasWorkOrder(order);
            if (quotasWorkOrder != null) {
                marketQuotaIniallzation.setPreemptedAmount(
                        decimalFormat.format(
                                Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
                                        + Double.parseDouble(quotasWorkOrder.getComentTotalAmount())
                        )
                );
                marketQuotaIniallzation.setRemainAmount(
                        decimalFormat.format(
                                Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                                        - Double.parseDouble(quotasWorkOrder.getComentTotalAmount())
                        )
                );
                marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaIniallzation);
                if (json != null) {           //初始化部门配额信息并创建明细记录信息
                    JSONArray jsonArray = JSONArray.fromObject(json);
                    for (int i = 0; i < jsonArray.size(); i++) {
                        JSONObject det = JSONObject.fromObject(jsonArray.get(i));
                        MarketQuotasWorkDet marketQuotasWorkDet = new MarketQuotasWorkDet();
                        marketQuotasWorkDet.setOrderNo(quotasWorkOrder.getOrderNo());
                        marketQuotasWorkDet.setCompanyCode(det.getString("CompanyCode") + "@" + getStringDate(new Date(), "yyyyMMddHHmmssSSS"));
                        marketQuotasWorkDet.setCompanyName(det.getString("CompanyName"));
                        marketQuotasWorkDet.setOriginalAmount("0");
                        marketQuotasWorkDet.setComentTotalAmount(decimalFormat.format(Double.parseDouble(det.getString("CountAmount")) * 1000000));
                        marketQuotasWorkDet.setIntoTable("Y");
                        if (marketActivitiesService.addMarketQuotasWorkDet(marketQuotasWorkDet) == null) {
                            Write(returnPars(-1, "", "地市配额工单创建失败，请联系管理员检查！"));
                            throw new RuntimeException("事务回滚");
                        }
                    }
                } else {
                    Write(returnPars(-1, "", "未检测到部门配额信息，请确认！"));
                    throw new RuntimeException("事务回滚");
                }
                //新建成功后创建关联的主工单对象
                RepairOrder repairOrder = new RepairOrder();
                repairOrder.setOrderLogo(MarketQuotasWorkOrder.MarketQuotasWorkOrder);
                repairOrder.setOrderNo(quotasWorkOrder.getOrderNo());
                repairOrder.setOrderName(quotasWorkOrder.getOrderName());
                repairOrder.setOrderType("1");
                repairOrder.setOrderState("0");
                repairOrder.setPushState("0");
                repairOrder.setCreatorId(String.valueOf(user.getRowNo()));
                repairOrder.setCreatorName(user.getEmployeeName());
                repairOrder.setCreatorDate(new Date());
                repairOrder.setEnableState("0");
                RepairOrder onsefr = repairOrderService.addRepairOrder(repairOrder);
                if (onsefr != null) {
                    Map<String, String> map = new HashMap<>();
                    map.put("node", role);
                    String processId = jbpmUtil.startPIByKey("ActivitieQuotaCounty", map).getId();
                    SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userId));
                    bpms_riskoff_service.setBpms_riskoff_process(onsefr.getId(), processId, 1, user);
                    Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                    this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "营销活动管理员", this.user.getRowNo(), this.user);
                    String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), USER.getRowNo(), this.user);
                    WaitTask waitTask = new WaitTask();
                    waitTask.setName(order.getOrderName());
                    waitTask.setCreationTime(new Date());
                    waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitialization.jsp?repair=" + onsefr.getId() + "&task=" + taskid);
                    waitTask.setState(WaitTask.HANDLE);
                    waitTask.setHandleUserId(USER.getRowNo());
                    waitTask.setHandleUserName(USER.getEmployeeName());
                    waitTask.setHandleLoginName(USER.getLoginName());
                    waitTask.setCreateUserId(user.getRowNo());
                    waitTask.setCreateUserName(user.getEmployeeName());
                    waitTask.setCreateLoginName(user.getLoginName());
                    waitTask.setCode(onsefr.getOrderLogo());
                    waitTask.setTaskId(taskid);
                    this.waitTaskService.saveWait(waitTask, this.getRequest());
                    Write(returnPars(1, "", "工单：" + onsefr.getOrderName() + "添加成功，已推送至：" + USER.getEmployeeName() + "处，请等待审批！"));
                } else {
                    Write(returnPars(-1, "", "添加工单记录时出现错误，请联系管理员处理！"));
                    throw new RuntimeException("事务回滚");
                }
            } else {
                Write(returnPars(-1, "", "数据初始化失败，请联系管理员处理！"));
                throw new RuntimeException("事务回滚");
            }
        } catch (Exception e) {
            Write(returnPars(-1, "", "新建工单异常，请联系管理员处理！"));
            e.printStackTrace();
            logger.info(e.toString());
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 营销活动： 省公司配额增加申请同时统一配置市州分公司额度（或省公司总额增加申请）流程进行
     *
     * @return JSON字符串  returnPars  code为1：成功   -1；失败
     * @auther TX
     * @date 2021-5-13
     */
    public void handQuotaIniallzation() {
        try {
            String repair = getString("repair");
            String juese = getString("juese");  // 下一步可执行流程线条值
            String userid = getString("userId");// 用户id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String taskId = getString("TaskId");
            RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(repairOrder.getId());
            Bpms_riskoff_task webtask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (webtask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            jbpmUtil.completeTask(task.getId(), juese);
            Task newtask = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            String rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", newtask.getActivityName(), USER.getRowNo(), this.user);
            WaitTask wait = waitTaskService.queryWaitByTaskId(waitId);
            if (wait != null) {
                waitTaskService.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID（WaitTask）为空了==========：" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName(repairOrder.getOrderName());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitialization.jsp?repair=" + repairOrder.getId() + "&task=" + rtaskid);
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(repairOrder.getOrderLogo());
            waitTask.setTaskId(rtaskid);
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "审批提交成功，已提交至：" + USER.getEmployeeName() + " 进行审批！"));
        } catch (Exception e) {
            Write(returnPars(-1, "", "审批提交失败！"));
            e.printStackTrace();
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }

    /**
     * 工单审批通过--推送方法
     */
    public void complateRepairOrder() {
        try {
            String repair = getString("repair");//工单id
            String waitId = getString("waitId");//待办id
            String opinion = getString("opinion");//审批意见
            String taskId = getString("TaskId");//任务表id
            DecimalFormat decimalFormat = new DecimalFormat("0");
            RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(repair);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            if (task == null) {
                Write(returnPars(-1, "", "流程实例异常,请联系系统管理员！"));
                return;
            }
            MarketQuotasWorkOrder marketQuotasWorkOrder = marketActivitiesService.QueryMarketQuotasWorkOrderByOrderNo(repairOrder.getOrderNo());
            if (marketQuotasWorkOrder == null) {
                Write(returnPars(-1, "", "工单数据异常，未查询到工单信息！"));
                return;
            }
            List<MarketQuotasWorkDet> marketQuotasWorkDetList = marketActivitiesService.QueryMarketQuotasWorkDetListByOrderNo(marketQuotasWorkOrder.getOrderNo());
            if (marketQuotasWorkDetList.size() < 1) {
                Write(returnPars(-1, "", "工单数据异常，根据工单未查询到明细信息！"));
                return;
            }
            for (int i = 0; i < marketQuotasWorkDetList.size(); i++) {
                MarketQuotaIniallzation marketQuotaIniallz = new MarketQuotaIniallzation();
                marketQuotaIniallz.setCompanyCode(marketQuotasWorkDetList.get(i).getCompanyCode());
                marketQuotaIniallz.setCompanyName(marketQuotasWorkDetList.get(i).getCompanyName());
                marketQuotaIniallz.setAttributionCompanyCode(marketQuotasWorkOrder.getCompanyCode());
                marketQuotaIniallz.setAttributionCompanyName(marketQuotasWorkOrder.getCompanyName());
                marketQuotaIniallz.setCountAmount(marketQuotasWorkDetList.get(i).getComentTotalAmount());
                marketQuotaIniallz.setPreemptedAmount("0");
                marketQuotaIniallz.setComentTotalAmount("0");
                marketQuotaIniallz.setRemainAmount(marketQuotasWorkDetList.get(i).getComentTotalAmount());
                marketQuotaIniallz.setCreationTime(repairOrder.getCreatorDate());
                marketQuotaIniallz.setCreationName(repairOrder.getCreatorName());
                marketQuotaIniallz.setVersionNumber(getStringDate(new Date(), "yyyy"));
                marketQuotaIniallz.setEnableType("Y");
                marketActivitiesService.addMarketQuotaIniallzation(marketQuotaIniallz);
            }
            if ("省公司".equals(marketQuotasWorkOrder.getCompanyName())) {          //如果为省公司初始化保存本身信息
                MarketQuotaIniallzation marketQuotaIniallzation = new MarketQuotaIniallzation();
                marketQuotaIniallzation.setCompanyCode(marketQuotasWorkOrder.getCompanyCode());
                marketQuotaIniallzation.setCompanyName(marketQuotasWorkOrder.getCompanyName());
                marketQuotaIniallzation.setAttributionCompanyCode("-1");
                marketQuotaIniallzation.setAttributionCompanyName("-1");
                marketQuotaIniallzation.setCountAmount(marketQuotasWorkOrder.getCountAmount());
                marketQuotaIniallzation.setPreemptedAmount("0");
                marketQuotaIniallzation.setComentTotalAmount(marketQuotasWorkOrder.getComentTotalAmount());
                marketQuotaIniallzation.setRemainAmount(marketQuotasWorkOrder.getRemainAmount());
                marketQuotaIniallzation.setCreationTime(repairOrder.getCreatorDate());
                marketQuotaIniallzation.setCreationName(repairOrder.getCreatorName());
                marketQuotaIniallzation.setVersionNumber(getStringDate(new Date(), "yyyy"));
                marketQuotaIniallzation.setEnableType("Y");
                marketActivitiesService.addMarketQuotaIniallzation(marketQuotaIniallzation);
            } else {     //如果是地市公司初始化需要修改本身金额
                MarketQuotaIniallzation marketQuotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkOrder.getCompanyCode());
                if (Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
                        < Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())) {
                    Write(returnPars(-1, "", "预占数据异常，请联系管理员排查！"));
                    throw new RuntimeException(" 给事务回滚，自定义1");
                }
                marketQuotaIniallzation.setPreemptedAmount(
                        decimalFormat.format(
                                Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
                                        - Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())
                        )
                );
                marketQuotaIniallzation.setComentTotalAmount(
                        decimalFormat.format(
                                Double.parseDouble(marketQuotaIniallzation.getComentTotalAmount())
                                        + Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())
                        )
                );
                marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaIniallzation);
            }
            repairOrder.setOrderState("1");
            repairOrderService.updateRepairOrder(repairOrder);
            Bpms_riskoff_task webtask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (webtask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            jbpmUtil.completeTask(task.getId(), "END");
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(repairOrder.getCreatorId()));
            String rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(process.getProcess_sign(), "审批通过!", 1, "CS", "营销活动管理员", USER.getRowNo(), this.user);
            WaitTask wait = waitTaskService.queryWaitByTaskId(waitId);
            if (wait != null) {
                waitTaskService.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID（WaitTask）为空了==========：" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName(repairOrder.getOrderName());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitialization.jsp?repair=" + repairOrder.getId() + "&task=" + rtaskid);
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(repairOrder.getOrderLogo());
            waitTask.setTaskId(rtaskid);
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "操作成功，审批已通过！"));
        } catch (Exception e) {
            logger.error("数据错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", e.getMessage()));
            e.printStackTrace();
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }

    /**
     * 工单审批完成 通知代办关闭
     *
     * @params
     */
    public void closeWait() {
        try {
            String waitId = getString("waitId");//待办id
            String id = getString("repair");
            Bpms_riskoff_task btask = bpms_riskoff_service.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);//获取待办信息
            if (btask != null && wt != null) {
                bpms_riskoff_service.updateBpms_riskoff_task("完成", 2, btask.getId());
                waitTaskService.updateWait(wt, this.getRequest());
            } else {
                Write(returnPars(-1, "", "操作失败，未查询到对应的待办或任务信息！"));
                return;
            }
            Write(returnPars(1, "", "操作成功！"));
        } catch (Exception e) {
            e.printStackTrace();
            Write(returnPars(-1, "", "操作失败，待办信息异常！"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 工单作废
     */
    public void invalidRepairOrder() {
        try {
            String repair = getString("repair");// 工单id
            String waitId = getString("waitId");// 待办id
            RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(repairOrder.getId());
            repairOrder.setOrderState("-1");
            repairOrderService.updateRepairOrder(repairOrder);
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                waitTaskService.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            jbpmUtil.deleteProcessInstance(process.getProcess_sign());// 删除流程
            Write(returnPars(1, "", "工单作废成功！"));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "工单作废失败！"));
        }
    }

    /**
     * 流程退回
     */
    public void returnRepairOrder() {
        try {
            String repair = getString("repair");// 工单id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("TaskId");// 任务表id
            DecimalFormat decimalFormat = new DecimalFormat("0");
            RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
            MarketQuotasWorkOrder marketQuotasWorkOrder = marketActivitiesService.QueryMarketQuotasWorkOrderByOrderNo(repairOrder.getOrderNo());
            if (!"省公司".equals(marketQuotasWorkOrder.getCompanyName())) {
                MarketQuotaIniallzation marketQuotaIniallzation = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(marketQuotasWorkOrder.getCompanyCode());
                if (Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
                        < Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())) {
                    Write(returnPars(-1, "", "预占数据异常，请联系管理员排查！"));
                    throw new RuntimeException(" 给事务回滚，自定义1");
                }
                marketQuotaIniallzation.setPreemptedAmount(
                        decimalFormat.format(
                                Double.parseDouble(marketQuotaIniallzation.getPreemptedAmount())
                                        - Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())
                        )
                );
                marketQuotaIniallzation.setRemainAmount(
                        decimalFormat.format(
                                Double.parseDouble(marketQuotaIniallzation.getRemainAmount())
                                        + Double.parseDouble(marketQuotasWorkOrder.getComentTotalAmount())
                        )
                );
                marketActivitiesService.UpdateMarketQuotaIniallzation(marketQuotaIniallzation);
            }
            repairOrder.setOrderState("2");
            repairOrderService.updateRepairOrder(repairOrder);
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(repairOrder.getCreatorId()));
            bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                waitTaskService.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName(repairOrder.getOrderName());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitialization.jsp?repair=" + repairOrder.getId());
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(repairOrder.getOrderLogo());
            waitTask.setTaskId("");
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "工单退回成功！工单已退回至: " + USER.getEmployeeName()));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "工单退回失败！"));
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }


    /**
     * ----------------------------------------------------------------------------------------------------------------------------   基础功能代码
     * /**
     * 初始化加载地市配额信息
     *
     * @return returnPars
     * @auther TX
     * @date 2021-5-13
     */
    public void findRepairOrder() {
        try {
            String repair = getString("repair");
            if (repair != "") {
                RepairOrder repairOrder = repairOrderService.findRepairOrderById(repair);
                if (repairOrder != null) {
                    MarketQuotasWorkOrder quotasWorkOrder = marketActivitiesService.QueryMarketQuotasWorkOrderByOrderNo(repairOrder.getOrderNo());
                    List<MarketQuotasWorkDet> list = marketActivitiesService.QueryMarketQuotasWorkDetListByOrderNo(repairOrder.getOrderNo());
                    Map<String, Object> map = new HashMap<>();
                    map.put("repairOrder", repairOrder);
                    map.put("quotasWorkOrder", quotasWorkOrder);
                    map.put("list", list);
                    Write(returnPars(1, map, "查询成功！"));
                } else {
                    Write(returnPars(-1, "", "未查询工单信息，请刷新页面重试！"));
                }
            } else {
                Write(returnPars(-1, "", "获取数据失败，请刷新页面重试！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 查询部门配额列表
     *
     * @return LayuiPage对象
     * @auther TX
     * @date 2021-5-13
     */
    public void findQuotaIniallzation() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            page = marketActivitiesService.findQuotaIniallzation(page, user);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        } catch (Exception e) {
            logger.info(e.toString());
            this.Write("列表加载失败！");
        }
    }

    /**
     * 配额划拨查询部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransfer() {
        try {
            String COMPANY_CODE = getString("COMPANY_CODE");
            if (COMPANY_CODE == "") {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            MarketQuotaIniallzation oneself = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(COMPANY_CODE);
            if (oneself != null) {
                Write(returnPars(1, oneself, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 配额划拨查询子部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransferList() {
        try {
            String COMPANY_CODE = getString("COMPANY_CODE");
            if (COMPANY_CODE == "") {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            MarketQuotaIniallzation oneself = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(COMPANY_CODE);
            if (oneself != null) {
                List<MarketQuotaIniallzation> marketQuotaIniallzationList = marketActivitiesService.getMarketQuotaIniallzationListByCompanyCode(oneself.getCompanyCode());
                Write(returnPars(1, marketQuotaIniallzationList, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 配额划拨查询子部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransferListByVwUserinfo() {
        try {
            String COMPANY_CODE = getString("COMPANY_CODE");
            if (COMPANY_CODE == "") {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            MarketQuotaIniallzation oneself = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(COMPANY_CODE);
            if (oneself != null) {
                List<Map<String, Object>> countyList = marketActivitiesService.QueryCountyName(oneself.getCompanyName());
                Map<String, Object> map = new HashMap<>();
                map.put("oneself", oneself);
                map.put("countyList", countyList);
                Write(returnPars(1, map, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 配额划拨查询上一级部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransferByAttributionCompanyCode() {
        try {
            String COMPANY_CODE = getString("COMPANY_CODE");
            if (COMPANY_CODE == "") {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            MarketQuotaIniallzation oneself = marketActivitiesService.getMarketQuotaIniallzationByCompanyCode(COMPANY_CODE);
            if (oneself != null) {
                Write(returnPars(1, oneself, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 配额划拨查询同级级部门信息
     *
     * @return returnPars对象
     * @auther TX
     * @date 2021-5-13
     */
    public void QueryCitiesTransferListByAttributionCompanyCode() {
        try {
            String AttributionCompanyCode = getString("AttributionCompanyCode");
            if (AttributionCompanyCode == "") {
                Write(returnPars(-1, "", "参数异常，请关闭页面重新发起！"));
                return;
            }
            List<MarketQuotaIniallzation> oneself = marketActivitiesService.getMarketQuotaIniallzationListByCompanyCode(AttributionCompanyCode);
            if (oneself.size() > 0) {
                Write(returnPars(1, oneself, "数据查询成功！"));
            } else {
                Write(returnPars(-1, "", "查询数据异常，请关闭页面重新发起！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 根据部门名称查询配额信息
     *
     * @return JSON字符串   returnPars
     * @auther TX
     * @date 2021-5-17
     */
    public void QueryMarketQuotaIniallzationByCompanyName() {
        try {
            List<Map<String,Object>> listtwo = marketActivitiesService.getVwUser(user);
            String COMPANY_NAME = "";
            if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
            } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (listtwo.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
            } else {
                COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
            }
            MarketQuotaIniallzation oneself = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(COMPANY_NAME);
            if (oneself != null) {
                Map<String, Object> map = new HashMap<>();
                boolean flag = false;
                boolean flagTwo = false;
                //logger.info("权限数据为=="+JSONArray.fromObject(marketActivitiesService.findByRowNo(user.getRowNo())).toString());
                if (checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062731")) {   //判断是否为营销活动省公司管理员
                    logger.info("营销活动省公司管理员");
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
                    if(marketActivitiesService.GetMarketQuotaInialVersionNumber().equals("null")){
                        flag = true;
                    }else {
                        flag = Integer.parseInt(simpleDateFormat.format(new Date())) > Integer.parseInt(marketActivitiesService.GetMarketQuotaInialVersionNumber());
                    }
                    //System.out.println("==="+Integer.parseInt(marketActivitiesService.GetMarketQuotaInialVersionNumber()));
                }
                if (checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062725")) {       //判断是否未营销活动市公司管理员
                    logger.info("营销活动市公司管理员");
                    String VersionNumber = marketActivitiesService.GetMarketQuotaInialVersionNumber();
                    List<MarketQuotaIniallzation> list = marketActivitiesService.QueryQuotaIniallzationByUserRowNo(String.valueOf(user.getRowNo()), VersionNumber);
                    flagTwo = list.size() <= 0;
                }
                map.put("flag", flag);
                map.put("flagTwo", flagTwo);
//                map.put("flag", "true");
//                map.put("flagTwo", "true");
                Write(returnPars(1, oneself, map));
            }else if(oneself == null && checkOrderAdmin(marketActivitiesService.findByRowNo(user.getRowNo()), "16062731")){ //没有配额信息，又是省公司
                logger.info("营销活动省公司管理员2");
                Map<String, Object> map = new HashMap<>();
                boolean flag = true;
                boolean flagTwo = false;
                map.put("flag", flag);
                map.put("flagTwo", flagTwo);
                Write(returnPars(2, COMPANY_NAME, map));
            } else {
                Write(returnPars(-1, COMPANY_NAME, "当前" + COMPANY_NAME + "暂无营销活动配额，请先申请配额后办理营销活动！"));
            }
        } catch (Exception e) {
            logger.info(e.toString());
            Write(returnPars(-1, "", "数据查询异常，请联系管理员处理！"));
        }
    }

    /**
     * 获取初始化地市部门   返回   code为1：成功   -1；失败
     *
     * @return JSON字符串   returnPars
     * @auther TX
     * @date 2021-5-19
     */
    public void FillInTheData() {
        String type = getString("type");
        List<Map<String, String>> companyList = marketActivitiesService.getCompanyList();
        if (companyList.size() > 0) {
            Write(returnPars(1, companyList, ""));
        } else {
            Write(returnPars(-1, "", "获取地市部门失败，请联系管理员查看！"));
        }
    }


    /**
     * 查询活动代码
     *
     * @return LayuiPage对象
     * @auther TX
     * @date 2021-5-13
     */
    public void findActiveCodeByPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            String ActiveCode = getString("ActiveCode");        //活动代码
            String transferMonth = getString("TransferMonth");//月份

            String ActiveType = getString("ActiveType");        //列表类型
            //System.out.println("列表类型==" + ActiveType);
            page = marketActivitiesService.findActiveCodeByPage(page, ActiveCode, ActiveType, transferMonth);
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            this.Write(json);
        } catch (Exception e) {
            logger.info(e.toString());
            this.Write("列表加载失败！");
        }
    }

    public void findActiveCodeByPageNew() {
        Result r = new Result();
        try {
            String ActiveType = getString("ActiveType");        //列表类型
            String month = getString("month");
            String market_proportion = getString("market_proportion");
            List<Map<String, String>> byMarketStockSystemGroup = marketActivitiesService.findActiveCodeByPageNew(ActiveType, month, market_proportion);
            //System.out.println("欠费数据===" + JSONHelper.SerializeWithOutInnerClass(page));
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(byMarketStockSystemGroup);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("获取存量统存集团客户失败" + e);
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }


    /**
     * 修改活动启用状态   返回   code为1：成功   -1；失败
     *
     * @return JSON字符串   returnPars
     * @auther TX
     * @date 2021-5-13
     */
    public void updateEnable() {
        try {
            String ACTIVE_CODE = getString("ACTIVE_CODE");  //活动代码
            String ENABLE_TYPE = getString("ENABLE_TYPE");  //代码类型  1：启用，0：停用
            if (ACTIVE_CODE != null && ACTIVE_CODE != "" && ENABLE_TYPE != null && ENABLE_TYPE != "") {
                MarketActiveCode marketActiveCode = marketActivitiesService.getMarketActiveCodeByActiveCode(ACTIVE_CODE);
                marketActiveCode.setEnableType(ENABLE_TYPE);
                marketActivitiesService.updateMarketActiveCode(marketActiveCode);
                Write(returnPars(1, "", "营销活动代码状态更改成功！"));
            } else {
                Write(returnPars(-1, "", "营销活动代码状态更改,参数异常！"));
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("营销活动代码状态更改错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "营销活动代码状态更改--》功能异常！"));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description: TODO 返回参数生成
     */
    private static String returnPars(int state, Object data, Object msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    /**
     * <方法序号：2.1 > <方法名：checkOrderAdmin> <详细描述：循环遍历查询是否为规定的角色>
     *
     * @Param: [list, no]
     * @return: boolean
     * @Author: ChuHongQuan
     * @Date: 2019/10/11 15:57
     */
    public boolean checkOrderAdmin(List<Map<String,Object>> list, String no) {

        boolean flag = false;
        for (int i = 0; i < list.size(); i++) {
            if ((list.get(i).get("ROLE_ID").toString()).equals(no)) {// 16
                flag = true;
                break;
            }
        }
        return flag;
    }


    /**
     * 导入Excel显示数据
     */
    public void importExcelAndShow() {
        try {
            ExcelUtil excelReader = new ExcelUtil(file1);
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList<>();
            HashMap maps;
            for (int i = 1; i <= map.size(); i++) {
                maps = new HashMap();
                maps.put("ActiveType", map.get(i).get(0));//类型
                maps.put("ActiveName", map.get(i).get(1));//活动名称
                maps.put("ActiveCode", map.get(i).get(2));//活动代码
                maps.put("DepositedAmount", map.get(i).get(3));//预存金额
                maps.put("ActiveRate", map.get(i).get(4));//送费比例
                maps.put("TransferMonth", map.get(i).get(5));//送费划拨月份
                maps.put("ExpirationTime", map.get(i).get(6));//到期时间
                list.add(maps);
            }
            String json = JSONHelper.SerializeWithNeedAnnotation(list);
            if (map.size() > 0) {
                this.Write(json);
            } else {
                this.Write("NULL");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 获取当前时间：格式转换：
     *
     * @param str 格式参数：
     * @return date 时间：
     */
    public Date toDate(String str) {
        Date d = new Date();
        SimpleDateFormat sdf = null;
        if (str == null) {
            sdf = new SimpleDateFormat("yyyy-MM-dd");
        } else {
            sdf = new SimpleDateFormat(str);
        }
        try {
            return sdf.parse(sdf.format(d));
        } catch (ParseException e) {
            throw new Error(" date Exception throw ");
        }
    }

    /**
     * 保存导入的Excel数据(营销活动(辅助类)——活动代码实体)
     */
    public void saveExcelData() {
        try {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            String jsonone = getString("jsonone");
            //System.out.println("===>"+jsonone);
            JSONArray jsonArr = JSONArray.fromObject(jsonone);
            //System.out.println("===>" + jsonArr);
            if (jsonone != null) {
                for (int i = 0; i < jsonArr.size(); i++) {
                    JSONObject obj = jsonArr.getJSONObject(i);
                    //先查
                    MarketActiveCode activeCode = marketActivitiesService.getMarketActiveCodeByActiveCode(obj.get("ActiveCode").toString());
                    if(activeCode!=null){
                        continue;
                    }else {
                        MarketActiveCode mac = new MarketActiveCode();
                        mac.setActiveCode(obj.get("ActiveCode").toString());
                        mac.setActiveName(obj.get("ActiveName").toString());
                        String at = obj.get("ActiveType").toString();
                        if (at.equals("存量统存")) {
                            mac.setActiveType("0");
                            String ar = obj.get("ActiveRate").toString();
                            mac.setActiveRate(ar);
                        } else if (at.equals("集客综合通信业务包")) {
                            mac.setActiveType("1");
                            String ar = obj.get("ActiveRate").toString();
                            if (ar.contains("*") && ar.contains("%")) {  //本金* 10 %
                                if(StringUtils.isNumeric(ar.substring(3, 5))){
                                    mac.setActiveRate(ar.substring(3, 5));
                                }else {
                                    this.Write("NO");
                                    return;
                                }
                            }else if(ar.contains("%")){ //10%
                                if(StringUtils.isNumeric(ar.substring(0, ar.length()-1))){
                                    mac.setActiveRate(ar.substring(0, ar.length()-1));
                                }else {
                                    this.Write("NO");
                                    return;
                                }
                                //str.matches("[+-]?[0-9]+(\\.[0-9]{1,4})?")
                            }else if(StringUtils.isNumeric(ar)){ //10
                                if(Integer.valueOf(ar)>0 && Integer.valueOf(ar)<101){
                                    mac.setActiveRate(ar);
                                }else {
                                    this.Write("NO");
                                    return;
                                }
                            }else if (ar.matches("[+-]?[0-9]+(\\.[0-9]{1,4})?")){ //0.1
                                Double a = Double.valueOf(ar);
                                //Integer in = Integer.valueOf(String.valueOf(a));
                                //Integer in = (int)Math.floor(a);
                                String s = String.valueOf(a * 100);
                                String substring = s.substring(0, s.indexOf("."));
                                mac.setActiveRate(substring);
                            } else {
                                this.Write("NO");
                                return;
                            }
                        }
                        //mac.setActiveRate(obj.get("ActiveRate").toString());

                        mac.setExpirationTime(sdf.parse(obj.get("ExpirationTime").toString()));
                        mac.setTransferMonth(obj.get("TransferMonth").toString());
                        String da = obj.get("DepositedAmount").toString();
                        String money = "";
                        if (da.contains("-")) {
                            String[] s = da.split("-");
                            String left = s[0];
                            String right = s[1];
                            if (left.contains("万元")) {
                                money = left.replace("万元", "0000");
                            } else if (left.contains("万")) {
                                money = left.replace("万", "0000");
                            } else if (left.contains("元")) {
                                money = left.replace("元", "");
                            } else {
                                money = left;
                            }
                            money += "-";
                            if (right.contains("万元")) {
                                money += right.replace("万元", "0000");
                            } else if (right.contains("万")) {
                                money += right.replace("万", "0000");
                            } else if (right.contains("元")) {
                                money += right.replace("元", "");
                            } else {
                                money += right;
                            }
                            mac.setDepositedAmount(money);
                        } else if (da.contains("至")) {
                            String[] s = da.split("至");
                            String left = s[0];
                            String right = s[1];
                            if (left.contains("万元")) {
                                money = left.replace("万元", "0000");
                            } else if (left.contains("万")) {
                                money = left.replace("万", "0000");
                            } else if (left.contains("元")) {
                                money = left.replace("元", "");
                            } else {
                                money = left;
                            }
                            money += "-";
                            if (right.contains("万元")) {
                                money += right.replace("万元", "0000");
                            } else if (right.contains("万")) {
                                money += right.replace("万", "0000");
                            } else if (right.contains("元")) {
                                money += right.replace("元", "");
                            } else {
                                money += right;
                            }
                            mac.setDepositedAmount(money);
                        }else {
                            this.Write("NO");
                            return;
                        }
                        mac.setEnableType("1"); //默认启用 1:启用 0:停用
                        //System.out.println("数据为=="+JSONHelper.Serialize(mac));
                        marketActivitiesService.saveExcelData(mac);
                    }
                }
            }
            this.Write("YES");
        } catch (Exception e) {
            this.Write("NO");
            e.printStackTrace();
        }
    }

    /**
     * 营销活动配额工单 分页查询
     */
    public void queryQuotasWorkOrder() {
        //分页
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            //条件查询
            String orderNo = getString("OrderNo");
            String creatorName = getString("creatorName");
            String orderState = getString("orderState");
            String companyName = getString("companyName");
            String orderName = getString("orderName");

            page = marketActivitiesService.queryQuotasWorkOrder(page, orderNo, creatorName, orderState, companyName, orderName);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 根据ORDER_NO 查询详细信息
     */
    public void findByORDER_NO() {
        try {
            JSONObject pars = new JSONObject();
            String orderNo = getString("ORDER_NO");
            //logger.info("数据为1=="+orderNo);
            Map<String, Object> QuotasWorkOrder = marketActivitiesService.queryQuotasWorkOrderByOrderNo(orderNo);
            logger.info("主工单数据1=="+QuotasWorkOrder);
            Map<String, Object> json = marketActivitiesService.queryRepairOrderByOrderNo(orderNo);
            String RepairOrder = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(json);
            logger.info("流程数据2=="+RepairOrder);
            pars.put("QuotasWorkOrder", QuotasWorkOrder);
            pars.put("RepairOrder", RepairOrder);
            Write(pars.toString());
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 营销活动
     * 获取存量统存集团客户
     */
    public void findByMarketStockSystemGroup() {
        logger.info("运行获取存量统存集团客户");
        Result r = new Result();
        try {
            //String account_no = getString("account_no");//boss工号
            String account_no = user.getBossUserName();
            //System.out.println(account_no);
            String unit_id = getString("unit_id");//集团编号
            //System.out.println(unit_id);
            List<Map<String, String>> byMarketStockSystemGroup = marketActivitiesService.findByMarketStockSystemGroup(account_no, unit_id);
            //System.out.println("欠费数据===" + JSONHelper.SerializeWithOutInnerClass(page));
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(byMarketStockSystemGroup);
            Write(r.toString());
        } catch (Exception e) {
            logger.info("获取存量统存集团客户失败" + e);
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("");
            Write(r.toString());
        }
    }

    /**
     * 添加营销活动工单
     */
    public void addMarketActivityWorkOrder() {
        logger.info("营销活动工单开始流程");
        try {
            List<Map<String,Object>> listtwo = marketActivitiesService.getVwUser(user);

            String COMPANY_NAME = "";
            if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
            } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") || (listtwo.get(0).get("COUNTY_NAME").toString()).contains("直属")) {
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
            } else {
                COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
            }
            String role = this.getString("role");
            Integer userId = this.getInteger("userId");
            MarketActivityWorkOrder order = new MarketActivityWorkOrder();
            DecimalFormat decimalFormat = new DecimalFormat("0");
            String order_name = getString("order_name");// 工单标题
            String orderNo = "";//申请编码前面的字母
            order.setOrder_name(order_name);
            List<Object[]> sone = marketActivitiesService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                orderNo = (String) sone.get(i)[2];
            }
            order.setOrder_no(orderNo + getStringDate(new Date(), null));
            String order_mome = getString("order_mome");// 申请说明
            order.setOrder_mome(order_mome);

            String principal_amount = getString("principal_amount");// 合同期总预存本金金额
            order.setPrincipal_amount(
                    decimalFormat.format(
                            Double.parseDouble(principal_amount) * 100
                    )
            );//元转分存数据库

            String delivery_amount = getString("delivery_amount");//活动预期总送费
            //System.out.println("delivery_amount=="+delivery_amount);
            order.setDelivery_amount(
                    decimalFormat.format(
                            Double.parseDouble(delivery_amount) * 100
                    )
            );//元转分存数据库

            String estimated_amount = getString("estimated_amount");// 本次活动后预估收入(元/月)->转成分
            order.setEstimated_amount(
                    decimalFormat.format(
                            Double.parseDouble(estimated_amount) * 100
                    )
            );//元转分存数据库

            String sub_amount = getString("sub_amount");// 本次活动申请后办理三方分成金额
            order.setSub_amount(
                    decimalFormat.format(
                            Double.parseDouble(sub_amount) * 100
                    )
            );//元转分存数据库
            String order_category = getString("order_category");//存量统存 集客综合
            String active_code = getString("active_code");
            String active_name = getString("active_name");
            String active_rate = getString("active_rate");
            //活动工单生成
            order.setActive_code(active_code);
            order.setActive_name(active_name);
            order.setActive_rate(active_rate);
            //String deptStr = getString("deptStr");
            String attachmentId = getString("attachmentId");
            String unit_id = getString("unit_id");
            //根据集团280查询
            MarketGroupStatistics marketGroupStatistics = marketActivitiesService.getMarketGroupStatistics(unit_id);
            if (marketGroupStatistics == null) {
                order.setContrast("平衡");
            } else {
                order.setPre_amount(marketGroupStatistics.getTotal_revenue());
                String c = String.valueOf((Double.parseDouble(order.getEstimated_amount()) - Double.parseDouble(marketGroupStatistics.getTotal_revenue())) / Double.parseDouble(marketGroupStatistics.getTotal_revenue()) * 100);
                String a = "-100";
                String b = "0";
                //精确表示
                BigDecimal dataA = new BigDecimal(a);
                BigDecimal dataB = new BigDecimal(b);
                BigDecimal dataC = new BigDecimal(c);

                String s1 = String.valueOf(dataC.compareTo(dataB));//和0比较 大于为1，相同为0，小于为-1
                String s2 = String.valueOf(dataC.compareTo(dataA));//和-100比较 大于为1，相同为0，小于为-1
                if (s1.equals("1") || s1.equals("0")) {
                    order.setContrast("大于");
                } else if (s1.equals("-1") || s2.equals("1")) {
                    order.setContrast("平衡");
                } else {
                    order.setContrast("小于");
                }
            }
            order.setUnit_id(unit_id);//集团编号
            String unit_name = getString("unit_name");
            order.setUnit_name(unit_name);//集团名称
            String unit_grade = getString("unit_grade");
            order.setUnit_grade(unit_grade);//集团等级
            order.setOrder_category(order_category);
            String zhanghu = getString("zhanghu");//存量统存账户
            order.setUser_rowno(String.valueOf(user.getRowNo()));
            order.setUser_name(user.getEmployeeName());
            MarketWorkOrderLink marketWorkOrderLink = new MarketWorkOrderLink();
            if (order_category.equals("0")) {
                order.setLink_no("营销活动工单生成");
                order.setDeptStr(COMPANY_NAME);
                order = marketActivitiesService.addMarketActivityWorkOrder(order);
                //存环节表
                //marketWorkOrderLink.setLink_name("营销活动工单发起");//环节名称
                marketWorkOrderLink.setLink_name("营销活动工单评估");//环节名称
                marketWorkOrderLink.setLink_state("0");//环节状态 0.进行中 1.已完成
                marketWorkOrderLink.setCreator_boss(user.getBossUserName());
                marketWorkOrderLink.setCreator_id(String.valueOf(user.getRowNo()));
                marketWorkOrderLink.setCreator_name(user.getEmployeeName()); //发起人
                marketWorkOrderLink.setStart_time(new Date());//发起时间
                marketWorkOrderLink.setMarket_work_id(order.getId());//绑定营销活动工单编号
                marketActivitiesService.addMarketWorkOrderLink(marketWorkOrderLink);
            } else if (order_category.equals("1")) {
                order.setLink_no("营销活动存量统存工单生成");
                SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
                String start = getString("contract_period_start");//合同开始时间
                String end = getString("contract_period_end");//合同失效时间
                order.setStart_date(yyyyMMdd.parse(start));
                order.setEnd_date(yyyyMMdd.parse(end));
                order.setStock_system_id(zhanghu);
                order.setActual_amount(decimalFormat.format(
                        Double.parseDouble(principal_amount) * 100
                ));//合同期实际总预存本金金额 分
                order.setActual_fee_amount(decimalFormat.format(
                        Double.parseDouble(delivery_amount) * 100
                )); //合同期实际总送费金额 分;
                order = marketActivitiesService.addMarketActivityWorkOrder(order);
                //存环节表
                marketWorkOrderLink.setLink_name("营销活动工单评估");//环节名称
                // marketWorkOrderLink.setLink_name("营销活动工单发起");//环节名称
                marketWorkOrderLink.setLink_state("0");//环节状态 0.进行中 1.已完成
                marketWorkOrderLink.setCreator_boss(user.getBossUserName());
                marketWorkOrderLink.setCreator_id(String.valueOf(user.getRowNo()));
                marketWorkOrderLink.setCreator_name(user.getEmployeeName()); //发起人
                marketWorkOrderLink.setStart_time(new Date());//发起时间
                marketWorkOrderLink.setMarket_work_id(order.getId());//绑定营销活动工单编号
                marketActivitiesService.addMarketWorkOrderLink(marketWorkOrderLink);
                //MarketStockSystemGroup marketStockBySerialNumber = marketActivitiesService.findMarketStockBySerialNumber(zhanghu);
                //System.out.println("数据---"+JSONHelper.Serialize(marketStockBySerialNumber));
                //marketStockBySerialNumber.setSerial_number("1");
                marketActivitiesService.upDataMarketStockSystemGroup(zhanghu);
            }
            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] attachmentjson = attachmentId.split(",");
                    if (attachmentjson.length > 0) {
                        for (int i = 0; i < attachmentjson.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(marketWorkOrderLink.getId());
                            sa.setAttachmentId(attachmentjson[i]);
                            sa.setLink(marketWorkOrderLink.MarketWorkOrderLink);
                            marketActivitiesService.saveSandA(sa);
                        }
                    }
                }
            }
            if (marketWorkOrderLink != null) {
                String node = "";
                if ("QX".equals(role)) {
                    node = "ROLE_QXSM";
                } else if ("SGS".equals(role)) {
                    node = "ROLE_DSDM";
                } else if ("SZK".equals(role)) {
                    node = "ROLE_STFGLY";
                }
                Map<String, String> map = new HashMap<>();
                map.put("node", node);
                //String processId = jbpmUtil.startPIByKey(node, map).getId();
                String processId = jbpmUtil.startPIByKey("ActivityWorkOrderOne", map).getId();
                SystemUser USER = systemUserService.getUserInfoRowNo(userId);
                bpms_riskoff_service.setBpms_riskoff_process(marketWorkOrderLink.getId(), processId, 1, user);
                Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
                this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "营销活动工单发起人", this.user.getRowNo(), this.user);
                String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), USER.getRowNo(), this.user);
                WaitTask waitTask = new WaitTask();
                waitTask.setName("[营销活动]" + order.getOrder_name());
                waitTask.setCreationTime(new Date());
                waitTask.setUrl("jsp/MarketActivitiesView/ActiveWorkApproval.jsp?repair=" + marketWorkOrderLink.getId() + "&task=" + taskid);
                waitTask.setState(WaitTask.HANDLE);
                waitTask.setHandleUserId(USER.getRowNo());
                waitTask.setHandleUserName(USER.getEmployeeName());
                waitTask.setHandleLoginName(USER.getLoginName());
                waitTask.setCreateUserId(user.getRowNo());
                waitTask.setCreateUserName(user.getEmployeeName());
                waitTask.setCreateLoginName(user.getLoginName());
                waitTask.setCode(order.MarketActivityWorkOrder);
                waitTask.setTaskId(taskid);
                this.waitTaskService.saveWait(waitTask, this.getRequest());
                Write(returnPars(1, "", "审批提交成功，已提交至：" + USER.getEmployeeName() + " 进行审批！"));
//                Write(returnPars(1,"","工单：添加成功，已推送至：处，请等待审批！"));
            } else {
                Write(returnPars(-1, "", "添加工单记录时出现错误，请联系管理员处理！"));
                throw new RuntimeException("事务回滚");
            }
        } catch (Exception var16) {
            logger.info("流程发起失败" + var16);
            var16.printStackTrace();
            this.Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 营销活动工单流程进行
     *
     * @return JSON字符串  returnPars  code为1：成功   -1；失败
     */
    public void HandUpdateActivityWorkOrder() {
        try {
            logger.info("营销活动流程进行");
            String id = getString("repair");//环节工单id
            String juese = getString("juese");  // 下一步可执行流程线条值
            String userid = getString("userId");// 用户id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String taskId = getString("TaskId");
            String node = "";
            MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
            String market_work_id = marketWorkOrderLink.getMarket_work_id();//环节工单获取营销活动信息id
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(market_work_id);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(marketWorkOrderLink.getId());
            Bpms_riskoff_task webtask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (webtask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            if (juese.equals("ROLE_DSSM")) {
                if (marketActivityWorkOrder.getContrast().equals("平衡")) {
                    node = "平衡";
                } else if (marketActivityWorkOrder.getContrast().equals("小于")) {
                    node = "小于";
                }
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", node);
                jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
            } else {
                jbpmUtil.completeTask(task.getId(), juese);
            }
            Task newtask = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            String rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", newtask.getActivityName(), USER.getRowNo(), this.user);
            WaitTask wait = waitTaskService.queryWaitByTaskId(waitId);
            if (wait != null) {
                waitTaskService.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID（WaitTask）为空了==========：" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName("[营销活动审批]" + marketActivityWorkOrder.getOrder_name());
            waitTask.setCreationTime(new Date());
            //waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitializationTwo.jsp?repair=" + marketWorkOrderLink.getId() + "&task=" + rtaskid);
            waitTask.setUrl("jsp/MarketActivitiesView/ActiveWorkApproval.jsp?repair=" + marketWorkOrderLink.getId() + "&task=" + rtaskid);
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(marketActivityWorkOrder.MarketActivityWorkOrder);
            waitTask.setTaskId(rtaskid);
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "审批提交成功，已提交至：" + USER.getEmployeeName() + " 进行审批！"));
            //Write(returnPars(1, "", "审批提交成功，已提交至： 进行审批！"));
        } catch (Exception e) {
            Write(returnPars(-1, "", "审批提交失败！"));
            e.printStackTrace();
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }

    /**
     * 导出营销活动代码模板信息
     */
    public void RedListInform() {
        try {
            String retrieve = this.getString("retrieve");
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name = "";
            String filepath = null;
//            if ("1".equals(retrieve)) {
            name = "营销活动代码模板";
            filepath = request.getSession().getServletContext().getRealPath("/template/MarketCodeInformation.xlsx");
//            } else if ("2".equals(retrieve)) {
//                name = "红名单产品信息模板";
//                filepath = request.getSession().getServletContext().getRealPath("/template/RedListInformationTwo.xlsx");
//            } else if ("3".equals(retrieve)) {
//                name = "集团统付信息模板";
//                filepath = request.getSession().getServletContext().getRealPath("/template/RedListInformationThree.xlsx");
//            }
            //System.out.println(filepath);
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
        }
    }


    /**
     * 查询营销活动工单
     */
    public void findMarketActivityWorkOrderByPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);

            String unit_id = getString("unit_id");//集团编码
            String order_no = getString("order_no");//工单编号

            String ActiveType = getString("ActiveType");        //列表类型
            //System.out.println("列表类型=="+ActiveType);
            page = marketActivitiesService.findMarketActivityWorkOrderByPage(page, ActiveType, unit_id, order_no, String.valueOf(user.getRowNo()));
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            //System.out.println("数据==>"+json);
            this.Write(json);
        } catch (Exception e) {
            logger.info(e.toString());
            this.Write("列表加载失败！");
        }
    }

    /**
     * 根据环节单id查询营销活动工单信息
     */
    public void findMarketActivityByLinkId() {
        try {
            String id = getString("id");
            MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
            String market_work_id = marketWorkOrderLink.getMarket_work_id();//获取营销活动信息id
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(market_work_id);
            //System.out.println("营销活动工单信息==="+JSONHelper.Serialize(marketActivityWorkOrder));
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(marketActivityWorkOrder));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("NO");
        }
    }

    /**
     * 根据营销活动id查询营销活动工单信息
     */
    public void findMarketActivityById() {
        try {
            String id = getString("id");
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(id);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(marketActivityWorkOrder));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("NO");
        }
    }

    /**
     * 根据环节单id查询环节信息
     */
    public void findMarketLinkByLinkId() {
        try {
            String id = getString("id");
            MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(marketWorkOrderLink));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("NO");
        }
    }


    /**
     * 营销合同发起环节
     * 同意
     */
    public void returnHintBackLogData() {
        logger.info("同意");
        try {
            String id = getString("repair");//id
            //String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 意见
            String taskId = getString("TaskId");
            String juese = getString("juese");

            //Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
//            RepairOrder repairOrder = repairOrderService.findRepairOrderById(id);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            if (task == null) {
                Write(returnPars(-1, "", "流程实例异常,请联系系统管理员！"));
                return;
            }
            MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
            String market_work_id = marketWorkOrderLink.getMarket_work_id();//环节工单获取营销活动信息id
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(market_work_id);
            marketActivityWorkOrder.setOrder_status("3");
            //RedRollList rl = redRollListService.getRedRollList(id); //查询工单信息
            marketWorkOrderLink.setLink_state("1");
            marketWorkOrderLink.setEnd_time(new Date());
            marketActivitiesService.upDataMarketWorkOrderLink(marketWorkOrderLink);//更改环节为已完成
            marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);//更改工单
            Bpms_riskoff_task webtask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (webtask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            if (juese.equals("ROLE_DSSM")) {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "END");
                jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
            } else {
                jbpmUtil.completeTask(task.getId(), "END");
            }
            //SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(marketWorkOrderLink.getCreator_id()));
            //String rtaskid = this.bpms_riskoff_service.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "CS", "营销活动生成工单发起人", USER.getRowNo(), this.user);
            WaitTask wait = waitTaskService.queryWaitByTaskId(waitId);
            if (wait != null) {
                waitTaskService.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID（WaitTask）为空了==========：" + waitId);
            }
//            WaitTask waitTask = new WaitTask();
//            waitTask.setName("[营销活动确认]" + marketActivityWorkOrder.getOrder_name());
//            waitTask.setCreationTime(new Date());
//            //waitTask.setUrl("jsp/MarketActivitiesView/handQuotaInitializationTwo.jsp?repair=" + marketWorkOrderLink.getId() + "&task=" + rtaskid);
//            waitTask.setUrl("jsp/MarketActivitiesView/ActiveWorkApproval.jsp?repair=" + marketWorkOrderLink.getId() + "&task=" + rtaskid);
//            waitTask.setState(WaitTask.HANDLE);
//            waitTask.setHandleUserId(USER.getRowNo());
//            waitTask.setHandleUserName(USER.getEmployeeName());
//            waitTask.setHandleLoginName(USER.getLoginName());
//            waitTask.setCreateUserId(user.getRowNo());
//            waitTask.setCreateUserName(user.getEmployeeName());
//            waitTask.setCreateLoginName(user.getLoginName());
//            waitTask.setCode(marketActivityWorkOrder.MarketActivityWorkOrder);
//            waitTask.setTaskId(rtaskid);
//            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "操作成功，审批已通过！"));
            //Write(returnPars(1, "", "审批提交成功，已提交至：" + USER.getEmployeeName() + " 进行审批！"));
        } catch (Exception e) {
            logger.error("数据错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", e.getMessage()));
            e.printStackTrace();
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }


    /**
     * 营销合同评估
     * 完成生成待办
     */
    public void returnHintBackLogDataTwo() {
        logger.info("同意");
        try {
            String id = getString("repair");//id
            //String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 意见
            String taskId = this.getString("TaskId");
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(process.getProcess_sign()).uniqueResult();// 获取流程任务表信息
            if (task == null) {
                Write(returnPars(-1, "", "流程实例异常,请联系系统管理员！"));
                return;
            }
            MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
            String market_work_id = marketWorkOrderLink.getMarket_work_id();//环节工单获取营销活动信息id
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(market_work_id);
            marketActivityWorkOrder.setOrder_status("3");
            //RedRollList rl = redRollListService.getRedRollList(id); //查询工单信息
            marketWorkOrderLink.setLink_state("1");
            marketWorkOrderLink.setEnd_time(new Date());
            marketActivitiesService.upDataMarketWorkOrderLink(marketWorkOrderLink);//更改环节为已完成
            marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);//更改工单
            Bpms_riskoff_task webtask = this.bpms_riskoff_service.getBpms_riskoff_task(taskId);
            if (webtask != null) {
                this.bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            jbpmUtil.completeTask(task.getId(), "END");
            WaitTask wait = waitTaskService.queryWaitByTaskId(waitId);
            if (wait != null) {
                waitTaskService.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID（WaitTask）为空了==========：" + waitId);
            }
            Write(returnPars(1, "", "操作成功，审批已通过！"));
            //Write(returnPars(1, "", "审批提交成功，已提交至：" + USER.getEmployeeName() + " 进行审批！"));
        } catch (Exception e) {
            logger.error("数据错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", e.getMessage()));
            e.printStackTrace();
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }


    /**
     * 发起活动签约环节
     *
     * @params
     */
    public void closeWaitActiveWork() {
        try {
            String id = getString("repair");
            String role = getString("role");
            Integer userId = getInteger("userId");
            //账户
            //合同期实际总预存本金金额（元）; 【自动计
            //算】合同期实际总送费金额(元) = 合同期实际总预存本金金额(元) *
            //送费比例;
            //合同起止日期;
            String actual_amount = getString("actual_amount");
            String actual_fee_amount = getString("actual_fee_amount");
            String start_date = getString("start_date");
            String end_date = getString("end_date");
            String stock_system_id = getString("stock_system_id");
            //附件
            String id1 = getString("pdfId");//合同
            String id2 = getString("xlsId");//授权书
            String id3 = getString("wordId");//经办人

            DecimalFormat decimalFormat = new DecimalFormat("0");
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(id);

            //判断工单类别 0.集客综合通信 1.存量统存 order_category
            if (marketActivityWorkOrder.getOrder_category().equals("0")) {
                //算钱，预占
                MarketQuotaIniallzation companyName = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(marketActivityWorkOrder.getDeptStr());
                //System.out.println("获取剩余额度金额=="+Double.parseDouble(companyName.getCountAmount()));
                //System.out.println("获取要送的金额=="+Double.parseDouble(actual_fee_amount) * 100);
                //System.out.println("比较总金额是否大于送的金额==" + (Double.parseDouble(companyName.getRemainAmount()) > (Double.parseDouble(actual_fee_amount) * 100)));
                if (Double.parseDouble(companyName.getRemainAmount()) > (Double.parseDouble(actual_fee_amount) * 100)) {
                    companyName.setPreemptedAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyName.getPreemptedAmount())
                                            + Double.parseDouble(actual_fee_amount) * 100
                            )
                            //Delivery_amount
                    );//预占金额+
                    companyName.setRemainAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyName.getRemainAmount())
                                            - Double.parseDouble(actual_fee_amount) * 100
                            )
                    );//剩余总额度—
                    marketActivitiesService.UpdateMarketQuotaIniallzation(companyName);
                    SimpleDateFormat yyyyMMdd = new SimpleDateFormat("yyyyMMdd");
                    marketActivityWorkOrder.setStart_date(yyyyMMdd.parse(start_date));
                    marketActivityWorkOrder.setEnd_date(yyyyMMdd.parse(end_date));
                    marketActivityWorkOrder.setStock_system_id(stock_system_id);
                    marketActivityWorkOrder.setActual_amount(decimalFormat.format(
                            Double.parseDouble(actual_amount) * 100
                    ));//合同期实际总预存本金金额 分
                    marketActivityWorkOrder.setActual_fee_amount(decimalFormat.format(
                            Double.parseDouble(actual_fee_amount) * 100
                    )); //合同期实际总送费金额 分;
                } else {
                    Write(returnPars(-1, "", "营销活动配额不足,请核实后再进行申请！"));
                    throw new RuntimeException("事务回滚");
                }
            }
            marketActivityWorkOrder.setOrder_status("1");
            marketActivityWorkOrder.setLink_no("营销活动签约环节");
            //RedRollList rl = redRollListService.getRedRollList(id); //查询工单信息
            marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);//更改工单

            //发起新的环节
            MarketWorkOrderLink order = new MarketWorkOrderLink();
            //存环节表
            order.setLink_name("营销活动工单签约");//环节名称
            // order.setLink_name("营销活动工单评估");//环节名称
            order.setLink_state("0");//环节状态 0.进行中 1.已完成
            order.setCreator_boss(user.getBossUserName());
            order.setCreator_id(String.valueOf(user.getRowNo()));
            order.setCreator_name(user.getEmployeeName()); //发起人
            order.setStart_time(new Date());//发起时间
            order.setMarket_work_id(marketActivityWorkOrder.getId());//绑定营销活动工单编号
            marketActivitiesService.addMarketWorkOrderLink(order);
            //合同附件
            if (!StringUtils.isEmpty(id1)) {
                if (id1 != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] attachmentjson = id1.split(",");
                    if (attachmentjson.length > 0) {
                        for (int i = 0; i < attachmentjson.length; i++) {
                            //System.out.println("合同附件=="+attachmentjson[i]);
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(order.getId());
                            sa.setAttachmentId(attachmentjson[i]);
                            sa.setLink("CONTRACT");
                            marketActivitiesService.saveSandA(sa);
                        }
                    }
                }
            }
            //授权书
            if (!StringUtils.isEmpty(id2)) {
                if (id2 != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] attachmentjson = id2.split(",");
                    if (attachmentjson.length > 0) {
                        for (int i = 0; i < attachmentjson.length; i++) {
                            //System.out.println("授权书=="+attachmentjson[i]);
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(order.getId());
                            sa.setAttachmentId(attachmentjson[i]);
                            sa.setLink("AUTHORIZATION");
                            marketActivitiesService.saveSandA(sa);
                        }
                    }
                }
            }
            //经办人身份证
            if (!StringUtils.isEmpty(id3)) {
                if (id3 != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] attachmentjson = id3.split(",");
                    if (attachmentjson.length > 0) {
                        for (int i = 0; i < attachmentjson.length; i++) {
                            //System.out.println("经办人身份证=="+attachmentjson[i]);
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(order.getId());
                            sa.setAttachmentId(attachmentjson[i]);
                            sa.setLink("HANDLER");
                            marketActivitiesService.saveSandA(sa);
                        }
                    }
                }
            }

            //发起新的审批
            String node = "";
            if ("QX".equals(role)) {
                node = "ROLE_QXYW";
            } else if ("SGS".equals(role)) {
                node = "ROLE_DSBM";
            } else if ("SZK".equals(role)) {
                node = "ROLE_QXYW";
            }
            Map<String, String> map = new HashMap<>();
            map.put("node", node);
            //String processId = jbpmUtil.startPIByKey(node, map).getId();
            String processId = jbpmUtil.startPIByKey("ActivityWorkOrderTwo", map).getId();
            SystemUser USER = systemUserService.getUserInfoRowNo(userId);
            bpms_riskoff_service.setBpms_riskoff_process(order.getId(), processId, 1, user);
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            this.bpms_riskoff_service.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "营销活动签约工单发起人", this.user.getRowNo(), this.user);
            String taskid = this.bpms_riskoff_service.setBpms_riskoff_task(processId, null, 1, "SH", task.getActivityName(), USER.getRowNo(), this.user);
            WaitTask waitTask = new WaitTask();
            waitTask.setName("[营销活动签约]" + marketActivityWorkOrder.getOrder_name());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/ActiveSignContractApproval.jsp?repair=" + order.getId() + "&task=" + taskid);
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(marketActivityWorkOrder.MarketActivityWorkOrder);
            waitTask.setTaskId(taskid);
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "审批提交成功，已提交至：" + USER.getEmployeeName() + " 进行审批！"));
//            Write(returnPars(1, "", "审批提交成功，已提交至： 进行审批！"));
        } catch (Exception e) {
            e.printStackTrace();
            Write(returnPars(-1, "", "操作失败，待办信息异常！"));
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 活动评估工单审批完成 通知代办关闭
     */
    public void closeWaitActiveWorkTwo() {
        try {
            String waitId = getString("waitId");//待办id
            String id = getString("repair");
            //String role = getString("role");
            //Integer userId = getInteger("userId");
            Bpms_riskoff_task btask = bpms_riskoff_service.getBpms_riskoff_taskByStatus(id, user.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);//获取待办信息
            if (btask != null && wt != null) {
                bpms_riskoff_service.updateBpms_riskoff_task("完成", 2, btask.getId());
                waitTaskService.updateWait(wt, this.getRequest());
                MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
                String market_work_id = marketWorkOrderLink.getMarket_work_id();//环节工单获取营销活动信息id
                MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(market_work_id);
                marketActivityWorkOrder.setOrder_status("1");
                marketActivityWorkOrder.setLink_no("营销活动办理环节");
                //RedRollList rl = redRollListService.getRedRollList(id); //查询工单信息
                //marketWorkOrderLink.setLink_state("1");
                marketWorkOrderLink.setEnd_time(new Date());
                marketActivitiesService.upDataMarketWorkOrderLink(marketWorkOrderLink);//更改环节为已完成
                marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);//更改工单
                //发起新的环节
                MarketWorkOrderLink order = new MarketWorkOrderLink();
                //存环节表
                order.setLink_name("营销活动工单办理");//环节名称
                order.setLink_state("0");//环节状态 0.进行中 1.已完成
                order.setCreator_boss(user.getBossUserName());
                order.setCreator_id(String.valueOf(user.getRowNo()));
                order.setCreator_name(user.getEmployeeName()); //发起人
                order.setStart_time(new Date());//发起时间
                order.setMarket_work_id(marketActivityWorkOrder.getId());//绑定营销活动工单编号
                marketActivitiesService.addMarketWorkOrderLink(order);
                Write(returnPars(1, "", "操作成功!"));
            } else {
                Write(returnPars(-1, "", "操作失败，未查询到对应的待办或任务信息！"));
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write(returnPars(-1, "", "操作失败，待办信息异常！"));
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 营销工单
     * 流程退回
     */
    public void ReturnUpdateActiveWork() {
        try {
            String id = getString("repair");// 工单id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("TaskId");// 任务表id
            //DecimalFormat decimalFormat = new DecimalFormat("0");
            MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);//环节
            String market_work_id = marketWorkOrderLink.getMarket_work_id();//环节工单获取营销活动信息id
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(market_work_id);
            marketActivityWorkOrder.setOrder_status("2");
            //String id = getString("repair");
            DecimalFormat decimalFormat = new DecimalFormat("0");
            //MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(id);

            //判断工单类别 0.集客综合通信 1.存量统存 order_category
            if (marketWorkOrderLink.getLink_name().equals("营销活动工单签约")) {
                if (marketActivityWorkOrder.getOrder_category().equals("0")) {
                    //算钱，预占
                    MarketQuotaIniallzation companyName = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(marketActivityWorkOrder.getDeptStr());
                    companyName.setPreemptedAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyName.getPreemptedAmount())
                                            - Double.parseDouble(marketActivityWorkOrder.getActual_fee_amount())
                            )
                            //Delivery_amount
                    );//预占金额-
                    companyName.setRemainAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyName.getRemainAmount())
                                            + Double.parseDouble(marketActivityWorkOrder.getActual_fee_amount())
                            )
                    );//剩余总额度+
                    marketActivitiesService.UpdateMarketQuotaIniallzation(companyName);
                }
            }
            //判断为存量统存
            if (marketActivityWorkOrder.getOrder_category().equals("1")) {
                marketActivitiesService.upDataMarketSystemGroup(marketActivityWorkOrder.getStock_system_id());
            }
            marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);//更改工单
            SystemUser USER = this.systemUserService.getUserInfoRowNo(Integer.parseInt(marketWorkOrderLink.getCreator_id()));
            bpms_riskoff_service.updateBpms_riskoff_task(opinion, 2, taskId);
            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                waitTaskService.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            WaitTask waitTask = new WaitTask();
            waitTask.setName("[退回]" + marketActivityWorkOrder.getOrder_name());
            waitTask.setCreationTime(new Date());
            waitTask.setUrl("jsp/MarketActivitiesView/ActiveWorkReturn.jsp?repair=" + marketWorkOrderLink.getId());
            waitTask.setState(WaitTask.HANDLE);
            waitTask.setHandleUserId(USER.getRowNo());
            waitTask.setHandleUserName(USER.getEmployeeName());
            waitTask.setHandleLoginName(USER.getLoginName());
            waitTask.setCreateUserId(user.getRowNo());
            waitTask.setCreateUserName(user.getEmployeeName());
            waitTask.setCreateLoginName(user.getLoginName());
            waitTask.setCode(marketActivityWorkOrder.MarketActivityWorkOrder);
            waitTask.setTaskId("");
            this.waitTaskService.saveWait(waitTask, this.getRequest());
            Write(returnPars(1, "", "工单退回成功！工单已退回至: " + USER.getEmployeeName()));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "工单退回失败！"));
            throw new RuntimeException(" 给事务回滚，自定义1");
        }
    }

    /**
     * 营销活动工单作废
     */
    public void invalidActiveWork() {
        try {
            String id = getString("repair");// 工单id
            String waitId = getString("waitId");// 待办id
            MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
            Bpms_riskoff_process process = bpms_riskoff_service.getbpms_riskoff_processBizid(marketWorkOrderLink.getId());
            String market_work_id = marketWorkOrderLink.getMarket_work_id();//环节工单获取营销活动信息id
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(market_work_id);
            DecimalFormat decimalFormat = new DecimalFormat("0");
            //判断是不是集客综合通信
//            if (marketActivityWorkOrder.getOrder_category().equals("0")) {
//                MarketQuotaIniallzation companyName = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(marketActivityWorkOrder.getDeptStr());
//                companyName.setPreemptedAmount(
//                        decimalFormat.format(
//                                Double.parseDouble(companyName.getPreemptedAmount())
//                                        + Double.parseDouble(marketActivityWorkOrder.getDelivery_amount())
//                        )
//                        //Delivery_amount
//                );//预占金额+
//                companyName.setRemainAmount(
//                        decimalFormat.format(
//                                Double.parseDouble(companyName.getRemainAmount())
//                                        - Double.parseDouble(marketActivityWorkOrder.getDelivery_amount())
//                        )
//                );//剩余总额度—
//                marketActivitiesService.UpdateMarketQuotaIniallzation(companyName);
//            }
            marketActivityWorkOrder.setOrder_status("-1");
            //RedRollList rl = redRollListService.getRedRollList(id); //查询工单信息
            marketWorkOrderLink.setLink_state("1");
            marketWorkOrderLink.setEnd_time(new Date());
            marketActivitiesService.upDataMarketWorkOrderLink(marketWorkOrderLink);//更改环节为已完成
            marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);//更改工单

            WaitTask wt = waitTaskService.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                waitTaskService.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            jbpmUtil.deleteProcessInstance(process.getProcess_sign());// 删除流程
            Write(returnPars(1, "", "工单作废成功！"));
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("错误信息：" + e.getMessage(), e);
            Write(returnPars(-1, "", "工单作废失败！"));
        }
    }


    /**
     * id查询营销活动工单信息和环节信息
     */
    public void findMarketActiviLinktyById() {
        Result r = new Result();
        try {
            String id = getString("id");
            JSONObject pars = new JSONObject();
            //MarketWorkOrderLink marketWorkOrderLink = marketActivitiesService.getMarketWorkOrderLink(id);
            //String market_work_id = marketWorkOrderLink.getMarket_work_id();//获取营销活动信息id
            MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(id);
//            if(marketActivityWorkOrder==null){
//                pars.put("order",marketActivityWorkOrder);
//            }else {
            pars.put("order", JSONHelper.SerializeWithNeedAnnotationDateFormats(marketActivityWorkOrder));
//            }
            List<Map<String, String>> marketActiviLinktyById = marketActivitiesService.findMarketActiviLinktyById(id);
            //System.out.println("营销活动工单信息==="+JSONHelper.Serialize(marketActivityWorkOrder));

            pars.put("market", marketActiviLinktyById);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(pars);
            //System.out.println("数据==>"+r.toString());
            Write(r.toString());
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("NO");
        }
    }


    /**
     * 根据集团280查询
     */
    public void findMarketGroupByUnitId() {
        try {
            String unit_id = getString("unit_id");
            //根据集团280查询
            MarketGroupStatistics marketGroupStatistics = marketActivitiesService.getMarketGroupStatistics(unit_id);
            this.Write(JSONHelper.SerializeWithNeedAnnotation(marketGroupStatistics));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("NO");
        }
    }


//    public Result getUnitInfo(String unitId, String login_no) {
//        JSONObject bodyContent = new JSONObject();
//        bodyContent.put("LOGIN_NO", login_no);
//        bodyContent.put("UNIT_ID", Long.parseLong(unitId));
//        String paras = ESBReqMsgUtil.packMsgByRoute("15", "11", bodyContent);
//        if (isES) {
//            //正式服务器
//            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38 + "com_sitech_Government_atom_inter_IGovernmentCapitalSvc_getUnitInfo", paras, "UTF-8");
//        }
//
//        String resultStr = CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_38 + "com_sitech_Government_atom_inter_IGovernmentCapitalSvc_getUnitInfo", paras);
//        Result result = HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
//        logger.info("===>:" + result);
//        return result;
//    }


    /**
     * login_no  BOSSS工号
     * unitId  集团编码
     */
    public void getGroupData() {
        String unitId = getString("unit_id");
        //user.getBossUserName();
        Result r = new Result();
        Result result = UnitAccountInfoSrv.getInstance().getUnitInfo(unitId, user.getBossUserName());
        //Result result = UnitAccountInfoSrv.getInstance().getUnitInfo(unitId, user.getBossUserName());
        //System.out.println("集团统付数据==" + result);
        if (result.getCode() == 200) {
            JSONObject json = JSONObject.fromObject(result.getData());
            JSONObject root_ = JSONObject.fromObject(json.getString("ROOT"));
            if ("OK".equals(root_.getString("RETURN_MSG"))) {
                JSONObject out_data = root_.getJSONObject("OUT_DATA");
                JSONObject info = out_data.getJSONObject("INFO");
                //System.out.println("info==" + info);
                JSONArray contract_list = info.getJSONArray("CONTRACT_LIST");
                //System.out.println("contract_list==" + contract_list);
                //Write(contract_list.toString());
                r.setCode(ResultCode.SUCCESS);
                r.setMessage("成功");
                r.setData(contract_list);
                System.out.println(r.toString());
                Write(r.toString());
            } else {
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("");
                Write(r.toString());
            }
        }
    }

    /**
     * 营销活动业务办理
     */
    public void activeHandleBusiness() {
        Result r = new Result();
        String id = getString("repair");//营销活动id
        String bearer_number = getString("bearer_number");//承载号码
        String one_time_amount = getString("one_time_amount");//本次送费金额
        String one_deposit_amount = getString("one_deposit_amount");//本次办理金额
        String pay_mode = getString("pay_mode");//是否为浮动交费
        String active_code = getString("active_code");//营销活动代码

        //获取营销活动信息
        MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(id);
        DecimalFormat decimalFormat = new DecimalFormat("0");

        if (Double.parseDouble(marketActivityWorkOrder.getActual_fee_amount()) < (Double.parseDouble(one_time_amount) * 100)) {
            //System.out.println("送费金额已超总送费金额");
            Write(returnPars(-1, "", "操作失败！送费金额已超总送费金额"));
            return;
        } else {
            if (marketActivityWorkOrder.getSent_amount() == null) {
                //发起新的环节
                MarketWorkOrderLink link = new MarketWorkOrderLink();
                //存环节表
                link.setLink_name("营销活动工单办理");//环节名称
                link.setLink_state("0");//环节状态 0.进行中 1.已完成
                link.setCreator_boss(user.getBossUserName());
                link.setCreator_id(String.valueOf(user.getRowNo()));
                link.setCreator_name(user.getEmployeeName()); //发起人
                link.setStart_time(new Date());//发起时间
                link.setMarket_work_id(marketActivityWorkOrder.getId());//绑定营销活动工单编号
                marketActivitiesService.addMarketWorkOrderLink(link);
                //System.out.println("营销活动业务办理数据==>" + JSONHelper.Serialize(marketActivityWorkOrder));
            }
            try {
                //调用boss接口
                Result result = marketActivitiesService.cfmMarketingActivitiesHandle(bearer_number, pay_mode, one_deposit_amount, active_code, user.getBossUserName());
                if (result.getCode() == 200) {
                    JSONObject json = JSONObject.fromObject(result.getData());
                    JSONObject root = JSONObject.fromObject(json.get("ROOT"));
                    //JSONObject json = JSONObject.fromObject("{\"ROOT\":{\"RETURN_MSG\":\"OK\",\"RETURN_CODE\":0,\"USER_MSG\":\"OK\",\"OUT_DATA\":{\"ORDER_ID\":\"OS19211201106981707\",\"PASS_FLAG\":\"Y\"},\"DETAIL_MSG\":\"OK\",\"RUN_IP\":\"**************\",\"PROMPT_MSG\":\"\"}}");
                    //JSONObject root = JSONObject.fromObject(json.get("ROOT"));
                    //System.out.println("root=="+root);
                    if (root.getString("RETURN_CODE").equals("0")) {
                        //判断是否有订单号节点
                        JSONObject out_data = root.getJSONObject("OUT_DATA");
                        if (out_data.has("ORDER_ID") && out_data.getString("PASS_FLAG").equals("Y")) {
                            //存推送数据
                            //办理状态 market_state
                            //办理金额 market_money
                            //market_user_id;//办理人工号
                            //market_order_id;//办理工单号（BOSS端返回）
                            //;//推送BOSS端返回信息
                            //market_activity_id;//关联营销活动工单id
                            MarketBossWorkOrder marketGroupStatistics = new MarketBossWorkOrder();
                            marketGroupStatistics.setMarket_state("1");
                            marketGroupStatistics.setMarket_money(one_deposit_amount);//办理金额元
                            marketGroupStatistics.setMarket_user_id(String.valueOf(user.getRowNo()));
                            marketGroupStatistics.setMarket_order_id(out_data.getString("ORDER_ID"));
                            marketGroupStatistics.setMarket_activity_id(id);
                            marketActivitiesService.addMarketBossWorkOrder(marketGroupStatistics);

                            marketGroupStatistics.setMarket_date(new Date());
                            JSONObject data = new JSONObject();
                            data.put("out_data", root.getString("OUT_DATA"));
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("YES");
                            r.setData(data);
                            //修改工单信息
                            if (marketActivityWorkOrder.getSent_amount() == "" || marketActivityWorkOrder.getSent_amount() == null) {
                                marketActivityWorkOrder.setSent_amount(
                                        decimalFormat.format(
                                                Double.parseDouble("0")
                                                        + Double.parseDouble(one_time_amount) * 100
                                        )
                                );
                            } else {
                                marketActivityWorkOrder.setSent_amount(
                                        decimalFormat.format(
                                                Double.parseDouble(marketActivityWorkOrder.getSent_amount())
                                                        + Double.parseDouble(one_time_amount) * 100
                                        )
                                );
                            }
                            MarketActivityWorkOrder order = marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);
                            //判断送费使用额度
                            if (order.getSent_amount().equals(order.getActual_fee_amount())) {
                                order.setOrder_status("0");
                                MarketActivityWorkOrder workOrder = marketActivitiesService.UpdateMarketActivityWorkOrder(order);
                                List<MarketWorkOrderLink> marketLinkById = marketActivitiesService.getMarketLinkById(workOrder.getId());
                                for (int i = 0; i < marketLinkById.size(); i++) {
                                    if (marketLinkById.get(i).getLink_state().equals("0")) {
                                        marketLinkById.get(i).setLink_state("1");
                                        marketLinkById.get(i).setEnd_time(new Date());
                                        marketActivitiesService.upDataMarketWorkOrderLink(marketLinkById.get(i));
                                    }
                                }
                                //修改预占金额
                                MarketQuotaIniallzation companyName = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(workOrder.getDeptStr());
                                companyName.setPreemptedAmount(
                                        decimalFormat.format(
                                                Double.parseDouble(companyName.getPreemptedAmount())
                                                        - Double.parseDouble(order.getSent_amount())
                                        )
                                        //Delivery_amount
                                );//预占金额-
                                companyName.setComentTotalAmount(
                                        decimalFormat.format(
                                                Double.parseDouble(companyName.getComentTotalAmount())
                                                        + Double.parseDouble(order.getSent_amount())
                                        )
                                );//已使用额度+
                                marketActivitiesService.UpdateMarketQuotaIniallzation(companyName);
                            }
                        } else {
                            MarketBossWorkOrder marketGroupStatistics = new MarketBossWorkOrder();
                            marketGroupStatistics.setMarket_state("-1");
                            marketGroupStatistics.setMarket_money(one_deposit_amount);//办理金额元
                            marketGroupStatistics.setMarket_user_id(String.valueOf(user.getRowNo()));
                            marketGroupStatistics.setMarket_return_msg(root.getString("OUT_DATA"));
                            marketGroupStatistics.setMarket_activity_id(id);
                            marketActivitiesService.addMarketBossWorkOrder(marketGroupStatistics);
                            //System.out.println("错误");
                            JSONObject data = new JSONObject();
                            //JSONObject out_data = JSONObject.fromObject(root.get("OUT_DATA"));
                            JSONObject mean_all = JSONObject.fromObject(out_data.get("MEAN_ALL"));
                            //JSONObject.fromObject(mean_all.get("LIMIT_INFO"));
                            JSONArray limit_info = mean_all.getJSONArray("LIMIT_INFO");
                            //NOTE
                            List notes = new ArrayList<>();
                            for (int i = 0; i < limit_info.size(); i++) {
                                JSONObject jsonObject = JSONObject.fromObject(limit_info.get(i));
                                String note = jsonObject.getString("NOTE");
                                notes.add(note);
                            }
                            data.put("NOTES", notes);
                            r.setCode(ResultCode.UNAUTHORIZED);
                            r.setMessage("NO");
                            r.setData(data);
                        }
                    } else {
                        MarketBossWorkOrder marketGroupStatistics = new MarketBossWorkOrder();
                        marketGroupStatistics.setMarket_state("-1");
                        marketGroupStatistics.setMarket_money(one_deposit_amount);//办理金额元
                        marketGroupStatistics.setMarket_user_id(String.valueOf(user.getRowNo()));
                        marketGroupStatistics.setMarket_return_msg(root.getString("RETURN_MSG"));
                        marketGroupStatistics.setMarket_activity_id(id);
                        marketActivitiesService.addMarketBossWorkOrder(marketGroupStatistics);
                        JSONObject data = new JSONObject();
                        data.put("return_msg", root.getString("RETURN_MSG"));
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("NO");
                        r.setData(data);
                    }
                } else {
                    r.setCode(ResultCode.FAIL);
                    r.setMessage("NO");
                    r.setData("调用接口失败");
                }
                Write(r.toString());
            } catch (Exception e) {
                logger.info("营销活动推送Boss接口错误信息==>" + e);
                r.setCode(ResultCode.INTERNAL_SERVER_ERROR);
                r.setMessage("NO");
                r.setData("调用接口失败");
                //e.printStackTrace();
                Write(r.toString());
                throw new RuntimeException("事务回滚");
            }
            //Write(returnPars(1, "", "操作成功！"));
        }
    }


    /**
     * 营销活动业务完成
     */
    public void activeCompleteBusiness() {
        Result r = new Result();
        String id = getString("repair");//营销活动id
        //获取营销活动信息
        MarketActivityWorkOrder marketActivityWorkOrder = marketActivitiesService.getMarketActivityWorkOrder(id);
        DecimalFormat decimalFormat = new DecimalFormat("0");
        try {
            if (marketActivityWorkOrder.getSent_amount() == null) {
                //发起新的环节
                MarketWorkOrderLink link = new MarketWorkOrderLink();
                //存环节表
                link.setLink_name("营销活动工单办理");//环节名称
                link.setLink_state("0");//环节状态 0.进行中 1.已完成
                link.setCreator_boss(user.getBossUserName());
                link.setCreator_id(String.valueOf(user.getRowNo()));
                link.setCreator_name(user.getEmployeeName()); //发起人
                link.setStart_time(new Date());//发起时间
                link.setMarket_work_id(marketActivityWorkOrder.getId());//绑定营销活动工单编号
                marketActivitiesService.addMarketWorkOrderLink(link);
                //System.out.println("营销活动业务办理数据==>" + JSONHelper.Serialize(marketActivityWorkOrder));
            }
            //完成工单
            marketActivityWorkOrder.setOrder_status("0");
            MarketActivityWorkOrder order = marketActivitiesService.UpdateMarketActivityWorkOrder(marketActivityWorkOrder);
            List<MarketWorkOrderLink> marketLinkById = marketActivitiesService.getMarketLinkById(order.getId());
            //完成环节
            for (int i = 0; i < marketLinkById.size(); i++) {
                if (marketLinkById.get(i).getLink_state().equals("0")) {
                    marketLinkById.get(i).setLink_state("1");
                    marketLinkById.get(i).setEnd_time(new Date());
                    marketActivitiesService.upDataMarketWorkOrderLink(marketLinkById.get(i));
                }
            }
            //判断 0.集客综合通信 1.存量统存
            if (order.getOrder_category().equals("0")) {
                //MarketActivityWorkOrder workOrder = marketActivitiesService.UpdateMarketActivityWorkOrder(order);
                //修改预占金额
                MarketQuotaIniallzation companyName = marketActivitiesService.getMarketQuotaIniallzationByCompanyName(order.getDeptStr());
                companyName.setPreemptedAmount(
                        decimalFormat.format(
                                Double.parseDouble(companyName.getPreemptedAmount())
                                        - Double.parseDouble(order.getActual_fee_amount())
                        )
                        //Delivery_amount
                );//预占金额-
                if(order.getSent_amount()==null||order.getSent_amount()==""){
                    companyName.setComentTotalAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyName.getComentTotalAmount())
                                            + Double.parseDouble("0")
                            )
                    );//已使用额度+
                }else {
                    companyName.setComentTotalAmount(
                            decimalFormat.format(
                                    Double.parseDouble(companyName.getComentTotalAmount())
                                            + Double.parseDouble(order.getSent_amount())
                            )
                    );//已使用额度+
                }
                marketActivitiesService.UpdateMarketQuotaIniallzation(companyName);
            }
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("YES");
            r.setData("营销活动完成");
            Write(r.toString());
        } catch (Exception e) {
            logger.info("营销活动完成错误==>" + e);
            r.setCode(ResultCode.FAIL);
            r.setMessage("NO");
            r.setData("营销活动完成失败");
            //e.printStackTrace();
            Write(r.toString());
            throw new RuntimeException("事务回滚");
        }
        //Write(returnPars(1, "", "操作成功！"));
//        }
    }

    /**
     * 根据号码查询是否为浮动缴费
     * login_no 工号
     * means_id 营销活动代码
     * phone 电话号码
     */
    public void IntChkQueryFloat() {
        String active_code = getString("active_code");
        String phone = getString("phone");
        Result result = marketActivitiesService.IntChkQueryFloat(user.getBossUserName(), active_code, phone);
        //System.out.println("查询数据=="+result.toString());
        Write(result.toString());
    }


}
