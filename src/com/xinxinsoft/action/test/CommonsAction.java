package com.xinxinsoft.action.test;

import java.io.BufferedReader;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Map;

import com.xinxinsoft.utils.CodecUtils;
import org.json.JSONArray;
import org.json.JSONObject;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.eipManagementPer.EIPSystemDept;
import com.xinxinsoft.entity.core.eipManagementPer.EIPSystemDeptUser;
import com.xinxinsoft.entity.core.eipManagementPer.EIPSystemUser;
import com.xinxinsoft.service.appOpenService.NoResApplyService;
import com.xinxinsoft.service.core.OwnDepartmentsService;
import com.xinxinsoft.service.core.dept.SystemDeptService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.utils.EncryptionUtils;
import com.xinxinsoft.utils.MD5;
import com.xinxinsoft.utils.easyh.JSONHelper;

/**
 * 测试或部分系统帮助功能调用action类
 *
 * <AUTHOR> 二〇一九年十月二十四日 17:41:41
 */
public class CommonsAction extends BaseAction {

    /**
     *
     */
    private static final long serialVersionUID = -6095302141107097513L;
    private SystemDeptService deptService;
    private OwnDepartmentsService ownService;
    private SystemUserService userService;
    private NoResApplyService noResApplyService;

    public SystemDeptService getDeptService() {
        return deptService;
    }

    public void setDeptService(SystemDeptService deptService) {
        this.deptService = deptService;
    }

    public OwnDepartmentsService getOwnService() {
        return ownService;
    }

    public void setOwnService(OwnDepartmentsService ownService) {
        this.ownService = ownService;
    }

    public SystemUserService getUserService() {
        return userService;
    }

    public void setUserService(SystemUserService userService) {
        this.userService = userService;
    }


    public NoResApplyService getNoResApplyService() {
        return noResApplyService;
    }

    public void setNoResApplyService(NoResApplyService noResApplyService) {
        this.noResApplyService = noResApplyService;
    }


    public void startApply() {
        //noResApplyService.startApply(user,"test0002","");
        writeText("OK");
    }

    public void completeApply() {
        String tid = getString("tid");
        String outCome = getString("outCome");
        //noResApplyService.completeApply(tid, outCome, outCome, user, user);
        writeText("OK");
    }

    /**
     * 获取用户按钮
     */
    public void findBtnList() {
        String tid = getString("tid");
        Map<String, String> map = noResApplyService.getOperBtn(tid);
        Write(JSONHelper.SerializeWithNeedAnnotation(map));
    }

    /**
     * 同步用户信息
     */
    public void SyncUser() {
        String path = getString("Jsonfileurl");

        if (!path.equals("")) {
            StringBuffer strbuffer = new StringBuffer();

            File file = new File(path);
            if (!file.exists()) {
                System.out.println("Can't Find " + file);
            } else {
                try {
                    FileInputStream fis = new FileInputStream(file);
                    System.out.println(file.length());
                    InputStreamReader inputStreamReader = new InputStreamReader(
                            fis, "UTF-8");
                    BufferedReader in = new BufferedReader(inputStreamReader);

                    String str;
                    while ((str = in.readLine()) != null) {
                        strbuffer.append(str); // new String(str,"UTF-8")
                    }
                    in.close();
                } catch (IOException e) {
                    e.getStackTrace();
                }
                if (!strbuffer.equals("")) {
                    JSONObject dataJson = new JSONObject(strbuffer.toString());// 创建一个包含原始json串的json对象
                    //System.out.println("JSON===>" + dataJson);
                    JSONArray Departments = dataJson
                            .getJSONArray("Departments");// 获取部门信息
                    for (int i = 0; i < Departments.length(); i++) {
                        getEIPDepartmentData(Departments.getJSONObject(i));
                    }
                }
            }

        }
    }

    /**
     * 全量：解析人员相关信息
     *
     * @param DepartmentsDate
     * @date : SmapOID，SmapUID 字段，在2018-07-02 17:32 新增
     */
    public void getEIPDepartmentData(JSONObject DepartmentsDate) {
        JSONObject obj = DepartmentsDate;
        Integer Department_No = obj.getInt("Department_No");//
        Integer Department_Parent_No = obj.getInt("Department_Parent_No");
        String Department_Name = obj.getString("Department_Name");
        String Company_Code = obj.getString("Company_Code");
        Integer Department_Level = obj.getInt("Department_Level");
        Integer Visible = obj.getBoolean("Visible") ? 1 : 0;// SmapOID
        String Department_Order = obj.getString("Department_Order");

        String SmapOID = obj.getString("SmapOID");

        EIPSystemDept eipdept = new EIPSystemDept();
        eipdept.setDepartmentNo(Department_No);
        eipdept.setDepartmentParentNo(Department_Parent_No);
        eipdept.setDepartmentName(Department_Name);
        eipdept.setCompanyCode(Company_Code);
        eipdept.setDepartmentLevel(Department_Level);
        eipdept.setVisible(Visible);
        eipdept.setDepartmentOrder(Department_Order);
        eipdept.setSmapOID(SmapOID);
        chageEIPDept(eipdept);

        try {
            // 人员：
            if (obj.get("Employees") != null) {
                JSONArray childEmpl = obj.getJSONArray("Employees");
                if (null != childEmpl && childEmpl.length() > 0) {
                    for (int i = 0; i < childEmpl.length(); i++) {
                        JSONObject objChile = childEmpl.getJSONObject(i);
                        EIPSystemUser eipuser = new EIPSystemUser();
                        Integer rownop = objChile.getInt("RowNo");
                        String loginNamep = objChile.getString("Login_Name");
                        String namep = objChile.getString("Employee_Name");
                        eipuser.setRowNo(rownop);
                        eipuser.setLoginName(loginNamep);
                        eipuser.setEmployeeNo(objChile.getString("Employee_No"));
                        eipuser.setEmployeeName(namep);
                        eipuser.setMobile(objChile.getString("Mobile"));

                        eipuser.setMail(objChile.getString("Mail") != null ? CodecUtils
                                .desEncryp(
                                        objChile.getString("Mail"),
                                        EncryptionUtils.KEY_MW
                                                + MD5.MD5(
                                                EncryptionUtils.KEY_MW)
                                                .toUpperCase()) : "");
                        eipuser.setEmployeeStatus((objChile
                                .getBoolean("Employee_Status") == true ? 0 : 4));
                        eipuser.setEmployeeLevel(String.valueOf(objChile
                                .getInt("Employee_Level")));
                        eipuser.setLoginPwd("21218cca77804d2ba1922c33e0151105");//
                        eipuser.setSmapOID(objChile.getString("SmapOID"));
                        eipuser.setSmapUID(objChile.getString("SmapUID") != null ? CodecUtils
                                .desEncryp(
                                        objChile.getString("SmapUID"),
                                        EncryptionUtils.KEY_MW
                                                + MD5.MD5(
                                                EncryptionUtils.KEY_MW)
                                                .toUpperCase()) : "");
                        eipuser.setUserType(objChile.getInt("UserType"));
                        eipuser.setUserTypeName(objChile
                                .getString("UserTypeName"));
                        chageEIPUser(eipuser);
                        if (objChile.get("OwnDepartments") != null) {
                            // 人员部门关系：
                            JSONArray childOwnDepa = objChile
                                    .getJSONArray("OwnDepartments");
                            if (null != childOwnDepa
                                    && childOwnDepa.length() > 0) {
                                for (int j = 0; j < childOwnDepa.length(); j++) {
                                    JSONObject objChileOwnDepa = childOwnDepa
                                            .getJSONObject(j);
                                    EIPSystemDeptUser detpuser = new EIPSystemDeptUser();
                                    detpuser.setIsMaindpt((objChileOwnDepa
                                            .getBoolean("IsMainDpt") + ""));
                                    Integer nop = objChileOwnDepa
                                            .getInt("DepartmentId");
                                    detpuser.setDepartmentNo(nop);
                                    detpuser.setOrderNo(objChileOwnDepa
                                            .getString("Employee_Order"));
                                    detpuser.setRowNo(eipuser.getRowNo());
                                    chagEIPDeptUser(detpuser);
                                }
                            }
                        }
                    }
                }
            }
        } catch (Exception e) {
            e.getStackTrace();
        }
        // /解析子部门：
        try {
            if (obj.get("Children") != null) {
                JSONArray children2 = obj.getJSONArray("Children");
                if (null != children2 && children2.length() > 0) {
                    for (int i = 0; i < children2.length(); i++) {
                        getEIPDepartmentData(children2.getJSONObject(i));
                    }
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * eip:备用表：EIPSystemDeptUser
     *
     * @param user
     */
    public void chagEIPDeptUser(EIPSystemDeptUser deptuser) {
        userService.addEIPDeptUser(deptuser);
    }

    /**
     * eip:备用表：EIPSystemDept
     *
     * @param user
     */
    public void chageEIPDept(EIPSystemDept dept) {
        deptService.addEIPDept(dept);
    }

    /**
     * eip:备用表：EIPSystemUser
     *
     * @param user
     */
    public void chageEIPUser(EIPSystemUser user) {
        userService.addEIPSystemUser(user);
    }
}
