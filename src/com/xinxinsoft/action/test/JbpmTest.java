package com.xinxinsoft.action.test;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.common.DefaultAction;

public class JbpmTest extends DefaultAction {

	private Logger logger = LoggerFactory.getLogger(JbpmTest.class);
	/**
	 * 
	 */
	private static final long serialVersionUID = 4915436068515029223L;
	private JbpmUtil jbpmUtil;
	private TransferJBPMUtils transferJBPMUtils;

	// 发布流程
	public void pushJbpm() {
		try {
			// com/xinxinsoft/jbpmProcess/Transfer.jpdl.xml
			String jpbmXML = getString("jbpmXML");
			String id = jbpmUtil.deployNew(jpbmXML);
			writeText("发布的JPBM流程ID为：" + id);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			writeText("发布流程JPBM流程失败");
		}

	}

	// 启动流程
	public void startJbpm() {
		try {
			String processName = getString("processName");
			Map<String, String> map = new HashMap<String, String>();
			map.put("decisionKey", "APPLY");
			map.put("decisionVal", "QX");
			// map.put("USERID", "111");
			String processId = jbpmUtil.startPIByKey(processName, map).getId();
			writeText("启动流程成功流程ID为：" + processId);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			writeText("启动流程失败");
		}
	}

	// 删除流程所有实例
	public void deleteJbpm() {
		try {
			String processName = getString("processName");
			System.out.println(processName);
			List<String> list = jbpmUtil
					.findProcessDefinitionIdsByName(processName);
			String resMsg = "";
			if (list != null) {
				for (int i = 0; i < list.size(); i++) {
					// writeText("通过流程名称获取大流程ID为:"+list.get(i));
					jbpmUtil.deleteDeploymentCascade(list.get(i));
					resMsg += list.get(i) + "|";
				}
				writeText("通过流程名称获取大流程ID为:" + resMsg + "///删除成功!");
			} else {
				writeText("N");
			}
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			writeText("删除流程信息失败");
		}
	}

	// 更具流程ID删除流程
	public void killProcess() {
		try {
			String pid = getString("processId");
			jbpmUtil.deleteProcessInstance(pid);
			writeText("删除流程实例成功！" + pid);
		} catch (Exception e) {
			// TODO: handle exception
			e.printStackTrace();
			writeText("删除流程实例失败！！！");
		}
	}

	/**
	 * 执行流程
	 */
	public void completeJpbm() {
		String pid = getString("processId");
		String t = getString("t");
		// 获取任务对象
		Task task = jbpmUtil.getTaskService().createTaskQuery()
				.processInstanceId(pid).uniqueResult();
		logger.info("当前流程节点======》" + task.getId());

		if ("市公司政企部经理".equals(task.getActivityName())) {
			Map<String, String> map = new HashMap<String, String>();
			map.put("decisionKey", "DSDMDO");
			map.put("decisionVal", "YES");
			jbpmUtil.completeTask(task.getId(), map);
		} else {
			if (t.isEmpty()) {
				jbpmUtil.completeTask(task.getId());
			} else {
				jbpmUtil.completeTask(task.getId(), t);
			}
		}

		writeText("当前流程节点======》" + task.getActivityName());
	}

	// 获取线条值
	public void findTransition() {
		String pid = getString("processId");
		if (!"".equals(pid) && pid!=null) {
			// 获取任务对象
			try {
				logger.info("所查询的流程ID：=======》" + pid);
				Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
				Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
				JSONArray jArray = new JSONArray();
				logger.info("当前流程节点====》" + task.getActivityName());
				for (String outcome : setlist) {
					JSONObject obj = new JSONObject();
					obj.put("transitionName", outcome);
					logger.info("当前可执行的流程走向====》" + outcome);
					jArray.add(obj);
				}
				writeText(jArray.toString());
			} catch (Exception e) {
				// TODO: handle exception
				logger.info("流程信息获取异常====》" + e);
				writeText("流程信息获取异常");
			}
			
		}else{
			writeText("流程ID为空");
		}
	}

	// 流程查看
	public void findTask() {
		try {
			String pid = getString("processId");
			// 获取任务对象
			Task task = jbpmUtil.getTaskService().createTaskQuery()
					.processInstanceId(pid).uniqueResult();
			writeText(task.getName());
		} catch (Exception e) {
			// TODO: handle exception
		}
	}

	public JbpmUtil getJbpmUtil() {
		return jbpmUtil;
	}

	public void setJbpmUtil(JbpmUtil jbpmUtil) {
		this.jbpmUtil = jbpmUtil;
	}

	public TransferJBPMUtils getTransferJBPMUtils() {
		return transferJBPMUtils;
	}

	public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
		this.transferJBPMUtils = transferJBPMUtils;
	}

}
