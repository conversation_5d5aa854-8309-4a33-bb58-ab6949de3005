package com.xinxinsoft.action.sign;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.sign.Seal;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.sign.SealService;
import com.xinxinsoft.service.sign.SignService;
import com.xinxinsoft.utils.GetFile;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.StringHelper;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;
import net.sf.json.JSONArray;

import java.text.DateFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

public class EomSealAction extends BaseAction {

    private final static String FTP_URL = Config.getString("FTP_URL");//渠道编码（由签章服务平台注册每 个系统的渠道编码）
    private SealService sealService;
    private SignService signService;
    private AttachmentService attachmentService;
    private SystemUserService systemUserService;
    public SealService getSealService() {
        return sealService;
    }

    public void setSealService(SealService sealService) {
        this.sealService = sealService;
    }

    public SignService getSignService() {
        return signService;
    }

    public void setSignService(SignService signService) {
        this.signService = signService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    /**
     * 查询个人信息
     */
    //列表展示
    @Override
    public PageResponse doList(PageRequest page) {
        String sealName=getString("sealName");
        String departName=getString("departName");
        return sealService.dolist(sealName,departName,page);
    }

    /**
     * 查询页面列表
     */
    public void findByPage() {
        Map<String,Object> resultMap= new HashMap<>();
        Integer pageNo = getInteger("pageNo");
        Integer pageSize = getInteger("pageSize");
        LayuiPage page = new LayuiPage(pageNo, pageSize);
        String json = "";
        try {
            Map<String,Object> paramMap = new HashMap<>();
            String departNumber = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
            paramMap.put("departNumber",departNumber);
            paramMap.put("applyNo","zqdzyz");
            paramMap.put("typeCode","2");
            resultMap = signService.getSealImgList(paramMap);

            JSONArray array = (JSONArray)resultMap.get("datas");
            page.setCount(array.size());
            List list = new ArrayList();
            try {
                for (int i = 0; i < array.size(); i++) {
                    net.sf.json.JSONObject jsonObject = (net.sf.json.JSONObject) array.getJSONObject(i);
                    list.add(jsonObject);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
            page.setData(list);
            json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }

//        try {
//            Integer pageNo = getInteger("pageNo");
//            Integer pageSize = getInteger("pageSize");
//            LayuiPage page = new LayuiPage(pageNo, pageSize);
//            String sealName = getString("sealName");
//            String applyNo = getString("applyNo");
//            String departName = getString("departName");
//            String departNumber = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
//            List<Map<String, String>> listRole = systemUserService.getPriorityTwo(user.getRowNo()+"");
//            boolean flag=false;
//            for(int i=0;i<listRole.size();i++){
//                if("系统管理员".equals(listRole.get(i).get("CNAME"))){
//                    flag=true;
//                    break;
//                }
//            }
//            if(flag){
//                page=sealService.findByPage(page,sealName,applyNo,departName,null);
//            }else{
//                page=sealService.findByPage(page,sealName,applyNo,departName,departNumber);
//            }
//            Collection collection = page.getData();
//            if(null != collection){
//                List<Seal>   list   =    new  ArrayList<Seal>(collection);
//                for (Seal seal : list) {
//                    seal.setCreateTime(dealDateFormat(seal.getCreateTime()));
//                }
//            }
//
//            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
//            Write(json);
//        }
//        catch (Exception e) {
//            e.printStackTrace();
//            Write("NO");
//        }
    }
    /**
     * 印章图片增加
     */
    public void addSeal(){
        Map<String,Object> resultMap= new HashMap<>();

        try {
            String applyNo = getString("applyNo");
            String sealName=getString("sealName");
            String departNumber=getString("departNumber");
            String departName=getString("departName");
            String userAccount=getString("userAccount");
            String userName=getString("userName");
            String typeCode=getString("typeCode");
            String attachmentId=getString("attachmentId");
            if(!StringHelper.isBlank(applyNo) || !StringHelper.isBlank(sealName) || !StringHelper.isBlank(departNumber) || !StringHelper.isBlank(departName)
                    || !StringHelper.isBlank(userName) || !StringHelper.isBlank(userAccount) ||!StringHelper.isBlank(typeCode)){

                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","请检查参数！");
                Write(JSONHelper.Serialize(resultMap));
            }
            String[] split = attachmentId.split(",");
            if(split.length > 1){//判断上传文件有多少个
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","请检查图片个数！");
                Write(JSONHelper.Serialize(resultMap));
            }
            Attachment attachment = attachmentService.getAttachmentById(split[0]);
            if(null == attachment){
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","请检查是否有图片！");
                Write(JSONHelper.Serialize(resultMap));
            }
            StorageCfg storageCfg= attachmentService.queryStorageCfg();
            String base64= GetFile.getImgStr(storageCfg.getFileName() + attachment.getAttachmentUrl());
            Map<String,Object> map = new HashMap<>();
            map.put("applyNo",applyNo);
            map.put("sealName",sealName);
            map.put("departNumber",departNumber);
            map.put("departName",departName);
            map.put("userAccount",userAccount);
            map.put("userName",userName);
            map.put("typeCode",typeCode);
            map.put("base64",base64);
            map.put("attachmentId",split[0]);
            resultMap = signService.addSeal(map);//拼接参数取调用接口
            if(null == resultMap){
                resultMap.put("resp_code",1);
                resultMap.put("resp_msg","接口调用异常！");
            }
        } catch (Exception e) {
            resultMap.put("resp_code",1);
            resultMap.put("resp_msg","印章添加异常！");
            e.printStackTrace();
        }
        Write(JSONHelper.Serialize(resultMap));
    }

    /**
     * 印章修改
     */
    public void updateSealImg(){
        Map<String,Object> resultMap= new HashMap<>();
        try {
            String sealId = getString("sealId");//获取印章ID
            String attachmentId=getString("attachmentId");//获取文件ID
            if(null == attachmentId || "".equals(attachmentId)){
                resultMap.put("resp_code",0);
                resultMap.put("resp_msg","");
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            String[] split = attachmentId.split(",");
            String tempID=split[0];
            Attachment attachment = attachmentService.getAttachmentById(tempID);
            if(null == attachment){
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","请重新上传印章");
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            StorageCfg storageCfg= attachmentService.queryStorageCfg();
            String base64= GetFile.getImgStr(storageCfg.getFileName() + attachment.getAttachmentUrl());//读取上传图片文件转成base64
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("sealId",sealId);
            paramMap.put("base64",base64);
            paramMap.put("attachmentId",split[0]);
            resultMap = signService.updateSealImg(paramMap);
            if(null == resultMap){
                resultMap.put("resp_code",1);
                resultMap.put("resp_msg","接口调用异常！");
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("resp_code",1);
            resultMap.put("resp_msg","印章修改异常！");
            e.printStackTrace();
        }
        Write(JSONHelper.Serialize(resultMap));
//        Map<String,Object> resultMap= new HashMap<>();
//        try {
//            String sealId=getString("sealId");//获取印章ID
//            String attachmentId=getString("attachmentId");//获取文件ID
//            Seal seal = sealService.selectSealById(sealId);
//            if(null == seal){
//                resultMap.put("resp_code",-1);
//                resultMap.put("resp_msg","请检查印章是否正确！");
//                Write(JSONHelper.Serialize(resultMap));
//                return ;
//            }
//            String[] split = attachmentId.split(",");
//            String tempID=split[0];
//            if("".equals(tempID)){//修改印章，如果不改源文件传过来的ID为""，所以判断
//                Attachment attachment = attachmentService.getAttachmentById(seal.getAttachmentId());//根据印章数据文件ID查询文件
//                if(null != attachment){//如果能查出来则证明没有修改文件
//                    resultMap.put("resp_code",0);
//                    resultMap.put("resp_msg","修改成功！");
//                    Write(JSONHelper.Serialize(resultMap));
//                    return ;
//                }else{//查不出来有可能数据库没有加进去，所以查询到base64重新生成图片，路径生成根据上传文件路径而来
//                    String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
//                    String  ftpUrl= FileUpload.getFtpURL()+urlDate;
//                    String pixstr =FileUpload.getFilePix(seal.getSealFilepath());
//                    Long time = System.currentTimeMillis();
//
//                    boolean boo = GetFile.generateImage(seal.getBase64(),ftpUrl+time + pixstr);
//                    if(boo){//根据base64文件生成成功保存数据库，如果失败则需要手动处理
//                        final Attachment attachmentEntity = new Attachment();
//                        attachmentEntity.setAttachmentName(time + pixstr);// 防重名
//                        attachmentEntity.setAttachmentUrl(urlDate+time + pixstr);
//                        attachmentEntity.setUploadDate(new Date());
//                        attachmentEntity.setRealName(seal.getSealName()+pixstr);
//                        attachmentEntity.setUploadUser((SystemUser) this
//                                .getRequest()
//                                .getSession()
//                                .getAttribute(
//                                        SystemConfig.instance().getSessionItems()
//                                                .getCurrentLoginUser()));
//                        String attId = this.attachmentService
//                                .addEntity(attachmentEntity);
//                        seal.setAttachmentId(attId);
//                        sealService.saveOrUpdateSeal(seal);//修改印章里面的文件ID
//
//                        resultMap.put("resp_code",0);
//                        resultMap.put("resp_msg","修改成功！");
//                        Write(JSONHelper.Serialize(resultMap));
//                        return ;
//                    }else{
//                        resultMap.put("resp_code",0);
//                        resultMap.put("resp_msg","异常！请联系管理员");
//                        Write(JSONHelper.Serialize(resultMap));
//                        return ;
//                    }
//                }
//            }else{//修改印章，如果根据印章原文件ID能查出数据，证明页面没有删除源文件
//                Attachment attachment = attachmentService.getAttachmentById(seal.getAttachmentId());
//                if(null != attachment){
//                    resultMap.put("resp_code",-1);
//                    resultMap.put("resp_msg","请检查图片个数！");
//                    Write(JSONHelper.Serialize(resultMap));
//                    return ;
//                }
//            }
//            if(split.length > 1){//判断上传的文件个数
//                resultMap.put("resp_code",-1);
//                resultMap.put("resp_msg","请检查图片个数！");
//                Write(JSONHelper.Serialize(resultMap));
//            }
//            Attachment attachment = attachmentService.getAttachmentById(tempID);//根据文件ID查询文件
//            if(null == attachment){
//                resultMap.put("resp_code",-1);
//                resultMap.put("resp_msg","请检查是否有图片！");
//                Write(JSONHelper.Serialize(resultMap));
//                return ;
//            }
//            String base64= GetFile.getImgStr(FTP_URL + attachment.getAttachmentUrl());//读取上传图片文件转成base64
//            Map<String,Object> paramMap = new HashMap<>();
//            paramMap.put("sealId",sealId);
//            paramMap.put("base64",base64);
//            paramMap.put("attachmentId",split[0]);
//            paramMap.put("userName",seal.getUserName());
//            resultMap = signService.updateSealImg(paramMap);
//            if(null == resultMap){
//                resultMap.put("resp_code",1);
//                resultMap.put("resp_msg","接口调用异常！");
//            }
//        } catch (Exception e) {
//            resultMap.put("resp_code",1);
//            resultMap.put("resp_msg","印章修改异常！");
//            e.printStackTrace();
//        }
//        Write(JSONHelper.Serialize(resultMap));
    }

    /**
     * 根据ID获取数据
     *
     * 查看印章图片
     */
    public void getSealInfo(){
        Map<String,Object> resultMap= new HashMap<>();
        String sealId = getString("sealId");
        try {
            Map<String,Object> paramMap = new HashMap<>();
            String departNumber = user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
            paramMap.put("departNumber",departNumber);
            paramMap.put("applyNo","zqdzyz");
            paramMap.put("typeCode","2");
            paramMap.put("sealId",sealId);
            resultMap = signService.getSealImgList(paramMap);
            if(null == resultMap){
                resultMap.put("resp_code",1);
                resultMap.put("resp_msg","接口调用异常！");
            }else{
                JSONArray array = (JSONArray)resultMap.get("datas");
                net.sf.json.JSONObject jsonObject = (net.sf.json.JSONObject) array.getJSONObject(0);
                resultMap.put("datas",jsonObject);
            }
        } catch (Exception e) {
            e.printStackTrace();
            resultMap.put("resp_code",1);
            resultMap.put("resp_msg","异常！");
        }

        Write(JSONHelper.Serialize(resultMap));
//        Map<String,Object> resultMap= new HashMap<>();
//        try {
//            String sealId=getString("sealId");
//            Seal seal = sealService.selectSealById(sealId);
//            if(null == seal){
//                resultMap.put("resp_code",-1);
//                resultMap.put("resp_msg","请检查印章是否正确！");
//                Write(JSONHelper.Serialize(resultMap));
//            }
//            resultMap.put("resp_code",0);
//            resultMap.put("datas",seal);
//            Write(JSONHelper.Serialize(resultMap));
//        } catch (Exception e) {
//            resultMap.put("resp_code",1);
//            resultMap.put("resp_msg","印章修改异常！");
//            e.printStackTrace();
//        }
//        Write(JSONHelper.Serialize(resultMap));
    }

    /**
     * 查询图片
     */
    public void getAttachmentById(){
        String sealId=getString("id");
        Seal seal = sealService.selectSealById(sealId);
        if(null == seal.getAttachmentId() || "".equals(seal.getAttachmentId())){
            Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(""));
        }
        List<Map<String,String>> list =sealService.selectAttachmentList(seal.getAttachmentId());
//        Attachment attachment = attachmentService.getAttachmentById(seal.getAttachmentId());
        Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(list));
    }
    public static String dealDateFormat(String oldDate) {
        Date date1 = null;
        DateFormat df2 = null;
        try {
            DateFormat df = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss");
            Date date = df.parse(oldDate);
            SimpleDateFormat df1 = new SimpleDateFormat ("EEE MMM dd HH:mm:ss Z yyyy", Locale.UK);
            date1 = df1.parse(date.toString());
            df2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        } catch (ParseException e) {

            e.printStackTrace();
        }
        return df2.format(date1);
    }
}
