package com.xinxinsoft.action.sign;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.sign.Cert;
import com.xinxinsoft.service.sign.SignService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.StringHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

import java.util.HashMap;
import java.util.Map;

public class EomCertAction extends BaseAction {

    private SignService signService;

    public SignService getSignService() {
        return signService;
    }

    public void setSignService(SignService signService) {
        this.signService = signService;
    }

    /**
     * 查询证书信息
     */
    //列表展示
    @Override
    public PageResponse doList(PageRequest page) {
        String userName=getString("userName");
        String mobilephone=getString("mobilephone");
        String departName=getString("departName");
        return signService.dolist(userName,mobilephone,departName,page);
    }

    /**
     * 查询文件列表
     */
    public void findByPage() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String userName = getString("userName");
            String mobilephone = getString("mobilephone");
            String departName = getString("departName");

            page=signService.findByPage(page,userName,mobilephone,departName);
            String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            Write(json);
        }
        catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }
    /**
     * 页面增加证书
     */
    public void addCert(){
        Map<String,Object> resultMap= new HashMap<>();
        try {
            String applyNo = getString("applyNo");
            String certName = getString("certName");
            String departNumber = getString("departNumber");
            String departName = getString("departName");
            String userName = getString("userName");
            String cardNumber = getString("cardNumber");
            String mobilephone = getString("mobilephone");
            String email = getString("email");
            String typeCode = getString("typeCode");//判断页面参数是否传完，未传完重新编辑
            if(!StringHelper.isBlank(applyNo) || !StringHelper.isBlank(certName) || !StringHelper.isBlank(departNumber) || !StringHelper.isBlank(departName)
                    || !StringHelper.isBlank(userName) || !StringHelper.isBlank(cardNumber) || !StringHelper.isBlank(mobilephone)|| !StringHelper.isBlank(email) ||!StringHelper.isBlank(typeCode)){

                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","请检查参数！");
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }

            if(!certName.contains("证书")){
                certName = certName + "证书";
            }
            Map<String,Object> map = new HashMap<>();
            map.put("applyNo",applyNo);
            map.put("certName",certName);
            map.put("departNumber",departNumber);
            map.put("departName",departName);
            map.put("userName",userName);
            map.put("cardNumber",cardNumber);
            map.put("mobilephone",mobilephone);
            map.put("email",email);
            map.put("typeCode",typeCode);

            Map<String, Object> certMap = signService.selectCert(map);//查询数据库是否有数据
            if(null != certMap){
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","该机构下该人已经申请过证书！");

                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            resultMap = signService.addCert(map);//拼接数据调用接口

            if(null == resultMap){
                resultMap.put("resp_code",1);
                resultMap.put("resp_msg","接口调用异常！");
            }
        } catch (Exception e) {
            resultMap.put("resp_code",1);
            resultMap.put("resp_msg","证书添加异常！");
            e.printStackTrace();
        }
        Write(JSONHelper.Serialize(resultMap));
    }

    /**
     * 页面证书注销
     */
    public void revokeCert(){
        Map<String,Object> resultMap= new HashMap<>();
        try {
            String certId = getString("certId");
            if(null == certId || "".equals(certId)){
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","参数错误！");
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            Cert cert = signService.selectCertById(certId);//查询数据库是否有证书，没有证书则返回
            if(null == cert){
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","证书查询错误，请检查");
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("applyNo",cert.getApplyNo());
            paramMap.put("departNumber",cert.getDepartNumber());
            paramMap.put("departName",cert.getDepartName());
            paramMap.put("userName",cert.getUserName());
            paramMap.put("cardNumber",cert.getCardNum());
            paramMap.put("mobilephone",cert.getMobilephone());
            paramMap.put("email",cert.getEmail());
            paramMap.put("typeCode",cert.getTypeCode());
            resultMap = signService.revokeCert(paramMap);//拼接参数调用接口
            if(null == resultMap){
                resultMap.put("resp_code",1);
                resultMap.put("resp_msg","接口调用异常！");
            }
        } catch (Exception e) {
            resultMap.put("resp_code",-1);
            resultMap.put("resp_msg","证书注销异常！");
            e.printStackTrace();
        }
        Write(JSONHelper.Serialize(resultMap));
    }

    /**
     * 证书更新
     */
    public void updateCert(){
        Map<String,Object> resultMap= new HashMap<>();
        try {
            String certId = getString("certId");
            if(null == certId || "".equals(certId)){
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","参数错误！");
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            Cert cert = signService.selectCertById(certId);
            if(null == cert){
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","证书查询错误，请检查");
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            Map<String,Object> paramMap = new HashMap<>();
            paramMap.put("applyNo",cert.getApplyNo());
            paramMap.put("certName",cert.getCertName());
            paramMap.put("departNumber",cert.getDepartNumber());
            paramMap.put("departName",cert.getDepartName());
            paramMap.put("userName",cert.getUserName());
            paramMap.put("cardNumber",cert.getCardNum());
            paramMap.put("mobilephone",cert.getMobilephone());
            paramMap.put("email",cert.getEmail());
            paramMap.put("typeCode",cert.getTypeCode());

            try {
                signService.deleteCert(paramMap);//逻辑删除本地数据
            } catch (Exception e) {
                resultMap.put("resp_code",-1);
                resultMap.put("resp_msg","本地证书删除异常！");
                e.printStackTrace();
                Write(JSONHelper.Serialize(resultMap));
                return ;
            }
            resultMap = signService.addCert(paramMap);//调用接口
            if(null == resultMap){//调用接口返回数据为null，则修改上一步删除的数据
                resultMap.put("resp_code",1);
                resultMap.put("resp_msg","接口调用异常！");
                signService.updateCertById(certId);
            }
        } catch (Exception e) {
            resultMap.put("resp_code",-1);
            resultMap.put("resp_msg","证书更新异常！");
            e.printStackTrace();
        }
        Write(JSONHelper.Serialize(resultMap));
    }
}
