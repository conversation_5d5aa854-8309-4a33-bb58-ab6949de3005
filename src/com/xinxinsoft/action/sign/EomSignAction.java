package com.xinxinsoft.action.sign;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.sign.Seal;
import com.xinxinsoft.entity.sign.Sign;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.sign.SealService;
import com.xinxinsoft.service.sign.SignService;
import com.xinxinsoft.utils.*;
import com.xinxinsoft.utils.common.FileUpload;
import net.sf.json.JSONArray;
import org.apache.commons.lang.StringUtils;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.util.*;

public class EomSignAction extends BaseAction {

    private final static String SIGN_DOWNLOADPDF = Config.getString("SIGN_DOWNLOADPDF");//#签章文件下载地址
    private final static String SIGN_UPLOADPDF = Config.getString("SIGN_UPLOADPDF");//#签章文件上传地址

    private SignService signServie;

    public SignService getSignServie() {
        return signServie;
    }

    public void setSignServie(SignService signServie) {
        this.signServie = signServie;
    }
    private SealService sealServie;

    public SealService getSealServie() {
        return sealServie;
    }

    public void setSealServie(SealService sealServie) {
        this.sealServie = sealServie;
    }

    private AttachmentService attachmentService; // 附件：
    private String fileFileName;
    private File file; //文件

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public String getFileFileName() {
        return fileFileName;
    }

    public void setFileFileName(String fileFileName) {
        this.fileFileName = fileFileName;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

    /**
     * 获取token
     */
    public void getToken(){
        Sign token = signServie.getToken();
        if(null == token){
            Write(JSONHelper.toJson("请检查,证书获取错误"));
        }
        Write(JSONHelper.toJson(token));
    }
    /**
     * 证书申请
     */
    public void addCert(){

        Map<String,Object> map=new HashMap<>();
        map.put("applyNo","lizhi_ya");//申请人账号
        map.put("certName","测试网络部印章申请");//证书名称
        map.put("departNumber","8256");//机构编码
        map.put("departName","网络部");//机构名称
        map.put("userName","雅安_宝兴_李志");//申请人姓名
        map.put("cardNumber","123456");//身份证号
        map.put("mobilephone","18000527723");//手机号
        map.put("email","<EMAIL>");//邮箱号
        map.put("typeCode","2");//证书类型，1 个人证书，2 机构证 书
        Map<String, Object> stringObjectMap = signServie.addCert(map);
        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("证书申请错误"));
        }
        Write(JSONHelper.Serialize(stringObjectMap));
    }

    /**
     * 证书注销
     */
    public void revokeCert(){
        Map<String,Object> map=new HashMap<>();
        map.put("applyNo","lizhi_ya");//申请人账号
        map.put("departNumber","8256");//机构编码
        map.put("departName","网络部");//机构名称
        map.put("userName","雅安_宝兴_李志");//申请人姓名
        map.put("cardNumber","123456");//身份证号
        map.put("mobilephone","18000527723");//手机号
        map.put("email","<EMAIL>");//邮箱号
        map.put("typeCode","2");//证书类型，1 个人证书，2 机构证 书
        Map<String, Object> stringObjectMap = signServie.revokeCert(map);
        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("证书注销错误"));
        }
        Write(JSONHelper.Serialize(stringObjectMap));
    }

    /**
     * 根据印章查询
     */
   public void getSealImgList(){
       Map<String,Object> maps=new HashMap<>();
       maps.put("channelNo","sc-eom");
       maps.put("departNumber","8256");//机构编码
       maps.put("sealId","");//印章 id（在签章服务平台系统申请 印章，返回的印章 id）--非必填
       maps.put("applyNo","lizhi_ya");//账号
       maps.put("typeCode","2");//章分类 1 个人章 2.机构章

       List<Seal> seals = sealServie.selectSeals(maps);

       Map<String,Object> map=new HashMap<>();

       map.put("departNumber","8256");//机构编码
       map.put("sealId","");//印章 id（在签章服务平台系统申请 印章，返回的印章 id）--非必填
       map.put("applyNo","lizhi_ya");//账号
       map.put("typeCode","2");
       Map<String, Object> stringObjectMap = signServie.getSealImgList(map);
       JSONArray array = (JSONArray)stringObjectMap.get("datas");

       try {
           for (int i = 0; i < array.size(); i++) {
               net.sf.json.JSONObject jsonObject = (net.sf.json.JSONObject) array.getJSONObject(i);
               String base641 = (String)jsonObject.get("base64");
               String sealId = (String)jsonObject.get("sealId");
               GetFile.generateImage(base641,"E:\\sftp\\boe\\systemFiles\\"+sealId+".png");
           }
       } catch (Exception e) {
           e.printStackTrace();
       }
       if(null == stringObjectMap){
           Write(JSONHelper.Serialize("印章查询错误"));
       }
       Write(JSONHelper.Serialize(stringObjectMap));
   }

    /**
     * 印章申请
     */
    public void addSeal(){
        String base64=GetFile.getImgStr("C:\\Users\\<USER>\\Desktop\\陈俊宏1.png");

        Map<String,Object> map = new HashMap<>();
        map.put("applyNo","lizhi_ya");//印章申请人账号
        map.put("sealName","测试网络部印章申请");//印章名称
        map.put("departNumber","8256");// 印章使用人机构编码
        map.put("departName","测试网络部印章申请");//印章使用人机构名称
        map.put("userAccount","lizhi_ya");//印章使用人账号
        map.put("userName","雅安_宝兴_李志");//印章使用人姓名
        map.put("typeCode","2");//章类型；1.个人章，2 机构章
        map.put("base64",base64);//印章图片 base64 字符串
//        map.put("sealId", UUIDUtil.getInstance().generatUUID(true));
//        user.getSystemDept().get(0).getSystemCompany().getCompanyCode();
//        user.getSystemDept().get(0).getSystemCompany().getCompanyName();
//        Seal seal=new Seal();
//        seal.setBase64(base64);
//        seal.setSealId(map.get("sealId")+"");
//        sealServie.saveOrUpdateSeal(seal);
        Map<String, Object> stringObjectMap = signServie.addSeal(map);

        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("印章申请错误"));
        }
        Write(JSONHelper.Serialize(stringObjectMap));
    }

    /**
     * 印章修改
     */
    public void updateSealImg(){
        Map<String,Object> map1=new HashMap<>();
        map1.put("departNumber","8256");//机构编码
        map1.put("sealId","0c6618a1aca540869c940e6629b3956c");//印章 id（在签章服务平台系统申请 印章，返回的印章 id）--非必填
        map1.put("applyNo","lizhi_ya");//账号
        Map<String, Object> getSealMap = signServie.getSealImgList(map1);
        Map<String,Object> map=new HashMap<>();
        String base64=GetFile.getImgStr("C:\\Users\\<USER>\\Desktop\\陈俊宏.png");
        map.put("sealId","733c4a13bcc14ae68226f3ae3f55a502");//印章 id（在签章服务平台系统申请 印章，返回的印章 id）--非必填
        map.put("base64",base64);//账号
        Map<String, Object> stringObjectMap = signServie.updateSealImg(map);
        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("印章修改错误"));
        }
        Write(JSONHelper.Serialize(stringObjectMap));
    }

    /**
     * 坐标签章
     */
    public void sealByXy(){
        Map<String,Object> map = new HashMap<>();
        map.put("departNumber","8256");//机构编码
        map.put("departName","测试网络部印章申请");//机构名称
        map.put("applyNo","lizhi_ya");//申请人账号
        map.put("userName","雅安_宝兴_李志");//申请人姓名
        map.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
        map.put("chapterType","2");//章类型；1.个人章，2 机构章
        map.put("xy","1,200,200");//坐标（page,x,y,指的在 PDF 中的 坐标；以 PDF 坐下角为 0 坐标，x 指距离左下角横向距离 x 像素，y 指距离左下角纵向距离 y 像素， page 指 pdf 页数；例如参数：1， 100,100 坐标指在 pdf 文档第一 页，距离左下角横向距离 100 像 素，距离左下角纵向距离 100 像素 的坐标）
        map.put("sealWidth",81);//非必填--》Integer 印章宽度，单位毫米，默认为 41 毫米。 注意，仅当印章类型为机构章时，该值有 效
        map.put("sealId","733c4a13bcc14ae68226f3ae3f55a502");//印章 id（生成章返回的编码）
        File file=new File("C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");

        if(checkFileSize(file,2,"M")){
            String buFile = GetFile.PDFToBase64(file);
            map.put("reqPdf",buFile);//Pdf 文件传输方式，fileUrl 通过 get 请求 获取文件，reqPdf 通过 base64 字符串 传输文件。两种方式二选一，如果同时传 值，优先使用 reqPdf
        }else{
            map.put("fileUrl",SIGN_DOWNLOADPDF+"C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");
            map.put("uploadUrl",SIGN_UPLOADPDF);
        }

        Map<String, Object> stringObjectMap = signServie.sealByXy(map);

        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("坐标签章错误"));
        }

        net.sf.json.JSONObject datas = net.sf.json.JSONObject.fromObject(stringObjectMap.get("datas"));
        //判断fileId，如果存在则去Attachment中查询数据，如果不存在则转换base64
        if(null == datas.get("fileId") || "".equals(datas.get("fileId")) ){
            Attachment attachment = attachmentService.getAttachmentById(datas.get("fileId").toString());
            Write(JSONHelper.Serialize("数据获取错误"));
        }
        String reBase64 = (String)datas.get("reBase64");
        GetFile.base64StringToPdf(reBase64,"E:\\sftp\\boe\\systemFiles\\"+ UUIDUtil.getInstance().generatUUID(false) +".pdf");
        Write(JSONHelper.Serialize(stringObjectMap));
    }
    /**
     * 关键字签章
     */
    public void sealByKey(){
        Map<String,Object> map = new HashMap<>();
        map.put("departNumber","8256");//机构编码
        map.put("departName","测试网络部印章申请");//机构名称
        map.put("applyNo","lizhi_ya");//申请人账号
        map.put("userName","雅安_宝兴_李志");//申请人姓名
        map.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
        map.put("chapterType","2");//章类型；1.个人章，2 机构章
        map.put("key","中国移动通信集团");//关键字内容（PDF 内容中的关键字）
        map.put("offsetX",0);//非必填--》Integer 横向偏移量，单位毫米，正数向右偏移， 负数向左偏移
        map.put("offsetY",0);//非必填--》Integer 纵向偏移量，单位毫米，正数向上偏移， 负数向下偏移
        map.put("sealId","733c4a13bcc14ae68226f3ae3f55a502");//印章 id（生成章返回的编码）
        File file=new File("C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");
        if(checkFileSize(file,2,"M")){
            String buFile = GetFile.PDFToBase64(file);
            map.put("reqPdf",buFile);//Pdf 文件传输方式，fileUrl 通过 get 请求 获取文件，reqPdf 通过 base64 字符串 传输文件。两种方式二选一，如果同时传 值，优先使用 reqPdf
        }else{
            map.put("fileUrl",SIGN_DOWNLOADPDF+"C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");
            map.put("uploadUrl",SIGN_UPLOADPDF);
        }
        Map<String, Object> stringObjectMap = signServie.sealByKey(map);
        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("关键字签章错误"));
        }
        net.sf.json.JSONObject datas = net.sf.json.JSONObject.fromObject(stringObjectMap.get("datas"));
        if(null == datas.get("fileId") || "".equals(datas.get("fileId")) ){
            Attachment attachment = attachmentService.getAttachmentById(datas.get("fileId").toString());
            Write(JSONHelper.Serialize("数据获取错误"));
        }
        String reBase64 = (String)datas.get("reBase64");
        GetFile.base64StringToPdf(reBase64,"E:\\sftp\\boe\\systemFiles\\"+ UUIDUtil.getInstance().generatUUID(false) +".pdf");
        Write(JSONHelper.Serialize(stringObjectMap));
    }
    /**
     * 骑缝签章
     */
    public void sealByStraddle(){
        Map<String,Object> map = new HashMap<>();
        map.put("departNumber","8256");//机构编码
        map.put("departName","测试网络部印章申请");//机构名称
        map.put("applyNo","lizhi_ya");//申请人账号
        map.put("userName","雅安_宝兴_李志");//申请人姓名
        map.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
        map.put("chapterType","2");//章类型；1.个人章，2 机构章
        map.put("leftRight","1");//1:靠左，2. 靠右（PDF 做骑缝签章 有靠左签章与靠右签章）

        map.put("sealWidth",41);//非必填--》Integer 印章宽度，单位毫米，默认为 41 毫米。 注意，仅当印章类型为机构章时，该值有 效
        map.put("sealHeight",100);//骑缝章高度（章距离 pdf 顶部的距 离）
        map.put("sealId","733c4a13bcc14ae68226f3ae3f55a502");//印章 id（生成章返回的编码）
//        File file=new File("C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");
        File file=new File("E:\\sftp\\boe\\systemFiles\\63e0949c-06b9-456c-97ed-6334ffb9d1b8_q.pdf");
        if(checkFileSize(file,3,"M")){
            String buFile = GetFile.PDFToBase64(file);
            map.put("reqPdf",buFile);//Pdf 文件传输方式，fileUrl 通过 get 请求 获取文件，reqPdf 通过 base64 字符串 传输文件。两种方式二选一，如果同时传 值，优先使用 reqPdf
        }else{
            map.put("fileUrl",SIGN_DOWNLOADPDF+"C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");
            map.put("uploadUrl",SIGN_UPLOADPDF);
        }

        Map<String, Object> stringObjectMap = signServie.sealByStraddle(map);
        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("骑缝签章错误"));
        }
        net.sf.json.JSONObject datas = net.sf.json.JSONObject.fromObject(stringObjectMap.get("datas"));
        if(null == datas.get("fileId") || "".equals(datas.get("fileId")) ){
            Attachment attachment = attachmentService.getAttachmentById(datas.get("fileId").toString());
            Write(JSONHelper.Serialize("数据获取错误"));
        }

        String reBase64 = (String)datas.get("reBase64");
        GetFile.base64StringToPdf(reBase64,"E:\\sftp\\boe\\systemFiles\\"+ UUIDUtil.getInstance().generatUUID(false) +"_q.pdf");
        Write(JSONHelper.Serialize(stringObjectMap));
    }
    /**
     * 多坐标批量签章
     */
    public void multiSealByXy(){
        Map<String,Object> paramMap=new HashMap<>();
        Map<String,Object> map=new HashMap<>();
        Map<String,Object> map2=new HashMap<>();
        Map<String,Object> map3=new HashMap<>();
        List<Map<String,Object>> listMap=new ArrayList<Map<String, Object>>();
        map.put("departNumber","8256");//机构编码
        map.put("departName","测试网络部印章申请");//机构名称
        map.put("applyNo","lizhi_ya");//申请人账号
        map.put("userName","雅安_宝兴_李志");//申请人姓名
        map.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
        map.put("chapterType","2");//章类型；1.个人章，2 机构章
        map.put("xy","1,200,200");//坐标（page,x,y,指的在 PDF 中的 坐标；以 PDF 坐下角为 0 坐标，x 指距离左下角横向距离 x 像素，y 指距离左下角纵向距离 y 像素， page 指 pdf 页数；例如参数：1， 100,100 坐标指在 pdf 文档第一 页，距离左下角横向距离 100 像 素，距离左下角纵向距离 100 像素 的坐标）
        map.put("sealWidth",81);//非必填--》Integer 印章宽度，单位毫米，默认为 41 毫米。 注意，仅当印章类型为机构章时，该值有 效
        map.put("sealId","733c4a13bcc14ae68226f3ae3f55a502");//印章 id（生成章返回的编码）

        map2.put("departNumber","8256");//机构编码
        map2.put("departName","测试网络部印章申请");//机构名称
        map2.put("applyNo","lizhi_ya");//申请人账号
        map2.put("userName","雅安_宝兴_李志");//申请人姓名
        map2.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
        map2.put("chapterType","2");//章类型；1.个人章，2 机构章
        map2.put("xy","28,300,300");//坐标（page,x,y,指的在 PDF 中的 坐标；以 PDF 坐下角为 0 坐标，x 指距离左下角横向距离 x 像素，y 指距离左下角纵向距离 y 像素， page 指 pdf 页数；例如参数：1， 100,100 坐标指在 pdf 文档第一 页，距离左下角横向距离 100 像 素，距离左下角纵向距离 100 像素 的坐标）
        map2.put("sealWidth",21);//非必填--》Integer 印章宽度，单位毫米，默认为 41 毫米。 注意，仅当印章类型为机构章时，该值有 效
        map2.put("sealId","733c4a13bcc14ae68226f3ae3f55a502");//印章 id（生成章返回的编码）

        map3.put("departNumber","8256");//机构编码
        map3.put("departName","测试网络部印章申请");//机构名称
        map3.put("applyNo","lizhi_ya");//申请人账号
        map3.put("userName","雅安_宝兴_李志");//申请人姓名
        map3.put("signseal","1");//2 只签名不签章。1.既签名又签章（既签 名，又签章指的是既有数字签名，也有印 章图片）
        map3.put("chapterType","2");//章类型；1.个人章，2 机构章
        map3.put("xy","29,400,400");//坐标（page,x,y,指的在 PDF 中的 坐标；以 PDF 坐下角为 0 坐标，x 指距离左下角横向距离 x 像素，y 指距离左下角纵向距离 y 像素， page 指 pdf 页数；例如参数：1， 100,100 坐标指在 pdf 文档第一 页，距离左下角横向距离 100 像 素，距离左下角纵向距离 100 像素 的坐标）
        map3.put("sealWidth",11);//非必填--》Integer 印章宽度，单位毫米，默认为 41 毫米。 注意，仅当印章类型为机构章时，该值有 效
        map3.put("sealId","733c4a13bcc14ae68226f3ae3f55a502");//印章 id（生成章返回的编码）
        listMap.add(map);
        listMap.add(map2);
        listMap.add(map3);
        paramMap.put("xySet",listMap);
        File file=new File("C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");
        if(checkFileSize(file,2,"M")){
            String buFile = GetFile.PDFToBase64(file);
            paramMap.put("reqPdf",buFile);
        }else{
            map.put("fileUrl",SIGN_DOWNLOADPDF+"C:\\Users\\<USER>\\Desktop\\二级平台-电子签章能力平台接口规范v1.2@20200706(1).pdf");
            map.put("uploadUrl",SIGN_UPLOADPDF);
        }
        Map<String, Object> stringObjectMap = signServie.multiSealByXy(paramMap);
        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("多坐标批量签章错误"));
        }
        net.sf.json.JSONObject datas = net.sf.json.JSONObject.fromObject(stringObjectMap.get("datas"));
        if(null == datas.get("fileId") || "".equals(datas.get("fileId")) ){
            Attachment attachment = attachmentService.getAttachmentById(datas.get("fileId").toString());
            Write(JSONHelper.Serialize("数据获取错误"));
        }
        String reBase64 = (String)datas.get("reBase64");
        GetFile.base64StringToPdf(reBase64,"E:\\sftp\\boe\\systemFiles\\"+ UUIDUtil.getInstance().generatUUID(false) +"_d.pdf");

        Write(JSONHelper.Serialize(stringObjectMap));
    }

    /**
     * 签名验签--》该服务接口主要验证PDF是否被串改
     */
    public void signCheck(){
        Map<String,Object> map = new HashMap<>();
        File file=new File("E:\\sftp\\boe\\systemFiles\\07872656-3cfa-47ff-be9b-ea5fffd16d1f.pdf");
        String buFile = GetFile.PDFToBase64(file);
        map.put("base64File",buFile);//文件
        Map<String, Object> stringObjectMap = signServie.signCheck(map);
        if(null == stringObjectMap){
            Write(JSONHelper.Serialize("签名验签错误"));
        }
        net.sf.json.JSONObject datas = net.sf.json.JSONObject.fromObject(stringObjectMap.get("datas"));
        Write(JSONHelper.Serialize(stringObjectMap));
    }

    /**
     * 文件下载
     */
    public void downloadPdf() {
        Map<String,Object> map= new HashMap<>();
        String path = getString("path");
        OutputStream out = null;
        FileInputStream input = null;
        try {
            if (StringHelper.isBlank(path)) {
                input = new FileInputStream(path);
                HttpServletResponse response = getResponse();
                out = response.getOutputStream();
                String filename = new String(path.substring(path.lastIndexOf(File.separator) + 1, path.length()));
                response.setHeader("Content-Disposition", "attachment; filename=" +  filename);
                byte[] buffer = new byte[1024];
                int len = 0;
                while ((len = input.read(buffer)) != -1) {
                    out.write(buffer, 0, len);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            map.put("respCode","1");
            map.put("respMessage",e.getMessage());
            map.put("fileId","");
            Write(JSONHelper.Serialize(map));
        } finally {
            try {
                if (input != null) {
                    input.close();
                }
                if (out != null) {
                    out.close();
                }
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 文件上传
     */
    public void uploadPdf() {
        // 先将文件上传到服务器，放回参数至页面
        // 页面点击保存时，保存数据
        System.out.println("go in upload");
        Map<String,Object> map = new HashMap<>();
        try {
            //判断上传文件的名称是否为空
            if (file != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";

                String  ftpUrl=FileUpload.getFtpURL()+urlDate;

                File headPath = new File(ftpUrl);//获取文件夹路径
                if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                // fileFileName= new String(fileFileName.getBytes(), "UTF-8");
                String pixstr =FileUpload.getFilePix(fileFileName);
                if(StringUtils.isEmpty(pixstr)){
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, file, time + pixstr)) {

                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate+time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(fileFileName);
                    attachmentEntity.setDescribe("签章返回盖章文件");
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));

                    String attachmentId =this.attachmentService.addEntity(attachmentEntity);

                    map.put("respCode","00000");
                    map.put("respMessage","success");
                    map.put("fileId",attachmentId);
                    writeText(JSONHelper.Serialize(map));

                } else {
                    map.put("respCode","1");
                    map.put("respMessage","调用文件上传失败");
                    map.put("fileId","");
                    writeText(JSONHelper.Serialize(map));
                }
            } else {
                map.put("respCode","1");
                map.put("respMessage","文件为空，请检查文件");
                map.put("fileId","");
                writeText(JSONHelper.Serialize(map));
            }

        } catch (Exception e) {
            e.printStackTrace();
            map.put("respCode","1");
            map.put("respMessage",e.getMessage());
            map.put("fileId","");
            writeText(JSONHelper.Serialize(map));
        }
    }

    /**
     * 判断文件大小
     *
     * @param file
     *            文件
     * @param size
     *            限制大小
     * @param unit
     *            限制单位（B,K,M,G）
     * @return
     */
    public boolean checkFileSize(File file, int size, String unit) {
        long len = file.length();
        double fileSize = 0;
        if ("B".equals(unit.toUpperCase())) {
            fileSize = (double) len;
        } else if ("K".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1024;
        } else if ("M".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1048576;
        } else if ("G".equals(unit.toUpperCase())) {
            fileSize = (double) len / 1073741824;
        }
        if (fileSize > size) {
            return false;
        }
        return true;
    }
}
