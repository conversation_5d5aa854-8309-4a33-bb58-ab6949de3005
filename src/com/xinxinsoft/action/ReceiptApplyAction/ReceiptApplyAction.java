package com.xinxinsoft.action.ReceiptApplyAction;

import java.io.*;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.NumberFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.xinxinsoft.action.test.JbpmTest;
import com.xinxinsoft.entity.contractUniformity.ContractInfo;
import com.xinxinsoft.entity.receiptApplys.*;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.ESBReqMsgUtil;
import com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyAmountService;
import com.xinxinsoft.service.config.Config;
import com.xinxinsoft.utils.HDFS.utils.HDFSUtils;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.commons.lang.StringUtils;
import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.action.manualInvApply.ManualInvApplyAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.manualInvApply.ManualInvApply;
import com.xinxinsoft.entity.manualInvApply.ManualInvApplyDet;
import com.xinxinsoft.entity.transfer.TransferAccountInformation;
import com.xinxinsoft.entity.transfer.TransferCitiesData;
import com.xinxinsoft.entity.transfer.TransferInformation;
import com.xinxinsoft.entity.transfer.TransferTask;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.UrlConnection;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;


public class ReceiptApplyAction extends BaseAction {
    /**
     *
     */
    private static final Logger logger = Logger.getLogger(ReceiptApplyAction.class);
    private static final long serialVersionUID = 1L;

    //发布环境
    private static final String ESB_URL_172 = Config.getString("TEST_ESBWS_URL");
    //正式环境
    private static final String ESB_URL_38 =Config.getString("ESBWS_URL");
    private static Boolean isES = false;

    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp()) ||"*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }
    private String audit = Config.getString("AUDIT_INTERS_FJ_SWITCH");
    private WaitTaskService service;                // 代办
    private SystemUserService systemUserService;
    private ReceiptApplyService receiptApplyService;
    private TransferJBPMUtils transferJBPMUtils;
    private JbpmUtil jbpmUtil;
    private AttachmentService attachmentService;
    private File branchFile1;
    private String branchFile1FileName;
    private File file1;
    private String file1FileName;
    private File productsFile1;
    private String productsFile1FileName;

    @Resource(name = "ReceiptApplyAmountService")
    private ReceiptApplyAmountService receiptApplyAmountService;



    public void uploadFile() {
        try {
            //判断上传文件的名称是否为空
            if (branchFile1 != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                String ftpUrl = FileUpload.getFtpURL() + urlDate;
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                String pixstr = FileUpload.getFilePix(branchFile1FileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, branchFile1, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(branchFile1FileName);
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    writeText(urlDate + time + pixstr);
                    ///审计接口调用
                    if ("start".equals(audit)) {
                        final String request = DateUtil.getIpAddr(this.getRequest());
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                ///审计接口调用
                                CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                            }
                        }).start();
                    }
                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }

        } catch (Exception e) {
            e.printStackTrace();
            writeText("0");
        }
    }

    public void uploadFileTwo() {
        try {
            //判断上传文件的名称是否为空
            if (productsFile1 != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                String ftpUrl = FileUpload.getFtpURL() + urlDate;
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                String pixstr = FileUpload.getFilePix(productsFile1FileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, productsFile1, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(productsFile1FileName);
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    writeText(urlDate + time + pixstr);
                    ///审计接口调用
                    if ("start".equals(audit)) {
                        final String request = DateUtil.getIpAddr(this.getRequest());
                        new Thread(new Runnable() {
                            @Override
                            public void run() {
                                ///审计接口调用
                                CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                            }
                        }).start();
                    }
                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }

        } catch (Exception e) {
            e.printStackTrace();
            writeText("0");
        }
    }

    /**
     * 工单分页查询
     */
    public void queryReceiptApply() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            Integer tabindex = getInteger("tabindex");//状态 0处理中 1我创建 2我经手
            String number = getString("number");
            String title = getString("title");
            String groupCode = getString("groupCode");
            String state = getString("state");
            String LostEffectDate = getString("LostEffectDate");
            String LostEffectDatetwo = getString("LostEffectDatetwo");
            String phone = getString("phone");
            if (phone == null || phone.equals("null") || phone.equals("")) {
                user = this.user;
                page = receiptApplyService.findReceiptApplyList(page, number, title, groupCode, state, LostEffectDate, LostEffectDatetwo, user.getRowNo(), tabindex);
            } else {
                user = systemUserService.getUserByPhone(phone);
                page = receiptApplyService.findReceiptApplyPhone(page, user.getRowNo());
            }

            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 工单分页查询
     */
    public void queryReceiptApplyTwo() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            Integer tabindex = getInteger("tabindex");
            String number = getString("number");
            String title = getString("title");
            String groupCode = getString("groupCode");
            String state = getString("state");
            String LostEffectDate = getString("LostEffectDate");
            String LostEffectDatetwo = getString("LostEffectDatetwo");
            String code = getString("selectcon");
            SystemUser user = this.user;// 获取当前用户
            List list = receiptApplyService.findRoleByRowNo(user.getRowNo()); // 获取用户权限
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16") || (list.get(i).toString()).equals("2297")) {
                    flag = true;
                    break;
                }
            } // 获取公司编码
            //String pageStr = receiptApplyService.queryReceiptApplyTwo(page, number, title, groupCode, state, LostEffectDate, LostEffectDatetwo, user, tabindex, code);
            page = receiptApplyService.findQueryReceiptApply(page, number, title, groupCode, state, LostEffectDate, LostEffectDatetwo, user, tabindex, code,flag);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 账户分页查询
     * <p>
     * ,String serialNo,String applyNO,String contrctNo,String productNo,
     * SystemUser user,String companyCodes
     */
    public void queryReceiptApplyDet() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String serialNo = getString("serialNo");
            String applyNO = getString("applyNO");
            String contrctNo = getString("contrctNo");
            String productNo = getString("productNo");
            String companyCodes = getString("selectcon");
            SystemUser user = this.user;// 获取当前用户
            List list = receiptApplyService.findRoleByRowNo(user.getRowNo()); // 获取用户权限
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16") || (list.get(i).toString()).equals("2297")) {
                    flag = true;
                    break;
                }
            } // 获取公司编码
            String pageStr = receiptApplyService.queryReceiptApplyDet(page, serialNo, applyNO, contrctNo, productNo, user, companyCodes,flag);
            this.Write(pageStr);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 新增信息
     */
    public void add() {
        try {
            logger.info("----->新增正负补收");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
            String role = getString("role");//发起人节点
            String id = getString("id");
            String userid = getString("userid");//下一步处理人id
            String waitId = getString("waitId");//待办id
            String attachmentId = getString("attachmentId");//附件id
            String jsondata = getString("json");//列表信息集合
            String title = getString("title");//标题
            //String isTota = getString("isTota");//是否申请金额调整
            String groupCode = getString("groupCode");//集团280
            String totalAmount = getString("totalAmount");//总金额
            String appMemo = getString("appMemo");//申请说明
            Integer defRidio = getInteger("defRidio");//工单业务类型
            String taxRate = getString("taxRate");//填报税率
            String numberType = getString("numberType");//账户类型
            /*String branchFileUrl =getString("branchFileUrl");
            String customerLetterFileUrl =getString("customerLetterFileUrl");
            String systemFileUrl =getString("systemFileUrl");
            String productsFileUrl =getString("productsFileUrl");*/
            //String branchsFilePath = getString("branchsFilePath");//分公司领导集体决策纪要文件路径
            //String productsFilePath = getString("productsFilePath");//集团产品一体化流程文件路径
            DecimalFormat decimalFormat = new DecimalFormat("0");
            String IBM = "";
            List<Object[]> sone = receiptApplyService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = getStringDatetwo(new Date());
            List<SystemDept> deptList = user.getSystemDept();
            String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();
            String departmentName = deptList.get(0).getDepartmentName();
            ReceiptApply receiptApply = new ReceiptApply();
            receiptApply.setApplyNO(IBM + sateTime);
            receiptApply.setCompanyCode(Integer.parseInt(companyCode));
            receiptApply.setRegion(departmentName);
            receiptApply.setCreateDate(new Date());
            receiptApply.setCreator(user.getRowNo() + "");
            receiptApply.setCreatorName(user.getEmployeeName());
            receiptApply.setState("1");
            receiptApply.setUNIT_ID(groupCode);
            receiptApply.setApplyMemo(appMemo);
            receiptApply.setApplyType(defRidio);
            receiptApply.setApplyTitle(title);
            //receiptApply.setIsTotal(isTota);
            receiptApply.setTax_Rate(taxRate);
            receiptApply.setTotalAmount(totalAmount);
            receiptApply.setContrctType(numberType);
            receiptApply.setHANDLER_ID(userid);
            //receiptApply.setPolicyFile(branchsFilePath);
            //receiptApply.setUnifyFile(productsFilePath);
            /*receiptApply.setBranchFileUrl(branchFileUrl);
            receiptApply.setCustomerLetterFileUrl(customerLetterFileUrl);
            receiptApply.setSystemFileUrl(systemFileUrl);
            receiptApply.setProductsFileUrl(productsFileUrl);*/
            ReceiptApply receiptApplyT = receiptApplyService.addReceiptApply(receiptApply);
            JSONArray jsonObject1 = JSONArray.fromObject(jsondata);
            for (int i = 0; i < jsonObject1.size(); i++) {
                String s = jsonObject1.getString(i);
                JSONObject data = JSONObject.fromObject(s);
                String contrctNo=data.getString("contrctNo");
//                List<ReceiptApply> findByMonth = receiptApplyService.getFindByMonth(String.valueOf(user.getRowNo()), data.getString("contrctNo"));
                List<Map<String, String>> findByYear = receiptApplyService.getFindByYear(contrctNo);
                logger.info("----->获取账户【"+contrctNo+"】3个月申请次数【"+findByYear.size()+"】");
                ReceiptApplyDet receiptApplyDet = new ReceiptApplyDet();
                if(findByYear.size()>1){
                    logger.info("---->账户3月内大于1次【"+contrctNo+"】");
                    receiptApplyDet.setRemind_RaDet("0");
                }else {
                    receiptApplyDet.setRemind_RaDet("1");
                }
                receiptApplyDet.setApplyNO(receiptApplyT.getApplyNO());
                receiptApplyDet.setSerialNo(receiptApplyT.getApplyNO() + i);
                receiptApplyDet.setContrctPattern(data.getString("contrctPattern"));
                receiptApplyDet.setContrctNo(contrctNo);
                receiptApplyDet.setContrctType(data.getString("contrctType"));
                receiptApplyDet.setProductNo(data.getString("productNo"));
                if (data.getString("idNo") != null && !"null".equals(data.getString("idNo"))
                        && !"".equals(data.getString("idNo")) && !"undefined".equals(data.getString("idNo"))) {
                    receiptApplyDet.setIdNo(data.getString("idNo"));
                }

                if (data.getString("maxBusiness") != null && !"null".equals(data.getString("maxBusiness"))
                        && !"".equals(data.getString("maxBusiness")) && !"undefined".equals(data.getString("maxBusiness"))) {
                    receiptApplyDet.setMaxBusiness(data.getString("maxBusiness"));
                }

                if (data.getString("minBusiness") != null && !"null".equals(data.getString("minBusiness"))
                        && !"".equals(data.getString("minBusiness")) && !"undefined".equals(data.getString("minBusiness"))) {
                    receiptApplyDet.setMinBusiness(data.getString("minBusiness"));
                }

                if (data.getString("contractFilePath") != null && !"null".equals(data.getString("contractFilePath"))
                        && !"".equals(data.getString("contractFilePath")) && !"undefined".equals(data.getString("contractFilePath"))) {
                    receiptApplyDet.setContract_FilePath(data.getString("contractFilePath"));
                }

                if (data.getString("contractFileName") != null && !"null".equals(data.getString("contractFileName"))
                        && !"".equals(data.getString("contractFileName")) && !"undefined".equals(data.getString("contractFileName"))) {
                    receiptApplyDet.setContract_FileName(data.getString("contractFileName"));
                }

                if (data.getString("subject") != null && !"null".equals(data.getString("subject"))
                        && !"".equals(data.getString("subject")) && !"undefined".equals(data.getString("subject"))) {
                    receiptApplyDet.setSubject(data.getString("subject"));
                }
                receiptApplyDet.setCreateDate(new Date());
                receiptApplyDet.setState("0");
                receiptApplyDet.setAmount(data.getString("amount"));
                receiptApplyDet.setScene_Selection(data.getString("sceneSelection"));//场景选择
                receiptApplyService.addReceiptApplyDet(receiptApplyDet);
            }
            //负补收预占金额
            if (receiptApplyT.getApplyType() == -1) {
                try {
                    List<Map<String, Object>> listtwo = receiptApplyAmountService.getVwUserNew(receiptApplyT.getCreator());
                    String COMPANY_NAME = "";
                    ReceiptApplyAmount oneself = new ReceiptApplyAmount();
                    if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                        //logger.info("正补收额度配额信息省公司");
                        COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyName(simpleDateFormat.format(new Date()), COMPANY_NAME);
                    } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).contains("西昌市公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).contains("宝轮营销部")) {
                        //&& (json[0].TWODNAME).indexOf("西昌市公司")== -1&& (json[0].TWODNAME).indexOf("宝轮营销部")== -1
                        COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(new Date()), COMPANY_NAME, "省公司");
                    } else {
                        COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(new Date()), COMPANY_NAME, listtwo.get(0).get("COMPANY_NAME").toString());
                    }
                    //System.out.println("部门为==>"+COMPANY_NAME);
                    logger.info("负补收部门为==>" + COMPANY_NAME + "<==时间为==>" + receiptApplyT.getCreateDate() + "<==部门金额为==>" + Double.parseDouble(oneself.getRemainAmount()) + "<==部门剩余金额为==>" + Double.parseDouble(receiptApplyT.getTotalAmount()) * 100);
                    logger.info("部门==" + JSONObject.fromObject(oneself));
                    if (oneself.getRemainAmount() == null || Double.parseDouble(oneself.getRemainAmount()) <= 0) {
                        logger.info("负补收新增余额额度不足，请确认！" + "=部门额度=" + oneself.getRemainAmount() + "=申请额度=" + Double.parseDouble(receiptApplyT.getTotalAmount()) * 100);
                        Write("NO");
                        throw new RuntimeException("额度不足，请确认！");
                    }
                    BigDecimal data1 = new BigDecimal(Double.parseDouble(oneself.getRemainAmount()));
                    BigDecimal data2 = new BigDecimal(Double.parseDouble(receiptApplyT.getTotalAmount()) * 100);
                    if (data1.compareTo(data2) < 0) {
                        logger.info("负补收新增余额额度不足，请确认！" + "=部门额度=" + oneself.getRemainAmount() + "=申请额度=" + Double.parseDouble(receiptApplyT.getTotalAmount()) * 100);
                        Write("NO");
                        throw new RuntimeException("新增余额额度不足，请确认！");
                    } else {
                        //预占
                        oneself.setPreemptedAmount(
                                decimalFormat.format(
                                        Double.parseDouble(oneself.getPreemptedAmount()) + Double.parseDouble(receiptApplyT.getTotalAmount()) * 100
                                )
                        );
                        //RemainAmount 剩余的额度
                        oneself.setRemainAmount(
                                decimalFormat.format(
                                        Double.parseDouble(oneself.getRemainAmount()) - Double.parseDouble(receiptApplyT.getTotalAmount()) * 100
                                )
                        );
                        receiptApplyAmountService.UpdateReceiptApplyAmount(oneself);//更新额度
                    }
                    ReceiptApplyQuotasWorkOrder receiptApplyQuotasWorkOrder = new ReceiptApplyQuotasWorkOrder();
                    receiptApplyQuotasWorkOrder.setOrderNo(receiptApplyT.getId());
                    receiptApplyQuotasWorkOrder.setCompanyName(COMPANY_NAME);
                    receiptApplyQuotasWorkOrder.setTotalAmount(decimalFormat.format(Double.parseDouble(receiptApplyT.getTotalAmount()) * 100));
                    receiptApplyQuotasWorkOrder.setCreatorName(user.getEmployeeName());
                    receiptApplyQuotasWorkOrder.setCreatorDate(receiptApplyT.getCreateDate());
                    receiptApplyAmountService.addReceiptApplyQuotasWorkOrder(receiptApplyQuotasWorkOrder);

                } catch (Exception e) {
                    logger.error("负补收预占有误" + e.getMessage(), e);
                    Write("NO");
                    throw new RuntimeException(" 给事务回滚，自定义");
                }
            }
            Map<String, String> map = new HashMap<String, String>();
            map.put("decisionKey", "APPLY");
            map.put("decisionVal", role); // 所属地区（区县：QX、市公司：SGS、省重客：SZK）
            //String processId = transferJBPMUtils.startTransfer("ReceipyApply", map);
            //新流程
            String processId = transferJBPMUtils.startTransfer("ReceipyApplySceneProcessFinal", map);//ReceipyApplySceneProcessFinal ReceipyApplyProcessFinal
            //logger.info("测试processId=="+processId);
            // 流程启动完成
            // 保存信息到流程表
            ReceiptApplyProcess ProcessList = new ReceiptApplyProcess();
            ProcessList.setProcessId(processId);// 流程id
            ProcessList.setProcessName(receiptApplyT.getApplyTitle());// 流程名称
            ProcessList.setUserId(user.getEmployeeName());
            ProcessList.setTransferId(receiptApply.getId());
            ProcessList.setCreatorNo(user.getRowNo() + "");
            ProcessList.setStartTime(getStringDate(new Date()));
            ProcessList.setState(1);
            receiptApplyService.saveProcessList(ProcessList); // 保存任务表信息
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            // 先保存自己本身的任务
            ReceiptApplyTask TaskList = new ReceiptApplyTask();
            TaskList.setRole("客户经理");
            TaskList.setProcess(receiptApply.getId());
            TaskList.setCreator(user.getEmployeeName());
            TaskList.setCreatorNo(user.getRowNo() + "");
            TaskList.setCreatDate(getStringDate(new Date()));
            TaskList.setPlanDate(getStringDate(new Date()));
            TaskList.setOper(user.getEmployeeName());
            TaskList.setOperNo(user.getRowNo() + "");
            TaskList.setOperDate(getStringDate(new Date()));
            TaskList.setSpendTime("0");
            TaskList.setStatus("2");
            TaskList.setType("SH");
            TaskList.setExpectedCompletionTime(getStringDate(new Date()));
            receiptApplyService.saveTaskList(TaskList);// 保存流程表信息
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            //保存下一步任务信息
            ReceiptApplyTask TaskListtwo = new ReceiptApplyTask();
            TaskListtwo.setProcess(receiptApplyT.getId());
            TaskListtwo.setRole(task.getActivityName());
            TaskListtwo.setCreator(user.getEmployeeName());
            TaskListtwo.setCreatorNo(user.getRowNo() + "");
            TaskListtwo.setCreatDate(getStringDatethree());
            TaskListtwo.setPlanDate(getStringDate(new Date()));
            TaskListtwo.setOper(USER.getEmployeeName());
            TaskListtwo.setOperNo(USER.getRowNo() + "");
            //TaskListtwo.setOperDate(getStringDate(new Date()));
            TaskListtwo.setSpendTime("0");
            TaskListtwo.setStatus("1");
            TaskListtwo.setType("SH");
            TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
            ReceiptApplyTask TaskBeantwo = receiptApplyService.saveTaskList(TaskListtwo);// 保存流程表信息
            List<SingleAndAttachment> ss = receiptApplyService.getSingleAndAttachment(id);
            //遍历获取的附件中间表数据
            if (ss != null) {
                for (int i = 0; i < ss.size(); i++) {
                    if (i == 0) {
                        if (!"".equals(attachmentId)) {
                            attachmentId += "," + ss.get(i).getAttachmentId() + ",";
                        } else {
                            attachmentId += ss.get(i).getAttachmentId() + ",";
                        }
                    } else {
                        attachmentId += ss.get(i).getAttachmentId() + ",";
                    }
                }
            }
            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(receiptApply.getId());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(ReceiptApply.ReceiptApply);
                            receiptApplyService.saveSandA(sa);
                        }
                    }
                }
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                System.out.println("================开始代办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================结束代办================");
            }
            daibantwo(receiptApplyT, userid, processId, user, TaskBeantwo);// 代办
            Write("YES");
        } catch (Exception e) {
            //e.printStackTrace();
            logger.error("发起正负补收有误!" + e.getMessage(), e);
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    /**
     * 流程进行
     */
    public void handleReceiptApply() {
        try {
            String pid = getString("processId");// 流程id
            String t = getString("nextTask");// 下一步可执行流程线条值
            String userid = getString("userId");//下一步处理用户id
            String id = getString("id");//id
            String opinion = getString("opinion");// 审批意见
            String waitId = getString("waitId");// 待办id
            String receiptApplyTaskID = getString("receiptApplyTaskId");
            String phone = getString("phone");
            if (waitId == null || waitId.equals("") || receiptApplyTaskID == null || receiptApplyTaskID.equals("")) {
                WaitTask waitTask = receiptApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                receiptApplyTaskID = str.substring(str.lastIndexOf("=") + 1);
            }
            ReceiptApplyTask receiptApplyTask = receiptApplyService.getTaskList(receiptApplyTaskID);// 根据流程id查询任务表信息
            if (receiptApplyTask != null) {
                receiptApplyTask.setOperDate(getStringDate(new Date()));//操作时间
                receiptApplyTask.setReplyContent(opinion);//审批意见
                receiptApplyTask.setStatus("2");
                receiptApplyService.updateTask(receiptApplyTask);//修改任务表processtracking
            }
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            List<SystemDept> deptList = user.getSystemDept();
            String code = deptList.get(0).getSystemCompany().getCompanyCode();
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            Map<String, String> map = new HashMap<String, String>();
            String str = pid.substring(0, pid.indexOf("."));
            //logger.info("测试str=="+str+"===="+task.getActivityName());
            if ("ReceipyApplyProcessFinal".equals(str)) {
                if ("市公司领导".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SGSLD");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("区县业务管理员".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "QXDM");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("区县政企部主任".equals(task.getActivityName())) {
//					TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "QXSM");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("市公司政企部经理".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SGSZQBJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("市公司政企部经理副".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, "市公司政企部经理");
                    map.put("decisionKey", "SGSZQBJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("省公司管理员".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SGSGLY");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("区县分管经理".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "QXFGJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("省重客业务管理室经理".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SZKYWGLSJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else {
                    jbpmUtil.completeTask(task.getId(), t);
                }
            } else {
                if ("市公司领导".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SGSLD");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("市公司政企部经理".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SGSZQBJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("市公司政企部经理副".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, "市公司政企部经理");
                    map.put("decisionKey", "SGSZQBJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("省公司管理员".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SGSGLY");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    if (t.equals("ALL")) {
                        jbpmUtil.completeTask(task.getId(), t);
                    } else {
                        jbpmUtil.completeTask(task.getId(), map, t);
                    }
                } else if ("区县分管经理".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "QXFGJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else if ("省重客业务管理室经理".equals(task.getActivityName())) {
                    TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                    map.put("decisionKey", "SZKYWGLSJL");
//					if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
//						if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
//							map.put("decisionVal", "YES");
//						} else {
//							map.put("decisionVal", "NO");
//						}
//					} else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                    map.put("decisionVal", "YES");
//					}
                    jbpmUtil.completeTask(task.getId(), map);
                } else {
                    jbpmUtil.completeTask(task.getId(), t);
                }
            }
            //保存下一步任务信息
            Task tasktwo = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            ReceiptApplyTask receiptApplyTasktwo = new ReceiptApplyTask();
            receiptApplyTasktwo.setProcess(id);
            receiptApplyTasktwo.setRole(tasktwo.getActivityName());
            receiptApplyTasktwo.setCreator(user.getEmployeeName());
            receiptApplyTasktwo.setCreatorNo(user.getRowNo() + "");
            receiptApplyTasktwo.setCreatDate(getStringDate(new Date()));
            receiptApplyTasktwo.setPlanDate(getStringDate(new Date()));
            receiptApplyTasktwo.setOper(USER.getEmployeeName());
            receiptApplyTasktwo.setOperNo(USER.getRowNo() + "");
            //TaskListtwo.setOperDate(getStringDate(new Date()));
            receiptApplyTasktwo.setSpendTime("0");
            receiptApplyTasktwo.setStatus("1");
            receiptApplyTasktwo.setType("SH");
            receiptApplyTasktwo.setExpectedCompletionTime(getStringDate(new Date()));
            ReceiptApplyTask receiptApplyTaskthree = receiptApplyService.saveTaskList(receiptApplyTasktwo);// 保存流程表信息
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                service.updateWait(wait, this.getRequest());
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            daibantwo(receiptApply, userid, pid, user, receiptApplyTaskthree);// 调用service层方法生成代办
            //更新下一步审批人
            receiptApply.setHANDLER_ID(userid);
            receiptApplyService.updateReceiptApply(receiptApply);
            Write("OK");
        } catch (Error ee) {
            logger.info("正负流程进行错误===>" + ee);
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义1");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("正负流程进行错误2===>" + e);
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }


    /**
     * 完成方法
     */
    public void completeTransition() {
        try {
            String pid = getString("processId");// 流程id
            String id = getString("id");// 账户信息id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 审批意见
            String receiptApplyTaskID = getString("receiptApplyTaskId");
            String phone = getString("phone");
            DecimalFormat decimalFormat = new DecimalFormat("0");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            if (waitId == null || waitId.equals("") || receiptApplyTaskID == null || receiptApplyTaskID.equals("")) {
                WaitTask waitTask = receiptApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                receiptApplyTaskID = str.substring(str.lastIndexOf("=") + 1);
            }
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);

            receiptApply.setState("5");
            receiptApply.setHANDLER_ID(receiptApply.getCreator());
            receiptApplyService.updateReceiptApply(receiptApply);
            ReceiptApplyTask receiptApplyTask = receiptApplyService.getTaskList(receiptApplyTaskID);// 根据流程id查询任务表信息
            if (receiptApplyTask != null) {
                receiptApplyTask.setOperDate(getStringDate(new Date()));// 操作时间
                receiptApplyTask.setReplyContent(opinion);//审批意见
                receiptApplyTask.setStatus("2");
                receiptApplyService.updateTask(receiptApplyTask);//修改任务表
            }
            /*receiptApply.setState("0");
            receiptApplyService.updateReceiptApply(receiptApply);*/
            //jbpmUtil.deleteProcessInstance(pid);//删除流程
            //保存下一步任务信息
            ReceiptApplyTask TaskListtwo = new ReceiptApplyTask();
            TaskListtwo.setProcess(id);
            TaskListtwo.setRole("客户经理");
            TaskListtwo.setCreator(user.getEmployeeName());
            TaskListtwo.setCreatorNo(user.getRowNo() + "");
            TaskListtwo.setCreatDate(getStringDate(new Date()));
            TaskListtwo.setPlanDate(getStringDate(new Date()));
            TaskListtwo.setOper(receiptApply.getCreatorName());
            TaskListtwo.setOperNo(receiptApply.getCreator());
            TaskListtwo.setSpendTime("0");
            TaskListtwo.setStatus("1");
            TaskListtwo.setType("SH");
            TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
            ReceiptApplyTask receiptApplyTaskthree = receiptApplyService.saveTaskList(TaskListtwo);// 保存流程表信息
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
            Map<String, String> map = new HashMap<String, String>();
            if ("市公司领导".equals(task.getActivityName())) {
                map.put("decisionKey", "SGSLD");
                map.put("decisionVal", "NO");
                //jbpmUtil.completeTask(task.getId(), map);
                jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSBM");
            } else if ("市公司政企部经理".equals(task.getActivityName())) {
                map.put("decisionKey", "SGSZQBJL");
                map.put("decisionVal", "NO");
                //jbpmUtil.completeTask(task.getId(), map);
                jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
            } else if ("市公司政企部经理副".equals(task.getActivityName())) {
                map.put("decisionKey", "SGSZQBJL");
                map.put("decisionVal", "NO");
                //jbpmUtil.completeTask(task.getId(), map);
                jbpmUtil.completeTask(task.getId(), map, "ROLE_DSSM");
            } else if ("省公司管理员".equals(task.getActivityName())) {
                map.put("decisionKey", "SGSGLY");
                map.put("decisionVal", "NO");
                //jbpmUtil.completeTask(task.getId(), map);
                jbpmUtil.completeTask(task.getId(), map, "ROLE_SGSMR");
            } else if ("区县分管经理".equals(task.getActivityName())) {
                map.put("decisionKey", "QXFGJL");
                map.put("decisionVal", "NO");
                //jbpmUtil.completeTask(task.getId(), map);
                jbpmUtil.completeTask(task.getId(), map, "ROLE_DSBM");
            } else if ("省重客业务管理室经理".equals(task.getActivityName())) {
                map.put("decisionKey", "SZKYWGLSJL");
                map.put("decisionVal", "NO");
                //jbpmUtil.completeTask(task.getId(), map);
                jbpmUtil.completeTask(task.getId(), map, "ROLE_SZKSM");
            } else {
                jbpmUtil.completeTask(task.getId(), "结束");
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                service.updateWait(wait, this.getRequest());
            } else {
                Write("NO");
                throw new RuntimeException(" 给事务回滚，自定义");
            }
            daiban(receiptApply, receiptApply.getCreator(), pid, user, receiptApplyTaskthree, "FQZF");
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("正负完成方法===>" + e.getMessage(), e);
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }


    /**
     * 退回方法
     */
    public void returnTransition() {
        try {
            String id = getString("id");// 转账信息id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String receiptApplyTaskID = getString("receiptApplyTaskId");
            String phone = getString("phone");
            DecimalFormat decimalFormat = new DecimalFormat("0");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            if (waitId == null || waitId.equals("") || receiptApplyTaskID == null || receiptApplyTaskID.equals("")) {
                WaitTask waitTask = receiptApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                receiptApplyTaskID = str.substring(str.lastIndexOf("=") + 1);
            }
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
            ReceiptApplyTask receiptApplyTask = receiptApplyService.getTaskList(receiptApplyTaskID);// 根据流程id查询任务表信息
            if (receiptApplyTask != null) {
                receiptApplyTask.setOperDate(getStringDate(new Date()));// 操作时间
                receiptApplyTask.setReplyContent(opinion);//审批意见
                receiptApplyTask.setStatus("0");
                receiptApplyTask = receiptApplyService.updateTask(receiptApplyTask);//修改任务表
            }
            receiptApply.setState("2");
            ReceiptApply receiptApplyT = receiptApplyService.updateReceiptApply(receiptApply);
            if (receiptApplyT.getApplyType() == -1) {
                List<Map<String, Object>> listtwo = receiptApplyAmountService.getVwUserNew(receiptApplyT.getCreator());
                String COMPANY_NAME = "";
                ReceiptApplyAmount oneself = new ReceiptApplyAmount();
                SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
                logger.info("正负补收工单信息==" + JSONObject.fromObject(receiptApplyT));
                if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                    COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                    oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyName(simpleDateFormat.format(receiptApply.getCreateDate()), "省公司");//查询工单创建日期金额
                } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).equals("西昌市公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).equals("宝轮营销部")) {
                    COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                    oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(receiptApply.getCreateDate()), COMPANY_NAME, "省公司");//查询工单创建日期金额
                    logger.info("正补收额度配额信息市公司" + COMPANY_NAME);
                    logger.info("正补收额度配额信息市公司配额" + JSONObject.fromObject(oneself));
                } else {
                    COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
                    oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(receiptApply.getCreateDate()), COMPANY_NAME, listtwo.get(0).get("COMPANY_NAME").toString());//查询工单创建日期金额
                    logger.info("正补收额度配额信息区县" + COMPANY_NAME);
                    logger.info("正补收额度配额信息区县配额" + JSONObject.fromObject(oneself));
                }

                //RemainAmount 预占额度 3305600 30092
                oneself.setPreemptedAmount(
                        decimalFormat.format(
                                Double.parseDouble(oneself.getPreemptedAmount()) - Double.parseDouble(receiptApplyT.getTotalAmount()) * 100
                        )
                );
                //RemainAmount 剩余的额度
                oneself.setRemainAmount(
                        decimalFormat.format(
                                Double.parseDouble(oneself.getRemainAmount()) + Double.parseDouble(receiptApplyT.getTotalAmount()) * 100
                        )
                );
                ReceiptApplyAmount receiptApplyAmount = receiptApplyAmountService.UpdateReceiptApplyAmount(oneself);//更新额度
                //System.out.println("更新后=="+JSONObject.fromObject(receiptApplyAmount));
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                System.out.println("================退回开始代办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================退回结束代办================");
            } else {
                Write("NO");
                throw new RuntimeException(" 给事务回滚，自定义");
            }
            jbpmUtil.deleteProcessInstance(processId);
            daibanthree(receiptApply, receiptApply.getCreator(), waitId, user, receiptApplyTask);// 调用service层方法生成待办
            Write("OK");
        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
            logger.error("正负退回方法===>" + e.getMessage(), e);
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    /**
     * 作废方法
     */
    public void Invalid() {
        try {
            String id = getString("id");// 转账信息id
            String opinion = getString("opinion");//作废原因
            String waitId = getString("waitId");
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.querUsers(user.getLoginName());
            }
            if (waitId == null || waitId.equals("")) {
                WaitTask waitTask = receiptApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
            receiptApply.setState("-1");
            receiptApplyService.updateReceiptApply(receiptApply);
            if (wait != null) {
                System.out.println("================作废开始代办================");
                service.updateWait(wait, this.getRequest());
                ReceiptApplyTask TaskListtwo = new ReceiptApplyTask();
                TaskListtwo.setProcess(id);
                TaskListtwo.setRole("客户经理");
                TaskListtwo.setCreator(user.getEmployeeName());
                TaskListtwo.setCreatorNo(user.getRowNo() + "");
                TaskListtwo.setCreatDate(getStringDate(new Date()));
                TaskListtwo.setPlanDate(getStringDate(new Date()));
                TaskListtwo.setOper(receiptApply.getCreatorName());
                TaskListtwo.setOperNo(receiptApply.getCreator());
                TaskListtwo.setSpendTime("0");
                TaskListtwo.setStatus("2");
                TaskListtwo.setType("SH");
                TaskListtwo.setOperDate(getStringDate(new Date()));// 操作时间
                TaskListtwo.setReplyContent(opinion);//审批意见
                TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                receiptApplyService.saveTaskList(TaskListtwo);// 保存流程表信息
                System.out.println("================作废结束代办================");
            } else {
                Write("NO");
                throw new RuntimeException(" 给事务回滚，自定义");
            }
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            logger.info("正负作废方法===>" + e);
            Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }

    /**
     * 转发阅读结束方法
     */
    public void readEnd() {
        try {
            String id = getString("id");
            String waitId = getString("waitId");
            String receiptApplyTaskID = getString("receiptApplyTaskId");
            if (waitId == null || waitId.equals("") || receiptApplyTaskID == null || receiptApplyTaskID.equals("")) {
                WaitTask waitTask = receiptApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                receiptApplyTaskID = str.substring(str.lastIndexOf("=") + 1);
            }
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
            if (receiptApply.getBossStatus() == null) {
                String text = setBossService(receiptApply, user);
                if ("OK".equals(text)) {
                    logger.info("这是正负补收完成按钮操作以后获取到的task任务Id："+receiptApplyTaskID);
                    ReceiptApplyTask receiptApplyTask = receiptApplyService.getTaskList(receiptApplyTaskID);// 根据流程id查询任务表信息
                    logger.info("这是正负补收完成按钮操作以后获取到的task任务对象："+receiptApplyTask.getUuid()+";并且："+receiptApplyTask != null);
                    if (receiptApplyTask != null) {
                        logger.info("这是正负补收完成按钮操作以后获取到的task任务对象更新开始操作："+receiptApplyTask.getUuid());
                        receiptApplyTask.setOperDate(getStringDate(new Date()));// 操作时间
                        receiptApplyTask.setReplyContent("");//审批意见
                        receiptApplyTask.setStatus("2");
                        receiptApplyService.updateTask(receiptApplyTask);//修改任务表
                        logger.info("这是正负补收完成按钮操作以后获取到的task任务对象更新结束操作："+receiptApplyTask.getUuid());
                    }
                    receiptApply.setState("0");
                    receiptApply.setBossStatus("0");
                    receiptApplyService.updateReceiptApply(receiptApply);
                    WaitTask wait = service.queryWaitByTaskId(waitId);
                    if (wait != null) {
                        System.out.println("================阅读开始代办================");
                        service.updateWait(wait, this.getRequest());
                        System.out.println("================阅读结束代办================");
                    } else {
                        Write("NO");
                        throw new RuntimeException(" 给事务回滚，自定义");
                    }
                    Write("OK");
                } else {
                    Write(text);
                }
            } else {
                logger.info("这是正负补收完成按钮操作以后获取到的task任务Id-2："+receiptApplyTaskID);
                ReceiptApplyTask receiptApplyTask = receiptApplyService.getTaskList(receiptApplyTaskID);// 根据流程id查询任务表信息
                logger.info("这是正负补收完成按钮操作以后获取到的task任务对象："+receiptApplyTask.getUuid()+";并且："+receiptApplyTask != null);
                if (receiptApplyTask != null) {
                    logger.info("这是正负补收完成按钮操作以后获取到的task任务对象更新开始操作-2："+receiptApplyTask.getUuid());
                    receiptApplyTask.setOperDate(getStringDate(new Date()));// 操作时间
                    receiptApplyTask.setReplyContent("");//审批意见
                    receiptApplyTask.setStatus("2");
                    receiptApplyService.updateTask(receiptApplyTask);//修改任务表
                    logger.info("这是正负补收完成按钮操作以后获取到的task任务对象更新结束操作-2："+receiptApplyTask.getUuid());
                }
                receiptApply.setState("0");
                receiptApply.setBossStatus("0");
                receiptApplyService.updateReceiptApply(receiptApply);
                WaitTask wait = service.queryWaitByTaskId(waitId);
                if (wait != null) {
                    System.out.println("================阅读开始代办================");
                    service.updateWait(wait, this.getRequest());
                    System.out.println("================阅读结束代办================");
                } else {
                    Write("NO");
                    throw new RuntimeException(" 给事务回滚，自定义");
                }
                Write("OK");
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("正负补收转发阅读结束方法错误：" + e.getMessage(), e);
            Write("NO");
        }
    }

    /**
     * 流程结束客户经理和转发人员循环转发
     */
    public void loopForwarding() {
        try {
            String waitId = getString("waitId");
            String id = getString("id");// 账户信息id
            String receiptApplyTaskID = getString("receiptApplyTaskId");
            String userid = this.getString("userid");//接收人ID
            String processId = getString("processId");
            String opinion = getString("opinion");
            String type = getString("type");
            String phone = getString("phone");
            if (waitId == null || waitId.equals("") || receiptApplyTaskID == null || receiptApplyTaskID.equals("")) {
                WaitTask waitTask = receiptApplyService.getWaitTask(id);
                waitId = waitTask.getWaitId();
                String str = waitTask.getUrl();
                //receiptApplyTaskID = str.substring(str.lastIndexOf("=") + 1);
                String[] split = str.split("&");
                for (int i = 0; i < split.length; i++) {
                    if (split[i].contains("receiptApplyTaskId=")) {
                        String[] split1 = split[i].split("=");
                        receiptApplyTaskID = split1[1];
                    }
                }
            }
            logger.info("这是正负补收转发按钮操作以后获取到的task任务Id："+receiptApplyTaskID);
            ReceiptApplyTask receiptApplyTask = receiptApplyService.getTaskList(receiptApplyTaskID);// 根据流程id查询任务表信息
            logger.info("这是正负补收转发按钮操作以后获取到的task任务对象："+receiptApplyTask.getUuid()+";并且："+receiptApplyTask != null);
            if (receiptApplyTask != null) {
                logger.info("这是正负补收转发按钮操作以后获取到的task任务对象更新开始操作："+receiptApplyTask.getUuid());
                receiptApplyTask.setOperDate(getStringDate(new Date()));// 操作时间
                receiptApplyTask.setReplyContent(opinion);//审批意见
                receiptApplyTask.setStatus("2");
                receiptApplyService.updateTask(receiptApplyTask);//修改任务表
                logger.info("这是正负补收转发按钮操作以后获取到的task任务对象更新结束操作："+receiptApplyTask.getUuid());
            }
            SystemUser user = new SystemUser();
            if (phone == null || phone.equals("") || phone.equals("null")) {
                user = this.user;
            } else {
                user = systemUserService.getUserByPhone(phone);
                user = systemUserService.get4ALoginInfo(user.getLoginName());
            }
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
            if (processId == null) {
                ReceiptApplyProcess pid = receiptApplyService.getPid(receiptApply.getId());
                processId = pid.getProcessId();
            }
            if ("ZF".equals(type)) {
                SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(receiptApply.getCreator()));
                ReceiptApplyTask TaskListtwo = new ReceiptApplyTask();
                TaskListtwo.setProcess(id);
                TaskListtwo.setRole("客户经理");
                TaskListtwo.setCreator(user.getEmployeeName());
                TaskListtwo.setCreatorNo(user.getRowNo() + "");
                TaskListtwo.setCreatDate(getStringDate(new Date()));
                TaskListtwo.setPlanDate(getStringDate(new Date()));
                TaskListtwo.setOper(USER.getEmployeeName());
                TaskListtwo.setOperNo(USER.getRowNo() + "");
                TaskListtwo.setSpendTime("0");
                TaskListtwo.setStatus("1");
                TaskListtwo.setType("SH");
                TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                ReceiptApplyTask TaskBeantwo = receiptApplyService.saveTaskList(TaskListtwo);//保存流程表信息
                daiban(receiptApply, receiptApply.getCreator(), processId, user, TaskBeantwo, "FQZF");
            } else {
                if (receiptApply.getBossStatus() == null) {
                    String text = setBossService(receiptApply, user);
                    if ("OK".equals(text)) {
                        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                        ReceiptApplyTask TaskListtwo = new ReceiptApplyTask();
                        TaskListtwo.setProcess(id);
                        TaskListtwo.setRole("转发审核");
                        TaskListtwo.setCreator(user.getEmployeeName());
                        TaskListtwo.setCreatorNo(user.getRowNo() + "");
                        TaskListtwo.setCreatDate(getStringDate(new Date()));
                        TaskListtwo.setPlanDate(getStringDate(new Date()));
                        TaskListtwo.setOper(USER.getEmployeeName());
                        TaskListtwo.setOperNo(USER.getRowNo() + "");
                        TaskListtwo.setSpendTime("0");
                        TaskListtwo.setStatus("1");
                        TaskListtwo.setType("SH");
                        TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                        ReceiptApplyTask TaskBeantwo = receiptApplyService.saveTaskList(TaskListtwo);// 保存流程表信息
                        receiptApply.setState("0");
                        receiptApply.setBossStatus("0");
                        receiptApplyService.updateReceiptApply(receiptApply);
                        daiban(receiptApply, userid, processId, user, TaskBeantwo, "ZF");
                    } else {
                        Write(text);
                        throw new RuntimeException(" 给事务回滚，自定义");
                    }
                } else {
                    SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
                    ReceiptApplyTask TaskListtwo = new ReceiptApplyTask();
                    TaskListtwo.setProcess(id);
                    TaskListtwo.setRole("转发审核");
                    TaskListtwo.setCreator(user.getEmployeeName());
                    TaskListtwo.setCreatorNo(user.getRowNo() + "");
                    TaskListtwo.setCreatDate(getStringDate(new Date()));
                    TaskListtwo.setPlanDate(getStringDate(new Date()));
                    TaskListtwo.setOper(USER.getEmployeeName());
                    TaskListtwo.setOperNo(USER.getRowNo() + "");
                    TaskListtwo.setSpendTime("0");
                    TaskListtwo.setStatus("1");
                    TaskListtwo.setType("SH");
                    TaskListtwo.setExpectedCompletionTime(getStringDate(new Date()));
                    ReceiptApplyTask TaskBeantwo = receiptApplyService.saveTaskList(TaskListtwo);// 保存流程表信息
                    daiban(receiptApply, userid, processId, user, TaskBeantwo, "ZF");
                }
            }
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                System.out.println("================转发开始代办================");
                service.updateWait(wait, this.getRequest());
                System.out.println("================转发结束代办================");
            } else {
                Write("NO");
                throw new RuntimeException(" 给事务回滚，自定义");
            }
            Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("正负补收流程结束客户经理和转发人员循环转发错误：" + e.getMessage(), e);
            Write("No");
        }
    }

    /**
     * 根据ID查询跟踪处理的省公司管理员以及
     * 省公司政企业务管理室2次
     *
     * @return
     */
    public void getReceiptApplyTask() {
        try {
            String id = getString("id");
            Integer rolename = getInteger("rolename");
            String name = "";
            if (rolename == 1) {
                name = "省公司管理员";
            } else {
                name = "省公司政企业务管理室";
            }
            TransferTask p = receiptApplyService.getReceiptApplyTask(id, name);
            Write(JSONHelper.SerializeWithNeedAnnotation(p));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }

    /**
     * 查询配置金额
     */
    public void getTransferCitiesData() {
        try {
            String dangqianrenwu = getString("dangqianrenwu");
            List<SystemDept> deptList = user.getSystemDept();
            String code = deptList.get(0).getSystemCompany().getCompanyCode();
            TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, dangqianrenwu);
            Write(JSONHelper.SerializeWithNeedAnnotation(transferCitiesData));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }


    /**
     * 获取附件消息
     */
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = receiptApplyService.fuJian(id, biaoshi);
        Write(JSONHelper.Serialize(s));
    }

    /**
     * 根据id获取相关转账信息
     */
    public void findById() {
        try {
            String id = getString("id");
            Map<String, Object> map = receiptApplyService.findByRowNo(id);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(map));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void receiptApplyDet() {
        try {
            String id = getString("id");
            ReceiptApply receiptApply = receiptApplyService.getReceiptApplyNumber(id);
            List<ReceiptApplyDet> receiptApplyDet = receiptApplyService.getReceiptApplyDet(receiptApply.getApplyNO());
            Write(JSONHelper.Serialize(receiptApplyDet));
        } catch (Exception e) {
            e.printStackTrace();
            Write("ON");
        }
    }


    /**
     * 根据ID查询跟踪处理
     *
     * @return
     */
    public void processtracking() {
        String id = getString("id");
        List<ReceiptApplyTask> p = receiptApplyService.processtracking(id);
        Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
    }

    /**
     * 下载文件
     */
    public void downloadContractFujian() {
        try {
            String id = getString("id");//附件id
            receiptApplyService.downloFujian(id);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 调用boss接口推送信息
     */
    public String setBossService(ReceiptApply receiptApply, SystemUser user) {
        DecimalFormat decimalFormat = new DecimalFormat("0");
        try {
            List<ReceiptApplyDet> list = receiptApplyService.getReceiptApplyDetBossStatus(receiptApply.getApplyNO());
            List<SystemDept> deptList = user.getSystemDept();
            String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();
            String code = receiptApplyService.getCompanyRegion(companyCode);
            int s = Integer.parseInt(code) - 100;
            for (int i = 0; i < list.size(); i++) {
                if ("1".equals(list.get(i).getContrctPattern())) {
                    String url = ESB_URL_172 + "s2400Cfm";
                    if(isES){
                        url = ESB_URL_38 + "s2400Cfm";
                    }
                    JSONObject object = new JSONObject();
                    if ("1".equals(list.get(i).getContrctPattern())) {
                        object.put("FAST_COLL_FLAG", "1");
                        object.put("PHONE_NO", "");
                    } else {
                        object.put("FAST_COLL_FLAG", "0");
                        object.put("PHONE_NO", list.get(i).getProductNo() == null ? "" : list.get(i).getProductNo());
                    }
                    /*object.put("FAST_COLL_FLAG","1");
                    object.put("PHONE_NO","");*/
                    object.put("CONTRACT_NO", list.get(i).getContrctNo() == null ? "" : list.get(i).getContrctNo());
                    object.put("UNIT_ID", receiptApply.getUNIT_ID());
                    object.put("AUDIT_SN", list.get(i).getSerialNo());
                    object.put("REGION_ID", s + "");
                    object.put("OPR_TYPE", "0");
                    object.put("REMARK", receiptApply.getApplyTitle() == null ? "" : receiptApply.getApplyTitle());
                    NumberFormat format = NumberFormat.getInstance();
                    Number number = format.parse(list.get(i).getAmount());
                    double temp = number.doubleValue() * 100.0;
                    format.setGroupingUsed(false);
                    //设置返回数的小数部分所允许的最大位数
                    format.setMaximumFractionDigits(0);
                    String amount = format.format(temp);
                    if (receiptApply.getApplyType() == -1) {
                        object.put("ADJ_FEE", "-" + amount);
                    } else {
                        object.put("ADJ_FEE", amount);
                    }
                    object.put("LOGIN_NO", user.getBossUserName());
                    if (list.get(i).getIdNo() != null) {
                        object.put("ID_NO", list.get(i).getIdNo());
                    }
                    //String json = setParamObj(object,list.get(i).getProductNo());
                    String json = ESBReqMsgUtil.packMsgByRoute("14", user.getBossUserName(), object);
                    String jsonString = UrlConnection.responseGBK(url, json.toString());
                    JSONObject jsthree = JSONObject.fromObject(jsonString);
                    String datatwo = jsthree.getString("res");
                    JSONObject jsone = JSONObject.fromObject(datatwo);
                    JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                    if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                        list.get(i).setBossStatus("0");
                        receiptApplyService.updateReceiptApplyDet(list.get(i));
                    } else {
                        list.get(i).setBossMsg(jstwo.getString("RETURN_MSG") + "-" + jstwo.getString("DETAIL_MSG"));//保存错误信息
                        receiptApplyService.updateReceiptApplyDet(list.get(i));
                    }
                } else {
                    String url = ESB_URL_172 + "s2400BackFeeCfm";
                    if(isES){
                        url = ESB_URL_38 + "s2400BackFeeCfm";
                    }
                    File file = new File(list.get(i).getContract_FilePath());
                    boolean bl = uploadTxt(file);
                    if (bl) {
                        JSONObject object = new JSONObject();
                        object.put("LOGIN_NO", user.getBossUserName());
                        object.put("PHONE_NO", list.get(i).getProductNo() == null ? "" : list.get(i).getProductNo());
                        object.put("OP_CODE", "8037");
                        object.put("OP_NOTE", "订单补收申请");
                        object.put("OP_SN", list.get(i).getSerialNo());
                        object.put("PAY_TYPE", "w");
                        object.put("CONTRACT_NO", list.get(i).getContrctNo() == null ? "" : Long.parseLong(list.get(i).getContrctNo()));
                        object.put("ID_NO", list.get(i).getIdNo() == null ? "" : Long.parseLong(list.get(i).getIdNo()));
                        NumberFormat format = NumberFormat.getInstance();
                        Number number = format.parse(list.get(i).getAmount());
                        double temp = number.doubleValue() * 100.0;
                        format.setGroupingUsed(false);
                        //设置返回数的小数部分所允许的最大位数
                        format.setMaximumFractionDigits(0);
                        String amount = format.format(temp);
                        if (receiptApply.getApplyType() == -1) {
                            object.put("ADJ_FEE", -Long.parseLong(amount));//补收金额
                            object.put("ADJ_INFO", list.get(i).getSubject() + "|" + -Long.parseLong(amount) + "|" + "#");//补收科目|补收金额|#
                        } else {
                            object.put("ADJ_FEE", Long.parseLong(amount));//补收金额
                            object.put("ADJ_INFO", list.get(i).getSubject() + "|" + Long.parseLong(amount) + "|" + "#");//补收科目|补收金额|#
                        }

                        object.put("PBIZ_TYPE", list.get(i).getMaxBusiness());
                        object.put("CBIZ_TYPE", list.get(i).getMinBusiness());
                        object.put("BILL_MONTH", getBillMonth());//补收年月 写当月yyyymm
                        object.put("FILE_NAME", file.getName());//上传的合同文件名
                        object.put("FILE_PATH", "/ftpsale/billingfile/" + file.getName() + "|");//上传合同文件路径
                        object.put("UNIT_ID", receiptApply.getUNIT_ID());
                        String json = ESBReqMsgUtil.packMsgByRoute("14", user.getBossUserName(), object);
                        String jsonString = UrlConnection.responseGBK(url, json.toString());
                        JSONObject jsthree = JSONObject.fromObject(jsonString);
                        String datatwo = jsthree.getString("res");
                        JSONObject jsone = JSONObject.fromObject(datatwo);
                        JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                        if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                            list.get(i).setBossStatus("0");
                            receiptApplyService.updateReceiptApplyDet(list.get(i));
                        } else {
                            list.get(i).setBossMsg(jstwo.getString("RETURN_MSG") + "-" + jstwo.getString("DETAIL_MSG"));//保存错误信息
                            receiptApplyService.updateReceiptApplyDet(list.get(i));
                        }
                    }
                }
            }
            //System.out.println(receiptApply.getApplyNO() + "============");
            List<ReceiptApplyDet> listtwo = receiptApplyService.getReceiptApplyDetBossStatus(receiptApply.getApplyNO());
            if (listtwo.size() > 0) {
                String text = "";
                for (int j = 0; j < listtwo.size(); j++) {
                    if (listtwo.get(j).getBossStatus() == null || listtwo.get(j).getBossStatus() == "") {
                        text += listtwo.get(j).getContrctNo() + listtwo.get(j).getBossMsg() + ";";
                    }
                }
                return "操作失败,(注:" + text.substring(0, text.length() - 1) + ",请联系系统管理员)";
            } else {
                receiptApply.setState("0");
                receiptApply.setBossStatus("0");
                receiptApplyService.updateReceiptApply(receiptApply);
                if (receiptApply.getApplyType() == -1) {
                    List<Map<String, Object>> listuser = receiptApplyAmountService.getVwUserNew(receiptApply.getCreator());
                    String COMPANY_NAME = "";
                    ReceiptApplyAmount oneself = new ReceiptApplyAmount();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
                    logger.info("推送boss正负补收工单信息==" + receiptApply.getId());
                    if (listuser.get(0).get("COMPANY_NAME").equals("省公司")) {
                        COMPANY_NAME = listuser.get(0).get("COMPANY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyName(simpleDateFormat.format(receiptApply.getCreateDate()), "省公司");//创建日期金额
                    } else if (!(listuser.get(0).get("COUNTY_NAME").toString()).contains("分公司") && !(listuser.get(0).get("COUNTY_NAME").toString()).equals("西昌市公司") && !(listuser.get(0).get("COUNTY_NAME").toString()).equals("宝轮营销部")) {
                        COMPANY_NAME = listuser.get(0).get("COMPANY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(receiptApply.getCreateDate()), COMPANY_NAME, "省公司");//创建日期金额
                        logger.info("推送boss正补收额度配额信息市公司" + COMPANY_NAME);
                        logger.info("推送boss正补收额度配额信息市公司配额" + JSONObject.fromObject(oneself));
                    } else {
                        COMPANY_NAME = listuser.get(0).get("COUNTY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(receiptApply.getCreateDate()), COMPANY_NAME, listuser.get(0).get("COMPANY_NAME").toString());//创建日期金额
                        logger.info("推送boss正补收额度配额信息区县" + COMPANY_NAME);
                        logger.info("推送boss正补收额度配额信息区县配额" + JSONObject.fromObject(oneself));
                    }
                    //预占额度
                    //BigDecimal preemptedAmount = new BigDecimal(Double.parseDouble(oneself.getPreemptedAmount()));
                    //申请金额
                    //BigDecimal totalAmount = new BigDecimal(Double.parseDouble(receiptApply.getTotalAmount()) * 100);
                    if (Double.valueOf(String.format("%.2f", Double.parseDouble(receiptApply.getTotalAmount()) * 100)) > Double.valueOf(String.format("%.2f", Double.parseDouble(oneself.getPreemptedAmount())))) {
                        //if (Double.parseDouble(receiptApply.getTotalAmount()) * 100 > Double.parseDouble(oneself.getPreemptedAmount())) {
                        logger.info("总申请额度==》" + Double.valueOf(String.format("%.2f", Double.parseDouble(receiptApply.getTotalAmount()) * 100)));
                        logger.info("总预占额度==》" + Double.valueOf(String.format("%.2f", Double.parseDouble(oneself.getPreemptedAmount()))));
                        Write("NO");
                        throw new RuntimeException("预占额度不足，请确认！");
                    }
                    //RemainAmount 预占额度
                    oneself.setPreemptedAmount(
                            decimalFormat.format(
                                    Double.parseDouble(oneself.getPreemptedAmount()) - Double.parseDouble(receiptApply.getTotalAmount()) * 100
                            )
                    );
                    //ComentTotalAmount 已分配额度
                    oneself.setComentTotalAmount(
                            decimalFormat.format(
                                    Double.parseDouble(oneself.getComentTotalAmount()) + Double.parseDouble(receiptApply.getTotalAmount()) * 100
                            )
                    );
                    receiptApplyAmountService.UpdateReceiptApplyAmount(oneself);//更新额度
                    //System.out.println("更新后=="+JSONObject.fromObject(receiptApplyAmount));
                }
                return "OK";
            }
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("正负补收推送接口错误：" + e.getMessage(), e);
            return "NON";
        }
    }

    /**
     * 冲正调用boss接口推送信息
     */
    public String setBossServiceOne(String serialNo, ReceiptApply receiptApply, SystemUser user) {
        ReceiptApplyDet receiptApplyDet = receiptApplyService.getReceiptApplyDetSerialNo(serialNo);

        String url = ESB_URL_172 + "s2400Cfm";
        if(isES){
            url = ESB_URL_38 + "s2400Cfm";
        }
        List<SystemDept> deptList = user.getSystemDept();
        String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();
        String code = receiptApplyService.getCompanyRegion(companyCode);
        int s = Integer.parseInt(code) - 100;
        JSONObject object = new JSONObject();
        if ("1".equals(receiptApplyDet.getContrctPattern())) {
            object.put("FAST_COLL_FLAG", "1");
            object.put("PHONE_NO", "");
        } else {
            object.put("FAST_COLL_FLAG", "0");
            object.put("PHONE_NO", receiptApplyDet.getProductNo() == null ? "" : receiptApplyDet.getProductNo());
        }
        object.put("CONTRACT_NO", receiptApplyDet.getContrctNo() == null ? "" : receiptApplyDet.getContrctNo());
        object.put("UNIT_ID", receiptApply.getUNIT_ID());
        object.put("AUDIT_SN", receiptApplyDet.getSerialNo());
        object.put("REGION_ID", s + "");
        object.put("OPR_TYPE", "1");
        object.put("REMARK", receiptApply.getApplyTitle() == null ? "" : receiptApply.getApplyTitle());
        NumberFormat format = NumberFormat.getInstance();
        Number number;
        try {
            number = format.parse(receiptApplyDet.getAmount());
            double temp = number.doubleValue() * 100.0;
            format.setGroupingUsed(false);
            //设置返回数的小数部分所允许的最大位数
            format.setMaximumFractionDigits(0);
            String amount = format.format(temp);
            if (receiptApply.getApplyType() == -1) {
                object.put("ADJ_FEE", "-" + amount);
            } else {
                object.put("ADJ_FEE", amount);
            }
        } catch (ParseException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
        object.put("LOGIN_NO", user.getBossUserName());
        //String json = setParamObj(object,receiptApplyDet.getProductNo());
        String json = ESBReqMsgUtil.packMsgByRoute("14", user.getBossUserName(), object);
        String jsonString = UrlConnection.responseGBK(url, json.toString());
        JSONObject jsthree = JSONObject.fromObject(jsonString);
        String datatwo = jsthree.getString("res");
        JSONObject jsone = JSONObject.fromObject(datatwo);
        JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
        if ("0".equals(jstwo.getString("RETURN_CODE"))) {
            receiptApplyDet.setBossStatus("1");
            receiptApplyDet.setState("1");
            receiptApplyService.updateReceiptApplyDet(receiptApplyDet);
            return "0";
        } else {
            return "操作失败,(注:账户号码为" + receiptApplyDet.getContrctNo() + "未冲正成功," + jstwo.getString("RETURN_MSG") + ")";
        }
    }



    /**
     * 调用boss接口推送信息
     */
    public void getBossServiceOne() {
        String id = getString("id");
        String str = "";
        int count = 0;
        try {
            String url = ESB_URL_172 + "s2400Qry";
            if(isES){
                url = ESB_URL_38 + "s2400Qry";
            }
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
            List<ReceiptApplyDet> list = receiptApplyService.getReceiptApplyDetBoss(receiptApply.getApplyNO());
            List<SystemDept> deptList = user.getSystemDept();
            String companyCode = deptList.get(0).getSystemCompany().getCompanyCode();
            String code = receiptApplyService.getCompanyRegion(companyCode);
            int s = Integer.parseInt(code) - 100;
            String auditSn = "";
            for (int i = 0; i < list.size(); i++) {
                JSONObject object = new JSONObject();
                object.put("PHONE_NO", list.get(i).getProductNo() == null ? "" : list.get(i).getProductNo());
                object.put("AUDIT_SN", list.get(i).getSerialNo() == null ? "" : list.get(i).getSerialNo());
                object.put("BILL_CYCLE", "");
                object.put("LOGIN_NO", user.getBossUserName());
                String json = setParamObj(object, list.get(i).getProductNo());
                logger.info("正负补收查询调用接口输入参数：" + json);
                String jsonString = UrlConnection.responseGBK(url, json.toString());
                logger.info("正负补收查询调用接口返回参数：" + jsonString);
                JSONObject jsthree = JSONObject.fromObject(jsonString);
                String datatwo = jsthree.getString("res");
                JSONObject jsone = JSONObject.fromObject(datatwo);
                JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
                if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                    JSONObject jsfour = JSONObject.fromObject(jstwo.getString("OUT_DATA"));
                    if (jsfour.containsKey("ADJ_INFO")) {
                        JSONObject jsfive = JSONObject.fromObject(jsfour.getString("ADJ_INFO"));
                        if ("1".equals(jsfive.getString("STATUS"))) {
                            count += 1;
                            auditSn += jsfive.getString("AUDIT_SN") + "、";
                        }
                    }
                }
            }
            if (count == 0) {
                str = "接口查询失败,该工单暂时不能冲正，请确认BOSS端账户是否使用!";
            } else {
                int countTwo = 0;
                String[] orderNumber = auditSn.split("、");
                for (int j = 0; j < orderNumber.length; j++) {
                    String ss = setBossServiceOne(orderNumber[j], receiptApply, user);
                    if ("0".equals(ss)) {
                        countTwo += 1;
                    } else {
                        str += "编号为:" + orderNumber[j] + "冲正失败[" + ss + "]";
                    }
                }
                if (count == list.size()) {
                    if (count == countTwo) {
                        receiptApply.setState("3");
                        receiptApply.setBossStatus("1");
                        receiptApplyService.updateReceiptApply(receiptApply);
                        str = "OK";
                    } else {
                        receiptApply.setState("4");
                        receiptApply.setBossStatus("1");
                        receiptApplyService.updateReceiptApply(receiptApply);
                    }
                } else {
                    receiptApply.setState("4");
                    receiptApply.setBossStatus("1");
                    receiptApplyService.updateReceiptApply(receiptApply);
                    if (count == countTwo) {
                        str = "OK";
                    }
                }
            }
            Write(str);
        } catch (Exception e) {
            e.printStackTrace();
            logger.error("正负补收冲正错误抛出！" + e.getMessage(), e);
            Write("NON");
        }
    }

    /**
     * json 数据格式化：
     *
     * @param body
     * @return
     */
    protected String setParamObj(JSONObject body, String productNo) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "10");
        routing.put("ROUTE_VALUE", productNo);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        System.out.println(root.toString());
        return root.toString();
    }

    /**
     * 完成代办生成
     *
     * @param receiptApply
     * @param userid
     * @param processId
     * @param user
     * @param receiptApplyTask
     * @param type
     */
    public void daiban(ReceiptApply receiptApply, String userid, String processId, SystemUser user, ReceiptApplyTask receiptApplyTask, String type) {
        try {
            String ktype = "K";
            WaitTask wt = new WaitTask();
            wt.setName("[正负补收]" + receiptApply.getApplyTitle());
            wt.setCreationTime(new Date());
            wt.setUrl("jsp/receiptApplys/seeReceiveApply.jsp?id=" + receiptApply.getId() + "&receiptApplyTaskId="
                    + receiptApplyTask.getUuid() + "&processId=" + processId + "&type=" + type + "&ktype=" + ktype);
            SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
            wt.setState(WaitTask.HANDLE);
            wt.setHandleUserId(USER.getRowNo());
            wt.setHandleUserName(USER.getEmployeeName());
            wt.setHandleLoginName(USER.getLoginName());
            wt.setCreateUserId(user.getRowNo());
            wt.setCreateUserName(user.getEmployeeName());
            wt.setCreateLoginName(user.getLoginName());
            wt.setCode(ReceiptApply.ReceiptApply);
            wt.setTaskId(receiptApply.getId());
            service.saveWaitPushMOA(wt, this.getRequest());
        } catch (NumberFormatException e) {
            e.printStackTrace();
        }
    }

    /**
     * 提交待办生成
     *
     * @param receiptApply
     * @param userid
     * @param processId
     * @param user
     * @param receiptApplyTask
     */
    public void daibantwo(ReceiptApply receiptApply, String userid, String processId, SystemUser user, ReceiptApplyTask receiptApplyTask) {
        WaitTask wt = new WaitTask();
        wt.setName("[正负补收]" + receiptApply.getApplyTitle());
        wt.setCreationTime(new Date());
//		wt.setUrl("jsp/receiptApplys/seeReceiveApply.jsp?id=" + receiptApply.getId() + "&processId="// 流程ID
//				+ processId
//				+ "&receiptApplyTaskId="
//				+ receiptApplyTask.getUuid());
        wt.setUrl("jsp/receiptApplys/seeReceiveApply.jsp");
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
        wt.setState(WaitTask.HANDLE);
        wt.setHandleUserId(USER.getRowNo());
        wt.setHandleUserName(USER.getEmployeeName());
        wt.setHandleLoginName(USER.getLoginName());
        wt.setCreateUserId(user.getRowNo());
        wt.setCreateUserName(user.getEmployeeName());
        wt.setCreateLoginName(user.getLoginName());
        wt.setCode("ZFBS");
        wt.setOrderNo(receiptApply.getApplyNO());
        wt.setTaskId(receiptApplyTask.getUuid());//task的id
//		service.saveWait(wt, this.getRequest());
        service.saveWaitPushMOA(wt, this.getRequest());
    }

    /**
     * 退回待办生成
     *
     * @param receiptApply
     * @param userid
     * @param waitId
     * @param user
     */
    public void daibanthree(ReceiptApply receiptApply, String userid, String waitId, SystemUser user, ReceiptApplyTask receiptApplyTask) {
        WaitTask wt = new WaitTask();
        wt.setName("[正负补收退回]" + receiptApply.getApplyTitle());
        wt.setCreationTime(new Date());
//		wt.setUrl("jsp/receiptApplys/returnReceiveApply.jsp?id=" + receiptApply.getId());// 这是需要改动的退回以后的页面地址
        wt.setUrl("jsp/receiptApplys/returnReceiveApply.jsp");
        SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
        wt.setState(WaitTask.HANDLE);
        wt.setHandleUserId(USER.getRowNo());
        wt.setHandleUserName(USER.getEmployeeName());
        wt.setHandleLoginName(USER.getLoginName());
        wt.setCreateUserId(user.getRowNo());
        wt.setCreateUserName(user.getEmployeeName());
        wt.setCreateLoginName(user.getLoginName());
        wt.setCode("ZFBS");
        wt.setTaskId(receiptApplyTask.getUuid());//taskId
        wt.setOrderNo(receiptApply.getApplyNO());
        service.saveWaitPushMOA(wt, this.getRequest());
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDate(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换
     *
     * @param currentTime
     * @return
     */
    public static String getStringDatetwo(Date currentTime) {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(currentTime);
        return dateString;
    }

    /**
     * 日期转换
     *
     * @return
     */
    public static String getStringDatethree() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.SECOND, 2);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String dateString = formatter.format(calendar.getTime());
        return dateString;
    }

    /**
     * 导入数据
     */
    public void importReceiptApply() {
        try {
            ExcelUtil excelReader = new ExcelUtil(file1);
            //InputStream is = new FileInputStream(file1);
            //Workbook wb=new XSSFWorkbook(is);
            //Sheet sheet =wb.getSheetAt(0);
            //int column =sheet.getRow(0).getPhysicalNumberOfCells();
            //对读取Excel表格内容测试
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
            for (int i = 1; i <= map.size(); i++) {
                Map<String, Object> maps = new HashMap<String, Object>();
                maps.put("contrctNo", map.get(i).get(0));//账户号码
                maps.put("contrctType", map.get(i).get(1));//账户类型
                maps.put("productNo", map.get(i).get(2));//服务号码
                maps.put("idNo", map.get(i).get(3));//用户号码
                String regEx = "[\\u4e00-\\u9fa5]";
                Pattern pat = Pattern.compile(regEx);
                String str = (String) map.get(i).get(4);
                Matcher matcher = pat.matcher(str);
                if (matcher.find()) {
                    Write("MONEY");
                    return;
                } else {
                    maps.put("amount", map.get(i).get(4));//金额

                }
                list.add(maps);
            }
            String json2 = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(list);
            if (map.size() > 0) {
                Write(json2);
            } else {
                Write("NULL");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    public void downLoadReceiptApply() {
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest req = ServletActionContext.getRequest();
            String name = "账户信息模板";
            String filepath = req.getRealPath("/template/receiptApply.xlsx");
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.addHeader("Content-Length", "" + data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 导出Excel
     */
    public void dwonloadExcel() {
        try {
            String serialNo = getString("serialNo");//工单流水
            String applyNO = getString("applyNO");//账户流水
            String contrctNo = getString("contrctNo");//账户号码
            String productNo = getString("productNo");//服务号码
            String companyCodes = getString("selectcon");//地市
            SystemUser user = this.user;// 获取当前用户
            List list = receiptApplyService.findRoleByRowNo(user.getRowNo()); // 获取用户权限
            boolean flag = false;

            for (int i = 0; i < list.size(); i++) {
                if ((list.get(i).toString()).equals("16") || (list.get(i).toString()).equals("2297")) {
                    flag = true;
                    break;
                }
            } // 获取公司编码
            List<Map<String, Object>> mapList = receiptApplyService.getReceiptApplyDetExcel(serialNo, applyNO, contrctNo, productNo, companyCodes, user,flag);
            receiptApplyService.downloadExcel(mapList);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 导出Excel
     */
    public void getBossStuts() {
        try {
            String id = getString("id");//工单流水
            ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
            if ("".equals(receiptApply.getBossStatus())) {
                Write("1");
            } else {
                Write(receiptApply.getBossStatus());
            }
        } catch (Exception e) {
            logger.error("验证正负补收状态有误:" + e.getMessage(), e);
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }

    public void abolish() {
        try {
            DecimalFormat decimalFormat = new DecimalFormat("0");
            String id = getString("id");// 转账信息id
            String opinion = getString("opinion");//作废原因
            String waitId = getString("waitId");
            String receiptApplyTaskID = getString("receiptApplyTaskId");
            ReceiptApplyTask receiptApplyTask = receiptApplyService.getTaskList(receiptApplyTaskID);// 根据流程id查询任务表信息
            WaitTask wait = service.queryWaitByTaskId(waitId);
            if (wait != null) {
                service.updateWait(wait, this.getRequest());
                ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
                receiptApply.setState("-1");
                receiptApplyService.updateReceiptApply(receiptApply);
                if (receiptApply.getApplyType() == -1) {
                    //判断有多少推送的单子,有就预占-,已使用+
                    //判断有多少未推送的单子,有就预占-,剩余+
                    Double reduce = 0.0;//推送
                    Double increase = 0.0;//未推送
                    List<ReceiptApplyDet> list = receiptApplyService.getReceiptApplyDetBossStatus(receiptApply.getApplyNO());
                    for (int i = 0; i < list.size(); i++) {
                        ReceiptApplyDet det = list.get(i);
                        if ("0".equals(det.getBossStatus())) {
                            reduce += Double.valueOf(det.getAmount());
                            //amount
                        } else {
                            increase += Double.valueOf(det.getAmount());
                            //amount
                        }
                    }
                    List<Map<String, Object>> listtwo = receiptApplyAmountService.getVwUserNew(receiptApply.getCreator());
                    String COMPANY_NAME = "";
                    ReceiptApplyAmount oneself = new ReceiptApplyAmount();
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
                    //logger.info("正负补收工单信息=="+JSONObject.fromObject(receiptApply));
                    if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                        COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyName(simpleDateFormat.format(receiptApply.getCreateDate()), "省公司");
                    } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).equals("西昌市公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).equals("宝轮营销部")) {
                        COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(receiptApply.getCreateDate()), COMPANY_NAME, "省公司");
                        logger.info("作废正补收额度配额信息市公司" + COMPANY_NAME);
                        logger.info("作废正补收额度配额信息市公司配额" + JSONObject.fromObject(oneself));
                    } else {
                        COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
                        oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(receiptApply.getCreateDate()), COMPANY_NAME, listtwo.get(0).get("COMPANY_NAME").toString());
                        logger.info("作废正补收额度配额信息区县" + COMPANY_NAME);
                        logger.info("作废正补收额度配额信息区县配额" + JSONObject.fromObject(oneself));
                    }
                    //RemainAmount 预占额度
                    oneself.setPreemptedAmount(
                            decimalFormat.format(
                                    Double.parseDouble(oneself.getPreemptedAmount()) - reduce * 100 - increase * 100
                            )
                    );
                    //RemainAmount 剩余的额度
                    oneself.setRemainAmount(
                            decimalFormat.format(
                                    Double.parseDouble(oneself.getRemainAmount()) + increase * 100
                            )
                    );
                    //ComentTotalAmount 已使用金额
                    oneself.setComentTotalAmount(
                            decimalFormat.format(
                                    Double.parseDouble(oneself.getComentTotalAmount()) + reduce * 100
                            )
                    );
                    receiptApplyAmountService.UpdateReceiptApplyAmount(oneself);//更新额度
//					//System.out.println("更新后=="+JSONObject.fromObject(receiptApplyAmount));
                }
                if (receiptApplyTask != null) {
                    receiptApplyTask.setOperDate(getStringDate(new Date()));// 操作时间
                    receiptApplyTask.setReplyContent(opinion);//审批意见
                    receiptApplyTask.setStatus("2");
                    receiptApplyService.updateTask(receiptApplyTask);//修改任务表
                }
            } else {
                Write("NO");
                throw new RuntimeException(" 给事务回滚，代办有误");
            }
            Write("OK");
        } catch (Exception e) {
            logger.error("正负补收作废方法有误:" + e.getMessage(), e);
//			e.printStackTrace();
            Write("NO");
            throw new RuntimeException(" 给事务回滚，正负补收作废方法有误");
        }
    }

    public void getContracNo() {
        try {
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String contrctNo = getString("contrctNo");//账户
            String json = receiptApplyService.getContracNo(page, contrctNo);
            Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 调用boss接口推送信息
     */
    public void sUserConInfo() {
        String pboneNo = getString("pboneNo");//服务号码
        String type = getString("type");//2离网,0在网
        String url = ESB_URL_172 + "sUserConInfo";
        if(isES){
            url = ESB_URL_38 + "sUserConInfo";
        }
        try {
            JSONObject object = new JSONObject();
            object.put("PHONE_NO", pboneNo);
            object.put("USER_FLAG", type);
            object.put("OP_CODE", "8006");
            String json = setParamObj(object, pboneNo);
            String jsonString = UrlConnection.responseGBK(url, json);
            JSONObject jsthree = JSONObject.fromObject(jsonString);
            String datatwo = jsthree.getString("res");
            JSONObject jsone = JSONObject.fromObject(datatwo);
            JSONObject jstwo = JSONObject.fromObject(jsone.getString("ROOT"));
            if ("0".equals(jstwo.getString("RETURN_CODE"))) {
                JSONObject outData = JSONObject.fromObject(jstwo.getString("OUT_DATA"));
                Write(outData.toString());
            } else {
                Write("NO");
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("NON");
        }
    }

    public void contractUpload() {
        try {
            if (file1 != null) {
                Long time = System.currentTimeMillis();
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                StorageCfg storageCfg = attachmentService.queryStorageCfg();
                String ftpUrl = storageCfg.getFileName() + urlDate;
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                String pixstr = FileUpload.getFilePix(file1FileName);
                if (StringUtils.isEmpty(pixstr)) {
                    Write(returnPars(-1, "", "未获取到文件类型"));
                }
                if (FileUpload.upload(ftpUrl, file1, time + pixstr)) {
                    JSONObject obj = new JSONObject();
                    obj.put("filePath", ftpUrl + time + pixstr);
                    obj.put("fileName", file1FileName);
                    Write(returnPars(0, obj.toString(), ""));
                } else {
                    Write(returnPars(-1, "", "上传文件失败"));
                }
            } else {
                Write(returnPars(-1, "", "文件为空"));
            }
        } catch (Exception e) {
            logger.error("正负补收合同上传" + e.getMessage(), e);
            Write(returnPars(-1, "", "上传文件未知错误" + e.getMessage()));
        }
    }

    /**
     * @author: liyang
     * @date: 2021/1/20 11:21
     * @Version: 1.0
     * @param: String
     * @return: String
     * @Description: TODO 返回参数生成
     */
    private static String returnPars(int state, String data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    /**
     * @author: liyang
     * @date: 2021/6/29 18:02
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO FTP文件上传
     */
    public boolean uploadTxt(File file) {
        FTPClient ftpClient = getFTPClient();
        boolean bl = false;
        String path = "/ftpsale/billingfile/";
        if (!path.endsWith("/")) {//判读路径是否以“/”结尾
            path += "/";
        }
        try {
            if (file != null) {
                ftpClient.makeDirectory(path);
                ftpClient.changeWorkingDirectory(path);
                ftpClient.setFileType(FTP.BINARY_FILE_TYPE);
                FileInputStream input = new FileInputStream(file);
                ftpClient.storeFile(file.getName(), input);
                input.close();
                bl = true;
            }
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            logger.error("正负补收推送合同文件错误：" + e.getMessage(), e);
            bl = false;
        }
        return bl;
    }

    /**
     * @author: liyang
     * @date: 2021/6/29 18:03
     * @Version: 1.0
     * @param:
     * @return:
     * @Description: TODO 连接FTP
     * ReceiptApplyAction_uploadContratcFileFtp.action
     */
    public static FTPClient getFTPClient() {
        FTPClient ftpClient = new FTPClient();
        try {
            ftpClient = new FTPClient();
            ftpClient.connect("***********", 21);// 连接FTP服务器
            ftpClient.login("ftpsale", "#=3hZy%7");// 登陆FTP服务器
            if (!FTPReply.isPositiveCompletion(ftpClient.getReplyCode())) {
                logger.info("未连接到FTP，用户名或密码错误。");
                ftpClient.disconnect();
            } else {
                System.out.println("FTP连接成功。");
                logger.info("FTP连接成功。");
            }
        } catch (IOException e) {
            e.printStackTrace();
            logger.error("正负补收推送接口错误：" + e.getMessage(), e);
        }
        return ftpClient;
    }

    public void downloadContract() {
        String filePath = this.getString("filePath");
        String fileName = this.getString("fileName");
        try {
            if (!StringUtils.isBlank(filePath)) {
                HttpServletResponse response = this.getResponse();
                byte[] data = FileUtil.getContent(filePath);
                logger.info("合同下载名称：" + fileName);
                response.reset();
                response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream;charset=UTF-8");
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();
            } else {
                logger.error("参数错误：" + filePath + "==" + fileName);
                Write("error");
            }
        } catch (Exception ex) {
            Write("error");
        }
    }

    public static String getBillMonth() {
        Calendar cal = Calendar.getInstance();
        int day = cal.get(Calendar.DATE);
        int month = cal.get(Calendar.MONTH) + 1;
        int year = cal.get(Calendar.YEAR);
        System.out.println("日期: " + day);
        System.out.println("月份: " + month);
        System.out.println("年份: " + year);
        String date = "";
        if (month < 10) {
            date = String.valueOf(year) + "0" + String.valueOf(month);
        } else {
            date = String.valueOf(year) + String.valueOf(month);
        }
        return date;
    }

    public void findTransition() {
        String id = getString("id");
        ReceiptApplyProcess rap = receiptApplyService.getPid(id);
        String pid = rap.getProcessId();
        JbpmTest jt = new JbpmTest();
        if (!"".equals(pid) && pid != null) {
            // 获取任务对象
            try {
                logger.info("所查询的流程ID：=======》" + pid);
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                //JSONArray jArray = new JSONArray();
                logger.info("当前流程节点====》" + task.getActivityName());
                JSONObject obj = new JSONObject();
                for (String outcome : setlist) {
                    if (!outcome.equals("ALL")) {
                        obj.put("transitionName", outcome);//选人
                        logger.info("当前可执行的流程走向====》" + outcome);
                    }
                    //jArray.add(obj);
                }
                obj.put("dangqianrenwu", task.getActivityName());//当前角色名字
                obj.put("pid", pid);//流程id
                System.out.println("==" + obj.toString());
                writeText(obj.toString());
            } catch (Exception e) {
                // TODO: handle exception
                logger.info("流程信息获取异常====》" + e);
                writeText("流程信息获取异常");
            }
        } else {
            writeText("流程ID为空");
        }
    }


    /**
     * 获取当前流程按钮 (手机端用)
     *
     * @throws ParseException
     */
    public void findTransitionApp() throws ParseException {
        Result r = new Result();
        String id = getString("id");
        String phone = getString("phone");
        List outcomes = new ArrayList<>();
        SystemUser user = new SystemUser();
        try {
            user = systemUserService.getUserByPhone(phone);
            //System.out.println("获取的loginName为=="+user.getLoginName());
            user = systemUserService.querUsers(user.getLoginName());
            if (user == null) {
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("查询人员失败，号码有误");
                Write(r.toString());
                return;
            }
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("根据号码查询人员异常");
            Write(r.toString());
            return;
        }
        ReceiptApplyProcess rap = receiptApplyService.getPid(id);
        ReceiptApply receiptApply = receiptApplyService.getReceiptApply(id);
        String pid = rap.getProcessId();
        //JbpmTest jt = new JbpmTest();
        if (!"".equals(pid) && pid != null) {
            // 获取任务对象
            try {
                logger.info("所查询的流程ID：=======》" + pid);
                Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();
                Set<String> setlist = jbpmUtil.findOutComesByTaskId(task.getId());
                //JSONArray jArray = new JSONArray();
                List buttons = new ArrayList<>();
                logger.info("当前流程节点====》" + task.getActivityName());
                String str = pid.substring(0, pid.indexOf("."));
                //logger.info("测试str=="+str+"===="+task.getActivityName());

                JSONObject obj = new JSONObject();
                for (String outcome : setlist) {
                    if (!outcome.equals("结束")) {
                        outcomes.add(outcome);
                    }
                    //jArray.add(obj);
                }
                if ("ReceipyApplySceneProcessFinal".equals(str)) {
                    if (task.getActivityName().equals("市公司客户经理室经理") || task.getActivityName().equals("市公司业务管理员") || task.getActivityName().equals("市公司业务管理室经理")
                            || task.getActivityName().equals("省公司政企业务管理室") || task.getActivityName().equals("区县业务管理员") || task.getActivityName().equals("区县政企部主任")
                            || task.getActivityName().equals("省重客客户经理室经理") || task.getActivityName().equals("省重客业务管理员") || task.getActivityName().equals("省重客分管经理")) {
//						dangqianrenwu == '市公司客户经理室经理' || dangqianrenwu == '市公司业务管理员' || dangqianrenwu == '市公司业务管理室经理' || dangqianrenwu == '省公司管理员'
//								|| dangqianrenwu == '省公司政企业务管理室' || dangqianrenwu == '区县业务管理员' || dangqianrenwu == '区县政企部主任'|| dangqianrenwu == '省重客客户经理室经理'
//								|| dangqianrenwu == '省重客业务管理员'|| dangqianrenwu == '省重客分管经理'
                        //提交
                        buttons.add("提交");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("省公司管理员")) {
                        buttons.add("提交");
                        buttons.add("转审");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司政企部经理") || task.getActivityName().equals("市公司领导") || task.getActivityName().equals("区县分管经理")
                            || task.getActivityName().equals("省重客业务管理室经理")) {
                        List<SystemDept> deptList = user.getSystemDept();
                        String code = deptList.get(0).getSystemCompany().getCompanyCode();
                        TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                        if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
                            if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
                                buttons.add("提交");
                            } else {
                                buttons.add("完成");
                            }
                        } else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                            buttons.add("提交");
                        }
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("省公司政企客户部经理副") || task.getActivityName().equals("省公司政企客户部经理正")) {
                        buttons.add("提交");
                        buttons.add("完成");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    }else if ( task.getActivityName().equals("其他审批人10") || task.getActivityName().equals("其他审批人9") || task.getActivityName().equals("其他审批人3")
                            || task.getActivityName().equals("其他审批人4") || task.getActivityName().equals("其他审批人5") || task.getActivityName().equals("其他审批人6")
                            || task.getActivityName().equals("其他审批人") || task.getActivityName().equals("其他审批人2")
                            || task.getActivityName().equals("其他审批人7") || task.getActivityName().equals("其他审批人8")) {
                        buttons.add("提交");
                        buttons.add("转审");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    }
                } else {
                    if (task.getActivityName().equals("市公司客户经理室经理") || task.getActivityName().equals("省重客客户经理室经理") || task.getActivityName().equals("省重客业务管理员")
                    ) {
                        //提交
                        buttons.add("提交");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("区县业务管理员") || task.getActivityName().equals("区县政企部主任")) {
                        if ("ReceipyApplyProcessFinal".equals(str)) {
                            List<SystemDept> deptList = user.getSystemDept();
                            String code = deptList.get(0).getSystemCompany().getCompanyCode();
                            TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                            if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
                                if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
                                    buttons.add("提交");
                                } else {
                                    buttons.add("完成");
                                }
                            } else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                                buttons.add("提交");
                            }
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        } else {
                            buttons.add("提交");
                            obj.put("pid", pid);
                            obj.put("buttons", buttons);
                            obj.put("outcomes", outcomes);
                            r.setCode(ResultCode.SUCCESS);
                            r.setMessage("成功");
                            r.setData(obj);
                        }
                    } else if (task.getActivityName().equals("区县分管经理") || task.getActivityName().equals("市公司领导") || task.getActivityName().equals("省公司业务管理室经理")
                            || task.getActivityName().equals("省公司分管经理") || task.getActivityName().equals("省公司政企客户部经理副") || task.getActivityName().equals("省公司政企客户部经理正")) {
                        List<SystemDept> deptList = user.getSystemDept();
                        String code = deptList.get(0).getSystemCompany().getCompanyCode();
                        TransferCitiesData transferCitiesData = new TransferCitiesData();
                        if (task.getActivityName().equals("省公司政企客户部经理副") || task.getActivityName().equals("省公司政企客户部经理正")) {
                            transferCitiesData = receiptApplyService.getTransferCitiesData(code, "省公司政企客户部经理");
                            //System.out.println("省公司政企客户部经理(正/副)=="+transferCitiesData);
                            buttons.add("提交");
                            buttons.add("完成");
                        } else {
                            transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                            if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
                                if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
                                    buttons.add("提交");
                                } else {
                                    buttons.add("完成");
                                }
                            } else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                                buttons.add("提交");
                            }
                        }
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司业务管理员") || task.getActivityName().equals("市公司业务管理室经理") || task.getActivityName().equals("其他审批人10")
                            || task.getActivityName().equals("其他审批人9") || task.getActivityName().equals("其他审批人3") || task.getActivityName().equals("省公司政企部业务管理室")
                            || task.getActivityName().equals("其他审批人4") || task.getActivityName().equals("其他审批人5") || task.getActivityName().equals("其他审批人6")
                            || task.getActivityName().equals("其他审批人") || task.getActivityName().equals("其他审批人2") || task.getActivityName().equals("省公司政企业务管理室")
                            || task.getActivityName().equals("其他审批人7") || task.getActivityName().equals("其他审批人8")) {
                        buttons.add("提交");
                        buttons.add("转审");
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    } else if (task.getActivityName().equals("市公司政企部经理副") || task.getActivityName().equals("市公司政企部经理") || task.getActivityName().equals("省公司管理员")) {
                        List<SystemDept> deptList = user.getSystemDept();
                        String code = deptList.get(0).getSystemCompany().getCompanyCode();
                        if (task.getActivityName().equals("市公司政企部经理副") || task.getActivityName().equals("市公司政企部经理")) {
                            TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, "市公司政企部经理");
                            if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
                                if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
                                    buttons.add("提交");
                                    buttons.add("转审");
                                } else {
                                    buttons.add("完成");
                                    buttons.add("转审");
                                }
                            } else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                                buttons.add("提交");
                                buttons.add("转审");
                            }
                        } else {
                            TransferCitiesData transferCitiesData = receiptApplyService.getTransferCitiesData(code, task.getActivityName());
                            if (Double.parseDouble(transferCitiesData.getAmount()) > 0) {
                                if (Double.parseDouble(receiptApply.getTotalAmount()) >= Double.parseDouble(transferCitiesData.getAmount())) {
                                    buttons.add("提交");
                                    buttons.add("转审");
                                } else {
                                    buttons.add("完成");
                                    buttons.add("转审");
                                }
                            } else if (Double.parseDouble(transferCitiesData.getAmount()) == 0) {
                                buttons.add("提交");
                                buttons.add("转审");
                            }
                        }
                        obj.put("pid", pid);
                        obj.put("buttons", buttons);
                        obj.put("outcomes", outcomes);
                        r.setCode(ResultCode.SUCCESS);
                        r.setMessage("成功");
                        r.setData(obj);
                    }
                }
                //System.out.println("获取的角色为=="+r.toString());
                Write(r.toString());
            } catch (Exception e) {
                // TODO: handle exception
                logger.info("正负补收手机端按钮流程信息获取异常====》" + e);
                r.setCode(ResultCode.FAIL);
                r.setMessage("失败");
                r.setData("流程信息获取异常");
                Write(r.toString());
            }
        } else {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("流程ID为空");
            Write(r.toString());
        }
    }

    public void findComprehensiveById() {
        Result r = new Result();
        Map<String, Object> map = new HashMap<>();
        try {
            String id = getString("id");
            String orderNo = getString("orderNo");
            ReceiptApply receiptApply = new ReceiptApply();
            if (id != null && !"".equals(id) && !"null".equals(id)) {
                receiptApply = receiptApplyService.getReceiptApplyNumber(id);
            } else {
                receiptApply = receiptApplyService.getReceiptApplyApplyNO(orderNo);
            }
            List<ReceiptApplyDet> receiptApplyDet = receiptApplyService.getReceiptApplyDet(receiptApply.getApplyNO());//详情
            logger.info("receiptApply.getId()=="+receiptApply.getId());
            List<ReceiptApplyTask> p = receiptApplyService.processtracking(receiptApply.getId());
            //System.out.println("数据为3=="+JSONObject.fromObject(p));
            map.put("findById", receiptApply);
            map.put("receiptApplyDet", receiptApplyDet);
            map.put("processtracking", p);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(map);
            //System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormat(r));
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(r));
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData("查询详情失败");
            Write(r.toString());
        }
    }


    /**
     * 新增工单验证权限
     */
    public void authorityManagement() {
        try {
            boolean judge = false;
            String path = "/EOMAPP/UploadFiles/test.txt";//服务器上文件路径
            //String path = "D:\\20210204\\test.txt";//服务器上文件路径
            File fileObj = new File(path);
            // 如果文件不存在，直接返回
            if (!fileObj.exists()) {
                //System.out.println("JUDGE");
                Write("JUDGE");
                return;
            }
            FileInputStream fis = new FileInputStream(fileObj);
            BufferedReader bf = new BufferedReader(new InputStreamReader(fis, "UTF-8"));
            String line = null;
            while ((line = bf.readLine()) != null) {
                String[] item = line.split("\\|");
                try {
                    if (item[0].equals(user.getMobile()) || item[1].equals(user.getLoginName())) {
                        judge = true;
                    }
                } catch (Exception e) {
                    judge = true;
                    logger.info("新增工单验证权限查询数据失败==" + e.getMessage());
                }
            }
            if (judge) {
                System.out.println("NO");
                Write("NO");
            } else {
                System.out.println("OK");
                Write("OK");
            }
        } catch (Exception e) {
            Write("NO");
            logger.info("正负补收新增工单验证权限失败==>" + e.getMessage());
        }
    }

    public void verificationQuota() {
        try {
            String totalAmount = getString("totalAmount");
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy");
            List<Map<String, Object>> listtwo = receiptApplyAmountService.getVwUser(user);
            String COMPANY_NAME = "";
            ReceiptApplyAmount oneself = new ReceiptApplyAmount();
            if (listtwo.get(0).get("COMPANY_NAME").equals("省公司")) {
                //logger.info("正补收额度配额信息省公司");
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyName(simpleDateFormat.format(new Date()), COMPANY_NAME);
            } else if (!(listtwo.get(0).get("COUNTY_NAME").toString()).contains("分公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).contains("西昌市公司") && !(listtwo.get(0).get("COUNTY_NAME").toString()).contains("宝轮营销部")) {
                //logger.info("正补收额度配额信息区县==>"+COMPANY_NAME+"==工号==>"+user.getEmployeeName()+"==名字=="+user.getRowNo());
                COMPANY_NAME = listtwo.get(0).get("COMPANY_NAME").toString();
                oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(new Date()), COMPANY_NAME, "省公司");
            } else {
                //logger.info("正补收额度配额信息区县==>"+COMPANY_NAME+"==工号==>"+user.getEmployeeName()+"==名字=="+user.getRowNo());
                COMPANY_NAME = listtwo.get(0).get("COUNTY_NAME").toString();
                //String[] name=user.getEmployeeName().split("_");
                //String CompanyName = name[0];
                oneself = receiptApplyAmountService.getReceiptApplyAmountCompanyNameNew(simpleDateFormat.format(new Date()), COMPANY_NAME, listtwo.get(0).get("COMPANY_NAME").toString());
            }
            logger.info("正补收额度配额信息==>" + COMPANY_NAME + "==工号==>" + user.getEmployeeName() + "==名字==" + user.getRowNo());
            if (oneself == null) {
                Write("YES");
            } else {
                if (Double.parseDouble(oneself.getRemainAmount()) < Double.parseDouble(totalAmount) * 100) {
                    //System.out.println("YES");
                    Write("YES");
                } else {
                    //System.out.println("NO");
                    Write("NO");
                }
            }
        } catch (Exception e) {
            logger.info("验证额度错误==" + e.getMessage());
            //System.out.println(e.getMessage());
            Write("PASS");
        }
    }

    /**
     * 根据ApplyNO获取相关转账信息
     */
    public void getReceiptApplyApplyNO() {
        try {
            String orderNo = getString("orderNo");
            ReceiptApply receipt = receiptApplyService.getReceiptApplyApplyNO(orderNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(receipt));
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
        }
    }

    /**
     * 验证账户号码在当月是否重复申请过
     */
    public void getFindByMonth(){
        try{
            String jsonone = getString("jsonone");
            String x="";
            JSONArray jsonObject = JSONArray.fromObject(jsonone);
            for (int i = 0; i < jsonObject.size(); i++) {
                String s = jsonObject.getString(i);
                JSONObject data = JSONObject.fromObject(s);
                List<ReceiptApply> findByMonth = receiptApplyService.getFindByMonth(String.valueOf(user.getRowNo()),data.getString("contrctNo"));
                if(findByMonth.size()>2){
                    x+=data.getString("contrctNo");
                }
            }
//            System.out.println("x=="+x);
            Write(x);
        }catch (Exception e){
            logger.error("验证账户号码在当月是否重复申请过失败:"+e.getMessage(),e);
            Write("NO");
        }
    }

    public void getFindByYear(){
        try{
            String contrctNo = getString("contrctNo");
            List<Map<String, String>> findByYear = receiptApplyService.getFindByYear(contrctNo);
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(findByYear));
        }catch (Exception e){
            logger.error("验证该集团申请金额失败:"+e.getMessage(),e);
            Write("NO");
        }
    }


    public File getFile1() {
        return file1;
    }

    public String getFile1FileName() {
        return file1FileName;
    }

    public void setFile1FileName(String file1FileName) {
        this.file1FileName = file1FileName;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public TransferJBPMUtils getTransferJBPMUtils() {
        return transferJBPMUtils;
    }

    public void setTransferJBPMUtils(TransferJBPMUtils transferJBPMUtils) {
        this.transferJBPMUtils = transferJBPMUtils;
    }

    public JbpmUtil getJbpmUtil() {
        return jbpmUtil;
    }

    public void setJbpmUtil(JbpmUtil jbpmUtil) {
        this.jbpmUtil = jbpmUtil;
    }

    public ReceiptApplyService getReceiptApplyService() {
        return receiptApplyService;
    }

    public void setReceiptApplyService(ReceiptApplyService receiptApplyService) {
        this.receiptApplyService = receiptApplyService;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public File getBranchFile1() {
        return branchFile1;
    }

    public void setBranchFile1(File branchFile1) {
        this.branchFile1 = branchFile1;
    }

    public String getBranchFile1FileName() {
        return branchFile1FileName;
    }

    public void setBranchFile1FileName(String branchFile1FileName) {
        this.branchFile1FileName = branchFile1FileName;
    }

    public File getProductsFile1() {
        return productsFile1;
    }

    public void setProductsFile1(File productsFile1) {
        this.productsFile1 = productsFile1;
    }

    public String getProductsFile1FileName() {
        return productsFile1FileName;
    }

    public void setProductsFile1FileName(String productsFile1FileName) {
        this.productsFile1FileName = productsFile1FileName;
    }
}
