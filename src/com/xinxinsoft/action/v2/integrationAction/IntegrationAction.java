package com.xinxinsoft.action.v2.integrationAction;

import java.io.ByteArrayInputStream;
import java.io.InputStream;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;

import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.dom4j.Document;
import org.dom4j.Element;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.action.dedicatedFlow.DedicatedFlowAction;
import com.xinxinsoft.entity.basetype.ProductType;
import com.xinxinsoft.entity.basetype.ZProductType;
import com.xinxinsoft.entity.boss.StartPreOrderOut;
import com.xinxinsoft.entity.commonSingManagement.OrderForm;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.contract.Contract;
import com.xinxinsoft.entity.contract.OrderContractManyToOne;
import com.xinxinsoft.entity.core.Dictionary;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.dedicatedFlow.OrderInformation;
import com.xinxinsoft.entity.dedicatedFlow.OrderStages;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.holiday.Holiday;
import com.xinxinsoft.entity.integration.Bis_DemandList;
import com.xinxinsoft.entity.integration.Bis_DemandOrdercontent;
import com.xinxinsoft.entity.integration.Bis_OrderContent;
import com.xinxinsoft.entity.integration.Bis_ProcessList;
import com.xinxinsoft.entity.integration.Bis_TaskList;
import com.xinxinsoft.entity.processLink.LinkTemplate;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.service.commonSingManagement.CommonSingleService;
import com.xinxinsoft.service.core.processService.ProcessService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.dedicatedFlow.DedicatedFlowService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.groupcustomer.GroupCustomerService;
import com.xinxinsoft.service.v2.integrationService.IntegrationService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.XMLToVeiw;
import com.xinxinsoft.utils.XmlUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

public class IntegrationAction extends BaseAction{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private static final Logger logger = Logger.getLogger(DedicatedFlowAction.class);
	private static ResourceBundle s = ResourceBundle.getBundle("WebService-config");
	private static String EXPECTEDNUMBEROFDAYS=s.getString("EXPECTEDNUMBEROFDAYS");
	private CommonSingleService commonSingleService;
	private AttachmentService attachmentService;//附件：
	private GroupCustomerService groupCustomerService;//集团客户：
	private SystemUserService systemUserService;
	private IntegrationService integrationService;
	private DedicatedFlowService dedicatedFlowService;
	private WaitTaskService service;//代办
	private ProcessService processService;
	private static Boolean isES = false;
	static {
		if("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())){
			isES = true;
		}
	}
	public CommonSingleService getCommonSingleService() {
		return commonSingleService;
	}

	public DedicatedFlowService getDedicatedFlowService() {
		return dedicatedFlowService;
	}

	public void setDedicatedFlowService(DedicatedFlowService dedicatedFlowService) {
		this.dedicatedFlowService = dedicatedFlowService;
	}

	public ProcessService getProcessService() {
		return processService;
	}

	public void setProcessService(ProcessService processService) {
		this.processService = processService;
	}

	public void setCommonSingleService(CommonSingleService commonSingleService) {
		this.commonSingleService = commonSingleService;
	}

	public AttachmentService getAttachmentService() {
		return attachmentService;
	}

	public void setAttachmentService(AttachmentService attachmentService) {
		this.attachmentService = attachmentService;
	}

	public GroupCustomerService getGroupCustomerService() {
		return groupCustomerService;
	}

	public void setGroupCustomerService(GroupCustomerService groupCustomerService) {
		this.groupCustomerService = groupCustomerService;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}

	public WaitTaskService getService() {
		return service;
	}

	public void setService(WaitTaskService service) {
		this.service = service;
	}

	public IntegrationService getIntegrationService() {
		return integrationService;
	}

	public void setIntegrationService(IntegrationService integrationService) {
		this.integrationService = integrationService;
	}

	/**
	 * 保存方法；
	 */
	public void saveIntegration(){
		try{
			String id = getString("id");
			Bis_DemandList DemandList= integrationService.getDemandList(id);
			String attachmentId = getString("attachmentId");
			if(DemandList!=null){
				String zpclass="class1";//这是业务分类
				String pcode = this.getString("pcode");//产品类型
				String ot = this.getString("ot");//操作类型
				String zpcode = this.getString("zpcode");//产品
				String demandTitle = this.getString("demandTitle");//需求名称
				String xuqiuDate = this.getString("xuqiuDate");//需求完成时间
				String booleans = this.getString("boolean");//是否加急
				String contact = this.getString("contact");//联系人
				String contactMoblie = this.getString("contactMoblie");//联系电话
				String dDesc = this.getString("dDesc");//需求描述
				String groupCoding = this.getString("groupCoding");//集团280
				String money = this.getString("money");//产品资费
				String rebate = this.getString("rebate");//折扣
				String zproductid = this.getString("zproductid");//产品信息保存的ID
				String whetherOrNotToMerge = this.getString("whetherOrNotToMerge");//是否融合系统
				//保存需求单信息
				//Bis_DemandList demand = new Bis_DemandList();
				DemandList.setDemandTitle(demandTitle);
				//查不到时：是根据区域首写字母和产品类型：
				if("error".equals(commonSingleService.returnCode("888","888",user,false,"8"))){
					this.Write("error");
					return;
				}
				String quxian="";
				String fengongsi="";
				List<Object[]> sone=commonSingleService.getSystemDept(user.getRowNo());
				for(int i=0;i<sone.size();i++){
					quxian = (String)sone.get(i)[0];
					fengongsi=(String)sone.get(i)[1];
				}
				DemandList.setDemandNumber(commonSingleService.returnCode("888","888",user,false,"8"));
				DemandList.setOperType(ot);
				DemandList.setdClass(zpclass);
				DemandList.setdType(pcode);
				DemandList.setdProduct(zpcode);
				DemandList.setdNumber("1");
				DemandList.setGroupCode(groupCoding);
				DemandList.setContact(contact);
				DemandList.setContactMoblie(contactMoblie);
				DemandList.setdDesc(dDesc);
				DemandList.setCity(fengongsi);
				DemandList.setDistrict(quxian);
				DemandList.setCreator(user.getEmployeeName());
				DemandList.setCreatorNo(user.getRowNo()+"");
				DemandList.setCreatDate(getStringDate(new Date()));
				DemandList.setXuqiuDate(xuqiuDate);
				DemandList.setBooleans(booleans);
				DemandList.setState("-2");
				DemandList.setWhetherOrNotToMerge(whetherOrNotToMerge);
				Bis_DemandList demandList= integrationService.updateDemandList(DemandList);
				//保存产品资费以及折扣
				String[] XMLid = null;
				XMLid = zproductid.split(",");
				for(int i=0;i<XMLid.length;i++){
					Bis_OrderContent orderContent = integrationService.getOrderContent(XMLid[i]);
					if(orderContent!=null){
						orderContent.setMoney(money);
						orderContent.setRebate(rebate);
						orderContent.setDemandNumber(demandList.getDemandNumber());
						integrationService.saveOrderContent(orderContent);
					}
					//保存需求单和产品的中间表信息
					Bis_DemandOrdercontent demandOrdercontent = integrationService.getordertentOronetomony(XMLid[i]);
					if(demandOrdercontent!=null){
						demandOrdercontent.setDemandId(demandList.getUuid());
						demandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.updateDemandOrdercontent(demandOrdercontent);
						demandOrdercontent=null;
					}else{
						Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
						DemandOrdercontent.setDemandId(demandList.getUuid());
						DemandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.saveDemandOrdercontent(DemandOrdercontent);
					}
				}
				if (!StringUtils.isEmpty(attachmentId)) {
					if (attachmentId != null) {
						// 判断是否上传了附件，获取前台提交的附件Id；
						String[] json = attachmentId.split(",");
						if (json.length > 0) {
							for (int i = 0; i < json.length; i++) {
								SingleAndAttachment sa = new SingleAndAttachment();
								sa.setOrderID(id);
								sa.setAttachmentId(json[i]);
								sa.setLink(Bis_DemandList.Integration);
								integrationService.saveSandA(sa);
							}
						}
					}
				}
			}else{
				String zpclass="class1";//这是业务分类
				String pcode = this.getString("pcode");//产品类型
				String ot = this.getString("ot");//操作类型
				String zpcode = this.getString("zpcode");//产品
				String demandTitle = this.getString("demandTitle");//需求名称
				String xuqiuDate = this.getString("xuqiuDate");//需求完成时间
				String booleans = this.getString("boolean");//是否加急
				String contact = this.getString("contact");//联系人
				String contactMoblie = this.getString("contactMoblie");//联系电话
				String dDesc = this.getString("dDesc");//需求描述
				String groupCoding = this.getString("groupCoding");//集团280
				String money = this.getString("money");//产品资费
				String rebate = this.getString("rebate");//折扣
				String zproductid = this.getString("zproductid");//产品信息保存的ID
				String whetherOrNotToMerge = this.getString("whetherOrNotToMerge");//是否融合系统
				//保存需求单信息
				Bis_DemandList demand = new Bis_DemandList();
				demand.setDemandTitle(demandTitle);
				//查不到时：是根据区域首写字母和产品类型：
				if("error".equals(commonSingleService.returnCode("888","888",user,false,"8"))){
					this.Write("error");
					return;
				}
				String quxian="";
				String fengongsi="";
				List<Object[]> sone=commonSingleService.getSystemDept(user.getRowNo());
				for(int i=0;i<sone.size();i++){
					quxian = (String)sone.get(i)[0];
					fengongsi=(String)sone.get(i)[1];
				}
				demand.setDemandNumber(commonSingleService.returnCode("888","888",user,false,"8"));
				demand.setOperType(ot);
				demand.setdClass(zpclass);
				demand.setdType(pcode);
				demand.setdProduct(zpcode);
				demand.setdNumber("1");
				demand.setGroupCode(groupCoding);
				demand.setContact(contact);
				demand.setContactMoblie(contactMoblie);
				demand.setdDesc(dDesc);
				demand.setCity(fengongsi);
				demand.setDistrict(quxian);
				demand.setCreator(user.getEmployeeName());
				demand.setCreatorNo(user.getRowNo()+"");
				demand.setCreatDate(getStringDate(new Date()));
				demand.setXuqiuDate(xuqiuDate);
				demand.setBooleans(booleans);
				demand.setState("-2");
				demand.setWhetherOrNotToMerge(whetherOrNotToMerge);
				Bis_DemandList demandList= integrationService.saveBis_DemandList(demand);
				/*//保存产品资费以及折扣
				Bis_OrderContent orderContent = integrationService.getOrderContent(zproductid);
				if(orderContent!=null){
					orderContent.setMoney(money);
					orderContent.setRebate(rebate);
					orderContent.setDemandNumber(demandList.getDemandNumber());
					integrationService.saveOrderContent(orderContent);
				}
				//保存需求单和产品的中间表信息
				Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
				DemandOrdercontent.setDemandId(demandList.getUuid());
				DemandOrdercontent.setOrdercontentId(zproductid);
				integrationService.saveDemandOrdercontent(DemandOrdercontent);*/
				//保存产品资费以及折扣
				String[] XMLid = null;
				XMLid = zproductid.split(",");
				for(int i=0;i<XMLid.length;i++){
					Bis_OrderContent orderContent = integrationService.getOrderContent(XMLid[i]);
					if(orderContent!=null){
						orderContent.setMoney(money);
						orderContent.setRebate(rebate);
						orderContent.setDemandNumber(demandList.getDemandNumber());
						integrationService.saveOrderContent(orderContent);
					}
					//保存需求单和产品的中间表信息
					Bis_DemandOrdercontent demandOrdercontent = integrationService.getordertentOronetomony(XMLid[i]);
					if(demandOrdercontent!=null){
						demandOrdercontent.setDemandId(demandList.getUuid());
						demandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.updateDemandOrdercontent(demandOrdercontent);
						demandOrdercontent=null;
					}else{
						Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
						DemandOrdercontent.setDemandId(demandList.getUuid());
						DemandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.saveDemandOrdercontent(DemandOrdercontent);
					}
				}
				
				if (!StringUtils.isEmpty(attachmentId)) {
					if (attachmentId != null) {
						// 判断是否上传了附件，获取前台提交的附件Id；
						String[] json = attachmentId.split(",");
						if (json.length > 0) {
							for (int i = 0; i < json.length; i++) {
								SingleAndAttachment sa = new SingleAndAttachment();
								sa.setOrderID(demandList.getUuid());
								sa.setAttachmentId(json[i]);
								sa.setLink(Bis_DemandList.Integration);
								integrationService.saveSandA(sa);
							}
						}
					}
				}
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 提交方法；
	 */
	public void submitIntegration(){
		try{
			String id = getString("id");
			Bis_DemandList DemandList= integrationService.getDemandList(id);
			String attachmentId = getString("attachmentId");
			if(DemandList!=null){
				//查询产品对象
				//Bis_OrderContent orderContent = integrationService.getDraftOrderContent(id);
				String zpclass="class1";//这是业务分类
				String pcode = this.getString("pcode");//产品类型
				String ot = this.getString("ot");//操作类型
				String zpcode = this.getString("zpcode");//产品
				String demandTitle = this.getString("demandTitle");//需求名称
				String xuqiuDate = this.getString("xuqiuDate");//需求完成时间
				String booleans = this.getString("boolean");//是否加急
				String contact = this.getString("contact");//联系人
				String contactMoblie = this.getString("contactMoblie");//联系电话
				String dDesc = this.getString("dDesc");//需求描述
				String groupCoding = this.getString("groupCoding");//集团280
				String money = this.getString("money");//产品资费
				String rebate = this.getString("rebate");//折扣
				String zproductid = this.getString("zproductid");//产品信息保存的ID
				String approval = this.getString("approval");//接收人名称
				String approvalId = this.getString("approvalId");//接收人ID
				String ccName = this.getString("ccName");//抄送人名称
				String ccId = this.getString("ccId");//抄送人ID
				String whetherOrNotToMerge = getString("whetherOrNotToMerge");//是否走融合系统
				//保存需求单信息
				//Bis_DemandList demand = new Bis_DemandList();
				DemandList.setDemandTitle(demandTitle);
				//查不到时：是根据区域首写字母和产品类型：
				if("error".equals(commonSingleService.returnCode("888","888",user,false,"8"))){
					this.Write("error");
					return;
				}
				String quxian="";
				String fengongsi="";
				List<Object[]> sone=commonSingleService.getSystemDept(user.getRowNo());
				for(int i=0;i<sone.size();i++){
					quxian = (String)sone.get(i)[0];
					fengongsi=(String)sone.get(i)[1];
				}
				//DemandList.setDemandNumber(commonSingleService.returnCode("888",null,user,true,null));
				DemandList.setOperType(ot);
				DemandList.setdClass(zpclass);
				DemandList.setdType(pcode);
				DemandList.setdProduct(zpcode);
				DemandList.setdNumber("1");
				DemandList.setGroupCode(groupCoding);
				DemandList.setContact(contact);
				DemandList.setContactMoblie(contactMoblie);
				DemandList.setdDesc(dDesc);
				DemandList.setCity(fengongsi);
				DemandList.setDistrict(quxian);
				DemandList.setCreator(user.getEmployeeName());
				DemandList.setCreatorNo(user.getRowNo()+"");
				DemandList.setCreatDate(getStringDate(new Date()));
				DemandList.setXuqiuDate(xuqiuDate);
				DemandList.setBooleans(booleans);
				DemandList.setState("7");
				DemandList.setWhetherOrNotToMerge(whetherOrNotToMerge);
				Bis_DemandList demandList= integrationService.updateDemandList(DemandList);
				//保存产品资费以及折扣
				/*Bis_OrderContent orderContent = integrationService.getOrderContent(zproductid);
				if(orderContent!=null){
					orderContent.setMoney(money);
					orderContent.setRebate(rebate);
					orderContent.setDemandNumber(demandList.getDemandNumber());
					integrationService.saveOrderContent(orderContent);
				}
				//保存需求单和产品的中间表信息
				Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
				DemandOrdercontent.setDemandId(demandList.getUuid());
				DemandOrdercontent.setOrdercontentId(zproductid);
				integrationService.saveDemandOrdercontent(DemandOrdercontent);*/
				//保存产品资费以及折扣
				String[] XMLid = null;
				XMLid = zproductid.split(",");
				for(int i=0;i<XMLid.length;i++){
					Bis_OrderContent orderContent = integrationService.getOrderContent(XMLid[i]);
					if(orderContent!=null){
						orderContent.setMoney(money);
						orderContent.setRebate(rebate);
						orderContent.setDemandNumber(demandList.getDemandNumber());
						integrationService.saveOrderContent(orderContent);
					}
					//保存需求单和产品的中间表信息
					Bis_DemandOrdercontent demandOrdercontent = integrationService.getordertentOronetomony(XMLid[i]);
					if(demandOrdercontent!=null){
						demandOrdercontent.setDemandId(demandList.getUuid());
						demandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.updateDemandOrdercontent(demandOrdercontent);
						demandOrdercontent=null;
					}else{
						Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
						DemandOrdercontent.setDemandId(demandList.getUuid());
						DemandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.saveDemandOrdercontent(DemandOrdercontent);
					}
				}
				//保存流程表信息
		        SimpleDateFormat formatter=new SimpleDateFormat("yyyyMMddhhmmssSSSS");  
				Bis_ProcessList ProcessList = new Bis_ProcessList();
				ProcessList.setDemandNumber(demandList.getDemandNumber());
				ProcessList.setProcess(formatter.format(new Date()));
				ProcessList.setProcessName(demandTitle);
				ProcessList.setCreator(user.getEmployeeName());
				ProcessList.setCreatorNo(user.getRowNo()+"");
				ProcessList.setCreatDate(getStringDate(new Date()));
				ProcessList.setStatus("1");
				Bis_ProcessList ProcessBean= integrationService.saveProcessList(ProcessList);
				//保存任务表信息
				Calendar dar=Calendar.getInstance();
			    dar.setTime(new Date());
			    dar.add(java.util.Calendar.HOUR_OF_DAY, 4);
			    
			    //先保存自己本身的任务===========
			    Bis_TaskList TaskList = new Bis_TaskList();
				TaskList.setProcess(ProcessBean.getProcess());
				TaskList.setCreator(user.getEmployeeName());
				TaskList.setCreatorNo(user.getRowNo()+"");
				TaskList.setCreatDate(getStringDate(new Date()));
				TaskList.setPlanDate(getStringDate(new Date()));
				TaskList.setOper(user.getEmployeeName());
				TaskList.setOperNo(user.getRowNo()+"");
				TaskList.setOperDate(getStringDate(new Date()));
				TaskList.setSpendTime("0");
				TaskList.setStatus("2");
				TaskList.setType("SH");
				TaskList.setExpectedCompletionTime(getStringDate(new Date()));
				Bis_TaskList TaskBean= integrationService.saveTaskList(TaskList);
				//先保存自己本身的任务===========
				//先保存自己的抄送任务
				Bis_TaskList TaskListone = new Bis_TaskList();
				TaskListone.setProcess(ProcessBean.getProcess());
				TaskListone.setCreator(user.getEmployeeName());
				TaskListone.setCreatorNo(user.getRowNo()+"");
				TaskListone.setCreatDate(getStringDate(new Date()));
				TaskListone.setPlanDate(getStringDate(new Date()));
				TaskListone.setOper(user.getEmployeeName());
				TaskListone.setOperNo(user.getRowNo()+"");
				TaskListone.setOperDate(getStringDate(new Date()));
				TaskListone.setSpendTime("0");
				TaskListone.setStatus("2");
				TaskListone.setType("CS");
				TaskListone.setExpectedCompletionTime(getStringDate(new Date()));
				Bis_TaskList TaskBeanone= integrationService.saveTaskList(TaskListone);
				
				String[] appname =null;
			    String[] appid = null;
				if(!approvalId.equals("")&&approvalId!=null){
					appname = approval.split(";");
				    appid = approvalId.split(";");
				}
				SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String da = "2017-01-25 08:00:00";
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		        Calendar date = Calendar.getInstance();
		        date.setTime(new Date());
		        date.set(Calendar.DATE, date.get(Calendar.DATE)+1);
		        String lastDay = sdf.format(date.getTime()) + " 08:00:00";//明天日期
		        Date dateTmp=getDate(sdf2.parse(lastDay),0);
			    if(appid!=null){
				    for(int i=0;i<appid.length;i++){
			    		Bis_TaskList TaskListtwo = new Bis_TaskList();
			    		TaskListtwo.setProcess(ProcessBean.getProcess());
			    		TaskListtwo.setCreator(user.getEmployeeName());
			    		TaskListtwo.setCreatorNo(user.getRowNo()+"");
			    		TaskListtwo.setCreatDate(getStringDate(new Date()));
			    		TaskListtwo.setPlanDate(getStringDate(dar.getTime()));
			    		TaskListtwo.setOper(appname[i]);
			    		TaskListtwo.setOperNo(appid[i]);
			    		TaskListtwo.setFatherUuid(TaskBean.getUuid());
			    		TaskListtwo.setStatus("1");
			    		TaskListtwo.setType("SH");
			    		TaskListtwo.setExpectedCompletionTime(sdf2.format(dateTmp));
			    		Bis_TaskList TaskListdcdsa=integrationService.saveTaskList(TaskListtwo);
			    		daibanone(demandList,ProcessBean,appid[i],TaskListdcdsa,zproductid,"SH");
				    }
			    }
			    String[] csid =null;
				String[] csname = null;//抄送人名称
				if(!ccId.equals("")&&ccId!=null){
					csid = ccId.split(";");
					csname = ccName.split(";");
				}
				if(csid!=null){
					for(int i=0;i<csid.length;i++){
			    		Bis_TaskList TaskListthree = new Bis_TaskList();
			    		TaskListthree.setProcess(ProcessBean.getProcess());
			    		TaskListthree.setCreator(user.getEmployeeName());
			    		TaskListthree.setCreatorNo(user.getRowNo()+"");
			    		TaskListthree.setCreatDate(getStringDate(new Date()));
			    		TaskListthree.setPlanDate(getStringDate(dar.getTime()));
			    		TaskListthree.setOper(csname[i]);
			    		TaskListthree.setOperNo(csid[i]);
			    		TaskListthree.setFatherUuid(TaskBeanone.getUuid());
			    		TaskListthree.setStatus("1");
			    		TaskListthree.setType("CS");
			    		TaskListthree.setExpectedCompletionTime(sdf2.format(dateTmp));
			    		Bis_TaskList TaskListdsa= integrationService.saveTaskList(TaskListthree);
			    		daibanone(demandList,ProcessBean,csid[i],TaskListdsa,zproductid,"CS");
				    }
				}
				
				if (!StringUtils.isEmpty(attachmentId)) {
					if (attachmentId != null) {
						// 判断是否上传了附件，获取前台提交的附件Id；
						String[] json = attachmentId.split(",");
						if (json.length > 0) {
							for (int i = 0; i < json.length; i++) {
								SingleAndAttachment sa = new SingleAndAttachment();
								sa.setOrderID(id);
								sa.setAttachmentId(json[i]);
								sa.setLink(Bis_DemandList.Integration);
								integrationService.saveSandA(sa);
							}
						}
					}
				}
				
			}else{
				String zpclass="class1";//这是业务分类
				String pcode = this.getString("pcode");//产品类型
				String ot = this.getString("ot");//操作类型
				String zpcode = this.getString("zpcode");//产品
				String demandTitle = this.getString("demandTitle");//需求名称
				String xuqiuDate = this.getString("xuqiuDate");//需求完成时间
				String booleans = this.getString("boolean");//是否加急
				String contact = this.getString("contact");//联系人
				String contactMoblie = this.getString("contactMoblie");//联系电话
				String dDesc = this.getString("dDesc");//需求描述
				String groupCoding = this.getString("groupCoding");//集团280
				String money = this.getString("money");//产品资费
				String rebate = this.getString("rebate");//折扣
				String zproductid = this.getString("zproductid");//产品信息保存的ID
				String approval = this.getString("approval");//接收人名称
				String approvalId = this.getString("approvalId");//接收人ID
				String ccName = this.getString("ccName");//抄送人名称
				String ccId = this.getString("ccId");//抄送人ID
				String whetherOrNotToMerge = getString("whetherOrNotToMerge");//是否走融合系统
				//保存需求单信息
				Bis_DemandList demand = new Bis_DemandList();
				demand.setDemandTitle(demandTitle);
				//查不到时：是根据区域首写字母和产品类型：
				if("error".equals(commonSingleService.returnCode("888","888",user,false,"8"))){
					this.Write("error");
					return;
				}
				String quxian="";
				String fengongsi="";
				List<Object[]> sone=commonSingleService.getSystemDept(user.getRowNo());
				for(int i=0;i<sone.size();i++){
					quxian = (String)sone.get(i)[0];
					fengongsi=(String)sone.get(i)[1];
				}
				demand.setDemandNumber(commonSingleService.returnCode("888","888",user,false,"8"));
				demand.setOperType(ot);
				demand.setdClass(zpclass);
				demand.setdType(pcode);
				demand.setdProduct(zpcode);
				demand.setdNumber("1");
				demand.setGroupCode(groupCoding);
				demand.setContact(contact);
				demand.setContactMoblie(contactMoblie);
				demand.setdDesc(dDesc);
				demand.setCity(fengongsi);
				demand.setDistrict(quxian);
				demand.setCreator(user.getEmployeeName());
				demand.setCreatorNo(user.getRowNo()+"");
				demand.setCreatDate(getStringDate(new Date()));
				demand.setXuqiuDate(xuqiuDate);
				demand.setBooleans(booleans);
				demand.setState("7");
				demand.setWhetherOrNotToMerge(whetherOrNotToMerge);
				Bis_DemandList demandList= integrationService.saveBis_DemandList(demand);
				/*//保存产品资费以及折扣
				Bis_OrderContent orderContent = integrationService.getOrderContent(zproductid);
				if(orderContent!=null){
					orderContent.setMoney(money);
					orderContent.setRebate(rebate);
					orderContent.setDemandNumber(demandList.getDemandNumber());
					integrationService.saveOrderContent(orderContent);
				}
				//保存需求单和产品的中间表信息
				Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
				DemandOrdercontent.setDemandId(demandList.getUuid());
				DemandOrdercontent.setOrdercontentId(zproductid);
				integrationService.saveDemandOrdercontent(DemandOrdercontent);*/
				//保存产品资费以及折扣
				String[] XMLid = null;
				XMLid = zproductid.split(",");
				for(int i=0;i<XMLid.length;i++){
					Bis_OrderContent orderContent = integrationService.getOrderContent(XMLid[i]);
					if(orderContent!=null){
						orderContent.setMoney(money);
						orderContent.setRebate(rebate);
						orderContent.setDemandNumber(demandList.getDemandNumber());
						integrationService.saveOrderContent(orderContent);
					}
					//保存需求单和产品的中间表信息
					Bis_DemandOrdercontent demandOrdercontent = integrationService.getordertentOronetomony(XMLid[i]);
					if(demandOrdercontent!=null){
						demandOrdercontent.setDemandId(demandList.getUuid());
						demandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.updateDemandOrdercontent(demandOrdercontent);
						demandOrdercontent=null;
					}else{
						Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
						DemandOrdercontent.setDemandId(demandList.getUuid());
						DemandOrdercontent.setOrdercontentId(XMLid[i]);
						integrationService.saveDemandOrdercontent(DemandOrdercontent);
					}
				}
				//保存流程表信息
		        SimpleDateFormat formatter=new SimpleDateFormat("yyyyMMddhhmmssSSSS");  
				Bis_ProcessList ProcessList = new Bis_ProcessList();
				ProcessList.setDemandNumber(demandList.getDemandNumber());
				ProcessList.setProcess(formatter.format(new Date()));
				ProcessList.setProcessName(demandTitle);
				ProcessList.setCreator(user.getEmployeeName());
				ProcessList.setCreatorNo(user.getRowNo()+"");
				ProcessList.setCreatDate(getStringDate(new Date()));
				ProcessList.setStatus("1");
				Bis_ProcessList ProcessBean= integrationService.saveProcessList(ProcessList);
				//保存任务表信息
				Calendar dar=Calendar.getInstance();
			    dar.setTime(new Date());
			    dar.add(java.util.Calendar.HOUR_OF_DAY, 4);
			    
			    //先保存自己本身的任务===========
			    Bis_TaskList TaskList = new Bis_TaskList();
				TaskList.setProcess(ProcessBean.getProcess());
				TaskList.setCreator(user.getEmployeeName());
				TaskList.setCreatorNo(user.getRowNo()+"");
				TaskList.setCreatDate(getStringDate(new Date()));
				TaskList.setPlanDate(getStringDate(new Date()));
				TaskList.setOper(user.getEmployeeName());
				TaskList.setOperNo(user.getRowNo()+"");
				TaskList.setOperDate(getStringDate(new Date()));
				TaskList.setSpendTime("0");
				TaskList.setStatus("2");
				TaskList.setType("SH");
				TaskList.setExpectedCompletionTime(getStringDate(new Date()));
				Bis_TaskList TaskBean= integrationService.saveTaskList(TaskList);
				//先保存自己本身的任务===========
				//先保存自己的抄送任务
				Bis_TaskList TaskListone = new Bis_TaskList();
				TaskListone.setProcess(ProcessBean.getProcess());
				TaskListone.setCreator(user.getEmployeeName());
				TaskListone.setCreatorNo(user.getRowNo()+"");
				TaskListone.setCreatDate(getStringDate(new Date()));
				TaskListone.setPlanDate(getStringDate(new Date()));
				TaskListone.setOper(user.getEmployeeName());
				TaskListone.setOperNo(user.getRowNo()+"");
				TaskListone.setOperDate(getStringDate(new Date()));
				TaskListone.setSpendTime("0");
				TaskListone.setStatus("2");
				TaskListone.setType("CS");
				TaskListone.setExpectedCompletionTime(getStringDate(new Date()));
				Bis_TaskList TaskBeanone= integrationService.saveTaskList(TaskListone);
				
				String[] appname =null;
			    String[] appid = null;
				if(!approvalId.equals("")&&approvalId!=null){
					appname = approval.split(";");
				    appid = approvalId.split(";");
				}
				SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
				String da = "2017-01-25 08:00:00";
				SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		        Calendar date = Calendar.getInstance();
		        date.setTime(new Date());
		        date.set(Calendar.DATE, date.get(Calendar.DATE)+1);
		        String lastDay = sdf.format(date.getTime()) + " 08:00:00";//明天日期
		        Date dateTmp=getDate(sdf2.parse(lastDay),0);
			    if(appid!=null){
				    for(int i=0;i<appid.length;i++){
			    		Bis_TaskList TaskListtwo = new Bis_TaskList();
			    		TaskListtwo.setProcess(ProcessBean.getProcess());
			    		TaskListtwo.setCreator(user.getEmployeeName());
			    		TaskListtwo.setCreatorNo(user.getRowNo()+"");
			    		TaskListtwo.setCreatDate(getStringDate(new Date()));
			    		TaskListtwo.setPlanDate(getStringDate(dar.getTime()));
			    		TaskListtwo.setOper(appname[i]);
			    		TaskListtwo.setOperNo(appid[i]);
			    		TaskListtwo.setFatherUuid(TaskBean.getUuid());
			    		TaskListtwo.setStatus("1");
			    		TaskListtwo.setType("SH");
			    		TaskListtwo.setExpectedCompletionTime(sdf2.format(dateTmp));
			    		Bis_TaskList TaskListdcdsa=integrationService.saveTaskList(TaskListtwo);
			    		daibanone(demandList,ProcessBean,appid[i],TaskListdcdsa,zproductid,"SH");
				    }
			    }
			    String[] csid =null;
				String[] csname = null;//抄送人名称
				if(!ccId.equals("")&&ccId!=null){
					csid = ccId.split(";");
					csname = ccName.split(";");
				}
				if(csid!=null){
					for(int i=0;i<csid.length;i++){
			    		Bis_TaskList TaskListthree = new Bis_TaskList();
			    		TaskListthree.setProcess(ProcessBean.getProcess());
			    		TaskListthree.setCreator(user.getEmployeeName());
			    		TaskListthree.setCreatorNo(user.getRowNo()+"");
			    		TaskListthree.setCreatDate(getStringDate(new Date()));
			    		TaskListthree.setPlanDate(getStringDate(dar.getTime()));
			    		TaskListthree.setOper(csname[i]);
			    		TaskListthree.setOperNo(csid[i]);
			    		TaskListthree.setFatherUuid(TaskBeanone.getUuid());
			    		TaskListthree.setStatus("1");
			    		TaskListthree.setType("CS");
			    		TaskListthree.setExpectedCompletionTime(sdf2.format(dateTmp));
			    		Bis_TaskList TaskListdsa= integrationService.saveTaskList(TaskListthree);
			    		daibanone(demandList,ProcessBean,csid[i],TaskListdsa,zproductid,"CS");
				    }
				}
				if (!StringUtils.isEmpty(attachmentId)) {
					if (attachmentId != null) {
						// 判断是否上传了附件，获取前台提交的附件Id；
						String[] json = attachmentId.split(",");
						if (json.length > 0) {
							for (int i = 0; i < json.length; i++) {
								SingleAndAttachment sa = new SingleAndAttachment();
								sa.setOrderID(demandList.getUuid());
								sa.setAttachmentId(json[i]);
								sa.setLink(Bis_DemandList.Integration);
								integrationService.saveSandA(sa);
							}
						}
					}
				}
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 转发方法
	 */
	public void handleIntegration(){
		try{
			String approval = getString("approval");//主送人
			String approvalId = getString("approvalId");//主送人ID
			String ccName = getString("ccName");//抄送人
			String ccId = getString("ccId");//抄送人ID
			String opinion = getString("opinion");//意见
			String id = getString("id");//需求单ID
			String OrderContentId = getString("OrderContentId");//产品ID
			String tasklistID = getString("tasklistID");//任务ID
			String ProcessId = getString("ProcessId");//流程ID
			String waitId = getString("waitId");//流程ID
			String type = getString("type");//流程ID
			String[] appname=null;
			String[] appid=null;
			if(!type.equals("CS")){
				appname = approval.split(";");
				appid = approvalId.split(";");
			}
		    Bis_DemandList demandList= integrationService.getDemandList(id);
		    Bis_ProcessList ProcessBean= integrationService.getProcessList(ProcessId);
		    //Bis_OrderContent orderContent = integrationService.getOrderContent(OrderContentId);
		    Bis_TaskList TaskList =  integrationService.getTaskList(tasklistID);
		    TaskList.setStatus("2");
		    TaskList.setOperDate(getStringDate(new Date()));
		    TaskList.setReplyContent(opinion);
		    integrationService.updateTasklist(TaskList);
		    
		    Calendar dar=Calendar.getInstance();
		    dar.setTime(new Date());
		    dar.add(java.util.Calendar.HOUR_OF_DAY, 4);
		    
		    SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String da = "2017-01-25 08:00:00";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	        Calendar date = Calendar.getInstance();
	        date.setTime(new Date());
	        date.set(Calendar.DATE, date.get(Calendar.DATE)+1);
	        String lastDay = sdf.format(date.getTime()) + " 08:00:00";//明天日期
	        Date dateTmp=getDate(sdf2.parse(lastDay),0);
		    if(!type.equals("CS")){
		    	if(appid!=null){
				    for(int i=0;i<appid.length;i++){
			    		Bis_TaskList TaskListtwo = new Bis_TaskList();
			    		TaskListtwo.setProcess(ProcessId);
			    		TaskListtwo.setCreator(user.getEmployeeName());
			    		TaskListtwo.setCreatorNo(user.getRowNo()+"");
			    		TaskListtwo.setCreatDate(getStringDate(new Date()));
			    		TaskListtwo.setPlanDate(getStringDate(dar.getTime()));
			    		TaskListtwo.setOper(appname[i]);
			    		TaskListtwo.setOperNo(appid[i]);
			    		TaskListtwo.setFatherUuid(tasklistID);
			    		TaskListtwo.setStatus("1");
			    		TaskListtwo.setType("SH");
			    		TaskListtwo.setExpectedCompletionTime(sdf2.format(dateTmp));
			    		Bis_TaskList TaskListdcdsa=integrationService.saveTaskList(TaskListtwo);
			    		daibanone(demandList,ProcessBean,appid[i],TaskListdcdsa,OrderContentId,"SH");
				    }
		    	}
		    }
		    String[] csid =null;
			String[] csname = null;//抄送人名称
			if(!ccId.equals("")&&ccId!=null){
				csid = ccId.split(";");
				csname = ccName.split(";");
			}
			if(csid!=null){
				for(int i=0;i<csid.length;i++){
		    		Bis_TaskList TaskListthree = new Bis_TaskList();
		    		TaskListthree.setProcess(ProcessId);
		    		TaskListthree.setCreator(user.getEmployeeName());
		    		TaskListthree.setCreatorNo(user.getRowNo()+"");
		    		TaskListthree.setCreatDate(getStringDate(new Date()));
		    		TaskListthree.setPlanDate(getStringDate(dar.getTime()));
		    		TaskListthree.setOper(csname[i]);
		    		TaskListthree.setOperNo(csid[i]);
		    		TaskListthree.setFatherUuid(tasklistID);
		    		TaskListthree.setStatus("1");
		    		TaskListthree.setType("CS");
		    		TaskListthree.setExpectedCompletionTime(sdf2.format(dateTmp));
		    		Bis_TaskList TaskListdsa= integrationService.saveTaskList(TaskListthree);
		    		daibanone(demandList,ProcessBean,csid[i],TaskListdsa,OrderContentId,"CS");
			    }
			}
			//结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 阅读方法
	 */
	public void readIntegration(){
		try{
			String opinion = getString("opinion");//意见
			String id = getString("id");//需求单ID
			String OrderContentId = getString("OrderContentId");//产品ID
			String tasklistID = getString("tasklistID");//任务ID
			String ProcessId = getString("ProcessId");//流程ID
			String waitId = getString("waitId");//流程ID
			Bis_TaskList TaskList =  integrationService.getTaskList(tasklistID);
		    TaskList.setStatus("2");
		    TaskList.setOperDate(getStringDate(new Date()));
		    TaskList.setReplyContent(opinion);
		    integrationService.updateTasklist(TaskList);
		    //结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
		    Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	
	/**
	 * 回复方法
	 */
	public void completeIntegration(){
		try{
			String opinion = getString("opinion");//意见
			String id = getString("id");//需求单ID
			String OrderContentId = getString("OrderContentId");//产品ID
			String tasklistID = getString("tasklistID");//任务ID
			String ProcessId = getString("ProcessId");//流程ID
			String waitId = getString("waitId");//流程ID
			Bis_TaskList TaskList =  integrationService.getTaskList(tasklistID);
		    TaskList.setStatus("2");
		    TaskList.setOperDate(getStringDate(new Date()));
		    TaskList.setReplyContent(opinion);
		    //Bis_TaskList Bis_TaskList= integrationService.updateTasklist(TaskList);
		    
		    int process= integrationService.getProcess(ProcessId,tasklistID);
		    if(process==0){
		    	Bis_DemandList demandList= integrationService.getDemandList(id);
		    	demandList.setState("1");
			    Bis_ProcessList ProcessBean= integrationService.getProcessList(ProcessId);
			    //Bis_OrderContent orderContent = integrationService.getOrderContent(OrderContentId);
		    	daibantwo(demandList,ProcessBean,demandList.getCreatorNo(),tasklistID,OrderContentId,"WC");
		    }
		    //结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
		    Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	//查询集团
	public void queryCustomer(){
			String groupCoding=getString("groupCoding");
			String groupName=getString("groupName");
			try {
				if(isES) {
					//接口调用反馈数据信息
					GroupCustomer customer = com.xinxinsoft.sendComms.GroupCustomerService.getInstance().getCustInfoQuery(groupCoding, groupName);
					///判断集团是否存在
					if (customer.getGroupCoding() != null && customer.getGroupName() != null) {
						GroupCustomer findDBCustomer = new GroupCustomer();
						findDBCustomer = groupCustomerService.queryGroup(customer.getGroupCoding().trim());
						//判断数据库是否存在集团客户信息
						if (null == findDBCustomer) {
							groupCustomerService.addGroupCustomer(customer);
							findDBCustomer = customer; //赋值
						} else {
							//数据库存在数据则更新数据信息
                            findDBCustomer.setGroupCoding(customer.getGroupCoding());
                            findDBCustomer.setGroupName(customer.getGroupName());
                            findDBCustomer.setGroupLevel(customer.getGroupLevel());
                            findDBCustomer.setHomeRegion(customer.getHomeRegion());
                            findDBCustomer.setContactAddress(customer.getContactAddress());
                            findDBCustomer.setContacts(customer.getContacts());
                            findDBCustomer.setContactPhone(customer.getContactPhone());
                            findDBCustomer.setCity(customer.getCity());
                            findDBCustomer.setUser_name(customer.getUser_name());
                            findDBCustomer.setChinese_name(customer.getChinese_name());
                            findDBCustomer.setMobile_phone(customer.getMobile_phone());
                            findDBCustomer.setLongitude(customer.getLongitude());
                            findDBCustomer.setLatitude(customer.getLatitude());
                            findDBCustomer.setReal_type(customer.getReal_type());
							groupCustomerService.updateGroupCustomer(findDBCustomer);
						}
						List<GroupCustomer> s = new ArrayList<GroupCustomer>();
						s.add(findDBCustomer);
						String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(s);
						Write(json);
					}
				}else {
					List<GroupCustomer> map =integrationService.dolist(groupCoding,groupName);
					String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(map);
					Write(json);
				}
			} catch (Exception e) {
				e.printStackTrace();
			    List<GroupCustomer> map =integrationService.dolist(groupCoding,groupName);
				String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(map);
				Write(json);
			}

	}
	
	/**
	 * 查询产品地址
	 */
	public void getZProductType(){
		String zpcode=getString("zpcode");
		ZProductType zproductType =integrationService.getZProductType(zpcode);
		String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(zproductType);
		Write(json);
	}
	
	//待办生成
	public void daibanone(Bis_DemandList demandList,Bis_ProcessList processBean,String userid,Bis_TaskList tasklist,String OrderContentid,String type) { 
		if(type.equals("SH")){
			WaitTask wt = new WaitTask();
			wt.setName("[通专融合]"+demandList.getDemandTitle());
			wt.setCreationTime(new Date());
			wt.setUrl("jsp/integration/handleIntegration.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
					+ processBean.getProcess()
					+ "&tasklistID="//任务ID
					+ tasklist.getUuid()
					/*+ "&OrderContentId="//产品ID
					+ OrderContentid*/
					+ "&zpcode="//产品
					+ demandList.getdProduct()
					+ "&operType="//操作类型
					+ demandList.getOperType()
					+ "&dType="//产品类型queryinttegration
					+ demandList.getdType()
					+ "&groupCoding="//集团280
					+ demandList.getGroupCode()
					+ "&type="//状态
					+ type);
			SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
			wt.setState(WaitTask.HANDLE);
			wt.setHandleUserId(USER.getRowNo());
			wt.setHandleUserName(USER.getEmployeeName());
			wt.setHandleLoginName(USER.getLoginName());
			wt.setCreateUserId(user.getRowNo());
			wt.setCreateUserName(user.getEmployeeName());
			wt.setCreateLoginName(user.getLoginName());
			wt.setCode(Bis_DemandList.Integration);
			wt.setTaskId(demandList.getUuid());
			service.saveWait(wt,this.getRequest());
		}else{
			WaitTask wt = new WaitTask();
			wt.setName("[抄送-通专融合]"+demandList.getDemandTitle());
			wt.setCreationTime(new Date());
			wt.setUrl("jsp/integration/handleIntegration.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
					+ processBean.getProcess()
					+ "&tasklistID="//任务ID
					+ tasklist.getUuid()
					/*+ "&OrderContentId="//产品ID
					+ OrderContentid*/
					+ "&zpcode="//产品
					+ demandList.getdProduct()
					+ "&operType="//操作类型
					+ demandList.getOperType()
					+ "&dType="//产品类型
					+ demandList.getdType()
					+ "&groupCoding="//集团280
					+ demandList.getGroupCode()
					+ "&type="//状态
					+ type);
			SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
			wt.setState(WaitTask.HANDLE);
			wt.setHandleUserId(USER.getRowNo());
			wt.setHandleUserName(USER.getEmployeeName());
			wt.setHandleLoginName(USER.getLoginName());
			wt.setCreateUserId(user.getRowNo());
			wt.setCreateUserName(user.getEmployeeName());
			wt.setCreateLoginName(user.getLoginName());
			wt.setCode(Bis_DemandList.Integration);
			wt.setTaskId(demandList.getUuid());
			service.saveWait(wt,this.getRequest());
		}
	}
	
	//待办生成
	public void daibantwo(Bis_DemandList demandList,Bis_ProcessList processBean,String userid,String tasklistID,String OrderContentId,String type) { 
			WaitTask wt = new WaitTask();
			wt.setName("[通专融合]"+demandList.getDemandTitle());
			wt.setCreationTime(new Date());
			wt.setUrl("jsp/integration/handleIntegration.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
					+ processBean.getProcess()
					/*+ "&OrderContentId="//产品ID
					+ OrderContentId*/
					+ "&zpcode="//产品
					+ demandList.getdProduct()
					+ "&operType="//操作类型
					+ demandList.getOperType()
					+ "&dType="//产品类型
					+ demandList.getdType()
					+ "&groupCoding="//集团280
					+ demandList.getGroupCode()
					+ "&type="//状态
					+ type
					+ "&tasklistID="
					+ tasklistID);
			SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(demandList.getCreatorNo()));
			wt.setState(WaitTask.HANDLE);
			wt.setHandleUserId(USER.getRowNo());
			wt.setHandleUserName(USER.getEmployeeName());
			wt.setHandleLoginName(USER.getLoginName());
			wt.setCreateUserId(user.getRowNo());
			wt.setCreateUserName(user.getEmployeeName());
			wt.setCreateLoginName(user.getLoginName());
			wt.setCode(Bis_DemandList.Integration);
			wt.setTaskId(demandList.getUuid());
			service.saveWait(wt,this.getRequest());
	}
	
	/**
	 * 添加信息
	 */
	public void addOrderDetail() {
		try {
			String xmlJson = getString("json");
			String zpcode = getString("zpcode");
			String draftId = getString("draftId");
			if(!"".equals(draftId) && draftId!=null){
				Bis_OrderContent orderContent = integrationService.getOrderContent(draftId);
				orderContent.setContent(XMLToVeiw.VeiwToXML(xmlJson));
				Bis_OrderContent OrderContenttwo=null;
				OrderContenttwo=integrationService.addOrderContent(orderContent);
				if(OrderContenttwo.getContent()==null){
					writeText("NO");
				}else{
					writeText(OrderContenttwo.getUuid());
				}
			}else{
				Bis_OrderContent OrderContent = new Bis_OrderContent();
				Bis_OrderContent OrderContenttwo=null;
				OrderContent.setContent(XMLToVeiw.VeiwToXML(xmlJson));
				OrderContent.setoProduct(zpcode);
				OrderContent.setStatus("1");
				OrderContenttwo=integrationService.addOrderContent(OrderContent);
				if(OrderContenttwo.getContent()==null){
					writeText("NO");
				}else{
					writeText(OrderContenttwo.getUuid());
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			writeText("NO");
		}
	}
	
	/**
	 * 查询产品信息
	 */
	public void getOrderContent(){
		String id = getString("id");
		Bis_OrderContent OrderContent=null;
		OrderContent = integrationService.getOrderContent(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(OrderContent));
	}
	
	public void getDemandList(){
		String id = getString("id");
		Bis_DemandList demandList=null;
		demandList = integrationService.getDemandList(id);
		System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormats(demandList));
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(demandList));
	}
	
	public void demandList(){
		String code = getString("code");//需求单编号
		String title = getString("title");//需求单名称
		PageRequest page = new PageRequest(getRequest());
		PageResponse response = integrationService.demandList(code,title,page,user);
		String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
		Write(json);
	}
	public static String getStringDate(Date currentTime) {
	   SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
	   String dateString = formatter.format(currentTime);
	   return dateString;
	}
	
	/**
	 * 根据订单ID查询产品信息
	 */
	public void getOrderDetailByOID(){
		try {
			String orderId=getString("oid");
			int tdCountLine=getInteger("tdCountLine");
			Bis_OrderContent orderDetail=integrationService.getOrderDetailByID(orderId);
			if(orderDetail!=null){
			 String html=XmlToHtml(orderDetail.getContent(),tdCountLine);
			 Write(html);
			}else{
				Write("");
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * XML格式转换HTML(table)
	 * @param xmlString XML内容
	 * @param tdCountLine TD的展示个数
	 * @return  返回HTML信息
	 * @throws Exception 错误
	 */
	public static String XmlToHtml(String xmlString,int tdCountLine) throws Exception {
		InputStream  in= new ByteArrayInputStream(
				xmlString.getBytes("UTF-8"));
		Document doc = XmlUtil.getDocument(in);

		List<Element> list = XmlUtil.getElements(XmlUtil.getRoot(doc));
		int i=0;
		StringBuilder html=new StringBuilder();
		for (Element element : list) {
			
			if(tdCountLine==1){
				html.append("<tr>");
				html.append(generateHTML(element.getName(),XmlUtil.getAttributeByName(element,"CN").getText(),XmlUtil.getAttributeByName(element,"VALUE").getText(),element.getText()));
				html.append("</tr>");
			}else{
				//结束标签
				if(i%tdCountLine ==0 && i!=0){
					html.append("</tr>");
				}
				//判断添加开始标签
				if(i%tdCountLine ==0){
					html.append("<tr>");
				}
				html.append(generateHTML(element.getName(),XmlUtil.getAttributeByName(element,"CN").getText(),XmlUtil.getAttributeByName(element,"VALUE").getText(),element.getText()));
				
				//判断总数量不能除尽时，添加结束标签
				if(list.size()==(i+1)){
					html.append("</tr>");
				}
			}
			i++;
		}
		return html.toString();
	}
	
	/**
	 * 根据XMLnode元素反馈HTML
	 */
	public static String generateHTML(String CODE,String CN,String VALUE,String TEXT){
		StringBuilder html=new StringBuilder();
		html.append("<td  class=\"tdForm_Rd_Title\"><label>"+CN+"：</label></td>");
		html.append("<td style=\"text-align: left;\" class=\"tdForm_Rd_Title\"><input type=\"hidden\" id=\""+CODE+"\" value=\""+VALUE+"\"><label>"+TEXT+"</label></td>");
		return html.toString();
	}
	
	public void againDemand(){
		try{
			String id=getString("id");//需求单ID
			String OrderContentId=getString("OrderContentId");//产品ID
			String tasklistID=getString("tasklistID");//任务ID
			String ProcessId=getString("ProcessId");//流程ID
			String waitId=getString("waitId");//代办ID
			String approval=getString("approval");//主送人
			String approvalId=getString("approvalId");//主送人名称
			String ccName=getString("ccName");//抄送人名称
			String ccId=getString("ccId");//抄送人ID
			Bis_DemandList demandList= integrationService.getDemandList(id);
		    Bis_ProcessList ProcessBean= integrationService.getProcessList(ProcessId);
		    //Bis_OrderContent orderContent = integrationService.getOrderContent(OrderContentId);
		    Calendar dar=Calendar.getInstance();
		    dar.setTime(new Date());
		    dar.add(java.util.Calendar.HOUR_OF_DAY, 4);
			String[] appname =null;
		    String[] appid = null;
			if(!approvalId.equals("")&&approvalId!=null){
				appname = approval.split(";");
			    appid = approvalId.split(";");
			}
			SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String da = "2017-01-25 08:00:00";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	        Calendar date = Calendar.getInstance();
	        date.setTime(new Date());
	        date.set(Calendar.DATE, date.get(Calendar.DATE)+1);
	        String lastDay = sdf.format(date.getTime()) + " 08:00:00";//明天日期
	        Date dateTmp=getDate(sdf2.parse(lastDay),0);
		    if(appid!=null){
			    for(int i=0;i<appid.length;i++){
		    		Bis_TaskList TaskListtwo = new Bis_TaskList();
		    		TaskListtwo.setProcess(ProcessBean.getProcess());
		    		TaskListtwo.setCreator(user.getEmployeeName());
		    		TaskListtwo.setCreatorNo(user.getRowNo()+"");
		    		TaskListtwo.setCreatDate(getStringDate(new Date()));
		    		TaskListtwo.setPlanDate(getStringDate(dar.getTime()));
		    		TaskListtwo.setOper(appname[i]);
		    		TaskListtwo.setOperNo(appid[i]);
		    		TaskListtwo.setFatherUuid(tasklistID);
		    		TaskListtwo.setStatus("1");
		    		TaskListtwo.setType("SH");
		    		TaskListtwo.setExpectedCompletionTime(sdf2.format(dateTmp));
		    		Bis_TaskList TaskListdcdsa=integrationService.saveTaskList(TaskListtwo);
		    		daibanone(demandList,ProcessBean,appid[i],TaskListdcdsa,OrderContentId,"SH");
			    }
		    }
		    String[] csid =null;
			String[] csname = null;//抄送人名称
			if(!ccId.equals("")&&ccId!=null){
				csid = ccId.split(";");
				csname = ccName.split(";");
			}
			if(csid!=null){
				for(int i=0;i<csid.length;i++){
		    		Bis_TaskList TaskListthree = new Bis_TaskList();
		    		TaskListthree.setProcess(ProcessBean.getProcess());
		    		TaskListthree.setCreator(user.getEmployeeName());
		    		TaskListthree.setCreatorNo(user.getRowNo()+"");
		    		TaskListthree.setCreatDate(getStringDate(new Date()));
		    		TaskListthree.setPlanDate(getStringDate(dar.getTime()));
		    		TaskListthree.setOper(csname[i]);
		    		TaskListthree.setOperNo(csid[i]);
		    		TaskListthree.setFatherUuid(tasklistID);
		    		TaskListthree.setStatus("1");
		    		TaskListthree.setType("CS");
		    		TaskListthree.setExpectedCompletionTime(sdf2.format(dateTmp));
		    		Bis_TaskList TaskListdsa= integrationService.saveTaskList(TaskListthree);
		    		daibanone(demandList,ProcessBean,csid[i],TaskListdsa,OrderContentId,"CS");
			    }
			}
			//结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
		    Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/**
	 * 获取附件消息
	 */
	public void fuJian() {
		String id = getString("id");
		String biaoshi = getString("biaoshi");
		List<Map<String, String>> s = integrationService.fuJian(id,biaoshi);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(s));
	}
	
	/**
	 * 根据ID查询跟踪处理
	 * @return
	 */
	public void processtracking() {
		String id = getString("id");
		List<Bis_TaskList> p = integrationService.processtracking(id);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(p));
	}
	
	public void contractOfTransfer(){
		try{
			String id=getString("id");//需求单ID
			//String OrderContentId=getString("OrderContentId");//产品ID
			String tasklistID=getString("tasklistID");//任务ID
			//String ProcessId=getString("ProcessId");//流程ID
			String waitId=getString("waitId");//代办ID
			Bis_DemandList demandList= integrationService.getDemandList(id);//查询是否有需求单
			demandList.setState("1");
			integrationService.updateDemandList(demandList);
			//Bis_ProcessList ProcessBean= integrationService.getProcessList(ProcessId);//查询是否有流程
			//Bis_OrderContent orderContent = integrationService.getOrderContent(OrderContentId);//查询是否有刹那品信息
			Bis_TaskList TaskList =  integrationService.getTaskList(tasklistID);//查询是否有任务
			TaskList.setStatus("2");
			integrationService.updateTasklist(TaskList);
			
			//结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
	    	Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void queryOrderType(){
		String pcode = getString("pcode");
		ProductType productType= integrationService.queryProduectBidTY(pcode);
		System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormats(productType));
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(productType));
	}
	
	public void getdemandListtwo(){
		String id = getString("id");//需求单编号
		PageRequest page = new PageRequest(getRequest());
		PageResponse response = integrationService.getdemandListtwo(id,page);
		String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
		Write(json);
	}
	
	public void returnIntegration(){
		try{
			String id=getString("id");//需求单ID
			String waitId=getString("waitId");//代办ID
			String OrderContentId=getString("OrderContentId");//产品信息ID
			String zpcode=getString("zpcode");//产品CODE
			String operType=getString("operType");//操作类型
			String dType=getString("dType");////产品类型
			String ProcessId=getString("ProcessId");//流程ID
			String groupCoding=getString("groupCoding");//集团280
			Bis_DemandList demandList= integrationService.getDemandList(id);//查询需求单
			demandList.setState("0");
			integrationService.updateDemandList(demandList);
			
			WaitTask wt = new WaitTask();
			wt.setName("[通专融合]"+demandList.getDemandTitle());
			wt.setCreationTime(new Date());
			wt.setUrl("jsp/integration/returnToResubmit.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
					+ ProcessId
					/*+ "&productid="//产品ID
					+ OrderContentId*/
					+ "&dproduct="//产品
					+ demandList.getdProduct()
					+ "&opertype="//操作类型
					+ demandList.getOperType()
					+ "&dtype="//产品类型
					+ demandList.getdType()
					+ "&groupcode="//集团280
					+ demandList.getGroupCode()
					+ "&userID="//状态
					+ user.getRowNo()
					/*+ "&type="//状态
					+ type
					+ "&tasklistID="
					+ tasklistID*/);
			SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(demandList.getCreatorNo()));
			wt.setState(WaitTask.HANDLE);
			wt.setHandleUserId(USER.getRowNo());
			wt.setHandleUserName(USER.getEmployeeName());
			wt.setHandleLoginName(USER.getLoginName());
			wt.setCreateUserId(user.getRowNo());
			wt.setCreateUserName(user.getEmployeeName());
			wt.setCreateLoginName(user.getLoginName());
			wt.setCode(Bis_DemandList.Integration);
			wt.setTaskId(demandList.getUuid());
			service.saveWait(wt,this.getRequest());
			
			//结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	public void toVoidsubmit(){
		try{
			String id = getString("id");
			String waitId=getString("waitId");//代办ID
			Bis_DemandList DemandList= integrationService.getDemandList(id);
			String attachmentId = getString("attachmentId");
			String zpclass="class1";//这是业务分类
			String pcode = this.getString("pcode");//产品类型
			String ot = this.getString("ot");//操作类型
			String zpcode = this.getString("zpcode");//产品
			String demandTitle = this.getString("demandTitle");//需求名称
			String xuqiuDate = this.getString("xuqiuDate");//需求完成时间
			String booleans = this.getString("boolean");//是否加急
			String contact = this.getString("contact");//联系人
			String contactMoblie = this.getString("contactMoblie");//联系电话
			String dDesc = this.getString("dDesc");//需求描述
			String groupCoding = this.getString("groupCoding");//集团280
			String money = this.getString("money");//产品资费
			String rebate = this.getString("rebate");//折扣
			String zproductid = this.getString("zproductid");//产品信息保存的ID
			String userID= getString("userID");
			String whetherOrNotToMerge = getString("whetherOrNotToMerge");//是否走融合系统
			DemandList.setDemandTitle(demandTitle);
			//查不到时：是根据区域首写字母和产品类型：
			if("error".equals(commonSingleService.returnCode("888","888",user,false,"8"))){
				this.Write("error");
				return;
			}
			String quxian="";
			String fengongsi="";
			List<Object[]> sone=commonSingleService.getSystemDept(user.getRowNo());
			for(int i=0;i<sone.size();i++){
				quxian = (String)sone.get(i)[0];
				fengongsi=(String)sone.get(i)[1];
			}
			//DemandList.setDemandNumber(commonSingleService.returnCode("888",null,user,true,null));
			DemandList.setOperType(ot);
			DemandList.setdClass(zpclass);
			DemandList.setdType(pcode);
			DemandList.setdProduct(zpcode);
			DemandList.setdNumber("1");
			DemandList.setGroupCode(groupCoding);
			DemandList.setContact(contact);
			DemandList.setContactMoblie(contactMoblie);
			DemandList.setdDesc(dDesc);
			DemandList.setCity(fengongsi);
			DemandList.setDistrict(quxian);
			DemandList.setCreator(user.getEmployeeName());
			DemandList.setCreatorNo(user.getRowNo()+"");
			DemandList.setCreatDate(getStringDate(new Date()));
			DemandList.setXuqiuDate(xuqiuDate);
			DemandList.setBooleans(booleans);
			DemandList.setState("1");
			DemandList.setWhetherOrNotToMerge(whetherOrNotToMerge);
			Bis_DemandList demandList= integrationService.updateDemandList(DemandList);
			/*//保存产品资费以及折扣
			Bis_OrderContent orderContent = integrationService.getOrderContent(zproductid);
			if(orderContent!=null){
				orderContent.setMoney(money);
				orderContent.setRebate(rebate);
				orderContent.setDemandNumber(demandList.getDemandNumber());
				integrationService.saveOrderContent(orderContent);
			}*/
			
			//保存产品资费以及折扣
			String[] XMLid = null;
			XMLid = zproductid.split(",");
			for(int i=0;i<XMLid.length;i++){
				Bis_OrderContent orderContent = integrationService.getOrderContent(XMLid[i]);
				if(orderContent!=null){
					orderContent.setMoney(money);
					orderContent.setRebate(rebate);
					orderContent.setDemandNumber(demandList.getDemandNumber());
					integrationService.saveOrderContent(orderContent);
				}
				//保存需求单和产品的中间表信息
				Bis_DemandOrdercontent demandOrdercontent = integrationService.getordertentOronetomony(XMLid[i]);
				if(demandOrdercontent!=null){
					demandOrdercontent.setDemandId(demandList.getUuid());
					demandOrdercontent.setOrdercontentId(XMLid[i]);
					integrationService.updateDemandOrdercontent(demandOrdercontent);
					demandOrdercontent=null;
				}else{
					Bis_DemandOrdercontent DemandOrdercontent = new Bis_DemandOrdercontent();
					DemandOrdercontent.setDemandId(demandList.getUuid());
					DemandOrdercontent.setOrdercontentId(XMLid[i]);
					integrationService.saveDemandOrdercontent(DemandOrdercontent);
				}
			}
			//保存附件
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] json = attachmentId.split(",");
					if (json.length > 0) {
						for (int i = 0; i < json.length; i++) {
							SingleAndAttachment sa = new SingleAndAttachment();
							sa.setOrderID(id);
							sa.setAttachmentId(json[i]);
							sa.setLink(Bis_DemandList.Integration);
							integrationService.saveSandA(sa);
						}
					}
				}
			}
			
			//结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
		    Bis_ProcessList processBean=integrationService.getBis_ProcessList(demandList.getDemandNumber());
			String type = "DDJL";
			WaitTask wt = new WaitTask();
			wt.setName("[通专融合]"+demandList.getDemandTitle());
			wt.setCreationTime(new Date());
			/*wt.setUrl("jsp/integration/handleIntegrationtwo.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
					+ processBean.getProcess()
					+ "&OrderContentId="//产品ID
					+ orderContent.getUuid()
					+ "&zpcode="//产品
					+ demandList.getdProduct()
					+ "&operType="//操作类型
					+ demandList.getOperType()
					+ "&dType="//产品类型
					+ demandList.getdType()
					+ "&groupCoding="//集团280
					+ demandList.getGroupCode()
					+ "&type="//状态
					+ type
					);*/
			
			
			/*if(demandList.getWhetherOrNotToMerge().equals("1")){
				wt.setUrl("http://10.113.193.18:1306/tailor/http://*************:8080/EOM/jsp/integration/handleIntegrationtwo.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
						+ processBean.getProcess()
						+ "&OrderContentId="//产品ID
						+ OrderContentId
						+ "&zpcode="//产品
						+ demandList.getdProduct()
						+ "&operType="//操作类型
						+ demandList.getOperType()
						+ "&dType="//产品类型
						+ demandList.getdType()
						+ "&groupCoding="//集团280
						+ demandList.getGroupCode()
						+ "&type="//状态
						+ type
						);
			}else{*/
			wt.setUrl("jsp/integration/handleIntegrationtwo.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
					+ processBean.getProcess()
					/*+ "&OrderContentId="//产品ID
					+ OrderContentId*/
					+ "&zpcode="//产品
					+ demandList.getdProduct()
					+ "&operType="//操作类型
					+ demandList.getOperType()
					+ "&dType="//产品类型
					+ demandList.getdType()
					+ "&groupCoding="//集团280
					+ demandList.getGroupCode()
					+ "&type="//状态
					+ type
					);
			/*}*/
			
			SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(userID));
			wt.setState(WaitTask.HANDLE);
			wt.setHandleUserId(USER.getRowNo());
			wt.setHandleUserName(USER.getEmployeeName());
			wt.setHandleLoginName(USER.getLoginName());
			wt.setCreateUserId(user.getRowNo());
			wt.setCreateUserName(user.getEmployeeName());
			wt.setCreateLoginName(user.getLoginName());
			wt.setCode(Bis_DemandList.Integration);
			wt.setTaskId(demandList.getUuid());
			service.saveWait(wt,this.getRequest());
		    
		    
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 这是需求单作废方法，如果需求单作废了，
	 * 那么签订的相应合同也要作废
	 */
	public void toVoid(){
		try{
			String id = getString("id");
			String waitId=getString("waitId");//代办ID
			Bis_DemandList DemandList= integrationService.getDemandList(id);
			DemandList.setState("-1");//作废状态
			integrationService.updateDemandList(DemandList);
			List<OrderContractManyToOne> of=integrationService.getOrderContractManyToOne(id);
			Contract contract=null;
			for(int i=0;i<of.size();i++){
				contract= integrationService.getContract(of.get(i).getContractID());
			}
			contract.setState(2);
			integrationService.updateContract(contract);
			//结束当前代办
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
		    Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 这是需求单作废方法，如果需求单作废了，
	 * 那么签订的相应合同也要作废
	 */
	public void deleteIntegration(){
		try{
			String id = getString("id");
			Bis_DemandList DemandList= integrationService.getDemandList(id);
			DemandList.setState("-1");//作废状态
			integrationService.updateDemandList(DemandList);
		    Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	/**
	 * 订单经理提交方法
	 * 生成正式订单；生成工作台
	 */
	public void orderManagerSubmission(){
		try{
			String id = getString("id");
			String waitId=getString("waitId");//代办ID
			String ProcessId = getString("ProcessId");
			String opinion = getString("opinion");//意见
			String attachmentId = getString("attachmentId");
			Bis_ProcessList ProcessBean= integrationService.getProcessList(ProcessId);
			//先保存自己本身的任务===========
		    Bis_TaskList TaskList = new Bis_TaskList();
			TaskList.setProcess(ProcessBean.getProcess());
			TaskList.setCreator(user.getEmployeeName());
			TaskList.setCreatorNo(user.getRowNo()+"");
			TaskList.setCreatDate(getStringDate(new Date()));
			TaskList.setPlanDate(getStringDate(new Date()));
			TaskList.setOper(user.getEmployeeName());
			TaskList.setOperNo(user.getRowNo()+"");
			TaskList.setOperDate(getStringDate(new Date()));
			TaskList.setSpendTime("0");
			TaskList.setStatus("2");
			TaskList.setType("SH");
			TaskList.setReplyContent(opinion);
			//Bis_TaskList TaskBean= integrationService.saveTaskList(TaskList);
    		
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] json = attachmentId.split(",");
					if (json.length > 0) {
						for (int i = 0; i < json.length; i++) {
							SingleAndAttachment sa = new SingleAndAttachment();
							sa.setOrderID(id);
							sa.setAttachmentId(json[i]);
							sa.setLink(Bis_DemandList.Integration);
							dedicatedFlowService.saveSandA(sa);
							//得到每个对象中的属性值
						}
					}
				}
			}
			
    		
			Bis_DemandList DemandList= integrationService.getDemandList(id);
			DemandList.setState("4");
			integrationService.updateDemandList(DemandList);
			String quxian="";
			String fengongsi="";
			String bumen="";
			List<Object[]> sone=integrationService.getSystemDept(user.getRowNo());
			for(int i=0;i<sone.size();i++){
				quxian = (String)sone.get(i)[0];
				fengongsi=(String)sone.get(i)[1];
				bumen=(String)sone.get(i)[2];
			}
			//查询集团信息
			GroupCustomer findDBCustomer=groupCustomerService.queryGroup(DemandList.getGroupCode());
			//查询需求单所有的产品根据查询出来的
 			int of=integrationService.getOrdertentCount(id);
			System.out.println(of);
			//查询所有产品集合根据中间表的条数来循环修改
			List<Bis_OrderContent> listBis_OrderContent = integrationService.getlistBis_OrderContent(DemandList.getDemandNumber());
			for(int i=0;i<of;i++){
				String title = "";
				if(listBis_OrderContent.get(i).getTitle()==null){
					title="";
				}else{
					title="-"+listBis_OrderContent.get(i).getTitle();
				}
				OrderForm orderFormone = new OrderForm();
				orderFormone.setUserId(user.getRowNo());
				orderFormone.setUserName(user.getEmployeeName());
				orderFormone.setpCode(DemandList.getdType());//产品类型
				//根据产品编码 查询 业务编码并且保存
				//BusinessType bt = dedicatedFlowService.queryBcode(pCode.get(j));
				orderFormone.setbCode(DemandList.getdClass());
				orderFormone.setOrderTitle(DemandList.getDemandTitle()+title);
				orderFormone.setDemandName(DemandList.getDemandTitle());
				orderFormone.setParentOrderNumber(DemandList.getUuid());
				orderFormone.setDraftTime(new Date());
				orderFormone.setState("0");
				orderFormone.setDraftmanId(user.getRowNo() + "");
				orderFormone.setDraftman(user.getEmployeeName());
				orderFormone.setSubmissionMode("W");
				orderFormone.setType("DI");
				orderFormone.setTransmitState("1");
				orderFormone.setOperationType(DemandList.getOperType());//操作类型
				orderFormone.setGroupCustomerId(findDBCustomer.getGroupId());//集团客户ID
				orderFormone.setOrderReqDescription(DemandList.getdDesc());//需求描述
				orderFormone.setOrderReqDescriptionHTML("<p>"+DemandList.getdDesc()+"</p>");
				orderFormone.setOrderReqTimeLimit(stringdate(DemandList.getXuqiuDate()));
				orderFormone.setSignedStatus(null);
				orderFormone.setRemarks(DemandList.getdDesc());//备注
				orderFormone.setOrderTypeIdent(1);
				orderFormone.setZpcode(DemandList.getdProduct());
				orderFormone.setOrderNumber(commonSingleService.returnCode("888","888",user,false,"8"));//订单编号
				orderFormone.setSystemDeptID(bumen);
				orderFormone.setPrantsystemDeptID(quxian);
				orderFormone.setSystemCompanyID(fengongsi);
				orderFormone.setInttype("INTTYPE");
				OrderForm orderForm= integrationService.saveOrUpdateOrder(orderFormone);
				listBis_OrderContent.get(i).setOrderNumbertwo(orderForm.getOrderNumber());
				integrationService.updateOrderDetail(listBis_OrderContent.get(i));
				
				//getlink(id,waitId);
				Bis_DemandList demandList=integrationService.getDemandList(id);
				//OrderForm orderForm = integrationService.getorderfrom(demandList.getUuid());
				String orderStageId = null;
				List<LinkTemplate> pt = dedicatedFlowService.queryProcFlow(orderForm.getpCode(),orderForm.getOperationType());
				for (LinkTemplate l : pt) {
					OrderStages ss = new OrderStages();
					ss.setOrderId(orderForm.getOrderId());//订单ID
					ss.setTwoStageId(l.getLinkTempCode());
					// 根据模板需求时限：// 预计完成时限 //WF 2016-11-26 13:31 修改：//
					if (!StringUtils.isEmpty(l.getLinkNeedLimit())) {
						ss.setExpectedCompletionTime(DateUtil.getDateAddHour(Integer.parseInt(l.getLinkNeedLimit())));
					}
					SystemUser user2 = systemUserService.getUserInfoRowNo(orderForm.getUserId());
					if ("1".equals(l.getBossTrackName())) {
						ss.setIsBossValue("1");
					}
					if ("需求申请".equals(l.getLinkName())) {
						ss.setIsBossValue("0");
						if ("1".equals(l.getBossTrackName())) {
							ss.setStageState("-1");
							dedicatedFlowService.saveOrderStages(ss);
							orderStageId = l.getLinkTempCode();
						} else {
							ss.setStageState("1");
							ss.setActionUser(user2.getEmployeeName());
							ss.setActionUserPhone(user2.getMobile());
							ss.setOperTime(DateUtil.getDateone());
							dedicatedFlowService.saveOrderStages(ss);
							orderStageId = l.getLinkTempCode();
						}
					} else {
						ss.setStageState("-1");
						dedicatedFlowService.saveOrderStages(ss);
					}
				}
				
				
				Dictionary dictionary= integrationService.getDictionary(demandList.getOperType());//操作类型
				List<SystemDept> deptList = user.getSystemDept();
				String citycode =deptList.get(0).getSystemCompany().getCompanyCode();
				OrderForm order= new OrderForm();
				String emergencySituation = dictionary.getEmergencyDegree();//紧急程度；
				String demandArea = "000";
				String sourceChannel = "000";//固定000客户经理
				String requestType = dictionary.getEmergencyDegree();//紧急程度
				String city = citycode;//地市CODE
				order = dedicatedFlowService.queryId(orderForm.getOrderId());
				GroupCustomer customer = groupCustomerService.getGroupCustomerSQLById(order.getGroupCustomerId());
				order.setState("0");
				order.setOrderStatus(0);
				OrderInformation orderinfor = new OrderInformation();
				if (emergencySituation != null
						&& !"".equals(emergencySituation)) {
					orderinfor.setEmergencySituation(emergencySituation);
				}
				if (requestType != null && !"".equals(requestType)) {
					orderinfor.setRequestType(requestType);
				}
				if (demandArea != null && !"".equals(demandArea)) {
					orderinfor.setDemandArea(demandArea);
				}
				if (sourceChannel != null && !"".equals(sourceChannel)) {
					orderinfor.setSourceChannel(sourceChannel);
				}
				if (city != null && !"".equals(city)) {
					orderinfor.setCity(city);
				}
				orderinfor.setOrderId(order.getOrderId());
				order.setDraftTime(new Date());
				if(!"1".equals(demandList.getWhetherOrNotToMerge())){//等于1的话那么就走融合系统，否则就推boss
					logger.info("===========================================推向BOSS============================================================");
					ResourceBundle s = ResourceBundle.getBundle("WebService-config");
					String BOSSSwitch = s.getString("BOSSSwitch");
					/**
					 * 查询预受理状态
					 * 
					 * @param so_no 订单系统订单号
					 * @param oper_no 工号ID
					 * @param oper_name 工号名称
					 * @param grpcust_id 集团编码    集团280
					 * @param busi_req_type 业务请求类型   字典表编号
					 * @param chn_id  渠道    固定000
					 * @param region_code 地市编码      地市code
					 * @param group_id  区域编码       固定000  
					 * @param obtain_time 业务获取时间     当前时间
					 * @param chance_name 业务请求名称     订单标题
					 * @param pri_code 紧急程度  可固定
					 * @param demand_desc 需求描述
					 * @param person_no si 工号
					 */
					if ("start".equals(BOSSSwitch)) {
						try {
							if ("".equals(user.getBossUserName())
									|| "null".equals(user.getBossUserName())
									|| null == user.getBossUserName()) {
								Write("-1");
								return;
							}
							StartPreOrderOut orderOut = CMCCOpenService
									.getInstance().startPreOrder(
											order.getOrderNumber(),
											user.getOperNo(),
											user.getOperName(),
											customer.getGroupCoding(),
											orderinfor.getRequestType(),
											orderinfor.getSourceChannel(),
											orderinfor.getCity(),
											orderinfor.getDemandArea(),
											new SimpleDateFormat("yyyy-MM-dd hh:mm:ss").format(new Date()),
											order.getOrderTitle(),
											orderinfor.getEmergencySituation(),
											"", 
											user.getBossUserName());
							if ("0000000".equals(orderOut.getResCode())) {
								order.setBossState(1);
								order.setBossFormNo(orderOut.getBossNo());
								logger.info("BOSS推送成功：参数getBossNo："+orderOut.getBossNo());
							} else {
								order.setBossState(0);
								Write("3");
								return;
							}
						} catch (Exception e) {
							e.printStackTrace();
							logger.info("+BOSS推送失败："+e.getMessage());
							Write("3"+e.getMessage());
							return;
						}
		
					}
				}else{
					Bis_ProcessList processBean=integrationService.getBis_ProcessList(demandList.getDemandNumber());
					String type = "RHXT";
					WaitTask wt = new WaitTask();
					String titletwo = "";
					if(listBis_OrderContent.get(i).getTitle()==null){
						titletwo="";
					}else{
						titletwo="-"+listBis_OrderContent.get(i).getTitle();
					}
					wt.setName("[通专融合]"+demandList.getDemandTitle()+titletwo);
					wt.setCreationTime(new Date());
					wt.setUrl("http://10.113.193.18:1306/tailor/http://*************:8080/EOM/jsp/integration/handleIntegrationtwo.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
							+ processBean.getProcess()
							+ "&OrderContentId="//产品ID
							+ listBis_OrderContent.get(i).getUuid()
							+ "&zpcode="//产品
							+ demandList.getdProduct()
							+ "&operType="//操作类型
							+ demandList.getOperType()
							+ "&dType="//产品类型
							+ demandList.getdType()
							+ "&groupCoding="//集团280
							+ demandList.getGroupCode()
							+ "&type="//状态
							+ type
							+ "&un="
							+ user.getLoginName()
							+ "&T="
							+ System.currentTimeMillis()
							+ "&orderId="//订单ID，融合需要
							+ orderForm.getOrderId());
					//SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
					//SystemUser USERTWO= systemUserService.getUserInfoRowNo(Integer.parseInt(demandList.getCreatorNo()));
					wt.setState(WaitTask.HANDLE);
					wt.setHandleUserId(user.getRowNo());
					wt.setHandleUserName(user.getEmployeeName());
					wt.setHandleLoginName(user.getLoginName());
					wt.setCreateUserId(user.getRowNo());
					wt.setCreateUserName(user.getEmployeeName());
					wt.setCreateLoginName(user.getLoginName());
					wt.setCode(Bis_DemandList.Integration);
					wt.setTaskId(demandList.getUuid());
					service.saveWait(wt,this.getRequest());
				}
				dedicatedFlowService.saveOrderinfor(orderinfor);
				dedicatedFlowService.saveOrUpdateOrder(order);
				processService.updateOrder(order.getOrderId(),orderStageId, "1", new Date(), "", "");
				//结束当前代办
			}
		    if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	public Date stringdate(String dataString){
		Date date=null;
		try {
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			date = sdf.parse(dataString);
		} catch (ParseException e) {
			e.printStackTrace();
		}
		return date;
	}
		
	
	public void orderlist(){
		try{
			PageRequest page = new PageRequest(getRequest());
			String number = getString("number");
			String name = getString("name"); 
			PageResponse response = integrationService.getOrderForm(name,number,page,user);
			String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(response);
			System.out.println("========================-------------------"+json);
			Write(json);
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	public void getorderStages(){
		try {
			String oid = getString("oid");
			List<Map<String, String>> bu=integrationService.getOrderFormid(oid);
			Write(JSONHelper.SerializeWithNeedAnnotation(bu));
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	public void copyOdetail(){
		try{
			String id = getString("id");
			Bis_OrderContent orderDetail = integrationService.getOrderDetailtwo(id);
			Bis_OrderContent OrderContent = new Bis_OrderContent();
			Bis_OrderContent OrderContenttwo=null;
			OrderContent.setContent(orderDetail.getContent());
			OrderContent.setoProduct(orderDetail.getoProduct());
			OrderContent.setStatus("1");
			OrderContenttwo=integrationService.addOrderContent(OrderContent);
			this.Write(OrderContenttwo.getUuid());
		}catch(Exception e){
			e.printStackTrace();
			this.Write("ON");
		}
	}
	
	public void deleteOdetail(){
		try{
			String id = getString("id");
			integrationService.deleteOdetail(id);
			integrationService.deleteOrdercontentOnetoMony(id);
			this.Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			this.Write("ON");
		}
	}
	
	public void updateOdetail(){
		try{
			String id = getString("id");//id
			String title = getString("title");//这是产品信息的标题
			String[] XMLid = null;
			String[] titleText = null; 
			XMLid = id.split(",");
			if(title!=null){
				titleText = title.split(",");
			}
			
			for(int i=0;i<XMLid.length;i++){
				Bis_OrderContent orderDetail = integrationService.getOrderDetailtwo(XMLid[i]);
				orderDetail.setTitle(titleText[i]);
				integrationService.updateOrderDetail(orderDetail);
			}
			
			this.Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			this.Write("ON");
		}
	}
	
	public void getorderContentidlist(){
		try{
			List<Map<String, String>> l = new ArrayList<Map<String,String>>();
			String id = getString("id");//id
			Bis_DemandList DemandList= integrationService.getDemandList(id);
			List<Map<String, String>> list = integrationService.getorderContentidlist(DemandList.getDemandNumber());
			for(int i=0;i<list.size();i++){
				String UUID = list.get(i).get("UUID");
	            String TITLE = list.get(i).get("TITLE");
	            String MONEY = list.get(i).get("MONEY");
	            String REBATE = list.get(i).get("REBATE");
	            String OPRODUCT = list.get(i).get("OPRODUCT");
	            String ORDERNUMBERTWO = list.get(i).get("ORDERNUMBERTWO");
	            if(ORDERNUMBERTWO==null){
	            	ORDERNUMBERTWO="false";
	            }
	            
	            Map<String, String> m2 = new HashMap<String, String>();
	            m2.put("UUID",UUID);
	            m2.put("TITLE",TITLE);
	            m2.put("MONEY",MONEY);
	            m2.put("REBATE",REBATE);
	            m2.put("OPRODUCT",OPRODUCT);
	            m2.put("ORDERNUMBERTWO",ORDERNUMBERTWO);
	            l.add(m2);
			}
			String json = JSONHelper.SerializeWithNeedAnnotation(l);
			System.out.println(json);
			this.Write(json);
		}catch(Exception e){
			e.printStackTrace();
			this.Write("ON");
		}
	}
	
	public void getorderNumber(){
		try{
			String number = getString("number");//id
			OrderForm orderNumber= integrationService.getorderNumber(number);
			String json = JSONHelper.SerializeWithNeedAnnotation(orderNumber);
			this.Write(json);
		}catch(Exception e){
			e.printStackTrace();
			this.Write("ON");
		}
	}
	
	/**
	 * 客户经理提交到订单经理
	 */
	public void submissionManager(){
		try{
			String userid = getString("userId");
			String id = getString("id");
			String waitId = getString("waitId");
			Bis_DemandList demandList=integrationService.getDemandList(id);
			demandList.setState("2");
			integrationService.updatedemandList(demandList);
			Bis_ProcessList processBean=integrationService.getBis_ProcessList(demandList.getDemandNumber());
			String type = "DDJL";
			WaitTask wt = new WaitTask();
			wt.setName("[通专融合]"+demandList.getDemandTitle());
			wt.setCreationTime(new Date());
			/*if(demandList.getWhetherOrNotToMerge().equals("1")){
				wt.setUrl("http://10.113.193.18:1306/tailor/http://*************:8080/EOM/jsp/integration/handleIntegrationtwo.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
						+ processBean.getProcess()
						+ "&OrderContentId="//产品ID
						+ OrderContentId
						+ "&zpcode="//产品
						+ demandList.getdProduct()
						+ "&operType="//操作类型
						+ demandList.getOperType()
						+ "&dType="//产品类型
						+ demandList.getdType()
						+ "&groupCoding="//集团280
						+ demandList.getGroupCode()
						+ "&type="//状态
						+ type
						);
			}else{*/
			wt.setUrl("jsp/integration/handleIntegrationtwo.jsp?id="+demandList.getUuid()+"&ProcessId="//流程ID
					+ processBean.getProcess()
					/*+ "&OrderContentId="//产品ID
					+ OrderContentId*/
					+ "&zpcode="//产品
					+ demandList.getdProduct()
					+ "&operType="//操作类型
					+ demandList.getOperType()
					+ "&dType="//产品类型
					+ demandList.getdType()
					+ "&groupCoding="//集团280
					+ demandList.getGroupCode()
					+ "&type="//状态
					+ type
					);
			/*}*/
			SystemUser USER= systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
			SystemUser USERTWO= systemUserService.getUserInfoRowNo(Integer.parseInt(demandList.getCreatorNo()));
			wt.setState(WaitTask.HANDLE);
			wt.setHandleUserId(USER.getRowNo());
			wt.setHandleUserName(USER.getEmployeeName());
			wt.setHandleLoginName(USER.getLoginName());
			wt.setCreateUserId(USERTWO.getRowNo());
			wt.setCreateUserName(USERTWO.getEmployeeName());
			wt.setCreateLoginName(USERTWO.getLoginName());
			wt.setCode(Bis_DemandList.Integration);
			wt.setTaskId(demandList.getUuid());
			service.saveWait(wt,this.getRequest());
			if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	public void getOrderNumber(){
		try {
			String number=getString("number");
			int tdCountLine=getInteger("tdCountLine");
			Bis_OrderContent orderDetail=integrationService.getOrderNumber(number);
			if(orderDetail!=null){
			 String html=XmlToHtml(orderDetail.getContent(),tdCountLine);
			 Write(html);
			}else{
				Write("");
			}
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void getGroupCustomer(){
		try {
			String groupCoding=getString("groupCoding");
			GroupCustomer customer = groupCustomerService.queryGroup(groupCoding);
			String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(customer);
			Write(json);
		} catch (Exception e) {
			e.printStackTrace();
			Write("NO");
		}
	}
	
	public void submissionFusion(){
		try{
			String id=getString("id");
			String waitId=getString("waitId");
			String attachmentId=getString("attachmentId");
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] json = attachmentId.split(",");
					if (json.length > 0) {
						for (int i = 0; i < json.length; i++) {
							SingleAndAttachment sa = new SingleAndAttachment();
							sa.setOrderID(id);
							sa.setAttachmentId(json[i]);
							sa.setLink(Bis_DemandList.Integration);
							dedicatedFlowService.saveSandA(sa);
							//得到每个对象中的属性值
						}
					}
				}
			}
			if(!"".equals(waitId)&&waitId!=null){
				WaitTask wait = service.queryWaitByTaskId(waitId);
				service.updateWait(wait,this.getRequest());
			}
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}
	
	/*public void gets(){
		try{
			System.out.println("进入多线程方法");
			String name = "李阳";
			Treund tr = new Treund(name);
			tr.start();
			System.out.println("多线程方法运行完毕");
			Write("OK");
		}catch(Exception e){
			e.printStackTrace();
			Write("NO");
		}
	}*/
	
	
	/**
	 * 查询节假日
	 */
	public void getDateCount(){
		try{
			SimpleDateFormat sdf2 = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			String da = "2017-01-25 08:00:00";
			SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
	        Calendar date = Calendar.getInstance();
	        date.setTime(sdf2.parse(da));
	        date.set(Calendar.DATE, date.get(Calendar.DATE)+1);
	        String lastDay = sdf.format(date.getTime()) + " 08:00:00";//明天日期
	        Date dateTmp=getDate(sdf2.parse(lastDay),0);
	        Write("计算后的时间为"+sdf2.format(dateTmp));
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
	
	public Date getDate(Date date,int times){
        Holiday holidaya = integrationService.getHolidayBean(date);
        Date dateTmp = date;
        while(holidaya != null){
        	Calendar date2 = Calendar.getInstance();
        	date2.setTime(holidaya.getDateV());
        	date2.set(Calendar.DATE, date2.get(Calendar.DATE)+1);
        	dateTmp = date2.getTime();
        	holidaya = integrationService.getHolidayBean(date2.getTime());
        }
        times++;
        if(times < Integer.parseInt(EXPECTEDNUMBEROFDAYS)) {
        	Calendar date2 = Calendar.getInstance();
        	date2.setTime(dateTmp);
        	date2.set(Calendar.DATE, date2.get(Calendar.DATE)+1);
        	dateTmp = date2.getTime();
        	dateTmp = getDate(dateTmp,times);
        }
		return dateTmp;
	}
	
	public void getBisProcessList(){
		try{
			String demendnumber = getString("demendnumber");
			Bis_ProcessList process= integrationService.getBis_ProcessList(demendnumber);
			String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotation(process);
			Write(json);
		}catch(Exception e){
			e.printStackTrace();
			Write("ON");
		}
	}
}
