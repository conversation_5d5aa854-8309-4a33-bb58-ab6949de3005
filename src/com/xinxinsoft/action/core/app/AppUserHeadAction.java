package com.xinxinsoft.action.core.app;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.io.PrintWriter;
import java.util.UUID;

import org.apache.commons.io.IOUtils;
import org.apache.struts2.ServletActionContext;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.service.core.app.AppUserInfoService;

/**
 * @Title AppUserHeadAction.java
 * @Package com.xinxinsoft.action.core.app
 * <AUTHOR> QQ：361229957 E-mail：<EMAIL>
 * @date 2016-4-6 下午4:20:46
 * @version 1.0 
 */
public class AppUserHeadAction extends BaseAction{
	
	private File headimg; //上传的文件
    private String headimageFileName; //文件名称
    private AppUserInfoService appUserInfoService;
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	/**
	 * 用户头像上传
	 * @throws Exception
	 */
	public void upload() throws Exception{
		//图片将要上传到物理位置
		Integer rowNo = getInteger("rowNo");
		String savepath = this.getProjectDir() + "/headimg"; // ServletActionContext.getServletContext().getRealPath("headimg");
		String saveDir = savepath;
		File savefile = new File(savepath);
		if(!savefile.exists()){
			savefile.mkdirs();
		}
		InputStream is = new FileInputStream(headimg);
		savepath += "/" + UUID.randomUUID()+".jpg";
		OutputStream os = new FileOutputStream(savepath);
		IOUtils.copy(is, os); //简便方法
		is.close();
		os.flush();
		os.close();
		String headPath = savepath.replace(this.getProjectDir(), "");
		appUserInfoService.saveHeadImage(rowNo, headPath , saveDir);
		PrintWriter out = getResponse().getWriter();
		out.print(headPath);
		
		//getUserService().updateUserHead(getUid(), headPath);
		
	}
	public File getHeadimg() {
		return headimg;
	}
	public void setHeadimg(File headimg) {
		this.headimg = headimg;
	}
	public String getHeadimageFileName() {
		return headimageFileName;
	}
	public void setHeadimageFileName(String headimageFileName) {
		this.headimageFileName = headimageFileName;
	}
	public AppUserInfoService getAppUserInfoService() {
		return appUserInfoService;
	}
	public void setAppUserInfoService(AppUserInfoService appUserInfoService) {
		this.appUserInfoService = appUserInfoService;
	}

}
