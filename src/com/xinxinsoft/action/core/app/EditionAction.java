package com.xinxinsoft.action.core.app;

import java.io.File;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

import org.apache.log4j.Logger;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.Edition;
import com.xinxinsoft.service.core.app.EditionService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.page.PageRequest;
import com.xinxinsoft.utils.page.PageResponse;

/**
 * 版本接口
 * <AUTHOR>
 * @date 2016-11-9 下午5:25:53
 */
public class EditionAction extends BaseAction{
	private static final Logger log = Logger.getLogger(EditionAction.class);
	private static final long serialVersionUID = 1L;
	private EditionService editionService;
	public EditionService getEditionService() {
		return editionService;
	}
	public void setEditionService(EditionService editionService) {
		this.editionService = editionService;
	}
	
	/**
	 * 版本信息
	 */
	
	public PageResponse doList(PageRequest page){		
		String editionType = getString("editionType");//版本类型		
		if(editionType!=null&&!"".equals(editionType)){
			editionType=editionType.toUpperCase();			
		}
		return editionService.getEditionInfo(editionType, page);		
	}
	
	/**
	 * 删除该版本
	 */
	public void deleteEdition(){
		String editionId = getString("editionId");//版本ID
		String editionType = getString("editionType");//版本类型
		if(editionType!=null&&!"".equals(editionType)){
			editionType=editionType.toUpperCase();			
		}
		boolean bo = editionService.deleteEditionById(editionId,editionType);
		if(bo){
			writeText("删除成功");
		}else{
			writeText("已经是最后一个版本,不能删除");
		}
	}
	
	/**
	 * 查询版本信息
	 */
	public void editionInfo(){
		try{
			String editionId = getString("id");
			Map<String,String> map = new HashMap<String,String>();
			Edition edition = editionService.getEditionById(editionId);
			map.put("editionNumber", edition.getEditionNumber());
			map.put("editionType", edition.getEditionType());
			map.put("editionName", edition.getEditionName());
			map.put("updateInstructions", edition.getUpdateInstructions());
			String json = JSONHelper.SerializeWithNeedAnnotation(map);
			Write(json);
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	
	/**
	 * 修改版本信息
	 */
	public void updateEdition(){	
		try {
			String editionId = getString("id");
			String oldEditionNumber = getString("oldEditionNumber");//修改前的版本号			
			String editionNumber = getString("editionNumber");//修改后的版本号
			String editionType = getString("editionType");
			if(editionType!=null){//版本类型不为空转成大写
				editionType=editionType.toUpperCase();
			}
			String editionName = getString("editionName");
			String updateInstructions = getString("updateInstructions");
			
			Edition edition = editionService.getEditionById(editionId);
			edition.setEditionNumber(editionNumber);
			edition.setEditionName(editionName);
			edition.setEditionType(editionType);
			edition.setUpdateInstructions(updateInstructions);
			editionService.updateEdition(edition);
			getResponse().getWriter().print("OK");						
		    //修改文件的名称    
		    String oldFileName="EOMAPP_"+ oldEditionNumber+".apk";//原文件名
		    String newFileName="EOMAPP_"+ editionNumber+".apk";//新文件名
		    String oldPath=FileUpload.getEditionURL()+oldFileName;//配置文件存放地址 
		    //String newPath=FileUpload.getEditionURL()+newFileName;//配置文件存放地址 
		    System.out.println("old path := " + oldPath);
			File file = new File(oldPath);
			String c=file.getParent();
	        File f=new File(c+File.separator+newFileName);   
	        if(file.renameTo(f))   
	        {   
	        System.out.println("修改成功!");   
	        }   
	        else   
	        {   
	        System.out.println("修改失败");   
	        }  
			} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
	}
	
	
	
}
