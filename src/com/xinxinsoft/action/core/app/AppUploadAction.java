package com.xinxinsoft.action.core.app;

import java.io.File;


import org.apache.log4j.Logger;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.Edition;
import com.xinxinsoft.service.core.app.AppUploadService;
import com.xinxinsoft.service.core.app.EditionService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.OutputData;
import com.xinxinsoft.utils.common.FileUpload;


/**
 * 
 * <AUTHOR>
 * @date 2017-3-16 下午2:52:20
 */
public class AppUploadAction extends BaseAction{
	private static final Logger log = Logger.getLogger(AppUploadAction.class);
	private static final long serialVersionUID = 1L;
	private AppUploadService appUploadService;
	private File attachment;// 上传文件file对象
	private String attachmentFileName;// 上传文件名
	private SystemUserService systemUserService;//用户	
	private EditionService editionService;
		
	public AppUploadService getAppUploadService() {
		return appUploadService;
	}

	public void setAppUploadService(AppUploadService appUploadService) {
		this.appUploadService = appUploadService;
	}

	public File getAttachment() {
		return attachment;
	}

	public void setAttachment(File attachment) {
		this.attachment = attachment;
	}

	public String getAttachmentFileName() {
		return attachmentFileName;
	}

	public void setAttachmentFileName(String attachmentFileName) {
		this.attachmentFileName = attachmentFileName;
	}

	public SystemUserService getSystemUserService() {
		return systemUserService;
	}

	public void setSystemUserService(SystemUserService systemUserService) {
		this.systemUserService = systemUserService;
	}
	
	public EditionService getEditionService() {
		return editionService;
	}

	public void setEditionService(EditionService editionService) {
		this.editionService = editionService;
	}

	/**
	 * EOMAPP文件上传
	 */
	public void AttachmentUploadAppSrv(){
		
		String editionType=getString("editionType");//版本类型
		String editionNumber=getString("editionNumber");//版本号
		String editionName=getString("editionName");//版本名称
		String updateInstructions=getString("updateInstructions");//版本更新内容
		if(editionType!=null&&!"".equals(editionType)){//版本名称转成大写
			editionType=editionType.toUpperCase();
		}
		OutputData od = new OutputData();
		try {
			if(editionType.equals("ANDROID")){
				int num = editionService.getEditionNumber(editionNumber, editionType);//判断该版本是否已经存在
				if(num>0){
					od.setErrorFlag("N");
					od.setErrorMessage("该版本已经存在,请重新上传");
					od.setList(null);
					Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(od));
					return;
				}
				if (attachment != null) {																	
					String  ftpUrl="";
					ftpUrl=FileUpload.getEditionURL();//配置文件存放地址
			
					  File headPath = new File(ftpUrl);//获取文件夹路径
				        if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
				        	headPath.mkdirs();
				        }
				        String pixstr =".apk";
				        String serFileName="EOMAPP_"+ editionNumber+pixstr;			        	       			       		       
					        try
					        {
					        	boolean uplaodFlag=false;		        	
					        	uplaodFlag=FileUpload.upload(ftpUrl,attachment,serFileName);
					        	if (uplaodFlag) {											        		
									Edition edition = new Edition();
									edition.setEditionName(editionName);								
									edition.setEditionType(editionType);
									edition.setEditionNumber(editionNumber);
									edition.setUpdateInstructions(updateInstructions);
									this.editionService.addEdtion(edition);
									od.setErrorFlag("Y");
									od.setErrorMessage("上传文件成功");
									od.setList(null);
									Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(od));								  
								} else {
									od.setErrorFlag("N");
									od.setErrorMessage("上传文件失败");
									od.setList(null);
									Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(od));
								}
					        }catch(Exception ex)
					        {
					        	ex.printStackTrace();
								od.setErrorFlag("N");
								od.setErrorMessage("上传文件失败："+ex.getMessage());
								od.setList(null);
								log.error("文件上传失败:" + ex.getMessage());
								Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(od));
					        }				        			        	
				} else {
					od.setErrorFlag("N");
					od.setErrorMessage("上传文件为空");
					od.setList(null);
					Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(od));
				}
			}else{
				Edition edition = new Edition();
				edition.setEditionName(editionName);			
				edition.setEditionType(editionType);
				edition.setEditionNumber(editionNumber);
				edition.setUpdateInstructions(updateInstructions);
				this.editionService.addEdtion(edition);
			}
		} catch (Exception e) {
			e.printStackTrace();
			od.setErrorFlag("N");
			od.setErrorMessage("上传文件失败："+e.getMessage());
			od.setList(null);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(od));
			log.error("文件上传失败:" + e.getMessage());
		}
	}					
}
