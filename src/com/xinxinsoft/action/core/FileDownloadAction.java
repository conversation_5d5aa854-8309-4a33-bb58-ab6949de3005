package com.xinxinsoft.action.core;

import java.io.FileNotFoundException;
import java.io.InputStream;
import java.io.UnsupportedEncodingException;

import javax.servlet.http.HttpServletRequest;

import org.apache.struts2.ServletActionContext;

import com.opensymphony.xwork2.ActionSupport;

/**
 * @Title: FileDownloadAction.java
 * @Package com.xinxinsoft.action.task
 * <AUTHOR> QQ：361229957 E-mail：<EMAIL>
 * @date 2012-1-5 下午05:17:19
 * @version V1.0
 */
public class FileDownloadAction extends ActionSupport {

	 /**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private String inputPath;   //该属性可以在配置文件中动态指定该属性值 
     
	    /** 
	     * 依赖注入该属性值的setter方法 
	     * @param inputPath 
	     */ 
	    public void setInputPath(String inputPath) { 
	        this.inputPath = inputPath; 
	    } 
	     
	    /** 
	     * 定义一个返回InputStream的方法；该方法将作为被下载文件的入口 
	     * 且需要配置stream类型结果时指定inputName参数 
	     * InputName参数的值就是方法去掉个体前缀，首字母小写的字符串 
	     * @return 
	     */ 
	    public InputStream getTargetFile() { 
	        //ServeltContext提供getResourceAsStream（）方法返回指定文件对应的输入流 
	        return ServletActionContext.getServletContext().getResourceAsStream(inputPath); 
	    } 
}
