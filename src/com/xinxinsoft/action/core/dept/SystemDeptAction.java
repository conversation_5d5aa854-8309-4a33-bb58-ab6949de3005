package com.xinxinsoft.action.core.dept;

import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Random;

import org.apache.commons.lang.StringUtils;

import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.utils.common.DefaultAction;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.service.core.dept.SystemDeptService;

/**
 * 部门Action
 * <AUTHOR>
 * @date 2016-9-5
 */
public class SystemDeptAction extends DefaultAction{
	
	private SystemDeptService sysDeptService;

	public SystemDeptService getSysDeptService() {
		return sysDeptService;
	}

	public void setSysDeptService(SystemDeptService sysDeptService) {
		this.sysDeptService = sysDeptService;
	}
	
	/**
	 * 获取Tree
	 */
	public void getTree(){
		String result = this.sysDeptService.getDeptByJson();
		System.out.println(result);
		Write(result);
		
	}

	/**
	 * 初始加载...
	 */
	public void getParentTree(){
		String result = this.sysDeptService.getParentDeptByJson();
		Write(result);
	}
	
	/**
	 * 根据 初始加载 的id 查询下面的子节点：
	 */
	public void getChildNodeZTree(){
		String pid = this.getString("id");
		if(StringUtils.isEmpty(pid)){
			Write("error");
		}
		String result = this.sysDeptService.getChildDeptByJson(pid);
		Write(result);
	}
	/**
	 * 获取公司：
	 */
	public void getPCompany(){
		String result = this.sysDeptService.getPCompanyByJson();
		Write(result);
	}
	/**
	 * 新增：
	 */
	public void saveDept(){ 
		String pdeptId = this.getString("pdeptId");
		String deptName = this.getString("deptName");
		if(StringUtils.isEmpty(deptName)){
			Write("error_dn");
			return;
		}
		String result = "success";
		if(!StringUtils.isEmpty(pdeptId)){
			Map<String,String> dept = this.sysDeptService.findDeptByNo(Integer.valueOf(pdeptId));
			if(dept!=null){
				SystemDept newdept= new SystemDept();
							newdept.setDepartmentName(deptName);
							Object ojb = (dept.get("DEPARTMENT_NO"));
							newdept.setDepartmentParentNo(Integer.valueOf(ojb+""));
							Object obj_l= dept.get("DEPARTMENT_LEVEL");
							newdept.setDepartmentLevel(Integer.valueOf(obj_l+"")+1);
							newdept.setInsertTime(new Date());
							newdept.setCompanyCode(dept.get("COMPANY_CODE"));
						Integer deptno = randInt(111111,133300);
						Boolean isR =  true;
							while (isR) {
								if(this.sysDeptService.findDeptByNo((deptno))!=null){
									deptno=randInt(111111,133300);
								}else{
									isR=false;
									break;
								}
							} 
							newdept.setDepartmentNo(deptno);
							newdept.setDepartmentOrder("777777");
							newdept.setVisible(1);
							this.sysDeptService.saveDept(newdept);

			}else{
				result = "FAULL";
			}
		}else{ 
				result = "FAULL"; 
		}
		
		Write(result);
	}
	/**
	 * 修改
	 */
	public void updateDept(){
		String pdeptId = this.getString("pdeptId");
		String deptName = this.getString("deptName");
		if(StringUtils.isEmpty(deptName)){
			Write("error_dn");
			return;
		}
		String result = "success";
		Map<String,String> dept = this.sysDeptService.findDeptByNo(Integer.valueOf(pdeptId));
		if(dept!=null){
			SystemDept dept_up= new SystemDept();
			dept_up.setDepartmentName(deptName);
			Object obj_n = dept.get("DEPARTMENT_NO");
			dept_up.setDepartmentNo(Integer.valueOf(obj_n+""));
			this.sysDeptService.updateDept(dept_up);
		}else{
			result = "FAULL";
		}
		Write(result);
	}
	/**
	 * 删除
	 */
	public void deleteDept(){
		String pdeptId = this.getString("pdeptId");
		String result = "success";
		Map<String,String> dept = this.sysDeptService.findDeptByNo(Integer.valueOf(pdeptId));
		if(dept!=null){
			Object obj_n = dept.get("DEPARTMENT_NO");
			if(this.sysDeptService.deleteDept(Integer.valueOf(obj_n+""))==0){
				result = "ERROR_D";
			}
		}else{
			result = "FAULL";
		}
		Write(result);
	}
	
	
	public static int randInt(int min, int max) {

	    // NOTE: Usually this should be a field rather than a method
	    // variable so that it is not re-seeded every call.
	    Random rand = new Random();
 
	    // nextInt is normally exclusive of the top value,
	    // so add 1 to make it inclusive
	    int randomNum = rand.nextInt((max - min) + 1) + min;

	    return randomNum;
	} 
}
