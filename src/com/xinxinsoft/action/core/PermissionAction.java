package com.xinxinsoft.action.core;

import java.io.PrintWriter;
import java.util.HashMap;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;

import org.apache.struts2.ServletActionContext;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.LoginUser;

import com.xinxinsoft.service.core.user.LoginUserService;
import com.xinxinsoft.utils.SystemConfig;

@SuppressWarnings("serial")
public class PermissionAction extends BaseAction {
	
	public static Map<String, Object> loginUsersMap = new HashMap<String, Object>();

	private String validat;
	private LoginUserService loginUserService;
	
	public String getValidat() {
		return validat;
	}

	public void setValidat(String validat) {
		this.validat = validat;
	}
	
	public void checkValidatorCode() throws Exception{
		PrintWriter out = ServletActionContext.getResponse().getWriter();
		try {
			String validateCode = (String) ServletActionContext.getRequest().getSession().getAttribute("validateCode");
			if( validateCode.toUpperCase().trim().equals(validat.toUpperCase().trim()) )
				out.print("yes");
			else
				out.print("no");
		} catch (Exception e) {
			e.printStackTrace();
			out.print("no");
		}finally{
			out.close();
		}
	}
	
	/**
	 * 用户登录
	 * @throws Exception
	 */
	public String login() throws Exception{
		HttpServletRequest request = ServletActionContext.getRequest();
		String result = "loginSuccess";
		String rs = "";
		boolean duplicatLogin = false; //重复登录
		String username = null;
		try{
			username = request.getParameter(SystemConfig.instance().getLoginPageFormItems().getUsername());
			String password = request.getParameter(SystemConfig.instance().getLoginPageFormItems().getPassowrd());
			LoginUser lu = loginUserService.login(username, password);
			HttpSession currSession = request.getSession();
			if(lu == null){
				result = "loginError";
				rs = "loginError";
			}else{
				if(loginUsersMap == null){
					loginUsersMap = new HashMap<String, Object>();
				}
//				HttpSession oldSession = (HttpSession)loginUsersMap.get(username);
//				//限制同机器多浏览器和多机器只能登录一个用户
//				try {
//					if(oldSession != null && oldSession.getAttribute("userLoginName") != null){ //旧的session未过期
//						String oldSessionId = oldSession.getId();
//						String currSessionId = currSession.getId();
//						if(oldSessionId != null && oldSessionId.equals(currSessionId)){ //旧的session的id和当前session的id相同,重复登录
//							rs = "dupLogin";
//							duplicatLogin = true;
//						}else{//不是重复登录，将之前登录的session失效
//							oldSession.invalidate();
//						}
//					}
//				} catch (Exception e) {
//					e.printStackTrace();
//				}
				if(!duplicatLogin){
					loginUsersMap.put(username, currSession);
					request.getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), lu);
					request.getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), username);
					request.getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), username);
					request.getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), loginUserService.loadMenu_(lu.getName(),"新闻管理"));
				}else{
					result = "loginError";
				}
			}
		}catch(Exception e){
			e.printStackTrace();
			result = "loginError";
			rs = "loginError";
		}
		request.setAttribute(SystemConfig.instance().getSessionItems().getResult(), rs);
		request.setAttribute(SystemConfig.instance().getSessionItems().getUsername(), username);
		return result;
	}
	
	public String getIpAddr(HttpServletRequest request) {
		String ip = request.getHeader("x-forwarded-for");
		//判断是否为反向代理,多级反向代理的话，X-Forwarded-For的值并不止一个，而是一串IP值，究竟哪个才是真正的用户端的真实IP呢？        
		//是取X-Forwarded-For中第一个非unknown的有效IP字符串        
		if(ip != null && !"".equals(ip) && !"unknown".equalsIgnoreCase(ip)){            
			String[] tempArray = ip.split(",");            
			for(int i = 0 ;i <tempArray.length ; i++){                
				if(!"unknown".equalsIgnoreCase(tempArray[i])){                    
					ip = tempArray[i].replaceAll("\\s","");                    
					break;                
				}            
			}        
		}        
		
		if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {              
			ip = request.getHeader("Proxy-Client-IP");          
		}         
		
		if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {              
			ip = request.getHeader("WL-Proxy-Client-IP");          
		}         
		
		if(ip == null || ip.length() == 0 || "unknown".equalsIgnoreCase(ip)) {              
			ip = request.getRemoteAddr();                       
		}                 
		
		return ip;     
	}
	
	
	public void pageCheck() throws Exception{
		HttpServletRequest request = ServletActionContext.getRequest();
		HttpServletResponse response = ServletActionContext.getResponse();
		HttpSession session = request.getSession();
//		System.out.println("\r\n===================================================");
//		System.out.println("action 请求的："+request.getParameter("lastRequestTime"));
//		System.out.println("会话的："+session.getAttribute("lastRequestTime"));
//		System.out.println("===================================================\r\n");
		String reqLastRequestTime = request.getParameter("lastRequestTime");
		String sesLastRequestTime = (String)session.getAttribute("lastRequestTime");
//		response.setCharacterEncoding("utf-8");
//		response.setHeader("Cache-Control", "no-cache");
//		response.setContentType("text/html");
		if("evaluation-2012".equals(reqLastRequestTime) || (reqLastRequestTime != null && sesLastRequestTime != null && reqLastRequestTime.equals(sesLastRequestTime))){
//			response.getWriter().print("{}");
//			response.getWriter().flush();
//			response.getWriter().close();
		}else{
			System.out.println("error");
			response.getWriter().print("error");
			response.getWriter().flush();
			response.getWriter().close();
		}
	}

	public void setLoginUserService(LoginUserService loginUserService) {
		this.loginUserService = loginUserService;
	}
}
