package com.xinxinsoft.action.core;

import java.io.BufferedOutputStream;
import java.io.File;
import java.io.OutputStream;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.List;

import javax.servlet.http.HttpServletResponse;

import org.apache.struts2.ServletActionContext;

import com.opensymphony.xwork2.ModelDriven;
import com.xinxinsoft.entity.holiday.Holiday;
import com.xinxinsoft.service.holiday.HolidayService;
import com.xinxinsoft.utils.FtpUtil;
import com.xinxinsoft.utils.StringUtil;
import com.xinxinsoft.utils.common.CalendarUtil;
import com.xinxinsoft.utils.common.DefaultAction;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;

/**
 * 节假日action
 * <AUTHOR>
 *
 */
@SuppressWarnings("serial")
public class HolidayAction extends DefaultAction implements ModelDriven<Holiday>{
	private HolidayService holidayService;
	private Holiday holiday = new Holiday();
	private File uploadify;//上传file对象
	private String uploadifyFileName;//上传文件名
	private String uploadifyContentType;//上传文件类型

	public HolidayService getHolidayService() {
		return holidayService;
	}

	public void setHolidayService(HolidayService holidayService) {
		this.holidayService = holidayService;
	}
	
	public File getUploadify() {
		return uploadify;
	}

	public void setUploadify(File uploadify) {
		this.uploadify = uploadify;
	}

	public String getUploadifyFileName() {
		return uploadifyFileName;
	}

	public void setUploadifyFileName(String uploadifyFileName) {
		this.uploadifyFileName = uploadifyFileName;
	}

	public String getUploadifyContentType() {
		return uploadifyContentType;
	}

	public void setUploadifyContentType(String uploadifyContentType) {
		this.uploadifyContentType = uploadifyContentType;
	}

	@Override
	public Holiday getModel() {
		return holiday;
	}
	/**
	 * 根据id获取字典
	 */
	@SuppressWarnings("unused")
	public void getHolidayById(){
		String i = getString("id");
		Holiday entity = this.holidayService.getEntityById(getString("id"));
		SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
		String json = JSONHelper.toJson(entity, "yyyy-MM-dd");
		Write(json);
	}
	/**
	 * 分页
	 * @throws Exception
	 */
	public void getPageInfo() throws Exception{
		Write(this.holidayService.getPageInfo(getPageRequest()));
	}
	/**
	 * 删除
	 */
	public void deleteHoliday(){
		try{
			String id = getString("id");
			if(id!=null && !"".equals(id)){
				this.holidayService.deleteEntity(this.holidayService.getEntityById(id));
				writeText("T");
				return;
			}
		}catch(Exception e){
			e.printStackTrace();
			writeText("F");
		}
		writeText("F");
	}
	/**
	 * 修改节假日信息
	 */
	public void updateEntity(){
		try{
			if(StringUtil.isNotNull(holiday.getId())){
				Holiday entity = holidayService.getEntityById(holiday.getId());
				entity.setName(holiday.getName());
				holidayService.updateEntity(entity);
				writeText("T");
			}
		}catch(Exception e){
			e.printStackTrace();
			writeText("F");
		}
		writeText("F");
	}
	/**
	 * 添加节假日
	 */
	public void addHoliday(){
		boolean flag = true;
		try {
			String week = "";
			if(holiday.getDateV() != null){
				week = holidayService.getWeekFromDate(holiday.getDateV());
				SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
				String dateStr = sdf.format(holiday.getDateV());
				//根据日期查询导入日期是否存在
				flag =  holidayService.judgeDateIsExist(dateStr);
				if(flag){
					this.writeText("EXISTS");
					return;
				}
				//设置农历
				holiday.setWeek(week);
				holiday.setLunarCalendar(CalendarUtil.solarToLunar(dateStr));
				holidayService.addEntity(holiday);
				writeText("T");
			}else{
				writeText("F");
			}
		} catch (Exception e) {
			e.printStackTrace();
			writeText("F");
		}
	}
	/**
	 * 上传模板
	 */
	/*public void uploadTemplate(){
		if(uploadify != null && uploadifyFileName.equals("节假日数据模板.txt")){
			if(FileUpload.upload(FileUpload.getFtpURL(), uploadify, uploadifyFileName)){
				writeText("T");
			}else{
				writeText("F");
			}
		}
	}*/
	/**
	 * 下载模板
	 * @throws Exception 
	 */
	public void downloadTemplate(){
		HttpServletResponse response = ServletActionContext.getResponse();
		String path = FtpUtil.getFtpURL()+"节假日数据模板.xls";
		File file = new File(path);
		if(!file.exists() && !file.isDirectory()){
			writeText("F");
		}else{
			try{
				byte[] data = FileUtil.toByteArray(path);
				String fileName = URLEncoder.encode("节假日数据模板.xls", "UTF-8");
				response.reset();
				response.setHeader("Content-Disposition", "attachment;filename=\""+fileName+"\"");
				response.addHeader("Content-Length", ""+data.length);
				response.setContentType("application/octet-stream;charset=UTF-8");
				OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
				outputStream.write(data);
				outputStream.flush();
				outputStream.close();
				response.flushBuffer();
			}catch(Exception e){
				e.printStackTrace();
			}
		}
	}
	/**
	 * 批量导入数据
	 * @throws Exception 
	 */
	public void batchImport(){
		if(uploadify == null){
			this.writeText("EMPTY");
			return;
		}
		String suffix = uploadifyFileName.substring(uploadifyFileName.lastIndexOf(".")+1);
		if(!"xls".equals(suffix)){
			writeText("FORMAT_ERROR");
			return;
		}
		try{
			this.holidayService.batchDataImport(uploadify,getResponse());
		}catch(Exception e){
			this.writeText("ERROR");
		}
		this.writeText("SUCCESS");
	}

	/**
	 * 导出Excel数据
	 */
	@SuppressWarnings("rawtypes")
	public void exportExcelData(){
		File file = new File(FtpUtil.getFtpURL());
		if(!file.exists()){
			file.mkdirs();
		}
		String path = FtpUtil.getFtpURL()+"节假日数据表.xls";
		List holidayList = this.holidayService.getAll();
		this.holidayService.exportExcel(path, holidayList);
	}
	
	
	/**
	 * 批量导入数据
	 * @throws Exception 
	 */
	public void batchUsersImport(){
		if(uploadify == null){
			this.writeText("EMPTY");
			return;
		}
		String suffix = uploadifyFileName.substring(uploadifyFileName.lastIndexOf(".")+1);
		if(!"xls".equals(suffix) && !"xlsx".equals(suffix)){
			writeText("FORMAT_ERROR");
			return;
		}
		try{
			this.holidayService.batchUsersDaoImport(uploadify,suffix,getResponse());
		}catch(Exception e){
			this.writeText("ERROR");
		}
		this.writeText("SUCCESS");
	}
}
