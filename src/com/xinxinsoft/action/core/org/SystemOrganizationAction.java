package com.xinxinsoft.action.core.org;

import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;

import javax.servlet.RequestDispatcher;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONObject;

import org.apache.struts2.ServletActionContext;

import com.opensymphony.xwork2.ActionContext;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.LoginUser;
import com.xinxinsoft.entity.core.SystemOrganization;
import com.xinxinsoft.service.core.org.SystemOrganizationService;

/**
 * 组织机构Action
 * 
 * <AUTHOR>
 * 
 * 
 */
public class SystemOrganizationAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = -6976447323075254217L;
	private SystemOrganization systemOrganization;
	private SystemOrganizationService orgService;

	public SystemOrganizationService getOrgService() {
		return orgService;
	}

	public void setOrgService(SystemOrganizationService orgService) {
		this.orgService = orgService;
	}

	/**
	 * 保存新增的组织机构或者修改组织机构
	 * 
	 * @param org
	 *            组织机构
	 * @return
	 * @throws Exception
	 */
	public String saveOrUpdateSysOrg(SystemOrganization org) throws Exception {
		HttpServletRequest req = (HttpServletRequest) ActionContext
				.getContext().get(ServletActionContext.HTTP_REQUEST);
		HttpServletResponse rep = (HttpServletResponse) ActionContext
				.getContext().get(ServletActionContext.HTTP_RESPONSE);
		try {
			orgService.saveOrUpdateSysOrg(org);

			return SUCCESS;
		} catch (Exception e) {
			System.out.println("出错了...");
			RequestDispatcher rd = null;
			req.setAttribute("ActionException", e);
			rd = req.getRequestDispatcher("/common/error.jsp");
			rd.forward(req, rep);
			return INPUT;
		} finally {
		}
	}
	
	public void saveOrgTree()throws Exception{
		try{
			HttpServletRequest request = (HttpServletRequest) ActionContext
				.getContext().get(ServletActionContext.HTTP_REQUEST);
			String curOrgId = request.getParameter("curOrgId");
		    String txtId = request.getParameter("txtId");
			String txtName = request.getParameter("txtName");
			String txtPhone = request.getParameter("txtPhone");
			String txtFax = request.getParameter("txtFax");
			String areDec = request.getParameter("areDec");
			String subLeader = request.getParameter("subLeader");
			String directLeader = request.getParameter("directLeader");
			StringBuffer json = new StringBuffer();
			int parentId =0 ;
			if(null != curOrgId &&curOrgId.length()>0){
				parentId = new Integer(curOrgId);
			}
			if(parentId==0){
				json.append("{\"result\":\"操作失败！\"}");
				this.Write(json.toString());
			}else{
				LoginUser sl=new LoginUser();
				sl.setName(subLeader);
				LoginUser dl=new LoginUser();
				dl.setName(directLeader);
				if(null != txtId && txtId.length()>0){
					SystemOrganization curOrg = new SystemOrganization();
					curOrg.setId(new Integer(txtId));
					curOrg.setName(txtName);
					curOrg.setFax(txtFax);
					curOrg.setTelephone(txtPhone);
					curOrg.setDescription(areDec);
					curOrg.setDirectLeader(dl);
					curOrg.setUpperLeader(sl);
					curOrg = orgService.saveOrUpdateSysOrg(curOrg);
					json.append("{\"id\":\"").append(curOrg.getId()).append("\", \"layer\":\"").append(curOrg.getLayer()).append("\", \"result\":\"success\"}");
//					System.out.println("保存： " + json);
					this.Write(json.toString());
				}else{
					SystemOrganization org = new SystemOrganization();
					org.setFax(txtFax);
					org.setName(txtName);
					org.setTelephone(txtPhone);
					org.setDescription(areDec);
					org.setDirectLeader(dl);
					org.setUpperLeader(sl);
					org = orgService.saveSysOrg(org, new Integer(curOrgId));
					json.append("{\"id\":\"").append(org.getId()).append("\", \"layer\":\"").append(org.getLayer()).append("\", \"result\":\"success\"}");
//					System.out.println("新建： " + json);
					this.Write(json.toString());
				}
			}
		}catch(Exception e){
			e.printStackTrace();
		}
		
		
	}

	/**
	 * 得到组织树
	 * 
	 * @return
	 * @throws Exception
	 */
	public void getOrgTree() throws Exception {
		HttpServletRequest req = (HttpServletRequest) ActionContext
				.getContext().get(ServletActionContext.HTTP_REQUEST);
		HttpServletResponse rep = (HttpServletResponse) ActionContext
				.getContext().get(ServletActionContext.HTTP_RESPONSE);
		rep.setHeader("Cache-Control", "no-cache");
		rep.setContentType("text/json;charset=UTF-8");
		try {
			req.setCharacterEncoding("utf-8");
		} catch (UnsupportedEncodingException e1) {
			e1.printStackTrace();
		}

		PrintWriter out = rep.getWriter();

		try {
			orgService=getOrgService();
			String json = orgService.getOrgByJson();
			System.out.println(json);
			this.Write(json);
		} catch (Exception e) {
//			RequestDispatcher rd = null;
//			req.setAttribute("ActionException", e);
//			rd = req.getRequestDispatcher("/common/error.jsp");
//			rd.forward(req, rep);
			this.Write("{id:0, item:[{id:0, text:\"数据有误\",open:\"1\"}]}");
		} finally {
			out.flush();
			out.close();
		}
	}
	
	public void getDelOrg() throws Exception{
		HttpServletRequest request = (HttpServletRequest) ActionContext.getContext().get(ServletActionContext.HTTP_REQUEST);
		String curOrgId = request.getParameter("curOrgId");
		try{
			if(null != curOrgId && curOrgId.length()>0){
				SystemOrganization curOrg = orgService.getOrgById(new Integer(curOrgId));
				curOrg.setParent(null);
				orgService.deleteOrgById(curOrg.getId());
				this.Write("删除成功！");
			}
		}catch(Exception e){
			e.fillInStackTrace();
			e.printStackTrace();
			this.Write("删除失败！");
		}
	}
	
	public void getOrgById() throws Exception{
		HttpServletRequest request = (HttpServletRequest) ActionContext
				.getContext().get(ServletActionContext.HTTP_REQUEST);
		String curOrgId = request.getParameter("curOrgId");
		if(curOrgId != null && curOrgId.length()>0){
			SystemOrganization org = orgService.getOrgById(new Integer(curOrgId));
			int id = org.getId();
			String name = "";
			if(org.getName() != null){ name = org.getName();}; 
			String fax = ""; 
			if(org.getFax() != null){ fax = org.getFax();};
			String telephone = "";
			if(org.getTelephone() != null){ telephone = org.getTelephone();};
			String dec = "";
			if(org.getDescription() != null){ dec = org.getDescription();};
			String directLeader="";
			String subLeader="";
			if(org.getDirectLeader()!=null){
				directLeader=org.getDirectLeader().getName();
			}
			if(org.getUpperLeader()!=null){
				subLeader=org.getUpperLeader().getName();
			}
			JSONObject json = new JSONObject();
			json.put("id", id);
			json.put("name", name);
			json.put("fax", fax);
			json.put("telephone", telephone);
			json.put("desc", dec);
			
			json.put("directLeader", directLeader);
			json.put("subLeader",subLeader);
			
		    this.Write(json.toString());
		}
	}

	public SystemOrganization getSystemOrganization() {
		return systemOrganization;
	}

	public void setSystemOrganization(SystemOrganization systemOrganization) {
		this.systemOrganization = systemOrganization;
	}

}
