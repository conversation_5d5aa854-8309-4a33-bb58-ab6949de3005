package com.xinxinsoft.action.core.user;

import java.io.File;
import java.io.IOException;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.struts2.ServletActionContext;
import com.opensymphony.xwork2.ActionContext;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.LoginUser;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.service.core.user.LoginUserService;
import com.xinxinsoft.utils.PageConfig;
public class LoginUserAction extends BaseAction {

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private LoginUser user;
	private LoginUserService loginUserService;
	private String newPassowrd;
	private String fileName;
	private File file;
	/**
	 * @return the newPassowrd
	 */
	public String getNewPassowrd() {
	    return newPassowrd;
	}

	/**
	 * @param newPassowrd the newPassowrd to set
	 */
	public void setNewPassowrd(String newPassowrd) {
	    this.newPassowrd = newPassowrd;
	}

	public LoginUserService getLoginUserService() {
		return loginUserService;
	}

	public void setLoginUserService(LoginUserService loginUserService) {
		this.loginUserService = loginUserService;
	}

	public LoginUser getUser() {
		return user;
	}

	public void setUser(LoginUser user) {
		this.user = user;
	}

	public void userLogin() throws Exception {}
	
	/**
	 * 判断用户名是否可用
	 * @throws Exception
	 */
	public void checkUserName() throws Exception {
		HttpServletRequest req=(HttpServletRequest)ActionContext.getContext().get(ServletActionContext.HTTP_REQUEST);
		String userName=(String)req.getParameter("txtUserName");
		if(loginUserService.checkUserName(userName.toLowerCase())==true){
			this.Write("{'isAvailable':'true'}");
		}else{
			this.Write("{'isAvailable':'false'}");
		}
	}
	
	/**
	 * 查询所有用户
	 * @throws IOException
	 */
	@SuppressWarnings("unchecked")
	public void searchAllUsers()throws IOException{
			ActionContext ct = ActionContext.getContext();
			HttpServletRequest request=(HttpServletRequest)ct.get(ServletActionContext.HTTP_REQUEST);
			String name = "";
			String role = "全部";
			String loginName="";
			String state="";
			String gs="";
			String time="";
			if(request.getParameter("name")!=null){
				 name = new String(request.getParameter("name"));
			}
			if(request.getParameter("role")!=null){
				role = new String(request.getParameter("role"));
			}
			if(null!=request.getParameter("loginName")){
				loginName=request.getParameter("loginName");
			}
			if(null!=request.getParameter("state")){
				state=request.getParameter("state").toString();
			}
			if(null!=request.getParameter("gs")){
				gs=request.getParameter("gs").toString();
			}
			if(null!=request.getParameter("time")){
				time=request.getParameter("time").toString();
			}
			//jqGrid自动传递的参数 排序字段
			String order=request.getParameter("sidx");
			//jqGrid自动传递的参数 排序方式
			String orderType = request.getParameter("sord");
			//创造json所需的Map
			Map pageInfo = new HashMap();
			//新建分页信息 并设置页数和行数
			PageConfig config=new PageConfig();
			config.setPageIndex(Integer.valueOf(request.getParameter("page")));
			config.setPageSize(Integer.valueOf(request.getParameter("rows")));
			
			config = loginUserService.getUser(config,loginName,name,role,state,gs,time,order,orderType);
			pageInfo.put("page", config.getPageIndex());
			pageInfo.put("total", config.getPageSum());
			pageInfo.put("records", config.getRowsSum());
			String jsonStr = loginUserService.creReleaseInfoJSON1(config.getResult(), pageInfo);
			ServletResponse response = ServletActionContext.getResponse();
			response.setCharacterEncoding("UTF-8");
			try {
				this.Write(jsonStr);
			} catch (Exception e) {
				e.printStackTrace();
			}	
		}
	
	/**
	 * 通过ID获取到用户信息
	 * @throws Exception
	 */
	public void getUserById() throws Exception{
		HttpServletRequest  request=ServletActionContext.getRequest();
		String userId = request.getParameter("userId");
		try{
			if(null != userId && userId.length()>0){
				Set<Role> sets = loginUserService.getRoleByUserId(Integer.parseInt(userId));
				StringBuffer sb = new StringBuffer();
				for(Role role:sets){
					sb.append(role.getId());
					sb.append("*");
				}
				JSONObject json = new JSONObject();
				json.put("userRoles", sb.toString());
				this.Write(json.toString());
			}
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	/**
	 * 加载用户菜单信息
	 * @throws Exception
	 */
	@SuppressWarnings("unchecked")
	public void getRoles() throws Exception{
		@SuppressWarnings("unused")
		HttpServletRequest  request=ServletActionContext.getRequest();
		try{
				List<Role> list = (List<Role>) loginUserService.getRoleByUserId(1);
				JSONArray jsonArray =  new JSONArray();
		        for(int i=0;i<list.size();i++){   
		        	 JSONObject json = new JSONObject();   
		        	 json.put("id", ((Role) list.get(i)).getId());   
		        	 json.put("roleName", list.get(i).getName());
		             jsonArray.add(json);   
		        }   
				this.Write(jsonArray.toString());
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	/**
	 * 根据Id来删除用户
	 */
	public void deleteUserById(){
		HttpServletRequest  request=ServletActionContext.getRequest();
		try{
			int id = Integer.valueOf(request.getParameter("id"));
			loginUserService.deleteUserById(id);
			this.Write("OK");
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	/**
	 * 修改状态
	 */
	public void updateState(){
		HttpServletRequest  request=ServletActionContext.getRequest();
		try{
			int id = Integer.valueOf(request.getParameter("id"));
			String res=loginUserService.updateState(id);
			if(res.equals("1")){
				this.Write("失效");
			}
			else if(res.equals("2")){
				this.Write("启用");
			}
			
		}catch(Exception e){
			e.printStackTrace();
		}
	}
	
	/**
	 * 更新用户信息
	 * @return
	 */
	public String updateUser(){
		HttpServletRequest  request=ServletActionContext.getRequest();
		try {
			String orgId = "0";
			if(request.getParameter("org")!=null ){
				orgId = new String(request.getParameter("org"));
				getUser().setSystemOrg(loginUserService.getOrgById(orgId));
			}
			loginUserService.updateUser(getUser(), request.getParameter("userRole"));
			request.setAttribute("value", "编辑用户成功！");
			return "editUserSucessfully";
		} catch (Exception e) {
			e.printStackTrace();
			request.setAttribute("value", "编辑用户失败！");
			return "editUserFailed";
		}
	}
	/**
	 * 添加用户
	 * @return
	 */
	public String addUser(){
		HttpServletRequest  request=ServletActionContext.getRequest();
		try {
			/*System.out.println("-------"+fileName);
			String path=this.getProjectDir()+fileName;*/
			String orgId = "0";
			user.setCreateTime(new Date());
			if(request.getParameter("org")!=null ){
				orgId = new String(request.getParameter("org"));
				getUser().setSystemOrg(loginUserService.getOrgById(orgId));
			}
			loginUserService.addUser(getUser(), request.getParameter("userRole"));
			request.setAttribute("value", "新建用户成功！");
			return "addUserSucessfully";
		} catch (Exception e) {
			e.printStackTrace();
			request.setAttribute("value", "新建用户失败！");
			return "addUserFailed";
		}
	}
	//更新用户密码
	public void updateUserProfile() throws IOException{
	    HttpServletRequest req=ServletActionContext.getRequest();
	    String username = (String)req.getSession().getAttribute("userLoginName");
	    String password=req.getParameter("passowrd");
	    String newPassword=req.getParameter("newPassword");
	    if(loginUserService.updateUserProfile(username, password, newPassword)){
	    	this.Write("OK");
	    }else{
	    	this.Write("FAIL");
	    }
	}
	/**
	 * 获取所有用户：
	 */
	public void getAllUser(){
		JSONArray jsona = new JSONArray();
		List<LoginUser> users=loginUserService.getAllUser();
		for (LoginUser user : users) {
			JSONObject j = new JSONObject();
   			j.put("id", user.getSystemOrg().getId()+""+user.getId());
   			j.put("pId", user.getSystemOrg().getId());
   			j.put("name",user.getRealName());
   			j.put("uid",user.getId());
   			jsona.add(j);
		}
		this.Write(jsona.toString());
	
	}
	
	
	public String getFileName() {
		return fileName;
	}

	public void setFileName(String fileName) {
		this.fileName = fileName;
	}

	public File getFile() {
		return file;
	}

	public void setFile(File file) {
		this.file = file;
	}

}