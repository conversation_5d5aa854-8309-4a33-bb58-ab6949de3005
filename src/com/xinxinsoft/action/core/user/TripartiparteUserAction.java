package com.xinxinsoft.action.core.user;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemDept;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.core.tripartiparteUser.TripartiparteOrder;
import com.xinxinsoft.entity.core.tripartiparteUser.TripartiparteUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.core.user.TripartipartUserService;
import com.xinxinsoft.service.holiday.HolidayService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * @Description: TODO
 * @Author: TX
 * @Date: 2022/12/6 14:18
 * @Version: 1.0
 */
public class TripartiparteUserAction extends BaseAction {
    private static final Logger logger = LoggerFactory.getLogger(TripartiparteUserAction.class);

    @Resource(name="SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "holidayService")
    private HolidayService holidayService;
    @Resource(name = "TripartipartUserService")
    private TripartipartUserService tripartipartUserService;

    @Resource(name="Bpms_riskoff_service")
    private Bpms_riskoff_service taskService;
    @Resource(name="WaitTaskService")
    private WaitTaskService service;

    private File file;
    public File getFile() {
        return file;
    }
    public void setFile(File file) {
        this.file = file;
    }

    /**
     * @Description TODO 三方人员信息导入申请
     * <AUTHOR>
     * @Date 2022/12/7 16:55
     **/
    public void addTripartiparteUser(){
        try{
            String title = getString("title");
            String mome = getString("mome");
            String userJsonList = getString("userJsonList");

            String userID = getString("userID");

            String attachmentId = getString("attachmentId");
            SystemUser systemUser = systemUserService.getUserInfoRowNo(Integer.parseInt(userID));
            List<Map<String, String>> userList = tripartipartUserService.getVwUserinf(String.valueOf(user.getRowNo()));
            if (systemUser==null){
                Write(returnPars(-1, "", "亲爱的同事,您选择的审批人信息异常,请重新选择或联系管理员处理!"));
                return;
            }
            if (userList.size()<1){
                Write(returnPars(-1, "", "亲爱的同事,用户登陆信息异常,请重新登陆或联系管理员处理!"));
                return;
            }

            String IBM = "";
            List<Object[]> sone = tripartipartUserService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                IBM = (String) sone.get(i)[2];
            }
            String sateTime = taskService.getNumber();
            TripartiparteOrder tripartiparteOrder = new TripartiparteOrder();
            tripartiparteOrder.setOrderNo(IBM+sateTime);
            tripartiparteOrder.setOrderTital(title);
            tripartiparteOrder.setOrderMome(mome);
            tripartiparteOrder.setOrderState(0);
            tripartiparteOrder.setCreationDate(new Date());
            tripartiparteOrder.setCreationRow(String.valueOf(userList.get(0).get("ROWNO")));
            tripartiparteOrder.setCreationName(String.valueOf(userList.get(0).get("EMPLOYEE_NAME")));
            tripartiparteOrder.setCreationCompanyCode(String.valueOf(userList.get(0).get("COMPANY_CODE")));
            tripartiparteOrder.setCreationCompanyName(String.valueOf(userList.get(0).get("COMPANY_NAME")));
            tripartiparteOrder = tripartipartUserService.savaTripartiparteOrder(tripartiparteOrder);
            if (tripartiparteOrder!=null){
                JSONArray jsonArray = JSONArray.fromObject(userJsonList);
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject userData = jsonArray.getJSONObject(i);
                    TripartiparteUser tripartiparteUser = new TripartiparteUser();
                    tripartiparteUser.setSuperiorOrder(tripartiparteOrder.getOrderNo());
                    tripartiparteUser.setUserName(String.valueOf(userData.get("userName")));
                    tripartiparteUser.setUserPhone(String.valueOf(userData.get("userMobile")));
                    tripartiparteUser.setUser4aName(String.valueOf(userData.get("user4aName")));
                    tripartiparteUser.setUserBossname(String.valueOf(userData.get("userBossname")));
                    tripartiparteUser.setUserEipdePartment(String.valueOf(userData.get("userEipdePartment")));
                    tripartiparteUser.setUserEipdePartmentMome(String.valueOf(userData.get("userEipdePartmentMome")));
                    tripartiparteUser.setInertState(0);
                    if (tripartipartUserService.savaTripartiparteUser(tripartiparteUser)==null){
                        throw new Exception("三方人员信息保存失败");
                    }
                }
            }else {
                throw new Exception("审批工单创建失败");
            }

            if (!StringUtils.isEmpty(attachmentId)) {
                if (attachmentId != null) {
                    // 判断是否上传了附件，获取前台提交的附件Id；
                    String[] json = attachmentId.split(",");
                    if (json.length > 0) {
                        for (int i = 0; i < json.length; i++) {
                            SingleAndAttachment sa = new SingleAndAttachment();
                            sa.setOrderID(tripartiparteOrder.getId());
                            sa.setAttachmentId(json[i]);
                            sa.setLink(TripartiparteOrder.TRIPARTIPARTEORDER);
                            tripartipartUserService.saveSandA(sa);
                        }
                    }
                }
            }

            String processId = "Tripartiparte_"+sateTime;
            taskService.setBpms_riskoff_process(tripartiparteOrder.getId(), processId, 1, user);
            taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "起草人", user.getRowNo(), user);//先保存自己本身的任务
            taskService.setBpms_riskoff_task(processId, null, 1, "SH", "三方人员信息管理人员", systemUser.getRowNo(), user);//预存下一步任务
            //setTripartiparteOrderWaitTask("[三方人员导入]",tripartiparteOrder,systemUser);
            if (!setTripartiparteOrderWaitTask("[三方人员导入]",tripartiparteOrder,systemUser).equals(1)){
                throw new Exception("EIP待办创建失败");
            };
            Write(returnPars(1, "", "您的工单已推送至：" + systemUser.getEmployeeName() + ",请等待处理！"));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.info("三方人员导入工单创建异常："+e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，三方人员导入工单创建异常:【"+e.getMessage()+"】，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 工单审批通过方法
     * <AUTHOR>
     * @Date 2022/7/13 15:56
     **/
    public void complateTripartiparteOrder(){
        try {
            String id = getString("id");

            //手机端获取user
            String phone = getString("phone");
            SystemUser users = new SystemUser();
            if (phone == null) {
                users = this.user;
            } else {
                users = this.systemUserService.getUserByPhone(phone);
            }

            TripartiparteOrder tripartiparteOrder = tripartipartUserService.queryTripartiparteOrder(id);
            if (tripartiparteOrder==null){
                throw new Exception("查询工单信息失败："+id);
            }
            List<TripartiparteUser> tripartiparteUserList = tripartipartUserService.queryTripartiparteUserListByOrderNo(tripartiparteOrder.getOrderNo());
            Bpms_riskoff_task riskoff_task=taskService.getBpms_riskoff_taskByStatus(id,users.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = taskService.queryWaitByTaskId(id,users.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)){
                throw new RuntimeException("任务信息异常");
            }else if (!riskoff_task.getOper_no().equals(users.getRowNo())){
                throw new RuntimeException("任务信息异常");
            }else if (wt == null) {
                throw new RuntimeException("待办信息异常");
            }

            CopyOnWriteArrayList<HashMap<String,String>> mapdepts = holidayService.findSystemDepts();	//所有部门信息
            CopyOnWriteArrayList<Integer>  depetno =  holidayService.findSystemDeptsNo();				//查询所有部门编号
            CopyOnWriteArrayList<Integer> userno =  holidayService.findSystemUserNo();

            Boolean isR = false ,isDept = false;
            for (int i = 0;i<tripartiparteUserList.size();i++){
                TripartiparteUser tripartiparteUser = tripartiparteUserList.get(i);
                SystemDept dept = new SystemDept();//部门信息
                SystemUser user = new SystemUser();

                if (tripartiparteUser.getInertState().equals(0)){
                    //解析部门路径
                    if (!"".equals(tripartiparteUser.getUserEipdePartmentMome())){
                        isDept = holidayService.setParas(tripartiparteUser.getUserEipdePartmentMome(), user,
                                mapdepts, i, i, depetno, userno, dept);
                    }else {
                        tripartiparteUser.setInertState(-1);
                        tripartiparteUser.setInertMsg("用户部门路径信息异常");
                        tripartipartUserService.savaTripartiparteUser(tripartiparteUser);
                        continue;
                    }
                    user.setEmployeeName(tripartiparteUser.getUserName());
                    user.setMobile(tripartiparteUser.getUserPhone());
                    user.setLoginName(tripartiparteUser.getUser4aName());
                    user.setBossUserName(tripartiparteUser.getUserBossname());
                    user.setLoginPwd("21218cca77804d2ba1922c33e0151105");
                    user.setEmployDate(new Date());

                    //验证数据表中是否已经存在用户信息
                    isR=holidayService.isDbDates(user.getLoginName(), user.getMobile());
                    if(!isR){
                        //报存Users_4A表
                        holidayService.saveUsers4A(user.getLoginName(),user.getEmployeeName(),user.getMobile());
                        //保存用户user数据
                        holidayService.saveUser(user);
                        if(isDept){
                            //保存部门数据
                            holidayService.saveDept(dept);
                            //保存在表示是订单系统新建部门
                            holidayService.saveDeptTest(dept.getDepartmentName(),dept.getDepartmentNo(),dept.getDepartmentParentNo(),dept.getCompanyCode());
                            HashMap<String,String>  mapd = new HashMap<String, String>();
                            mapd.put("COMPANY_CODE", dept.getCompanyCode());
                            mapd.put("DEPARTMENT_NO", String.valueOf(dept.getDepartmentNo()));
                            mapd.put("DEPARTMENT_LEVEL", String.valueOf(dept.getDepartmentLevel()));
                            mapd.put("DEPARTMENT_ORDER", dept.getDepartmentOrder());
                            mapd.put("DEPARTMENT_PARENT_NO", String.valueOf(dept.getDepartmentParentNo()));
                            mapd.put("DEPARTMENT_NAME", dept.getDepartmentName());
                            mapdepts.add(mapd);
                        }
                        //添加用户和部门关系表
                        holidayService.saveDeptOrUser(dept.getDepartmentNo(),user.getRowNo());
                        // 添加用户为普通用户权限
                        holidayService.saveUserOrRole(user.getRowNo());
                        //存储在指定的 systemtestusers 表中，保证晚上更新eip账号不会同该表中的用户信息
                        holidayService.saveTestUser(user.getRowNo(),user.getLoginName());
                        tripartiparteUser.setInertState(1);
                        tripartiparteUser.setInertMsg("用户数据导入成功");
                    }else {
                        tripartiparteUser.setInertState(-1);
                        tripartiparteUser.setInertMsg("用户数据已存在");
                    }
                    tripartipartUserService.savaTripartiparteUser(tripartiparteUser);
                }
            }
            tripartiparteOrder.setOrderState(1);
            tripartipartUserService.savaTripartiparteOrder(tripartiparteOrder);
            taskService.updateBpms_riskoff_task("处理完成", 2, riskoff_task.getId());
            service.updateWait(wt, this.getRequest());
            Write(returnPars(1,"","亲爱的同事，工单已处理完成！"));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.error("三方人员信息审批完成方法异常：" + e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，三方人员信息完成方法异常["+e.getMessage()+"],请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 工单完成方法
     * <AUTHOR>
     * @Date 2022/7/13 15:56
     **/
    public void confirmTripartiparteOrder(){
        try {
            String id = getString("id");
            TripartiparteOrder tripartiparteOrder = tripartipartUserService.queryTripartiparteOrder(id);
            if (tripartiparteOrder==null){
                throw new Exception("查询工单信息失败："+id);
            }
            Bpms_riskoff_task riskoff_task=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)){
                throw new RuntimeException("任务信息异常");
            }else if (!riskoff_task.getOper_no().equals(user.getRowNo())){
                throw new RuntimeException("任务信息异常");
            }else if (wt == null) {
                throw new RuntimeException("待办信息异常");
            }

            taskService.updateBpms_riskoff_task("工单已完成工单", 2, riskoff_task.getId());
            service.updateWait(wt, this.getRequest());
            Write(returnPars(1,"","亲爱的同事，工单已完成！"));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.info("三方人员导入完成异常：" + e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，三方人员导入完成异常["+e.getMessage()+"],请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 工单退回方法
     * <AUTHOR>
     * @Date 2022/8/1 15:23
     **/
    public void returnTripartiparteOrder(){
        try{
            String id = getString("id");
            String opinion = getString("opinion");//审批意见

            //手机端获取user
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = this.systemUserService.getUserByPhone(phone);
            }

            TripartiparteOrder tripartiparteOrder = tripartipartUserService.queryTripartiparteOrder(id);
            if (tripartiparteOrder==null){
                throw new Exception("查询工单信息失败："+id);
            }
            SystemUser systemUser = systemUserService.getUserInfoRowNo(Integer.parseInt(tripartiparteOrder.getCreationRow()));
            Bpms_riskoff_process process= taskService.getbpms_riskoff_processBizid(id);//根据ID查询流程
            Bpms_riskoff_task riskoff_task=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)){
                throw new RuntimeException("任务信息异常");
            }else if (!riskoff_task.getOper_no().equals(user.getRowNo())){
                throw new RuntimeException("任务信息异常");
            }else if (wt == null) {
                throw new RuntimeException("待办信息异常");
            }
            tripartiparteOrder.setOrderState(2);
            tripartipartUserService.savaTripartiparteOrder(tripartiparteOrder);
            taskService.updateBpms_riskoff_task(opinion, 0, riskoff_task.getId());
            service.updateWait(wt, this.getRequest());
            taskService.setBpms_riskoff_task(process.getProcess_sign(), "", 1, "SH", "起草人", systemUser.getRowNo(), user);
            if (!setTripartiparteOrderWaitTask("[三方人员导入]",tripartiparteOrder,systemUser).equals(1)){
                throw new Exception("EIP待办创建失败");
            };
            //setTripartiparteOrderWaitTask("[三方人员导入]",tripartiparteOrder,systemUser);
            Write(returnPars(1, "", "亲爱的同事,处理成功工单已退回至起草人处!"));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.error("三方人员导入工单退回异常："+e.getMessage(),e);
            Write(returnPars(-1, "", "亲爱的同事，三方人员导入审批工单退回异常：【"+e.getMessage()+"】，请联系管理员处理"));
        }
    }

    /**
     * @Description TODO 工单作废方法
     * <AUTHOR>
     * @Date 2022/8/1 15:23
     **/
    public void GiveTripartiparteOrder(){
        try{
            String id = getString("id");

            //手机端获取user
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = this.systemUserService.getUserByPhone(phone);
            }

            TripartiparteOrder tripartiparteOrder = tripartipartUserService.queryTripartiparteOrder(id);
            if (tripartiparteOrder==null){
                throw new Exception("查询工单信息失败："+id);
            }
            List<TripartiparteUser> tripartiparteUserList = tripartipartUserService.queryTripartiparteUserListByOrderNo(tripartiparteOrder.getOrderNo());
            Bpms_riskoff_task riskoff_task=taskService.getBpms_riskoff_taskByStatus(id,user.getRowNo());//根据业务ID查询当前任务
            WaitTask wt = taskService.queryWaitByTaskId(id,user.getRowNo());//根据待办id查询待办信息
            if (!riskoff_task.getStatus().equals(1)){
                throw new RuntimeException("任务信息异常");
            }else if (!riskoff_task.getOper_no().equals(user.getRowNo())){
                throw new RuntimeException("任务信息异常");
            }else if (wt == null) {
                throw new RuntimeException("待办信息异常");
            }

            for (int i = 0;i<tripartiparteUserList.size();i++){
                TripartiparteUser users = tripartiparteUserList.get(i);
                users.setInertState(-1);
                tripartipartUserService.savaTripartiparteUser(users);
            }
            tripartiparteOrder.setOrderState(-1);
            tripartipartUserService.savaTripartiparteOrder(tripartiparteOrder);
            taskService.updateBpms_riskoff_task("工单已作废！", 0, riskoff_task.getId());
            service.updateWait(wt, this.getRequest());
            Write(returnPars(1, "", "亲爱的同事,处理成功工单已作废!"));
        }catch (Exception e){
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            e.printStackTrace();
            logger.error("三方人员导入工单作废异常："+e.getMessage(),e);
            Write(returnPars(-1, "", "亲爱的同事，三方人员导入工单作废异常：【"+e.getMessage()+"】，请联系管理员处理"));
        }
    }

    /**
     * @Description TODO 生成待办
     * <AUTHOR>
     * @param WaitTatle             待办类型
     * @param tripartiparteOrder    工单对象
     * @param systemUser            下一步处理人对象
     * @return java.lang.Integer
     * @Date 2022/12/7 16:37
     **/
    public Integer setTripartiparteOrderWaitTask(String WaitTatle, TripartiparteOrder tripartiparteOrder, SystemUser systemUser){

        //手机端获取user
        String phone = getString("phone");
        SystemUser user = new SystemUser();
        if (phone == null) {
            user = this.user;
        } else {
            user = this.systemUserService.getUserByPhone(phone);
        }

        WaitTask waitTask = new WaitTask();
        waitTask.setName(WaitTatle + tripartiparteOrder.getOrderTital());//待办名称
        waitTask.setCreationTime(new Date());// 代办生成时间
        waitTask.setUrl("jsp/user/tripartiteUser/handTripartiteUser.jsp");
        waitTask.setState(WaitTask.HANDLE);// 状态为待处理
        waitTask.setHandleUserId(systemUser.getRowNo());// 处理人id
        waitTask.setHandleUserName(systemUser.getEmployeeName());// 处理人名称
        waitTask.setHandleLoginName(systemUser.getLoginName());// 处理人登录名
        waitTask.setCreateUserId(user.getRowNo());// 创建人id
        waitTask.setCreateUserName(user.getEmployeeName());// 创建人名称
        waitTask.setCreateLoginName(user.getLoginName());// 创建人登录名
        waitTask.setCode("SFRYDR");//标识
        waitTask.setTaskId(tripartiparteOrder.getId());
        waitTask.setOrderNo(tripartiparteOrder.getOrderNo());
        return service.saveWaitPushMOA(waitTask, this.getRequest());
    }

    /**
     * @Description TODO 查询列表信息
     * <AUTHOR>
     * @Date 2022/4/24 16:58
     **/
    public void findAllByPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);


            //手机端获取user
            String phone = getString("phone");
            SystemUser user = new SystemUser();
            if (phone == null) {
                user = this.user;
            } else {
                user = this.systemUserService.getUserByPhone(phone);
            }

            String orderNo = getString("orderNo");        //工单编号
            String orderTitle = getString("orderTitle");        //工单名称
            String stateCreatorDate = getString("stateCreatorDate");    //工单创建时间   开始时间
            String endCreatorDate = getString("endCreatorDate");        //工单创建时间   结束时间

            String tableType = getString("tableType");        //列表状态
            page = tripartipartUserService.findAllByPage(page,orderNo,orderTitle,stateCreatorDate,endCreatorDate,tableType,user);
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            this.Write(json);
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("列表加载失败！");
        }

    }

    /**
     * @Description TODO 查询工单信息
     * <AUTHOR>
     * @Date 2022/12/7 17:43
     **/
    public void findTripartiparteOrder(){
        try{
            String id = getString("id");
            String type = getString("type");
            String orderNo = getString("orderNo");
            TripartiparteOrder tripartiparteOrder = new TripartiparteOrder();
            String cw="";
            if(id!=null&&!"".equals(id)){
                tripartiparteOrder = tripartipartUserService.queryTripartiparteOrder(id);
                cw=id;
            }else{
                tripartiparteOrder = tripartipartUserService.queryTripartiparteOrderNo(orderNo);
                cw=orderNo;
            }
            if (tripartiparteOrder==null){
                throw new Exception("查询工单信息失败："+cw);
            }
            List<Map<String, String>> userList = tripartipartUserService.getVwUserinf(String.valueOf(tripartiparteOrder.getCreationRow()));
            List<TripartiparteUser> tripartiparteUserList = tripartipartUserService.queryTripartiparteUserListByOrderNo(tripartiparteOrder.getOrderNo());
            List<String> buttenList = new ArrayList<String>();
            if (type.equals("SHOW")){
                buttenList.add("CLOSE");        //关闭
            }else {
                switch (tripartiparteOrder.getOrderState()){
                    case 0:
                        buttenList.add("AGREE");        //同意
                        buttenList.add("RETURN");       //退回
                        break;
                    case 1:
                        buttenList.add("CONFIRM");      //完成
                        break;
                    case 2:
                        buttenList.add("GIVE");      //作废
                        break;
                    default:
                        buttenList.add("CLOSE");        //关闭
                        break;
                }
            }
            Map<String,Object> retMap = new HashMap<String,Object>();
            retMap.put("TripartiparteOrder",tripartiparteOrder);
            retMap.put("TripartiparteUserList",tripartiparteUserList);
            retMap.put("USER",userList.get(0));
            retMap.put("buttenList",buttenList);
            Write(returnPars(1,retMap,"查询成功！"));
        }catch (Exception e){
            e.printStackTrace();
            logger.info("三方人员工单查询："+e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，三方人员工单查询异常:【"+e.getMessage()+"】，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 根据ID查询跟踪处理
     * <AUTHOR>
     * @Date 2022/7/14 10:20
     **/
    public void processtracking() {
        Result r = new Result();
        String id = getString("id");
        try {
            List<Bpms_riskoff_task> tasks = taskService.processtracking(id);
            r.setCode(ResultCode.SUCCESS);
            r.setMessage("成功");
            r.setData(tasks);
            Write(r.toString());
        } catch (Exception e) {
            r.setCode(ResultCode.FAIL);
            r.setMessage("失败");
            r.setData(e.getMessage());
            Write(r.toString());
        }

    }

    /**
     * @Description TODO 导入用户信息验证
     * <AUTHOR>
     * @Date 2022/12/7 14:54
     **/
    public void userInformationCheck(){
        try{
            String userMobile = getString("userMobile");
            String user4aName = getString("user4aName");
            String userBossname = getString("userBossname");

            if (holidayService.isDbDates(user4aName,userMobile) || tripartipartUserService.isUserBossName(userBossname)){
                Write(returnPars(-1,"","用户信息已存在"));
            }else {
                Write(returnPars(1,"","查询完成！"));
            }
        }catch (Exception e){
            e.printStackTrace();
            logger.info("用户信息验证异常："+e.getMessage(),e);
            Write(returnPars(-1,"","用户信息验证异常【"+e.getMessage()+"】"));
        }
    }

    /**
     * @Description TODO 获取附件消息
     * <AUTHOR>
     * @Date 2022/7/14 10:19
     **/
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = tripartipartUserService.fuJian(id, biaoshi);
        Write(JSONHelper.Serialize(s));
    }

    /**
     * @Description TODO 三方用户导入模板下载
     * <AUTHOR>
     * @Date 2022/4/26 14:07
     **/
    public void downLoadTripartiparteUserModel(){
        try{
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd");
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest req = ServletActionContext.getRequest();
            String name = "三方用户导入模板"+formatter.format(new Date());
            String filepath = req.getRealPath("/template/TripartiparteUserModel.xlsx");
            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name+".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\""+fileName+"\"");
            response.addHeader("Content-Length", ""+data.length);
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
            outputStream.write(data);
            outputStream.flush();
            outputStream.close();
            response.flushBuffer();
        }catch(Exception e){
            e.printStackTrace();
            logger.info("三方用户导入模板下载："+e.getMessage(),e);
        }
    }

    public void importTemplateFile() {
        try {
            ExcelUtil excelReader = new ExcelUtil(this.file);
            InputStream is = new FileInputStream(this.file);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList<Map<String, Object>>();
            int i;
            HashMap maps;
            if (column == 6) {
                for (i = 1; i <= map.size(); ++i) {
                    maps = new HashMap();
                    maps.put("UserName", ((Map) map.get(i)).get(0));
                    maps.put("Mobile", ((Map) map.get(i)).get(1));
                    maps.put("User4AName", ((Map) map.get(i)).get(2));
                    maps.put("BossUserName", ((Map) map.get(i)).get(3));
                    maps.put("EipDepartment", ((Map) map.get(i)).get(4));
                    maps.put("EipDepartmentMome", ((Map) map.get(i)).get(5));
                    list.add(maps);
                }
                Write(returnPars(1,list,"导入成功！"));
            } else {
                this.Write(returnPars(-1,"","亲爱的同事，文件格式异常，请确认文件格式！"));
            }
        }catch (Exception e) {
            logger.info("文件导入失败："+e.getMessage(),e);
            Write(returnPars(-1,"","亲爱的同事，文件上传异常，请联系管理员处理！"));
        }
    }

    /**
     * @Description TODO 数据返回格式
     * <AUTHOR>
     * @param state 状态
     * @param data  返回数据
     * @param msg   返回信息
     * @return java.lang.String
     * @Date 2022/12/6 14:21
     **/
    private static String returnPars(int state,Object data,String msg){
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code",state);
        mapJson.put("data",data);
        mapJson.put("msg",msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    public void QueryOrderNo(){
        try{
            String id = getString("taskUuid");
            WaitTask waitTask = tripartipartUserService.QueryOrderNo(id);
            //Write(JSONHelper.SerializeWithNeedAnnotation(waitTask));
            Write(String.valueOf(JSONObject.fromObject(waitTask)));
        }catch (Exception e){
            logger.error("EIP数据查询异常："+e.getMessage(),e);
            e.printStackTrace();
            Write("数据查询异常");
        }
    }
}
