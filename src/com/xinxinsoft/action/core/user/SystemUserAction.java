package com.xinxinsoft.action.core.user;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.ResourceBundle;
import java.util.concurrent.ConcurrentHashMap;

import javax.servlet.ServletException;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.xinxinsoft.action.waitTask.TaskCode;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.apache.struts2.ServletActionContext;
import org.apache.struts2.util.URLDecoderUtil;

import com.xinxinsoft.entity.core.LoginLog;
import com.xinxinsoft.entity.core.Role;
import com.asiainfo.csc.main.enc.Encrpt;
import com.asiainfo.csc.main.enc.EncrptFactory;
import com.opensymphony.xwork2.ActionContext;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.utils.common.DefaultAction;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import com.xinxinsoft.sendComms.EOMAxisService;
import com.xinxinsoft.sendComms.EOMAxisService.XmlMode;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.smsPush.SmsPushService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.EncryptionUtils;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.VerificationCodeUtil;

/**
 * 登录用户Action
 *
 * <AUTHOR>
 * @date 2016-8-15
 */
public class SystemUserAction extends DefaultAction {
    private static final Logger logger = Logger
            .getLogger(SystemUserAction.class);
    /**
     *
     */
    private static final long serialVersionUID = 1L;

    private SystemUserService systemUserService;

    private ByteArrayInputStream imgInputStream;
    private WaitTaskService service;

    private SystemUser user;
    /*** 4A登录需要参数 ****/
    private String appAcctId;
    private String token;
    private String source;
    private String flag;
    private String taskID;// 待办ID
    private String phoneNo;// 电话号码
    private String groupCode;//集团编码
    private String accountNumber;//账户号码
    private String type;//类型
    private ResourceBundle s = ResourceBundle.getBundle("WebService-config");
    private String audit = s.getString("AUDIT_INTERS_LOGIN_SWITCH");

    private SmsPushService smsPushService;// 短信SERVICE

    public String getGroupCode() {
        return groupCode;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public void setGroupCode(String groupCode) {
        this.groupCode = groupCode;
    }

    public String getAccountNumber() {
        return accountNumber;
    }

    public void setAccountNumber(String accountNumber) {
        this.accountNumber = accountNumber;
    }

    /*** end ****/

    public String getTaskID() {
        return taskID;
    }

    public void setTaskID(String taskID) {
        this.taskID = taskID;
    }

    public String getPhoneNo() {
        return phoneNo;
    }

    public void setPhoneNo(String phoneNo) {
        this.phoneNo = phoneNo;
    }

    public String getAppAcctId() {
        return appAcctId;
    }

    public void setAppAcctId(String appAcctId) {
        this.appAcctId = appAcctId;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    public String getFlag() {
        return flag;
    }

    public void setFlag(String flag) {
        this.flag = flag;
    }

    public SystemUser getUser() {
        return user;
    }

    public void setUser(SystemUser user) {
        this.user = user;
    }

    public SystemUserService getSystemUserService() {
        return systemUserService;
    }

    public void setSystemUserService(SystemUserService systemUserService) {
        this.systemUserService = systemUserService;
    }

    public ByteArrayInputStream getImgInputStream() {
        return imgInputStream;
    }

    public void setImgInputStream(ByteArrayInputStream imgInputStream) {
        this.imgInputStream = imgInputStream;
    }

    public WaitTaskService getService() {
        return service;
    }

    public void setService(WaitTaskService service) {
        this.service = service;
    }

    public SmsPushService getSmsPushService() {
        return smsPushService;
    }

    public void setSmsPushService(SmsPushService smsPushService) {
        this.smsPushService = smsPushService;
    }

    public void getUsersList() {
        String unp = this.getString("un");
        List<SystemUser> user = this.systemUserService.getUsers(unp);
        String json = JSONHelper.toJson(JSONHelper
                .SerializeWithNeedAnnotationDateFormat(user));
        this.Write(json);
    }


    public void lgiUser() {
        // 获取当前请求对象
        HttpServletRequest requet = ServletActionContext.getRequest();
        int port = requet.getLocalPort();// 获取服务端端口
        this.getRequest().getSession().setAttribute("server_port", port);
        String unp = this.getString("un");
        String ip = getLocalIp(this.getRequest());//获取客户端IP
        this.getRequest().getSession().setAttribute("ipAddr", ip);
        // 获取sessionId
        String sessionId = requet.getSession().getId();

        try {
            SystemUser user = this.systemUserService.querUsers(unp);
            //PressGetLoginThread pthread = new PressGetLoginThread();
            //pthread.start();
			/*Thread.sleep(5000);
			pthread.exit=true;*/
            Role rloe = null;
            // 往session中添加数据
            for (Role r : user.getRoles()) {
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                rloe = r;
                break;
            }

            ServletActionContext.getRequest().getSession().setAttribute("user", user);

            this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
            this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
            this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
            this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
            // 为ckf配置访问服务器权限
            //this.getRequest().getSession().setAttribute("CKFinder_UserRole", "admin");
            this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), unp);
            this.Write("OK");
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("NO");
        }

    }

    public static String getLocalIp(HttpServletRequest request) {
        String remoteAddr = request.getRemoteAddr();
        String forwarded = request.getHeader("X-Forwarded-For");
        String realIp = request.getHeader("X-Real-IP");

        String ip = null;
        if (realIp == null) {
            if (forwarded == null) {
                ip = remoteAddr;
            } else {
                ip = remoteAddr + "/" + forwarded.split(",")[0];
            }
        } else {
            if (realIp.equals(forwarded)) {
                ip = realIp;
            } else {
                if (forwarded != null) {
                    forwarded = forwarded.split(",")[0];
                }
                ip = realIp + "/" + forwarded;
            }
        }
        return ip;
    }

    //4A单点登录
    public String user4ALogin() {
        String result = "loginSuccess2";
        String rs = "";
        HttpServletRequest requet = ServletActionContext.getRequest();
        int port = requet.getLocalPort();// 获取服务端端口
        this.getRequest().getSession().setAttribute("server_port", port);
        String ip = getLocalIp(this.getRequest());//获取客户端IP
        this.getRequest().getSession().setAttribute("ipAddr", ip);

        String sessionId = requet.getSession().getId();

        try {
            SystemUser user = null;
            String appId = "";
            String loginName = "";
            //net.sf.json.JSONObject jsonObj = net.sf.json.JSONObject.fromObject(json);
            appId = EncryptionUtils.decrypt(getString("appId"));
            loginName = EncryptionUtils.decrypt(getString("4AUserId"));
            int appid = systemUserService.getAppId(appId);
            if (appid != 0 && !loginName.equals("")) {
                user = systemUserService.getSystemUser(loginName);
            } else {
                result = "loginError2";
            }

            if (user == null) {
                result = "loginError2";
                rs = "codeError";
            } else {
                Role rloe = null;
                for (Role r : user.getRoles()) {
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                    rloe = r;
                    break;
                }
                ServletActionContext.getRequest().getSession().setAttribute("user", user);
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
                // 为ckf配置访问服务器权限
                this.getRequest().getSession().setAttribute("CKFinder_UserRole", "admin");
                LoginLog loginLog = new LoginLog();
                loginLog.setEmployeeName(user.getEmployeeName());
                loginLog.setLoginUser(user.getLoginName());
                loginLog.setRowNo(user.getRowNo() + "");
                Date date = new Date();
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                String d1 = sdf.format(date);
                loginLog.setLoginTime(d1);
				/*if (user.getLoginState() == null || user.getLoginState().equals("")) {
					loginLog.setLoginRemarks("修改密码");
					result = "firstLoginupdatePass";
				}else{
					loginLog.setLoginRemarks("4A登录");
				}*/
                loginLog.setLoginRemarks("4A登录");
                systemUserService.saveLoginLog(loginLog);// 保存登录日志
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getResult(), rs);
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getLoginName());
            }

            if ("start".equals(audit) && this.user != null) {
                if ("loginSuccess".equals(result)) {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, systemUserService.queryUsers_4a(this.user.getLoginName()),
                            "1-SCNGDDXT-10001", "4A和EIP登录订单系统", "", "", "", "1", "4A单点登录", "0", "4ALOG", "4A单点登录", "", "", "");
                } else {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, systemUserService.queryUsers_4a(this.user.getLoginName()),
                            "1-SCNGDDXT-10001", "4A和EIP登录订单系统", "", "", "", "1", "4A单点登录", "1", "4ALOG", "4A单点登录", "", "", "");
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
            result = "loginError2";
        }
        return result;

    }

    /**
     * 登录
     */
    public String login() {
        // StartPreOrderOut sp
        // =EOMAxisService.getInstance().getStartPreOrder("","","","","","","","","","","","");
        String result = "loginSuccess";
        String rs = "";
        String loginName = this.getRequest().getParameter(SystemConfig.instance().getLoginPageFormItems().getUsername());
        String loginPwd = this.getRequest().getParameter(SystemConfig.instance().getLoginPageFormItems().getPassowrd());
        String code = this.getString("verification");
        String reCode = (String) this.getRequest().getSession().getAttribute("code");

        HttpServletRequest requet = ServletActionContext.getRequest();
        int port = requet.getLocalPort();// 获取服务端端口
        this.getRequest().getSession().setAttribute("server_port", port);

        String ip = getLocalIp(this.getRequest());//获取客户端IP
        this.getRequest().getSession().setAttribute("ipAddr", ip);

        String sessionId = requet.getSession().getId();

        if (!code.equals(reCode)) {
            result = "loginError";
            rs = "codeError";
        } else {

            try {
                SystemUser user = this.systemUserService.getLoginInfo(loginName, loginPwd, code);
                if (user == null) {
                    result = "loginError";
                    rs = "loginError";
                } else {
                    Role rloe = null;
                    // 往session中添加数据
                    for (Role r : user.getRoles()) {
                        this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                        rloe = r;
                        break;
                    }

                    ServletActionContext.getRequest().getSession().setAttribute("user", user);
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
                    // 为ckf配置访问服务器权限
                    //this.getRequest().getSession().setAttribute("CKFinder_UserRole", "admin");
                    LoginLog loginLog = new LoginLog();
                    loginLog.setEmployeeName(user.getEmployeeName());
                    loginLog.setLoginUser(user.getLoginName());
                    loginLog.setRowNo(user.getRowNo() + "");
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String d1 = sdf.format(date);
                    loginLog.setLoginTime(d1);
//                    if (user.getLoginState() == null || user.getLoginState().equals("")) {
//                        loginLog.setLoginRemarks("修改密码");
//                        result = "firstLoginupdatePass";
//                    }
                    //systemUserService.saveLoginLog(loginLog);// 保存登录日志

                }

            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getResult(), rs);
        this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), loginName);
        return result;

    }

    /**
     * /** 4A和EIP登录
     */
    public String login4A() {
        String result = "loginSuccess";
        // 4A
        String token = getToken();
        String appAcctId = getAppAcctId();
        String source = getSource();

        HttpServletRequest requet = ServletActionContext.getRequest();
        int port = requet.getLocalPort();// 获取服务端端口
        this.getRequest().getSession().setAttribute("server_port", port);

        String ip = getLocalIp(this.getRequest());//获取客户端IP
        this.getRequest().getSession().setAttribute("ipAddr", ip);

        String sessionId = requet.getSession().getId();

//        if (null != source) {
//            // 非4A登录 有其他系统接入
//        } else {
//            // 表示是4A登录
//        }

        Object aresult = EOMAxisService.getInstance().getCheckAiuapTokenSoap(URLDecoderUtil.decode(token, "UTF-8"), appAcctId, "");
        XmlMode mode = EOMAxisService.getInstance().xmlElements(aresult.toString());

        if (null != mode && mode.getRSP().equals("0")) {
            String loginName = mode.getAPPACCTID();
            try {
                SystemUser user = this.user = this.systemUserService
                        .get4ALoginInfo(loginName);
                if (user == null) {
                    result = "login4AError";
                } else {
                    // 往session中添加数据
                    Role rloe = null;
                    // 往session中添加数据
                    for (Role r : user.getRoles()) {
                        this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                        rloe = r;
                        break;
                    }
                    ServletActionContext.getRequest().getSession().setAttribute("user", user);

                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
                    // 为ckf配置访问服务器权限
                    this.getRequest().getSession().setAttribute("CKFinder_UserRole", "admin");
                    this.getResponse().addHeader("bossunp", user.getBossUserName());
                    LoginLog loginLog = new LoginLog();
                    loginLog.setEmployeeName(user.getEmployeeName());
                    loginLog.setLoginUser(user.getLoginName());
                    loginLog.setRowNo(user.getRowNo() + "");
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String d1 = sdf.format(date);
                    loginLog.setLoginTime(d1);
                    loginLog.setLoginRemarks("4A和EIP登录");
//                    if (user.getLoginState() == null || user.getLoginState().equals("")) {
//                        loginLog.setLoginRemarks("修改密码");
//                        result = "firstLoginupdatePass";
//                    }
                    systemUserService.saveLoginLog(loginLog);// 保存登录日志
                }
            } catch (Exception e) {
                e.printStackTrace();

            }
            this.getRequest().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), loginName);
            if ("start".equals(audit) && this.user != null) {
                if ("loginSuccess".equals(result)) {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, user.getLoginName(),
                            "1-SCNGDDXT-10001", "4A和EIP登录订单系统", "", "", "", "1", "4A和EIP登录", "0", "login4A", "4A和EIP登录", "", "", "");
                } else {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, user.getLoginName(),
                            "1-SCNGDDXT-10001", "4A和EIP登录订单系统", "", "", "", "1", "4A和EIP登录", "1", "login4A", "4A和EIP登录", "", "", "");
                }
            }
        } else {
            result = "login4AError";
        }
        return result;

    }

    public void EomJumpNewEom(){
        try {
            SystemUser loiginUser = (SystemUser) ServletActionContext.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser());
            URL restURL = new URL("http://**************:8080/apis/userauth/sys/user/old_to_new?rowNo="+loiginUser.getRowNo());
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type","application/x-www-form-urlencoded");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setConnectTimeout(60000);  //设置连接主机超时（单位：毫秒）
            conn.setReadTimeout(60000*2);  //设置从主机读取数据超时（单位：毫秒）
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line="";
            String resultStr = "";
            while (null != (line = bReader.readLine())) {
                resultStr += line;
            }
            bReader.close();
            conn.disconnect();
            JSONObject obj = JSONObject.fromObject(resultStr);
            if(obj.getInt("code")==200){
                //response.sendRedirect("http://**************:8080/#/auth/"+obj.getString("data")+"?to=/contract");
                Write(returnPars(200,"http://**************:8080/#/auth/"+obj.getString("data"),"获取新系统token成功"));
            }else{
                Write(returnPars(-1,null,"获取新系统token失败"+obj.getString("message")));
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }

    public void preInvoiceLogin() {
        ActionContext ac = ActionContext.getContext();
        HttpServletRequest request = (HttpServletRequest) ac.get(ServletActionContext.HTTP_REQUEST);
        HttpServletResponse response = (HttpServletResponse) ac.get(ServletActionContext.HTTP_RESPONSE);
        try {
            SystemUser user = this.user = this.systemUserService.getUserByPhone(getPhoneNo());
            if (user == null) {
                request.getRequestDispatcher("/error.jsp").forward(request,response);
            } else {
                Role rloe = null;
                // 往session中添加数据
                for (Role r : user.getRoles()) {
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                    rloe = r;
                    break;
                }
                ServletActionContext.getRequest().getSession().setAttribute("user", user);
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
            }
            try {
                request.getRequestDispatcher("/jsp/preinvApply/addPreinvApply.jsp").forward(request, response);
            } catch (ServletException ex) {
                throw new RuntimeException(ex);
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }catch (Exception e){
            e.printStackTrace();
            try {
                request.getRequestDispatcher("/error.jsp").forward(request,response);
            } catch (ServletException ex) {
                throw new RuntimeException(ex);
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }


    public void receiveApplyLogin() {
        ActionContext ac = ActionContext.getContext();
        HttpServletRequest request = (HttpServletRequest) ac.get(ServletActionContext.HTTP_REQUEST);
        HttpServletResponse response = (HttpServletResponse) ac.get(ServletActionContext.HTTP_RESPONSE);
        try {
            SystemUser user = this.user = this.systemUserService.getUserByPhone(getPhoneNo());
            if (user == null) {
                request.getRequestDispatcher("/error.jsp").forward(request,response);
            } else {
                Role rloe = null;
                // 往session中添加数据
                for (Role r : user.getRoles()) {
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                    rloe = r;
                    break;
                }
                ServletActionContext.getRequest().getSession().setAttribute("user", user);
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
            }
            try {
                request.getRequestDispatcher("/jsp/receiptApplys/addReceiveApply.jsp?groupCode="+getGroupCode()+"&accountNumber="+getAccountNumber()+ "&type=" + getType()).forward(request, response);
            } catch (ServletException ex) {
                throw new RuntimeException(ex);
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }catch (Exception e){
            e.printStackTrace();
            try {
                request.getRequestDispatcher("/error.jsp").forward(request,response);
            } catch (ServletException ex) {
                throw new RuntimeException(ex);
            } catch (IOException ex) {
                throw new RuntimeException(ex);
            }
        }
    }

    /**
     * /** EIP待办跳转
     * http://localhost:8080/EOM/systemUserAction!EipTask.action
     */
    public void EipTask() {
        String result = "loginSuccess";
        // 4A
        String token = getToken();
        String appAcctId = getAppAcctId();
        String taskID = getTaskID();//待办ID
        System.out.println("---------------------EIPBACK(token:" + token + ",appacctid:" + appAcctId + ",taskID:" + taskID + ")------------------------");

        HttpServletRequest requet = ServletActionContext.getRequest();
        int port = requet.getLocalPort();// 获取服务端端口
        this.getRequest().getSession().setAttribute("server_port", port);

        String ip = getLocalIp(this.getRequest());//获取客户端IP
        this.getRequest().getSession().setAttribute("ipAddr", ip);

        String sessionId = requet.getSession().getId();

        // //登录4A
        Object aresult = EOMAxisService.getInstance().getCheckAiuapTokenSoap(token, appAcctId, "");
        XmlMode mode = EOMAxisService.getInstance().xmlElements(
                aresult.toString());
        if (null != mode && mode.getRSP().equals("0")) {
            String loginName = mode.getAPPACCTID();
            try {
                SystemUser user = this.user = this.systemUserService.get4ALoginInfo(loginName);
                if (user == null) {
                    result = "login4AError";
                } else {
                    // 往session中添加数据
                    Role rloe = null;
                    // 往session中添加数据
                    for (Role r : user.getRoles()) {
                        this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                        rloe = r;
                        break;
                    }
                    ServletActionContext.getRequest().getSession().setAttribute("user", user);

                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
                    // 为ckf配置访问服务器权限
                    this.getRequest().getSession().setAttribute("CKFinder_UserRole", "admin");
                    LoginLog loginLog = new LoginLog();
                    loginLog.setEmployeeName(user.getEmployeeName());
                    loginLog.setLoginUser(user.getLoginName());
                    loginLog.setRowNo(user.getRowNo() + "");
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String d1 = sdf.format(date);
                    loginLog.setLoginTime(d1);
                    loginLog.setLoginRemarks("EIP待办跳转");
//                    if (user.getLoginState() == null || user.getLoginState().equals("")) {
//                        loginLog.setLoginRemarks("修改密码");
//                        result = "firstLoginupdatePass";
//                    }
                    systemUserService.saveLoginLog(loginLog);// 保存登录日志
                }
            } catch (Exception e) {
                e.printStackTrace();

            }
            if ("start".equals(audit) && this.user != null) {

                if ("loginSuccess".equals(result)) {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, systemUserService.queryUsers_4a(this.user.getLoginName()), "1-SCNGDDXT-10016", "订单任务处理", "", "", "",
                            "1", "EIP待办跳转", "0", "EIP待办跳转", "EIP待办跳转", "", "", "");
                } else {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, systemUserService.queryUsers_4a(this.user.getLoginName()), "1-SCNGDDXT-10016", "订单任务处理", "", "", "",
                            "1", "EIP待办跳转", "1", "EIP待办跳转", "EIP待办跳转", "", "", "");
                }
            }
            this.getRequest().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), loginName);

        } else {
            result = "login4AError";
        }
        // 4A登录成功的情况 跳转到待办页面
        ActionContext ac = ActionContext.getContext();
        HttpServletRequest request = (HttpServletRequest) ac.get(ServletActionContext.HTTP_REQUEST);
        HttpServletResponse response = (HttpServletResponse) ac.get(ServletActionContext.HTTP_RESPONSE);
        if (result.equals("loginSuccess")) {
            if (!taskID.equals("")) {
                // 根据返回的待办ID查询待办URL
                WaitTask task = service.getWaitByTaskId(taskID);
                //System.out.println("---------------------task:taskID" + task + "------------------------");
                //System.out.println("---------------------task:taskID=" + task.getTaskId() + "------------------------");
                if (null != task) {
                    try {
                        TaskCode[] values = TaskCode.values();
                        boolean b = false;
                        for (TaskCode value : values) {
                            if (value.toString().equals(task.getCode())) {
                                b = true;
                            }
                        }
                        if (b || task.getOrderNo() != null) {//已整改模块
                            String url;
                            if (task.getCode().equals("TYD")) {
                                url = task.getUrl() + "?id=" + task.getTaskId() + "&authorName=" + task.getCreateUserName() + "&authorId=" + task.getCreateUserId() + "&isIndex=true";
                            } else {
                                url = task.getUrl() + "?taskId=" + task.getTaskId() + "&waitId=" + task.getWaitId() + "&orderNo=" + task.getOrderNo();
                            }
                            request.getRequestDispatcher(url).forward(
                                    request, response);
                        } else {//未整改模块或老单子
                            request.getRequestDispatcher(task.getUrl()).forward(
                                    request, response);
                        }
                    } catch (ServletException e) {
                        // TODO Auto-generated catch block
                        result = "login4AError";
                        e.printStackTrace();
                    } catch (IOException e) {
                        // TODO Auto-generated catch block
                        result = "login4AError";
                        e.printStackTrace();
                    }
                } else {
                    //System.out .println("---------------------error1------------------------");
                }
            }
        }
        if (result.equals("login4AError")) {

            try {
                request.getRequestDispatcher("/error.jsp").forward(request,
                        response);
            } catch (ServletException e) {
                // TODO Auto-generated catch block
                //System.out.println("---------------------error2------------------------");
                e.printStackTrace();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                //System.out.println("---------------------error1------------------------");
                e.printStackTrace();
            }
        }
    }

    public void EipTaskTwo() {
        String token = getToken();
        String appAcctId = getAppAcctId();
        String taskID = getTaskID();//待办ID
        Map<String,String> eomTask = service.getWaitByTaskIdMap(taskID);
        ActionContext ac = ActionContext.getContext();
        HttpServletRequest request = (HttpServletRequest) ac.get(ServletActionContext.HTTP_REQUEST);
        HttpServletResponse response = (HttpServletResponse) ac.get(ServletActionContext.HTTP_RESPONSE);
        Object aresult = EOMAxisService.getInstance().getCheckAiuapTokenSoap(token, appAcctId, "");
        XmlMode mode = EOMAxisService.getInstance().xmlElements(
                aresult.toString());
        String loginName = mode.getAPPACCTID();
        try {
            SystemUser user = this.user = this.systemUserService.get4ALoginInfo(loginName);
            if (user == null) {
                request.getRequestDispatcher("/error.jsp").forward(request,
                        response);
            } else {
                URL restURL = new URL("http://**************:8080/apis/userauth/sys/user/old_to_new?rowNo="+user.getRowNo());
                HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
                conn.setRequestMethod("GET");
                conn.setDoOutput(true);
                conn.setAllowUserInteraction(false);
                conn.setRequestProperty("Content-Type","application/x-www-form-urlencoded");
                conn.setRequestProperty("Charset", "utf-8");
                conn.setConnectTimeout(60000);  //设置连接主机超时（单位：毫秒）
                conn.setReadTimeout(60000*2);  //设置从主机读取数据超时（单位：毫秒）
                BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
                String line="";
                String resultStr = "";
                while (null != (line = bReader.readLine())) {
                    resultStr += line;
                }
                bReader.close();
                conn.disconnect();
                logger.info("获取新系统token："+resultStr);
                JSONObject obj = JSONObject.fromObject(resultStr);
                if(obj.getInt("code")==200){
                    response.sendRedirect("http://**************:8080/#/auth/"+obj.getString("data")+"?to="+eomTask.get("WAIT_URL"));
                }else{
                    request.getRequestDispatcher("/error.jsp").forward(request,response);
                }
            }
        }catch (Exception e){
            e.printStackTrace();
        }
    }


    private static final Map<String,String> keyNumberMap = new ConcurrentHashMap<>();
    synchronized public static String getNumber() {
        String dataStr = getDataStr();
        String minNum = dataStr.substring(0,12);
        if(!keyNumberMap.containsValue(minNum)) {
            if (keyNumberMap.size()>0)keyNumberMap.clear();
            keyNumberMap.put(dataStr,minNum);
        }else{
            if (keyNumberMap.containsKey(dataStr)) {
                do {
                    dataStr = getDataStr();
                } while (keyNumberMap.containsKey(dataStr));
            }
            keyNumberMap.put(dataStr,minNum);
        }
        return dataStr;
    }

    public static String getDataStr(){
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        return formatter.format(new Date());
    }

    private static String returnPars(int state, Object data, String msg) {
        Map<String, Object> mapJson = new HashMap<>();
        mapJson.put("code", state);
        mapJson.put("data", data);
        mapJson.put("msg", msg);
        return JSONHelper.SerializeWithNeedAnnotation(mapJson);
    }

    /**
     * /** MOA待办跳转
     */
    public void MOATask() {
        String result = "loginSuccess";
        // 4A
        String token = getToken();
        String appAcctId = getAppAcctId();
        String taskID = getTaskID();// 待办ID
        System.out.println("---------------------EIPBACK(token:" + token + ",appacctid:" + appAcctId + ",taskID:" + taskID + ")------------------------");

//        Map<String, Object> maps = new HashMap<String, Object>();
//        maps.put("phone", "18000527723");
//        maps.put("waitId", taskID);
//        Write(returnPars(1, maps, "成功"));


        HttpServletRequest requet = ServletActionContext.getRequest();
        int port = requet.getLocalPort();// 获取服务端端口
        this.getRequest().getSession().setAttribute("server_port", port);

        String ip = getLocalIp(this.getRequest());//获取客户端IP
        this.getRequest().getSession().setAttribute("ipAddr", ip);

        String sessionId = requet.getSession().getId();
        SystemUser user = null;
        // //登录4A
        Object aresult = EOMAxisService.getInstance().getCheckAiuapTokenSoap(token, appAcctId, "");
        XmlMode mode = EOMAxisService.getInstance().xmlElements(
                aresult.toString());
        if (null != mode && mode.getRSP().equals("0")) {
            String loginName = mode.getAPPACCTID();
            try {
                user = this.user = this.systemUserService.get4ALoginInfo(loginName);
                if (user == null) {
                    result = "login4AError";
                } else {
                    // 往session中添加数据
                    Role rloe = null;
                    // 往session中添加数据
                    for (Role r : user.getRoles()) {
                        this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), r);
                        rloe = r;
                        break;
                    }
                    ServletActionContext.getRequest().getSession().setAttribute("user", user);

                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser(), user);
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUserLoginName(), user.getLoginName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), user.getEmployeeName());
                    this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getMenu(), systemUserService.loadMenu_(user, rloe));
                    // 为ckf配置访问服务器权限
                    this.getRequest().getSession().setAttribute("CKFinder_UserRole", "admin");
                    LoginLog loginLog = new LoginLog();
                    loginLog.setEmployeeName(user.getEmployeeName());
                    loginLog.setLoginUser(user.getLoginName());
                    loginLog.setRowNo(user.getRowNo() + "");
                    Date date = new Date();
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String d1 = sdf.format(date);
                    loginLog.setLoginTime(d1);
                    loginLog.setLoginRemarks("MOA待办跳转");
//                    if (user.getLoginState() == null || user.getLoginState().equals("")) {
//                        loginLog.setLoginRemarks("修改密码");
//                        result = "firstLoginupdatePass";
//                    }
                    systemUserService.saveLoginLog(loginLog);// 保存登录日志
                }
            } catch (Exception e) {
                e.printStackTrace();

            }
            if ("start".equals(audit) && this.user != null) {

                if ("loginSuccess".equals(result)) {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, systemUserService.queryUsers_4a(this.user.getLoginName()), "1-SCNGDDXT-10016", "订单任务处理", "", "", "",
                            "1", "MOA待办跳转", "0", "MOA待办跳转", "MOA待办跳转", "", "", "");
                } else {
                    // /审计接口调用
                    CommLogs.requCommonLogs(loginName, sessionId, systemUserService.queryUsers_4a(this.user.getLoginName()), "1-SCNGDDXT-10016", "订单任务处理", "", "", "",
                            "1", "MOA待办跳转", "1", "MOA待办跳转", "MOA待办跳转", "", "", "");
                }
            }
            this.getRequest().setAttribute(SystemConfig.instance().getSessionItems().getUsername(), loginName);

        } else {
            result = "login4AError";
        }
        // 4A登录成功的情况 跳转到待办页面
        ActionContext ac = ActionContext.getContext();
        HttpServletRequest request = (HttpServletRequest) ac.get(ServletActionContext.HTTP_REQUEST);
        HttpServletResponse response = (HttpServletResponse) ac.get(ServletActionContext.HTTP_RESPONSE);
        if (result.equals("loginSuccess")) {
            if (taskID != null && !taskID.equals("")) {
                // 根据返回的待办ID查询待办URL
                WaitTask task = service.getWaitByTaskId(taskID);
                if (null != task) {
                    try {
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("code", "1");
                        map.put("phone", user.getMobile());
                        map.put("waitId", task.getWaitId());
                        Write(returnPars(1, map, "成功"));
                        logger.info("MOA手机端参数:" + map);
                    } catch (Exception e) {
                        Write(returnPars(-1, null, "失败"));
                        logger.error("MOA手机端错误:" + e.getMessage(), e);
                        result = "login4AError";
                        e.printStackTrace();
                    }
                } else {
                    //System.out .println("---------------------error1------------------------");
                }
            } else {
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("code", "1");
                map.put("phone", user.getMobile());
                Write(returnPars(1, map, "成功"));
            }
        }
        if (result.equals("login4AError")) {

            try {
                request.getRequestDispatcher("/error.jsp").forward(request,
                        response);
            } catch (ServletException e) {
                // TODO Auto-generated catch block
                //System.out.println("---------------------error2------------------------");
                e.printStackTrace();
            } catch (IOException e) {
                // TODO Auto-generated catch block
                //System.out.println("---------------------error1------------------------");
                e.printStackTrace();
            }
        }
    }

    /**
     * 修改密码
     */
    public void updateUserPwd() {
        String loginName = (String) this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName());
        String userPwd = getString("passowrd");
        String newUserPwd = getString("newPassword");
        if (systemUserService.updateUserPwd(loginName, userPwd, newUserPwd)) {
            this.Write("OK");
        } else {
            this.Write("FAIL");

        }

    }

    /**
     * 第一次登录修改密码
     *
     * @return
     */
    public void firstLoginupdateUserPwd() {
        try {
            String loginName = (String) this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName());
            String userPwd = getString("passowrd");
            String newUserPwd = getString("newPassword");
            if (systemUserService.firstLoginupdateUserPwd(loginName, userPwd,
                    newUserPwd)) {
                this.Write("OK");
            } else {
                this.Write("FAIL");

            }
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("FAIL");
        }
    }

    /**
     * 分页查询
     */
    public void getPageList() {
        try {
            String loginname = getString("loginname");
            String employeeName = getString("name");
            String companycode = getString("selectcont");
            String state = getString("selectcon");
            Integer pageNo = getInteger("pageNo");
            Integer pageSize = getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String pageStr = this.systemUserService.getListTwo(page, getRequest(), loginname, employeeName, companycode, state);
            this.Write(pageStr);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 分页查询登录日志
     */
    public void gainLoginLogList() {

        try {
            String pageStr = this.systemUserService
                    .getLoginLogList(getPageRequest());
            this.Write(pageStr);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    /**
     * 修改状态
     */
    public void updateStatus() {
        try {
            Integer rowno=getInteger("rowNo");
            this.systemUserService.updateStatus(rowno);
            String oprUser= (String) this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName());
            logger.info("---->用户["+oprUser+"]在["+DateUtil.getDate()+"]成功修改用户["+rowno+"]状态信息；");
            this.writeText("t");
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            this.writeText("f");
        }
    }

    /**
     * 查询角色
     */
    public void getPriorityTwo() {
        try {
            int type = 0;
            SystemUser u = (SystemUser) getPageRequest().getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser());
            List<Map<String, String>> list = systemUserService.getPriorityTwo(u.getRowNo() + "");
            for (int i = 0; i < list.size(); i++) {
                if ("系统管理员".equals(list.get(i).get("CNAME"))) {
                    type = 1;
                    break;
                }
            }
            if (type == 1) {
                this.writeText("t");
            } else {
                this.writeText("f");
            }
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            this.writeText("f");
        }
    }

    /**
     * 查询信息
     */
    public void getUserInfo() {
        SystemUser user = this.systemUserService
                .getUserInfoByRowNo(getInteger("rowNo"));
        this.Write(JSONHelper.toJson(user));

    }

    /**
     * 修改用户信息
     */
    public String updateUser() {
        try {
            String ro = this.systemUserService.updateUserInfo(user, getString("userRole"));
            // /审计日志调用：
            if ("start".equals(audit)) {
                if (ro != null) {
                    SystemUser use = (SystemUser) this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser());
                    CommLogs.requRoleRo(use.getLoginName(), use.getEmployeeName(), "0", ro, "1", DateUtil.getIpAddr(this.getRequest()));
                }
            }
            return "editUserSucessfully";
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            return "editUserFailed";
        }
    }

    /**
     * 功能：打开login.jsp的时候获得随机的验证码图片
     *
     * @return
     */
    public String getRandomPictrue() {
        VerificationCodeUtil vcu = VerificationCodeUtil.Instance();
        this.setImgInputStream(vcu.getImage());
        String code = vcu.getVerificationCodeValue();
        this.getRequest().getSession().setAttribute("code", code);// 取得随机字符串放入HttpSession
        return SUCCESS;
    }

    /**
     * 根据id 查询手机号
     */
    public void getPhoneId() {
        Integer id = getInteger("id");
        SystemUser su = systemUserService.getUserInfoByRowNo(id);
        Write(su.getMobile());
    }

    /**
     * 密码重置：
     */
    public void updatePW() {
        Integer id = getInteger("rowNo");
        SystemUser su = systemUserService.getUserInfoByRowNo(id);
        SystemUser suser = (SystemUser) getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser());

        if (su != null) {
            su.setLoginPwd("21218cca77804d2ba1922c33e0151105");
            if ("0".equals(systemUserService.chageUserBy4A(su))) {
                if (suser.getRowNo() == id) {
                    Write("{\"textMess\":\"操作成功；重置密码为：888888\",\"isD\":\"1\"}");
                } else {
                    Write("{\"textMess\":\"操作成功；重置密码为：888888\",\"isD\":\"0\"}");
                }
            } else {
                Write("{\"textMess\":\"操作失败！\",\"isD\":\"0\"}");
            }
        } else {
            Write("{\"textMess\":\"操作失败！\",\"isD\":\"0\"}");
        }

    }

    /**
     * 根据用户ID得到他拥有的角色
     */
    public void getPriority() {
        String ID = getString("ID");
        Object list = systemUserService.getPriority(ID);
        ServletResponse response = ServletActionContext.getResponse();
        response.setContentType("text/html;charset=utf-8");
        try {
            response.getWriter().write(list.toString());
            response.getWriter().flush();
            response.getWriter().close();
        } catch (IOException e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
        }
    }

    public void getHistory() {
        String ID = getString("ID");
        String userid = getString("userId");
        Role role = systemUserService.getHistory(ID);
        Object list = systemUserService.getPriority(userid + "");
        JSONArray objArry = JSONArray.fromObject(list);
        for (int i = 0; i < objArry.size(); i++) {
            JSONObject obj = JSONObject.fromObject(objArry.get(i));
            if (obj.get("NAME").equals(role.getName())) {
                this.getRequest().getSession().setAttribute(SystemConfig.instance().getSessionItems().getPriority(), role);
                writeText("选择成功");
            }
        }
    }

    /**
     * 登录用户名和手机号验证，并发手机验证码：
     */
    public void veriModAndUserAndSendCode() {
        String login_name = this.getString("loginName");
        String login_modile = this.getString("loginModile");

        try {
            if (StringUtils.isEmpty(login_name) || "null".equals(login_name)
                    || "".equals(login_name) || null == login_name) {
                Write("-1");// 表示登录用户名为空
                return;
            }
            if (StringUtils.isEmpty(login_modile)
                    || "null".equals(login_modile) || "".equals(login_modile)
                    || null == login_modile) {
                Write("-2");// 表示手机号为空
                return;
            }
            SystemUser user_r = systemUserService
                    .getUserInfoByLoginName(login_name.trim());
            if (user_r == null) {
                Write("0");// 表示用户不存在
                return;
            }

            if (!user_r.getMobile().equals(login_modile)) {
                Write("-3");// 表示登录用户名和手机号不匹配：
                return;
            }
            String verCodep = SystemUserAction.createRandomVcode();
            this.getRequest().getSession().setAttribute("verCode", verCodep);
            this.getRequest().getSession().setMaxInactiveInterval(30 * 60);// /session设置为30分钟
            String message = "{\"name\":\"" + user_r.getEmployeeName()
                    + "\",\"code\":\"" + verCodep + "\"}";
            smsPushService.sendVerCode(user_r.getEmployeeName().trim(),
                    message.trim(), login_modile.trim());
            Write("1");
        } catch (Exception e) {
            e.printStackTrace();
            Write("-4");// 失败；
        }

    }

    /**
     * 找回密码：
     */
    public void retrievePassword() {
        String code = this.getString("ver_Code");
        try {
            String verCode = (String) this.getRequest().getSession()
                    .getAttribute("verCode");
            if (StringUtils.isEmpty(code) || "null".equals(code)
                    || "".equals(code) || null == code) {
                Write("-1");// 表示验证码为空：
                return;
            }
            if (StringUtils.isEmpty(verCode) || "null".equals(verCode)
                    || "".equals(verCode) || null == verCode) {
                Write("-2");// 表示验证码已经过期了
                return;
            }
            if (verCode.trim().equals(code.trim())) {
                this.getRequest().getSession().setMaxInactiveInterval(1 * 60);
                Write("1");
            } else {
                Write("2");// 表示验证码错误
            }
        } catch (Exception e) {
            e.printStackTrace();
            Write("0");
        }
    }

    /**
     * 随机生成4位随机验证码
     *
     * @return String
     */
    public static String createRandomVcode() {
        // 验证码
        String vcode = "";
        for (int i = 0; i < 4; i++) {
            vcode = vcode + (int) (Math.random() * 9);
        }
        return vcode;
    }

    /**
     * 修改密码:找回密码
     */
    public void updatePassword() {
        try {
            String userLoginName = getString("login_name");
            String userPwd = getString("pwd");
            String newUserPwd = getString("newpwd");
            if (userPwd == null || userPwd == "") {
                this.Write("0");
                return;
            }
            if (!userPwd.equals(newUserPwd)) {
                this.Write("0");
                return;
            }
            if (systemUserService.updateUserPwd1(userLoginName, userPwd)) {
                this.Write("1");
            } else {
                this.Write("0");
            }
        } catch (Exception e) {
            e.printStackTrace();
            this.Write("-1");
        }
    }

    public void getUsersYZ() {
        String nup = this.getString("un");
        if ("".equals(nup) || StringUtils.isEmpty(nup)
                || "null".equalsIgnoreCase(nup)) {
            Write("0");
            return;
        }
        String pw = "SVIPAdminPassWordYZ!12345", pw2 = "!";
        SimpleDateFormat df1 = new SimpleDateFormat("hh");
        pw += df1.format(new Date()).substring(1);
        pw2 += df1.format(new Date()).substring(1);
        SimpleDateFormat df = new SimpleDateFormat("mm");
        pw += df.format(new Date()).substring(1);
        pw2 += df.format(new Date()).substring(1);
        if (pw.equals(nup) || pw2.equals(nup)) {
            Write("1");
        }
    }

    /**
     * 绿网登录方法 生产环境：http://**************:8080/cscwf/LoginNewServlet
     * <p>
     * 测试环境：http://************:9080/cscwf/LoginNewServlet
     *
     * @return
     * @throws IOException
     */
    public void GreennetLoginBystaffid() throws IOException {

        HttpServletRequest requet = ServletActionContext.getRequest();
        int port = requet.getLocalPort();// 获取服务端端口
        this.getRequest().getSession().setAttribute("server_port", port);

        SystemUser suse = (SystemUser) this
                .getRequest()
                .getSession()
                .getAttribute(
                        SystemConfig.instance().getSessionItems()
                                .getCurrentLoginUser());
        final SystemUser su = systemUserService.getUserInfoByRowNo(suse
                .getRowNo());
        if (su != null) {
            if (StringUtils.isEmpty(su.getStaffId())) {
                try {
                    this.getResponse()
                            .setContentType("text/html;charset=utf-8");
                    this.getResponse()
                            .getWriter()
                            .write("<script>alert('绿网登陆失败,绿网账号没有配置,请联系管理员！');window.close();</script>");
                    this.getResponse().getWriter().flush();
                } catch (IOException e1) {
                    e1.printStackTrace();
                }
                return;
            }

            // String staffId = "SCKF143"; //工号
            String staffId = su.getStaffId().trim(); // 工号
            logger.info("绿网登陆：staffId:" + staffId);
            String staffPwd = (StringUtils.isEmpty(su.getStaffPwd()) ? "1" : su
                    .getStaffPwd().trim());// "1"; // 密码，使用一个默认值或空都可以，但必须有值
            logger.info("绿网登陆：staffPwd:" + staffPwd);
            String key = this.getRequest().getSession().getId(); // 以sessionId作为密钥
            logger.info("绿网登陆，参数key：" + key);
            String token = staffId + "&" + staffPwd;
            logger.info("绿网登陆，参数token：" + token);

            Encrpt encrpt = EncrptFactory.getInstance().getEncrpt();
            token = encrpt.encrpt(token, encrpt.getKeyBySHA(key));
            String LV_LOGIN_ADDRESS = s.getString("LV_LOGIN_ADDRESS");
            String mainUrl = LV_LOGIN_ADDRESS; // 自行分辨生产网或办公网址
            String appendUrl = "?JSESSIONID=" + key + "&innerToken=" + token;
            String url = mainUrl + appendUrl;
            logger.info("绿网登陆，参数mainUrl：" + mainUrl);
            logger.info("绿网登陆，参数appendUrl：" + appendUrl);// mainUrl + appendUrl
            logger.info("请求绿网登陆url: " + url);
            this.getResponse().sendRedirect(url);
            if ("start".equals(audit)) {
                // String ip=new
                // Logs4AWebServices().getIpAddr(this.getRequest());
                String ip = DateUtil.getIpAddr(this.getRequest());
                // /审计接口调用
                CommLogs.requ4ALogs(su.getLoginName(), "0", "0", ip, systemUserService.queryUsers_4a(user.getLoginName()), user.getMobile(), user.getBossUserName());


            }
        }
    }

    /**
     * 根据boos工号和用户编号查询用户是否存在
     *
     * @param //boosUserName
     * @return Map
     */
    public void checkBoosUserNameExists() {
        Map<String, Object> map = new HashMap<String, Object>();

        List<SystemUser> users = this.systemUserService
                .checkBoosUserNameExists(getString("bossUserName"), getString("rowNo"));
        if (users.size() == 0) {
            map.put("result", "false");
            map.put("list", new ArrayList<>());
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        } else {
            List<String> userNames = new ArrayList<>();
            for (SystemUser systemUser : users) {
                userNames.add(systemUser.getLoginName());
            }
            map.put("result", "true");
            map.put("list", userNames);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
        }

    }
}
