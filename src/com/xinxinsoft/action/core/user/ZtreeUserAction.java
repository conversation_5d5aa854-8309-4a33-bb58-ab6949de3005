package com.xinxinsoft.action.core.user;

import java.io.UnsupportedEncodingException;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.xinxinsoft.service.core.user.StructureOfPersonnelService;
import org.apache.struts2.ServletActionContext;

import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.core.user.ZtreeUserService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.task.PreinvApplyImportTask;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.common.DefaultAction;
import com.xinxinsoft.utils.easyh.JSONHelper;

public class ZtreeUserAction extends DefaultAction {
	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	private ZtreeUserService service;
	private AttachmentService attachmentService;
	private StructureOfPersonnelService sopSevvice;
	/**
	 * 人员
	 */
	protected SystemUser user = (SystemUser) ServletActionContext.getRequest().getSession()
			.getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser());

	public AttachmentService getAttachmentService() {
		return attachmentService;
	}

	public void setAttachmentService(AttachmentService attachmentService) {
		this.attachmentService = attachmentService;
	}

	private PreinvApplyImportTask preinvApplyImportTask;

	public PreinvApplyImportTask getPreinvApplyImportTask() {
		return preinvApplyImportTask;
	}

	public void setPreinvApplyImportTask(PreinvApplyImportTask preinvApplyImportTask) {
		this.preinvApplyImportTask = preinvApplyImportTask;
	}

	public StructureOfPersonnelService getSopSevvice() {
		return sopSevvice;
	}

	public void setSopSevvice(StructureOfPersonnelService sopSevvice) {
		this.sopSevvice = sopSevvice;
	}

	/**
	 * 查询部门
	 */
	public void getZtree() {

		/*
		 * node.ID = dept.Department_No; node.Name = dept.Department_Name;
		 * node.P_ID = dept.Department_Parent_No; node.IsParent =
		 * Request.QueryString["SelectContent"] != "company"; node.NoCheck =
		 * Request.QueryString["SelectContent"] == "employee"; node.IconOpen =
		 * "../zTree_v3-master/css/zTreeStyle/img/diy/1_open.png";
		 * node.IconClose =
		 * "../zTree_v3-master/css/zTreeStyle/img/diy/1_close.png"; node.Icon =
		 * "../zTree_v3-master/css/zTreeStyle/img/diy/1_close.png"; node.Data =
		 * dept;
		 */
		/*
		 * 
		 * 选择部门 单选 radio_department 只查询部门不查询 人员 选择人员 单选 radio_personnel 查询人员和部门
		 * 人员
		 */

		String type = getString("type");
		List<Map<String, String>> sd = service.getZtree(type);
		Map<String, String> map = new HashMap<>();
		map.put("id","1000");
		map.put("name","常用联系人");
		map.put("pid","0");
		map.put("isParent","true");
		map.put("nocheck","true");
		sd.add(map);
		putIcon(sd);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(sd));
	}

	/**
	 * 根据公司编码查询人员树
	 */
	public void getZtreeByCompanyCode() {
		String type = getString("type");
		String companyCode = getString("companyCode");
		List<Map<String, String>> sd = service.getZtreeByCompanyCode(type, companyCode);

		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(sd));
	}

	/**
	 * 查询常用联系人
	 */
	public void getZtreeTwo() {
		String type = getString("type");
		List<Map<String, String>> sd = service.getZtreeTwo(type);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(sd));
	}

	/***
	 * 根据角色名称查询：
	 */
	public void selectZtree() {

		String ROLE = getString("ROLE");
		List<Map<String, String>> sd = service.selectZtreeByUname(ROLE);
		putIcon(sd);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(sd));
	}

	public void sonSelectByUId() {
		String id = getString("id");
		String companycode = getString("companycode");
		List<Map<String, String>> sd = service.SelectZtreeByUId(id, user,companycode);
		putIcon(sd);
		writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));
	}

	public void sonSelectByUserId() {
		try {
			String id = getString("id");
			String userId = getString("userId");
			SystemUser user =sopSevvice.querUsers(Integer.parseInt(userId));
			List<Map<String, String>> sd = sopSevvice.SelectZtreeByUId(id, user);
			putIcon(sd);
			writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));
		} catch (ParseException e) {
			writeText("error");
		}
	}


	public void putIcon(List<Map<String, String>> data) {
		for (Map<String, String> map : data) {
			if ("true".equals(map.get("isParent"))) {
				map.put("icon", "images/zuzhijigou.png");
			} else {
				map.put("icon", "images/renyuan.png");
			}
		}
	}

	/**
	 * 转账人员架构树信息
	 */
	public void TransfergetUser() {
		Integer id = getInteger("id");
		String type = getString("type");

		/**
		 * Number：该字段用于区分模块  根据模块需要修改查询条件
		 * 1：高风险业务
		 */
		String Number = getString("T");
		if (Number==null){
			Number = "0";
		}
		List<Map<String, String>> sd=null;
		if(id==1000){
			sd=service.getUserId2(id,"",user.getRowNo());
		}else{
			sd = service.TransfergetUser(id, type,user.getRowNo(),Number);
		}
		putIcon(sd);
		writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));
	}

	// =================================================

	public void delete() {
		String ma = getString("indexid");

		String[] json = ma.split(",");
		if (json.length > 0) {
			for (int i = 0; i < json.length; i++) {
				System.out.println(json[i]); // 得到 每个对象中的属性值
			}
		}

	}

	public void queryatt() {
		List<Map<String, String>> map = service.queryarr(getId());
		Write(JSONHelper.SerializeWithNeedAnnotation(map));
	}

	public void deletsurface() {
		try {
			Integer i = getId();
			service.deletsurface(i);
		} catch (Exception e) {
			e.printStackTrace();
		}

	}

	/**
	 * 根据id 查询 人员信息
	 */
	public void getUserId() {
		Integer id = getInteger("id");
		String type = getString("type");

		List<Map<String, String>> sd = service.getUserId(id, type);
		writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));

	}

	/**
	 * 根据id 查询 人员信息
	 */
	public void getUserId2() {
		Integer id = getInteger("id");
		String type = getString("type");

		List<Map<String, String>> sd = service.getUserId2(id, type, user.getRowNo());
		// System.out.println(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));
		writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));

	}

	/**
	 * 根据名字模糊查询
	 * 
	 * @param name
	 * @return
	 * @throws UnsupportedEncodingException
	 */
	public void getUserName() throws UnsupportedEncodingException {
		String name = getString("name");
		List<Map<String, String>> sd = service.getUserName(name);

		writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));

	}

	/**
	 * 根据角色和名字查询人员
	 */
	public void getUserByNameAndRole() {
		String name = getString("name");
		String id = getString("id");
		List<Map<String, String>> sd = service.SelectZtreeByUId(id, user,null);
		List<Map<String, String>> result = new ArrayList<>();
		for (Map<String, String> map : sd) {
			for (String obj : map.values()) {
				if (obj.indexOf(name) != -1) {
					result.add(map);
				}
			}

		}
		writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(result));
	}

	/**
	 * 根据公司编码和名字查询
	 */
	public void getUserByNameAndCompany() {
		String name = getString("name");
		String company = getString("company");

		List<Map<String, String>> sd = service.getUserNameByCompany(name, company);

		writeText(JSONHelper.SerializeWithNeedAnnotationDateFormats(sd));
	}

	/**
	 * 清理不需要的部门信息
	 */
	public void setDept() {
		List<Map<Object, Object>> showDeptList = service.getShowDept();

		CleerDept(showDeptList);
	}

	/**
	 * 循环清理
	 * 
	 * @param depts
	 */
	public void CleerDept(List<Map<Object, Object>> depts) {
		String DeptIDS = "";
		String pDeptIDS = "";
		for (int i = 0; i < depts.size(); i++) {
			if (i + 1 == depts.size()) {
				DeptIDS += depts.get(i).get("DEPARTMENT_NO").toString();
				pDeptIDS += depts.get(i).get("DEPARTMENT_PARENT_NO").toString();
			} else {
				DeptIDS += depts.get(i).get("DEPARTMENT_NO").toString() + ",";
				pDeptIDS += depts.get(i).get("DEPARTMENT_PARENT_NO").toString() + ",";
			}

		}
		if (!"".equals(DeptIDS)) {
			int result = service.updateDeptVisible(1, DeptIDS); // 修改状态
			System.out.println("修改条数:" + result);

		}
		// 判断父级ID是否为空，不为空则把父ID视为ID查询
		if (!"".equals(pDeptIDS)) {
			List<Map<Object, Object>> getDeptList = service.getDeptByIds(pDeptIDS);
			CleerDept(getDeptList);
		}
		writeText("OK");

	}

	public ZtreeUserService getService() {
		return service;
	}

	public void setService(ZtreeUserService service) {
		this.service = service;
	}

}
