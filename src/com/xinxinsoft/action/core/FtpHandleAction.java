package com.xinxinsoft.action.core;

import java.io.*;
import java.lang.reflect.Field;
import java.net.URLEncoder;
import java.util.*;
import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

import com.google.gson.JsonObject;
import com.itextpdf.text.*;
import com.itextpdf.text.pdf.PdfWriter;
import com.xinxinsoft.action.groupUpdateAction.GroupCustomersAction;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.entity.sys.fileStorage.StorageCfg;
import com.xinxinsoft.sendComms.unitService.GroupInfoSrv;
import com.xinxinsoft.service.core.json.JSONObject;
import com.xinxinsoft.service.groupcustomer.GroupCustomerService;
import com.xinxinsoft.utils.DocConverter;
import com.xinxinsoft.utils.easyh.JSONHelper;
import org.apache.commons.lang.StringUtils;
import org.apache.struts2.ServletActionContext;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.FtpUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.common.FileUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

public class FtpHandleAction extends BaseAction {
    @Resource(name = "GroupCustomerService")
    GroupCustomerService groupCustomerService;

    private static Logger logger = LoggerFactory.getLogger(FtpHandleAction.class);
    /**
     *
     */
    private static final long serialVersionUID = 12213L;

    private File uploadify;// 上传文件file对象
    private String uploadifyFileName;// 上传文件名
    private String uploadifyContentType;// 上传文件类型

    /**
     * 附件service
     */
    private AttachmentService attachmentService;

    private File file;

    private String fileFileName;
    private String fileContentType;
    private String savePath;
    private ResourceBundle s = ResourceBundle.getBundle("WebService-config");
    private String audit = s.getString("AUDIT_INTERS_FJ_SWITCH");
    //页面下载提示文件名
    private String fileName;

    public String getFileFileName() {
        return fileFileName;
    }

    public void setFileFileName(String fileFileName) {
        this.fileFileName = fileFileName;
    }

    public String getFileContentType() {
        return fileContentType;
    }

    public void setFileContentType(String fileContentType) {
        this.fileContentType = fileContentType;
    }

    public String getSavePath() {
        return savePath;
    }

    public void setSavePath(String savePath) {
        this.savePath = savePath;
    }

    public AttachmentService getAttachmentService() {
        return attachmentService;
    }

    public void setAttachmentService(AttachmentService attachmentService) {
        this.attachmentService = attachmentService;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    /**
     * 附件上传：
     */
    /**
     * 附件上传：
     */
    //
    public void uploadFile() {
        // 先将文件上传到服务器，放回参数至页面
        // 页面点击保存时，保存数据
        String type=getString("typeOne");
        try {
            //判断上传文件的名称是否为空
            if (file != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                String  ftpUrl="";
                StorageCfg storageCfg= attachmentService.queryStorageCfg();
                if("AUDITWORKSHEET".equals(type)){
                    //ftpUrl=FileUpload.getFtpURL_auditWorksheet()+urlDate;
                    ftpUrl=storageCfg.getFileName()+FileUpload.getFtpURL_auditWorksheet()+urlDate;
                }else{
                    //ftpUrl=FileUpload.getFtpURL()+urlDate;
                    ftpUrl=storageCfg.getFileName()+urlDate;
                  //ftpUrl="D:\\20210204\\";
                }
                File headPath = new File(ftpUrl);//获取文件夹路径
                if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                // fileFileName= new String(fileFileName.getBytes(), "UTF-8");
                String pixstr =FileUpload.getFilePix(fileFileName);
                if(StringUtils.isEmpty(pixstr)){
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, file, time + pixstr)) {

                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate+time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(fileFileName);
                    attachmentEntity.setVersion(storageCfg.getId());
                    attachmentEntity.setUploadUser(user);
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    writeText(attachmentId.toString());

                    ///审计接口调用
                    if("start".equals(audit)){
                        String request= DateUtil.getIpAddr(this.getRequest());
                        ///审计接口调用
                        CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                    }
                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }

        } catch (Exception e) {
            e.printStackTrace();
            writeText("0");
        }

    }

    public void orderUploadFile() {
        // 先将文件上传到服务器，放回参数至页面
        // 页面点击保存时，保存数据
        Map<String,Object> mapJson = new HashMap<>();
        String type=getString("typeOne");
        String fileExplain=getString("fileExplain");
        try {
            //判断上传文件的名称是否为空
            if (file != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
                String  ftpUrl="";
                StorageCfg storageCfg= attachmentService.queryStorageCfg();
                if("AUDITWORKSHEET".equals(type)){
                    //ftpUrl=FileUpload.getFtpURL_auditWorksheet()+urlDate;
                    ftpUrl=storageCfg.getFileName()+FileUpload.getFtpURL_auditWorksheet()+urlDate;
                }else{
                    //ftpUrl=FileUpload.getFtpURL()+urlDate;
                    ftpUrl=storageCfg.getFileName()+urlDate;
                    //ftpUrl="D:\\20210204\\";
                }
                File headPath = new File(ftpUrl);//获取文件夹路径
                if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                // fileFileName= new String(fileFileName.getBytes(), "UTF-8");
                String pixstr =FileUpload.getFilePix(fileFileName);
                if(StringUtils.isEmpty(pixstr)){
                    //writeText("0");
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("userName","");
                    mapJson.put("msg","文件上传失败");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return;
                }

                if (FileUpload.upload(ftpUrl, file, time + pixstr)) {

                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate+time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(fileFileName);
                    attachmentEntity.setVersion(storageCfg.getId());
                    attachmentEntity.setUploadUser(user);
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    //writeText(attachmentId.toString());
                    mapJson.put("code",1);
                    mapJson.put("data",attachmentEntity);
                    mapJson.put("userName",user.getEmployeeName());
                    mapJson.put("msg","文件上传成功");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    ///审计接口调用
                    if("start".equals(audit)){
                        String request= DateUtil.getIpAddr(this.getRequest());
                        ///审计接口调用
                        CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                    }
                } else {
                    //writeText("0");
                    mapJson.put("code",-1);
                    mapJson.put("data","");
                    mapJson.put("userName","");
                    mapJson.put("msg","文件上传失败");
                    Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                    return;
                }
            } else {
                //writeText("0");
                mapJson.put("code",-1);
                mapJson.put("data","");
                mapJson.put("userName","");
                mapJson.put("msg","文件上传失败");
                Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
                return;
            }
        } catch (Exception e) {
            e.printStackTrace();
            //writeText("0");
            mapJson.put("code",-1);
            mapJson.put("data","");
            mapJson.put("userName","");
            mapJson.put("msg","文件上传失败");
            Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(mapJson));
        }

    }
    /**
     * 附件上传：
     * 上传前端指定的文件
     * gcy
     * 返回json信息(附件id，大小，名字)
     */
    public void uploadFiles() {
        // 先将文件上传到服务器，放回参数至页面
        // 页面点击保存时，保存数据
        String type = getString("typeOne");
        try {
            //判断上传文件的名称是否为空
            if (file != null) {
                //获取毫秒数
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                String ftpUrl = "";
                StorageCfg storageCfg = attachmentService.queryStorageCfg();
                if ("AUDITWORKSHEET".equals(type)) {
                    //ftpUrl=FileUpload.getFtpURL_auditWorksheet()+urlDate;
                    ftpUrl = storageCfg.getFileName() + FileUpload.getFtpURL_auditWorksheet() + urlDate;
                } else {
                    //ftpUrl=FileUpload.getFtpURL()+urlDate;
                    ftpUrl = storageCfg.getFileName() + urlDate;
                    //ftpUrl="D:\\20210204\\";
                }
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                // fileFileName= new String(fileFileName.getBytes(), "UTF-8");
                String pixstr = FileUpload.getFilePix(fileFileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }
//                String pix = null;
//                String[] n = fileFileName.split("\\.");
//                if(n!=null && n.length>0){
//                    pix =n[n.length-1];
//                }
                if (FileUpload.upload(ftpUrl, file, time + pixstr)) {
                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(fileFileName);
                    attachmentEntity.setVersion(storageCfg.getId());
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    //System.out.println("附件id" +attachmentId.toString());
                    JSONObject json=new JSONObject();
                    json.put("attachmentId",attachmentId);
                    json.put("fileFileName",fileFileName);
                    json.put("pixstr",pixstr);
                    json.put("file",file.length() / (1024.0));
                    //writeText(attachmentId.toString());
                    Write(json.toString());
                    ///审计接口调用
                    if ("start".equals(audit)) {
                        String request = DateUtil.getIpAddr(this.getRequest());
                        ///审计接口调用
                        CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                    }
                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }
        } catch (Exception e) {
            e.printStackTrace();
            writeText("0");
        }

    }

    /**
     * 附件上传：
     */
    //
    public void uploadFileTwo() {
        System.out.println("运行");
        // 先将文件上传到服务器，放回参数至页面
        // 页面点击保存时，保存数据
        String type = getString("typeOne");
        try {
            //判断上传文件的名称是否为空
            if (file != null) {
                //获取毫秒数showCorrectClaimForFunds
                Long time = System.currentTimeMillis();
                //根据当天日期生成文件夹：名称：20480
                String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
                String ftpUrl = "";
                StorageCfg storageCfg = attachmentService.queryStorageCfg();
                if ("AUDITWORKSHEET".equals(type)) {
                    //ftpUrl=FileUpload.getFtpURL_auditWorksheet()+urlDate;
                    ftpUrl = storageCfg.getFileName() + FileUpload.getFtpURL_auditWorksheet() + urlDate;
                } else {
                    ftpUrl = storageCfg.getFileName() + urlDate;
                }
                File headPath = new File(ftpUrl);//获取文件夹路径
                if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                    headPath.mkdirs();
                }
                // fileFileName= new String(fileFileName.getBytes(), "UTF-8");
                String pixstr = FileUpload.getFilePix(fileFileName);
                if (StringUtils.isEmpty(pixstr)) {
                    writeText("0");
                }

                if (FileUpload.upload(ftpUrl, file, time + pixstr)) {

                    final Attachment attachmentEntity = new Attachment();
                    attachmentEntity.setAttachmentName(time + pixstr);// 防重名
                    attachmentEntity.setAttachmentUrl(urlDate + time + pixstr);
                    attachmentEntity.setUploadDate(new Date());
                    attachmentEntity.setRealName(fileFileName);
                    attachmentEntity.setVersion(storageCfg.getId());
                    attachmentEntity.setUploadUser((SystemUser) this
                            .getRequest()
                            .getSession()
                            .getAttribute(
                                    SystemConfig.instance().getSessionItems()
                                            .getCurrentLoginUser()));
                    String attachmentId = this.attachmentService
                            .addEntity(attachmentEntity);
                    writeText(attachmentId.toString());

                    ///审计接口调用
                    if ("start".equals(audit)) {
                        String request = DateUtil.getIpAddr(this.getRequest());
                        ///审计接口调用
                        CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
                    }
                } else {
                    writeText("0");
                }
            } else {
                writeText("0");
            }

        } catch (Exception e) {
            e.printStackTrace();
            writeText("0");
        }

    }

    /**
     * 删除附件
     */
    public void deleteFile() {

        String id = getString("id");

        try {
            this.attachmentService.delAttachment(id, this.getRequest());
            Write("删除成功");
        } catch (Exception e) {
            // TODO Auto-generated catch block
            e.printStackTrace();
            Write("删除失败");
        }

    }

    public void previewArea(){
        try{
            String id = getString("id");//附件id
            String suffix=getString("suffix");//后缀名
            HttpServletResponse response = ServletActionContext.getResponse();
            final Attachment entity = attachmentService.getAttachmentById(id);
            StorageCfg storageCfg = attachmentService.queryStorageCfgById(entity.getVersion());
            String path="";
            if(suffix.equals("pdf")||suffix.equals("PDF")){
                //pdf
                path = storageCfg.getFileName() + entity.getAttachmentUrl();
                response.setContentType("application/pdf");
                FileInputStream in = new FileInputStream(path);
                OutputStream out = response.getOutputStream();
                byte[] b = new byte[1024];
                while ((in.read(b)) != -1) {
                    out.write(b);
                }
                out.flush();
                in.close();
                out.close();
            }else if(suffix.equals("doc")||suffix.equals("DOC")||suffix.equals("docx")||suffix.equals("DOCX")){
                //Word文档
                String url = storageCfg.getFileName() + entity.getAttachmentUrl();
                String splitUrl = url.substring(0, url.lastIndexOf("."));
                DocConverter converter = new DocConverter(splitUrl + "."+suffix, "", 0);
                converter.converdTp();
                path = storageCfg.getFileName() + entity.getAttachmentUrl();
                response.setContentType("application/pdf");
                FileInputStream in = new FileInputStream(path);
                OutputStream out = response.getOutputStream();
                byte[] b = new byte[1024];
                while ((in.read(b)) != -1) {
                    out.write(b);
                }
                out.flush();
                in.close();
                out.close();
                File file = new File(splitUrl + ".pdf");
                File file1 = new File(splitUrl + "(1).pdf");
                File file2 = new File(splitUrl + ".swf");
                file.delete();
                file1.delete();
                file2.delete();
            }else{
                //图片
                String url = storageCfg.getFileName() + entity.getAttachmentUrl();
                String splitUrl = url.substring(0, url.lastIndexOf("."));
                List<String> imageUrlList = new ArrayList();
                imageUrlList.add(url);
                File pdfFile = pdf(imageUrlList, splitUrl+".pdf");
                pdfFile.createNewFile();
                response.setContentType("application/pdf");
                FileInputStream in = new FileInputStream(splitUrl+".pdf");
                OutputStream out = response.getOutputStream();
                byte[] b = new byte[1024];
                while ((in.read(b)) != -1) {
                    out.write(b);
                }
                out.flush();
                in.close();
                out.close();
                File file = new File(splitUrl + ".pdf");
                file.delete();
            }
        }catch (Exception e){
            logger.error("附件预览错误信息："+e.getMessage(),e);
        }
    }

    /**
     * gcy
     * 图片转pdf
     * 根据前端传过来的图片id集合
     * 获取图片地址转换成pdf
     */
    public void addpDf() {
        //前端接受附件id集合
        String attachmentId = getString("attachmentId");
        //判断是否上传了附件,获取前台提交的附件Id；
        String[] fileId = attachmentId.split(",");
        //创建个存放图片地址的集合
        List<String> imageUrlList = new ArrayList();
        //根据附件id循环获取
        String path = "";
        for (int i = 0; i < fileId.length; i++) {
            Attachment attachment = attachmentService.getAttachment(fileId[i]);//根据附件id查询路径
            StorageCfg storageCfg = attachmentService.queryStorageCfgById(attachment.getVersion());//查询实时路径
            path = storageCfg.getFileName() + attachment.getAttachmentUrl();//拼接路径
            imageUrlList.add(path); //添加图片地址到集合
        }
        try {
            //存放pdf文件的路径
            //获取毫秒数
            Long time = System.currentTimeMillis();
            //根据当天日期生成文件夹：名称：
            String urlDate = FileUpload.getDateToString("yyyyMMdd") + "/";
            String ftpUrl = "";
            String url = "";
            StorageCfg storageCfg = attachmentService.queryStorageCfg();
            ftpUrl = storageCfg.getFileName() + urlDate + time + ".pdf";//pdf文件路径
            //ftpUrl = "D:\\" + urlDate + time + ".pdf";//pdf文件路径
            //url = "D:\\" + urlDate;//pdf文件夹路径
            File headPath = new File(url);//获取文件夹路径
            if (!headPath.exists()) {//判断文件夹是否创建，没有创建则创建新文件夹
                headPath.mkdirs();
            }
            String pdfUrl = ftpUrl;//存放路径
            File file = pdf(imageUrlList, pdfUrl);//生成pdf
            file.createNewFile();
            //把pdf文件路径存入合同表
            System.out.println("pdf路径为" + pdfUrl);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    public static File pdf(List<String> imageUrllist, String pdfUrl) {
        //new一个pdf文档
        Document doc = new Document(PageSize.A4, 20, 20, 20, 20);
        try {
            //pdf写入
            PdfWriter.getInstance(doc, new FileOutputStream(pdfUrl));
            //打开文档
            doc.open();
            //遍历集合，将图片放在pdf文件
            for (int i = 0; i < imageUrllist.size(); i++) {
                //在pdf创建一页：此处为每一张图片是pdf文件的一页
                doc.newPage();
                //通过文件路径获取image
                Image png1 = Image.getInstance(imageUrllist.get(i));
                // 获取图片的宽高
                float imageHeight = png1.getScaledHeight();
                float imageWidth = png1.getScaledWidth();
                // 设置页面宽高与图片一致
                Rectangle rectangle = new Rectangle(imageWidth, imageHeight);
                doc.setPageSize(rectangle);
                // 图片居中
                png1.setAlignment(Image.ALIGN_CENTER);
                // 新建一页添加图片
                doc.newPage();
                doc.add(png1);
            }
            doc.close();
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
        //输出流
        File mOutputPdfFile = new File(pdfUrl);
        if (!mOutputPdfFile.exists()) {
            mOutputPdfFile.deleteOnExit();
            return null;
        }
        //反回文件输出流
        return mOutputPdfFile;
    }

    /**
     * 附件下载：
     *
     * @throws Exception
     */
    public void attachmentUpload() throws Exception {
        try {
            //验证文件：
            HttpServletResponse response = ServletActionContext.getResponse();
            String type = getString("typeOne");
            final Attachment entity = attachmentService.getAttachmentById(getString("id"));
            StorageCfg storageCfg = attachmentService.queryStorageCfgById(entity.getVersion());
            String path = "";
            if ("AUDITWORKSHEET".equals(type)) {
                //path = FileUpload.getFtpURL_auditWorksheet() + entity.getAttachmentUrl();
                path = storageCfg.getFileName() + FileUpload.getFtpURL_auditWorksheet() + entity.getAttachmentUrl();
            } else {
				/*//小于到配置文件，其不包含当前配置时间
				if (Integer.parseInt(DateUtil.convertDateToString(entity.getUploadDate(), "yyyyMM")) < Integer.parseInt(FileUpload.getFtpYear())) {
					path = FileUpload.getFtpURL2() + entity.getAttachmentUrl();
				} else {
					path = FileUpload.getFtpURL() + entity.getAttachmentUrl();
				}*/
                path = storageCfg.getFileName() + entity.getAttachmentUrl();
            }
            logger.info("下载文件地址:" + path);

            File file = new File(path);
            if (!file.exists() && !file.isDirectory()) {
                this.Write("error");
            } else {

                byte[] data = FileUtil.toByteArray2(path);
                fileName = URLEncoder.encode(entity.getRealName(), "UTF-8");
                response.reset();
                response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream;charset=UTF-8");
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();

                ///审计接口调用
                if ("start".equals(audit)) {
                    if (getInteger("par") == 1) {
                        String request = DateUtil.getIpAddr(this.getRequest());
                        String un = String.valueOf(this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
                        ///审计接口调用
                        CommLogs.requFlies(un, "0", entity.getRealName(), un, "", request);
                    }
                }
            }
        } catch (Exception e) {
            if ("org.apache.catalina.connector.ClientAbortException".equalsIgnoreCase(e.getClass().getName())) {
                logger.info("ClientAbortException");
            } else {
                throw e;
            }
        }
    }

    /**
     * 一键下单 附件下载
     * @throws Exception
     */
    public void attachmentUploadNew() throws Exception {
        try {
            //验证文件：
            HttpServletResponse response = ServletActionContext.getResponse();
            String type = getString("typeOne");
            final Attachment entity = attachmentService.getAttachmentById(getString("id"));
            StorageCfg storageCfg = attachmentService.queryStorageCfgById(entity.getVersion());
            String path = "";
            if ("AUDITWORKSHEET".equals(type)) {
                //path = FileUpload.getFtpURL_auditWorksheet() + entity.getAttachmentUrl();
                path = storageCfg.getFileName() + FileUpload.getFtpURL_auditWorksheet() + entity.getAttachmentUrl();
            } else {
				/*//小于到配置文件，其不包含当前配置时间
				if (Integer.parseInt(DateUtil.convertDateToString(entity.getUploadDate(), "yyyyMM")) < Integer.parseInt(FileUpload.getFtpYear())) {
					path = FileUpload.getFtpURL2() + entity.getAttachmentUrl();
				} else {
					path = FileUpload.getFtpURL() + entity.getAttachmentUrl();
				}*/
                path = storageCfg.getFileName() + entity.getAttachmentUrl();
            }
            logger.info("下载文件地址:" + path);

            File file = new File(path);
            if (!file.exists() && !file.isDirectory()) {
                this.Write("error");
            } else {

                byte[] data = FileUtil.toByteArray2(path);
                fileName = URLEncoder.encode(entity.getRealName(), "UTF-8");

                String[] split = fileName.split("_");
                String suffix = "."+fileName.substring(fileName.lastIndexOf(".") + 1);//获取后缀名
                //List<GroupCustomer> custInfoQuery = GroupInfoSrv.getInstance().getCustInfoQuery(split[0], "", "", "1", "1");
                GroupCustomer findDBCustomer=groupCustomerService.queryGroup(split[0]);
                String GroupName=findDBCustomer.getGroupName();
                String ara=split[0]+"_"+ GroupName + suffix;
                fileName = URLEncoder.encode(ara, "UTF-8");

                response.reset();
                response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"");
                response.addHeader("Content-Length", "" + data.length);
                response.setContentType("application/octet-stream;charset=UTF-8");
                OutputStream outputStream = new BufferedOutputStream(response.getOutputStream());
                outputStream.write(data);
                outputStream.flush();
                outputStream.close();
                response.flushBuffer();

                ///审计接口调用
                if ("start".equals(audit)) {
                    if (getInteger("par") == 1) {
                        String request = DateUtil.getIpAddr(this.getRequest());
                        String un = String.valueOf(this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getUserLoginName()));
                        ///审计接口调用
                        CommLogs.requFlies(un, "0", entity.getRealName(), un, "", request);
                    }
                }
            }
        } catch (Exception e) {
            if ("org.apache.catalina.connector.ClientAbortException".equalsIgnoreCase(e.getClass().getName())) {
                logger.info("ClientAbortException");
            } else {
                throw e;
            }
        }
    }


    /**
     * 上传文件
     */
    public void upload() {
        try {
            if (uploadify != null) {
                if (FtpUtil.upload(FtpUtil.getFtpURL(), uploadify, uploadifyFileName)) {

                    Write("{fileId:1,url:'" + uploadifyFileName + "',name:'" + uploadifyFileName + "',state:'200'}");
                } else {
                    writeText("ERROR");
                }
            } else {
                writeText("ERROR");
            }

        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
            writeText("ERROR");
        }

    }

    public void swfupload() {
        try {
            if (file != null) {
                if (FtpUtil.upload(FtpUtil.getFtpURL(), file, file.getName())) {
                } else {
                    writeText("ERROR");
                }
            } else {
                writeText("ERROR");
            }

        } catch (Exception e) {
            // TODO: handle exception
            e.printStackTrace();
            writeText("ERROR");
        }
    }

    public void downLoad() throws Exception {

        FtpUtil.download("****************************/FTPUpLoad/凭证管理.html", "E:", 100000);
    }

    public File getUploadify() {
        return uploadify;
    }

    public void setUploadify(File uploadify) {
        this.uploadify = uploadify;
    }

    public String getUploadifyFileName() {
        return uploadifyFileName;
    }

    public void setUploadifyFileName(String uploadifyFileName) {
        this.uploadifyFileName = uploadifyFileName;
    }

    public String getUploadifyContentType() {
        return uploadifyContentType;
    }

    public void setUploadifyContentType(String uploadifyContentType) {
        this.uploadifyContentType = uploadifyContentType;
    }

    public File getFile() {
        return file;
    }

    public void setFile(File file) {
        this.file = file;
    }

}
