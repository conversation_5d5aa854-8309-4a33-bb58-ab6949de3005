package com.xinxinsoft.action.core;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Map;

import com.xinxinsoft.entity.basetype.ProductType;
import com.xinxinsoft.entity.core.Dictionary;
import com.xinxinsoft.entity.core.ProductTypeDictionary;
import com.xinxinsoft.entity.dedicatedFlow.OrderInformation;
import com.xinxinsoft.service.core.DictionaryService;
import com.xinxinsoft.utils.common.DefaultAction;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.easyh.PageRequest;

/**
 * 字典Action
 * <AUTHOR>
 * @date 2016-9-26
 */
public class DictionaryAction extends DefaultAction{

	/**
	 * 
	 */
	private static final long serialVersionUID = 1L;
	
	private DictionaryService dicService;
	private Dictionary dic;
	private ProductTypeDictionary productTypeDictionary;
	
	private List<String> name;
	
	
	
	
	public List<String> getName() {
		return name;
	}

	public void setName(List<String> name) {
		this.name = name;
	}

	public Dictionary getDic() {
		return dic;
	}

	public void setDic(Dictionary dic) {
		this.dic = dic;
	}

	public DictionaryService getDicService() {
		return dicService;
	}

	public void setDicService(DictionaryService dicService) {
		this.dicService = dicService;
	}
	

	public ProductTypeDictionary getProductTypeDictionary() {
		return productTypeDictionary;
	}

	public void setProductTypeDictionary(ProductTypeDictionary productTypeDictionary) {
		this.productTypeDictionary = productTypeDictionary;
	}

	/**
	 * 根据code获取字典
	 */
	public void getDictionaryCode(){
		Dictionary entity = this.dicService.getDictionaryCode(getString("code"));
		String json = JSONHelper.toJson(entity);
		System.out.println(json);
		Write(json);
	}
	/**
	 * 根据code获取字典
	 */
	public void getDictionaryCodeAndType(){
		Dictionary entity = this.dicService.getDictionaryCodeAndType(getString("code"),getString("type"));
		String json = JSONHelper.toJson(entity);
		System.out.println(json);
		Write(json);
	}
	
	/**
	 * 根据Id获取字典
	 */
	public void getDictionaryById(){
		Dictionary entity = this.dicService.getEntityById(getString("id"));
		String json = JSONHelper.toJson(entity);
		System.out.println(json);
		Write(json);
	}	
	
	
	/**
	 * 新增
	 */
	public void addDictionary(){
		
		try {
			if(!"1".equals(getString("father")))
			{			
				Dictionary father=dicService.getEntityById(getString("father"));
			    dic.setFather(father);
		    }
			this.dicService.addEntity(dic);
			writeText("T");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			writeText("F");
		}
	}
	
	/**
	 * 删除
	 */
	public void deleteDictionary(){
		try {
			String id = getString("id");
			this.dicService.deleteEntity(this.dicService.getEntityById(id));
			writeText("T");
		} catch (Exception e) {
			e.printStackTrace();
			writeText("F");
		}
		
	}
	
	/**
	 * 分页
	 * @throws Exception 
	 */
	public void getPageInfo() throws Exception{
		
		Write(this.dicService.getPageInfo(getPageRequest()));
	}
	
	/**
	 * 修改
	 */
	public void updateEntity(){
		
		try {
			
			
			if(!"1".equals(getString("father")))
			{			
				Dictionary father=dicService.getEntityById(getString("father"));
			    dic.setFather(father);
		    }else{
			    dic.setFather(null);
		    }
			this.dicService.updateEntity(dic);
			
			
			writeText("T");
		} catch (Exception e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			writeText("F");
		}
	}
	
	/**
	 * 获取绑定下拉列表的数据
	 */
	public void bindDic(){
		
		List<Dictionary> dicList = this.dicService.getListByType(getString("type"));
		String json = JSONHelper.toJson(dicList);
		Write(json);
		
	}
	/**
	 * 获取所有可以当父字典的
	 */
	public void querySelect(){
		
		List<Map<String, String>> dics=dicService.querySelect(getString("id"));
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(dics));
	}
	/**
	 * 根据类型集合 查询
	 */
	public void queryListName(){
		String typep = this.getString("type");
		List<Map<String, String>> dics=dicService.queryListName(name,typep);
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(dics));
	}
	
	public void getzpcodename(){
		String zpcode = this.getString("zpcode");
		String pcode = this.getString("pcode");
		Write(dicService.queryZpcodeByName(zpcode,pcode));
	}
	
	/**
	 * 根据父字典id 获取
	 */
	public void queryListfID(){
		List<Map<String, String>> dics=dicService.queryListfID(getString("id"));
		Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(dics));
	}
	/**
	 * ProductTypeDictionary分页查询
	 *  
	 */
	public void gainProductTypeDictionaryList(){
		try{
			String pageStr = dicService.gainProductTypeDictionary(getPageRequest());
			this.Write(pageStr);
		}catch (Exception e) {
			e.printStackTrace();
		}
	}
	/**
	 * 修改ProductTypeDictionary
	 *  
	 */
	public String updateProductTypeDictionary(){
		try{
			System.out.println(productTypeDictionary.getTypeUuid());
			System.out.println(productTypeDictionary.getOperationCode());
			System.out.println(productTypeDictionary.getOperationType());
			System.out.println(productTypeDictionary.getProductCode());
			System.out.println(productTypeDictionary.getProductType());
			System.out.println(productTypeDictionary.getOrderLimit());
			System.out.println(productTypeDictionary.getStepLimit());
			
			this.dicService.updateProductTypeDictionary(productTypeDictionary);
			return "productTypedictionaryList";
		}catch (Exception e) {
			e.printStackTrace();
			return "error";
		}
	}
	/**
	 * 查询出所用已经提交的 产品类型 并插入产品类型字典表
	 */
	public void saveProduectTypeDictionary() {
		try{
			List<ProductTypeDictionary> productTypeDictionaryList = new ArrayList<ProductTypeDictionary>();
			List<ProductType> productTypeList =  dicService.queryProduectList();
			List<Dictionary> dictionaryList = dicService.getListByType("OT");
			for(ProductType productType : productTypeList){
				for(Dictionary dictionary : dictionaryList){
					ProductTypeDictionary productTypeDictionary = new ProductTypeDictionary();
					productTypeDictionary.setProductType(productType.getProductName());
					productTypeDictionary.setProductCode(productType.getpCode());
					productTypeDictionary.setOperationCode(dictionary.getCode());
					productTypeDictionary.setOperationType(dictionary.getName());
					productTypeDictionary.setOrderLimit(5);
					productTypeDictionary.setStepLimit(3);
					productTypeDictionaryList.add(productTypeDictionary);
				}
			}
			System.out.println(productTypeDictionaryList.size()+"================");
			if(productTypeDictionaryList.size()>0){
				dicService.saveOrupdate(productTypeDictionaryList);
				System.out.println("=========成了=======");
			}
		}catch (Exception e) {
			e.printStackTrace();
		}
		
	}
	/**
	 * 根据操作类型编码和产品类型编码查询产品类型字典对象
	 * @param ocode
	 * @param pcode
	 * @return json字符串
	 */
	public void findPtdByOcodeAndPcode(){
		try{
			ProductTypeDictionary p = dicService.findPtdByOcodeAndPcode(getString("ocode"),getString("pcode"));
			
			String s = afterNDay(Integer.valueOf(p.getOrderLimit()));
			p.setOrderLimitDay(s);
			Write(JSONHelper.SerializeWithNeedAnnotationDateFormats(p));
		}catch (Exception e) {
			e.printStackTrace();
		}
	}

	public String afterNDay(int n) {
		Calendar c = Calendar.getInstance();
		DateFormat df = new SimpleDateFormat("yyyy-MM-dd");
		c.setTime(new Date());
		c.add(Calendar.DATE, n);
		Date d2 = c.getTime();
		String s = df.format(d2);
		return s;
	}
	
	
	
	
}
