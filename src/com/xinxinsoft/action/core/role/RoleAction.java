package com.xinxinsoft.action.core.role;

import java.io.IOException;
import java.io.PrintWriter;
import java.io.UnsupportedEncodingException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import org.apache.struts2.ServletActionContext;
import org.dom4j.Document;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.opensymphony.xwork2.ActionContext;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.Role;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyTaskService;
import com.xinxinsoft.service.core.role.RoleService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.page.LayuiPage;

public class RoleAction extends BaseAction {
	
	private Role role;
	private RoleService roleService;
	//日志
		private Logger logger=LoggerFactory.getLogger(RoleAction.class);
	/**
	 * 
	 */
	private static final long serialVersionUID = 7384899003322308139L;
	
	public String addRole(){
		roleService.addRole(role);
		//审计日志：
//		SystemUser use =(SystemUser) this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser());
//		CommLogs.requRoleRo(use.getLoginName(), use.getEmployeeName(), "0", "{角色名称："+role.getName()+",中文名称："+role.getCname()+",优先级："+role.getPriority()+",角色描述："+role.getDescn()+"}","2", this.getRequest());
		return "goback";
	}

	public Role getRole() {
		return role;
	}

	public void setRole(Role role) {
		this.role = role;
	}

	public RoleService getRoleService() {
		return roleService;
	}

	public void setRoleService(RoleService roleService) {
		this.roleService = roleService;
	}
	
	//跳转到修改角色
	public String gotoEditRole(){
		ServletRequest request = ServletActionContext.getRequest();
		String id = request.getParameter("id");
		Role systemRole = roleService.getRoleByID(Integer.valueOf(id));
		request.setAttribute("role", systemRole);
		request.setAttribute("value", 1);
		return "display";
	}
	
	//删除角色
	public void deleteRole(){
		ServletRequest request = ServletActionContext.getRequest();
		ServletResponse response = ServletActionContext.getResponse();
		String id = request.getParameter("id");
		try {
			roleService.deleteRole(Integer.valueOf(id));
			//审计日志：
//			SystemUser use =(SystemUser) this.getRequest().getSession().getAttribute(SystemConfig.instance().getSessionItems().getCurrentLoginUser());
//			CommLogs.requRoleRo(use.getLoginName(), use.getEmployeeName(), "0", "角色删除","3", this.getRequest());
//			
			response.getWriter().print("ok");
			response.getWriter().flush();
			response.getWriter().close();
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	//验证英文名是否相同
	public void validateEnName() throws IOException{
		HttpServletRequest req=(HttpServletRequest)ActionContext.getContext().get(ServletActionContext.HTTP_REQUEST);
		HttpServletResponse rep=(HttpServletResponse)ActionContext.getContext().get(ServletActionContext.HTTP_RESPONSE);
		String eName=(String)req.getParameter("eName");
		rep.setHeader("Cache-Control", "no-cache");
		rep.setContentType("text/html");
		PrintWriter out=rep.getWriter();
		if(roleService.validateEnName(eName.trim().toLowerCase())==true){
			out.print("{'isAvailable':'true'}");
		}else{
			out.print("{'isAvailable':'false'}");
		}
	}
	//修改角色
	public String editRole(){
		Role sr = roleService.getRoleByID(role.getId());
		sr.setCname(role.getCname());
		sr.setDescn(role.getDescn());
		sr.setName(role.getName());
		sr.setPriority(role.getPriority());//角色优先级标识
		sr.setRole_level(role.getRole_level());
		roleService.amendRole(sr);
		return "goback";
	}
	
	//按条件分页查询角色
	public void searchRoleByCondition() throws UnsupportedEncodingException{
		ActionContext ct = ActionContext.getContext();
		HttpServletRequest request=(HttpServletRequest)ct.get(ServletActionContext.HTTP_REQUEST);
		String eName = "";
		String cName = getString("cName");
		Integer pageSize = Integer.parseInt(getString("pageSize")); //获得每页数据最大量  
		Integer pageNo =  Integer.parseInt(getString("pageNo")); //获得当前页数
		
		//总数据量设置
		 int count= roleService.getRoleCountByCondition(eName, cName);
	//	System.out.println("共"+totalPage+"页");
		 LayuiPage page = new LayuiPage(pageNo,pageSize);
		 try {
			 page.setData(roleService.creReleaseInfo(roleService.getRoleByCondition(page.getPageNo(), page.getPageSize(), eName, cName)));
			 page.setCount(count);
			 page.setCode(0);
			 logger.info("searchRoleByCondition：成功");
		 } catch (Exception e) {
			page.setCode(1);
			logger.info(e.getMessage());
		}
		 this.Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
	}
	
	
	//角色查询
	public void loadAllRoleForRoleItem() throws UnsupportedEncodingException{

		String rows=getString("rows");
		String page=getString("page");
		String order=getString("sidx");
		String orderType=getString("sord");
		String name=getString("name");
		String str = "";
		if(name!=null&&!name.equals("")){
			str = java.net.URLDecoder.decode(name, "UTF-8");
		}else{
			str = null;
		}
		List lst=roleService.loadAllRoleForRoleItem(str, order, orderType, page, rows);
		JSONArray js=new JSONArray();
		List result=(List)lst.get(3);
		for(Object obj1:result){
			Role news=(Role)obj1;
			JSONObject obj=new JSONObject();
			obj.put("id", news.getId());
			obj.put("cell",generateJsonString(news));
			js.add(obj);
		}
		writeText("{\"page\":"+lst.get(0)+",\"total\":"+lst.get(1)+",\"records\":"+lst.get(2)+",\"rows\":"+js.toString()+"}");

	}
	private String generateJsonString(Role role){
		StringBuffer sb=new StringBuffer();
		sb.append("[")
		.append("\"")
		.append(nvlToString(role.getName())).append("\"")
		.append(",\"").append(nvlToString(role.getCname())).append("\"")
		.append(",\"").append(role.getIsSystemRole()==true?"是":"否").append("\"")
		.append("]");
		return sb.toString();
	}
	private String nvlToString(Object obj){
		if(null==obj){
			return "";
		}else{
			return obj.toString();
		}
	}

	//验证英文名是否相同
	public void loadMenuByRoleName() throws IOException{
			HttpServletRequest req=(HttpServletRequest)ActionContext.getContext().get(ServletActionContext.HTTP_REQUEST);
			HttpServletResponse rep=(HttpServletResponse)ActionContext.getContext().get(ServletActionContext.HTTP_RESPONSE);
			Integer id=Integer.valueOf(req.getParameter("id"));
			rep.setHeader("Cache-Control", "no-cache");
			rep.setContentType("text/html");
			PrintWriter out=rep.getWriter();
			out.print(roleService.loadMenuByRoleName(id));
		}
	
	//验证英文名是否相同
	public void updateRoleMenu() throws IOException{
			HttpServletRequest req=(HttpServletRequest)ActionContext.getContext().get(ServletActionContext.HTTP_REQUEST);
			HttpServletResponse rep=(HttpServletResponse)ActionContext.getContext().get(ServletActionContext.HTTP_RESPONSE);
			Integer id=Integer.valueOf(req.getParameter("id"));
			String menuIds=req.getParameter("menuIds");
			roleService.updateRoleMenu(id, menuIds);
			rep.setHeader("Cache-Control", "no-cache");
			rep.setContentType("text/html");
			PrintWriter out=rep.getWriter();
			out.print("OK");
		}
	
	public void menuTree() throws Exception{
		HttpServletRequest req=(HttpServletRequest)ActionContext.getContext().get(ServletActionContext.HTTP_REQUEST);
		HttpServletResponse resp=(HttpServletResponse)ActionContext.getContext().get(ServletActionContext.HTTP_RESPONSE);
		String roleId = req.getParameter("roleId");
		Document doc = roleService.loadMenuByRoleName_(Integer.parseInt(roleId));
		String content = doc.asXML();
//		System.out.println(content);
		resp.setContentType("text/xml; charset=UTF-8");
		resp.setCharacterEncoding("utf-8");
		resp.getWriter().write(content);
	}
	
	public void getPt(){
		String priorityId = "";
		try{
			int priority = getInteger("priority");
			priorityId = roleService.getPt(priority);
			Write(priorityId);
		}catch(Exception e){
			e.printStackTrace();
			Write(priorityId);
		}
		
	}
	
}
