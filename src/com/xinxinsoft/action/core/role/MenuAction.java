package com.xinxinsoft.action.core.role;

import org.apache.commons.lang.StringUtils;
import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.core.MenuItem;
import com.xinxinsoft.service.core.role.MenuService;

/**
 * 菜单管理：
 * <AUTHOR>
 *
 */
public class MenuAction extends BaseAction{
	
	private static final long serialVersionUID = 4233785779104014577L;
	
	private MenuService menuService;

	public MenuService getMenuService() {
		return menuService;
	}

	public void setMenuService(MenuService menuService) {
		this.menuService = menuService;
	}
	/**
	 * 查询所以菜单：
	 */
	public void selectAllMenu(){
		String jsonstr = menuService.selectAllMenu();
		this.Write(jsonstr);
	}
	
	/**
	 * 获取没被禁用的父菜单：
	 */
	public void getParent(){
		String jsonstr = menuService.queryParnetMenu();
		this.Write(jsonstr);
	}
	/**
	 * 根据Id查询菜单：
	 */
	public void findeMenuById(){
		String id=this.getString("id");
		if(!StringUtils.isEmpty(id)){
			String jsonstr = menuService.queryMenuById(Integer.valueOf(id));
			this.Write(jsonstr);
		}
	}
	
	/**
	 *保存：菜单
	 */
	public void addMenu(){
		String menuName=this.getString("menuName");//菜单名称：
		String parentId=this.getString("parentId");//父菜单Id：
		String isMenu=this.getString("iSMenu");//是否启用：还是禁用
		String action=this.getString("action");//子菜单的页面访问路径：
		String iconImage=this.getString("iconImage");//父级菜单的图标名称
		String menuId= this.getString("menuId");
		String isSaveOrUpdate= this.getString("isSaveOrUpdate");
		//----------------请求的参数判断：是否为空--------------------
		if("-1".equals(parentId)){
			if(StringUtils.isEmpty(iconImage)){
				this.Write("error_img");
			}
		}else{
			if(StringUtils.isEmpty(action)){
				this.Write("error_a");
			}
		}
		if(StringUtils.isEmpty(menuName)){
			this.Write("error_n");
		}
		if("save".equals(isSaveOrUpdate)){
			//判断菜单名称是否存在：
			if(menuService.isExistsMenuName(menuName)){
				this.Write("error_mnIs");
			}
			//页面路径：
			if(menuService.isExistsAction(action)){
				this.Write("error_aIs");
			}
		}
		//----------------------------end-------------------------------
		MenuItem minew = new MenuItem();
		if("update".equals(isSaveOrUpdate)){
			//根据Id 来判断是更新还是添加：
			if(!StringUtils.isEmpty(menuId)){
				minew.setId(Integer.valueOf(menuId));
			}
		}
			//-1表示没又选择父级菜单，表示是父菜单：不等于-1，就表示是子菜单：action 是访问路径：
			if("-1".equals(parentId)){
				minew.setIconImage(iconImage.trim());
				minew.setLayer(1);//父级菜单就为1：
			}else{
				//根据ID查询：
				MenuItem miPar = menuService.findByEnintyId(Integer.valueOf(parentId));
				
				minew.setAction(action.trim());
				minew.setLayer(2);//子级菜单就为2：
				minew.setParent(miPar);//保存父级的ID
			}
			
			minew.setEnable(("1".equals(isMenu)?true:false));//是否为禁用菜单：如果为禁用，权限管理就应该没有：
			minew.setName(menuName.trim());
			
		MenuItem m = menuService.saveOrUpdate(minew);
		if(m.getId()>0){
			this.Write("success");
		}else{
			this.Write("error");
		}
	}
	/**
	 * 根据Id 删除：
	 */
	public void deleteMenu(){
		String id= this.getString("id");
		if(!StringUtils.isEmpty(id)){
			this.Write(menuService.queryDeleteById(Integer.valueOf(id)));
		}else{
			this.Write("error_i");
		}
		
	}
}
