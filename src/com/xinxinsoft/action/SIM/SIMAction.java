package com.xinxinsoft.action.SIM;

import com.google.gson.GsonBuilder;
import com.xinxinsoft.action.BaseAction;

import com.xinxinsoft.entity.SIM.*;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_process;
import com.xinxinsoft.entity.publicEntity.Bpms_riskoff_task;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils;
import com.xinxinsoft.service.PublicService.Bpms_riskoff_service;
import com.xinxinsoft.service.SIM.SIMService;
import com.xinxinsoft.service.claimForFunds.ClaimForFundsService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.BeanUtils;
import com.xinxinsoft.utils.ExcelUtil;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.common.FileUtil;
import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.page.LayuiPage;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.apache.struts2.ServletActionContext;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.*;

public class SIMAction extends BaseAction {

    @Resource(name = "SIMService")
    private SIMService SIMService;

    private File file1;

    @Resource(name = "JBPMUtil")
    private JbpmUtil jbpmUtil;
    @Resource(name = "WaitTaskService")
    private WaitTaskService service;
    @Resource(name = "TransferJBPMUtils")
    private TransferJBPMUtils transferJBPMUtils;//TransferJBPMUtils
    @Resource(name = "Bpms_riskoff_service")
    private Bpms_riskoff_service taskService;
    @Resource(name = "SystemUserService")
    private SystemUserService systemUserService;
    @Resource(name = "ClaimForFundsService")
    private ClaimForFundsService claimForFundsService;

    private Logger logger = LoggerFactory.getLogger(SIMAction.class);

    public File getFile1() {
        return file1;
    }

    public void setFile1(File file1) {
        this.file1 = file1;
    }


    public static String getUnlockedNumber() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyyMMddHHmmssSSS");
        String dateString = formatter.format(new Date());
        return dateString;
    }


    /**
     * 导出模板信息
     */
    public void exportSIMSegment() {
        try {
            HttpServletResponse response = ServletActionContext.getResponse();
            HttpServletRequest request = ServletActionContext.getRequest();
            String name = "";
            String filepath = null;

            name = "SIM号段模板";
            filepath = request.getSession().getServletContext().getRealPath("/template/SIMSegment.xlsx");

            byte[] data = FileUtil.toByteArray(filepath);
            String fileName = URLEncoder.encode(name + ".xlsx", "UTF-8");
            response.reset();
            response.setHeader("Content-Disposition", "attachment;filename=\"" + fileName + "\"");
            response.setHeader("Content-Length", data.length + "");
            response.setContentType("application/octet-stream;charset=UTF-8");
            OutputStream stream = new BufferedOutputStream(response.getOutputStream());
            stream.write(data);
            stream.flush();
            stream.close();
            response.flushBuffer();
        } catch (Exception var9) {
            var9.printStackTrace();
        }
    }

    /**
     * SIM号段导入
     */
    public void importSIMSegment() {
        try {
            ExcelUtil excelReader = new ExcelUtil(this.file1);
            InputStream is = new FileInputStream(this.file1);
            Workbook wb = new XSSFWorkbook(is);
            Sheet sheet = wb.getSheetAt(0);
            int column = sheet.getRow(0).getPhysicalNumberOfCells();
            System.out.println("这是列数" + column);
            Map<Integer, Map<Integer, Object>> map = excelReader.readExcelContent();
            List<Map<String, Object>> list = new ArrayList();
            int i;
            HashMap maps;
            for (i = 1; i <= map.size(); ++i) {
                maps = new HashMap();
                maps.put("system", ((Map) map.get(i)).get(0));//归属系统
                maps.put("SIMSegment", ((Map) map.get(i)).get(1));//号段
                maps.put("city", ((Map) map.get(i)).get(2));//市州分公司
                maps.put("start", ((Map) map.get(i)).get(3));//开始号码
                maps.put("end", ((Map) map.get(i)).get(4));//结束号码
                maps.put("makeType", ((Map) map.get(i)).get(5));//制卡类型
                maps.put("makeSize", ((Map) map.get(i)).get(6));//制卡尺寸
                maps.put("state", ((Map) map.get(i)).get(7));//当前状态
                maps.put("quantity", ((Map) map.get(i)).get(8));//数量
                maps.put("SIMTransferTime", ((Map) map.get(i)).get(9));//SIM卡调拨时间
                maps.put("cardTrader", ((Map) map.get(i)).get(10));//卡商
                maps.put("whetherVoice", ((Map) map.get(i)).get(11));//是否语音
                maps.put("whether5G", ((Map) map.get(i)).get(12));//是否5G
                maps.put("ICCID", ((Map) map.get(i)).get(13));//ICCID起止
                maps.put("remarks", ((Map) map.get(i)).get(14));//备注
                list.add(maps);
            }

            String json2 = JSONHelper.SerializeWithNeedAnnotation(list);
            if (map.size() > 0) {
                this.Write(json2);
            } else {
                this.Write("NULL");
            }
        } catch (Exception var11) {
            var11.printStackTrace();
            this.Write("NO");
        }
    }


    /**
     * 根据SIM号段 生成 号码
     */
    public void SIMCreate() {
        try {
            String jsonone = getString("jsonone");
            JSONArray jsonArr = JSONArray.fromObject(jsonone);
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");

            if (jsonone != null) {
                for (int i = 0; i < jsonArr.size(); i++) {
                    JSONObject obj = jsonArr.getJSONObject(i);
                    //添加号段
                    SIMSegment SIMSegment = new SIMSegment();
                    SIMSegment.setSystem(obj.get("system").toString());//归属系统
                    SIMSegment.setSIMSegment(obj.get("SIMSegment").toString()); //号段
                    SIMSegment.setCity(obj.get("city").toString());//市州分公司
                    SIMSegment.setMakeType(obj.get("makeType").toString());//制卡类型
                    SIMSegment.setMakeSize(obj.get("makeSize").toString());//制卡尺寸
                    SIMSegment.setWhetherVoice(obj.get("whetherVoice").toString());//是否语音
                    SIMSegment.setWhether5G(obj.get("whether5G").toString());//是否5G
                    SIMSegment.setAddTime(new Date()); //SIM卡入库时间
                    SIMSegment.setSIMTransferTime(sdf.parse(obj.get("SIMTransferTime").toString()));//SIM卡调拨时间
                    SIMSegment.setCardTrader(obj.get("cardTrader").toString());//卡商
                    SIMSegment.setICCID(obj.get("ICCID").toString());//ICCID起止
                    SIMSegment.setQuantity(obj.get("quantity").toString());//数量
                    //当前状态
                    if (obj.get("state").toString().equals("未分配")) {
                        SIMSegment.setState("0");
                    } else if (obj.get("state").toString().equals("已分配")) {
                        SIMSegment.setState("1");
                    } else if (obj.get("state").toString().equals("已作废")) {
                        SIMSegment.setState("2");
                    }
                    SIMSegment.setRemarks(obj.get("remarks").toString());//备注
                    SIMSegment.setStartNumber(obj.get("start").toString());//开始号码
                    SIMSegment.setEndNumber(obj.get("end").toString());//结束号码
                    this.SIMService.addSIMSegment(SIMSegment);

                    //根据号段 生成 号码
                    for (int j = Integer.valueOf(obj.get("start").toString()); j <= Integer.valueOf(obj.get("end").toString()); j++) {
                        SIMNumber Number = new SIMNumber();
                        Number.setSIMSegment(obj.get("SIMSegment").toString()); //号段
                        Number.setSIMNumber(String.valueOf(j));//号码
                        if (obj.get("state").toString().equals("未分配")) {
                            Number.setState("0");
                        } else if (obj.get("state").toString().equals("已分配")) {
                            Number.setState("1");
                        } else if (obj.get("state").toString().equals("已作废")) {
                            Number.setState("2");
                        }
                        Number.setRank(j);//索引
                        this.SIMService.addSIMNumber(Number);
                    }
                }
            }
            this.Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加SIM卡申请工单
     */
    public void addSIMOrder() {
        String attachmentId = getString("attachmentId");// 附件
        Integer userId = this.getInteger("userId");


        try {
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd");
            String orderNo = "";//申请编码前面的字母
            List<Object[]> sone = SIMService.getbumen(user.getRowNo());
            for (int i = 0; i < sone.size(); i++) {
                orderNo = (String) sone.get(i)[2];
            }

            //保存SIM工单
            SIMWorkOrder workOrder = new SIMWorkOrder();
            workOrder.setOrderNo(orderNo + getStringDate(new Date(), null));//工单编号
            workOrder.setOrderTitle(getString("redListTitle"));// 工单标题
            workOrder.setOrderMemo(getString("orderMemo"));// 需求描述
            workOrder.setGroupCode(getString("lblGroupCoding")); //集团编码
            workOrder.setGroupName(getString("lblGroupName"));//集团名称
            workOrder.setCompanyCode(getString("deptStr"));// 所属地市
            workOrder.setRegion(getString("deptStrP")); //所属区县
            workOrder.setApplicantID(getString("creator"));// 申请人ID
            workOrder.setApplicantName(getString("name"));// 申请人姓名
            workOrder.setApplicantPhone(getString("mobile"));//申请人电话
            workOrder.setApplicationTime(new Date());
            workOrder.setState("0");
            SIMWorkOrder Order = this.SIMService.addSIMOrder(workOrder);

            //保存SIM卡申请信息
            SIMApplyForInfo info = new SIMApplyForInfo();
            info.setOrderNo(Order.getOrderNo());// 工单编号
            info.setSystem(getString("system")); //归属系统
            info.setMakeType(getString("makeType")); //制卡类型
            info.setMakeSize(getString("makeSize"));//制卡尺寸
            info.setQuantity(getString("quantity"));// 号卡数量
            info.setExpectTime(simpleDateFormat.parse(getString("expectTime")));//期望拿卡时间
            info.setInitialTime(simpleDateFormat.parse(getString("initialTime")));//最迟拿卡时间
            info.setOpenCard(getString("openCard"));//累计开卡数
            info.setOpenCardRate(getString("openCardRate") + "%");//开卡率
            info.setLibType(getString("LibType"));//库存卡类型
            info.setLibQuantity(getString("LibQuantity"));//库存卡数量
            info.setWhetherVoice(getString("whetherVoice"));//是否语音
            info.setWhether5G(getString("whether5G"));//是否5G
            info.setAssignedQuantity("0");//已分配数量开始 为0
            this.SIMService.addSIMApplyForInfo(info);

            Map<String, String> map = new HashMap<String, String>();
            map.put("node", "ROLE_DSBM");
            String processId = this.transferJBPMUtils.startTransfer("SimCard", map);

            //附件
            if (!StringUtils.isEmpty(attachmentId) && attachmentId != null) {
                String[] jsontwo = attachmentId.split(",");
                if (jsontwo.length > 0) {
                    for (int i = 0; i < jsontwo.length; ++i) {
                        SingleAndAttachment sa = new SingleAndAttachment();
                        sa.setOrderID(Order.getId());
                        sa.setAttachmentId(jsontwo[i]);
                        sa.setLink(SIMWorkOrder.SIMWORKORDER);
                        this.claimForFundsService.saveSandA(sa);
                    }
                }
            }

            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
            this.taskService.updateBpms_riskoff_task("", 2, task.getId());
            this.taskService.setBpms_riskoff_process(Order.getId(), processId, 1, this.user);
            this.taskService.setBpms_riskoff_task(processId, "发起工单", 2, "SH", "客户经理", this.user.getRowNo(), this.user);
            String taskid = this.taskService.setBpms_riskoff_task(processId, (String) null, 1, "SH", task.getActivityName(), userId, this.user);
            this.commitBackLogData(Order, userId, processId, this.user, taskid);
            this.Write("YES");
        } catch (Exception e) {
            this.Write("NO");
            e.printStackTrace();
            throw new RuntimeException("事务回滚");
        }
    }

    /**
     * 生成待办
     *
     * @param workOrder
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void commitBackLogData(SIMWorkOrder workOrder, Integer userid, String processId, SystemUser user, String
            taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[SIM物联网]" + workOrder.getOrderTitle());
        waitTask.setCreationTime(new Date());//WebRoot\jsp\redList\redRollApproval.jsp handleRedListtwo
        waitTask.setUrl("jsp/SIM/SIMApproval.jsp?id=" + workOrder.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNO=" + workOrder.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("SIM");
        waitTask.setTaskId(workOrder.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }


    /**
     * 生成完成提示待办
     *
     * @param workOrder
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void hintBackLogData(SIMWorkOrder workOrder, Integer userid, String processId, SystemUser user, String
            taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[SIM物联网提示]" + workOrder.getOrderTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/SIM/SIMApproval2.jsp?id=" + workOrder.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNO=" + workOrder.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("SIM");
        waitTask.setTaskId(workOrder.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }


    /**
     * 根据ID查询工单信息
     */
    public void querySIMById() {
        try {
            String id = this.getString("id");
            Map<String, Object> map = this.SIMService.querySIMById(id);
            this.Write((new GsonBuilder()).serializeNulls().setDateFormat("yyyy-MM-dd HH:mm:ss").excludeFieldsWithoutExposeAnnotation().create().toJson(map));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("NO");
        }
    }


    /**
     * 根据工单编号查询申请详情
     */
    public void getSIMByOrderNo() {
        try {
            String orderNo = this.getString("orderNO");
            SIMApplyForInfo Info = this.SIMService.getSIMByOrderNo(orderNo);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(Info));
        } catch (Exception var3) {
            var3.printStackTrace();
            this.Write("ON");
        }
    }

    /**
     * 获取附件消息
     */
    public void fuJian() {
        String id = getString("id");
        String biaoshi = getString("biaoshi");
        List<Map<String, String>> s = SIMService.fuJian(id, biaoshi);
        Write(JSONHelper.Serialize(s));
    }


    /**
     * 流程进行
     */
    public void handleSIM() {
        logger.info("SIM流程进行中");
        try {
            String pid = this.getString("processId");
            String id = this.getString("id");
            String t = this.getString("zhuanshentwo");
            String userid = this.getString("userId");
            String opinion = this.getString("opinion");
            String waitId = this.getString("waitId");
            String taskId = this.getString("TaskId");

            Bpms_riskoff_task Redtask = this.taskService.getBpms_riskoff_task(taskId);
            SIMWorkOrder workOrder = this.SIMService.querySIMById2(id);
            String orderNo = workOrder.getOrderNo();
            SIMApplyForInfo info = SIMService.getSIMByOrderNo(orderNo);

            if (Redtask != null) {
                this.taskService.updateBpms_riskoff_task(opinion, 2, taskId);
            }
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();


            String node = "";

            Integer q = Integer.valueOf(info.getQuantity());//数量
            if ("ROLE_DSDM".equals(t) || "ROLE_SGSYWGLSJL".equals(t)) {
                if (q >= 50000) {
                    node = "大于5W";
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("node", node);
                    this.jbpmUtil.completeTask(task.getId(), map);
                } else {
                    node = "小于等于5W";
                    Map<String, Object> map = new HashMap<String, Object>();
                    map.put("node", node);
                    this.jbpmUtil.completeTask(task.getId(), map);
                }
            } else {
                this.jbpmUtil.completeTask(task.getId(), t);
            }

            String rtaskid = this.taskService.setBpms_riskoff_task(pid, "", 1, "SH", task.getActivityName(), Integer.parseInt(userid), this.user);
            //System.out.println("待办ID==========：" + waitId);
            WaitTask wt = this.service.queryWaitByTaskId(waitId);
            if (wt == null) {
                throw new Error("待办ID==========：" + waitId);
            }
            System.out.println("================处理中开始代办================");
            this.service.updateWait(wt, this.getRequest());
            System.out.println("================处理中结束代办================");
            this.commitBackLogData(workOrder, Integer.parseInt(userid), pid, this.user, rtaskid);
            this.Write("YES");
            System.out.println("流程完成");
        } catch (Error var15) {
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        } catch (Exception var16) {
            var16.printStackTrace();
            this.Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 查询任务信息
     */
    public void queryProcessTrackingById() {
        String id = this.getString("id");
        List<Bpms_riskoff_task> RedList = this.taskService.getPublicEntityTaskList(id);
        this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(RedList));
    }


    /**
     * 完成生成待办
     */
    public void returnHintBackLogData() {
        logger.info("同意,SIM");
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 意见
            String taskId = this.getString("TaskId");
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
            SIMWorkOrder workOrder = this.SIMService.querySIMById2(id);//查询工单信息

            Bpms_riskoff_task Redtask = this.taskService.getBpms_riskoff_task(taskId);
            if (Redtask != null) {
                this.taskService.updateBpms_riskoff_task(opinion, 2, taskId); //修改任务表
            }
            //taskService.updateBpms_riskoff_task(opinion, 2, taskId);     //修改任务表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(workOrder.getApplicantID()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());

                workOrder.setState("1");
                SIMWorkOrder wo = SIMService.updateSIMOrder(workOrder);
                hintBackLogData(wo, process.getCreator_no(), processId, user, rtaskid);// 生成待办
                Write("YES");
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
        } catch (Exception e) {
            logger.info("推送Boss接口错误信息==>" + e);
            e.printStackTrace();
            Write("NO!推送Boss接口出错");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 查询号段信息
     */
    public void querySIMSegment() {
        try {
            String makeType = getString("makeType");
            String makeSize = getString("makeSize");//制卡尺寸
            String whetherVoice = getString("whetherVoice");//是否语音
            String whether5G = getString("whether5G");//是否5G

            List<SIMSegment> simSegment = this.SIMService.querySIMSegment(makeType, makeSize, whetherVoice, whether5G);
            this.Write(JSONHelper.SerializeWithNeedAnnotationDateFormat(simSegment));
        } catch (Exception e) {
            Write("NO");
            e.printStackTrace();
        }
    }

    /**
     * 分配号码
     */
    public void querySIMNumber() {
        try {
            String simSegment = getString("SIMSegment");//号段
            String orderNo = getString("orderNO");//工单编号
            String needNum = getString("needNum");//本次分配数量
            String name = getString("name");//操作人
            String deptStr = getString("deptStr");//操作人地市
            String bossNo = getString("bossNo"); //BOSS工号
            String q = getString("quantity");//数量

            SIMSegment segment = this.SIMService.findBySIMSegment(simSegment, q);//号段
            List<SIMNumber> number = this.SIMService.querySIMNumber(simSegment);//号码
            SIMApplyForInfo info = this.SIMService.getSIMByOrderNo(orderNo);//申请信息
            SIMWorkOrder workOrder = this.SIMService.getWorkOrder(orderNo);//工单信息
            Date date = new Date();//获取当前时间
            String quantity = segment.getQuantity();//获取号段的号码剩余数量
            String infoQ = info.getQuantity();//获取需求数量
            String infoA = info.getAssignedQuantity();//获取已分配数量


            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            if (Integer.valueOf(needNum) <= Integer.valueOf(quantity)) {//判断本次分配数量是否超过该号段剩余号码数量
                if (Integer.valueOf(needNum) <= Integer.valueOf(infoQ) - Integer.valueOf(infoA)) {//判断  本次分配数量 + 已分配数量  是否超过工单的需求数量
                    //分配号码
                    for (int i = 0; i < Integer.valueOf(needNum); i++) {
                        number.get(i).setState("1");//号码状态改为已分配
                        number.get(i).setOrderNo(orderNo); //工单编号
                        number.get(i).setAllocatedTime(date);//当前时间
                        this.SIMService.AssignNumber(number.get(i));
                    }
                    SIMNumber start = this.SIMService.findByDate(sdf.format(date));//获取当前时间第一个
                    SIMNumber end = this.SIMService.findByDate2(sdf.format(date));//获取当前时间最后一个


                    //更改号段数量
                    segment.setQuantity(String.valueOf(Integer.valueOf(quantity) - Integer.valueOf(needNum)));//修改数量
                    segment.setStartNumber(String.valueOf(Integer.valueOf(end.getSIMNumber()) + 1));//更改号段的开始号码
                    this.SIMService.updateSIMSegment(segment);

                    //更改已分配数量
                    int i2 = Integer.valueOf(infoA) + Integer.valueOf(needNum);
                    info.setAssignedQuantity(String.valueOf(i2));
                    this.SIMService.updateInfo(info);

                    //新增一条已分配号段
                    SIMSegment seg = new SIMSegment();
                    seg.setSystem(segment.getSystem());
                    seg.setSIMSegment(segment.getSIMSegment());
                    seg.setCity(workOrder.getCompanyCode());
                    seg.setStartNumber(start.getSIMNumber());
                    seg.setEndNumber(end.getSIMNumber());
                    seg.setMakeType(segment.getMakeType());
                    seg.setMakeSize(segment.getMakeSize());
                    seg.setWhetherVoice(segment.getWhetherVoice());
                    seg.setWhether5G(segment.getWhether5G());
                    seg.setAddTime(segment.getAddTime());
                    seg.setSIMTransferTime(new Date());
                    seg.setCardTrader(segment.getCardTrader());
                    seg.setICCID(segment.getICCID());
                    seg.setQuantity(needNum);
                    seg.setState("1");
                    seg.setRemarks(segment.getRemarks());
                    this.SIMService.addSIMSegment(seg);

                    //记录号段操作信息(分配)
                    SIMRecordInfo sr = new SIMRecordInfo();
                    sr.setSIMSegment(simSegment);//记录分配号段
                    sr.setAllocationCity(workOrder.getCompanyCode());//记录分配地市
                    sr.setStartNumber(start.getSIMNumber());//记录分配开始号码
                    sr.setEndNumber(end.getSIMNumber());//记录分配结束号码
                    sr.setOperator(name);//记录操作人
                    sr.setOperatorNo(bossNo);//记录操作人 BOSS工号
                    sr.setCity(deptStr);//记录操作人地市
                    sr.setOperationTime(new Date());
                    sr.setState("1");//状态 1: 分配
                    this.SIMService.addSIMRecordInfo(sr);
                } else {
                    Write("NO1");
                }
            } else {
                Write("NO2");
            }
            Write("YES");
        } catch (NumberFormatException e) {
            Write("NO");
            e.printStackTrace();
        }
    }

    /**
     * 查询分配信息
     */
    public void queryAssignedInfo() {
        try {
            String orderNo = getString("orderNo");//工单编号

            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            List<SIMNumber> numbers = this.SIMService.findByOrderNo(orderNo);

            Set<String> set = new HashSet<>();
            for (int i = 0; i < numbers.size(); i++) {
                String format = sdf.format(numbers.get(i).getAllocatedTime());
                set.add(format);
            }

            List<Map<String, String>> list = new ArrayList<>();
            for (String s : set) {
                Map map = new HashMap();
                SIMNumber start = this.SIMService.findByDate(s);
                SIMNumber end = this.SIMService.findByDate2(s);

                map.put("SIMSegment", start.getSIMSegment());
                map.put("SIMNumber", start.getSIMNumber() + "-" + end.getSIMNumber());
                list.add(map);
            }

            String serialize = JSONHelper.Serialize(list);
            System.out.println(serialize);
            Write(JSONHelper.Serialize(list));
        } catch (Exception e) {
            Write("NO");
            e.printStackTrace();
        }
    }


    /**
     * 完成流程
     */
    public void complateCorrectClaimForFunds() {
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            String pid = this.getString("processId");   //流程id
            String id = this.getString("id");       //工单id
            String waitId = this.getString("waitId");   //待办id
            String opinion = this.getString("opinion");     //处理意见
            String taskId = this.getString("TaskId");   //任务id
            SIMWorkOrder wo = this.SIMService.querySIMById2(id);    //工单
            WaitTask wt = this.service.queryWaitByTaskId(waitId);       //待办
            Task task = this.jbpmUtil.getTaskService().createTaskQuery().processInstanceId(pid).uniqueResult();     //流程
            Bpms_riskoff_task Redtask = this.taskService.getBpms_riskoff_task(taskId);
            if (Redtask != null) {
                this.taskService.updateBpms_riskoff_task("已确认", 2, taskId);
            }
            if (wt != null) {
                wo.setState("1");
                this.service.updateWait(wt, this.getRequest());     //结束待办
                this.taskService.updateBpms_riskoff_task("已确认", 2, taskId);   //结束任务
                Map<String, Object> map = new HashMap<String, Object>();
                map.put("node", "END");
                this.jbpmUtil.completeTask(task.getId(), map);    //结束流程
                this.taskService.updatebpmsRiskoffProcess(id, 2);
                this.Write("YES");
            } else {
                this.Write("NO");
            }
        } catch (Exception var12) {
            var12.printStackTrace();
            this.Write("NO!推送boss接口失败");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 流程退回
     */
    public void returnClaimData() {
        try {
            String id = getString("id");// 开票id
            String processId = getString("processId");// 流程id
            String waitId = getString("waitId");// 待办id
            String opinion = getString("opinion");// 退回意见
            String taskId = getString("taskId");// 任务表id
            String dangqianrenwu = getString("dangqianrenwu");//获取当前任务
            String city = getString("DataCity");//分配地市
            String name = getString("name");//操作人
            String deptStr = getString("deptStr");//操作人地市
            String bossNo = getString("bossNo"); //BOSS工号
            Bpms_riskoff_process process = taskService.getbpms_riskoff_processBizid(id);
            SIMWorkOrder wo = this.SIMService.querySIMById2(id);    //工单
            SIMApplyForInfo info = this.SIMService.getSIMByOrderNo(wo.getOrderNo());//申请信息
            wo.setState("2");// 修改状态为退回
            SIMWorkOrder wwo = SIMService.updateSIMOrder(wo);
            taskService.updateBpms_riskoff_task(opinion, 2, taskId);     //修改任务表
            Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();// 获取流程任务表信息
            String rtaskid = taskService.setBpms_riskoff_task(processId, "", 1, "SH", task.getActivityName(), Integer.valueOf(wo.getApplicantID()), user);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 查询待办
            if (wt != null) {
                service.updateWait(wt, this.getRequest());
            } else {
                throw new RuntimeException("未查询到待办信息" + waitId);
            }
            handBackLog(wwo, process.getCreator_no(), processId, user, rtaskid);// 生成待办
            Write("YES");
        } catch (Exception e) {
            e.printStackTrace();
            Write("NO");
            throw new RuntimeException("事务回滚");
        }
    }


    /**
     * 生成退回待办
     *
     * @param workOrder
     * @param userid
     * @param processId
     * @param user
     * @param taskid
     */
    public void handBackLog(SIMWorkOrder workOrder, Integer userid, String processId, SystemUser user, String
            taskid) {
        WaitTask waitTask = new WaitTask();
        waitTask.setName("[SIM退回]" + workOrder.getOrderTitle());
        waitTask.setCreationTime(new Date());
        waitTask.setUrl("jsp/SIM/SIMReturn.jsp?id=" + workOrder.getId() + "&processId=" + processId + "&taskId=" + taskid + "&orderNO=" + workOrder.getOrderNo());
        SystemUser USER = this.systemUserService.getUserInfoRowNo(userid);
        waitTask.setState(WaitTask.HANDLE);
        waitTask.setHandleUserId(USER.getRowNo());
        waitTask.setHandleUserName(USER.getEmployeeName());
        waitTask.setHandleLoginName(USER.getLoginName());
        waitTask.setCreateUserId(user.getRowNo());
        waitTask.setCreateUserName(user.getEmployeeName());
        waitTask.setCreateLoginName(user.getLoginName());
        waitTask.setCode("SIM");
        waitTask.setTaskId(workOrder.getId());
        this.service.saveWait(waitTask, this.getRequest());
    }


    /**
     * <方法序号：11 > <方法名：Invalid> <详细描述：作废工单及其审批流程>
     *
     * @Param: []
     * @return: void
     * @Author: gcy
     */
    public void Invalid() {
        try {
            System.out.println("作废执行");
            String id = this.getString("id");
            String waitId = this.getString("waitId");//待办id
            String processId = this.getString("processId");//流程id
            SIMWorkOrder SIMWorkOrder = this.SIMService.querySIMById2(id);
            String redId = SIMWorkOrder.getId();
            //-2作废工单
            SIMWorkOrder.setState("-2");
            this.SIMService.updateSIMOrder(SIMWorkOrder);
            WaitTask wt = service.queryWaitByTaskId(waitId);// 根据待办id查询待办信息
            // 结束当前待办
            if (wt != null) {
                System.out.println("================处理中开始代办================");
                service.updateWait(wt, this.getRequest());
                System.out.println("================处理中结束代办================");
            } else {
                throw new Error("待办ID==========：" + waitId);
            }
            jbpmUtil.deleteProcessInstance(processId);//结束流程
            this.Write("OK");
        } catch (Exception var7) {
            var7.printStackTrace();
            this.Write("NO");
            throw new RuntimeException(" 给事务回滚，自定义");
        }
    }


    /**
     * SIM列表查询
     */
    public void findAllByPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String orderNo = this.getString("orderNo");
            String orderTitle = this.getString("orderTitle");
            String applicantPhone = this.getString("applicantPhone");
            String startTime = this.getString("startTime");
            String endTime = this.getString("endTime");
            String statetwo = this.getString("statetwo");
            String state = this.getString("state");
            if (state != null && !"".equals(state)) {
                page = this.SIMService.findAll(page, orderNo, orderTitle, applicantPhone, startTime, endTime, this.user, statetwo, state);
                String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
                this.Write(json);
            } else {
                this.Write("NO");
            }
        } catch (Exception var12) {
            var12.printStackTrace();
            this.Write("NO");
        }

    }

    /**
     * SIM号段详情查询
     */
    public void findPage() {
        try {
            Integer pageNo = this.getInteger("pageNo");
            Integer pageSize = this.getInteger("pageSize");
            LayuiPage page = new LayuiPage(pageNo, pageSize);
            String SIMSegment = getString("SIMSegment");        //SIM号段
            String makeType = getString("makeType");        //制卡类型
            String makeSize = getString("makeSize");        //制卡尺寸
            String whetherVoice = getString("whetherVoice");        //是否语音
            String whether5G = getString("whether5G");        //是否5G
            String state = getString("ss");        //是否5G

//            // 获取用户权限
//            List list = whiteRollListService.findByRowNo(user.getRowNo());
//            boolean flag = false;
//
//            for (int i = 0; i < list.size(); i++) {
//                if ((list.get(i).toString()).equals("ROLE_REPM")) { //订单管理员
//                    flag = true;
//                    break;
//                }
//            }

            page = SIMService.findByPage(page, SIMSegment, makeType, makeSize, whetherVoice, whether5G, state);
            String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
            this.Write(json);
        } catch (Exception e) {
            System.out.println(e);
            logger.info(e.toString());
            this.Write("列表加载失败！");
        }
    }


    /**
     * 回收号段
     */
    public void recycleSIMSegment() {
        try {
            String SIMS = getString("SIMSegment");
            String startNumber = getString("startNumber");
            String endNumber = getString("endNumber");
            String name = getString("name");
            String bossNo = getString("bossNo");
            String deptStr = getString("deptStr");
            Date date = new Date();
            SIMSegment ss = this.SIMService.getSIMSegment(SIMS, startNumber, endNumber);
            String seg = ss.getSIMSegment();

            List<SIMNumber> simNumber = this.SIMService.getSIMNumber(SIMS, Integer.valueOf(startNumber), Integer.valueOf(endNumber));
            for (int i = 0; i < simNumber.size(); i++) {
                simNumber.get(i).setAllocatedTime(null);
                simNumber.get(i).setOrderNo(null);
                simNumber.get(i).setState("0");
                this.SIMService.AssignNumber(simNumber.get(i));
            }


            //记录号段操作信息(分配)
            SIMRecordInfo sr = new SIMRecordInfo();
            sr.setSIMSegment(SIMS);//记录分配号段
            sr.setAllocationCity(ss.getCity());//记录分配地市
            sr.setStartNumber(startNumber);//记录分配开始号码
            sr.setEndNumber(endNumber);//记录分配结束号码
            sr.setOperator(name);//记录操作人
            sr.setOperatorNo(bossNo);//记录操作人 BOSS工号
            sr.setCity(deptStr);//记录操作人地市
            sr.setOperationTime(new Date());
            sr.setState("2");//状态 2: 回收
            this.SIMService.addSIMRecordInfo(sr);

            //查询向上可合并号段
            SIMSegment upSegment = this.SIMService.getCanMergeSegment(seg, String.valueOf(Integer.valueOf(endNumber) + 1));
            SIMSegment segment = null;
            if (upSegment != null) {
                upSegment.setStartNumber(ss.getStartNumber());
                upSegment.setQuantity(String.valueOf(Integer.valueOf(ss.getQuantity()) + Integer.valueOf(upSegment.getQuantity())));
                upSegment.setSIMTransferTime(date);//修改调拨时间
                segment = this.SIMService.updateSIMSegment(upSegment);//合并号段
                this.SIMService.deleteSIMSegment(ss);//删除退回号段
            } else {
                ss.setCity(null);
                ss.setState("0");//0 :未分配
                ss.setSIMTransferTime(date);//修改调拨时间
                SIMSegment simSegment = this.SIMService.updateSIMSegment(ss);
            }

            //查询向下可合并号段
            SIMSegment downSegment = this.SIMService.getCanMergeSegment2(seg, String.valueOf(Integer.valueOf(startNumber) - 1));
            if (downSegment != null) {
                if (segment != null) {
                    downSegment.setEndNumber(segment.getEndNumber());
                    downSegment.setQuantity(String.valueOf(Integer.valueOf(downSegment.getQuantity()) + Integer.valueOf(segment.getQuantity())));
                    downSegment.setSIMTransferTime(date);//修改调拨时间
                    this.SIMService.updateSIMSegment(downSegment);//合并号段
                    this.SIMService.deleteSIMSegment(segment);//删除退回号段
                } else {
                    downSegment.setEndNumber(ss.getEndNumber());
                    downSegment.setQuantity(String.valueOf(Integer.valueOf(downSegment.getQuantity()) + Integer.valueOf(ss.getQuantity())));
                    downSegment.setSIMTransferTime(date);//修改调拨时间
                    this.SIMService.updateSIMSegment(downSegment);//合并号段
                    this.SIMService.deleteSIMSegment(segment);//删除退回号段
                }
            }
            Write("OK");
        } catch (Exception e) {
            Write("NO");
            e.printStackTrace();
        }
    }
}
