package com.xinxinsoft.action.IDC;

import java.beans.IntrospectionException;
import java.io.File;
import java.lang.reflect.InvocationTargetException;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

import com.xinxinsoft.entity.boss.BossInterfaceParameter;
import com.xinxinsoft.entity.enclosure.Attachment;
import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.sendComms.IdcService;
import com.xinxinsoft.service.enclosure.AttachmentService;
import com.xinxinsoft.service.webService.CommLogs;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.StringUtil;
import com.xinxinsoft.utils.SystemConfig;
import com.xinxinsoft.utils.common.FileUpload;

import com.xinxinsoft.utils.easyh.JSONHelper;
import com.xinxinsoft.utils.result.Result;
import com.xinxinsoft.utils.result.ResultCode;
import com.xinxinsoft.utils.result.ResultGenerator;
import org.apache.commons.lang.StringUtils;
import org.jbpm.api.Execution;
import org.jbpm.api.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.xinxinsoft.action.BaseAction;
import com.xinxinsoft.entity.IDC.IDCApply;
import com.xinxinsoft.entity.IDC.IDCFlow;
import com.xinxinsoft.entity.IDC.IDCTask;
import com.xinxinsoft.entity.commonSingManagement.SingleAndAttachment;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.entity.waitTask.WaitTask;
import com.xinxinsoft.sendComms.CMCCOpenService;
import com.xinxinsoft.service.IDCService.IDCApplyService;
import com.xinxinsoft.service.IDCService.IDCFlowService;
import com.xinxinsoft.service.IDCService.IDCTaskService;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.service.transfer.TransferInformationService;
import com.xinxinsoft.service.waitTask.WaitTaskService;
import com.xinxinsoft.utils.JbpmUtil;
import com.xinxinsoft.utils.page.LayuiPage;












import javax.annotation.Resource;
import javax.xml.ws.Action;


public class IDCApplyAction extends BaseAction{
	@Resource(name="IDCApplyService")
	private IDCApplyService applyService;

	@Resource(name="IDCFlowService")
	private IDCFlowService	flowService;

	@Resource(name = "IDCTaskService")
	private IDCTaskService	taskService;

	@Resource(name = "JBPMUtil")
	private JbpmUtil jbpmUtil;

	@Resource(name = "CMCCOpenService")
	private CMCCOpenService cmccOpenService;

	private Logger logger = LoggerFactory.getLogger(IDCApplyAction.class);
	//首页代办
    @Resource(name="WaitTaskService")
	private WaitTaskService waitTaskService;
	//用户
    @Resource(name="SystemUserService")
	private SystemUserService systemUserService;
	//生成申请编号
    @Resource(name="TransferInformationService")
	private TransferInformationService  tInformationService;
	@Resource(name = "AttachmentService")
	private AttachmentService attachmentService;
	private ResourceBundle s = ResourceBundle .getBundle("WebService-config");
	private String audit = s.getString("AUDIT_INTERS_FJ_SWITCH");
	private File file1;
	private String file1FileName;
	public String getFile1FileName() {
		return file1FileName;
	}

	public void setFile1FileName(String file1FileName) {
		this.file1FileName = file1FileName;
	}

	public File getFile1() {
		return file1;
	}

	public void setFile1(File file1) {
		this.file1 = file1;
	}
	//添加工单
	public void addIDCApply(){
		Map<String, String> result = new HashMap<String, String>();
		try{
			IDCApply idcApply = new IDCApply();
			IDCFlow	idcFlow = new IDCFlow();
			IDCTask idcTask = new IDCTask();
			String waitId = getString("waitId");
			Date date = null;
			try {
				date = formatForDate(getStringDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
			} catch (ParseException e1) {
				logger.error("IDC错误信息："+e1.getMessage(),e1);
			}
			if(waitId!=null&&waitId!=""){
				//查询首页待办
				WaitTask waitTask = waitTaskService.queryWaitByTaskId(waitId);
				if(waitTask!=null){
					//完成首页待办
					waitTaskService.updateWait(waitTask, this.getRequest());
				}
			}
			String IBM="";//申请编码前面的字母
			List<Object[]> sone= tInformationService.getbumen(user.getRowNo());
			for(int i=0;i<sone.size();i++){
				IBM=(String) sone.get(i)[2];
			}
			String title = getString("title");
			String UNIT_ID = getString("UNIT_ID");
			String UNIT_Name = getString("UNIT_Name");
			String is95 = getString("is95");
			String feeCode = getString("feeCode");
			String creatorId = getString("creatorId");
			String feePrice = getString("feePrice");
			String creatorName = getString("creatorName");
			Integer oneU = getInteger("oneU");
			String oneUPrice = getString("oneUPrice");
			Integer towU = getInteger("towU");
			String towUPrice = getString("towUPrice");
			Integer threeU = getInteger("threeU");
			String threeUPrice = getString("threeUPrice");
			Integer fourU = getInteger("fourU");
			String fourUPrice = getString("fourUPrice");
			String rackNumber = getString("rackNumber");
			String rackNumberPrice = getString("rackNumberPrice");
			String	bandwidth = getString("bandwidth");
			String	bandwidthPrice = getString("bandwidthPrice");
			Integer numberOfPorts = getInteger("numberOfPorts");
			String rackDescription = getString("rackDescription");
			String broadbandDescription = getString("broadbandDescription");
			Integer ipNumber = getInteger("ipNumber");
			Integer WGPortNumber = getInteger("WMPortNumber");
			Double discount = getDouble("discount");
			String charging95 = getString("charging95");
			Double total = getDouble("total");
			String idcName = getString("idcname");
			String priceInformation = getString("priceInformation");
			Integer countipNumber = getInteger("countipNumber");
			String bandwidthType = getString("bandwidthType");
			String feeName = getString("feeName");
			String iPV4Price = getString("IPV4Price");
			String attachmentId = getString("attachmentId");
			Integer bandwidthTotal = getInteger("bandwidthTotal");
			String bandwidthTotalPrice = getString("bandwidthTotalPrice");
			String bandwidthTotalPricetwo = getString("bandwidthTotalPricetwo");
			String rackTotalPrice = getString("rackTotalPrice");
			String idaApplyId = getString("idaApplyId");
			String role=getString("role");
			String dealNo= getString("dealNo");
			String dealName= getString("dealName");
			String qxUserid = getString("qxUserid");
			String sgsUserid = getString("sgsUserid");
			String billingCycle = getString("billingCycle");
			String BUSI_REQ_TYPE = getString("BUSI_REQ_TYPE");
			String PHONE_NO = getString("PHONE_NO");
			logger.info("bandwidthTotalPrice:"+bandwidthTotalPrice+",bandwidthTotalPricetwo:"+bandwidthTotalPricetwo);
			if("1".equals(feeCode)&&"1".equals(is95)){
				if (!"".equals(bandwidthTotalPricetwo)&&bandwidthTotalPricetwo!=null){
					bandwidthTotalPrice = bandwidthTotalPricetwo;
				}else {
					bandwidthTotalPrice="0";
				}
			}
			if("".equals(bandwidthTotalPrice) || bandwidthTotalPrice==null){
				bandwidthTotalPrice="0";
			}
			if("".equals(rackNumberPrice) || rackNumberPrice==null){
				rackNumberPrice="0";			
			}
			
			if("".equals(oneUPrice) || oneUPrice==null){
				oneUPrice="0";
			}
			
			if("".equals(towUPrice) || towUPrice==null){
				towUPrice="0";
			}
			
			if("".equals(threeUPrice) || threeUPrice==null){
				threeUPrice="0";
			}
			
			if("".equals(fourUPrice) || fourUPrice==null){
				fourUPrice="0";
			}
			
			if("".equals(bandwidthTotal) || bandwidthTotal==null){
				bandwidthTotal=0;
			}
			
			idcApply.setBandwidthTotal(bandwidthTotal);//总带宽
			idcApply.setBandwidthTotalPrice(bandwidthTotalPrice);//带宽签约总价（元/月）
			idcApply.setRackTotalPrice(rackTotalPrice);//机架签约总价
			idcApply.setBandwidthType(bandwidthType);//端口宽带类别
			idcApply.setFeeName(feeName);//套餐名称
			idcApply.setTitle(title);//标题
			idcApply.setCountipNumber(countipNumber);//ip配套数量
			idcApply.setUNIT_ID(UNIT_ID);//集团280
			idcApply.setUNIT_Name(UNIT_Name);//集团名称
			idcApply.setIdcName(idcName);//意向数据中心编码
			idcApply.setIs95(is95);//是否95计费
			idcApply.setFeeCode(feeCode);//是否是套餐
			idcApply.setCreatorId(creatorId);//创建人ID
			idcApply.setCreatorName(creatorName);//创建人名称
			idcApply.setCreateDate(date);//创建时间
			idcApply.setOneU(oneU);//1U个数
			idcApply.setOneUPrice(oneUPrice);//1U单价
			idcApply.setTowU(towU);//2U个数
			idcApply.setTowUPrice(towUPrice);//2U单价
			idcApply.setThreeU(threeU);//3U个数
			idcApply.setThreeUPrice(threeUPrice);//3U单价
			idcApply.setFourU(fourU);//4U个数
			idcApply.setFourUPrice(fourUPrice);//4U单价
			if (!"".equals(bandwidth)&&bandwidth!=null){
				idcApply.setBandwidth(new BigInteger(bandwidth));//单宽带（M）
			}else {
				idcApply.setBandwidth(new BigInteger("0"));
			}
			idcApply.setBandwidthPrice(bandwidthPrice);//单宽带单价（元/M）
			idcApply.setNumberOfPorts(numberOfPorts);//端口个数
			idcApply.setRackNumber(rackNumber);//整机架个数
			idcApply.setRackNumberPrice(rackNumberPrice);//整机架单价
			idcApply.setRackDescription(rackDescription);//机架描述
			idcApply.setBroadbandDescription(broadbandDescription);//宽带描述
			idcApply.setIpNumber(ipNumber);//ipV4额外数量	
			idcApply.setIPV4Price(iPV4Price);//额外IPV4单价
			idcApply.setWGPortNumber(WGPortNumber);//万M端口数量
			idcApply.setDiscount(discount);//折扣
			idcApply.setCharging95(charging95);//95计费保底
			idcApply.setTotal(total);//保底总价
			idcApply.setState(0);//工单状态
			idcApply.setPriceInformation(priceInformation);//价格信息
			idcApply.setFeeNo(getInteger("feeNo"));//套餐数量
			idcApply.setFeePrice(feePrice);
			idcApply.setOrderNo(IBM+getStringDate(new Date(), null));//工单编号
			idcApply.setBillingCycle(billingCycle);		//出帐周期
			idcApply.setBUSI_REQ_TYPE(BUSI_REQ_TYPE);	//操作类型
			idcApply.setPHONE_NO(PHONE_NO);
			try {
				getIsNullAttribute(idcApply);//属性值为空的属性名
			} catch (IllegalAccessException | IllegalArgumentException
					| InvocationTargetException | IntrospectionException e) {
				logger.error("IDC错误信息："+e.getMessage(),e);
			}
			//发布一个流程图，只用发布一次
			//jbpmUtil.deployNew("com/xinxinsoft/jbpmProcess/IDC/IDCApply.jpdl.xml");
			//返回页面数据的map
			BossInterfaceParameter idcNameList  = applyService.SelectValueList(idcApply.getIdcName(),"idcName").size()==0?null:applyService.SelectValueList(idcApply.getIdcName(),"idcName").get(0);
			if(idcNameList==null){
				result.put("message", "工单发起失败");
				result.put("err", "意向数据中心查询失败");
				logger.info("工单添加失败");
				Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
				return;
			}
			String idcTaskType ="";
			Map<String, String> map = new HashMap<String, String>();
			if("0".equals(feeCode)) {
				if (idcApply.getDiscount() < 100 && idcApply.getDiscount() > 0) {
					if("SGS".equals(role)){
						map.put("role", "province");
					}else{
						map.put("role", "city");
					}
				}else {
				map.put("role", "END");
				idcTaskType = "complete";
				String feeCode1 = "";
					if ("0".equals(idcApply.getFeeCode())) {	//套餐资费
						feeCode1 = "12000200000003";
					} else {		//独立资费
						if (idcApply.getIs95().equals("1")) {
							feeCode1 = "12000200000001";
						}else if (idcApply.getIs95().equals("2")) {
							feeCode1 = "12000200000004";
						}else if (idcApply.getIs95().equals("0")) {
							feeCode1 = "12000200000002";
						}
					}
				String BandwidthTotal = "0M";
				if (idcApply.getBandwidthTotal()!=null&&!"".equals(idcApply.getBandwidthTotal())){
					if (idcApply.getBandwidthTotal()>=1000){
						Integer Total = idcApply.getBandwidthTotal()/1000;
						BandwidthTotal = Total.toString()+"G";
					}else {
						BandwidthTotal = idcApply.getBandwidthTotal().toString()+"M";
					}
				}
					DecimalFormat df = new DecimalFormat("#0.0000");
					Map<String, Object> s6429OpenRetur = null;
					if ("0".equals(idcApply.getFeeCode())) {		//套餐模式
						s6429OpenRetur = IdcService.getInstance().preIDCSvc(idcApply,
								systemUserService.getUserInfoByRowNo(Integer.valueOf(idcApply.getCreatorId())),
								feeCode1,//资费类型
								idcApply.getIdcName(),//意向数据中心编码
								idcApply.getFeeName(),//套餐项目名称
								df.format((Float.valueOf(idcApply.getDiscount() + "") / 100f)) + "",//折扣率
								idcApply.getCharging95(),//95保底量
								idcApply.getOneUPrice() + "",//1U机位单价（元/月）
								idcApply.getTowUPrice() + "",//2U机位单价（元/月）
								idcApply.getThreeUPrice() + "",//3U机位单价（元/月）
								idcApply.getFourUPrice() + "",//4U机位单价（元/月）
								idcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
								idcApply.getTotal().toString(),//总费用（参考值）
								(50 * idcApply.getIpNumber()) + ".00",//额外IP费用（元/月）
								idcApply.getIs95(),//计费模式
								idcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
								idcApply.getFeePrice(),//套餐单价（元/月）
								idcApply.getFeeNo().equals(0) ? "" : idcApply.getFeeNo() + "",//套餐数量
								idcApply.getBandwidthTotalPrice(),//带宽签约总价 （元/月）
								idcApply.getRackTotalPrice()!=null?idcApply.getRackTotalPrice():"0",//机架签约总费用
								BandwidthTotal,//总带宽大小
								idcApply.getBillingCycle(), //出账周期
								(idcApply.getCountipNumber() + idcApply.getIpNumber()) + "",//IPv4数量
								"1200020003701",//IPv4性质
								idcApply.getCountipNumber() + "",//IPv4配套数量
								idcApply.getIpNumber() + "",//IPv4额外数量
								"",//IPV4描述(备注)
								idcApply.getBandwidthType()!=null?idcApply.getBandwidthType():"",//带宽模式类别
								idcApply.getBandwidth()!=null?idcApply.getBandwidth().toString():"",//单端口带宽大小
								idcApply.getNumberOfPorts()!=null?idcApply.getNumberOfPorts().toString():"",//端口数量
								idcApply.getBroadbandDescription()!=null?idcApply.getBroadbandDescription():"",//带宽描述(备注)
								"1200020002501",//供电类型
								"1200020002601",//机架规格
								idcApply.getOneU(),//1U机位数量
								idcApply.getTowU(),//2U机位数量
								idcApply.getThreeU(),//3U机位数量
								idcApply.getFourU(),//4U机位数量
								idcApply.getRackNumber(),//整机架数量
								idcApply.getRackDescription()!=null?idcApply.getRackDescription():"",//机架描述(备注)
								"",//IPv6网段数量
								"",//IPv6网络位
								""//IPV6描述(备注)
						);//推送boss
					}else {		//独立模式
						s6429OpenRetur = IdcService.getInstance().preIDCSvc(idcApply,
								systemUserService.getUserInfoByRowNo(Integer.valueOf(idcApply.getCreatorId())),
								feeCode1,//资费类型
								idcApply.getIdcName(),//意向数据中心编码
								idcApply.getCharging95(),//95保底量
								idcApply.getOneUPrice() + "",//1U机位单价（元/月）
								idcApply.getTowUPrice() + "",//2U机位单价（元/月）
								idcApply.getThreeUPrice() + "",//3U机位单价（元/月）
								idcApply.getFourUPrice() + "",//4U机位单价（元/月）
								idcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
								idcApply.getTotal().toString(),//总费用（参考值）
								(50 * idcApply.getIpNumber()) + ".00",//额外IP费用（元/月）
								idcApply.getIs95(),//九五计费带宽万兆单价（元/月）
								idcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
								idcApply.getBandwidthTotalPrice(),//包端口带宽签约总价 （元/月）
								idcApply.getRackTotalPrice()!=null?idcApply.getRackTotalPrice():"0",//机架签约总费用
								BandwidthTotal,//总带宽大小
								idcApply.getBillingCycle(), //出账周期
								(idcApply.getCountipNumber() + idcApply.getIpNumber()) + "",//IPv4数量
								"1200020003701",//IPv4性质
								idcApply.getCountipNumber() + "",//IPv4配套数量
								idcApply.getIpNumber() + "",//IPv4额外数量
								"",//IPV4描述(备注)
								idcApply.getBandwidthType()!=null?idcApply.getBandwidthType():"",//带宽模式类别
								idcApply.getBandwidth()!=null?idcApply.getBandwidth().toString():"",//单端口带宽大小（单位兆）
								idcApply.getNumberOfPorts()!=null?idcApply.getNumberOfPorts().toString():"",//端口数量
								idcApply.getBroadbandDescription()!=null?idcApply.getBroadbandDescription():"",//带宽描述(备注)
								"1200020002501",//供电类型
								"1200020002601",//机架规格
								idcApply.getOneU(),//1U机位数量
								idcApply.getTowU(),//2U机位数量
								idcApply.getThreeU(),//3U机位数量
								idcApply.getFourU(),//4U机位数量
								idcApply.getRackNumber(),//整机架数量
								idcApply.getRackDescription()!=null?idcApply.getRackDescription():"",//机架描述(备注)
								"",//IPv6网段数量
								"",//IPv6网络位
								""//IPV6描述(备注)
						);//推送boss
					}
				if (null != s6429OpenRetur.get("RETURN_CODE") && "0".equals(s6429OpenRetur.get("RETURN_CODE"))) {
					if (null != s6429OpenRetur.get("BOSS_FORM_NO")&&null != s6429OpenRetur.get("PHONE_NO")) {
						idcApply.setBoosFormNo(s6429OpenRetur.get("BOSS_FORM_NO").toString());
						idcApply.setPHONE_NO(s6429OpenRetur.get("PHONE_NO").toString());
					} else {
						result.put("err", "boos报错数据失败");
						result.put("message", s6429OpenRetur.get("RET_MSG").toString());
						Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
						throw new RuntimeException(" 给事务回滚，自定义");
					}
				} else {
					result.put("err", "推送boss失败");
					result.put("message", "" + (s6429OpenRetur.get("RETURN_MSG") == null ? (s6429OpenRetur.get("RET_MSG") == null ? s6429OpenRetur.get("DETAIL_MSG") : s6429OpenRetur.get("RET_MSG")) : s6429OpenRetur.get("RETURN_MSG")));
					Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
					throw new RuntimeException(" 给事务回滚，自定义");
				}
			}
			}else {
				Double one;
				Double tow;
				Double three;
				Double four;
				Double NumberPricetwo;
				if ("1200020000101".equals(idcApply.getIdcName())
						||"1200020000102".equals(idcApply.getIdcName())
						||"1200020000103".equals(idcApply.getIdcName())
				){
					//省级
					one = 333.00;
					tow = 417.00;
					three = 500.00;
					four = 583.00;
					NumberPricetwo = 5000.00;
				}else {
					//地市
					one = 267.00;
					tow = 333.00;
					three = 400.00;
					four = 457.00;
					NumberPricetwo = 4000.00;
				}
				if (idcApply.getIs95().equals("0")||idcApply.getIs95().equals("2")){
					if(Float.parseFloat(idcApply.getBandwidthPrice())<5400*(applyService.queryCharging95(idcApply.getCharging95(),idcApply.getIs95()))+5400
							|| (!"0".equals(idcApply.getRackNumberPrice()) && !"0.00".equals(idcApply.getRackNumberPrice()) ? Double.parseDouble(idcApply.getRackNumberPrice()):NumberPricetwo) < NumberPricetwo
							|| (!"0".equals(idcApply.getOneUPrice()) && !"0.00".equals(idcApply.getOneUPrice()) ? Double.parseDouble(idcApply.getOneUPrice()):one) < one
							|| (!"0".equals(idcApply.getTowUPrice()) && !"0.00".equals(idcApply.getTowUPrice()) ? Double.parseDouble(idcApply.getTowUPrice()):tow) < tow
							|| (!"0".equals(idcApply.getThreeUPrice()) && !"0.00".equals(idcApply.getThreeUPrice()) ? Double.parseDouble(idcApply.getThreeUPrice()):three) < three
							|| (!"0".equals(idcApply.getFourUPrice()) && !"0.00".equals(idcApply.getFourUPrice()) ? Double.parseDouble(idcApply.getFourUPrice()):four) < four

					){
						if("SGS".equals(role)){
							map.put("role", "province");
						}else{
							map.put("role", "city");
						}
					}else{
						if (idcApply.getBandwidthTotal() < 1000) {
							map.put("role", "END");
							idcTaskType = "complete";
							String feeCode1 = "";
							if ("0".equals(idcApply.getFeeCode())) {	//套餐资费
								feeCode1 = "12000200000003";
							} else {		//独立资费
								if (idcApply.getIs95().equals("1")) {
									feeCode1 = "12000200000001";
								}else if (idcApply.getIs95().equals("2")) {
									feeCode1 = "12000200000004";
								}else if (idcApply.getIs95().equals("0")) {
									feeCode1 = "12000200000002";
								}
							}
								String BandwidthTotal = "0M";
							if (idcApply.getBandwidthTotal()!=null&&!"".equals(idcApply.getBandwidthTotal())){
								if (idcApply.getBandwidthTotal()>=1000){
									Integer Total = idcApply.getBandwidthTotal()/1000;
									BandwidthTotal = Total.toString()+"G";
								}else {
									BandwidthTotal = idcApply.getBandwidthTotal().toString()+"M";
								}
							}
							DecimalFormat df = new DecimalFormat("#0.0000");
							Map<String, Object> s6429OpenRetur = null;
							if ("0".equals(idcApply.getFeeCode())) {		//套餐模式
								s6429OpenRetur = IdcService.getInstance().preIDCSvc(idcApply,
										systemUserService.getUserInfoByRowNo(Integer.valueOf(idcApply.getCreatorId())),
										feeCode1,//资费类型
										idcApply.getIdcName(),//意向数据中心编码
										idcApply.getFeeName(),//套餐项目名称
										df.format((Float.valueOf(idcApply.getDiscount() + "") / 100f)) + "",//折扣率
										idcApply.getCharging95(),//95保底量
										idcApply.getOneUPrice() + "",//1U机位单价（元/月）
										idcApply.getTowUPrice() + "",//2U机位单价（元/月）
										idcApply.getThreeUPrice() + "",//3U机位单价（元/月）
										idcApply.getFourUPrice() + "",//4U机位单价（元/月）
										idcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
										idcApply.getTotal().toString(),//总费用（参考值）
										(50 * idcApply.getIpNumber()) + ".00",//额外IP费用（元/月）
										idcApply.getIs95(),//九五计费带宽万兆单价（元/月）
										idcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
										idcApply.getFeePrice(),//套餐单价（元/月）
										idcApply.getFeeNo().equals(0) ? "" : idcApply.getFeeNo() + "",//套餐数量
										idcApply.getBandwidthTotalPrice(),//包端口带宽签约总价 （元/月）
										idcApply.getRackTotalPrice()!=null?idcApply.getRackTotalPrice():"0",//机架签约总费用
										BandwidthTotal,//总带宽大小
										idcApply.getBillingCycle(), //出账周期
										(idcApply.getCountipNumber() + idcApply.getIpNumber()) + "",//IPv4数量
										"1200020003701",//IPv4性质
										idcApply.getCountipNumber() + "",//IPv4配套数量
										idcApply.getIpNumber() + "",//IPv4额外数量
										"",//IPV4描述(备注)
										idcApply.getBandwidthType()!=null?idcApply.getBandwidthType():"",//带宽模式类别
										idcApply.getBandwidth()!=null?idcApply.getBandwidth().toString():"",//单端口带宽大小（单位兆）
										idcApply.getNumberOfPorts()!=null?idcApply.getNumberOfPorts().toString():"",//端口数量
										idcApply.getBroadbandDescription()!=null?idcApply.getBroadbandDescription():"",//带宽描述(备注)
										"1200020002501",//供电类型
										"1200020002601",//机架规格
										idcApply.getOneU(),//1U机位数量
										idcApply.getTowU(),//2U机位数量
										idcApply.getThreeU(),//3U机位数量
										idcApply.getFourU(),//4U机位数量
										idcApply.getRackNumber(),//整机架数量
										idcApply.getRackDescription()!=null?idcApply.getRackDescription():"",//机架描述(备注)
										"",//IPv6网段数量
										"",//IPv6网络位
										""//IPV6描述(备注)
								);//推送boss
							}else {		//独立模式
								s6429OpenRetur = IdcService.getInstance().preIDCSvc(idcApply,
										systemUserService.getUserInfoByRowNo(Integer.valueOf(idcApply.getCreatorId())),
										feeCode1,//资费类型
										idcApply.getIdcName(),//意向数据中心编码
										idcApply.getCharging95(),//95保底量
										idcApply.getOneUPrice() + "",//1U机位单价（元/月）
										idcApply.getTowUPrice() + "",//2U机位单价（元/月）
										idcApply.getThreeUPrice() + "",//3U机位单价（元/月）
										idcApply.getFourUPrice() + "",//4U机位单价（元/月）
										idcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
										idcApply.getTotal().toString(),//总费用（参考值）
										(50 * idcApply.getIpNumber()) + ".00",//额外IP费用（元/月）
										idcApply.getIs95(),//九五计费带宽万兆单价（元/月）
										idcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
										idcApply.getBandwidthTotalPrice(),//包端口带宽签约总价 （元/月）
										idcApply.getRackTotalPrice()!=null?idcApply.getRackTotalPrice():"0",//机架签约总费用
										BandwidthTotal,//总带宽大小
										idcApply.getBillingCycle(), //出账周期
										(idcApply.getCountipNumber() + idcApply.getIpNumber()) + "",//IPv4数量
										"1200020003701",//IPv4性质
										idcApply.getCountipNumber() + "",//IPv4配套数量
										idcApply.getIpNumber() + "",//IPv4额外数量
										"",//IPV4描述(备注)
										idcApply.getBandwidthType()!=null?idcApply.getBandwidthType():"",//带宽模式类别
										idcApply.getBandwidth()!=null?idcApply.getBandwidth().toString():"",//单端口带宽大小（单位兆）
										idcApply.getNumberOfPorts()!=null?idcApply.getNumberOfPorts().toString():"",//端口数量
										idcApply.getBroadbandDescription()!=null?idcApply.getBroadbandDescription():"",//带宽描述(备注)
										"1200020002501",//供电类型
										"1200020002601",//机架规格
										idcApply.getOneU(),//1U机位数量
										idcApply.getTowU(),//2U机位数量
										idcApply.getThreeU(),//3U机位数量
										idcApply.getFourU(),//4U机位数量
										idcApply.getRackNumber(),//整机架数量
										idcApply.getRackDescription()!=null?idcApply.getRackDescription():"",//机架描述(备注)
										"",//IPv6网段数量
										"",//IPv6网络位
										""//IPV6描述(备注)
								);//推送boss
							}
							if (null != s6429OpenRetur.get("RETURN_CODE") && "0".equals(s6429OpenRetur.get("RETURN_CODE"))) {
								if (null != s6429OpenRetur.get("BOSS_FORM_NO")&&null != s6429OpenRetur.get("PHONE_NO")) {
									idcApply.setBoosFormNo(s6429OpenRetur.get("BOSS_FORM_NO").toString());
									idcApply.setPHONE_NO(s6429OpenRetur.get("PHONE_NO").toString());
								} else {
									result.put("err", "boos报错数据失败");
									result.put("message", s6429OpenRetur.get("RET_MSG").toString());
									Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
									throw new RuntimeException(" 给事务回滚，自定义");
								}
							} else {
								result.put("err", "推送boss失败");
								result.put("message", "" + (s6429OpenRetur.get("RETURN_MSG") == null ? (s6429OpenRetur.get("RET_MSG") == null ? s6429OpenRetur.get("DETAIL_MSG") : s6429OpenRetur.get("RET_MSG")) : s6429OpenRetur.get("RETURN_MSG")));
								Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
								throw new RuntimeException(" 给事务回滚，自定义");
							}
						} else {
							if ("SGS".equals(role)) {
								map.put("role", "province");
							} else {
								map.put("role", "city");
							}
						}
					}
				}else {
					Double change;
					if ("1200020003301".equals(idcApply.getBandwidthType())){
						if ("1200020000101".equals(idcApply.getIdcName())
								||"1200020000102".equals(idcApply.getIdcName())
								||"1200020000103".equals(idcApply.getIdcName())
						){
							change = 26.00;
						}else {
							change = 23.00;
						}
					}else if ("1200020003302".equals(idcApply.getBandwidthType())){
						if ("1200020000101".equals(idcApply.getIdcName())
								||"1200020000102".equals(idcApply.getIdcName())
								||"1200020000103".equals(idcApply.getIdcName())
						){
							change = 26.00;
						}else {
							change = 23.00;
						}
					}else if ("1200020003303".equals(idcApply.getBandwidthType())){
						if ("1200020000101".equals(idcApply.getIdcName())
								||"1200020000102".equals(idcApply.getIdcName())
								||"1200020000103".equals(idcApply.getIdcName())
						){
							change = 23.00;
						}else {
							change = 21.00;
						}
					}else{
						change = 5400.00;
					}
					if((!"".equals(idcApply.getBandwidthPrice()) && idcApply.getBandwidthPrice()!=null ? Double.parseDouble(idcApply.getBandwidthPrice()):change)<change
							|| (!"0".equals(idcApply.getRackNumberPrice()) && !"0.00".equals(idcApply.getRackNumberPrice()) ? Double.parseDouble(idcApply.getRackNumberPrice()):NumberPricetwo) < NumberPricetwo
							|| (!"0".equals(idcApply.getOneUPrice()) && !"0.00".equals(idcApply.getOneUPrice()) ? Double.parseDouble(idcApply.getOneUPrice()):one) < one
							|| (!"0".equals(idcApply.getTowUPrice()) && !"0.00".equals(idcApply.getTowUPrice()) ? Double.parseDouble(idcApply.getTowUPrice()):tow) < tow
							|| (!"0".equals(idcApply.getThreeUPrice()) && !"0.00".equals(idcApply.getThreeUPrice()) ? Double.parseDouble(idcApply.getThreeUPrice()):three) < three
							|| (!"0".equals(idcApply.getFourUPrice()) && !"0.00".equals(idcApply.getFourUPrice()) ? Double.parseDouble(idcApply.getFourUPrice()):four) < four

					){
						if("SGS".equals(role)){
							map.put("role", "province");
						}else{
							map.put("role", "city");
						}
					}else{
						if (idcApply.getBandwidthTotal() < 1000) {
							map.put("role", "END");
							idcTaskType = "complete";
							String feeCode1 = "";
							if ("0".equals(idcApply.getFeeCode())) {	//套餐资费
								feeCode1 = "12000200000003";
							} else {		//独立资费
								if (idcApply.getIs95().equals("1")) {
									feeCode1 = "12000200000001";
								}else if (idcApply.getIs95().equals("2")) {
									feeCode1 = "12000200000004";
								}else if (idcApply.getIs95().equals("0")) {
									feeCode1 = "12000200000002";
								}
							}
							//String feeCode1 = "0";
							String BandwidthTotal = "0M";
							if (idcApply.getBandwidthTotal()!=null&&!"".equals(idcApply.getBandwidthTotal())){
								if (idcApply.getBandwidthTotal()>=1000){
									Integer Total = idcApply.getBandwidthTotal()/1000;
									BandwidthTotal = Total.toString()+"G";
								}else {
									BandwidthTotal = idcApply.getBandwidthTotal().toString()+"M";
								}
							}
							DecimalFormat df = new DecimalFormat("#0.0000");
							Map<String, Object> s6429OpenRetur = null;
							if ("0".equals(idcApply.getFeeCode())) {		//套餐模式
								s6429OpenRetur = IdcService.getInstance().preIDCSvc(idcApply,
										systemUserService.getUserInfoByRowNo(Integer.valueOf(idcApply.getCreatorId())),
										feeCode1,//资费类型
										idcApply.getIdcName(),//意向数据中心编码
										idcApply.getFeeName(),//套餐项目名称
										df.format((Float.valueOf(idcApply.getDiscount() + "") / 100f)) + "",//折扣率
										idcApply.getCharging95(),//95保底量
										idcApply.getOneUPrice() + "",//1U机位单价（元/月）
										idcApply.getTowUPrice() + "",//2U机位单价（元/月）
										idcApply.getThreeUPrice() + "",//3U机位单价（元/月）
										idcApply.getFourUPrice() + "",//4U机位单价（元/月）
										idcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
										idcApply.getTotal().toString(),//总费用（参考值）
										(50 * idcApply.getIpNumber()) + ".00",//额外IP费用（元/月）
										idcApply.getIs95(),//九五计费带宽万兆单价（元/月）
										idcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
										idcApply.getFeePrice(),//套餐单价（元/月）
										idcApply.getFeeNo().equals(0) ? "" : idcApply.getFeeNo() + "",//套餐数量
										idcApply.getBandwidthTotalPrice(),//包端口带宽签约总价 （元/月）
										idcApply.getRackTotalPrice()!=null?idcApply.getRackTotalPrice():"0",//机架签约总费用
										BandwidthTotal,//总带宽大小
										idcApply.getBillingCycle(), //出账周期
										(idcApply.getCountipNumber() + idcApply.getIpNumber()) + "",//IPv4数量
										"1200020003701",//IPv4性质
										idcApply.getCountipNumber() + "",//IPv4配套数量
										idcApply.getIpNumber() + "",//IPv4额外数量
										"",//IPV4描述(备注)
										idcApply.getBandwidthType()!=null?idcApply.getBandwidthType():"",//带宽模式类别
										idcApply.getBandwidth()!=null?idcApply.getBandwidth().toString():"",//单端口带宽大小（单位兆）
										idcApply.getNumberOfPorts()!=null?idcApply.getNumberOfPorts().toString():"",//端口数量
										idcApply.getBroadbandDescription()!=null?idcApply.getBroadbandDescription():"",//带宽描述(备注)
										"1200020002501",//供电类型
										"1200020002601",//机架规格
										idcApply.getOneU(),//1U机位数量
										idcApply.getTowU(),//2U机位数量
										idcApply.getThreeU(),//3U机位数量
										idcApply.getFourU(),//4U机位数量
										idcApply.getRackNumber(),//整机架数量
										idcApply.getRackDescription()!=null?idcApply.getRackDescription():"",//机架描述(备注)
										"",//IPv6网段数量
										"",//IPv6网络位
										""//IPV6描述(备注)
								);//推送boss
							}else {		//独立模式
								s6429OpenRetur = IdcService.getInstance().preIDCSvc(idcApply,
										systemUserService.getUserInfoByRowNo(Integer.valueOf(idcApply.getCreatorId())),
										feeCode1,//资费类型
										idcApply.getIdcName(),//意向数据中心编码
										idcApply.getCharging95(),//95保底量
										idcApply.getOneUPrice() + "",//1U机位单价（元/月）
										idcApply.getTowUPrice() + "",//2U机位单价（元/月）
										idcApply.getThreeUPrice() + "",//3U机位单价（元/月）
										idcApply.getFourUPrice() + "",//4U机位单价（元/月）
										idcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
										idcApply.getTotal().toString(),//总费用（参考值）
										(50 * idcApply.getIpNumber()) + ".00",//额外IP费用（元/月）
										idcApply.getIs95(),//九五计费带宽万兆单价（元/月）
										idcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
										idcApply.getBandwidthTotalPrice(),//包端口带宽签约总价 （元/月）
										idcApply.getRackTotalPrice()!=null?idcApply.getRackTotalPrice():"0",//机架签约总费用
										BandwidthTotal,//总带宽大小
										idcApply.getBillingCycle(), //出账周期
										(idcApply.getCountipNumber() + idcApply.getIpNumber()) + "",//IPv4数量
										"1200020003701",//IPv4性质
										idcApply.getCountipNumber() + "",//IPv4配套数量
										idcApply.getIpNumber() + "",//IPv4额外数量
										"",//IPV4描述(备注)
										idcApply.getBandwidthType()!=null?idcApply.getBandwidthType():"",//带宽模式类别
										idcApply.getBandwidth()!=null?idcApply.getBandwidth().toString():"",//单端口带宽大小（单位兆）
										idcApply.getNumberOfPorts()!=null?idcApply.getNumberOfPorts().toString():"",//端口数量
										idcApply.getBroadbandDescription()!=null?idcApply.getBroadbandDescription():"",//带宽描述(备注)
										"1200020002501",//供电类型
										"1200020002601",//机架规格
										idcApply.getOneU(),//1U机位数量
										idcApply.getTowU(),//2U机位数量
										idcApply.getThreeU(),//3U机位数量
										idcApply.getFourU(),//4U机位数量
										idcApply.getRackNumber(),//整机架数量
										idcApply.getRackDescription()!=null?idcApply.getRackDescription():"",//机架描述(备注)
										"",//IPv6网段数量
										"",//IPv6网络位
										""//IPV6描述(备注)
								);//推送boss
							}
							if (null != s6429OpenRetur.get("RETURN_CODE") && "0".equals(s6429OpenRetur.get("RETURN_CODE"))) {
								if (null != s6429OpenRetur.get("BOSS_FORM_NO")&&null != s6429OpenRetur.get("PHONE_NO")) {
									idcApply.setBoosFormNo(s6429OpenRetur.get("BOSS_FORM_NO").toString());
									idcApply.setPHONE_NO(s6429OpenRetur.get("PHONE_NO").toString());
								} else {
									result.put("err", "boos报错数据失败");
									result.put("message", s6429OpenRetur.get("RET_MSG").toString());
									Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
									throw new RuntimeException(" 给事务回滚，自定义");
								}
							} else {
								result.put("err", "推送boss失败");
								result.put("message", "" + (s6429OpenRetur.get("RETURN_MSG") == null ? (s6429OpenRetur.get("RET_MSG") == null ? s6429OpenRetur.get("DETAIL_MSG") : s6429OpenRetur.get("RET_MSG")) : s6429OpenRetur.get("RETURN_MSG")));
								Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
								throw new RuntimeException(" 给事务回滚，自定义");
							}

						/*if(null!="11"&&"0".equals("0")){
							if(null!="00"){
								applyService.setBossFormNo(idcApply.getId(), "cscds");
							}else {
								result.put("err", "boos报错数据失败");
								result.put("message", "boos报错数据失败");
								Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
								throw new RuntimeException(" 给事务回滚，自定义");
							}
						}else{
							result.put("err", "推送boss失败");
							result.put("message","推送boss失败");
							Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
							throw new RuntimeException(" 给事务回滚，自定义");
						}*/
						} else {
							if ("SGS".equals(role)) {
								map.put("role", "province");
							} else {
								map.put("role", "city");
							}
						}
					}

				}
			}
			//启动一个流程并获得一个流程ID
			String processId = jbpmUtil.startPIByKey("IDCApplyProcess",map).getId();
			//当前任务节点
			Task task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).uniqueResult();
			idcApply = applyService.addIDCApply(idcApply);
			idcFlow.setOrderNo(idcApply.getOrderNo());
			idcFlow.setTotal(idcApply.getTotal());
			idcFlow.setFlowID(processId);
			idcFlow.setIdaApplyId(idcApply.getId());
			idcFlow.setGroupName(idcApply.getUNIT_Name());
			idcFlow.setGroupNo(idcApply.getUNIT_ID());
			idcFlow.setCreateDate(getStringDate(new Date(), "yyyy-MM-dd HH:mm:ss"));
			idcFlow.setCreator(idcApply.getCreatorId());
			idcFlow.setCreatorName(idcApply.getCreatorName());
			if("".equals(idcTaskType)){
				idcFlow.setDealNo(getString("dealNo"));
				idcFlow.setDealName(getString("dealName"));
			}else{
				idcFlow.setDealNo(user.getRowNo()+"");
				idcFlow.setDealName(user.getEmployeeName());
			}
			idcFlow.setState(0);
			idcFlow.setTitle(idcApply.getTitle());
			idcTask.setCreateDate(date);
			idcTask.setCreatorName(idcApply.getCreatorName());
			idcTask.setCreatorNo(idcApply.getCreatorId());
			Date date2 = null;
			try {
				date2 = formatForDate(getStringDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
			} catch (ParseException e) {
				logger.error("IDC错误信息："+e.getMessage(),e);
			}
			date2.setTime(date.getTime()+1000L);
			idcTask.setDealDate(date2);
			idcTask.setDealNo(idcApply.getCreatorId());
			idcTask.setDealName(idcApply.getCreatorName());
			idcTask.setFlowId(processId);
			idcTask.setState(1);
			idcTask.setTaskMemo("");
			if(task!=null){
				idcTask.setTaskId(task.getId());
			}else{
				idcTask.setTaskId(getStringDate(new Date(), null));
				
			}
			idcTask.setTaskName("客户经理");
			flowService.addIDCApplyFlow(idcFlow);
			taskService.addIDCApplyTask(idcTask);//完成发起申请
			//添加待处理任务
			if("".equals(idcTaskType)){
				IDCTask pendingIDCTask = new IDCTask();
				pendingIDCTask.setCreateDate(date2);
				pendingIDCTask.setCreatorNo(user.getRowNo()+"");
				pendingIDCTask.setCreatorName(user.getEmployeeName());
				pendingIDCTask.setDealDate(null);
				pendingIDCTask.setDealNo(dealNo);
				pendingIDCTask.setDealName(dealName);
				pendingIDCTask.setFlowId(processId);
				pendingIDCTask.setState(0);
				pendingIDCTask.setTaskId(null);
				pendingIDCTask.setTaskMemo(null);
				pendingIDCTask.setTaskName(null);
				taskService.addIDCApplyTask(pendingIDCTask);//添加代办
				submitWait(idcApply.getId()
						,idcApply.getOrderNo()
						,"[IDC]"+idcApply.getTitle()
						,idcFlow.getFlowID()
						,dealNo
						,user,idcFlow.getDealNo(),null,pendingIDCTask.getId());//添加主页代办
			}else{
				submitWait(idcApply.getId()
						,idcApply.getOrderNo()
						,"[IDC]"+idcApply.getTitle()
						,idcFlow.getFlowID()
						,user.getRowNo()+""
						,user,idcFlow.getDealNo(),null,null);//添加主页代办
			}
			List<SingleAndAttachment> ss = tInformationService.getSingleAndAttachment(idaApplyId);
			//遍历获取的附件中间表数据
			if(ss!=null){
				for (int i=0; i<ss.size();i++){
					if(i==0){
						if(!"".equals(attachmentId)){
							attachmentId += ","+ss.get(i).getAttachmentId()+",";
						}else{
							attachmentId += ss.get(i).getAttachmentId()+",";
						}
					}else{
						attachmentId += ss.get(i).getAttachmentId()+",";
					}
				}
			}
			if (!StringUtils.isEmpty(attachmentId)) {
				if (attachmentId != null) {
					// 判断是否上传了附件，获取前台提交的附件Id；
					String[] json = attachmentId.split(",");
					if (json.length > 0) {
						for (int i = 0; i < json.length; i++) {
							SingleAndAttachment sa = new SingleAndAttachment();
							sa.setOrderID(idcApply.getId());
							sa.setAttachmentId(json[i]);
							sa.setLink("IDCApply");
							applyService.saveSandA(sa);
						}
					}
				}
			}
			if(qxUserid!=null && !"".equals(qxUserid) && !"undefined".equals(qxUserid) && !"null".equals(qxUserid)&&
					sgsUserid!=null && !"".equals(sgsUserid) && !"undefined".equals(sgsUserid) && !"null".equals(sgsUserid)){
				showWait(idcApply.getId()
						,"[IDC]"+idcApply.getTitle()					
						,processId
						,qxUserid
						,user);//添加主页代办
				showWait(idcApply.getId()
						,"[IDC]"+idcApply.getTitle()					
						,processId
						,sgsUserid
						,user);//添加主页代办
			}
			if(idcApply.getId()!=null||idcApply.getId()!=""){
				result.put("message", "工单成功发起");
				result.put("err", "");
				logger.info("工单添加成功");
			}else{
				result.put("message", "工单发起失败");
				result.put("err", "工单添加失败");
				logger.info("工单添加失败");
			}
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		}catch(Throwable e){
			e.printStackTrace();
			result.put("message", "工单发起失败");
			result.put("err", "工单添加失败");
			logger.error("IDC错误信息："+e.getMessage(),e);
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		}
	}

	/**
	 * 查询添加页面下拉框的值
	 */
	public void BandwidthSelectValueList(){
		Map<String,Object> selectList = new HashMap<>();
		String BandwidthType = getString("BandwidthType");
		selectList.put("BandwidthSelect",applyService.BandwidthSelectValueList(BandwidthType));
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(selectList));
	}
	/**
	 * 查询添加页面下拉框的值
	 */
	public void SelectValueList(){
		Map<String,Object> selectList = new HashMap<>();
		selectList.put("IdcName",applyService.SelectValueList(null,"idcName"));
		selectList.put("feeName",applyService.SelectValueList(null,"feeName"));
		selectList.put("bandwidthType",applyService.SelectValueList(null,"bandwidthType"));
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(selectList));
	}
	/**
	 * 代办查询
	 */
	public void IDCFlowList(){
		Integer pageNo= getInteger("pageNo");
		Integer pageSize= getInteger("pageSize");
		String  groupName= getString("groupName");
		String  groupNo = getString("groupNo");
		String  orderNo = getString("orderNo");
		String state = getString("state");
		LayuiPage page = new LayuiPage(pageNo,pageSize);
		page=flowService.listPage(user, page,groupNo,groupName,orderNo,state);
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(page));
	}
    /**
     * 流程跟踪
     */
	public void IDCTaskList(){
		String flowId = getString("flowId");
		String idaApplyId = getString("idaApplyId");
		List<IDCTask> list=null;
		if("".equals(flowId)||flowId==null){
			IDCFlow idcFlow= flowService.queryIDCFlow(idaApplyId);
			list = taskService.getList(idcFlow.getFlowID());
		}else{
			list = taskService.getList(flowId);
		}
		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(list));
	}
	/**
	 * 查询详情
	 * 返回按钮
	 */
	@SuppressWarnings("unused")
	public void findIDCApply(){
		try{
			IDCApply idcApply =  applyService.queryIDCApply(getString("idaApplyId"));
			String processId = getString("flowId");
			String dealNo = getString("dealNo");
			String forkTaskType =getString("forkTaskType");
			String role = "";
			//获取task
			String  btn = "";
			String taskName = "";
			String zsBtn="";
			List<Task> task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).list();
			if(task.size()>1){
				for(int i =0;i<task.size();i++){
					if("A".equals(forkTaskType)&&"省公司IDC业务管理员".equals(task.get(i).getActivityName())){
						btn = "submission";
						role = "FORK";
					}else if("B".equals(forkTaskType)&&"省公司IDC业务稽核管理员".equals(task.get(i).getActivityName())){
						btn = "submission";
						role = "FORK";
					}
				}
			}else if(task.size()==1){
				if ("省公司IDC业务管理员".equals(task.get(0).getActivityName())) {
					if ("0".equals(idcApply.getFeeCode())) {
						if (idcApply.getDiscount()>0 && idcApply.getDiscount()<100){
							role = "ROLE_SGSIDCJKGLSJL";
							btn = "submission";
						}else {
							btn = "agree";
							role = "END";
						}
					}else {
						Double oneU;
						Double towU;
						Double threeU;
						Double fourU;
						Double NumberPrice;
						if ("1200020000101".equals(idcApply.getIdcName())
								||"1200020000102".equals(idcApply.getIdcName())
								||"1200020000103".equals(idcApply.getIdcName())
						){
							//省级
							oneU = 333.00;
							towU = 417.00;
							threeU = 500.00;
							fourU = 583.00;
							NumberPrice = 5000.00;
						}else {
							//地市
							oneU = 267.00;
							towU = 333.00;
							threeU = 400.00;
							fourU = 457.00;
							NumberPrice = 4000.00;
						}
						if(idcApply.getIs95().equals("0")||idcApply.getIs95().equals("2")){
							if (Double.parseDouble(idcApply.getBandwidthPrice()) < 5400*(applyService.queryCharging95(idcApply.getCharging95(),idcApply.getIs95()))+5400
									|| (!"0".equals(idcApply.getRackNumberPrice()) && !"0.00".equals(idcApply.getRackNumberPrice()) ? Double.parseDouble(idcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(idcApply.getOneUPrice()) && !"0.00".equals(idcApply.getOneUPrice()) ? Double.parseDouble(idcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(idcApply.getTowUPrice()) && !"0.00".equals(idcApply.getTowUPrice()) ? Double.parseDouble(idcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(idcApply.getThreeUPrice()) && !"0.00".equals(idcApply.getThreeUPrice()) ? Double.parseDouble(idcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(idcApply.getFourUPrice()) && !"0.00".equals(idcApply.getFourUPrice()) ? Double.parseDouble(idcApply.getFourUPrice()):fourU) < fourU
							) {
								role = "ROLE_SGSIDCJKGLSJL";
								btn = "submission";
							} else {
								btn = "agree";
								role = "END";
							}
						}else{
							Double change;
							if ("1200020003301".equals(idcApply.getBandwidthType())){
								if ("1200020000101".equals(idcApply.getIdcName())
										||"1200020000102".equals(idcApply.getIdcName())
										||"1200020000103".equals(idcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003302".equals(idcApply.getBandwidthType())){
								if ("1200020000101".equals(idcApply.getIdcName())
										||"1200020000102".equals(idcApply.getIdcName())
										||"1200020000103".equals(idcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003303".equals(idcApply.getBandwidthType())){
								if ("1200020000101".equals(idcApply.getIdcName())
										||"1200020000102".equals(idcApply.getIdcName())
										||"1200020000103".equals(idcApply.getIdcName())
								){
									change = 23.00;
								}else {
									change = 21.00;
								}
							}else{
								change = 5400.00;
							}
							if ((!"".equals(idcApply.getBandwidthPrice()) && idcApply.getBandwidthPrice()!=null ? Double.parseDouble(idcApply.getBandwidthPrice()):change) < change
									|| (!"0".equals(idcApply.getRackNumberPrice()) && !"0.00".equals(idcApply.getRackNumberPrice()) ? Double.parseDouble(idcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(idcApply.getOneUPrice()) && !"0.00".equals(idcApply.getOneUPrice()) ? Double.parseDouble(idcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(idcApply.getTowUPrice()) && !"0.00".equals(idcApply.getTowUPrice()) ? Double.parseDouble(idcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(idcApply.getThreeUPrice()) && !"0.00".equals(idcApply.getThreeUPrice()) ? Double.parseDouble(idcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(idcApply.getFourUPrice()) && !"0.00".equals(idcApply.getFourUPrice()) ? Double.parseDouble(idcApply.getFourUPrice()):fourU) < fourU
							) {
								role = "ROLE_SGSIDCJKGLSJL";
								btn = "submission";
							} else {
								btn = "agree";
								role = "END";
							}
						}
					}
				}else if("省公司IDC业务稽核管理员".equals(task.get(0).getActivityName())) {
					if ("0".equals(idcApply.getFeeCode())) {
						if (idcApply.getDiscount()>0 && idcApply.getDiscount()<100){
							role = "ROLE_SGSIDCJKGLSJL";
							btn = "submission";
						}else {
							btn = "agree";
							role = "END";
						}
					}else {
						Double oneU;
						Double towU;
						Double threeU;
						Double fourU;
						Double NumberPrice;
						if ("1200020000101".equals(idcApply.getIdcName())
								||"1200020000102".equals(idcApply.getIdcName())
								||"1200020000103".equals(idcApply.getIdcName())
						){
							//省级
							oneU = 333.00;
							towU = 417.00;
							threeU = 500.00;
							fourU = 583.00;
							NumberPrice = 5000.00;
						}else {
							//地市
							oneU = 267.00;
							towU = 333.00;
							threeU = 400.00;
							fourU = 457.00;
							NumberPrice = 4000.00;
						}
						if(idcApply.getIs95().equals("0")||idcApply.getIs95().equals("2")){
							if (Double.parseDouble(idcApply.getBandwidthPrice()) < 5400*(applyService.queryCharging95(idcApply.getCharging95(),idcApply.getIs95()))+5400
									|| (!"0".equals(idcApply.getRackNumberPrice()) && !"0.00".equals(idcApply.getRackNumberPrice()) ? Double.parseDouble(idcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(idcApply.getOneUPrice()) && !"0.00".equals(idcApply.getOneUPrice()) ? Double.parseDouble(idcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(idcApply.getTowUPrice()) && !"0.00".equals(idcApply.getTowUPrice()) ? Double.parseDouble(idcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(idcApply.getThreeUPrice()) && !"0.00".equals(idcApply.getThreeUPrice()) ? Double.parseDouble(idcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(idcApply.getFourUPrice()) && !"0.00".equals(idcApply.getFourUPrice()) ? Double.parseDouble(idcApply.getFourUPrice()):fourU) < fourU
							) {
								role = "ROLE_SGSIDCJKGLSJL";
								btn = "submission";
							} else {
								btn = "agree";
								role = "END";
							}
						}else{
							Double change;
							if ("1200020003301".equals(idcApply.getBandwidthType())){
								if ("1200020000101".equals(idcApply.getIdcName())
										||"1200020000102".equals(idcApply.getIdcName())
										||"1200020000103".equals(idcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003302".equals(idcApply.getBandwidthType())){
								if ("1200020000101".equals(idcApply.getIdcName())
										||"1200020000102".equals(idcApply.getIdcName())
										||"1200020000103".equals(idcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003303".equals(idcApply.getBandwidthType())){
								if ("1200020000101".equals(idcApply.getIdcName())
										||"1200020000102".equals(idcApply.getIdcName())
										||"1200020000103".equals(idcApply.getIdcName())
								){
									change = 23.00;
								}else {
									change = 21.00;
								}
							}else{
								change = 5400.00;
							}
							if ((!"".equals(idcApply.getBandwidthPrice()) && idcApply.getBandwidthPrice()!=null ? Double.parseDouble(idcApply.getBandwidthPrice()):change) < change
									|| (!"0".equals(idcApply.getRackNumberPrice()) && !"0.00".equals(idcApply.getRackNumberPrice()) ? Double.parseDouble(idcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(idcApply.getOneUPrice()) && !"0.00".equals(idcApply.getOneUPrice()) ? Double.parseDouble(idcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(idcApply.getTowUPrice()) && !"0.00".equals(idcApply.getTowUPrice()) ? Double.parseDouble(idcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(idcApply.getThreeUPrice()) && !"0.00".equals(idcApply.getThreeUPrice()) ? Double.parseDouble(idcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(idcApply.getFourUPrice()) && !"0.00".equals(idcApply.getFourUPrice()) ? Double.parseDouble(idcApply.getFourUPrice()):fourU) < fourU
							) {
								role = "ROLE_SGSIDCJKGLSJL";
								btn = "submission";
							} else {
								btn = "agree";
								role = "END";
							}
						}
					}
				}else if("市公司管理员".equals(task.get(0).getActivityName())) {
					btn = "submission";
					role = "ROLE_SGSIDCYEGLY,ROLE_SGSIDCJHGLY";
					taskName=task.get(0).getActivityName();
				}else if("省重客管理员".equals(task.get(0).getActivityName())) {
					btn = "submission";
					role = "ROLE_SGSIDCYEGLY,ROLE_SGSIDCJHGLY";
					taskName=task.get(0).getActivityName();
				}else if("省公司集客部IDC管理室经理".equals(task.get(0).getActivityName())) {
					if (processId.indexOf("IDCApplyProcess")!=-1&&"1".equals(idcApply.getFeeCode())&&idcApply.getBandwidthTotal() < 1) {
						btn = "agree";
						role = "END";
					}else {
						btn = "submission";
						role = "ROLE_SGSJKBFGFZ";
					}
				}else if("省公司集客部分管副总".equals(task.get(0).getActivityName())) {
						btn = "agree";
						role = "END";
				}
			}else{
				btn = "complete";
			}
			Map<String,Object> result = new HashMap<String, Object>();
			result.put("idcApply", idcApply);
			result.put("btn", btn);
			result.put("role", role);
			result.put("taskName", taskName);
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		}catch(Exception e){
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			Map<String,Object> result = new HashMap<String, Object>();
			result.put("idcApply","");
			result.put("btn","");
			result.put("role","");
			result.put("taskName","");
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		}
	}
	/**
	 * 提交/同意
	 */
	@SuppressWarnings("unused")
	public void submitTask(){
		Map<String, String> result = new HashMap<String, String>();
		try {
			Date date = formatForDate(getStringDate(new Date(),"yyyy-MM-dd HH:mm:ss"));
			String dealNo = getString("dealNo");
			String dealName = getString("dealName");
			String processId = getString("flowId");
			String idcApplyId = getString("idcApplyId");
			String orderNo= getString("orderNo");
			String taskMemo = getString("taskMemo");
			String btn = getString("but");
			String waitId = getString("waitId");
			String role = getString("role");
			String fileId = getString("fileId");
			String forkTaskType =getString("forkTask");
			String jhUserid = getString("jhUserid");
			String idcUserid = getString("idcUserid");
			String idcTaskId = getString("idcTaskId");
			String szkfgUserid = getString("szkfgUserid");
			String sgsldUserid = getString("sgsldUserid");
			
			List<Task> task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).list();
			String taskId = "";
			String taskName = "";
			Map<String, String> map = new HashMap<String, String>();
			IDCApply IdcApply = applyService.queryIDCApply(idcApplyId);
			map.put("btn", btn);
			String taskRoleNameq="省公司IDC业务管理员";//省公司IDC业务稽核管理员
			if(task.size()>1){
				for(int i =0;i<task.size();i++){
					if("A".equals(forkTaskType)&&"省公司IDC业务管理员".equals(task.get(i).getActivityName())){
						taskId = task.get(i).getId();
						taskName=task.get(i).getActivityName();
					}else if("B".equals(forkTaskType)&&"省公司IDC业务稽核管理员".equals(task.get(i).getActivityName())){
						taskId = task.get(i).getId();
						taskName=task.get(i).getActivityName();
					}
				}
				jbpmUtil.completeTask(taskId);
			}else {
				if ("省公司IDC业务管理员".equals(task.get(0).getActivityName())) {
					if ("0".equals(IdcApply.getFeeCode())){
                        if (IdcApply.getDiscount()>0 && IdcApply.getDiscount()<100){
                            map.put("role", "ROLE_SGSIDCJKGLSJL");
                        }else {
                            map.put("role", "END");
                        }
                    }else {
						Double oneU;
						Double towU;
						Double threeU;
						Double fourU;
						Double NumberPrice;
						if ("1200020000101".equals(IdcApply.getIdcName())
								||"1200020000102".equals(IdcApply.getIdcName())
								||"1200020000103".equals(IdcApply.getIdcName())
						){
							//省级
							oneU = 333.00;
							towU = 417.00;
							threeU = 500.00;
							fourU = 583.00;
							NumberPrice = 5000.00;
						}else {
							//地市
							oneU = 267.00;
							towU = 333.00;
							threeU = 400.00;
							fourU = 457.00;
							NumberPrice = 4000.00;
						}
						if(IdcApply.getIs95().equals("0")||IdcApply.getIs95().equals("2")){
							if (Double.parseDouble(IdcApply.getBandwidthPrice()) < 5400*(applyService.queryCharging95(IdcApply.getCharging95(),IdcApply.getIs95()))+5400
									|| (!"0".equals(IdcApply.getRackNumberPrice()) && !"0.00".equals(IdcApply.getRackNumberPrice()) ? Double.parseDouble(IdcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(IdcApply.getOneUPrice()) && !"0.00".equals(IdcApply.getOneUPrice()) ? Double.parseDouble(IdcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(IdcApply.getTowUPrice()) && !"0.00".equals(IdcApply.getTowUPrice()) ? Double.parseDouble(IdcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(IdcApply.getThreeUPrice()) && !"0.00".equals(IdcApply.getThreeUPrice()) ? Double.parseDouble(IdcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(IdcApply.getFourUPrice()) && !"0.00".equals(IdcApply.getFourUPrice()) ? Double.parseDouble(IdcApply.getFourUPrice()):fourU) < fourU
							) {
								map.put("role", "ROLE_SGSIDCJKGLSJL");
							} else {
								map.put("role", "END");
							}
						}else{
							Double change;
							if ("1200020003301".equals(IdcApply.getBandwidthType())){
								if ("1200020000101".equals(IdcApply.getIdcName())
										||"1200020000102".equals(IdcApply.getIdcName())
										||"1200020000103".equals(IdcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003302".equals(IdcApply.getBandwidthType())){
								if ("1200020000101".equals(IdcApply.getIdcName())
										||"1200020000102".equals(IdcApply.getIdcName())
										||"1200020000103".equals(IdcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003303".equals(IdcApply.getBandwidthType())){
								if ("1200020000101".equals(IdcApply.getIdcName())
										||"1200020000102".equals(IdcApply.getIdcName())
										||"1200020000103".equals(IdcApply.getIdcName())
								){
									change = 23.00;
								}else {
									change = 21.00;
								}
							}else{
								change = 5400.00;
							}
							if ((!"".equals(IdcApply.getBandwidthPrice()) && IdcApply.getBandwidthPrice()!=null ? Double.parseDouble(IdcApply.getBandwidthPrice()):change) < change
									|| (!"0".equals(IdcApply.getRackNumberPrice()) && !"0.00".equals(IdcApply.getRackNumberPrice()) ? Double.parseDouble(IdcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(IdcApply.getOneUPrice()) && !"0.00".equals(IdcApply.getOneUPrice()) ? Double.parseDouble(IdcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(IdcApply.getTowUPrice()) && !"0.00".equals(IdcApply.getTowUPrice()) ? Double.parseDouble(IdcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(IdcApply.getThreeUPrice()) && !"0.00".equals(IdcApply.getThreeUPrice()) ? Double.parseDouble(IdcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(IdcApply.getFourUPrice()) && !"0.00".equals(IdcApply.getFourUPrice()) ? Double.parseDouble(IdcApply.getFourUPrice()):fourU) < fourU
							) {
								map.put("role", "ROLE_SGSIDCJKGLSJL");
							} else {
								map.put("role", "END");
							}
						}
                    }
					jbpmUtil.completeTask(task.get(0).getId(), map);
				} else if ("省公司IDC业务稽核管理员".equals(task.get(0).getActivityName())) {
                    if ("0".equals(IdcApply.getFeeCode())){
                        if (IdcApply.getDiscount()>0 && IdcApply.getDiscount()<100){
                            map.put("role", "ROLE_SGSIDCJKGLSJL");
                        }else {
                            map.put("role", "END");
                        }
                    }else {
						Double oneU;
						Double towU;
						Double threeU;
						Double fourU;
						Double NumberPrice;
						if ("1200020000101".equals(IdcApply.getIdcName())
								||"1200020000102".equals(IdcApply.getIdcName())
								||"1200020000103".equals(IdcApply.getIdcName())
						){
							//省级
							oneU = 333.00;
							towU = 417.00;
							threeU = 500.00;
							fourU = 583.00;
							NumberPrice = 5000.00;
						}else {
							//地市
							oneU = 267.00;
							towU = 333.00;
							threeU = 400.00;
							fourU = 457.00;
							NumberPrice = 4000.00;
						}
						if(IdcApply.getIs95().equals("0")||IdcApply.getIs95().equals("2")){
							if (Double.parseDouble(IdcApply.getBandwidthPrice()) < 5400*(applyService.queryCharging95(IdcApply.getCharging95(),IdcApply.getIs95()))+5400
									|| (!"0".equals(IdcApply.getRackNumberPrice()) && !"0.00".equals(IdcApply.getRackNumberPrice()) ? Double.parseDouble(IdcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(IdcApply.getOneUPrice()) && !"0.00".equals(IdcApply.getOneUPrice()) ? Double.parseDouble(IdcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(IdcApply.getTowUPrice()) && !"0.00".equals(IdcApply.getTowUPrice()) ? Double.parseDouble(IdcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(IdcApply.getThreeUPrice()) && !"0.00".equals(IdcApply.getThreeUPrice()) ? Double.parseDouble(IdcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(IdcApply.getFourUPrice()) && !"0.00".equals(IdcApply.getFourUPrice()) ? Double.parseDouble(IdcApply.getFourUPrice()):fourU) < fourU
							) {
								map.put("role", "ROLE_SGSIDCJKGLSJL");
							} else {
								map.put("role", "END");
							}
						}else{
							Double change;
							if ("1200020003301".equals(IdcApply.getBandwidthType())){
								if ("1200020000101".equals(IdcApply.getIdcName())
										||"1200020000102".equals(IdcApply.getIdcName())
										||"1200020000103".equals(IdcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003302".equals(IdcApply.getBandwidthType())){
								if ("1200020000101".equals(IdcApply.getIdcName())
										||"1200020000102".equals(IdcApply.getIdcName())
										||"1200020000103".equals(IdcApply.getIdcName())
								){
									change = 26.00;
								}else {
									change = 23.00;
								}
							}else if ("1200020003303".equals(IdcApply.getBandwidthType())){
								if ("1200020000101".equals(IdcApply.getIdcName())
										||"1200020000102".equals(IdcApply.getIdcName())
										||"1200020000103".equals(IdcApply.getIdcName())
								){
									change = 23.00;
								}else {
									change = 21.00;
								}
							}else{
								change = 5400.00;
							}
							if ((!"".equals(IdcApply.getBandwidthPrice()) && IdcApply.getBandwidthPrice()!=null ? Double.parseDouble(IdcApply.getBandwidthPrice()):change) < change
									|| (!"0".equals(IdcApply.getRackNumberPrice()) && !"0.00".equals(IdcApply.getRackNumberPrice()) ? Double.parseDouble(IdcApply.getRackNumberPrice()):NumberPrice) < NumberPrice
									|| (!"0".equals(IdcApply.getOneUPrice()) && !"0.00".equals(IdcApply.getOneUPrice()) ? Double.parseDouble(IdcApply.getOneUPrice()):oneU) < oneU
									|| (!"0".equals(IdcApply.getTowUPrice()) && !"0.00".equals(IdcApply.getTowUPrice()) ? Double.parseDouble(IdcApply.getTowUPrice()):towU) < towU
									|| (!"0".equals(IdcApply.getThreeUPrice()) && !"0.00".equals(IdcApply.getThreeUPrice()) ? Double.parseDouble(IdcApply.getThreeUPrice()):threeU) < threeU
									|| (!"0".equals(IdcApply.getFourUPrice()) && !"0.00".equals(IdcApply.getFourUPrice()) ? Double.parseDouble(IdcApply.getFourUPrice()):fourU) < fourU
							) {
								map.put("role", "ROLE_SGSIDCJKGLSJL");
							} else {
								map.put("role", "END");
							}
						}
                    }
					jbpmUtil.completeTask(task.get(0).getId(), map);
				} else if  (processId.indexOf("IDCApplyProcess")!=-1&&"省公司集客部IDC管理室经理".equals(task.get(0).getActivityName())){
					if ("1".equals(IdcApply.getFeeCode())&&IdcApply.getBandwidthTotal() < 1) {
						map.put("role", "END");
					}else {
						map.put("role", "ROLE_SGSJKBFGFZ");
					}
					jbpmUtil.completeTask(task.get(0).getId(), map);
				} else{
					jbpmUtil.completeTask(task.get(0).getId());
				}
				taskId=task.get(0).getId();
				taskName=task.get(0).getActivityName();
			}
			//修改流程信息
			flowService.updateIDCApplyFlow(processId, dealNo, dealName,0);
			//保存任务信息
			taskService.completeIDCApplyTaskRecordOne(processId,taskId, taskName,date, taskMemo,1,idcTaskId);
			//查询首页待办
			WaitTask waitTask = waitTaskService.queryWaitByTaskId(waitId);
			if(waitTask!=null){
				//完成首页待办
				waitTaskService.updateWait(waitTask, this.getRequest());
			}
			if(StringUtil.isNotNull(fileId)){
				//添加线下审批附件ID
				applyService.updataFileId(fileId,idcApplyId);
			}
			String idctask="";
			String jhtask ="";
			String zctask="";
			//提交才添加代办
			if("submission".equals(btn)){
				//添加待处理任务
				if(jhUserid!=null && !"".equals(jhUserid) && !"undefined".equals(jhUserid) && !"null".equals(jhUserid)&&
						idcUserid!=null && !"".equals(idcUserid) && !"undefined".equals(idcUserid) && !"null".equals(idcUserid)){
					SystemUser idcuser = systemUserService.getByUserInfoRowNo(Integer.parseInt(idcUserid));
					IDCTask pendingIDCTask = new IDCTask();
					pendingIDCTask.setCreateDate(date);
					pendingIDCTask.setCreatorNo(user.getRowNo()+"");
					pendingIDCTask.setCreatorName(user.getEmployeeName());
					pendingIDCTask.setDealDate(null);
					pendingIDCTask.setDealNo(idcUserid);
					pendingIDCTask.setDealName(idcuser.getEmployeeName());
					pendingIDCTask.setFlowId(processId);
					pendingIDCTask.setState(0);
					pendingIDCTask.setTaskId(null);
					pendingIDCTask.setTaskMemo(null);
					pendingIDCTask.setTaskName("省公司IDC业务管理员");
					taskService.addIDCApplyTask(pendingIDCTask);//添加代办
					idctask=pendingIDCTask.getId();
					SystemUser jhuser = systemUserService.getByUserInfoRowNo(Integer.parseInt(jhUserid));
					IDCTask pendingIDCTaskT = new IDCTask();
					pendingIDCTaskT.setCreateDate(date);
					pendingIDCTaskT.setCreatorNo(user.getRowNo()+"");
					pendingIDCTaskT.setCreatorName(user.getEmployeeName());
					pendingIDCTaskT.setDealDate(null);
					pendingIDCTaskT.setDealNo(jhUserid);
					pendingIDCTaskT.setDealName(jhuser.getEmployeeName());
					pendingIDCTaskT.setFlowId(processId);
					pendingIDCTaskT.setState(0);
					pendingIDCTaskT.setTaskId(null);
					pendingIDCTaskT.setTaskMemo(null);
					pendingIDCTaskT.setTaskName("省公司IDC业务稽核管理员");
					taskService.addIDCApplyTask(pendingIDCTaskT);//添加代办
					jhtask=pendingIDCTaskT.getId();
				}else{
					if(task.size()<2){
						if(!"A".equals(forkTaskType)|| !"B".equals(forkTaskType)){
							IDCTask pendingIDCTask = new IDCTask();
							pendingIDCTask.setCreateDate(date);
							pendingIDCTask.setCreatorNo(user.getRowNo()+"");
							pendingIDCTask.setCreatorName(user.getEmployeeName());
							pendingIDCTask.setDealDate(null);
							pendingIDCTask.setDealNo(getString("dealNo"));
							pendingIDCTask.setDealName(getString("dealName"));
							pendingIDCTask.setFlowId(processId);
							pendingIDCTask.setState(0);
							pendingIDCTask.setTaskId(null);
							pendingIDCTask.setTaskMemo(null);
							pendingIDCTask.setTaskName(null);
							taskService.addIDCApplyTask(pendingIDCTask);//添加代办
							zctask=pendingIDCTask.getId();
						}
					}
				}
			}else if("agree".equals(btn)){ //同意
				String feeCode="";
				if ("0".equals(IdcApply.getFeeCode())) {	//套餐资费
					feeCode = "12000200000003";
				} else {		//独立资费
					if (IdcApply.getIs95().equals("1")) {
						feeCode = "12000200000001";
					}else if (IdcApply.getIs95().equals("2")) {
						feeCode = "12000200000004";
					}else if (IdcApply.getIs95().equals("0")) {
						feeCode = "12000200000002";
					}
				}
				String BandwidthTotal = "0M";
				if (IdcApply.getBandwidthTotal()!=null&&!"".equals(IdcApply.getBandwidthTotal())){
					if (IdcApply.getBandwidthTotal()>=1000){
						Integer Total = IdcApply.getBandwidthTotal()/1000;
						BandwidthTotal = Total.toString()+"G";
					}else {
						BandwidthTotal = IdcApply.getBandwidthTotal().toString()+"M";
					}
				}
				DecimalFormat df =new DecimalFormat("#0.0000");
				Map<String, Object> s6429OpenRetur = null;
				if ("0".equals(IdcApply.getFeeCode())){		//套餐资费
					s6429OpenRetur = IdcService.getInstance().preIDCSvc(IdcApply,
							systemUserService.getUserInfoByRowNo(Integer.valueOf(IdcApply.getCreatorId())),
							feeCode,//资费类型
							IdcApply.getIdcName(),//意向数据中心编码
							IdcApply.getFeeName(),//套餐项目名称
							df.format((Float.valueOf(IdcApply.getDiscount()+"")/100f))+"",//折扣率
							IdcApply.getCharging95(),//95保底量
							IdcApply.getOneUPrice()+"",//1U机位单价（元/月）
							IdcApply.getTowUPrice()+"",//2U机位单价（元/月）
							IdcApply.getThreeUPrice()+"",//3U机位单价（元/月）
							IdcApply.getFourUPrice()+"",//4U机位单价（元/月）
							IdcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
							IdcApply.getTotal().toString(),//总费用（参考值）
							(50*IdcApply.getIpNumber())+".00",//额外IP费用（元/月）
							IdcApply.getIs95(),//九五计费带宽万兆单价（元/月）
							IdcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
							IdcApply.getFeePrice(),//套餐单价（元/月）
							IdcApply.getFeeNo().equals(0)?"":IdcApply.getFeeNo()+"",//套餐数量
							IdcApply.getBandwidthTotalPrice(),//包端口带宽签约总价 （元/月）
							IdcApply.getRackTotalPrice()!=null?IdcApply.getRackTotalPrice():"0",//机架签约总费用
							BandwidthTotal,//总带宽大小
							IdcApply.getBillingCycle(),//出账周期
							(IdcApply.getCountipNumber()+IdcApply.getIpNumber())+"",//IPv4数量
							"1200020003701",//IPv4性质
							IdcApply.getCountipNumber()+"",//IPv4配套数量
							IdcApply.getIpNumber()+"",//IPv4额外数量
							"",//IPV4描述(备注)
							IdcApply.getBandwidthType()!=null?IdcApply.getBandwidthType():"",//带宽模式类别
							IdcApply.getBandwidth()!=null?IdcApply.getBandwidth().toString():"",//单端口带宽大小（单位兆）
							IdcApply.getNumberOfPorts()!=null?IdcApply.getNumberOfPorts().toString():"",//端口数量
							IdcApply.getBroadbandDescription()!=null?IdcApply.getBroadbandDescription():"",//带宽描述(备注)
							"1200020002501",//供电类型
							"1200020002601",//机架规格
							IdcApply.getOneU(),//1U机位数量
							IdcApply.getTowU(),//2U机位数量
							IdcApply.getThreeU(),//3U机位数量
							IdcApply.getFourU(),//4U机位数量
							IdcApply.getRackNumber(),//整机架数量
							IdcApply.getRackDescription()!=null?IdcApply.getRackDescription():"",//机架描述(备注)
							"",//IPv6网段数量
							"",//IPv6网络位
							""//IPV6描述(备注)
					);//推送boss
				}else {
					s6429OpenRetur = IdcService.getInstance().preIDCSvc(IdcApply,
							systemUserService.getUserInfoByRowNo(Integer.valueOf(IdcApply.getCreatorId())),
							feeCode,//资费类型
							IdcApply.getIdcName(),//意向数据中心编码
							IdcApply.getCharging95(),//95保底量
							IdcApply.getOneUPrice()+"",//1U机位单价（元/月）
							IdcApply.getTowUPrice()+"",//2U机位单价（元/月）
							IdcApply.getThreeUPrice()+"",//3U机位单价（元/月）
							IdcApply.getFourUPrice()+"",//4U机位单价（元/月）
							IdcApply.getBandwidthPrice(),//包端口带宽单价（元/M/月）
							IdcApply.getTotal().toString(),//总费用（参考值）
							(50*IdcApply.getIpNumber())+".00",//额外IP费用（元/月）
							IdcApply.getIs95(),//九五计费带宽万兆单价（元/月）
							IdcApply.getRackNumberPrice(),//裸机机柜单价（元/月）
							IdcApply.getBandwidthTotalPrice(),//包端口带宽签约总价 （元/月）
							IdcApply.getRackTotalPrice()!=null?IdcApply.getRackTotalPrice():"0",//机架签约总费用
							BandwidthTotal,//总带宽大小
							IdcApply.getBillingCycle(),//出账周期
							(IdcApply.getCountipNumber()+IdcApply.getIpNumber())+"",//IPv4数量
							"1200020003701",//IPv4性质
							IdcApply.getCountipNumber()+"",//IPv4配套数量
							IdcApply.getIpNumber()+"",//IPv4额外数量
							"",//IPV4描述(备注)
							IdcApply.getBandwidthType()!=null?IdcApply.getBandwidthType():"",//带宽模式类别
							IdcApply.getBandwidth()!=null?IdcApply.getBandwidth().toString():"",//单端口带宽大小（单位兆）
							IdcApply.getNumberOfPorts()!=null?IdcApply.getNumberOfPorts().toString():"",//端口数量
							IdcApply.getBroadbandDescription()!=null?IdcApply.getBroadbandDescription():"",//带宽描述(备注)
							"1200020002501",//供电类型
							"1200020002601",//机架规格
							IdcApply.getOneU(),//1U机位数量
							IdcApply.getTowU(),//2U机位数量
							IdcApply.getThreeU(),//3U机位数量
							IdcApply.getFourU(),//4U机位数量
							IdcApply.getRackNumber(),//整机架数量
							IdcApply.getRackDescription()!=null?IdcApply.getRackDescription():"",//机架描述(备注)
							"",//IPv6网段数量
							"",//IPv6网络位
							""//IPV6描述(备注)
					);//推送boss
				}
				if(null!=s6429OpenRetur.get("RETURN_CODE")&&"0".equals(s6429OpenRetur.get("RETURN_CODE"))){
					if (null != s6429OpenRetur.get("BOSS_FORM_NO")&&null != s6429OpenRetur.get("PHONE_NO")) {
						applyService.setBossUpdateOrder(IdcApply.getId(), s6429OpenRetur.get("BOSS_FORM_NO").toString(),s6429OpenRetur.get("PHONE_NO").toString(),0);
					}else {
						result.put("err", "boos报错数据失败");
						result.put("message", s6429OpenRetur.get("RET_MSG").toString());
						Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
						throw new RuntimeException(" 给事务回滚，自定义");
					}
				}else{
					result.put("err", "推送boss失败");
					result.put("message",""+(s6429OpenRetur.get("RETURN_MSG")==null?(s6429OpenRetur.get("RET_MSG")==null?s6429OpenRetur.get("DETAIL_MSG"):s6429OpenRetur.get("RET_MSG")):s6429OpenRetur.get("RETURN_MSG")));
					Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
					throw new RuntimeException(" 给事务回滚，自定义");
				}
				
			}
			if(jhUserid!=null && !"".equals(jhUserid) && !"undefined".equals(jhUserid) && !"null".equals(jhUserid)&&
					idcUserid!=null && !"".equals(idcUserid) && !"undefined".equals(idcUserid) && !"null".equals(idcUserid)){
				submitWait(idcApplyId
						,orderNo
						,"[IDC]"+IdcApply.getTitle()					
						,processId
						,jhUserid
						,user,jhUserid,"B",jhtask);//添加主页代办
				submitWait(idcApplyId
						,orderNo
						,"[IDC]"+IdcApply.getTitle()					
						,processId
						,idcUserid
						,user,idcUserid,"A",idctask);//添加主页代办
			}else{
				if(task.size()<2){
					if(!"A".equals(forkTaskType)|| !"B".equals(forkTaskType)){
						submitWait(idcApplyId
								,orderNo
								,"[IDC]"+IdcApply.getTitle()					
								,processId
								,dealNo
								,user,dealNo,null,zctask);//添加主页代办
					}
				}
			}
			if(szkfgUserid!=null && !"".equals(szkfgUserid) && !"undefined".equals(szkfgUserid) && !"null".equals(szkfgUserid)){
				showWait(idcApplyId
						,"[IDC]"+IdcApply.getTitle()					
						,processId
						,szkfgUserid
						,user);//添加主页代办
			}
			if(sgsldUserid!=null && !"".equals(sgsldUserid) && !"undefined".equals(sgsldUserid) && !"null".equals(sgsldUserid)){
				showWait(idcApplyId
						,"[IDC]"+IdcApply.getTitle()					
						,processId
						,sgsldUserid
						,user);//添加主页代办
			}
			
			result.put("message", "成功");
			result.put("err", "");
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			result.put("message", "失败");
			result.put("err", e.getMessage());
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
			throw new RuntimeException(" 给事务回滚，自定义");
		}
	}
	/**
	 * 完成
	 */
	public void completeTask(){
		Map<String, String> result = new HashMap<String, String>();
		try {
			IDCApply idcApply =  applyService.queryIDCApply(getString("idcApplyId"));
			System.out.println(getString("idaApplyId"));
			String dealNo = getString("dealNo");
			String dealName = getString("dealName");
			String processId = getString("flowId");
			String waitId = getString("waitId");
			flowService.updateIDCApplyFlow(processId, dealNo, dealName,1);
			applyService.updateState(idcApply.getId(), 1);
			taskService.updateAllTaskState(processId,1);
			//查询首页待办
			WaitTask waitTask = waitTaskService.queryWaitByTaskId(waitId);
			if(waitTask!=null){
				//完成首页待办
				waitTaskService.updateWait(waitTask, this.getRequest());
			}
			result.put("message", "成功");
			result.put("err", "");
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			result.put("message", "失败");
			result.put("err", e.getMessage());
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
			throw new RuntimeException(" 给事务回滚，自定义");
		}
	}
	
	/**
	 * 同意
	 */
	public void agree(){
		Map<String, String> result = new HashMap<String, String>();
		try {
			String waitId = getString("waitId");
			//查询首页待办
			WaitTask waitTask = waitTaskService.queryWaitByTaskId(waitId);
			if(waitTask!=null){
				//完成首页待办
				waitTaskService.updateWait(waitTask, this.getRequest());
			}
			result.put("message", "成功");
			result.put("err", "");
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			result.put("message", "失败");
			result.put("err", e.getMessage());
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
			throw new RuntimeException(" 给事务回滚，自定义");
		}
	}
	
	/**
	 * 退回
	 */
	public void returnIDC(){
		String idcApplyId = getString("idcApplyId");
		String orderNo = getString("orderNo");
		//流程ID
		String processId = getString("flowId");
		//处理人意见
		String taskMemo = getString("taskMemo");
		//当前处里人名称
		String dealName = getString("dealName");
		//当前处理人编号
		String dealNo = getString("dealNo");
		//首页待办ID
		String waitId = getString("waitId");
		String forkTaskType =getString("forkTask");
		String idcTaskId = getString("idcTaskId");
		Map<String, Object> result = new HashMap<String, Object>();
		IDCApply idcApply =  applyService.queryIDCApply(idcApplyId);
		if(waitId==null||"".equals(waitId)){
			logger.info("-----方法：returnIDC 空值：waitId-----");
			return;
		}
		if(processId==null||"".equals(processId)){
			logger.info("-----方法：returnIDC 空值：processId-----");
			return;
		}
		if(dealName==null||"".equals(dealName)){
			logger.info("-----方法：returnIDC 空值：dealName-----");
			return;
		}
		if(dealNo==null||"".equals(dealNo)){
			logger.info("-----方法：returnIDC 空值：dealNo-----");
			return;
		}
		if(taskMemo==null||"".equals(taskMemo)){
			logger.info("-----方法：returnIDC 空值：taskMemo-----");
			return;
		}
		try {
			WaitTask waitTask = null;
			Date date = formatForDate(getStringDate(new Date(),"yyyy-MM-dd HH:mm:ss"));//当前时间
			List<Task> task = jbpmUtil.getTaskService().createTaskQuery().processInstanceId(processId).list();
			if(task.size()>1){
				if("A".equals(forkTaskType)|| "B".equals(forkTaskType)){
					List<WaitTask> waitTaskList = waitTaskService.queryListWaitByTaskId(idcApplyId);
					//修改主页任务状态
					for(int i=0;i<waitTaskList.size();i++){
						waitTaskService.updateWait(waitTaskList.get(i), this.getRequest());
					}
				}
				for(int j=0;j<task.size();j++){
					//修改任务信息
					List<IDCTask> taskList=taskService.getIDCTaskList(processId);
					for(int k=0;k<taskList.size();k++){
						if(taskList.get(k).getTaskName().equals(task.get(0).getActivityName())){
							taskService.completeIDCApplyTaskRecordTwo(processId, task.get(j).getId(), task.get(j).getActivityName(),
									taskList.get(k).getDealNo(), taskList.get(k).getDealName(), date, taskMemo,-1);
						}
					}
				}
			}else{
				waitTask = waitTaskService.queryWaitByTaskId(waitId);
				//修改主页任务状态
				waitTaskService.updateWait(waitTask, this.getRequest());
				taskService.completeIDCApplyTaskRecordOne(processId, task.get(0).getId(), task.get(0).getActivityName(),
						 date, taskMemo,-1,idcTaskId);
			}
			//修改流程信息
			flowService.updateIDCApplyFlow(processId, dealNo,dealName,-1);
			applyService.updateState(idcApplyId, -1);
			//删除流程
			jbpmUtil.deleteProcessInstance(processId);
			returnWait(idcApplyId,idcApply.getTitle(),processId,dealNo,user);
			result.put("message", "成功");
			result.put("err", "");
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			result.put("message", "失败");
			result.put("err",e.getMessage());
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
			throw new RuntimeException(" 给事务回滚，自定义");
		}
	}
	/**
	 * 作废
	 */
	public void invalid(){
		Map<String, String> result = new HashMap<String, String>();
		String idcApplyId = getString("idcApplyId");
		String waitId = getString("waitId");
		String orderNo = getString("orderNo");
		try {
			if(waitId!=null&&waitId!=""){
				//查询首页待办
				WaitTask waitTask = waitTaskService.queryWaitByTaskId(waitId);
				if(waitTask!=null){
					//完成首页待办
					waitTaskService.updateWait(waitTask, this.getRequest());
				}
			}
			applyService.updateState(idcApplyId, -2);
			flowService.updateIDCApplyState(orderNo, -2);
			result.put("message", "成功");
			result.put("err", "");
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			result.put("message", "失败");
			result.put("err", e.getMessage());
			Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(result));
			throw new RuntimeException(" 给事务回滚，自定义");
		}
	}

	/**
	 * 文件上传
	 */
	public void uploadFile(){
		Map<String,Object> map = new HashMap<>();
		try {
			//判断上传文件的名称是否为空
			if (file1 != null) {
				//获取毫秒数
				Long time = System.currentTimeMillis();
				//根据当天日期生成文件夹：名称：
				String urlDate = FileUpload.getDateToString("yyyyMMdd")+"/";
				String  ftpUrl=FileUpload.getFtpURL()+"IDCApprovalFile/"+urlDate;
				File headPath = new File(ftpUrl);//获取文件夹路径
				if(!headPath.exists()){//判断文件夹是否创建，没有创建则创建新文件夹
					headPath.mkdirs();
				}
				String suffix = "."+file1FileName.substring(file1FileName.lastIndexOf(".") + 1);//获取后缀名
				if(StringUtils.isEmpty(suffix)){
					writeText("0");
				}
				if (FileUpload.upload(ftpUrl, file1, time + suffix)) {

					final Attachment attachmentEntity = new Attachment();
					attachmentEntity.setAttachmentName(time + suffix);// 防重名
					attachmentEntity.setAttachmentUrl("IDCApprovalFile/"+urlDate+time + suffix);
					attachmentEntity.setUploadDate(new Date());
					attachmentEntity.setRealName(file1FileName);
					attachmentEntity.setUploadUser((SystemUser) this
							.getRequest()
							.getSession()
							.getAttribute(
									SystemConfig.instance().getSessionItems()
											.getCurrentLoginUser()));
					String attachmentId = this.attachmentService
							.addEntity(attachmentEntity);
					map.put("state","1");
					map.put("file",attachmentEntity);
					//审计接口调用
					if("start".equals(audit)){
						final String request= DateUtil.getIpAddr(this.getRequest());
						new Thread(new Runnable() {
							@Override
							public void run() {
								//审计接口调用
								CommLogs.requFlies(attachmentEntity.getUploadUser().getLoginName(), "0", attachmentEntity.getRealName(), attachmentEntity.getUploadUser().getEmployeeName(), "1", request);
							}
						}).start();
					}
				} else {
					map.put("state","0");
				}
			} else {
				map.put("state","0");
			}
			writeText(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(map));
		} catch (Exception e) {
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			writeText("0");
		}
	}
	//查询线下联合审批附件
	public void queryfile(){
		String fileId = getString("fileId");
		Attachment attachment = null;
		if(StringUtil.isNotNull(fileId)){
			attachment = attachmentService.getAttachmentById(fileId);
		}

		Write(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(attachment));
	}
	/**
	 * 下载文件
	 */
	public void downloadContractFujian() {
		try {
			String id = getString("id");//附件id
			applyService.downloFujian(id);
		}
		catch (Exception e) {
			e.printStackTrace();
			logger.error("IDC错误信息："+e.getMessage(),e);
			Write("NO");
		}
	}

	/**
	 * 生成主页代办
	 * @param ID uuID
	 * @param FlowName 标题
	 * @param FlowId 流程ID
	 * @param userid 登陆人ID
	 * @param user 登陆人
	 */
	public void submitWait(String ID,String orderNo,String FlowName, String FlowId, String userid, SystemUser user,String dealNo,String type,String taskid) {
		WaitTask wt = new WaitTask();
		wt.setName(FlowName);
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/IDC/handleIdcApplytwo.jsp?idaApplyId=" + ID + "&flowId="// 流程ID
				+ FlowId+"&dealNo="+dealNo+"&type="+type+"&idcTaskId="+taskid);
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(IDCApply.IDCApply);
		wt.setTaskId(ID);
		waitTaskService.saveWait(wt,this.getRequest());
	}
	
	/**
	 * 生成主页代办
	 * @param ID uuID
	 * @param FlowName 标题
	 * @param FlowId 流程ID
	 * @param userid 登陆人ID
	 * @param user 登陆人
	 */
	public void showWait(String ID,String FlowName, String FlowId, String userid, SystemUser user) {
		WaitTask wt = new WaitTask();
		wt.setName(FlowName);
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/IDC/showIdcApply.jsp?idaApplyId=" + ID + "&flowId="// 流程ID
				+ FlowId);
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(IDCApply.IDCApply);
		wt.setTaskId(ID);
		waitTaskService.saveWait(wt,this.getRequest());
	}

	// 退回待办生成
	public void returnWait(String ID,String title, String FlowId, String userid, SystemUser user) {
		WaitTask wt = new WaitTask();
		wt.setName("[IDC]"+title);
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/IDC/idcApplyTwo.jsp?idaApplyId=" + ID + "&flowId="// 流程ID
				+ FlowId);
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(IDCApply.IDCApply);
		wt.setTaskId(ID);
		waitTaskService.saveWait(wt,this.getRequest());
	}
	//完成代办生成
	public void completeWait(String ID,String orderNo,String FlowName, String FlowId, String userid, SystemUser user,String dealNo) {
		WaitTask wt = new WaitTask();
		wt.setName(FlowName);
		wt.setCreationTime(new Date());
		wt.setUrl("jsp/IDC/IDCAudit.jsp?idaApplyId=" + ID + "&flowId="// 流程ID
				+ FlowId+"&dealNo="+dealNo);
		SystemUser USER = systemUserService.getUserInfoRowNo(Integer.parseInt(userid));
		wt.setState(WaitTask.HANDLE);
		wt.setHandleUserId(USER.getRowNo());
		wt.setHandleUserName(USER.getEmployeeName());
		wt.setHandleLoginName(USER.getLoginName());
		wt.setCreateUserId(user.getRowNo());
		wt.setCreateUserName(user.getEmployeeName());
		wt.setCreateLoginName(user.getLoginName());
		wt.setCode(IDCApply.IDCApply);
		wt.setTaskId(ID);
		waitTaskService.saveWait(wt,this.getRequest());
	}

	/**
	 * 日期转换2
	 *
	 * @param strDate
	 * @return
	 * @throws ParseException
	 */
	public Date formatForDate(String strDate) throws ParseException {
		Date date = null;
		if (strDate != null && !"".equals(strDate)) {
			SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
			date = format.parse(strDate);
		}
		return date;
	}
	
	public void getGroupCustomer(){
		String groupCoding = getString("groupCoding");
		List<GroupCustomer> map =applyService.getGroupCustomer(groupCoding);
		String json = com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormats(map);
		Write(json);
	}
	
	/**
	 * 工单分页查询
	 */
	public void queryList() {
		try {
			Integer pageNo = getInteger("pageNo");
			Integer pageSize = getInteger("pageSize"); 
			LayuiPage page = new LayuiPage(pageNo, pageSize);

			Integer tabindex = getInteger("tabindex");		//表格类型   审批中  全部。。。。。
			String number = getString("number");				//工单申请编号
			String title = getString("title");				//工单标题
			String groupCode = getString("groupCode");		//集团280编码
			String state = getString("state");				//工单状态
            String startTime = this.getString("startTime");	//工单创建时间
            String endTime = this.getString("endTime");		//工单结束时间
            String BUSI_REQ_TYPE = getString("BUSI_REQ_TYPE");//工单类型 变更/申请
			String pageStr = applyService.queryReceiptApply(page, number,title,groupCode, state,user.getRowNo(),tabindex,startTime,endTime,BUSI_REQ_TYPE);
			this.Write(pageStr);
		} catch (Exception e) {
			logger.error("IDC错误信息："+e.getMessage(),e);
			e.printStackTrace();
		} 
	}

	/**
	 * IDC列表信息查询
	 */
	public void queryParameterByID() {
		try {
			String idcname = getString("idcname");
			String bandwidthType = getString("bandwidthType");
			String feeName = getString("feeName");
			String bandwidth = getString("bandwidth");
			List<Map<String,Object>> idcnamelist = applyService.queryParameterByID(idcname);
			List<Map<String,Object>> bandwidthTypelist = applyService.queryParameterByID(bandwidthType);
			List<Map<String,Object>> feeNamelist = applyService.queryParameterByID(feeName);
			List<Map<String,Object>> bandwidthlist = applyService.queryParameterByID(bandwidth);
			Map<String,String> idcnameMap = new HashMap<>();
			idcnameMap.put("idcname",idcnamelist.size()>0?idcnamelist.get(0).get("PARAM_NAME").toString():"---");
			idcnameMap.put("bandwidthType",bandwidthTypelist.size()>0?bandwidthTypelist.get(0).get("PARAM_NAME").toString():"---");
			idcnameMap.put("feeName",feeNamelist.size()>0?feeNamelist.get(0).get("PARAM_NAME").toString():"---");
			idcnameMap.put("bandwidth",bandwidthlist.size()>0?bandwidthlist.get(0).get("PARAM_NAME").toString():"---");
			Write(JSONHelper.SerializeWithNeedAnnotation(idcnameMap));
		} catch (Exception e) {
			logger.error("IDC错误信息："+e.getMessage(),e);
			e.printStackTrace();
		}
	}

	/**
	 * 变更工单列表查询
	 */
	public void queryChangeSelectByUser() {
		Result resultTwo = ResultGenerator.genSuccessResult();
		try {
			String PHONE_NO = getString("PHONE_NO");
			String UNIT_ID = getString("UNIT_ID");
			if (UNIT_ID!=null&&!"".equals(UNIT_ID)){
				Result result = IdcService.getInstance().getIdcChangeList(PHONE_NO,UNIT_ID,user);
				Write(result.toString());
			}else {
				resultTwo.setCode(ResultCode.FAIL);
				resultTwo.setMessage("亲爱的同事，您的集团编号为空了！,请确认！");
				Write(resultTwo.toString());
			}
		} catch (Exception e) {
			logger.error("IDC错误信息："+e.getMessage(),e);
			resultTwo.setCode(ResultCode.FAIL);
			resultTwo.setMessage("亲爱的同事，接口查询错误！");
			resultTwo.setData(e.getMessage());
			Write(resultTwo.toString());
			e.printStackTrace();
		}
	}

	/**
	 * 变更工单详情查询
	 */
	public void queryChangeOrderByOrderId() {
		Result result = ResultGenerator.genSuccessResult();
		try {
			String ID_NO = getString("ID_NO");
			String prod_id = getString("PROD_ID");
			String prod_prcid = getString("PROD_PRCID");
			String unit_id = getString("UNIT_ID");
			result = IdcService.getInstance().ProdDynamicprodAttrInitSvcEntity(ID_NO,prod_id,prod_prcid,unit_id,user);
			Write(result.toString());
		} catch (Exception e) {
			logger.error("IDC错误信息："+e.getMessage(),e);
			result.setCode(ResultCode.FAIL);
			result.setMessage("亲爱的同事，接口查询错误！");
			result.setData(e.getMessage());
			Write(result.toString());
			e.printStackTrace();
		}
	}

	public void queryAllOrderPage(){
		try {
			Integer pageNo = this.getInteger("pageNo");
			Integer pageSize = this.getInteger("pageSize");
			LayuiPage page = new LayuiPage(pageNo, pageSize);

			String orderNo = getString("orderNo");				//工单编号
			String title = getString("title");					//工单名称
			String UNIT_ID = getString("UNIT_ID");				//集团编号
			String creatorName = getString("creatorName");		//创建人名称
			String PHONE_NO = getString("PHONE_NO");				//计费单号
			String BUSI_REQ_TYPE = getString("BUSI_REQ_TYPE");	//申请类型
			String orderState = getString("orderState");			//工单状态
			String stateCreatorDate = getString("stateCreatorDate");	//工单创建时间   开始时间
			String endCreatorDate = getString("endCreatorDate");		//工单创建时间   结束时间

			page = applyService.queryAllOrderByPage(page,orderNo,title,UNIT_ID,creatorName,PHONE_NO,BUSI_REQ_TYPE,orderState,stateCreatorDate,endCreatorDate);
			String json = JSONHelper.SerializeWithNeedAnnotationDateFormats(page);
			this.Write(json);
		} catch (Exception e) {
			e.printStackTrace();
			this.Write("列表加载失败！");
		}
	}

	public void QuerAllByExcel(){
		try {
			String orderNo = getString("orderNo");				//工单编号
			String title = getString("title");					//工单名称
			String UNIT_ID = getString("UNIT_ID");				//集团编号
			String creatorName = getString("creatorName");		//创建人名称
			String PHONE_NO = getString("PHONE_NO");				//计费单号
			String BUSI_REQ_TYPE = getString("BUSI_REQ_TYPE");	//申请类型
			String orderState = getString("orderState");			//工单状态
			String stateCreatorDate = getString("stateCreatorDate");	//工单创建时间   开始时间
			String endCreatorDate = getString("endCreatorDate");		//工单创建时间   结束时间
			List<Map<String,Object>> mapList = applyService.queryAllOrderByList(orderNo,title,UNIT_ID,creatorName,PHONE_NO,BUSI_REQ_TYPE,
					orderState,stateCreatorDate,endCreatorDate);
			applyService.queryAllOrderToExcel(mapList);
		} catch (Exception e) {
			e.printStackTrace();
			this.Write("列表加载失败！");
		}
	}
}
