package com.xinxinsoft.sendComms;

import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.result.Result;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * @Description: TODO
 * @Author: TX
 * @Date: 2022/6/15 15:48
 * @Version: 1.0
 */
public class MentaryContractSrv {

    private static final Logger logger= LoggerFactory.getLogger(MentaryContractSrv.class);

    private static Boolean isES = false;
    //测试环境
    private static final String ESB_URL_172= "http://**************:51000/esbWS/rest/";
    //正式环境
    private static final String ESB_URL_38 = "http://*************:51000/esbWS/rest/";
    static {
        if("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())){
            isES = true;
        }
    }
    private MentaryContractSrv(){}

    private static MentaryContractSrv MentaryContractSrv =null;

    public static MentaryContractSrv getInstance() {
        if (MentaryContractSrv == null) {
            synchronized (MentaryContractSrv.class) {
                if (MentaryContractSrv == null) {
                    MentaryContractSrv = new MentaryContractSrv();
                }
            }
        }
        return MentaryContractSrv;
    }

    /**
     * @Description TODO 补录合同查询
     * <AUTHOR> 
     * @param cust_id       集团编号
     * @param phone_no      专线号码
     * @param login_no      当前用户boss工号
     * @return com.xinxinsoft.utils.result.Result
     * @Date 2022/6/15 16:13 
     **/
    public Result qryPhoneContractEntity(String  cust_id, String phone_no ,String login_no){
        JSONObject BODY= new JSONObject();
        BODY.put("ENTITY_NAME", "qryPhoneContractEntity");
        BODY.put("CUST_ID", cust_id);
        BODY.put("PHONE_NO", phone_no);
        BODY.put("DO_METHOD","qry");
        BODY.put("PAGE_NUM","1");
        BODY.put("PAGE_SIZE","5");
        BODY.put("LOGIN_NO",login_no);
        String getParas = ESBReqMsgUtil.packMsgByRoute("14",login_no,BODY);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"com_sitech_salemng_service_common_inter_ICommonServiceAoSvc_commonQry",getParas,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_38+"com_sitech_salemng_service_common_inter_ICommonServiceAoSvc_commonQry", getParas);
        logger.info("补录合同查询：===>:"+resultStr);
        return  HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }

    public Result updatePhoneContractEntity(String  contract_no, String contract_name ,String audit_date, String paycycle ,
                                            String effect_date,String losteffect_date,String group_id ,String group_name,String region_id,String phone_no,String login_no,String unit_id){
        JSONObject BODY= new JSONObject();
        BODY.put("ENTITY_NAME", "qryPhoneContractEntity");
        BODY.put("DO_METHOD","update");
        BODY.put("IS_CONTRACT","Y");
        BODY.put("REMARK","指南针合同重录");

        JSONObject CONTRACT_INFO= new JSONObject();
        CONTRACT_INFO.put("CONTRACT_ID",contract_no);                   //合同编码
        CONTRACT_INFO.put("CONTRACT_NAME",contract_name);               //合同名称
        CONTRACT_INFO.put("CONTRACT_SIGN_DATE",audit_date);             //合同签订时间
        CONTRACT_INFO.put("CUST_ID",unit_id);                           //集团280
        CONTRACT_INFO.put("OPER_NO",login_no);                          //操作工号
        CONTRACT_INFO.put("PAYCYCLE",paycycle);                         //周期
        CONTRACT_INFO.put("START_DATE",effect_date);                    //协议生效时间
        CONTRACT_INFO.put("SIGN_DATE",losteffect_date);                 //协议失效时间

        BODY.put("CONTRACT_ID", contract_no);           //合同编码
        BODY.put("GROUP_ID", group_id);                 //区县id
        BODY.put("GROUP_NAME", group_name);             //区县
        BODY.put("REGION_ID", region_id);               //地市id
        BODY.put("PHONE_NO",phone_no);                  //专线号码
        BODY.put("OPER_NO",login_no);                   //操作工号
        BODY.put("UNIT_ID",unit_id);                    //集团280
        BODY.put("CONTRACT_INFO",CONTRACT_INFO);
        String getParas = ESBReqMsgUtil.packMsgByRoute("14",login_no,BODY);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"com_sitech_salemng_service_common_inter_ICommonServiceAoSvc_commonQry",getParas,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172+"com_sitech_salemng_service_common_inter_ICommonServiceAoSvc_commonQry", getParas);
        logger.info("补录合同：===>:"+resultStr);
        return  HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
    }

    public static void main(String[] args) {
        MentaryContractSrv.getInstance().qryPhoneContractEntity("2803115683","","aa0002107");
    }
}
