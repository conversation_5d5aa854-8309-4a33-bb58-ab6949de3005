package com.xinxinsoft.sendComms.omsService;

import com.xinxinsoft.entity.pms.PmsProdPriceInfo;
import com.xinxinsoft.entity.pms.PmsProductInfo;
import com.xinxinsoft.entity.pms.PmsProductLabel;
import com.xinxinsoft.sendComms.CMCC1000OpenService;
import com.xinxinsoft.sendComms.ESBReqMsgUtil;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.service.appOpenService.OMSService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.JSONHelper;
import com.xinxinsoft.utils.result.Result;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * @version v1.0
 * @ProjectName: EOM
 * @Description: TODO(获取BOSS系统6611的全部产品信息)
 * @Author: Leo
 * @Date: 2021/8/12 16:20
 */
public class GoodsPrcInfoSrv {

    private static final Logger logger= LoggerFactory.getLogger(GoodsPrcInfoSrv.class);

    private static Boolean isES = false;
    //测试环境
    private static final String ESB_URL_172= "http://**************:51000/esbWS/rest/";
    //正式环境
    private static final String ESB_URL_38 = "http://*************:51000/esbWS/rest/";
    static {
        if("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())){
            isES = true;
        }
    }
    private GoodsPrcInfoSrv(){}
    private static GoodsPrcInfoSrv goodsPrcInfoSrv=null;
    public static GoodsPrcInfoSrv getInstance() {
        if (goodsPrcInfoSrv == null) {
            synchronized (GoodsPrcInfoSrv.class) {
                if (goodsPrcInfoSrv == null) {
                    goodsPrcInfoSrv = new GoodsPrcInfoSrv();
                }
            }
        }
        return goodsPrcInfoSrv;
    }

    /**
     * @Return: com.xinxinsoft.utils.result.Result
     * @Author: Leo
     * @Date: 2021/8/13 14:52
     * @Description: 获取6611下所有的产品信息，用于产品信息同步
     */
    public Result QryAllGoodsInfo(){
        JSONObject bodyContent=new JSONObject();
        bodyContent.put("WORN_SERV_CODE","sGrpAllGoodsSvc");
        bodyContent.put("LABEL_CLASS_ID", "ALL");


        JSONObject commonInfo=new JSONObject();
        commonInfo.put("PROVINCE_GROUP", "10008");  //默认10008-四川

        bodyContent.put("COMMON_INFO",commonInfo);
        String paras= ESBReqMsgUtil.packMsgNoBodyByRoute("15","11",bodyContent);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"com_sitech_ordersvc_person_comp_inter_s4000_IP4000BusiRouterCoSvc_pQrySwitch",paras,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESB_URL_38+"com_sitech_ordersvc_person_comp_inter_s4000_IP4000BusiRouterCoSvc_pQrySwitch", paras);
        Result result= HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
        logger.info("===>:"+result);
        return result;
    }


    /**
     * @Return: com.xinxinsoft.utils.result.Result
     * @Author: Leo
     * @Date: 2021/8/13 14:52
     * @Description: 获取6611下所有的产品信息，用于产品信息同步
     */
    public Result IDynamicSqlQryAoSvc(){
        JSONObject bodyContent=new JSONObject();
        JSONObject authen_info=new JSONObject();
        authen_info.put("AUTHEN_OP_CODE", "6655");
        bodyContent.put("AUTHEN_INFO",authen_info);

        JSONObject busi_info=new JSONObject();
        busi_info.put("SVC_NAME", "d665522");
        busi_info.put("", "");
        busi_info.put("PARAM_2", "0");
        bodyContent.put("BUSI_INFO",busi_info);
        String paras= ESBReqMsgUtil.packMsgByRoute("15","11",bodyContent);
        if(isES) {
            //正式服务器
            return HttpURLConnectClientFactory.responseByCharset(ESB_URL_38+"com_sitech_ordersvc_person_atom_inter_s1018_IDynamicSqlQryAoSvc_qry",paras,"UTF-8");
        }
        String resultStr= CMCC1000OpenService.getInstance().bdcesPatamss(ESB_URL_172+"com_sitech_ordersvc_person_atom_inter_s1018_IDynamicSqlQryAoSvc_qry", paras);
        Result result= HttpURLConnectClientFactory.analyticParamsByResultTest(resultStr);
        logger.info("===>:"+result);
        return result;
    }

    public static void main(String[] args) {
        //OMSService omsService = new OMSService();
       /* List<PmsProductInfo> listPmsProductInfo = new ArrayList<>();
        List<PmsProductLabel> listPmsProductLabel = new ArrayList<>();
        List<PmsProdPriceInfo> listPmsProdPriceInfo = new ArrayList<>();*/
        Result result= GoodsPrcInfoSrv.getInstance().QryAllGoodsInfo();

    }
}
