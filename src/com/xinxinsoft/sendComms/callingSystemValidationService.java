package com.xinxinsoft.sendComms;

import com.xinxinsoft.entity.oms.ServiceStandardizationTesting;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;

/**
 * 调用新系统验证类
 */
public class callingSystemValidationService {
    private static String GETSYSTOKEN = "http://10.114.129.109:8080/apis/eomApi/sysController/getSysToken?";//获取token路径

    private static String CHECKPAPERDOCSEAL = "http://10.114.129.109:8080/apis/eom-ocr/ocr/recognize/stamp?";//印章验真能力

    private static String STAMPCOMPARE = "http://10.114.129.109:8080/apis/eom-ocr/ocr/recognize/stampCompare?";//印章相似度

    private static String STAMP = "http://10.114.129.109:8080/apis/eom-ocr/ocr/recognize/stamp?";//印章识别

    private static String TEXT = "http://10.114.129.109:8080/apis/eom-ocr/ocr/recognize/text?";//文字识别

    private static String IDCARD = "http://10.114.129.109:8080/apis/eom-ocr/ocr/recognize/idCard?";//身份证

    private static String qrcode = "http://10.114.129.109:8080/apis/eom-ocr/ocr/recognize/qrcode?";//二维码

    private callingSystemValidationService() {
    }

    private static callingSystemValidationService callingSystemValidationService = null;

    public static callingSystemValidationService getInstance() {
        if (callingSystemValidationService == null) {
            synchronized (callingSystemValidationService.class) {
                if (callingSystemValidationService == null) {
                    callingSystemValidationService = new callingSystemValidationService();
                }
            }
        }
        return callingSystemValidationService;
    }

    public String getSysToken() {
        try {
            // 正式环境地址
            URL restUrl = new URL(GETSYSTOKEN);

            HttpURLConnection conn = (HttpURLConnection) restUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "application/json");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("sysSource", "eom");

            OutputStreamWriter outStreamWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            outStreamWriter.write("{\"username\":\"sys_eom_user\",\"password\":\"eom20230822\"}");
            outStreamWriter.flush();
            outStreamWriter.close();

            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line, resultStr = "";
            while (null != (line = bReader.readLine())) {
                resultStr += line;
            }
            bReader.close();
            //{"code":200,"data":{"token":"b5827197e97bec9c718076d8e1fa89b1ff7d3f43725cb241feed01e72a7c4044de501362cc8a47c3"},"message":"操作成功"}
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 盖章识别
     *
     * @param file
     * @return
     */
    public String checkPaperDocSeal(File file, String getStr, String token) {
        try {
            // 正式环境地址
            URL restUrl = new URL(CHECKPAPERDOCSEAL + "?" + getStr);

            // 创建JSONObject对象
//            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("majorWords", "asas");//集团名称
//            jsonObject.put("bossNo", "12");//boss工号 非必填
//            jsonObject.put("oprPhone", "1234556");//操作人手机号
//            jsonObject.put("sysModule", "asas");//系统模板
//            jsonObject.put("batchNo", "asa");//操作批次号
//            jsonObject.put("bizid", "1225");//对应业务标识
//            jsonObject.put("orderNo", "CD2023061600015");
//            jsonObject.put("groupCode", "2803696055");//集团编号
//            jsonObject.put("token", "2803696055");//token

            // 创建文件对象
//            File file = new File("D:\\20210204\\456.jpg");


            // 创建连接
            HttpURLConnection conn = (HttpURLConnection) restUrl.openConnection();
            conn.setRequestMethod("GET");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("sysSource", "eom");
            conn.setRequestProperty("token", token);
            // 设置请求体参数
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"jsonObject\"\r\n\r\n");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"\r\n");
            outStreamWriter.write("Content-Type: application/octet-stream\r\n\r\n");
            outStreamWriter.flush();

            // 写入文件内容
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                conn.getOutputStream().write(buffer, 0, bytesRead);
            }
            fileInputStream.close();

            // 写入结束标识
            outStreamWriter.write("\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--\r\n");
            outStreamWriter.flush();
            outStreamWriter.close();

            // 获取响应
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line, resultStr = "";
            while ((line = bReader.readLine()) != null) {
                resultStr += line;
            }
            bReader.close();
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /**
     * 文字识别
     *
     * @param file
     * @return
     */
    public String text(File file, String getStr, String token) {
        try {
            // 正式环境地址
            URL restURL = new URL(TEXT + getStr);
            // 创建JSONObject对象
            JSONObject jsonObject = new JSONObject();

            // 创建连接
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("sysSource", "eom");
            conn.setRequestProperty("token", token);
            // 设置请求体参数
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"jsonObject\"\r\n\r\n");
            outStreamWriter.write(jsonObject.toString() + "\r\n");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"\r\n");
            outStreamWriter.write("Content-Type: application/octet-stream\r\n\r\n");
            outStreamWriter.flush();

            // 写入文件内容
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                conn.getOutputStream().write(buffer, 0, bytesRead);
            }
            fileInputStream.close();

            // 写入结束标识
            outStreamWriter.write("\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--\r\n");
            outStreamWriter.flush();
            outStreamWriter.close();

            // 获取响应
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line, resultStr = "";
            while ((line = bReader.readLine()) != null) {
                resultStr += line;
            }
            bReader.close();
//            System.out.println(resultStr);
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public String idCard(File file, String getStr, String token) {
        try {
            // 正式环境地址
            URL restURL = new URL(IDCARD + getStr);
            // 创建JSONObject对象
            JSONObject jsonObject = new JSONObject();
            // 创建连接
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("sysSource", "eom");
            conn.setRequestProperty("token", token);
            // 设置请求体参数
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"jsonObject\"\r\n\r\n");
            outStreamWriter.write(jsonObject.toString() + "\r\n");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"\r\n");
            outStreamWriter.write("Content-Type: application/octet-stream\r\n\r\n");
            outStreamWriter.flush();

            // 写入文件内容
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                conn.getOutputStream().write(buffer, 0, bytesRead);
            }
            fileInputStream.close();

            // 写入结束标识
            outStreamWriter.write("\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--\r\n");
            outStreamWriter.flush();
            outStreamWriter.close();

            // 获取响应
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line, resultStr = "";
            while ((line = bReader.readLine()) != null) {
                resultStr += line;
            }
            bReader.close();
//            System.out.println(resultStr);
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    /*
     * <AUTHOR>
     * @Date 2023/9/20 14:52
     * @Description 二维码识别
     **/
    public String qrcode(File file, String getStr, String token) {
        try {
            // 正式环境地址
            URL restURL = new URL(qrcode + getStr);
            // 创建JSONObject对象
            JSONObject jsonObject = new JSONObject();

            // 创建连接
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("sysSource", "eom");
            conn.setRequestProperty("token", token);
            // 设置请求体参数
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"jsonObject\"\r\n\r\n");
            outStreamWriter.write(jsonObject.toString() + "\r\n");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"\r\n");
            outStreamWriter.write("Content-Type: application/octet-stream\r\n\r\n");
            outStreamWriter.flush();

            // 写入文件内容
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                conn.getOutputStream().write(buffer, 0, bytesRead);
            }
            fileInputStream.close();

            // 写入结束标识
            outStreamWriter.write("\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--\r\n");
            outStreamWriter.flush();
            outStreamWriter.close();

            // 获取响应
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line, resultStr = "";
            while ((line = bReader.readLine()) != null) {
                resultStr += line;
            }
            bReader.close();
//            System.out.println(resultStr);
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public String checkPaperStamp(File file, String getStr, String token) {
        try {
            // 正式环境地址
            URL restURL = new URL(STAMPCOMPARE + getStr);
            // 创建JSONObject对象
            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("majorWords", "asas");//集团名称
//            jsonObject.put("bossNo", "12");//boss工号 非必填
//            jsonObject.put("oprPhone", "1234556");//操作人手机号
//            jsonObject.put("sysModule", "asas");//系统模板
//            jsonObject.put("batchNo", "asa");//操作批次号
//            jsonObject.put("bizid", "1225");//对应业务标识
//            jsonObject.put("orderNo", "CD2023061600015");
//            jsonObject.put("groupCode", "2803696055");//集团编号
//            jsonObject.put("token", "2803696055");//token

            // 创建文件对象
//            File file = new File("D:\\20210204\\456.jpg");
            // 创建连接
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("sysSource", "eom");
            conn.setRequestProperty("token", token);
            // 设置请求体参数
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"jsonObject\"\r\n\r\n");
            outStreamWriter.write(jsonObject.toString() + "\r\n");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"\r\n");
            outStreamWriter.write("Content-Type: application/octet-stream\r\n\r\n");
            outStreamWriter.flush();

            // 写入文件内容
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                conn.getOutputStream().write(buffer, 0, bytesRead);
            }
            fileInputStream.close();

            // 写入结束标识
            outStreamWriter.write("\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--\r\n");
            outStreamWriter.flush();
            outStreamWriter.close();

            // 获取响应
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line, resultStr = "";
            while ((line = bReader.readLine()) != null) {
                resultStr += line;
            }
            bReader.close();
//            System.out.println(resultStr);
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public String stamp(File file, String getStr, String token) {
        try {
            // 正式环境地址
            URL restURL = new URL(STAMP + getStr);
            // 创建JSONObject对象
            JSONObject jsonObject = new JSONObject();
//            jsonObject.put("majorWords", "asas");//集团名称
//            jsonObject.put("bossNo", "12");//boss工号 非必填
//            jsonObject.put("oprPhone", "1234556");//操作人手机号
//            jsonObject.put("sysModule", "asas");//系统模板
//            jsonObject.put("batchNo", "asa");//操作批次号
//            jsonObject.put("bizid", "1225");//对应业务标识
//            jsonObject.put("orderNo", "CD2023061600015");
//            jsonObject.put("groupCode", "2803696055");//集团编号
//            jsonObject.put("token", "2803696055");//token

            // 创建文件对象
//            File file = new File("D:\\20210204\\456.jpg");
            // 创建连接
            HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
            conn.setRequestMethod("POST");
            conn.setDoOutput(true);
            conn.setAllowUserInteraction(false);
            conn.setRequestProperty("Content-Type", "multipart/form-data;boundary=----WebKitFormBoundary7MA4YWxkTrZu0gW");
            conn.setRequestProperty("Charset", "utf-8");
            conn.setRequestProperty("sysSource", "eom");
            conn.setRequestProperty("token", token);
            // 设置请求体参数
            OutputStreamWriter outStreamWriter = new OutputStreamWriter(conn.getOutputStream(), "UTF-8");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"jsonObject\"\r\n\r\n");
            outStreamWriter.write(jsonObject.toString() + "\r\n");
            outStreamWriter.write("------WebKitFormBoundary7MA4YWxkTrZu0gW\r\n");
            outStreamWriter.write("Content-Disposition: form-data; name=\"file\"; filename=\"" + file.getName() + "\"\r\n");
            outStreamWriter.write("Content-Type: application/octet-stream\r\n\r\n");
            outStreamWriter.flush();

            // 写入文件内容
            FileInputStream fileInputStream = new FileInputStream(file);
            byte[] buffer = new byte[1024];
            int bytesRead;
            while ((bytesRead = fileInputStream.read(buffer)) != -1) {
                conn.getOutputStream().write(buffer, 0, bytesRead);
            }
            fileInputStream.close();

            // 写入结束标识
            outStreamWriter.write("\r\n------WebKitFormBoundary7MA4YWxkTrZu0gW--\r\n");
            outStreamWriter.flush();
            outStreamWriter.close();

            // 获取响应
            BufferedReader bReader = new BufferedReader(new InputStreamReader(conn.getInputStream(), "utf-8"));
            String line, resultStr = "";
            while ((line = bReader.readLine()) != null) {
                resultStr += line;
            }
            bReader.close();
//            System.out.println(resultStr);
            return resultStr;
        } catch (Exception e) {
            e.printStackTrace();
            return "";
        }
    }

    public static void main(String[] args) {
        //aa20722198f7b726718076d8e1fa89b1ff7d3f43725cb241feed01e72a7c4044de501362cc8a47c3
//        System.out.println(getSysToken());
        //b5827197e97bec9c718076d8e1fa89b1ff7d3f43725cb241feed01e72a7c4044de501362cc8a47c3
//        System.out.println(checkPaperDocSeal());
    }
}
