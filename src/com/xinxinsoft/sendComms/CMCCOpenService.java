package com.xinxinsoft.sendComms;

import java.io.*;
import java.net.HttpURLConnection;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.security.PrivateKey;
import java.text.SimpleDateFormat;
import java.util.*;

import com.google.gson.JsonObject;
import com.xinxinsoft.sendComms.omsService.common.HttpURLConnectClientFactory;
import com.xinxinsoft.utils.FtpUtil;
import com.xinxinsoft.utils.common.FileUpload;
import com.xinxinsoft.utils.result.Result;
import net.sf.json.JSONObject;

import com.google.gson.Gson;
import com.google.gson.reflect.TypeToken;
import com.sitech.common.util.MD5;
import com.sitech.common.util.RSAUtils;
import com.sitech.common.util.StringUtil;
import com.xinxinsoft.entity.IDC.IDCApply;
import com.xinxinsoft.entity.boss.RESULT_DATA;
import com.xinxinsoft.entity.boss.StartPreOrderOut;
import com.xinxinsoft.entity.commonSingManagement.ResourceSurvey;
import com.xinxinsoft.entity.core.SystemUser;
import com.xinxinsoft.service.core.json.JSONArray;
import com.xinxinsoft.service.core.user.SystemUserService;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.UrlConnection;

import org.apache.axis.client.Call;
import org.apache.axis.client.Service;
import org.apache.commons.lang.StringUtils;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

/**
 * 功能：调用移动能力开发平台接口类
 *
 * <AUTHOR>
 */
public class CMCCOpenService {

    private static final Logger logger = Logger.getLogger(CMCCOpenService.class);

    private static CMCCOpenService openservice = null;

    /* 能力开放平台 */
    public static String appKey = "11000321";
    public static String userName = "jianlinjun";
    public static String login_no = "oc0040";
    // http://218.205.252.26:32000/rest/1.0/qryPreDeal?
    public static String qryPreDealWsdl = FileUpload.getQryPreDealWsdl_Url();//"http://************:10000/rest/1.0/qryPreDeal?";
    public static String startPreOrderWsdl = FileUpload.getStartPreOrderWsdl_Url();//"http://************:10000/rest/1.0/startPreOrder?";
    public static String qryResourceResult4PreOrderWsdl = FileUpload.getQryResourceResult4PreOrderWsdl_Url();// "http://************:10000/rest/1.0/qryResourceResult4PreOrder?";
    // BOSS定时查询预受理订单开关 start表示开启 close表示关闭
    public static String BOSSSwitch = "strat";
    private static Boolean isES = false;

    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }

    /**
     * 正式环境ESB 请求地址
     */
    private static String ESBWSURL = FileUpload.getESBWS_Url();//FileUpload.getESBWS_Url();//"http://*************:51000/esbWS/rest/";

    private static final String REQUER_DISTRICTS = "http://*************:9999/districts/query/";

    /**
     * 请求无纸化系统 URL
     */
    private static final String endpoint = FileUpload.getEndpointUrl();//"http://************:17001/services/ContractService";

    static {
        if ("*************".equals(DateUtil.getLocalIp())) {
            /**
             * 测试环境ESB 请求地址
             */
            ESBWSURL = FileUpload.getTest_ESBWS_Url();//"http://*************:52000/esbWS/rest/";
        }
    }

    // static ApplicationContext context = new ClassPathXmlApplicationContext(
    // new String[] { "applicationContext.xml" });
    // static SystemDeptService service = context
    // .getBean(SystemDeptService.class); // 订单service
    public static CMCCOpenService getInstance() {
        if (openservice == null) {
            synchronized (CMCCOpenService.class) {
                if (openservice == null) {
                    openservice = new CMCCOpenService();
                }
            }
        }
        return openservice;
    }

    private CMCCOpenService() {
        // loadWebService();
    }

    /**
     * 查询预受理状态
     *
     * @param boss_from_no
     * @throws Exception
     */
    public Object QryPreDeal(String boss_form_no) throws Exception {
        String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss").format(new Date());
        // 设置请求参数并排序
        String orginStr = "appKey=" + appKey + "&login_no=" + login_no
                + "&timeStamp=" + timeStamp + "&userName=" + userName
                + "&boss_form_no=" + boss_form_no;
        String signStr = toSign(orginStr);
        orginStr = orginStr + "&sign=" + signStr;
        String url = qryPreDealWsdl + orginStr;
        //	String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(url,"");
        URL restURL = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);
        conn.setAllowUserInteraction(false);
        conn.setRequestProperty("Content-Type",
                "application/x-www-form-urlencoded");
        conn.setRequestProperty("Charset", "utf-8");
        BufferedReader bReader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "utf-8"));
        String line, resultStr = "";
        while (null != (line = bReader.readLine())) {
            resultStr += line;
        }
        bReader.close();
        if (null != resultStr) {
            JSONObject json = JSONObject.fromObject(resultStr);
            if (having(json, "resCode")) {
                String resCode = json.get("resCode").toString();
                if (resCode.equals("0000000")) {
                    String data = json.getString("outData");
                    JSONObject list = JSONObject.fromObject(data);
                    if (having(list, "RESULT_LIST")) {
                        JSONObject info = JSONObject.fromObject(list
                                .getString("RESULT_LIST"));
                        if (having(info, "RESULT_INFO")) {
                            Gson gson = new Gson();
                            List<RESULT_DATA> result = gson.fromJson(
                                    info.getString("RESULT_INFO"),
                                    new TypeToken<List<RESULT_DATA>>() {
                                    }.getType());
                            return result;
                        } else {
                            return null;
                        }
                    } else {
                        return null;
                    }
                }
            }
        }
        return null;

    }

    /**
     * 查询预受理状态
     *
     * @param so_no         订单系统订单号
     * @param oper_no       工号ID
     * @param oper_name     工号名称
     * @param grpcust_id    集团编码
     * @param busi_req_type 业务请求类型
     * @param chn_id        渠道
     * @param region_code   地市编码
     * @param group_id      区域编码
     * @param obtain_time   业务获取时间
     * @param chance_name   业务请求名称
     * @param pri_code      紧急程度
     * @param demand_desc   需求描述
     * @param person_no     si工号
     * @throws Exception
     */
    public StartPreOrderOut startPreOrder(String so_no, String oper_no,
                                          String oper_name, String grpcust_id, String busi_req_type,
                                          String chn_id, String region_code, String group_id,
                                          String obtain_time, String chance_name, String pri_code,
                                          String demand_desc, String person_no) throws Exception {
        StartPreOrderOut SP = new StartPreOrderOut();
        String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss")
                .format(new Date());
        // 设置请求参数并排序
        String orginStr = "appKey=" + appKey + "&login_no=" + login_no
                + "&timeStamp=" + timeStamp + "&userName=" + userName
                + "&so_no=" + so_no + "&oper_name=" + oper_name
                + "&grpcust_id=" + grpcust_id + "&busi_req_type="
                + busi_req_type + "&chn_id=" + chn_id + "&region_code="
                + region_code + "&group_id=" + group_id + "&obtain_time="
                + obtain_time + "&chance_name=" + chance_name + "&pri_code="
                + pri_code + "&demand_desc=" + demand_desc + "&person_no="
                + person_no;
        String signStr = toSign(orginStr);
        orginStr = orginStr + "&sign=" + signStr;
        String url = startPreOrderWsdl + orginStr;
        //System.out.println("------------startPreOrdercanshut:---------------"+ url);
        URL restURL = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);
        conn.setAllowUserInteraction(false);
        conn.setRequestProperty("Content-Type",
                "application/x-www-form-urlencoded");
        conn.setRequestProperty("Charset", "utf-8");
        conn.setConnectTimeout(60000);  //设置连接主机超时（单位：毫秒）
        conn.setReadTimeout(60000 * 2);  //设置从主机读取数据超时（单位：毫秒）
        BufferedReader bReader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "utf-8"));
        String line, resultStr = "";
        while (null != (line = bReader.readLine())) {
            resultStr += line;
        }
        bReader.close();
        conn.disconnect();
        //System.out.println("------------startPreOrderresult:---------------" + resultStr);
        if (resultStr != null) {
            JSONObject jsonObject = JSONObject.fromObject(resultStr);
            String resCode = jsonObject.getString("resCode");
            SP.setResCode(resCode);
            SP.setResMsg(jsonObject.getString("resMsg"));
            if (resCode.equals("0000000")) {
                SP.setDetail_msg(jsonObject.getString("detailMsg"));
                JSONObject jsonNo = JSONObject.fromObject(jsonObject
                        .getString("outData"));
                SP.setBossNo(jsonNo.getString("BOSS_FORM_NO"));
            }

        }
        //System.out.println("------------startPreOrderresultA:---------------" + resultStr);

        return SP;

    }

    //
    public static String toSign(String orginStr) throws Exception {
        // 读取私钥
        String path = CMCCOpenService.class.getResource("rsa_private_key.pem").getPath();
        PrivateKey skPrivateKey = RSAUtils.fileToPrivateKey(path);
        // 将参数排序
        orginStr = StringUtil.sortOrginReqStr(orginStr);
        // 将排序后的参数作urlencoding编码
        String urlencodingStr = URLEncoder.encode(orginStr, "utf-8");
        String sign = "";
        try {
            String signStr = RSAUtils.sign(
                    MD5.ToMD5(urlencodingStr).getBytes(), skPrivateKey);
            sign = URLEncoder.encode(signStr, "UTF-8");
        } catch (Exception e) {
            e.printStackTrace();
        }
        return sign;
    }

    public static boolean having(JSONObject json, String key) {
        try {
            if (json.containsKey(key)) {
                return true;
            } else {
                return false;
            }
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 查勘结果查询
     *
     * @param bossformno
     * @throws Exception
     */
    public static List<ResourceSurvey> getResourceSurvey(String bossformno)
            throws Exception {
        String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss")
                .format(new Date());
        // 设置请求参数并排序
        String orginStr = "appKey=" + appKey + "&login_no=" + login_no
                + "&timeStamp=" + timeStamp + "&userName=" + userName
                + "&boss_form_no=" + bossformno;
        String signStr = toSign(orginStr);
        orginStr = orginStr + "&sign=" + signStr;
        String url = qryResourceResult4PreOrderWsdl + orginStr;
        URL restURL = new URL(url);
        //System.out.println("------------qryResourceResult4PreOrderWsdl:---------------"+ url);
        HttpURLConnection conn = (HttpURLConnection)
                restURL.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);
        conn.setAllowUserInteraction(false);
        conn.setRequestProperty("Content-Type",
                "application/x-www-form-urlencoded");
        conn.setRequestProperty("Charset", "utf-8");
        BufferedReader bReader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "utf-8"));
        String line, resultStr = "";
        while (null != (line = bReader.readLine())) {
            resultStr += line;
        }
        bReader.close();
        if (null != resultStr) {
            JSONObject json = JSONObject.fromObject(resultStr);
            if (having(json, "resCode")) {
                String resCode = json.get("resCode").toString();

                if (resCode.equals("0000000")) {
                    String data = json.getString("outData");
                    JSONObject list = JSONObject.fromObject(data);
                    if (having(list, "RESULT_LIST")) {
                        JSONObject info = JSONObject.fromObject(list
                                .getString("RESULT_LIST"));
                        if (having(info, "RESULT_INFO")) {
                            Gson gson = new Gson();
                            List<ResourceSurvey> result = new ArrayList<ResourceSurvey>();
                            String re = info.getString("RESULT_INFO");
                            try {
                                result = gson.fromJson(
                                        re,
                                        new TypeToken<List<ResourceSurvey>>() {
                                        }.getType());
                            } catch (Exception e) {
                                ResourceSurvey r = gson.fromJson(info.getString("RESULT_INFO"), ResourceSurvey.class);
                                result.add(r);
                            }

                            return result;
                        } else {
                        }
                    } else {
                    }
                }
                //System.out.println("------------qryResourceResult4PreOrderWsdlresult:---------------"+ resultStr);
            }
        }
        return null;
    }

    /**
     * 新建小微集团：
     *
     * @param id_type              集团证件类型
     * @param id_iccid             集团证件号码
     * @param cust_name            集团名称
     * @param id_address           集团证件地址
     * @param login_no             操作工号
     * @param resp_id_type         责任人证件类型
     * @param resp_id_iccid        责任人证件号码
     * @param resp_id_address      责任人证件地址
     * @param resp_id_validdate    证件有效期
     * @param resp_cust_name       责任人名称
     * @param resp_register_office 签证机关
     * @param resp_sex_code        性别
     * @param resp_birthday        证件有效期
     * @param resp_nation          国籍  1:中国
     * @param resp_nation_id       民族
     * @param url_Cfm              请求地址url
     * @param home_phone           固定电话
     * @param contact_phone        移动电话
     * @param file_path            相对路径，如：/upload/ftpcustmng/
     * @param file_name            文件类型只能为：xls,xlsx,txt,doc,docx,rar,zip,tar,jpg，文件大小不超过2M 上传的认证文件名中请不要带\|&;$%@'"<>()+,\n\r等特殊字符！
     * @return
     * @throws Exception
     */
    public Map<String, Object> s3851AppCfm(String id_type, String id_iccid,
                                           String cust_name, String id_address, String OP_LOGIN_NO,
                                           String resp_id_type, String resp_id_iccid, String resp_id_address,
                                           String resp_id_validdate, String resp_cust_name, String resp_register_office,
                                           String resp_sex_code, String resp_birthday, String resp_nation, String resp_nation_id, String home_phone, String contact_phone, String url_Cfm, String phone_no, String file_path, String file_name) throws Exception {
        // 设置请求参数并排序
        String orginStr = "appKey=" + appKey + "&timeStamp=" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "&userName=" + userName
                + "&id_type=" + id_type
                + "&id_iccid=" + id_iccid
                + "&cust_name=" + cust_name
                + "&id_address=" + id_address
                + "&login_no=" + login_no
                + "&service_no=" + OP_LOGIN_NO
                + "&phone_no=" + phone_no
                + "&resp_id_type=" + resp_id_type
                + "&resp_id_iccid=" + resp_id_iccid
                + "&resp_id_address=" + resp_id_address
                + "&resp_id_validdate=" + resp_id_validdate
                + "&resp_cust_name=" + resp_cust_name
                + "&resp_register_office=" + resp_register_office
                + "&resp_sex_code=" + resp_sex_code
                + "&resp_birthday=" + resp_birthday
                + "&resp_nation=" + resp_nation
                + "&resp_nation_id=" + resp_nation_id
                + "&home_phone=" + home_phone
                + "&contact_phone=" + contact_phone
                + "&file_path=" + file_path
                + "&file_name=" + file_name;
        String signStr = toSign(orginStr);
        orginStr = orginStr + "&sign=" + signStr;
        Map<String, Object> map = new HashMap<String, Object>();
        String url = url_Cfm + orginStr;
        //	String orginStr = "appKey=11000321&timeStamp=20190429084212&userName=jianlinjun&id_type=8&id_iccid=510726197709130026&cust_name=北川羌族自治县永昌姐妹家政服务部&id_address=北川羌族自治县永昌镇巴拿恰商业街石泉南路35号&login_no=oc0040&service_no=aagh5P&phone_no=15182336063&resp_id_type=1&resp_id_iccid=510726197709130026&resp_id_address=四川省北川羌族自治县永昌镇新川小区新丰苑D2一单元601&resp_id_validdate=20320921&resp_cust_name=李晓英&resp_register_office=北川羌族自治县公安局&resp_sex_code=12&resp_birthday=20320921&resp_nation=1&resp_nation_id=43&home_phone=08165350699&contact_phone=13458042370&file_path=/EOMAPP/UploadFiles/FtpFiles/S3851PATH/20190429/&file_name=zip_1556498532185.zip";
        logger.info(url);
        URL restURL = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);
        conn.setAllowUserInteraction(false);
        conn.setRequestProperty("Content-Type",
                "application/x-www-form-urlencoded");
        conn.setRequestProperty("Charset", "utf-8");
        conn.setConnectTimeout(60000);  //设置连接主机超时（单位：毫秒）
        conn.setReadTimeout(60000 * 2);  //设置从主机读取数据超时（单位：毫秒）
        BufferedReader bReader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "utf-8"));
        String line, resultStr = "";
        while (null != (line = bReader.readLine())) {
            resultStr += line;
        }
        bReader.close();
        conn.disconnect();
        //String resultStr =  CMCC1000OpenService.getInstance().bdcesPatams(url,"");
//		String resultStr="{\"resCode\":\"E40638441011\",\"resMsg\":\"无法获取该错误代码的错误信息，请直接查看明细信息 ERRID:45821354\",\"detailMsg\":\"集团名称长度不能少于4个汉字！\",\"outData\":\"\"}";
        logger.info(resultStr);
        if (resultStr != null) {
            JSONObject jsonObject = JSONObject.fromObject(resultStr);
            String resCode = jsonObject.getString("resCode");
            map.put("resCode", resCode);
            map.put("resMsg", jsonObject.getString("resMsg"));
            if (CMCCOpenService.having(jsonObject, "detailMsg")) {
                if (!StringUtils.isBlank(jsonObject.getString("detailMsg"))) {
                    map.put("resMsg", jsonObject.getString("detailMsg"));
                }
            }
            if (("0000000").equals(resCode)) {
                JSONObject jsonNo = JSONObject.fromObject(jsonObject.getString("outData"));
                map.put("IS_COUNT", (jsonNo.getString("IS_COUNT")));
                map.put("UNIT_ID", (jsonNo.getString("UNIT_ID")));
                map.put("CUST_ID", (jsonNo.getString("CUST_ID")));
                map.put("CUST_NAME", (jsonNo.getString("CUST_NAME")));
            }
        }

        return map;

    }

    /**
     * 小微集团查询
     *
     * @param login_no 操作工号
     * @param id_iccid 集团证件号码
     * @param url_qry  请求地址：
     * @return
     * @throws Exception
     */
    public static Map<String, Object> s3851AppQry(String login_no, String id_iccid, String url_qry) throws Exception {
        Map<String, Object> map = new HashMap<String, Object>();
        // 设置请求参数并排序

        String orginStr = "appKey=" + appKey + "&timeStamp=" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "&userName=" + userName + "&login_no=" + login_no + "&id_type=8"
                + "&id_iccid=" + id_iccid;
        String signStr = toSign(orginStr);
        orginStr = orginStr + "&sign=" + signStr;
        String url = url_qry + orginStr;

        System.out.println("=================url:" + url);
        URL restURL = new URL(url);
        HttpURLConnection conn = (HttpURLConnection)
                restURL.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);
        conn.setAllowUserInteraction(false);
        conn.setRequestProperty("Content-Type",
                "application/x-www-form-urlencoded");
        conn.setRequestProperty("Charset", "utf-8");
        BufferedReader bReader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "utf-8"));
        String line, resultStr = "";

        while (null != (line = bReader.readLine())) {
            resultStr += line;
        }
        bReader.close();
        System.out.println("=====================================s3851AppQry=======================>>" + resultStr);
        if (null != resultStr) {
            JSONObject json = JSONObject.fromObject(resultStr);
            if (having(json, "resCode")) {
                String resCode = json.get("resCode").toString();
                map.put("resCode", resCode);
                String RETURN_MSG = json.get("resMsg").toString();
                map.put("resMsg", RETURN_MSG);
                if (("0").equals(resCode)) {
                    String detail_msg = json.getString("detail_msg");
                    map.put("detail_msg", detail_msg);
                    JSONObject jsonNo = JSONObject.fromObject(json.getString("outData"));
                    map.put("IS_COUNT", (jsonNo.getString("IS_COUNT")));
                }
            }
        }

        return map;
    }

    /**
     * 订单系统开通工单
     *
     * @param SERVICE_NAME        服务名称，s6429Open
     * @param CALL_TIME           服务调用时间，时间格式：yyyy-mm-dd hh24:mi:ss
     * @param SERVICE_CALLER      服务调用方,ORDER_SYS_APP|订单系统APP
     *                            REQUEST_INFO
     *                            -- * @param SHEET_TYPE  		   工单类型,100|开通
     * @param UNIT_ID             客户编码，eg：28000025793
     *                            -- * @param old_group_id         组织机构编码
     * @param ORDER_ID            订单系统订单编码
     *                            --* @param OPER_NO				boss工号
     *                            -- * @param	OPER_PHONE			工号电话
     *                            -- * @param	OPER_NAME				工号名称
     * @param LOGIN_NO            boss工号
     * @param PROD_ID             产品编码
     * @param PROD_NAME           产品名称
     * @param PROD_PRC_ID         资费编码
     * @param PROD_PRC_NAME       资费名称
     * @param CHANNEL_TYPE        渠道编码，取值待定
     *                            -- * @param REGION_CODE  	  	    地市编码
     * @param BUSI_REQ_TYPE       业务请求类型
     *                            --* @param GROUP_ID  		     区域编码
     *                            --* @param	GROUP_NAME			区域名称
     *                            -- * @param  BUSI_REQ_TYPE       	业务请求大类型
     * @param DEMAND_DESC         需求描述
     * @param CHANCE_NAME         业务请求名称
     * @param OPER_TIME           操作，时间格式：yyyy-mm-dd hh24:mi:ss
     * @param ISRECOVER           是否预覆盖资源        1-否，2-是
     *                            PRODUCT_INFO        详细信息
     * @param PRODUCT_ORDER_CHARS 产品属性值
     * @param PRODUCT_ORDER_CHAR  一条产品属性
     * @param PROP_ID
     * @param ITEM_TYPE           字段类型（0，文本，1，下拉）
     * @param FIELD_CN_NAME       属性中文名
     * @param FIELD_EN_NAME       属性英文编码
     * @return
     * @throws Exception
     * @param    BUSI_REQ_CLASS 业务请求小类型
     * --* @param PRI_CODE   		     紧急程度
     * @param     RESULT_VALUE  字段值
     * @update DATE 2019,7,23 修改；新增内容ISRECOVER 字段
     */
    public Map<String, Object> preOrderSvc(
            //String 	SHEET_TYPE,
            String UNIT_ID,
            String ORDER_ID,
            String REGION_ID,
            String LOGIN_NO,
            String PROD_ID,
            String PROD_NAME,
            String PROD_PRC_ID,
            String PROD_PRC_NAME,
            String CHANNEL_TYPE,
            String BUSI_REQ_TYPE,
            String BUSI_REQ_CLASS,
            String PRI_CODE,
            String DEMAND_DESC,
            String CHANCE_NAME,
            String OPER_TIME,
            String ISRECOVER,
            String product_order_char) throws Exception {
        String url = ESBWSURL + "orderSysSVC";
        //String url = "http://*************:52000/esbWS/rest/orderSysSVC";
        JSONObject obj_ = new JSONObject();
        //JSONObject request_info= new JSONObject();

        JSONObject request_info_product = JSONObject.fromObject(product_order_char);
        //JSONObject request_info_product_char= new JSONObject();
        //request_info_product_char.put("PRODUCT_INFO", request_info_product);
        obj_.put("SERVICE_NAME", "s6429Open");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        obj_.put("UNIT_ID", UNIT_ID);
        obj_.put("ORDER_ID", ORDER_ID);
        obj_.put("REGION_ID", REGION_ID);
        //	request_info.put("OLD_GROUP_ID	", old_group_id);
        obj_.put("LOGIN_NO", LOGIN_NO);
        obj_.put("PRI_CODE", PRI_CODE);
        //request_info.put("OPER_NO", OPER_NO);
        //request_info.put("OPER_NAME", OPER_NAME);
        //request_info.put("OPER_PHONE", OPER_PHONE);
        obj_.put("CHANCE_NAME", CHANCE_NAME);
        obj_.put("CHANNEL_TYPE", CHANNEL_TYPE);
        //	obj_.put("REGION_CODE", REGION_CODE);
        obj_.put("CHANCE_DESC", DEMAND_DESC);
        obj_.put("BUSI_REQ_TYPE", BUSI_REQ_TYPE);
        obj_.put("BUSI_REQ_CLASS", BUSI_REQ_CLASS);
        obj_.put("PROD_ID", PROD_ID);
        obj_.put("PROD_NAME", PROD_NAME);
        obj_.put("PROD_PRCID", PROD_PRC_ID);
        obj_.put("PROD_PRC_NAME", PROD_PRC_NAME);
        obj_.put("ISRECOVER", ISRECOVER);
        obj_.put("PRODUCT_INFO_LIST", request_info_product);

        //obj_.put("REQUEST_INFO", request_info);
        //System.out.println(obj_.toString());
        ///String resultStr=CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(obj_));//

        String resultStr = setPost(url, obj_);
        System.out.println(resultStr);
        Map<String, Object> map_ = new HashMap<String, Object>();
        if (resultStr != null) {
            JSONObject jsonObject = JSONObject.fromObject(resultStr);

            //jsonObject = JSONObject.fromObject(jsonObject.getString("res"));
            logger.info("返回报文：" + jsonObject.toString());
            JSONObject root_ = JSONObject.fromObject(jsonObject.getString("ROOT"));
            try {
                map_.put("RETURN_CODE", root_.getString("RETURN_CODE"));
                map_.put("RETURN_MSG", root_.getString("RETURN_MSG"));
                map_.put("DETAIL_MSG", root_.getString("DETAIL_MSG"));
            } catch (Exception e) {
                JSONObject body = JSONObject.fromObject(root_.getString("BODY"));
                map_.put("resCode", body.getString("RETURN_CODE"));
                map_.put("resMsg", body.getString("RETURN_MSG"));
                map_.put("detail_msg", body.getString("PROMPT_MSG"));
                JSONObject out_date = JSONObject.fromObject(body.getString("OUT_DATA"));
                if ("0".equals(body.getString("RETURN_CODE"))) {
                    if (having(out_date, "BOSS_FORM_NO")) {
                        map_.put("BOSS_FORM_NO", (out_date.getString("BOSS_FORM_NO")));
                    }
                    if (having(out_date, "RET_MSG")) {
                        map_.put("RET_MSG", (out_date.getString("RET_MSG")));
                    }
                    if (having(out_date, "RET_CODE")) {
                        map_.put("RET_CODE", (out_date.getString("RET_CODE")));
                    }
                    if (having(out_date, "RETURN_MSG")) {
                        map_.put("RET_MSG", (out_date.getString("RETURN_MSG")));
                    }

                }
            }

        }
        return map_;

    }

    /**
     * 参数详情查看  （附件7.IDC订单系统审批需展示字段.xlsx）
     *
     * @param object
     * @return
     */
    public Map<String, Object> preIDCSvc(IDCApply idcApply, SystemUser user, Object... object) {
        String url = ESBWSURL + "orderSysSVC";
        Map<String, Object> map_ = new HashMap<String, Object>();
        JSONObject obj_ = new JSONObject();
        CMCC1000OpenService cService = new CMCC1000OpenService();
        if (user.getBossUserName() == null) {
            map_.put("RETURN_MSG", "账号未配置Boos工号");
            map_.put("RETURN_CODE", "1");
            return map_;
        }
        Map<String, Object> maps = cService.pressGetLoginMsgSvc(user.getBossUserName());
        logger.info(com.xinxinsoft.utils.easyh.JSONHelper.SerializeWithNeedAnnotationDateFormat(maps));
        if (maps != null) {
            if (!"0".equals(maps.get("RETURN_CODE"))) {
                map_.put("RETURN_MSG", "" + (maps.get("Status").equals("0") ? "boos工号【" + user.getBossUserName() + "】校验超时，请稍后重试" : maps) + "");
                map_.put("RETURN_CODE", "1");
                return map_;
            }
        } else {
            map_.put("RETURN_MSG", "【" + user.getBossUserName() + "】Boos工号校验失败");
            return map_;
        }
        String[] prop_id = {"120002000000",//资费类型
                "12000200001",//意向数据中心编码
                "12000200002",//套餐项目名称
                "12000200003",//折扣率
                "12000200005",//保底量
                "12000200006",//1U机位单价（元/月)
                "12000200007",//2U机位单价（元/月）
                "12000200008",//3U机位单价（元/月）
                "12000200009",//4U机位单价（元/月）
                "12000200011",//包端口带宽单价（元/月）
                "12000200012",//保底总费用（参考值）
                "1200020001201",//额外IP费用（元/月）
                "12000200013",//计费模式
                "1200020001301",//裸机机柜单价（元/月）
                "1200020001302",//套餐单价（元/月）
                "1200020001303",//套餐数量
                "1200020001205",//包端口带宽签约总价 （元/月）
                "1200020001206",//机架签约总费用
                "1200020001207",//总带宽大小
                "12000200010",//IPv4数量
                "12000200037",//IPv4性质
                "12000200038",//IPv4配套数量
                "12000200039",//IPv4额外数量
                "12000200040",//IPV4描述(备注)
                "12000200033",//带宽模式类别
                "12000200034",//单端口带宽大小（单位兆）
                "12000200035",//端口数量
                "12000200036",//带宽描述(备注)
                "12000200025",//供电类型
                "12000200026",//机架规格
                "12000200027",//1U机位数量
                "12000200028",//2U机位数量
                "12000200029",//3U机位数量
                "12000200030",//4U机位数量
                "12000200031",//整机架数量
                "12000200032",//机架描述(备注)
                "20201019095900"//出帐周期(备注)
        };
        int index = 0;
        String prod_prcid = "ACCZ77566";
        String prod_prc_name = "IDC独立资费";
        if ("0".equals(idcApply.getFeeCode())) {
            prod_prcid = "ACCZ77567";
            prod_prc_name = "IDC套餐资费";
        }
        obj_.put("SERVICE_NAME", "s6429Open");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        obj_.put("UNIT_ID", idcApply.getUNIT_ID());//idcApply.getUNIT_ID() ，2801172543
        obj_.put("ORDER_ID", idcApply.getOrderNo());
        obj_.put("REGION_ID", maps.get("REGION_ID"));//地市编码 maps.get("REGION_ID")
        obj_.put("LOGIN_NO", user.getBossUserName());//boos工号,, user.getBossUserName()
        obj_.put("PRI_CODE", "01");//紧急程度01一般02紧急 03特急  04重要
        obj_.put("CHANCE_NAME", idcApply.getTitle());//业务请求名称
        obj_.put("CHANNEL_TYPE", "04");//渠道编码，取值待定
        obj_.put("CHANCE_DESC", "1");//
        obj_.put("BUSI_REQ_TYPE", "01");//业务请求类型
        obj_.put("BUSI_REQ_CLASS", "01a");//业务请求小类型
        obj_.put("PROD_ID", "APCZ66691");//产品编码
        obj_.put("PROD_NAME", "集团IDC托管");//产品名称
        obj_.put("PROD_PRCID", prod_prcid);//资费编码
        obj_.put("PROD_PRC_NAME", prod_prc_name);//资费名称

        JSONObject product_info_list = new JSONObject();
        net.sf.json.JSONArray product_infos = new net.sf.json.JSONArray();
        int i = 0;
        while (i < 19) {
            JSONObject product_info = new JSONObject();
            product_info.put("RESULT_VALUE", object[index]);
            product_info.put("PROP_ID", prop_id[index]);
            product_infos.add(product_info);
            i++;
            index++;
        }
        net.sf.json.JSONArray group_product_infos = new net.sf.json.JSONArray();
        int l = 0;
        String[] group_id = {"IPV4", "PORT", "RACK"};
        while (l < 3) {
            int[] j = {5, 4, 9};//GROUP_PRODUCT_INFO节点里面的PRODUCT_INFO数量
            JSONObject group_product_info = new JSONObject();
            net.sf.json.JSONArray group_product_info_product_infos = new net.sf.json.JSONArray();
            while (0 < j[l]) {
                JSONObject group_product_info_product_info = new JSONObject();
                group_product_info_product_info.put("RESULT_VALUE", object[index]);
                group_product_info_product_info.put("PROP_ID", prop_id[index]);
                group_product_info_product_infos.add(group_product_info_product_info);
                index++;
                j[l]--;
            }
            group_product_info.put("GROUP_ID", group_id[l]);
            group_product_info.put("PRODUCT_INFO", group_product_info_product_infos);
            group_product_infos.add(group_product_info);
            l++;
        }
        product_info_list.put("PRODUCT_INFO", product_infos);
        product_info_list.put("GROUP_PRODUCT_INFO", group_product_infos);
        obj_.put("PRODUCT_INFO_LIST", product_info_list);
        logger.info("IDC推送boos数据：" + obj_);
        logger.info("IDC推送地址：" + url);
        String resultStr = null;
        try {
            //服务器专用
            resultStr = UrlConnection.responseGBK(url, CMCCOpenService.getInstance().setParamObj1(obj_, user.getMobile()));
            logger.info("IDC返回数据：" + resultStr);
        } catch (Exception e1) {
            e1.printStackTrace();
            map_.put("RETURN_CODE", "111");
            map_.put("RETURN_MSG", "推送boos失败,请稍后重试");
            logger.info("推送boos失败，preIDCSvc()");
        }
        if (resultStr != null) {
            JSONObject json = JSONObject.fromObject(resultStr);
            String reuslt = json.getString("Status");
            String jxresult = "";
            if (CMCCOpenService.having(json, "res")) {
                jxresult = json.getString("res");
            } else {
                map_.put("res", resultStr);
                map_.put("Status", json.getString("Status"));
            }
            if ("1".equals(reuslt)) {
                JSONObject jsonObject = JSONObject.fromObject(jxresult);
                logger.info("返回报文" + jsonObject);
                JSONObject root_ = JSONObject.fromObject(jsonObject.getString("ROOT"));
                try {
                    map_.put("RETURN_CODE", root_.getString("RETURN_CODE"));
                    map_.put("RETURN_MSG", root_.getString("RETURN_MSG"));
                    map_.put("DETAIL_MSG", root_.getString("DETAIL_MSG"));
                } catch (Exception e) {
                    JSONObject body = JSONObject.fromObject(root_.getString("BODY"));
                    map_.put("RETURN_CODE", body.getString("RETURN_CODE"));
                    map_.put("RETURN_MSG", body.getString("RETURN_MSG"));
                    map_.put("DETAIL_MSG", body.getString("PROMPT_MSG"));
                    JSONObject out_date = JSONObject.fromObject(body.getString("OUT_DATA"));
                    if ("0".equals(body.getString("RETURN_CODE"))) {
                        if (having(out_date, "BOSS_FORM_NO")) {
                            map_.put("BOSS_FORM_NO", (out_date.getString("BOSS_FORM_NO")));
                        }
                        if (having(out_date, "RET_MSG")) {
                            map_.put("RET_MSG", (out_date.getString("RET_MSG")));
                        }
                        if (having(out_date, "RET_CODE")) {
                            map_.put("RET_CODE", (out_date.getString("RET_CODE")));
                        }
                        if (having(out_date, "RETURN_MSG")) {
                            map_.put("RET_MSG", (out_date.getString("RETURN_MSG")));
                        }

                    }
                }
            }

        }
        return map_;
    }

    /**
     * 服务名称 sDynSvc
     *
     * @param SERVICE_NAME
     * @param SERVICE_CALLER 服务调用方,ORDER_SYS_APP|订单系统APP
     * @param SVC_NAME       服务名称
     * @param REGION_ID      地市编码
     * @param OP_CODE        opcode
     * @param LOGIN_NO       boss工号
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> presDynSvc(String SVC_NAME, String REGION_ID, String OP_CODE, String LOGIN_NO) throws Exception {
        List<Map<String, Object>> maplis = new ArrayList<>();
        String url = ESBWSURL + "orderSysSVC";
        JSONObject obj_ = new JSONObject();
        obj_.put("SVC_NAME", SVC_NAME);
        obj_.put("REGION_ID", REGION_ID);
        obj_.put("OP_CODE", OP_CODE);
        obj_.put("LOGIN_NO", LOGIN_NO);
        obj_.put("SERVICE_NAME", "sDynSvc");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        String resultStr = setPost(url, obj_);
        if (resultStr != null) {
            System.out.println("===1=====presDynSvc========url" + resultStr);
            Map<String, Object> map_ = new HashMap<String, Object>();
            JSONObject body = setRetObject(resultStr, map_);
            if ("0".equals(map_.get("resCode"))) {
                JSONObject out_date = JSONObject.fromObject(body.getString("OUT_DATA"));
                try {
                    JSONArray jsonarray = new JSONArray(out_date.getString("ROW"));
                    for (int i = 0; i < jsonarray.length(); i++) {
                        Map<String, Object> map = new HashMap<String, Object>();
                        JSONObject jsonStr_ = JSONObject.fromObject(jsonarray.getString(i));
                        map.put("CODE_VALUE", jsonStr_.getString("CODE_VALUE"));
                        map.put("GROUP_ID", jsonStr_.getString("GROUP_ID"));
                        map.put("END_VALUE", jsonStr_.getString("END_VALUE"));
                        map.put("CODE_NAME", jsonStr_.getString("CODE_NAME"));//new String(.getBytes("gbk"), "utf-8")
                        maplis.add(map);
                    }
                } catch (Exception e) {
                    map_.put("resCode", out_date.getString("RETURN_CODE"));
                    map_.put("resMsg", out_date.getString("RETURN_MSG"));
                    map_.put("detail_msg", out_date.getString("DETAIL_MSG"));
                    map_.put("ESBRETCODE", out_date.getString("ESBRETCODE"));//new String(.getBytes("gbk"), "utf-8")
                    maplis.add(map_);
                }
            } else {
                maplis.add(map_);
            }

        }
        return maplis;

    }

    /***
     * 返回参数解析：
     * @param resultStr
     * @param map_
     * @return
     */
    protected JSONObject setRetObject(String resultStr, Map<String, Object> map_) {
        JSONObject jsonObject = JSONObject.fromObject(resultStr);
        JSONObject root_ = JSONObject.fromObject(jsonObject.getString("ROOT"));
        JSONObject body = JSONObject.fromObject(root_.getString("BODY"));
        map_.put("resCode", body.getString("RETURN_CODE"));
        map_.put("resMsg", body.getString("RETURN_MSG"));
        map_.put("detail_msg", body.getString("PROMPT_MSG"));
        return body;
    }

    /**
     * 服务名称，sQBroadPortNum
     *
     * @param STANDARD_ADDRESS 标准地址名称(覆盖区域名称)
     * @param GROUP_ID
     * @param LOGIN_NO
     * @return
     * @throws Exception
     */
    public Map<String, Object> presQBroadPortNum(String STANDARD_ADDRESS, String GROUP_ID, String LOGIN_NO) throws Exception {
        // 设置请求参数并排序

        Map<String, Object> map = new HashMap<String, Object>();
        //String url = "http://*************:52000/esbWS/rest/orderSysSVC";
        String url = ESBWSURL + "orderSysSVC";
        JSONObject obj_ = new JSONObject();
        obj_.put("STANDARD_ADDRESS", STANDARD_ADDRESS);
        obj_.put("GROUP_ID", GROUP_ID);
        obj_.put("SERVICE_NAME", "sQBroadPortNum");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        String resultStr = setPost(url, obj_);
        if (resultStr != null) {
            JSONObject body = setRetObject(resultStr, map);
            if ("0".equals(map.get("resCode"))) {
                JSONObject out_date = JSONObject.fromObject(body.getString("OUT_DATA"));
                try {
                    if (having(out_date, "IS_VERIFY")) {
                        map.put("IS_VERIFY", (out_date.getString("IS_VERIFY")));
                    }
                    if (having(out_date, "PORT_NUM")) {
                        map.put("PORT_NUM", (out_date.getString("PORT_NUM")));
                    }
                } catch (Exception e) {
                    if (having(out_date, "RETURN_CODE")) {
                        map.put("resCode", (out_date.getString("RETURN_CODE")));
                    }
                    if (having(out_date, "RETURN_MSG")) {
                        map.put("resMsg", (out_date.getString("RETURN_MSG")));
                    }
                    if (having(out_date, "DETAIL_MSG")) {
                        map.put("detail_msg", (out_date.getString("DETAIL_MSG")));
                    }
                    if (having(out_date, "USER_MSG")) {
                        map.put("userMsg", (out_date.getString("USER_MSG")));
                    }
                }

            }

        }
        return map;
    }

    /**
     * 服务名称，sQryBandAddress
     *
     * @param REGION_ID         地区标识
     * @param WorkType
     * @param AreaName          小区名称
     * @param AreaAddress       小区地址
     * @param AddressId         覆盖范围id
     * @param AddressName       标准地址名称(覆盖区域名称)
     * @param VillageName       乡镇
     * @param JDName            街道名称
     * @param ProdType          产品类型 1：固话 2：宽带空值，表示不限制
     * @param StreetName        道路/行政村
     * @param StandardAddressId 标准地址ID
     * @param Amount            默认：50
     * @param OldAddressId      旧地址ID
     * @param AccessType        接入方式
     * @param ResType           默认：1
     * @param MINROWNUM
     * @param MAXROWNUM
     * @param DistrictCode      小区归属区县代码
     * @param NetAttr           网络归属
     * @param keyWords          一条产品属性
     * @param LOGIN_NO
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> presQryBandAddress(String REGION_ID, String WorkType, String AreaName, String AreaAddress, String AddressId,
                                                        String AddressName, String VillageName, String JDName, String ProdType, String StreetName,
                                                        String StandardAddressId, String Amount, String OldAddressId, String AccessType,
                                                        String ResType, String MINROWNUM, String MAXROWNUM, String DistrictCode,
                                                        String NetAttr, String keyWords, String LOGIN_NO) throws Exception {
        String url = ESBWSURL + "orderSysSVC";
        //String url = "http://*************:52000/esbWS/rest/orderSysSVC";
        JSONObject obj_ = new JSONObject();
        JSONObject request_info_data_Param = new JSONObject();
        JSONObject param_ = new JSONObject();
        param_.put("REGION_ID", REGION_ID);
        param_.put("WorkType", WorkType);
        param_.put("AreaName", AreaName);
        param_.put("AreaAddress", AreaAddress);
        param_.put("AddressId", AddressId);
        param_.put("AddressName", AddressName);
        param_.put("VillageName", VillageName);
        param_.put("JDName", JDName);
        param_.put("StreetName", StreetName);
        param_.put("StandardAddressId", StandardAddressId);
        param_.put("Amount", Amount);
        param_.put("ProdType", ProdType);
        param_.put("OldAddressId", OldAddressId);
        param_.put("AccessType", AccessType);
        param_.put("ResType", ResType);
        param_.put("MINROWNUM", MINROWNUM);
        param_.put("MAXROWNUM", MAXROWNUM);
        param_.put("DistrictCode", DistrictCode);
        param_.put("NetAttr", NetAttr);
        param_.put("keyWords", keyWords);


        request_info_data_Param.put("Param", param_);
        obj_.put("Data", request_info_data_Param);

        obj_.put("SERVICE_NAME", "sQryBandAddress");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        List<Map<String, Object>> maplis = new ArrayList<Map<String, Object>>();
        String resultStr = setPost(url, obj_);
        if (resultStr != null) {
            System.out.println("===============================presQryBandAddress==============================>>" + resultStr);

            Map<String, Object> map_ = new HashMap<String, Object>();

            JSONObject jsonObject = setRetObject(resultStr, map_);
            if ("0".equals(map_.get("resCode"))) {
                JSONObject jsonNo = JSONObject.fromObject(jsonObject.getString("OUT_DATA"));
                try {
                    map_.put("resCode", jsonNo.getString("RETURN_CODE"));
                    map_.put("resMsg", jsonNo.getString("RETURN_MSG"));
                    map_.put("userMsg", jsonNo.getString("USER_MSG"));
                    map_.put("detail_msg", jsonNo.getString("DETAIL_MSG"));
                } catch (Exception ee) {
                    map_.put("TOTAL_CNT", (jsonNo.getString("TOTAL_CNT")));
                    JSONObject json_out_data = JSONObject.fromObject(jsonNo.getString("OUT_DATA"));
                    try {
                        JSONArray jsonarray = new JSONArray(json_out_data.getString("Param"));
                        for (int i = 0; i < jsonarray.length(); i++) {
                            Map<String, Object> map = new HashMap<String, Object>();
                            JSONObject jsonStr_ = JSONObject.fromObject(jsonarray.getString(i));
                            map.put("AddressId", jsonStr_.getString("AddressId"));
                            map.put("AddressName", jsonStr_.getString("AddressName"));
                            map.put("AreaCode", jsonStr_.getString("AreaCode"));
                            map.put("AreaName", jsonStr_.getString("AreaName"));
                            map.put("EnterType", jsonStr_.getString("EnterType"));
                            map.put("BandWidth", jsonStr_.getString("BandWidth"));
                            map.put("NetAttr", jsonStr_.getString("NetAttr"));
                            map.put("WorkFlag", jsonStr_.getString("WorkFlag"));
                            map.put("AreaAddress", jsonStr_.getString("AreaAddress"));
                            map.put("VillageName", jsonStr_.getString("VillageName"));
                            map.put("StreetName", jsonStr_.getString("StreetName"));
                            map.put("RestPortNum", jsonStr_.getString("RestPortNum"));
                            map.put("DistrictCode", jsonStr_.getString("DistrictCode"));
                            map.put("GD_ADDRESSID", jsonStr_.getString("GD_ADDRESSID"));
                            map.put("JDName", jsonStr_.getString("JDName"));
                            map.put("GROUP_ID", jsonStr_.getString("GROUP_ID"));
                            maplis.add(map);
                        }
                    } catch (com.xinxinsoft.service.core.json.JSONException e) {
                        JSONObject jsonStr_ = JSONObject.fromObject(json_out_data.getString("Param"));
                        Map<String, Object> map = new HashMap<String, Object>();
                        map.put("AddressId", jsonStr_.getString("AddressId"));
                        map.put("AddressName", jsonStr_.getString("AddressName"));
                        map.put("AreaCode", jsonStr_.getString("AreaCode"));
                        map.put("AreaName", jsonStr_.getString("AreaName"));
                        map.put("EnterType", jsonStr_.getString("EnterType"));
                        map.put("BandWidth", jsonStr_.getString("BandWidth"));
                        map.put("NetAttr", jsonStr_.getString("NetAttr"));
                        map.put("WorkFlag", jsonStr_.getString("WorkFlag"));
                        map.put("AreaAddress", jsonStr_.getString("AreaAddress"));
                        map.put("VillageName", jsonStr_.getString("VillageName"));
                        map.put("StreetName", jsonStr_.getString("StreetName"));
                        map.put("RestPortNum", jsonStr_.getString("RestPortNum"));
                        map.put("DistrictCode", jsonStr_.getString("DistrictCode"));
                        map.put("GD_ADDRESSID", jsonStr_.getString("GD_ADDRESSID"));
                        map.put("JDName", jsonStr_.getString("JDName"));
                        map.put("GROUP_ID", jsonStr_.getString("GROUP_ID"));
                        maplis.add(map);
                    }
                }

            }
            maplis.add(map_);
        }
        return maplis;

    }

//	/**
//	 * 判断返回的数据集是一条还是多条
//	 * @param s 源字符串
//	 * @param c 需要匹配的字符串
//	 * @return
//	 */
//	public static int counter(String s,String c){
//		  int count=0;
//		    Pattern p = Pattern.compile(c);
//		    Matcher m = p.matcher(s);
//		    while (m.find()) {
//		        count++;
//		    }
//		    return count;
//	}

    /**
     * 尊享码活动校验
     *
     * @param PHONE_NO 电话号码
     * @param MEANS_ID 活动代码
     * @param LOGIN_NO 办理工号
     * @param urlstr   请求地址
     * @return
     */
    public String checkZxmLimitValue(String PHONE_NO, String MEANS_ID, String LOGIN_NO, String urlstr) throws Exception {

        //	List<Map<String,Object>> maplis = new ArrayList<>();

        // 设置请求参数并排序
        String orginStr = "appKey=" + appKey + "&timeStamp=" + new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()) + "&userName=" + userName + "&login_no=" + login_no
                + "&phone_no=" + PHONE_NO + "&means_id=" + MEANS_ID + "&service_no=" + LOGIN_NO;
        String signStr = toSign(orginStr);
        orginStr = orginStr + "&sign=" + signStr;
        String url = urlstr + orginStr;
        System.out.println("==========checkZxmLimitValue=======url:" + url);
        URL restURL = new URL(url);
        HttpURLConnection conn = (HttpURLConnection)
                restURL.openConnection();
        conn.setRequestMethod("GET");
        conn.setDoOutput(true);
        conn.setAllowUserInteraction(false);
        conn.setRequestProperty("Content-Type",
                "application/x-www-form-urlencoded");
        conn.setRequestProperty("Charset", "utf-8");
        BufferedReader bReader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "utf-8"));
        String line, resultStr = "";

        while (null != (line = bReader.readLine())) {
            resultStr += line;
        }
        bReader.close();
        System.out.println("===============================>" + resultStr);
         /*   if (resultStr != null) {
    			JSONObject jsonObject = JSONObject.fromObject(resultStr);
    			String resCode = jsonObject.getString("resCode");
    			 Map<String,Object> map = new HashMap<String, Object>();
    			 map.put("resCode",resCode);
    			 map.put("resMsg", jsonObject.getString("resMsg"));
    			 map.put("detailMsg", jsonObject.getString("detailMsg"));

    			if("0000000".equals(resCode)){
    				JSONObject jsonNo = JSONObject.fromObject(jsonObject.getString("outData"));
    				JSONArray jsonarray =new JSONArray(jsonNo.getString("LIMIT_INFO"));
    					for (int i = 0; i < jsonarray.length(); i++) {
    						 Map<String,Object> map_ = new HashMap<String, Object>();
    						JSONObject jsonStr_ = JSONObject.fromObject(jsonarray.getString(i));
    						map_.put("LIMIT_NAME", jsonStr_.getString("LIMIT_NAME"));
    						map_.put("NOTE", jsonStr_.getString("NOTE"));
    						map_.put("PASS_FLAG", jsonStr_.getString("PASS_FLAG"));
    						maplis.add(map_);
						}
    			}else{
        			 maplis.add(map);
    				return maplis;
    			}
    		} */
        return resultStr;
    }

    /**
     * 请求无纸化系统
     *
     * @param ContractNO   合同编号  例: 【雅安分公司-宝兴分公司-集团专线】2017102000001
     * @param OperName     合同创建人
     * @param operDate     合同创建时间
     * @param ContractType 合同类型
     * @param Region       所属地区
     * @return
     */
    public String paperlessSystem(String ContractNO, String OperName, String operDate, String ContractType, String Region) {
        try {
            // 直接引用远程的wsdl文件
            // 以下都是套路
            Service service = new Service();
            Call call = (Call) service.createCall();
            call.setTargetEndpointAddress(endpoint);
            call.setOperationName("AddContractService");
            call.addParameter("Token", org.apache.axis.encoding.XMLType.XSD_DATE, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.addParameter("ContractNO", org.apache.axis.encoding.XMLType.XSD_DATE, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.addParameter("OperName", org.apache.axis.encoding.XMLType.XSD_DATE, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.addParameter("operDate", org.apache.axis.encoding.XMLType.XSD_DATE, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.addParameter("ContractType", org.apache.axis.encoding.XMLType.XSD_DATE, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.addParameter("Region", org.apache.axis.encoding.XMLType.XSD_DATE, javax.xml.rpc.ParameterMode.IN);// 接口的参数
            call.setReturnClass(String.class);
            call.setReturnType(org.apache.axis.encoding.XMLType.XSD_STRING);// 设置返回类型
            String result = (String) call.invoke(new Object[]{new SimpleDateFormat("yyyyMMddHHmmss").format(new Date()), ContractNO, OperName, operDate, ContractType, Region});
            // 给方法传递参数，并且调用方法
            //  System.out.println("result is: " + result);
            return result;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }

    }

    /**
     * @param body
     * @param phone
     * @return
     */
    public String setParamObj1(JSONObject body, String phone) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "10");
        routing.put("ROUTE_VALUE", phone);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        return root.toString();
    }

    /**
     * json 数据格式化：方法1
     *
     * @param body
     * @return
     */
    public String setParamObj(JSONObject body) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "10");
        routing.put("ROUTE_VALUE", "");
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        return root.toString();
    }

    /**
     * json 数据格式化：方法2
     *
     * @param body
     * @param phone
     * @return
     */
    public String setParamObj(JSONObject body, String bossno) {
        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject header = new JSONObject();
        JSONObject routing = new JSONObject();
        routing.put("ROUTE_KEY", "14");
        routing.put("ROUTE_VALUE", bossno);
        header.put("POOL_ID", "31");
        header.put("DB_ID", "");
        header.put("ENV_ID", "1");
        header.put("CONTACT_ID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        header.put("CHANNEL_ID", "155");
        header.put("USERNAME", "zqddxt");
        header.put("PASSWORD", "123456");
        header.put("ENDUSRLOGINID", "");
        header.put("ENDUSRIP", "");
        header.put("ROUTING", routing);
        root_.put("HEADER", header);
        root_.put("BODY", body);
        root.put("ROOT", root_);
        return root.toString();
    }

    /**
     * JSON 数据格式化： 方法3
     *
     * @param requestObject
     */
    public String setParamObj_2(JSONObject requestObject) {
        JSONObject root = new JSONObject();
        JSONObject contractRoot = new JSONObject();
        JSONObject tcpCont = new JSONObject();
        JSONObject svcCont = new JSONObject();
        tcpCont.put("svcCode", "73_Resource_CoverList");
        tcpCont.put("token", "psde92j59sg7trn9msa0i6cv3g");
        tcpCont.put("reqTime", DateUtil.convertDateToString(new Date(), "YYYYMMddHHmmSSS"));
        tcpCont.put("transactionID", "300100050120160202**********");
        contractRoot.put("tcpCont", tcpCont);
        svcCont.put("requestObject", requestObject);
        contractRoot.put("svcCont", svcCont);
        root.put("contractRoot", contractRoot);
        return root.toString();
    }


    public static void main(String[] args) throws Exception {
        // String json = java.net.URLEncoder.encode(obj.toString(), "utf-8");

    }

    /**
     * Post请求
     *
     * @param url
     * @param bjoj JSONObject
     * @return
     * @throws Exception
     */
    protected String setPost(String url, JSONObject bjoj) throws Exception {
        String obj = setParamObj(bjoj);
        logger.info("请求地址：" + url);
        URL restURL = new URL(url);
        HttpURLConnection conn = (HttpURLConnection) restURL.openConnection();
        conn.setRequestMethod("POST");
        conn.setDoOutput(true);
        conn.setAllowUserInteraction(false);
        // 设置维持长连接
        conn.setRequestProperty("Connection", "Keep-Alive");
        // 设置文件字符集:
        conn.setRequestProperty("Charset", "GBK");
        System.out.println("=====================>" + obj.toString());
        //转换为字节数组
        byte[] data = (obj).getBytes();
        // 设置文件长度
        conn.setRequestProperty("Content-Length", String.valueOf(data.length));
        // 设置文件类型:
        conn.setRequestProperty("contentType", "application/json");
        conn.setRequestProperty("Accept", "application/json");

        conn.setConnectTimeout(60000);  //设置连接主机超时（单位：毫秒）
        conn.setReadTimeout(60000 * 2);  //设置从主机读取数据超时（单位：毫秒）
        OutputStream out = conn.getOutputStream();
        // 写入请求的字符串
        out.write(obj.getBytes());
        out.flush();
        out.close();

        BufferedReader bReader = new BufferedReader(new InputStreamReader(
                conn.getInputStream(), "GBK"));
        String line, resultStr = "";
        while (null != (line = bReader.readLine())) {
            resultStr += line;
        }
        bReader.close();
        conn.disconnect();
        System.out.println("+++++++++++++++++++++++++++++++++++++++++++++:" + resultStr);
        return resultStr;
    }

    /**
     * 商机接口
     *
     * @param url               访问地址
     * @param opp_name          商机名称
     * @param business_other    商机种类（-1:非ICT商机，1:ICT商机）
     * @param opp_source        商机来源（05:调度中台）
     * @param opp_kind          商机类型（11:营销任务转商机）
     * @param invest_amount     预估金额（单位万元）
     * @param opp_content       商机内容
     * @param opp_desc          商机背景
     * @param cust_code         集团客户编码
     * @param opp_contacter     集团客户联系人
     * @param opp_contacter_tel 集团客户联系人电话
     * @param cust_mgr_id       客户经理ID
     * @param marketing_code    营销编号
     * @param push_order_flag   是否一键下单（1:是）
     * @param prod_list         产品列表
     * @param product_code      产品类型编码
     * @param order_no          订单编号
     * @param boss_order_no     BOSS订单编号
     * @return
     * @throws Exception
     */
    public Map<String, Object> oppSJPost(String url, String bust_list, String invest_amount, String cust_code, String opp_contacter, String opp_contacter_tel, String cust_mgr_id, List<Map<String, String>> prod_list) throws Exception {

        JSONObject root = new JSONObject();
        JSONObject root_ = new JSONObject();
        JSONObject tcpCont = new JSONObject();
        JSONObject svcCont = new JSONObject();

        JSONObject requestObject = JSONObject.fromObject(bust_list);
        tcpCont.put("svcCode", "72_createQuickOrderMarketOpp");
        tcpCont.put("transactionID", "" + (new Random(**********).nextInt() * *********) + "" + System.currentTimeMillis() + "");
        tcpCont.put("reqTime", "" + System.currentTimeMillis() + "");
        requestObject.remove("invest_amount");
        requestObject.put("invest_amount", "" + invest_amount + "");
        requestObject.put("cust_code", "" + cust_code + "");
        requestObject.put("opp_contacter", "" + opp_contacter + "");
        requestObject.put("opp_contacter_tel", "" + opp_contacter_tel + "");
        requestObject.put("cust_mgr_id", "" + cust_mgr_id + "");
        net.sf.json.JSONArray prod_lists = new net.sf.json.JSONArray();
        for (Map<String, String> map : prod_list) {
            JSONObject prod_list_ = new JSONObject();
            prod_list_.put("product_code", "" + map.get("product_code") + "");
            prod_list_.put("order_no", "" + map.get("order_no") + "");
            prod_list_.put("boss_order_no", "" + map.get("boss_order_no") + "");
            prod_lists.add(prod_list_);
        }
        requestObject.put("prod_list", prod_lists);
        svcCont.put("requestObject", requestObject);
        root_.put("tcpCont", tcpCont);
        root_.put("svcCont", svcCont);
        root.put("contractRoot", root_);
        String resultStr = setPoststr(url, root);
        // String resultStr="{\"contractRoot\":{\"tcpCont\":{\"transactionId\":\"300100050120160202**********\",\"rspTime\":\"20180510110433559\"},\"svcCont\":{\"resultCode\":\"0\",\"resultObject\":{\"opp_no\":\"100007001\"}}}}";
        System.out.println("==============================" + resultStr);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("return", resultStr);
        return map;
    }

    /**
     * Post请求
     *
     * @param url
     * @param bjoj string
     * @return
     * @throws Exception
     */
    private String setPoststr(String url, JSONObject bjoj) throws Exception {
        System.out.println("==========================+>" + bjoj);
        //RequestConfig config = RequestConfig.custom().setSocketTimeout(30000).setConnectTimeout(30000).build();
        //  CloseableHttpClient client = HttpClients.custom().setDefaultRequestConfig(config).build();
        HttpClient cl = new DefaultHttpClient();
        HttpPost hot = new HttpPost(url);
        hot.setHeader("Connection", "Keep-Alive");
        hot.setHeader("Charset", "UTF-8");
        hot.setHeader("Accept", "application/json");
        hot.setHeader("contentType", "application/json");
        StringEntity entity = new StringEntity(bjoj.toString(), "UTF-8");
        hot.setEntity(entity);
        HttpResponse hr = cl.execute(hot);
        // CloseableHttpResponse hr = client.execute(hot);
        String resultStr = null;
        if (hr != null) {
            HttpEntity st = hr.getEntity();
            if (st != null) {
                resultStr = EntityUtils.toString(st, "UTF-8");
            }
        }
        return resultStr;
    }

    /**
     * 网络部环节信息查询接口
     *
     * @param cHANCE_ID
     * @param string
     * @return
     * @throws Exception
     */
    public List<Map<String, Object>> qryIrmsSyncInfo(String cHANCE_ID, String string) throws Exception {
        List<Map<String, Object>> maplis = new ArrayList<>();
        String url = ESBWSURL + "orderSysSVC";
        JSONObject obj_ = new JSONObject();
        obj_.put("CHANCE_ID", cHANCE_ID);
        obj_.put("SHEET_TYPE", string);
        obj_.put("SERVICE_NAME", "qryIrmsSyncInfo");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        String resultStr = setPost(url, obj_);

        if (resultStr != null) {
            Map<String, Object> map_ = new HashMap<String, Object>();
            JSONObject root_ = JSONObject.fromObject(resultStr);
            JSONObject root = JSONObject.fromObject(root_.getString("ROOT"));
            JSONObject body = JSONObject.fromObject(root.getString("BODY"));
            map_.put("resCode", body.getString("RETURN_CODE"));
            map_.put("resMsg", body.getString("RETURN_MSG"));
            map_.put("detail_msg", body.getString("PROMPT_MSG"));
            String RETURN_MSG = body.getString("RETURN_CODE");
            if ("0".equals(RETURN_MSG)) {
                if (having(body, "OUT_DATA")) {
                    JSONObject jsonNo = JSONObject.fromObject(body.getString("OUT_DATA"));
                    if (having(jsonNo, "OUT_DATA")) {
                        JSONObject jsonNo_obj = JSONObject.fromObject(jsonNo.getString("OUT_DATA"));
                        if (having(jsonNo_obj, "ROOT")) {
                            JSONObject jsonNo_root = JSONObject.fromObject(jsonNo_obj.getString("ROOT"));
                            if (having(jsonNo_root, "RETURN_DETAIL")) {
                                JSONObject RETURN_DETAIL = JSONObject.fromObject(jsonNo_root.getString("RETURN_DETAIL"));
                                if (having(RETURN_DETAIL, "form")) {
                                    JSONObject json_form = JSONObject.fromObject(RETURN_DETAIL.getString("form"));
                                    List<Map<String, Object>> mapli_sheet = new ArrayList<>();
                                    if (having(json_form, "faultSheet")) {
                                        JSONArray jsonarray = new JSONArray(json_form.getString("faultSheet"));
                                        for (int i = 0; i < jsonarray.length(); i++) {
                                            Map<String, Object> map_sheet = new HashMap<String, Object>();
                                            JSONObject jsonStr_ = JSONObject.fromObject(jsonarray.getString(i));
                                            if (having(jsonStr_, "currentPerson")) {
                                                map_sheet.put("currentPerson", jsonStr_.getString("currentPerson"));
                                            }
                                            if (having(jsonStr_, "sendTime")) {
                                                map_sheet.put("sendTime", jsonStr_.getString("sendTime"));
                                            }
                                            if (having(jsonStr_, "serialnum")) {
                                                map_sheet.put("serialnum", jsonStr_.getString("serialnum"));
                                            }
                                            if (having(jsonStr_, "sheetname")) {
                                                map_sheet.put("sheetname", jsonStr_.getString("sheetname"));
                                            }
                                            if (having(jsonStr_, "isOver")) {
                                                map_sheet.put("isOver", jsonStr_.getString("isOver"));
                                            }
                                            if (having(jsonStr_, "fromNo")) {
                                                map_sheet.put("fromNo", jsonStr_.getString("fromNo"));
                                            }
                                            if (having(jsonStr_, "phone")) {
                                                map_sheet.put("phone", jsonStr_.getString("phone"));
                                            } else {
                                                map_sheet.put("phone", "--");
                                            }
                                            if (having(jsonStr_, "xcsg")) {
                                                JSONObject json_xcsg = JSONObject.fromObject(jsonStr_.getString("xcsg"));
                                                JSONArray xcsg_sheet_list = new JSONArray(json_xcsg.getString("scwlSheet"));
                                                List<Map<String, Object>> mapli_ = new ArrayList<>();
                                                for (int j = 0; j < xcsg_sheet_list.length(); j++) {
                                                    Map<String, Object> xcsg = new HashMap<String, Object>();
                                                    JSONObject json_xcsg_ = JSONObject.fromObject(xcsg_sheet_list.getString(j));
                                                    xcsg.put("scwlcsSendTime", json_xcsg_.getString("scwlcsSendTime"));
                                                    xcsg.put("scwlcsPhone", json_xcsg_.getString("scwlcsPhone"));
                                                    xcsg.put("scwlcsPerson", json_xcsg_.getString("scwlcsPerson"));
                                                    xcsg.put("scwlcsNo", json_xcsg_.getString("scwlcsNo"));
                                                    xcsg.put("scwlcsName", json_xcsg_.getString("scwlcsName"));
                                                    mapli_.add(xcsg);
                                                }
                                                map_sheet.put("xcsg", mapli_);
                                            }
                                            mapli_sheet.add(map_sheet);
                                        }
                                    }

                                    map_.put("falutSheet", mapli_sheet);
                                    maplis.add(map_);
                                }
                            }
                        }

                        if (having(jsonNo_obj, "form")) {
                            JSONObject json_form = JSONObject.fromObject(jsonNo_obj.getString("form"));
                            map_.put("from_type", json_form.getString("from_type"));
                            map_.put("current_state", json_form.getString("current_state"));
                            List<Map<String, Object>> mapli_sheet = new ArrayList<>();
                            if (having(json_form, "falutSheet")) {
                                JSONArray jsonarray = new JSONArray(json_form.getString("falutSheet"));
                                for (int i = 0; i < jsonarray.length(); i++) {
                                    Map<String, Object> map_sheet = new HashMap<String, Object>();
                                    JSONObject jsonStr_ = JSONObject.fromObject(jsonarray.getString(i));
                                    map_sheet.put("phone", jsonStr_.getString("phone"));
                                    map_sheet.put("sheetname", jsonStr_.getString("sheetname"));
                                    map_sheet.put("serialnum", jsonStr_.getString("serialnum"));
                                    map_sheet.put("sendTime", jsonStr_.getString("sendTime"));
                                    map_sheet.put("currentPerson", jsonStr_.getString("currentPerson"));
                                    if (having(jsonStr_, "xcsg")) {
                                        JSONObject json_xcsg = JSONObject.fromObject(jsonStr_.getString("xcsg"));
                                        JSONArray xcsg_sheet_list = new JSONArray(json_xcsg.getString("scwlSheet"));
                                        List<Map<String, Object>> mapli_ = new ArrayList<>();
                                        for (int j = 0; j < xcsg_sheet_list.length(); j++) {
                                            Map<String, Object> xcsg = new HashMap<String, Object>();
                                            JSONObject json_xcsg_ = JSONObject.fromObject(xcsg_sheet_list.getString(j));
                                            xcsg.put("scwlcsSendTime", json_xcsg_.getString("scwlcsSendTime"));
                                            xcsg.put("scwlcsPhone", json_xcsg_.getString("scwlcsPhone"));
                                            xcsg.put("scwlcsPerson", json_xcsg_.getString("scwlcsPerson"));
                                            xcsg.put("scwlcsNo", json_xcsg_.getString("scwlcsNo"));
                                            xcsg.put("scwlcsName", json_xcsg_.getString("scwlcsName"));
                                            mapli_.add(xcsg);
                                        }
                                        map_sheet.put("xcsg", mapli_);
                                    }
                                    mapli_sheet.add(map_sheet);
                                }
                            }
                            map_.put("falutSheet", mapli_sheet);
                            maplis.add(map_);

                        }
                    }
                }
            }


        }

        return maplis;
    }

    /**
     * s8000CfmCard
     * 普通缴费确认
     *
     * @param login_no工号     非空
     * @param login_password 工号密码	非空
     * @param group_id       机构代码	可不传入该节点，如果传入必须非空
     * @param op_code        操作代码	非空，传入8000、
     * @param contract_no    帐户ID	可空
     * @param phone_no       缴费号码	非空
     * @param pay_money      交费金额	非空,费用为分, ，可以传多个，中间用逗号分隔。
     * @param pay_path       缴费渠道	非空： 01:营业厅 02:自助终端 03:网上营业厅 04:充值平台 05:空中充值代理商 06:一级BOSS 07:银行 08:待办渠道 09:短信营业厅 10:wap营业厅 11:集团网上营业厅 12:IVR 13:经分	 14:工作队收取 15:快信营业厅 16:系统后台 17:网上商城 18:下月账务收取等 28:系统-签约用户-自动充值 42:天猫 43:移动商城
     * @param pay_method     缴费方式	非空： 0:现金 y:银行卡 2:信用卡 3:交费卡 c:充值卡 8:赠费 9:支票 B:空中充值转入 H:年度积分清零 j:积分换预存   J:集团帐号划拨 U:新业务卡充值 s:商务卡 A44:易充值 A22:深圳统一支付平台(签约用户 A33:深圳统一支付平台(非签约用户) 11:手机支付缴费 12:外省缴费 13:店员快充
     * @param pay_type       帐本类型	非空，可以传多个账本，中间用逗号分隔
     * @param delay_rate     滞纳金优惠率	可空，取值【0，1】之间，0：不优惠，1:全部优惠。
     * @param remonth_rate   补收月租优惠率	可空，取值【0，1】之间，0：不优惠，1:全部优惠。
     * @param bank_code      银行代码[对支票交费]	可为空
     * @param check_no       支票号码[对支票交费]	可为空
     * @param pay_note       交费备注	非空， 格式为：“注释|代缴费号码|卡号”，代缴费号码和卡号可以为空，但是“|”要保留
     * @param foreign_sn     外部流水号	非空，唯一
     * @param foreign_time   外部缴费时间	可为空，格式为YYYYMMDDHHMISS
     * @param ctrl_flag      控制标志	可为空 第1位：发送短信标志 0发送短信，1不发送； 第2位：缴费查询标志,规划字段，暂未使用 0调用缴费查询 1不调用； 第3位：是否根据foreign_sn校验唯一性,规划字段，暂未使用  0 不校验   1 校验
     * @param pay_rate       折扣率	用于根据传入值取缴费发送短信模板，目前只配置一级BOSS手机支付交话费时候传入0.99进行短信下发
     * @return
     * @throws Exception
     */
    public Map<String, Object> pres8000CfmCardSvc(String login_no, String login_password, String group_id, String op_code, Long contract_no, String phone_no, String pay_money, String pay_path, String pay_method, String pay_type, Double delay_rate, Double remonth_rate, String bank_code, String check_no, String pay_note, String foreign_sn, String foreign_time, String ctrl_flag, String pay_rate, String phone) throws Exception {
        JSONObject root = new JSONObject();
        root.put("LOGIN_NO", login_no);
        root.put("LOGIN_PASSWORD", login_password);
        root.put("GROUP_ID", group_id);
        root.put("OP_CODE", op_code);
        root.put("CONTRACT_NO", contract_no);
        root.put("PHONE_NO", phone_no);
        root.put("PAY_MONEY", pay_money);
        root.put("PAY_PATH", pay_path);
        root.put("PAY_METHOD", pay_method);
        root.put("PAY_TYPE", pay_type);
        root.put("DELAY_RATE", delay_rate);
        root.put("REMONTH_RATE", remonth_rate);
        root.put("BANK_CODE", !StringUtils.isBlank(bank_code) ? bank_code : "");
        root.put("CHECK_NO", !StringUtils.isBlank(check_no) ? check_no : "");
        root.put("PAY_NOTE", !StringUtils.isBlank(pay_note) ? pay_note + "|" : "");
        root.put("FOREIGN_SN", foreign_sn);
        if (!StringUtils.isBlank(foreign_time)) {
            root.put("FOREIGN_TIME", new SimpleDateFormat("yyyyMMddHHmmss").format(foreign_time));
        } else {
            root.put("FOREIGN_TIME", DateUtil.convertDateToString(new Date(), "yyyyMMddHHmmss"));
        }
        root.put("CTRL_FLAG", !StringUtils.isBlank(ctrl_flag) ? ctrl_flag : "");
        root.put("PAY_RATE", !StringUtils.isBlank(pay_rate) ? pay_rate : "");
        //服务器专用
        String resultStr = UrlConnection.responseGBK(ESBWSURL + "s8000CfmCard", CMCCOpenService.getInstance().setParamObj1(root, phone));
        //本地测试85调用正式环境

        //String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(ESBWSURL+"s8000CfmCard", CMCCOpenService.getInstance().setParamObj1(root,phone));
        //测试服专用测试
        //String resultStr= CMCC1000OpenService.getInstance().bdcesPatams("http://*************:52000/esbWS/rest/s8000CfmCard", CMCCOpenService.getInstance().setParamObj1(root,phone));
        logger.info(resultStr);
        Map<String, Object> rmap = new HashMap<String, Object>();
        rmap.put("content", CMCCOpenService.getInstance().setParamObj1(root, phone));
        if (resultStr != null) {
            JSONObject json = JSONObject.fromObject(resultStr);
            String reuslt = json.getString("Status");
            String jxresult = "";
            if (CMCCOpenService.having(json, "res")) {
                jxresult = json.getString("res");
            } else {
                rmap.put("res", resultStr);
                rmap.put("Status", json.getString("Status"));
            }
            if ("1".equals(reuslt)) {
                JSONObject jsonObject = JSONObject.fromObject(jxresult);
                if (CMCCOpenService.having(jsonObject, "ROOT")) {
                    JSONObject root_ = JSONObject.fromObject(jsonObject.getString("ROOT"));
                    //返回错误代码	0:成功，其他失败
                    if (CMCCOpenService.having(root_, "RETURN_CODE")) {
                        rmap.put("return_code", root_.getString("RETURN_CODE"));
                    }
                    //返回信息
                    if (CMCCOpenService.having(root_, "RETURN_MSG")) {
                        rmap.put("return_msg", root_.getString("RETURN_MSG"));
                    }
                    //返回详细信息
                    if (CMCCOpenService.having(root_, "DETAIL _MSG")) {
                        rmap.put("detail_msg", root_.getString("DETAIL_MSG"));
                    }
                    if (CMCCOpenService.having(root_, "OUT_DATA")) {
                        //缴费流水	可以传出多个流水，中间用逗号分隔
                        JSONObject out_data = JSONObject.fromObject(root_.getString("OUT_DATA"));
                        if (CMCCOpenService.having(out_data, "PAY_ACCEPT")) {
                            rmap.put("pay_accept", out_data.getString("PAY_ACCEPT"));
                        }
                        //缴费日期
                        if (CMCCOpenService.having(out_data, "TOTAL_DATE")) {
                            rmap.put("total_date", out_data.getString("TOTAL_DATE"));
                        }
                    }

                }

            } else {
                rmap.put("res", json.getString("res"));
                rmap.put("Status", json.getString("Status"));
            }
        }
        return rmap;
    }

    /***
     *  8000方法2：
     * @param params
     * @return
     * @throws Exception
     */
    public Map<String, Object> pres8000CfmCardSvc(String params) throws Exception {
        //服务器专用
        String resultStr = UrlConnection.responseGBK(ESBWSURL + "s8000CfmCard", params);
        //本地测试85调用正式环境
        //String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(url, params);
        //测试服专用测试
        //String resultStr= CMCC1000OpenService.getInstance().bdcesPatams("http://*************:52000/esbWS/rest/s8000CfmCard", params);
        logger.info(resultStr);

        //{"ROOT":{"RETURN_CODE":0,"RETURN_MSG":"ok!","USER_MSG":"ok!","DETAIL_MSG":"OK!","PROMPT_MSG":"","OUT_DATA":{"PAY_ACCEPT":"10007818720847","TOTAL_DATE":20190702}}}
        Map<String, Object> rmap = new HashMap<String, Object>();
        if (resultStr != null) {
            JSONObject json = JSONObject.fromObject(resultStr);
            String reuslt = json.getString("Status");
            String jxresult = "";
            if (CMCCOpenService.having(json, "res")) {
                jxresult = json.getString("res");
            } else {
                rmap.put("res", resultStr);
                rmap.put("Status", json.getString("Status"));
            }
            if ("1".equals(reuslt)) {
                JSONObject jsonObject = JSONObject.fromObject(jxresult);
                if (CMCCOpenService.having(jsonObject, "ROOT")) {
                    JSONObject root_ = JSONObject.fromObject(jsonObject.getString("ROOT"));
                    //返回错误代码	0:成功，其他失败
                    if (CMCCOpenService.having(root_, "RETURN_CODE")) {
                        rmap.put("return_code", root_.getString("RETURN_CODE"));
                    }
                    //返回信息
                    if (CMCCOpenService.having(root_, "RETURN_MSG")) {
                        rmap.put("return_msg", root_.getString("RETURN_MSG"));
                    }
                    //返回详细信息
                    if (CMCCOpenService.having(root_, "DETAIL _MSG")) {
                        rmap.put("detail_msg", root_.getString("DETAIL_MSG"));
                    }
                    if (CMCCOpenService.having(root_, "OUT_DATA")) {
                        //缴费流水	可以传出多个流水，中间用逗号分隔
                        JSONObject out_data = JSONObject.fromObject(root_.getString("OUT_DATA"));
                        if (CMCCOpenService.having(out_data, "PAY_ACCEPT")) {
                            rmap.put("pay_accept", out_data.getString("PAY_ACCEPT"));
                        }
                        //缴费日期
                        if (CMCCOpenService.having(out_data, "TOTAL_DATE")) {
                            rmap.put("total_date", out_data.getString("TOTAL_DATE"));
                        }
                    }

                }

            } else {
                rmap.put("res", json.getString("res"));
                rmap.put("Status", json.getString("Status"));
            }
        }
        return rmap;
    }

    /**
     * s8000CfmCard  参数格式化
     * 普通缴费确认
     *
     * @param login_no工号     非空
     * @param login_password 工号密码	非空
     * @param group_id       机构代码	可不传入该节点，如果传入必须非空
     * @param op_code        操作代码	非空，传入8000、
     * @param contract_no    帐户ID	可空
     * @param phone_no       缴费号码	非空
     * @param pay_money      交费金额	非空,费用为分, ，可以传多个，中间用逗号分隔。
     * @param pay_path       缴费渠道	非空： 01:营业厅 02:自助终端 03:网上营业厅 04:充值平台 05:空中充值代理商 06:一级BOSS 07:银行 08:待办渠道 09:短信营业厅 10:wap营业厅 11:集团网上营业厅 12:IVR 13:经分	 14:工作队收取 15:快信营业厅 16:系统后台 17:网上商城 18:下月账务收取等 28:系统-签约用户-自动充值 42:天猫 43:移动商城
     * @param pay_method     缴费方式	非空： 0:现金 y:银行卡 2:信用卡 3:交费卡 c:充值卡 8:赠费 9:支票 B:空中充值转入 H:年度积分清零 j:积分换预存   J:集团帐号划拨 U:新业务卡充值 s:商务卡 A44:易充值 A22:深圳统一支付平台(签约用户 A33:深圳统一支付平台(非签约用户) 11:手机支付缴费 12:外省缴费 13:店员快充
     * @param pay_type       帐本类型	非空，可以传多个账本，中间用逗号分隔
     * @param delay_rate     滞纳金优惠率	可空，取值【0，1】之间，0：不优惠，1:全部优惠。
     * @param remonth_rate   补收月租优惠率	可空，取值【0，1】之间，0：不优惠，1:全部优惠。
     * @param bank_code      银行代码[对支票交费]	可为空
     * @param check_no       支票号码[对支票交费]	可为空
     * @param pay_note       交费备注	非空， 格式为：“注释|代缴费号码|卡号”，代缴费号码和卡号可以为空，但是“|”要保留
     * @param foreign_sn     外部流水号	非空，唯一
     * @param foreign_time   外部缴费时间	可为空，格式为YYYYMMDDHHMISS
     * @param ctrl_flag      控制标志	可为空 第1位：发送短信标志 0发送短信，1不发送； 第2位：缴费查询标志,规划字段，暂未使用 0调用缴费查询 1不调用； 第3位：是否根据foreign_sn校验唯一性,规划字段，暂未使用  0 不校验   1 校验
     * @param pay_rate       折扣率	用于根据传入值取缴费发送短信模板，目前只配置一级BOSS手机支付交话费时候传入0.99进行短信下发
     * @return
     * @throws Exception
     */
    public String setParamPres8000CfmCardSvc(String login_no, String login_password, String group_id, String op_code, Long contract_no, String phone_no, String pay_money, String pay_path, String pay_method, String pay_type, Double delay_rate, Double remonth_rate, String bank_code, String check_no, String pay_note, String foreign_sn, String foreign_time, String ctrl_flag, String pay_rate, String phone) throws Exception {
        JSONObject root = new JSONObject();
        root.put("LOGIN_NO", login_no);
        root.put("LOGIN_PASSWORD", login_password);
        root.put("GROUP_ID", group_id);
        root.put("OP_CODE", op_code);
        root.put("CONTRACT_NO", contract_no);
        root.put("PHONE_NO", phone_no);
        root.put("PAY_MONEY", pay_money);
        root.put("PAY_PATH", pay_path);
        root.put("PAY_METHOD", pay_method);
        root.put("PAY_TYPE", pay_type);
        root.put("DELAY_RATE", delay_rate);
        root.put("REMONTH_RATE", remonth_rate);
        root.put("BANK_CODE", !StringUtils.isBlank(bank_code) ? bank_code : "");
        root.put("CHECK_NO", !StringUtils.isBlank(check_no) ? check_no : "");
        root.put("PAY_NOTE", !StringUtils.isBlank(pay_note) ? pay_note + "|" : "");
        root.put("FOREIGN_SN", foreign_sn);
        if (!StringUtils.isBlank(foreign_time)) {
            root.put("FOREIGN_TIME", new SimpleDateFormat("yyyyMMddHHmmss").format(foreign_time));
        } else {
            root.put("FOREIGN_TIME", DateUtil.convertDateToString(new Date(), "yyyyMMddHHmmss"));
        }
        root.put("CTRL_FLAG", !StringUtils.isBlank(ctrl_flag) ? ctrl_flag : "");
        root.put("PAY_RATE", !StringUtils.isBlank(pay_rate) ? pay_rate : "");
        ///正式环境：
        return CMCCOpenService.getInstance().setParamObj(root, login_no);
        //测试环境：
        //return CMCCOpenService.getInstance().setParamObj(root,login_no);
    }


    /**
     * qryIrmsSyncInf(透明化环节信息查询)
     *
     * @throws Exception
     */
    public Map<String, Object> qryIrmsSyncInf(String chance_id, String sheet_type, String sheetline_id) throws Exception {
        String url = ESBWSURL + "orderSysSVC";
        JSONObject obj_ = new JSONObject();
        obj_.put("CHANCE_ID", chance_id);
        obj_.put("SHEET_TYPE", sheet_type);
        obj_.put("SHEETLINE_ID", sheetline_id);
        obj_.put("SERVICE_NAME", "qryIrmsSyncInfo");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        String resultStr = setPost(url, obj_);
//					String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(obj_));

        logger.info("qryIrmsSyncInf+++ESB返回数据：" + resultStr);
        Map<String, Object> map_ = new HashMap<String, Object>();
        if (resultStr != null) {
            JSONObject root_ = JSONObject.fromObject(resultStr);
//				JSONObject res = JSONObject.fromObject(root_.getString("res"));
            JSONObject root = JSONObject.fromObject(root_.getString("ROOT"));
            if (!having(root, "BODY")) {
                map_.put("RETURN_CODE", root.getString("RETURN_CODE"));
                map_.put("RETURN_MSG", root.getString("RETURN_MSG"));
                map_.put("DETAIL_MSG", root.getString("DETAIL_MSG"));
                return map_;
            }
            map_.put("result", root.getString("BODY"));
        }
        return map_;
    }

    /**
     * qryBosschanceBycustId(工单信息查询接口)
     *
     * @param UNIT_ID       1	String	10	集团280编码	2801172543
     * @param CHANCE_STATUS 0	String	5	工单状态	01|在途、03|已完成、08|非正常终止
     * @param REQ_TYPE      0	String	5	请求类型	01|开通、02|变更、03|暂停、04|暂停恢复、05|退订
     * @param START_TIME    0	String	10	开始时间	20181031
     * @param END_TIME      0	String	10	结束时间	20181131
     * @param OPER_NO       0	String	20	BOSS工号
     * @throws Exception
     */
    public Map<String, Object> qryBosschanceBycustId(String unit_id, String chance_status, String req_type, String start_time, String end_time, String oper_no, String page_num, String page_size) throws Exception {
        String url = ESBWSURL + "orderSysSVC";
        JSONObject obj_ = new JSONObject();
        obj_.put("UNIT_ID", unit_id);
        obj_.put("CHANCE_STATUS", chance_status);
        obj_.put("REQ_TYPE", req_type);
        obj_.put("START_TIME", start_time);
        obj_.put("END_TIME", end_time);
        obj_.put("OPER_NO", oper_no);
        obj_.put("PAGE_NUM", page_num);
        obj_.put("PAGE_SIZE", page_size);
        obj_.put("SERVICE_NAME", "qryChanceInfoBycustId");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        String resultStr = setPost(url, obj_);
//		String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(obj_));
        logger.info("qryBosschanceBycustId+++ESB返回数据：" + resultStr);
        Map<String, Object> map_ = new HashMap<String, Object>();
        if (resultStr != null) {
            JSONObject root_ = JSONObject.fromObject(resultStr);
//			JSONObject res = JSONObject.fromObject(root_.getString("res"));

            JSONObject root = JSONObject.fromObject(root_.getString("ROOT"));
            if (!having(root, "BODY")) {
                map_.put("RETURN_CODE", root.getString("RETURN_CODE"));
                map_.put("RETURN_MSG", root.getString("RETURN_MSG"));
                map_.put("DETAIL_MSG", root.getString("DETAIL_MSG"));
                return map_;
            }
            map_.put("result", root.getString("BODY"));
        }
        return map_;
    }

    /**
     * qryChanceDetailMsg(BOSS工单流转记录查询)
     *
     * @param CHANCE_ID 1	String	20	BOSS编号	20181022220783235
     * @throws Exception
     */
    public Map<String, Object> qryChanceDetailMsg(String chance_id) throws Exception {
        String url = ESBWSURL + "orderSysSVC";
        JSONObject obj_ = new JSONObject();
        obj_.put("CHANCE_ID", chance_id);
        obj_.put("SERVICE_NAME", "qryChanceDetailMsg");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        String resultStr = setPost(url, obj_);
//		String resultStr= CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(obj_));
        Map<String, Object> map_ = new HashMap<String, Object>();
        logger.info("qryChanceDetailMsg+++ESB返回数据：" + resultStr);
        if (resultStr != null) {
            JSONObject root_ = JSONObject.fromObject(resultStr);
//				 JSONObject res = JSONObject.fromObject(root_.getString("res"));
            JSONObject root = JSONObject.fromObject(root_.getString("ROOT"));
            if (!having(root, "BODY")) {
                map_.put("RETURN_CODE", root.getString("RETURN_CODE"));
                map_.put("RETURN_MSG", root.getString("RETURN_MSG"));
                map_.put("DETAIL_MSG", root.getString("DETAIL_MSG"));
                return map_;
            }
            map_.put("result", root.getString("BODY"));

        }
        return map_;
    }

    /**
     * qryGrpPrdPrc(集团产品资费查询服务)
     *
     * @param SERVICE_NAME   服务名称， 默认qryGrpPrdPrc
     * @param CALL_TIME      服务调用时间，  时间格式：yyyy-mm-dd hh24:mi:ss
     * @param SERVICE_CALLER 服务调用方,   ORDER_SYS_APP|订单系统APP
     * @param prod_id        产品编码
     * @param region_id      地市编码
     * @param login_no       计费号码
     * @return
     * @throws Exception
     */
    public String qryGrpPrdPrc(String prod_id, String region_id, String login_no) throws Exception {
        String url = ESBWSURL + "orderSysSVC";
        JSONObject obj_ = new JSONObject();
        obj_.put("PROD_ID", prod_id);
        obj_.put("REGION_ID", region_id);
        obj_.put("LOGIN_NO", login_no);
        obj_.put("SERVICE_NAME", "qryGrpPrdPrc");
        obj_.put("SERVICE_CALLER", "ORDER_SYS_APP");
        obj_.put("CALL_TIME", new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date()));
        //服务器专用
        String resultStr = UrlConnection.responseGBK(url, CMCCOpenService.getInstance().setParamObj(obj_));

        //return setPost(url,obj_);
        //本地测试连接
        //String resultStr=  CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(obj_));
        if (resultStr != null) {
            JSONObject json = JSONObject.fromObject(resultStr);
            String reuslt = json.getString("Status");
            String jxresult = "";
            if (CMCCOpenService.having(json, "res")) {
                jxresult = json.getString("res");
            }
            if ("1".equals(reuslt)) {
                JSONObject jsonObject = JSONObject.fromObject(jxresult);
                if (CMCCOpenService.having(jsonObject, "ROOT")) {
                    return jsonObject.getString("ROOT");
                }
            }
            return jxresult;
        } else {
            return ":{\"return_code\":\"-100\",\"return_msg\":\"服务异常!\",\"detail_msg\":\"\"}";
        }
    }


    /***
     *     产品编码查询
     * @param prcs            定价编码
     * @param qry_type 查询类型	1：开户；6：资费订购
     * @param main_pg_prcid  主商品定价
     * @param tar_pg_id     目标商品
     * @param tar_pg_prcid  目标商品定价
     * @param cur_pg_prcid  变更前商品定价
     * @param group_id        组织节点
     * @param release_flag        是否校验市场发布	Y表示不校验，N或空表示校验
     * @param cur_exp_date        变更前资费的失效时间
     * @param qry_flag                是否是两个主资费	Y是/N否
     * @param id_no                    用户ID
     * @param phone_no            号码
     * @param prc_class_flag    是否根据分类查询资费标记
     * @param open_time            开户时间
     * @param continue_flag        是否查询续签资费	当为Y时查询续签资费
     * @param fast_chk                是否需要做可见即可得校验
     * @param package_prc            包资费
     * @param login_no            工号
     * @param op_code            操作代码	1000-开户； 1104-资费变更
     * @return
     */
    public String com_sitech_pgmng_atom_inter_IPopularPgPrcQryAoSvc_query(String[] prcs, String qry_type
            , String main_pg_prcid, String tar_pg_id, String tar_pg_prcid, String cur_pg_prcid, String group_id, String release_flag, String cur_exp_date
            , String qry_flag, String id_no, String phone_no, String prc_class_flag, String open_time, String continue_flag, String fast_chk, String package_prc
            , String login_no, String op_code) {
        String url = ESBWSURL + "com_sitech_pgmng_atom_inter_IPopularPgPrcQryAoSvc_query";
        //com_sitech_pgmng_atom_inter_IPopularPgPrcQryAoSvc_query
        JSONObject body = new JSONObject();
        JSONObject busi_info = new JSONObject();
        net.sf.json.JSONArray pgprc_list = new net.sf.json.JSONArray();
        JSONObject prc_id = new JSONObject();
        for (int i = 0; i < prcs.length; i++) {
            prc_id.put("PRC_ID", prcs[i]);
            pgprc_list.add(prc_id);
        }
        busi_info.put("PGPRC_LIST", pgprc_list);
        if (!StringUtils.isBlank(qry_type)) {
            busi_info.put("QRY_TYPE", qry_type);
        } else {
            busi_info.put("QRY_TYPE", "");
        }
        if (!StringUtils.isBlank(main_pg_prcid)) {
            busi_info.put("MAIN_PG_PRCID", main_pg_prcid);//主商品定价
        } else {
            busi_info.put("MAIN_PG_PRCID", "");
        }
        if (!StringUtils.isBlank(tar_pg_id)) {
            busi_info.put("TAR_PG_ID", tar_pg_id);//目标商品
        } else {
            busi_info.put("TAR_PG_ID", "");
        }
        if (!StringUtils.isBlank(tar_pg_prcid)) {
            busi_info.put("TAR_PG_PRCID", tar_pg_prcid);//目标商品定价
        } else {
            busi_info.put("TAR_PG_PRCID", "");
        }
        if (!StringUtils.isBlank(cur_pg_prcid)) {
            busi_info.put("CUR_PG_PRCID", cur_pg_prcid);//变更前商品定价
        } else {
            busi_info.put("CUR_PG_PRCID", "");
        }
        if (!StringUtils.isBlank(group_id)) {
            busi_info.put("GROUP_ID", group_id);//组织节点
        } else {
            busi_info.put("GROUP_ID", "");
        }
        if (!StringUtils.isBlank(release_flag)) {
            busi_info.put("RELEASE_FLAG", release_flag);//是否校验市场发布	Y表示不校验，N或空表示校验
        } else {
            busi_info.put("RELEASE_FLAG", "");
        }
        if (!StringUtils.isBlank(cur_exp_date)) {
            busi_info.put("CUR_EXP_DATE", cur_exp_date);//变更前资费的失效时间
        } else {
            busi_info.put("CUR_EXP_DATE", "");
        }
        if (!StringUtils.isBlank(qry_flag)) {
            busi_info.put("QRY_FLAG", qry_flag);//是否是两个主资费	Y是/N否
        } else {
            busi_info.put("QRY_FLAG", "");
        }
        if (!StringUtils.isBlank(id_no)) {
            busi_info.put("ID_NO", id_no);//用户ID
        } else {
            busi_info.put("ID_NO", id_no);
        }
        if (!StringUtils.isBlank(phone_no)) {
            busi_info.put("PHONE_NO", phone_no);//号码
        } else {
            busi_info.put("PHONE_NO", "");
        }
        if (!StringUtils.isBlank(prc_class_flag)) {
            busi_info.put("PRC_CLASS_FLAG", prc_class_flag);//是否根据分类查询资费标记	Y是/N否
        } else {
            busi_info.put("PRC_CLASS_FLAG", "");
        }
        if (!StringUtils.isBlank(open_time)) {
            busi_info.put("OPEN_TIME", open_time);//开户时间
        } else {
            busi_info.put("OPEN_TIME", "");
        }
        if (!StringUtils.isBlank(continue_flag)) {
            busi_info.put("CONTINUE_FLAG", continue_flag);//是否查询续签资费	当为Y时查询续签资费
        } else {
            busi_info.put("CONTINUE_FLAG", continue_flag);
        }
        if (!StringUtils.isBlank(fast_chk)) {
            busi_info.put("FAST_CHK", fast_chk);//是否需要做可见即可得校验
        } else {
            busi_info.put("FAST_CHK", "");
        }
        if (!StringUtils.isBlank(package_prc)) {
            busi_info.put("PACKAGE_PRC", package_prc);//包资费
        } else {
            busi_info.put("PACKAGE_PRC", "");//包资费
        }
        JSONObject opr_info = new JSONObject();
        if (!StringUtils.isBlank(login_no)) {
            opr_info.put("LOGIN_NO", login_no);//工号
        } else {
            opr_info.put("LOGIN_NO", "");
        }
        if (!StringUtils.isBlank(op_code)) {
            opr_info.put("OP_CODE", op_code);//操作代码
        } else {
            opr_info.put("OP_CODE", "");
        }
        body.put("BUSI_INFO", busi_info);
        body.put("OPR_INFO", opr_info);
        //本地测试
        //String resultStr=  CMCC1000OpenService.getInstance().bdcesPatams("http://*************:52000/esbWS/res/com_sitech_pgmng_atom_inter_IPopularPgPrcQryAoSvc_query", CMCCOpenService.getInstance().setParamObj1(body,phone_no));
        //服务器
        String resultStr = UrlConnection.responseGBK(url, CMCCOpenService.getInstance().setParamObj(body));
        logger.info(resultStr);
        if (resultStr != null) {
            JSONObject json = JSONObject.fromObject(resultStr);
            String reuslt = json.getString("Status");
            String jxresult = "";
            if (CMCCOpenService.having(json, "res")) {
                jxresult = json.getString("res");
            }
            if ("1".equals(reuslt)) {
                JSONObject jsonObject = JSONObject.fromObject(jxresult);
                if (CMCCOpenService.having(jsonObject, "contractRoot")) {
                    JSONObject contractRoot = JSONObject.fromObject(jsonObject.get("contractRoot"));
                    if (CMCCOpenService.having(contractRoot, "svcCont")) {
                        return contractRoot.getString("svcCont");
                    } else {
                        return contractRoot.getString("contractRoot");
                    }
                } else {
                    return jxresult;
                }
            } else {
                return "{\"return_code\":\"-1\",\"return_msg\":\"" + jxresult + "\",\"detail_msg\":\"\"}";
            }
        } else {
            return "{\"return_code\":\"-100\",\"return_msg\":\"服务异常!\",\"detail_msg\":\"\"}";
        }
    }

    /**
     * @param phone_no
     * @param master_serv_id
     * @param channel_type
     * @param province_group
     * @param login_no
     * @param service_no
     * @param act_id
     * @param means_id
     * @return
     */
    public String s4035IntChk(String phone_no, String master_serv_id, String channel_type, String province_group, String login_no, String service_no, String act_id, String means_id, String back_code) {
        String url = ESBWSURL + "com_sitech_marketsvc_comp_inter_s4035_IP4035IntChkCoSvc_chk";//"s4035IntChk";
//		JSONObject body = new JSONObject();
//		JSONObject request_info = new JSONObject();
//		JSONObject opr_info = new JSONObject();
//		opr_info.put("CHANNEL_TYPE",channel_type);
//		opr_info.put("PROVINCE_GROUP",province_group);
//		opr_info.put("LOGIN_NO",login_no);
//		//opr_info.put("SERVICE_NO",service_no);
//		opr_info.put("PHONE_NO",service_no);
//		opr_info.put("ACT_ID",act_id);
//		opr_info.put("MEANS_ID",means_id);
//		request_info.put("OPR_INFO",opr_info);
//		body.put("MASTER_SERV_ID",master_serv_id);
//		body.put("SERVICE_NO",phone_no);
//		body.put("REQUEST_INFO",request_info);
        JSONObject body = new JSONObject();
        JSONObject request_info = new JSONObject();
        JSONObject opr_info = new JSONObject();
        opr_info.put("CHANNEL_TYPE", channel_type);
        opr_info.put("PHONE_NO", service_no);
        opr_info.put("ACT_ID", act_id);
        opr_info.put("LOGIN_NO", login_no);
        opr_info.put("PROVINCE_GROUP", province_group);
        if (!StringUtils.isBlank(back_code)) {
            opr_info.put("BACK_CODE", back_code);
        } else {
            opr_info.put("BACK_CODE", "");
        }
        net.sf.json.JSONArray prod_lists = new net.sf.json.JSONArray();
        String[] strm = means_id.split(",");
        for (int i = 0; i < strm.length; i++) {
            JSONObject prod_list_ = new JSONObject();
            prod_list_.put("MEANS_ID", "" + strm[i]);
            prod_lists.add(prod_list_);
        }
        opr_info.put("MEANS_ID", prod_lists);
        request_info.put("OPR_INFO", opr_info);
        body.put("REQUEST_INFO", request_info);
        //本地测试
        // String resultStr=  CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj1(body,phone_no));
        //String resultStr=  CMCC1000OpenService.getInstance().bdcesPatams("http://*************:52000/esbWS/res/s4035IntChk", CMCCOpenService.getInstance().setParamObj1(body,phone_no));

        //服务器
        String resultStr = UrlConnection.responseGBK(url, CMCCOpenService.getInstance().setParamObj1(body, phone_no));
        //String resultStr= "{\"Status\":\"1\",\"res\":\"{\\\"RETURN_CODE\	\\":\\\"0\\\",\\\"RETURN_MSG\\\":\\\"ok!\\\",\\\"USER_MSG\\\":\\\"处理成功!\\\",\\\"DETAIL_MSG\\\":\\\"OK!\\\",\\\"PROMPT_MSG\\\":{\\\"@type\\\":\\\"string\\\"},\\\"OUT_DATA\\\":{\\\"MEAN_ALL\\\":{\\\"LIMIT_INFO\\\":[{\\\"LIMIT_CODE\\\":\\\"0000\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_NAME\\\":\\\"参加了某类(活动ID)或某些(档次ID)或任一营销活动达到一定次数后不能再参加\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"NOTE\\\":\\\"该活动一年只能参加一次\\\",\\\"CODE_NAME\\\":\\\"4-与营销活动关系\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"LIMIT_VALUE\\\":\\\"F99479210000\\\",\\\"LIMIT_TYPE\\\":\\\"15\\\",\\\"LIMIT_SERIAL\\\":\\\"F99479210000\\\",\\\"TEAM_NO\\\":{\\\"@type\\\":\\\"string\\\"},\\\"PASS_FLAG\\\":\\\"Y\\\"},{\\\"LIMIT_CODE\\\":\\\"0000\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_NAME\\\":\\\"参加了某类(活动ID)或某些(档次ID)或任一营销活动达到一定次数后不能再参加\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"NOTE\\\":\\\"办理过终端活动的用户才能参加该活动\\\",\\\"CODE_NAME\\\":\\\"4-与营销活动关系\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"LIMIT_VALUE\\\":\\\"F99479210001\\\",\\\"LIMIT_TYPE\\\":\\\"15\\\",\\\"LIMIT_SERIAL\\\":\\\"F99479210001\\\",\\\"TEAM_NO\\\":\\\"1\\\",\\\"PASS_FLAG\\\":\\\"N\\\"},{\\\"LIMIT_CODE\\\":\\\"8060\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_NAME\\\":\\\"参加了某类(活动ID)或某些(档次ID)或任一营销活动达到一定次数后不能再参加\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"NOTE\\\":\\\"近三个月购买了终端的用户才能参加该活动\\\",\\\"CODE_NAME\\\":\\\"4-与营销活动关系\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"LIMIT_VALUE\\\":\\\"3\\\",\\\"LIMIT_TYPE\\\":\\\"13\\\",\\\"LIMIT_SERIAL\\\":\\\"F99479210002\\\",\\\"TEAM_NO\\\":\\\"1\\\",\\\"PASS_FLAG\\\":\\\"N\\\"},{\\\"LIMIT_CODE\\\":\\\"7101\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_NAME\\\":\\\"某些工号才能办理\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"NOTE\\\":\\\"仅以旧换新平台工号可以办理\\\",\\\"CODE_NAME\\\":\\\"7-目标用户群限制\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"LIMIT_VALUE\\\":\\\"105876545\\\",\\\"LIMIT_TYPE\\\":\\\"08\\\",\\\"LIMIT_SERIAL\\\":\\\"F99479210003\\\",\\\"TEAM_NO\\\":{\\\"@type\\\":\\\"string\\\"},\\\"PASS_FLAG\\\":\\\"N\\\"},{\\\"LIMIT_CODE\\\":\\\"4027\\\",\\\"LIMIT_LEVEL\\\":\\\"2\\\",\\\"LIMIT_NAME\\\":\\\"参加了某类(活动ID)或某些(档次ID)或任一营销活动达到一定次数后不能再参加\\\",\\\"LIMIT_TAR\\\":\\\"0\\\",\\\"NOTE\\\":\\\"对不起，该类型活动只能参加一次\\\",\\\"CODE_NAME\\\":\\\"4-与营销活动关系\\\",\\\"TAR_NAME\\\":\\\"办理号码\\\",\\\"LIMIT_VALUE\\\":\\\"^AZ411024^1^0\\\",\\\"LIMIT_TYPE\\\":\\\"02\\\",\\\"LIMIT_SERIAL\\\":\\\"F99479210004\\\",\\\"TEAM_NO\\\":{\\\"@type\\\":\\\"string\\\"},\\\"PASS_FLAG\\\":\\\"Y\\\"}],\\\"PASS_FLAG\\\":\\\"N\\\"},\\\"PASS_FLAG\\\":\\\"N\\\"}}\"}";// CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(body));
        logger.info(resultStr);
        if (resultStr != null) {
            JSONObject json = JSONObject.fromObject(resultStr);
            String reuslt = json.getString("Status");
            String jxresult = "";
            if (CMCCOpenService.having(json, "res")) {
                jxresult = json.getString("res");
            }
            //
            if ("1".equals(reuslt)) {
                JSONObject jsonObject = JSONObject.fromObject(jxresult);
                if (CMCCOpenService.having(jsonObject, "OUT_DATA")) {
                    JSONObject contractRoot = JSONObject.fromObject(jsonObject.get("OUT_DATA"));
                    if (CMCCOpenService.having(contractRoot, "MEAN_ALL")) {
                        JSONObject mean_all = JSONObject.fromObject(contractRoot.get("MEAN_ALL"));
                        if (CMCCOpenService.having(mean_all, "LIMIT_INFO")) {
                            jsonObject.put("OUT_DATA", mean_all.getString("LIMIT_INFO"));
                            jxresult = jsonObject + "";
                        }
                    }
                } else if (CMCCOpenService.having(jsonObject, "ROOT")) {
                    return jsonObject.get("ROOT") + "";
                }

                return jxresult;

            } else {
                return "{\"return_code\":\"-1\",\"return_msg\":\"" + jxresult + "\",\"detail_msg\":\"\"}";
            }
        } else {
            return "{\"return_code\":\"-100\",\"return_msg\":\"服务异常!\",\"detail_msg\":\"\"}";
        }

    }


    /***
     *  融合专线（勘察工单） 审批后调用6429工单处理接口
     * @param chance_id BOSS 6429商机编码
     * @param oper_code 类型编号 Y,N
     * @param oper_desc   意见
     * @param aagh5P 发起人工号
     * @param deal_name 发起人名字
     * @param call_time 调用实践
     * @return
     */
    public String sDealRejectionOrder(String chance_id, String oper_code, String oper_desc, String aagh5P, String deal_name, String call_time) {
        //System.out.println("BOSS 6429商机编码"+chance_id);
        //System.out.println("类型编号"+oper_code);
        //System.out.println("意见"+oper_desc);
        //System.out.println("发起人工号"+aagh5P);
        //System.out.println("发起人名字 "+deal_name);
        //System.out.println("调用实践"+call_time);
        //String url =  ESBWSURL + "orderSysSVC";
        JSONObject obj = new JSONObject();
        obj.put("SERVICE_NAME", "orderSysDealInvestEvaluateAuditEntity");//写死
        obj.put("SERVICE_CALLER", "ORDER_SYS_APP");//写死
        obj.put("CALL_TIME", call_time);//调用实践
        JSONObject obj_ = new JSONObject();
        obj_.put("aagh5P", aagh5P);//办理工号
        obj_.put("DEAL_NAME", deal_name);//办理工号名称
        obj_.put("AUDIT_FLAG", oper_code);//审批标识  Y-通过/同意  N-不通过/不同意
        obj_.put("BUSI_ID", chance_id);//商机编码，对应BOSS工单编码
        obj_.put("REASON", oper_desc);//原因
        obj.put("BUSI_INFO", obj_);
        //System.out.println("拼接的字符串为" + obj);
        //String getParas = CMCCOpenService.getInstance().setParamObj(obj);
        //System.out.println("6429工单处理接口，输入参数为" + getParas + "=============================================");

        // 本地端接口访问地址
        //String url = "http://10.113.171.172:51000/esbWS/rest/com_sitech_salemng_service_common_inter_ICompDealOutSysBusinessSvc_dealPreOpp";
        //String resultStr = CMCC1000OpenService.getInstance().bdcesPatams(url, CMCCOpenService.getInstance().setParamObj(obj));
        //String s = HttpURLConnectClientFactory.analyticParamsByResult(resultStr).toString();
        //logger.info("6429工单处理接口，返回参数：" + resultStr);
        //logger.info("url：" + url);
        logger.info("===>sDealRejectionOrder的请求参数为:" + obj);
        // BOSS端接口访问地址
        Result resultStr = HttpURLConnectClientFactory.responseText(CMCCOpenService.getInstance().setParamObj(obj)
                , "com_sitech_salemng_service_common_inter_ICompDealOutSysBusinessSvc_dealPreOpp"
                , "UTF-8");
        logger.info("===>sDealRejectionOrder:" + resultStr.toString());
        return resultStr.toString();
        //return s;
    }

    public String queryAreaService(String province, String city, String county, String tows) throws UnsupportedEncodingException {
        String url = REQUER_DISTRICTS; //http://*************:9999/v1/districts/query
        JSONObject body = new JSONObject();
        body.put("province", province);
        body.put("city", city);
        body.put("county", county);
        body.put("tows", tows);
        logger.info("入参为" + body);
        if (isES) {
            //Result result = HttpURLConnectClientFactory.responseByCharset(url, body.toString(), "UTF-8");
            String resultStr = UrlConnection.responseUTF8(url, body.toString());
            JSONObject jsonObject = JSONObject.fromObject(resultStr);
            logger.info("出参为" + resultStr);
            return jsonObject.getString("res");
        }
        String jsonString = CMCC1000OpenService.getInstance().bdcesPatams(url, body.toString());
        //logger.info("===>queryAreaService:province1" + URLDecoder.decode(province, "UTF-8"));
        //logger.info("===>queryAreaService:province2" + province);
        //String resultStr=  CMCC1000OpenService.getInstance().bdcesPatams(url, body.toString());
        //Result result = HttpURLConnectClientFactory.responseByCharset(url, body.toString(), "UTF-8");
        //JSONObject jsonObject = JSONObject.fromObject(resultStr);
        //System.out.println("输出"+jsonObject.getString("res"));
        return jsonString;
        //logger.info("区域查询路径为"+url);
    }
}
