package com.xinxinsoft.sendComms;

import com.xinxinsoft.entity.groupcustomer.GroupCustomer;
import com.xinxinsoft.utils.DateUtil;
import com.xinxinsoft.utils.WsdlUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 功能：调用集团客户信息查询类
 *
 * <AUTHOR>
 */
public class GroupJobNumberService {

    private String customerSoapWsdl = "";
    private static final Logger logger = LoggerFactory.getLogger(GroupJobNumberService.class);
    private static GroupJobNumberService customerService = null;
    private String xmlheader = "<?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:impl='http://impl.services.inf.ztesoft.com'><soapenv:Header/><soapenv:Body><impl:commonService soapenv:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/'><reqXml xsi:type='soapenc:string' xmlns:soapenc='http://schemas.xmlsoap.org/soap/encoding/'><![CDATA[";
    private String xmlend = "]]></reqXml> </impl:commonService></soapenv:Body></soapenv:Envelope>";
    private static Boolean isES = false;

    static {
        if ("*************".equals(DateUtil.getLocalIp()) || "*************".equals(DateUtil.getLocalIp())) {
            isES = true;
        }
    }

    public static GroupJobNumberService getInstance() {
        if (customerService == null) {
            synchronized (GroupJobNumberService.class) {
                if (customerService == null) {
                    customerService = new GroupJobNumberService();
                }
            }
        }
        return customerService;
    }

    private GroupJobNumberService() {
        loadWebService();
    }

    /**
     * 功能：加载数据库配置文件
     */
    public void loadWebService() {
        InputStream in = this.getClass().getClassLoader()
                .getResourceAsStream("WebService-config.properties");
        Properties properties = new Properties();
        try {
            properties.load(in);
            customerSoapWsdl = properties.getProperty("customerSoapWsdl");
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                in.close();
                properties.clear();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }

    /**
     * 功能:查询集团客户接口
     *
     * @param CUST_CODE 集团编码
     * @param CUST_NAME 集团名称
     * @return
     * @throws Exception
     */
    public GroupCustomer getCustInfoQuery(String CUST_CODE, String CUST_NAME)
            throws Exception {
        if (isES) {
            String TRANSACTION_ID = getTRANSACTIONID();
            String XmlStr = xmlheader
                    + getXml(TRANSACTION_ID, CUST_CODE, CUST_NAME) + xmlend;
            TuxedoClient client = new TuxedoClient();
            //logger.info("请求地址===>" + customerSoapWsdl);
            logger.info("集团查询请求入参===>" + XmlStr);
            String result = client.tuxedoSoapClient(XmlStr, customerSoapWsdl);
            //logger.info("集团查询请求出参===>" + result);
            // String result =
            // "<?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><soapenv:Body><ns1:commonServiceResponse soapenv:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/' xmlns:ns1='http://impl.services.inf.ztesoft.com'><commonServiceReturn>&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&lt;ROOT&gt;&lt;HEADER&gt;&lt;RESPONSE&gt;&lt;DESC&gt;&#x9274;&#x6743;&#x4E0D;&#x901A;&#x8FC7;!&lt;/DESC&gt;&lt;CODE&gt;1003&lt;/CODE&gt;&lt;/RESPONSE&gt;&lt;TRANSACTION_ID&gt;EOM20161216160001087673&lt;/TRANSACTION_ID&gt;&lt;/HEADER&gt;&lt;RESULT /&gt;&lt;/ROOT&gt;</commonServiceReturn></ns1:commonServiceResponse></soapenv:Body></soapenv:Envelope>";
            result = result.replace("&lt;", '<' + "");
            result = result.replace("&gt;", '>' + "");
            result = result.replace("&quot;", "'");

            //String A = "</commonServiceReturn></ns1:commonServiceResponse></soapenv:Body></soapenv:Envelope>";
            //String B = "<?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><soapenv:Body><ns1:commonServiceResponse soapenv:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/' xmlns:ns1='http://impl.services.inf.ztesoft.com'><commonServiceReturn>";
            result = result.substring(390, result.length() - 84);
            // String result =
            // "<?xml version='1.0' encoding='UTF-8'?><ROOT><HEADER><RESPONSE><DESC>&#x6D41;&#x7A0B;&#x5904;&#x7406;&#x6210;&#x529F;</DESC><CODE>0000</CODE></RESPONSE><TRANSACTION_ID>EOM20161219153144035211</TRANSACTION_ID></HEADER> <RESULT><CUST_ADDR>cIKZ9pg1amE=</CUST_ADDR><CUST_NAME>++w+3SAb4Cyq+8vM3So7Vlo7Y=</CUST_NAME><GROUP_ID>1120324</GROUP_ID><CONTACT_NAME>Xi9pvpCxY9JgARiis0+VtM8myakmnSOo</CONTACT_NAME><ORG_NAME>&#x9526;&#x5C4F;&#x4E8C;&#x7EA7;&#x5B9E;&#x4F53;&#x7247;&#x533A;&#xFF08;&#x81EA;&#x6709;&#xFF09;</ORG_NAME><CUST_VALUE>C4</CUST_VALUE><CUST_CODE>2802960501</CUST_CODE><CONTACT_PHONE /><BEL_CITY>&#x7EF5;&#x9633;</BEL_CITY></RESULT></ROOT>";
            return xmlElements(result);
        } else {
        	String result ="<?xml version='1.0' encoding='UTF-8'?><ROOT><HEADER><RESPONSE><DESC>&#x6D41;&#x7A0B;&#x5904;&#x7406;&#x6210;&#x529F;</DESC><CODE>0000</CODE></RESPONSE><TRANSACTION_ID>EOM20161219153144035211</TRANSACTION_ID></HEADER> <RESULT><CUST_ADDR>cIKZ9pg1amE=</CUST_ADDR><CUST_NAME>++w+3SAb4Cyq+8vM3So7Vlo7Y=</CUST_NAME><GROUP_ID>1120324</GROUP_ID><CONTACT_NAME>Xi9pvpCxY9JgARiis0+VtM8myakmnSOo</CONTACT_NAME><ORG_NAME>&#x9526;&#x5C4F;&#x4E8C;&#x7EA7;&#x5B9E;&#x4F53;&#x7247;&#x533A;&#xFF08;&#x81EA;&#x6709;&#xFF09;</ORG_NAME><CUST_VALUE>C4</CUST_VALUE><CUST_CODE>2800001234</CUST_CODE><CONTACT_PHONE /><BEL_CITY>&#x7EF5;&#x9633;</BEL_CITY></RESULT></ROOT>";
            //return null;
			return xmlElements(result);
        }

    }


    public List<GroupCustomer> getCustInfoQueryTwo(String CUST_CODE, String CUST_NAME, String USER_NAME)
            throws Exception {
        if (isES) {
            String TRANSACTION_ID = getTRANSACTIONID();
            String XmlStr = xmlheader
                    + getXmlTwo(TRANSACTION_ID, CUST_CODE, CUST_NAME, USER_NAME) + xmlend;
            TuxedoClient client = new TuxedoClient();
            //logger.info("请求地址===>" + customerSoapWsdl);
            logger.info("集团查询请求入参===>" + XmlStr);
            String result = client.tuxedoSoapClient(XmlStr, customerSoapWsdl);
            //logger.info("集团查询请求出参===>" + result);
            // String result =
            // "<?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><soapenv:Body><ns1:commonServiceResponse soapenv:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/' xmlns:ns1='http://impl.services.inf.ztesoft.com'><commonServiceReturn>&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&lt;ROOT&gt;&lt;HEADER&gt;&lt;RESPONSE&gt;&lt;DESC&gt;&#x9274;&#x6743;&#x4E0D;&#x901A;&#x8FC7;!&lt;/DESC&gt;&lt;CODE&gt;1003&lt;/CODE&gt;&lt;/RESPONSE&gt;&lt;TRANSACTION_ID&gt;EOM20161216160001087673&lt;/TRANSACTION_ID&gt;&lt;/HEADER&gt;&lt;RESULT /&gt;&lt;/ROOT&gt;</commonServiceReturn></ns1:commonServiceResponse></soapenv:Body></soapenv:Envelope>";
            result = result.replace("&lt;", '<' + "");
            result = result.replace("&gt;", '>' + "");
            result = result.replace("&quot;", "'");

            //String A = "</commonServiceReturn></ns1:commonServiceResponse></soapenv:Body></soapenv:Envelope>";
            //String B = "<?xml version='1.0' encoding='utf-8'?><soapenv:Envelope xmlns:soapenv='http://schemas.xmlsoap.org/soap/envelope/' xmlns:xsd='http://www.w3.org/2001/XMLSchema' xmlns:xsi='http://www.w3.org/2001/XMLSchema-instance'><soapenv:Body><ns1:commonServiceResponse soapenv:encodingStyle='http://schemas.xmlsoap.org/soap/encoding/' xmlns:ns1='http://impl.services.inf.ztesoft.com'><commonServiceReturn>";
            result = result.substring(390, result.length() - 84);
            // String result =
            // "<?xml version='1.0' encoding='UTF-8'?><ROOT><HEADER><RESPONSE><DESC>&#x6D41;&#x7A0B;&#x5904;&#x7406;&#x6210;&#x529F;</DESC><CODE>0000</CODE></RESPONSE><TRANSACTION_ID>EOM20161219153144035211</TRANSACTION_ID></HEADER> <RESULT><CUST_ADDR>cIKZ9pg1amE=</CUST_ADDR><CUST_NAME>++w+3SAb4Cyq+8vM3So7Vlo7Y=</CUST_NAME><GROUP_ID>1120324</GROUP_ID><CONTACT_NAME>Xi9pvpCxY9JgARiis0+VtM8myakmnSOo</CONTACT_NAME><ORG_NAME>&#x9526;&#x5C4F;&#x4E8C;&#x7EA7;&#x5B9E;&#x4F53;&#x7247;&#x533A;&#xFF08;&#x81EA;&#x6709;&#xFF09;</ORG_NAME><CUST_VALUE>C4</CUST_VALUE><CUST_CODE>2802960501</CUST_CODE><CONTACT_PHONE /><BEL_CITY>&#x7EF5;&#x9633;</BEL_CITY></RESULT></ROOT>";
            return xmlElementsTwo(result);
        } else {
            String result = "<?xml version='1.0' encoding='UTF-8'?><ROOT><HEADER><RESPONSE><CODE>0000</CODE><DESC>流程处理成功</DESC></RESPONSE><TRANSACTION_ID>12515645</TRANSACTION_ID></HEADER><RESULT><CUST_INFO><CONTACT_NAME>6labVAIokAQ=</CONTACT_NAME><ORG_NAME/><BEL_CITY>成都</BEL_CITY><LONGITUDE>104.3834188137797</LONGITUDE><CONTACT_PHONE>13581869980</CONTACT_PHONE><USER_NAME>are0w0001</USER_NAME><GROUP_ID>1409424</GROUP_ID><CUST_VALUE>C2</CUST_VALUE><MOBILE_PHONE>13880268520</MOBILE_PHONE><CHINESE_NAME>袁雅玲</CHINESE_NAME><CUST_CODE>2803746362</CUST_CODE><LATITUDE>30.856152557574</LATITUDE><CUST_NAME>四川省新津县梦琪鞋店</CUST_NAME><CUST_ADDR>G4n9L+fr2nzUh4MVUUl/g85cfp6BrJUiNgwFpiVx4Zy2eviTFcVSl4/DxYMWR1HM</CUST_ADDR></CUST_INFO><CUST_INFO><CONTACT_NAME>6labVAIokAQ=</CONTACT_NAME><ORG_NAME/><BEL_CITY>成都</BEL_CITY><LONGITUDE>104.3834188137797</LONGITUDE><CONTACT_PHONE>13581869980</CONTACT_PHONE><USER_NAME>are0w0001</USER_NAME><GROUP_ID>1409424</GROUP_ID><CUST_VALUE>C2</CUST_VALUE><MOBILE_PHONE>13880268520</MOBILE_PHONE><CHINESE_NAME>袁雅玲</CHINESE_NAME><CUST_CODE>2801555645</CUST_CODE><LATITUDE>30.856152557574</LATITUDE><CUST_NAME>四川省新津县梦琪鞋店</CUST_NAME><CUST_ADDR>G4n9L+fr2nzUh4MVUUl/g85cfp6BrJUiNgwFpiVx4Zy2eviTFcVSl4/DxYMWR1HM</CUST_ADDR></CUST_INFO></RESULT></ROOT>";
            return xmlElementsTwo(result);
        }

    }

    /**
     * 生成入参XML
     *
     * @param TRANSACTION_ID 请求流水
     * @param CUST_CODE      集团编码
     * @param CUST_NAME      集团名称
     * @return
     */
    private String getXml(String TRANSACTION_ID, String CUST_CODE,
                          String CUST_NAME) {
        // StringBuilder sb = new StringBuilder(
        // "<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        StringBuilder sb = new StringBuilder("");
        sb.append("<ROOT>");
        sb.append("<HEADER>");
        sb.append("<TRANSACTION_ID>" + TRANSACTION_ID + "</TRANSACTION_ID>");
        sb.append("<SERVICE_CODE>custInfoQuery</SERVICE_CODE>");
        sb.append("<CLIENT_NAME>EOM</CLIENT_NAME>");
        sb.append("<PASSWORD>123456</PASSWORD>");
        sb.append("</HEADER>");
        sb.append("<BODY>");
        sb.append("<CUST_CODE>" + CUST_CODE + "</CUST_CODE>");
        sb.append("<CUST_NAME>" + CUST_NAME + "</CUST_NAME>");
        sb.append("</BODY>");
        sb.append("</ROOT>");

        return sb.toString();
    }

    /**
     * 生成入参XML
     *
     * @param TRANSACTION_ID 请求流水
     * @param CUST_CODE      集团编码
     * @param CUST_NAME      集团名称
     * @param CUST_NAME      用户名称
     * @return
     */
    private String getXmlTwo(String TRANSACTION_ID, String CUST_CODE,
                             String CUST_NAME, String USER_NAME) {
        // StringBuilder sb = new StringBuilder(
        // "<?xml version=\"1.0\" encoding=\"utf-8\"?>");
        StringBuilder sb = new StringBuilder("");
        sb.append("<ROOT>");
        sb.append("<HEADER>");
        sb.append("<TRANSACTION_ID>" + TRANSACTION_ID + "</TRANSACTION_ID>");
        sb.append("<SERVICE_CODE>custInfoQueryNew</SERVICE_CODE>");
        sb.append("<CLIENT_NAME>EOM</CLIENT_NAME>");
        sb.append("<PASSWORD>123456</PASSWORD>");
        sb.append("</HEADER>");
        sb.append("<BODY>");
        sb.append("<CUST_CODE>" + CUST_CODE + "</CUST_CODE>");
        sb.append("<CUST_NAME>" + CUST_NAME + "</CUST_NAME>");
        sb.append("<USER_NAME>" + USER_NAME + "</USER_NAME>");
        sb.append("</BODY>");
        sb.append("</ROOT>");
        return sb.toString();
    }

    /**
     * 根据集团编码查询子集团
     *
     * @param CUST_CODE
     * @return
     * @throws DocumentException
     */
    public String queryRelaCust(String CUST_CODE) {
        String callXml = WsdlUtil.getInstance().getXMLfor138(getRelaCust(getTRANSACTIONID(), CUST_CODE));
        String res = WsdlUtil.getInstance().requestWsdl(callXml, customerSoapWsdl);

        return res;

    }

    private String getRelaCust(String TRANSACTION_ID, String CUST_CODE) {
        StringBuilder sb = new StringBuilder();
        sb.append("<ROOT>");
        sb.append("<HEADER>");
        sb.append("<TRANSACTION_ID>" + TRANSACTION_ID + "</TRANSACTION_ID>");
        sb.append("<SERVICE_CODE>queryRelaCust</SERVICE_CODE>");
        sb.append("<CLIENT_NAME>SCCRM0</CLIENT_NAME>");
        sb.append("<PASSWORD>123456</PASSWORD>");
        sb.append("</HEADER>");
        sb.append("<BODY>");
        sb.append("<CUST_CODE>" + CUST_CODE + "</CUST_CODE>");
        sb.append("</BODY>");
        sb.append("</ROOT>");
        return sb.toString();
    }

    public GroupCustomer xmlElements(String xmlDoc) {
        GroupCustomer customer = new GroupCustomer();
        Document doc = null;
        try {
            // 读取并解析XML文档
            // SAXReader就是一个管道，用一个流的方式，把xml文件读出来
            // SAXReader reader = new SAXReader(); //User.hbm.xml表示你要解析的xml文档
            // Document document = reader.read(new File("User.hbm.xml"));
            // 下面的是通过解析xml字符串的

            String code = "";

            doc = DocumentHelper.parseText(xmlDoc); // 将字符串转为XML
            Element rootElt = doc.getRootElement(); // 获取根节点
            Iterator head = rootElt.elementIterator("HEADER"); // /获取根节点下的子节点header
            // 遍历header节点
            while (head.hasNext()) {
                Element recordEless = (Element) head.next();
                Iterator RESPONSE = recordEless.elementIterator("RESPONSE"); // /获取HEADER节点下的子节点RESPONSE
                while (RESPONSE.hasNext()) {
                    Element RESPONSEEless = (Element) RESPONSE.next();
                    code = RESPONSEEless.elementTextTrim("CODE"); // /获取HEADER节点下的子节点RESPONSE
                }
            }
            if (code.equals("0000")) {
                Iterator result = rootElt.elementIterator("RESULT"); // /获取根节点下的子节点RESULT
                while (result.hasNext()) {
                    Element recordEless = (Element) result.next();
                    customer.setGroupCoding(recordEless
                            .elementTextTrim("CUST_CODE"));
                    customer.setGroupName(recordEless
                            .elementTextTrim("CUST_NAME"));
                    customer.setGroupLevel(recordEless
                            .elementTextTrim("CUST_VALUE"));
                    customer.setHomeRegion(recordEless
                            .elementTextTrim("ORG_NAME"));
                    customer.setCity(recordEless.elementTextTrim("BEL_CITY"));
                    customer.setContactAddress(decryptFrom(recordEless
                            .elementTextTrim("CUST_ADDR")));
                    customer.setContacts(decryptFrom(recordEless
                            .elementTextTrim("CONTACT_NAME")));
                    customer.setContactPhone(recordEless
                            .elementTextTrim("CONTACT_PHONE"));
                    customer.setUser_name(recordEless
                            .elementTextTrim("USER_NAME"));
                    customer.setChinese_name(recordEless
                            .elementTextTrim("CHINESE_NAME"));
                    customer.setMobile_phone(recordEless
                            .elementTextTrim("MOBILE_PHONE"));

                    customer.setLongitude(recordEless.elementTextTrim("LONGITUDE"));
                    customer.setLatitude(recordEless.elementTextTrim("LATITUDE"));

                }
            }

        } catch (DocumentException e) {
            e.printStackTrace();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return customer;
    }


    public List<GroupCustomer> xmlElementsTwo(String xmlDoc) {
        List<GroupCustomer> lists = new ArrayList<>();
        Document doc = null;
        try {
            // 读取并解析XML文档
            // SAXReader就是一个管道，用一个流的方式，把xml文件读出来
            // SAXReader reader = new SAXReader(); //User.hbm.xml表示你要解析的xml文档
            // Document document = reader.read(new File("User.hbm.xml"));
            // 下面的是通过解析xml字符串的

            String code = "";

            doc = DocumentHelper.parseText(xmlDoc); // 将字符串转为XML
            Element rootElt = doc.getRootElement(); // 获取根节点
            Iterator head = rootElt.elementIterator("HEADER"); // /获取根节点下的子节点header
            // 遍历header节点
            while (head.hasNext()) {
                Element recordEless = (Element) head.next();
                Iterator RESPONSE = recordEless.elementIterator("RESPONSE"); // /获取HEADER节点下的子节点RESPONSE
                while (RESPONSE.hasNext()) {
                    Element RESPONSEEless = (Element) RESPONSE.next();
                    code = RESPONSEEless.elementTextTrim("CODE"); // /获取HEADER节点下的子节点RESPONSE
                }
            }
            if (code.equals("0000")) {
                Iterator result = rootElt.elementIterator("RESULT"); // /获取根节点下的子节点RESULT
                while (result.hasNext()) {
                    Element resultEless = (Element) result.next();
                    Iterator cust_info = resultEless.elementIterator("CUST_INFO");
                    while (cust_info.hasNext()) {
						GroupCustomer customer = new GroupCustomer();
                        Element recordEless = (Element) cust_info.next();
                        //System.out.println("节点" + recordEless);
                        //code = RESPONSEEless.elementTextTrim("CODE"); // /获取HEADER节点下的子节点RESPONSE
//						customer.setGroupId(recordEless
//								.elementTextTrim("GROUP_ID"));
                        customer.setGroupCoding(recordEless
                                .elementTextTrim("CUST_CODE"));
                        customer.setGroupName(recordEless
                                .elementTextTrim("CUST_NAME"));
                        customer.setGroupLevel(recordEless
                                .elementTextTrim("CUST_VALUE"));
                        customer.setHomeRegion(recordEless
                                .elementTextTrim("ORG_NAME"));
                        customer.setCity(recordEless.elementTextTrim("BEL_CITY"));
                        customer.setContactAddress(decryptFrom(recordEless
                                .elementTextTrim("CUST_ADDR")));
                        customer.setContacts(decryptFrom(recordEless
                                .elementTextTrim("CONTACT_NAME")));
                        customer.setContactPhone(recordEless
                                .elementTextTrim("CONTACT_PHONE"));
                        customer.setUser_name(recordEless
                                .elementTextTrim("USER_NAME"));
                        customer.setChinese_name(recordEless
                                .elementTextTrim("CHINESE_NAME"));
                        customer.setMobile_phone(recordEless
                                .elementTextTrim("MOBILE_PHONE"));
                        customer.setLongitude(recordEless.elementTextTrim("LONGITUDE"));
                        customer.setLatitude(recordEless.elementTextTrim("LATITUDE"));
                        lists.add(customer);
                    }
                }
            }

        } catch (DocumentException e) {
            e.printStackTrace();
            return null;
        } catch (Exception e) {
            e.printStackTrace();
            return null;
        }
        return lists;
    }

    /**
     * 解密字符串
     *
     * @param str
     * @return
     */
    private String decryptFrom(String str) {
        if (null != str && !str.equals("")) {
            String result = str.replace(" ", "");
            String keys = "l0m8qnucCOw67IaJl0m8qnuc"; // 定值不变
            // String pass = Des3Util.encryptToBase64(keys, str, "UTF-8"); //
            // 加密方法
            // System.out.println("加密后的字符:"+pass);
            String noPass = Des3Util.decryptFromBase64(keys, result, "UTF-8");// 解密方法
            return noPass;
        } else
            return "";

    }

    /*
     * 生成流水号，项目名+流水号+6-10位时间戳
     */
    private String getTRANSACTIONID() {
        String timeStamp = new SimpleDateFormat("yyyyMMddHHmmss")
                .format(new Date());
        String randomStr = "";
        for (int i = 0; i < 6; i++) {
            int x = (int) (Math.random() * 10);
            randomStr = randomStr + x;
        }
        return "EOM" + timeStamp + randomStr;
    }

    public static void main(String[] args) throws Exception {
        // String xml="<?xml version='1.0' encoding='utf-8'?>"+
        // "<ROOT><HEADER>"+
        // "<RESPONSE>"+
        // "<DESC>&#x6D41;&#x7A0B;&#x5904;&#x7406;&#x6210;&#x529F;</DESC>"+
        // "<CODE>0000</CODE>"+
        // "</RESPONSE>"+
        // "<TRANSACTION_ID>EOM20170118170255752876</TRANSACTION_ID>"+
        // "</HEADER>"+
        // "<RESULT>"+
        // "<CUST_ADDR>XtBWF0SMkwHMjdcTBw8TsJYysyCUmQxlaMuBetIj2Z4QzK2jZJmxrM7YxB/ZgZEC7gK3b6WH37dp"+
        // "h4OKsNHDOg==</CUST_ADDR>"+
        // "<CUST_NAME>&#x519C;&#x4E1A;&#x94F6;&#x884C;&#x5DF4;&#x4E2D;&#x5E02;&#x5206;&#x884C;</CUST_NAME>"+
        // "<GROUP_ID>1286888</GROUP_ID>"+
        // "<CONTACT_NAME>Y8ti7b0A5hVLnHvSBSfDIA==</CONTACT_NAME>"+
        // "<ORG_NAME />"+
        // "<CUST_VALUE>A+</CUST_VALUE>"+
        // "<CUST_CODE>2800012251</CUST_CODE>"+
        // "<CONTACT_PHONE />"+
        // "<BEL_CITY>&#x5DF4;&#x4E2D;</BEL_CITY>"+
        // "</RESULT>"+
        // "</ROOT>";
        // xmlElements(xml);
        // 读取原始json文件并进行操作和输出

    }
}
