<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	   xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jaxws="http://cxf.apache.org/jaxws"
	   xsi:schemaLocation="
		    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		    http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
		    http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd
		    http://cxf.apache.org/jaxws  http://cxf.apache.org/schemas/jaxws.xsd"
	   default-autowire="byName">

	<import resource="classpath:META-INF/cxf/cxf.xml" />
	<import resource="classpath:META-INF/cxf/cxf-extension-soap.xml" />
	<import resource="classpath:META-INF/cxf/cxf-servlet.xml" />

	<!-- 集团客户快速建档 -->
	<bean id="UnitInfoService" class="com.xinxinsoft.service.ums.UnitInfoService"></bean>
	<bean id="springContextUtil" class="com.xinxinsoft.utils.SpringContextUtil" />
	<!-- Service -->
	<!-- 后台系统用户服务 -->
	<bean id="LoginUserService" class="com.xinxinsoft.service.core.user.LoginUserService">
		<property name="passwordEncoder">
			<ref bean="passwordEncoder"/>
		</property>
	</bean>
	<!-- 后台系统组织服务 -->
	<bean id="SystemOrganizationService"
		  class="com.xinxinsoft.service.core.org.SystemOrganizationService" />
	<!-- 后台系统菜单服务 -->
	<bean id="MenuItemService" class="com.xinxinsoft.service.core.menuitem.MenuItemService">
		<property name="loginUserService">
			<ref bean="LoginUserService"></ref>
		</property>
		<property name="roleService">
			<ref bean="RoleService"></ref>
		</property>
	</bean>
	<!-- 后台系统角色服务 -->
	<bean id="RoleService" class="com.xinxinsoft.service.core.role.RoleService">
		<property name="menuItemService">
			<ref bean="MenuItemService"></ref>
		</property>
	</bean>
	<bean id="ContractUtils" class="com.xinxinsoft.utils.ContractUtils">
		<property name="attachmentService">
			<ref bean="AttachmentService" />
		</property>

		<property name="contractService">
			<ref bean="ContractService" />
		</property>
	</bean>
	<!-- 客户信息服务 -->
	<bean id="customerService" class="com.xinxinsoft.service.customer.customerService">
	</bean>
	<!-- 集团客户信息服务 -->
	<bean id="GroupCustomerService"
		  class="com.xinxinsoft.service.groupcustomer.GroupCustomerService">
	</bean>
	<!--集团关键人信息服务 -->
	<bean id="GroupPeopleService"
		  class="com.xinxinsoft.service.customeraccount.GroupPeopleService">
	</bean>
	<!-- 客户账户服务 -->
	<bean id="CustomerAccountService"
		  class="com.xinxinsoft.service.customeraccount.CustomerAccountService">
	</bean>
	<!--环节评分信息 -->
	<bean id="LinkScoreService" class="com.xinxinsoft.service.linkScore.LinkScoreService">
	</bean>
	<!-- 附件类型服务 -->
	<!-- <bean id="EnclosureTypeService" class="com.xinxinsoft.service.enclosure.EnclosureTypeService">
		</bean> -->
	<bean id="AttachmentTypeService" class="com.xinxinsoft.service.enclosure.AttachmentTypeService">
	</bean>
	<!-- 附件服务 -->
	<!-- <bean id="EnclosureService" class="com.xinxinsoft.service.enclosure.EnclosureService">
		</bean> -->
	<bean id="AttachmentService" class="com.xinxinsoft.service.enclosure.AttachmentService">
	</bean>
	<!-- 类型-值 服务 -->
	<bean id="TypeValueService" class="com.xinxinsoft.service.core.typeValue.TypeValueService" />
	<!-- 密码加密服务 -->
	<bean id="passwordEncoder" class="org.acegisecurity.providers.encoding.Md5PasswordEncoder"></bean>
	<!-- 定时清楚验证码 -->
	<bean id="ClearCode" class="com.xinxinsoft.task.ClearCode">
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!-- App用户登录服务 -->
	<bean id="AppLoginService" class="com.xinxinsoft.service.core.app.AppLoginService"></bean>
	<!-- App用户信息服务 -->
	<bean id="AppUserInfoService" class="com.xinxinsoft.service.core.app.AppUserInfoService"></bean>
	<!-- 订单发起 -->
	<bean id="OrderTaskService" class="com.xinxinsoft.service.ordertask.OrderTaskService"></bean>
	<!-- App用户WebService服务端 -->
	<bean id="AppLoginWebService" class="com.xinxinsoft.service.webService.AppLoginWebService">
		<constructor-arg name="appLoginService">
			<ref bean="AppLoginService" />
		</constructor-arg>
		<constructor-arg name="appUserInfoService">
			<ref bean="AppUserInfoService" />
		</constructor-arg>
	</bean>
	<!-- App用户WebService服务端 -->
	<jaxws:server id="webServiceLogin"
				  serviceClass="com.xinxinsoft.service.webService.AppLoginWebService"
				  address="/AppLoginWebService">
		<jaxws:serviceBean>
			<ref bean="AppLoginWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<!-- 4A用户变更WebService服务端 -->
	<bean id="AccountChange4AWebService"
		  class="com.xinxinsoft.service.webService.AccountChange4AWebService">
		<constructor-arg name="user4AService">
			<ref bean="User4AService" />
		</constructor-arg>
	</bean>
	<!-- 4A用户变更WebService服务端 -->
	<jaxws:server id="webService4AChange"
				  serviceClass="com.xinxinsoft.service.webService.AccountChange4AWebService"
				  address="/AccountChange4AWebService">
		<jaxws:serviceBean>
			<ref bean="AccountChange4AWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<!-- 合同webservice -->
	<bean id="ContractWebService"
		  class="com.xinxinsoft.service.webService.WebService.ContractWebService">
		<constructor-arg name="contractService">
			<ref bean="ContractService" />
		</constructor-arg>
	</bean>
	<jaxws:server id="webServiceContract"
				  serviceClass="com.xinxinsoft.service.webService.WebService.ContractWebService"
				  address="/ContractWebService">
		<jaxws:serviceBean>
			<ref bean="ContractWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<!-- 订单任务webservice -->
	<bean id="CreateOrderTaskWebService"
		  class="com.xinxinsoft.service.webService.WebService.CreateOrderTaskWebService">
		<constructor-arg name="contractService">
			<ref bean="ContractService" />
		</constructor-arg>
		<constructor-arg name="taskService">
			<ref bean="WaitTaskService" />
		</constructor-arg>
		<constructor-arg name="systemUserService">
			<ref bean="SystemUserService" />
		</constructor-arg>
		<constructor-arg name="orderTaskService">
			<ref bean="OrderTaskService" />
		</constructor-arg>
	</bean>
	<jaxws:server id="webServiceCreateOrderTask"
				  serviceClass="com.xinxinsoft.service.webService.WebService.CreateOrderTaskWebService"
				  address="/CreateOrderTaskWebService">
		<jaxws:serviceBean>
			<ref bean="CreateOrderTaskWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<!--addService -->
	<!-- 用户登录 -->
	<bean id="SystemUserService" class="com.xinxinsoft.service.core.user.SystemUserService">
		<property name="passwordEncoder">
			<ref bean="passwordEncoder" />
		</property>
		<property name="loginUserService">
			<ref bean="LoginUserService" />
		</property>
	</bean>
	<!-- 消息通知SERVICE -->
	<bean id="messagesInfoService" class="com.xinxinsoft.service.messages.MessagesInfoService"></bean>
	<!-- 部门Service -->
	<bean id="systemDeptService" class="com.xinxinsoft.service.core.dept.SystemDeptService"></bean>
	<!-- 业务类型 -->
	<bean id="BusinessTypeService" class="com.xinxinsoft.service.basetype.BusinessTypeService"></bean>
	<!-- 产品类型 -->
	<bean id="ProductTypeService" class="com.xinxinsoft.service.basetype.ProductTypeService"></bean>
	<!-- 订单详细填报(根据产品类型填报内容) 2017-05-11 11:23:55 -->
	<bean id="orderDetailService" class="com.xinxinsoft.service.order.OrderDetailService"></bean>
	<!-- 通用单管理 SERVICE -->
	<bean id="commonSingleService"
		  class="com.xinxinsoft.service.commonSingManagement.CommonSingleService">
		<property name="dicService">
			<ref bean="DictionaryService" />
		</property>
		<property name="service">
			<ref bean="WaitTaskService" />
		</property>
		<property name="attservcie">
			<ref bean="AttachmentService" />
		</property>
	</bean>
	<!--人员选择树 -->
	<bean id="ZtreeUserService" class="com.xinxinsoft.service.core.user.ZtreeUserService"></bean>
	<!-- 知识库（案例） -->
	<bean id="KnowledgeService" class="com.xinxinsoft.service.knowledge.KnowledgeService" />
	<!-- 待办任务 -->
	<bean id="WaitTaskService" class="com.xinxinsoft.service.waitTask.WaitTaskService">
		<property name="pushService">
			<ref bean="smsPushService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
	</bean>
	<!-- 定时清理缓存文件 -->
	<bean id="DeleteFile" class="com.xinxinsoft.task.DeleteFile" >
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!-- 字典 -->
	<bean id="DictionaryService" class="com.xinxinsoft.service.core.DictionaryService" />
	<!-- 产品流程 -->
	<bean id="ProductFlowService" class="com.xinxinsoft.service.processLink.ProductFlowService" />
	<!-- 需求申请 -->
	<bean id="DedicatedFlowService"
		  class="com.xinxinsoft.service.dedicatedFlow.DedicatedFlowService" />
	<!-- 需求申请 -->
	<bean id="ExpenseApplyService"
		  class="com.xinxinsoft.service.dedicatedFlow.ExpenseApplyService" />
	<!-- 环节模板 -->
	<bean id="LinkTemplateService" class="com.xinxinsoft.service.processLink.LinkTemplateService" />
	<!-- 菜单管理 -->
	<bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"></bean>
	<!-- 短信 -->
	<bean id="smsPushService" class="com.xinxinsoft.service.smsPush.SmsPushService"></bean>
	<!--BOSS预受理业务发起 -->
	<bean id="startPreOrderOutService" class="com.xinxinsoft.service.boss.StartPreOrderOutService"></bean>
	<!--BOSS查询预受理状态 -->
	<bean id="qryPreDealOutService" class="com.xinxinsoft.service.boss.QryPreDealOutService"></bean>
	<!--订单统计SERVICE -->
	<!-- ims -->
	<bean id="imsService" class="com.xinxinsoft.service.dedicatedFlow.ims.ImsService"></bean>
	<bean id="statisticsService" class="com.xinxinsoft.service.statistics.StatisticsService"></bean>
	<!--BOSS -->
	<bean id="CMCCOpenService" class="com.xinxinsoft.sendComms.CMCCOpenService"></bean>
	<!-- 1000 -->
	<bean id="CMCC1000OpenService" class="com.xinxinsoft.sendComms.CMCC1000OpenService"></bean>
	<!-- ims -->
	<bean id="itoService" class="com.xinxinsoft.service.dedicatedFlow.ito.ItoService"></bean>
	<!-- processService -->
	<bean id="processService"
		  class="com.xinxinsoft.service.core.processService.ProcessService"></bean>
	<!--集团接口 -->
	<bean id="customerServices" class="com.xinxinsoft.sendComms.GroupCustomerService"></bean>
	<!--app统计登陆次数 -->
	<bean id="appLoginService" class="com.xinxinsoft.service.appLoginService.appLoginService"></bean>
	<!--超时订单top3 -->
	<bean id="OverTimeService" class="com.xinxinsoft.service.overTime.OverTimeService"></bean>
	<!-- 统计分享 -->
	<bean id="CountShareService" class="com.xinxinsoft.service.countShare.CountShareService"></bean>
	<!-- 删除jbpm有关表数据 -->
	<bean id="jbpmProcessService" class="com.xinxinsoft.service.jbpmProcess.JbpmProcessService"></bean>
	<!-- 4A主从帐号关系视图 -->
	<bean id="User4AService" class="com.xinxinsoft.service.core.user.User4AService"></bean>
	<!-- boss 环节 -->
	<bean id="BossTacheService" class="com.xinxinsoft.service.basetype.BossTacheService"></bean>
	<!-- EIP用户部门关系 -->
	<bean id="OwnDepartmentsService" class="com.xinxinsoft.service.core.OwnDepartmentsService"></bean>
	<bean id="sngListExcelService"
		  class="com.xinxinsoft.service.commonSingManagement.SingListExcelService"></bean>
	<!--4A开关 -->
	<bean id="Open4AStateService" class="com.xinxinsoft.service.core.app.Open4AStateService"></bean>
	<!-- APP版本上传 -->
	<bean id="appUploadService" class="com.xinxinsoft.service.core.app.AppUploadService"></bean>
	<!-- App版本管理 -->
	<bean id="EditionService" class="com.xinxinsoft.service.core.app.EditionService"></bean>
	<bean id="IbbnService" class="com.xinxinsoft.service.IBossService.IBossByNoService"></bean>
	<!-- 节假日管理 -->
	<bean id="holidayService" class="com.xinxinsoft.service.holiday.HolidayService"></bean>
	<!-- 专业订单合同 -->
	<bean id="ContractService" class="com.xinxinsoft.service.contract.ContractService"></bean>
	<!-- 新增的业务办理 -->
	<bean id="BusinessEntityService"
		  class="com.xinxinsoft.service.businessProc.BusinessEntityService"></bean>
	<!-- 勘测结果 -->
	<bean id="ReconnaissanceResultService"
		  class="com.xinxinsoft.service.reconnaissanceResult.ReconnaissanceResultService"></bean>
	<!-- 稽查 -->
	<bean id="CheckService" class="com.xinxinsoft.service.checkservice.CheckService" />
	<!-- 存送单service -->
	<bean id="SaveSendService" class="com.xinxinsoft.service.SaveSendService.SaveSendService" />
	<!-- 补收service -->
	<bean id="SupCollectService"
		  class="com.xinxinsoft.service.SaveSendService.SupCollectService" />
	<!-- 终端service -->
	<bean id="TerminalService" class="com.xinxinsoft.service.SaveSendService.TerminalService" />
	<!-- 转账service -->
	<bean id="TransferAccountsService"
		  class="com.xinxinsoft.service.SaveSendService.TransferAccountsService" />
	<!-- 工单service -->
	<bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"></bean>
	<!-- 存送稽核service -->
	<bean id="SaveSendCheckService"
		  class="com.xinxinsoft.service.SaveSendService.SaveSendCheckService"></bean>
	<!-- 存送定时器service -->
	<bean id="SaveSendCheckTimeService"
		  class="com.xinxinsoft.service.SaveSendService.SaveSendCheckTimeService"></bean>
	<!-- 补收定时器service -->
	<bean id="SuoCollectCheckTimeService"
		  class="com.xinxinsoft.service.SaveSendService.SuoCollectCheckTimeService"></bean>
	<!-- 终端定时器service -->
	<bean id="TerminalCheckTimeService"
		  class="com.xinxinsoft.service.SaveSendService.TerminalCheckTimeService"></bean>
	<!-- 转账定时器service -->
	<bean id="TransferAccountsCheckTimeService"
		  class="com.xinxinsoft.service.SaveSendService.TransferAccountsCheckTimeService"></bean>
	<bean id="BOGetService" class="com.xinxinsoft.service.webService.BOGetService">
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
	</bean>
	<!-- 绿网ftp管理 -->
	<bean id="operFTPservice" class="com.xinxinsoft.service.ftpss.OperFTPservice"></bean>
	<!-- 原因分类 -->
	<bean id="QuestionService" class="com.xinxinsoft.service.checkservice.QuestionService" />
	<!-- 投诉建议 -->
	<bean id="RecommendedManagementService"
		  class="com.xinxinsoft.service.RecommendedManagement.RecommendedManagementService" />
	<!-- 合同接口服务 -->
	<bean id="ContractHttpService" class="com.xinxinsoft.service.httpService.ContractHttpService" />
	<bean id="Logs4AWebServices" class="com.xinxinsoft.service.webService.Logs4AWebServices">
		<property name="ibbnService">
			<ref bean="IbbnService" />
		</property>
	</bean>
	<jaxws:server id="WatermarkContractWebservice"
				  serviceClass="com.xinxinsoft.service.webService.WatermarkContract.WatermarkContractWebservice"
				  address="/WatermarkContractWebservice">
		<jaxws:serviceBean>
			<ref bean="WatermarkContractWebservice" />
		</jaxws:serviceBean>
	</jaxws:server>
	<bean id="WatermarkContractWebservice" class="com.xinxinsoft.service.webService.WatermarkContract.WatermarkContractWebservice"></bean>
	<!-- 尊享码活动，渠道信息service -->
	<bean id="ChannelInfoService" class="com.xinxinsoft.service.honorCode.ChannelInfoService"></bean>
	<!-- 尊享码活动，尊享码活动表service -->
	<bean id="HonorCodeBusinessService"
		  class="com.xinxinsoft.service.honorCode.HonorCodeBusinessService"></bean>
	<!-- 营销活动，营销活动表service -->
	<bean id="ActivityService" class="com.xinxinsoft.service.honorCode.ActivityService"></bean>
	<!-- 通专融合 -->
	<bean id="IntegrationService" class="com.xinxinsoft.service.v2.integrationService.IntegrationService"></bean>
	<bean id="VariousSqlQueryService" class="com.xinxinsoft.service.appOpenService.VariousSqlQueryService">
		<property name="conSer">
			<ref bean="ContractService" />
		</property>
	</bean>
	<!-- 欠费管理，欠费信息 -->
	<bean id="ArrearsService" class="com.xinxinsoft.service.arrearsModule.ArrearsService"></bean>
	<!-- 催缴单信息  -->
	<bean id="PaymentRecordService" class="com.xinxinsoft.service.arrearsModule.PaymentRecordService"></bean>

	<!-- 欠费管理，协议补签 -->
	<bean id="AgreementService" class="com.xinxinsoft.service.arrearsModule.AgreementService"></bean>
	<!-- 欠费统计结果 -->
	<bean id="ArrearsStatisticalService" class="com.xinxinsoft.service.arrearsModule.ArrearsStatisticalService"></bean>
	<!-- 转账管理 -->
	<bean id="TransferInformationService" class="com.xinxinsoft.service.transfer.TransferInformationService">
		<property name="transferJBPMUtils">
			<ref bean="TransferJBPMUtils"/>
		</property>
		<property name="jbpmUtil">
			<ref bean="JBPMUtil" />
		</property>
		<property name="service">
			<ref bean="WaitTaskService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
	</bean>
	<!-- 生成合同订单关系CSV文件 -->
	<bean id="ContractToOrderCSVService" class="com.xinxinsoft.service.contractToOrderCSVService.ContractToOrderCSVService"></bean>
	<!-- 支付 -->
	<bean id="PaymentProviderService" class="com.xinxinsoft.service.pay.PaymentProviderService" ></bean>
	<!-- 业务参数,业务类型为02时使用 -->
	<bean id="BusiParameterService" class="com.xinxinsoft.service.pay.Busi_ParameterService" ></bean>
	<!-- 商品信息 -->
	<bean id="GoodsInfoService" class="com.xinxinsoft.service.pay.Goods_InfoService" ></bean>
	<bean id="OrderQueryService" class="com.xinxinsoft.service.pay.OrderQueryService"></bean>
	<bean id="OrderQuerySendService" class="com.xinxinsoft.service.pay.OrderQuerySendService"></bean>
	<!-- 退款 -->
	<bean id="RefundService" class="com.xinxinsoft.service.pay.RefundService" ></bean>
	<bean id="RefundQueryService" class="com.xinxinsoft.service.pay.RefundQueryService"></bean>
	<bean id="RefundQuerySendService" class="com.xinxinsoft.service.pay.RefundQuerySendService"></bean>
	<!-- 支付发起webservice -->
	<bean id="paymentProviderWebService" class="com.xinxinsoft.service.webService.pay.PaymentProviderWebService" >
		<constructor-arg name="paymentProviderService" ref="PaymentProviderService"></constructor-arg>
		<constructor-arg name="busiParameterService" ref="BusiParameterService"></constructor-arg>
		<constructor-arg name="goodsInfoService" ref="GoodsInfoService"></constructor-arg>
		<constructor-arg name="orderQueryService" ref="OrderQueryService"></constructor-arg>
		<constructor-arg name="orderQuerySendService" ref="OrderQuerySendService"></constructor-arg>
		<constructor-arg name="refundService" ref="RefundService"></constructor-arg>
		<constructor-arg name="refundQueryService" ref="RefundQueryService"></constructor-arg>
		<constructor-arg name="refundQuerySendService" ref="RefundQuerySendService"></constructor-arg>
		<constructor-arg name="paymentOrderService" ref="PaymentOrderService"></constructor-arg>
		<constructor-arg name="refundOrderService" ref="RefundOrderService"></constructor-arg>
		<constructor-arg name="variousSqlQueryService" ref="VariousSqlQueryService"></constructor-arg>
	</bean>
	<!-- 支付发起webservice -->
	<jaxws:server id="webServicePaymentProvider"
				  serviceClass="com.xinxinsoft.service.webService.pay.PaymentProviderWebService"
				  address="/paymentProviderWebService">
		<jaxws:serviceBean>
			<ref bean="paymentProviderWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<!--  支付通知 -->
	<bean id="PaymentOrderService" class="com.xinxinsoft.service.pay.PaymentOrderService" autowire="byType"></bean>
	<!-- 退款通知 -->
	<bean id="RefundOrderService" class="com.xinxinsoft.service.pay.RefundOrderService" autowire="byType"></bean>
	<!-- ict合同-->
	<bean id="IctContractService" class="com.xinxinsoft.service.IctContract.IctContractService" autowire="byType"></bean>
	<!-- Ict合同退回webservice -->
	<bean id="OSB_BP_SOA_HQ_ImportRetRevContDraftRouteSrvPortImpl"
		  class="com.xinxinsoft.service.OSB_BP_SOA_HQ_00025.OSB_BP_SOA_HQ_ImportRetRevContDraftRouteSrvPortImpl">
		<constructor-arg name="ictContractService">
			<ref bean="IctContractService" />
		</constructor-arg>
		<constructor-arg name="taskService">
			<ref bean="WaitTaskService" />
		</constructor-arg>
	</bean>
	<jaxws:server id="OSB_BP_SOA_HQ_ImportRetRevContDraftRouteSrvPort"
				  serviceClass="com.xinxinsoft.service.OSB_BP_SOA_HQ_00025.OSB_BP_SOA_HQ_ImportRetRevContDraftRouteSrvPortImpl"
				  address="/OSB_BP_SOA_HQ_ImportRetRevContDraftRouteSrvPort" wsdlLocation="/META-INF/ictBack/OSB_BP_SOA_HQ_ImportRetRevContDraftRouteSrv.wsdl">
		<jaxws:serviceBean>
			<ref bean="OSB_BP_SOA_HQ_ImportRetRevContDraftRouteSrvPortImpl" />
		</jaxws:serviceBean>
	</jaxws:server>
	<!-- ict合同task -->
	<bean id="IctContractTask" class="com.xinxinsoft.task.IctContractTask">
		<property name="ictContractService">
			<ref bean="IctContractService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!-- 1000 -->
	<bean id="Various1000SqlQueryService" class="com.xinxinsoft.service.appOpenService.Various1000SqlQueryService"></bean>
	<!-- 接口日志表 -->
	<bean id="InterfaceLogService" class="com.xinxinsoft.service.anySrc.InterfaceLogService"></bean>
	<!--对账单表  -->
	<bean id="ReconciliationService" class="com.xinxinsoft.service.pay.ReconciliationService"></bean>
	<!-- 稽核service -->
	<bean id="AuditWorksheetService" class="com.xinxinsoft.service.AuditWorksheetService.AuditWorksheetService"></bean>
	<!-- 结算表 -->
	<bean id="SettlementService" class="com.xinxinsoft.service.pay.SettlementService"></bean>
	<!--快速开户产品表  -->
	<bean id="AccountOrderInfoService" class="com.xinxinsoft.service.pay.AccountOrderInfoService"></bean>
	<!-- 合同认领service -->
	<bean id="ClaimAContractService" class="com.xinxinsoft.service.contract.ClaimAContractService"></bean>
	<!-- 预开票表 -->
	<bean id="PreinvApplyService" class="com.xinxinsoft.service.PreinvApply.PreinvApplyService"></bean>
	<!-- 资金认领 -->
	<bean id="claimForFundModelService" class="com.xinxinsoft.service.claimForFunds.claimForFundModelService" />
	<bean id="ClaimForFundsService" class="com.xinxinsoft.service.claimForFunds.ClaimForFundsService" />
	<!-- 地市节点金额相关的service -->
	<bean id="TransferCitiesDataService" class="com.xinxinsoft.service.transfer.TransferCitiesDataService"></bean>
	<!-- 转账管理 -->
	<bean id="TransferInformationTwoService" class="com.xinxinsoft.service.transfer.TransferInformationTwoService">
		<property name="transferJBPMUtils">
			<ref bean="TransferJBPMUtils"/>
		</property>
		<property name="service">
			<ref bean="WaitTaskService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
	</bean>
	<!-- job日志 -->
	<bean id="JobLogServicer" class="com.xinxinsoft.service.executejoblog.JobLogServicer"></bean>
	<!-- 应收未收管理 -->
	<bean id="ReceiveApplyService" class="com.xinxinsoft.service.receiveApply.ReceiveApplyService"></bean>
	<!-- 快速订单 -->
	<bean id="QuickQueryService" class="com.xinxinsoft.service.quickQueryService.QuickQueryService" />
	<!-- 稽核综合 -->
	<bean id="AuditMultipleService" class="com.xinxinsoft.service.AuditWorksheetService.AuditMultipleService"></bean>
	<bean id="AuditWorkListService" class="com.xinxinsoft.service.AuditWorksheetService.AuditWorkListService"></bean>
	<!-- 合同管理 -->
	<bean id="ContractUniformityService" class="com.xinxinsoft.service.contractUniformityService.ContractUniformityService"></bean>
	<!-- IDC 申请 -->
	<bean id="IDCApplyService" class="com.xinxinsoft.service.IDCService.IDCApplyService"></bean>
	<!-- IDC 流程-->
	<bean id="IDCFlowService" class="com.xinxinsoft.service.IDCService.IDCFlowService"></bean>
	<!-- IDC 任务 -->
	<bean id="IDCTaskService" class="com.xinxinsoft.service.IDCService.IDCTaskService"></bean>
	<!-- 业务周期 -->
	<bean id="BusinssService" class="com.xinxinsoft.service.businss.BusinssService"></bean>
	<!-- 营销活动预开票 -->
	<bean id="ActivityPreinvApplyService" class="com.xinxinsoft.service.activityPreinvService.ActivityPreinvApplyService"></bean>
	<!-- 手工开票 -->
	<bean id="ManualInvApplyService" class="com.xinxinsoft.service.manualInvApply.ManualInvApplyService"></bean>
	<!-- 白名单管理 -->
	<bean id="WhiteListService" class="com.xinxinsoft.service.whiteList.WhiteListService"></bean>
	<bean id="WhiteListInformationService" class="com.xinxinsoft.service.whiteList.WhiteListInformationService"></bean>
	<!-- 存送 任务 -->
	<bean id="BigAmountApplyTaskService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyTaskService" />
	<!-- 存送 任务 -->
	<bean id="BigAmountApplyFlowService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyFlowService" />
	<!-- 存送 任务 -->
	<bean id="BigAmountApplyService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyService" />
	<!-- 存送 活动 -->
	<bean id="BigAmountDepositSendContractService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountDepositSendContractService" />
	<!-- 大额存送boos接口-->
	<bean id="IChkPreInvoiceActAoSvc" class="com.xinxinsoft.service.webService.IChkPreInvoiceActAoSvc" />
	<!-- 正负补收 -->
	<bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"></bean>
	<!-- 行业终端 -->
	<bean id="IndustryTerminalService" class="com.xinxinsoft.service.IndustryTerminal.IndustryTerminalService"></bean>
	<!--支付操作-->
	<bean id="PaymentService" class="com.xinxinsoft.service.pay.PaymentService"></bean>
	<!--支付详细信息查询操作-->
	<bean id="QueryPaymentService" class="com.xinxinsoft.service.pay.QueryPaymentService"></bean>
	<!--支付退款操作service-->
	<bean id="PayRefundService" class="com.xinxinsoft.service.pay.PayRefundService"></bean>
	<!-- 支付发起webservice -->
	<bean id="paymentWebService" class="com.xinxinsoft.service.webService.pay.PaymentWebService" >
		<constructor-arg name="paymentService" ref="PaymentService"></constructor-arg>
		<constructor-arg name="queryPaymentService" ref="QueryPaymentService"></constructor-arg>
		<constructor-arg name="payRefundService" ref="PayRefundService"></constructor-arg>
	</bean>
	<!-- 欠费回收统计 -->
	<bean id="ArrearsSingSerivce" class="com.xinxinsoft.service.arrearsModule.ArrearsSingSerivce">
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
	</bean>
	<!-- 支付发起webservice -->
	<jaxws:server id="webServicePayment"  serviceClass="com.xinxinsoft.service.webService.pay.PaymentWebService"
				  address="/paymentWebService">
		<jaxws:serviceBean>
			<ref bean="paymentWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<!-- 无资源下单审批 -->
	<bean id="NoResApplyService" class="com.xinxinsoft.service.appOpenService.NoResApplyService">
		<property name="jbpmUtil">
			<ref bean="JBPMUtil" />
		</property>
		<property name="waitTaskService">
			<ref bean="WaitTaskService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
	</bean>
	<!-- 工单Service -->
	<bean id="InquiryOrderService" class="com.xinxinsoft.service.InquiryOrder.InquiryOrderService"/>
	<!-- 缓停 -->
	<bean id="SuspensionApplicationService" class="com.xinxinsoft.service.SuspensionApplicationService.SuspensionApplicationService"></bean>

	<bean id="OMSService" class="com.xinxinsoft.service.appOpenService.OMSService"></bean>
	<bean id="PMSService" class="com.xinxinsoft.service.appOpenService.PMSService"></bean>
	<bean id="CustomClauseContractService" class="com.xinxinsoft.service.contract.CustomClauseContractService"></bean>
	<!-- ICT -->
	<bean id="ICTApplicationService" class="com.xinxinsoft.service.ICT.ICTApplicationService"></bean>
	<bean id="ReductionICTApplicationService" class="com.xinxinsoft.service.ICT.ReductionICTApplicationService"></bean>
	<bean id="Bpms_riskoff_service" class="com.xinxinsoft.service.PublicService.Bpms_riskoff_service">
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
	</bean>
	<bean id="MonthlyinvoiceService" class="com.xinxinsoft.service.monthlyinvoice.MonthlyinvoiceService"></bean>
	<!--        //风控导出Cvs-->
	<bean id="RiskcontrolDerivedService" class="com.xinxinsoft.service.riskcontrolDerivedService.RiskcontrolDerivedService"></bean>
	<!--	//转账接口-->
	<bean id="TransferService" class="com.xinxinsoft.service.transfer.TransferService"></bean>
	<bean id="TransferInterfaceService" class="com.xinxinsoft.service.transfer.TransferInterfaceService"></bean>
	<!-- 推送商机系统记录 -->
	<bean id="PushBusiOppLogService" class="com.xinxinsoft.service.oms.PushBusiOppLogService"></bean>
	<!--一键下单回调商机系统接口  -->
	<bean id="BusiOppService" class="com.xinxinsoft.sendComms.omsService.BusiOppService"></bean>
	<!-- 开户欠费Service -->
	<bean id="accountOpenService" class="com.xinxinsoft.service.accountOpenService.AccountOpenService" scope="prototype"></bean>
	<!-- 合同电子签章 -->
	<bean id="SealService" class="com.xinxinsoft.service.sign.SealService"/>
	<bean id="SignService" class="com.xinxinsoft.service.sign.SignService">
		<property name="sealService">
			<ref bean="SealService" />
		</property>
	</bean>
	<!-- 业务强开 -->
	<bean id="fourtOpenService"  class="com.xinxinsoft.service.fourtOpenService.fourtOpenService"></bean>
	<!--销售工单（客户经理需求单）-->
	<bean id="OmsSellOrderService" class="com.xinxinsoft.service.oms.OmsSellOrderService"/>
	<!--工单受理工作台-->
	<bean id="OmsOrderWorkbenchService" class="com.xinxinsoft.service.oms.OmsOrderWorkbenchService"/>
	<!--销售工单产品受理详细-->
	<bean id="OmsOrderProductService" class="com.xinxinsoft.service.oms.OmsOrderProductService"/>
	<bean id="StructureOfPersonnelService" class="com.xinxinsoft.service.core.user.StructureOfPersonnelService"/>
	<!--服务标准化测试用例-->
	<bean id="ServiceStandardizationTestingService" class="com.xinxinsoft.service.oms.ServiceStandardizationTestingService"/>
	<bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
	<bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
	<!--DICT工单信息同步接口-->
	<bean id="DictOrderHttpService" class="com.xinxinsoft.service.httpService.DictOrderHttpService"></bean>
	<!-- 红名单Service -->
	<bean id="redRollListService" class="com.xinxinsoft.service.redListService.RedRollListService" scope="prototype"></bean>
	<!-- IDC推送接口Service -->
	<bean id="IdcService"  class="com.xinxinsoft.sendComms.IdcService"></bean>
	<!-- 面对面支付 -->
	<bean id="voucherCenterWebService" class="com.xinxinsoft.service.webService.pay.VoucherCenterWebService">
		<constructor-arg name="paymentService" ref="PaymentService"></constructor-arg>
		<constructor-arg name="queryPaymentService" ref="QueryPaymentService"></constructor-arg>
		<constructor-arg name="payRefundService" ref="PayRefundService"></constructor-arg>
	</bean>
	<!-- 支付发起webservice -->
	<jaxws:server id="webServiceVoucherCenter"
				  serviceClass="com.xinxinsoft.service.webService.pay.VoucherCenterWebService"
				  address="/voucherCenterWebService">
		<jaxws:serviceBean>
			<ref bean="voucherCenterWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<bean id="ValuableCardService" class="com.xinxinsoft.service.valuableCard.ValuableCardService"></bean>
	<!--公共工单Service-->
	<bean id="RepairOrderService" class="com.xinxinsoft.service.RepairOrderService.RepairOrderService" />
	<!--特殊支付计划-->
	<bean id="SpeciaPlanService" class="com.xinxinsoft.service.SpeciaPlanService.SpeciaPlanService" />
	<bean id="SpeciaPlanSev" class="com.xinxinsoft.sendComms.SpeciaPlanSev" />
	<!--一键甩单APP统计分析-->
	<bean id="OmsStatisticalService" class="com.xinxinsoft.service.oms.OmsStatisticalService" />
	<bean id="TariffManagementService" class="com.xinxinsoft.service.tariffManagement.TariffManagementService"/>
	<!--一键下单驳回统计分析-->
	<bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService" />
	<!--风险控制闭环管理-->
	<bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
	<!--政企白名单-->
	<bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>
	<!--统付成员管理-->
	<bean id="AllPayDesignService" class="com.xinxinsoft.service.AllMembersPayService.AllPayDesignService" />
	<!--SIM物联网卡-->
	<bean id="SIMService" class="com.xinxinsoft.service.SIM.SIMService" scope="prototype"></bean>
	<!--操作文档-->
	<bean id="UploadDocumentService" class="com.xinxinsoft.service.uploadDocumentService.UploadDocumentService" scope="prototype"></bean>
	<bean id="LateFeeMoneyDataService" class="com.xinxinsoft.service.claimForFunds.LateFeeMoneyDataService" />
	<!--欠费和销账-->
	<bean id="ArrearsWriteOffService" class="com.xinxinsoft.service.arrearsWriteOffService.ArrearsWriteOffService" scope="prototype"></bean>
	<!--SIM高频号码-->
	<bean id="IMSHighFrequencyService" class="com.xinxinsoft.service.IMSHighFrequencyService.IMSHighFrequencyService" scope="prototype"></bean>
	<!--预开票打标管理-->
	<bean id="PreinvApplyMarkingService" class="com.xinxinsoft.service.preinvApplyMarkingService.PreinvApplyMarkingService" scope="prototype"></bean>
	<!--营销活动接口-->
	<bean id="MarketActivitiesService" class="com.xinxinsoft.service.MarketActivitiesService.MarketActivitiesService" />
	<!--高风险业务申请管理-->
	<bean id="RiskDutyService" class="com.xinxinsoft.service.RiskDutyService.RiskDutyService" />
	<!--Api支付操作 -->
	<bean id="PayApiService" class="com.xinxinsoft.service.pay.PayApiService"></bean>
	<!-- 支付中心v4.5.0 统一接入规范 (V2.1.5)直接调用API支付发起service -->
	<bean id="payCenterAPIWebService" class="com.xinxinsoft.service.webService.pay.PayCenterAPIWebService">
		<constructor-arg name="payApiService" ref="PayApiService"></constructor-arg>
	</bean>
	<!-- 支付中心v4.5.0 统一接入规范 (V2.1.5)直接调用API支付发起webservice -->
	<jaxws:server id="webServiceCenterApi"
				  serviceClass="com.xinxinsoft.service.webService.pay.PayCenterAPIWebService"
				  address="/payCenterAPIWebService">
		<jaxws:serviceBean>
			<ref bean="payCenterAPIWebService" />
		</jaxws:serviceBean>
	</jaxws:server>
	<bean id="ServiceShutdownAndStartupService" class="com.xinxinsoft.service.ServiceShutdownAndStartupService.ServiceShutdownAndStartupService" />
	<!--正负补收额度管理-->
	<bean id="ReceiptApplyAmountService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyAmountService" />
	<!--和飞速成员管理-->
	<bean id="AndFlySpeedService" class="com.xinxinsoft.service.andFlySpeedService.AndFlySpeedService" />
	<!--异常号码管理-->
	<bean id="AbnormalNumberService" class="com.xinxinsoft.service.abnormalNumberService.AbnormalNumberService" />
	<!--	集团实名预约-->
	<bean id="realNameReservService" class="com.xinxinsoft.service.RealNameReservService.realNameReservService" />
	<!--138附件管理-->
	<bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService" />
	<!--合同签章角色配置-->
	<bean id="missionConfigService" class="com.xinxinsoft.service.MissionConfigService.missionConfigService" />
	<!--集团账户管理-->
	<bean id="GroupAccountService" class="com.xinxinsoft.service.groupAccountService.GroupAccountService" />
	<!--问卷调查服务方法类-->
	<bean id="QuestionnaireService" class="com.xinxinsoft.service.QuestionSurveyService.QuestionnaireService" />

	<!--#####################定时任务配置service##################-->
	<!--绿网FTP同步（作废）-->
	<bean id="lwFtpTask" class="com.xinxinsoft.task.LwFtpTask">
		<property name="operFTPservice">
			<ref bean="operFTPservice" />
		</property>
		<property name="workOrderService">
			<ref bean="WorkOrderServcie" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!-- 审计日志推送job -->
	<bean id="AuditintersLogsTask" class="com.xinxinsoft.task.AuditintersLogsTask">
		<property name="ibbnService">
			<ref bean="IbbnService" />
		</property>
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
		<!--<property name="jobLogServicer">
			 <ref bean="JobLogServicer"/>
		</property>-->
	</bean>
	<!-- BOSS同步获取环节数据（作废） -->
	<bean id="BossTask" class="com.xinxinsoft.task.BossTask">
		<property name="singleService">
			<ref bean="commonSingleService" />
		</property>
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!--同步订单环节信息 -->
	<bean id="StagesAyncBossTask" class="com.xinxinsoft.task.StagesAyncBossTask">
		<property name="singleService">
			<ref bean="commonSingleService" />
		</property>
		<property name="flowService">
			<ref bean="DedicatedFlowService" />
		</property>
		<property name="lateService">
			<ref bean="LinkTemplateService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!--EIP用户 -->
	<bean id="EipUserTask" class="com.xinxinsoft.task.EipUserTask">
		<property name="deptService">
			<ref bean="systemDeptService" />
		</property>
		<property name="ownService">
			<ref bean="OwnDepartmentsService" />
		</property>
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>

	<!--数据库  视图存储过程物化视图数据存储（作废）-->
	<bean id="proceduresTask" class="com.xinxinsoft.task.ProceduresTask">
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>

	<!--4a用户同步 -->
	<bean id="Proc4AOracle" class="com.xinxinsoft.task.Proc4AOracle">
		<property name="userService">
			<ref bean="User4AService" />
		</property>
		<property name="uuService">
			<ref bean="SystemUserService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!-- 资源查勘 （作废）-->
	<bean id="ResourceSurveyTask" class="com.xinxinsoft.task.ResourceSurveyTask">
		<property name="singleService">
			<ref bean="commonSingleService" />
		</property>
		<property name="openService">
			<ref bean="CMCCOpenService" />
		</property>
		<property name="waitTaskservice">
			<ref bean="WaitTaskService" />
		</property>
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!--更新耗时表任务-->
	<bean id="holidayTask" class="com.xinxinsoft.task.HolidayTask">
		<property name="holidayService">
			<ref bean="holidayService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>

	<!-- 尊享码上传 -->
	<bean id="HonorUpLoadTask" class="com.xinxinsoft.task.HonorUpLoadTask">
		<property name="activityService">
			<ref bean="ActivityService"/>
		</property>
		<property name="channelInfoService">
			<ref bean="ChannelInfoService"/>
		</property>
		<property name="honorCodeBusinessService">
			<ref bean="HonorCodeBusinessService"/>
		</property>
		<!--<property name="jobLogServicer">
			 <ref bean="JobLogServicer"/>
		</property>-->
	</bean>

	<!-- 欠费催缴单短信定时通知 -->
	<bean id="PaymentMessageTask" class="com.xinxinsoft.task.PaymentMessageTask">
		<property name="pService">
			<ref bean="PaymentRecordService"/>
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>

	<!-- 订单预警 -->
	<bean id="OrderWarningTask" class="com.xinxinsoft.task.OrderWarningTask">
		<property name="singleService">
			<ref bean="commonSingleService" />
		</property>
		<property name="smsPushService">
			<ref bean="smsPushService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!-- 生成合同CSV文件 -->
	<bean id="ContractCsv" class="com.xinxinsoft.task.ContractCsv">
		<property name="contractToOrderCSVService">
			<ref bean="ContractToOrderCSVService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>

	<!-- 合同CSV方法-->
	<bean id="ContractToOrderCsv" class="com.xinxinsoft.task.ContractToOrderCsv">
		<property name="contractToOrderCSVService">
			<ref bean="ContractToOrderCSVService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>

	<!-- 对账单下载 -->
	<bean id="reconciliationTask" class="com.xinxinsoft.task.ReconciliationTask">
		<property name="reconciliationService">
			<ref bean="ReconciliationService" />
		</property>
		<property name="settlementService">
			<ref bean="SettlementService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!-- 预开票开票结果数据同步 -->
	<bean id="PreinvApplyImportTask" class="com.xinxinsoft.task.PreinvApplyImportTask">
		<property name="preinvApplyService">
			<ref bean="PreinvApplyService" />
		</property>
		<property name="service">
			<ref bean="WaitTaskService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
	</bean>

	<!-- 欠费信息同步 -->
	<bean id="arrearsTask" class="com.xinxinsoft.task.ArrearsTask">
		<property name="psService">
			<ref bean="PaymentRecordService"/>
		</property>
		<property name="arrearsService">
			<ref bean="ArrearsService"/>
		</property>
	</bean>
	<!-- 欠费非打标数据定时器 -->
	<bean id="ArrearsSingTask" class="com.xinxinsoft.task.ArrearsSingTask">
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
		<property name="arrearsService">
			<ref bean="ArrearsService"/>
		</property>
		<property name="arrearsSingSerivce">
			<ref bean="ArrearsSingSerivce"/>
		</property>
	</bean>
	<!-- 资金认领相关同步-->
	<bean id="ClaimForFundsTask" class="com.xinxinsoft.task.ClaimForFundsTask">
		<property name="claimForFundsService">
			<ref bean="ClaimForFundsService" />
		</property>
		<property name="taskService">
			<ref bean="Bpms_riskoff_service" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 欠费统计执行 -->
	<bean id="ArrearsStatisticalTask" class="com.xinxinsoft.task.ArrearsStatisticalTask">
		<property name="arrearsService">
			<ref bean="ArrearsService" />
		</property>
		<property name="arrearsStatisticalService">
			<ref bean="ArrearsStatisticalService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>

	<!-- 稽核任务 -->
	<bean id="AuditWorksheetTask" class="com.xinxinsoft.task.AuditWorksheetTask">
		<property name="service">
			<ref bean="WaitTaskService" />
		</property>

		<property name="auditWorksheetService">
			<ref bean="AuditWorksheetService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>

		<property name="contractUniformityService">
			<ref bean="ContractUniformityService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 合同一致性同步 -->
	<bean id="ContractUniformityTask" class="com.xinxinsoft.task.ContractUniformityTask">
		<property name="contractUniformityService">
			<ref bean="ContractUniformityService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 专业订单工作台 -->
	<bean id="AutomaticArchivingTask" class="com.xinxinsoft.task.AutomaticArchivingTask">
		<property name="dedicatedFlowService">
			<ref bean="DedicatedFlowService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 月结发票下载BOSS电子发票 -->
	<bean id="SEinvQryTask" class="com.xinxinsoft.task.SEinvQryTask">
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>

		<property name="monthlyinvoiceService">
			<ref bean="MonthlyinvoiceService" />
		</property>
	</bean>
	<!-- 商机信息反馈推送 -->
	<bean id="BusiOppTask" class="com.xinxinsoft.task.BusiOppTask">
		<!--推送商机系统记录servcie-->
		<property name="pushBusiOppLogService">
			<ref bean="PushBusiOppLogService" />
		</property>
		<!--一键下单回调商机系统接口-->
		<property name="busiOppService">
			<ref bean="BusiOppService" />
		</property>
	</bean>
	<!--一键下单定时任务-->
	<bean id="OneClickOrderTask" class="com.xinxinsoft.task.OneClickOrderTask">
		<property name="omsService">
			<ref bean="OMSService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 指南针建档集团数据推送 -->
	<bean id="CompassFilingGroupTask" class="com.xinxinsoft.task.CompassFilingGroupTask">
		<property name="unitInfoService">
			<ref bean="UnitInfoService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 文件空间大小提示 -->
	<bean id="AttachmentSpaceReminderTask" class="com.xinxinsoft.task.AttachmentSpaceReminderTask">
		<property name="attachmentService">
			<ref bean="AttachmentService" />
		</property>
		<property name="pushService">
			<ref bean="smsPushService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>

	<!--集团产品更新服务-->
	<bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
		<property name="omsService">
			<ref bean="OMSService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!--预受理定时器-->
	<bean id="OmsOrderManagerSignInTask" class="com.xinxinsoft.task.OmsOrderManagerSignInTask" scope="prototype">
		<property name="omsSellOrderService">
			<ref bean="OmsSellOrderService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
		<property name="service">
			<ref bean="WaitTaskService" />
		</property>
		<property name="taskService">
			<ref bean="Bpms_riskoff_service" />
		</property>
		<property name="structureOfPersonnelService">
			<ref bean="StructureOfPersonnelService" />
		</property>
	</bean>

	<!-- 欠费销账管理 -->
	<bean id="ArrearsWriteOffTask" class="com.xinxinsoft.task.ArrearsWriteOffTask">
		<property name="arrearsWriteOffService">
			<ref bean="ArrearsWriteOffService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 集团效益评估统计-->
	<bean id="MarketActivitiesTask" class="com.xinxinsoft.task.MarketActivitiesTask">
		<property name="marketActivitiesService">
			<ref bean="MarketActivitiesService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>
	<!-- 异常数据管理 -->
	<bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
		<property name="abnormalNumberService">
			<ref bean="AbnormalNumberService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer" />
		</property>
	</bean>

	<!--营销活动管理 src\com\xinxinsoft\service\marketingActivitiesService\MarketingActivitiesService.java-->
	<bean id="MarketingActivitiesService" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesService" />

	<!--终端管理-->
	<bean id="TerminalActivityService" class="com.xinxinsoft.service.terminalActivityAdministration.TerminalActivityService" />

	<!--app动态导入合同数据-->
	<bean id="AppDynamicContractService" class="com.xinxinsoft.service.contract.AppDynamicContractService" />
	<bean id="ContractExpireTask" class="com.xinxinsoft.task.ContractExpireTask">
		<property name="customClauseContractService">
			<ref bean="CustomClauseContractService" />
		</property>
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<bean id="ContractExpiredTask" class="com.xinxinsoft.task.ContractExpiredTask">
		<property name="customClauseContractService">
			<ref bean="CustomClauseContractService" />
		</property>
		<property name="userService">
			<ref bean="SystemUserService" />
		</property>
		<property name="jobLogServicer">
			<ref bean="JobLogServicer"/>
		</property>
	</bean>
	<!--三方用户信息导入-->
	<bean id="TripartipartUserService" class="com.xinxinsoft.service.core.user.TripartipartUserService" />

	<!--白名单定时器-->
	<bean id="EnterPriseTask" class="com.xinxinsoft.task.EnterPriseTask" />

	<!--集团报损-->
	<bean id="groupReportedLossesService" class="com.xinxinsoft.service.GroupReportedService.GroupReportedLossesService" />
    </beans>