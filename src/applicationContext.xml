<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" 
	xmlns:context="http://www.springframework.org/schema/context"
	xmlns:aop="http://www.springframework.org/schema/aop" 
	xmlns:tx="http://www.springframework.org/schema/tx"
	xmlns:task="http://www.springframework.org/schema/task" 
	xsi:schemaLocation="
		http://www.springframework.org/schema/beans
		http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		http://www.springframework.org/schema/context
		http://www.springframework.org/schema/context/spring-context-3.1.xsd
		http://www.springframework.org/schema/aop 
		http://www.springframework.org/schema/aop/spring-aop-3.1.xsd
		http://www.springframework.org/schema/tx
		http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
		http://www.springframework.org/schema/task
		http://www.springframework.org/schema/task/spring-task-3.0.xsd
		">
	<import resource="classpath:META-INF/cxf/cxf.xml" />
	<import resource="classpath:META-INF/cxf/cxf-extension-soap.xml" />
	<import resource="classpath:META-INF/cxf/cxf-servlet.xml" /> 
	<import resource="applicationContext-manager.xml" />
	<import resource="applicationContext-action.xml" />
	<import resource="applicationContext-jpbm.xml" />

	<bean
		class="org.springframework.beans.factory.config.PropertyPlaceholderConfigurer">
		<property name="systemPropertiesModeName" value="SYSTEM_PROPERTIES_MODE_OVERRIDE">
		</property>
		<property name="ignoreResourceNotFound" value="true" />
		<property name="locations">
			<list>
				<value>classpath:DataBase-config.properties</value>
			</list>
		</property>
	</bean>
 <!-- 配置 C3P0 数据源 -->
	<bean id="dataSource" class="com.mchange.v2.c3p0.ComboPooledDataSource"
		destroy-method="close">
		<property name="driverClass" value="${jdbc.driverClassName}"></property>
		<property name="jdbcUrl" value="${jdbc.url}"></property>
		<property name="user" value="${jdbc.username}"></property>
		<property name="password" value="${jdbc.password}"></property>
		<!--初始化时获取的连接数，取值应在minPoolSize与maxPoolSize之间。Default: 3 -->
		<property name="initialPoolSize" value="3" ></property>
		<!--连接池中保留的最大连接数。Default: 15 -->
		<property name="maxPoolSize" value="20" ></property>
		<!--当连接池中的连接耗尽的时候c3p0一次同时获取的连接数。Default: 3 -->
		<property name="acquireIncrement" value="3" ></property>
		<!--每60秒检查所有连接池中的空闲连接。Default: 0 -->
		<property name="idleConnectionTestPeriod" value="60" ></property>
		<!--连接关闭时默认将所有未提交的操作回滚。Default: false -->
		<property name="autoCommitOnClose" value="true" ></property>
		<!--定义所有连接测试都执行的测试语句。在使用连接测试的情况下这个一显著提高测试速度。注意：
		测试的表必须在初始数据源的时候就存在。Default: null-->
		<property name="preferredTestQuery" value="select 1 from dual"></property>
		<!--因性能消耗大请只在需要的时候使用它。如果设为true那么在每个connection提交的 时候都将校验其有效性。建议使用idleConnectionTestPeriod或automaticTestTable
			等方法来提升连接测试的性能。Default: false -->
		<property name="testConnectionOnCheckout" value="false" ></property>
		<!--如果设为true那么在取得连接的同时将校验连接的有效性。Default: false -->
		<property name="testConnectionOnCheckin" value="false"></property>
		<!--定义在从数据库获取新连接失败后重复尝试的次数。Default: 30 -->
		<property name="acquireRetryAttempts"  value="30" ></property>
		<!--两次连接中间隔时间，单位毫秒。Default: 1000 -->
		<property name="acquireRetryDelay" value="1000"></property>
		<!--获取连接失败将会引起所有等待连接池来获取连接的线程抛出异常。但是数据源仍有效
		保留，并在下次调用getConnection()的时候继续尝试获取连接。如果设为true，那么在尝试
		获取连接失败后该数据源将申明已断开并永久关闭。Default: false-->
		<property name="breakAfterAcquireFailure" value="false"></property>
		<!--最大空闲时间,60秒内未使用则连接被丢弃。若为0则永不丢弃。Default: 0 -->
		<property name="maxIdleTime" value="25000"></property>
		<!--c3p0是异步操作的，缓慢的JDBC操作通过帮助进程完成。扩展这些操作可以有效的提升性能
		通过多线程实现多个操作同时被执行。Default: 3-->
		<property name="numHelperThreads" value="6"></property>
		<!--JDBC的标准参数，用以控制数据源内加载的PreparedStatements数量。但由于预缓存的statements
		属于单个connection而不是整个连接池。所以设置这个参数需要考虑到多方面的因素。
		如果maxStatements与maxStatementsPerConnection均为0，则缓存被关闭。Default: 0-->
		<property name="maxStatements" value="0"></property>
		<!--maxStatementsPerConnection定义了连接池内单个连接所拥有的最大缓存statements数。Default: 0 -->
		<property name="maxStatementsPerConnection" value="0"></property>
		<!--当连接池用完时客户端调用getConnection()后等待获取新连接的时间，超时后将抛出
		SQLException,如设为0则无限期等待。单位毫秒。Default: 0 -->
		<property name="checkoutTimeout" value="180000"></property>

	</bean>

	<bean id="sessionFactory" class="org.springframework.orm.hibernate3.LocalSessionFactoryBean">
		<property name="dataSource">
			<ref local="dataSource" />
		</property>
		<property name="mappingLocations">
			<list>
				<value>classpath:com/xinxinsoft/entity/**/*.hbm.xml</value>
				<!-- 以下几个jbpm.*.hbm.xml由jBPM自带 -->
				<value>classpath:jbpm.repository.hbm.xml</value>
				<value>classpath:jbpm.execution.hbm.xml</value>
				<value>classpath:jbpm.history.hbm.xml</value>
				<value>classpath:jbpm.task.hbm.xml</value>
				<value>classpath:jbpm.identity.hbm.xml</value>
			</list>
		</property>

		<property name="hibernateProperties">
			<props>
				<prop key="hibernate.connection.provider_class">
					org.hibernate.connection.C3P0ConnectionProvider
				</prop>
				<prop key="hibernate.dialect">
					${jdbc.dialectName}
				</prop>
				<prop key="hibernate.show_sql">false</prop>
				<prop key="hibernate.format_sql">true</prop>
				<!-- validate 加载hibernate时，验证创建数据库表结构
					create 每次加载hibernate，重新创建数据库表结构
					create-drop 加载hibernate时创建，退出是删除表结构
					update 加载hibernate自动更新数据库结构-->
				<prop key="hibernate.hbm2ddl.auto">none</prop>
				<prop key="hibernate.cache.use_second_level_cache">
					false
				</prop>
				<prop key="hibernate.cache.use_query_cache">false</prop>
				<prop key="hibernate.jdbc.batch_size">50</prop>
				<prop key="hibernate.jdbc.fetch_size">50</prop>
			</props>
		</property>
	</bean>
 
	<!-- 1. 配置 hibernate 的事务管理器 -->
	<bean id="transactionManager"
		class="org.springframework.orm.hibernate3.HibernateTransactionManager">
		<property name="sessionFactory">
			<ref bean="sessionFactory" />
		</property>
	</bean>

	<!-- <aop:config proxy-target-class="true">
		<aop:pointcut id="serviceOperation" expression="execution(* com.xinxinsoft.service..*Service.*(..))" />
		<aop:advisor pointcut-ref="serviceOperation" advice-ref="txAdvice" />
	</aop:config> -->
	<aop:config proxy-target-class="true">
		<aop:pointcut id="serviceOperation" expression="execution(* com.xinxinsoft.action..*.*(..)) or execution(* com.xinxinsoft.service..*Service.*(..))" />
		<aop:advisor pointcut-ref="serviceOperation" advice-ref="txAdvice" />
	</aop:config>

	<tx:advice id="txAdvice" transaction-manager="transactionManager">
		<tx:attributes>
			<tx:method name="*" propagation="REQUIRED" rollback-for="java.lang.Exception" />
			<tx:method name="find*" read-only="true" />
		</tx:attributes>
	</tx:advice>
	<!-- JOB执行 -->
	 <import resource="applicationContext-task.xml" />
	<!-- <context:component-scan base-package="com.xinxinsoft.service"/> -->
</beans>


