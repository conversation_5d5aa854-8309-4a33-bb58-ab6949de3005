<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE struts PUBLIC
        "-//Apache Software Foundation//DTD Struts Configuration 2.3//EN"
        "http://struts.apache.org/dtds/struts-2.3.dtd">
<struts>
    <constant name="struts.action.extension" value="do,action"/>
    <constant name="struts.i18n.encoding" value="utf-8"></constant>
    <constant name="struts.enable.SlashesInActionNames" value="true"></constant>
    <constant name="struts.objectFactory" value="spring"/>
    <!--    <constant name="struts.devMode" value="true"></constant>-->
    <constant name="struts.objectFactory.spring.autoWire" value="name"/>
    <constant name="struts.multipart.maxSize" value="1000000000"/>
    <constant name="struts.multipart.saveDir" value="/tmp"/>
    <package name="default" namespace="/" extends="struts-default">

        <!-- 后台系统用户ACTON -->
        <action name="jsp/user/loginUserAction!*" class="LoginUserAction" method="{1}">
            <result name="addUserSucessfully" type="redirect">jsp/user/searchUser.jsp</result>
            <result name="addUserFailed" type="redirect">jsp/user/searchUser.jsp</result>
            <result name="editUserSucessfully" type="redirect">jsp/user/searchUser.jsp</result>
            <result name="editUserFailed" type="redirect">jsp/user/searchUser.jsp</result>
            <result name="index">jsp/index/index.jsp</result>
            <result name="invalid.token">/jsp/index/resubmit.jsp</result>
            <interceptor-ref name="defaultStack"/>
            <interceptor-ref name="token">
                <param name="includeMethods">addUser,updateUser</param>
            </interceptor-ref>
        </action>

        <!-- 后台系统角色ACTON -->
        <action name="jsp/role/roleAction!*" class="RoleAction" method="{1}">
            <result name="display">/jsp/role/editRole.jsp</result>
            <result name="goback" type="redirect">/jsp/role/searchRole.jsp</result>
            <result name="invalid.token">/jsp/index/resubmit.jsp</result>
            <interceptor-ref name="defaultStack"/>
            <interceptor-ref name="token">
                <param name="includeMethods">editRole,addRole</param>
            </interceptor-ref>
        </action>

        <!-- 后台系统角色菜单ACTON -->
        <action name="jsp/roleMenu/roleAction!*" class="RoleAction" method="{1}">
            <result name="display">/jsp/role/editRole.jsp</result>
            <result name="goback" type="redirect">/jsp/role/searchRole.jsp</result>
            <result name="invalid.token">/jsp/index/resubmit.jsp</result>
            <interceptor-ref name="defaultStack"/>
            <interceptor-ref name="token">
                <param name="includeMethods">editRole,addRole</param>
            </interceptor-ref>
        </action>
        <!-- 后台系统组织结构ACTON -->
        <action name="jsp/org/systemOrganizationAction!*" class="SystemOrganizationAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/success.jsp</result>
            <result name="getOrgTree">/success.jsp</result>
        </action>

        <!-- 类型ACTON
        <action name="typeAction!*" class="TypeAction" method="{1}">
        </action> -->
        <!-- ftp系统文件操作 --><!-- 附件上传：下载 -->
        <action name="jsp/base/ftpHandleAction!*" class="ftpHandleAction" method="{1}">
        </action>
        <!-- 后台系统权限action -->
        <action name="permissionAction!*" class="permissionAction" method="{1}">
            <result name="loginSuccess" type="redirect">/jsp/index/index.jsp</result>
            <result name="loginError">/jsp/index/login4A.jsp</result>
        </action>

        <!-- 客户信息管理-->
        <action name="jsp/customer/customerAction!*" class="customerAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/serachcustomer.jsp</result>
            <!-- <result name="getOrgTree">/success.jsp</result> -->
        </action>


        <!-- 集团客户信息管理-->
        <action name="jsp/groupcustomer/GroupCustomerAction!*" class="GroupCustomerAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/serachGroupCustomer.jsp</result>
            <!-- <result name="getOrgTree">/success.jsp</result> -->
        </action>


        <!--集团关键人管理  -->
        <action name="jsp/customeraccount/GroupPeopleAction!*" class="GroupPeopleAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/serachGroupPeople.jsp</result>
            <!-- <result name="getOrgTree">/success.jsp</result> -->
        </action>
        <!-- 客户账户管理-->
        <action name="jsp/customeraccount/CustomerAccountAction!*" class="CustomerAccountAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/serachCustomerAccount.jsp</result>
            <!-- <result name="getOrgTree">/success.jsp</result> -->
        </action>
        <!-- 附件类型管理-->
        <action name="jsp/enclosure/EnclosureTypeAction!*" class="EnclosureTypeAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/serachEnclosureType.jsp</result>
            <!-- <result name="getOrgTree">/success.jsp</result> -->
        </action>
        <!-- 附件管理-->
        <action name="jsp/enclosure/attachmentAction!*" method="{1}" class="AttachmentAction"/>
        <!-- App用户信息 -->
        <action name="jsp/news/AppUserHeadAction!*" class="AppUserHeadAction" method="{1}">
            <result name="rs">/jsp/group/result.jsp</result>
        </action>

        <!-- App用户信息 -->
        <action name="jsp/user/AppUserLoginAction!*" class="AppUserHeadAction" method="{1}">
            <result name="rs">/jsp/group/result.jsp</result>
        </action>

        <!-- add -->
        <!-- 登录用户 -->
        <action name="systemUserAction!*" class="SystemUserAction" method="{1}">
            <result name="loginSuccess" type="redirect">/jsp/index/index.jsp</result>
            <result name="loginSuccess2" type="redirect">/jsp/index/index.jsp</result>
            <result name="loginError2" type="redirect">/jsp/index/error4A.jsp</result>
            <result name="firstLoginupdatePass" type="redirect">/jsp/index/firstLoginupdatePass.jsp</result>
            <result name="loginError" type="redirect">/jsp/index/login4A.jsp</result>
            <result name="login4AError" type="redirect">/jsp/index/error.jsp</result>
            <result name="editUserSucessfully" type="redirect">jsp/user/searchUser.jsp</result>
            <result name="editUserFailed" type="redirect">jsp/user/searchUser.jsp</result>
            <interceptor-ref name="defaultStack"/>
            <interceptor-ref name="token">
                <param name="includeMethods">updateUser</param>
            </interceptor-ref>
            <result name="success" type="stream">
                <param name="contentType">image/jpeg</param>
                <param name="inputName">imgInputStream</param>
                <param name="bufferSize">2048</param>
            </result>
        </action>

        <!-- 消息通知信息： -->
        <action name="jsp/msg/messagesInfoAction!*" class="messagesInfoAction" method="{1}">
            <result name="error">/error.jsp</result>
        </action>


        <!-- 部门管理 -->
        <action name="jsp/dept/DeptAction!*" class="systemDeptAction" method="{1}"></action>


        <!-- 业务类型： -->
        <action name="jsp/basetype/BusinessTypeAction!*" class="BusinessTypeAction" method="{1}">

            <result name="error">/error.jsp</result>
            <result name="add">/jsp/basetype/businesstype/addBusinessType.jsp</result>
            <result name="query">/jsp/basetype/businesstype/queryBusinessType.jsp</result>

        </action>
        <!-- 产品类型： -->
        <action name="jsp/basetype/producttypeaction!*" class="ProductTypeAction" method="{1}">
            <result name="error">/error.jsp</result>
            <result name="Jump">/jsp/basetype/producttype/addProductType.jsp</result>
            <result name="query">/jsp/basetype/producttype/queryProductType.jsp</result>

        </action>

        <!-- 消息通知信息： -->
        <action name="jsp/commonSingManagement/commonSingleAction!*" class="commonSingleAction" method="{1}">
            <result name="error">/error.jsp</result>
        </action>
        <action name="testProcessAction!*" class="testProcessAction" method="{1}">
            <result name="error">/error.jsp</result>
            <result name="createprocesss">/jsp/processtest/createLeave.jsp</result>
            <result name="list">/jsp/processtest/askForLeave.jsp</result>
            <result name="dealLeave">/jsp/processtest/dealLeave.jsp</result>
            <result name="pendingprocess">/jsp/processtest/pendingprocessList.jsp</result>
        </action>

        <!-- 人员树 -->
        <action name="jsp/user/ZtreeUserAction!*" class="ZtreeUserAction" method="{1}">
            <result name="success" type="stream">
                <!-- 指定被下载内容的位置 -->

                <!-- 下载文件类型
                <param name="contentType">application/msword</param>  -->
                <!-- 下载对话框所弹出的文件名动态显示实际文件名 -->
                <!--  <param name="contentType">
                 application/octet-stream;charset=ISO8859-1
                 </param> -->

                <param name="contentDisposition">
                    attachment;filename="${fileFileName}"
                </param>
                <!-- 下载的InputStream流，Struts2自己动对应Action中的getDownloadFile方法，该方法必须返回InputStream类型 -->
                <param name="inputName">downloadFile</param>
            </result>
        </action>

        <!-- 知识库 -->
        <action name="jsp/knowledge/knowledgeAction!*" class="knowledgeAction" method="{1}"></action>

        <!-- 待办任务 -->
        <action name="jsp/waitTask/waitTaskAction!*" class="WaitTaskAction" method="{1}"></action>

        <!-- 字典 -->
        <action name="jsp/core/dictionaryAction!*" class="dictionaryAction" method="{1}">
            <result name="productTypedictionaryList">
                /jsp/productTypeDictionary/searchProductTypeDictionary.jsp
            </result>
            <result name="error">/error.jsp</result>
        </action>
        <!-- 节假日 -->
        <action name="jsp/core/holidayAction!*" class="holidayAction" method="{1}">
            <result name="error">/error.jsp</result>
        </action>


        <!-- 产品流程-->
        <action name="jsp/processLink/productFlowAction!*" class="ProductFlowAction" method="{1}"></action>

        <!-- 需求申请-->
        <action name="jsp/dedicatedFlow/dedicatedFlowAction!*" class="DedicatedFlowAction" method="{1}"></action>
        <!-- 客户验收-->
        <action name="jsp/dedicatedFlow/coutomeracceptanceaction!*" class="CoutomerAcceptanceAction"
                method="{1}"></action>

        <!-- 环节模板-->
        <action name="jsp/processLink/linkTemplateAction!*" class="LinkTemplateAction" method="{1}"></action>
        <!-- 资费申请-->
        <action name="jsp/dedicatedFlow/expenseApplyAction!*" class="ExpenseApplyAction" method="{1}"></action>
        <!-- 菜单管理 -->
        <action name="jsp/menu/menuAction!*" class="MenuAction" method="{1}"></action>
        <action name="smsPushAction!*" class="smsPushAction" method="{1}"></action>

        <action name="statisticsAction!*" class="statisticsAction" method="{1}"></action>
        <!-- ims-->
        <action name="imsAction!*" class="imsAction" method="{1}">
        </action>
        <!-- ito-->
        <action name="itoAction!*" class="itoAction" method="{1}">
        </action>
        <!-- 超时订单top3 -->
        <action name="jsp/over/voertime!*" class="overTimeAction" method="{1}">
        </action>
        <!-- app统计登陆次数 -->
        <action name="jsp/applogin/appLogin!*" class="appLoginAction" method="{1}">
        </action>

        <!-- app -->
        <action name="jsp/app/linkJbpmTo!*" class="linkJbpmToAppAction" method="{1}">
        </action>
        <action name="jsp/app/waitTaskToApp!*" class="waitTaskToAppAction" method="{1}">
        </action>
        <action name="jsp/app/orderFormToApp!*" class="orderFormToAppAction" method="{1}">
        </action>

        <action name="jsp/app/attachmentDownloadAction!*" class="attachmentDownloadAction" method="{1}">
        </action>

        <action name="jsp/app/uploadToApp!*" class="uploadToAppAction" method="{1}">
        </action>
        <action name="jsp/app/loginVerify!*" class="loginVerifyAction" method="{1}"/>
        <action name="jsp/app/deleteAttachmentAction!*" class="deleteAttachmentAction" method="{1}">
        </action>
        <action name="jsp/app/groupCustomerToAppAction!*" class="groupCustomerToAppAction" method="{1}">
        </action>
        <!-- app end -->


        <!-- 集团等级提升全网升级 -->
        <action name="jsp/dedicatedFlow/upGroupLevelProcessAction!*" class="upGroupLevelProcessAction" method="{1}">
        </action>
        <!-- 统计分享 -->
        <action name="jsp/countShare/CountShareAction!*" class="countShareAction" method="{1}">
        </action>
        <!-- boss环节 -->
        <action name="jsp/BossTacheAction!*" class="BossTacheAction" method="{1}"></action>

        <!-- 导出任务统计的Excel表 -->
        <action name="singListExcelAction!*" class="singListExcelAction" method="{1}">
        </action>

        <!--4A开关  -->
        <action name="jsp/app/Open4AStateAction!*" class="Open4AStateAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/serachOpen4AState.jsp</result>
            <!-- <result name="getOrgTree">/success.jsp</result> -->
        </action>

        <!-- App版本上传-->
        <action name="jsp/appEdition/appUploadAction!*" class="AppUploadAction" method="{1}"></action>
        <!-- App版本管理 -->
        <action name="jsp/appEdition/EditionAction!*" class="EditionAction" method="{1}">
            <result name="error">/error.jsp</result>
            <result name="add">/jsp/appEdition/upload.jsp</result>
            <result name="query">/jsp/appEdition/editionInfo.jsp</result>

        </action>
        <!-- App版本下载-->
        <action name="jsp/app/appDownloadAction!*" class="AppDownloadAction" method="{1}"></action>
        <action name="orderListYearAction!*" class="orderListYearAction" method="{1}"></action>


        <action name="jsp/iboss/iBossByNoUpOrder!*" class="IBossByNoAction" method="{1}"></action>

        <!-- APP的密码找回以及验证码的Action -->
        <action name="AppUploadPassword!*" class="AppUploadPassword" method="{1}"></action>
        <!-- 专业订单合同 -->
        <action name="ContractAction!*" class="ContractAction" method="{1}"></action>


        <action name="jsp/order/OrderDetailAction!*" class="orderDetailAction" method="{1}"></action>

        <!--业务实体-->
        <action name="jsp/businessProc/BusinessEntityAction!*" class="BusinessEntityAction"
                method="{1}">
            <result name="error">/error.jsp</result>
            <result name="success">/serachBusinessEntity.jsp</result>
        </action>

        <!-- 工单 -->
        <action name="jsp/workOrder/WorkOrderAction!*" class="WorkOrderAction" method="{1}"></action>
        <!--稽查-->
        <action name="jsp/check/CheckAction!*" class="CheckAction" method="{1}"></action>
        <!-- 存送单 -->
        <action name="SaveSendAction!*" class="SaveSendAction" method="{1}"></action>

        <!-- 补收 -->
        <action name="SupCollectSAction!*" class="SupCollectSAction" method="{1}"></action>
        <!-- 终端 -->
        <action name="TerminalAction!*" class="TerminalAction" method="{1}"></action>
        <action name="TransferAccountsAction!*" class="TransferAccountsAction" method="{1}"></action>
        <!-- ftp附件推送与获取 -->
        <action name="LwFtpAction!*" class="LwFtpAction" method="{1}"></action>

        <!-- App工单 -->
        <action name="AppWorkOrderAction!*" class="AppWorkOrderAction" method="{1}"></action>
        <action name="SaveSendCheckAction!*" class="SaveSendCheckAction" method="{1}"></action>
        <action name="UpdateCustomerAcceptanceAction!*" class="UpdateCustomerAcceptanceAction" method="{1}"></action>
        <!--APP 数据统计-->
        <action name="APPStatisticsAction!*" class="APPStatisticsAction" method="{1}"></action>
        <!--问题分类-->
        <action name="jsp/question/QuestionAction!*" class="QuestionAction" method="{1}"></action>
        <!--投诉建议-->
        <action name="RecommendedManagementAction!*" class="RecommendedManagementAction" method="{1}"></action>
        <!--合同接口http-->
        <action name="ContractSrv_*" class="ContractServlet" method="{1}"></action>
        <!-- App图片下载-->
        <action name="AppContractAction!*" class="AppContractAction" method="{1}"></action>
        <!-- 尊享码活动，渠道信息 -->
        <action name="ChannelInfo_*" class="ChannelInfoAction" method="{1}"></action>
        <!-- 尊享码活动，尊享码信息 -->
        <action name="HonorCodeBusiness_*" class="HonorCodeBusinessAction" method="{1}"></action>
        <!-- app尊享码活动，app尊享码信息 -->
        <action name="AppHonorCodeBusiness_*" class="AppHonorCodeBusinessAction" method="{1}"></action>
        <!-- 营销活动 -->
        <action name="Activity_*" class="ActivityAction" method="{1}"></action>
        <!-- 通专融合 -->
        <action name="IntegrationAction!*" class="IntegrationAction" method="{1}"></action>
        <action name="webApi_*" class="AppGetOrPostWebApiAction" method="{1}"></action>
        <action name="webQueryApi_*" class="AppGetOrPostWebQueryApiAction" method="{1}"></action>
        <!-- 欠费管理，欠费信息 -->
        <action name="Arrears_*" class="ArrearsAction" method="{1}"></action>
        <!-- 欠费统计 -->
        <action name="ArrearsStatisticalAction_*" class="ArrearsStatisticalAction" method="{1}"></action>
        <!-- 欠费管理，协议补录 -->
        <action name="Agreement_*" class="AgreementAction" method="{1}"></action>
        <!-- 欠费管理，催缴单信息 -->
        <action name="PaymentRecord_*" class="PaymentRecordAction" method="{1}"></action>
        <!-- 转账管理 -->
        <action name="Transfer_*" class="TransferInformationAction" method="{1}"></action>
        <!-- 转账管理(app) -->
        <action name="AppTransfer_*" class="APPTransferInformationAction" method="{1}"></action>
        <!-- jpbm流程 -->
        <action name="jbpm_*" class="JbpmTest" method="{1}"></action>
        <!-- 支付结果 -->
        <action name="PaymentOrder_*" class="PaymentOrderAction" method="{1}"></action>
        <!-- 退款结果 -->
        <action name="RefundOrder_*" class="RefundOrderAction" method="{1}"></action>
        <!-- ict合同结果 -->
        <action name="IctContract!*" class="IctContractAction" method="{1}"></action>
        <!-- 附加资费接口-->
        <action name="S4000Cfm_*" class="S4000CfmAction" method="{1}"></action>
        <!-- 对账单接口-->
        <action name="Reconciliation_*" class="ReconciliationAction" method="{1}"></action>
        <!-- 1000-->
        <action name="b1000OpenSrv_*" class="b1000OpenSev" method="{1}"></action>
        <!-- 1000 query-->
        <action name="b1000QuerySrv_*" class="b1000QuerySev" method="{1}"></action>
        <!-- 快速开户产品 -->
        <action name="AccountOrderInfo_*" class="AccountOrderInfoAction" method="{1}"></action>
        <!-- 稽核接口 -->
        <action name="AuditWorksheetHttpAction_*" class="AuditWorksheetHttpAction" method="{1}"></action>
        <!-- 稽核 -->
        <action name="AuditWorksheetAction_*" class="AuditWorksheetAction" method="{1}"></action>
        <!-- 合同认领 -->
        <action name="ClaimAContractAction_*" class="ClaimAContractAction" method="{1}"></action>
        <!-- 预开票 -->
        <action name="preinvApply_*" class="PreinvApplyAction" method="{1}"></action>
		<!-- 预开票APP -->
		<action name="AppPreinvApply_*" class="AppPreinvApplyAction" method="{1}"></action>
		<!-- 尊享码开放接口 -->
		<action name="enjoyCodeSrv_*" class="HonorCodeInterfaceAction" method="{1}"></action>
        <!-- 资金认领 -->
        <action name="ClaimForFundAction_*" class="ClaimForFundAction" method="{1}"></action>
        <action name="ClaimForFundModelAction_*" class="ClaimForFundModelAction" method="{1}"></action>
        <!-- 地市节点金额(转账管理)-->
        <action name="TransferCitiesDataAction_*" class="TransferCitiesDataAction" method="{1}"></action>
        <!-- 转账管理 -->
        <action name="TransferTwo_*" class="TransferInformationTwoAction" method="{1}"></action>
		<!-- job日志 -->
		<action name="JobLogAction_*" class="JobLogAction" method="{1}"></action>
        <!-- 应收未收管理 -->
        <action name="ReceiveApplyAction_*" class="ReceiveApplyAction" method="{1}"></action>
		<!-- 应收未收管理 （APP）-->
		<action name="AppReceiveApplyAction_*" class="AppReceiveApplyAction" method="{1}"></action>
		<!-- 快单查询 -->
		<action name="jsp/quickquery/quickqueryAction!*" class="quickqueryAction" method="{1}">
			<result name="error">/error.jsp</result>
		</action>
		<!-- 快单详情 -->
		<action name="jsp/quickquery/quickQueryDetailsAction!*" class="quickQueryDetailsAction" method="{1}">
			<result name="error">/error.jsp</result>
		</action>
        <!-- 稽核综合管理 -->
        <action name="AuditMultipleAction_*" class="AuditMultipleAction" method="{1}"></action>
        <!-- 稽核信息管理 -->
        <action name="AuditWorkListAction_*" class="AuditWorkListAction" method="{1}"></action>
        <!-- 合同管理 -->
        <action name="ContractUniformityAction_*" class="ContractUniformityAction" method="{1}"></action>
        <!-- IDC-->
        <action name="IDCApplyAction!*" class="IDCApplyAction" method="{1}"></action>
        <!-- 业务周期 -->
        <action name="BusinssAction_*" class="BusinssAction" method="{1}"></action>
        <!--  调用sGrpJttfConRedIn接口 -->
        <action name="BusinssGrpJttfConRedInAction_*" class="BusinssGrpJttfConRedInAction" method="{1}"></action>
        <!-- 调用sTaxpayerNumberQry接口 -->
        <action name="BusinssTaxpayerNumberQryAction_*" class="BusinssTaxpayerNumberQryAction" method="{1}"></action>
        <!-- 稽核 -->
        <action name="AuditWorksheetActionTwo_*" class="AuditWorksheetActionTwo" method="{1}"></action>
        <action name="ManualInvApplyAction_*" class="ManualInvApplyAction" method="{1}"></action>
        <!-- 营销活动预开票 -->
        <action name="actPreinvApply_*" class="activityPreinvApplyAction" method="{1}"></action>
        <!-- 白名单管理 -->
        <action name="WhiteListAction_*" class="WhiteListAction" method="{1}"></action>
        <action name="WhiteListInformationAction_*" class="WhiteListInformationAction" method="{1}"></action>
        <!-- 存送 -->
        <action name="BigAmountApply_*" class="BigAmountApplyAction" method="{1}"></action>
		<!-- 正负补收 -->
        <action name="ReceiptApplyAction_*" class="ReceiptApplyAction" method="{1}"></action>
		<!-- 行业终端 -->
        <action name="IndustryTerminalBySMMCCalculateARPUAction_*" class="IndustryTerminalBySMMCCalculateARPUAction"
                method="{1}"></action>
        <action name="IndustryTerminalAction_*" class="IndustryTerminalAction" method="{1}"></action>
		<!-- 通知支付 -->
		<action name="Payment_*" class="PaymentAction" method="{1}"></action>
		<!-- 通知退款 -->
		<action name="PayRefund_*" class="PayRefundAction" method="{1}"></action>
        <!-- 欠费回收 -->
        <action name="ArrearsSingAction_*" class="ArrearsSingAction" method="{1}"></action>
        <!--工单审批-->
        <action name="JobApproval_*" class="JobApprovalAction" method="{1}"/>
        <!--WEB端 工单审批-->
        <action name="RejectOrder_*" class="RejectOrderAction" method="{1}"/>
        <!-- 列查询 -->
        <action name="jsp/inquiryOrder/InquiryOrderAction!*" class="InquiryOrderAction" method="{1}"></action>
        <!-- 缓停 -->
        <action name="SuspensionApplicationAction_*" class="SuspensionApplicationAction" method="{1}"></action>
		<action name="jsp/oms/omsSrv_*" class="OMSAction" method="{1}"></action>
		<action name="jsp/pms/pmsSrv_*" class="PMSAction" method="{1}"></action>
		<action name="jsp/iboss/iBossByNoUpOrder_*" class="IBossByNoAction" method="{1}"></action>
		<action name="contractAct_*" class="customClauseContractAction" method="{1}"></action>
        <!-- ICT -->
        <action name="ICTApplicationAction_*" class="ICTApplicationAction" method="{1}"></action>
        <action name="ReductionICTApplicationAction_*" class="ReductionICTApplicationAction" method="{1}"></action>
		<!-- 资金认领APP -->
		<action name="AppClaimForFunds_*" class="AppClaimForFundsAction" method="{1}"></action>
		<!-- 月结发票APP -->
		<action name="AppMonthlyinvoice_*" class="AppMonthlyinvoice" method="{1}"></action>
		<!-- 月结发票BOSS请求 -->
		<action name="jsp/iboss/InvoiceReceiveOrder_*" class="InvoiceReceiveOrder" method="{1}"></action>
		<!-- 数据导出Cvs-->
        <action name="RiskcontrolDerivedAction_*" class="RiskcontrolDerivedAction"
                method="{1}"></action>
        <!--转账接口-->
        <action name="TransferAction_*" class="TransferAction" method="{1}"></action>
		<!--开户欠费-->
		<action name="AccountOpenAction_*" class="AccountOpenAction"
				method="{1}"></action>
		<!--合同电子签章-->
        <action name="EomSignAction_*" class="EomSignAction" method="{1}"></action>
        <action name="EomCertAction!*" class="EomCertAction" method="{1}"></action>
        <action name="EomSealAction!*" class="EomSealAction" method="{1}"></action>
        <action name="PayProviderInfoAction!*" class="PayProviderInfoAction"  method="{1}"></action>
		<!--业务强开-->
		<action name="fourtOpenAction_*" class="fourtOpenAction" method="{1}"></action>
        <action name="AppFourtOpenAction_*" class="appFourtOpenAction" method="{1}"></action>
		<!--电子合同生成接口-->
		<action name="ContractInterfaceAction_*" class="ContractInterfaceAction" method="{1}"></action>
        <!--销售工单（客户经理需求单）-->
        <action name="OmsSellOrderAction_*" class="OmsSellOrderAction"   method="{1}"></action>
        <!--工单受理工作台-->
        <action name="OmsOrderWorkbenchAction_*" class="OmsOrderWorkbenchAction" method="{1}"></action>
        <!--销售工单产品受理详细-->
        <action name="OmsOrderProductAction_*" class="OmsOrderProductAction" method="{1}"></action>
		<action name="AppOmsSellOrderAction_*" class="AppOmsSellOrderAction" method="{1}"></action>
		<action name="BusiTurnOrderSrv_*" class="ServiceStandardizationTestingAction" method="{1}"></action>
		<action name="OrderTesting_*" class="OrderTesting" method="{1}"></action>
        <!--集团客户快速建档-->
        <action name="ums/unitInfo_*" class="UnitInfoAction" method="{1}"></action>
        <!--集团统付-->
        <action name="GroupPaymentAction_*" class="GroupPaymentAction" method="{1}"></action>
        <!--集团统付回调接口-->
        <action name="GroupPayHttpAction_*" class="GroupPayHttpAction" method="{1}"></action>
		<!--DICT工单信息-->
		<action name="dictOrderApi_*" class="DictOrderHttpAction" method="{1}"></action>
        <!--红名单-->
        <action name="redRollList_*" class="redRollListAction"  method="{1}"></action>
        <!--透明化接口-->
        <action name="IGrpOrderInfoAction_*" class="IGrpOrderInfoAction"  method="{1}"></action>
        <!--面对面支付-->
        <action name="PayProviderInfoAction!*" class="PayProviderInfoAction" method="{1}"></action>
        <action name="ValuableCardAction_*" class="ValuableCardAction" method="{1}"></action>
		<!--PC端API调用支付页面-->
		<action name="PayApiAction!*" class="PayApiAction" method="{1}"></action>
		<!--对外接口，根据电话查询用户-->
		<action name="ExternalInterfaceSrv_*" class="ExternalInterfaceAction" method="{1}"></action>
        <!--特殊支付计划-->
        <action name="SpeciaPlanAction_*" class="SpeciaPlanAction" method="{1}"></action>
        <action name="TariffManagementAction_*" class="TariffManagementAction" method="{1}"></action>
        <!--一键下单驳回统计-->
        <action name="jobRejectionInfoAction_*" class="JobRejectionInfoAction" method="{1}"></action>
        <!--风险控制闭环管理-->
        <action name="RiskClosedLoopSrc_*" class="RiskClosedLoopAction" method="{1}"></action>
        <!--政企白名单-->
        <action name="whiteRollList_*" class="com.xinxinsoft.action.whiteRollListAction.WhiteRollListAction" method="{1}"></action>
        <!--SIM物联网卡-->
        <action name="SIMAction_*" class="SIMAction" method="{1}"></action>
		<!--高风险业务申请管理-->
		<action name="appRiskDutyAction_*" class="appRiskDutyAction" method="{1}"></action>
		<action name="RiskDutyAction_*" class="RiskDutyAction" method="{1}"></action>
        <!--IMS 高频号码管理-->
        <action name="IMSHighFrequencyAction_*" class="com.xinxinsoft.action.IMSHighFrequencyAction.IMSHighFrequencyAction" method="{1}"></action>
        <!--欠费和销账-->
        <action name="ArrearsWriteOffAction_*" class="com.xinxinsoft.action.arrearsWriteOffAction.ArrearsWriteOffAction"  method="{1}"></action>
        <!--预开票打标管理-->
        <action name="PreinvApplyMarkingAction_*" class="com.xinxinsoft.action.preinvApplyMarkingAction.PreinvApplyMarkingAction" method="{1}"></action>
		<action name="queryTransparentInterfaceAction_*" class="queryTransparentInterfaceAction" method="{1}"></action>
        <!--营销活动-->
        <action name="MarketActivitiesAction_*"  class="com.xinxinsoft.action.MarketActivitiesAction.MarketActivitiesAction" method="{1}"/>
		<!--boss信息查询接口类-->
		<action name="AppUnitAccountAction_*" class="AppUnitAccountAction" method="{1}"></action>
		<action name="FilePreviewAction_*" class="FilePreviewAction" method="{1}"></action>
		<!--集团添加或更新-->
		<action name="GroupUpdateAction_*" class="GroupUpdateAction" method="{1}"></action>
        <action name="GroupCustomersAction_*" class="GroupCustomersAction" method="{1}"></action>
        <!--统付成员管理-->
        <action name="AllPayDesignAction_*" class="AllPayDesignAction" method="{1}"></action>
        <!--资金认领新版-->
        <action name="ClaimForFundAction_*" class="ClaimForFundAction" method="{1}"></action>
        <action name="ClaimForFundsAct_*" class="ClaimForFundsAct" method="{1}"></action>
		<action name="OmsOrderProductHttpAction_*" class="OmsOrderProductHttpAction" method="{1}"></action>
		<action name="AppServiceShutdownAndStartupAction_*" class="AppServiceShutdownAndStartupAction" method="{1}"></action>
        <!--正负补收额度管理-->
        <action name="ReceiptApplyAmountAction_*" class="ReceiptApplyAmountAction" method="{1}"></action>
        <!--统付成员管理-->
        <action name="AndFlySpeedAction_*" class="AndFlySpeedAction" method="{1}"></action>
		<action name="AppDocumentationAction_*" class="AppDocumentationAction" method="{1}"></action>
		<!--异常号码管理-->
        <action name="AbnormalNumberAction_*" class="AbnormalNumberAction" method="{1}"></action>
		<action name="AppCustomClauseContractAction_*" class="AppCustomClauseContractAction" method="{1}"></action>
        <!--集团实名预约-->
        <action name="realNameReservAtion_*" class="RealNameReservAtion" method="{1}"></action>
        <!--138附件管理-->
        <action name="AttachmentActionTwo_*" class="AttachmentActionTwo" method="{1}"></action>
        <!--合同签章角色配置-->
        <action name="missionConfigAction_*" class="missionConfigAction" method="{1}"></action>

        <!--营销活动工单管理-->
        <action name="MarketingActivitiesAction_*" class="MarketingActivitiesAction" method="{1}"></action>

        <!--终端活动工单管理-->
        <action name="TerminalActivityAction_*" class="TerminalActivityAction" method="{1}"></action>
        <!--app动态导入合同数据-->
        <action name="AppDynamicContractSrc_*" class="AppDynamicContractAction" method="{1}"></action>

        <!-- 合同补录-->
        <action name="mentaryContractAction_*" class="mentaryContractAction" method="{1}"></action>
        <action name="appMentaryContractAction_*" class="appMentaryContractAction" method="{1}"></action>
        <!-- 集团账户-->
        <action name="GroupAccountAction_*" class="groupAccountAction" method="{1}"></action>
        <action name="AppGroupAccountAction_*" class="appGroupAccountAction" method="{1}"></action>
        <!-- 系统保障管理类-->
        <action name="aquickAlarmAction_*" class="quickAlarmAction" method="{1}"></action>
        <!-- 问卷调查后台管理类-->
        <action name="questionnaireAction_*" class="questionnaireAction" method="{1}"></action>
        <action name="appQuestionnaireAction_*" class="appQuestionnaireAction" method="{1}"></action>
        <!--三方人员导入审批-->
        <action name="TripartiparteUserAction_*" class="TripartiparteUserAction" method="{1}"></action>

        <!--定时器触发-->
        <action name="trmerAction_*" class="trmerAction" method="{1}"></action>
        <!--APP账户（号码）校验方法类-->
        <action name="appVerificAction_*" class="appVerificAction" method="{1}"></action>

        <!--图片校验类-->
        <action name="callingSystemValidationAction_*" class="callingSystemValidationAction" method="{1}"></action>

        <!--集团报损-->
        <action name="groupReportedLossesAction_*" class="groupReportedLossesAction" method="{1}"></action>
    </package>
</struts>