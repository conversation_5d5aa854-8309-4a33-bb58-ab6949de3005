#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成XML配置文件差异的Excel汇总表
"""

import os
import xml.etree.ElementTree as ET
import pandas as pd
from collections import defaultdict

def parse_spring_context_simple(file_path):
    """简化解析Spring配置文件"""
    beans_info = {}
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # 解析bean定义
        for bean in root.findall('.//{http://www.springframework.org/schema/beans}bean'):
            bean_id = bean.get('id', '')
            bean_class = bean.get('class', '')
            if bean_id:
                properties = []
                for prop in bean.findall('.//{http://www.springframework.org/schema/beans}property'):
                    prop_name = prop.get('name', '')
                    prop_value = prop.get('value', '')
                    prop_ref = prop.get('ref', '')
                    if prop_name:
                        properties.append({
                            'name': prop_name,
                            'value': prop_value or prop_ref
                        })
                
                beans_info[bean_id] = {
                    'class': bean_class,
                    'properties': properties
                }
    
    except Exception as e:
        print(f"解析Spring配置文件 {file_path} 失败: {e}")
    
    return beans_info

def parse_struts_config_simple(file_path):
    """简化解析Struts配置文件"""
    actions_info = {}
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # 解析action
        for package in root.findall('.//package'):
            package_name = package.get('name', '')
            for action in package.findall('.//action'):
                action_name = action.get('name', '')
                action_class = action.get('class', '')
                action_method = action.get('method', '')
                
                if action_name:
                    key = f"{package_name}.{action_name}"
                    actions_info[key] = {
                        'package': package_name,
                        'name': action_name,
                        'class': action_class,
                        'method': action_method
                    }
    
    except Exception as e:
        print(f"解析Struts配置文件 {file_path} 失败: {e}")
    
    return actions_info

def analyze_bean_category(bean_id, bean_class):
    """分析Bean的业务分类"""
    if 'Action' in bean_class:
        if 'app' in bean_id.lower() or 'App' in bean_id:
            return 'APP接口'
        elif 'http' in bean_id.lower() or 'Http' in bean_id:
            return 'HTTP接口'
        elif 'pay' in bean_id.lower() or 'Pay' in bean_id:
            return '支付相关'
        elif 'contract' in bean_id.lower() or 'Contract' in bean_id:
            return '合同管理'
        elif 'oms' in bean_id.lower() or 'Oms' in bean_id:
            return '订单管理'
        else:
            return 'Action控制器'
    elif 'Service' in bean_class:
        return '业务服务'
    elif 'Manager' in bean_class:
        return '业务管理'
    elif 'DAO' in bean_class or 'Dao' in bean_class:
        return '数据访问'
    else:
        return '其他组件'

def generate_xml_diff_excel():
    """生成XML配置差异Excel文件"""
    
    config_85_dir = "config/85"
    config_86_dir = "config/86"
    
    xml_files = [
        'applicationContext-action.xml',
        'applicationContext-jpbm.xml', 
        'applicationContext-manager.xml',
        'struts.xml'
    ]
    
    all_differences = []
    file_summary = []
    
    for xml_file in xml_files:
        file1_path = os.path.join(config_85_dir, xml_file)
        file2_path = os.path.join(config_86_dir, xml_file)
        
        if not os.path.exists(file1_path) or not os.path.exists(file2_path):
            continue
        
        print(f"分析文件: {xml_file}")
        
        if xml_file == 'struts.xml':
            # Struts配置处理
            actions1 = parse_struts_config_simple(file1_path)
            actions2 = parse_struts_config_simple(file2_path)
            
            all_keys = set(actions1.keys()) | set(actions2.keys())
            
            only_85 = 0
            only_86 = 0
            different = 0
            
            for key in all_keys:
                if key in actions1 and key in actions2:
                    if actions1[key] != actions2[key]:
                        different += 1
                        all_differences.append({
                            '配置文件': xml_file,
                            '配置类型': 'Struts Action',
                            '配置项': key,
                            '差异类型': '配置不同',
                            '85环境': f"{actions1[key]['class']}.{actions1[key]['method']}",
                            '86环境': f"{actions2[key]['class']}.{actions2[key]['method']}",
                            '业务分类': 'Action映射',
                            '重要程度': '中'
                        })
                elif key in actions1:
                    only_85 += 1
                    all_differences.append({
                        '配置文件': xml_file,
                        '配置类型': 'Struts Action',
                        '配置项': key,
                        '差异类型': '仅在85中',
                        '85环境': f"{actions1[key]['class']}.{actions1[key]['method']}",
                        '86环境': '',
                        '业务分类': 'Action映射',
                        '重要程度': '高' if 'pay' in key.lower() or 'contract' in key.lower() else '中'
                    })
                else:
                    only_86 += 1
                    all_differences.append({
                        '配置文件': xml_file,
                        '配置类型': 'Struts Action',
                        '配置项': key,
                        '差异类型': '仅在86中',
                        '85环境': '',
                        '86环境': f"{actions2[key]['class']}.{actions2[key]['method']}",
                        '业务分类': 'Action映射',
                        '重要程度': '中'
                    })
            
            file_summary.append({
                '配置文件': xml_file,
                '文件类型': 'Struts配置',
                '总差异数': only_85 + only_86 + different,
                '仅在85中': only_85,
                '仅在86中': only_86,
                '配置不同': different,
                '主要差异': 'Action映射差异'
            })
        
        else:
            # Spring配置处理
            beans1 = parse_spring_context_simple(file1_path)
            beans2 = parse_spring_context_simple(file2_path)
            
            all_bean_ids = set(beans1.keys()) | set(beans2.keys())
            
            only_85 = 0
            only_86 = 0
            different = 0
            
            for bean_id in all_bean_ids:
                if bean_id in beans1 and bean_id in beans2:
                    bean1 = beans1[bean_id]
                    bean2 = beans2[bean_id]
                    
                    if (bean1['class'] != bean2['class'] or 
                        len(bean1['properties']) != len(bean2['properties'])):
                        different += 1
                        
                        # 分析属性差异
                        props1_names = {p['name'] for p in bean1['properties']}
                        props2_names = {p['name'] for p in bean2['properties']}
                        prop_diff = props1_names.symmetric_difference(props2_names)
                        
                        all_differences.append({
                            '配置文件': xml_file,
                            '配置类型': 'Spring Bean',
                            '配置项': bean_id,
                            '差异类型': '配置不同',
                            '85环境': f"{bean1['class']} ({len(bean1['properties'])}个属性)",
                            '86环境': f"{bean2['class']} ({len(bean2['properties'])}个属性)",
                            '业务分类': analyze_bean_category(bean_id, bean1['class']),
                            '重要程度': get_importance_level(bean_id, bean1['class'])
                        })
                
                elif bean_id in beans1:
                    only_85 += 1
                    bean1 = beans1[bean_id]
                    all_differences.append({
                        '配置文件': xml_file,
                        '配置类型': 'Spring Bean',
                        '配置项': bean_id,
                        '差异类型': '仅在85中',
                        '85环境': f"{bean1['class']} ({len(bean1['properties'])}个属性)",
                        '86环境': '',
                        '业务分类': analyze_bean_category(bean_id, bean1['class']),
                        '重要程度': get_importance_level(bean_id, bean1['class'])
                    })
                
                else:
                    only_86 += 1
                    bean2 = beans2[bean_id]
                    all_differences.append({
                        '配置文件': xml_file,
                        '配置类型': 'Spring Bean',
                        '配置项': bean_id,
                        '差异类型': '仅在86中',
                        '85环境': '',
                        '86环境': f"{bean2['class']} ({len(bean2['properties'])}个属性)",
                        '业务分类': analyze_bean_category(bean_id, bean2['class']),
                        '重要程度': get_importance_level(bean_id, bean2['class'])
                    })
            
            file_summary.append({
                '配置文件': xml_file,
                '文件类型': 'Spring配置',
                '总差异数': only_85 + only_86 + different,
                '仅在85中': only_85,
                '仅在86中': only_86,
                '配置不同': different,
                '主要差异': 'Bean配置差异'
            })
    
    # 创建DataFrame
    differences_df = pd.DataFrame(all_differences)
    summary_df = pd.DataFrame(file_summary)
    
    # 统计分析
    if not differences_df.empty:
        # 按业务分类统计
        category_stats = differences_df.groupby('业务分类').agg({
            '配置项': 'count',
            '重要程度': lambda x: (x == '高').sum()
        }).rename(columns={
            '配置项': '差异数量',
            '重要程度': '高重要性数量'
        }).reset_index()
        
        # 按差异类型统计
        type_stats = differences_df.groupby('差异类型').size().reset_index(name='数量')
        
        # 重要差异筛选
        important_diffs = differences_df[differences_df['重要程度'] == '高'].copy()
        
        # 按配置文件统计
        file_stats = differences_df.groupby('配置文件').agg({
            '配置项': 'count',
            '重要程度': lambda x: (x == '高').sum()
        }).rename(columns={
            '配置项': '差异数量',
            '重要程度': '高重要性数量'
        }).reset_index()
    else:
        category_stats = pd.DataFrame()
        type_stats = pd.DataFrame()
        important_diffs = pd.DataFrame()
        file_stats = pd.DataFrame()
    
    # 生成Excel文件
    output_file = 'EOM系统XML配置差异汇总表.xlsx'
    
    with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
        # 所有差异详情
        if not differences_df.empty:
            differences_df.to_excel(writer, sheet_name='所有差异详情', index=False)
        
        # 重要差异
        if not important_diffs.empty:
            important_diffs.to_excel(writer, sheet_name='重要差异', index=False)
        
        # 文件汇总
        summary_df.to_excel(writer, sheet_name='文件汇总', index=False)
        
        # 业务分类统计
        if not category_stats.empty:
            category_stats.to_excel(writer, sheet_name='业务分类统计', index=False)
        
        # 差异类型统计
        if not type_stats.empty:
            type_stats.to_excel(writer, sheet_name='差异类型统计', index=False)
        
        # 配置文件统计
        if not file_stats.empty:
            file_stats.to_excel(writer, sheet_name='配置文件统计', index=False)
    
    print(f"\n✅ XML配置差异Excel文件生成完成！")
    print(f"📄 文件名：{output_file}")
    print(f"📊 差异总数：{len(differences_df)} 个")
    print(f"📋 配置文件：{len(xml_files)} 个")
    
    if not differences_df.empty:
        print(f"\n📈 差异分布：")
        for _, row in type_stats.iterrows():
            print(f"  {row['差异类型']}: {row['数量']}个")
        
        print(f"\n🎯 业务分类分布：")
        for _, row in category_stats.iterrows():
            print(f"  {row['业务分类']}: {row['差异数量']}个 (高重要性: {row['高重要性数量']}个)")

def get_importance_level(bean_id, bean_class):
    """获取重要程度"""
    # 支付相关 - 高重要性
    if 'pay' in bean_id.lower() or 'Pay' in bean_id:
        return '高'
    
    # 合同相关 - 高重要性
    if 'contract' in bean_id.lower() or 'Contract' in bean_id:
        return '高'
    
    # 订单相关 - 高重要性
    if 'oms' in bean_id.lower() or 'Oms' in bean_id or 'order' in bean_id.lower():
        return '高'
    
    # HTTP接口 - 中重要性
    if 'http' in bean_id.lower() or 'Http' in bean_id:
        return '中'
    
    # APP接口 - 中重要性
    if 'app' in bean_id.lower() or 'App' in bean_id:
        return '中'
    
    # 其他 - 低重要性
    return '低'

if __name__ == "__main__":
    generate_xml_diff_excel()
