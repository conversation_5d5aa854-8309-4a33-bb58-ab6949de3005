#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细分析XML配置文件的差异
"""

import os
import xml.etree.ElementTree as ET
import difflib
from collections import defaultdict

def read_file_content(file_path):
    """读取文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return f.readlines()
    except UnicodeDecodeError:
        try:
            with open(file_path, 'r', encoding='gbk') as f:
                return f.readlines()
        except Exception as e:
            return [f"读取文件失败: {e}\n"]
    except Exception as e:
        return [f"读取文件失败: {e}\n"]

def parse_spring_context(file_path):
    """解析Spring配置文件"""
    beans_info = {}
    properties_info = {}
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # 解析bean定义
        for bean in root.findall('.//{http://www.springframework.org/schema/beans}bean'):
            bean_id = bean.get('id', '')
            bean_class = bean.get('class', '')
            if bean_id:
                beans_info[bean_id] = {
                    'class': bean_class,
                    'properties': [],
                    'constructor_args': []
                }
                
                # 解析属性
                for prop in bean.findall('.//{http://www.springframework.org/schema/beans}property'):
                    prop_name = prop.get('name', '')
                    prop_value = prop.get('value', '')
                    prop_ref = prop.get('ref', '')
                    beans_info[bean_id]['properties'].append({
                        'name': prop_name,
                        'value': prop_value,
                        'ref': prop_ref
                    })
                
                # 解析构造参数
                for arg in bean.findall('.//{http://www.springframework.org/schema/beans}constructor-arg'):
                    arg_value = arg.get('value', '')
                    arg_ref = arg.get('ref', '')
                    beans_info[bean_id]['constructor_args'].append({
                        'value': arg_value,
                        'ref': arg_ref
                    })
        
        # 解析属性文件引用
        for prop_placeholder in root.findall('.//{http://www.springframework.org/schema/context}property-placeholder'):
            location = prop_placeholder.get('location', '')
            if location:
                properties_info['property_files'] = properties_info.get('property_files', [])
                properties_info['property_files'].append(location)
    
    except Exception as e:
        print(f"解析Spring配置文件 {file_path} 失败: {e}")
    
    return beans_info, properties_info

def parse_struts_config(file_path):
    """解析Struts配置文件"""
    actions_info = {}
    packages_info = {}
    
    try:
        tree = ET.parse(file_path)
        root = tree.getroot()
        
        # 解析package
        for package in root.findall('.//package'):
            package_name = package.get('name', '')
            package_extends = package.get('extends', '')
            package_namespace = package.get('namespace', '')
            
            if package_name:
                packages_info[package_name] = {
                    'extends': package_extends,
                    'namespace': package_namespace,
                    'actions': []
                }
                
                # 解析action
                for action in package.findall('.//action'):
                    action_name = action.get('name', '')
                    action_class = action.get('class', '')
                    action_method = action.get('method', '')
                    
                    if action_name:
                        action_info = {
                            'name': action_name,
                            'class': action_class,
                            'method': action_method,
                            'results': []
                        }
                        
                        # 解析result
                        for result in action.findall('.//result'):
                            result_name = result.get('name', 'success')
                            result_type = result.get('type', '')
                            result_value = result.text or ''
                            
                            action_info['results'].append({
                                'name': result_name,
                                'type': result_type,
                                'value': result_value.strip()
                            })
                        
                        actions_info[f"{package_name}.{action_name}"] = action_info
                        packages_info[package_name]['actions'].append(action_name)
    
    except Exception as e:
        print(f"解析Struts配置文件 {file_path} 失败: {e}")
    
    return actions_info, packages_info

def compare_spring_configs(file1_path, file2_path):
    """对比Spring配置文件"""
    beans1, props1 = parse_spring_context(file1_path)
    beans2, props2 = parse_spring_context(file2_path)
    
    differences = {
        'only_in_85': {},
        'only_in_86': {},
        'different_beans': {},
        'same_beans': {}
    }
    
    all_bean_ids = set(beans1.keys()) | set(beans2.keys())
    
    for bean_id in all_bean_ids:
        if bean_id in beans1 and bean_id in beans2:
            bean1 = beans1[bean_id]
            bean2 = beans2[bean_id]
            
            # 比较bean的class和属性
            if (bean1['class'] != bean2['class'] or 
                bean1['properties'] != bean2['properties'] or
                bean1['constructor_args'] != bean2['constructor_args']):
                differences['different_beans'][bean_id] = {
                    '85': bean1,
                    '86': bean2
                }
            else:
                differences['same_beans'][bean_id] = bean1
        elif bean_id in beans1:
            differences['only_in_85'][bean_id] = beans1[bean_id]
        else:
            differences['only_in_86'][bean_id] = beans2[bean_id]
    
    return differences, props1, props2

def compare_struts_configs(file1_path, file2_path):
    """对比Struts配置文件"""
    actions1, packages1 = parse_struts_config(file1_path)
    actions2, packages2 = parse_struts_config(file2_path)
    
    differences = {
        'only_in_85': {},
        'only_in_86': {},
        'different_actions': {},
        'same_actions': {}
    }
    
    all_action_keys = set(actions1.keys()) | set(actions2.keys())
    
    for action_key in all_action_keys:
        if action_key in actions1 and action_key in actions2:
            action1 = actions1[action_key]
            action2 = actions2[action_key]
            
            if action1 != action2:
                differences['different_actions'][action_key] = {
                    '85': action1,
                    '86': action2
                }
            else:
                differences['same_actions'][action_key] = action1
        elif action_key in actions1:
            differences['only_in_85'][action_key] = actions1[action_key]
        else:
            differences['only_in_86'][action_key] = actions2[action_key]
    
    return differences, packages1, packages2

def analyze_xml_differences():
    """分析XML配置文件差异"""
    
    config_85_dir = "config/85"
    config_86_dir = "config/86"
    
    xml_files = [
        'applicationContext-action.xml',
        'applicationContext-jpbm.xml', 
        'applicationContext-manager.xml',
        'struts.xml'
    ]
    
    doc_content = []
    
    # 文档头部
    doc_content.extend([
        "# EOM系统XML配置文件详细差异分析",
        "",
        "## 📋 分析说明",
        "",
        "本报告详细分析了85和86环境中Spring和Struts配置文件的差异：",
        "- **Spring配置**: Bean定义、依赖注入、属性配置",
        "- **Struts配置**: Action映射、Result配置、Package定义",
        "",
        f"**分析文件数量**: {len(xml_files)} 个",
        "",
        "---",
        ""
    ])
    
    for xml_file in xml_files:
        file1_path = os.path.join(config_85_dir, xml_file)
        file2_path = os.path.join(config_86_dir, xml_file)
        
        if not os.path.exists(file1_path) or not os.path.exists(file2_path):
            doc_content.extend([
                f"## ❌ {xml_file}",
                "",
                "**状态**: 文件缺失",
                f"- 85目录: {'存在' if os.path.exists(file1_path) else '缺失'}",
                f"- 86目录: {'存在' if os.path.exists(file2_path) else '缺失'}",
                "",
                "---",
                ""
            ])
            continue
        
        print(f"分析文件: {xml_file}")
        
        doc_content.extend([
            f"## 🔍 {xml_file}",
            ""
        ])
        
        if xml_file == 'struts.xml':
            # Struts配置特殊处理
            differences, packages1, packages2 = compare_struts_configs(file1_path, file2_path)
            
            doc_content.extend([
                "**文件类型**: Struts2配置文件",
                f"**功能**: Action映射和结果配置",
                ""
            ])
            
            # 统计信息
            total_diffs = (len(differences['only_in_85']) + 
                          len(differences['only_in_86']) + 
                          len(differences['different_actions']))
            
            doc_content.extend([
                "### 📊 差异统计",
                "",
                f"- 仅在85中的Action: {len(differences['only_in_85'])} 个",
                f"- 仅在86中的Action: {len(differences['only_in_86'])} 个",
                f"- 配置不同的Action: {len(differences['different_actions'])} 个",
                f"- 相同的Action: {len(differences['same_actions'])} 个",
                f"- **总差异数**: {total_diffs} 个",
                ""
            ])
            
            # 详细差异
            if differences['only_in_85']:
                doc_content.extend([
                    "### 📌 仅在85环境中的Action",
                    "",
                    "| Package.Action | Class | Method | Results |",
                    "|----------------|-------|--------|---------|"
                ])
                for action_key, action_info in differences['only_in_85'].items():
                    results = ', '.join([r['name'] for r in action_info['results']])
                    doc_content.append(
                        f"| `{action_key}` | `{action_info['class']}` | `{action_info['method']}` | {results} |"
                    )
                doc_content.append("")
            
            if differences['only_in_86']:
                doc_content.extend([
                    "### 📌 仅在86环境中的Action",
                    "",
                    "| Package.Action | Class | Method | Results |",
                    "|----------------|-------|--------|---------|"
                ])
                for action_key, action_info in differences['only_in_86'].items():
                    results = ', '.join([r['name'] for r in action_info['results']])
                    doc_content.append(
                        f"| `{action_key}` | `{action_info['class']}` | `{action_info['method']}` | {results} |"
                    )
                doc_content.append("")
            
            if differences['different_actions']:
                doc_content.extend([
                    "### 🔄 配置不同的Action",
                    ""
                ])
                for action_key, action_diff in differences['different_actions'].items():
                    action_85 = action_diff['85']
                    action_86 = action_diff['86']
                    
                    doc_content.extend([
                        f"#### {action_key}",
                        "",
                        "| 属性 | 85环境 | 86环境 |",
                        "|------|--------|--------|",
                        f"| Class | `{action_85['class']}` | `{action_86['class']}` |",
                        f"| Method | `{action_85['method']}` | `{action_86['method']}` |"
                    ])
                    
                    # 比较Results
                    results_85 = {r['name']: r for r in action_85['results']}
                    results_86 = {r['name']: r for r in action_86['results']}
                    all_result_names = set(results_85.keys()) | set(results_86.keys())
                    
                    if all_result_names:
                        doc_content.extend([
                            "",
                            "**Results差异**:",
                            "",
                            "| Result名称 | 85环境 | 86环境 |",
                            "|------------|--------|--------|"
                        ])
                        for result_name in sorted(all_result_names):
                            r85 = results_85.get(result_name, {})
                            r86 = results_86.get(result_name, {})
                            r85_str = f"{r85.get('type', '')}:{r85.get('value', '')}" if r85 else "未配置"
                            r86_str = f"{r86.get('type', '')}:{r86.get('value', '')}" if r86 else "未配置"
                            doc_content.append(f"| {result_name} | {r85_str} | {r86_str} |")
                    
                    doc_content.append("")
        
        else:
            # Spring配置文件处理
            differences, props1, props2 = compare_spring_configs(file1_path, file2_path)
            
            doc_content.extend([
                "**文件类型**: Spring配置文件",
                f"**功能**: {get_spring_file_description(xml_file)}",
                ""
            ])
            
            # 统计信息
            total_diffs = (len(differences['only_in_85']) + 
                          len(differences['only_in_86']) + 
                          len(differences['different_beans']))
            
            doc_content.extend([
                "### 📊 差异统计",
                "",
                f"- 仅在85中的Bean: {len(differences['only_in_85'])} 个",
                f"- 仅在86中的Bean: {len(differences['only_in_86'])} 个",
                f"- 配置不同的Bean: {len(differences['different_beans'])} 个",
                f"- 相同的Bean: {len(differences['same_beans'])} 个",
                f"- **总差异数**: {total_diffs} 个",
                ""
            ])
            
            # 详细差异
            if differences['only_in_85']:
                doc_content.extend([
                    "### 📌 仅在85环境中的Bean",
                    "",
                    "| Bean ID | Class | 属性数量 | 说明 |",
                    "|---------|-------|----------|------|"
                ])
                for bean_id, bean_info in differences['only_in_85'].items():
                    prop_count = len(bean_info['properties'])
                    description = analyze_bean_purpose(bean_id, bean_info['class'])
                    doc_content.append(
                        f"| `{bean_id}` | `{bean_info['class']}` | {prop_count} | {description} |"
                    )
                doc_content.append("")
            
            if differences['only_in_86']:
                doc_content.extend([
                    "### 📌 仅在86环境中的Bean",
                    "",
                    "| Bean ID | Class | 属性数量 | 说明 |",
                    "|---------|-------|----------|------|"
                ])
                for bean_id, bean_info in differences['only_in_86'].items():
                    prop_count = len(bean_info['properties'])
                    description = analyze_bean_purpose(bean_id, bean_info['class'])
                    doc_content.append(
                        f"| `{bean_id}` | `{bean_info['class']}` | {prop_count} | {description} |"
                    )
                doc_content.append("")
            
            if differences['different_beans']:
                doc_content.extend([
                    "### 🔄 配置不同的Bean",
                    ""
                ])
                for bean_id, bean_diff in differences['different_beans'].items():
                    bean_85 = bean_diff['85']
                    bean_86 = bean_diff['86']
                    
                    doc_content.extend([
                        f"#### {bean_id}",
                        "",
                        "| 属性 | 85环境 | 86环境 |",
                        "|------|--------|--------|",
                        f"| Class | `{bean_85['class']}` | `{bean_86['class']}` |",
                        f"| 属性数量 | {len(bean_85['properties'])} | {len(bean_86['properties'])} |"
                    ])
                    
                    # 比较属性
                    props_85 = {p['name']: p for p in bean_85['properties']}
                    props_86 = {p['name']: p for p in bean_86['properties']}
                    all_prop_names = set(props_85.keys()) | set(props_86.keys())
                    
                    if all_prop_names:
                        doc_content.extend([
                            "",
                            "**属性差异**:",
                            "",
                            "| 属性名 | 85环境值 | 86环境值 |",
                            "|--------|----------|----------|"
                        ])
                        for prop_name in sorted(all_prop_names):
                            p85 = props_85.get(prop_name, {})
                            p86 = props_86.get(prop_name, {})
                            p85_val = p85.get('value') or p85.get('ref', '') if p85 else "未配置"
                            p86_val = p86.get('value') or p86.get('ref', '') if p86 else "未配置"
                            doc_content.append(f"| {prop_name} | {p85_val} | {p86_val} |")
                    
                    doc_content.append("")
        
        # 原始差异对比
        content1 = read_file_content(file1_path)
        content2 = read_file_content(file2_path)
        diff = list(difflib.unified_diff(
            content1, content2,
            fromfile=f"85/{xml_file}",
            tofile=f"86/{xml_file}",
            lineterm=''
        ))
        
        if diff:
            doc_content.extend([
                "### 📝 原始文件差异 (前30行)",
                "",
                "```diff"
            ])
            
            for i, line in enumerate(diff[:30]):
                doc_content.append(line.rstrip())
            
            if len(diff) > 30:
                doc_content.append(f"... (还有 {len(diff) - 30} 行差异)")
            
            doc_content.extend([
                "```",
                ""
            ])
        
        doc_content.extend(["---", ""])
    
    # 总结
    doc_content.extend([
        "## 📊 分析总结",
        "",
        "### 🎯 主要发现",
        "",
        "1. **配置完整性**: 85和86环境的XML配置存在显著差异",
        "2. **环境特化**: 不同环境针对特定需求进行了配置调整",
        "3. **功能差异**: 部分功能在不同环境中有不同的实现方式",
        "",
        "### 💡 建议",
        "",
        "1. **配置文档化**: 建议为每个环境的特殊配置编写说明文档",
        "2. **版本管理**: 建立XML配置文件的版本控制和变更追踪",
        "3. **自动化验证**: 开发配置文件的自动化验证工具",
        "4. **环境一致性**: 评估是否需要统一某些配置以提高维护效率",
        "",
        "---",
        "",
        "*本报告通过解析XML结构生成，提供了配置差异的详细分析。*"
    ])
    
    # 写入文档
    output_file = 'EOM系统XML配置文件详细差异分析.md'
    with open(output_file, 'w', encoding='utf-8') as f:
        f.write('\n'.join(doc_content))
    
    print(f"\n✅ XML配置文件差异分析完成！")
    print(f"📄 报告文件：{output_file}")
    print(f"📊 分析文件：{len(xml_files)} 个")

def get_spring_file_description(filename):
    """获取Spring配置文件的功能描述"""
    descriptions = {
        'applicationContext-action.xml': 'Action层Bean配置，包含Controller和Service',
        'applicationContext-jpbm.xml': '数据访问层配置，包含DataSource和DAO',
        'applicationContext-manager.xml': '业务管理层配置，包含Manager和Service'
    }
    return descriptions.get(filename, 'Spring配置文件')

def analyze_bean_purpose(bean_id, bean_class):
    """分析Bean的用途"""
    if 'Action' in bean_class:
        return 'Struts Action控制器'
    elif 'Service' in bean_class:
        return '业务服务层'
    elif 'Manager' in bean_class:
        return '业务管理层'
    elif 'DAO' in bean_class or 'Dao' in bean_class:
        return '数据访问层'
    elif 'DataSource' in bean_class:
        return '数据源配置'
    elif 'Transaction' in bean_class:
        return '事务管理'
    elif 'Interceptor' in bean_class:
        return '拦截器'
    elif 'Filter' in bean_class:
        return '过滤器'
    else:
        return '配置组件'

if __name__ == "__main__":
    analyze_xml_differences()
