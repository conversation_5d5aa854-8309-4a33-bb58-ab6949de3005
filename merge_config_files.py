#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
合并85和86两套配置文件到merge目录
"""

import os
import shutil
import xml.etree.ElementTree as ET
from xml.dom import minidom
import re

def create_merge_directory():
    """创建merge目录"""
    merge_dir = "config/merge"
    if os.path.exists(merge_dir):
        shutil.rmtree(merge_dir)
    os.makedirs(merge_dir, exist_ok=True)
    return merge_dir

def merge_properties_files(file1_path, file2_path, output_path):
    """合并properties文件"""
    props1 = {}
    props2 = {}
    comments1 = {}
    comments2 = {}
    
    # 读取第一个文件
    try:
        with open(file1_path, 'r', encoding='utf-8') as f:
            lines1 = f.readlines()
        
        current_comment = []
        for line in lines1:
            line = line.rstrip()
            if line.startswith('#') or line.startswith('!'):
                current_comment.append(line)
            elif '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                props1[key] = value.strip()
                if current_comment:
                    comments1[key] = '\n'.join(current_comment)
                    current_comment = []
            else:
                current_comment = []
    except Exception as e:
        print(f"读取文件 {file1_path} 失败: {e}")
    
    # 读取第二个文件
    try:
        with open(file2_path, 'r', encoding='utf-8') as f:
            lines2 = f.readlines()
        
        current_comment = []
        for line in lines2:
            line = line.rstrip()
            if line.startswith('#') or line.startswith('!'):
                current_comment.append(line)
            elif '=' in line and not line.startswith('#'):
                key, value = line.split('=', 1)
                key = key.strip()
                props2[key] = value.strip()
                if current_comment:
                    comments2[key] = '\n'.join(current_comment)
                    current_comment = []
            else:
                current_comment = []
    except Exception as e:
        print(f"读取文件 {file2_path} 失败: {e}")
    
    # 合并配置
    all_keys = set(props1.keys()) | set(props2.keys())
    
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write("# EOM系统配置文件 - 合并版本\n")
        f.write("# 此文件由85和86环境配置合并生成\n")
        f.write("# 生成时间: 自动生成\n\n")
        
        for key in sorted(all_keys):
            # 写入注释
            if key in comments1:
                f.write(f"{comments1[key]}\n")
            elif key in comments2:
                f.write(f"{comments2[key]}\n")
            
            # 写入差异说明
            if key in props1 and key in props2:
                if props1[key] != props2[key]:
                    f.write(f"# 差异说明: 85环境={props1[key]}, 86环境={props2[key]}\n")
                    # 选择更完整的值（通常是85环境）
                    value = props1[key] if len(props1[key]) >= len(props2[key]) else props2[key]
                    f.write(f"{key}={value}\n")
                else:
                    f.write(f"{key}={props1[key]}\n")
            elif key in props1:
                f.write(f"# 仅在85环境中存在\n")
                f.write(f"{key}={props1[key]}\n")
            else:
                f.write(f"# 仅在86环境中存在\n")
                f.write(f"{key}={props2[key]}\n")
            
            f.write("\n")

def parse_xml_with_comments(file_path):
    """解析XML文件并保留注释信息"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 使用minidom解析以保留格式
        dom = minidom.parseString(content)
        return dom, content
    except Exception as e:
        print(f"解析XML文件 {file_path} 失败: {e}")
        return None, None

def get_element_key(element):
    """获取元素的唯一标识"""
    if element.tagName == 'bean':
        return f"bean#{element.getAttribute('id')}"
    elif element.tagName == 'action':
        return f"action#{element.getAttribute('name')}"
    elif element.tagName == 'package':
        return f"package#{element.getAttribute('name')}"
    elif element.tagName == 'property':
        return f"property#{element.getAttribute('name')}"
    else:
        return f"{element.tagName}#{element.getAttribute('name') or element.getAttribute('id')}"

def count_element_attributes(element):
    """统计元素的属性和子元素数量"""
    attr_count = len(element.attributes.items()) if element.attributes else 0
    child_count = len([child for child in element.childNodes if child.nodeType == child.ELEMENT_NODE])
    return attr_count + child_count

def merge_xml_files(file1_path, file2_path, output_path):
    """合并XML文件"""
    dom1, content1 = parse_xml_with_comments(file1_path)
    dom2, content2 = parse_xml_with_comments(file2_path)
    
    if not dom1 or not dom2:
        # 如果其中一个文件解析失败，复制较大的文件
        if os.path.exists(file1_path) and os.path.exists(file2_path):
            size1 = os.path.getsize(file1_path)
            size2 = os.path.getsize(file2_path)
            source_file = file1_path if size1 >= size2 else file2_path
            shutil.copy2(source_file, output_path)
        return
    
    # 获取根元素
    root1 = dom1.documentElement
    root2 = dom2.documentElement
    
    # 创建新的DOM
    merged_dom = minidom.Document()
    merged_root = merged_dom.createElement(root1.tagName)
    
    # 复制根元素的属性
    for attr_name, attr_value in root1.attributes.items():
        merged_root.setAttribute(attr_name, attr_value)
    
    merged_dom.appendChild(merged_root)
    
    # 添加合并说明注释
    comment = merged_dom.createComment(
        " EOM系统配置文件 - 合并版本\n"
        "     此文件由85和86环境配置合并生成\n"
        "     合并规则: 以属性多的配置为准\n"
        "     生成时间: 自动生成 "
    )
    merged_root.appendChild(comment)
    merged_root.appendChild(merged_dom.createTextNode("\n    "))
    
    # 收集两个文件中的所有元素
    elements1 = {}
    elements2 = {}
    
    def collect_elements(root, elements_dict):
        for child in root.childNodes:
            if child.nodeType == child.ELEMENT_NODE:
                key = get_element_key(child)
                elements_dict[key] = child
    
    collect_elements(root1, elements1)
    collect_elements(root2, elements2)
    
    # 合并元素
    all_keys = set(elements1.keys()) | set(elements2.keys())
    
    for key in sorted(all_keys):
        if key in elements1 and key in elements2:
            elem1 = elements1[key]
            elem2 = elements2[key]
            
            # 比较元素复杂度
            count1 = count_element_attributes(elem1)
            count2 = count_element_attributes(elem2)
            
            if count1 != count2:
                # 添加差异说明注释
                diff_comment = merged_dom.createComment(
                    f" 差异说明: {key} - 85环境({count1}个属性/子元素), 86环境({count2}个属性/子元素) "
                )
                merged_root.appendChild(diff_comment)
                merged_root.appendChild(merged_dom.createTextNode("\n    "))
            
            # 选择属性更多的元素
            selected_elem = elem1 if count1 >= count2 else elem2
            env_label = "85环境" if count1 >= count2 else "86环境"
            
            if count1 != count2:
                env_comment = merged_dom.createComment(f" 采用{env_label}配置 ")
                merged_root.appendChild(env_comment)
                merged_root.appendChild(merged_dom.createTextNode("\n    "))
            
            # 克隆选中的元素
            cloned_elem = selected_elem.cloneNode(True)
            merged_root.appendChild(cloned_elem)
            merged_root.appendChild(merged_dom.createTextNode("\n    "))
        
        elif key in elements1:
            # 仅在85中存在
            only_85_comment = merged_dom.createComment(f" 仅在85环境中存在: {key} ")
            merged_root.appendChild(only_85_comment)
            merged_root.appendChild(merged_dom.createTextNode("\n    "))
            
            cloned_elem = elements1[key].cloneNode(True)
            merged_root.appendChild(cloned_elem)
            merged_root.appendChild(merged_dom.createTextNode("\n    "))
        
        else:
            # 仅在86中存在
            only_86_comment = merged_dom.createComment(f" 仅在86环境中存在: {key} ")
            merged_root.appendChild(only_86_comment)
            merged_root.appendChild(merged_dom.createTextNode("\n    "))
            
            cloned_elem = elements2[key].cloneNode(True)
            merged_root.appendChild(cloned_elem)
            merged_root.appendChild(merged_dom.createTextNode("\n    "))
    
    # 写入合并后的XML文件
    try:
        with open(output_path, 'w', encoding='utf-8') as f:
            # 格式化输出
            xml_str = merged_dom.toprettyxml(indent="    ", encoding=None)
            # 移除空行
            lines = [line for line in xml_str.split('\n') if line.strip()]
            f.write('\n'.join(lines))
    except Exception as e:
        print(f"写入XML文件 {output_path} 失败: {e}")

def merge_configurations():
    """合并配置文件"""
    
    config_85_dir = "config/85"
    config_86_dir = "config/86"
    merge_dir = create_merge_directory()
    
    # 配置文件列表
    config_files = [
        'FtpConfig.properties',
        'WebService-config.properties',
        'applicationContext-action.xml',
        'applicationContext-jpbm.xml',
        'applicationContext-manager.xml',
        'struts.xml'
    ]
    
    print(f"开始合并配置文件到 {merge_dir} 目录...")
    
    merge_summary = []
    
    for config_file in config_files:
        file1_path = os.path.join(config_85_dir, config_file)
        file2_path = os.path.join(config_86_dir, config_file)
        output_path = os.path.join(merge_dir, config_file)
        
        if not os.path.exists(file1_path) and not os.path.exists(file2_path):
            print(f"⚠️  {config_file}: 两个环境都不存在此文件")
            continue
        elif not os.path.exists(file1_path):
            print(f"📋 {config_file}: 仅86环境存在，直接复制")
            shutil.copy2(file2_path, output_path)
            merge_summary.append({
                'file': config_file,
                'status': '仅86环境存在',
                'action': '直接复制'
            })
        elif not os.path.exists(file2_path):
            print(f"📋 {config_file}: 仅85环境存在，直接复制")
            shutil.copy2(file1_path, output_path)
            merge_summary.append({
                'file': config_file,
                'status': '仅85环境存在',
                'action': '直接复制'
            })
        else:
            print(f"🔄 {config_file}: 合并两个环境的配置")
            
            if config_file.endswith('.properties'):
                merge_properties_files(file1_path, file2_path, output_path)
                merge_summary.append({
                    'file': config_file,
                    'status': '两环境都存在',
                    'action': 'Properties合并'
                })
            elif config_file.endswith('.xml'):
                merge_xml_files(file1_path, file2_path, output_path)
                merge_summary.append({
                    'file': config_file,
                    'status': '两环境都存在',
                    'action': 'XML合并(以属性多的为准)'
                })
            else:
                # 其他文件类型，选择较大的文件
                size1 = os.path.getsize(file1_path)
                size2 = os.path.getsize(file2_path)
                source_file = file1_path if size1 >= size2 else file2_path
                env = "85" if size1 >= size2 else "86"
                shutil.copy2(source_file, output_path)
                merge_summary.append({
                    'file': config_file,
                    'status': '两环境都存在',
                    'action': f'选择{env}环境(文件更大)'
                })
    
    # 生成合并报告
    generate_merge_report(merge_dir, merge_summary)
    
    print(f"\n✅ 配置文件合并完成！")
    print(f"📁 输出目录: {merge_dir}")
    print(f"📄 合并文件: {len(merge_summary)} 个")

def generate_merge_report(merge_dir, merge_summary):
    """生成合并报告"""
    report_path = os.path.join(merge_dir, "合并报告.md")
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("# EOM配置文件合并报告\n\n")
        f.write("## 📋 合并说明\n\n")
        f.write("本目录包含了85和86环境配置文件的合并结果：\n\n")
        f.write("### 🔧 合并规则\n\n")
        f.write("1. **Properties文件**: 合并所有配置项，差异项以85环境为准\n")
        f.write("2. **XML文件**: 以属性和子元素更多的配置为准\n")
        f.write("3. **差异标注**: 在配置上方添加注释说明差异\n")
        f.write("4. **环境标识**: 标明配置来源环境\n\n")
        f.write("### 📊 合并结果\n\n")
        f.write("| 配置文件 | 状态 | 处理方式 |\n")
        f.write("|----------|------|----------|\n")
        
        for item in merge_summary:
            f.write(f"| {item['file']} | {item['status']} | {item['action']} |\n")
        
        f.write(f"\n### 📁 文件清单\n\n")
        f.write("合并后的配置文件：\n\n")
        
        for item in merge_summary:
            f.write(f"- **{item['file']}**: {item['action']}\n")
        
        f.write(f"\n### 💡 使用建议\n\n")
        f.write("1. **部署前验证**: 部署前请仔细检查合并后的配置\n")
        f.write("2. **环境适配**: 根据目标环境调整相关配置\n")
        f.write("3. **测试验证**: 在测试环境验证配置的正确性\n")
        f.write("4. **备份原配置**: 保留原始配置文件作为备份\n\n")
        f.write("---\n\n")
        f.write("*此报告由自动化脚本生成*\n")

if __name__ == "__main__":
    merge_configurations()
