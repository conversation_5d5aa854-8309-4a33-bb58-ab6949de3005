# EOMç³»ç»éç½®æä»¶ - è¯¦ç»åå¹¶çæ¬
# æ­¤æä»¶ç±85å86ç¯å¢éç½®åå¹¶çæ
# åå¹¶è§å: ä¿çææéç½®ï¼è¯¦ç»æ æ³¨å·®å¼
# çææ¶é´: èªå¨çæ

# ä¸¤ç¯å¢éç½®ä¸è´
ACCT=16|78|48|-8|-48|-122|108|44|-64|22|-49|50|-1|-5|86|-64|89

#å·¥ä½æ¥å°æ¶æ°
# ä¸¤ç¯å¢éç½®ä¸è´
ADD_HOURS=8

##################################################################################################
# ä¸¤ç¯å¢éç½®ä¸è´
ARREARS_UPLOAD=/EOMAPP/UploadFiles/QFDATA/

#####################################################å®¡è®¡æ¥å£:æ¨¡åstart########################################################################
#å®¡è®¡æ¥å£å¼å³:éä»¶   start è¡¨ç¤ºå¼å¯ï¼ close  è¡¨ç¤ºå³é­
# ä¸¤ç¯å¢éç½®ä¸è´
AUDIT_INTERS_FJ_SWITCH=close

#å®¡è®¡æ¥å£å¼å³:ç»é   start è¡¨ç¤ºå¼å¯ï¼ close  è¡¨ç¤ºå³é­
# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: AUDIT_INTERS_LOGIN_SWITCH
# 85ç¯å¢å¼: close
# 86ç¯å¢å¼: start
# å·®å¼åæ: åè½å¼å³å·®å¼ï¼85ç¯å¢=closeï¼86ç¯å¢=start
# ================================
# å½åéç¨85ç¯å¢éç½®
AUDIT_INTERS_LOGIN_SWITCH=close
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# AUDIT_INTERS_LOGIN_SWITCH=start

#å®¡è®¡æ¥å£å¼å³:æ¶æ¯   start è¡¨ç¤ºå¼å¯ï¼ close  è¡¨ç¤ºå³é­
# ä¸¤ç¯å¢éç½®ä¸è´
AUDIT_INTERS_MESS_SWITCH=close

#å®¡è®¡æ¥å£å¼å³:è®¢å   start è¡¨ç¤ºå¼å¯ï¼ close  è¡¨ç¤ºå³é­
# ä¸¤ç¯å¢éç½®ä¸è´
AUDIT_INTERS_ORDER_SWITCH=close

#å®¡è®¡æ¥å£å¼å³:è§è²   start è¡¨ç¤ºå¼å¯ï¼ close  è¡¨ç¤ºå³é­
# ä¸¤ç¯å¢éç½®ä¸è´
AUDIT_INTERS_ROLE_SWITCH=close

#å®¡è®¡æ¥å£å¼å³:ç¥è¯åº   start è¡¨ç¤ºå¼å¯ï¼ close  è¡¨ç¤ºå³é­
# ä¸¤ç¯å¢éç½®ä¸è´
AUDIT_INTERS_ZSK_SWITCH=close

#######################################æ´»å¨ææ¡£ä¿å­å°å###############################################
# ä¸¤ç¯å¢éç½®ä¸è´
AVTIVITY_DOWNLOAD=/EOMAPP/UploadFiles/EnjoyFiles/

#BOSSå®æ¶æ¥è¯¢é¢åçè®¢åå¼å³ startè¡¨ç¤ºå¼å¯  closeè¡¨ç¤ºå³é­
# ä¸¤ç¯å¢éç½®ä¸è´
BOSSSwitch=start

#é¢åçä¸å¡åèµ·æ¥å£
# ä¸¤ç¯å¢éç½®ä¸è´
BOSSTargetNamespace=http://ws.sitech.com

# ä¸¤ç¯å¢éç½®ä¸è´
BO_GET_SERVER_ADDRESS=http://*************:8090/ecmServer?wsdl

######################################ååæ¥å£æ°æ®åå¥å°å################################################
# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: CONTRACT_ADDERSS
# 85ç¯å¢å¼: http://*************:8080/OrderSysIntoContract/Contract_Info_Recevie_srv.svc?wsdl
# 86ç¯å¢å¼: http://*************:8080/OrderSysIntoContract/Contract_Info_Recevie_srv.svc?wsdl
# å·®å¼åæ: æå¡å¨IPå°åå·®å¼ï¼85ç¯å¢æå.85æå¡å¨ï¼86ç¯å¢æå.86æå¡å¨
# ================================
# å½åéç¨85ç¯å¢éç½®
CONTRACT_ADDERSS=http://*************:8080/OrderSysIntoContract/Contract_Info_Recevie_srv.svc?wsdl
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# CONTRACT_ADDERSS=http://*************:8080/OrderSysIntoContract/Contract_Info_Recevie_srv.svc?wsdl

######################################ååæ¥å£æ°æ®æ´æ¹å°å################################################
# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: CONTRACT_UPDATEADDERSS
# 85ç¯å¢å¼: http://*************:8080/OrderSysIntoContract/Contract_Status_Change_srv.svc?wsdl
# 86ç¯å¢å¼: http://*************:8080/OrderSysIntoContract/Contract_Status_Change_srv.svc?wsdl
# å·®å¼åæ: æå¡å¨IPå°åå·®å¼ï¼85ç¯å¢æå.85æå¡å¨ï¼86ç¯å¢æå.86æå¡å¨
# ================================
# å½åéç¨85ç¯å¢éç½®
CONTRACT_UPDATEADDERSS=http://*************:8080/OrderSysIntoContract/Contract_Status_Change_srv.svc?wsdl
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# CONTRACT_UPDATEADDERSS=http://*************:8080/OrderSysIntoContract/Contract_Status_Change_srv.svc?wsdl

#æå¼/å³é­èåæ¥Task:0å³é­,1å¼å¯
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨86ç¯å¢ä¸­å­å¨: CONTROL_HOLIDAYTASK_STATE
# éç½®å¼: 0
# ç¨éåæ: ä¸å¡éç½®
# ===================================
# CONTROL_HOLIDAYTASK_STATE=0
# æ³¨æ: æ­¤éç½®ä»å¨86ç¯å¢ä¸­å­å¨ï¼é»è®¤æ³¨é

#EIPæ³¨åçåºç¨Code
# ä¸¤ç¯å¢éç½®ä¸è´
CurRegAppCode=7DF33169-98B0-4B4E-89F7-999A9D582C30

#EIPè®¢ååæ­¥å¼å³    startè¡¨ç¤ºå¼å¯  closeè¡¨ç¤ºå³é­
# ä¸¤ç¯å¢éç½®ä¸è´
EIPOrderSwitch=start

##http://**************:8200/CMS/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv.v0/proxy/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv
#######################################ESBæ¥å£å°å###############################################
# ä¸¤ç¯å¢éç½®ä¸è´
ESB_URL=http://*************:51000/esbWS/rest/

# ä¸¤ç¯å¢éç½®ä¸è´
EXPECTEDNUMBEROFDAYS=1

# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: HONOR_UPLOAD
# 85ç¯å¢å¼: /EOMAPP/UploadFiles/EnjoyFiles/
# 86ç¯å¢å¼: 
# å·®å¼åæ: éç½®å¼ä¸åï¼å¯è½æ¯ç¯å¢ç¹å®éç½®
# ================================
# å½åéç¨85ç¯å¢éç½®
HONOR_UPLOAD=/EOMAPP/UploadFiles/EnjoyFiles/
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# HONOR_UPLOAD=

#####################################################å®¡è®¡æ¥å£:æ¨¡åend########################################################################
# ä¸¤ç¯å¢éç½®ä¸è´
LV_LOGIN_ADDRESS=http://**************:8080/cscwf/LoginNewServlet

#LV_SERVER_ADDRESS=**************
#REQ_SERVER_ADDRESS=http://*************:26001/gather/services/AioxPort?wsdl
# ä¸¤ç¯å¢éç½®ä¸è´
LV_SERVER_ADDRESS=**************

#åæ·å·
# ä¸¤ç¯å¢éç½®ä¸è´
MERCHANT=5000001

##http://**************:8200/CMS/OSB_CMS_CMS_HQ_ImportRevenueContractDraftSrv.v0/proxy/OSB_CMS_CMS_HQ_ImportRevenueContractDraftSrv
#æ¥è¯¢æ¶å¥ç±»OSB_CMS_CMS_HQ_00013
##http://**************:8100/CMS/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv.v0/proxy/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv
# ä¸¤ç¯å¢éç½®ä¸è´
OSB_CMS_CMS_HQ_00013=http://**************:8100/CMS/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv.v0/proxy/OSB_CMS_CMS_HQ_PageInquiryRevenueContractSrv

#å¯¼å¥æ¶å¥ç±»OSB_CMS_CMS_HQ_00023
## http://**************:8100/CMS/OSB_CMS_CMS_HQ_ImportRevenueContractDraftSrv.v0/proxy/OSB_CMS_CMS_HQ_ImportRevenueContractDraftSrv
# ä¸¤ç¯å¢éç½®ä¸è´
OSB_CMS_CMS_HQ_00023=http://**************:8100/CMS/OSB_CMS_CMS_HQ_ImportRevenueContractDraftSrv.v0/proxy/OSB_CMS_CMS_HQ_ImportRevenueContractDraftSrv

##################################################################################################
#######################################é¾æåè­¦å¤©æ°###############################################
# ä¸¤ç¯å¢éç½®ä¸è´
OVERDUE_DAY=0

#å å¯KEY
# ä¸¤ç¯å¢éç½®ä¸è´
PAYKEY=2141BE739A86BF5BEAE0F3CB2C0C81E4

###########################################ç»ä¸è®¢åæ¯ä»å¹³å°å°åååæ°ä¿¡æ¯###################################
#æ¥å£å°å
# ä¸¤ç¯å¢éç½®ä¸è´
PAYMENTPROVIDERURL=http\://10.109.139.181\:8999/

#æ¯ä»åå°éç¥å°å
# ä¸¤ç¯å¢éç½®ä¸è´
PAY_NOTIFY_URL=http://*************:8080/EOM/PaymentOrder_savePaymentOrder.action

#æ¯ä»åå°éç¥å°å
# ä¸¤ç¯å¢éç½®ä¸è´
PAY_RET_URL=www.baidu.com

#######################################ç¨½æ ¸æä»¶å¯¼å¥å°å###############################################
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: PREINVAPPLY_RISKDATA
# éç½®å¼: /EOMAPP/UploadFiles/AuditWorkFile/
# ç¨éåæ: ä¸å¡éç½®
# ===================================
PREINVAPPLY_RISKDATA=/EOMAPP/UploadFiles/AuditWorkFile/

# ä¸¤ç¯å¢éç½®ä¸è´
PREINVAPPLY_UPLOAD=/EOMAPP/UploadFiles/PreinvData/

# ä¸¤ç¯å¢éç½®ä¸è´
PWD=8|6|-96|-76|80|-63|43|-118|66

# ä¸¤ç¯å¢éç½®ä¸è´
RECONCILIATION_UPLOAD=/EOMAPP/UploadFiles/PayResult/

#éæ¬¾åå°éç¥å°å
# ä¸¤ç¯å¢éç½®ä¸è´
REFUND_NOTIFY_URL=http://*************:8080/EOM/RefundOrder_saveRefundOrder.action

#éæ¬¾åå°éç¥å°å
# ä¸¤ç¯å¢éç½®ä¸è´
REFUND_RET_URL=www.baidu.com

#REQ_SERVER_ADDRESS=http://*************:21010/gather/services/AioxPort?wsdl
# ä¸¤ç¯å¢éç½®ä¸è´
REQ_SERVER_ADDRESS=http://*************:23003/gather/services/AioxPort?wsdl

# ä¸¤ç¯å¢éç½®ä¸è´
REQ_SERVER_QN=http://www.asiainfo.com/web/

# ä¸¤ç¯å¢éç½®ä¸è´
SENDMESSAGECOUNT=0

# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: SERVER_ADDRESS
# 85ç¯å¢å¼: *************
# 86ç¯å¢å¼: *************
# å·®å¼åæ: æå¡å¨IPå°åå·®å¼ï¼85ç¯å¢æå.85æå¡å¨ï¼86ç¯å¢æå.86æå¡å¨
# ================================
# å½åéç¨85ç¯å¢éç½®
SERVER_ADDRESS=*************
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# SERVER_ADDRESS=*************

#4Aå®å¼åæ°
# ä¸¤ç¯å¢éç½®ä¸è´
SERVICEID=SCNGDDXT

#æ¶é´æ³æ ¼å¼
# ä¸¤ç¯å¢éç½®ä¸è´
SSOKeyTimeValue=yyyyMMddHH

#ç»ææ¶é´å°æ¶(æ´æ°)
# ä¸¤ç¯å¢éç½®ä¸è´
WORK_END_HOUR=17

#ç»ææ¶é´åé(æ´æ°)
# ä¸¤ç¯å¢éç½®ä¸è´
WORK_END_MINUTE=30

#å¼å§æ¶é´(æ´æ°)
# ä¸¤ç¯å¢éç½®ä¸è´
WORK_START_HOUR=9

#å¼å§æ¶é´åé(æ´æ°)
# ä¸¤ç¯å¢éç½®ä¸è´
WORK_START_MINUTE=30

# ä¸¤ç¯å¢éç½®ä¸è´
appPushSoapWsdl=http://*************:8080/portal/ws/pushMsgService?wsdl

# ä¸¤ç¯å¢éç½®ä¸è´
checkAiuapTokenSoapTargetNamespace=http\://tempuri.org/

#4Aé¨æ·æ¥å¥å°å
# ä¸¤ç¯å¢éç½®ä¸è´
checkAiuapTokenSoapWsdl=http\://**************\:9081/uac/services/CheckAiuapTokenSoap?wsdl

#éå¢å®¢æ·ä¿¡æ¯æ¥è¯¢æ¥å£
# ä¸¤ç¯å¢éç½®ä¸è´
customerSoapWsdl=http://*************:8080/ecmServer?wsdl

#EIPæ¯æ¥è®¤è¯å¯¹
# ä¸¤ç¯å¢éç½®ä¸è´
eipGetDayCodeKeyTargetNamespace=http://eipsps.scmcc.com.cn/

# ä¸¤ç¯å¢éç½®ä¸è´
eipGetDayCodeKeyWsdl=http://************/EIP.SSOAppCenterServer.WebService/SSOAppService.asmx

#EIPç»ä¸å¾åå°å
# ä¸¤ç¯å¢éç½®ä¸è´
eipTaskTodoTargetNamespace=http\://tempuri.org/

# ä¸¤ç¯å¢éç½®ä¸è´
eipTaskTodoWsdl=http://************/EIP.SSOAppCenterServer.WebService/TaskTodo.asmx

#è´¦å·å¯ç éªè¯æ¥å£å°å
# ä¸¤ç¯å¢éç½®ä¸è´
mainAcctCheckSoapWsdl=http://**************:9081/uac/services/MainAcctCheckServices?wsdl&view=true

#æ¥è¯¢é¢åçç¶æ
# ä¸¤ç¯å¢éç½®ä¸è´
qryPreDealWsdl=http://*************:11800/services/qryPreDeal?wsdl

#targetNamespace
# ä¸¤ç¯å¢éç½®ä¸è´
sendMessageTargetNamespace=http\://tempuri.org/

#ç­ä¿¡ç½å³
#sendMessageWsdl
# ä¸¤ç¯å¢éç½®ä¸è´
sendMessageWsdl=http://*************/ReportEditService/ReportEditService.svc?wsdl

#ç­ä¿¡éªè¯ç éªè¯æ¥å£å°å
# ä¸¤ç¯å¢éç½®ä¸è´
smAuthenCheckSoapWsdl=http://**************:9081/uac/services/SmAuthenCheckServices?wsdl&view=true

# ä¸¤ç¯å¢éç½®ä¸è´
startPreOrderWsdl=http\://*************\:11800/services/startPreOrder?wsdl

#targetNamespace
# ä¸¤ç¯å¢éç½®ä¸è´
targetNamespace=http\://tempuri.org/

#MOAå¾åè·³è½¬çACTIONå°å
# ä¸¤ç¯å¢éç½®ä¸è´
taskMoaUrl=https://transorder.scmcc.com.cn:38080/EOM_WebServices/EOMAPP_DZG/html/risk_operation_new/middlePage.html?taskID=

#EIPå¾åè·³è½¬çACTIONå°å
# ä¸¤ç¯å¢éç½®ä¸è´
taskUrl=http://*************:8080/EOM/systemUserAction!EipTask.action?taskID=

#æ¥é
#wsdl=http://*************/FaMobileTaskApp/FinanceMobileTask.asmx
# ä¸¤ç¯å¢éç½®ä¸è´
wsdl=http://fa.scmcc.com.cn/FaMobileTaskApp/FinanceMobileTask.asmx

