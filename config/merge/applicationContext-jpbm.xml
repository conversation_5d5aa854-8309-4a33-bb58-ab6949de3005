<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jaxws="http://cxf.apache.org/jaxws"
	xsi:schemaLocation="
		    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		    http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
		    http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd
		    http://cxf.apache.org/jaxws  http://cxf.apache.org/schemas/jaxws.xsd"
	default-autowire="byName">

    <!-- EOM系统配置文件 - 详细合并版本 -->
    <!-- 此文件由85和86环境配置合并生成 -->
    <!-- 合并规则: 保留所有配置，详细标注差异 -->
    <!-- 生成时间: 自动生成 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BigAADecisionHandlerImpl -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BigAADecisionHandlerImpl" class="com.xinxinsoft.jpbm.AssignmentUtils.BigAADecisionHandlerImpl">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinssDecisionImpl -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinssDecisionImpl" class="com.xinxinsoft.jpbm.AssignmentUtils.BusinssDecisionImpl">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundsChangeImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundsChangeImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ClaimForFundsChangeImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundsImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundsImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ClaimForFundsImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DecisionImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DecisionImp" class="com.xinxinsoft.jpbm.AssignmentUtils.DecisionImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DecisionImpTwo -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DecisionImpTwo" class="com.xinxinsoft.jpbm.AssignmentUtils.DecisionImpTwo">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DecisionImpl -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DecisionImpl" class="com.xinxinsoft.jpbm.AssignmentUtils.DecisionImpl">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EndStartEvent -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EndStartEvent" class="com.xinxinsoft.service.appOpenService.jbpmEvent.EndStartEvent">
    <property name="noResApplyService">
    <ref bean="NoResApplyService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ICTApplicationImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ICTApplicationImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ICTApplicationImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#JBPMUtil -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="JBPMUtil" class="com.xinxinsoft.utils.JbpmUtil">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ManuallnvApplyImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ManuallnvApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ManuallnvApplyImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReceiptApplyImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReceiptApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ReceiptApplyImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReductionICTApplicationImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReductionICTApplicationImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ReductionICTApplicationImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#StartEndEvent -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="StartEndEvent" class="com.xinxinsoft.service.appOpenService.jbpmEvent.StartEndEvent">
    <property name="noResApplyService">
    <ref bean="NoResApplyService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SuspensionApplicationImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SuspensionApplicationImp" class="com.xinxinsoft.jpbm.AssignmentUtils.SuspensionApplicationImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TaskEndEvent -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TaskEndEvent" class="com.xinxinsoft.service.appOpenService.jbpmEvent.TaskEndEvent">
    <property name="noResApplyService">
    <ref bean="NoResApplyService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TaskStartEvent -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TaskStartEvent" class="com.xinxinsoft.service.appOpenService.jbpmEvent.TaskStartEvent">
    <property name="noResApplyService">
    <ref bean="NoResApplyService" />
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferImp" class="com.xinxinsoft.jpbm.AssignmentUtils.TransferImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferJBPMUtils -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferJBPMUtils" class="com.xinxinsoft.jpbm.AssignmentUtils.TransferJBPMUtils">
    <property name="jbpmUtil" ref="JBPMUtil"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ValuableCardImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ValuableCardImp" class="com.xinxinsoft.jpbm.AssignmentUtils.ValuableCardImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#processEngine -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="processEngine" factory-bean="springHelper" factory-method="createProcessEngine" />
    <!-- <bean id="repositoryService" factory-bean="processEngine" factory-method="getRepositoryService"/>
    <bean id="executionService" factory-bean="processEngine" factory-method="getExecutionService"/>
    <bean id="historyService" factory-bean="processEngine" factory-method="getHistoryService"/>
    <bean id="taskService" factory-bean="processEngine" factory-method="getTaskService"/>
    <bean id="identityService" factory-bean="processEngine" factory-method="getIdentityService"/>
    <bean id="managementService" factory-bean="processEngine" factory-method="getManagementService"/> -->
    <bean id="JBPMUtil" class="com.xinxinsoft.utils.JbpmUtil">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#receiveApplyImp -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="receiveApplyImp" class="com.xinxinsoft.jpbm.AssignmentUtils.receiveApplyImp">
    <property name="processEngine" ref="processEngine"></property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#springHelper -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="springHelper" class="org.jbpm.pvm.internal.processengine.SpringHelper">
    <property name="jbpmCfg" value="jbpm.cfg.xml" />
    </bean>

</beans>