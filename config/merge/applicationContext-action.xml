<?xml version="1.0" ?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx" xsi:schemaLocation="       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd" default-autowire="byName">
    <!-- EOM系统配置文件 - 合并版本
     此文件由85和86环境配置合并生成
     合并规则: 以属性多的配置为准
     生成时间: 自动生成 -->
    <bean id="APPStatisticsAction" class="com.xinxinsoft.action.appAction.APPStatisticsAction" scope="prototype">
        <property name="statisticsService">
            <ref bean="statisticsService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#APPTransferInformationAction -->
    <bean id="APPTransferInformationAction" class="com.xinxinsoft.action.appAction.AppTransferInformationAction" scope="prototype">
        <property name="tInformationService">
            <ref bean="TransferInformationService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="zService">
            <ref bean="ZtreeUserService"/>
        </property>
        <property name="tInformationTwoService">
            <ref bean="TransferInformationTwoService"/>
        </property>
    </bean>
    <bean id="AbnormalNumberAction" class="com.xinxinsoft.action.abnormalNumberAction.AbnormalNumberAction" scope="prototype"/>
    <!-- 仅在85环境中存在: bean#AccountOpenAction -->
    <bean id="AccountOpenAction" class="com.xinxinsoft.action.accountOpenAction.AccountOpenAction" scope="prototype">
        <property name="accountOpenService">
            <ref bean="accountOpenService"/>
        </property>
        <property name="unitInfoService">
            <ref bean="UnitInfoService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="AccountOrderInfoAction" class="com.xinxinsoft.action.pay.AccountOrderInfoAction" scope="prototype">
        <property name="accountOrderInfoService">
            <ref bean="AccountOrderInfoService"/>
        </property>
    </bean>
    <bean id="ActivityAction" class="com.xinxinsoft.action.honorCode.ActivityAction" scope="prototype">
        <property name="activityService">
            <ref bean="ActivityService"/>
        </property>
        <property name="channelInfoService">
            <ref bean="ChannelInfoService"/>
        </property>
        <property name="honorCodeBusinessService">
            <ref bean="HonorCodeBusinessService"/>
        </property>
    </bean>
    <bean id="AgreementAction" class="com.xinxinsoft.action.arrearsModule.AgreementAction" scope="prototype">
        <property name="agrService">
            <ref bean="AgreementService"/>
        </property>
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
        <property name="contractService">
            <ref bean="ContractService"/>
        </property>
    </bean>
    <bean id="AllPayDesignAction" class="com.xinxinsoft.action.AllMembersPayAction.AllPayDesignAction" scope="prototype"/>
    <bean id="AndFlySpeedAction" class="com.xinxinsoft.action.andFlySpeedAction.AndFlySpeedAction" scope="prototype"/>
    <!-- 仅在85环境中存在: bean#AppClaimForFundsAction -->
    <bean id="AppClaimForFundsAction" class="com.xinxinsoft.action.appAction.AppClaimForFundsAction" scope="prototype"/>
    <bean id="AppContractAction" class="com.xinxinsoft.action.appAction.AppContractAction" scope="prototype">
	</bean>
    <bean id="AppContractFacticityLogAction" class="com.xinxinsoft.action.appAction.AppContractFacticityLogAction" autowire="byType" scope="prototype">
	</bean>
    <!-- 仅在85环境中存在: bean#AppCustomClauseContractAction -->
    <bean id="AppCustomClauseContractAction" class="com.xinxinsoft.action.appAction.AppCustomClauseContractAction" autowire="byType" scope="prototype">
        <property name="customClauseContractService">
            <ref bean="CustomClauseContractService"/>
        </property>
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
        <property name="signService">
            <ref bean="SignService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#AppDocumentationAction -->
    <bean id="AppDocumentationAction" class="com.xinxinsoft.action.appAction.AppDocumentationAction" scope="prototype">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="AppDownloadAction" class="com.xinxinsoft.action.appAction.AppDownloadAction" scope="prototype">
	</bean>
    <!-- 仅在85环境中存在: bean#AppDynamicContractAction -->
    <bean id="AppDynamicContractAction" class="com.xinxinsoft.action.contract.AppDynamicContractAction" scope="prototype"/>
    <bean id="AppGetOrPostWebApiAction" class="com.xinxinsoft.action.appOpenAction.AppGetOrPostWebApiAction" scope="prototype">
        <property name="querySer">
            <ref bean="VariousSqlQueryService"/>
        </property>
        <property name="sysSer">
            <ref bean="SystemUserService"/>
        </property>
        <property name="singSer">
            <ref bean="commonSingleService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
    </bean>
    <!-- 差异说明: bean#AppGetOrPostWebQueryApiAction - 85环境(10个属性/子元素), 86环境(9个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="AppGetOrPostWebQueryApiAction" class="com.xinxinsoft.action.appOpenAction.AppGetOrPostWebQueryApiAction" scope="prototype">
        <property name="querySer">
            <ref bean="VariousSqlQueryService"/>
        </property>
        <property name="sysSer">
            <ref bean="SystemUserService"/>
        </property>
        <property name="singSer">
            <ref bean="commonSingleService"/>
        </property>
        <property name="conSer">
            <ref bean="ContractService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="customerService">
            <ref bean="customerServices"/>
        </property>
        <property name="dediSer">
            <ref bean="DedicatedFlowService"/>
        </property>
    </bean>
    <bean id="AppGroupInviteCodeAction" class="com.xinxinsoft.action.appAction.AppGroupInviteCodeAction" autowire="byType" scope="prototype"/>
    <bean id="AppHonorCodeBusinessAction" class="com.xinxinsoft.action.httpAction.AppHonorCodeBusinessAction" scope="prototype">
        <property name="honorCodeBusinessService">
            <ref bean="HonorCodeBusinessService"/>
        </property>
        <property name="channelInfoService">
            <ref bean="ChannelInfoService"/>
        </property>
        <property name="contractService">
            <ref bean="ContractService"/>
        </property>
        <property name="service">
            <ref bean="SystemUserService"/>
        </property>
        <property name="smsService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#AppMonthlyinvoice -->
    <bean id="AppMonthlyinvoice" class="com.xinxinsoft.action.appAction.AppMonthlyinvoice" scope="prototype">
        <property name="monthlyinvoiceService">
            <ref bean="MonthlyinvoiceService"/>
        </property>
        <property name="preinvApplyService">
            <ref bean="PreinvApplyService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#AppOmsSellOrderAction -->
    <bean id="AppOmsSellOrderAction" class="com.xinxinsoft.action.appAction.AppOmsSellOrderAction" scope="prototype">
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="integrationService">
            <ref bean="IntegrationService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="customClauseContractService">
            <ref bean="CustomClauseContractService"/>
        </property>
        <property name="claimForFundsService">
            <ref bean="ClaimForFundsService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#AppPreinvApplyAction -->
    <bean id="AppPreinvApplyAction" class="com.xinxinsoft.action.appAction.AppPreinvApplyAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="preinvApplyService">
            <ref bean="PreinvApplyService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#AppReceiveApplyAction -->
    <bean id="AppReceiveApplyAction" class="com.xinxinsoft.action.appAction.AppReceiveApplyAction" scope="prototype">
        <property name="receiveApplyService">
            <ref bean="ReceiveApplyService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#AppServiceShutdownAndStartupAction -->
    <bean id="AppServiceShutdownAndStartupAction" class="com.xinxinsoft.action.appAction.AppServiceShutdownAndStartupAction" scope="prototype"/>
    <bean id="AppUnitAccountAction" class="com.xinxinsoft.action.appAction.AppUnitAccountAction" scope="prototype"/>
    <bean id="AppUploadAction" class="com.xinxinsoft.action.core.app.AppUploadAction" scope="prototype">
        <property name="appUploadService">
            <ref bean="AppUploadService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="editionService">
            <ref bean="EditionService"/>
        </property>
    </bean>
    <bean id="AppUploadPassword" class="com.xinxinsoft.action.appAction.AppUploadPassword" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="smsPushService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <bean id="AppUserHeadAction" class="com.xinxinsoft.action.core.app.AppUserHeadAction" scope="prototype">
        <property name="appUserInfoService">
            <ref bean="AppUserInfoService"/>
        </property>
    </bean>
    <bean id="AppWorkOrderAction" class="com.xinxinsoft.action.appAction.AppWorkOrderAction" scope="prototype">
        <property name="workOrderServcie">
            <ref bean="WorkOrderServcie"/>
        </property>
    </bean>
    <!-- 差异说明: bean#ArrearsAction - 85环境(5个属性/子元素), 86环境(4个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="ArrearsAction" class="com.xinxinsoft.action.arrearsModule.ArrearsAction" scope="prototype">
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
        <property name="psService">
            <ref bean="PaymentRecordService"/>
        </property>
    </bean>
    <bean id="ArrearsSingAction" class="com.xinxinsoft.action.arrearsModule.ArrearsSingAction" scope="prototype">
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
        <property name="arrearsSingSerivce">
            <ref bean="ArrearsSingSerivce"/>
        </property>
    </bean>
    <bean id="ArrearsStatisticalAction" class="com.xinxinsoft.action.arrearsModule.ArrearsStatisticalAction" scope="prototype">
        <property name="arrearsStatisticalService">
            <ref bean="ArrearsStatisticalService"/>
        </property>
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
    </bean>
    <bean id="ArrearsWriteOffAction" class="com.xinxinsoft.action.arrearsWriteOffAction.ArrearsWriteOffAction" scope="prototype"/>
    <bean id="AttachmentAction" class="com.xinxinsoft.action.enclosure.AttachmentAction" scope="prototype">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="bogSerice">
            <ref bean="BOGetService"/>
        </property>
    </bean>
    <bean id="AttachmentActionTwo" class="com.xinxinsoft.action.attachmentAction.AttachmentAction" scope="prototype"/>
    <bean id="AttachmentTypeAction" class="com.xinxinsoft.action.enclosure.AttachmentTypeAction" scope="prototype">
        <property name="attachmentTypeService">
            <ref bean="AttachmentTypeService"/>
        </property>
    </bean>
    <bean id="AuditMultipleAction" class="com.xinxinsoft.action.auditWorksheet.AuditMultipleAction" scope="prototype">
        <property name="auditMultipleService">
            <ref bean="AuditMultipleService"/>
        </property>
    </bean>
    <bean id="AuditWorkListAction" class="com.xinxinsoft.action.auditWorksheet.AuditWorkListAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="auditWorksheetService">
            <ref bean="AuditWorksheetService"/>
        </property>
        <property name="auditWorkListService">
            <ref bean="AuditWorkListService"/>
        </property>
    </bean>
    <bean id="AuditWorksheetAction" class="com.xinxinsoft.action.auditWorksheet.AuditWorksheetAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="auditWorksheetService">
            <ref bean="AuditWorksheetService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="AuditWorksheetActionTwo" class="com.xinxinsoft.action.auditWorksheet.AuditWorksheetActionTwo" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="auditWorksheetService">
            <ref bean="AuditWorksheetService"/>
        </property>
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="AuditWorksheetHttpAction" class="com.xinxinsoft.action.httpAction.AuditWorksheetHttpAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="auditWorksheetService">
            <ref bean="AuditWorksheetService"/>
        </property>
        <property name="interfaceLogService">
            <ref bean="InterfaceLogService"/>
        </property>
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
    </bean>
    <bean id="BigAmountApplyAction" class="com.xinxinsoft.action.bigAmountApply.BigAmountApplyAction" scope="prototype"/>
    <bean id="BossTacheAction" class="com.xinxinsoft.action.basetype.BossTacheAction" scope="prototype">
        <property name="service">
            <ref bean="BossTacheService"/>
        </property>
    </bean>
    <bean id="BusinessEntityAction" class="com.xinxinsoft.action.businessProc.BusinessEntityAction" scope="prototype">
        <property name="businessEntityService">
            <ref bean="BusinessEntityService"/>
        </property>
    </bean>
    <bean id="BusinessTypeAction" class="com.xinxinsoft.action.basetype.BusinessTypeAction" scope="prototype">
        <property name="businessTypeService">
            <ref bean="BusinessTypeService"/>
        </property>
    </bean>
    <bean id="BusinssAction" class="com.xinxinsoft.action.businss.BusinssAction" scope="prototype">
        <property name="businssService">
            <ref bean="BusinssService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="waitTaskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="BusinssGrpJttfConRedInAction" class="com.xinxinsoft.action.businss.BusinssConnectorByGrpJttfConRedInAction" scope="prototype"/>
    <bean id="BusinssTaxpayerNumberQryAction" class="com.xinxinsoft.action.businss.BusinssConnectorByTaxpayerNumberQryAction" scope="prototype"/>
    <bean id="ChannelInfoAction" class="com.xinxinsoft.action.honorCode.ChannelInfoAction" scope="prototype">
        <property name="channelInfoService">
            <ref bean="ChannelInfoService"/>
        </property>
        <property name="contractService">
            <ref bean="ContractService"/>
        </property>
        <property name="honorCodeBusinessService">
            <ref bean="HonorCodeBusinessService"/>
        </property>
    </bean>
    <bean id="CheckAction" class="com.xinxinsoft.action.checkentityAction.CheckAction" scope="prototype">
        <property name="checkService">
            <ref bean="CheckService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="ClaimAContractAction" class="com.xinxinsoft.action.contract.ClaimAContractAction" scope="prototype">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="claimAContractService">
            <ref bean="ClaimAContractService"/>
        </property>
    </bean>
    <bean id="ClaimForFundAction" class="com.xinxinsoft.action.claimForFunds.ClaimForFundsTwoAction" scope="prototype"/>
    <bean id="ClaimForFundModelAction" class="com.xinxinsoft.action.claimForFunds.claimForFundModelAction" scope="prototype"/>
    <bean id="ClaimForFundsAct" class="com.xinxinsoft.filter.ClaimForFundsAct" scope="prototype"/>
    <!-- 差异说明: bean#ClaimForFundsAction - 85环境(10个属性/子元素), 86环境(9个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="ClaimForFundsAction" class="com.xinxinsoft.action.claimForFunds.ClaimForFundsAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="claimForFundsService">
            <ref bean="ClaimForFundsService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="lateFeeMoneyDataService">
            <ref bean="LateFeeMoneyDataService"/>
        </property>
        <!--<property name="customerServiceXml">
			<ref bean="customerServices"/>
		</property>
		<property name="customerService">
			<ref bean="GroupCustomerService"/>
		</property>-->
    </bean>
    <bean id="ContractAction" class="com.xinxinsoft.action.contract.ContractAction" scope="prototype">
        <property name="contractService">
            <ref bean="ContractService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="orderDetailService">
            <ref bean="orderDetailService"/>
        </property>
        <property name="productTypeService">
            <ref bean="ProductTypeService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="contractUtils">
            <ref bean="ContractUtils"/>
        </property>
        <property name="contractCsv">
            <ref bean="ContractCsv"/>
        </property>
        <property name="contractToOrderCsv">
            <ref bean="ContractToOrderCsv"/>
        </property>
    </bean>
    <bean id="ContractECHttpAction" class="com.xinxinsoft.action.httpAction.ContractECHttpAction" autowire="byType" scope="prototype">
	</bean>
    <bean id="ContractInterfaceAction" class="com.xinxinsoft.action.httpAction.ContractInterfaceAction" scope="prototype">
        <property name="customClauseContractService">
            <ref bean="CustomClauseContractService"/>
        </property>
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
        <property name="signService">
            <ref bean="SignService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="pushService">
            <ref bean="smsPushService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
    </bean>
    <bean id="ContractServlet" class="com.xinxinsoft.action.httpAction.ContractServlet" scope="prototype">
        <property name="contractHttpService">
            <ref bean="ContractHttpService"/>
        </property>
    </bean>
    <bean id="ContractUniformityAction" class="com.xinxinsoft.action.contractUniformityAction.ContractUniformityAction" scope="prototype">
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="CoutomerAcceptanceAction" class="com.xinxinsoft.action.dedicatedFlow.CoutomerAcceptanceAction" scope="prototype">
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="processService">
            <ref bean="processService"/>
        </property>
    </bean>
    <bean id="CustomerAccountAction" class="com.xinxinsoft.action.customeraccount.CustomerAccountAction" scope="prototype">
        <property name="customerAccountService">
            <ref bean="CustomerAccountService"/>
        </property>
    </bean>
    <bean id="DedicatedFlowAction" class="com.xinxinsoft.action.dedicatedFlow.DedicatedFlowAction" scope="prototype">
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="dictionaryService">
            <ref bean="DictionaryService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="cmccOpenService">
            <ref bean="CMCCOpenService"/>
        </property>
        <property name="commonSingleService">
            <ref bean="commonSingleService"/>
        </property>
        <property name="processService">
            <ref bean="processService"/>
        </property>
        <property name="smsPushService">
            <ref bean="smsPushService"/>
        </property>
        <property name="lateService">
            <ref bean="LinkTemplateService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#DictOrderHttpAction -->
    <bean id="DictOrderHttpAction" class="com.xinxinsoft.action.httpAction.DictOrderHttpAction" scope="prototype"/>
    <bean id="EditionAction" class="com.xinxinsoft.action.core.app.EditionAction" scope="prototype">
        <property name="editionService">
            <ref bean="EditionService"/>
        </property>
    </bean>
    <bean id="EomCertAction" class="com.xinxinsoft.action.sign.EomCertAction" scope="prototype">
        <property name="signService">
            <ref bean="SignService"/>
        </property>
    </bean>
    <bean id="EomSealAction" class="com.xinxinsoft.action.sign.EomSealAction" scope="prototype">
        <property name="sealService">
            <ref bean="SealService"/>
        </property>
        <property name="signService">
            <ref bean="SignService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="EomSignAction" class="com.xinxinsoft.action.sign.EomSignAction" scope="prototype">
        <property name="signServie">
            <ref bean="SignService"/>
        </property>
        <property name="sealServie">
            <ref bean="SealService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="ExpenseApplyAction" class="com.xinxinsoft.action.dedicatedFlow.ExpenseApplyAction" scope="prototype">
        <property name="service">
            <ref bean="ExpenseApplyService"/>
        </property>
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="processService">
            <ref bean="processService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#ExternalInterfaceAction -->
    <bean id="ExternalInterfaceAction" class="com.xinxinsoft.action.appAction.ExternalInterfaceAction" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="iBossByNoService">
            <ref bean="IbbnService"/>
        </property>
    </bean>
    <bean id="FilePreviewAction" class="com.xinxinsoft.action.filePreview.FilePreviewAction" scope="prototype"/>
    <bean id="GroupCustomerAction" class="com.xinxinsoft.action.groupcustomer.GroupCustomerAction" scope="prototype">
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="customerService">
            <ref bean="customerServices"/>
        </property>
    </bean>
    <bean id="GroupCustomersAction" class="com.xinxinsoft.action.groupUpdateAction.GroupCustomersAction" scope="prototype"/>
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"/>
    <bean id="GroupPayHttpAction" class="com.xinxinsoft.action.httpAction.GroupPayHttpAction" scope="prototype"/>
    <bean id="GroupPaymentAction" class="com.xinxinsoft.action.groupPaymentAction.GroupPaymentAction" scope="prototype"/>
    <bean id="GroupPeopleAction" class="com.xinxinsoft.action.customeraccount.GroupPeopleAction" scope="prototype">
        <property name="groupPeopleService">
            <ref bean="GroupPeopleService"/>
        </property>
    </bean>
    <bean id="GroupTaxpayerAction" class="com.xinxinsoft.action.groupTaxpayerAction.GroupTaxpayerAction" scope="prototype"/>
    <!-- 仅在85环境中存在: bean#GroupUpdateAction -->
    <bean id="GroupUpdateAction" class="com.xinxinsoft.action.groupUpdateAction.GroupUpdateAction" scope="prototype"/>
    <bean id="GuideDocumentationAction" class="com.xinxinsoft.action.httpAction.GuideDocumentationAction" autowire="byType" scope="prototype">
	</bean>
    <bean id="HonorCodeBusinessAction" class="com.xinxinsoft.action.honorCode.HonorCodeBusinessAction" scope="prototype">
        <property name="honorCodeBusinessService">
            <ref bean="HonorCodeBusinessService"/>
        </property>
        <property name="contractService">
            <ref bean="ContractService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#HonorCodeInterfaceAction -->
    <bean id="HonorCodeInterfaceAction" class="com.xinxinsoft.action.honorCode.HonorCodeInterfaceAction" scope="prototype">
        <property name="activityService">
            <ref bean="ActivityService"/>
        </property>
        <property name="channelInfoService">
            <ref bean="ChannelInfoService"/>
        </property>
        <property name="honorCodeBusinessService">
            <ref bean="HonorCodeBusinessService"/>
        </property>
        <property name="contractService">
            <ref bean="ContractService"/>
        </property>
        <property name="service">
            <ref bean="SystemUserService"/>
        </property>
        <property name="smsService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <!-- 差异说明: bean#IBossByNoAction - 85环境(9个属性/子元素), 86环境(4个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="IBossByNoAction" class="com.xinxinsoft.action.IBossAction.IBossByNoAction" scope="prototype">
        <property name="iboss">
            <ref bean="IbbnService"/>
        </property>
        <property name="noResApplyService">
            <ref bean="NoResApplyService"/>
        </property>
        <property name="variousSqlQueryService">
            <ref bean="VariousSqlQueryService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="waitTaskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="customClauseContractService">
            <ref bean="CustomClauseContractService"/>
        </property>
    </bean>
    <bean id="ICTApplicationAction" class="com.xinxinsoft.action.ICT.ICTApplicationAction" scope="prototype">
        <property name="ictApplicationService">
            <ref bean="ICTApplicationService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
    </bean>
    <bean id="IDCApplyAction" class="com.xinxinsoft.action.IDC.IDCApplyAction" scope="prototype">
        <!--<property name="applyService">
			<ref bean="IDCApplyService"/>
		</property>
		<property name="flowService">
			<ref bean="IDCFlowService"/>
		</property>
		<property name="taskService">
			<ref bean="IDCTaskService"/>
		</property>
		<property name="jbpmUtil">
			<ref bean="JBPMUtil"/>
		</property>
		<property name="waitTaskService">
			<ref bean="WaitTaskService" />
		</property>
		<property name="systemUserService">
			<ref bean="SystemUserService" />
		</property>
		<property name="tInformationService">
			<ref bean="TransferInformationService" />
		</property>
		<property name="cmccOpenService">
			<ref bean="CMCCOpenService" />
		</property>-->
    </bean>
    <bean id="IGrpOrderInfoAction" class="com.xinxinsoft.action.IBossAction.IGrpOrderInfoAction" scope="prototype"/>
    <bean id="IMSHighFrequencyAction" class="com.xinxinsoft.action.IMSHighFrequencyAction.IMSHighFrequencyAction" scope="prototype"/>
    <!-- 仅在85环境中存在: bean#IctContractAction -->
    <bean id="IctContractAction" class="com.xinxinsoft.action.IctContract.IctContractAction" scope="prototype">
        <property name="ictContractService">
            <ref bean="IctContractService"/>
        </property>
    </bean>
    <bean id="IndustryTerminalAction" class="com.xinxinsoft.action.IndustryTerminal.IndustryTerminalAction" scope="prototype">
        <property name="industryTerminalService">
            <ref bean="IndustryTerminalService"/>
        </property>
        <property name="waitTaskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
    </bean>
    <bean id="IndustryTerminalBySMMCCalculateARPUAction" class="com.xinxinsoft.action.IndustryTerminal.IndustryTerminalBySMMCCalculateARPUAction" scope="prototype"/>
    <bean id="InquiryOrderAction" class="com.xinxinsoft.action.InquiryOrder.InquiryOrderAction" scope="prototype">
        <property name="inquiryOrderService">
            <ref bean="InquiryOrderService"/>
        </property>
    </bean>
    <bean id="IntegrationAction" class="com.xinxinsoft.action.v2.integrationAction.IntegrationAction" scope="prototype">
        <property name="commonSingleService">
            <ref bean="commonSingleService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="integrationService">
            <ref bean="IntegrationService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="processService">
            <ref bean="processService"/>
        </property>
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#InvoiceReceiveOrder -->
    <bean id="InvoiceReceiveOrder" class="com.xinxinsoft.action.httpAction.InvoiceReceiveOrder" scope="prototype">
        <property name="monthlyinvoiceService">
            <ref bean="MonthlyinvoiceService"/>
        </property>
    </bean>
    <bean id="JbpmTest" class="com.xinxinsoft.action.test.JbpmTest">
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
    </bean>
    <bean id="JobApprovalAction" class="com.xinxinsoft.action.appOpenAction.JobApprovalAction" autowire="byType" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="noResApplyService">
            <ref bean="NoResApplyService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#JobLogAction -->
    <bean id="JobLogAction" class="com.xinxinsoft.action.executejoblog.JobLogAction" scope="prototype">
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="JobRejectionInfoAction" class="com.xinxinsoft.action.jobRejectionInfoAction.JobRejectionInfoAction" scope="prototype">
        <property name="jobRejectionInfoService">
            <ref bean="JobRejectionInfoService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="receiveApplyService">
            <ref bean="ReceiveApplyService"/>
        </property>
    </bean>
    <bean id="LinkScoreAction" class="com.xinxinsoft.action.linkScore.LinkScoreAction" scope="prototype">
        <property name="linkScoreService">
            <ref bean="LinkScoreService"/>
        </property>
    </bean>
    <bean id="LinkTemplateAction" class="com.xinxinsoft.action.processLink.LinkTemplateAction" scope="prototype">
        <property name="service">
            <ref bean="LinkTemplateService"/>
        </property>
        <property name="dictionaryService">
            <ref bean="DictionaryService"/>
        </property>
    </bean>
    <bean id="LoginUserAction" class="com.xinxinsoft.action.core.user.LoginUserAction" scope="prototype">
        <property name="loginUserService">
            <ref bean="LoginUserService"/>
        </property>
    </bean>
    <bean id="LwFtpAction" class="com.xinxinsoft.action.LwFtpAction.LwFtpAction" scope="prototype">
        <property name="lwFtpTask">
            <ref bean="lwFtpTask"/>
        </property>
    </bean>
    <bean id="ManualInvApplyAction" class="com.xinxinsoft.action.manualInvApply.ManualInvApplyAction" scope="prototype">
        <property name="manualInvApplyService">
            <ref bean="ManualInvApplyService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="MarketActivitiesAction" class="com.xinxinsoft.action.MarketActivitiesAction.MarketActivitiesAction" scope="prototype"/>
    <bean id="MarketingActivitiesAction" class="com.xinxinsoft.action.marketingActivitiesAction.MarketingActivitiesAction" scope="prototype"/>
    <bean id="MarketingActivitiesActionTwo" class="com.xinxinsoft.action.marketingActivitiesAction.MarketingActivitiesActionTwo" scope="prototype"/>
    <bean id="MenuAction" class="com.xinxinsoft.action.core.role.MenuAction" scope="prototype">
        <property name="menuService">
            <ref bean="MenuService"/>
        </property>
    </bean>
    <bean id="MoaAction" class="com.xinxinsoft.action.interfaceMoaAction.MoaAction" scope="prototype"/>
    <!-- 差异说明: bean#OMSAction - 85环境(9个属性/子元素), 86环境(8个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="OMSAction" class="com.xinxinsoft.action.appOpenAction.OMSAction" autowire="byType" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="omsService">
            <ref bean="OMSService"/>
        </property>
        <property name="querySer">
            <ref bean="VariousSqlQueryService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="OmsOrderProductAction" class="com.xinxinsoft.action.oms.OmsOrderProductAction" scope="prototype">
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#OmsOrderProductHttpAction -->
    <bean id="OmsOrderProductHttpAction" class="com.xinxinsoft.action.httpAction.OmsOrderProductHttpAction" scope="prototype">
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="V2omsSellOrderService">
            <ref bean="V2OmsSellOrderService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="OmsOrderWorkbenchAction" class="com.xinxinsoft.action.oms.OmsOrderWorkbenchAction" scope="prototype">
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="unitInfoService">
            <ref bean="UnitInfoService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
    </bean>
    <bean id="OmsSellOrderAction" class="com.xinxinsoft.action.oms.OmsSellOrderAction" scope="prototype">
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#OmsStatisticalAction -->
    <bean id="OmsStatisticalAction" class="com.xinxinsoft.action.oms.OmsStatisticalAction" scope="prototype">
        <property name="omsStatisticalService">
            <ref bean="OmsStatisticalService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#OneClickOrderAction -->
    <bean id="OneClickOrderAction" class="com.xinxinsoft.action.appAction.OneClickOrderAction" scope="prototype">
        <property name="omsService">
            <ref bean="OMSService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="Open4AStateAction" class="com.xinxinsoft.action.appAction.Open4AStateAction" scope="prototype">
        <property name="open4AStateService">
            <ref bean="Open4AStateService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#OrderTesting -->
    <bean id="OrderTesting" class="com.xinxinsoft.action.oms.OrderTesting" scope="prototype">
        <property name="testingService">
            <ref bean="ServiceStandardizationTestingService"/>
        </property>
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="PMSAction" class="com.xinxinsoft.action.appOpenAction.PMSAction" autowire="byType" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="pmsService">
            <ref bean="PMSService"/>
        </property>
    </bean>
    <bean id="PayApiAction" class="com.xinxinsoft.action.pay.PayApiAction" scope="prototype">
        <property name="payApiService">
            <ref bean="PayApiService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="PayProviderInfoAction" class="com.xinxinsoft.action.pay.PayProviderInfoAction" scope="prototype">
        <property name="orderQuerySendService">
            <ref bean="OrderQuerySendService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#PayRefundAction -->
    <bean id="PayRefundAction" class="com.xinxinsoft.action.pay.PayRefundAction" autowire="byType" scope="prototype">
        <property name="payRefundService">
            <ref bean="PayRefundService"/>
        </property>
        <property name="paymentService">
            <ref bean="PaymentService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#PaymentAction -->
    <bean id="PaymentAction" class="com.xinxinsoft.action.pay.PaymentAction" autowire="byType" scope="prototype">
        <property name="paymentService">
            <ref bean="PaymentService"/>
        </property>
        <property name="payApiService">
            <ref bean="PayApiService"/>
        </property>
    </bean>
    <!-- 差异说明: bean#PaymentOrderAction - 85环境(7个属性/子元素), 86环境(4个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="PaymentOrderAction" class="com.xinxinsoft.action.pay.PaymentOrderAction" autowire="byType" scope="prototype">
        <property name="paymentOrderService">
            <ref bean="PaymentOrderService"/>
        </property>
        <property name="paymentProviderService">
            <ref bean="PaymentProviderService"/>
        </property>
        <property name="variousSqlQueryService">
            <ref bean="VariousSqlQueryService"/>
        </property>
    </bean>
    <bean id="PaymentRecordAction" class="com.xinxinsoft.action.arrearsModule.PaymentRecordAction" scope="prototype">
        <property name="pService">
            <ref bean="PaymentRecordService"/>
        </property>
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
    </bean>
    <bean id="PreinvApplyAction" class="com.xinxinsoft.action.PreinvApply.PreinvApplyAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="preinvApplyService">
            <ref bean="PreinvApplyService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
    </bean>
    <bean id="PreinvApplyMarkingAction" class="com.xinxinsoft.action.preinvApplyMarkingAction.PreinvApplyMarkingAction" scope="prototype"/>
    <bean id="PreinvIoTCardAction" class="com.xinxinsoft.action.preinvIoTCardAction.PreinvIoTCardAction" scope="prototype"/>
    <bean id="ProductFlowAction" class="com.xinxinsoft.action.processLink.ProductFlowAction" scope="prototype">
        <property name="service">
            <ref bean="ProductFlowService"/>
        </property>
    </bean>
    <bean id="ProductTypeAction" class="com.xinxinsoft.action.basetype.ProductTypeAction" scope="prototype">
        <property name="service">
            <ref bean="ProductTypeService"/>
        </property>
    </bean>
    <bean id="QuestionAction" class="com.xinxinsoft.action.checkentityAction.QuestionAction" scope="prototype">
        <property name="questionService">
            <ref bean="QuestionService"/>
        </property>
    </bean>
    <bean id="RealNameReservAtion" class="com.xinxinsoft.action.RealNameReservAtion.realNameReservAtion" scope="prototype"/>
    <bean id="ReceiptApplyAction" class="com.xinxinsoft.action.ReceiptApplyAction.ReceiptApplyAction" scope="prototype">
        <property name="receiptApplyService">
            <ref bean="ReceiptApplyService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="ReceiptApplyAmountAction" class="com.xinxinsoft.action.ReceiptApplyAction.ReceiptApplyAmountAction" scope="prototype"/>
    <bean id="ReceiveApplyAction" class="com.xinxinsoft.action.receiveApply.ReceiveApplyAction" scope="prototype">
        <property name="receiveApplyService">
            <ref bean="ReceiveApplyService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="RecommendedManagementAction" class="com.xinxinsoft.action.recommendedManagementAction.RecommendedManagementAction" scope="prototype">
        <property name="recommendedManagementService">
            <ref bean="RecommendedManagementService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="ReconciliationAction" class="com.xinxinsoft.action.pay.ReconciliationAction" scope="prototype">
        <property name="reconciliationService">
            <ref bean="ReconciliationService"/>
        </property>
        <property name="settlementService">
            <ref bean="SettlementService"/>
        </property>
        <property name="variousSqlQueryService">
            <ref bean="VariousSqlQueryService"/>
        </property>
    </bean>
    <bean id="ReductionICTApplicationAction" class="com.xinxinsoft.action.ICT.ReductionICTApplicationAction" scope="prototype">
        <property name="reductionIctApplicationService">
            <ref bean="ReductionICTApplicationService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="ictApplicationService">
            <ref bean="ICTApplicationService"/>
        </property>
    </bean>
    <!-- 差异说明: bean#RefundOrderAction - 85环境(7个属性/子元素), 86环境(4个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="RefundOrderAction" class="com.xinxinsoft.action.pay.RefundOrderAction" autowire="byType" scope="prototype">
        <property name="refundOrderService">
            <ref bean="RefundOrderService"/>
        </property>
        <property name="paymentProviderService">
            <ref bean="PaymentProviderService"/>
        </property>
        <property name="variousSqlQueryService">
            <ref bean="VariousSqlQueryService"/>
        </property>
    </bean>
    <bean id="RejectOrderAction" class="com.xinxinsoft.action.rejectWordkOrderAction.RejectOrderAction" autowire="byType" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="noResApplyService">
            <ref bean="NoResApplyService"/>
        </property>
    </bean>
    <bean id="RiskClosedLoopAction" class="com.xinxinsoft.action.riskClosedLoop.RiskClosedLoopAction" scope="prototype">
        <property name="riskClosedLoopService">
            <ref bean="RiskClosedLoopService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="claimForFundsService">
            <ref bean="ClaimForFundsService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#RiskControlAction -->
    <bean id="RiskControlAction" class="com.xinxinsoft.action.arrearsWriteOffAction.RiskControlAction" scope="prototype"/>
    <bean id="RiskDutyAction" class="com.xinxinsoft.action.RiskDuty.RiskDutyAction" scope="prototype"/>
    <bean id="RiskcontrolDerivedAction" class="com.xinxinsoft.action.riskcontrolDerivedAction.RiskcontrolderivedAction" scope="prototype">
        <property name="riskcontrolDerivedService">
            <ref bean="RiskcontrolDerivedService"/>
        </property>
        <property name="preinvApplyService">
            <ref bean="PreinvApplyService"/>
        </property>
        <property name="whiteListInformationService">
            <ref bean="WhiteListInformationService"/>
        </property>
        <property name="arrearsSingSerivce">
            <ref bean="ArrearsSingSerivce"/>
        </property>
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
    </bean>
    <bean id="RoleAction" class="com.xinxinsoft.action.core.role.RoleAction" scope="prototype">
        <property name="roleService">
            <ref bean="RoleService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#S4000CfmAction -->
    <bean id="S4000CfmAction" class="com.xinxinsoft.action.httpAction.S4000CfmAction" scope="prototype">
        <property name="interfaceLogService">
            <ref bean="InterfaceLogService"/>
        </property>
    </bean>
    <bean id="SIMAction" class="com.xinxinsoft.action.SIM.SIMAction" scope="prototype"/>
    <bean id="SRestoreDeadUserAction" class="com.xinxinsoft.action.SRestoreDeadUser.SRestoreDeadUserAction" scope="prototype"/>
    <bean id="SaveSendAction" class="com.xinxinsoft.action.SaveSendAction.SaveSendAction" scope="prototype">
        <property name="saveSendService">
            <ref bean="SaveSendService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="SaveSendCheckAction" class="com.xinxinsoft.action.SaveSendAction.SaveSendCheckAction" scope="prototype">
        <property name="saveSendCheckService">
            <ref bean="SaveSendCheckService"/>
        </property>
        <property name="saveSendService">
            <ref bean="SaveSendService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="saveSendCheckTimeService">
            <ref bean="SaveSendCheckTimeService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#ServiceStandardizationTestingAction -->
    <bean id="ServiceStandardizationTestingAction" class="com.xinxinsoft.action.oms.ServiceStandardizationTestingAction" scope="prototype">
        <property name="testingService">
            <ref bean="ServiceStandardizationTestingService"/>
        </property>
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="SpeciaPlanAction" class="com.xinxinsoft.action.SpeciaPlanAction.SpeciaPlanAction" scope="prototype"/>
    <bean id="SupCollectSAction" class="com.xinxinsoft.action.SaveSendAction.SupCollectSAction" scope="prototype">
        <property name="supCollectService">
            <ref bean="SupCollectService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="suoCollectCheckTimeService">
            <ref bean="SuoCollectCheckTimeService"/>
        </property>
    </bean>
    <bean id="SuspensionApplicationAction" class="com.xinxinsoft.action.suspensionApplicationAction.SuspensionApplicationAction" scope="prototype">
        <property name="saService">
            <ref bean="SuspensionApplicationService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
    </bean>
    <bean id="SystemOrganizationAction" class="com.xinxinsoft.action.core.org.SystemOrganizationAction" scope="prototype">
        <property name="orgService">
            <ref bean="SystemOrganizationService"/>
        </property>
    </bean>
    <bean id="SystemUserAction" class="com.xinxinsoft.action.core.user.SystemUserAction" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="smsPushService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <bean id="TariffManagementAction" class="com.xinxinsoft.action.tariffManagement.TariffManagementAction" scope="prototype"/>
    <bean id="TerminalAction" class="com.xinxinsoft.action.SaveSendAction.TerminalAction" scope="prototype">
        <property name="terminalService">
            <ref bean="TerminalService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="terminalCheckTimeService">
            <ref bean="TerminalCheckTimeService"/>
        </property>
    </bean>
    <bean id="TerminalActivityAction" class="com.xinxinsoft.action.terminalActivityAdministration.TerminalActivityAction" scope="prototype"/>
    <bean id="TransferAccountsAction" class="com.xinxinsoft.action.SaveSendAction.TransferAccountsAction" scope="prototype">
        <property name="transferAccountsService">
            <ref bean="TransferAccountsService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="transferAccountsCheckTimeService">
            <ref bean="TransferAccountsCheckTimeService"/>
        </property>
    </bean>
    <bean id="TransferAction" class="com.xinxinsoft.action.transfer.TransferAction" scope="prototype">
        <property name="transferService">
            <ref bean="TransferService"/>
        </property>
        <property name="transferInterfaceService">
            <ref bean="TransferInterfaceService"/>
        </property>
    </bean>
    <bean id="TransferCitiesDataAction" class="com.xinxinsoft.action.transfer.TransferCitiesDataAction" scope="prototype">
        <property name="transferCitiesDataService">
            <ref bean="TransferCitiesDataService"/>
        </property>
    </bean>
    <bean id="TransferInformationAction" class="com.xinxinsoft.action.transfer.TransferInformationAction" scope="prototype">
        <property name="tInformationService">
            <ref bean="TransferInformationService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <!-- 差异说明: bean#TransferInformationTwoAction - 85环境(11个属性/子元素), 86环境(10个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="TransferInformationTwoAction" class="com.xinxinsoft.action.transfer.TransferInformationTwoAction" scope="prototype">
        <property name="tInformationService">
            <ref bean="TransferInformationTwoService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="lateFeeMoneyDataService">
            <ref bean="LateFeeMoneyDataService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
    </bean>
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!-- 差异说明: bean#UnitInfoAction - 85环境(8个属性/子元素), 86环境(7个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="UnitInfoAction" class="com.xinxinsoft.action.ums.UnitInfoAction" autowire="byType" scope="prototype">
        <property name="unitInfoService">
            <ref bean="UnitInfoService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="iBossByNoService">
            <ref bean="IbbnService"/>
        </property>
    </bean>
    <bean id="UpdateCustomerAcceptanceAction" class="com.xinxinsoft.action.appAction.UpdateCustomerAcceptanceAction" scope="prototype">
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="processService">
            <ref bean="processService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#UploadDocumentAction -->
    <bean id="UploadDocumentAction" class="com.xinxinsoft.action.uploadDocumentAction.UploadDocumentAction" scope="prototype"/>
    <bean id="V2AppOmsSellOrderAction" class="com.xinxinsoft.action.appAction.V2AppOmsSellOrderAction" scope="prototype">
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="claimForFundsService">
            <ref bean="ClaimForFundsService"/>
        </property>
    </bean>
    <bean id="V2OmsOrderWorkbenchAction" class="com.xinxinsoft.action.oms.V2OmsOrderWorkbenchAction" scope="prototype">
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="V2OmsSellOrderAction" class="com.xinxinsoft.action.oms.V2OmsSellOrderAction" scope="prototype">
        <property name="omsOrderWorkbenchService">
            <ref bean="OmsOrderWorkbenchService"/>
        </property>
        <property name="omsOrderProductService">
            <ref bean="OmsOrderProductService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="ValuableCardAction" class="com.xinxinsoft.action.valuableCard.ValuableCardAction" scope="prototype"/>
    <bean id="WaitTaskAction" class="com.xinxinsoft.action.waitTask.WaitTaskAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
    </bean>
    <bean id="WhiteListAction" class="com.xinxinsoft.action.whiteList.WhiteListAction" scope="prototype">
        <property name="whiteListService">
            <ref bean="WhiteListService"/>
        </property>
        <property name="whiteListInformationService">
            <ref bean="WhiteListInformationService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="WhiteListInformationAction" class="com.xinxinsoft.action.whiteList.WhiteListInformationAction" scope="prototype">
        <property name="whiteListInformationService">
            <ref bean="WhiteListInformationService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="waitTaskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="WhiteRollListAction" class="com.xinxinsoft.action.whiteRollListAction.WhiteRollListAction" scope="prototype"/>
    <bean id="WorkOrderAction" class="com.xinxinsoft.action.workOrder.WorkOrderAction" scope="prototype">
        <property name="workOrderServcie">
            <ref bean="WorkOrderServcie"/>
        </property>
    </bean>
    <bean id="ZtreeUserAction" class="com.xinxinsoft.action.core.user.ZtreeUserAction" scope="prototype">
        <property name="service">
            <ref bean="ZtreeUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="sopSevvice">
            <ref bean="StructureOfPersonnelService"/>
        </property>
    </bean>
    <bean id="activityPreinvApplyAction" class="com.xinxinsoft.action.activityPreinvAction.ActivityPreinvApplyAction" scope="prototype">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="activityPreinvApplyService">
            <ref bean="ActivityPreinvApplyService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
    </bean>
    <bean id="appFourtOpenAction" class="com.xinxinsoft.action.appAction.AppFourtOpenAction" scope="prototype" autowire="byType"/>
    <bean id="appGroupAccountAction" class="com.xinxinsoft.action.appAction.AppGroupAccountAction" scope="prototype"/>
    <bean id="appLoginAction" class="com.xinxinsoft.action.appLoginAction.appLoginAction" scope="prototype">
        <property name="apploginService">
            <ref bean="appLoginService"/>
        </property>
        <property name="countShareService">
            <ref bean="CountShareService"/>
        </property>
    </bean>
    <bean id="appMentaryContractAction" class="com.xinxinsoft.action.appAction.AppMentaryContractAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <bean id="appRiskDutyAction" class="com.xinxinsoft.action.appAction.appRiskDutyAction" scope="prototype"/>
    <bean id="appVerificAction" class="com.xinxinsoft.action.appAction.AppVerificAction" scope="prototype"/>
    <bean id="attachmentDownloadAction" class="com.xinxinsoft.action.appAction.AttachmentDownloadAction" scope="prototype">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#b1000OpenSev -->
    <bean id="b1000OpenSev" class="com.xinxinsoft.action.appOpenAction.Broadband1000OpeningAction" scope="prototype">
        <property name="varSer">
            <ref bean="Various1000SqlQueryService"/>
        </property>
        <property name="sysSer">
            <ref bean="SystemUserService"/>
        </property>
        <property name="singSer">
            <ref bean="commonSingleService"/>
        </property>
        <property name="querySer">
            <ref bean="VariousSqlQueryService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#b1000QuerySev -->
    <bean id="b1000QuerySev" class="com.xinxinsoft.action.appOpenAction.Broadband1000OpeningQueryAction" scope="prototype">
        <property name="varSer">
            <ref bean="Various1000SqlQueryService"/>
        </property>
        <property name="sysSer">
            <ref bean="SystemUserService"/>
        </property>
        <property name="singSer">
            <ref bean="commonSingleService"/>
        </property>
        <property name="querySer">
            <ref bean="VariousSqlQueryService"/>
        </property>
    </bean>
    <bean id="callingSystemValidationAction" class="com.xinxinsoft.action.callingSystemValidation.callingSystemValidationAction" scope="prototype"/>
    <bean id="claimFundReturnAction" class="com.xinxinsoft.action.claimForFunds.ClaimFundReturnAction" scope="prototype"/>
    <bean id="commonSingleAction" class="com.xinxinsoft.action.commonSingManagement.CommonSingleAction" scope="prototype">
        <property name="commonSingleService" ref="commonSingleService"/>
        <property name="businessTypeService" ref="BusinessTypeService"/>
        <property name="productTypeService" ref="ProductTypeService"/>
        <property name="groupCustomerService" ref="GroupCustomerService"/>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="countShareAction" class="com.xinxinsoft.action.countShare.CountShareAction" scope="prototype">
        <property name="countShareService">
            <ref bean="CountShareService"/>
        </property>
    </bean>
    <bean id="customClauseContractAction" class="com.xinxinsoft.action.contract.CustomClauseContractAction" autowire="byType" scope="prototype">
        <property name="customClauseContractService">
            <ref bean="CustomClauseContractService"/>
        </property>
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
        <property name="signService">
            <ref bean="SignService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
    </bean>
    <bean id="customerAction" class="com.xinxinsoft.action.customer.customerAction" scope="prototype">
        <property name="customerService">
            <ref bean="customerService"/>
        </property>
    </bean>
    <bean id="deleteAttachmentAction" class="com.xinxinsoft.action.appAction.DeleteAttachmentAction" scope="prototype">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="dictionaryAction" class="com.xinxinsoft.action.core.DictionaryAction" scope="prototype">
        <property name="dicService">
            <ref bean="DictionaryService"/>
        </property>
    </bean>
    <bean id="fourtOpenAction" class="com.xinxinsoft.action.fourtOpenAction.fourtOpenAction" scope="prototype" autowire="byType"/>
    <bean id="ftpHandleAction" class="com.xinxinsoft.action.core.FtpHandleAction" scope="prototype">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="groupAccountAction" class="com.xinxinsoft.action.groupAccountAction.GroupAccountAction" scope="prototype"/>
    <!-- 差异说明: bean#groupCustomerToAppAction - 85环境(7个属性/子元素), 86环境(6个属性/子元素) -->
    <!-- 采用85环境配置 -->
    <bean id="groupCustomerToAppAction" class="com.xinxinsoft.action.appAction.GroupCustomerToAppAction" scope="prototype">
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="customerService">
            <ref bean="customerServices"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="sysSer">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="groupReportedLossesAction" class="com.xinxinsoft.action.GroupReportedAction.GroupReportedLossesAction" scope="prototype"/>
    <bean id="holidayAction" class="com.xinxinsoft.action.core.HolidayAction">
        <property name="holidayService">
            <ref bean="holidayService"/>
        </property>
    </bean>
    <bean id="imsAction" class="com.xinxinsoft.action.dedicatedFlow.ims.ImsAction" scope="prototype">
        <property name="imsService">
            <ref bean="imsService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="itoAction" class="com.xinxinsoft.action.dedicatedFlow.ito.ItoAction" scope="prototype">
        <property name="itoService">
            <ref bean="itoService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="processService">
            <ref bean="processService"/>
        </property>
    </bean>
    <bean id="knowledgeAction" class="com.xinxinsoft.action.knowledge.KnowledgeAction" scope="prototype">
        <property name="knowledgeService">
            <ref bean="KnowledgeService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="pushService">
            <ref bean="smsPushService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="linkJbpmToAppAction" class="com.xinxinsoft.action.appAction.LinkJbpmToAppCreatAction" scope="prototype">
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="loginVerifyAction" class="com.xinxinsoft.action.appAction.LoginVerifyBy4AAction" scope="prototype">
        <property name="user4AService">
            <ref bean="User4AService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="mentaryContractAction" class="com.xinxinsoft.action.contract.MentaryContractAction" scope="prototype"/>
    <bean id="messagesInfoAction" class="com.xinxinsoft.action.messages.MessagesInfoAction" scope="prototype">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="messagesInfoService">
            <ref bean="messagesInfoService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="missionConfigAction" class="com.xinxinsoft.action.MissionConfigAction.missionConfigAction" scope="prototype"/>
    <bean id="orderDetailAction" class="com.xinxinsoft.action.order.OrderDetailAction" scope="prototype">
        <property name="orderDetailService">
            <ref bean="orderDetailService"/>
        </property>
    </bean>
    <bean id="orderFormToAppAction" class="com.xinxinsoft.action.appAction.OrderFormToAppAction" scope="prototype">
        <property name="commonSingleService" ref="commonSingleService"/>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
    </bean>
    <bean id="orderListYearAction" class="com.xinxinsoft.action.commonSingManagement.OrderListYearAction" scope="prototype">
        <property name="service">
            <ref bean="sngListExcelService"/>
        </property>
        <property name="commonSingleService">
            <ref bean="commonSingleService"/>
        </property>
    </bean>
    <bean id="overTimeAction" class="com.xinxinsoft.action.overTime.OverTimeAction" scope="prototype">
        <property name="overTimeService">
            <ref bean="OverTimeService"/>
        </property>
        <property name="countShareService">
            <ref bean="CountShareService"/>
        </property>
    </bean>
    <bean id="permissionAction" class="com.xinxinsoft.action.core.PermissionAction" scope="prototype">
        <property name="loginUserService">
            <ref bean="LoginUserService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#queryTransparentInterfaceAction -->
    <bean id="queryTransparentInterfaceAction" class="com.xinxinsoft.action.httpAction.queryTransparentInterfaceAction" scope="prototype"/>
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <bean id="quickQueryDetailsAction" class="com.xinxinsoft.action.webOpenAction.QuickQueryDetailsAction" scope="prototype">
        <property name="querySer">
            <ref bean="VariousSqlQueryService"/>
        </property>
        <property name="sysSer">
            <ref bean="SystemUserService"/>
        </property>
        <property name="singSer">
            <ref bean="commonSingleService"/>
        </property>
        <property name="conSer">
            <ref bean="ContractService"/>
        </property>
        <property name="groupCustomerService">
            <ref bean="GroupCustomerService"/>
        </property>
        <property name="customerService">
            <ref bean="customerServices"/>
        </property>
        <property name="dediSer">
            <ref bean="DedicatedFlowService"/>
        </property>
    </bean>
    <bean id="quickqueryAction" class="com.xinxinsoft.action.webOpenAction.QuickQueryAction" scope="prototype">
        <property name="qqService">
            <ref bean="QuickQueryService"/>
        </property>
    </bean>
    <bean id="redRollListAction" class="com.xinxinsoft.action.redRollListAction.RedRollListAction" scope="prototype">
        <property name="redRollListService">
            <ref bean="redRollListService"/>
        </property>
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="claimForFundsService">
            <ref bean="ClaimForFundsService"/>
        </property>
    </bean>
    <bean id="singListExcelAction" class="com.xinxinsoft.action.commonSingManagement.singListExcelAction" scope="prototype">
        <property name="service">
            <ref bean="sngListExcelService"/>
        </property>
    </bean>
    <bean id="smsPushAction" class="com.xinxinsoft.action.smsPush.SmsPushAction" scope="prototype">
        <property name="smsPushService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <bean id="statisticsAction" class="com.xinxinsoft.action.statistics.StatisticsAction" scope="prototype">
        <property name="statisticsService">
            <ref bean="statisticsService"/>
        </property>
    </bean>
    <bean id="systemDeptAction" class="com.xinxinsoft.action.core.dept.SystemDeptAction" scope="prototype">
        <property name="sysDeptService">
            <ref bean="systemDeptService"/>
        </property>
    </bean>
    <bean id="testProcessAction" class="com.xinxinsoft.action.jbpmProcess.TestAction" scope="prototype">
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="jbpmProcessService">
            <ref bean="jbpmProcessService"/>
        </property>
    </bean>
    <!-- 仅在85环境中存在: bean#trmerAction -->
    <bean id="trmerAction" class="com.xinxinsoft.action.TimerTrigAction.TrmerAction" scope="prototype"/>
    <bean id="upGroupLevelProcessAction" class="com.xinxinsoft.action.dedicatedFlow.upGroupLevelProcessAction" scope="prototype">
        <property name="service">
            <ref bean="ExpenseApplyService"/>
        </property>
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="taskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="processService">
            <ref bean="processService"/>
        </property>
    </bean>
    <bean id="upTestAction" class="com.xinxinsoft.action.upTest.upTestAction" scope="prototype">
	</bean>
    <bean id="uploadToAppAction" class="com.xinxinsoft.action.appAction.UploadToAppAction" scope="prototype">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="waitTaskToAppAction" class="com.xinxinsoft.action.appAction.WaitTaskToAppAction" scope="prototype">
        <property name="singleService" ref="commonSingleService"/>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
    </bean>
</beans>