<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx"
	xsi:schemaLocation="
		    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		    http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
		    http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd"
	default-autowire="byName">

    <!-- EOM系统配置文件 - 详细合并版本 -->
    <!-- 此文件由85和86环境配置合并生成 -->
    <!-- 合并规则: 保留所有配置，详细标注差异 -->
    <!-- 生成时间: 自动生成 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#APPStatisticsAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="APPStatisticsAction" class="com.xinxinsoft.action.appAction.APPStatisticsAction"
    scope="prototype">
    <property name="statisticsService">
    <ref bean="statisticsService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#APPTransferInformationAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="APPTransferInformationAction" class="com.xinxinsoft.action.appAction.AppTransferInformationAction"
    scope="prototype">
    <property name="tInformationService">
    <ref bean="TransferInformationService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="zService">
    <ref bean="ZtreeUserService" />
    </property>
    <property name="tInformationTwoService">
    <ref bean="TransferInformationTwoService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AbnormalNumberAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AbnormalNumberAction" class="com.xinxinsoft.action.abnormalNumberAction.AbnormalNumberAction" scope="prototype"/>
    <!--手机电子合同-->
    <bean id="AppCustomClauseContractAction" class="com.xinxinsoft.action.appAction.AppCustomClauseContractAction" autowire="byType" scope="prototype">
    <property name="customClauseContractService">
    <ref bean="CustomClauseContractService" />
    </property>
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService"/>
    </property>
    <property name="signService">
    <ref bean="SignService"/>
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService"/>
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AccountOpenAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="AccountOpenAction" class="com.xinxinsoft.action.accountOpenAction.AccountOpenAction" scope="prototype">
    <property name="accountOpenService">
    <ref bean="accountOpenService" />
    </property>
    <property name="unitInfoService">
    <ref bean="UnitInfoService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AccountOrderInfoAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AccountOrderInfoAction" class="com.xinxinsoft.action.pay.AccountOrderInfoAction" scope="prototype">
    <property name="accountOrderInfoService">
    <ref bean="AccountOrderInfoService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ActivityAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ActivityAction" class="com.xinxinsoft.action.honorCode.ActivityAction"
    scope="prototype">
    <property name="activityService">
    <ref bean="ActivityService" />
    </property>
    <property name="channelInfoService">
    <ref bean="ChannelInfoService"/>
    </property>
    <property name="honorCodeBusinessService">
    <ref bean="HonorCodeBusinessService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AgreementAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AgreementAction" class="com.xinxinsoft.action.arrearsModule.AgreementAction"
    scope="prototype">
    <property name="agrService">
    <ref bean="AgreementService" />
    </property>
    <property name="arrearsService">
    <ref bean="ArrearsService" />
    </property>
    <property name="contractService">
    <ref bean="ContractService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AllPayDesignAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AllPayDesignAction" class="com.xinxinsoft.action.AllMembersPayAction.AllPayDesignAction" scope="prototype"/>
    <!--SIM物联网卡-->
    <bean id="SIMAction" class="com.xinxinsoft.action.SIM.SIMAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AndFlySpeedAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AndFlySpeedAction" class="com.xinxinsoft.action.andFlySpeedAction.AndFlySpeedAction" scope="prototype"/>
    <bean id="AppDocumentationAction" class="com.xinxinsoft.action.appAction.AppDocumentationAction" scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppClaimForFundsAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppClaimForFundsAction" class="com.xinxinsoft.action.appAction.AppClaimForFundsAction" scope="prototype" />
    <bean id="AppMonthlyinvoice" class="com.xinxinsoft.action.appAction.AppMonthlyinvoice" scope="prototype">
    <property name="monthlyinvoiceService">
    <ref bean="MonthlyinvoiceService" />
    </property>
    <property name="preinvApplyService">
    <ref bean="PreinvApplyService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppContractAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppContractAction" class="com.xinxinsoft.action.appAction.AppContractAction" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppContractFacticityLogAction -->
    <!-- 85环境: 4个属性, 0个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppContractFacticityLogAction" class="com.xinxinsoft.action.appAction.AppContractFacticityLogAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppCustomClauseContractAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppCustomClauseContractAction" class="com.xinxinsoft.action.appAction.AppCustomClauseContractAction" autowire="byType" scope="prototype">
    <property name="customClauseContractService">
    <ref bean="CustomClauseContractService" />
    </property>
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService"/>
    </property>
    <property name="signService">
    <ref bean="SignService"/>
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService"/>
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppDocumentationAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppDocumentationAction" class="com.xinxinsoft.action.appAction.AppDocumentationAction" scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppDownloadAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppDownloadAction" class="com.xinxinsoft.action.appAction.AppDownloadAction"
    scope="prototype">
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppDynamicContractAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppDynamicContractAction" class="com.xinxinsoft.action.contract.AppDynamicContractAction" scope="prototype"/>
    <!-- 系统保障管理类-->
    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <!-- 问卷调查管理类-->
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppGetOrPostWebApiAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#groupCustomerService; 仅86环境: {http://www.springframework.org/schema/beans}property#conSer -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppGetOrPostWebApiAction" class="com.xinxinsoft.action.appOpenAction.AppGetOrPostWebApiAction"
    scope="prototype">
    <property name="querySer">
    <ref bean="VariousSqlQueryService" />
    </property>
    <property name="sysSer">
    <ref bean="SystemUserService" />
    </property>
    <property name="singSer">
    <ref bean="commonSingleService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppGetOrPostWebQueryApiAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#dediSer -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppGetOrPostWebQueryApiAction" class="com.xinxinsoft.action.appOpenAction.AppGetOrPostWebQueryApiAction"
    scope="prototype">
    <property name="querySer">
    <ref bean="VariousSqlQueryService" />
    </property>
    <property name="sysSer">
    <ref bean="SystemUserService" />
    </property>
    <property name="singSer">
    <ref bean="commonSingleService" />
    </property>
    <property name="conSer">
    <ref bean="ContractService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="customerService">
    <ref bean="customerServices" />
    </property>
    <property name="dediSer">
    <ref bean="DedicatedFlowService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="AppGetOrPostWebQueryApiAction", class="com.xinxinsoft.action.appOpenAction.AppGetOrPostWebQueryApiAction", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppGroupInviteCodeAction -->
    <!-- 85环境: 4个属性, 0个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppGroupInviteCodeAction" class="com.xinxinsoft.action.appAction.AppGroupInviteCodeAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppHonorCodeBusinessAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppHonorCodeBusinessAction" class="com.xinxinsoft.action.httpAction.AppHonorCodeBusinessAction" scope="prototype">
    <property name="honorCodeBusinessService">
    <ref bean="HonorCodeBusinessService"/>
    </property>
    <property name="channelInfoService">
    <ref bean="ChannelInfoService"/>
    </property>
    <property name="contractService">
    <ref bean="ContractService"/>
    </property>
    <property name="service">
    <ref bean="SystemUserService" />
    </property>
    <property name="smsService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppMonthlyinvoice -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppMonthlyinvoice" class="com.xinxinsoft.action.appAction.AppMonthlyinvoice" scope="prototype">
    <property name="monthlyinvoiceService">
    <ref bean="MonthlyinvoiceService" />
    </property>
    <property name="preinvApplyService">
    <ref bean="PreinvApplyService"/>
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppOmsSellOrderAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppOmsSellOrderAction" class="com.xinxinsoft.action.appAction.AppOmsSellOrderAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="integrationService">
    <ref bean="IntegrationService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="customClauseContractService">
    <ref bean="CustomClauseContractService" />
    </property>
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppPreinvApplyAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppPreinvApplyAction" class="com.xinxinsoft.action.appAction.AppPreinvApplyAction" scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="preinvApplyService">
    <ref bean="PreinvApplyService"/>
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppReceiveApplyAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppReceiveApplyAction" class="com.xinxinsoft.action.appAction.AppReceiveApplyAction"
    scope="prototype">
    <property name="receiveApplyService">
    <ref bean="ReceiveApplyService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#AppServiceShutdownAndStartupAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: APP接口Action -->
    <!-- ===================================== -->

    <bean id="AppServiceShutdownAndStartupAction" class="com.xinxinsoft.action.appAction.AppServiceShutdownAndStartupAction" scope="prototype"/>
    <bean id="ClaimForFundsAct" class="com.xinxinsoft.filter.ClaimForFundsAct" scope="prototype"/>
    <!--正负补收额度管理-->
    <bean id="ReceiptApplyAmountAction" class="com.xinxinsoft.action.ReceiptApplyAction.ReceiptApplyAmountAction" scope="prototype"/>
    <!--和飞速成员管理-->
    <bean id="AndFlySpeedAction" class="com.xinxinsoft.action.andFlySpeedAction.AndFlySpeedAction" scope="prototype"/>
    <bean id="AppDocumentationAction" class="com.xinxinsoft.action.appAction.AppDocumentationAction" scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppUnitAccountAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppUnitAccountAction" class="com.xinxinsoft.action.appAction.AppUnitAccountAction" scope="prototype"/>
    <bean id="FilePreviewAction" class="com.xinxinsoft.action.filePreview.FilePreviewAction" scope="prototype"/>
    <!--集团添加或更新-->
    <bean id="GroupUpdateAction" class="com.xinxinsoft.action.groupUpdateAction.GroupUpdateAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppUploadAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppUploadAction" class="com.xinxinsoft.action.core.app.AppUploadAction"
    scope="prototype">
    <property name="appUploadService">
    <ref bean="AppUploadService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="editionService">
    <ref bean="EditionService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppUploadPassword -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppUploadPassword" class="com.xinxinsoft.action.appAction.AppUploadPassword"
    scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppUserHeadAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppUserHeadAction" class="com.xinxinsoft.action.core.app.AppUserHeadAction"
    scope="prototype">
    <property name="appUserInfoService">
    <ref bean="AppUserInfoService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppWorkOrderAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppWorkOrderAction" class="com.xinxinsoft.action.appAction.AppWorkOrderAction"
    scope="prototype">
    <property name="workOrderServcie">
    <ref bean="WorkOrderServcie" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#psService -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsAction" class="com.xinxinsoft.action.arrearsModule.ArrearsAction"
    scope="prototype">
    <property name="arrearsService">
    <ref bean="ArrearsService" />
    </property>
    <property name="psService">
    <ref bean="PaymentRecordService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="ArrearsAction", class="com.xinxinsoft.action.arrearsModule.ArrearsAction", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsSingAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsSingAction" class="com.xinxinsoft.action.arrearsModule.ArrearsSingAction" scope="prototype">
    <property name="arrearsService">
    <ref bean="ArrearsService" />
    </property>
    <property name="arrearsSingSerivce">
    <ref bean="ArrearsSingSerivce" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsStatisticalAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsStatisticalAction" class="com.xinxinsoft.action.arrearsModule.ArrearsStatisticalAction" scope="prototype">
    <property name="arrearsStatisticalService">
    <ref bean="ArrearsStatisticalService" />
    </property>
    <property name="arrearsService">
    <ref bean="ArrearsService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsWriteOffAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsWriteOffAction" class="com.xinxinsoft.action.arrearsWriteOffAction.ArrearsWriteOffAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AttachmentAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AttachmentAction" class="com.xinxinsoft.action.enclosure.AttachmentAction"
    scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="bogSerice">
    <ref bean="BOGetService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AttachmentActionTwo -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AttachmentActionTwo" class="com.xinxinsoft.action.attachmentAction.AttachmentAction" scope="prototype"/>
    <!--合同签章角色配置-->
    <bean id="missionConfigAction" class="com.xinxinsoft.action.MissionConfigAction.missionConfigAction" scope="prototype"/>
    <bean id="AppContractFacticityLogAction" class="com.xinxinsoft.action.appAction.AppContractFacticityLogAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AttachmentTypeAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AttachmentTypeAction" class="com.xinxinsoft.action.enclosure.AttachmentTypeAction"
    scope="prototype">
    <property name="attachmentTypeService">
    <ref bean="AttachmentTypeService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditMultipleAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditMultipleAction" class="com.xinxinsoft.action.auditWorksheet.AuditMultipleAction" scope="prototype">
    <property name="auditMultipleService">
    <ref bean="AuditMultipleService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditWorkListAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditWorkListAction" class="com.xinxinsoft.action.auditWorksheet.AuditWorkListAction" scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="auditWorksheetService">
    <ref bean="AuditWorksheetService" />
    </property>
    <property name="auditWorkListService">
    <ref bean="AuditWorkListService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditWorksheetAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditWorksheetAction"
    class="com.xinxinsoft.action.auditWorksheet.AuditWorksheetAction"
    scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="auditWorksheetService">
    <ref bean="AuditWorksheetService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditWorksheetActionTwo -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditWorksheetActionTwo"
    class="com.xinxinsoft.action.auditWorksheet.AuditWorksheetActionTwo"
    scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="auditWorksheetService">
    <ref bean="AuditWorksheetService" />
    </property>
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditWorksheetHttpAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditWorksheetHttpAction" class="com.xinxinsoft.action.httpAction.AuditWorksheetHttpAction" scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="auditWorksheetService">
    <ref bean="AuditWorksheetService" />
    </property>
    <property name="interfaceLogService">
    <ref bean="InterfaceLogService" />
    </property>
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BigAmountApplyAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BigAmountApplyAction" class="com.xinxinsoft.action.bigAmountApply.BigAmountApplyAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BossTacheAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BossTacheAction" class="com.xinxinsoft.action.basetype.BossTacheAction"
    scope="prototype">
    <property name="service">
    <ref bean="BossTacheService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinessEntityAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinessEntityAction" class="com.xinxinsoft.action.businessProc.BusinessEntityAction"
    scope="prototype">
    <property name="businessEntityService">
    <ref bean="BusinessEntityService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinessTypeAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinessTypeAction" class="com.xinxinsoft.action.basetype.BusinessTypeAction"
    scope="prototype">
    <property name="businessTypeService">
    <ref bean="BusinessTypeService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinssAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinssAction" class="com.xinxinsoft.action.businss.BusinssAction" scope="prototype">
    <property name="businssService">
    <ref bean="BusinssService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinssGrpJttfConRedInAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinssGrpJttfConRedInAction" class="com.xinxinsoft.action.businss.BusinssConnectorByGrpJttfConRedInAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinssTaxpayerNumberQryAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinssTaxpayerNumberQryAction" class="com.xinxinsoft.action.businss.BusinssConnectorByTaxpayerNumberQryAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ChannelInfoAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ChannelInfoAction" class="com.xinxinsoft.action.honorCode.ChannelInfoAction"
    scope="prototype">
    <property name="channelInfoService">
    <ref bean="ChannelInfoService" />
    </property>
    <property name="contractService">
    <ref bean="ContractService" />
    </property>
    <property name="honorCodeBusinessService">
    <ref bean="HonorCodeBusinessService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CheckAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CheckAction" class="com.xinxinsoft.action.checkentityAction.CheckAction"
    scope="prototype">
    <property name="checkService">
    <ref bean="CheckService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimAContractAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimAContractAction" class="com.xinxinsoft.action.contract.ClaimAContractAction" scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="claimAContractService">
    <ref bean="ClaimAContractService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundAction" class="com.xinxinsoft.action.claimForFunds.ClaimForFundsTwoAction" scope="prototype"/>
    <bean id="OmsOrderProductHttpAction" class="com.xinxinsoft.action.httpAction.OmsOrderProductHttpAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="V2omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundModelAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundModelAction" class="com.xinxinsoft.action.claimForFunds.claimForFundModelAction" scope="prototype"/>
    <bean id="ClaimForFundAction" class="com.xinxinsoft.action.claimForFunds.ClaimForFundsTwoAction" scope="prototype"/>
    <bean id="OmsOrderProductHttpAction" class="com.xinxinsoft.action.httpAction.OmsOrderProductHttpAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="V2omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundsAct -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundsAct" class="com.xinxinsoft.filter.ClaimForFundsAct" scope="prototype"/>
    <!--正负补收额度管理-->
    <bean id="ReceiptApplyAmountAction" class="com.xinxinsoft.action.ReceiptApplyAction.ReceiptApplyAmountAction" scope="prototype"/>
    <!--和飞速成员管理-->
    <bean id="AndFlySpeedAction" class="com.xinxinsoft.action.andFlySpeedAction.AndFlySpeedAction" scope="prototype"/>
    <bean id="AppDocumentationAction" class="com.xinxinsoft.action.appAction.AppDocumentationAction" scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundsAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#lateFeeMoneyDataService -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundsAction" class="com.xinxinsoft.action.claimForFunds.ClaimForFundsAction"
    scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils" />
    </property>
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="lateFeeMoneyDataService">
    <ref bean="LateFeeMoneyDataService" />
    </property>
    <!--<property name="customerServiceXml">
    <ref bean="customerServices"/>
    </property>
    <property name="customerService">
    <ref bean="GroupCustomerService"/>
    </property>-->
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="ClaimForFundsAction", class="com.xinxinsoft.action.claimForFunds.ClaimForFundsAction", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractAction -->
    <!-- 85环境: 3个属性, 11个子元素 -->
    <!-- 86环境: 3个属性, 11个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractAction" class="com.xinxinsoft.action.contract.ContractAction"
    scope="prototype">
    <property name="contractService">
    <ref bean="ContractService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="orderDetailService">
    <ref bean="orderDetailService" />
    </property>
    <property name="productTypeService">
    <ref bean="ProductTypeService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="contractUtils">
    <ref bean="ContractUtils" />
    </property>
    <property name="contractCsv">
    <ref bean="ContractCsv" />
    </property>
    <property name="contractToOrderCsv">
    <ref bean="ContractToOrderCsv" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractECHttpAction -->
    <!-- 85环境: 4个属性, 0个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractECHttpAction" class="com.xinxinsoft.action.httpAction.ContractECHttpAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractInterfaceAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractInterfaceAction" class="com.xinxinsoft.action.httpAction.ContractInterfaceAction" scope="prototype">
    <property name="customClauseContractService">
    <ref bean="CustomClauseContractService" />
    </property>
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService" />
    </property>
    <property name="signService">
    <ref bean="SignService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractServlet -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractServlet" class="com.xinxinsoft.action.httpAction.ContractServlet"
    scope="prototype">
    <property name="contractHttpService">
    <ref bean="ContractHttpService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractUniformityAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractUniformityAction" class="com.xinxinsoft.action.contractUniformityAction.ContractUniformityAction"
    scope="prototype">
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CoutomerAcceptanceAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CoutomerAcceptanceAction"
    class="com.xinxinsoft.action.dedicatedFlow.CoutomerAcceptanceAction"
    scope="prototype">
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="processService">
    <ref bean="processService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CustomerAccountAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CustomerAccountAction"
    class="com.xinxinsoft.action.customeraccount.CustomerAccountAction"
    scope="prototype">
    <property name="customerAccountService">
    <ref bean="CustomerAccountService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DedicatedFlowAction -->
    <!-- 85环境: 3个属性, 12个子元素 -->
    <!-- 86环境: 3个属性, 12个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DedicatedFlowAction" class="com.xinxinsoft.action.dedicatedFlow.DedicatedFlowAction"
    scope="prototype">
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="dictionaryService">
    <ref bean="DictionaryService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="cmccOpenService">
    <ref bean="CMCCOpenService" />
    </property>
    <property name="commonSingleService">
    <ref bean="commonSingleService" />
    </property>
    <property name="processService">
    <ref bean="processService" />
    </property>
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    <property name="lateService">
    <ref bean="LinkTemplateService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#DictOrderHttpAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="DictOrderHttpAction" class="com.xinxinsoft.action.httpAction.DictOrderHttpAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EditionAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EditionAction" class="com.xinxinsoft.action.core.app.EditionAction"
    scope="prototype">
    <property name="editionService">
    <ref bean="EditionService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EomCertAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EomCertAction" class="com.xinxinsoft.action.sign.EomCertAction" scope="prototype">
    <property name="signService">
    <ref bean="SignService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EomSealAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EomSealAction" class="com.xinxinsoft.action.sign.EomSealAction" scope="prototype">
    <property name="sealService">
    <ref bean="SealService" />
    </property>
    <property name="signService">
    <ref bean="SignService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EomSignAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EomSignAction" class="com.xinxinsoft.action.sign.EomSignAction" scope="prototype">
    <property name="signServie">
    <ref bean="SignService" />
    </property>
    <property name="sealServie">
    <ref bean="SealService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ExpenseApplyAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ExpenseApplyAction" class="com.xinxinsoft.action.dedicatedFlow.ExpenseApplyAction"
    scope="prototype">
    <property name="service">
    <ref bean="ExpenseApplyService" />
    </property>
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="processService">
    <ref bean="processService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#ExternalInterfaceAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="ExternalInterfaceAction" class="com.xinxinsoft.action.appAction.ExternalInterfaceAction" scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="iBossByNoService">
    <ref bean="IbbnService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#FilePreviewAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="FilePreviewAction" class="com.xinxinsoft.action.filePreview.FilePreviewAction" scope="prototype"/>
    <!--集团添加或更新-->
    <bean id="GroupUpdateAction" class="com.xinxinsoft.action.groupUpdateAction.GroupUpdateAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupCustomerAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupCustomerAction" class="com.xinxinsoft.action.groupcustomer.GroupCustomerAction"
    scope="prototype">
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="customerService">
    <ref bean="customerServices" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupCustomersAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupCustomersAction" class="com.xinxinsoft.action.groupUpdateAction.GroupCustomersAction" scope="prototype"/>
    <!--PC端API调用支付页面-->
    <bean id="PayApiAction" class="com.xinxinsoft.action.pay.PayApiAction" scope="prototype">
    <property name="payApiService">
    <ref bean="PayApiService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupInviteCodeHttpAction -->
    <!-- 85环境: 4个属性, 0个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupPayHttpAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupPayHttpAction" class="com.xinxinsoft.action.httpAction.GroupPayHttpAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupPaymentAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupPaymentAction" class="com.xinxinsoft.action.groupPaymentAction.GroupPaymentAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupPeopleAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupPeopleAction" class="com.xinxinsoft.action.customeraccount.GroupPeopleAction"
    scope="prototype">
    <property name="groupPeopleService">
    <ref bean="GroupPeopleService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupTaxpayerAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#GroupUpdateAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="GroupUpdateAction" class="com.xinxinsoft.action.groupUpdateAction.GroupUpdateAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GuideDocumentationAction -->
    <!-- 85环境: 4个属性, 0个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GuideDocumentationAction" class="com.xinxinsoft.action.httpAction.GuideDocumentationAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#HonorCodeBusinessAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="HonorCodeBusinessAction" class="com.xinxinsoft.action.honorCode.HonorCodeBusinessAction"
    scope="prototype">
    <property name="honorCodeBusinessService">
    <ref bean="HonorCodeBusinessService" />
    </property>
    <property name="contractService">
    <ref bean="ContractService"/>
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#HonorCodeInterfaceAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="HonorCodeInterfaceAction" class="com.xinxinsoft.action.honorCode.HonorCodeInterfaceAction" scope="prototype">
    <property name="activityService">
    <ref bean="ActivityService" />
    </property>
    <property name="channelInfoService">
    <ref bean="ChannelInfoService"/>
    </property>
    <property name="honorCodeBusinessService">
    <ref bean="HonorCodeBusinessService"/>
    </property>
    <property name="contractService">
    <ref bean="ContractService" />
    </property>
    <property name="service">
    <ref bean="SystemUserService" />
    </property>
    <property name="smsService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IBossByNoAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#noResApplyService, {http://www.springframework.org/schema/beans}property#systemUserService, {http://www.springframework.org/schema/beans}property#variousSqlQueryService, {http://www.springframework.org/schema/beans}property#customClauseContractService, {http://www.springframework.org/schema/beans}property#waitTaskService -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IBossByNoAction" class="com.xinxinsoft.action.IBossAction.IBossByNoAction"
    scope="prototype">
    <property name="iboss">
    <ref bean="IbbnService" />
    </property>
    <property name="noResApplyService">
    <ref bean="NoResApplyService" />
    </property>
    <property name="variousSqlQueryService">
    <ref bean="VariousSqlQueryService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="customClauseContractService">
    <ref bean="CustomClauseContractService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="IBossByNoAction", class="com.xinxinsoft.action.IBossAction.IBossByNoAction", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ICTApplicationAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ICTApplicationAction" class="com.xinxinsoft.action.ICT.ICTApplicationAction" scope="prototype">
    <property name="ictApplicationService">
    <ref bean="ICTApplicationService"/>
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IDCApplyAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IDCApplyAction" class="com.xinxinsoft.action.IDC.IDCApplyAction" scope="prototype">
    <!--<property name="applyService">
    <ref bean="IDCApplyService"/>
    </property>
    <property name="flowService">
    <ref bean="IDCFlowService"/>
    </property>
    <property name="taskService">
    <ref bean="IDCTaskService"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil"/>
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="tInformationService">
    <ref bean="TransferInformationService" />
    </property>
    <property name="cmccOpenService">
    <ref bean="CMCCOpenService" />
    </property>-->
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IGrpOrderInfoAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IGrpOrderInfoAction" class="com.xinxinsoft.action.IBossAction.IGrpOrderInfoAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IMSHighFrequencyAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IMSHighFrequencyAction" class="com.xinxinsoft.action.IMSHighFrequencyAction.IMSHighFrequencyAction" scope="prototype"></bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#IctContractAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 合同管理Action -->
    <!-- ===================================== -->

    <bean id="IctContractAction" class="com.xinxinsoft.action.IctContract.IctContractAction"
    scope="prototype">
    <property name="ictContractService">
    <ref bean="IctContractService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IndustryTerminalAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IndustryTerminalAction" class="com.xinxinsoft.action.IndustryTerminal.IndustryTerminalAction" scope="prototype">
    <property name="industryTerminalService">
    <ref bean="IndustryTerminalService" />
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IndustryTerminalBySMMCCalculateARPUAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IndustryTerminalBySMMCCalculateARPUAction" class="com.xinxinsoft.action.IndustryTerminal.IndustryTerminalBySMMCCalculateARPUAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#InquiryOrderAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="InquiryOrderAction" class="com.xinxinsoft.action.InquiryOrder.InquiryOrderAction"
    scope="prototype">
    <property name="inquiryOrderService">
    <ref bean="InquiryOrderService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IntegrationAction -->
    <!-- 85环境: 3个属性, 8个子元素 -->
    <!-- 86环境: 3个属性, 8个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IntegrationAction" class="com.xinxinsoft.action.v2.integrationAction.IntegrationAction" scope="prototype">
    <property name="commonSingleService">
    <ref bean="commonSingleService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="integrationService">
    <ref bean="IntegrationService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="processService">
    <ref bean="processService" />
    </property>
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#InvoiceReceiveOrder -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="InvoiceReceiveOrder" class="com.xinxinsoft.action.httpAction.InvoiceReceiveOrder" scope="prototype">
    <property name="monthlyinvoiceService">
    <ref bean="MonthlyinvoiceService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#JbpmTest -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="JbpmTest" class="com.xinxinsoft.action.test.JbpmTest">
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#JobApprovalAction -->
    <!-- 85环境: 4个属性, 2个子元素 -->
    <!-- 86环境: 4个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="JobApprovalAction" class="com.xinxinsoft.action.appOpenAction.JobApprovalAction" autowire="byType" scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="noResApplyService">
    <ref bean="NoResApplyService"/>
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#JobLogAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="JobLogAction" class="com.xinxinsoft.action.executejoblog.JobLogAction" scope="prototype">
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#JobRejectionInfoAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="JobRejectionInfoAction" class="com.xinxinsoft.action.jobRejectionInfoAction.JobRejectionInfoAction" scope="prototype">
    <property name="jobRejectionInfoService">
    <ref bean="JobRejectionInfoService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="receiveApplyService">
    <ref bean="ReceiveApplyService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LinkScoreAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LinkScoreAction" class="com.xinxinsoft.action.linkScore.LinkScoreAction"
    scope="prototype">
    <property name="linkScoreService">
    <ref bean="LinkScoreService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LinkTemplateAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LinkTemplateAction" class="com.xinxinsoft.action.processLink.LinkTemplateAction"
    scope="prototype">
    <property name="service">
    <ref bean="LinkTemplateService" />
    </property>
    <property name="dictionaryService">
    <ref bean="DictionaryService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LoginUserAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LoginUserAction" class="com.xinxinsoft.action.core.user.LoginUserAction"
    scope="prototype">
    <property name="loginUserService">
    <ref bean="LoginUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LwFtpAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LwFtpAction" class="com.xinxinsoft.action.LwFtpAction.LwFtpAction"
    scope="prototype">
    <property name="lwFtpTask">
    <ref bean="lwFtpTask" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ManualInvApplyAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ManualInvApplyAction" class="com.xinxinsoft.action.manualInvApply.ManualInvApplyAction" scope="prototype">
    <property name="manualInvApplyService">
    <ref bean="ManualInvApplyService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MarketActivitiesAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MarketActivitiesAction" class="com.xinxinsoft.action.MarketActivitiesAction.MarketActivitiesAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MarketingActivitiesAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MarketingActivitiesAction" class="com.xinxinsoft.action.marketingActivitiesAction.MarketingActivitiesAction" scope="prototype"/>
    <!-- 合同补录管理类-->
    <bean id="mentaryContractAction" class="com.xinxinsoft.action.contract.MentaryContractAction" scope="prototype"/>
    <bean id="appMentaryContractAction" class="com.xinxinsoft.action.appAction.AppMentaryContractAction" scope="prototype"/>
    <bean id="ContractECHttpAction" class="com.xinxinsoft.action.httpAction.ContractECHttpAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MarketingActivitiesActionTwo -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MarketingActivitiesActionTwo" class="com.xinxinsoft.action.marketingActivitiesAction.MarketingActivitiesActionTwo" scope="prototype"/>
    <!--营销活动工单管理-->
    <bean id="MarketingActivitiesAction" class="com.xinxinsoft.action.marketingActivitiesAction.MarketingActivitiesAction" scope="prototype"/>
    <!-- 合同补录管理类-->
    <bean id="mentaryContractAction" class="com.xinxinsoft.action.contract.MentaryContractAction" scope="prototype"/>
    <bean id="appMentaryContractAction" class="com.xinxinsoft.action.appAction.AppMentaryContractAction" scope="prototype"/>
    <bean id="ContractECHttpAction" class="com.xinxinsoft.action.httpAction.ContractECHttpAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MenuAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MenuAction" class="com.xinxinsoft.action.core.role.MenuAction"
    scope="prototype">
    <property name="menuService">
    <ref bean="MenuService"></ref>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MoaAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OMSAction -->
    <!-- 85环境: 4个属性, 5个子元素 -->
    <!-- 86环境: 4个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#attachmentService -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OMSAction" class="com.xinxinsoft.action.appOpenAction.OMSAction" autowire="byType" scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="querySer">
    <ref bean="VariousSqlQueryService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="OMSAction", class="com.xinxinsoft.action.appOpenAction.OMSAction", autowire="byType", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsOrderProductAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsOrderProductAction" class="com.xinxinsoft.action.oms.OmsOrderProductAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#OmsOrderProductHttpAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 订单管理Action -->
    <!-- ===================================== -->

    <bean id="OmsOrderProductHttpAction" class="com.xinxinsoft.action.httpAction.OmsOrderProductHttpAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="V2omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsOrderWorkbenchAction -->
    <!-- 85环境: 3个属性, 9个子元素 -->
    <!-- 86环境: 3个属性, 9个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsOrderWorkbenchAction" class="com.xinxinsoft.action.oms.OmsOrderWorkbenchAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="unitInfoService">
    <ref bean="UnitInfoService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsSellOrderAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsSellOrderAction" class="com.xinxinsoft.action.oms.OmsSellOrderAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#OmsStatisticalAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 订单管理Action -->
    <!-- ===================================== -->

    <bean id="OmsStatisticalAction" class="com.xinxinsoft.action.oms.OmsStatisticalAction" scope="prototype">
    <property name="omsStatisticalService">
    <ref bean="OmsStatisticalService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#OneClickOrderAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="OneClickOrderAction" class="com.xinxinsoft.action.appAction.OneClickOrderAction" scope="prototype">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#Open4AStateAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="Open4AStateAction" class="com.xinxinsoft.action.appAction.Open4AStateAction"
    scope="prototype">
    <property name="open4AStateService">
    <ref bean="Open4AStateService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#OrderTesting -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 配置组件 -->
    <!-- ===================================== -->

    <bean id="OrderTesting" class="com.xinxinsoft.action.oms.OrderTesting" scope="prototype">
    <property name="testingService">
    <ref bean="ServiceStandardizationTestingService" />
    </property>
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PMSAction -->
    <!-- 85环境: 4个属性, 2个子元素 -->
    <!-- 86环境: 4个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PMSAction" class="com.xinxinsoft.action.appOpenAction.PMSAction" autowire="byType" scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="pmsService">
    <ref bean="PMSService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PayApiAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PayApiAction" class="com.xinxinsoft.action.pay.PayApiAction" scope="prototype">
    <property name="payApiService">
    <ref bean="PayApiService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PayProviderInfoAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PayProviderInfoAction" class="com.xinxinsoft.action.pay.PayProviderInfoAction" scope="prototype">
    <property name="orderQuerySendService">
    <ref bean="OrderQuerySendService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#PayRefundAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 支付相关Action -->
    <!-- ===================================== -->

    <bean id="PayRefundAction" class="com.xinxinsoft.action.pay.PayRefundAction" autowire="byType" scope="prototype">
    <property name="payRefundService">
    <ref bean="PayRefundService"/>
    </property>
    <property name="paymentService">
    <ref bean="PaymentService"/>
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#PaymentAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 支付相关Action -->
    <!-- ===================================== -->

    <bean id="PaymentAction" class="com.xinxinsoft.action.pay.PaymentAction" autowire="byType" scope="prototype">
    <property name="paymentService">
    <ref bean="PaymentService"/>
    </property>
    <property name="payApiService">
    <ref bean="PayApiService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PaymentOrderAction -->
    <!-- 85环境: 4个属性, 3个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#paymentProviderService, {http://www.springframework.org/schema/beans}property#paymentOrderService, {http://www.springframework.org/schema/beans}property#variousSqlQueryService -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PaymentOrderAction" class="com.xinxinsoft.action.pay.PaymentOrderAction" autowire="byType" scope="prototype">
    <property name="paymentOrderService">
    <ref bean="PaymentOrderService" />
    </property>
    <property name="paymentProviderService">
    <ref bean="PaymentProviderService" />
    </property>
    <property name="variousSqlQueryService">
    <ref bean="VariousSqlQueryService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="PaymentOrderAction", class="com.xinxinsoft.action.pay.PaymentOrderAction", autowire="byType", scope="prototype" -->
    <!-- 子元素:  -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PaymentRecordAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PaymentRecordAction" class="com.xinxinsoft.action.arrearsModule.PaymentRecordAction"
    scope="prototype">
    <property name="pService">
    <ref bean="PaymentRecordService" />
    </property>
    <property name="arrearsService">
    <ref bean="ArrearsService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PreinvApplyAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PreinvApplyAction" class="com.xinxinsoft.action.PreinvApply.PreinvApplyAction" scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="preinvApplyService">
    <ref bean="PreinvApplyService"/>
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PreinvApplyMarkingAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PreinvApplyMarkingAction" class="com.xinxinsoft.action.preinvApplyMarkingAction.PreinvApplyMarkingAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PreinvIoTCardAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ProductFlowAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ProductFlowAction" class="com.xinxinsoft.action.processLink.ProductFlowAction"
    scope="prototype">
    <property name="service">
    <ref bean="ProductFlowService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ProductTypeAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ProductTypeAction" class="com.xinxinsoft.action.basetype.ProductTypeAction"
    scope="prototype">
    <property name="service">
    <ref bean="ProductTypeService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#QuestionAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="QuestionAction" class="com.xinxinsoft.action.checkentityAction.QuestionAction"
    scope="prototype">
    <property name="questionService">
    <ref bean="QuestionService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RealNameReservAtion -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RealNameReservAtion" class="com.xinxinsoft.action.RealNameReservAtion.realNameReservAtion" scope="prototype"/>
    <!--318附件管理-->
    <bean id="AttachmentActionTwo" class="com.xinxinsoft.action.attachmentAction.AttachmentAction" scope="prototype"/>
    <!--合同签章角色配置-->
    <bean id="missionConfigAction" class="com.xinxinsoft.action.MissionConfigAction.missionConfigAction" scope="prototype"/>
    <bean id="AppContractFacticityLogAction" class="com.xinxinsoft.action.appAction.AppContractFacticityLogAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReceiptApplyAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReceiptApplyAction" class="com.xinxinsoft.action.ReceiptApplyAction.ReceiptApplyAction"
    scope="prototype">
    <property name="receiptApplyService">
    <ref bean="ReceiptApplyService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReceiptApplyAmountAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReceiptApplyAmountAction" class="com.xinxinsoft.action.ReceiptApplyAction.ReceiptApplyAmountAction" scope="prototype"/>
    <!--和飞速成员管理-->
    <bean id="AndFlySpeedAction" class="com.xinxinsoft.action.andFlySpeedAction.AndFlySpeedAction" scope="prototype"/>
    <bean id="AppDocumentationAction" class="com.xinxinsoft.action.appAction.AppDocumentationAction" scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReceiveApplyAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReceiveApplyAction" class="com.xinxinsoft.action.receiveApply.ReceiveApplyAction"
    scope="prototype">
    <property name="receiveApplyService">
    <ref bean="ReceiveApplyService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RecommendedManagementAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RecommendedManagementAction"
    class="com.xinxinsoft.action.recommendedManagementAction.RecommendedManagementAction"
    scope="prototype">
    <property name="recommendedManagementService">
    <ref bean="RecommendedManagementService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReconciliationAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReconciliationAction" class="com.xinxinsoft.action.pay.ReconciliationAction" scope="prototype">
    <property name="reconciliationService">
    <ref bean="ReconciliationService" />
    </property>
    <property name="settlementService">
    <ref bean="SettlementService" />
    </property>
    <property name="variousSqlQueryService">
    <ref bean="VariousSqlQueryService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReductionICTApplicationAction -->
    <!-- 85环境: 3个属性, 8个子元素 -->
    <!-- 86环境: 3个属性, 8个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReductionICTApplicationAction" class="com.xinxinsoft.action.ICT.ReductionICTApplicationAction" scope="prototype">
    <property name="reductionIctApplicationService">
    <ref bean="ReductionICTApplicationService"/>
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="ictApplicationService">
    <ref bean="ICTApplicationService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RefundOrderAction -->
    <!-- 85环境: 4个属性, 3个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#paymentProviderService, {http://www.springframework.org/schema/beans}property#refundOrderService, {http://www.springframework.org/schema/beans}property#variousSqlQueryService -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RefundOrderAction" class="com.xinxinsoft.action.pay.RefundOrderAction" autowire="byType" scope="prototype">
    <property name="refundOrderService">
    <ref bean="RefundOrderService" />
    </property>
    <property name="paymentProviderService">
    <ref bean="PaymentProviderService" />
    </property>
    <property name="variousSqlQueryService">
    <ref bean="VariousSqlQueryService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="RefundOrderAction", class="com.xinxinsoft.action.pay.RefundOrderAction", autowire="byType", scope="prototype" -->
    <!-- 子元素:  -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RejectOrderAction -->
    <!-- 85环境: 4个属性, 2个子元素 -->
    <!-- 86环境: 4个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RejectOrderAction" class="com.xinxinsoft.action.rejectWordkOrderAction.RejectOrderAction"  autowire="byType" scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="noResApplyService">
    <ref bean="NoResApplyService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RiskClosedLoopAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RiskClosedLoopAction" class="com.xinxinsoft.action.riskClosedLoop.RiskClosedLoopAction" scope="prototype">
    <property name="riskClosedLoopService">
    <ref bean="RiskClosedLoopService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#RiskControlAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RiskDutyAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RiskDutyAction" class="com.xinxinsoft.action.RiskDuty.RiskDutyAction" scope="prototype"/>
    <!--资金认领新版-->
    <bean id="ClaimForFundModelAction" class="com.xinxinsoft.action.claimForFunds.claimForFundModelAction" scope="prototype"/>
    <bean id="ClaimForFundAction" class="com.xinxinsoft.action.claimForFunds.ClaimForFundsTwoAction" scope="prototype"/>
    <bean id="OmsOrderProductHttpAction" class="com.xinxinsoft.action.httpAction.OmsOrderProductHttpAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="V2omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RiskcontrolDerivedAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RiskcontrolDerivedAction" class="com.xinxinsoft.action.riskcontrolDerivedAction.RiskcontrolderivedAction"
    scope="prototype">
    <property name="riskcontrolDerivedService">
    <ref bean="RiskcontrolDerivedService" />
    </property>
    <property name="preinvApplyService">
    <ref bean="PreinvApplyService" />
    </property>
    <property name="whiteListInformationService">
    <ref bean="WhiteListInformationService" />
    </property>
    <property name="arrearsSingSerivce">
    <ref bean="ArrearsSingSerivce" />
    </property>
    <property name="arrearsService">
    <ref bean="ArrearsService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RoleAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RoleAction" class="com.xinxinsoft.action.core.role.RoleAction"
    scope="prototype">
    <property name="roleService">
    <ref bean="RoleService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#S4000CfmAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="S4000CfmAction" class="com.xinxinsoft.action.httpAction.S4000CfmAction" scope="prototype">
    <property name="interfaceLogService">
    <ref bean="InterfaceLogService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SIMAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SIMAction" class="com.xinxinsoft.action.SIM.SIMAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SRestoreDeadUserAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SRestoreDeadUserAction" class="com.xinxinsoft.action.SRestoreDeadUser.SRestoreDeadUserAction" scope="prototype"/>
    <!--APP账户（号码）校验方法类-->
    <bean id="appVerificAction" class="com.xinxinsoft.action.appAction.AppVerificAction" scope="prototype"/>
    <bean id="GuideDocumentationAction" class="com.xinxinsoft.action.httpAction.GuideDocumentationAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SaveSendAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SaveSendAction" class="com.xinxinsoft.action.SaveSendAction.SaveSendAction"
    scope="prototype">
    <property name="saveSendService">
    <ref bean="SaveSendService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SaveSendCheckAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SaveSendCheckAction"
    class="com.xinxinsoft.action.SaveSendAction.SaveSendCheckAction"
    scope="prototype">
    <property name="saveSendCheckService">
    <ref bean="SaveSendCheckService" />
    </property>
    <property name="saveSendService">
    <ref bean="SaveSendService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="saveSendCheckTimeService">
    <ref bean="SaveSendCheckTimeService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#ServiceStandardizationTestingAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="ServiceStandardizationTestingAction" class="com.xinxinsoft.action.oms.ServiceStandardizationTestingAction" scope="prototype">
    <property name="testingService">
    <ref bean="ServiceStandardizationTestingService" />
    </property>
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SpeciaPlanAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SpeciaPlanAction" class="com.xinxinsoft.action.SpeciaPlanAction.SpeciaPlanAction" scope="prototype"/>
    <!--一键甩单APP统计分析-->
    <bean id="OmsStatisticalAction" class="com.xinxinsoft.action.oms.OmsStatisticalAction" scope="prototype">
    <property name="omsStatisticalService">
    <ref bean="OmsStatisticalService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SupCollectSAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SupCollectSAction" class="com.xinxinsoft.action.SaveSendAction.SupCollectSAction"
    scope="prototype">
    <property name="supCollectService">
    <ref bean="SupCollectService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="suoCollectCheckTimeService">
    <ref bean="SuoCollectCheckTimeService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SuspensionApplicationAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SuspensionApplicationAction" class="com.xinxinsoft.action.suspensionApplicationAction.SuspensionApplicationAction" scope="prototype">
    <property name="saService">
    <ref bean="SuspensionApplicationService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SystemOrganizationAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SystemOrganizationAction" class="com.xinxinsoft.action.core.org.SystemOrganizationAction"
    scope="prototype">
    <property name="orgService">
    <ref bean="SystemOrganizationService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SystemUserAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SystemUserAction" class="com.xinxinsoft.action.core.user.SystemUserAction"
    scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TariffManagementAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TariffManagementAction" class="com.xinxinsoft.action.tariffManagement.TariffManagementAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TerminalAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TerminalAction" class="com.xinxinsoft.action.SaveSendAction.TerminalAction"
    scope="prototype">
    <property name="terminalService">
    <ref bean="TerminalService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="terminalCheckTimeService">
    <ref bean="TerminalCheckTimeService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TerminalActivityAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TerminalActivityAction" class="com.xinxinsoft.action.terminalActivityAdministration.TerminalActivityAction" scope="prototype"/>
    <!-- 集团账户管理类-->
    <bean id="groupAccountAction" class="com.xinxinsoft.action.groupAccountAction.GroupAccountAction" scope="prototype"/>
    <bean id="appGroupAccountAction" class="com.xinxinsoft.action.appAction.AppGroupAccountAction" scope="prototype"/>
    <!--app动态导入合同数据-->
    <bean id="AppDynamicContractAction" class="com.xinxinsoft.action.contract.AppDynamicContractAction" scope="prototype"/>
    <!-- 系统保障管理类-->
    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <!-- 问卷调查管理类-->
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferAccountsAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferAccountsAction"
    class="com.xinxinsoft.action.SaveSendAction.TransferAccountsAction"
    scope="prototype">
    <property name="transferAccountsService">
    <ref bean="TransferAccountsService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="transferAccountsCheckTimeService">
    <ref bean="TransferAccountsCheckTimeService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferAction" class="com.xinxinsoft.action.transfer.TransferAction" scope="prototype">
    <property name="transferService">
    <ref bean="TransferService" />
    </property>
    <property name="transferInterfaceService">
    <ref bean="TransferInterfaceService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferCitiesDataAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferCitiesDataAction" class="com.xinxinsoft.action.transfer.TransferCitiesDataAction" scope="prototype">
    <property name="transferCitiesDataService">
    <ref bean="TransferCitiesDataService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferInformationAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferInformationAction" class="com.xinxinsoft.action.transfer.TransferInformationAction"
    scope="prototype">
    <property name="tInformationService">
    <ref bean="TransferInformationService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferInformationTwoAction -->
    <!-- 85环境: 3个属性, 8个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#attachmentService -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferInformationTwoAction" class="com.xinxinsoft.action.transfer.TransferInformationTwoAction"
    scope="prototype">
    <property name="tInformationService">
    <ref bean="TransferInformationTwoService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="lateFeeMoneyDataService">
    <ref bean="LateFeeMoneyDataService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="TransferInformationTwoAction", class="com.xinxinsoft.action.transfer.TransferInformationTwoAction", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TripartiparteUserAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#USIMAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#UnitInfoAction -->
    <!-- 85环境: 4个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: autowire(仅85环境) -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="UnitInfoAction" class="com.xinxinsoft.action.ums.UnitInfoAction" autowire="byType" scope="prototype">
    <property name="unitInfoService">
    <ref bean="UnitInfoService"/>
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="iBossByNoService">
    <ref bean="IbbnService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="UnitInfoAction", class="com.xinxinsoft.action.ums.UnitInfoAction", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#UpdateCustomerAcceptanceAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="UpdateCustomerAcceptanceAction"
    class="com.xinxinsoft.action.appAction.UpdateCustomerAcceptanceAction"
    scope="prototype">
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="processService">
    <ref bean="processService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#UploadDocumentAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="UploadDocumentAction" class="com.xinxinsoft.action.uploadDocumentAction.UploadDocumentAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#V2AppOmsSellOrderAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="V2AppOmsSellOrderAction" class="com.xinxinsoft.action.appAction.V2AppOmsSellOrderAction" scope="prototype">
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#V2OmsOrderWorkbenchAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="V2OmsOrderWorkbenchAction" class="com.xinxinsoft.action.oms.V2OmsOrderWorkbenchAction" scope="prototype">
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#V2OmsSellOrderAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="V2OmsSellOrderAction" class="com.xinxinsoft.action.oms.V2OmsSellOrderAction" scope="prototype">
    <property name="omsOrderWorkbenchService">
    <ref bean="OmsOrderWorkbenchService" />
    </property>
    <property name="omsOrderProductService">
    <ref bean="OmsOrderProductService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ValuableCardAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ValuableCardAction" class="com.xinxinsoft.action.valuableCard.ValuableCardAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WaitTaskAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WaitTaskAction" class="com.xinxinsoft.action.waitTask.WaitTaskAction"
    scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WhiteListAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WhiteListAction" class="com.xinxinsoft.action.whiteList.WhiteListAction"
    scope="prototype">
    <property name="whiteListService">
    <ref bean="WhiteListService" />
    </property>
    <property name="whiteListInformationService">
    <ref bean="WhiteListInformationService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WhiteListInformationAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WhiteListInformationAction" class="com.xinxinsoft.action.whiteList.WhiteListInformationAction"
    scope="prototype">
    <property name="whiteListInformationService">
    <ref bean="WhiteListInformationService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WhiteRollListAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WhiteRollListAction" class="com.xinxinsoft.action.whiteRollListAction.WhiteRollListAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WorkOrderAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WorkOrderAction" class="com.xinxinsoft.action.workOrder.WorkOrderAction"
    scope="prototype">
    <property name="workOrderServcie">
    <ref bean="WorkOrderServcie" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ZtreeUserAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ZtreeUserAction" class="com.xinxinsoft.action.core.user.ZtreeUserAction"
    scope="prototype">
    <property name="service">
    <ref bean="ZtreeUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="sopSevvice">
    <ref bean="StructureOfPersonnelService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#activityPreinvApplyAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="activityPreinvApplyAction" class="com.xinxinsoft.action.activityPreinvAction.ActivityPreinvApplyAction" scope="prototype">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="activityPreinvApplyService">
    <ref bean="ActivityPreinvApplyService"/>
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appFourtOpenAction -->
    <!-- 85环境: 4个属性, 0个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appFourtOpenAction" class="com.xinxinsoft.action.appAction.AppFourtOpenAction" scope="prototype" autowire="byType" />
    <!--终端活动管理-->
    <bean id="TerminalActivityAction" class="com.xinxinsoft.action.terminalActivityAdministration.TerminalActivityAction" scope="prototype"/>
    <!-- 集团账户管理类-->
    <bean id="groupAccountAction" class="com.xinxinsoft.action.groupAccountAction.GroupAccountAction" scope="prototype"/>
    <bean id="appGroupAccountAction" class="com.xinxinsoft.action.appAction.AppGroupAccountAction" scope="prototype"/>
    <!--app动态导入合同数据-->
    <bean id="AppDynamicContractAction" class="com.xinxinsoft.action.contract.AppDynamicContractAction" scope="prototype"/>
    <!-- 系统保障管理类-->
    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <!-- 问卷调查管理类-->
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appGroupAccountAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appGroupAccountAction" class="com.xinxinsoft.action.appAction.AppGroupAccountAction" scope="prototype"/>
    <!--app动态导入合同数据-->
    <bean id="AppDynamicContractAction" class="com.xinxinsoft.action.contract.AppDynamicContractAction" scope="prototype"/>
    <!-- 系统保障管理类-->
    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <!-- 问卷调查管理类-->
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appLoginAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appLoginAction" class="com.xinxinsoft.action.appLoginAction.appLoginAction"
    scope="prototype">
    <property name="apploginService">
    <ref bean="appLoginService" />
    </property>
    <property name="countShareService">
    <ref bean="CountShareService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appMentaryContractAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appMentaryContractAction" class="com.xinxinsoft.action.appAction.AppMentaryContractAction" scope="prototype"/>
    <bean id="ContractECHttpAction" class="com.xinxinsoft.action.httpAction.ContractECHttpAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appQuestionnaireAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appRiskDutyAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appRiskDutyAction" class="com.xinxinsoft.action.appAction.appRiskDutyAction" scope="prototype"/>
    <bean id="RiskDutyAction" class="com.xinxinsoft.action.RiskDuty.RiskDutyAction" scope="prototype"/>
    <!--资金认领新版-->
    <bean id="ClaimForFundModelAction" class="com.xinxinsoft.action.claimForFunds.claimForFundModelAction" scope="prototype"/>
    <bean id="ClaimForFundAction" class="com.xinxinsoft.action.claimForFunds.ClaimForFundsTwoAction" scope="prototype"/>
    <bean id="OmsOrderProductHttpAction" class="com.xinxinsoft.action.httpAction.OmsOrderProductHttpAction" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="V2omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appVerificAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appVerificAction" class="com.xinxinsoft.action.appAction.AppVerificAction" scope="prototype"/>
    <bean id="GuideDocumentationAction" class="com.xinxinsoft.action.httpAction.GuideDocumentationAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#attachmentDownloadAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="attachmentDownloadAction"
    class="com.xinxinsoft.action.appAction.AttachmentDownloadAction"
    scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#b1000OpenSev -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="b1000OpenSev" class="com.xinxinsoft.action.appOpenAction.Broadband1000OpeningAction"
    scope="prototype">
    <property name="varSer">
    <ref bean="Various1000SqlQueryService" />
    </property>
    <property name="sysSer">
    <ref bean="SystemUserService" />
    </property>
    <property name="singSer">
    <ref bean="commonSingleService" />
    </property>
    <property name="querySer">
    <ref bean="VariousSqlQueryService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#b1000QuerySev -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="b1000QuerySev" class="com.xinxinsoft.action.appOpenAction.Broadband1000OpeningQueryAction"
    scope="prototype">
    <property name="varSer">
    <ref bean="Various1000SqlQueryService" />
    </property>
    <property name="sysSer">
    <ref bean="SystemUserService" />
    </property>
    <property name="singSer">
    <ref bean="commonSingleService" />
    </property>
    <property name="querySer">
    <ref bean="VariousSqlQueryService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#callingSystemValidationAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#claimFundReturnAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#commonSingleAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="commonSingleAction"
    class="com.xinxinsoft.action.commonSingManagement.CommonSingleAction"
    scope="prototype">
    <property name="commonSingleService" ref="commonSingleService"></property>
    <property name="businessTypeService" ref="BusinessTypeService"></property>
    <property name="productTypeService" ref="ProductTypeService"></property>
    <property name="groupCustomerService" ref="GroupCustomerService"></property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#countShareAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="countShareAction" class="com.xinxinsoft.action.countShare.CountShareAction"
    scope="prototype">
    <property name="countShareService">
    <ref bean="CountShareService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#customClauseContractAction -->
    <!-- 85环境: 4个属性, 5个子元素 -->
    <!-- 86环境: 4个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="customClauseContractAction" class="com.xinxinsoft.action.contract.CustomClauseContractAction" autowire="byType" scope="prototype">
    <property name="customClauseContractService">
    <ref bean="CustomClauseContractService" />
    </property>
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService"/>
    </property>
    <property name="signService">
    <ref bean="SignService"/>
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService"/>
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#customerAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="customerAction" class="com.xinxinsoft.action.customer.customerAction"
    scope="prototype">
    <property name="customerService">
    <ref bean="customerService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#deleteAttachmentAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="deleteAttachmentAction" class="com.xinxinsoft.action.appAction.DeleteAttachmentAction"
    scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#dictionaryAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="dictionaryAction" class="com.xinxinsoft.action.core.DictionaryAction"
    scope="prototype">
    <property name="dicService">
    <ref bean="DictionaryService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#fourtOpenAction -->
    <!-- 85环境: 4个属性, 0个子元素 -->
    <!-- 86环境: 4个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="fourtOpenAction" class="com.xinxinsoft.action.fourtOpenAction.fourtOpenAction" scope="prototype" autowire="byType" />
    <bean id="appFourtOpenAction" class="com.xinxinsoft.action.appAction.AppFourtOpenAction" scope="prototype" autowire="byType" />
    <!--终端活动管理-->
    <bean id="TerminalActivityAction" class="com.xinxinsoft.action.terminalActivityAdministration.TerminalActivityAction" scope="prototype"/>
    <!-- 集团账户管理类-->
    <bean id="groupAccountAction" class="com.xinxinsoft.action.groupAccountAction.GroupAccountAction" scope="prototype"/>
    <bean id="appGroupAccountAction" class="com.xinxinsoft.action.appAction.AppGroupAccountAction" scope="prototype"/>
    <!--app动态导入合同数据-->
    <bean id="AppDynamicContractAction" class="com.xinxinsoft.action.contract.AppDynamicContractAction" scope="prototype"/>
    <!-- 系统保障管理类-->
    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <!-- 问卷调查管理类-->
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ftpHandleAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ftpHandleAction" class="com.xinxinsoft.action.core.FtpHandleAction"
    scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#groupAccountAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="groupAccountAction" class="com.xinxinsoft.action.groupAccountAction.GroupAccountAction" scope="prototype"/>
    <bean id="appGroupAccountAction" class="com.xinxinsoft.action.appAction.AppGroupAccountAction" scope="prototype"/>
    <!--app动态导入合同数据-->
    <bean id="AppDynamicContractAction" class="com.xinxinsoft.action.contract.AppDynamicContractAction" scope="prototype"/>
    <!-- 系统保障管理类-->
    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <!-- 问卷调查管理类-->
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#groupCustomerToAppAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 仅85环境: {http://www.springframework.org/schema/beans}property#sysSer -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="groupCustomerToAppAction"
    class="com.xinxinsoft.action.appAction.GroupCustomerToAppAction"
    scope="prototype">
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="customerService">
    <ref bean="customerServices" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="sysSer">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- 86环境替代配置 (已注释): -->
    <!-- 属性: id="groupCustomerToAppAction", class="com.xinxinsoft.action.appAction.GroupCustomerToAppAction", scope="prototype" -->
    <!-- 子元素: {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property, {http://www.springframework.org/schema/beans}property -->

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#groupReportedLossesAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#holidayAction -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="holidayAction" class="com.xinxinsoft.action.core.HolidayAction">
    <property name="holidayService">
    <ref bean="holidayService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#imsAction -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="imsAction" class="com.xinxinsoft.action.dedicatedFlow.ims.ImsAction"
    scope="prototype">
    <property name="imsService">
    <ref bean="imsService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#itoAction -->
    <!-- 85环境: 3个属性, 6个子元素 -->
    <!-- 86环境: 3个属性, 6个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="itoAction" class="com.xinxinsoft.action.dedicatedFlow.ito.ItoAction"
    scope="prototype">
    <property name="itoService">
    <ref bean="itoService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="processService">
    <ref bean="processService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#knowledgeAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="knowledgeAction" class="com.xinxinsoft.action.knowledge.KnowledgeAction"
    scope="prototype">
    <property name="knowledgeService">
    <ref bean="KnowledgeService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#linkJbpmToAppAction -->
    <!-- 85环境: 3个属性, 4个子元素 -->
    <!-- 86环境: 3个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="linkJbpmToAppAction"
    class="com.xinxinsoft.action.appAction.LinkJbpmToAppCreatAction"
    scope="prototype">
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#loginVerifyAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="loginVerifyAction" class="com.xinxinsoft.action.appAction.LoginVerifyBy4AAction"
    scope="prototype">
    <property name="user4AService">
    <ref bean="User4AService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#mentaryContractAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="mentaryContractAction" class="com.xinxinsoft.action.contract.MentaryContractAction" scope="prototype"/>
    <bean id="appMentaryContractAction" class="com.xinxinsoft.action.appAction.AppMentaryContractAction" scope="prototype"/>
    <bean id="ContractECHttpAction" class="com.xinxinsoft.action.httpAction.ContractECHttpAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#messagesInfoAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="messagesInfoAction" class="com.xinxinsoft.action.messages.MessagesInfoAction"
    scope="prototype">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="messagesInfoService">
    <ref bean="messagesInfoService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#missionConfigAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="missionConfigAction" class="com.xinxinsoft.action.MissionConfigAction.missionConfigAction" scope="prototype"/>
    <bean id="AppContractFacticityLogAction" class="com.xinxinsoft.action.appAction.AppContractFacticityLogAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#orderDetailAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="orderDetailAction" class="com.xinxinsoft.action.order.OrderDetailAction"
    scope="prototype">
    <property name="orderDetailService">
    <ref bean="orderDetailService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#orderFormToAppAction -->
    <!-- 85环境: 3个属性, 3个子元素 -->
    <!-- 86环境: 3个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="orderFormToAppAction" class="com.xinxinsoft.action.appAction.OrderFormToAppAction"
    scope="prototype">
    <property name="commonSingleService" ref="commonSingleService"></property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#orderListYearAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="orderListYearAction"
    class="com.xinxinsoft.action.commonSingManagement.OrderListYearAction"
    scope="prototype">
    <property name="service">
    <ref bean="sngListExcelService" />
    </property>
    <property name="commonSingleService">
    <ref bean="commonSingleService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#overTimeAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="overTimeAction" class="com.xinxinsoft.action.overTime.OverTimeAction"
    scope="prototype">
    <property name="overTimeService">
    <ref bean="OverTimeService" />
    </property>
    <property name="countShareService">
    <ref bean="CountShareService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#permissionAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="permissionAction" class="com.xinxinsoft.action.core.PermissionAction"
    scope="prototype">
    <property name="loginUserService">
    <ref bean="LoginUserService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#queryTransparentInterfaceAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="queryTransparentInterfaceAction" class="com.xinxinsoft.action.httpAction.queryTransparentInterfaceAction" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#questionnaireAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#quickAlarmAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="quickAlarmAction" class="com.xinxinsoft.action.QuickAlarmAction.quickAlarmAction" scope="prototype"/>
    <!-- 问卷调查管理类-->
    <bean id="questionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.QuestionnaireAction" scope="prototype"/>
    <bean id="appQuestionnaireAction" class="com.xinxinsoft.action.QuestionSurveyAction.AppQuestionnaireAction" scope="prototype"/>
    <!--三方人员导入审批-->
    <bean id="TripartiparteUserAction" class="com.xinxinsoft.action.core.user.TripartiparteUserAction" scope="prototype"/>
    <!--USIM物联网卡管理-->
    <bean id="USIMAction" class="com.xinxinsoft.action.USIMAction.USIMAction" scope="prototype"/>
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeHttpAction" class="com.xinxinsoft.action.httpAction.GroupInviteCodeHttpAction" autowire="byType" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#quickQueryDetailsAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="quickQueryDetailsAction" class="com.xinxinsoft.action.webOpenAction.QuickQueryDetailsAction"
    scope="prototype">
    <property name="querySer">
    <ref bean="VariousSqlQueryService" />
    </property>
    <property name="sysSer">
    <ref bean="SystemUserService" />
    </property>
    <property name="singSer">
    <ref bean="commonSingleService" />
    </property>
    <property name="conSer">
    <ref bean="ContractService" />
    </property>
    <property name="groupCustomerService">
    <ref bean="GroupCustomerService" />
    </property>
    <property name="customerService">
    <ref bean="customerServices" />
    </property>
    <property name="dediSer">
    <ref bean="DedicatedFlowService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#quickqueryAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="quickqueryAction" class="com.xinxinsoft.action.webOpenAction.QuickQueryAction"
    scope="prototype">
    <property name="qqService">
    <ref bean="QuickQueryService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#redRollListAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="redRollListAction" class="com.xinxinsoft.action.redRollListAction.RedRollListAction" scope="prototype">
    <property name="redRollListService">
    <ref bean="redRollListService" />
    </property>
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#singListExcelAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="singListExcelAction"
    class="com.xinxinsoft.action.commonSingManagement.singListExcelAction"
    scope="prototype">
    <property name="service">
    <ref bean="sngListExcelService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#smsPushAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="smsPushAction" class="com.xinxinsoft.action.smsPush.SmsPushAction"
    scope="prototype">
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#statisticsAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="statisticsAction" class="com.xinxinsoft.action.statistics.StatisticsAction"
    scope="prototype">
    <property name="statisticsService">
    <ref bean="statisticsService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#systemDeptAction -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="systemDeptAction" class="com.xinxinsoft.action.core.dept.SystemDeptAction"
    scope="prototype">
    <property name="sysDeptService">
    <ref bean="systemDeptService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#testProcessAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="testProcessAction" class="com.xinxinsoft.action.jbpmProcess.TestAction"
    scope="prototype">
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="jbpmProcessService">
    <ref bean="jbpmProcessService" />
    </property>
    </bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#trmerAction -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 业务Action控制器 -->
    <!-- ===================================== -->

    <bean id="trmerAction" class="com.xinxinsoft.action.TimerTrigAction.TrmerAction" scope="prototype"/>
    <!--离网号码-->
    <bean id="SRestoreDeadUserAction" class="com.xinxinsoft.action.SRestoreDeadUser.SRestoreDeadUserAction" scope="prototype"/>
    <!--APP账户（号码）校验方法类-->
    <bean id="appVerificAction" class="com.xinxinsoft.action.appAction.AppVerificAction" scope="prototype"/>
    <bean id="GuideDocumentationAction" class="com.xinxinsoft.action.httpAction.GuideDocumentationAction" autowire="byType" scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#upGroupLevelProcessAction -->
    <!-- 85环境: 3个属性, 7个子元素 -->
    <!-- 86环境: 3个属性, 7个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="upGroupLevelProcessAction"
    class="com.xinxinsoft.action.dedicatedFlow.upGroupLevelProcessAction"
    scope="prototype">
    <property name="service">
    <ref bean="ExpenseApplyService" />
    </property>
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="taskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="processService">
    <ref bean="processService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#upTestAction -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="upTestAction" class="com.xinxinsoft.action.upTest.upTestAction"
    scope="prototype">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#uploadToAppAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="uploadToAppAction" class="com.xinxinsoft.action.appAction.UploadToAppAction"
    scope="prototype">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#waitTaskToAppAction -->
    <!-- 85环境: 3个属性, 2个子元素 -->
    <!-- 86环境: 3个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="waitTaskToAppAction" class="com.xinxinsoft.action.appAction.WaitTaskToAppAction"
    scope="prototype">
    <property name="singleService" ref="commonSingleService"></property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    </bean>

</beans>