# EOMç³»ç»éç½®æä»¶ - è¯¦ç»åå¹¶çæ¬
# æ­¤æä»¶ç±85å86ç¯å¢éç½®åå¹¶çæ
# åå¹¶è§å: ä¿çææéç½®ï¼è¯¦ç»æ æ³¨å·®å¼
# çææ¶é´: èªå¨çæ

# ä¸¤ç¯å¢éç½®ä¸è´
APP_EDITION_DIR=/EOMAPP/UploadFiles/apk/

#æ¯ä»ä¸­å¿v4.5.0 ç»ä¸æ¥å¥è§è (V2.1.5)ç´æ¥è°ç¨APIæ¯ä»åèµ·webservice
# ä¸¤ç¯å¢éç½®ä¸è´
APP_PAY_CENTER_API=http://*************:8080/EOM/services/payCenterAPIWebService?wsdl

#æ¯ä»webService
# ä¸¤ç¯å¢éç½®ä¸è´
APP_PAY_VOUCHER_CENTER=http://*************:8080/EOM/services/voucherCenterWebService?wsdl

#138_APIæµè¯å°å
# ä¸¤ç¯å¢éç½®ä¸è´
ATTACHMENT_138_TEST_URL=http://111.9.13.162:50080/cust

#138_APIæ­£å¼å°å
# ä¸¤ç¯å¢éç½®ä¸è´
ATTACHMENT_138_URL=http://10.113.222.59:8080/cust

#è·åACCESSTOKEN
# ä¸¤ç¯å¢éç½®ä¸è´
ATT_ACCESS_TOKEN_URL=/oauth/token

#èµæè¯¦æ
# ä¸¤ç¯å¢éç½®ä¸è´
ATT_DETAILS_URL=/openapi/profile/

#èµæä¸è½½
# ä¸¤ç¯å¢éç½®ä¸è´
ATT_DOWNLOAD_URL=/openapi/profile/download/

#èµææç´¢
# ä¸¤ç¯å¢éç½®ä¸è´
ATT_SEARCH_URL=/openapi/profile

#èµæä¸ä¼ 
# ä¸¤ç¯å¢éç½®ä¸è´
ATT_UPLOAD_URL=/openapi/profile

# ä¸¤ç¯å¢éç½®ä¸è´
AUDITWORKFILE_URL=/EOMAPP/UploadFiles/AuditWorkFile/

#è·³è½¬ææå½åå»ºâ»â¾¯
# ä¸¤ç¯å¢éç½®ä¸è´
AUTHORIZATION_LETTER=/openapi/profile/authorization

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: BACKUP_HOST
# éç½®å¼: *************
# ç¨éåæ: ä¸å¡éç½®
# ===================================
BACKUP_HOST=*************

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: BACKUP_PATH
# éç½®å¼: /data/work5
# ç¨éåæ: æä»¶è·¯å¾éç½®
# ===================================
BACKUP_PATH=/data/work5

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: BOSSNO
# éç½®å¼: aagh38,aagh7h
# ç¨éåæ: ä¸å¡éç½®
# ===================================
BOSSNO=aagh38,aagh7h

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: BUSINOTIFY_URL
# éç½®å¼: http://*************:8080/EOM/Payment_busiNotifyUrl.action
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
BUSINOTIFY_URL=http://*************:8080/EOM/Payment_busiNotifyUrl.action

#éæ¬¾åå°åè°
# ä¸¤ç¯å¢éç½®ä¸è´
CANCEL_API_NOTIFY_URL=http://*************:8080/EOM/Payment_cancelNotifyAPI.action

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: CANCEL_NOTIFY_URL
# éç½®å¼: http://*************:8080/EOM/Payment_cancelNotifyUrl.action
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
CANCEL_NOTIFY_URL=http://*************:8080/EOM/Payment_cancelNotifyUrl.action

# ä¸¤ç¯å¢éç½®ä¸è´
CHANNELNO=sc-eom

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: CONTRACTCSV_URL
# éç½®å¼: /EOMAPP/contracts/contractCsv/
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
CONTRACTCSV_URL=/EOMAPP/contracts/contractCsv/

# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: CONTRACTFTP_URL
# 85ç¯å¢å¼: /EOMAPP/contracts/
# 86ç¯å¢å¼: /EOMAPP/UploadFiles/contracts/
# å·®å¼åæ: éç½®å¼ä¸åï¼å¯è½æ¯ç¯å¢ç¹å®éç½®
# ================================
# å½åéç¨85ç¯å¢éç½®
CONTRACTFTP_URL=/EOMAPP/contracts/
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# CONTRACTFTP_URL=/EOMAPP/UploadFiles/contracts/

# ä¸¤ç¯å¢éç½®ä¸è´
CONTRACTINFO_URL=/EOMAPP/UploadFiles/6429/OrderFiles/

# ä¸¤ç¯å¢éç½®ä¸è´
CONTRACT_URL=http://218.205.252.26:38080/EOM_WebServices/EOMAPP_DZG/middleconnectdeel.html?token=$1$&un4A=$2$&unid=5D8BEE27&tval=30&valparam=$3$

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: CONT_PAPER_PDF_URL
# éç½®å¼: /EOMAPP/UploadFiles/openroderdocx2pdf/paper/
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
CONT_PAPER_PDF_URL=/EOMAPP/UploadFiles/openroderdocx2pdf/paper/

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: CONT_PDF_URL
# éç½®å¼: /EOMAPP/UploadFiles/openroderdocx2pdf/
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
CONT_PDF_URL=/EOMAPP/UploadFiles/openroderdocx2pdf/

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: DOCUMENT_URL
# éç½®å¼: /EOMAPP/UploadFiles/Document/
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
DOCUMENT_URL=/EOMAPP/UploadFiles/Document/

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: DSJ_PORT
# éç½®å¼: 21
# ç¨éåæ: ä¸å¡éç½®
# ===================================
DSJ_PORT=21

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: DSJ_PSD
# éç½®å¼: %pNj#uf3
# ç¨éåæ: ä¸å¡éç½®
# ===================================
DSJ_PSD=%pNj#uf3

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: DSJ_USER
# éç½®å¼: zx_ftp
# ç¨éåæ: ä¸å¡éç½®
# ===================================
DSJ_USER=zx_ftp

##################################################################################################
#######################################è¯·æ±æ çº¸åç³»ç» URL###########################################
# ä¸¤ç¯å¢éç½®ä¸è´
ENDPOINT_URL=http://10.95.253.74:17001/services/ContractService

# ä¸¤ç¯å¢éç½®ä¸è´
ESBDOWLOWNDFTP_URL=/baseapp/usedRecords

##\u8D44\u91D1\u8BA4\u9886\u6587\u4EF6\u5939
# ä¸¤ç¯å¢éç½®ä¸è´
ESBFTP_URL=/EOMAPP/UploadFiles/esbftp

##################################################################################################
#############ESB æ­£å¼ç¯å¢å°å###############################################################
# ä¸¤ç¯å¢éç½®ä¸è´
ESBWS_URL=http://*************:51000/esbWS/rest/

# ä¸¤ç¯å¢éç½®ä¸è´
FINANCIALSYSTEMFTP_URL=/EOMAPP/UploadFiles/financialsystenftp

# ä¸¤ç¯å¢éç½®ä¸è´
FTP_AUDITWORKSHEETURL=ChkFiles/

# ä¸¤ç¯å¢éç½®ä¸è´
FTP_INDUSTRYTERMINAL=/EOMAPP/UploadFiles/INDUSTRYTERMINAL

######æ¬ è´¹####
# ä¸¤ç¯å¢éç½®ä¸è´
FTP_TEMP_ERRORMSG=/EOMAPP/UploadFiles/QFDATATEMP/

# ä¸¤ç¯å¢éç½®ä¸è´
FTP_TEMP_FILEPATH=/EOMAPP/UploadFiles/TEMP

#\u5730\u5740
# ä¸¤ç¯å¢éç½®ä¸è´
FTP_URL=/EOMAPP/UploadFiles/

# ä¸¤ç¯å¢éç½®ä¸è´
FTP_URL_2=/EOMAPP01/UploadFiles/

# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: FTP_YEAR
# 85ç¯å¢å¼: 201912
# 86ç¯å¢å¼: 201907
# å·®å¼åæ: æ°å¼éç½®å·®å¼ï¼85ç¯å¢=201912ï¼86ç¯å¢=201907
# ================================
# å½åéç¨85ç¯å¢éç½®
FTP_YEAR=201912
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# FTP_YEAR=201907

# ä¸¤ç¯å¢éç½®ä¸è´
INVALID_DATE=5

# ä¸¤ç¯å¢éç½®ä¸è´
IS_CONTRACT_SWITCH=true

#å å¯KEY
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: KEY
# éç½®å¼: go2ve0rn1me9nt1an0de2nt3er3prise
# ç¨éåæ: è®¤è¯éç½®
# ===================================
KEY=go2ve0rn1me9nt1an0de2nt3er3prise

##LW_FTP_HOST=*************
# ä¸¤ç¯å¢éç½®ä¸è´
LW_FTP_HOST=*************

##LW_FTP_PATH=/home/<USER>/lw/ZQFILE
# ä¸¤ç¯å¢éç½®ä¸è´
LW_FTP_PATH=/home/<USER>/lw/ZQFILE

# ä¸¤ç¯å¢éç½®ä¸è´
LW_FTP_PORT=21

# ä¸¤ç¯å¢éç½®ä¸è´
LW_FTP_PSD=JkLv^1227

##send txt to lw_ftp
# ä¸¤ç¯å¢éç½®ä¸è´
LW_FTP_TEXT=/EOMAPP/UploadFiles/LwFtpFiles/

# ä¸¤ç¯å¢éç½®ä¸è´
LW_FTP_USER=aiadmin

##dsj
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: MAIN_HOST
# éç½®å¼: *************
# ç¨éåæ: ä¸å¡éç½®
# ===================================
MAIN_HOST=*************

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: MAIN_PATH
# éç½®å¼: /data/work4
# ç¨éåæ: æä»¶è·¯å¾éç½®
# ===================================
MAIN_PATH=/data/work4

#PAYMENT_URL=http://*************:51000/esbWs/rest/
#åæ·å·
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: MERCHANT_NO
# éç½®å¼: 3002001
# ç¨éåæ: ä¸å¡éç½®
# ===================================
MERCHANT_NO=3002001

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: OVERTIME_DAY
# éç½®å¼: 3
# ç¨éåæ: ä¸å¡éç½®
# ===================================
OVERTIME_DAY=3

###########################################æ°ç ç»ä¸è®¢åæ¯ä»å¹³å°å°åååæ°ä¿¡æ¯###################################
#æ¥å£å°å
# æ¯ä»ä¸­å¿åå¸ç¯å¢ï¼10.113.171.139:18002
# æ¯ä»ä¸­å¿çäº§ç¯å¢ï¼10.113.171.41:18002
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: PAYMENT_URL
# éç½®å¼: http://10.113.171.41:18002/
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
PAYMENT_URL=http://10.113.171.41:18002/

# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: PAYNOTIFY_URL
# éç½®å¼: http://*************:8080/EOM/Payment_notifyUrl.action
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
PAYNOTIFY_URL=http://*************:8080/EOM/Payment_notifyUrl.action

#æ¯ä»åå°éç¥å°å
# ä¸¤ç¯å¢éç½®ä¸è´
PAY_API_NOTIFY_URL=http://*************:8080/EOM/Payment_notifyAPIUrl.action

#æ¯ä»æ¥å£å°å
# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: PAY_API_URL
# 85ç¯å¢å¼: http://10.113.171.41:18002/
# 86ç¯å¢å¼: http://10.113.171.139:18002/
# å·®å¼åæ: æ¯ä»ç¯å¢å·®å¼ï¼85ç¯å¢ä½¿ç¨çäº§æ¯ä»(.41)ï¼86ç¯å¢ä½¿ç¨æµè¯æ¯ä»(.139)
# ================================
# å½åéç¨85ç¯å¢éç½®
PAY_API_URL=http://10.113.171.41:18002/
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# PAY_API_URL=http://10.113.171.139:18002/

# ä¸¤ç¯å¢éç½®ä¸è´
QRYPREDEALWSDL_URL=http://10.113.38.84:10000/rest/1.0/qryPreDeal?

####################################è½åå¼æ¾å¹³å° ç¸å³è®¿é®å°å#########################################
# ä¸¤ç¯å¢éç½®ä¸è´
QRYRESOURCERESULT4PREORDERWSDL_URL=http://10.113.38.84:10000/rest/1.0/qryResourceResult4PreOrder?

#æ¥è¯¢ææå½
# ä¸¤ç¯å¢éç½®ä¸è´
QUERY_LETTER_URL=/openapi/profile/authorization/

#æ¯ä»åå°éç¥å°å
#PAYRET_URL = www.baidu.com
#æ¯ä»åå°éç¥å°å
#PAYNOTIFY_URL = http://*************:8080/EOM/PaymentOrder_savePaymentOrder.action
#PAYNOTIFY_URL=http://*************:8080/EOM/Payment_PaymentNotify.action
#éæ¬¾åå°éç¥å°å
#REFUNDRET_URL = www.baidu.com
#éæ¬¾åå°éç¥å°å
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: REFUNDNOTIFY_URL
# éç½®å¼: http://*************:8080/EOM/PayRefund_payRefundNotify.action
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
REFUNDNOTIFY_URL=http://*************:8080/EOM/PayRefund_payRefundNotify.action

##################################################################################################
# ä¸¤ç¯å¢éç½®ä¸è´
REQUER_URL=http://10.113.222.58:9999/pois/

#######################################S3851æ°å»ºå°å###############################################
# ä¸¤ç¯å¢éç½®ä¸è´
S3851APPCFM_URL=HTTP://10.113.38.84:10000/rest/1.0/s3851AppCfm?

# ä¸¤ç¯å¢éç½®ä¸è´
SELECTCONTRACT_URL=http://10.114.129.104:8080/apis/system/log/selectContract?code=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_ADDCERT=/api-sign/http/seal/addCert?access_token=

# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: SIGN_DOWNLOADPDF
# 85ç¯å¢å¼: http://*************:8080/EOM/EomSignAction_downloadPdf.action?path=
# 86ç¯å¢å¼: http://10.108.226.83:8080/EOM/EomSignAction_downloadPdf.action?path=
# å·®å¼åæ: éç½®å¼ä¸åï¼å¯è½æ¯ç¯å¢ç¹å®éç½®
# ================================
# å½åéç¨85ç¯å¢éç½®
SIGN_DOWNLOADPDF=http://*************:8080/EOM/EomSignAction_downloadPdf.action?path=
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# SIGN_DOWNLOADPDF=http://10.108.226.83:8080/EOM/EomSignAction_downloadPdf.action?path=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_GETSEALIMGLIST=/api-sign/http/seal/getSealImgList?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_MULTISEALBYXY=/api-sign/http/sign/multiSealByXy?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_REVOKECERT=/api-sign/http/seal/revokeCert?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_SEALBYKEY=/api-sign/http/sign/sealByKey?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_SEALBYSTRADDLE=/api-sign/http/sign/sealByStraddle?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_SEALBYXY=/api-sign/http/sign/sealByXy?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_SIGNCHECK=/api-sign/http/sign/signCheck?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_TOKEN=/api-auth/oauth/client/token

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_UPDATESEALIMG=/api-sign/http/seal/updateSealImg?access_token=

# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: SIGN_UPLOADPDF
# 85ç¯å¢å¼: http://*************:8080/EOM/EomSignAction_uploadPdf.action
# 86ç¯å¢å¼: http://10.108.226.83:8080/EOM/EomSignAction_uploadPdf.action
# å·®å¼åæ: éç½®å¼ä¸åï¼å¯è½æ¯ç¯å¢ç¹å®éç½®
# ================================
# å½åéç¨85ç¯å¢éç½®
SIGN_UPLOADPDF=http://*************:8080/EOM/EomSignAction_uploadPdf.action
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# SIGN_UPLOADPDF=http://10.108.226.83:8080/EOM/EomSignAction_uploadPdf.action

# ä¸¤ç¯å¢éç½®ä¸è´
SIGN_URL=http://10.114.176.10:9100

# ä¸¤ç¯å¢éç½®ä¸è´
SIIGN_ADDSEAL=/api-sign/http/seal/addSeal?access_token=

# ä¸¤ç¯å¢éç½®ä¸è´
STARTPREORDERWSDL_URL=http://10.113.38.84:10000/rest/1.0/startPreOrder?

####################ESB  æµè¯ç¯å¢å°å###############################################################
# ä¸¤ç¯å¢éç½®ä¸è´
TEST_ESBWS_URL=http://10.113.183.51:52000/esbWS/rest/

##S3851_FTP_URL=/EOMAPP/UploadFiles/FtpFiles/S3851PATH/
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: UNIT_FILE_DIR
# éç½®å¼: /EOMAPP/UploadFiles/FtpFiles/S3851PATH/
# ç¨éåæ: æä»¶è·¯å¾éç½®
# ===================================
UNIT_FILE_DIR=/EOMAPP/UploadFiles/FtpFiles/S3851PATH/

#10.114.208.231
############################################################################################
##################################################################################################
#éå¢éªçæ¥å£
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: UNIT_VERIFY_TRUTH_URL
# éç½®å¼: http://10.113.222.58:9999/pois/check/
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
UNIT_VERIFY_TRUTH_URL=http://10.113.222.58:9999/pois/check/

# ========== éç½®å·®å¼ ==========
# éç½®é¡¹: USER_PHOTO_DIR
# 85ç¯å¢å¼: /EOMAPP/FTPUpLoad/PhotoDir/
# 86ç¯å¢å¼: /EOMAPP/UploadFiles/PhotoDir/
# å·®å¼åæ: æä»¶è·¯å¾ç»æå·®å¼ï¼85ç¯å¢ä½¿ç¨FTPUpLoadç®å½ï¼86ç¯å¢ä½¿ç¨UploadFilesç®å½
# ================================
# å½åéç¨85ç¯å¢éç½®
USER_PHOTO_DIR=/EOMAPP/FTPUpLoad/PhotoDir/
# å¦éä½¿ç¨86ç¯å¢éç½®ï¼è¯·åæ¶ä¸è¡æ³¨éå¹¶æ³¨éä¸è¡
# USER_PHOTO_DIR=/EOMAPP/UploadFiles/PhotoDir/

###############å¾®ä¿¡è¿è¥å¹³å°æä¾çæ¥å£##########################################################
# ========== ç¯å¢ç¬æéç½® ==========
# ä»å¨85ç¯å¢ä¸­å­å¨: WECHAT_URL
# éç½®å¼: http://10.114.208.230:8080/zqwx/zq/receive/invoice
# ç¨éåæ: æ¥å£å°åéç½®
# ===================================
WECHAT_URL=http://10.114.208.230:8080/zqwx/zq/receive/invoice

