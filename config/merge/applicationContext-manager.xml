<?xml version="1.0" ?>
<beans xmlns="http://www.springframework.org/schema/beans" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop" xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jaxws="http://cxf.apache.org/jaxws" xsi:schemaLocation="       http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd       http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd       http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd       http://cxf.apache.org/jaxws  http://cxf.apache.org/schemas/jaxws.xsd" default-autowire="byName">
    <!-- EOM系统配置文件 - 合并版本
     此文件由85和86环境配置合并生成
     合并规则: 以属性多的配置为准
     生成时间: 自动生成 -->
    <bean id="AbnormalNumberService" class="com.xinxinsoft.service.abnormalNumberService.AbnormalNumberService"/>
    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
        <property name="abnormalNumberService">
            <ref bean="AbnormalNumberService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="AccountChange4AWebService" class="com.xinxinsoft.service.webService.AccountChange4AWebService">
        <constructor-arg name="user4AService">
            <ref bean="User4AService"/>
        </constructor-arg>
    </bean>
    <bean id="AccountOrderInfoService" class="com.xinxinsoft.service.pay.AccountOrderInfoService"/>
    <bean id="ActivityPreinvApplyService" class="com.xinxinsoft.service.activityPreinvService.ActivityPreinvApplyService"/>
    <bean id="ActivityService" class="com.xinxinsoft.service.honorCode.ActivityService"/>
    <bean id="AgreementService" class="com.xinxinsoft.service.arrearsModule.AgreementService"/>
    <bean id="AllPayDesignService" class="com.xinxinsoft.service.AllMembersPayService.AllPayDesignService"/>
    <bean id="AndFlySpeedService" class="com.xinxinsoft.service.andFlySpeedService.AndFlySpeedService"/>
    <bean id="AppDynamicContractService" class="com.xinxinsoft.service.contract.AppDynamicContractService"/>
    <bean id="AppLoginService" class="com.xinxinsoft.service.core.app.AppLoginService"/>
    <bean id="AppLoginWebService" class="com.xinxinsoft.service.webService.AppLoginWebService">
        <constructor-arg name="appLoginService">
            <ref bean="AppLoginService"/>
        </constructor-arg>
        <constructor-arg name="appUserInfoService">
            <ref bean="AppUserInfoService"/>
        </constructor-arg>
    </bean>
    <bean id="AppUserInfoService" class="com.xinxinsoft.service.core.app.AppUserInfoService"/>
    <bean id="ArrearsService" class="com.xinxinsoft.service.arrearsModule.ArrearsService"/>
    <bean id="ArrearsSingSerivce" class="com.xinxinsoft.service.arrearsModule.ArrearsSingSerivce">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="ArrearsSingTask" class="com.xinxinsoft.task.ArrearsSingTask">
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
        <property name="arrearsSingSerivce">
            <ref bean="ArrearsSingSerivce"/>
        </property>
    </bean>
    <bean id="ArrearsStatisticalService" class="com.xinxinsoft.service.arrearsModule.ArrearsStatisticalService"/>
    <bean id="ArrearsStatisticalTask" class="com.xinxinsoft.task.ArrearsStatisticalTask">
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
        <property name="arrearsStatisticalService">
            <ref bean="ArrearsStatisticalService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ArrearsWriteOffService" class="com.xinxinsoft.service.arrearsWriteOffService.ArrearsWriteOffService" scope="prototype"/>
    <bean id="ArrearsWriteOffTask" class="com.xinxinsoft.task.ArrearsWriteOffTask">
        <property name="arrearsWriteOffService">
            <ref bean="ArrearsWriteOffService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="AttachmentService" class="com.xinxinsoft.service.enclosure.AttachmentService">
	</bean>
    <bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService"/>
    <bean id="AttachmentSpaceReminderTask" class="com.xinxinsoft.task.AttachmentSpaceReminderTask">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="pushService">
            <ref bean="smsPushService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="AttachmentTypeService" class="com.xinxinsoft.service.enclosure.AttachmentTypeService">
	</bean>
    <bean id="AuditMultipleService" class="com.xinxinsoft.service.AuditWorksheetService.AuditMultipleService"/>
    <bean id="AuditWorkListService" class="com.xinxinsoft.service.AuditWorksheetService.AuditWorkListService"/>
    <bean id="AuditWorksheetService" class="com.xinxinsoft.service.AuditWorksheetService.AuditWorksheetService"/>
    <bean id="AuditWorksheetTask" class="com.xinxinsoft.task.AuditWorksheetTask">
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="auditWorksheetService">
            <ref bean="AuditWorksheetService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="AuditintersLogsTask" class="com.xinxinsoft.task.AuditintersLogsTask">
        <property name="ibbnService">
            <ref bean="IbbnService"/>
        </property>
        <property name="userService">
            <ref bean="SystemUserService"/>
        </property>
        <!--<property name="jobLogServicer">
			 <ref bean="JobLogServicer"/>
		</property>-->
    </bean>
    <bean id="AutomaticArchivingTask" class="com.xinxinsoft.task.AutomaticArchivingTask">
        <property name="dedicatedFlowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="BOGetService" class="com.xinxinsoft.service.webService.BOGetService">
        <property name="userService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="BigAmountApplyFlowService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyFlowService"/>
    <bean id="BigAmountApplyService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyService"/>
    <bean id="BigAmountApplyTaskService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyTaskService"/>
    <bean id="BigAmountDepositSendContractService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountDepositSendContractService"/>
    <bean id="BossTacheService" class="com.xinxinsoft.service.basetype.BossTacheService"/>
    <bean id="BossTask" class="com.xinxinsoft.task.BossTask">
        <property name="singleService">
            <ref bean="commonSingleService"/>
        </property>
        <property name="userService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="Bpms_riskoff_service" class="com.xinxinsoft.service.PublicService.Bpms_riskoff_service">
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="BusiOppService" class="com.xinxinsoft.sendComms.omsService.BusiOppService"/>
    <bean id="BusiOppTask" class="com.xinxinsoft.task.BusiOppTask">
        <!--推送商机系统记录servcie-->
        <property name="pushBusiOppLogService">
            <ref bean="PushBusiOppLogService"/>
        </property>
        <!--一键下单回调商机系统接口-->
        <property name="busiOppService">
            <ref bean="BusiOppService"/>
        </property>
    </bean>
    <bean id="BusiParameterService" class="com.xinxinsoft.service.pay.Busi_ParameterService"/>
    <bean id="BusinessEntityService" class="com.xinxinsoft.service.businessProc.BusinessEntityService"/>
    <bean id="BusinessTypeService" class="com.xinxinsoft.service.basetype.BusinessTypeService"/>
    <bean id="BusinssService" class="com.xinxinsoft.service.businss.BusinssService"/>
    <bean id="CMCC1000OpenService" class="com.xinxinsoft.sendComms.CMCC1000OpenService"/>
    <bean id="CMCCOpenService" class="com.xinxinsoft.sendComms.CMCCOpenService"/>
    <bean id="ChannelInfoService" class="com.xinxinsoft.service.honorCode.ChannelInfoService"/>
    <bean id="CheckService" class="com.xinxinsoft.service.checkservice.CheckService"/>
    <bean id="ClaimAContractService" class="com.xinxinsoft.service.contract.ClaimAContractService"/>
    <bean id="ClaimForFundsService" class="com.xinxinsoft.service.claimForFunds.ClaimForFundsService"/>
    <bean id="ClaimForFundsTask" class="com.xinxinsoft.task.ClaimForFundsTask">
        <property name="claimForFundsService">
            <ref bean="ClaimForFundsService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ClaimFundReturnService" class="com.xinxinsoft.service.claimForFunds.ClaimFundReturnService"/>
    <bean id="ClearCode" class="com.xinxinsoft.task.ClearCode">
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="CompassFilingGroupTask" class="com.xinxinsoft.task.CompassFilingGroupTask">
        <property name="unitInfoService">
            <ref bean="UnitInfoService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ContractCsv" class="com.xinxinsoft.task.ContractCsv">
        <property name="contractToOrderCSVService">
            <ref bean="ContractToOrderCSVService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ContractExpiredTask" class="com.xinxinsoft.task.ContractExpiredTask">
        <property name="customClauseContractService">
            <ref bean="CustomClauseContractService"/>
        </property>
        <property name="userService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ContractHttpService" class="com.xinxinsoft.service.httpService.ContractHttpService"/>
    <bean id="ContractService" class="com.xinxinsoft.service.contract.ContractService"/>
    <bean id="ContractToOrderCSVService" class="com.xinxinsoft.service.contractToOrderCSVService.ContractToOrderCSVService"/>
    <bean id="ContractToOrderCsv" class="com.xinxinsoft.task.ContractToOrderCsv">
        <property name="contractToOrderCSVService">
            <ref bean="ContractToOrderCSVService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ContractUniformityService" class="com.xinxinsoft.service.contractUniformityService.ContractUniformityService"/>
    <bean id="ContractUniformityTask" class="com.xinxinsoft.task.ContractUniformityTask">
        <property name="contractUniformityService">
            <ref bean="ContractUniformityService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ContractUtils" class="com.xinxinsoft.utils.ContractUtils">
        <property name="attachmentService">
            <ref bean="AttachmentService"/>
        </property>
        <property name="contractService">
            <ref bean="ContractService"/>
        </property>
    </bean>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
        <property name="omsService">
            <ref bean="OMSService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="CountShareService" class="com.xinxinsoft.service.countShare.CountShareService"/>
    <bean id="CustomClauseContractService" class="com.xinxinsoft.service.contract.CustomClauseContractService"/>
    <bean id="CustomerAccountService" class="com.xinxinsoft.service.customeraccount.CustomerAccountService">
	</bean>
    <bean id="DedicatedFlowService" class="com.xinxinsoft.service.dedicatedFlow.DedicatedFlowService"/>
    <bean id="DeleteFile" class="com.xinxinsoft.task.DeleteFile">
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="DictOrderHttpService" class="com.xinxinsoft.service.httpService.DictOrderHttpService"/>
    <bean id="DictionaryService" class="com.xinxinsoft.service.core.DictionaryService"/>
    <bean id="EditionService" class="com.xinxinsoft.service.core.app.EditionService"/>
    <bean id="EipUserTask" class="com.xinxinsoft.task.EipUserTask">
        <property name="deptService">
            <ref bean="systemDeptService"/>
        </property>
        <property name="ownService">
            <ref bean="OwnDepartmentsService"/>
        </property>
        <property name="userService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="EnterPriseTask" class="com.xinxinsoft.task.EnterPriseTask"/>
    <bean id="ExpenseApplyService" class="com.xinxinsoft.service.dedicatedFlow.ExpenseApplyService"/>
    <bean id="GoodsInfoService" class="com.xinxinsoft.service.pay.Goods_InfoService"/>
    <bean id="GroupAccountService" class="com.xinxinsoft.service.groupAccountService.GroupAccountService"/>
    <bean id="GroupCustomerService" class="com.xinxinsoft.service.groupcustomer.GroupCustomerService">
	</bean>
    <bean id="GroupInviteCodeService" class="com.xinxinsoft.service.groupInviteCodeService.GroupInviteCodeService"/>
    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
    <bean id="GroupPeopleService" class="com.xinxinsoft.service.customeraccount.GroupPeopleService">
	</bean>
    <bean id="GroupTaxpayerService" class="com.xinxinsoft.service.groupTaxpayerService.GroupTaxpayerService"/>
    <bean id="HonorCodeBusinessService" class="com.xinxinsoft.service.honorCode.HonorCodeBusinessService"/>
    <!-- 仅在85环境中存在: bean#HonorUpLoadTask -->
    <bean id="HonorUpLoadTask" class="com.xinxinsoft.task.HonorUpLoadTask">
        <property name="activityService">
            <ref bean="ActivityService"/>
        </property>
        <property name="channelInfoService">
            <ref bean="ChannelInfoService"/>
        </property>
        <property name="honorCodeBusinessService">
            <ref bean="HonorCodeBusinessService"/>
        </property>
    </bean>
    <bean id="ICTApplicationService" class="com.xinxinsoft.service.ICT.ICTApplicationService"/>
    <bean id="IChkPreInvoiceActAoSvc" class="com.xinxinsoft.service.webService.IChkPreInvoiceActAoSvc"/>
    <bean id="IDCApplyService" class="com.xinxinsoft.service.IDCService.IDCApplyService"/>
    <bean id="IDCFlowService" class="com.xinxinsoft.service.IDCService.IDCFlowService"/>
    <bean id="IDCTaskService" class="com.xinxinsoft.service.IDCService.IDCTaskService"/>
    <bean id="IMSHighFrequencyService" class="com.xinxinsoft.service.IMSHighFrequencyService.IMSHighFrequencyService" scope="prototype"/>
    <bean id="IbbnService" class="com.xinxinsoft.service.IBossService.IBossByNoService"/>
    <bean id="IctContractService" class="com.xinxinsoft.service.IctContract.IctContractService" autowire="byType"/>
    <bean id="IctContractTask" class="com.xinxinsoft.task.IctContractTask">
        <property name="ictContractService">
            <ref bean="IctContractService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="IdcService" class="com.xinxinsoft.sendComms.IdcService"/>
    <bean id="IndustryTerminalService" class="com.xinxinsoft.service.IndustryTerminal.IndustryTerminalService"/>
    <bean id="InquiryOrderService" class="com.xinxinsoft.service.InquiryOrder.InquiryOrderService"/>
    <bean id="IntegrationService" class="com.xinxinsoft.service.v2.integrationService.IntegrationService"/>
    <bean id="InterfaceLogService" class="com.xinxinsoft.service.anySrc.InterfaceLogService"/>
    <bean id="JobLogServicer" class="com.xinxinsoft.service.executejoblog.JobLogServicer"/>
    <bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService"/>
    <bean id="KnowledgeService" class="com.xinxinsoft.service.knowledge.KnowledgeService"/>
    <bean id="LateFeeMoneyDataService" class="com.xinxinsoft.service.claimForFunds.LateFeeMoneyDataService"/>
    <bean id="LinkScoreService" class="com.xinxinsoft.service.linkScore.LinkScoreService">
	</bean>
    <bean id="LinkTemplateService" class="com.xinxinsoft.service.processLink.LinkTemplateService"/>
    <bean id="LoginUserService" class="com.xinxinsoft.service.core.user.LoginUserService">
        <property name="passwordEncoder">
            <ref bean="passwordEncoder"/>
        </property>
    </bean>
    <bean id="Logs4AWebServices" class="com.xinxinsoft.service.webService.Logs4AWebServices">
        <property name="ibbnService">
            <ref bean="IbbnService"/>
        </property>
    </bean>
    <bean id="ManualInvApplyService" class="com.xinxinsoft.service.manualInvApply.ManualInvApplyService"/>
    <bean id="MarketActivitiesService" class="com.xinxinsoft.service.MarketActivitiesService.MarketActivitiesService"/>
    <bean id="MarketActivitiesTask" class="com.xinxinsoft.task.MarketActivitiesTask">
        <property name="marketActivitiesService">
            <ref bean="MarketActivitiesService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="MarketingActivitiesService" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesService"/>
    <bean id="MarketingActivitiesServiceTwo" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesServiceTwo"/>
    <bean id="MenuItemService" class="com.xinxinsoft.service.core.menuitem.MenuItemService">
        <property name="loginUserService">
            <ref bean="LoginUserService"/>
        </property>
        <property name="roleService">
            <ref bean="RoleService"/>
        </property>
    </bean>
    <bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"/>
    <bean id="MoaService" class="com.xinxinsoft.service.moaService.MoaService"/>
    <bean id="MonthlyinvoiceService" class="com.xinxinsoft.service.monthlyinvoice.MonthlyinvoiceService"/>
    <bean id="NoResApplyService" class="com.xinxinsoft.service.appOpenService.NoResApplyService">
        <property name="jbpmUtil">
            <ref bean="JBPMUtil"/>
        </property>
        <property name="waitTaskService">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="OMSService" class="com.xinxinsoft.service.appOpenService.OMSService"/>
    <bean id="OcrIdentificationLogsService" class="com.xinxinsoft.service.ocrIdentificationLogsService.OcrIdentificationLogsService"/>
    <bean id="OmsOrderManagerSignInTask" class="com.xinxinsoft.task.OmsOrderManagerSignInTask" scope="prototype">
        <property name="omsSellOrderService">
            <ref bean="OmsSellOrderService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="structureOfPersonnelService">
            <ref bean="StructureOfPersonnelService"/>
        </property>
    </bean>
    <bean id="OmsOrderProductService" class="com.xinxinsoft.service.oms.OmsOrderProductService"/>
    <bean id="OmsOrderWorkbenchService" class="com.xinxinsoft.service.oms.OmsOrderWorkbenchService"/>
    <bean id="OmsSellOrderService" class="com.xinxinsoft.service.oms.OmsSellOrderService"/>
    <bean id="OmsStatisticalService" class="com.xinxinsoft.service.oms.OmsStatisticalService"/>
    <bean id="OneClickOrderTask" class="com.xinxinsoft.task.OneClickOrderTask">
        <property name="omsService">
            <ref bean="OMSService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="Open4AStateService" class="com.xinxinsoft.service.core.app.Open4AStateService"/>
    <bean id="OrderQuerySendService" class="com.xinxinsoft.service.pay.OrderQuerySendService"/>
    <bean id="OrderQueryService" class="com.xinxinsoft.service.pay.OrderQueryService"/>
    <bean id="OrderTaskService" class="com.xinxinsoft.service.ordertask.OrderTaskService"/>
    <bean id="OrderWarningTask" class="com.xinxinsoft.task.OrderWarningTask">
        <property name="singleService">
            <ref bean="commonSingleService"/>
        </property>
        <property name="smsPushService">
            <ref bean="smsPushService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="OverTimeService" class="com.xinxinsoft.service.overTime.OverTimeService"/>
    <bean id="OwnDepartmentsService" class="com.xinxinsoft.service.core.OwnDepartmentsService"/>
    <bean id="PMSService" class="com.xinxinsoft.service.appOpenService.PMSService"/>
    <bean id="PayApiService" class="com.xinxinsoft.service.pay.PayApiService"/>
    <bean id="PayRefundService" class="com.xinxinsoft.service.pay.PayRefundService"/>
    <bean id="PaymentMessageTask" class="com.xinxinsoft.task.PaymentMessageTask">
        <property name="pService">
            <ref bean="PaymentRecordService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="PaymentOrderService" class="com.xinxinsoft.service.pay.PaymentOrderService" autowire="byType"/>
    <bean id="PaymentProviderService" class="com.xinxinsoft.service.pay.PaymentProviderService"/>
    <bean id="PaymentRecordService" class="com.xinxinsoft.service.arrearsModule.PaymentRecordService"/>
    <bean id="PaymentService" class="com.xinxinsoft.service.pay.PaymentService"/>
    <bean id="PreinvApplyImportTask" class="com.xinxinsoft.task.PreinvApplyImportTask">
        <property name="preinvApplyService">
            <ref bean="PreinvApplyService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="PreinvApplyMarkingService" class="com.xinxinsoft.service.preinvApplyMarkingService.PreinvApplyMarkingService" scope="prototype"/>
    <!-- 仅在85环境中存在: bean#PreinvApplyRiskDataTask -->
    <bean id="PreinvApplyRiskDataTask" class="com.xinxinsoft.task.PreinvApplyRiskDataTask">
        <property name="riskClosedLoopService">
            <ref bean="RiskClosedLoopService"/>
        </property>
    </bean>
    <bean id="PreinvApplyService" class="com.xinxinsoft.service.PreinvApply.PreinvApplyService"/>
    <bean id="PreinvIoTCardService" class="com.xinxinsoft.service.preinvIoTCardService.PreinvIoTCardService"/>
    <bean id="Proc4AOracle" class="com.xinxinsoft.task.Proc4AOracle">
        <property name="userService">
            <ref bean="User4AService"/>
        </property>
        <property name="uuService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="ProductFlowService" class="com.xinxinsoft.service.processLink.ProductFlowService"/>
    <bean id="ProductTypeService" class="com.xinxinsoft.service.basetype.ProductTypeService"/>
    <bean id="PushBusiOppLogService" class="com.xinxinsoft.service.oms.PushBusiOppLogService"/>
    <bean id="QueryPaymentService" class="com.xinxinsoft.service.pay.QueryPaymentService"/>
    <bean id="QuestionService" class="com.xinxinsoft.service.checkservice.QuestionService"/>
    <bean id="QuestionnaireService" class="com.xinxinsoft.service.QuestionSurveyService.QuestionnaireService"/>
    <bean id="QuickQueryService" class="com.xinxinsoft.service.quickQueryService.QuickQueryService"/>
    <bean id="ReceiptApplyAmountService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyAmountService"/>
    <bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"/>
    <bean id="ReceiveApplyService" class="com.xinxinsoft.service.receiveApply.ReceiveApplyService"/>
    <bean id="RecommendedManagementService" class="com.xinxinsoft.service.RecommendedManagement.RecommendedManagementService"/>
    <bean id="ReconciliationService" class="com.xinxinsoft.service.pay.ReconciliationService"/>
    <bean id="ReconnaissanceResultService" class="com.xinxinsoft.service.reconnaissanceResult.ReconnaissanceResultService"/>
    <bean id="ReductionICTApplicationService" class="com.xinxinsoft.service.ICT.ReductionICTApplicationService"/>
    <bean id="RefundOrderService" class="com.xinxinsoft.service.pay.RefundOrderService" autowire="byType"/>
    <bean id="RefundQuerySendService" class="com.xinxinsoft.service.pay.RefundQuerySendService"/>
    <bean id="RefundQueryService" class="com.xinxinsoft.service.pay.RefundQueryService"/>
    <bean id="RefundService" class="com.xinxinsoft.service.pay.RefundService"/>
    <bean id="RepairOrderService" class="com.xinxinsoft.service.RepairOrderService.RepairOrderService"/>
    <bean id="ResourceSurveyTask" class="com.xinxinsoft.task.ResourceSurveyTask">
        <property name="singleService">
            <ref bean="commonSingleService"/>
        </property>
        <property name="openService">
            <ref bean="CMCCOpenService"/>
        </property>
        <property name="waitTaskservice">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="userService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService"/>
    <bean id="RiskDutyService" class="com.xinxinsoft.service.RiskDutyService.RiskDutyService"/>
    <bean id="RiskcontrolDerivedService" class="com.xinxinsoft.service.riskcontrolDerivedService.RiskcontrolDerivedService"/>
    <bean id="RoleService" class="com.xinxinsoft.service.core.role.RoleService">
        <property name="menuItemService">
            <ref bean="MenuItemService"/>
        </property>
    </bean>
    <bean id="SEinvQryTask" class="com.xinxinsoft.task.SEinvQryTask">
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
        <property name="monthlyinvoiceService">
            <ref bean="MonthlyinvoiceService"/>
        </property>
    </bean>
    <bean id="SIMService" class="com.xinxinsoft.service.SIM.SIMService" scope="prototype"/>
    <bean id="SRestoreDeadUserService" class="com.xinxinsoft.service.SRestoreDeadUser.SRestoreDeadUserService"/>
    <bean id="SaveSendCheckService" class="com.xinxinsoft.service.SaveSendService.SaveSendCheckService"/>
    <bean id="SaveSendCheckTimeService" class="com.xinxinsoft.service.SaveSendService.SaveSendCheckTimeService"/>
    <bean id="SaveSendService" class="com.xinxinsoft.service.SaveSendService.SaveSendService"/>
    <bean id="SealService" class="com.xinxinsoft.service.sign.SealService"/>
    <bean id="ServiceShutdownAndStartupService" class="com.xinxinsoft.service.ServiceShutdownAndStartupService.ServiceShutdownAndStartupService"/>
    <bean id="ServiceStandardizationTestingService" class="com.xinxinsoft.service.oms.ServiceStandardizationTestingService"/>
    <bean id="SettlementService" class="com.xinxinsoft.service.pay.SettlementService"/>
    <bean id="SignService" class="com.xinxinsoft.service.sign.SignService">
        <property name="sealService">
            <ref bean="SealService"/>
        </property>
    </bean>
    <bean id="SpeciaPlanService" class="com.xinxinsoft.service.SpeciaPlanService.SpeciaPlanService"/>
    <bean id="SpeciaPlanSev" class="com.xinxinsoft.sendComms.SpeciaPlanSev"/>
    <bean id="StagesAyncBossTask" class="com.xinxinsoft.task.StagesAyncBossTask">
        <property name="singleService">
            <ref bean="commonSingleService"/>
        </property>
        <property name="flowService">
            <ref bean="DedicatedFlowService"/>
        </property>
        <property name="lateService">
            <ref bean="LinkTemplateService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="StructureOfPersonnelService" class="com.xinxinsoft.service.core.user.StructureOfPersonnelService"/>
    <bean id="SuoCollectCheckTimeService" class="com.xinxinsoft.service.SaveSendService.SuoCollectCheckTimeService"/>
    <bean id="SupCollectService" class="com.xinxinsoft.service.SaveSendService.SupCollectService"/>
    <bean id="SuspensionApplicationService" class="com.xinxinsoft.service.SuspensionApplicationService.SuspensionApplicationService"/>
    <bean id="SysLogChangeTask" class="com.xinxinsoft.task.SysLogChangeTask">
        <property name="sysLogService">
            <ref bean="SysLogService"/>
        </property>
        <property name="pushService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <bean id="SysLogService" class="com.xinxinsoft.service.sysLogService.SysLogService"/>
    <bean id="SystemOrganizationService" class="com.xinxinsoft.service.core.org.SystemOrganizationService"/>
    <bean id="SystemUserService" class="com.xinxinsoft.service.core.user.SystemUserService">
        <property name="passwordEncoder">
            <ref bean="passwordEncoder"/>
        </property>
        <property name="loginUserService">
            <ref bean="LoginUserService"/>
        </property>
    </bean>
    <bean id="SystemWarningTask" class="com.xinxinsoft.task.SystemWarningTask" scope="prototype">
        <property name="pushService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <bean id="TariffManagementService" class="com.xinxinsoft.service.tariffManagement.TariffManagementService"/>
    <bean id="TelnetPort" class="com.xinxinsoft.task.TelnetPort">
        <property name="smsPushService">
            <ref bean="smsPushService"/>
        </property>
    </bean>
    <bean id="TerminalActivityService" class="com.xinxinsoft.service.terminalActivityAdministration.TerminalActivityService"/>
    <bean id="TerminalCheckTimeService" class="com.xinxinsoft.service.SaveSendService.TerminalCheckTimeService"/>
    <bean id="TerminalService" class="com.xinxinsoft.service.SaveSendService.TerminalService"/>
    <bean id="TransferAccountsCheckTimeService" class="com.xinxinsoft.service.SaveSendService.TransferAccountsCheckTimeService"/>
    <bean id="TransferAccountsService" class="com.xinxinsoft.service.SaveSendService.TransferAccountsService"/>
    <bean id="TransferCitiesDataService" class="com.xinxinsoft.service.transfer.TransferCitiesDataService"/>
    <bean id="TransferInformationService" class="com.xinxinsoft.service.transfer.TransferInformationService">
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="TransferInformationTwoService" class="com.xinxinsoft.service.transfer.TransferInformationTwoService">
        <property name="transferJBPMUtils">
            <ref bean="TransferJBPMUtils"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="TransferInterfaceService" class="com.xinxinsoft.service.transfer.TransferInterfaceService"/>
    <bean id="TransferService" class="com.xinxinsoft.service.transfer.TransferService"/>
    <bean id="TripartipartUserService" class="com.xinxinsoft.service.core.user.TripartipartUserService"/>
    <bean id="TypeValueService" class="com.xinxinsoft.service.core.typeValue.TypeValueService"/>
    <bean id="USIMService" class="com.xinxinsoft.service.USIMService.USIMService"/>
    <bean id="UnitInfoService" class="com.xinxinsoft.service.ums.UnitInfoService"/>
    <bean id="UploadDocumentService" class="com.xinxinsoft.service.uploadDocumentService.UploadDocumentService" scope="prototype"/>
    <bean id="User4AService" class="com.xinxinsoft.service.core.user.User4AService"/>
    <bean id="V2OmsOrderManagerSignInTask" class="com.xinxinsoft.task.V2OmsOrderManagerSignInTask" scope="prototype">
        <property name="omsSellOrderService">
            <ref bean="V2OmsSellOrderService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="taskService">
            <ref bean="Bpms_riskoff_service"/>
        </property>
        <property name="structureOfPersonnelService">
            <ref bean="StructureOfPersonnelService"/>
        </property>
    </bean>
    <bean id="V2OmsSellOrderService" class="com.xinxinsoft.service.oms.V2OmsSellOrderService"/>
    <bean id="ValuableCardService" class="com.xinxinsoft.service.valuableCard.ValuableCardService"/>
    <bean id="Various1000SqlQueryService" class="com.xinxinsoft.service.appOpenService.Various1000SqlQueryService"/>
    <bean id="VariousSqlQueryService" class="com.xinxinsoft.service.appOpenService.VariousSqlQueryService">
        <property name="conSer">
            <ref bean="ContractService"/>
        </property>
    </bean>
    <bean id="WaitTaskService" class="com.xinxinsoft.service.waitTask.WaitTaskService">
        <property name="pushService">
            <ref bean="smsPushService"/>
        </property>
        <property name="systemUserService">
            <ref bean="SystemUserService"/>
        </property>
    </bean>
    <bean id="WatermarkContractWebservice" class="com.xinxinsoft.service.webService.WatermarkContract.WatermarkContractWebservice"/>
    <bean id="WhiteListInformationService" class="com.xinxinsoft.service.whiteList.WhiteListInformationService"/>
    <bean id="WhiteListService" class="com.xinxinsoft.service.whiteList.WhiteListService"/>
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"/>
    <bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"/>
    <bean id="ZtreeUserService" class="com.xinxinsoft.service.core.user.ZtreeUserService"/>
    <bean id="accountOpenService" class="com.xinxinsoft.service.accountOpenService.AccountOpenService" scope="prototype"/>
    <bean id="appLoginService" class="com.xinxinsoft.service.appLoginService.appLoginService"/>
    <bean id="appUploadService" class="com.xinxinsoft.service.core.app.AppUploadService"/>
    <bean id="arrearsTask" class="com.xinxinsoft.task.ArrearsTask">
        <property name="psService">
            <ref bean="PaymentRecordService"/>
        </property>
        <property name="arrearsService">
            <ref bean="ArrearsService"/>
        </property>
    </bean>
    <bean id="claimForFundModelService" class="com.xinxinsoft.service.claimForFunds.claimForFundModelService"/>
    <bean id="commonSingleService" class="com.xinxinsoft.service.commonSingManagement.CommonSingleService">
        <property name="dicService">
            <ref bean="DictionaryService"/>
        </property>
        <property name="service">
            <ref bean="WaitTaskService"/>
        </property>
        <property name="attservcie">
            <ref bean="AttachmentService"/>
        </property>
    </bean>
    <bean id="customerService" class="com.xinxinsoft.service.customer.customerService">
	</bean>
    <bean id="customerServices" class="com.xinxinsoft.sendComms.GroupCustomerService"/>
    <bean id="fourtOpenService" class="com.xinxinsoft.service.fourtOpenService.fourtOpenService"/>
    <bean id="groupReportedLossesService" class="com.xinxinsoft.service.GroupReportedService.GroupReportedLossesService"/>
    <bean id="holidayService" class="com.xinxinsoft.service.holiday.HolidayService"/>
    <bean id="holidayTask" class="com.xinxinsoft.task.HolidayTask">
        <property name="holidayService">
            <ref bean="holidayService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="imsService" class="com.xinxinsoft.service.dedicatedFlow.ims.ImsService"/>
    <bean id="itoService" class="com.xinxinsoft.service.dedicatedFlow.ito.ItoService"/>
    <bean id="jbpmProcessService" class="com.xinxinsoft.service.jbpmProcess.JbpmProcessService"/>
    <bean id="lwFtpTask" class="com.xinxinsoft.task.LwFtpTask">
        <property name="operFTPservice">
            <ref bean="operFTPservice"/>
        </property>
        <property name="workOrderService">
            <ref bean="WorkOrderServcie"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="messagesInfoService" class="com.xinxinsoft.service.messages.MessagesInfoService"/>
    <bean id="missionConfigService" class="com.xinxinsoft.service.MissionConfigService.missionConfigService"/>
    <bean id="operFTPservice" class="com.xinxinsoft.service.ftpss.OperFTPservice"/>
    <bean id="orderDetailService" class="com.xinxinsoft.service.order.OrderDetailService"/>
    <bean id="passwordEncoder" class="org.acegisecurity.providers.encoding.Md5PasswordEncoder"/>
    <bean id="payCenterAPIWebService" class="com.xinxinsoft.service.webService.pay.PayCenterAPIWebService">
        <constructor-arg name="payApiService" ref="PayApiService"/>
    </bean>
    <bean id="paymentProviderWebService" class="com.xinxinsoft.service.webService.pay.PaymentProviderWebService">
        <constructor-arg name="paymentProviderService" ref="PaymentProviderService"/>
        <constructor-arg name="busiParameterService" ref="BusiParameterService"/>
        <constructor-arg name="goodsInfoService" ref="GoodsInfoService"/>
        <constructor-arg name="orderQueryService" ref="OrderQueryService"/>
        <constructor-arg name="orderQuerySendService" ref="OrderQuerySendService"/>
        <constructor-arg name="refundService" ref="RefundService"/>
        <constructor-arg name="refundQueryService" ref="RefundQueryService"/>
        <constructor-arg name="refundQuerySendService" ref="RefundQuerySendService"/>
        <constructor-arg name="paymentOrderService" ref="PaymentOrderService"/>
        <constructor-arg name="refundOrderService" ref="RefundOrderService"/>
        <constructor-arg name="variousSqlQueryService" ref="VariousSqlQueryService"/>
    </bean>
    <bean id="paymentWebService" class="com.xinxinsoft.service.webService.pay.PaymentWebService">
        <constructor-arg name="paymentService" ref="PaymentService"/>
        <constructor-arg name="queryPaymentService" ref="QueryPaymentService"/>
        <constructor-arg name="payRefundService" ref="PayRefundService"/>
    </bean>
    <bean id="proceduresTask" class="com.xinxinsoft.task.ProceduresTask">
        <property name="userService">
            <ref bean="SystemUserService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="processService" class="com.xinxinsoft.service.core.processService.ProcessService"/>
    <bean id="qryPreDealOutService" class="com.xinxinsoft.service.boss.QryPreDealOutService"/>
    <bean id="realNameReservService" class="com.xinxinsoft.service.RealNameReservService.realNameReservService"/>
    <bean id="reconciliationTask" class="com.xinxinsoft.task.ReconciliationTask">
        <property name="reconciliationService">
            <ref bean="ReconciliationService"/>
        </property>
        <property name="settlementService">
            <ref bean="SettlementService"/>
        </property>
        <property name="jobLogServicer">
            <ref bean="JobLogServicer"/>
        </property>
    </bean>
    <bean id="redRollListService" class="com.xinxinsoft.service.redListService.RedRollListService" scope="prototype"/>
    <bean id="smsPushService" class="com.xinxinsoft.service.smsPush.SmsPushService"/>
    <bean id="sngListExcelService" class="com.xinxinsoft.service.commonSingManagement.SingListExcelService"/>
    <bean id="springContextUtil" class="com.xinxinsoft.utils.SpringContextUtil"/>
    <bean id="startPreOrderOutService" class="com.xinxinsoft.service.boss.StartPreOrderOutService"/>
    <bean id="statisticsService" class="com.xinxinsoft.service.statistics.StatisticsService"/>
    <bean id="systemDeptService" class="com.xinxinsoft.service.core.dept.SystemDeptService"/>
    <bean id="voucherCenterWebService" class="com.xinxinsoft.service.webService.pay.VoucherCenterWebService">
        <constructor-arg name="paymentService" ref="PaymentService"/>
        <constructor-arg name="queryPaymentService" ref="QueryPaymentService"/>
        <constructor-arg name="payRefundService" ref="PayRefundService"/>
    </bean>
    <import resource="classpath:META-INF/cxf/cxf-servlet.xml"/>
    <jaxws:server id="WatermarkContractWebservice" serviceClass="com.xinxinsoft.service.webService.WatermarkContract.WatermarkContractWebservice" address="/WatermarkContractWebservice">
        <jaxws:serviceBean>
            <ref bean="WatermarkContractWebservice"/>
        </jaxws:serviceBean>
    </jaxws:server>
    <jaxws:server id="webService4AChange" serviceClass="com.xinxinsoft.service.webService.AccountChange4AWebService" address="/AccountChange4AWebService">
        <jaxws:serviceBean>
            <ref bean="AccountChange4AWebService"/>
        </jaxws:serviceBean>
    </jaxws:server>
    <jaxws:server id="webServiceCenterApi" serviceClass="com.xinxinsoft.service.webService.pay.PayCenterAPIWebService" address="/payCenterAPIWebService">
        <jaxws:serviceBean>
            <ref bean="payCenterAPIWebService"/>
        </jaxws:serviceBean>
    </jaxws:server>
    <jaxws:server id="webServiceLogin" serviceClass="com.xinxinsoft.service.webService.AppLoginWebService" address="/AppLoginWebService">
        <jaxws:serviceBean>
            <ref bean="AppLoginWebService"/>
        </jaxws:serviceBean>
    </jaxws:server>
    <jaxws:server id="webServicePayment" serviceClass="com.xinxinsoft.service.webService.pay.PaymentWebService" address="/paymentWebService">
        <jaxws:serviceBean>
            <ref bean="paymentWebService"/>
        </jaxws:serviceBean>
    </jaxws:server>
    <jaxws:server id="webServicePaymentProvider" serviceClass="com.xinxinsoft.service.webService.pay.PaymentProviderWebService" address="/paymentProviderWebService">
        <jaxws:serviceBean>
            <ref bean="paymentProviderWebService"/>
        </jaxws:serviceBean>
    </jaxws:server>
    <jaxws:server id="webServiceVoucherCenter" serviceClass="com.xinxinsoft.service.webService.pay.VoucherCenterWebService" address="/voucherCenterWebService">
        <jaxws:serviceBean>
            <ref bean="voucherCenterWebService"/>
        </jaxws:serviceBean>
    </jaxws:server>
</beans>