<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
	xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:aop="http://www.springframework.org/schema/aop"
	xmlns:tx="http://www.springframework.org/schema/tx" xmlns:jaxws="http://cxf.apache.org/jaxws"
	xsi:schemaLocation="
		    http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans-3.1.xsd
		    http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx-3.1.xsd
		    http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop-3.1.xsd
		    http://cxf.apache.org/jaxws  http://cxf.apache.org/schemas/jaxws.xsd"
	default-autowire="byName">

    <!-- EOM系统配置文件 - 详细合并版本 -->
    <!-- 此文件由85和86环境配置合并生成 -->
    <!-- 合并规则: 保留所有配置，详细标注差异 -->
    <!-- 生成时间: 自动生成 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AbnormalNumberService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AbnormalNumberService" class="com.xinxinsoft.service.abnormalNumberService.AbnormalNumberService" />
    <!--	集团实名预约-->
    <bean id="realNameReservService" class="com.xinxinsoft.service.RealNameReservService.realNameReservService" />
    <!--138附件管理-->
    <bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService" />
    <!-- 异常数据管理 -->
    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
    <property name="abnormalNumberService">
    <ref bean="AbnormalNumberService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AbnormalNumberTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
    <property name="abnormalNumberService">
    <ref bean="AbnormalNumberService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AccountChange4AWebService -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AccountChange4AWebService" -->
    <!-- class="com.xinxinsoft.service.webService.AccountChange4AWebService"> -->
    <!-- <constructor-arg name="userService"> -->
    <!-- <ref bean="SystemUserService" /> -->
    <!-- </constructor-arg> -->
    <!-- </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AccountOrderInfoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AccountOrderInfoService" class="com.xinxinsoft.service.pay.AccountOrderInfoService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ActivityPreinvApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ActivityPreinvApplyService" class="com.xinxinsoft.service.activityPreinvService.ActivityPreinvApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ActivityService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ActivityService" class="com.xinxinsoft.service.honorCode.ActivityService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AgreementService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AgreementService" class="com.xinxinsoft.service.arrearsModule.AgreementService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AllPayDesignService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AllPayDesignService" class="com.xinxinsoft.service.AllMembersPayService.AllPayDesignService" />
    <!--SIM物联网卡-->
    <bean id="SIMService" class="com.xinxinsoft.service.SIM.SIMService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AndFlySpeedService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AndFlySpeedService" class="com.xinxinsoft.service.andFlySpeedService.AndFlySpeedService" />
    <!--异常号码管理-->
    <bean id="AbnormalNumberService" class="com.xinxinsoft.service.abnormalNumberService.AbnormalNumberService" />
    <!--	集团实名预约-->
    <bean id="realNameReservService" class="com.xinxinsoft.service.RealNameReservService.realNameReservService" />
    <!--138附件管理-->
    <bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService" />
    <!-- 异常数据管理 -->
    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
    <property name="abnormalNumberService">
    <ref bean="AbnormalNumberService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppDynamicContractService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppDynamicContractService" class="com.xinxinsoft.service.contract.AppDynamicContractService" />
    <!--问卷调查服务方法类-->
    <bean id="QuestionnaireService" class="com.xinxinsoft.service.QuestionSurveyService.QuestionnaireService" />
    <!--三方人员导入审批-->
    <bean id="TripartipartUserService" class="com.xinxinsoft.service.core.user.TripartipartUserService" />
    <bean id="V2OmsOrderManagerSignInTask" class="com.xinxinsoft.task.V2OmsOrderManagerSignInTask" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="structureOfPersonnelService">
    <ref bean="StructureOfPersonnelService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppLoginService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppLoginService" class="com.xinxinsoft.service.core.app.AppLoginService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppLoginWebService -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppLoginWebService" class="com.xinxinsoft.service.webService.AppLoginWebService">
    <constructor-arg name="appLoginService">
    <ref bean="AppLoginService" />
    </constructor-arg>
    <constructor-arg name="appUserInfoService">
    <ref bean="AppUserInfoService" />
    </constructor-arg>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AppUserInfoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AppUserInfoService" class="com.xinxinsoft.service.core.app.AppUserInfoService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsService" class="com.xinxinsoft.service.arrearsModule.ArrearsService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsSingSerivce -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsSingSerivce" class="com.xinxinsoft.service.arrearsModule.ArrearsSingSerivce">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsSingTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsSingTask" class="com.xinxinsoft.task.ArrearsSingTask">
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    <property name="arrearsService">
    <ref bean="ArrearsService"/>
    </property>
    <property name="arrearsSingSerivce">
    <ref bean="ArrearsSingSerivce"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsStatisticalService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsStatisticalService" class="com.xinxinsoft.service.arrearsModule.ArrearsStatisticalService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsStatisticalTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsStatisticalTask" class="com.xinxinsoft.task.ArrearsStatisticalTask">
    <property name="arrearsService">
    <ref bean="ArrearsService" />
    </property>
    <property name="arrearsStatisticalService">
    <ref bean="ArrearsStatisticalService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsWriteOffService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsWriteOffService" class="com.xinxinsoft.service.arrearsWriteOffService.ArrearsWriteOffService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ArrearsWriteOffTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ArrearsWriteOffTask" class="com.xinxinsoft.task.ArrearsWriteOffTask">
    <property name="arrearsWriteOffService">
    <ref bean="ArrearsWriteOffService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AttachmentService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AttachmentService" class="com.xinxinsoft.service.enclosure.AttachmentService">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AttachmentServiceTwo -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService" />
    <!-- 异常数据管理 -->
    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
    <property name="abnormalNumberService">
    <ref bean="AbnormalNumberService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AttachmentSpaceReminderTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AttachmentSpaceReminderTask" class="com.xinxinsoft.task.AttachmentSpaceReminderTask">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AttachmentTypeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AttachmentTypeService" class="com.xinxinsoft.service.enclosure.AttachmentTypeService">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditMultipleService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditMultipleService" class="com.xinxinsoft.service.AuditWorksheetService.AuditMultipleService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditWorkListService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditWorkListService" class="com.xinxinsoft.service.AuditWorksheetService.AuditWorkListService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditWorksheetService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditWorksheetService" class="com.xinxinsoft.service.AuditWorksheetService.AuditWorksheetService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditWorksheetTask -->
    <!-- 85环境: 2个属性, 5个子元素 -->
    <!-- 86环境: 2个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditWorksheetTask" class="com.xinxinsoft.task.AuditWorksheetTask">
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="auditWorksheetService">
    <ref bean="AuditWorksheetService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AuditintersLogsTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AuditintersLogsTask" class="com.xinxinsoft.task.AuditintersLogsTask">
    <property name="ibbnService">
    <ref bean="IbbnService" />
    </property>
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <!--<property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>-->
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#AutomaticArchivingTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="AutomaticArchivingTask" class="com.xinxinsoft.task.AutomaticArchivingTask">
    <property name="dedicatedFlowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BOGetService -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BOGetService" class="com.xinxinsoft.service.webService.BOGetService">
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BigAmountApplyFlowService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BigAmountApplyFlowService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyFlowService" />
    <!-- 存送 任务 -->
    <bean id="BigAmountApplyService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyService" />
    <!-- 存送 活动 -->
    <bean id="BigAmountDepositSendContractService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountDepositSendContractService" />
    <!-- 大额存送boos接口-->
    <bean id="IChkPreInvoiceActAoSvc" class="com.xinxinsoft.service.webService.IChkPreInvoiceActAoSvc" />
    <!-- 正负补收 -->
    <bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BigAmountApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BigAmountApplyService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyService" />
    <!-- 存送 活动 -->
    <bean id="BigAmountDepositSendContractService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountDepositSendContractService" />
    <!-- 大额存送boos接口-->
    <bean id="IChkPreInvoiceActAoSvc" class="com.xinxinsoft.service.webService.IChkPreInvoiceActAoSvc" />
    <!-- 正负补收 -->
    <bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BigAmountApplyTaskService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BigAmountApplyTaskService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyTaskService" />
    <!-- 存送 任务 -->
    <bean id="BigAmountApplyFlowService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyFlowService" />
    <!-- 存送 任务 -->
    <bean id="BigAmountApplyService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountApplyService" />
    <!-- 存送 活动 -->
    <bean id="BigAmountDepositSendContractService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountDepositSendContractService" />
    <!-- 大额存送boos接口-->
    <bean id="IChkPreInvoiceActAoSvc" class="com.xinxinsoft.service.webService.IChkPreInvoiceActAoSvc" />
    <!-- 正负补收 -->
    <bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BigAmountDepositSendContractService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BigAmountDepositSendContractService" class="com.xinxinsoft.service.bigAmountApplyService.BigAmountDepositSendContractService" />
    <!-- 大额存送boos接口-->
    <bean id="IChkPreInvoiceActAoSvc" class="com.xinxinsoft.service.webService.IChkPreInvoiceActAoSvc" />
    <!-- 正负补收 -->
    <bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BossTacheService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BossTacheService" class="com.xinxinsoft.service.basetype.BossTacheService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BossTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BossTask" class="com.xinxinsoft.task.BossTask">
    <property name="singleService">
    <ref bean="commonSingleService" />
    </property>
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#Bpms_riskoff_service -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="Bpms_riskoff_service" class="com.xinxinsoft.service.PublicService.Bpms_riskoff_service">
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusiOppService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusiOppService" class="com.xinxinsoft.sendComms.omsService.BusiOppService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusiOppTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusiOppTask" class="com.xinxinsoft.task.BusiOppTask">
    <!--推送商机系统记录servcie-->
    <property name="pushBusiOppLogService">
    <ref bean="PushBusiOppLogService" />
    </property>
    <!--一键下单回调商机系统接口-->
    <property name="busiOppService">
    <ref bean="BusiOppService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusiParameterService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusiParameterService" class="com.xinxinsoft.service.pay.Busi_ParameterService" ></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinessEntityService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinessEntityService"
    class="com.xinxinsoft.service.businessProc.BusinessEntityService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinessTypeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinessTypeService" class="com.xinxinsoft.service.basetype.BusinessTypeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#BusinssService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="BusinssService" class="com.xinxinsoft.service.businss.BusinssService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CMCC1000OpenService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CMCC1000OpenService" class="com.xinxinsoft.sendComms.CMCC1000OpenService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CMCCOpenService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CMCCOpenService" class="com.xinxinsoft.sendComms.CMCCOpenService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ChannelInfoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ChannelInfoService" class="com.xinxinsoft.service.honorCode.ChannelInfoService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CheckService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CheckService" class="com.xinxinsoft.service.checkservice.CheckService" />
    <!-- 存送单service -->
    <bean id="SaveSendService" class="com.xinxinsoft.service.SaveSendService.SaveSendService" />
    <!-- 补收service -->
    <bean id="SupCollectService"
    class="com.xinxinsoft.service.SaveSendService.SupCollectService" />
    <!-- 终端service -->
    <bean id="TerminalService" class="com.xinxinsoft.service.SaveSendService.TerminalService" />
    <!-- 转账service -->
    <bean id="TransferAccountsService"
    class="com.xinxinsoft.service.SaveSendService.TransferAccountsService" />
    <!-- 工单service -->
    <bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimAContractService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimAContractService" class="com.xinxinsoft.service.contract.ClaimAContractService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundsService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundsService" class="com.xinxinsoft.service.claimForFunds.ClaimForFundsService" />
    <bean id="ClaimForFundsTask" class="com.xinxinsoft.task.ClaimForFundsTask">
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimForFundsTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClaimForFundsTask" class="com.xinxinsoft.task.ClaimForFundsTask">
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClaimFundReturnService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ClearCode -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ClearCode" class="com.xinxinsoft.task.ClearCode">
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CompassFilingGroupTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CompassFilingGroupTask" class="com.xinxinsoft.task.CompassFilingGroupTask">
    <property name="unitInfoService">
    <ref bean="UnitInfoService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractCsv -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractCsv" class="com.xinxinsoft.task.ContractCsv">
    <property name="contractToOrderCSVService">
    <ref bean="ContractToOrderCSVService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractExpiredTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractExpiredTask" class="com.xinxinsoft.task.ContractExpiredTask">
    <property name="customClauseContractService">
    <ref bean="CustomClauseContractService" />
    </property>
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractHttpService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractHttpService" class="com.xinxinsoft.service.httpService.ContractHttpService" />
    <bean id="proceduresTask" class="com.xinxinsoft.task.ProceduresTask">
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractService" class="com.xinxinsoft.service.contract.ContractService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractToOrderCSVService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractToOrderCSVService" class="com.xinxinsoft.service.contractToOrderCSVService.ContractToOrderCSVService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractToOrderCsv -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractToOrderCsv" class="com.xinxinsoft.task.ContractToOrderCsv">
    <property name="contractToOrderCSVService">
    <ref bean="ContractToOrderCSVService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractUniformityService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractUniformityService" class="com.xinxinsoft.service.contractUniformityService.ContractUniformityService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractUniformityTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractUniformityTask" class="com.xinxinsoft.task.ContractUniformityTask">
    <property name="contractUniformityService">
    <ref bean="ContractUniformityService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractUtils -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractUtils" class="com.xinxinsoft.utils.ContractUtils">
    <property name="attachmentService">
    <ref bean="AttachmentService" />
    </property>
    <property name="contractService">
    <ref bean="ContractService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ContractualProductTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CountShareService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CountShareService" class="com.xinxinsoft.service.countShare.CountShareService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CustomClauseContractService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CustomClauseContractService" class="com.xinxinsoft.service.contract.CustomClauseContractService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#CustomerAccountService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="CustomerAccountService"
    class="com.xinxinsoft.service.customeraccount.CustomerAccountService">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DedicatedFlowService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DedicatedFlowService"
    class="com.xinxinsoft.service.dedicatedFlow.DedicatedFlowService" />
    <!-- 需求申请 -->
    <bean id="ExpenseApplyService"
    class="com.xinxinsoft.service.dedicatedFlow.ExpenseApplyService" />
    <!-- 环节模板 -->
    <bean id="LinkTemplateService" class="com.xinxinsoft.service.processLink.LinkTemplateService" />
    <!-- 菜单管理 -->
    <bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DeleteFile -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DeleteFile" class="com.xinxinsoft.task.DeleteFile" >
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DictOrderHttpService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DictOrderHttpService" class="com.xinxinsoft.service.httpService.DictOrderHttpService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#DictionaryService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="DictionaryService" class="com.xinxinsoft.service.core.DictionaryService" />
    <!-- 产品流程 -->
    <bean id="ProductFlowService" class="com.xinxinsoft.service.processLink.ProductFlowService" />
    <!-- 需求申请 -->
    <bean id="DedicatedFlowService"
    class="com.xinxinsoft.service.dedicatedFlow.DedicatedFlowService" />
    <!-- 需求申请 -->
    <bean id="ExpenseApplyService"
    class="com.xinxinsoft.service.dedicatedFlow.ExpenseApplyService" />
    <!-- 环节模板 -->
    <bean id="LinkTemplateService" class="com.xinxinsoft.service.processLink.LinkTemplateService" />
    <!-- 菜单管理 -->
    <bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EditionService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EditionService" class="com.xinxinsoft.service.core.app.EditionService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EipUserTask -->
    <!-- 85环境: 2个属性, 4个子元素 -->
    <!-- 86环境: 2个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EipUserTask" class="com.xinxinsoft.task.EipUserTask">
    <property name="deptService">
    <ref bean="systemDeptService" />
    </property>
    <property name="ownService">
    <ref bean="OwnDepartmentsService" />
    </property>
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#EnterPriseTask -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="EnterPriseTask" class="com.xinxinsoft.task.EnterPriseTask" />
    <bean id="SystemWarningTask" class="com.xinxinsoft.task.SystemWarningTask" scope="prototype">
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ExpenseApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ExpenseApplyService"
    class="com.xinxinsoft.service.dedicatedFlow.ExpenseApplyService" />
    <!-- 环节模板 -->
    <bean id="LinkTemplateService" class="com.xinxinsoft.service.processLink.LinkTemplateService" />
    <!-- 菜单管理 -->
    <bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GoodsInfoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GoodsInfoService" class="com.xinxinsoft.service.pay.Goods_InfoService" ></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupAccountService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupAccountService" class="com.xinxinsoft.service.groupAccountService.GroupAccountService" />
    <!-- 检查系统端口-->
    <bean id="TelnetPort" class="com.xinxinsoft.task.TelnetPort">
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupCustomerService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupCustomerService"
    class="com.xinxinsoft.service.groupcustomer.GroupCustomerService">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupInviteCodeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupInviteCodeService" class="com.xinxinsoft.service.groupInviteCodeService.GroupInviteCodeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupPaymentInterfaceService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupPaymentService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupPeopleService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="GroupPeopleService"
    class="com.xinxinsoft.service.customeraccount.GroupPeopleService">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#GroupTaxpayerService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#HonorCodeBusinessService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="HonorCodeBusinessService"
    class="com.xinxinsoft.service.honorCode.HonorCodeBusinessService"></bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#HonorUpLoadTask -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 配置组件 -->
    <!-- ===================================== -->

    <bean id="HonorUpLoadTask" class="com.xinxinsoft.task.HonorUpLoadTask">
    <property name="activityService">
    <ref bean="ActivityService"/>
    </property>
    <property name="channelInfoService">
    <ref bean="ChannelInfoService"/>
    </property>
    <property name="honorCodeBusinessService">
    <ref bean="HonorCodeBusinessService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ICTApplicationService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ICTApplicationService" class="com.xinxinsoft.service.ICT.ICTApplicationService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IChkPreInvoiceActAoSvc -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IChkPreInvoiceActAoSvc" class="com.xinxinsoft.service.webService.IChkPreInvoiceActAoSvc" />
    <!-- 正负补收 -->
    <bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IDCApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IDCApplyService" class="com.xinxinsoft.service.IDCService.IDCApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IDCFlowService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IDCFlowService" class="com.xinxinsoft.service.IDCService.IDCFlowService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IDCTaskService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IDCTaskService" class="com.xinxinsoft.service.IDCService.IDCTaskService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IMSHighFrequencyService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IMSHighFrequencyService" class="com.xinxinsoft.service.IMSHighFrequencyService.IMSHighFrequencyService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IbbnService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IbbnService" class="com.xinxinsoft.service.IBossService.IBossByNoService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IctContractService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IctContractService" class="com.xinxinsoft.service.IctContract.IctContractService" autowire="byType"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IctContractTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IctContractTask" class="com.xinxinsoft.task.IctContractTask">
    <property name="ictContractService">
    <ref bean="IctContractService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IdcService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IdcService"  class="com.xinxinsoft.sendComms.IdcService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IndustryTerminalService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IndustryTerminalService" class="com.xinxinsoft.service.IndustryTerminal.IndustryTerminalService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#InquiryOrderService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="InquiryOrderService" class="com.xinxinsoft.service.InquiryOrder.InquiryOrderService"/>
    <!-- 缓停 -->
    <bean id="SuspensionApplicationService" class="com.xinxinsoft.service.SuspensionApplicationService.SuspensionApplicationService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#IntegrationService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="IntegrationService" class="com.xinxinsoft.service.v2.integrationService.IntegrationService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#InterfaceLogService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="InterfaceLogService" class="com.xinxinsoft.service.anySrc.InterfaceLogService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#JobLogServicer -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="JobLogServicer" class="com.xinxinsoft.service.executejoblog.JobLogServicer"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#JobRejectionInfoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService" />
    <!--风险控制闭环管理-->
    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
    <!--政企白名单-->
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#KnowledgeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="KnowledgeService" class="com.xinxinsoft.service.knowledge.KnowledgeService" />
    <!-- 待办任务 -->
    <bean id="WaitTaskService" class="com.xinxinsoft.service.waitTask.WaitTaskService">
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LateFeeMoneyDataService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LateFeeMoneyDataService" class="com.xinxinsoft.service.claimForFunds.LateFeeMoneyDataService" />
    <!--欠费和销账-->
    <bean id="ArrearsWriteOffService" class="com.xinxinsoft.service.arrearsWriteOffService.ArrearsWriteOffService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LinkScoreService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LinkScoreService" class="com.xinxinsoft.service.linkScore.LinkScoreService">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LinkTemplateService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LinkTemplateService" class="com.xinxinsoft.service.processLink.LinkTemplateService" />
    <!-- 菜单管理 -->
    <bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#LoginUserService -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="LoginUserService" class="com.xinxinsoft.service.core.user.LoginUserService">
    <property name="passwordEncoder">
    <ref bean="passwordEncoder"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#Logs4AWebServices -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="Logs4AWebServices" class="com.xinxinsoft.service.webService.Logs4AWebServices">
    <property name="ibbnService">
    <ref bean="IbbnService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ManualInvApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ManualInvApplyService" class="com.xinxinsoft.service.manualInvApply.ManualInvApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MarketActivitiesService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MarketActivitiesService" class="com.xinxinsoft.service.MarketActivitiesService.MarketActivitiesService" />
    <!--高风险业务申请管理-->
    <bean id="RiskDutyService" class="com.xinxinsoft.service.RiskDutyService.RiskDutyService" />
    <!-- 集团效益评估统计-->
    <bean id="MarketActivitiesTask" class="com.xinxinsoft.task.MarketActivitiesTask">
    <property name="marketActivitiesService">
    <ref bean="MarketActivitiesService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MarketActivitiesTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MarketActivitiesTask" class="com.xinxinsoft.task.MarketActivitiesTask">
    <property name="marketActivitiesService">
    <ref bean="MarketActivitiesService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MarketingActivitiesService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MarketingActivitiesService" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesService" />
    <bean id="V2OmsSellOrderService" class="com.xinxinsoft.service.oms.V2OmsSellOrderService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MarketingActivitiesServiceTwo -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MarketingActivitiesServiceTwo" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesServiceTwo" />
    <!--营销活动工单管理-->
    <bean id="MarketingActivitiesService" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesService" />
    <bean id="V2OmsSellOrderService" class="com.xinxinsoft.service.oms.V2OmsSellOrderService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MenuItemService -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MenuItemService" class="com.xinxinsoft.service.core.menuitem.MenuItemService">
    <property name="loginUserService">
    <ref bean="LoginUserService"></ref>
    </property>
    <property name="roleService">
    <ref bean="RoleService"></ref>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MenuService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MoaService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MoaService" class="com.xinxinsoft.service.moaService.MoaService" />
    <!--风险管控预开票数据-->
    <bean id="PreinvApplyRiskDataTask" class="com.xinxinsoft.task.PreinvApplyRiskDataTask">
    <property name="riskClosedLoopService">
    <ref bean="RiskClosedLoopService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#MonthlyinvoiceService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="MonthlyinvoiceService" class="com.xinxinsoft.service.monthlyinvoice.MonthlyinvoiceService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#NoResApplyService -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="NoResApplyService" class="com.xinxinsoft.service.appOpenService.NoResApplyService">
    <property name="jbpmUtil">
    <ref bean="JBPMUtil" />
    </property>
    <property name="waitTaskService">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OMSService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OMSService" class="com.xinxinsoft.service.appOpenService.OMSService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OcrIdentificationLogsService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OcrIdentificationLogsService" class="com.xinxinsoft.service.ocrIdentificationLogsService.OcrIdentificationLogsService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsOrderManagerSignInTask -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsOrderManagerSignInTask" class="com.xinxinsoft.task.OmsOrderManagerSignInTask" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="OmsSellOrderService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="structureOfPersonnelService">
    <ref bean="StructureOfPersonnelService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsOrderProductService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsOrderProductService" class="com.xinxinsoft.service.oms.OmsOrderProductService"/>
    <bean id="StructureOfPersonnelService" class="com.xinxinsoft.service.core.user.StructureOfPersonnelService"/>
    <!--服务标准化测试用例-->
    <bean id="ServiceStandardizationTestingService" class="com.xinxinsoft.service.oms.ServiceStandardizationTestingService"/>
    <bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsOrderWorkbenchService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsOrderWorkbenchService" class="com.xinxinsoft.service.oms.OmsOrderWorkbenchService"/>
    <!--销售工单产品受理详细-->
    <bean id="OmsOrderProductService" class="com.xinxinsoft.service.oms.OmsOrderProductService"/>
    <bean id="StructureOfPersonnelService" class="com.xinxinsoft.service.core.user.StructureOfPersonnelService"/>
    <!--服务标准化测试用例-->
    <bean id="ServiceStandardizationTestingService" class="com.xinxinsoft.service.oms.ServiceStandardizationTestingService"/>
    <bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsSellOrderService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsSellOrderService" class="com.xinxinsoft.service.oms.OmsSellOrderService"/>
    <!--工单受理工作台-->
    <bean id="OmsOrderWorkbenchService" class="com.xinxinsoft.service.oms.OmsOrderWorkbenchService"/>
    <!--销售工单产品受理详细-->
    <bean id="OmsOrderProductService" class="com.xinxinsoft.service.oms.OmsOrderProductService"/>
    <bean id="StructureOfPersonnelService" class="com.xinxinsoft.service.core.user.StructureOfPersonnelService"/>
    <!--服务标准化测试用例-->
    <bean id="ServiceStandardizationTestingService" class="com.xinxinsoft.service.oms.ServiceStandardizationTestingService"/>
    <bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OmsStatisticalService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OmsStatisticalService" class="com.xinxinsoft.service.oms.OmsStatisticalService" />
    <bean id="TariffManagementService" class="com.xinxinsoft.service.tariffManagement.TariffManagementService"/>
    <!--一键下单驳回统计分析-->
    <bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService" />
    <!--风险控制闭环管理-->
    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
    <!--政企白名单-->
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OneClickOrderTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OneClickOrderTask" class="com.xinxinsoft.task.OneClickOrderTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#Open4AStateService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="Open4AStateService" class="com.xinxinsoft.service.core.app.Open4AStateService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OrderQuerySendService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OrderQuerySendService" class="com.xinxinsoft.service.pay.OrderQuerySendService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OrderQueryService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OrderQueryService" class="com.xinxinsoft.service.pay.OrderQueryService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OrderTaskService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OrderTaskService" class="com.xinxinsoft.service.ordertask.OrderTaskService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OrderWarningTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OrderWarningTask" class="com.xinxinsoft.task.OrderWarningTask">
    <property name="singleService">
    <ref bean="commonSingleService" />
    </property>
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OverTimeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OverTimeService" class="com.xinxinsoft.service.overTime.OverTimeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#OwnDepartmentsService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="OwnDepartmentsService" class="com.xinxinsoft.service.core.OwnDepartmentsService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PMSService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PMSService" class="com.xinxinsoft.service.appOpenService.PMSService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PayApiService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PayApiService" class="com.xinxinsoft.service.pay.PayApiService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PayRefundService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PayRefundService" class="com.xinxinsoft.service.pay.PayRefundService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PaymentMessageTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PaymentMessageTask" class="com.xinxinsoft.task.PaymentMessageTask">
    <property name="pService">
    <ref bean="PaymentRecordService"/>
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PaymentOrderService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PaymentOrderService" class="com.xinxinsoft.service.pay.PaymentOrderService" autowire="byType"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PaymentProviderService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PaymentProviderService" class="com.xinxinsoft.service.pay.PaymentProviderService" ></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PaymentRecordService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PaymentRecordService" class="com.xinxinsoft.service.arrearsModule.PaymentRecordService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PaymentService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PaymentService" class="com.xinxinsoft.service.pay.PaymentService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PreinvApplyImportTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PreinvApplyImportTask" class="com.xinxinsoft.task.PreinvApplyImportTask">
    <property name="preinvApplyService">
    <ref bean="PreinvApplyService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PreinvApplyMarkingService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PreinvApplyMarkingService" class="com.xinxinsoft.service.preinvApplyMarkingService.PreinvApplyMarkingService" scope="prototype"></bean>

    <!-- ========== 85环境独有配置 ========== -->
    <!-- 配置项: bean#PreinvApplyRiskDataTask -->
    <!-- 说明: 此配置仅在85环境中存在 -->
    <!-- 用途: 配置组件 -->
    <!-- ===================================== -->

    <bean id="PreinvApplyRiskDataTask" class="com.xinxinsoft.task.PreinvApplyRiskDataTask">
    <property name="riskClosedLoopService">
    <ref bean="RiskClosedLoopService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PreinvApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PreinvApplyService" class="com.xinxinsoft.service.PreinvApply.PreinvApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PreinvIoTCardService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#Proc4AOracle -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="Proc4AOracle" class="com.xinxinsoft.task.Proc4AOracle">
    <property name="userService">
    <ref bean="User4AService" />
    </property>
    <property name="uuService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ProductFlowService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ProductFlowService" class="com.xinxinsoft.service.processLink.ProductFlowService" />
    <!-- 需求申请 -->
    <bean id="DedicatedFlowService"
    class="com.xinxinsoft.service.dedicatedFlow.DedicatedFlowService" />
    <!-- 需求申请 -->
    <bean id="ExpenseApplyService"
    class="com.xinxinsoft.service.dedicatedFlow.ExpenseApplyService" />
    <!-- 环节模板 -->
    <bean id="LinkTemplateService" class="com.xinxinsoft.service.processLink.LinkTemplateService" />
    <!-- 菜单管理 -->
    <bean id="MenuService" class="com.xinxinsoft.service.core.role.MenuService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ProductTypeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ProductTypeService" class="com.xinxinsoft.service.basetype.ProductTypeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#PushBusiOppLogService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="PushBusiOppLogService" class="com.xinxinsoft.service.oms.PushBusiOppLogService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#QueryPaymentService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="QueryPaymentService" class="com.xinxinsoft.service.pay.QueryPaymentService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#QuestionService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="QuestionService" class="com.xinxinsoft.service.checkservice.QuestionService" />
    <!-- 投诉建议 -->
    <bean id="RecommendedManagementService"
    class="com.xinxinsoft.service.RecommendedManagement.RecommendedManagementService" />
    <!-- 合同接口服务 -->
    <bean id="ContractHttpService" class="com.xinxinsoft.service.httpService.ContractHttpService" />
    <bean id="proceduresTask" class="com.xinxinsoft.task.ProceduresTask">
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#QuestionnaireService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="QuestionnaireService" class="com.xinxinsoft.service.QuestionSurveyService.QuestionnaireService" />
    <!--三方人员导入审批-->
    <bean id="TripartipartUserService" class="com.xinxinsoft.service.core.user.TripartipartUserService" />
    <bean id="V2OmsOrderManagerSignInTask" class="com.xinxinsoft.task.V2OmsOrderManagerSignInTask" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="structureOfPersonnelService">
    <ref bean="StructureOfPersonnelService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#QuickQueryService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="QuickQueryService" class="com.xinxinsoft.service.quickQueryService.QuickQueryService" />
    <bean id="AuditMultipleService" class="com.xinxinsoft.service.AuditWorksheetService.AuditMultipleService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReceiptApplyAmountService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReceiptApplyAmountService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyAmountService" />
    <!--和飞速成员管理-->
    <bean id="AndFlySpeedService" class="com.xinxinsoft.service.andFlySpeedService.AndFlySpeedService" />
    <!--异常号码管理-->
    <bean id="AbnormalNumberService" class="com.xinxinsoft.service.abnormalNumberService.AbnormalNumberService" />
    <!--	集团实名预约-->
    <bean id="realNameReservService" class="com.xinxinsoft.service.RealNameReservService.realNameReservService" />
    <!--138附件管理-->
    <bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService" />
    <!-- 异常数据管理 -->
    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
    <property name="abnormalNumberService">
    <ref bean="AbnormalNumberService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReceiptApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReceiptApplyService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReceiveApplyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReceiveApplyService" class="com.xinxinsoft.service.receiveApply.ReceiveApplyService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RecommendedManagementService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RecommendedManagementService"
    class="com.xinxinsoft.service.RecommendedManagement.RecommendedManagementService" />
    <!-- 合同接口服务 -->
    <bean id="ContractHttpService" class="com.xinxinsoft.service.httpService.ContractHttpService" />
    <bean id="proceduresTask" class="com.xinxinsoft.task.ProceduresTask">
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReconciliationService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReconciliationService" class="com.xinxinsoft.service.pay.ReconciliationService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReconnaissanceResultService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReconnaissanceResultService"
    class="com.xinxinsoft.service.reconnaissanceResult.ReconnaissanceResultService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ReductionICTApplicationService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ReductionICTApplicationService" class="com.xinxinsoft.service.ICT.ReductionICTApplicationService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RefundOrderService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RefundOrderService" class="com.xinxinsoft.service.pay.RefundOrderService" autowire="byType"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RefundQuerySendService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RefundQuerySendService" class="com.xinxinsoft.service.pay.RefundQuerySendService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RefundQueryService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RefundQueryService" class="com.xinxinsoft.service.pay.RefundQueryService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RefundService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RefundService" class="com.xinxinsoft.service.pay.RefundService" ></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RepairOrderService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RepairOrderService" class="com.xinxinsoft.service.RepairOrderService.RepairOrderService" />
    <!--特殊支付计划-->
    <bean id="SpeciaPlanService" class="com.xinxinsoft.service.SpeciaPlanService.SpeciaPlanService" />
    <bean id="SpeciaPlanSev" class="com.xinxinsoft.sendComms.SpeciaPlanSev" />
    <!--一键甩单APP统计分析-->
    <bean id="OmsStatisticalService" class="com.xinxinsoft.service.oms.OmsStatisticalService" />
    <bean id="TariffManagementService" class="com.xinxinsoft.service.tariffManagement.TariffManagementService"/>
    <!--一键下单驳回统计分析-->
    <bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService" />
    <!--风险控制闭环管理-->
    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
    <!--政企白名单-->
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ResourceSurveyTask -->
    <!-- 85环境: 2个属性, 5个子元素 -->
    <!-- 86环境: 2个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ResourceSurveyTask" class="com.xinxinsoft.task.ResourceSurveyTask">
    <property name="singleService">
    <ref bean="commonSingleService" />
    </property>
    <property name="openService">
    <ref bean="CMCCOpenService" />
    </property>
    <property name="waitTaskservice">
    <ref bean="WaitTaskService" />
    </property>
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RiskClosedLoopService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
    <!--政企白名单-->
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RiskDutyService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RiskDutyService" class="com.xinxinsoft.service.RiskDutyService.RiskDutyService" />
    <!-- 集团效益评估统计-->
    <bean id="MarketActivitiesTask" class="com.xinxinsoft.task.MarketActivitiesTask">
    <property name="marketActivitiesService">
    <ref bean="MarketActivitiesService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RiskcontrolDerivedService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RiskcontrolDerivedService" class="com.xinxinsoft.service.riskcontrolDerivedService.RiskcontrolDerivedService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#RoleService -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="RoleService" class="com.xinxinsoft.service.core.role.RoleService">
    <property name="menuItemService">
    <ref bean="MenuItemService"></ref>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SEinvQryTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SEinvQryTask" class="com.xinxinsoft.task.SEinvQryTask">
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    <property name="monthlyinvoiceService">
    <ref bean="MonthlyinvoiceService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SIMService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SIMService" class="com.xinxinsoft.service.SIM.SIMService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SRestoreDeadUserService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SRestoreDeadUserService" class="com.xinxinsoft.service.SRestoreDeadUser.SRestoreDeadUserService" />
    <!-- 接口请求数据管理 -->
    <bean id="SysLogChangeTask" class="com.xinxinsoft.task.SysLogChangeTask">
    <property name="sysLogService">
    <ref bean="SysLogService" />
    </property>
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SaveSendCheckService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SaveSendCheckService"
    class="com.xinxinsoft.service.SaveSendService.SaveSendCheckService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SaveSendCheckTimeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SaveSendCheckTimeService"
    class="com.xinxinsoft.service.SaveSendService.SaveSendCheckTimeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SaveSendService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SaveSendService" class="com.xinxinsoft.service.SaveSendService.SaveSendService" />
    <!-- 补收service -->
    <bean id="SupCollectService"
    class="com.xinxinsoft.service.SaveSendService.SupCollectService" />
    <!-- 终端service -->
    <bean id="TerminalService" class="com.xinxinsoft.service.SaveSendService.TerminalService" />
    <!-- 转账service -->
    <bean id="TransferAccountsService"
    class="com.xinxinsoft.service.SaveSendService.TransferAccountsService" />
    <!-- 工单service -->
    <bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SealService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SealService" class="com.xinxinsoft.service.sign.SealService"/>
    <bean id="SignService" class="com.xinxinsoft.service.sign.SignService">
    <property name="sealService">
    <ref bean="SealService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ServiceShutdownAndStartupService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ServiceShutdownAndStartupService" class="com.xinxinsoft.service.ServiceShutdownAndStartupService.ServiceShutdownAndStartupService" />
    <!--正负补收额度管理-->
    <bean id="ReceiptApplyAmountService" class="com.xinxinsoft.service.ReceiptApplyService.ReceiptApplyAmountService" />
    <!--和飞速成员管理-->
    <bean id="AndFlySpeedService" class="com.xinxinsoft.service.andFlySpeedService.AndFlySpeedService" />
    <!--异常号码管理-->
    <bean id="AbnormalNumberService" class="com.xinxinsoft.service.abnormalNumberService.AbnormalNumberService" />
    <!--	集团实名预约-->
    <bean id="realNameReservService" class="com.xinxinsoft.service.RealNameReservService.realNameReservService" />
    <!--138附件管理-->
    <bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService" />
    <!-- 异常数据管理 -->
    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
    <property name="abnormalNumberService">
    <ref bean="AbnormalNumberService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ServiceStandardizationTestingService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ServiceStandardizationTestingService" class="com.xinxinsoft.service.oms.ServiceStandardizationTestingService"/>
    <bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SettlementService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SettlementService" class="com.xinxinsoft.service.pay.SettlementService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SignService -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SignService" class="com.xinxinsoft.service.sign.SignService">
    <property name="sealService">
    <ref bean="SealService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SpeciaPlanService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SpeciaPlanService" class="com.xinxinsoft.service.SpeciaPlanService.SpeciaPlanService" />
    <bean id="SpeciaPlanSev" class="com.xinxinsoft.sendComms.SpeciaPlanSev" />
    <!--一键甩单APP统计分析-->
    <bean id="OmsStatisticalService" class="com.xinxinsoft.service.oms.OmsStatisticalService" />
    <bean id="TariffManagementService" class="com.xinxinsoft.service.tariffManagement.TariffManagementService"/>
    <!--一键下单驳回统计分析-->
    <bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService" />
    <!--风险控制闭环管理-->
    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
    <!--政企白名单-->
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SpeciaPlanSev -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SpeciaPlanSev" class="com.xinxinsoft.sendComms.SpeciaPlanSev" />
    <!--一键甩单APP统计分析-->
    <bean id="OmsStatisticalService" class="com.xinxinsoft.service.oms.OmsStatisticalService" />
    <bean id="TariffManagementService" class="com.xinxinsoft.service.tariffManagement.TariffManagementService"/>
    <!--一键下单驳回统计分析-->
    <bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService" />
    <!--风险控制闭环管理-->
    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
    <!--政企白名单-->
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#StagesAyncBossTask -->
    <!-- 85环境: 2个属性, 4个子元素 -->
    <!-- 86环境: 2个属性, 4个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="StagesAyncBossTask" class="com.xinxinsoft.task.StagesAyncBossTask">
    <property name="singleService">
    <ref bean="commonSingleService" />
    </property>
    <property name="flowService">
    <ref bean="DedicatedFlowService" />
    </property>
    <property name="lateService">
    <ref bean="LinkTemplateService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#StructureOfPersonnelService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="StructureOfPersonnelService" class="com.xinxinsoft.service.core.user.StructureOfPersonnelService"/>
    <!--服务标准化测试用例-->
    <bean id="ServiceStandardizationTestingService" class="com.xinxinsoft.service.oms.ServiceStandardizationTestingService"/>
    <bean id="GroupPaymentService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentService"/>
    <bean id="GroupPaymentInterfaceService" class="com.xinxinsoft.service.GroupPaymentService.GroupPaymentInterfaceService"/>
    <bean id="ContractualProductTask" class="com.xinxinsoft.task.ContractualProductTask">
    <property name="omsService">
    <ref bean="OMSService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SuoCollectCheckTimeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SuoCollectCheckTimeService"
    class="com.xinxinsoft.service.SaveSendService.SuoCollectCheckTimeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SupCollectService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SupCollectService"
    class="com.xinxinsoft.service.SaveSendService.SupCollectService" />
    <!-- 终端service -->
    <bean id="TerminalService" class="com.xinxinsoft.service.SaveSendService.TerminalService" />
    <!-- 转账service -->
    <bean id="TransferAccountsService"
    class="com.xinxinsoft.service.SaveSendService.TransferAccountsService" />
    <!-- 工单service -->
    <bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SuspensionApplicationService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SuspensionApplicationService" class="com.xinxinsoft.service.SuspensionApplicationService.SuspensionApplicationService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SysLogChangeTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SysLogChangeTask" class="com.xinxinsoft.task.SysLogChangeTask">
    <property name="sysLogService">
    <ref bean="SysLogService" />
    </property>
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SysLogService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SysLogService" class="com.xinxinsoft.service.sysLogService.SysLogService" />
    <bean id="OcrIdentificationLogsService" class="com.xinxinsoft.service.ocrIdentificationLogsService.OcrIdentificationLogsService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SystemOrganizationService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SystemOrganizationService"
    class="com.xinxinsoft.service.core.org.SystemOrganizationService" />
    <!-- 后台系统菜单服务 -->
    <bean id="MenuItemService" class="com.xinxinsoft.service.core.menuitem.MenuItemService">
    <property name="loginUserService">
    <ref bean="LoginUserService"></ref>
    </property>
    <property name="roleService">
    <ref bean="RoleService"></ref>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SystemUserService -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SystemUserService" class="com.xinxinsoft.service.core.user.SystemUserService">
    <property name="passwordEncoder">
    <ref bean="passwordEncoder" />
    </property>
    <property name="loginUserService">
    <ref bean="LoginUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#SystemWarningTask -->
    <!-- 85环境: 3个属性, 1个子元素 -->
    <!-- 86环境: 3个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="SystemWarningTask" class="com.xinxinsoft.task.SystemWarningTask" scope="prototype">
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TariffManagementService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TariffManagementService" class="com.xinxinsoft.service.tariffManagement.TariffManagementService"/>
    <!--一键下单驳回统计分析-->
    <bean id="JobRejectionInfoService" class="com.xinxinsoft.service.jobRejectionInfoService.JobRejectionInfoService" />
    <!--风险控制闭环管理-->
    <bean id="RiskClosedLoopService" class="com.xinxinsoft.service.riskClosedLoop.RiskClosedLoopService" />
    <!--政企白名单-->
    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TelnetPort -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TelnetPort" class="com.xinxinsoft.task.TelnetPort">
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TerminalActivityService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TerminalActivityService" class="com.xinxinsoft.service.terminalActivityAdministration.TerminalActivityService" />
    <bean id="GroupAccountService" class="com.xinxinsoft.service.groupAccountService.GroupAccountService" />
    <!-- 检查系统端口-->
    <bean id="TelnetPort" class="com.xinxinsoft.task.TelnetPort">
    <property name="smsPushService">
    <ref bean="smsPushService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TerminalCheckTimeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TerminalCheckTimeService"
    class="com.xinxinsoft.service.SaveSendService.TerminalCheckTimeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TerminalService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TerminalService" class="com.xinxinsoft.service.SaveSendService.TerminalService" />
    <!-- 转账service -->
    <bean id="TransferAccountsService"
    class="com.xinxinsoft.service.SaveSendService.TransferAccountsService" />
    <!-- 工单service -->
    <bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferAccountsCheckTimeService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferAccountsCheckTimeService"
    class="com.xinxinsoft.service.SaveSendService.TransferAccountsCheckTimeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferAccountsService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferAccountsService"
    class="com.xinxinsoft.service.SaveSendService.TransferAccountsService" />
    <!-- 工单service -->
    <bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferCitiesDataService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferCitiesDataService" class="com.xinxinsoft.service.transfer.TransferCitiesDataService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferInformationService -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferInformationService" class="com.xinxinsoft.service.transfer.TransferInformationService">
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferInformationTwoService -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferInformationTwoService" class="com.xinxinsoft.service.transfer.TransferInformationTwoService">
    <property name="transferJBPMUtils">
    <ref bean="TransferJBPMUtils"/>
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferInterfaceService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferInterfaceService" class="com.xinxinsoft.service.transfer.TransferInterfaceService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TransferService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TransferService" class="com.xinxinsoft.service.transfer.TransferService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TripartipartUserService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TripartipartUserService" class="com.xinxinsoft.service.core.user.TripartipartUserService" />
    <bean id="V2OmsOrderManagerSignInTask" class="com.xinxinsoft.task.V2OmsOrderManagerSignInTask" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="structureOfPersonnelService">
    <ref bean="StructureOfPersonnelService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#TypeValueService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="TypeValueService" class="com.xinxinsoft.service.core.typeValue.TypeValueService" />
    <!-- 密码加密服务 -->
    <bean id="passwordEncoder" class="org.acegisecurity.providers.encoding.Md5PasswordEncoder"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#USIMService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="USIMService" class="com.xinxinsoft.service.USIMService.USIMService" />
    <!--自助缴费验证-->
    <bean id="GroupInviteCodeService" class="com.xinxinsoft.service.groupInviteCodeService.GroupInviteCodeService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#UnitInfoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="UnitInfoService" class="com.xinxinsoft.service.ums.UnitInfoService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#UploadDocumentService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="UploadDocumentService" class="com.xinxinsoft.service.uploadDocumentService.UploadDocumentService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#User4AService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="User4AService" class="com.xinxinsoft.service.core.user.User4AService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#V2OmsOrderManagerSignInTask -->
    <!-- 85环境: 3个属性, 5个子元素 -->
    <!-- 86环境: 3个属性, 5个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="V2OmsOrderManagerSignInTask" class="com.xinxinsoft.task.V2OmsOrderManagerSignInTask" scope="prototype">
    <property name="omsSellOrderService">
    <ref bean="V2OmsSellOrderService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="structureOfPersonnelService">
    <ref bean="StructureOfPersonnelService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#V2OmsSellOrderService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="V2OmsSellOrderService" class="com.xinxinsoft.service.oms.V2OmsSellOrderService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ValuableCardService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ValuableCardService" class="com.xinxinsoft.service.valuableCard.ValuableCardService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#Various1000SqlQueryService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="Various1000SqlQueryService" class="com.xinxinsoft.service.appOpenService.Various1000SqlQueryService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#VariousSqlQueryService -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="VariousSqlQueryService" class="com.xinxinsoft.service.appOpenService.VariousSqlQueryService">
    <property name="conSer">
    <ref bean="ContractService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WaitTaskService -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WaitTaskService" class="com.xinxinsoft.service.waitTask.WaitTaskService">
    <property name="pushService">
    <ref bean="smsPushService" />
    </property>
    <property name="systemUserService">
    <ref bean="SystemUserService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WatermarkContractWebservice -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WatermarkContractWebservice" class="com.xinxinsoft.service.webService.WatermarkContract.WatermarkContractWebservice"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WhiteListInformationService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WhiteListInformationService" class="com.xinxinsoft.service.whiteList.WhiteListInformationService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WhiteListService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WhiteListService" class="com.xinxinsoft.service.whiteList.WhiteListService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WhiteRollListService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WhiteRollListService" class="com.xinxinsoft.service.whiteRollListService.WhiteRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#WorkOrderServcie -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="WorkOrderServcie" class="com.xinxinsoft.service.workOrder.WorkOrderServcie"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#ZtreeUserService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="ZtreeUserService" class="com.xinxinsoft.service.core.user.ZtreeUserService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#accountOpenService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="accountOpenService" class="com.xinxinsoft.service.accountOpenService.AccountOpenService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appLoginService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appLoginService" class="com.xinxinsoft.service.appLoginService.appLoginService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#appUploadService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="appUploadService" class="com.xinxinsoft.service.core.app.AppUploadService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#arrearsTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="arrearsTask" class="com.xinxinsoft.task.ArrearsTask">
    <property name="psService">
    <ref bean="PaymentRecordService"/>
    </property>
    <property name="arrearsService">
    <ref bean="ArrearsService"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#claimForFundModelService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="claimForFundModelService" class="com.xinxinsoft.service.claimForFunds.claimForFundModelService" />
    <bean id="ClaimForFundsService" class="com.xinxinsoft.service.claimForFunds.ClaimForFundsService" />
    <bean id="ClaimForFundsTask" class="com.xinxinsoft.task.ClaimForFundsTask">
    <property name="claimForFundsService">
    <ref bean="ClaimForFundsService" />
    </property>
    <property name="taskService">
    <ref bean="Bpms_riskoff_service" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#commonSingleService -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="commonSingleService"
    class="com.xinxinsoft.service.commonSingManagement.CommonSingleService">
    <property name="dicService">
    <ref bean="DictionaryService" />
    </property>
    <property name="service">
    <ref bean="WaitTaskService" />
    </property>
    <property name="attservcie">
    <ref bean="AttachmentService" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#customerService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="customerService" class="com.xinxinsoft.service.customer.customerService">
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#customerServices -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="customerServices" class="com.xinxinsoft.sendComms.GroupCustomerService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#fourtOpenService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="fourtOpenService"  class="com.xinxinsoft.service.fourtOpenService.fourtOpenService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#groupReportedLossesService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->


    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#holidayService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="holidayService" class="com.xinxinsoft.service.holiday.HolidayService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#holidayTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="holidayTask" class="com.xinxinsoft.task.HolidayTask">
    <property name="holidayService">
    <ref bean="holidayService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#imsService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="imsService" class="com.xinxinsoft.service.dedicatedFlow.ims.ImsService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#itoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="itoService" class="com.xinxinsoft.service.dedicatedFlow.ito.ItoService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#jbpmProcessService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="jbpmProcessService" class="com.xinxinsoft.service.jbpmProcess.JbpmProcessService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#lwFtpTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="lwFtpTask" class="com.xinxinsoft.task.LwFtpTask">
    <property name="operFTPservice">
    <ref bean="operFTPservice" />
    </property>
    <property name="workOrderService">
    <ref bean="WorkOrderServcie" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#messagesInfoService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="messagesInfoService" class="com.xinxinsoft.service.messages.MessagesInfoService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#missionConfigService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="missionConfigService" class="com.xinxinsoft.service.MissionConfigService.missionConfigService" />
    <!--营销活动配额管理-->
    <bean id="MarketingActivitiesServiceTwo" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesServiceTwo" />
    <!--营销活动工单管理-->
    <bean id="MarketingActivitiesService" class="com.xinxinsoft.service.marketingActivitiesService.MarketingActivitiesService" />
    <bean id="V2OmsSellOrderService" class="com.xinxinsoft.service.oms.V2OmsSellOrderService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#operFTPservice -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="operFTPservice" class="com.xinxinsoft.service.ftpss.OperFTPservice"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#orderDetailService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="orderDetailService" class="com.xinxinsoft.service.order.OrderDetailService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#passwordEncoder -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="passwordEncoder" class="org.acegisecurity.providers.encoding.Md5PasswordEncoder"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#payCenterAPIWebService -->
    <!-- 85环境: 2个属性, 1个子元素 -->
    <!-- 86环境: 2个属性, 1个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="payCenterAPIWebService" class="com.xinxinsoft.service.webService.pay.PayCenterAPIWebService">
    <constructor-arg name="payApiService" ref="PayApiService"></constructor-arg>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#paymentProviderWebService -->
    <!-- 85环境: 2个属性, 11个子元素 -->
    <!-- 86环境: 2个属性, 11个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="paymentProviderWebService" class="com.xinxinsoft.service.webService.pay.PaymentProviderWebService" >
    <constructor-arg name="paymentProviderService" ref="PaymentProviderService"></constructor-arg>
    <constructor-arg name="busiParameterService" ref="BusiParameterService"></constructor-arg>
    <constructor-arg name="goodsInfoService" ref="GoodsInfoService"></constructor-arg>
    <constructor-arg name="orderQueryService" ref="OrderQueryService"></constructor-arg>
    <constructor-arg name="orderQuerySendService" ref="OrderQuerySendService"></constructor-arg>
    <constructor-arg name="refundService" ref="RefundService"></constructor-arg>
    <constructor-arg name="refundQueryService" ref="RefundQueryService"></constructor-arg>
    <constructor-arg name="refundQuerySendService" ref="RefundQuerySendService"></constructor-arg>
    <constructor-arg name="paymentOrderService" ref="PaymentOrderService"></constructor-arg>
    <constructor-arg name="refundOrderService" ref="RefundOrderService"></constructor-arg>
    <constructor-arg name="variousSqlQueryService" ref="VariousSqlQueryService"></constructor-arg>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#paymentWebService -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="paymentWebService" class="com.xinxinsoft.service.webService.pay.PaymentWebService" >
    <constructor-arg name="paymentService" ref="PaymentService"></constructor-arg>
    <constructor-arg name="queryPaymentService" ref="QueryPaymentService"></constructor-arg>
    <constructor-arg name="payRefundService" ref="PayRefundService"></constructor-arg>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#proceduresTask -->
    <!-- 85环境: 2个属性, 2个子元素 -->
    <!-- 86环境: 2个属性, 2个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="proceduresTask" class="com.xinxinsoft.task.ProceduresTask">
    <property name="userService">
    <ref bean="SystemUserService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#processService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="processService"
    class="com.xinxinsoft.service.core.processService.ProcessService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#qryPreDealOutService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="qryPreDealOutService" class="com.xinxinsoft.service.boss.QryPreDealOutService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#realNameReservService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="realNameReservService" class="com.xinxinsoft.service.RealNameReservService.realNameReservService" />
    <!--138附件管理-->
    <bean id="AttachmentServiceTwo" class="com.xinxinsoft.service.attachmentService.AttachmentService" />
    <!-- 异常数据管理 -->
    <bean id="AbnormalNumberTask" class="com.xinxinsoft.task.AbnormalNumberTask">
    <property name="abnormalNumberService">
    <ref bean="AbnormalNumberService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer" />
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#reconciliationTask -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="reconciliationTask" class="com.xinxinsoft.task.ReconciliationTask">
    <property name="reconciliationService">
    <ref bean="ReconciliationService" />
    </property>
    <property name="settlementService">
    <ref bean="SettlementService" />
    </property>
    <property name="jobLogServicer">
    <ref bean="JobLogServicer"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#redRollListService -->
    <!-- 85环境: 3个属性, 0个子元素 -->
    <!-- 86环境: 3个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="redRollListService" class="com.xinxinsoft.service.redListService.RedRollListService" scope="prototype"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#smsPushService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="smsPushService" class="com.xinxinsoft.service.smsPush.SmsPushService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#sngListExcelService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="sngListExcelService"
    class="com.xinxinsoft.service.commonSingManagement.SingListExcelService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#springContextUtil -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="springContextUtil" class="com.xinxinsoft.utils.SpringContextUtil" />
    <!-- Service -->
    <!-- 后台系统用户服务 -->
    <bean id="LoginUserService" class="com.xinxinsoft.service.core.user.LoginUserService">
    <property name="passwordEncoder">
    <ref bean="passwordEncoder"/>
    </property>
    </bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#startPreOrderOutService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="startPreOrderOutService" class="com.xinxinsoft.service.boss.StartPreOrderOutService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#statisticsService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="statisticsService" class="com.xinxinsoft.service.statistics.StatisticsService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#systemDeptService -->
    <!-- 85环境: 2个属性, 0个子元素 -->
    <!-- 86环境: 2个属性, 0个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="systemDeptService" class="com.xinxinsoft.service.core.dept.SystemDeptService"></bean>

    <!-- ========== 配置差异分析 ========== -->
    <!-- 配置项: bean#voucherCenterWebService -->
    <!-- 85环境: 2个属性, 3个子元素 -->
    <!-- 86环境: 2个属性, 3个子元素 -->
    <!-- 属性差异: 属性相同 -->
    <!-- 子元素差异: 子元素相同 -->
    <!-- ======================================= -->
    <!-- 当前采用85环境配置 -->

    <bean id="voucherCenterWebService" class="com.xinxinsoft.service.webService.pay.VoucherCenterWebService">
    <constructor-arg name="paymentService" ref="PaymentService"></constructor-arg>
    <constructor-arg name="queryPaymentService" ref="QueryPaymentService"></constructor-arg>
    <constructor-arg name="payRefundService" ref="PayRefundService"></constructor-arg>
    </bean>

</beans>